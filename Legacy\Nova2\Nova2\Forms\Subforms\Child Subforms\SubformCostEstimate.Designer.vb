<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformCostEstimate
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformCostEstimate))
        Me.LabelSellPrice = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditCostEstimate = New DevExpress.XtraEditors.TextEdit()
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonOK = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditCostEstimateAmount = New DevExpress.XtraEditors.TextEdit()
        Me.LabelUser = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkMediaService = New DevExpress.XtraEditors.LabelControl()
        CType(Me.TextEditCostEstimate.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditCostEstimateAmount.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!)
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Size = New System.Drawing.Size(196, 37)
        Me.LabelTitle.TabIndex = 0
        Me.LabelTitle.Text = "Cost Estimate"
        '
        'LabelSellPrice
        '
        Me.LabelSellPrice.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSellPrice.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelSellPrice.Location = New System.Drawing.Point(19, 104)
        Me.LabelSellPrice.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelSellPrice.Name = "LabelSellPrice"
        Me.LabelSellPrice.Size = New System.Drawing.Size(165, 17)
        Me.LabelSellPrice.TabIndex = 3
        Me.LabelSellPrice.Text = "Cost Estimate Number:"
        '
        'TextEditCostEstimate
        '
        Me.TextEditCostEstimate.EditValue = ""
        Me.TextEditCostEstimate.Location = New System.Drawing.Point(214, 100)
        Me.TextEditCostEstimate.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.TextEditCostEstimate.Name = "TextEditCostEstimate"
        Me.TextEditCostEstimate.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditCostEstimate.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditCostEstimate.Properties.Appearance.Options.UseFont = True
        Me.TextEditCostEstimate.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditCostEstimate.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TextEditCostEstimate.Size = New System.Drawing.Size(335, 24)
        Me.TextEditCostEstimate.TabIndex = 4
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        '
        'ButtonOK
        '
        Me.ButtonOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonOK.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonOK.Appearance.Options.UseFont = True
        Me.ButtonOK.ImageIndex = 2
        Me.ButtonOK.ImageList = Me.ImageList24x24
        Me.ButtonOK.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonOK.Location = New System.Drawing.Point(445, 167)
        Me.ButtonOK.LookAndFeel.SkinName = "Black"
        Me.ButtonOK.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonOK.Margin = New System.Windows.Forms.Padding(4, 16, 4, 4)
        Me.ButtonOK.Name = "ButtonOK"
        Me.ButtonOK.Size = New System.Drawing.Size(129, 37)
        Me.ButtonOK.TabIndex = 7
        Me.ButtonOK.Text = "OK"
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(581, 167)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Margin = New System.Windows.Forms.Padding(4, 16, 4, 4)
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(129, 37)
        Me.ButtonCancel.TabIndex = 8
        Me.ButtonCancel.Text = "Cancel"
        '
        'LabelControl1
        '
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl1.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl1.Location = New System.Drawing.Point(19, 135)
        Me.LabelControl1.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(166, 17)
        Me.LabelControl1.TabIndex = 5
        Me.LabelControl1.Text = "Cost Estimate Amount:"
        '
        'TextEditCostEstimateAmount
        '
        Me.TextEditCostEstimateAmount.EditValue = ""
        Me.TextEditCostEstimateAmount.Location = New System.Drawing.Point(214, 128)
        Me.TextEditCostEstimateAmount.Margin = New System.Windows.Forms.Padding(4, 4, 0, 4)
        Me.TextEditCostEstimateAmount.Name = "TextEditCostEstimateAmount"
        Me.TextEditCostEstimateAmount.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditCostEstimateAmount.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditCostEstimateAmount.Properties.Appearance.Options.UseFont = True
        Me.TextEditCostEstimateAmount.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditCostEstimateAmount.Properties.Appearance.Options.UseTextOptions = True
        Me.TextEditCostEstimateAmount.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.TextEditCostEstimateAmount.Properties.AppearanceReadOnly.BackColor = System.Drawing.Color.Gainsboro
        Me.TextEditCostEstimateAmount.Properties.AppearanceReadOnly.Options.UseBackColor = True
        Me.TextEditCostEstimateAmount.Properties.Mask.EditMask = "c4"
        Me.TextEditCostEstimateAmount.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditCostEstimateAmount.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TextEditCostEstimateAmount.Size = New System.Drawing.Size(335, 24)
        Me.TextEditCostEstimateAmount.TabIndex = 6
        '
        'LabelUser
        '
        Me.LabelUser.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelUser.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelUser.Location = New System.Drawing.Point(19, 73)
        Me.LabelUser.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelUser.Name = "LabelUser"
        Me.LabelUser.Size = New System.Drawing.Size(100, 17)
        Me.LabelUser.TabIndex = 1
        Me.LabelUser.Text = "Media Service:"
        '
        'HyperlinkMediaService
        '
        Me.HyperlinkMediaService.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkMediaService.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkMediaService.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkMediaService.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkMediaService.Location = New System.Drawing.Point(214, 73)
        Me.HyperlinkMediaService.Margin = New System.Windows.Forms.Padding(4)
        Me.HyperlinkMediaService.Name = "HyperlinkMediaService"
        Me.HyperlinkMediaService.Size = New System.Drawing.Size(57, 17)
        Me.HyperlinkMediaService.TabIndex = 2
        Me.HyperlinkMediaService.Text = "Select..."
        '
        'SubformCostEstimate
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(9.0!, 17.0!)
        Me.Controls.Add(Me.LabelUser)
        Me.Controls.Add(Me.HyperlinkMediaService)
        Me.Controls.Add(Me.TextEditCostEstimateAmount)
        Me.Controls.Add(Me.LabelControl1)
        Me.Controls.Add(Me.ButtonOK)
        Me.Controls.Add(Me.ButtonCancel)
        Me.Controls.Add(Me.LabelSellPrice)
        Me.Controls.Add(Me.TextEditCostEstimate)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(5)
        Me.Name = "SubformCostEstimate"
        Me.Padding = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.Size = New System.Drawing.Size(725, 219)
        Me.Controls.SetChildIndex(Me.TextEditCostEstimate, 0)
        Me.Controls.SetChildIndex(Me.LabelSellPrice, 0)
        Me.Controls.SetChildIndex(Me.ButtonCancel, 0)
        Me.Controls.SetChildIndex(Me.ButtonOK, 0)
        Me.Controls.SetChildIndex(Me.LabelControl1, 0)
        Me.Controls.SetChildIndex(Me.TextEditCostEstimateAmount, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkMediaService, 0)
        Me.Controls.SetChildIndex(Me.LabelUser, 0)
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        CType(Me.TextEditCostEstimate.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditCostEstimateAmount.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents LabelSellPrice As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditCostEstimate As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents ButtonOK As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl1 As LabelControl
    Friend WithEvents TextEditCostEstimateAmount As TextEdit
    Friend WithEvents LabelUser As LabelControl
    Friend WithEvents HyperlinkMediaService As LabelControl
End Class
