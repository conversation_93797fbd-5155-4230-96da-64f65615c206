﻿using DataAccess;
using System;
using System.Collections.Generic;
using System.Data;

namespace DataService.Security
{
    class RemoveLegacyRoleMembersCommand : Command
    {
        public Guid RoleId { get; set; }
        public DataTable Members { get; set; }

        public RemoveLegacyRoleMembersCommand(Guid roleid, List<DataRow> memberslist)
        {
            RoleId = roleid;

            // Create a new table.
            Members = new DataTable();
            Members.Columns.Add("name", typeof(string));

            // Convert the rows in the specified list to rows that match the schema of the table and then add them to the table.
            if (memberslist != null && memberslist.Count > 0)
            {
                for (int i = 0; i < memberslist.Count; i++)
                {
                    DataRow newrow = Members.NewRow();
                    newrow["name"] = memberslist[i]["username"];
                    Members.Rows.Add(newrow);
                }
            }
        }
    }
}
