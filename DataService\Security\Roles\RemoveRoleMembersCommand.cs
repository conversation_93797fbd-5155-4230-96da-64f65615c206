﻿using DataAccess;
using System;
using System.Collections.Generic;
using System.Data;

namespace DataService.Security
{
    class RemoveRoleMembersCommand : Command
    {
        public Guid SessionId { get; set; }
        public Guid RoleId { get; set; }
        public DataTable Members { get; set; }

        public RemoveRoleMembersCommand(Guid sessionid, Guid roleid, List<DataRow> memberslist)
        {
            SessionId = sessionid;
            RoleId = roleid;

            // Create a new table.
            Members = new DataTable();
            Members.Columns.Add("id", typeof(Guid));

            // Convert the rows in the specified list to rows that match the schema of the table and then add them to the table.
            if (memberslist != null && memberslist.Count > 0)
            {
                for (int i = 0; i < memberslist.Count; i++)
                {
                    DataRow newrow = Members.NewRow();
                    newrow["id"] = memberslist[i]["userid"];
                    Members.Rows.Add(newrow);
                }
            }
        }
    }
}
