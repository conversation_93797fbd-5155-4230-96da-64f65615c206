﻿using DataAccess;
using System;
using System.Collections.Generic;
using System.Data;

namespace DataService.Security.Users
{
   internal class DeleteUserChainPermissionsCommand : Command
    {
        public Guid UserId { get; set; }
        public DataTable Chains { get; set; }

        public DeleteUserChainPermissionsCommand(Guid UserId, List<DataRow> chainlist)
        {
            this.UserId = UserId;

            // Create a new table.
            Chains = new DataTable();
            Chains.Columns.Add("ID", typeof(int));

            // Convert the rows in the specified list to rows that match the schema of the table and then add them to the table.
            if (chainlist != null && chainlist.Count > 0)
            {
                for (int i = 0; i < chainlist.Count; i++)
                {
                    DataRow newrow = Chains.NewRow();
                    newrow["ID"] = chainlist[i]["ChainID"];
                    Chains.Rows.Add(newrow);
                }
            }
        }
    }
}
