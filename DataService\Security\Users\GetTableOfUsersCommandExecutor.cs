﻿using DataAccess;
using System.Data;

namespace DataService.Security
{
    internal class GetTableOfUsersCommandExecutor : CommandExecutor<GetTableOfUsersCommand>
    {
        public override void Execute(GetTableOfUsersCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.GetUserTable))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    command.Table = storedprocedure.OutputTable;
                    command.Table.PrimaryKey = new DataColumn[] { command.Table.Columns[0] };
                }
            }            
        }
    }
}
