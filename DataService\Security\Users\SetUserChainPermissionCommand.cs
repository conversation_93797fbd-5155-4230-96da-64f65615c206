﻿using DataAccess;
using System;
using System.Collections.Generic;
using System.Data;

namespace DataService.Security.Users
{
  public class SetUserChainPermissionCommand : Command
    {
        public Guid UserId { get; set; }
        public DataTable SelectedChains { get; set; }

        public SetUserChainPermissionCommand(Guid userid, List<DataRow> selectedChainlist)
        {
            UserId = userid;

            // Create a new table.
            SelectedChains = new DataTable();
            SelectedChains.Columns.Add("ID", typeof(int));

            // Convert the rows in the specified list to rows that match the schema of the table and then add them to the table.
            if (selectedChainlist != null && selectedChainlist.Count > 0)
            {
                for (int i = 0; i < selectedChainlist.Count; i++)
                {
                    DataRow newrow = SelectedChains.NewRow();
                    newrow["ID"] = selectedChainlist[i]["ChainID"];
                    SelectedChains.Rows.Add(newrow);
                }
            }
        }
    }
}
