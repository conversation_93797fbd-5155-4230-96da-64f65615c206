Public Class SubformBurst

    Const PRODUCTLOCATORNAME As String = "Product Locator"
    Private DataObject As Burst
    Private DataBindingCompleted As Boolean = False
    Private OriginalLastWeek As Date
    Private OriginalCrossoverQty As Integer = 0
    Private OriginalCrossoverAdsQty As Integer = 0
    Private OriginalBurstValue As Decimal = 0
    Private HasSelectedInstallRegardles As Boolean = False

    Private WaitForm As FormPleaseWait
    Private WithEvents BackgroundGenerator As New System.ComponentModel.BackgroundWorker

#Region "Property Fields"
    Dim _ProductLocatorContract As Boolean = False
#End Region

#Region "Properties"

    Property ProductLocatorContract As Boolean
        Get
            Return _ProductLocatorContract
        End Get
        Set(value As Boolean)
            _ProductLocatorContract = value
        End Set
    End Property

#End Region

#Region "Event Handlers"

    Private Sub SubformBurst_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        PerformDataBinding()
        SetInstallRegardless()
        InitializeControls()
        SetupTabPageInteractionDates()
        ' If the contract is signed, changing the last week of the burst must be prevented.
        If DataObject.ParentContract.Signed Then
            OriginalLastWeek = DataObject.LastWeek
            OriginalBurstValue = DataObject.NetRental
        End If

        DisableControlsAfterSignature()
        SetProductLocatorContractPropertyValue()

        ' DISABLED TO FIX PROBLEM IN TICKET 1684
        ' -----------------------------------------------------------------------------------------
        '' Delete any orphan store pools.
        'If DataObject.StoreListNotEmpty = False AndAlso DataObject.RelatedBurstView.Count = 0 Then
        '    DataObject.RemoveAllRelatives()
        'End If
        ' -----------------------------------------------------------------------------------------

    End Sub

    Private Sub ButtonOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonOK.Click
        ' Set the creation date of this burst if it is a new one.
        If DataObject.Row.RowState = DataRowState.Detached Then
            DataObject.CreationDate = Settings.GetServerTime(My.Settings.DBConnection, CType(TopLevelControl, BaseForm))
        End If
        ' Add validated event handler to PanelStorePoolSize.
        AddHandler LabelTitle.Validated, AddressOf FinalValidation
        ' Save and close.
        Save(True)
    End Sub

    Private Sub ButtonCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonCancel.Click
        DataObject.RejectChanges()
        RevertToParentSubform()
    End Sub

    Private Sub TextEditInstallWeeks_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles TextEditInstallWeeks.KeyPress

        ' Stop if the store list is not empty.
        If DataObject.StoreListSize > 0 Then
            CType(TopLevelControl, BaseForm).ShowMessage("The duration of a burst may not be modified if the store list is not empty.", "Sorry, Can't Do That")
            e.Handled = True
            Exit Sub
        End If

        ' Check if the user is happy to have all related bursts removed if any exist, tehn do it.
        If RemoveRelatedBursts() = False Then
            e.Handled = True
            Exit Sub
        End If

        ' Reset the error string of the LastWeek hyperlink.
        LiquidAgent.ControlValidation(HyperlinkLastWeek, String.Empty)

    End Sub

    Private Sub TextEditInstallStoreQty_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles TextEditInstallStoreQty.KeyPress

        ' Prevent user from entering a number with more than four digits.
        Dim KeyPressControl As TextEdit = CType(sender, TextEdit)
        If KeyPressControl.EditValue.ToString.Length = 5 AndAlso Char.IsDigit(e.KeyChar) Then
            e.Handled = True
        Else
            LiquidAgent.ControlValidation(HyperlinkMediaService, String.Empty)
        End If

    End Sub

    Private Sub TextEditBillableWeeks_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles TextEditBillableWeeks.KeyPress

        ' Issue a warning if the contract is signed.
        If DataObject.ParentContract.Signed Then
            DisplayWarning("Rental value of a signed contract may not be changed. Les will have your head!")
        End If

        ' Prevent user from entering a number with more than three digits.
        Dim KeyPressControl As TextEdit = CType(sender, TextEdit)
        If KeyPressControl.EditValue.ToString.Length = 3 AndAlso Char.IsDigit(e.KeyChar) Then
            e.Handled = True
        End If

    End Sub

    Private Sub TextEditFreeWeeks_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles TextEditFreeWeeks.KeyPress

        ' Issue a warning if the contract is signed.
        If DataObject.ParentContract.Signed Then
            DisplayWarning("Rental value of a signed contract may not be changed. Les will have your head!")
        End If

        ' Prevent user from entering a number with more than three digits.
        Dim KeyPressControl As TextEdit = CType(sender, TextEdit)
        If KeyPressControl.EditValue.ToString.Length = 3 AndAlso Char.IsDigit(e.KeyChar) Then
            e.Handled = True
        End If

    End Sub

    Private Sub TextEditBillableStoreQty_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles TextEditBillableStoreQty.KeyPress

        ' Issue a warning if the contract is signed.
        If DataObject.ParentContract.Signed Then
            DisplayWarning("Rental value of a signed contract may not be changed. Les will have your head!")
        End If

        ' Prevent user from entering a number with more than three digits.
        Dim KeyPressControl As TextEdit = CType(sender, TextEdit)
        If KeyPressControl.EditValue.ToString.Length = 5 AndAlso Char.IsDigit(e.KeyChar) Then
            e.Handled = True
        End If

    End Sub

    Private Sub TextEditFreeStoreQty_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles TextEditFreeStoreQty.KeyPress

        ' Issue a warning if the contract is signed.
        If DataObject.ParentContract.Signed Then
            DisplayWarning("Rental value of a signed contract may not be changed. Les will have your head!")
        End If

        ' Prevent user from entering a number with more than three digits.
        Dim KeyPressControl As TextEdit = CType(sender, TextEdit)
        If KeyPressControl.EditValue.ToString.Length = 5 AndAlso Char.IsDigit(e.KeyChar) Then
            e.Handled = True
        End If

    End Sub

    Private Sub TextEditCrossoverQty_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles TextEditCrossoverQty.KeyPress
        ' Prevent user from entering a number with more than three digits.
        Dim KeyPressControl As TextEdit = CType(sender, TextEdit)
        If KeyPressControl.EditValue.ToString.Length = 2 AndAlso Char.IsDigit(e.KeyChar) Then
            e.Handled = True
        End If
    End Sub

    Private Sub TextEditCrossoverQty_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles TextEditCrossoverQty.EditValueChanged

        ' Create an object to represent the control.
        Dim CurrentControl As TextEdit = CType(sender, TextEdit)
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)
        If IsDBNull(CurrentControl.EditValue) Then
            ' User doesn't want crossovers.
            TextEditAdsPerCrossover.EditValue = 0
        Else
            If CurrentControl.EditValue = 0 Then
                ' User doesn't want crossovers.
                TextEditAdsPerCrossover.EditValue = 0
            End If
        End If

    End Sub

    Private Sub GridCrossoverPreferences_RowsAdded(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewRowsAddedEventArgs) Handles GridCrossoverPreferences.RowsAdded
        LiquidAgent.ControlValidation(TextEditCrossoverQty, String.Empty)
    End Sub

    Private Sub GridCrossoverPreferences_RowsRemoved(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewRowsRemovedEventArgs) Handles GridCrossoverPreferences.RowsRemoved
        LiquidAgent.ControlValidation(TextEditCrossoverQty, String.Empty)
    End Sub

    Private Sub TextEditDiscount_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles TextEditDiscount.KeyPress

        ' Issue a warning if the contract is signed.
        If DataObject.ParentContract.Signed Then
            DisplayWarning("Rental value of a signed contract may not be changed. Les will have your head!")
        End If

    End Sub

    Private Sub TextEditDiscount_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles TextEditDiscount.EditValueChanged

        ' Create an object to represent the control.
        Dim CurrentControl As TextEdit = CType(sender, TextEdit)

        ' Reset the error text of the control to an empty string.
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)

    End Sub

    Private Sub TextEditRentalAmount_EditValueChanged(sender As Object, e As EventArgs) Handles TextEditRentalAmount.EditValueChanged
        UpdateAllLoadingFees()
        RefreshDisplayedLoadingFees()
    End Sub

    Private Sub TextEditRentalRate_KeyPress(ByVal sender As Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles TextEditRentalRate.KeyPress

        ' Issue a warning if the contract is signed.
        If DataObject.ParentContract.Signed Then
            DisplayWarning("Rental value of a signed contract may not be changed. Les will have your head!")
        End If

    End Sub

    Private Sub HyperlinkFirstWeek_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles HyperlinkFirstWeek.Click

        ' Prevent changes to the contract if it has already been signed.
        If DataObject.ParentContract.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If

        ' Stop if the store list is not empty.
        If DataObject.StoreListSize > 0 Then
            CType(TopLevelControl, BaseForm).ShowMessage("The first week may not be modified if the store list is not empty.", "Sorry, Can't Do That")
            Exit Sub
        End If

        ' Check if the user is happy to have all related bursts removed if any exist, tehn do it.
        If RemoveRelatedBursts() = False Then
            Exit Sub
        End If

        Dim NewDate As Nullable(Of Date) = DateSelector.GetDate(True, DataObject.FirstWeek)
        If NewDate.HasValue Then
            DataObject.FirstWeek = NewDate.Value
            HyperlinkFirstWeek.DataBindings("Text").ReadValue()
            HyperlinkLastWeek.DataBindings("Text").ReadValue()
            LiquidAgent.ControlValidation(HyperlinkMediaService, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkLastWeek_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles HyperlinkLastWeek.Click

        ' Prevent changes to the contract if it has already been signed.
        If DataObject.ParentContract.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If

        ' Stop if the store list is not empty.
        If DataObject.StoreListSize > 0 Then
            CType(TopLevelControl, BaseForm).ShowMessage("The last week may not be modified if the store list is not empty.", "Sorry, Can't Do That")
            Exit Sub
        End If

        ' Check if the user is happy to have all related bursts removed if any exist, tehn do it.
        If RemoveRelatedBursts() = False Then
            Exit Sub
        End If

        Dim NewDate As Nullable(Of Date) = DateSelector.GetDate(True, DataObject.LastWeek)
        If NewDate.HasValue Then
            DataObject.LastWeek = NewDate.Value
            HyperlinkLastWeek.DataBindings("Text").ReadValue()
            TextEditInstallWeeks.DataBindings("EditValue").ReadValue()
            TextEditFreeWeeks.DataBindings("EditValue").ReadValue()
            LiquidAgent.ControlValidation(HyperlinkLastWeek, String.Empty)
            LiquidAgent.ControlValidation(HyperlinkMediaService, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkMediaService_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles HyperlinkMediaService.Click

        ' Prevent changes to the contract if it has already been signed.
        If DataObject.ParentContract.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If

        ' Stop if this is a product locator contract.
        If ProductLocatorContract Then
            CType(TopLevelControl, BaseForm).ShowMessage("This is a product locator contract. Other media services may not be used.", "Sorry, Can't Do That")
            Exit Sub
        End If

        ' Stop if the store list is not empty.
        If DataObject.StoreListSize > 0 Then
            CType(TopLevelControl, BaseForm).ShowMessage("The media service may not be modified if the store list is not empty.", "Sorry, Can't Do That")
            Exit Sub
        End If

        ' Check if the user is happy to have all related bursts removed if any exist, tehn do it.
        If RemoveRelatedBursts() = False Then
            Exit Sub
        End If

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupMedia.SelectRowsByDate _
                                                (My.Settings.DBConnection, False, Nothing, DataObject.FirstWeek, DataObject.LastWeek)

        ' Update the data object and the label with the selection.
        If IsNothing(SelectedItems) = False Then
            If SelectedItems.Count > 0 Then
                DataObject.SelectedMedia = SelectedItems(0)
                CurrentControl.DataBindings("Text").ReadValue()
                LabelTotalUniverseValue.DataBindings("Text").ReadValue()
                LabelStoresThatAllowMediaValue.DataBindings("Text").ReadValue()
                ' Reset the error text of the control to an empty string.
                LiquidAgent.ControlValidation(CurrentControl, String.Empty)

                ' Disable the crossover categories if product locator was selected.
                If DataObject.MediaName = PRODUCTLOCATORNAME Then
                    OriginalCrossoverQty = DataObject.CrossoverQty
                    OriginalCrossoverAdsQty = DataObject.AdsPerCrossover

                    ' Product locator contracts may only have a homesite.
                    ButtonAddCrossoverPreference.Enabled = False
                    If DataObject.CrossoverQty > 0 Then
                        CType(TopLevelControl, BaseForm).ShowMessage("This is a product locator contract. No crossover installations are " &
                                                                     "permitted. 'Crossovers To Install' has been set to zero.", "Product Locator Contract Adjustment")
                        DataObject.CrossoverQty = 0
                        TextEditCrossoverQty.DataBindings("EditValue").ReadValue()
                        TextEditCrossoverQty.Enabled = False
                    End If
                    If DataObject.AdsPerCrossover > 0 Then
                        DataObject.AdsPerCrossover = 0
                        TextEditAdsPerCrossover.DataBindings("EditValue").ReadValue()
                        TextEditAdsPerCrossover.Enabled = False
                    End If
                Else


                    Dim AvailableCategoriesData As BindingSource = Lookup.GetCategoriesByMediaService(My.Settings.DBConnection, Nothing, Nothing, DataObject.MediaID)
                    Dim canUseCategory As Boolean = False

                    For Each availableCategory As DataRowView In AvailableCategoriesData
                        If availableCategory("CategoryName") = DataObject.Homesite Then
                            canUseCategory = True
                            Exit For
                        End If

                    Next

                    If canUseCategory = False Then
                        'Dim NewHomesite As DataRow
                        Dim NewHomesite As DataRow = DataObject.BurstCategoryTable.NewRow
                        NewHomesite("CategoryName") = "Select..."
                        NewHomesite("CategoryID") = 0
                        DataObject.SelectedHomesite = NewHomesite
                    End If

                    ButtonAddCrossoverPreference.Enabled = True
                    DataObject.CrossoverQty = OriginalCrossoverQty
                    TextEditCrossoverQty.DataBindings("EditValue").ReadValue()
                    TextEditCrossoverQty.Enabled = True
                    DataObject.AdsPerCrossover = OriginalCrossoverAdsQty
                    TextEditAdsPerCrossover.DataBindings("EditValue").ReadValue()
                    TextEditAdsPerCrossover.Enabled = True
                End If

                'Enable the interaction Dates if it is an interaction media

                If DataObject.MediaName.Contains("Demonstration") Then
                    TabPageInteractionDates.PageVisible = True
                Else
                    TabPageInteractionDates.PageVisible = False
                End If
            End If
        End If
        If DataObject.IsPcaStatusMedia Then
            HyperlinkPcaStatus.Visible = True
            LabelPCAStatus.Visible = True
        Else
            HyperlinkPcaStatus.Visible = False
            LabelPCAStatus.Visible = False
        End If
        ' Reset the error text of the related control to an empty string.
        LiquidAgent.ControlValidation(TextEditInstallStoreQty, String.Empty)

        ' Refresh related data bound controls.
        LabelStoresThatAllowMedia.DataBindings("ForeColor").ReadValue()
        LabelStoresThatAllowMediaValue.DataBindings("ForeColor").ReadValue()

    End Sub

    Private Sub HyperlinkPcaStatus_Click(sender As Object, e As EventArgs) Handles HyperlinkPcaStatus.Click
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupPcaStatus.SelectRows _
        (My.Settings.DBConnection, False, Nothing)

        ' Update the data object and the label with the selection.
        If IsNothing(SelectedItems) = False Then
            If SelectedItems.Count > 0 Then
                DataObject.SelectedPcaStatus = SelectedItems(0)
                CurrentControl.DataBindings("Text").ReadValue()
                ' Reset the error text of the control to an empty string.
                LiquidAgent.ControlValidation(CurrentControl, String.Empty)
                'we need to add the row here

            End If
        End If
    End Sub

    Private Sub HyperlinkBrand_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles HyperlinkBrand.Click

        ' Prevent changes to the contract if it has already been signed.
        If DataObject.ParentContract.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If

        ' Stop if the store list is not empty.
        If DataObject.StoreListSize > 0 Then
            CType(TopLevelControl, BaseForm).ShowMessage("The brand may not be modified if the store list is not empty.", "Sorry, Can't Do That")
            Exit Sub
        End If

        ' Check if the user is happy to have all related bursts removed if any exist, tehn do it.
        If RemoveRelatedBursts() = False Then
            Exit Sub
        End If

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupBrand.SelectRowsByClient _
        (My.Settings.DBConnection, False, Nothing, DataObject.ClientID)

        ' Update the data object and the label with the selection.
        If IsNothing(SelectedItems) = False Then
            If SelectedItems.Count > 0 Then
                DataObject.SelectedBrand = SelectedItems(0)
                CurrentControl.DataBindings("Text").ReadValue()
                ' Reset the error text of the control to an empty string.
                LiquidAgent.ControlValidation(CurrentControl, String.Empty)
            End If
        End If

    End Sub

    Private Sub HyperlinkChain_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles HyperlinkChain.Click

        ' Prevent changes to the contract if it has already been signed.
        If DataObject.ParentContract.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If

        ' Stop if the store list is not empty.
        If DataObject.StoreListSize > 0 Then
            CType(TopLevelControl, BaseForm).ShowMessage("The chain may not be modified if the store list is not empty.", "Sorry, Can't Do That")
            Exit Sub
        End If

        ' Check if the user is happy to have all related bursts removed if any exist, tehn do it.
        If RemoveRelatedBursts() = False Then
            Exit Sub
        End If

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupChain.SelectRows(My.Settings.DBConnection, False, Nothing)

        ' Update the data object and the label with the selection.
        If IsNothing(SelectedItems) = False Then
            If SelectedItems.Count > 0 Then
                DataObject.SelectedChain = SelectedItems(0)
                CurrentControl.DataBindings("Text").ReadValue()
                LabelTitle.DataBindings("Text").ReadValue()
                LabelTotalUniverseValue.DataBindings("Text").ReadValue()
                LabelStoresThatAllowMediaValue.DataBindings("Text").ReadValue()
                ' Reset the error text of the control to an empty string.
                LiquidAgent.ControlValidation(CurrentControl, String.Empty)
            End If
        End If

        ' Reset the error text of the related control to an empty string.
        LiquidAgent.ControlValidation(TextEditInstallStoreQty, String.Empty)

        ' Refresh related data bound controls.
        LabelStoresThatAllowMedia.DataBindings("ForeColor").ReadValue()
        LabelStoresThatAllowMediaValue.DataBindings("ForeColor").ReadValue()

    End Sub

    Private Sub HyperlinkHomesite_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles HyperlinkHomesite.Click

        ' Prevent changes to the contract if it has already been signed.
        If DataObject.ParentContract.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If

        ' Stop if no media service has been selected.
        If String.Compare(HyperlinkMediaService.Text, "Select...") = 0 Then
            CType(TopLevelControl, BaseForm).ShowMessage _
            ("Please select a media service before selecting a homesite.", "Media Service Required")
            Exit Sub
        End If

        ' Stop if the store list is not empty.
        If DataObject.StoreListSize > 0 Then
            CType(TopLevelControl, BaseForm).ShowMessage _
            ("The homesite may not be modified if the store list is not empty.", "Sorry, Can't Do That")
            Exit Sub
        End If

        ' Check if the user is happy to have all related bursts removed if any exist, tehn do it.
        If RemoveRelatedBursts() = False Then
            Exit Sub
        End If

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupCategory.SelectRowsByMediaService _
        (My.Settings.DBConnection, False, Nothing, DataObject.MediaID)

        ' Update the data object and the label with the selection.
        If IsNothing(SelectedItems) = False Then
            If SelectedItems.Count > 0 Then
                DataObject.SelectedHomesite = SelectedItems(0)
                CurrentControl.DataBindings("Text").ReadValue()
                ' Reset the error text of the control to an empty string.
                LiquidAgent.ControlValidation(CurrentControl, String.Empty)
            End If
        End If

    End Sub

    Private Sub ButtonAddLoadingFee_Click _
    (ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonAddLoadingFee.Click

        ' Prevent changes to the contract if it has already been signed.
        If DataObject.ParentContract.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If


        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupLoadingFee.SelectRows _
        (My.Settings.DBConnection, True, GridLoadingFees)

        ' Update the data object and the label with the selection.
        If IsNothing(SelectedItems) = False Then
            If SelectedItems.Count > 0 Then

                ' Add the new loading fees to the dataset.
                DataObject.AddLoadingFees(SelectedItems)

                ' Refresh the displayed amount in the cell and other related controls.
                RefreshDisplayedLoadingFees()

            End If
        End If

    End Sub

    Private Sub GridLoadingFees_CellEndEdit _
    (ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles GridLoadingFees.CellEndEdit


        ' Update the underlying datarow with the new loading fee amount.
        UpdateLoadingFee(e.RowIndex)

        ' Refresh the displayed amount in the cell and other related controls.
        RefreshDisplayedLoadingFees()

    End Sub

    Private Sub GridLoadingFees_CellMouseDoubleClick _
    (ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) Handles GridLoadingFees.CellMouseDoubleClick

        If e.RowIndex > -1 Then
            ' The row double-clicked is NOT the column header row. Handle the event.
            GridLoadingFees.CurrentCell = GridLoadingFees("LoadingFeePercentageColumn", e.RowIndex)
            GridLoadingFees.BeginEdit(True)
        End If
    End Sub

    Private Sub ButtonRemoveLoadingFee_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonRemoveLoadingFee.Click

        ' Prevent changes to the contract if it has already been signed.
        If DataObject.ParentContract.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If
        DataObject.RemoveGridRows(GridLoadingFees)
        RefreshDisplayedLoadingFees()
    End Sub

    Private Sub ButtonAddAdditionalCategory_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonAddAdditionalCategory.Click

        ' Stop if the store list is not empty.
        If DataObject.StoreListSize > 0 Then
            CType(TopLevelControl, BaseForm).ShowMessage("Additional categories may not be added if the store list is not empty.", "Sorry, Can't Do That")
            Exit Sub
        End If

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupCategory.SelectRows _
        (My.Settings.DBConnection, True, DataObject.BurstCategoryTable)

        ' Pass the selection to the data object to add categories.
        If SelectedItems.Count > 0 Then
            DataObject.AddAdditionalCategories(SelectedItems)
        End If

    End Sub

    Private Sub ButtonRemoveAdditionalCategory_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonRemoveAdditionalCategory.Click

        DataObject.RemoveCategories(GridAdditionalCategories)

        ' Check if the user is happy to have all related bursts removed if any exist, tehn do it.
        If RemoveRelatedBursts() = False Then
            Exit Sub
        End If

    End Sub

    Private Sub ButtonAddCrossoverPreference_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonAddCrossoverPreference.Click

        ' Prevent changes to the contract if it has already been signed.
        If DataObject.ParentContract.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupCategory.SelectRows _
        (My.Settings.DBConnection, True, DataObject.BurstCategoryTable, True)

        ' Pass the selection to the data object to add categories.
        If SelectedItems.Count > 0 Then
            DataObject.AddCrossoverPreferences(SelectedItems)
        End If

    End Sub

    Private Sub ButtonRemoveCrossoverPreference_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonRemoveCrossoverPreference.Click

        ' Prevent changes to the contract if it has already been signed.
        If DataObject.ParentContract.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If
        DataObject.RemoveCategories(GridCrossoverPreferences)
    End Sub

    Private Sub ButtonIncreasePriority_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonIncreasePriority.Click

        ' Prevent changes to the contract if it has already been signed.
        If DataObject.ParentContract.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If
        DataObject.ChangeCrossoverPriority(GridCrossoverPreferences, Burst.PriorityChangeValue.Up)
    End Sub

    Private Sub ButtonDecreasePriority_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonDecreasePriority.Click

        ' Prevent changes to the contract if it has already been signed.
        If DataObject.ParentContract.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If
        DataObject.ChangeCrossoverPriority(GridCrossoverPreferences, Burst.PriorityChangeValue.Down)
    End Sub

    Private Sub GridRelatedBursts_SelectionChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles GridRelatedBursts.SelectionChanged
        DataObject.UpdatePeerRelativeFilter(sender)
    End Sub

    Private Sub ButtonAddBurst_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonAddBurst.Click

        ' Stop if the store list is not empty.
        If DataObject.StoreListNotEmpty Then
            CType(TopLevelControl, BaseForm).ShowMessage _
            ("The store pool sharing configuration may not be modified if the store list is not empty.",
            "Store List Not Empty", MessageBoxIcon.Error)
            Exit Sub
        End If

        ' Confirm user action.
        Dim RelativeCount As Integer = DataObject.RelatedBurstView.Count
        If RelativeCount > 0 Then
            Dim Response As DialogResult = CType(TopLevelControl, BaseForm).ShowMessage _
            ("This burst currently shares a store pool with " & RelativeCount.ToString & " other burst(s). If you proceed " _
            & "with this action this burst" & vbCrLf & "will be disconnected from the shared store pool and won't be able to share " _
            & "stores with any other burst." & vbCrLf & vbCrLf & "Are you sure you wish to continue?", "Don't Share the " _
            & "Store List?", MessageBoxButtons.YesNoCancel)
            If Not Response = DialogResult.Yes Then
                Exit Sub
            End If
        End If

        ' Define a list of columns to hide from the Burst selection grid.
        Dim ColumnsToHide As New List(Of String)
        ColumnsToHide.Add("ChainNameColumn")

        ' Use the updated dataview to get a selection from the user.
        Dim Filter As String = "StorePoolID <> '" & DataObject.StorePoolID.ToString & "'"
        Dim SelectableBursts As New DataView(DataObject.PeerBurstView.Table, Filter, String.Empty, DataViewRowState.CurrentRows)
        Dim SelectedPeers As List(Of DataRow) = LookupBurst.SelectPeers(SelectableBursts, True, ColumnsToHide)
        DataObject.AddRelatives(SelectedPeers)

    End Sub

    Private Sub ButtonRemoveBurst_Click(sender As System.Object, e As System.EventArgs) Handles ButtonRemoveBurst.Click

        ' Stop if the store list is not empty.
        If DataObject.StoreListNotEmpty Then
            CType(TopLevelControl, BaseForm).ShowMessage _
            ("The store pool sharing configuration may not be modified if the store list is not empty.",
            "Store List Not Empty", MessageBoxIcon.Error)
            Exit Sub
        End If

        ' Check if the user is happy to have all related bursts removed if any exist, tehn do it.
        If RemoveRelatedBursts() = False Then
            Exit Sub
        End If

        ' Reset the error text of the related control to an empty string.
        LiquidAgent.ControlValidation(TextEditInstallStoreQty, String.Empty)

    End Sub

    Private Sub TextEditInstallStoreQty_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles TextEditInstallStoreQty.EditValueChanged

        ' Create an object to represent the control.
        Dim CurrentControl As TextEdit = CType(sender, TextEdit)

        ' Reset the error text of the control to an empty string.
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)

        ' Update the color of labels LabelStoresThatAllowMedia and LabelStoresThatAllowMediaValue
        If LabelStoresThatAllowMedia.DataBindings.Count > 0 Then
            LabelStoresThatAllowMedia.DataBindings("ForeColor").ReadValue()
        End If
        If LabelStoresThatAllowMediaValue.DataBindings.Count > 0 Then
            LabelStoresThatAllowMediaValue.DataBindings("ForeColor").ReadValue()
        End If

    End Sub

    Private Sub TextEditStorePoolCapacity_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles TextEditStorePoolCapacity.EditValueChanged

        ' Create an object to represent the control.
        Dim CurrentControl As TextEdit = CType(sender, TextEdit)

        ' Reset the error text of the control to an empty string.
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)

        ' Update the color of labels LabelStoresThatAllowMedia and LabelStoresThatAllowMediaValue
        If LabelStoresThatAllowMedia.DataBindings.Count > 0 Then
            LabelStoresThatAllowMedia.DataBindings("ForeColor").ReadValue()
        End If
        If LabelStoresThatAllowMediaValue.DataBindings.Count > 0 Then
            LabelStoresThatAllowMediaValue.DataBindings("ForeColor").ReadValue()
        End If

    End Sub

    Private Sub TextEditProductName_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles TextEditProductName.EditValueChanged

        ' Create an object to represent the control.
        Dim CurrentControl As TextEdit = CType(sender, TextEdit)

        ' Reset the error text of the control to an empty string.
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)

    End Sub

    Private Sub TextEditAdsPerInstallation_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) _
        Handles TextEditAdsPerHomesite.EditValueChanged, TextEditAdsPerCrossover.EditValueChanged, TextEditAdsPerShelfTalk.EditValueChanged

        ' Create an object to represent the control.
        Dim CurrentControl As TextEdit = CType(sender, TextEdit)

        ' Reset the error text of the control to an empty string.
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)

    End Sub


    Private Sub LabelMin_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles LabelMin.Click
        DataObject.SetPoolStoreCapacityToMin()
        TextEditStorePoolCapacity.DataBindings("EditValue").ReadValue()
        LabelStoresThatAllowMediaValue.DataBindings("ForeColor").ReadValue()
        LabelStoresThatAllowMedia.DataBindings("ForeColor").ReadValue()
        LiquidAgent.ControlValidation(TextEditStorePoolCapacity, String.Empty)
    End Sub

    Private Sub LabelMax_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles LabelMax.Click
        DataObject.SetPoolStoreCapacityToMax()
        TextEditStorePoolCapacity.DataBindings("EditValue").ReadValue()
        LabelStoresThatAllowMediaValue.DataBindings("ForeColor").ReadValue()
        LabelStoresThatAllowMedia.DataBindings("ForeColor").ReadValue()
        LiquidAgent.ControlValidation(TextEditStorePoolCapacity, String.Empty)
    End Sub

    Private Sub GridRelatedBursts_RowsRemoved _
    (ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewRowsRemovedEventArgs) Handles GridRelatedBursts.RowsRemoved

        ' Set the store pool capacity equal to the InstallStoreQty if the grid is empty.
        If DataObject.RelatedBurstView.Count = 0 Then
            DataObject.StorePoolCapacity = DataObject.InstallStoreQty
            TextEditStorePoolCapacity.DataBindings("EditValue").ReadValue()
        End If
    End Sub

    Private Sub ButtonEditStoreList_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonEditStoreList.Click

        ' Stop if this is a bottom end burst.
        If DataObject.ChainTypeID = 2 Then
            CType(TopLevelControl, BaseForm).ShowMessage("You're working with a bottom end chain. Bottom end chains do not require store lists.", "Bottom End Chain Requires No Stores")
            Exit Sub
        End If

        ' Open the store list editor.
        FormEditStoreList.EditStoreList(DataObject, CheckEditConfirmed)
        LabelStoreListSizeValue.DataBindings("Text").ReadValue()

    End Sub

    Private Sub CheckEditConfirmed_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckEditConfirmed.CheckedChanged

        ' Display error message if contract isn't signed yet (the change won't stick because of the design of the data object).
        If DataObject.ParentContract.Signed = False Then
            CType(TopLevelControl, BaseForm).ShowMessage("Only signed contracts may have their store lists confirmed.", "When in Doubt, Mumble!")
            Exit Sub
        End If

    End Sub
    'Private Sub CheckEditInstallRegardless_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

    '    ' Display error message if contract isn't signed yet (the change won't stick because of the design of the data object).
    '    If DataObject.ParentContract.Signed Then
    '        Contract.ModifyingAfterSignatureNotPermitted(Me)
    '        Exit Sub
    '    End If


    'End Sub

    Private Sub HyperlinkInstallAtHomesite_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles HyperlinkInstallAtHomesite.Click

        ' Prevent changes to the contract if it has already been signed.
        If DataObject.ParentContract.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If

        ' Change hyperlink appearances to show which one is selected.
        SelectHyperlink(CType(sender, LabelControl), PictureInstallAtHomesite, True)
        SelectHyperlink(HyperlinkDontInstallAtHomesite, PictureDontInstallAtHomesite, False)
        DataObject.InstallAtHomesite = True

    End Sub


    Private Sub HyperlinkDontInstallAtHomesite_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles HyperlinkDontInstallAtHomesite.Click

        ' Prevent changes to the contract if it has already been signed.
        If DataObject.ParentContract.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If

        ' Check if the user is happy to have all related bursts removed if any exist, tehn do it.
        If RemoveRelatedBursts() = False Then
            Exit Sub
        End If

        ' Change hyperlink appearances to show which one is selected.
        SelectHyperlink(CType(sender, LabelControl), PictureDontInstallAtHomesite, True)
        SelectHyperlink(HyperlinkInstallAtHomesite, PictureInstallAtHomesite, False)
        DataObject.InstallAtHomesite = False
        TextEditAdsPerHomesite.EditValue = 0

    End Sub

#Region "Validation"

    Private Sub FinalValidation(ByVal sender As Object, ByVal e As System.EventArgs)

        ' Check if other bursts must have their store lists confirmed before this one.
        If DataObject.StoreListConfirmed AndAlso DataObject.Row("StoreListConfirmed") = False Then
            ' The user ticked the 'Confirmed' box, proceed to validate.
            Dim PriorityBursts As List(Of DataRow) = DataObject.BurstsWithHigherStoreClaimPriorityAwaitingStoreListConfirmation
            If PriorityBursts.Count > 0 Then
                ' Build a list of numbers of contracts that have bursts waiting to have their store lists
                ' confirmed before this burst.
                Dim ContractListBuilder As New System.Text.StringBuilder
                For Each PriorityBurst As DataRow In PriorityBursts
                    If Not ContractListBuilder.ToString.Contains(PriorityBurst("ContractNumber")) Then
                        If ContractListBuilder.Length > 0 Then
                            ContractListBuilder.AppendLine()
                        End If
                        ContractListBuilder.Append(PriorityBurst("ContractNumber"))
                    End If
                Next
                ' Display the list of contracts and the error to the user.
                Dim ErrorMessage As String = "The following contracts have bursts that need to have their store lists " _
                & "confirmed before you can confirm this burst's store list:"
                LiquidAgent.ControlValidation(CheckEditConfirmed, ErrorMessage & vbCrLf & ContractListBuilder.ToString)
            End If
        Else
            LiquidAgent.ControlValidation(CheckEditConfirmed, String.Empty)
        End If

        Dim ValidatedControl As LabelControl = CType(HyperlinkHomesite, LabelControl)

        ' Check for errors.
        If String.Compare(ValidatedControl.Text, "Select...") = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "A homesite must be selected.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

        If HasSelectedInstallRegardles = False Then
            Dim ErrorMessage As String = "You have to specify whether the installers should install regardless."
            LiquidAgent.ControlValidation(CheckEditInstallRegardless, ErrorMessage)
        Else
            LiquidAgent.ControlValidation(CheckEditInstallRegardless, String.Empty)
        End If

    End Sub

    Private Sub TextEditDiscount_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles TextEditDiscount.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If CDec(ValidatedControl.EditValue) > 100 Then
            LiquidAgent.ControlValidation(ValidatedControl, "The discount may not be greater than 100%.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub TextEditNetRental_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles TextEditNetRental.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)
        Dim UserIsAuthorisedToModifySignedContract As Boolean _
            = DataObject.ParentContract.UserMayModifySignedContract(DataObject.ParentContract.ContractID)

        ' Check for errors.
        If DataObject.ParentContract.Signed _
            AndAlso Not CDec(ValidatedControl.EditValue) = OriginalBurstValue _
            AndAlso UserIsAuthorisedToModifySignedContract = False Then
            LiquidAgent.ControlValidation(ValidatedControl, "The value of this burst was " & OriginalBurstValue.ToString("C2") _
                                          & " when the contract was signed. This may not change.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub TextEditCrossoverQty_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles TextEditCrossoverQty.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If CInt(ValidatedControl.EditValue) > GridCrossoverPreferences.Rows.Count Then
            LiquidAgent.ControlValidation(ValidatedControl, "The quantity of crossovers to install can't be greater than the quantity of crossover categories selected.")
        ElseIf CInt(ValidatedControl.EditValue) = 0 AndAlso GridCrossoverPreferences.Rows.Count > 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "The quantity of crossovers to install can't be zero if crossover preferences have been selected.")
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkBrand_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles HyperlinkBrand.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        If String.Compare(ValidatedControl.Text, "Select...") = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "A brand must be selected.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkChain_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles HyperlinkChain.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        If String.Compare(ValidatedControl.Text, "Select...") = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "A chain must be selected.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkMediaService_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles HyperlinkMediaService.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        If String.Compare(ValidatedControl.Text, "Select...") = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "A media service must be selected.")
        ElseIf DataObject.MediaAllowsHomesite = False AndAlso DataObject.InstallAtHomesite Then
            LiquidAgent.ControlValidation(ValidatedControl, "The selected media service may not be used as a homesite installation.")
        ElseIf DataObject.MediaAllowsCrossover = False AndAlso DataObject.CrossoverQty > 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "The selected media service may not be used as a crossover installation.")
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkHomesite_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles HyperlinkHomesite.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        If String.Compare(ValidatedControl.Text, "Select...") = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "A homesite must be selected.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub TextEditStorePoolCapacity_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles TextEditStorePoolCapacity.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        Dim ConflictingContracts As String = DataObject.ConflictingContracts
        If CInt(ValidatedControl.EditValue) < DataObject.MinStorePoolCapacity _
        Or CInt(ValidatedControl.EditValue) > DataObject.MaxStorePoolCapacity Then
            LiquidAgent.ControlValidation(ValidatedControl,
            "The capacity of the store pool must be between (and including) " & DataObject.MinStorePoolCapacity.ToString("#,###") _
            & " and " & DataObject.MaxStorePoolCapacity.ToString("#,###") & ".")
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub TextEditInstallStoreQty_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles TextEditInstallStoreQty.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        Dim ConflictingContracts As String = DataObject.ConflictingContracts
        If ConflictingContracts.Length > 0 Then
            ' This burst uses up more stores than are available in the media gap.
            LiquidAgent.ControlValidation(ValidatedControl,
            "Saving this burst with the specified store quantity would create a media gap conflict with the " _
            & "following contracts:" & vbCrLf & ConflictingContracts)
        ElseIf DataObject.ParentContract.Signed _
        AndAlso DataObject.StoresAllowingCurrentMediaService < CInt(ValidatedControl.EditValue) Then
            ' This burst uses up more stores than are available for the media service in the selected chain.
            LiquidAgent.ControlValidation(ValidatedControl,
            "The 'Total Stores' quantity may not be greater than the number of stores that allow the selected media service.")
        ElseIf DataObject.StoreListSize > CInt(ValidatedControl.EditValue) Then
            ' This burst's store list contains more stores than allowed by the InstallStoreQty property.
            LiquidAgent.ControlValidation(ValidatedControl,
            "The size of the store list exceeds the total store quantity specified.")
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub
    Private Sub CheckEditInstallRegardless_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles CheckEditInstallRegardless.Validated, CheckEditDoNotInstallRegardless.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As CheckEdit = CType(sender, CheckEdit)

        ' Check for errors.
        If String.IsNullOrEmpty(ValidatedControl.Text) Then
            LiquidAgent.ControlValidation(ValidatedControl,
            "A product name must be specified to assist installation teams.")
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub
    Private Sub TextEditProductName_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles TextEditProductName.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If String.IsNullOrEmpty(ValidatedControl.Text) Then
            LiquidAgent.ControlValidation(ValidatedControl,
            "A product name must be specified to assist installation teams.")
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub TextEditAdsPerInstallation_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles TextEditAdsPerHomesite.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If String.IsNullOrEmpty(ValidatedControl.Text) Then
            LiquidAgent.ControlValidation(ValidatedControl,
            "Please specify how many ads are required for the homesite.")
        ElseIf DataObject.InstallAtHomesite = True AndAlso ValidatedControl.EditValue = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl,
            "You've requested a homesite installation for this burst but failed to say how many ads are needed.")
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub TextEditAdsPerCrossover_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles TextEditAdsPerCrossover.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Get the values we need to test.
        Dim ThisControlValue As Integer = 0
        Dim CrossoverQty As Integer = 0
        If IsDBNull(ValidatedControl.EditValue) = False Then
            ThisControlValue = ValidatedControl.EditValue
        End If
        If IsDBNull(TextEditCrossoverQty.EditValue) = False Then
            CrossoverQty = TextEditCrossoverQty.EditValue
        End If

        ' Check for errors.
        If String.IsNullOrEmpty(ValidatedControl.Text) Then
            LiquidAgent.ControlValidation(ValidatedControl,
            "Please specify how many ads are required for each crossover.")
        ElseIf ThisControlValue > 0 AndAlso CrossoverQty = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl,
            "If you want ads installed as crossovers then you need to specify how many crossovers you need in the Categories tab.")
        ElseIf ThisControlValue = 0 AndAlso CrossoverQty > 0 Then
            LiquidAgent.ControlValidation(ValidatedControl,
            "You've requested crossovers for this burst but failed to say how many ads are needed for each crossover installation.")
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkLastWeek_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles HyperlinkLastWeek.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        If DataObject.ParentContract.Signed Then
            ' Get the current server time.
            Dim ServerTime As Date = Settings.GetServerTime(My.Settings.DBConnection, CType(TopLevelControl, BaseForm))
            ' Get the cut-off date for changing contract dates (one week prior to commencement).
            Dim CutOffDate As Date = DateAdd(DateInterval.WeekOfYear, -1, DataObject.ParentContract.FirstWeek)
            ' Check for errors.
            If CutOffDate.Date <= ServerTime.Date _
                AndAlso Not DateDiff(DateInterval.DayOfYear, DataObject.LastWeek, OriginalLastWeek) = 0 _
                AndAlso My.User.IsInRole("sales_manager") = False Then
                ' The contract is signed and the cut-off date is passed and the last week has been changed. This is not permitted.
                LiquidAgent.ControlValidation(ValidatedControl,
                                              "The last week of any burst in a signed contract may not be changed once billing has started.")
                Exit Sub
            End If
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

#End Region

#End Region

#Region "Public Methods"

    Public Sub New(ByVal AddNew As Boolean, ByVal ParentContract As OldContract)

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        DataObject = New Burst(ParentContract, AddNew, My.Settings.DBConnection)

    End Sub

#End Region

#Region "Private Methods"

    Private Function RemoveRelatedBursts() As Boolean

        ' Confirm user action.
        Dim RelativeCount As Integer = DataObject.RelatedBurstView.Count
        If RelativeCount > 0 Then
            Dim Response As DialogResult = CType(TopLevelControl, BaseForm).ShowMessage _
            ("This burst currently shares a store pool with " & RelativeCount.ToString & " other burst(s). If you proceed " _
            & "with this action this burst" & vbCrLf & "will be disconnected from the shared store pool and won't be able to share " _
            & "stores with any other burst." & vbCrLf & vbCrLf & "Are you sure you wish to continue?", "Don't Share the " _
            & "Store List?", MessageBoxButtons.YesNoCancel)
            If Not Response = DialogResult.Yes Then
                Return False
            End If
        End If

        ' Leave the store sharing pool.
        DataObject.RemoveAllRelatives()

        Return True

    End Function

    Private Sub InitializeControls()

        ' Homesite installation options hyperlinks.
        If DataObject.InstallAtHomesite Then
            SelectHyperlink(HyperlinkInstallAtHomesite, PictureInstallAtHomesite, True)
            SelectHyperlink(HyperlinkDontInstallAtHomesite, PictureDontInstallAtHomesite, False)
        Else
            SelectHyperlink(HyperlinkDontInstallAtHomesite, PictureDontInstallAtHomesite, True)
            SelectHyperlink(HyperlinkInstallAtHomesite, PictureInstallAtHomesite, False)
        End If

        SelectHyperlink(HyperlinkApplyInstuctionsOnAllBursts, PictureApplyInstuctionsOnAllBursts, If((DataObject.ApplyInstructionsAcrossAllBursts), True, False))

        HyperlinkPcaStatus.Enabled = True

        If DataObject.IsPcaStatusMedia Then
            HyperlinkPcaStatus.Visible = True
            LabelPCAStatus.Visible = True
        Else
            HyperlinkPcaStatus.Visible = False
            LabelPCAStatus.Visible = False
        End If


    End Sub

    Private Sub SelectHyperlink(ByRef Hyperlink As LabelControl, ByRef Picture As PictureEdit, ByVal Selected As Boolean)

        ' Disable the clicked hyperlink.
        LiquidAgent.EnableHyperlink(Hyperlink, Not Selected)

        ' Display the image to indicate that it is currently selected.
        Picture.Visible = Selected

    End Sub

    Private Sub PerformDataBinding()

        LabelTitle.DataBindings.Add("Text", DataObject, "BurstTitle")

        ' Summary Page
        HyperlinkChain.DataBindings.Add("Text", DataObject, "ChainName", False, DataSourceUpdateMode.Never)
        HyperlinkMediaService.DataBindings.Add("Text", DataObject, "MediaName", False, DataSourceUpdateMode.Never)
        HyperlinkBrand.DataBindings.Add("Text", DataObject, "BrandName", False, DataSourceUpdateMode.Never)
        HyperlinkFirstWeek.DataBindings.Add("Text", DataObject, "FirstWeek", True, DataSourceUpdateMode.Never, 0, "d MMMM yyyy")
        HyperlinkLastWeek.DataBindings.Add("Text", DataObject, "LastWeek", True, DataSourceUpdateMode.Never, 0, "d MMMM yyyy")
        HyperlinkPcaStatus.DataBindings.Add("Text", DataObject, "PcaStatusName", False, DataSourceUpdateMode.Never)

        TextEditInstallWeeks.DataBindings.Add("EditValue", DataObject, "InstallWeeks", False, DataSourceUpdateMode.OnPropertyChanged)
        TextEditInstallStoreQty.DataBindings.Add("EditValue", DataObject, "InstallStoreQty", False, DataSourceUpdateMode.OnPropertyChanged)
        TextEditBillableWeeks.DataBindings.Add("EditValue", DataObject, "BillableWeeks", False, DataSourceUpdateMode.OnPropertyChanged)
        TextEditBillableStoreQty.DataBindings.Add("EditValue", DataObject, "BillableStoreQty", False, DataSourceUpdateMode.OnPropertyChanged)
        TextEditDiscount.DataBindings.Add("EditValue", DataObject, "Discount", False, DataSourceUpdateMode.OnPropertyChanged)
        TextEditFreeWeeks.DataBindings.Add("EditValue", DataObject, "FreeWeeks", False, DataSourceUpdateMode.OnPropertyChanged)
        TextEditFreeStoreQty.DataBindings.Add("EditValue", DataObject, "FreeStoreQty", False, DataSourceUpdateMode.OnPropertyChanged)

        TextEditRentalRate.DataBindings.Add("EditValue", DataObject, "RentalRate", False, DataSourceUpdateMode.OnPropertyChanged)
        TextEditRentalAmount.DataBindings.Add("EditValue", DataObject, "RentalAmount", False, DataSourceUpdateMode.OnPropertyChanged)
        TextEditLoadingFees.DataBindings.Add("EditValue", DataObject, "LoadingFees", False, DataSourceUpdateMode.OnPropertyChanged)
        TextEditDiscountAmount.DataBindings.Add("EditValue", DataObject, "DiscountAmount", False, DataSourceUpdateMode.OnPropertyChanged)
        TextEditNetRental.DataBindings.Add("EditValue", DataObject, "NetRental", False, DataSourceUpdateMode.OnPropertyChanged)

        LabelTotalUniverseValue.DataBindings.Add("Text", DataObject, "StoresInUniverse", False, DataSourceUpdateMode.Never)
        LabelStoresThatAllowMediaValue.DataBindings.Add("Text", DataObject, "StoresAllowingCurrentMediaService", False, DataSourceUpdateMode.Never)
        LabelStoresThatAllowMedia.DataBindings.Add("ForeColor", DataObject, "StoresAllowingCurrentMediaServiceLabelColor", False, DataSourceUpdateMode.Never)
        LabelStoresThatAllowMediaValue.DataBindings.Add("ForeColor", DataObject, "StoresAllowingCurrentMediaServiceLabelColor", False, DataSourceUpdateMode.Never)

        ' Other Pages
        TextEditCrossoverQty.DataBindings.Add("EditValue", DataObject, "CrossoverQty", False, DataSourceUpdateMode.OnPropertyChanged)
        MemoEditInstallationInstructions.DataBindings.Add("EditValue", DataObject, "InstallationInstructions", False, DataSourceUpdateMode.OnPropertyChanged)
        HyperlinkHomesite.DataBindings.Add("Text", DataObject, "Homesite", False, DataSourceUpdateMode.Never)
        LabelLoadingFeeTotalValue.DataBindings.Add("Text", DataObject, "LoadingFees", True, DataSourceUpdateMode.Never, 0, "c")
        TextEditStorePoolCapacity.DataBindings.Add("EditValue", DataObject, "StorePoolCapacity", False, DataSourceUpdateMode.OnPropertyChanged)
        LabelStoreListSizeValue.DataBindings.Add("Text", DataObject, "StoreListSize", False, DataSourceUpdateMode.Never)
        CheckEditConfirmed.DataBindings.Add("EditValue", DataObject, "StoreListConfirmed", False, DataSourceUpdateMode.OnPropertyChanged)
        TextEditProductName.DataBindings.Add("EditValue", DataObject, "ProductName", False, DataSourceUpdateMode.OnPropertyChanged)
        TextEditAdsPerHomesite.DataBindings.Add("EditValue", DataObject, "AdsPerInstallation", False, DataSourceUpdateMode.OnPropertyChanged)
        TextEditAdsPerCrossover.DataBindings.Add("EditValue", DataObject, "AdsPerCrossover", False, DataSourceUpdateMode.OnPropertyChanged)
        TextEditAdsPerShelfTalk.DataBindings.Add("EditValue", DataObject, "AdsPerShelfTalk", False, DataSourceUpdateMode.OnPropertyChanged)
        'CheckEditDoNotInstallRegardless.DataBindings.Add("EditValue", DataObject, "InstallRegardlessOfStock", False, DataSourceUpdateMode.Never)
        CheckEditInstallRegardless.DataBindings.Add("EditValue", DataObject, "InstallRegardlessOfStock", False, DataSourceUpdateMode.OnPropertyChanged)
        ' Grids
        GridLoadingFees.AutoGenerateColumns = False
        GridLoadingFees.DataSource = DataObject.BurstLoadingFeeTable
        GridAdditionalCategories.AutoGenerateColumns = False
        GridAdditionalCategories.DataSource = DataObject.AdditionalCategoryView
        GridCrossoverPreferences.AutoGenerateColumns = False
        GridCrossoverPreferences.DataSource = DataObject.CrossoverPreferenceView
        GridRelatedBursts.AutoGenerateColumns = False
        GridRelatedBursts.DataSource = DataObject.RelatedBurstView
        GridInstallationDays.AutoGenerateColumns = False
        GridInstallationDays.DataSource = DataObject.BurstInstallationDayTable

        DataBindingCompleted = True

    End Sub

    Protected Overrides Function Save() As Boolean

        DataObject.SaveToDataSet()

        Dim ErrorMessage As String = String.Empty
        Dim ContractID As Guid

        Dim prompDefaultBillingInstructions As Boolean = DataObject.prompSaveDefaultBillingInstructions(My.Settings.DBConnection, ContractID)

        If prompDefaultBillingInstructions = True Then
            Dim Response As DialogResult = CType(TopLevelControl, BaseForm).ShowMessage _
                       ("Nova has detected that you have burst(s) but do not have Billing Instructions. Would you like Default Billing Instructions to be created for you?",
                       "Create Default Billing Instructions?", MessageBoxButtons.YesNoCancel)

            If Response = DialogResult.Yes Then

                Dim Arguments(1) As Object
                Arguments(0) = prompDefaultBillingInstructions
                Arguments(1) = ContractID

                ' Run the process billing instructions on the background.
                BackgroundGenerator.RunWorkerAsync(Arguments)

                ' Create and display a  a friendly message for the user.
                WaitForm = New FormPleaseWait("creating billing instructions...")
                WaitForm.ShowDialog()

            End If
        End If

        Return True
    End Function


    Private Sub BackgroundGenerator_DoWork(ByVal sender As Object, ByVal e As System.ComponentModel.DoWorkEventArgs) Handles BackgroundGenerator.DoWork

        ' Define a string to contain possible errors when executing SQL server commands.
        Dim Errors As String = String.Empty

        ' Reconstruct the arguments that were passed to the background worker.
        Dim Arguments() As Object = e.Argument

        Dim canCreateBillingInstructions As Integer = If((CType(Arguments(0), Boolean)), 1, 0)
        Dim ContractID As Guid = CType(Arguments(0), Guid)

        If canCreateBillingInstructions = True Then

            System.Threading.Thread.Sleep(4000)
            ' Build a SQL command to generate default Billing Instructions.
            Dim SPCommand As String = $"Sales.spDefaultBillingInstructions '{ContractID}'"
            ' Execute the command.
            LiquidAgent.GetSqlScalarValue(My.Settings.DBConnection, SPCommand, Errors)
        End If

        ' Check if any errors occured.
        If Not String.IsNullOrEmpty(Errors) Then
            ' Errors occured. Remember them in the Result property of the worker.
            e.Result = Errors
        End If

    End Sub

    Private Sub BackgroundGenerator_RunWorkerCompleted(ByVal sender As Object, ByVal e As System.ComponentModel.RunWorkerCompletedEventArgs) Handles BackgroundGenerator.RunWorkerCompleted

        ' Kill the friendly "please wait" form.
        WaitForm.Dispose()

        ' Check if any errors occured.
        Dim Errors As String = String.Empty
        If Not IsNothing(e.Result) Then
            Errors = CStr(e.Result)
        End If

        If Not String.IsNullOrEmpty(Errors) Then
            ' Errors occured. Display them to the user.
            MessageBox.Show("Random catalysts ionized the vortex." & vbCrLf & vbCrLf _
            & Errors, "I Smell Something Burning", MessageBoxButtons.OK, MessageBoxIcon.Error)
        Else
            ' No errors occured. Update the labels.
            MessageBox.Show("Billing Instructions were succesfully created." & vbCrLf & vbCrLf _
            & Errors, "Default Billing Instructions", MessageBoxButtons.OK, MessageBoxIcon.Information)
        End If

    End Sub



    Private Sub UpdateAllLoadingFees()
        For Each gridrow As DataGridViewRow In GridLoadingFees.Rows
            UpdateLoadingFee(gridrow.Index)
        Next
    End Sub

    Private Sub UpdateLoadingFee(rowindex As Integer)
        Dim LoadingFeeRow As DataRow = CType(GridLoadingFees.Rows(rowindex).DataBoundItem, DataRowView).Row
        LoadingFeeRow("LoadingFeeAmount") = CDec(LoadingFeeRow("Percentage")) / 100 * DataObject.RentalAmount
    End Sub

    Private Sub RefreshDisplayedLoadingFees()
        ' Refresh the displayed amount in the cell and other related controls.
        If DataBindingCompleted Then
            GridLoadingFees.Refresh()
            LabelLoadingFeeTotalValue.DataBindings("Text").ReadValue()
            TextEditLoadingFees.DataBindings("EditValue").ReadValue()
            TextEditDiscountAmount.DataBindings("EditValue").ReadValue()
            TextEditNetRental.DataBindings("EditValue").ReadValue()
        End If
    End Sub

    Private Sub DisplayWarning(ByVal Message As String)

        ' Set the text of the warning message and make it visible.
        LabelWarningMessage.Text = Message.ToUpper
        LabelWarningMessage.Visible = True

    End Sub

    Private Sub DisableControlsAfterSignature()

        ' Prevent changes to the contract if it has already been signed.
        If DataObject.ParentContract.Signed Then
            TextEditInstallWeeks.Enabled = False
            TextEditCrossoverQty.Enabled = False
        End If

    End Sub
    Private Sub SetInstallRegardless()
        If String.Compare(DataObject.ChainName, "Select...") = 0 Then
            'it should not check either
        Else
            If DataObject.ParentContract.Signed Then
                HasSelectedInstallRegardles = True
            Else
                If DataObject.InstallRegardlessOfStock Then
                    CheckEditInstallRegardless.Checked = True
                    HasSelectedInstallRegardles = True
                Else
                    HasSelectedInstallRegardles = True
                    CheckEditDoNotInstallRegardless.Checked = True
                End If
            End If
        End If
    End Sub

    Private Sub SetupTabPageInteractionDates()
        If String.Compare(DataObject.MediaName, "Select...") = 0 Then
            'it should not check either
            TabPageInteractionDates.Visible = False
        Else
            If DataObject.MediaName.Contains("Demonstration") Then
                TabPageInteractionDates.PageVisible = True
            Else
                TabPageInteractionDates.PageVisible = False
            End If
        End If
    End Sub

    Private Sub SetProductLocatorContractPropertyValue()

        ' Check whether this is a product locator contract or not, then set the form property if necessary.
        ' First check if this burst's parent contract has any other bursts.
        Dim BurstCount As Integer = DataObject.ParentContract.BurstBindingSource.List.Count
        If BurstCount > 0 Then
            For i As Integer = 0 To BurstCount - 1
                Dim SiblingBurst As DataRow = CType(DataObject.ParentContract.BurstBindingSource.Item(i), DataRowView).Row
                If Not SiblingBurst("BurstID") = DataObject.Row("BurstID") Then
                    ' Other bursts exist besides this one. Check if the Product Locator media service was selected in the other bursts.
                    If SiblingBurst("MediaName") = PRODUCTLOCATORNAME Then
                        ' Product locator was selected in other bursts. Set the property to true.
                        ProductLocatorContract = True
                        ' Product locator contracts may only have a homesite.
                        ButtonAddCrossoverPreference.Enabled = False
                        Exit Sub
                    End If
                End If
            Next
        End If

    End Sub

    Private Sub CheckEditInstall_CheckedChanged(sender As Object, e As EventArgs) Handles CheckEditInstallRegardless.Click
        If DataObject.ParentContract.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If
        HasSelectedInstallRegardles = True
    End Sub

    Private Sub CheckEditDoNotInstallRegardless_CheckedChanged(sender As Object, e As EventArgs) Handles CheckEditDoNotInstallRegardless.Click
        If DataObject.ParentContract.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If
        HasSelectedInstallRegardles = True
    End Sub

    Private Sub HyperlinkApplyInstuctionsOnAllBursts_Click(sender As Object, e As EventArgs) Handles HyperlinkApplyInstuctionsOnAllBursts.Click

        Dim Response As DialogResult = CType(TopLevelControl, BaseForm).ShowMessage _
            ("Are you sure you want to apply the instructions on all the other related bursts? Doing so will replace the instructions (everything) in the other bursts with this one.",
            "Apply Instructions?", MessageBoxButtons.YesNoCancel)
        If Response = DialogResult.Yes Then
            SelectHyperlink(HyperlinkApplyInstuctionsOnAllBursts, PictureApplyInstuctionsOnAllBursts, True)
            DataObject.ApplyInstructionsAcrossAllBursts = True
        End If
    End Sub



    Private Sub HyperlinkApplyDatesAccrossBursts_Click(sender As Object, e As EventArgs) Handles HyperlinkApplyDatesAccrossBursts.Click
        Dim Response As DialogResult = CType(TopLevelControl, BaseForm).ShowMessage _
          ("Are you sure you want to apply the dates on all the other related bursts? Doing so will replace the dates (everything) in the other bursts with this one.",
          "Apply Dates?", MessageBoxButtons.YesNoCancel)
        If Response = DialogResult.Yes Then
            SelectHyperlink(HyperlinkApplyDatesAccrossBursts, PictureApplyDatesOnAllBursts, True)
            DataObject.ApplyDatesAcrossAllBursts = True
        End If
    End Sub

    Private Sub ButtonAddInstallationDay_Click(sender As Object, e As EventArgs) Handles ButtonAddInstallationDay.Click
        If DataObject.ParentContract.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If


        Dim SelectedItems As List(Of DataRow) = LookupInstallationDates.SelectRows _
        (My.Settings.DBConnection, True, GridInstallationDays)

        ' Pass the selection to the data object to add categories.
        If SelectedItems.Count > 0 Then
            DataObject.InstallationDays(SelectedItems)
        End If
    End Sub

    Private Sub ButtonDeleteInstallationDay_Click(sender As Object, e As EventArgs) Handles ButtonDeleteInstallationDay.Click
        If DataObject.ParentContract.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If
        DataObject.RemoveInstallationDays(GridInstallationDays)
    End Sub




#End Region

End Class

