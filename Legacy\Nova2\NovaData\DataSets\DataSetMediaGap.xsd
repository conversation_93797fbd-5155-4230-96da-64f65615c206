<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DataSetMediaGap" targetNamespace="http://tempuri.org/DataSetMediaGap.xsd" xmlns:mstns="http://tempuri.org/DataSetMediaGap.xsd" xmlns="http://tempuri.org/DataSetMediaGap.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="DBConnection" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Public" Name="DBConnection (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.NovaData.My.MySettings.GlobalReference.Default.DBConnection" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="BurstDataTableAdapter" GeneratorDataComponentClassName="BurstDataTableAdapter" Name="BurstData" UserDataComponentName="BurstDataTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.dbo.udfMediaGapGridContractData" DbObjectType="Function" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT        ContractID, ContractNumber, ChainName, MediaName, InstallStoreQty, FirstWeek, LastInstallWeek, ProductName, BrandName, CreatedBy, CreationDate, Homesite, 
                         StorePoolID, StorePoolCapacity
FROM            dbo.udfMediaGapGridContractData(@FromDate, @ToDate, @ChainIDList, @CategoryIDList, @MediaFamilyIDList) AS MediaGapGridContractData</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="FromDate" ColumnName="" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@FromDate" Precision="0" Scale="0" Size="0" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ToDate" ColumnName="" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@ToDate" Precision="0" Scale="0" Size="0" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ChainIDList" ColumnName="" DataSourceName="" DataTypeServer="nvarchar(6000)" DbType="String" Direction="Input" ParameterName="@ChainIDList" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="CategoryIDList" ColumnName="" DataSourceName="" DataTypeServer="nvarchar(6000)" DbType="String" Direction="Input" ParameterName="@CategoryIDList" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaFamilyIDList" ColumnName="" DataSourceName="" DataTypeServer="nvarchar(6000)" DbType="String" Direction="Input" ParameterName="@MediaFamilyIDList" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ContractID" DataSetColumn="ContractID" />
              <Mapping SourceColumn="ContractNumber" DataSetColumn="ContractNumber" />
              <Mapping SourceColumn="ChainName" DataSetColumn="ChainName" />
              <Mapping SourceColumn="MediaName" DataSetColumn="MediaName" />
              <Mapping SourceColumn="InstallStoreQty" DataSetColumn="InstallStoreQty" />
              <Mapping SourceColumn="FirstWeek" DataSetColumn="FirstWeek" />
              <Mapping SourceColumn="LastInstallWeek" DataSetColumn="LastWeek" />
              <Mapping SourceColumn="ProductName" DataSetColumn="ProductName" />
              <Mapping SourceColumn="BrandName" DataSetColumn="BrandName" />
              <Mapping SourceColumn="CreatedBy" DataSetColumn="CreatedBy" />
              <Mapping SourceColumn="CreationDate" DataSetColumn="CreationDate" />
              <Mapping SourceColumn="Homesite" DataSetColumn="Homesite" />
              <Mapping SourceColumn="StorePoolID" DataSetColumn="StorePoolID" />
              <Mapping SourceColumn="StorePoolCapacity" DataSetColumn="StorePoolCapacity" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ProvisionalBookingTableAdapter" GeneratorDataComponentClassName="ProvisionalBookingTableAdapter" Name="ProvisionalBooking" UserDataComponentName="ProvisionalBookingTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.dbo.udfMediaGapGridProvisionalBookingData" DbObjectType="Function" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>DELETE FROM Sales.ProvisionalBooking
WHERE        (ProvisionalBookingID = @Original_ProvisionalBookingID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_ProvisionalBookingID" ColumnName="ProvisionalBookingID" DataSourceName="NovaDB.Sales.ProvisionalBooking" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@Original_ProvisionalBookingID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ProvisionalBookingID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO Sales.ProvisionalBooking
                         (ProvisionalBookingID, ProvisionalBookingName, ChainID, CategoryID, MediaFamilyID, BrandID, FirstWeek, Duration, BookTime, ExpiryTime)
VALUES        (@ProvisionalBookingID,@ProvisionalBookingName,@ChainID,@CategoryID,@MediaFamilyID,@BrandID,@FirstWeek,@Duration,@BookTime,@ExpiryTime); 
SELECT ProvisionalBookingID, ProvisionalBookingName, ChainID, CategoryID, MediaFamilyID, BrandID, FirstWeek, Duration FROM Sales.ProvisionalBooking WHERE (ProvisionalBookingID = @ProvisionalBookingID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ProvisionalBookingID" ColumnName="ProvisionalBookingID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ProvisionalBookingID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ProvisionalBookingID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ProvisionalBookingName" ColumnName="ProvisionalBookingName" DataSourceName="" DataTypeServer="nvarchar(250)" DbType="String" Direction="Input" ParameterName="@ProvisionalBookingName" Precision="0" ProviderType="NVarChar" Scale="0" Size="250" SourceColumn="ProvisionalBookingName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ChainID" ColumnName="ChainID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ChainID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ChainID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="CategoryID" ColumnName="CategoryID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaFamilyID" ColumnName="MediaFamilyID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaFamilyID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaFamilyID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="BrandID" ColumnName="BrandID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="BrandID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="FirstWeek" ColumnName="FirstWeek" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@FirstWeek" Precision="0" ProviderType="DateTime" Scale="0" Size="8" SourceColumn="FirstWeek" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Duration" ColumnName="Duration" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@Duration" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="Duration" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="BookTime" ColumnName="BookTime" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@BookTime" Precision="0" ProviderType="DateTime" Scale="0" Size="8" SourceColumn="BookTime" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ExpiryTime" ColumnName="ExpiryTime" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@ExpiryTime" Precision="0" ProviderType="DateTime" Scale="0" Size="8" SourceColumn="ExpiryTime" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT        ProvisionalBookingID, ProvisionalBookingName, MediaFamilyName, CategoryName, ChainName, BrandName, FirstWeek, LastWeek, Weeks AS Duration, 
                         BookedBy, BookTime, ExpiryTime, MediaFamilyID, CategoryID, ChainID, BrandID
FROM            dbo.udfMediaGapGridProvisionalBookingData(@FromDate, @ToDate, @ChainIDList, @CategoryIDList, @MediaFamilyIDList) 
                         AS MediaGapGridProvisionalBookingData
WHERE        (ExpiryTime &gt; @CurrentTime)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="FromDate" ColumnName="" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@FromDate" Precision="0" Scale="0" Size="0" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ToDate" ColumnName="" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@ToDate" Precision="0" Scale="0" Size="0" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ChainIDList" ColumnName="" DataSourceName="" DataTypeServer="nvarchar(6000)" DbType="String" Direction="Input" ParameterName="@ChainIDList" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="CategoryIDList" ColumnName="" DataSourceName="" DataTypeServer="nvarchar(6000)" DbType="String" Direction="Input" ParameterName="@CategoryIDList" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaFamilyIDList" ColumnName="" DataSourceName="" DataTypeServer="nvarchar(6000)" DbType="String" Direction="Input" ParameterName="@MediaFamilyIDList" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="CurrentTime" ColumnName="ExpiryTime" DataSourceName="NovaDB.dbo.udfMediaGapGridProvisionalBookingData" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@CurrentTime" Precision="0" ProviderType="DateTime" Scale="0" Size="8" SourceColumn="ExpiryTime" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE       Sales.ProvisionalBooking
SET                ProvisionalBookingID = @ProvisionalBookingID, ProvisionalBookingName = @ProvisionalBookingName, ChainID = @ChainID, CategoryID = @CategoryID, 
                         MediaFamilyID = @MediaFamilyID, BrandID = @BrandID, FirstWeek = @FirstWeek, Duration = @Duration, BookTime = @BookTime, 
                         ExpiryTime = @ExpiryTime
WHERE        (ProvisionalBookingID = @Original_ProvisionalBookingID) AND (ProvisionalBookingName = @Original_ProvisionalBookingName) AND 
                         (ChainID = @Original_ChainID) AND (CategoryID = @Original_CategoryID) AND (MediaFamilyID = @Original_MediaFamilyID) AND (BrandID = @Original_BrandID) 
                         AND (FirstWeek = @Original_FirstWeek) AND (Duration = @Original_Duration);  
SELECT ProvisionalBookingID, ProvisionalBookingName, ChainID, CategoryID, MediaFamilyID, BrandID, FirstWeek, Duration FROM Sales.ProvisionalBooking WHERE (ProvisionalBookingID = @ProvisionalBookingID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ProvisionalBookingID" ColumnName="ProvisionalBookingID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ProvisionalBookingID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ProvisionalBookingID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ProvisionalBookingName" ColumnName="ProvisionalBookingName" DataSourceName="" DataTypeServer="nvarchar(250)" DbType="String" Direction="Input" ParameterName="@ProvisionalBookingName" Precision="0" ProviderType="NVarChar" Scale="0" Size="250" SourceColumn="ProvisionalBookingName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ChainID" ColumnName="ChainID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ChainID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ChainID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="CategoryID" ColumnName="CategoryID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaFamilyID" ColumnName="MediaFamilyID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaFamilyID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaFamilyID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="BrandID" ColumnName="BrandID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="BrandID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="FirstWeek" ColumnName="FirstWeek" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@FirstWeek" Precision="0" ProviderType="DateTime" Scale="0" Size="8" SourceColumn="FirstWeek" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Duration" ColumnName="Duration" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@Duration" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="Duration" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="BookTime" ColumnName="BookTime" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@BookTime" Precision="0" ProviderType="DateTime" Scale="0" Size="8" SourceColumn="BookTime" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ExpiryTime" ColumnName="ExpiryTime" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@ExpiryTime" Precision="0" ProviderType="DateTime" Scale="0" Size="8" SourceColumn="ExpiryTime" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_ProvisionalBookingID" ColumnName="ProvisionalBookingID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@Original_ProvisionalBookingID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ProvisionalBookingID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_ProvisionalBookingName" ColumnName="ProvisionalBookingName" DataSourceName="" DataTypeServer="nvarchar(250)" DbType="String" Direction="Input" ParameterName="@Original_ProvisionalBookingName" Precision="0" ProviderType="NVarChar" Scale="0" Size="250" SourceColumn="ProvisionalBookingName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_ChainID" ColumnName="ChainID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@Original_ChainID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ChainID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_CategoryID" ColumnName="CategoryID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@Original_CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_MediaFamilyID" ColumnName="MediaFamilyID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@Original_MediaFamilyID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaFamilyID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_BrandID" ColumnName="BrandID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@Original_BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="BrandID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_FirstWeek" ColumnName="FirstWeek" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@Original_FirstWeek" Precision="0" ProviderType="DateTime" Scale="0" Size="8" SourceColumn="FirstWeek" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_Duration" ColumnName="Duration" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@Original_Duration" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="Duration" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ProvisionalBookingID" DataSetColumn="ProvisionalBookingID" />
              <Mapping SourceColumn="ProvisionalBookingName" DataSetColumn="ProvisionalBookingName" />
              <Mapping SourceColumn="ChainID" DataSetColumn="ChainID" />
              <Mapping SourceColumn="CategoryID" DataSetColumn="CategoryID" />
              <Mapping SourceColumn="MediaFamilyID" DataSetColumn="MediaFamilyID" />
              <Mapping SourceColumn="BrandID" DataSetColumn="BrandID" />
              <Mapping SourceColumn="FirstWeek" DataSetColumn="FirstWeek" />
              <Mapping SourceColumn="MediaFamilyName" DataSetColumn="MediaFamilyName" />
              <Mapping SourceColumn="CategoryName" DataSetColumn="CategoryName" />
              <Mapping SourceColumn="ChainName" DataSetColumn="ChainName" />
              <Mapping SourceColumn="BrandName" DataSetColumn="BrandName" />
              <Mapping SourceColumn="LastWeek" DataSetColumn="LastWeek" />
              <Mapping SourceColumn="BookedBy" DataSetColumn="BookedBy" />
              <Mapping SourceColumn="BookTime" DataSetColumn="BookTime" />
              <Mapping SourceColumn="ExpiryTime" DataSetColumn="ExpiryTime" />
              <Mapping SourceColumn="Duration" DataSetColumn="Duration" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DataSetMediaGap" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_DataSetName="DataSetMediaGap" msprop:Generator_UserDSName="DataSetMediaGap">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="BurstData" msprop:Generator_UserTableName="BurstData" msprop:Generator_RowEvArgName="BurstDataRowChangeEvent" msprop:Generator_TableVarName="tableBurstData" msprop:Generator_TablePropName="BurstData" msprop:Generator_RowDeletingName="BurstDataRowDeleting" msprop:Generator_RowChangingName="BurstDataRowChanging" msprop:Generator_RowDeletedName="BurstDataRowDeleted" msprop:Generator_TableClassName="BurstDataDataTable" msprop:Generator_RowChangedName="BurstDataRowChanged" msprop:Generator_RowEvHandlerName="BurstDataRowChangeEventHandler" msprop:Generator_RowClassName="BurstDataRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ContractID" msdata:DataType="System.Guid, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnContractID" msprop:Generator_ColumnPropNameInRow="ContractID" msprop:Generator_ColumnPropNameInTable="ContractIDColumn" msprop:Generator_UserColumnName="ContractID" type="xs:string" />
              <xs:element name="ContractNumber" msprop:Generator_ColumnVarNameInTable="columnContractNumber" msprop:Generator_ColumnPropNameInRow="ContractNumber" msprop:Generator_ColumnPropNameInTable="ContractNumberColumn" msprop:Generator_UserColumnName="ContractNumber">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ChainName" msprop:Generator_ColumnVarNameInTable="columnChainName" msprop:Generator_ColumnPropNameInRow="ChainName" msprop:Generator_ColumnPropNameInTable="ChainNameColumn" msprop:Generator_UserColumnName="ChainName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="MediaName" msprop:Generator_ColumnVarNameInTable="columnMediaName" msprop:Generator_ColumnPropNameInRow="MediaName" msprop:Generator_ColumnPropNameInTable="MediaNameColumn" msprop:Generator_UserColumnName="MediaName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="InstallStoreQty" msprop:Generator_ColumnVarNameInTable="columnInstallStoreQty" msprop:Generator_ColumnPropNameInRow="InstallStoreQty" msprop:Generator_ColumnPropNameInTable="InstallStoreQtyColumn" msprop:Generator_UserColumnName="InstallStoreQty" type="xs:int" />
              <xs:element name="FirstWeek" msprop:Generator_ColumnVarNameInTable="columnFirstWeek" msprop:Generator_ColumnPropNameInRow="FirstWeek" msprop:Generator_ColumnPropNameInTable="FirstWeekColumn" msprop:Generator_UserColumnName="FirstWeek" type="xs:dateTime" />
              <xs:element name="LastWeek" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnLastWeek" msprop:Generator_ColumnPropNameInRow="LastWeek" msprop:Generator_ColumnPropNameInTable="LastWeekColumn" msprop:Generator_UserColumnName="LastWeek" type="xs:dateTime" minOccurs="0" />
              <xs:element name="ProductName" msprop:Generator_ColumnVarNameInTable="columnProductName" msprop:Generator_ColumnPropNameInRow="ProductName" msprop:Generator_ColumnPropNameInTable="ProductNameColumn" msprop:Generator_UserColumnName="ProductName" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="BrandName" msprop:Generator_ColumnVarNameInTable="columnBrandName" msprop:Generator_ColumnPropNameInRow="BrandName" msprop:Generator_ColumnPropNameInTable="BrandNameColumn" msprop:Generator_UserColumnName="BrandName" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CreatedBy" msprop:Generator_ColumnVarNameInTable="columnCreatedBy" msprop:Generator_ColumnPropNameInRow="CreatedBy" msprop:Generator_ColumnPropNameInTable="CreatedByColumn" msprop:Generator_UserColumnName="CreatedBy">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CreationDate" msprop:Generator_ColumnVarNameInTable="columnCreationDate" msprop:Generator_ColumnPropNameInRow="CreationDate" msprop:Generator_ColumnPropNameInTable="CreationDateColumn" msprop:Generator_UserColumnName="CreationDate" type="xs:dateTime" />
              <xs:element name="Homesite" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnHomesite" msprop:Generator_ColumnPropNameInRow="Homesite" msprop:Generator_ColumnPropNameInTable="HomesiteColumn" msprop:Generator_UserColumnName="Homesite" type="xs:boolean" minOccurs="0" />
              <xs:element name="StorePoolID" msdata:DataType="System.Guid, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnStorePoolID" msprop:Generator_ColumnPropNameInRow="StorePoolID" msprop:Generator_ColumnPropNameInTable="StorePoolIDColumn" msprop:Generator_UserColumnName="StorePoolID" type="xs:string" />
              <xs:element name="StorePoolCapacity" msprop:Generator_ColumnVarNameInTable="columnStorePoolCapacity" msprop:Generator_ColumnPropNameInRow="StorePoolCapacity" msprop:Generator_ColumnPropNameInTable="StorePoolCapacityColumn" msprop:Generator_UserColumnName="StorePoolCapacity" type="xs:int" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ContractList" msprop:Generator_UserTableName="ContractList" msprop:Generator_RowEvArgName="ContractListRowChangeEvent" msprop:Generator_TableVarName="tableContractList" msprop:Generator_TablePropName="ContractList" msprop:Generator_RowDeletingName="ContractListRowDeleting" msprop:Generator_RowChangingName="ContractListRowChanging" msprop:Generator_RowDeletedName="ContractListRowDeleted" msprop:Generator_TableClassName="ContractListDataTable" msprop:Generator_RowChangedName="ContractListRowChanged" msprop:Generator_RowEvHandlerName="ContractListRowChangeEventHandler" msprop:Generator_RowClassName="ContractListRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ContractID" msdata:DataType="System.Guid, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnContractID" msprop:Generator_ColumnPropNameInRow="ContractID" msprop:Generator_ColumnPropNameInTable="ContractIDColumn" msprop:Generator_UserColumnName="ContractID" type="xs:string" />
              <xs:element name="ContractNumber" msprop:Generator_ColumnVarNameInTable="columnContractNumber" msprop:Generator_ColumnPropNameInRow="ContractNumber" msprop:Generator_ColumnPropNameInTable="ContractNumberColumn" msprop:Generator_UserColumnName="ContractNumber" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ProvisionalBooking" msprop:Generator_UserTableName="ProvisionalBooking" msprop:Generator_RowEvArgName="ProvisionalBookingRowChangeEvent" msprop:Generator_TableVarName="tableProvisionalBooking" msprop:Generator_TablePropName="ProvisionalBooking" msprop:Generator_RowDeletingName="ProvisionalBookingRowDeleting" msprop:Generator_RowChangingName="ProvisionalBookingRowChanging" msprop:Generator_RowDeletedName="ProvisionalBookingRowDeleted" msprop:Generator_TableClassName="ProvisionalBookingDataTable" msprop:Generator_RowChangedName="ProvisionalBookingRowChanged" msprop:Generator_RowEvHandlerName="ProvisionalBookingRowChangeEventHandler" msprop:Generator_RowClassName="ProvisionalBookingRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ProvisionalBookingID" msdata:DataType="System.Guid, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnProvisionalBookingID" msprop:Generator_ColumnPropNameInRow="ProvisionalBookingID" msprop:Generator_ColumnPropNameInTable="ProvisionalBookingIDColumn" msprop:Generator_UserColumnName="ProvisionalBookingID" type="xs:string" />
              <xs:element name="ProvisionalBookingName" msprop:Generator_ColumnVarNameInTable="columnProvisionalBookingName" msprop:Generator_ColumnPropNameInRow="ProvisionalBookingName" msprop:Generator_ColumnPropNameInTable="ProvisionalBookingNameColumn" msprop:Generator_UserColumnName="ProvisionalBookingName" default="" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ChainID" msprop:Generator_ColumnVarNameInTable="columnChainID" msprop:Generator_ColumnPropNameInRow="ChainID" msprop:Generator_ColumnPropNameInTable="ChainIDColumn" msprop:Generator_UserColumnName="ChainID" type="xs:int" />
              <xs:element name="CategoryID" msprop:Generator_ColumnVarNameInTable="columnCategoryID" msprop:Generator_ColumnPropNameInRow="CategoryID" msprop:Generator_ColumnPropNameInTable="CategoryIDColumn" msprop:Generator_UserColumnName="CategoryID" type="xs:int" />
              <xs:element name="MediaFamilyID" msprop:Generator_ColumnVarNameInTable="columnMediaFamilyID" msprop:Generator_ColumnPropNameInRow="MediaFamilyID" msprop:Generator_ColumnPropNameInTable="MediaFamilyIDColumn" msprop:Generator_UserColumnName="MediaFamilyID" type="xs:int" />
              <xs:element name="BrandID" msdata:DataType="System.Guid, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnBrandID" msprop:Generator_ColumnPropNameInRow="BrandID" msprop:Generator_ColumnPropNameInTable="BrandIDColumn" msprop:Generator_UserColumnName="BrandID" type="xs:string" />
              <xs:element name="FirstWeek" msprop:Generator_ColumnVarNameInTable="columnFirstWeek" msprop:Generator_ColumnPropNameInRow="FirstWeek" msprop:Generator_ColumnPropNameInTable="FirstWeekColumn" msprop:Generator_UserColumnName="FirstWeek" type="xs:dateTime" />
              <xs:element name="MediaFamilyName" msprop:Generator_ColumnVarNameInTable="columnMediaFamilyName" msprop:Generator_ColumnPropNameInRow="MediaFamilyName" msprop:Generator_ColumnPropNameInTable="MediaFamilyNameColumn" msprop:Generator_UserColumnName="MediaFamilyName" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CategoryName" msprop:Generator_ColumnVarNameInTable="columnCategoryName" msprop:Generator_ColumnPropNameInRow="CategoryName" msprop:Generator_ColumnPropNameInTable="CategoryNameColumn" msprop:Generator_UserColumnName="CategoryName" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ChainName" msprop:Generator_ColumnVarNameInTable="columnChainName" msprop:Generator_ColumnPropNameInRow="ChainName" msprop:Generator_ColumnPropNameInTable="ChainNameColumn" msprop:Generator_UserColumnName="ChainName" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="BrandName" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnBrandName" msprop:Generator_ColumnPropNameInRow="BrandName" msprop:Generator_ColumnPropNameInTable="BrandNameColumn" msprop:Generator_UserColumnName="BrandName" default="" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="LastWeek" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnLastWeek" msprop:Generator_ColumnPropNameInRow="LastWeek" msprop:Generator_ColumnPropNameInTable="LastWeekColumn" msprop:Generator_UserColumnName="LastWeek" type="xs:dateTime" minOccurs="0" />
              <xs:element name="BookedBy" msprop:Generator_ColumnVarNameInTable="columnBookedBy" msprop:Generator_ColumnPropNameInRow="BookedBy" msprop:Generator_ColumnPropNameInTable="BookedByColumn" msprop:Generator_UserColumnName="BookedBy" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="BookTime" msprop:Generator_ColumnVarNameInTable="columnBookTime" msprop:Generator_ColumnPropNameInRow="BookTime" msprop:Generator_ColumnPropNameInTable="BookTimeColumn" msprop:Generator_UserColumnName="BookTime" type="xs:dateTime" />
              <xs:element name="ExpiryTime" msprop:Generator_ColumnVarNameInTable="columnExpiryTime" msprop:Generator_ColumnPropNameInRow="ExpiryTime" msprop:Generator_ColumnPropNameInTable="ExpiryTimeColumn" msprop:Generator_UserColumnName="ExpiryTime" type="xs:dateTime" />
              <xs:element name="Duration" msprop:Generator_ColumnVarNameInTable="columnDuration" msprop:Generator_ColumnPropNameInRow="Duration" msprop:Generator_ColumnPropNameInTable="DurationColumn" msprop:Generator_UserColumnName="Duration" type="xs:int" default="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ContractList" />
      <xs:field xpath="mstns:ContractID" />
    </xs:unique>
    <xs:unique name="ProvisionalBooking_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ProvisionalBooking" />
      <xs:field xpath="mstns:ProvisionalBookingID" />
    </xs:unique>
  </xs:element>
</xs:schema>