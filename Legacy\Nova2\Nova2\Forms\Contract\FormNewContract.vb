﻿Public Class FormNewContract

    Private SelectedAccountManager As DataRow = Nothing
    Private SelectedClient As DataRow = Nothing
    Private SelectedHeat As DataRow = Nothing
    Private SelectedContractClassification As DataRow = Nothing
    Private _NewContract As Contract = Nothing

#Region "Properties"

    Public ReadOnly Property NewContract As Contract
        Get
            Return _NewContract
        End Get
    End Property

    Public ReadOnly Property SelectedAccountManagerName As String
        Get
            If IsNothing(SelectedAccountManager) Then
                Return "Select..."
            Else
                Return SelectedAccountManager("FirstName") & " " & SelectedAccountManager("LastName")
            End If
        End Get
    End Property

    Public ReadOnly Property SelectedClientName As String
        Get
            If IsNothing(SelectedClient) Then
                Return "Select..."
            Else
                Return SelectedClient("ClientName")
            End If
        End Get
    End Property
    Public ReadOnly Property SelectedHeatName As String
        Get
            If IsNothing(SelectedHeat) Then
                Return "Select..."
            Else
                Return SelectedHeat("ContractProposalHeatName")
            End If
        End Get
    End Property
    Public ReadOnly Property SelectedClassificationName As String
        Get
            If IsNothing(SelectedContractClassification) Then
                Return "Select..."
            Else
                Return SelectedContractClassification("Classification")
            End If
        End Get
    End Property

#End Region

#Region "Event Handlers"

    Private Sub FormNewContract_Load(sender As Object, e As System.EventArgs) Handles Me.Load

        ' Create a string to hold possible errors during Account Manager list retrieval.
        Dim Errors As String = String.Empty

        ' Get a list of account managers that the current user has permission to modify contracts for.
        Dim AccountManagerBindingSource As BindingSource = Lookup.GetAccountManagersByPermission_EditMyContracts(My.Settings.DBConnection, Errors, Nothing)

        ' Add data bindings to controls.
        AddDataBindings()

    End Sub

    Private Sub ButtonOK_Click(sender As System.Object, e As System.EventArgs) Handles ButtonOK.Click
        SaveAndClose()
    End Sub

    Private Sub HyperlinkAccountManager_Click(sender As System.Object, e As System.EventArgs) Handles HyperlinkAccountManager.Click

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupAccountManager.SelectRowsByPermission_EditMyContracts(My.Settings.DBConnection, False, Nothing)

        ' Update the variable and the label with the selection.
        If SelectedItems.Count > 0 Then
            ' An account manager was selected successfully. Check if the account manager changed from a previous selection.
            If IsNothing(SelectedAccountManager) = False _
                AndAlso Not SelectedAccountManager("AccountManagerID") = SelectedItems(0).Item("AccountManagerID") Then
                ' The account manager selection has been changed. Forced user to reselct the client too.
                SelectedClient = Nothing
                HyperlinkClient.DataBindings("Text").ReadValue()
            End If
            SelectedAccountManager = SelectedItems(0)
            HyperlinkAccountManager.DataBindings("Text").ReadValue()
            ' Reset the error text of the control to an empty string.
            LiquidAgent.ControlValidation(HyperlinkAccountManager, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkClient_Click(sender As System.Object, e As System.EventArgs) Handles HyperlinkClient.Click

        ' Stop if no Account manager has been selected.
        If IsNothing(SelectedAccountManager) Then
            CType(TopLevelControl, BaseForm).ShowMessage _
            ("Please select an account manager before selecting a client.", "Account Manager Required")
            Exit Sub
        End If

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupClient.SelectRows(My.Settings.DBConnection, False, Nothing)

        ' Update the variable and the label with the selection.
        If SelectedItems.Count > 0 Then
            SelectedClient = SelectedItems(0)
            HyperlinkClient.DataBindings("Text").ReadValue()
            ' Reset the error text of the control to an empty string.
            LiquidAgent.ControlValidation(HyperlinkClient, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkProposalHeat_Click(sender As System.Object, e As System.EventArgs) Handles HyperlinkProposalHeat.Click

        ' Stop if no Account manager has been selected.


        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupProposalHeat.SelectRows(My.Settings.DBConnection, False, Nothing)

        ' Update the variable and the label with the selection.
        If SelectedItems.Count > 0 Then
            SelectedHeat = SelectedItems(0)
            HyperlinkProposalHeat.DataBindings("Text").ReadValue()
            ' Reset the error text of the control to an empty string.
            LiquidAgent.ControlValidation(HyperlinkProposalHeat, String.Empty)
        End If

    End Sub
    Private Sub HyperlinkProposalHeat_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles HyperlinkProposalHeat.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        If String.Compare(ValidatedControl.Text, "Select...") = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "A proposal type must be selected.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkClassification_Click(sender As Object, e As EventArgs) Handles HyperlinkClassification.Click
        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupContractClassification.SelectRows(My.Settings.DBConnection, False, Nothing)

        ' Update the variable and the label with the selection.
        If SelectedItems.Count > 0 Then
            SelectedContractClassification = SelectedItems(0)
            HyperlinkClassification.DataBindings("Text").ReadValue()
            ' Reset the error text of the control to an empty string.
            LiquidAgent.ControlValidation(HyperlinkClassification, String.Empty)
        End If
    End Sub
    Private Sub HyperlinkClassification_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles HyperlinkClassification.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        If String.Compare(ValidatedControl.Text, "Select...") = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "A classification must be selected.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkAccountManager_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles HyperlinkAccountManager.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        If String.Compare(ValidatedControl.Text, "Select...") = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "An account manager must be selected.")
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkClient_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles HyperlinkClient.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        If String.Compare(ValidatedControl.Text, "Select...") = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "A client must be selected.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

#End Region

#Region "Public Methods"

    Public Shared Function GetNewContract() As Contract
        ' Prompt the user for some contract properties by displaying this form then return a contract object.

        ' Instantiate this form.
        Dim NewContractForm As New FormNewContract
        NewContractForm.ShowDialog()

        ' Return the new contract object created by the form instance (or nothing if the user clicked cancel).
        Return NewContractForm.NewContract
        NewContractForm.Dispose()

    End Function

#End Region

#Region "Private Methods"

    Private Sub AddDataBindings()

        ' Form elements
        HyperlinkAccountManager.DataBindings.Add("Text", Me, "SelectedAccountManagerName")
        HyperlinkClient.DataBindings.Add("Text", Me, "SelectedClientName")
        HyperlinkProposalHeat.DataBindings.Add("Text", Me, "SelectedHeatName")
        HyperlinkClassification.DataBindings.Add("Text", Me, "SelectedClassificationName")

    End Sub

    Protected Overrides Sub Save()

        ' Create a new contract row needed to create a new contract object.
        Dim ContractDataSet As New DataSetContract
        Dim NewContractRow As DataSetContract.ContractRow = ContractDataSet.Contract.NewContractRow
        With NewContractRow
            .AccountManagerID = SelectedAccountManager("AccountManagerID")
            .AccountManagerName = SelectedAccountManager("FirstName") & " " & SelectedAccountManager("LastName")
            .Code = SelectedAccountManager("Code")
            .ProjectName = TextEditProject.Text.Trim
            .SpecialConditions = MemoEditSpecialConditions.Text.Trim
            .ClientID = SelectedClient("ClientID")
            .ClientName = SelectedClient("ClientName")
            .ClientBillingAddress = SelectedClient("ClientAddress")
            .ContractNumber = Contract.CreateNewContractNumber(SelectedAccountManager("Code"), My.Settings.DBConnection)
            .ContractProposalHeatId = SelectedHeat("ContractProposalHeatId")
            .ContractClassificationId = SelectedContractClassification("ContractClassificationId")
            .ContractClassificationName = SelectedContractClassification("Classification")

        End With
        If CheckEditRental.Checked Then
            NewContractRow.ContractType = "Rental"
        ElseIf CheckEditResearch.Checked Then
            NewContractRow.ContractType = "Research"
        ElseIf CheckEditDuplicate.Checked Then
            NewContractRow.ContractType = "Distribution"
        ElseIf CheckEditInstallationOnlyContract.Checked Then
            NewContractRow.ContractType = "InstallationOnly"
        End If
        NewContractRow.CreatedBy = My.User.Name
        NewContractRow.CreationDate = Now
        ContractDataSet.Contract.Rows.Add(NewContractRow)

        ' Create a new contract object for the NewContract property.
        _NewContract = New Contract(NewContractRow, My.Settings.DBConnection)

        ' Close the form.
        Close()

    End Sub



#End Region

End Class
