﻿Public Class SubformStoreGroups
    Private DataObject As StoreGroups

#Region "Event Handlers"

    Private Sub SubformGroupStore_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        ' Load values for all controls.
        TextEditGroupChainName.Text = DataObject.GroupChainName
        CheckEditDormant.EditValue = DataObject.Dormant

        ' Load grid data.
        GridGroupChainStores.AutoGenerateColumns = False
        GridGroupChainStores.DataSource = DataObject.GroupChainBindingSource

        ' Configure grid managers.
        Dim GridManagerMediaCategory As New GridManager(GridGroupChainStores, TextEditSearchMediaCategory, Nothing, Nothing,
        PictureAdvancedSearchMediaCategory, PictureClearSearchMediaCategory, Nothing, ButtonRemoveMediaCategory)

    End Sub

    Public Sub New(ByVal GroupChainObject As StoreGroups)
        ' This call is required by the Windows Form Designer.
        InitializeComponent()
        ' Add any initialization after the InitializeComponent() call.
        DataObject = GroupChainObject
    End Sub

    Private Sub ButtonSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonSave.Click
        Save(True)
    End Sub

    Private Sub ButtonCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonCancel.Click
        DataObject.Close(Me)
    End Sub

    Private Sub TextEditCategoryName_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles TextEditGroupChainName.EditValueChanged

        ' Create an object to represent the control.
        Dim CurrentControl As TextEdit = CType(sender, TextEdit)

        ' Update the form title as the user types a name.
        If Not String.IsNullOrEmpty(CurrentControl.Text) Then
            LabelTitle.Text = CurrentControl.Text
        End If

        ' Reset the error text of the control to an empty string.
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)

    End Sub

    Private Sub TextEditCategoryName_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles TextEditGroupChainName.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If ValidatedControl.Text.Length = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "The name of the group chain may not be blank.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

        ' Update the data object.
        DataObject.GroupChainName = CType(sender, TextEdit).Text

    End Sub

    Private Sub CheckEditDormant_EditValueChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles CheckEditDormant.EditValueChanged
        ' Update the data object.
        DataObject.Dormant = CType(sender, CheckEdit).EditValue
    End Sub

    Private Sub ButtonAddStoreGroup_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonAddStoreGroup.Click

        ' Save first if required.
        If LiquidShell.LiquidAgent.SaveAndProceed(DataObject.Row, Me, My.Settings.DBConnection, False) = False Then
            Exit Sub
        End If

        ' Get a selection of data rows from the user and add the selected items.
        Dim SelectedItems As List(Of DataRow) = LookupStoreByChain.SelectRows(My.Settings.DBConnection, True, GridGroupChainStores)
        DataObject.AddStoreGroups(Me, My.Settings.DBConnection, SelectedItems)

    End Sub

    Private Sub ButtonDeleteMediaCategory_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonRemoveMediaCategory.Click
        DataObject.DeleteChildRow(GridGroupChainStores, "GroupChainName")
    End Sub

#End Region

#Region "Methods"

    Protected Overrides Function Save() As Boolean
        ' Save the current object.
        Dim GridsToAudit As New List(Of DataGridView)
        GridsToAudit.Add(GridGroupChainStores)
        DataObject.Save(My.Settings.DBConnection, GridsToAudit)
        Return True
    End Function



#End Region
End Class
