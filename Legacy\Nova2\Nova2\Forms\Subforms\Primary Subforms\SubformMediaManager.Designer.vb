<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformMediaManager
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformMediaManager))
        Dim SuperToolTip1 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem1 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem1 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip2 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem2 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem2 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim SuperToolTip3 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem3 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem3 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip4 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem4 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem4 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle4 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle5 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim SuperToolTip5 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem5 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem5 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip6 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem6 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem6 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip7 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem7 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem7 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip8 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem8 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem8 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle6 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle7 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim SuperToolTip9 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem9 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem9 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip10 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem10 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem10 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Me.ImageList16x16 = New System.Windows.Forms.ImageList(Me.components)
        Me.XtraTabControl1 = New DevExpress.XtraTab.XtraTabControl()
        Me.TabPageDetails = New DevExpress.XtraTab.XtraTabPage()
        Me.GroupControlItems = New DevExpress.XtraEditors.GroupControl()
        Me.LabelControl11 = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearch = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearch = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearch = New DevExpress.XtraEditors.PictureEdit()
        Me.ButtonDelete = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAdd = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonEdit = New DevExpress.XtraEditors.SimpleButton()
        Me.GridItems = New System.Windows.Forms.DataGridView()
        Me.TabPageMediaGrouping = New DevExpress.XtraTab.XtraTabPage()
        Me.GroupStoreGroupings = New DevExpress.XtraEditors.GroupControl()
        Me.PictureClearSearchMediaGroup = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearchMediaGroup = New DevExpress.XtraEditors.PictureEdit()
        Me.TextEditSearchMediaGroup = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.ButtonDeleteMediaGroup = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonEditMediaGroup = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAddMediaGroup = New DevExpress.XtraEditors.SimpleButton()
        Me.GridMediaGrouping = New System.Windows.Forms.DataGridView()
        Me.MediaGroupingNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.LabelSearchStoreGroupings = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearchStoreGroupings = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearchInstallationTeams = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearchInstallationTeams = New DevExpress.XtraEditors.PictureEdit()
        Me.TabPageMediaChannels = New DevExpress.XtraTab.XtraTabPage()
        Me.GroupControl1 = New DevExpress.XtraEditors.GroupControl()
        Me.PictureClearSearchMediaChannels = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearchMediaChannels = New DevExpress.XtraEditors.PictureEdit()
        Me.TextEditSearchMediaChannels = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl()
        Me.ButtonDeleteMediaChannels = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonEditMediaChannels = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAddMediaChannels = New DevExpress.XtraEditors.SimpleButton()
        Me.GridMediaChannels = New System.Windows.Forms.DataGridView()
        Me.DataGridViewTextBoxColumn1 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.LabelControl3 = New DevExpress.XtraEditors.LabelControl()
        Me.TextEdit2 = New DevExpress.XtraEditors.TextEdit()
        Me.PictureEdit3 = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureEdit4 = New DevExpress.XtraEditors.PictureEdit()
        Me.MediaNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.HomesiteColumn = New System.Windows.Forms.DataGridViewCheckBoxColumn()
        Me.CrossoverColumn = New System.Windows.Forms.DataGridViewCheckBoxColumn()
        Me.isPNPPcaStatusColumn = New System.Windows.Forms.DataGridViewCheckBoxColumn()
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabControl1.SuspendLayout()
        Me.TabPageDetails.SuspendLayout()
        CType(Me.GroupControlItems, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlItems.SuspendLayout()
        CType(Me.TextEditSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridItems, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabPageMediaGrouping.SuspendLayout()
        CType(Me.GroupStoreGroupings, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupStoreGroupings.SuspendLayout()
        CType(Me.PictureClearSearchMediaGroup.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearchMediaGroup.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditSearchMediaGroup.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridMediaGrouping, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditSearchStoreGroupings.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearchInstallationTeams.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearchInstallationTeams.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabPageMediaChannels.SuspendLayout()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl1.SuspendLayout()
        CType(Me.PictureClearSearchMediaChannels.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearchMediaChannels.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditSearchMediaChannels.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridMediaChannels, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEdit2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureEdit3.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureEdit4.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Margin = New System.Windows.Forms.Padding(5, 5, 5, 21)
        Me.LabelTitle.Size = New System.Drawing.Size(240, 37)
        Me.LabelTitle.TabIndex = 0
        Me.LabelTitle.Text = "Media Manager"
        '
        'ImageList16x16
        '
        Me.ImageList16x16.ImageStream = CType(resources.GetObject("ImageList16x16.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList16x16.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList16x16.Images.SetKeyName(0, "add.png")
        Me.ImageList16x16.Images.SetKeyName(1, "pencil.png")
        Me.ImageList16x16.Images.SetKeyName(2, "delete.png")
        '
        'XtraTabControl1
        '
        Me.XtraTabControl1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.XtraTabControl1.AppearancePage.Header.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.XtraTabControl1.AppearancePage.Header.Options.UseFont = True
        Me.XtraTabControl1.Location = New System.Drawing.Point(15, 75)
        Me.XtraTabControl1.LookAndFeel.SkinName = "Black"
        Me.XtraTabControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.XtraTabControl1.Margin = New System.Windows.Forms.Padding(4)
        Me.XtraTabControl1.Name = "XtraTabControl1"
        Me.XtraTabControl1.SelectedTabPage = Me.TabPageDetails
        Me.XtraTabControl1.Size = New System.Drawing.Size(1108, 790)
        Me.XtraTabControl1.TabIndex = 2
        Me.XtraTabControl1.TabPages.AddRange(New DevExpress.XtraTab.XtraTabPage() {Me.TabPageDetails, Me.TabPageMediaGrouping, Me.TabPageMediaChannels})
        '
        'TabPageDetails
        '
        Me.TabPageDetails.Controls.Add(Me.GroupControlItems)
        Me.TabPageDetails.Margin = New System.Windows.Forms.Padding(4)
        Me.TabPageDetails.Name = "TabPageDetails"
        Me.TabPageDetails.Size = New System.Drawing.Size(1102, 758)
        Me.TabPageDetails.Text = "Details"
        '
        'GroupControlItems
        '
        Me.GroupControlItems.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlItems.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlItems.Appearance.Options.UseFont = True
        Me.GroupControlItems.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlItems.AppearanceCaption.Options.UseFont = True
        Me.GroupControlItems.Controls.Add(Me.LabelControl11)
        Me.GroupControlItems.Controls.Add(Me.TextEditSearch)
        Me.GroupControlItems.Controls.Add(Me.PictureClearSearch)
        Me.GroupControlItems.Controls.Add(Me.PictureAdvancedSearch)
        Me.GroupControlItems.Controls.Add(Me.ButtonDelete)
        Me.GroupControlItems.Controls.Add(Me.ButtonAdd)
        Me.GroupControlItems.Controls.Add(Me.ButtonEdit)
        Me.GroupControlItems.Controls.Add(Me.GridItems)
        Me.GroupControlItems.Location = New System.Drawing.Point(-1, 4)
        Me.GroupControlItems.LookAndFeel.SkinName = "Black"
        Me.GroupControlItems.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlItems.Margin = New System.Windows.Forms.Padding(4)
        Me.GroupControlItems.Name = "GroupControlItems"
        Me.GroupControlItems.Size = New System.Drawing.Size(1098, 745)
        Me.GroupControlItems.TabIndex = 1
        Me.GroupControlItems.Text = "Media List"
        '
        'LabelControl11
        '
        Me.LabelControl11.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl11.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl11.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl11.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl11.Location = New System.Drawing.Point(895, 714)
        Me.LabelControl11.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelControl11.Name = "LabelControl11"
        Me.LabelControl11.Size = New System.Drawing.Size(58, 17)
        Me.LabelControl11.TabIndex = 5
        Me.LabelControl11.Text = "Search:"
        '
        'TextEditSearch
        '
        Me.TextEditSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearch.EditValue = ""
        Me.TextEditSearch.Location = New System.Drawing.Point(960, 710)
        Me.TextEditSearch.Margin = New System.Windows.Forms.Padding(4, 5, 4, 7)
        Me.TextEditSearch.Name = "TextEditSearch"
        Me.TextEditSearch.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearch.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearch.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearch.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearch.Size = New System.Drawing.Size(103, 24)
        Me.TextEditSearch.TabIndex = 6
        '
        'PictureClearSearch
        '
        Me.PictureClearSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearch.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearch.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearch.Location = New System.Drawing.Point(1071, 4)
        Me.PictureClearSearch.Margin = New System.Windows.Forms.Padding(4)
        Me.PictureClearSearch.Name = "PictureClearSearch"
        Me.PictureClearSearch.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearch.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearch.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearch.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem1.Text = "Clear Search"
        ToolTipItem1.LeftIndent = 6
        ToolTipItem1.Text = "Click here to clear all search boxes."
        SuperToolTip1.Items.Add(ToolTipTitleItem1)
        SuperToolTip1.Items.Add(ToolTipItem1)
        Me.PictureClearSearch.SuperTip = SuperToolTip1
        Me.PictureClearSearch.TabIndex = 0
        Me.PictureClearSearch.TabStop = True
        '
        'PictureAdvancedSearch
        '
        Me.PictureAdvancedSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearch.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearch.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearch.Location = New System.Drawing.Point(1071, 713)
        Me.PictureAdvancedSearch.Margin = New System.Windows.Forms.Padding(4, 8, 4, 9)
        Me.PictureAdvancedSearch.Name = "PictureAdvancedSearch"
        Me.PictureAdvancedSearch.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearch.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearch.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearch.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem2.Text = "Advanced Search"
        ToolTipItem2.LeftIndent = 6
        ToolTipItem2.Text = "Click here to search individual column values."
        SuperToolTip2.Items.Add(ToolTipTitleItem2)
        SuperToolTip2.Items.Add(ToolTipItem2)
        Me.PictureAdvancedSearch.SuperTip = SuperToolTip2
        Me.PictureAdvancedSearch.TabIndex = 7
        Me.PictureAdvancedSearch.TabStop = True
        '
        'ButtonDelete
        '
        Me.ButtonDelete.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonDelete.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonDelete.Appearance.Options.UseFont = True
        Me.ButtonDelete.ImageIndex = 2
        Me.ButtonDelete.ImageList = Me.ImageList16x16
        Me.ButtonDelete.Location = New System.Drawing.Point(215, 709)
        Me.ButtonDelete.LookAndFeel.SkinName = "Black"
        Me.ButtonDelete.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonDelete.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonDelete.Name = "ButtonDelete"
        Me.ButtonDelete.Size = New System.Drawing.Size(96, 30)
        Me.ButtonDelete.TabIndex = 4
        Me.ButtonDelete.Text = "Delete"
        '
        'ButtonAdd
        '
        Me.ButtonAdd.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAdd.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAdd.Appearance.Options.UseFont = True
        Me.ButtonAdd.ImageIndex = 0
        Me.ButtonAdd.ImageList = Me.ImageList16x16
        Me.ButtonAdd.Location = New System.Drawing.Point(6, 709)
        Me.ButtonAdd.LookAndFeel.SkinName = "Black"
        Me.ButtonAdd.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAdd.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonAdd.Name = "ButtonAdd"
        Me.ButtonAdd.Size = New System.Drawing.Size(96, 30)
        Me.ButtonAdd.TabIndex = 2
        Me.ButtonAdd.Text = "Add"
        '
        'ButtonEdit
        '
        Me.ButtonEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonEdit.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonEdit.Appearance.Options.UseFont = True
        Me.ButtonEdit.ImageIndex = 1
        Me.ButtonEdit.ImageList = Me.ImageList16x16
        Me.ButtonEdit.Location = New System.Drawing.Point(111, 709)
        Me.ButtonEdit.LookAndFeel.SkinName = "Black"
        Me.ButtonEdit.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonEdit.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonEdit.Name = "ButtonEdit"
        Me.ButtonEdit.Size = New System.Drawing.Size(96, 30)
        Me.ButtonEdit.TabIndex = 3
        Me.ButtonEdit.Text = "Edit"
        '
        'GridItems
        '
        Me.GridItems.AllowUserToAddRows = False
        Me.GridItems.AllowUserToDeleteRows = False
        Me.GridItems.AllowUserToOrderColumns = True
        Me.GridItems.AllowUserToResizeRows = False
        DataGridViewCellStyle1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridItems.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle1
        Me.GridItems.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridItems.BackgroundColor = System.Drawing.Color.White
        Me.GridItems.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridItems.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridItems.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle2.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle2.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridItems.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle2
        Me.GridItems.ColumnHeadersHeight = 22
        Me.GridItems.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridItems.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.MediaNameColumn, Me.HomesiteColumn, Me.CrossoverColumn, Me.isPNPPcaStatusColumn})
        Me.GridItems.EnableHeadersVisualStyles = False
        Me.GridItems.GridColor = System.Drawing.Color.White
        Me.GridItems.Location = New System.Drawing.Point(3, 29)
        Me.GridItems.Margin = New System.Windows.Forms.Padding(4)
        Me.GridItems.Name = "GridItems"
        Me.GridItems.ReadOnly = True
        Me.GridItems.RowHeadersVisible = False
        Me.GridItems.RowHeadersWidth = 51
        DataGridViewCellStyle3.ForeColor = System.Drawing.Color.DimGray
        Me.GridItems.RowsDefaultCellStyle = DataGridViewCellStyle3
        Me.GridItems.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridItems.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridItems.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridItems.RowTemplate.Height = 19
        Me.GridItems.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridItems.ShowCellToolTips = False
        Me.GridItems.Size = New System.Drawing.Size(1093, 672)
        Me.GridItems.StandardTab = True
        Me.GridItems.TabIndex = 1
        '
        'TabPageMediaGrouping
        '
        Me.TabPageMediaGrouping.Controls.Add(Me.GroupStoreGroupings)
        Me.TabPageMediaGrouping.Margin = New System.Windows.Forms.Padding(4)
        Me.TabPageMediaGrouping.Name = "TabPageMediaGrouping"
        Me.TabPageMediaGrouping.Size = New System.Drawing.Size(1102, 758)
        Me.TabPageMediaGrouping.Text = "Media Grouping"
        '
        'GroupStoreGroupings
        '
        Me.GroupStoreGroupings.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupStoreGroupings.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupStoreGroupings.Appearance.Options.UseFont = True
        Me.GroupStoreGroupings.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupStoreGroupings.AppearanceCaption.ForeColor = System.Drawing.Color.DimGray
        Me.GroupStoreGroupings.AppearanceCaption.Options.UseFont = True
        Me.GroupStoreGroupings.AppearanceCaption.Options.UseForeColor = True
        Me.GroupStoreGroupings.Controls.Add(Me.PictureClearSearchMediaGroup)
        Me.GroupStoreGroupings.Controls.Add(Me.PictureAdvancedSearchMediaGroup)
        Me.GroupStoreGroupings.Controls.Add(Me.TextEditSearchMediaGroup)
        Me.GroupStoreGroupings.Controls.Add(Me.LabelControl1)
        Me.GroupStoreGroupings.Controls.Add(Me.ButtonDeleteMediaGroup)
        Me.GroupStoreGroupings.Controls.Add(Me.ButtonEditMediaGroup)
        Me.GroupStoreGroupings.Controls.Add(Me.ButtonAddMediaGroup)
        Me.GroupStoreGroupings.Controls.Add(Me.GridMediaGrouping)
        Me.GroupStoreGroupings.Controls.Add(Me.LabelSearchStoreGroupings)
        Me.GroupStoreGroupings.Controls.Add(Me.TextEditSearchStoreGroupings)
        Me.GroupStoreGroupings.Controls.Add(Me.PictureClearSearchInstallationTeams)
        Me.GroupStoreGroupings.Controls.Add(Me.PictureAdvancedSearchInstallationTeams)
        Me.GroupStoreGroupings.Location = New System.Drawing.Point(4, 4)
        Me.GroupStoreGroupings.LookAndFeel.SkinName = "Black"
        Me.GroupStoreGroupings.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupStoreGroupings.Margin = New System.Windows.Forms.Padding(4)
        Me.GroupStoreGroupings.Name = "GroupStoreGroupings"
        Me.GroupStoreGroupings.Size = New System.Drawing.Size(1093, 745)
        Me.GroupStoreGroupings.TabIndex = 19
        Me.GroupStoreGroupings.Text = "Media Groups"
        '
        'PictureClearSearchMediaGroup
        '
        Me.PictureClearSearchMediaGroup.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchMediaGroup.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchMediaGroup.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchMediaGroup.Location = New System.Drawing.Point(1066, 1)
        Me.PictureClearSearchMediaGroup.Margin = New System.Windows.Forms.Padding(4)
        Me.PictureClearSearchMediaGroup.Name = "PictureClearSearchMediaGroup"
        Me.PictureClearSearchMediaGroup.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchMediaGroup.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchMediaGroup.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchMediaGroup.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem3.Text = "Clear Search"
        ToolTipItem3.LeftIndent = 6
        ToolTipItem3.Text = "Click here to clear all search boxes."
        SuperToolTip3.Items.Add(ToolTipTitleItem3)
        SuperToolTip3.Items.Add(ToolTipItem3)
        Me.PictureClearSearchMediaGroup.SuperTip = SuperToolTip3
        Me.PictureClearSearchMediaGroup.TabIndex = 14
        Me.PictureClearSearchMediaGroup.TabStop = True
        '
        'PictureAdvancedSearchMediaGroup
        '
        Me.PictureAdvancedSearchMediaGroup.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearchMediaGroup.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearchMediaGroup.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearchMediaGroup.Location = New System.Drawing.Point(1059, 713)
        Me.PictureAdvancedSearchMediaGroup.Margin = New System.Windows.Forms.Padding(4, 8, 4, 9)
        Me.PictureAdvancedSearchMediaGroup.Name = "PictureAdvancedSearchMediaGroup"
        Me.PictureAdvancedSearchMediaGroup.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearchMediaGroup.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearchMediaGroup.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearchMediaGroup.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem4.Text = "Advanced Search"
        ToolTipItem4.LeftIndent = 6
        ToolTipItem4.Text = "Click here to search individual column values."
        SuperToolTip4.Items.Add(ToolTipTitleItem4)
        SuperToolTip4.Items.Add(ToolTipItem4)
        Me.PictureAdvancedSearchMediaGroup.SuperTip = SuperToolTip4
        Me.PictureAdvancedSearchMediaGroup.TabIndex = 13
        Me.PictureAdvancedSearchMediaGroup.TabStop = True
        '
        'TextEditSearchMediaGroup
        '
        Me.TextEditSearchMediaGroup.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchMediaGroup.EditValue = ""
        Me.TextEditSearchMediaGroup.Location = New System.Drawing.Point(936, 710)
        Me.TextEditSearchMediaGroup.Margin = New System.Windows.Forms.Padding(4, 5, 4, 7)
        Me.TextEditSearchMediaGroup.Name = "TextEditSearchMediaGroup"
        Me.TextEditSearchMediaGroup.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchMediaGroup.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchMediaGroup.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchMediaGroup.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchMediaGroup.Size = New System.Drawing.Size(116, 24)
        Me.TextEditSearchMediaGroup.TabIndex = 12
        '
        'LabelControl1
        '
        Me.LabelControl1.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl1.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl1.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl1.Location = New System.Drawing.Point(870, 713)
        Me.LabelControl1.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(58, 17)
        Me.LabelControl1.TabIndex = 11
        Me.LabelControl1.Text = "Search:"
        '
        'ButtonDeleteMediaGroup
        '
        Me.ButtonDeleteMediaGroup.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonDeleteMediaGroup.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonDeleteMediaGroup.Appearance.Options.UseFont = True
        Me.ButtonDeleteMediaGroup.ImageIndex = 2
        Me.ButtonDeleteMediaGroup.ImageList = Me.ImageList16x16
        Me.ButtonDeleteMediaGroup.Location = New System.Drawing.Point(213, 710)
        Me.ButtonDeleteMediaGroup.LookAndFeel.SkinName = "Black"
        Me.ButtonDeleteMediaGroup.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonDeleteMediaGroup.Margin = New System.Windows.Forms.Padding(4, 4, 19, 4)
        Me.ButtonDeleteMediaGroup.Name = "ButtonDeleteMediaGroup"
        Me.ButtonDeleteMediaGroup.Size = New System.Drawing.Size(96, 30)
        Me.ButtonDeleteMediaGroup.TabIndex = 10
        Me.ButtonDeleteMediaGroup.Text = "Delete"
        '
        'ButtonEditMediaGroup
        '
        Me.ButtonEditMediaGroup.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonEditMediaGroup.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonEditMediaGroup.Appearance.Options.UseFont = True
        Me.ButtonEditMediaGroup.ImageIndex = 1
        Me.ButtonEditMediaGroup.ImageList = Me.ImageList16x16
        Me.ButtonEditMediaGroup.Location = New System.Drawing.Point(109, 710)
        Me.ButtonEditMediaGroup.LookAndFeel.SkinName = "Black"
        Me.ButtonEditMediaGroup.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonEditMediaGroup.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonEditMediaGroup.Name = "ButtonEditMediaGroup"
        Me.ButtonEditMediaGroup.Size = New System.Drawing.Size(96, 30)
        Me.ButtonEditMediaGroup.TabIndex = 9
        Me.ButtonEditMediaGroup.Text = "Edit"
        '
        'ButtonAddMediaGroup
        '
        Me.ButtonAddMediaGroup.AccessibleRole = System.Windows.Forms.AccessibleRole.None
        Me.ButtonAddMediaGroup.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAddMediaGroup.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAddMediaGroup.Appearance.Options.UseFont = True
        Me.ButtonAddMediaGroup.ImageIndex = 0
        Me.ButtonAddMediaGroup.ImageList = Me.ImageList16x16
        Me.ButtonAddMediaGroup.Location = New System.Drawing.Point(6, 710)
        Me.ButtonAddMediaGroup.LookAndFeel.SkinName = "Black"
        Me.ButtonAddMediaGroup.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAddMediaGroup.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonAddMediaGroup.Name = "ButtonAddMediaGroup"
        Me.ButtonAddMediaGroup.Size = New System.Drawing.Size(96, 30)
        Me.ButtonAddMediaGroup.TabIndex = 8
        Me.ButtonAddMediaGroup.Text = "Add"
        '
        'GridMediaGrouping
        '
        Me.GridMediaGrouping.AllowUserToAddRows = False
        Me.GridMediaGrouping.AllowUserToDeleteRows = False
        Me.GridMediaGrouping.AllowUserToOrderColumns = True
        Me.GridMediaGrouping.AllowUserToResizeRows = False
        DataGridViewCellStyle4.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridMediaGrouping.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle4
        Me.GridMediaGrouping.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridMediaGrouping.BackgroundColor = System.Drawing.Color.White
        Me.GridMediaGrouping.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridMediaGrouping.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridMediaGrouping.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle5.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridMediaGrouping.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle5
        Me.GridMediaGrouping.ColumnHeadersHeight = 22
        Me.GridMediaGrouping.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridMediaGrouping.ColumnHeadersVisible = False
        Me.GridMediaGrouping.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.MediaGroupingNameColumn})
        Me.GridMediaGrouping.EnableHeadersVisualStyles = False
        Me.GridMediaGrouping.GridColor = System.Drawing.Color.White
        Me.GridMediaGrouping.Location = New System.Drawing.Point(3, 25)
        Me.GridMediaGrouping.Margin = New System.Windows.Forms.Padding(0, 0, 0, 4)
        Me.GridMediaGrouping.Name = "GridMediaGrouping"
        Me.GridMediaGrouping.ReadOnly = True
        Me.GridMediaGrouping.RowHeadersVisible = False
        Me.GridMediaGrouping.RowHeadersWidth = 51
        Me.GridMediaGrouping.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridMediaGrouping.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridMediaGrouping.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridMediaGrouping.RowTemplate.Height = 19
        Me.GridMediaGrouping.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridMediaGrouping.ShowCellToolTips = False
        Me.GridMediaGrouping.Size = New System.Drawing.Size(1089, 680)
        Me.GridMediaGrouping.StandardTab = True
        Me.GridMediaGrouping.TabIndex = 1
        '
        'MediaGroupingNameColumn
        '
        Me.MediaGroupingNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.MediaGroupingNameColumn.DataPropertyName = "MediaGroupName"
        Me.MediaGroupingNameColumn.HeaderText = "Media Group Name"
        Me.MediaGroupingNameColumn.MinimumWidth = 6
        Me.MediaGroupingNameColumn.Name = "MediaGroupingNameColumn"
        Me.MediaGroupingNameColumn.ReadOnly = True
        '
        'LabelSearchStoreGroupings
        '
        Me.LabelSearchStoreGroupings.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelSearchStoreGroupings.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSearchStoreGroupings.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelSearchStoreGroupings.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelSearchStoreGroupings.Location = New System.Drawing.Point(1621, 1244)
        Me.LabelSearchStoreGroupings.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelSearchStoreGroupings.Name = "LabelSearchStoreGroupings"
        Me.LabelSearchStoreGroupings.Size = New System.Drawing.Size(58, 17)
        Me.LabelSearchStoreGroupings.TabIndex = 5
        Me.LabelSearchStoreGroupings.Text = "Search:"
        '
        'TextEditSearchStoreGroupings
        '
        Me.TextEditSearchStoreGroupings.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchStoreGroupings.EditValue = ""
        Me.TextEditSearchStoreGroupings.Location = New System.Drawing.Point(1687, 1238)
        Me.TextEditSearchStoreGroupings.Margin = New System.Windows.Forms.Padding(4, 5, 4, 7)
        Me.TextEditSearchStoreGroupings.Name = "TextEditSearchStoreGroupings"
        Me.TextEditSearchStoreGroupings.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchStoreGroupings.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchStoreGroupings.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchStoreGroupings.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchStoreGroupings.Size = New System.Drawing.Size(103, 24)
        Me.TextEditSearchStoreGroupings.TabIndex = 6
        '
        'PictureClearSearchInstallationTeams
        '
        Me.PictureClearSearchInstallationTeams.AllowDrop = True
        Me.PictureClearSearchInstallationTeams.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchInstallationTeams.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchInstallationTeams.Location = New System.Drawing.Point(1797, 4)
        Me.PictureClearSearchInstallationTeams.Margin = New System.Windows.Forms.Padding(4)
        Me.PictureClearSearchInstallationTeams.Name = "PictureClearSearchInstallationTeams"
        Me.PictureClearSearchInstallationTeams.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchInstallationTeams.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchInstallationTeams.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchInstallationTeams.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem5.Text = "Clear Search"
        ToolTipItem5.LeftIndent = 6
        ToolTipItem5.Text = "Click here to clear all search boxes."
        SuperToolTip5.Items.Add(ToolTipTitleItem5)
        SuperToolTip5.Items.Add(ToolTipItem5)
        Me.PictureClearSearchInstallationTeams.SuperTip = SuperToolTip5
        Me.PictureClearSearchInstallationTeams.TabIndex = 0
        Me.PictureClearSearchInstallationTeams.TabStop = True
        '
        'PictureAdvancedSearchInstallationTeams
        '
        Me.PictureAdvancedSearchInstallationTeams.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearchInstallationTeams.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearchInstallationTeams.Location = New System.Drawing.Point(1797, 1242)
        Me.PictureAdvancedSearchInstallationTeams.Margin = New System.Windows.Forms.Padding(4, 8, 4, 9)
        Me.PictureAdvancedSearchInstallationTeams.Name = "PictureAdvancedSearchInstallationTeams"
        Me.PictureAdvancedSearchInstallationTeams.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearchInstallationTeams.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearchInstallationTeams.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearchInstallationTeams.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem6.Text = "Advanced Search"
        ToolTipItem6.LeftIndent = 6
        ToolTipItem6.Text = "Click here to search individual column values."
        SuperToolTip6.Items.Add(ToolTipTitleItem6)
        SuperToolTip6.Items.Add(ToolTipItem6)
        Me.PictureAdvancedSearchInstallationTeams.SuperTip = SuperToolTip6
        Me.PictureAdvancedSearchInstallationTeams.TabIndex = 7
        Me.PictureAdvancedSearchInstallationTeams.TabStop = True
        '
        'TabPageMediaChannels
        '
        Me.TabPageMediaChannels.Controls.Add(Me.GroupControl1)
        Me.TabPageMediaChannels.Margin = New System.Windows.Forms.Padding(4)
        Me.TabPageMediaChannels.Name = "TabPageMediaChannels"
        Me.TabPageMediaChannels.Size = New System.Drawing.Size(1102, 758)
        Me.TabPageMediaChannels.Text = "Media Channels"
        '
        'GroupControl1
        '
        Me.GroupControl1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControl1.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControl1.Appearance.Options.UseFont = True
        Me.GroupControl1.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControl1.AppearanceCaption.ForeColor = System.Drawing.Color.DimGray
        Me.GroupControl1.AppearanceCaption.Options.UseFont = True
        Me.GroupControl1.AppearanceCaption.Options.UseForeColor = True
        Me.GroupControl1.Controls.Add(Me.PictureClearSearchMediaChannels)
        Me.GroupControl1.Controls.Add(Me.PictureAdvancedSearchMediaChannels)
        Me.GroupControl1.Controls.Add(Me.TextEditSearchMediaChannels)
        Me.GroupControl1.Controls.Add(Me.LabelControl2)
        Me.GroupControl1.Controls.Add(Me.ButtonDeleteMediaChannels)
        Me.GroupControl1.Controls.Add(Me.ButtonEditMediaChannels)
        Me.GroupControl1.Controls.Add(Me.ButtonAddMediaChannels)
        Me.GroupControl1.Controls.Add(Me.GridMediaChannels)
        Me.GroupControl1.Controls.Add(Me.LabelControl3)
        Me.GroupControl1.Controls.Add(Me.TextEdit2)
        Me.GroupControl1.Controls.Add(Me.PictureEdit3)
        Me.GroupControl1.Controls.Add(Me.PictureEdit4)
        Me.GroupControl1.Location = New System.Drawing.Point(4, 4)
        Me.GroupControl1.LookAndFeel.SkinName = "Black"
        Me.GroupControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControl1.Margin = New System.Windows.Forms.Padding(4)
        Me.GroupControl1.Name = "GroupControl1"
        Me.GroupControl1.Size = New System.Drawing.Size(1093, 745)
        Me.GroupControl1.TabIndex = 20
        Me.GroupControl1.Text = "Media Channels"
        '
        'PictureClearSearchMediaChannels
        '
        Me.PictureClearSearchMediaChannels.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchMediaChannels.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchMediaChannels.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchMediaChannels.Location = New System.Drawing.Point(1066, 1)
        Me.PictureClearSearchMediaChannels.Margin = New System.Windows.Forms.Padding(4)
        Me.PictureClearSearchMediaChannels.Name = "PictureClearSearchMediaChannels"
        Me.PictureClearSearchMediaChannels.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchMediaChannels.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchMediaChannels.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchMediaChannels.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem7.Text = "Clear Search"
        ToolTipItem7.LeftIndent = 6
        ToolTipItem7.Text = "Click here to clear all search boxes."
        SuperToolTip7.Items.Add(ToolTipTitleItem7)
        SuperToolTip7.Items.Add(ToolTipItem7)
        Me.PictureClearSearchMediaChannels.SuperTip = SuperToolTip7
        Me.PictureClearSearchMediaChannels.TabIndex = 14
        Me.PictureClearSearchMediaChannels.TabStop = True
        '
        'PictureAdvancedSearchMediaChannels
        '
        Me.PictureAdvancedSearchMediaChannels.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearchMediaChannels.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearchMediaChannels.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearchMediaChannels.Location = New System.Drawing.Point(1059, 713)
        Me.PictureAdvancedSearchMediaChannels.Margin = New System.Windows.Forms.Padding(4, 8, 4, 9)
        Me.PictureAdvancedSearchMediaChannels.Name = "PictureAdvancedSearchMediaChannels"
        Me.PictureAdvancedSearchMediaChannels.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearchMediaChannels.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearchMediaChannels.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearchMediaChannels.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem8.Text = "Advanced Search"
        ToolTipItem8.LeftIndent = 6
        ToolTipItem8.Text = "Click here to search individual column values."
        SuperToolTip8.Items.Add(ToolTipTitleItem8)
        SuperToolTip8.Items.Add(ToolTipItem8)
        Me.PictureAdvancedSearchMediaChannels.SuperTip = SuperToolTip8
        Me.PictureAdvancedSearchMediaChannels.TabIndex = 13
        Me.PictureAdvancedSearchMediaChannels.TabStop = True
        '
        'TextEditSearchMediaChannels
        '
        Me.TextEditSearchMediaChannels.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchMediaChannels.EditValue = ""
        Me.TextEditSearchMediaChannels.Location = New System.Drawing.Point(936, 710)
        Me.TextEditSearchMediaChannels.Margin = New System.Windows.Forms.Padding(4, 5, 4, 7)
        Me.TextEditSearchMediaChannels.Name = "TextEditSearchMediaChannels"
        Me.TextEditSearchMediaChannels.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchMediaChannels.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchMediaChannels.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchMediaChannels.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchMediaChannels.Size = New System.Drawing.Size(116, 24)
        Me.TextEditSearchMediaChannels.TabIndex = 12
        '
        'LabelControl2
        '
        Me.LabelControl2.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl2.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl2.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl2.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl2.Location = New System.Drawing.Point(870, 713)
        Me.LabelControl2.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.Size = New System.Drawing.Size(58, 17)
        Me.LabelControl2.TabIndex = 11
        Me.LabelControl2.Text = "Search:"
        '
        'ButtonDeleteMediaChannels
        '
        Me.ButtonDeleteMediaChannels.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonDeleteMediaChannels.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonDeleteMediaChannels.Appearance.Options.UseFont = True
        Me.ButtonDeleteMediaChannels.ImageIndex = 2
        Me.ButtonDeleteMediaChannels.ImageList = Me.ImageList16x16
        Me.ButtonDeleteMediaChannels.Location = New System.Drawing.Point(213, 710)
        Me.ButtonDeleteMediaChannels.LookAndFeel.SkinName = "Black"
        Me.ButtonDeleteMediaChannels.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonDeleteMediaChannels.Margin = New System.Windows.Forms.Padding(4, 4, 19, 4)
        Me.ButtonDeleteMediaChannels.Name = "ButtonDeleteMediaChannels"
        Me.ButtonDeleteMediaChannels.Size = New System.Drawing.Size(96, 30)
        Me.ButtonDeleteMediaChannels.TabIndex = 10
        Me.ButtonDeleteMediaChannels.Text = "Delete"
        '
        'ButtonEditMediaChannels
        '
        Me.ButtonEditMediaChannels.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonEditMediaChannels.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonEditMediaChannels.Appearance.Options.UseFont = True
        Me.ButtonEditMediaChannels.ImageIndex = 1
        Me.ButtonEditMediaChannels.ImageList = Me.ImageList16x16
        Me.ButtonEditMediaChannels.Location = New System.Drawing.Point(109, 710)
        Me.ButtonEditMediaChannels.LookAndFeel.SkinName = "Black"
        Me.ButtonEditMediaChannels.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonEditMediaChannels.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonEditMediaChannels.Name = "ButtonEditMediaChannels"
        Me.ButtonEditMediaChannels.Size = New System.Drawing.Size(96, 30)
        Me.ButtonEditMediaChannels.TabIndex = 9
        Me.ButtonEditMediaChannels.Text = "Edit"
        '
        'ButtonAddMediaChannels
        '
        Me.ButtonAddMediaChannels.AccessibleRole = System.Windows.Forms.AccessibleRole.None
        Me.ButtonAddMediaChannels.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAddMediaChannels.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAddMediaChannels.Appearance.Options.UseFont = True
        Me.ButtonAddMediaChannels.ImageIndex = 0
        Me.ButtonAddMediaChannels.ImageList = Me.ImageList16x16
        Me.ButtonAddMediaChannels.Location = New System.Drawing.Point(6, 710)
        Me.ButtonAddMediaChannels.LookAndFeel.SkinName = "Black"
        Me.ButtonAddMediaChannels.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAddMediaChannels.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonAddMediaChannels.Name = "ButtonAddMediaChannels"
        Me.ButtonAddMediaChannels.Size = New System.Drawing.Size(96, 30)
        Me.ButtonAddMediaChannels.TabIndex = 8
        Me.ButtonAddMediaChannels.Text = "Add"
        '
        'GridMediaChannels
        '
        Me.GridMediaChannels.AllowUserToAddRows = False
        Me.GridMediaChannels.AllowUserToDeleteRows = False
        Me.GridMediaChannels.AllowUserToOrderColumns = True
        Me.GridMediaChannels.AllowUserToResizeRows = False
        DataGridViewCellStyle6.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridMediaChannels.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle6
        Me.GridMediaChannels.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridMediaChannels.BackgroundColor = System.Drawing.Color.White
        Me.GridMediaChannels.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridMediaChannels.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridMediaChannels.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle7.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GridMediaChannels.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle7
        Me.GridMediaChannels.ColumnHeadersHeight = 22
        Me.GridMediaChannels.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridMediaChannels.ColumnHeadersVisible = False
        Me.GridMediaChannels.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.DataGridViewTextBoxColumn1})
        Me.GridMediaChannels.EnableHeadersVisualStyles = False
        Me.GridMediaChannels.GridColor = System.Drawing.Color.White
        Me.GridMediaChannels.Location = New System.Drawing.Point(3, 25)
        Me.GridMediaChannels.Margin = New System.Windows.Forms.Padding(0, 0, 0, 4)
        Me.GridMediaChannels.Name = "GridMediaChannels"
        Me.GridMediaChannels.ReadOnly = True
        Me.GridMediaChannels.RowHeadersVisible = False
        Me.GridMediaChannels.RowHeadersWidth = 51
        Me.GridMediaChannels.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridMediaChannels.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridMediaChannels.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridMediaChannels.RowTemplate.Height = 19
        Me.GridMediaChannels.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridMediaChannels.ShowCellToolTips = False
        Me.GridMediaChannels.Size = New System.Drawing.Size(1089, 680)
        Me.GridMediaChannels.StandardTab = True
        Me.GridMediaChannels.TabIndex = 1
        '
        'DataGridViewTextBoxColumn1
        '
        Me.DataGridViewTextBoxColumn1.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.DataGridViewTextBoxColumn1.DataPropertyName = "MediaChannelName"
        Me.DataGridViewTextBoxColumn1.HeaderText = "Media Channel Name"
        Me.DataGridViewTextBoxColumn1.MinimumWidth = 6
        Me.DataGridViewTextBoxColumn1.Name = "DataGridViewTextBoxColumn1"
        Me.DataGridViewTextBoxColumn1.ReadOnly = True
        '
        'LabelControl3
        '
        Me.LabelControl3.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl3.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl3.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl3.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl3.Location = New System.Drawing.Point(1621, 1244)
        Me.LabelControl3.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelControl3.Name = "LabelControl3"
        Me.LabelControl3.Size = New System.Drawing.Size(58, 17)
        Me.LabelControl3.TabIndex = 5
        Me.LabelControl3.Text = "Search:"
        '
        'TextEdit2
        '
        Me.TextEdit2.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEdit2.EditValue = ""
        Me.TextEdit2.Location = New System.Drawing.Point(1687, 1238)
        Me.TextEdit2.Margin = New System.Windows.Forms.Padding(4, 5, 4, 7)
        Me.TextEdit2.Name = "TextEdit2"
        Me.TextEdit2.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEdit2.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEdit2.Properties.Appearance.Options.UseFont = True
        Me.TextEdit2.Properties.Appearance.Options.UseForeColor = True
        Me.TextEdit2.Size = New System.Drawing.Size(103, 24)
        Me.TextEdit2.TabIndex = 6
        '
        'PictureEdit3
        '
        Me.PictureEdit3.AllowDrop = True
        Me.PictureEdit3.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureEdit3.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureEdit3.Location = New System.Drawing.Point(1797, 4)
        Me.PictureEdit3.Margin = New System.Windows.Forms.Padding(4)
        Me.PictureEdit3.Name = "PictureEdit3"
        Me.PictureEdit3.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureEdit3.Properties.Appearance.Options.UseBackColor = True
        Me.PictureEdit3.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureEdit3.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem9.Text = "Clear Search"
        ToolTipItem9.LeftIndent = 6
        ToolTipItem9.Text = "Click here to clear all search boxes."
        SuperToolTip9.Items.Add(ToolTipTitleItem9)
        SuperToolTip9.Items.Add(ToolTipItem9)
        Me.PictureEdit3.SuperTip = SuperToolTip9
        Me.PictureEdit3.TabIndex = 0
        Me.PictureEdit3.TabStop = True
        '
        'PictureEdit4
        '
        Me.PictureEdit4.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureEdit4.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureEdit4.Location = New System.Drawing.Point(1797, 1242)
        Me.PictureEdit4.Margin = New System.Windows.Forms.Padding(4, 8, 4, 9)
        Me.PictureEdit4.Name = "PictureEdit4"
        Me.PictureEdit4.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureEdit4.Properties.Appearance.Options.UseBackColor = True
        Me.PictureEdit4.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureEdit4.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem10.Text = "Advanced Search"
        ToolTipItem10.LeftIndent = 6
        ToolTipItem10.Text = "Click here to search individual column values."
        SuperToolTip10.Items.Add(ToolTipTitleItem10)
        SuperToolTip10.Items.Add(ToolTipItem10)
        Me.PictureEdit4.SuperTip = SuperToolTip10
        Me.PictureEdit4.TabIndex = 7
        Me.PictureEdit4.TabStop = True
        '
        'MediaNameColumn
        '
        Me.MediaNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.MediaNameColumn.DataPropertyName = "MediaName"
        Me.MediaNameColumn.HeaderText = "Media"
        Me.MediaNameColumn.MinimumWidth = 6
        Me.MediaNameColumn.Name = "MediaNameColumn"
        Me.MediaNameColumn.ReadOnly = True
        '
        'HomesiteColumn
        '
        Me.HomesiteColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None
        Me.HomesiteColumn.DataPropertyName = "Homesite"
        Me.HomesiteColumn.HeaderText = "Homesite"
        Me.HomesiteColumn.MinimumWidth = 6
        Me.HomesiteColumn.Name = "HomesiteColumn"
        Me.HomesiteColumn.ReadOnly = True
        Me.HomesiteColumn.Resizable = System.Windows.Forms.DataGridViewTriState.[True]
        Me.HomesiteColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Automatic
        Me.HomesiteColumn.Width = 125
        '
        'CrossoverColumn
        '
        Me.CrossoverColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None
        Me.CrossoverColumn.DataPropertyName = "Crossover"
        Me.CrossoverColumn.HeaderText = "Crossover"
        Me.CrossoverColumn.MinimumWidth = 6
        Me.CrossoverColumn.Name = "CrossoverColumn"
        Me.CrossoverColumn.ReadOnly = True
        Me.CrossoverColumn.Resizable = System.Windows.Forms.DataGridViewTriState.[True]
        Me.CrossoverColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Automatic
        Me.CrossoverColumn.Width = 125
        '
        'isPNPPcaStatusColumn
        '
        Me.isPNPPcaStatusColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None
        Me.isPNPPcaStatusColumn.DataPropertyName = "isPNPPcaStatus"
        Me.isPNPPcaStatusColumn.HeaderText = "Artwork Status"
        Me.isPNPPcaStatusColumn.MinimumWidth = 6
        Me.isPNPPcaStatusColumn.Name = "isPNPPcaStatusColumn"
        Me.isPNPPcaStatusColumn.ReadOnly = True
        Me.isPNPPcaStatusColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Automatic
        Me.isPNPPcaStatusColumn.Width = 125
        '
        'SubformMediaManager
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(9.0!, 17.0!)
        Me.Controls.Add(Me.XtraTabControl1)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(5)
        Me.Name = "SubformMediaManager"
        Me.Padding = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.Size = New System.Drawing.Size(1162, 939)
        Me.Controls.SetChildIndex(Me.XtraTabControl1, 0)
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabControl1.ResumeLayout(False)
        Me.TabPageDetails.ResumeLayout(False)
        CType(Me.GroupControlItems, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlItems.ResumeLayout(False)
        CType(Me.TextEditSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridItems, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabPageMediaGrouping.ResumeLayout(False)
        CType(Me.GroupStoreGroupings, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupStoreGroupings.ResumeLayout(False)
        CType(Me.PictureClearSearchMediaGroup.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearchMediaGroup.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditSearchMediaGroup.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridMediaGrouping, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditSearchStoreGroupings.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearchInstallationTeams.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearchInstallationTeams.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabPageMediaChannels.ResumeLayout(False)
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl1.ResumeLayout(False)
        CType(Me.PictureClearSearchMediaChannels.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearchMediaChannels.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditSearchMediaChannels.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridMediaChannels, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEdit2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureEdit3.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureEdit4.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents ImageList16x16 As System.Windows.Forms.ImageList
    Friend WithEvents XtraTabControl1 As DevExpress.XtraTab.XtraTabControl
    Friend WithEvents TabPageDetails As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents GroupControlItems As GroupControl
    Friend WithEvents LabelControl11 As LabelControl
    Friend WithEvents TextEditSearch As TextEdit
    Friend WithEvents PictureClearSearch As PictureEdit
    Friend WithEvents PictureAdvancedSearch As PictureEdit
    Friend WithEvents ButtonDelete As SimpleButton
    Friend WithEvents ButtonAdd As SimpleButton
    Friend WithEvents ButtonEdit As SimpleButton
    Friend WithEvents GridItems As DataGridView
    Friend WithEvents TabPageMediaGrouping As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents GroupStoreGroupings As GroupControl
    Friend WithEvents PictureAdvancedSearchMediaGroup As PictureEdit
    Friend WithEvents TextEditSearchMediaGroup As TextEdit
    Friend WithEvents LabelControl1 As LabelControl
    Friend WithEvents ButtonDeleteMediaGroup As SimpleButton
    Friend WithEvents ButtonEditMediaGroup As SimpleButton
    Friend WithEvents ButtonAddMediaGroup As SimpleButton
    Friend WithEvents GridMediaGrouping As DataGridView
    Friend WithEvents LabelSearchStoreGroupings As LabelControl
    Friend WithEvents TextEditSearchStoreGroupings As TextEdit
    Friend WithEvents PictureClearSearchInstallationTeams As PictureEdit
    Friend WithEvents PictureAdvancedSearchInstallationTeams As PictureEdit
    Friend WithEvents TabPageMediaChannels As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents PictureClearSearchMediaGroup As PictureEdit
    Friend WithEvents MediaGroupingNameColumn As DataGridViewTextBoxColumn
    Friend WithEvents GroupControl1 As GroupControl
    Friend WithEvents PictureClearSearchMediaChannels As PictureEdit
    Friend WithEvents PictureAdvancedSearchMediaChannels As PictureEdit
    Friend WithEvents TextEditSearchMediaChannels As TextEdit
    Friend WithEvents LabelControl2 As LabelControl
    Friend WithEvents ButtonDeleteMediaChannels As SimpleButton
    Friend WithEvents ButtonEditMediaChannels As SimpleButton
    Friend WithEvents ButtonAddMediaChannels As SimpleButton
    Friend WithEvents GridMediaChannels As DataGridView
    Friend WithEvents LabelControl3 As LabelControl
    Friend WithEvents TextEdit2 As TextEdit
    Friend WithEvents PictureEdit3 As PictureEdit
    Friend WithEvents PictureEdit4 As PictureEdit
    Friend WithEvents DataGridViewTextBoxColumn1 As DataGridViewTextBoxColumn
    Friend WithEvents MediaNameColumn As DataGridViewTextBoxColumn
    Friend WithEvents HomesiteColumn As DataGridViewCheckBoxColumn
    Friend WithEvents CrossoverColumn As DataGridViewCheckBoxColumn
    Friend WithEvents isPNPPcaStatusColumn As DataGridViewCheckBoxColumn
End Class
