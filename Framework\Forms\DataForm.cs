﻿using Framework.Controls;
using System.Windows.Forms;

namespace Framework.Forms
{
    public partial class DataForm : Form
    {

        #region Startup

        public DataForm()
        {
            InitializeComponent();
            SetAppearanceProperties();
            DirtyStateManager = new DirtyStateManager(this);
            ErrorStateManager = new ErrorStateManager(this);
            SubscribeToThemeColorChangedEvent();
            Load += DataForm_Load;
        }

        private void SetAppearanceProperties()
        {
            Icon = FrameworkSettings.Icons.DataFormIcon;
            Font = FrameworkSettings.Fonts.FORMFONT;
            ForeColor = FrameworkSettings.Colors.FORMFORECOLOR;
            BackColor = FrameworkSettings.Colors.FORMBACKCOLOR;
        }

        private void DataForm_Load(object sender, System.EventArgs e)
        {
            DirtyStateManager.InitializeState();
            ErrorStateManager.InitializeState();
            ApplyTheme();
        }

        #endregion


        #region Dirty state

        private DirtyStateManager _DirtyStateManager;
        public DirtyStateManager DirtyStateManager
        {
            get { return _DirtyStateManager; }
            private set { _DirtyStateManager = value; }
        }

        #endregion


        #region Error state

        private ErrorStateManager _ErrorStateManager;
        public ErrorStateManager ErrorStateManager
        {
            get { return _ErrorStateManager; }
            private set { _ErrorStateManager = value; }
        }

        #endregion


        #region Theme color

        protected virtual void SubscribeToThemeColorChangedEvent()
        {
            FrameworkSettings.Colors.ThemeColorChanged += User_ThemeColorChanged;
        }

        private void User_ThemeColorChanged()
        {
            ApplyTheme();
        }

        protected virtual void ApplyTheme()
        {
            ApplyThemeToAllChildren(this);
        }

        private void ApplyThemeToAllChildren(Control parentcontrol)
        {
            for (int i = 0; i < parentcontrol.Controls.Count; i++)
            {
                Control childcontrol = parentcontrol.Controls[i];
                if (childcontrol is FlatButton)
                {
                    ((FlatButton)childcontrol).ApplyTheme();
                }
                else
                {
                    if (childcontrol.HasChildren)
                    {
                        ApplyThemeToAllChildren(childcontrol);
                    }
                }
            }
        }

        #endregion

    }
}
