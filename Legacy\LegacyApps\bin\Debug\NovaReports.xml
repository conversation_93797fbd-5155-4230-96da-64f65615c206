﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
NovaReports
</name>
</assembly>
<members>
<member name="T:NovaReports.My.Resources.Resources">
<summary>
  A strongly-typed resource class, for looking up localized strings, etc.
</summary>
</member>
<member name="P:NovaReports.My.Resources.Resources.ResourceManager">
<summary>
  Returns the cached ResourceManager instance used by this class.
</summary>
</member>
<member name="P:NovaReports.My.Resources.Resources.Culture">
<summary>
  Overrides the current thread's CurrentUICulture property for all
  resource lookups using this strongly typed resource class.
</summary>
</member>
<member name="T:NovaReports.DataSetContractReport">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:NovaReports.DataSetContractReport.ContractDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaReports.DataSetContractReport.BillingInstructionDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaReports.DataSetContractReport.BurstDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaReports.DataSetContractReport.ContractInventoryDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaReports.DataSetContractReport.MiscellaneousChargeDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaReports.DataSetContractReport.ResearchContractDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaReports.DataSetContractReport.ResearchCategoryDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaReports.DataSetContractReport.ContractRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaReports.DataSetContractReport.BillingInstructionRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaReports.DataSetContractReport.BurstRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaReports.DataSetContractReport.ContractInventoryRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaReports.DataSetContractReport.MiscellaneousChargeRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaReports.DataSetContractReport.ResearchContractRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaReports.DataSetContractReport.ResearchCategoryRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaReports.DataSetContractReport.ContractRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaReports.DataSetContractReport.BillingInstructionRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaReports.DataSetContractReport.BurstRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaReports.DataSetContractReport.ContractInventoryRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaReports.DataSetContractReport.MiscellaneousChargeRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaReports.DataSetContractReport.ResearchContractRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaReports.DataSetContractReport.ResearchCategoryRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaReports.DataSetContractReportTableAdapters.ContractTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaReports.DataSetContractReportTableAdapters.BillingInstructionTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaReports.DataSetContractReportTableAdapters.BurstTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaReports.DataSetContractReportTableAdapters.ContractInventoryTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaReports.DataSetContractReportTableAdapters.MiscellaneousChargeTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaReports.DataSetContractReportTableAdapters.ResearchContractTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaReports.DataSetContractReportTableAdapters.ResearchCategoryTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
</members>
</doc>
