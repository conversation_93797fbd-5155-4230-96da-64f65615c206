﻿using RestSharp;
using RestSharp.Extensions.MonoHttp;
using System;
using System.Net.NetworkInformation;
using Universal.Entities;

namespace DataAccess
{
    public static class ApiClient
    {
        private static string AUTHENTICATIONSERVER = Universal.Settings.AUTHENTICATIONSERVER;   // Defaults to test server if run in debugger.
        private static string PINGSERVER = Universal.Settings.PINGSERVER;

        public static Session GetNewSession(ref string errormessage, string username, string password)
        {
            var client = ApiClientFactory.GetApiClient(AUTHENTICATIONSERVER);
            var isConnected = IsConnectedToNetwork(PINGSERVER, ref errormessage);

            string windowsuser = Environment.UserName;
            string windowsdomain = Environment.UserDomainName;
            string computername = Environment.MachineName;
            string applicationname = Universal.Settings.APPLICATIONNAME;

            var request = new RestRequest("sessions", Method.GET);

            request.AddQueryParameter("username", username);
            request.AddQueryParameter("password", password);

//#if DEBUG
            //request.AddQueryParameter("windowsdomain", "PRIMEDIA");
            //request.AddQueryParameter("windowsuser", "jacqques");
//#else
            request.AddQueryParameter("windowsdomain", windowsdomain);
            request.AddQueryParameter("windowsuser", windowsuser);
//#endif
            request.AddQueryParameter("computername", computername);
            request.AddQueryParameter("applicationname", applicationname);

            IRestResponse<Session> response = client.Execute<Session>(request);
//#if DEBUG
//#else
            if (isConnected)
            {
                CheckResponseForErrors(ref errormessage, response);
                CheckResponseDataForErrors(ref errormessage, response.Data);
            }
//#endif

            return response.Data;
        }

        public static void SendPasswordResetCode(ref string errormessage, string usernameoremailaddress)
        {
            var client = ApiClientFactory.GetApiClient(AUTHENTICATIONSERVER);
            var isConnected = IsConnectedToNetwork(PINGSERVER, ref errormessage);

            var request = new RestRequest("passwordresetcodes");
            request.AddQueryParameter("usernameoremail", usernameoremailaddress);

            if (isConnected)
            {
                IRestResponse<DataTransferObject> response = client.Execute<DataTransferObject>(request);
                CheckResponseForErrors(ref errormessage, response);
                CheckResponseDataForErrors(ref errormessage, response.Data);
            }
        }

        public static void ResetPassword
            (ref string errormessage, string usernameoremailaddress, string passwordresetcode, string newpassword)
        {
            var client = ApiClientFactory.GetApiClient(AUTHENTICATIONSERVER);
            var isConnected = IsConnectedToNetwork(PINGSERVER, ref errormessage);

            var request = new RestRequest("passwords", Method.PUT);
            request.AddQueryParameter("usernameoremail", usernameoremailaddress);
            request.AddQueryParameter("passwordresetcode", passwordresetcode);
            request.AddQueryParameter("newpassword", newpassword);

            if (isConnected)
            {

                IRestResponse<DataTransferObject> response = client.Execute<DataTransferObject>(request);
                CheckResponseForErrors(ref errormessage, response);
                CheckResponseDataForErrors(ref errormessage, response.Data);

            }
        }

        public static void ChangePassword(ref string errormessage, Guid sessionid, string currentpassword, string newpassword)
        {
            var client = ApiClientFactory.GetApiClient(AUTHENTICATIONSERVER);
            var isConnected = IsConnectedToNetwork(PINGSERVER, ref errormessage);

            var request = new RestRequest("passwords", Method.PUT);
            request.AddQueryParameter("sessionid", sessionid.ToString());
            request.AddQueryParameter("currentpassword", currentpassword);
            request.AddQueryParameter("newpassword", newpassword);

            if (isConnected)
            {
                IRestResponse<DataTransferObject> response = client.Execute<DataTransferObject>(request);
                CheckResponseForErrors(ref errormessage, response);
                CheckResponseDataForErrors(ref errormessage, response.Data);
            }
        }

        public static void SendEmailVerificationCode(ref string errormessage, string emailaddress)
        {
            var client = ApiClientFactory.GetApiClient(AUTHENTICATIONSERVER);
            var isConnected = IsConnectedToNetwork(PINGSERVER, ref errormessage);

            var request = new RestRequest("emailverificationcodes", Method.GET);
            request.AddQueryParameter("emailaddress", emailaddress);

            if (isConnected)
            {
                IRestResponse<DataTransferObject> response = client.Execute<DataTransferObject>(request);
                CheckResponseForErrors(ref errormessage, response);
                CheckResponseDataForErrors(ref errormessage, response.Data);
            }
        }

        public static void CreateUser
            (
            ref string errormessage,
            string emailaddress,
            string emailverificationcode,
            string username,
            string password,
            string firstname,
            string lastname,
            string mobilephone,
            bool gender
            )
        {
            var client = ApiClientFactory.GetApiClient(AUTHENTICATIONSERVER);
            var isConnected = IsConnectedToNetwork(PINGSERVER, ref errormessage);

            var request = new RestRequest("users", Method.POST);
            request.AddQueryParameter("email", emailaddress);
            request.AddQueryParameter("emailverificationcode", emailverificationcode);
            request.AddQueryParameter("username", username);
            request.AddQueryParameter("password", password);
            request.AddQueryParameter("firstname", firstname);
            request.AddQueryParameter("lastname", lastname);
            request.AddQueryParameter("mobilephone", mobilephone);
            request.AddQueryParameter("gender", gender.ToString());

            if (isConnected)
            {
                IRestResponse<DataTransferObject> response = client.Execute<DataTransferObject>(request);
                CheckResponseForErrors(ref errormessage, response);
                CheckResponseDataForErrors(ref errormessage, response.Data);
            }
        }

        public static void SendForgottenUsername(ref string errormessage, string emailaddress)
        {
            var client = ApiClientFactory.GetApiClient(AUTHENTICATIONSERVER);
            var isConnected = IsConnectedToNetwork(PINGSERVER, ref errormessage);

            var request = new RestRequest("forgottenusernames", Method.GET);
            request.AddQueryParameter("emailaddress", emailaddress);

            if (isConnected)
            {
                IRestResponse<DataTransferObject> response = client.Execute<DataTransferObject>(request);
                CheckResponseDataForErrors(ref errormessage, response.Data);
            }
        }


        #region Error checking

        /// <summary>
        /// Checks to see if network is available by pinging the server. 
        /// Should this checking method fail, Check to see if the ISP is not configured to block pinging
        /// </summary>
        /// <param name="authenicationserver"></param>
        /// <param name="errormessage"></param>
        /// <returns></returns>
        public static bool IsConnectedToNetwork(string authenicationserver, ref string errormessage)
        {
            bool result = false;
            Ping ping = new Ping();
            try
            {
                PingReply reply = ping.Send(authenicationserver, 5000);

                if (reply.Status == IPStatus.Success)
                {
                    return true;
                }
                else
                {
                    errormessage = string.Format("Failed Connection. Trying to connect to the following domain: {0}.\n Please check that your pc is connected to the (right) network then try again.", authenicationserver);

                    return false;
                }
            }
            catch (Exception ex)
            {
                errormessage = string.Format("Failed Connection. Trying to connect to the following domain: {0}.\n Following error occured: {1}.", authenicationserver, ex.Message);
            }

            return result;
        }

        private static void CheckResponseForErrors(ref string errormessage, IRestResponse response)
        {
            if (string.IsNullOrEmpty(errormessage))
            {
                if (response.StatusCode != System.Net.HttpStatusCode.OK)
                {
                    errormessage = "ApiClient.CheckResponseForErrors - API response status code is " + response.StatusCode.ToString() + ".";
                }
            }
        }

        private static void CheckResponseDataForErrors(ref string errormessage, DataTransferObject responsedata)
        {
            if (string.IsNullOrEmpty(errormessage))
            {
                if (responsedata == null)
                {
                    errormessage = "ApiClient.CheckResponseDataForErrors - No response from the API.";
                    return;
                }
                if (string.IsNullOrEmpty(responsedata.ErrorMessage) == false)
                {
                    errormessage = responsedata.ErrorMessage;
                    return;
                }
            }
        }

        #endregion

    }

    internal class ApiClientFactory
    {
        internal static RestClient GetApiClient(string authenticationserver)
        {
            string baseurl = @"http://" + authenticationserver + "/api/";

          //  var baseUri = new Uri(HttpUtility.UrlEncode(string.Concat("http://", authenticationserver, "/")));
            return new RestClient(baseurl);
        }
    }
}
