﻿using DataAccess;

namespace DataService.Security
{
    class DeleteUsersCommandExecutor : CommandExecutor<DeleteUsersCommand>
    {

        public override void Execute(DeleteUsersCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.DeleteUsers))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.AddInputParameter("users", command.Users);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
            }
        }

    }
}
