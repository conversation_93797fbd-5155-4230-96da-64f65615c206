<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformMediaGap
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformMediaGap))
        Dim SuperToolTip3 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem3 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem3 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip1 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem1 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem1 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle14 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle15 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle20 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle16 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle17 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle18 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle19 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle21 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle22 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle23 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle24 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle25 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle26 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Me.ImageList16x16 = New System.Windows.Forms.ImageList()
        Me.GroupControlProvisionalBookings = New DevExpress.XtraEditors.GroupControl()
        Me.LabelControl7 = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearchProvisionalBooking = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearchProvisionalBooking = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearchProvisionalBooking = New DevExpress.XtraEditors.PictureEdit()
        Me.ButtonDelete = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAdd = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonEdit = New DevExpress.XtraEditors.SimpleButton()
        Me.GridProvisionalBookings = New System.Windows.Forms.DataGridView()
        Me.DescriptionColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.MediaFamilyNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.CategoryColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.ChainColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.BrandColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.FirstWeekColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.LastWeekColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DurationColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.BookedByColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.BookTimeColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.ExpiryTimeColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.GroupControlContracts = New DevExpress.XtraEditors.GroupControl()
        Me.GridContracts = New System.Windows.Forms.DataGridView()
        Me.ContractNumberColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.ButtonInfo = New DevExpress.XtraEditors.SimpleButton()
        Me.GroupControlMediaGapGrid = New DevExpress.XtraEditors.GroupControl()
        Me.GridMediaGapGrid = New System.Windows.Forms.DataGridView()
        Me.GroupControlViewOptions = New DevExpress.XtraEditors.GroupControl()
        Me.PanelViewOptions = New System.Windows.Forms.Panel()
        Me.ButtonSelectAll = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonQuery = New DevExpress.XtraEditors.SimpleButton()
        Me.ImageList24x24 = New System.Windows.Forms.ImageList()
        Me.Panel3 = New System.Windows.Forms.Panel()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.HyperlinkBookingList = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkGridView = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkFromDate = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkCategories = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkMediaFamilies = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkChains = New DevExpress.XtraEditors.LabelControl()
        Me.ButtonClear = New DevExpress.XtraEditors.SimpleButton()
        Me.TableLayoutPanelMediaGap = New System.Windows.Forms.TableLayoutPanel()
        Me.SimpleButton1 = New DevExpress.XtraEditors.SimpleButton()
        Me.SimpleButton2 = New DevExpress.XtraEditors.SimpleButton()
        CType(Me.GroupControlProvisionalBookings, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlProvisionalBookings.SuspendLayout()
        CType(Me.TextEditSearchProvisionalBooking.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearchProvisionalBooking.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearchProvisionalBooking.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridProvisionalBookings, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControlContracts, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlContracts.SuspendLayout()
        CType(Me.GridContracts, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControlMediaGapGrid, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlMediaGapGrid.SuspendLayout()
        CType(Me.GridMediaGapGrid, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControlViewOptions, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlViewOptions.SuspendLayout()
        Me.PanelViewOptions.SuspendLayout()
        Me.TableLayoutPanelMediaGap.SuspendLayout()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!)
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Size = New System.Drawing.Size(0, 30)
        Me.LabelTitle.TabIndex = 0
        Me.LabelTitle.Text = ""
        '
        'ImageList16x16
        '
        Me.ImageList16x16.ImageStream = CType(resources.GetObject("ImageList16x16.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList16x16.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList16x16.Images.SetKeyName(0, "add.png")
        Me.ImageList16x16.Images.SetKeyName(1, "pencil.png")
        Me.ImageList16x16.Images.SetKeyName(2, "delete.png")
        Me.ImageList16x16.Images.SetKeyName(3, "upload.png")
        Me.ImageList16x16.Images.SetKeyName(4, "refresh.png")
        Me.ImageList16x16.Images.SetKeyName(5, "info.png")
        Me.ImageList16x16.Images.SetKeyName(6, "clock.png")
        '
        'GroupControlProvisionalBookings
        '
        Me.GroupControlProvisionalBookings.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlProvisionalBookings.Appearance.Options.UseFont = True
        Me.GroupControlProvisionalBookings.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlProvisionalBookings.AppearanceCaption.Options.UseFont = True
        Me.GroupControlProvisionalBookings.Controls.Add(Me.LabelControl7)
        Me.GroupControlProvisionalBookings.Controls.Add(Me.TextEditSearchProvisionalBooking)
        Me.GroupControlProvisionalBookings.Controls.Add(Me.PictureClearSearchProvisionalBooking)
        Me.GroupControlProvisionalBookings.Controls.Add(Me.PictureAdvancedSearchProvisionalBooking)
        Me.GroupControlProvisionalBookings.Controls.Add(Me.ButtonDelete)
        Me.GroupControlProvisionalBookings.Controls.Add(Me.ButtonAdd)
        Me.GroupControlProvisionalBookings.Controls.Add(Me.ButtonEdit)
        Me.GroupControlProvisionalBookings.Controls.Add(Me.GridProvisionalBookings)
        Me.GroupControlProvisionalBookings.Dock = System.Windows.Forms.DockStyle.Fill
        Me.GroupControlProvisionalBookings.Location = New System.Drawing.Point(135, 321)
        Me.GroupControlProvisionalBookings.LookAndFeel.SkinName = "Black"
        Me.GroupControlProvisionalBookings.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlProvisionalBookings.Margin = New System.Windows.Forms.Padding(0)
        Me.GroupControlProvisionalBookings.Name = "GroupControlProvisionalBookings"
        Me.GroupControlProvisionalBookings.Size = New System.Drawing.Size(489, 204)
        Me.GroupControlProvisionalBookings.TabIndex = 2
        Me.GroupControlProvisionalBookings.Text = "Provisional Bookings in the Selected Area"
        '
        'LabelControl7
        '
        Me.LabelControl7.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl7.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl7.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl7.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl7.Location = New System.Drawing.Point(331, 180)
        Me.LabelControl7.Name = "LabelControl7"
        Me.LabelControl7.Size = New System.Drawing.Size(45, 13)
        Me.LabelControl7.TabIndex = 5
        Me.LabelControl7.Text = "Search:"
        '
        'TextEditSearchProvisionalBooking
        '
        Me.TextEditSearchProvisionalBooking.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchProvisionalBooking.EditValue = ""
        Me.TextEditSearchProvisionalBooking.Location = New System.Drawing.Point(382, 177)
        Me.TextEditSearchProvisionalBooking.Margin = New System.Windows.Forms.Padding(3, 4, 3, 5)
        Me.TextEditSearchProvisionalBooking.Name = "TextEditSearchProvisionalBooking"
        Me.TextEditSearchProvisionalBooking.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchProvisionalBooking.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchProvisionalBooking.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchProvisionalBooking.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchProvisionalBooking.Size = New System.Drawing.Size(80, 20)
        Me.TextEditSearchProvisionalBooking.TabIndex = 6
        '
        'PictureClearSearchProvisionalBooking
        '
        Me.PictureClearSearchProvisionalBooking.AllowDrop = True
        Me.PictureClearSearchProvisionalBooking.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchProvisionalBooking.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchProvisionalBooking.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchProvisionalBooking.Location = New System.Drawing.Point(468, 3)
        Me.PictureClearSearchProvisionalBooking.Name = "PictureClearSearchProvisionalBooking"
        Me.PictureClearSearchProvisionalBooking.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchProvisionalBooking.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchProvisionalBooking.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchProvisionalBooking.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem3.Text = "Clear Search"
        ToolTipItem3.LeftIndent = 6
        ToolTipItem3.Text = "Click here to clear all search boxes."
        SuperToolTip3.Items.Add(ToolTipTitleItem3)
        SuperToolTip3.Items.Add(ToolTipItem3)
        Me.PictureClearSearchProvisionalBooking.SuperTip = SuperToolTip3
        Me.PictureClearSearchProvisionalBooking.TabIndex = 0
        Me.PictureClearSearchProvisionalBooking.TabStop = True
        '
        'PictureAdvancedSearchProvisionalBooking
        '
        Me.PictureAdvancedSearchProvisionalBooking.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearchProvisionalBooking.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearchProvisionalBooking.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearchProvisionalBooking.Location = New System.Drawing.Point(468, 179)
        Me.PictureAdvancedSearchProvisionalBooking.Margin = New System.Windows.Forms.Padding(3, 6, 3, 7)
        Me.PictureAdvancedSearchProvisionalBooking.Name = "PictureAdvancedSearchProvisionalBooking"
        Me.PictureAdvancedSearchProvisionalBooking.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearchProvisionalBooking.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearchProvisionalBooking.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearchProvisionalBooking.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem1.Text = "Advanced Search"
        ToolTipItem1.LeftIndent = 6
        ToolTipItem1.Text = "Click here to search individual column values."
        SuperToolTip1.Items.Add(ToolTipTitleItem1)
        SuperToolTip1.Items.Add(ToolTipItem1)
        Me.PictureAdvancedSearchProvisionalBooking.SuperTip = SuperToolTip1
        Me.PictureAdvancedSearchProvisionalBooking.TabIndex = 7
        Me.PictureAdvancedSearchProvisionalBooking.TabStop = True
        '
        'ButtonDelete
        '
        Me.ButtonDelete.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonDelete.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonDelete.Appearance.Options.UseFont = True
        Me.ButtonDelete.ImageIndex = 2
        Me.ButtonDelete.ImageList = Me.ImageList16x16
        Me.ButtonDelete.Location = New System.Drawing.Point(167, 176)
        Me.ButtonDelete.LookAndFeel.SkinName = "Black"
        Me.ButtonDelete.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonDelete.Name = "ButtonDelete"
        Me.ButtonDelete.Size = New System.Drawing.Size(75, 23)
        Me.ButtonDelete.TabIndex = 4
        Me.ButtonDelete.Text = "Delete"
        '
        'ButtonAdd
        '
        Me.ButtonAdd.AccessibleRole = System.Windows.Forms.AccessibleRole.None
        Me.ButtonAdd.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAdd.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAdd.Appearance.Options.UseFont = True
        Me.ButtonAdd.ImageIndex = 0
        Me.ButtonAdd.ImageList = Me.ImageList16x16
        Me.ButtonAdd.Location = New System.Drawing.Point(5, 176)
        Me.ButtonAdd.LookAndFeel.SkinName = "Black"
        Me.ButtonAdd.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAdd.Name = "ButtonAdd"
        Me.ButtonAdd.Size = New System.Drawing.Size(75, 23)
        Me.ButtonAdd.TabIndex = 2
        Me.ButtonAdd.Text = "Add"
        '
        'ButtonEdit
        '
        Me.ButtonEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonEdit.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonEdit.Appearance.Options.UseFont = True
        Me.ButtonEdit.ImageIndex = 1
        Me.ButtonEdit.ImageList = Me.ImageList16x16
        Me.ButtonEdit.Location = New System.Drawing.Point(86, 176)
        Me.ButtonEdit.LookAndFeel.SkinName = "Black"
        Me.ButtonEdit.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonEdit.Name = "ButtonEdit"
        Me.ButtonEdit.Size = New System.Drawing.Size(75, 23)
        Me.ButtonEdit.TabIndex = 3
        Me.ButtonEdit.Text = "Edit"
        '
        'GridProvisionalBookings
        '
        Me.GridProvisionalBookings.AllowUserToAddRows = False
        Me.GridProvisionalBookings.AllowUserToDeleteRows = False
        Me.GridProvisionalBookings.AllowUserToOrderColumns = True
        Me.GridProvisionalBookings.AllowUserToResizeRows = False
        DataGridViewCellStyle14.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridProvisionalBookings.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle14
        Me.GridProvisionalBookings.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridProvisionalBookings.BackgroundColor = System.Drawing.Color.White
        Me.GridProvisionalBookings.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridProvisionalBookings.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridProvisionalBookings.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle15.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle15.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle15.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle15.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle15.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle15.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle15.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridProvisionalBookings.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle15
        Me.GridProvisionalBookings.ColumnHeadersHeight = 22
        Me.GridProvisionalBookings.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridProvisionalBookings.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.DescriptionColumn, Me.MediaFamilyNameColumn, Me.CategoryColumn, Me.ChainColumn, Me.BrandColumn, Me.FirstWeekColumn, Me.LastWeekColumn, Me.DurationColumn, Me.BookedByColumn, Me.BookTimeColumn, Me.ExpiryTimeColumn})
        Me.GridProvisionalBookings.EnableHeadersVisualStyles = False
        Me.GridProvisionalBookings.GridColor = System.Drawing.Color.White
        Me.GridProvisionalBookings.Location = New System.Drawing.Point(2, 22)
        Me.GridProvisionalBookings.Name = "GridProvisionalBookings"
        Me.GridProvisionalBookings.ReadOnly = True
        Me.GridProvisionalBookings.RowHeadersVisible = False
        DataGridViewCellStyle20.ForeColor = System.Drawing.Color.DimGray
        Me.GridProvisionalBookings.RowsDefaultCellStyle = DataGridViewCellStyle20
        Me.GridProvisionalBookings.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridProvisionalBookings.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridProvisionalBookings.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridProvisionalBookings.RowTemplate.Height = 19
        Me.GridProvisionalBookings.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridProvisionalBookings.ShowCellToolTips = False
        Me.GridProvisionalBookings.Size = New System.Drawing.Size(485, 148)
        Me.GridProvisionalBookings.StandardTab = True
        Me.GridProvisionalBookings.TabIndex = 1
        '
        'DescriptionColumn
        '
        Me.DescriptionColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.DescriptionColumn.DataPropertyName = "ProvisionalBookingName"
        Me.DescriptionColumn.HeaderText = "Description"
        Me.DescriptionColumn.Name = "DescriptionColumn"
        Me.DescriptionColumn.ReadOnly = True
        Me.DescriptionColumn.Width = 95
        '
        'MediaFamilyNameColumn
        '
        Me.MediaFamilyNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.MediaFamilyNameColumn.DataPropertyName = "MediaFamilyName"
        Me.MediaFamilyNameColumn.HeaderText = "Media Family"
        Me.MediaFamilyNameColumn.Name = "MediaFamilyNameColumn"
        Me.MediaFamilyNameColumn.ReadOnly = True
        Me.MediaFamilyNameColumn.Width = 105
        '
        'CategoryColumn
        '
        Me.CategoryColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.CategoryColumn.DataPropertyName = "CategoryName"
        Me.CategoryColumn.HeaderText = "Category"
        Me.CategoryColumn.Name = "CategoryColumn"
        Me.CategoryColumn.ReadOnly = True
        Me.CategoryColumn.Width = 84
        '
        'ChainColumn
        '
        Me.ChainColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.ChainColumn.DataPropertyName = "ChainName"
        Me.ChainColumn.HeaderText = "Chain"
        Me.ChainColumn.Name = "ChainColumn"
        Me.ChainColumn.ReadOnly = True
        Me.ChainColumn.Width = 64
        '
        'BrandColumn
        '
        Me.BrandColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.BrandColumn.DataPropertyName = "BrandName"
        Me.BrandColumn.HeaderText = "Brand"
        Me.BrandColumn.Name = "BrandColumn"
        Me.BrandColumn.ReadOnly = True
        Me.BrandColumn.Width = 65
        '
        'FirstWeekColumn
        '
        Me.FirstWeekColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.FirstWeekColumn.DataPropertyName = "FirstWeek"
        DataGridViewCellStyle16.Format = "d"
        DataGridViewCellStyle16.NullValue = Nothing
        Me.FirstWeekColumn.DefaultCellStyle = DataGridViewCellStyle16
        Me.FirstWeekColumn.HeaderText = "First Week"
        Me.FirstWeekColumn.Name = "FirstWeekColumn"
        Me.FirstWeekColumn.ReadOnly = True
        Me.FirstWeekColumn.Width = 91
        '
        'LastWeekColumn
        '
        Me.LastWeekColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.LastWeekColumn.DataPropertyName = "LastWeek"
        DataGridViewCellStyle17.Format = "d"
        Me.LastWeekColumn.DefaultCellStyle = DataGridViewCellStyle17
        Me.LastWeekColumn.HeaderText = "Last Week"
        Me.LastWeekColumn.Name = "LastWeekColumn"
        Me.LastWeekColumn.ReadOnly = True
        Me.LastWeekColumn.Width = 90
        '
        'DurationColumn
        '
        Me.DurationColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.DurationColumn.DataPropertyName = "Duration"
        Me.DurationColumn.HeaderText = "Duration"
        Me.DurationColumn.Name = "DurationColumn"
        Me.DurationColumn.ReadOnly = True
        Me.DurationColumn.Width = 80
        '
        'BookedByColumn
        '
        Me.BookedByColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.BookedByColumn.DataPropertyName = "BookedBy"
        Me.BookedByColumn.HeaderText = "Booked By"
        Me.BookedByColumn.Name = "BookedByColumn"
        Me.BookedByColumn.ReadOnly = True
        Me.BookedByColumn.Width = 93
        '
        'BookTimeColumn
        '
        Me.BookTimeColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.BookTimeColumn.DataPropertyName = "BookTime"
        DataGridViewCellStyle18.Format = "dd-MM-yyyy HH:mm"
        DataGridViewCellStyle18.NullValue = Nothing
        Me.BookTimeColumn.DefaultCellStyle = DataGridViewCellStyle18
        Me.BookTimeColumn.HeaderText = "Book Time"
        Me.BookTimeColumn.Name = "BookTimeColumn"
        Me.BookTimeColumn.ReadOnly = True
        Me.BookTimeColumn.Width = 92
        '
        'ExpiryTimeColumn
        '
        Me.ExpiryTimeColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.ExpiryTimeColumn.DataPropertyName = "ExpiryTime"
        DataGridViewCellStyle19.Format = "dd-MM-yyyy HH:mm"
        Me.ExpiryTimeColumn.DefaultCellStyle = DataGridViewCellStyle19
        Me.ExpiryTimeColumn.HeaderText = "Expiry Time"
        Me.ExpiryTimeColumn.Name = "ExpiryTimeColumn"
        Me.ExpiryTimeColumn.ReadOnly = True
        Me.ExpiryTimeColumn.Width = 99
        '
        'GroupControlContracts
        '
        Me.GroupControlContracts.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlContracts.Appearance.Options.UseFont = True
        Me.GroupControlContracts.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlContracts.AppearanceCaption.Options.UseFont = True
        Me.GroupControlContracts.Controls.Add(Me.GridContracts)
        Me.GroupControlContracts.Controls.Add(Me.ButtonInfo)
        Me.GroupControlContracts.Dock = System.Windows.Forms.DockStyle.Fill
        Me.GroupControlContracts.Location = New System.Drawing.Point(0, 321)
        Me.GroupControlContracts.LookAndFeel.SkinName = "Black"
        Me.GroupControlContracts.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlContracts.Margin = New System.Windows.Forms.Padding(0)
        Me.GroupControlContracts.Name = "GroupControlContracts"
        Me.GroupControlContracts.Size = New System.Drawing.Size(120, 204)
        Me.GroupControlContracts.TabIndex = 1
        Me.GroupControlContracts.Text = "Contracts"
        '
        'GridContracts
        '
        Me.GridContracts.AllowUserToAddRows = False
        Me.GridContracts.AllowUserToDeleteRows = False
        Me.GridContracts.AllowUserToOrderColumns = True
        Me.GridContracts.AllowUserToResizeRows = False
        DataGridViewCellStyle21.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridContracts.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle21
        Me.GridContracts.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridContracts.BackgroundColor = System.Drawing.Color.White
        Me.GridContracts.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridContracts.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridContracts.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle22.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle22.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle22.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle22.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle22.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle22.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle22.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridContracts.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle22
        Me.GridContracts.ColumnHeadersHeight = 22
        Me.GridContracts.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridContracts.ColumnHeadersVisible = False
        Me.GridContracts.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.ContractNumberColumn})
        Me.GridContracts.EnableHeadersVisualStyles = False
        Me.GridContracts.GridColor = System.Drawing.Color.White
        Me.GridContracts.Location = New System.Drawing.Point(2, 22)
        Me.GridContracts.MultiSelect = False
        Me.GridContracts.Name = "GridContracts"
        Me.GridContracts.ReadOnly = True
        Me.GridContracts.RowHeadersVisible = False
        DataGridViewCellStyle23.ForeColor = System.Drawing.Color.DimGray
        Me.GridContracts.RowsDefaultCellStyle = DataGridViewCellStyle23
        Me.GridContracts.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridContracts.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridContracts.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridContracts.RowTemplate.Height = 19
        Me.GridContracts.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridContracts.ShowCellToolTips = False
        Me.GridContracts.Size = New System.Drawing.Size(116, 148)
        Me.GridContracts.StandardTab = True
        Me.GridContracts.TabIndex = 0
        '
        'ContractNumberColumn
        '
        Me.ContractNumberColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.ContractNumberColumn.DataPropertyName = "ContractNumber"
        Me.ContractNumberColumn.HeaderText = "Contract Number"
        Me.ContractNumberColumn.Name = "ContractNumberColumn"
        Me.ContractNumberColumn.ReadOnly = True
        '
        'ButtonInfo
        '
        Me.ButtonInfo.AccessibleRole = System.Windows.Forms.AccessibleRole.None
        Me.ButtonInfo.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonInfo.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonInfo.Appearance.Options.UseFont = True
        Me.ButtonInfo.Enabled = False
        Me.ButtonInfo.ImageIndex = 5
        Me.ButtonInfo.ImageList = Me.ImageList16x16
        Me.ButtonInfo.Location = New System.Drawing.Point(5, 176)
        Me.ButtonInfo.LookAndFeel.SkinName = "Black"
        Me.ButtonInfo.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonInfo.Name = "ButtonInfo"
        Me.ButtonInfo.Size = New System.Drawing.Size(75, 23)
        Me.ButtonInfo.TabIndex = 1
        Me.ButtonInfo.Text = "Info"
        '
        'GroupControlMediaGapGrid
        '
        Me.GroupControlMediaGapGrid.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlMediaGapGrid.Appearance.Options.UseFont = True
        Me.GroupControlMediaGapGrid.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlMediaGapGrid.AppearanceCaption.Options.UseFont = True
        Me.TableLayoutPanelMediaGap.SetColumnSpan(Me.GroupControlMediaGapGrid, 3)
        Me.GroupControlMediaGapGrid.Controls.Add(Me.GridMediaGapGrid)
        Me.GroupControlMediaGapGrid.Dock = System.Windows.Forms.DockStyle.Fill
        Me.GroupControlMediaGapGrid.Location = New System.Drawing.Point(0, 0)
        Me.GroupControlMediaGapGrid.LookAndFeel.SkinName = "Black"
        Me.GroupControlMediaGapGrid.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlMediaGapGrid.Margin = New System.Windows.Forms.Padding(0)
        Me.GroupControlMediaGapGrid.Name = "GroupControlMediaGapGrid"
        Me.GroupControlMediaGapGrid.Size = New System.Drawing.Size(624, 306)
        Me.GroupControlMediaGapGrid.TabIndex = 0
        Me.GroupControlMediaGapGrid.Text = "Media Gap Grid"
        '
        'GridMediaGapGrid
        '
        Me.GridMediaGapGrid.AllowUserToAddRows = False
        Me.GridMediaGapGrid.AllowUserToDeleteRows = False
        Me.GridMediaGapGrid.AllowUserToResizeColumns = False
        Me.GridMediaGapGrid.AllowUserToResizeRows = False
        DataGridViewCellStyle24.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridMediaGapGrid.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle24
        Me.GridMediaGapGrid.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridMediaGapGrid.BackgroundColor = System.Drawing.Color.White
        Me.GridMediaGapGrid.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridMediaGapGrid.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridMediaGapGrid.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle25.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        DataGridViewCellStyle25.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle25.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle25.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle25.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle25.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle25.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridMediaGapGrid.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle25
        Me.GridMediaGapGrid.ColumnHeadersHeight = 48
        Me.GridMediaGapGrid.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridMediaGapGrid.EnableHeadersVisualStyles = False
        Me.GridMediaGapGrid.GridColor = System.Drawing.Color.LightGray
        Me.GridMediaGapGrid.Location = New System.Drawing.Point(2, 22)
        Me.GridMediaGapGrid.Name = "GridMediaGapGrid"
        Me.GridMediaGapGrid.ReadOnly = True
        Me.GridMediaGapGrid.RowHeadersVisible = False
        DataGridViewCellStyle26.ForeColor = System.Drawing.Color.DimGray
        Me.GridMediaGapGrid.RowsDefaultCellStyle = DataGridViewCellStyle26
        Me.GridMediaGapGrid.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridMediaGapGrid.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridMediaGapGrid.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridMediaGapGrid.RowTemplate.DefaultCellStyle.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridMediaGapGrid.RowTemplate.Height = 30
        Me.GridMediaGapGrid.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.CellSelect
        Me.GridMediaGapGrid.ShowCellToolTips = False
        Me.GridMediaGapGrid.Size = New System.Drawing.Size(620, 282)
        Me.GridMediaGapGrid.StandardTab = True
        Me.GridMediaGapGrid.TabIndex = 0
        '
        'GroupControlViewOptions
        '
        Me.GroupControlViewOptions.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlViewOptions.Appearance.Options.UseFont = True
        Me.GroupControlViewOptions.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlViewOptions.AppearanceCaption.Options.UseFont = True
        Me.GroupControlViewOptions.Controls.Add(Me.PanelViewOptions)
        Me.GroupControlViewOptions.Dock = System.Windows.Forms.DockStyle.Fill
        Me.GroupControlViewOptions.Location = New System.Drawing.Point(639, 0)
        Me.GroupControlViewOptions.LookAndFeel.SkinName = "Black"
        Me.GroupControlViewOptions.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlViewOptions.Margin = New System.Windows.Forms.Padding(0)
        Me.GroupControlViewOptions.Name = "GroupControlViewOptions"
        Me.TableLayoutPanelMediaGap.SetRowSpan(Me.GroupControlViewOptions, 3)
        Me.GroupControlViewOptions.Size = New System.Drawing.Size(160, 525)
        Me.GroupControlViewOptions.TabIndex = 3
        Me.GroupControlViewOptions.Text = "Options"
        '
        'PanelViewOptions
        '
        Me.PanelViewOptions.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelViewOptions.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelViewOptions.Controls.Add(Me.SimpleButton2)
        Me.PanelViewOptions.Controls.Add(Me.SimpleButton1)
        Me.PanelViewOptions.Controls.Add(Me.ButtonSelectAll)
        Me.PanelViewOptions.Controls.Add(Me.ButtonQuery)
        Me.PanelViewOptions.Controls.Add(Me.Panel3)
        Me.PanelViewOptions.Controls.Add(Me.Panel2)
        Me.PanelViewOptions.Controls.Add(Me.Panel1)
        Me.PanelViewOptions.Controls.Add(Me.HyperlinkBookingList)
        Me.PanelViewOptions.Controls.Add(Me.HyperlinkGridView)
        Me.PanelViewOptions.Controls.Add(Me.HyperlinkFromDate)
        Me.PanelViewOptions.Controls.Add(Me.HyperlinkCategories)
        Me.PanelViewOptions.Controls.Add(Me.HyperlinkMediaFamilies)
        Me.PanelViewOptions.Controls.Add(Me.HyperlinkChains)
        Me.PanelViewOptions.Controls.Add(Me.ButtonClear)
        Me.PanelViewOptions.Location = New System.Drawing.Point(2, 22)
        Me.PanelViewOptions.Margin = New System.Windows.Forms.Padding(0)
        Me.PanelViewOptions.Name = "PanelViewOptions"
        Me.PanelViewOptions.Padding = New System.Windows.Forms.Padding(3)
        Me.PanelViewOptions.Size = New System.Drawing.Size(156, 501)
        Me.PanelViewOptions.TabIndex = 0
        '
        'ButtonSelectAll
        '
        Me.ButtonSelectAll.AccessibleRole = System.Windows.Forms.AccessibleRole.None
        Me.ButtonSelectAll.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.ButtonSelectAll.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonSelectAll.Appearance.Options.UseFont = True
        Me.ButtonSelectAll.ImageList = Me.ImageList16x16
        Me.ButtonSelectAll.Location = New System.Drawing.Point(6, 261)
        Me.ButtonSelectAll.LookAndFeel.SkinName = "Black"
        Me.ButtonSelectAll.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonSelectAll.Name = "ButtonSelectAll"
        Me.ButtonSelectAll.Size = New System.Drawing.Size(144, 23)
        Me.ButtonSelectAll.TabIndex = 6
        Me.ButtonSelectAll.Text = "Select All"
        Me.ButtonSelectAll.Visible = False
        '
        'ButtonQuery
        '
        Me.ButtonQuery.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonQuery.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonQuery.Appearance.Options.UseFont = True
        Me.ButtonQuery.ImageIndex = 0
        Me.ButtonQuery.ImageList = Me.ImageList24x24
        Me.ButtonQuery.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonQuery.Location = New System.Drawing.Point(6, 76)
        Me.ButtonQuery.LookAndFeel.SkinName = "Black"
        Me.ButtonQuery.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonQuery.Margin = New System.Windows.Forms.Padding(3, 6, 3, 3)
        Me.ButtonQuery.Name = "ButtonQuery"
        Me.ButtonQuery.Size = New System.Drawing.Size(144, 32)
        Me.ButtonQuery.TabIndex = 4
        Me.ButtonQuery.Text = "Query"
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "refresh.png")
        '
        'Panel3
        '
        Me.Panel3.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel3.BackColor = System.Drawing.Color.Silver
        Me.Panel3.Location = New System.Drawing.Point(6, 66)
        Me.Panel3.Margin = New System.Windows.Forms.Padding(3, 6, 3, 3)
        Me.Panel3.Name = "Panel3"
        Me.Panel3.Size = New System.Drawing.Size(144, 1)
        Me.Panel3.TabIndex = 3
        '
        'Panel2
        '
        Me.Panel2.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel2.BackColor = System.Drawing.Color.Silver
        Me.Panel2.Location = New System.Drawing.Point(6, 149)
        Me.Panel2.Margin = New System.Windows.Forms.Padding(3, 3, 3, 6)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(144, 1)
        Me.Panel2.TabIndex = 7
        '
        'Panel1
        '
        Me.Panel1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel1.BackColor = System.Drawing.Color.Silver
        Me.Panel1.Location = New System.Drawing.Point(6, 178)
        Me.Panel1.Margin = New System.Windows.Forms.Padding(3, 3, 3, 6)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(144, 1)
        Me.Panel1.TabIndex = 9
        '
        'HyperlinkBookingList
        '
        Me.HyperlinkBookingList.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkBookingList.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkBookingList.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkBookingList.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkBookingList.Location = New System.Drawing.Point(6, 207)
        Me.HyperlinkBookingList.Name = "HyperlinkBookingList"
        Me.HyperlinkBookingList.Size = New System.Drawing.Size(135, 13)
        Me.HyperlinkBookingList.TabIndex = 11
        Me.HyperlinkBookingList.Text = "Provisional Booking List"
        '
        'HyperlinkGridView
        '
        Me.HyperlinkGridView.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkGridView.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkGridView.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.HyperlinkGridView.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkGridView.Enabled = False
        Me.HyperlinkGridView.Location = New System.Drawing.Point(6, 188)
        Me.HyperlinkGridView.Name = "HyperlinkGridView"
        Me.HyperlinkGridView.Size = New System.Drawing.Size(55, 13)
        Me.HyperlinkGridView.TabIndex = 10
        Me.HyperlinkGridView.Text = "Grid View"
        '
        'HyperlinkFromDate
        '
        Me.HyperlinkFromDate.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkFromDate.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkFromDate.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkFromDate.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkFromDate.Location = New System.Drawing.Point(6, 156)
        Me.HyperlinkFromDate.Margin = New System.Windows.Forms.Padding(3, 3, 3, 6)
        Me.HyperlinkFromDate.Name = "HyperlinkFromDate"
        Me.HyperlinkFromDate.Size = New System.Drawing.Size(99, 13)
        Me.HyperlinkFromDate.TabIndex = 8
        Me.HyperlinkFromDate.Text = "From 12-09-2020"
        '
        'HyperlinkCategories
        '
        Me.HyperlinkCategories.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkCategories.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkCategories.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkCategories.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkCategories.Location = New System.Drawing.Point(6, 44)
        Me.HyperlinkCategories.Name = "HyperlinkCategories"
        Me.HyperlinkCategories.Size = New System.Drawing.Size(83, 13)
        Me.HyperlinkCategories.TabIndex = 2
        Me.HyperlinkCategories.Text = "Categories (0)"
        '
        'HyperlinkMediaFamilies
        '
        Me.HyperlinkMediaFamilies.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkMediaFamilies.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkMediaFamilies.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkMediaFamilies.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkMediaFamilies.Location = New System.Drawing.Point(6, 25)
        Me.HyperlinkMediaFamilies.Name = "HyperlinkMediaFamilies"
        Me.HyperlinkMediaFamilies.Size = New System.Drawing.Size(104, 13)
        Me.HyperlinkMediaFamilies.TabIndex = 1
        Me.HyperlinkMediaFamilies.Text = "Media Families (0)"
        '
        'HyperlinkChains
        '
        Me.HyperlinkChains.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkChains.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkChains.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkChains.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkChains.Location = New System.Drawing.Point(6, 6)
        Me.HyperlinkChains.Name = "HyperlinkChains"
        Me.HyperlinkChains.Size = New System.Drawing.Size(60, 13)
        Me.HyperlinkChains.TabIndex = 0
        Me.HyperlinkChains.Text = "Chains (0)"
        '
        'ButtonClear
        '
        Me.ButtonClear.AccessibleRole = System.Windows.Forms.AccessibleRole.None
        Me.ButtonClear.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.ButtonClear.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonClear.Appearance.Options.UseFont = True
        Me.ButtonClear.ImageList = Me.ImageList16x16
        Me.ButtonClear.Location = New System.Drawing.Point(44, 117)
        Me.ButtonClear.LookAndFeel.SkinName = "Black"
        Me.ButtonClear.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonClear.Margin = New System.Windows.Forms.Padding(3, 6, 3, 6)
        Me.ButtonClear.Name = "ButtonClear"
        Me.ButtonClear.Size = New System.Drawing.Size(69, 23)
        Me.ButtonClear.TabIndex = 5
        Me.ButtonClear.Text = "Clear"
        '
        'TableLayoutPanelMediaGap
        '
        Me.TableLayoutPanelMediaGap.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanelMediaGap.BackColor = System.Drawing.Color.Transparent
        Me.TableLayoutPanelMediaGap.ColumnCount = 5
        Me.TableLayoutPanelMediaGap.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 120.0!))
        Me.TableLayoutPanelMediaGap.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 15.0!))
        Me.TableLayoutPanelMediaGap.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanelMediaGap.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 15.0!))
        Me.TableLayoutPanelMediaGap.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 160.0!))
        Me.TableLayoutPanelMediaGap.Controls.Add(Me.GroupControlProvisionalBookings, 2, 2)
        Me.TableLayoutPanelMediaGap.Controls.Add(Me.GroupControlViewOptions, 4, 0)
        Me.TableLayoutPanelMediaGap.Controls.Add(Me.GroupControlMediaGapGrid, 0, 0)
        Me.TableLayoutPanelMediaGap.Controls.Add(Me.GroupControlContracts, 0, 2)
        Me.TableLayoutPanelMediaGap.Location = New System.Drawing.Point(12, 12)
        Me.TableLayoutPanelMediaGap.Name = "TableLayoutPanelMediaGap"
        Me.TableLayoutPanelMediaGap.RowCount = 3
        Me.TableLayoutPanelMediaGap.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 60.0!))
        Me.TableLayoutPanelMediaGap.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 15.0!))
        Me.TableLayoutPanelMediaGap.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 40.0!))
        Me.TableLayoutPanelMediaGap.Size = New System.Drawing.Size(799, 525)
        Me.TableLayoutPanelMediaGap.TabIndex = 0
        '
        'SimpleButton1
        '
        Me.SimpleButton1.AccessibleRole = System.Windows.Forms.AccessibleRole.None
        Me.SimpleButton1.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.SimpleButton1.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton1.Appearance.Options.UseFont = True
        Me.SimpleButton1.ImageList = Me.ImageList16x16
        Me.SimpleButton1.Location = New System.Drawing.Point(6, 290)
        Me.SimpleButton1.LookAndFeel.SkinName = "Black"
        Me.SimpleButton1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton1.Name = "SimpleButton1"
        Me.SimpleButton1.Size = New System.Drawing.Size(144, 23)
        Me.SimpleButton1.TabIndex = 12
        Me.SimpleButton1.Text = "Only my PBs"
        Me.SimpleButton1.Visible = False
        '
        'SimpleButton2
        '
        Me.SimpleButton2.AccessibleRole = System.Windows.Forms.AccessibleRole.None
        Me.SimpleButton2.Anchor = System.Windows.Forms.AnchorStyles.Top
        Me.SimpleButton2.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.SimpleButton2.Appearance.Options.UseFont = True
        Me.SimpleButton2.Location = New System.Drawing.Point(6, 319)
        Me.SimpleButton2.LookAndFeel.SkinName = "Black"
        Me.SimpleButton2.LookAndFeel.UseDefaultLookAndFeel = False
        Me.SimpleButton2.Name = "SimpleButton2"
        Me.SimpleButton2.Size = New System.Drawing.Size(144, 23)
        Me.SimpleButton2.TabIndex = 12
        Me.SimpleButton2.Text = "Space by contract..."
        Me.SimpleButton2.Visible = False
        '
        'SubformMediaGap
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.Controls.Add(Me.TableLayoutPanelMediaGap)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Name = "SubformMediaGap"
        Me.Controls.SetChildIndex(Me.TableLayoutPanelMediaGap, 0)
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        CType(Me.GroupControlProvisionalBookings, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlProvisionalBookings.ResumeLayout(False)
        CType(Me.TextEditSearchProvisionalBooking.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearchProvisionalBooking.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearchProvisionalBooking.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridProvisionalBookings, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControlContracts, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlContracts.ResumeLayout(False)
        CType(Me.GridContracts, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControlMediaGapGrid, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlMediaGapGrid.ResumeLayout(False)
        CType(Me.GridMediaGapGrid, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControlViewOptions, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlViewOptions.ResumeLayout(False)
        Me.PanelViewOptions.ResumeLayout(False)
        Me.PanelViewOptions.PerformLayout()
        Me.TableLayoutPanelMediaGap.ResumeLayout(False)
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents ImageList16x16 As System.Windows.Forms.ImageList
    Friend WithEvents GroupControlProvisionalBookings As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelControl7 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditSearchProvisionalBooking As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearchProvisionalBooking As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearchProvisionalBooking As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents ButtonDelete As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonAdd As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonEdit As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GridProvisionalBookings As System.Windows.Forms.DataGridView
    Friend WithEvents GroupControlContracts As DevExpress.XtraEditors.GroupControl
    Friend WithEvents GridContracts As System.Windows.Forms.DataGridView
    Friend WithEvents GroupControlMediaGapGrid As DevExpress.XtraEditors.GroupControl
    Friend WithEvents GridMediaGapGrid As System.Windows.Forms.DataGridView
    Friend WithEvents GroupControlViewOptions As DevExpress.XtraEditors.GroupControl
    Friend WithEvents PanelViewOptions As System.Windows.Forms.Panel
    Friend WithEvents HyperlinkChains As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkCategories As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkMediaFamilies As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkFromDate As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ButtonClear As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents HyperlinkGridView As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ContractNumberColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents ButtonQuery As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonInfo As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Panel3 As System.Windows.Forms.Panel
    Friend WithEvents HyperlinkBookingList As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TableLayoutPanelMediaGap As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents Panel2 As System.Windows.Forms.Panel
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents ButtonSelectAll As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents DescriptionColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents MediaFamilyNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents CategoryColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents ChainColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents BrandColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents FirstWeekColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents LastWeekColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DurationColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents BookedByColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents BookTimeColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents ExpiryTimeColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents SimpleButton1 As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents SimpleButton2 As DevExpress.XtraEditors.SimpleButton

End Class
