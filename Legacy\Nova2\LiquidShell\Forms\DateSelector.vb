Public Class DateSelector

    Private ForceMondaySelect As Boolean

    Public Sub New(ByVal ForceMondaySelection As Boolean)

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.

        ' Set the calendar value to the next Monday.
        ForceMondaySelect = ForceMondaySelection
        Calendar.DateTime = Today.Date
        While Not Calendar.DateTime.DayOfWeek = DayOfWeek.Monday
            Calendar.DateTime = Calendar.DateTime.AddDays(1)
        End While

    End Sub

    Private Sub ButtonCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonCancel.Click
        Close()
    End Sub

    Private Sub ButtonSelect_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonSelect.Click

        ' Validate selection.
        If ForceMondaySelect Then
            If Not Calendar.DateTime.DayOfWeek = DayOfWeek.Monday Then
                ShowMessage("The selected date must be a Monday.", "Validation Failed", System.Windows.Forms.MessageBoxIcon.Error)
                Exit Sub
            End If
        End If

        ' Set dialog result and close.
        DialogResult = Windows.Forms.DialogResult.OK
        Close()

    End Sub

    Public Shared Function GetDate(ByVal ForceMondaySelection As Boolean) As Nullable(Of Date)
        Return UserSelectedDate(ForceMondaySelection, Nothing)
    End Function

    Public Shared Function GetDate(ByVal ForceMondaySelection As Boolean, ByVal CurrentDate As Date) As Nullable(Of Date)
        Return UserSelectedDate(ForceMondaySelection, CurrentDate)
    End Function

    Private Shared Function UserSelectedDate(ByVal ForceMondaySelection As Boolean, ByVal CurrentDate As Nullable(Of Date)) As Nullable(Of Date)

        ' Create an instance of the form.
        Dim Selector As New DateSelector(ForceMondaySelection)

        ' Preselect the current date.
        If CurrentDate.HasValue Then
            Selector.Calendar.DateTime = CurrentDate.Value
        End If

        ' Display the form.
        Selector.ShowDialog()

        If Selector.DialogResult = Windows.Forms.DialogResult.OK Then
            ' A date was selected. Return the selected date.
            Return Selector.Calendar.DateTime
        Else
            ' A date wasn't selected. Return nothing.
            Return Nothing
        End If

    End Function

End Class
