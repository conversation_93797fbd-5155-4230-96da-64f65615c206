﻿using DataAccess;
using System;

namespace DataService.Security
{
    class UpdateUserCommand : Command
    {
        public Guid SessionId;
        public Guid UserId;
        public string FirstName = string.Empty;
        public string LastName = string.Empty;
        public string EmailAddress = string.Empty;
        public string MobilePhone = string.Empty;
        public bool Gender;
        public string Notes = string.Empty;
        public bool Deleted;
        public string Username = string.Empty;
        public bool Enabled;

        public UpdateUserCommand
            (
            Guid sessionid,
            Guid userid,
            string firstname,
            string lastname,
            string emailaddress,
            string mobilephone,
            bool gender,
            string notes,
            bool deleted,
            string username,
            bool enabled
            )
        {
            SessionId = sessionid;
            UserId = userid;
            FirstName = firstname;
            LastName = lastname;
            EmailAddress = emailaddress;
            MobilePhone = mobilephone;
            Gender = gender;
            Notes = notes;
            Deleted = deleted;
            Username = username;
            Enabled = enabled;
        }
    }
}
