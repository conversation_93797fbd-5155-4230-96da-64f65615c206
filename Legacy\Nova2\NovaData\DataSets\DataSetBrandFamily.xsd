<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DataSetBrandFamily" targetNamespace="http://tempuri.org/DataSetBrandFamily.xsd" xmlns:mstns="http://tempuri.org/DataSetBrandFamily.xsd" xmlns="http://tempuri.org/DataSetBrandFamily.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="DBConnection" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Public" Name="DBConnection (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.NovaData.My.MySettings.GlobalReference.Default.DBConnection" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="BrandFamilyTableAdapter" GeneratorDataComponentClassName="BrandFamilyTableAdapter" Name="BrandFamily" UserDataComponentName="BrandFamilyTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Client.BrandFamily" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Client].[BrandFamily] WHERE (([BrandFamilyID] = @Original_BrandFamilyID) AND ([BrandFamilyName] = @Original_BrandFamilyName))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_BrandFamilyID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="BrandFamilyID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_BrandFamilyName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="BrandFamilyName" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Client].[BrandFamily] ([BrandFamilyName]) VALUES (@BrandFamilyName);
SELECT BrandFamilyID, BrandFamilyName FROM Client.BrandFamily WHERE (BrandFamilyID = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@BrandFamilyName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="BrandFamilyName" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT        Client.BrandFamily.BrandFamilyID, Client.BrandFamily.BrandFamilyName, dbo.udfBrandListByBrandFamily(Client.BrandFamily.BrandFamilyID) AS Brands
FROM            Client.BrandFamily INNER JOIN
                         Client.vBrandFamiliesByPermission_EditMyClients ON Client.BrandFamily.BrandFamilyID = Client.vBrandFamiliesByPermission_EditMyClients.BrandFamilyID</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Client].[BrandFamily] SET [BrandFamilyName] = @BrandFamilyName WHERE (([BrandFamilyID] = @Original_BrandFamilyID) AND ([BrandFamilyName] = @Original_BrandFamilyName));
SELECT BrandFamilyID, BrandFamilyName FROM Client.BrandFamily WHERE (BrandFamilyID = @BrandFamilyID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@BrandFamilyName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="BrandFamilyName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_BrandFamilyID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="BrandFamilyID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_BrandFamilyName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="BrandFamilyName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="BrandFamilyID" ColumnName="BrandFamilyID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@BrandFamilyID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="BrandFamilyID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="BrandFamilyID" DataSetColumn="BrandFamilyID" />
              <Mapping SourceColumn="BrandFamilyName" DataSetColumn="BrandFamilyName" />
              <Mapping SourceColumn="Brands" DataSetColumn="Brands" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="BrandFamilyMemberTableAdapter" GeneratorDataComponentClassName="BrandFamilyMemberTableAdapter" Name="BrandFamilyMember" UserDataComponentName="BrandFamilyMemberTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Client.BrandFamilyMember" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Client].[BrandFamilyMember] WHERE (([BrandID] = @Original_BrandID) AND ([BrandFamilyID] = @Original_BrandFamilyID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BrandID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_BrandFamilyID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="BrandFamilyID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Client].[BrandFamilyMember] ([BrandID], [BrandFamilyID]) VALUES (@BrandID, @BrandFamilyID);
SELECT BrandID, BrandFamilyID FROM Client.BrandFamilyMember WHERE (BrandFamilyID = @BrandFamilyID) AND (BrandID = @BrandID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BrandID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@BrandFamilyID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="BrandFamilyID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT        BrandID, BrandFamilyID
FROM            Client.BrandFamilyMember
WHERE        (BrandFamilyID = @BrandFamilyID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="BrandFamilyID" ColumnName="BrandFamilyID" DataSourceName="NovaDB.Client.BrandFamilyMember" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@BrandFamilyID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="BrandFamilyID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Client].[BrandFamilyMember] SET [BrandID] = @BrandID, [BrandFamilyID] = @BrandFamilyID WHERE (([BrandID] = @Original_BrandID) AND ([BrandFamilyID] = @Original_BrandFamilyID));
SELECT BrandID, BrandFamilyID FROM Client.BrandFamilyMember WHERE (BrandFamilyID = @BrandFamilyID) AND (BrandID = @BrandID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BrandID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@BrandFamilyID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="BrandFamilyID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BrandID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_BrandFamilyID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="BrandFamilyID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="BrandID" DataSetColumn="BrandID" />
              <Mapping SourceColumn="BrandFamilyID" DataSetColumn="BrandFamilyID" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="BrandTableAdapter" GeneratorDataComponentClassName="BrandTableAdapter" Name="Brand" UserDataComponentName="BrandTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Client.Brand" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT        BrandID, BrandName
FROM            Client.Brand</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="BrandID" DataSetColumn="BrandID" />
              <Mapping SourceColumn="BrandName" DataSetColumn="BrandName" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DataSetBrandFamily" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_DataSetName="DataSetBrandFamily" msprop:Generator_UserDSName="DataSetBrandFamily">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="BrandFamily" msprop:Generator_UserTableName="BrandFamily" msprop:Generator_RowEvArgName="BrandFamilyRowChangeEvent" msprop:Generator_TableVarName="tableBrandFamily" msprop:Generator_TablePropName="BrandFamily" msprop:Generator_RowDeletingName="BrandFamilyRowDeleting" msprop:Generator_RowChangingName="BrandFamilyRowChanging" msprop:Generator_RowDeletedName="BrandFamilyRowDeleted" msprop:Generator_RowEvHandlerName="BrandFamilyRowChangeEventHandler" msprop:Generator_TableClassName="BrandFamilyDataTable" msprop:Generator_RowChangedName="BrandFamilyRowChanged" msprop:Generator_RowClassName="BrandFamilyRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="BrandFamilyID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msprop:Generator_ColumnVarNameInTable="columnBrandFamilyID" msprop:Generator_ColumnPropNameInRow="BrandFamilyID" msprop:Generator_ColumnPropNameInTable="BrandFamilyIDColumn" msprop:Generator_UserColumnName="BrandFamilyID" type="xs:int" />
              <xs:element name="BrandFamilyName" msprop:Generator_ColumnVarNameInTable="columnBrandFamilyName" msprop:Generator_ColumnPropNameInRow="BrandFamilyName" msprop:Generator_ColumnPropNameInTable="BrandFamilyNameColumn" msprop:Generator_UserColumnName="BrandFamilyName" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Brands" msprop:nullValue="_throw" msprop:Generator_ColumnPropNameInRow="Brands" msprop:Generator_ColumnVarNameInTable="columnBrands" msprop:Generator_ColumnPropNameInTable="BrandsColumn" msprop:Generator_UserColumnName="Brands" default="" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2147483647" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="BrandFamilyMember" msprop:Generator_UserTableName="BrandFamilyMember" msprop:Generator_RowEvArgName="BrandFamilyMemberRowChangeEvent" msprop:Generator_TableVarName="tableBrandFamilyMember" msprop:Generator_TablePropName="BrandFamilyMember" msprop:Generator_RowDeletingName="BrandFamilyMemberRowDeleting" msprop:Generator_RowChangingName="BrandFamilyMemberRowChanging" msprop:Generator_RowDeletedName="BrandFamilyMemberRowDeleted" msprop:Generator_RowEvHandlerName="BrandFamilyMemberRowChangeEventHandler" msprop:Generator_TableClassName="BrandFamilyMemberDataTable" msprop:Generator_RowChangedName="BrandFamilyMemberRowChanged" msprop:Generator_RowClassName="BrandFamilyMemberRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="BrandID" msdata:DataType="System.Guid, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnBrandID" msprop:Generator_ColumnPropNameInRow="BrandID" msprop:Generator_ColumnPropNameInTable="BrandIDColumn" msprop:Generator_UserColumnName="BrandID" type="xs:string" />
              <xs:element name="BrandFamilyID" msprop:Generator_ColumnVarNameInTable="columnBrandFamilyID" msprop:Generator_ColumnPropNameInRow="BrandFamilyID" msprop:Generator_ColumnPropNameInTable="BrandFamilyIDColumn" msprop:Generator_UserColumnName="BrandFamilyID" type="xs:int" />
              <xs:element name="BrandName" msdata:ReadOnly="true" msdata:Expression="Parent(FK_BrandFamilyMember_Brand).BrandName" msprop:Generator_ColumnVarNameInTable="columnBrandName" msprop:Generator_ColumnPropNameInRow="BrandName" msprop:Generator_ColumnPropNameInTable="BrandNameColumn" msprop:Generator_UserColumnName="BrandName" type="xs:string" default="" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Brand" msprop:Generator_UserTableName="Brand" msprop:Generator_RowEvArgName="BrandRowChangeEvent" msprop:Generator_TableVarName="tableBrand" msprop:Generator_TablePropName="Brand" msprop:Generator_RowDeletingName="BrandRowDeleting" msprop:Generator_RowChangingName="BrandRowChanging" msprop:Generator_RowDeletedName="BrandRowDeleted" msprop:Generator_RowEvHandlerName="BrandRowChangeEventHandler" msprop:Generator_TableClassName="BrandDataTable" msprop:Generator_RowChangedName="BrandRowChanged" msprop:Generator_RowClassName="BrandRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="BrandID" msdata:DataType="System.Guid, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnBrandID" msprop:Generator_ColumnPropNameInRow="BrandID" msprop:Generator_ColumnPropNameInTable="BrandIDColumn" msprop:Generator_UserColumnName="BrandID" type="xs:string" />
              <xs:element name="BrandName" msprop:Generator_ColumnVarNameInTable="columnBrandName" msprop:Generator_ColumnPropNameInRow="BrandName" msprop:Generator_ColumnPropNameInTable="BrandNameColumn" msprop:Generator_UserColumnName="BrandName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:BrandFamily" />
      <xs:field xpath="mstns:BrandFamilyID" />
    </xs:unique>
    <xs:unique name="BrandFamilyMember_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:BrandFamilyMember" />
      <xs:field xpath="mstns:BrandID" />
      <xs:field xpath="mstns:BrandFamilyID" />
    </xs:unique>
    <xs:unique name="Brand_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Brand" />
      <xs:field xpath="mstns:BrandID" />
    </xs:unique>
  </xs:element>
  <xs:annotation>
    <xs:appinfo>
      <msdata:Relationship name="FK_BrandFamilyMember_BrandFamily" msdata:parent="BrandFamily" msdata:child="BrandFamilyMember" msdata:parentkey="BrandFamilyID" msdata:childkey="BrandFamilyID" msprop:Generator_UserChildTable="BrandFamilyMember" msprop:Generator_ChildPropName="GetBrandFamilyMemberRows" msprop:Generator_UserRelationName="FK_BrandFamilyMember_BrandFamily" msprop:Generator_RelationVarName="relationFK_BrandFamilyMember_BrandFamily" msprop:Generator_UserParentTable="BrandFamily" msprop:Generator_ParentPropName="BrandFamilyRow" />
      <msdata:Relationship name="FK_BrandFamilyMember_Brand" msdata:parent="Brand" msdata:child="BrandFamilyMember" msdata:parentkey="BrandID" msdata:childkey="BrandID" msprop:Generator_UserChildTable="BrandFamilyMember" msprop:Generator_ChildPropName="GetBrandFamilyMemberRows" msprop:Generator_UserRelationName="FK_BrandFamilyMember_Brand" msprop:Generator_RelationVarName="relationFK_BrandFamilyMember_Brand" msprop:Generator_UserParentTable="Brand" msprop:Generator_ParentPropName="BrandRow" />
    </xs:appinfo>
  </xs:annotation>
</xs:schema>