<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <Body>
    <ReportItems>
      <Tablix Name="table1">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>6.60001cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.7cm</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.3cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="ItemName">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>= Trim(Fields!ItemName.Value) &amp; "; Qty: " &amp; Fields!ItemQty.Value</Value>
                              <Style>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>6pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>ItemName</rd:DefaultName>
                      <ZIndex>1</ZIndex>
                      <Style>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>1pt</PaddingTop>
                        <PaddingBottom>1pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="SellPrice">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Left
(
FormatCurrency(Fields!SellPrice.Value,2).Replace("R","R ").Replace("R  ","R "),Len(FormatCurrency(Fields!SellPrice.Value,2).Replace("R","R ").Replace("R  ","R "))-3
)
+
Right
(
FormatCurrency(Fields!SellPrice.Value,2).Replace("R","R ").Replace("R  ","R "),3
)
.Replace(",",".")</Value>
                              <Style>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>6pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>SellPrice</rd:DefaultName>
                      <Style>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingTop>1pt</PaddingTop>
                        <PaddingBottom>1pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <rd:Selected>true</rd:Selected>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <Group Name="table1_Details_Group">
                <DataElementName>Detail</DataElementName>
              </Group>
              <TablixMembers>
                <TablixMember />
              </TablixMembers>
              <DataElementName>Detail_Collection</DataElementName>
              <DataElementOutput>Output</DataElementOutput>
              <KeepTogether>true</KeepTogether>
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <DataSetName>DataSetContractReport_ContractInventory</DataSetName>
        <Height>0.3cm</Height>
        <Width>8.30001cm</Width>
        <Style>
          <FontFamily>Verdana</FontFamily>
          <FontSize>6pt</FontSize>
        </Style>
      </Tablix>
    </ReportItems>
    <Height>0.3cm</Height>
    <Style />
  </Body>
  <Width>8.30001cm</Width>
  <Page>
    <PageHeight>29.7cm</PageHeight>
    <PageWidth>21cm</PageWidth>
    <LeftMargin>2.5cm</LeftMargin>
    <RightMargin>2.5cm</RightMargin>
    <TopMargin>2.5cm</TopMargin>
    <BottomMargin>2.5cm</BottomMargin>
    <ColumnSpacing>1cm</ColumnSpacing>
    <Style />
  </Page>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DBConnection">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString>Data Source=sqlserv_3;Initial Catalog=NovaDB;User ID=alan</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>91f97653-22be-4432-9751-0e3e558e98cf</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="DataSetContractReport_ContractInventory">
      <Query>
        <DataSourceName>DBConnection</DataSourceName>
        <CommandText>SELECT        Sales.ContractInventoryQty.ContractID, Ops.Inventory.ItemName, Ops.InventoryQty.ItemQty, Sales.ContractInventoryQty.SellPrice
FROM            Sales.ContractInventoryQty INNER JOIN
                         Ops.InventoryQty ON Sales.ContractInventoryQty.ItemQtyID = Ops.InventoryQty.ItemQtyID INNER JOIN
                         Ops.Inventory ON Ops.InventoryQty.ItemID = Ops.Inventory.ItemID
WHERE        (Sales.ContractInventoryQty.ContractID = @ContractID)</CommandText>
        <rd:UseGenericDesigner>true</rd:UseGenericDesigner>
      </Query>
      <Fields>
        <Field Name="ContractID">
          <DataField>ContractID</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ItemName">
          <DataField>ItemName</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ItemQty">
          <DataField>ItemQty</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="SellPrice">
          <DataField>SellPrice</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>DataSetContractReport</rd:DataSetName>
        <rd:TableName>ContractInventory</rd:TableName>
        <rd:TableAdapterFillMethod>Fill</rd:TableAdapterFillMethod>
        <rd:TableAdapterGetDataMethod>GetData</rd:TableAdapterGetDataMethod>
        <rd:TableAdapterName>ContractInventoryTableAdapter</rd:TableAdapterName>
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <ReportParameters>
    <ReportParameter Name="ContractID">
      <DataType>String</DataType>
      <Prompt>ContractID</Prompt>
    </ReportParameter>
  </ReportParameters>
  <Code>    Public Function DisplayAsCurrency(Number As Object) As String
        ' This function displays a numeric value as currency
        ' in this format: R 1 000 000.00

        ' First convert the passed object into a decimal.
        Dim NumberValue As Decimal = 0
        If Decimal.TryParse(Number.ToString, NumberValue) = False Then
            Return "--error--"
        End If

        ' Format the decimal the way we want.
        NumberValue = Math.Round(NumberValue, 2)
        Dim NumberString As String = NumberValue.ToString
        Dim PointPosition As Integer = NumberString.IndexOf(".")
        Dim DecimalPart As String = NumberString.Substring(PointPosition + 1, 2)

        ' Drop the fractions from NumberString.
        NumberString = NumberString.Substring(0, PointPosition)

        ' Loop through the number string's characters inserting a comma at every third position.
        Dim InsertPosition As Integer = PointPosition - 3
        While InsertPosition &gt; 0
            NumberString = NumberString.Insert(InsertPosition, " ")
            InsertPosition -= 3
        End While

        Return "R " &amp; NumberString &amp; "." &amp; DecimalPart

    End Function</Code>
  <Language>en-ZA</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>a56a1d51-bbc6-4a72-bd26-18405cf35e36</rd:ReportID>
</Report>