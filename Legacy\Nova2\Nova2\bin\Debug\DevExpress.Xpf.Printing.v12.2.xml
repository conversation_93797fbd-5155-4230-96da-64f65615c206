<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DevExpress.Xpf.Printing.v12.2</name>
    </assembly>
    <members>
        <member name="T:DevExpress.Xpf.Printing.LinkPreviewModel">

            <summary>
                <para>Provides the Preview Model functionality for printing links.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkPreviewModel.#ctor">
            <summary>
                <para>Initializes a new instance of the LinkPreviewModel class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkPreviewModel.#ctor(DevExpress.Xpf.Printing.LinkBase)">
            <summary>
                <para>Initializes a new instance of the LinkPreviewModel class with the specified link.
</para>
            </summary>
            <param name="link">
		A <see cref="T:DevExpress.Xpf.Printing.LinkBase"/> descendant.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Printing.LinkPreviewModel.IsEmptyDocument">
            <summary>
                <para>Gets the value specifying whether or not the document has any pages.
</para>
            </summary>
            <value><b>true</b> if the document is empty; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.LinkPreviewModel.Link">
            <summary>
                <para>Specifies the link that is associated with this LinkPreviewModel.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Printing.LinkBase"/> descendant.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.LinkPreviewModel.ParametersPanelContent">
            <summary>
                <para>Provides access to the elements of the <b>Parameters</b> panel.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.FrameworkElement"/> object that stores parameter editors available in the panel.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Printing.IDialogService">

            <summary>
                <para>Implements a service that is used to show dialogs on the client.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.IDialogService.GetParentWindow">
            <summary>
                <para>Gets the parent window of the dialog box.
</para>
            </summary>
            <returns>A <see cref="T:System.Windows.Window"/> object.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.IDialogService.ShowError(System.String,System.String)">
            <summary>
                <para>Shows the error dialog with the specified text and caption.
</para>
            </summary>
            <param name="text">
		A <see cref="T:System.String"/> value specifying the dialog's text.

            </param>
            <param name="caption">
		A <see cref="T:System.String"/> value specifying the dialog's caption.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.IDialogService.ShowInformation(System.String,System.String)">
            <summary>
                <para>Shows a dialog with the specified text and caption. 
</para>
            </summary>
            <param name="text">
		A <see cref="T:System.String"/> value specifying the dialog's text.

            </param>
            <param name="caption">
		A <see cref="T:System.String"/> value specifying the dialog's caption.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.IDialogService.ShowOpenFileDialog(System.String,System.String)">
            <summary>
                <para>Shows the <b>Open</b> dialog with the specified settings.
</para>
            </summary>
            <param name="caption">
		A <see cref="T:System.String"/> value specifying the dialog's caption.

            </param>
            <param name="filter">
		A <see cref="T:System.String"/> value specifying the file types to display.

            </param>
            <returns>A <see cref="T:System.IO.Stream"/> object.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.IDialogService.ShowOpenFileDialog(System.String,System.String,System.String@)">
            <summary>
                <para>Shows the <b>Open</b> dialog with the specified settings.
</para>
            </summary>
            <param name="caption">
		A <see cref="T:System.String"/> value specifying the dialog's caption.

            </param>
            <param name="filter">
		A <see cref="T:System.String"/> value, specifying the available file type extensions.

            </param>
            <param name="fileName">
		A <see cref="T:System.String"/> value, specifying the file name.

            </param>
            <returns>A <see cref="T:System.IO.Stream"/> object specifying where the dialog is transmitted.

</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.IDialogService.ShowSaveFileDialog(System.String,System.String,System.Int32,System.String,System.String)">
            <summary>
                <para>Shows the <b>Save File</b> dialog with the specified parameters.
</para>
            </summary>
            <param name="caption">
		A <see cref="T:System.String"/> value, specifying the dialog's caption.

            </param>
            <param name="filter">
		A <see cref="T:System.String"/> value, specifying the available file type extensions.

            </param>
            <param name="filterIndex">
		An integer value, specifying the file type extension that is selected by default.

            </param>
            <param name="initialDirectory">
		A <see cref="T:System.String"/> value, specifying the path to the folder opened by default.

            </param>
            <param name="fileName">
		A <see cref="T:System.String"/> value, specifying the file name.

            </param>
            <returns>A <see cref="T:System.IO.Stream"/> object specifying where the dialog is transmitted.

</returns>


        </member>
        <member name="T:DevExpress.Xpf.Printing.ZoomValueItem">

            <summary>
                <para>An individual document zoom factor value available in Print Preview.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.ZoomValueItem.#ctor(System.Double)">
            <summary>
                <para>Initializes a new instance of the ZoomValueItem class with the specified zoom value.
</para>
            </summary>
            <param name="zoomValue">
		A <see cref="T:System.Double"/> value specifying the zoom value.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Printing.ZoomValueItem.DisplayedText">
            <summary>
                <para>Specifies the caption for the zoom value item as it appears in Print Preview.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value specifying the caption for the zoom value item.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Printing.ZoomValueItem.Equals(System.Object)">
            <summary>
                <para>Determines whether or not the specified object is equal to the current ZoomValueItem instance.
</para>
            </summary>
            <param name="obj">
		The object to compare with the current object.

            </param>
            <returns><b>true</b> if the specified object is equal to the current ZoomValueItem instance; otherwise <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.ZoomValueItem.GetHashCode">
            <summary>
                <para>Gets the hash code (a number) that corresponds to the value of the current ZoomValueItem object.
</para>
            </summary>
            <returns>An integer value representing the hash code for the current object.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Printing.ZoomValueItem.ZoomValue">
            <summary>
                <para>Specifies the zoom factor value to which the ZoomValueItem corresponds.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value specifying the zoom factor value.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Printing.IPageSettingsConfiguratorService">

            <summary>
                <para>Provides a method for adjusting the page settings.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.IPageSettingsConfiguratorService.Configure(DevExpress.XtraPrinting.XtraPageSettingsBase,System.Windows.Window)">
            <summary>
                <para>Configures the specified <see cref="T:DevExpress.XtraPrinting.XtraPageSettingsBase"/> object.
</para>
            </summary>
            <param name="pageSettings">
		An <see cref="T:DevExpress.XtraPrinting.XtraPageSettingsBase"/> object that contains print settings.


            </param>
            <param name="ownerWindow">
		A <see cref="T:System.Windows.Window"/> object specifying the dialog's owner.

            </param>
            <returns><b>true</b> if page settings were configured; otherwise, <b>false</b>.

</returns>


        </member>
        <member name="T:DevExpress.Xpf.Printing.IExportSendService">

            <summary>
                <para>Provides methods to export and send a document via e-mail, in accordance with the <see cref="T:DevExpress.Xpf.Printing.PrintingSystem"/>'s export options settings.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.IExportSendService.Export(DevExpress.XtraPrinting.PrintingSystemBase,System.Windows.Window,DevExpress.XtraPrinting.ExportOptionsBase,DevExpress.Xpf.Printing.IDialogService)">
            <summary>
                <para>Exports the document in the specified format.

</para>
            </summary>
            <param name="ps">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystemBase"/> descendant.

            </param>
            <param name="ownerWindow">
		A <see cref="T:System.Windows.Window"/> object.

            </param>
            <param name="options">
		An <see cref="T:DevExpress.XtraPrinting.ExportOptionsBase"/> descendant.

            </param>
            <param name="dialogService">
		An object implementing the <see cref="T:DevExpress.Xpf.Printing.IDialogService"/> interface.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.IExportSendService.SendFileByEmail(DevExpress.XtraPrinting.PrintingSystemBase,DevExpress.XtraPrinting.Export.EmailSenderBase,System.Windows.Window,DevExpress.XtraPrinting.ExportOptionsBase,DevExpress.Xpf.Printing.IDialogService)">
            <summary>
                <para>Exports the document in the specified format and attaches it to an e-mail.

</para>
            </summary>
            <param name="ps">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystemBase"/> descendant.

            </param>
            <param name="emailSender">
		A <see cref="T:DevExpress.XtraPrinting.Export.EmailSenderBase"/> descendant.

            </param>
            <param name="ownerWindow">
		A <see cref="T:System.Windows.Window"/> object.

            </param>
            <param name="options">
		An <see cref="T:DevExpress.XtraPrinting.ExportOptionsBase"/> descendant.

            </param>
            <param name="dialogService">
		An object implementing the <see cref="T:DevExpress.Xpf.Printing.IDialogService"/> interface.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Printing.IDocumentPreviewModel">

            <summary>
                <para>Provides the basic functionality for displaying document models in <see cref="T:DevExpress.Xpf.Printing.DocumentPreview"/>.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.CurrentPageIndex">
            <summary>
                <para>Specifies the zero-based current page index.
</para>
            </summary>
            <value>A zero-based integer value specifying the current page index.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.CurrentPageNumber">
            <summary>
                <para>Specifies the current page number.
</para>
            </summary>
            <value>An integer value specifying the current page number.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.DocumentMapRootNode">
            <summary>
                <para>For internal use. Gets the root node of the Document Map.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.DocumentMapSelectedNode">
            <summary>
                <para>For internal use. Gets the currently selected node of the Document Map.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.ExportCommand">
            <summary>
                <para>Exports the current document.
</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.FirstPageCommand">
            <summary>
                <para>Navigates to the first page of the current document.
</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.FoundBrickInfo">
            <summary>
                <para>For internal use. Required to highlight an element found using the Search option.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.IsDocumentMapVisible">
            <summary>
                <para>Specifies whether or not the <b>Document Map</b> panel is visible.
</para>
            </summary>
            <value><b>true</b> to show the Document Map panel; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.IsEmptyDocument">
            <summary>
                <para>Gets the value specifying whether or not the document has any pages.
</para>
            </summary>
            <value><b>true</b> if the document is empty; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.IsParametersPanelVisible">
            <summary>
                <para>Specifies whether or not the <b>Parameters</b> panel is visible.

</para>
            </summary>
            <value><b>true</b> to show the Parameters panel; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.IsScaleVisible">
            <summary>
                <para>Gets the value indicating whether or not the <b>Scale</b> button is available in the Bar Manager of the Document Preview.
</para>
            </summary>
            <value><b>true</b> if scaling is available for the report; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.IsSearchVisible">
            <summary>
                <para>Gets the value indicating whether or not the <b>Search</b> command is accessible in a Document Preview.
</para>
            </summary>
            <value><b>true</b> if searching is available for the report; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.LastPageCommand">
            <summary>
                <para>Navigates to the last page of the current document.
</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.NextPageCommand">
            <summary>
                <para>Navigates to the next page of the current document.
</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.OpenCommand">
            <summary>
                <para>Opens a document.
</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.PageSetupCommand">
            <summary>
                <para>Runs the <b>Page Setup</b> dialog for the current document.
</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.ParametersPanelContent">
            <summary>
                <para>Provides access to the elements of the <b>Parameters</b> panel.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.FrameworkElement"/> object that stores parameter editors available in the panel.
</value>


        </member>
        <member name="E:DevExpress.Xpf.Printing.IDocumentPreviewModel.PreviewClick">
            <summary>
                <para>Occurs when the left mouse button is clicked in the document area in a Document Preview.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Printing.IDocumentPreviewModel.PreviewDoubleClick">
            <summary>
                <para>Occurs when the left mouse button is double-clicked in the document area in a Document Preview.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Printing.IDocumentPreviewModel.PreviewMouseMove">
            <summary>
                <para>Occurs when the mouse cursor moves over the document area in a Document Preview.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.PreviousPageCommand">
            <summary>
                <para>Navigates to the previous page of the current document.
</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.PrintCommand">
            <summary>
                <para>Prints the current document using the specified printer.
</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.PrintDirectCommand">
            <summary>
                <para>Prints the current document using the default printer.
</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.ProgressMaximum">
            <summary>
                <para>Gets the maximum value of the Progress Reflector.

</para>
            </summary>
            <value>An integer value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.ProgressValue">
            <summary>
                <para>Gets a value which reflects the state of a process being tracked by the Progress Reflector. 

</para>
            </summary>
            <value>An integer value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.ProgressVisibility">
            <summary>
                <para>Gets the Progress Reflector visibility state.
</para>
            </summary>
            <value><b>true</b> if the Progress Reflector is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.SaveCommand">
            <summary>
                <para>Saves the current document.
</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.ScaleCommand">
            <summary>
                <para>Adjusts the scale of the current report.



</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.SendCommand">
            <summary>
                <para>Exports and attaches the current document to an e-mail.
</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.StopCommand">
            <summary>
                <para>Interrupts the document's generation.
</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.ToggleDocumentMapCommand">
            <summary>
                <para>Executes the command, which shows or hides the Document Map panel.
</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface that identifies the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.ToggleParametersPanelCommand">
            <summary>
                <para>Executes the command, which shows or hides the Parameters panel.

</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface that identifies the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.ToggleSearchPanelCommand">
            <summary>
                <para>Executes the command, which shows or hides the <b>Search</b> panel.
</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface that identifies the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IDocumentPreviewModel.WatermarkCommand">
            <summary>
                <para>Runs the <b>Watermark</b> dialog for the current document.
</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Printing.ZoomItemBase">

            <summary>
                <para>The base class for the <see cref="T:DevExpress.Xpf.Printing.ZoomValueItem"/> class.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Printing.ZoomItemBase.DisplayedText">
            <summary>
                <para>Gets the text representation of a <see cref="T:DevExpress.Xpf.Printing.ZoomValueItem"/> in Print Preview.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Printing.ZoomItemBase.ToString">
            <summary>
                <para>Returns the text representation of the ZoomItemBase object.
</para>
            </summary>
            <returns>A string representation of the current object.
</returns>


        </member>
        <member name="T:DevExpress.Xpf.Printing.DocumentPreviewControl">

            <summary>
                <para>A control to preview documents.


</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.DocumentPreviewControl.#ctor">
            <summary>
                <para>Initializes a new instance of the DocumentPreviewControl class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewControl.DocumentPreviewControl(System.Windows.DependencyObject)">
            <summary>
                <para>Gets or sets the <see cref="T:DevExpress.Xpf.Printing.DocumentPreviewControl"/>. This is an attached property.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Printing.DocumentPreviewControl"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Printing.DocumentPreviewControl.DocumentPreviewControlProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.DocumentPreviewControl.GetDocumentPreviewControl(System.Windows.DependencyObject)">
            <summary>
                <para>Gets the value of the <see cref="P:DevExpress.Xpf.Printing.DocumentPreviewControl.DocumentPreviewControl"/> attached property from a given object.
</para>
            </summary>
            <param name="obj">
		An object whose DocumentPreviewControl property's value must be returned.

            </param>
            <returns>The value of the DocumentPreviewControl attached property for the specified object.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewControl.Model">
            <summary>
                <para>Specifies the model for the Document Preview.
</para>
            </summary>
            <value>An object implementing the <see cref="T:DevExpress.Xpf.Printing.IPreviewModel"/> interface.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Printing.DocumentPreviewControl.ModelProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.DocumentPreviewControl.SetDocumentPreviewControl(System.Windows.DependencyObject,DevExpress.Xpf.Printing.DocumentPreview)">
            <summary>
                <para>Sets the value of the <see cref="P:DevExpress.Xpf.Printing.DocumentPreviewControl.DocumentPreviewControl"/> attached property for a given object.
</para>
            </summary>
            <param name="obj">
		An object for which the DocumentPreviewControl attached property is set.

            </param>
            <param name="value">
		The value for the DocumentPreviewControl attached property.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Printing.XtraReportPreviewModel">

            <summary>
                <para>Provides the Preview Model functionality for Reporting.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.XtraReportPreviewModel.#ctor">
            <summary>
                <para>Initializes a new instance of the XtraReportPreviewModel class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Printing.XtraReportPreviewModel.#ctor(DevExpress.XtraReports.IReport)">
            <summary>
                <para>Initializes a new instance of the XtraReportPreviewModel class with the specified report.
</para>
            </summary>
            <param name="report">
		An object implementing the <see cref="T:DevExpress.XtraReports.IReport"/> interface (typically, the <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> class instance).

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Printing.XtraReportPreviewModel.AutoShowParametersPanel">
            <summary>
                <para>Specifies whether or not the <b>Parameters</b> panel is visible in the Print Preview.
</para>
            </summary>
            <value><b>true</b> to automatically show the <b>Parameters</b> UI; otherwise <b>false</b>.
</value>


        </member>
        <member name="E:DevExpress.Xpf.Printing.XtraReportPreviewModel.CustomizeParameterEditors">
            <summary>
                <para>Occurs when standard editors are created for the report parameters, according to their types.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Printing.XtraReportPreviewModel.IsEmptyDocument">
            <summary>
                <para>Gets the value specifying whether or not the document has any pages.
</para>
            </summary>
            <value><b>true</b> if the document is empty; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.XtraReportPreviewModel.IsSetWatermarkVisible">
            <summary>
                <para>Specifies whether or not the <b>Watermark</b> button is visible.

</para>
            </summary>
            <value><b>true</b> if the button is visible; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.XtraReportPreviewModel.ParametersPanelContent">
            <summary>
                <para>Provides access to the elements of the <b>Parameters</b> panel.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.FrameworkElement"/> object that stores parameter editors available in the panel.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.XtraReportPreviewModel.Report">
            <summary>
                <para>Specifies the report assigned to the model.
</para>
            </summary>
            <value>An object implementing the <see cref="T:DevExpress.XtraReports.IReport"/> interface (typically, the <see cref="T:DevExpress.XtraReports.UI.XtraReport"/> class instance).
</value>


        </member>
        <member name="T:DevExpress.Xpf.Printing.DocumentPreviewWindow">

            <summary>
                <para>A window to preview documents.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.DocumentPreviewWindow.#ctor">
            <summary>
                <para>Initializes a new instance of the DocumentPreviewWindow class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Printing.DocumentPreviewWindow.InitializeComponent">
            <summary>
                <para>Initializes the DocumentPreviewWindow from XAML.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewWindow.Model">
            <summary>
                <para>Specifies the model for the Document Preview.
</para>
            </summary>
            <value>An object implementing the <see cref="T:DevExpress.Xpf.Printing.IDocumentPreviewModel"/> interface.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Printing.DocumentPreviewWindow.ModelProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Printing.DocumentPreview">

            <summary>
                <para>A control with a toolbar and status bar to preview, print and export documents.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.DocumentPreview.#ctor">
            <summary>
                <para>Initializes a new instance of the DocumentPreview class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreview.BarManager">
            <summary>
                <para>Provides access to the <see cref="T:DevExpress.Xpf.Bars.BarManager"/> object associated with the DocumentPreview.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Bars.BarManager"/> object.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreview.DocumentPreviewControl">
            <summary>
                <para>Provides access to the <see cref="T:DevExpress.Xpf.Printing.DocumentPreviewControl"/> object associated with the DocumentPreview. This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Printing.DocumentPreviewControl"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Printing.DocumentPreview.DocumentPreviewControlProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.DocumentPreview.InitializeComponent">
            <summary>
                <para>Initializes the DocumentPreview from XAML.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreview.Model">
            <summary>
                <para>Specifies the model for the Document Preview.
</para>
            </summary>
            <value>An object implementing the <see cref="T:DevExpress.Xpf.Printing.IDocumentPreviewModel"/> interface.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Printing.DocumentPreview.ModelProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Printing.PrintingLocalizer">

            <summary>
                <para>Provides the means to localize the DXPrinting's user interface elements.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintingLocalizer.#ctor">
            <summary>
                <para>Initializes a new instance of the PrintingLocalizer class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintingLocalizer.CreateDefaultLocalizer">
            <summary>
                <para>Returns a Localizer object representing resources based on the the thread's language and regional settings (culture).
</para>
            </summary>
            <returns>An <see cref="T:DevExpress.Utils.Localization.XtraLocalizer`1"/> object representing resources based on the thread's culture.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintingLocalizer.CreateResXLocalizer">
            <summary>
                <para>For internal use. A <see cref="T:DevExpress.Utils.Localization.XtraLocalizer`1"/> object storing resources based on the thread's culture.
</para>
            </summary>
            <returns>Returns an <b>XtraLocalizer</b> object storing resources based on the thread's language and regional settings (culture). 
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintingLocalizer.GetString(DevExpress.Xpf.Printing.PrintingStringId)">
            <summary>
                <para>Returns a localized string for the given string identifier.
</para>
            </summary>
            <param name="id">
		A <see cref="T:DevExpress.Xpf.Printing.PrintingStringId"/> enumeration value identifying the string to localize.

            </param>
            <returns>A <see cref="T:System.String"/> corresponding to the specified identifier.
</returns>


        </member>
        <member name="T:DevExpress.Xpf.Printing.PrintingStringId">

            <summary>
                <para>Contains values corresponding to strings that can be localized.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.Cancel">
            <summary>
                <para>The text of the Cancel button, which may appear in any DXPrinting dialog.

<para><b>Default value</b>: "Cancel"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ClosePreview">
            <summary>
                <para>The caption of the Close Preview button.

<para><b>Default value</b>: "Close"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.CurrentPageDisplayFormat">
            <summary>
                <para>The pattern used to format the current and total page values in the status bar, shown at the bottom of the Print Preview window. 

<para><b>Default value</b>: "Page {0} of {1}"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.DecreaseZoom">
            <summary>
                <para>The tooltip text for the toolbar button that decreases the current zoom value.

<para><b>Default value</b>: "Zoom Out"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.DefaultPrintJobDescription">
            <summary>
                <para>The description used for a print job, by default.

<para><b>Default value</b>: "Document"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.DocumentMap">
            <summary>
                <para>The caption of the Document Map panel and the text of the corresponding button in the Main toolbar.

<para><b>Default value</b>: "Document Map"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.Error">
            <summary>
                <para>The title of the Error message box in the <b>Page Setup</b> dialog.

<para><b>Default value</b>: "Error"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.Exception_NoPrinterFound">
            <summary>
                <para>The text of an error message shown when there is no printer available.

<para><b>Default value</b>: "No printer has been found on the machine"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ExportCsv">
            <summary>
                <para>The caption of the drop-down item, which is present in the <b>Export Document...</b> drop-down button and exports the current document to CSV. 

<para><b>Default value</b>: "CSV File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ExportCsvToWindow">
            <summary>
                <para>The caption of the CSV export format available in the <b>Export Document to Window...</b> drop-down menu.

<para><b>Default value</b>: "CSV File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ExportFile">
            <summary>
                <para>The tooltip text for the drop-down toolbar button, which provides the capability to export a document to one of the available export formats.

<para><b>Default value</b>: "Export Document..."</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ExportFileToWindow">
            <summary>
                <para>The tooltip text for the toolbar button, which provides the capability to export a document to one of the available formats.

<para><b>Default value</b>: "Export Document to Window..."</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ExportHtm">
            <summary>
                <para>The caption of the drop-down item, which is present in the <b>Export Document...</b> drop-down button and exports the current document to HTML. 

<para><b>Default value</b>: "HTML File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ExportHtmToWindow">
            <summary>
                <para>The caption of the HTML export format available in the <b>Export Document to Window...</b> drop-down menu.

<para><b>Default value</b>: "HTML File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ExportImage">
            <summary>
                <para>The caption of the drop-down item, which is present in the <b>Export Document...</b> drop-down button, and exports the current document to an image file. 

<para><b>Default value</b>: "Image File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ExportImageToWindow">
            <summary>
                <para>The caption of the image export format available in the <b>Export Document to Window...</b> drop-down menu.

<para><b>Default value</b>: "Image File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ExportMht">
            <summary>
                <para>The caption of the drop-down item, which is present in the <b>Export Document...</b> drop-down button and exports the current document to MHT. 

<para><b>Default value</b>: "MHT File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ExportMhtToWindow">
            <summary>
                <para>The caption of the MHT export format available in the <b>Export Document to Window...</b> drop-down menu.

<para><b>Default value</b>: "MHT File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ExportPdf">
            <summary>
                <para>The caption of the drop-down item, which is present in the <b>Export Document...</b> drop-down button and exports the current document to PDF. 

<para><b>Default value</b>: "PDF File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ExportPdfToWindow">
            <summary>
                <para>The caption of the PDF export format available in the <b>Export Document to Window...</b> drop-down menu.

<para><b>Default value</b>: "PDF File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ExportRtf">
            <summary>
                <para>The caption of the drop-down item, which is present in the <b>Export Document...</b> drop-down button and exports the current document to RTF. 

<para><b>Default value</b>: "RTF File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ExportRtfToWindow">
            <summary>
                <para>The caption of the RTF export format available in the <b>Export Document to Window...</b> drop-down menu.

<para><b>Default value</b>: "RTF File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ExportTxt">
            <summary>
                <para>The caption of the drop-down item, which is present in the <b>Export Document...</b> drop-down button and exports the current document to Text. 

<para><b>Default value</b>: "Text File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ExportTxtToWindow">
            <summary>
                <para>The caption of the text export format available in the <b>Export Document to Window...</b> drop-down menu.

<para><b>Default value</b>: "TXT File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ExportXls">
            <summary>
                <para>The caption of the drop-down item, which is present in the <b>Export Document...</b> drop-down button and exports the current document to XLS. 

<para><b>Default value</b>: "XLS File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ExportXlsToWindow">
            <summary>
                <para>The caption of the XLS export format available in the <b>Export Document to Window...</b> drop-down menu.

<para><b>Default value</b>: "XLS File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ExportXlsx">
            <summary>
                <para>The caption of the drop-down item, which is present in the <b>Export Document...</b> drop-down button and exports the current document to XLSX. 

<para><b>Default value</b>: "XLSX File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ExportXlsxToWindow">
            <summary>
                <para>The caption of the XLSX export format available in the <b>Export Document to Window...</b> drop-down menu.

<para><b>Default value</b>: "XLSX File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ExportXps">
            <summary>
                <para>The caption of the drop-down item, which is present in the <b>Export Document...</b> drop-down button and exports the current document to XPS. 

<para><b>Default value</b>: "XPS File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ExportXpsToWindow">
            <summary>
                <para>The caption of the XPS export format available in the <b>Export Document to Window...</b> drop-down menu.

<para><b>Default value</b>: "XPS File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.FirstPage">
            <summary>
                <para>The tooltip text for the toolbar button, which moves Print Preview to the first page of the document.

<para><b>Default value</b>: "First Page"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.GoToPage">
            <summary>
                <para>The text before the editor, which shows the current page number in the status bar of the Print Preview window.

<para><b>Default value</b>: "Page:"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.IncreaseZoom">
            <summary>
                <para>The tooltip text for the toolbar button, which increases the current zoom value.

<para><b>Default value</b>: "Zoom In"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.Information">
            <summary>
                <para>The title of the dialog shown when a report is expired.

<para><b>Default value</b>: "Information"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.LastPage">
            <summary>
                <para>The tooltip text for the toolbar button, which moves Print Preview to the last page of the document.

<para><b>Default value</b>: "Last Page"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.Msg_EmptyDocument">
            <summary>
                <para>The text displayed when the document is empty.

<para><b>Default value</b>: "The document does not contain any pages."</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.MsgCaption">
            <summary>
                <para>The caption of all message boxes that can be invoked by the DXPrinting library at runtime.

<para><b>Default value</b>: "DXPrinting"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.NextPage">
            <summary>
                <para>The tooltip text for the toolbar button, which moves Print Preview to the next page of the document.

<para><b>Default value</b>: "Next Page"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.OK">
            <summary>
                <para>The text of the OK button, which may appear in any DXPrinting dialog.

<para><b>Default value</b>: "OK"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.Open">
            <summary>
                <para>The text of the tooltip shown for the Open button in the Main toolbar.

<para><b>Default value</b>: "Open"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PagesArePrepared">
            <summary>
                <para>The text of a status message shown after a document's pages are created.

<para><b>Default value</b>: "Pages are ready. Continue printing?"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PageSetup">
            <summary>
                <para>The tooltip text for the toolbar button, which invokes the Page Setup dialog for the current document.

<para><b>Default value</b>: "Page Setup..."</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PageSetupInches">
            <summary>
                <para>The description of the specified measurement uint in the <b>Page Setup</b> dialog.

<para><b>Default value</b>: "Inches"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PageSetupMarginsBottom">
            <summary>
                <para>The text of the bottom margin field's caption in the <b>Page Setup</b> dialog.

<para><b>Default value</b>: "Bottom:"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PageSetupMarginsCaptionFormat">
            <summary>
                <para>The text of the page margins section caption in the <b>Page Setup</b> dialog.

<para><b>Default value</b>: "Margins in {0}"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PageSetupMarginsLeft">
            <summary>
                <para>The text of the left margin field's caption in the <b>Page Setup</b> dialog.

<para><b>Default value</b>: "Left:"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PageSetupMarginsRight">
            <summary>
                <para>The text of the right margin field's caption in the <b>Page Setup</b> dialog.

<para><b>Default value</b>: "Right:"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PageSetupMarginsTop">
            <summary>
                <para>The text of the top margin field's caption in the <b>Page Setup</b> dialog.

<para><b>Default value</b>: "Top:"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PageSetupMillimeters">
            <summary>
                <para>The description of the specified measurement unit in the <b>Page Setup</b> dialog.

<para><b>Default value</b>: "Millimeters"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PageSetupOrientationCaption">
            <summary>
                <para>The caption of the text orientation radio group in the <b>Page Setup</b> dialog.

<para><b>Default value</b>: "Orientation:"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PageSetupOrientationLandscape">
            <summary>
                <para>The caption of the radio button corresponding to the landscape page orientation, in the <b>Page Setup</b> dialog.

<para><b>Default value</b>: "Landscape"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PageSetupOrientationPortrait">
            <summary>
                <para>The caption of the radio button corresponding to the portrait page orientation, in the <b>Page Setup</b> dialog.

<para><b>Default value</b>: "Portrait"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PageSetupPaperCaption">
            <summary>
                <para>The text of the paper settings group's caption in the <b>Page Setup</b> dialog.

<para><b>Default value</b>: "Paper"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PageSetupPaperSize">
            <summary>
                <para>The caption of the paper size drop-down selector in the <b>Page Setup</b> dialog.

<para><b>Default value</b>: "Paper size:"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PageSetupPrinter">
            <summary>
                <para>The caption of the printer drop-down selector in the <b>Page Setup</b> dialog.

<para><b>Default value</b>: "Printer:"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PageSetupPrinterCaption">
            <summary>
                <para>The text of the printer settings group's caption in the <b>Page Setup</b> dialog.

<para><b>Default value</b>: "Printer"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PageSetupPrinterComment">
            <summary>
                <para>The text of the printer comment caption in the <b>Page Setup</b> dialog.

<para><b>Default value</b>: "Comment:"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PageSetupPrinterPort">
            <summary>
                <para>The text of the printer port caption in the <b>Page Setup</b> dialog.

<para><b>Default value</b>: "Port:"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PageSetupPrinterType">
            <summary>
                <para>The text of the printer type caption in the <b>Page Setup</b> dialog.

<para><b>Default value</b>: "Type:"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PageSetupWindowTitle">
            <summary>
                <para>The title of the <b>Page Setup</b> dialog.

<para><b>Default value</b>: "Page Setup"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_A2">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "A2"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_A3">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "A3"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_A3Extra">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "A3 Extra"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_A3ExtraTransverse">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "A3 Extra Transverse"</para>


</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_A3Rotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "A3 Rotated"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_A3Transverse">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "A3 Transverse"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_A4">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "A4"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_A4Extra">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "A4 Extra"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_A4Plus">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "SuperA/SuperA/A4"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_A4Rotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "A4 Rotated"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_A4Small">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "A4 Small"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_A4Transverse">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "A4 Transverse"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_A5">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "A5"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_A5Extra">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "A5 Extra"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_A5Rotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "A5 Rotated"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_A5Transverse">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "A5 Transverse"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_A6">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "A6"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_A6Rotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "A6 Rotated"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_APlus">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "SuperA/SuperA/A4"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_B4">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "B4"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_B4Envelope">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "B4 Envelope"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_B4JisRotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "JIS B4 Rotated "</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_B5">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "B5"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_B5Envelope">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "B5 Envelope"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_B5Extra">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "ISO B5 Extra"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_B5JisRotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "JIS B5 Rotated"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_B5Transverse">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "JIS B5 Transverse"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_B6Envelope">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "B6 Envelope"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_B6Jis">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "JIS B6"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_B6JisRotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "JIS B6 Rotated"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_BPlus">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "SuperB/SuperB/A3"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_C3Envelope">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "C3 Envelope"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_C4Envelope">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "C4 Envelope"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_C5Envelope">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "C5 Envelope"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_C65Envelope">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "C65 Envelope"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_C6Envelope">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "C6 Envelope"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_CSheet">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "C Sheet"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_Custom">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Custom"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_DLEnvelope">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "DL Envelope"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_DSheet">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "D Sheet"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_ESheet">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "E Sheet"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_Executive">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Executive"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_Folio">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Folio"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_GermanLegalFanfold">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "German Legal Fanfold"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_GermanStandardFanfold">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "German Standard Fanfold"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_InviteEnvelope">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Invite Envelope"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_IsoB4">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Iso B4"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_ItalyEnvelope">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Italy Envelope"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_JapaneseDoublePostcard">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Japanese Double Postcard"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_JapaneseDoublePostcardRotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Japanese Double Postcard Rotated"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_JapaneseEnvelopeChouNumber3">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Japanese Envelope Chou Number 3"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_JapaneseEnvelopeChouNumber3Rotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Japanese Envelope Chou Number 3 Rotated"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_JapaneseEnvelopeChouNumber4">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Japanese Envelope Chou Number 4"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_JapaneseEnvelopeChouNumber4Rotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Japanese Envelope Chou Number 4 Rotated"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_JapaneseEnvelopeKakuNumber2">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Japanese Envelope Kaku Number 2"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_JapaneseEnvelopeKakuNumber2Rotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Japanese Envelope Kaku Number 2 Rotated"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_JapaneseEnvelopeKakuNumber3">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Japanese Envelope Kaku Number 3"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_JapaneseEnvelopeKakuNumber3Rotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Japanese Envelope Kaku Number 3 Rotated"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_JapaneseEnvelopeYouNumber4">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Japanese Envelope You Number 4"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_JapaneseEnvelopeYouNumber4Rotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Japanese Envelope You Number 4 Rotated"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_JapanesePostcard">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Japanese Postcard"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_JapanesePostcardRotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Japanese Postcard Rotated"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_Ledger">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Ledger"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_Legal">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Legal"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_LegalExtra">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Legal Extra"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_Letter">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Letter"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_LetterExtra">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Letter Extra"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_LetterExtraTransverse">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Letter Extra Transverse"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_LetterPlus">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Letter Plus"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_LetterRotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Letter Rotated"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_LetterSmall">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Letter Small"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_LetterTransverse">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Letter Transverse"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_MonarchEnvelope">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Monarch Envelope"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_Note">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Note"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_Number10Envelope">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Number 10 Envelope"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_Number11Envelope">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Number 11 Envelope"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_Number12Envelope">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Number 12 Envelope"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_Number14Envelope">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Number 14 Envelope"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_Number9Envelope">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Number 9 Envelope"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_PersonalEnvelope">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Personal Envelope (6 3/4)"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_Prc16K">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Prc 16K"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_Prc16KRotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Prc 16K Rotated"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_Prc32K">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Prc 32K"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_Prc32KBig">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Prc 32K Big"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_Prc32KBigRotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Prc 32K Big Rotated"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_Prc32KRotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Prc 32K Rotated"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_PrcEnvelopeNumber1">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Prc Envelope Number 1"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_PrcEnvelopeNumber10">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Prc Envelope Number 10"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_PrcEnvelopeNumber10Rotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Prc Envelope Number 10 Rotated"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_PrcEnvelopeNumber1Rotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Prc Envelope Number 1 Rotated"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_PrcEnvelopeNumber2">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Prc Envelope Number 2"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_PrcEnvelopeNumber2Rotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Prc Envelope Number 2 Rotated"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_PrcEnvelopeNumber3">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Prc Envelope Number 3"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_PrcEnvelopeNumber3Rotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Prc Envelope Number 3 Rotated"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_PrcEnvelopeNumber4">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Prc Envelope Number 4"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_PrcEnvelopeNumber4Rotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Prc Envelope Number 4 Rotated"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_PrcEnvelopeNumber5">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Prc Envelope Number 5"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_PrcEnvelopeNumber5Rotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Prc Envelope Number 5 Rotated"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_PrcEnvelopeNumber6">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Prc Envelope Number 6"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_PrcEnvelopeNumber6Rotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Prc Envelope Number 6 Rotated"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_PrcEnvelopeNumber7">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Prc Envelope Number 7"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_PrcEnvelopeNumber7Rotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Prc Envelope Number 7 Rotated"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_PrcEnvelopeNumber8">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Prc Envelope Number 8"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_PrcEnvelopeNumber8Rotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Prc Envelope Number 8 Rotated"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_PrcEnvelopeNumber9">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Prc Envelope Number 9"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_PrcEnvelopeNumber9Rotated">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Prc Envelope Number 9 Rotated"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_Quarto">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Quarto"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_Standard10x11">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Standard 10x11"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_Standard10x14">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Standard 10x14"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_Standard11x17">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Standard 11x17"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_Standard12x11">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Standard 12x11"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_Standard15x11">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Standard 15x11"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_Standard9x11">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Standard 9x11"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_Statement">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Statement"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_Tabloid">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Tabloid"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_TabloidExtra">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "Tabloid Extra"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PaperKind_USStandardFanfold">
            <summary>
                <para>A paper kind.

<para><b>Default value</b>: "US Standard Fanfold"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.Parameters">
            <summary>
                <para>The caption of the Parameters panel and the text of the corresponding button in the Main toolbar.

<para><b>Default value</b>: "Parameters"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ParametersReset">
            <summary>
                <para>The caption of the Reset button in the Parameters panel.

<para><b>Default value</b>: "Reset"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ParametersSubmit">
            <summary>
                <para>The caption of the Submit button in the Parameters panel.

<para><b>Default value</b>: "Submit"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PdfPasswordSecurityOptions_ChangingPermissions">
            <summary>
                <para>The caption of the Changing Permissions group in the <b>PDF Password Security</b> dialog.

<para><b>Default value</b>: "Changes allowed:"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PdfPasswordSecurityOptions_EnableCopying">
            <summary>
                <para>The text of the <b>PDF Password Security</b> dialog's option, which enables the copying of text, images and other content in the resulting PDF document.

<para><b>Default value</b>: "Enable copying of text, images and other content"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PdfPasswordSecurityOptions_EnableScreenReaders">
            <summary>
                <para>The text of the <b>PDF Password Security</b> dialog's option, which enables text access for screen reader devices for the resulting PDF document.

<para><b>Default value</b>: "Enable text access for screen reader devices for the visually impaired"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PdfPasswordSecurityOptions_OpenPassword">
            <summary>
                <para>The caption of the Open Password edit box in the <b>PDF Password Security</b> dialog.

<para><b>Default value</b>: "Document open password:"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PdfPasswordSecurityOptions_OpenPasswordHeader">
            <summary>
                <para>The caption of the header shown above the Open Password edit box in the <b>PDF Password Security</b> dialog.

<para><b>Default value</b>: "Document Open Password"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PdfPasswordSecurityOptions_Permissions">
            <summary>
                <para>The caption of the Permissions section in the <b>PDF Password Security</b> dialog.

<para><b>Default value</b>: "Permissions"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PdfPasswordSecurityOptions_PermissionsPassword">
            <summary>
                <para>The caption of the Change Permissions Password edit box in the <b>PDF Password Security</b> dialog.

<para><b>Default value</b>: "Change permissions password:"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PdfPasswordSecurityOptions_PrintingPermissions">
            <summary>
                <para>The text of the <b>PDF Password Security</b> dialog's option, which enables/disables printing the resulting PDF document.

<para><b>Default value</b>: "Printing allowed:"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PdfPasswordSecurityOptions_RequireOpenPassword">
            <summary>
                <para>The text of the <b>PDF Password Security</b> dialog's option, which enables the password requirement to open a document.

<para><b>Default value</b>: "Require a password to open the document"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PdfPasswordSecurityOptions_RestrictPermissions">
            <summary>
                <para>The text of the <b>PDF Password Security</b> dialog's option, which enables restricting editing and printing of the document.

<para><b>Default value</b>: "Restrict editing and printing of the document"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PdfPasswordSecurityOptions_Title">
            <summary>
                <para>The title of the <b>PDF Password Security</b> dialog.

<para><b>Default value</b>: "PDF Password Security"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PdfSignatureEditorWindow_Certificate">
            <summary>
                <para>The description of the Certificate field in the <b>Signature Options</b> dialog.

<para><b>Default value</b>: "Certificate:"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PdfSignatureEditorWindow_ContactInformation">
            <summary>
                <para>The description of the Contact Information field in the <b>Signature Options</b> dialog.

<para><b>Default value</b>: "Contact Information:"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PdfSignatureEditorWindow_Location">
            <summary>
                <para>The description of the Location field in the <b>Signature Options</b> dialog.

<para><b>Default value</b>: "Location:"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PdfSignatureEditorWindow_Reason">
            <summary>
                <para>The description of the Reason field in the <b>Signature Options</b> dialog.

<para><b>Default value</b>: "Reason:"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PdfSignatureEditorWindow_Title">
            <summary>
                <para>The title of the <b>Signature Options</b> dialog.

<para><b>Default value</b>: "Signature Options"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PictureWatermarkTitle">
            <summary>
                <para>The caption of the picture watermark tab in the <b>Watermark</b> dialog.

<para><b>Default value</b>: "Picture Watermark"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PreparingPages">
            <summary>
                <para>The text of a status message shown while a document's pages are being created.

<para><b>Default value</b>: "Preparing pages..."</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PreviousPage">
            <summary>
                <para>The tooltip text for the toolbar button, which moves Print Preview to the previous page of the document.

<para><b>Default value</b>: "Previous Page"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.Print">
            <summary>
                <para>The tooltip text for the <b>Print...</b> drop-down menu item that invokes the Print dialog for the current document.

<para><b>Default value</b>: "Print..."</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PrintDirect">
            <summary>
                <para>The tooltip text for the toolbar button, which prints the document preview directly, without any dialogs. 

<para><b>Default value</b>: "Quick Print"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PrintPdf">
            <summary>
                <para>The tooltip text for the <b>Print...</b> drop-down menu item that allows exporting the current document to PDF (in Silverlight only).

<para><b>Default value</b>: "Print via Pdf..."</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.PrintPreviewWindowCaption">
            <summary>
                <para>The caption of the Print Preview window.

<para><b>Default value</b>: "Print Preview"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.Refresh">
            <summary>
                <para>The text of the <b>Refresh</b> toolbar button in the dialog shown when a report is expired.

<para><b>Default value</b>: "Refresh"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.RepeatPassword_ConfirmationPasswordDoesNotMatch">
            <summary>
                <para>The text of the warning that appears if the confirmation password doesn't match.

<para><b>Default value</b>: "Confirmation password does not match. Please start over and enter the password again."</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.RepeatPassword_OpenPassword">
            <summary>
                <para>The caption of the Open Password edit box in the <b>Confirm Document Open Password</b> dialog.

<para><b>Default value</b>: "Document open password:"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.RepeatPassword_OpenPassword_Note">
            <summary>
                <para>The text of the note in the <b>Confirm Document Open Password</b> dialog.

<para><b>Default value</b>: "Please confirm the Document Open Password. Be sure to make a note of the password. It will be required to open the document."</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.RepeatPassword_OpenPassword_Title">
            <summary>
                <para>The title of the <b>Confirm Document Open Password</b> dialog.

<para><b>Default value</b>: "Confirm Document Open Password"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.RepeatPassword_PermissionsPassword">
            <summary>
                <para>The caption of the Permissions Password edit box in the <b>Confirm Permissions Password</b> dialog.

<para><b>Default value</b>: "_Permissions password:"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.RepeatPassword_PermissionsPassword_Note">
            <summary>
                <para>The text of the note in the <b>Confirm Permissions Password</b> dialog.

<para><b>Default value</b>: "Please confirm the Permissions Password. Be sure to make a note of the password. You will need it to change these settings in the future."</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.RepeatPassword_PermissionsPassword_Title">
            <summary>
                <para>The title of the <b>Confirm Permissions Password</b> dialog.

<para><b>Default value</b>: "Confirm Permissions Password"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.Save">
            <summary>
                <para>The text of the tooltip shown for the Save button in the Main toolbar.

<para><b>Default value</b>: "Save"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.Scaling">
            <summary>
                <para>The text of the tooltip shown for the Scale button in the Main toolbar, as well as the corresponding dialog's title.

<para><b>Default value</b>: "Scale"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.Scaling_Adjust_End_Label">
            <summary>
                <para>The caption of the drop-down list shown in the Scale dialog, when the "Adjust to" option is selected.

<para><b>Default value</b>: "normal size"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.Scaling_Adjust_Start_Label">
            <summary>
                <para>The caption of the radio button shown in the Scale dialog.

<para><b>Default value</b>: "Adjust to"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.Scaling_ComboBoxEdit_Validation_Error">
            <summary>
                <para>The text of the error shown when the value entered in the drop-down list of the Scale dialog is not valid.

<para><b>Default value</b>: "The value is not valid"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.Scaling_ComboBoxEdit_Validation_OutOfRange_Error">
            <summary>
                <para>The text of the error shown when the value entered in the drop-down list of the Scale dialog is out of the permissable range.

<para><b>Default value</b>: "The value is out of range"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.Scaling_Fit_End_Label">
            <summary>
                <para>The caption of the drop-down list shown in the Scale dialog, when the "Fit to" option is selected.

<para><b>Default value</b>: "pages wide"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.Scaling_Fit_Start_Label">
            <summary>
                <para>The caption of the radio button shown in the Scale dialog.

<para><b>Default value</b>: "Fit to"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.Search">
            <summary>
                <para>The text of the tooltip shown for the Search button in the Main toolbar.

<para><b>Default value</b>: "Search"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.Search_EmptyResult">
            <summary>
                <para>The notification displayed when no matches were found for the specified text.

<para><b>Default value</b>: "Your search did not match any text."</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.SendCsv">
            <summary>
                <para>The caption of the drop-down item, which is present in the <b>Send via e-mail...</b> drop-down button and exports the current document to CSV.

<para><b>Default value</b>: "CSV File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.SendFile">
            <summary>
                <para>The tooltip text for the drop-down toolbar button, which provides the capability to export a document to one of the available export formats and send it via e-mail.

<para><b>Default value</b>: "Send via e-mail..."</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.SendImage">
            <summary>
                <para>The caption of the drop-down item present in the <b>Send via e-mail...</b> drop-down button, and exports the current document to an image file.

<para><b>Default value</b>: "Image File"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.SendMht">
            <summary>
                <para>The caption of the drop-down item, which is present in the <b>Send via e-mail...</b> drop-down button and exports the current document to MHT.

<para><b>Default value</b>: "MHT File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.SendPdf">
            <summary>
                <para>The caption of the drop-down item, which is present in the <b>Send via e-mail...</b> drop-down button and exports the current document to PDF.

<para><b>Default value</b>: "PDF File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.SendRtf">
            <summary>
                <para>The caption of the drop-down item, which is present in the <b>Send via e-mail...</b> drop-down button and exports the current document to RTF.

<para><b>Default value</b>: "RTF File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.SendTxt">
            <summary>
                <para>The caption of the drop-down item, which is present in the <b>Send via e-mail...</b> drop-down button and exports the current document to Text.

<para><b>Default value</b>: "Text File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.SendXls">
            <summary>
                <para>The caption of the drop-down item, which is present in the <b>Send via e-mail...</b> drop-down button and exports the current document to XLS.

<para><b>Default value</b>: "XLS File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.SendXlsx">
            <summary>
                <para>The caption of the drop-down item, which is present in the <b>Send via e-mail...</b> drop-down button and exports the current document to XLSX.

<para><b>Default value</b>: "XLSX File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.SendXps">
            <summary>
                <para>The caption of the drop-down item, which is present in the <b>Send via e-mail...</b> drop-down button and exports the current document to XPS.

<para><b>Default value</b>: "XPS File"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.StatusBarCaption">
            <summary>
                <para>The caption of the status bar, shown at the bottom of the Print Preview window. 

<para><b>Default value</b>: "Status Bar"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.StopPageBuilding">
            <summary>
                <para>The caption of the button displayed in the status bar that interrupts document creation. 

<para><b>Default value</b>: "Stop"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.TextWatermarkTitle">
            <summary>
                <para>The caption of the text watermark tab in the <b>Watermark</b> dialog.

<para><b>Default value</b>: "Text Watermark"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ToolBarCaption">
            <summary>
                <para>The caption of the toolbar, which contains Print Preview buttons.

<para><b>Default value</b>: "Print Preview"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.Watermark">
            <summary>
                <para>The tooltip text for the button which invokes the <b>Watermark</b> dialog for the current document.

<para><b>Default value</b>: "Watermark"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.WatermarkClearAll">
            <summary>
                <para>The caption of the button that restores the default watermark settings in the <b>Watermark</b> dialog.

<para><b>Default value</b>: "Clear All"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.WatermarkFontBold">
            <summary>
                <para>The caption of the check box that enables the bold font in the <b>Watermark</b> dialog.

<para><b>Default value</b>: "Bold"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.WatermarkFontItalic">
            <summary>
                <para>The caption of the check box that enables the italic font in the <b>Watermark</b> dialog.

<para><b>Default value</b>: "Italic"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.WatermarkFontName">
            <summary>
                <para>The caption of the drop-down menu specifying the font in the <b>Watermark</b> dialog.

<para><b>Default value</b>: "Font"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.WatermarkFontSize">
            <summary>
                <para>The caption of the drop-down menu specifying the font size in the <b>Watermark</b> dialog.

<para><b>Default value</b>: "Size"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.WatermarkImageHorizontalAlignment">
            <summary>
                <para>The caption of the drop-down menu that specifies the picture watermark's horizontal alignment in the <b>Watermark</b> dialog.

<para><b>Default value</b>: "Horizontal alignment"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.WatermarkImageLoadError">
            <summary>
                <para>The text of the dialog detailing image loading error, in the <b>Watermark</b> dialog.

<para><b>Default value</b>: ""</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.WatermarkImageSizeMode">
            <summary>
                <para>The caption of the drop-down menu that specifies the picture watermark's size mode in the <b>Watermark</b> dialog.

<para><b>Default value</b>: "Size mode"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.WatermarkImageTiling">
            <summary>
                <para>The caption of the drop-down menu that specifies the picture watermark's tiling in the <b>Watermark</b> dialog.

<para><b>Default value</b>: "Tiling"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.WatermarkImageVerticalAlignment">
            <summary>
                <para>The caption of the drop-down menu that specifies the picture watermark's vertical alignment in the <b>Watermark</b> dialog.

<para><b>Default value</b>: "Vertical alignment"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.WatermarkLoadImage">
            <summary>
                <para>The caption of the dialog detailing image loading error, in the <b>Watermark</b> dialog.

<para><b>Default value</b>: "Image"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.WatermarkPageRange">
            <summary>
                <para>The caption of the options group related to the page range settings in the <b>Watermark</b> dialog.

<para><b>Default value</b>: "Page Range"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.WatermarkPageRangeAllPages">
            <summary>
                <para>The caption of the radio button that applies a watermark to all report pages in the <b>Watermark</b> dialog.

<para><b>Default value</b>: "All"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.WatermarkPageRangeHint">
            <summary>
                <para>The text of a hint demonstrating how the page range can be specified in the <b>Watermark</b> dialog.

<para><b>Default value</b>: "For example: 1,3,5-12"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.WatermarkPageRangePages">
            <summary>
                <para>The caption of the radio button that adds a watermark to specified report pages in the <b>Watermark</b> dialog.

<para><b>Default value</b>: "Pages"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.WatermarkPosition">
            <summary>
                <para>The caption of the options group related to the watermark position in the <b>Watermark</b> dialog.

<para><b>Default value</b>: "Position"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.WatermarkPositionBehind">
            <summary>
                <para>The caption of the radio button specifying that a watermark is drawn behind the page content, in the <b>Watermark</b> dialog.

<para><b>Default value</b>: "Behind"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.WatermarkPositionInFront">
            <summary>
                <para>The caption of the radio button specifying that a watermark is drawn in front of the page content in the <b>Watermark</b> dialog.

<para><b>Default value</b>: "In front"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.WatermarkText">
            <summary>
                <para>The caption of the watermark text drop-down menu, in the <b>Watermark</b> dialog.

<para><b>Default value</b>: "Text"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.WatermarkTextColor">
            <summary>
                <para>The caption of the drop-down palette for choosing the font color, in the <b>Watermark</b> dialog.

<para><b>Default value</b>: "Color"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.WatermarkTextDirection">
            <summary>
                <para>The caption of the drop-down menu specifying the text direction, in the <b>Watermark</b> dialog.

<para><b>Default value</b>: "Direction"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.WatermarkTitle">
            <summary>
                <para>The caption of the <b>Watermark</b> dialog.

<para><b>Default value</b>: "Watermark"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.WatermarkTransparency">
            <summary>
                <para>The caption of the transparency trackbar in the <b>Watermark</b> dialog.

<para><b>Default value</b>: "Transparency "</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.Zoom">
            <summary>
                <para>The text of the tooltip shown for the Zoom button in the Main toolbar.

<para><b>Default value</b>: "Zoom"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ZoomDisplayFormat">
            <summary>
                <para>The pattern used to format the current <b>Zoom</b> value in the status bar, shown at the bottom of the Print Preview window.

<para><b>Default value</b>: "Zoom: {0:0}%"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ZoomToPageHeight">
            <summary>
                <para>The tooltip text for the toolbar button, which zooms a document in or out as appropriate, so that the height of the current page fits the preview window.

<para><b>Default value</b>: "Page Height"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ZoomToPageWidth">
            <summary>
                <para>The tooltip text for the toolbar button, which zooms a document in or out as appropriate, so that the width of the current page fits the preview window.

<para><b>Default value</b>: "Page Width"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ZoomToTwoPages">
            <summary>
                <para>The tooltip text for the toolbar button, which zooms a document in or out as appropriate, so that only two full pages of the document are shown in the preview. 

<para><b>Default value</b>: "Two Pages"</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ZoomToWholePage">
            <summary>
                <para>The tooltip text for the toolbar button, which zooms a document in or out as appropriate, so that the current page fits the preview window.

<para><b>Default value</b>: "Whole Page"</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.PrintingStringId.ZoomValueItemFormat">
            <summary>
                <para>The display format of the zoom ratio drop-down selector in <b>Print Preview</b>.

<para><b>Default value</b>: "{0}%"</para>
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Printing.LinkBase">

            <summary>
                <para>The base class for all printing links within the DXPrinting library.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.CancelPrintAsync">
            <summary>
                <para>Stops asynchronous printing.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.CreateDocument(System.Boolean)">
            <summary>
                <para>Creates a document from the link, so it can be displayed or printed. Optionally, it can generate pages in the background.

</para>
            </summary>
            <param name="buildPagesInBackground">
		<b>true</b> to generate pages in the background; otherwise, <b>false</b>.


            </param>


        </member>
        <member name="E:DevExpress.Xpf.Printing.LinkBase.CreateDocumentFinished">
            <summary>
                <para>Occurs when the document creation has been finished.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Printing.LinkBase.CreateDocumentStarted">
            <summary>
                <para>Occurs when the document creation has been started.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Printing.LinkBase.CustomPaperSize">
            <summary>
                <para>Gets or sets a size of custom paper.

</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Size"/> value which represents the size of custom paper.

</value>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.Dispose">
            <summary>
                <para>Disposes the LinkBase object.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Printing.LinkBase.DocumentName">
            <summary>
                <para>Gets or sets the name of the document.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value representing the name of a document.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToCsv(System.String,DevExpress.XtraPrinting.CsvExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in CSV format using the specified CSV-specific options.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created CSV file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.CsvExportOptions"/> object which specifies the CSV export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToCsv(System.IO.Stream,DevExpress.XtraPrinting.CsvExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in CSV format using the specified CSV-specific options.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created CSV file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.CsvExportOptions"/> object which specifies the CSV export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToCsv(System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream in CSV format.

</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created CSV file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToCsv(System.String)">
            <summary>
                <para>Exports a report to the specified file path in CSV format.

</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created CSV file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToHtml(System.String,DevExpress.XtraPrinting.HtmlExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in HTML format using the specified HTML-specific options.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created HTML file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.HtmlExportOptions"/> object which specifies the HTML export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToHtml(System.IO.Stream,DevExpress.XtraPrinting.HtmlExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in HTML format using the specified HTML-specific options.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created HTML file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.HtmlExportOptions"/> object which specifies the HTML export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToHtml(System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream in HTML format.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created HTML file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToHtml(System.String)">
            <summary>
                <para>Exports a report to the specified file path in HTML format.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created HTML file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToImage(System.String,DevExpress.XtraPrinting.ImageExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in image format using the specified image-specific options.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created image file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.ImageExportOptions"/> object which specifies the image export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToImage(System.String,System.Drawing.Imaging.ImageFormat)">
            <summary>
                <para>Exports a report to the specified file path in the specified image format.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created image file.

            </param>
            <param name="format">
		A <see cref="T:System.Drawing.Imaging.ImageFormat"/> object which specifies the image format.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToImage(System.String)">
            <summary>
                <para>Exports a report to the specified file path in image format.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created image file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToImage(System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream as an image.


</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created image file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToImage(System.IO.Stream,DevExpress.XtraPrinting.ImageExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in image format using the specified image-specific options.

</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created image file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.ImageExportOptions"/> object which specifies the image export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToImage(System.IO.Stream,System.Drawing.Imaging.ImageFormat)">
            <summary>
                <para>Exports a report to the specified stream in the specified image format.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created image file should be sent.

            </param>
            <param name="format">
		A <see cref="T:System.Drawing.Imaging.ImageFormat"/> object which specifies the image format.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToMht(System.IO.Stream,DevExpress.XtraPrinting.MhtExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in MHT format using the specified MHT-specific options.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created MHT file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.MhtExportOptions"/> object which specifies the MHT export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToMht(System.String,DevExpress.XtraPrinting.MhtExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in MHT format using the specified MHT-specific options.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created MHT file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.MhtExportOptions"/> object which specifies the MHT export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToMht(System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream in MHT format.

</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created MHT file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToMht(System.String)">
            <summary>
                <para>Exports a report to the specified file path in MHT format.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created MHT file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToPdf(System.IO.Stream,DevExpress.XtraPrinting.PdfExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in PDF format using the specified PDF-specific options.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created PDF file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.PdfExportOptions"/> object which specifies the PDF export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToPdf(System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream in PDF format.

</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created PDF file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToPdf(System.String)">
            <summary>
                <para>Exports a report to the specified file path in PDF format.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created PDF file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToPdf(System.String,DevExpress.XtraPrinting.PdfExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in PDF format, using the specified PDF-specific options.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created PDF file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.PdfExportOptions"/> object which specifies the PDF export options to be applied when the pivot grid is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToRtf(System.String,DevExpress.XtraPrinting.RtfExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in RTF format using the specified RTF-specific options.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created RTF file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.RtfExportOptions"/> object which specifies the RTF export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToRtf(System.IO.Stream,DevExpress.XtraPrinting.RtfExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in RTF format using the specified RTF-specific options.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created RTF file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.RtfExportOptions"/> object which specifies the RTF export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToRtf(System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream in RTF format.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created RTF file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToRtf(System.String)">
            <summary>
                <para>Exports a report to the specified file path in RTF format.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created RTF file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToText(System.String,DevExpress.XtraPrinting.TextExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in text format using the specified TXT-specific options.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created text file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.TextExportOptions"/> object which specifies the TXT export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToText(System.IO.Stream,DevExpress.XtraPrinting.TextExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in text format using the specified TXT-specific options.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created text file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.TextExportOptions"/> object which specifies the TXT export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToText(System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream in Text format.

</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created text file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToText(System.String)">
            <summary>
                <para>Exports a report to the specified file path in text format.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created text file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToXls(System.String,DevExpress.XtraPrinting.XlsExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in XLS format using the specified XLS-specific options.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created XLS file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.XlsExportOptions"/> object which specifies the XLS export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToXls(System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream in XLS format.

</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created XLS file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToXls(System.String)">
            <summary>
                <para>Exports a report to the specified file path in XLS format.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created XLS file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToXls(System.IO.Stream,DevExpress.XtraPrinting.XlsExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in XLS format using the specified XLS-specific options.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created XLS file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.XlsExportOptions"/> object which specifies the XLS export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToXlsx(System.IO.Stream,DevExpress.XtraPrinting.XlsxExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in XLSX format using the specified XLSX-specific options.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created XLSX file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.XlsxExportOptions"/> object which specifies the XLSX export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToXlsx(System.String,DevExpress.XtraPrinting.XlsxExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in XLSX format using the specified XLSX-specific options.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created XLSX file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.XlsxExportOptions"/> object which specifies the XLSX export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToXlsx(System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream in XLSX format.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created XLSX file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToXlsx(System.String)">
            <summary>
                <para>Exports a report to the specified file path in XLSX format.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created XLSX file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToXps(System.IO.Stream,DevExpress.XtraPrinting.XpsExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in XPS format using the specified XPS-specific options.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created XPS file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.XpsExportOptions"/> object which specifies the XPS export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ExportToXps(System.String,DevExpress.XtraPrinting.XpsExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in XPS format using the specified XPS-specific options.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created XPS file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.XpsExportOptions"/> object which specifies the XPS export options to be applied when a report is exported.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Printing.LinkBase.Landscape">
            <summary>
                <para>Gets or sets a value indicating whether the page orientation is landscape.
</para>
            </summary>
            <value><b>true</b> if the page orientation is landscape; otherwise, <b>false</b>.

</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.LinkBase.Margins">
            <summary>
                <para>Gets or sets the margins of a report page.

</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Printing.Margins"/> object representing the margins of a report page.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.LinkBase.MinMargins">
            <summary>
                <para>Gets or sets the minimum values allowed for the <see cref="P:DevExpress.Xpf.Printing.LinkBase.Margins"/> property.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Printing.Margins"/> object representing the minimum margins.

</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.LinkBase.PaperKind">
            <summary>
                <para>Gets or sets the type of paper for the document.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Printing.PaperKind"/> enumeration value.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.Print">
            <summary>
                <para>Invokes the <b>Print</b> dialog and prints the current document.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.PrintAsync">
            <summary>
                <para>Starts asynchronous printing.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Printing.LinkBase.PrintCompleted">
            <summary>
                <para>Occurs after the printing is completed.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.PrintDirect">
            <summary>
                <para>Prints the current document to a default printer.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.PrintDirect(System.Printing.PrintQueue)">
            <summary>
                <para>Prints the current document to the printer specified by the print queue.
</para>
            </summary>
            <param name="queue">
		A <see cref="T:System.Printing.PrintQueue"/> object specifying the print queue.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.PrintDirectAsync">
            <summary>
                <para>Starts asynchronous printing using the default system printer.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.PrintDirectAsync(System.Printing.PrintQueue)">
            <summary>
                <para>Starts asynchronous printing using the default system printer with the specified printing queue.
</para>
            </summary>
            <param name="queue">
		A <see cref="T:System.Printing.PrintQueue"/> object.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Printing.LinkBase.PrintingSystem">
            <summary>
                <para>Gets the <b>Printing System</b> used to create and print a document for this link. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Printing.PrintingSystem"/> object which specifies the Printing System used to create and print a document. 
</value>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ShowPrintPreview(System.Windows.FrameworkElement)">
            <summary>
                <para>Displays the <b>Print Preview</b> of the link's document using the specified owner.


</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.FrameworkElement"/> instance specifying the owner of the <b>Print Preview</b>.

            </param>
            <returns>A FloatingContainer object representing the <b>Print Preview</b> floating container.

</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ShowPrintPreview(System.Windows.Window)">
            <summary>
                <para>Displays the <b>Print Preview</b> of the link's document using the specified owner.


</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.FrameworkElement"/> instance specifying the owner of the <b>Print Preview</b>.

            </param>
            <returns>A <see cref="T:System.Windows.Window"/> instance, representing the <b>Print Preview</b> window.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ShowPrintPreview(System.Windows.FrameworkElement,System.String)">
            <summary>
                <para>Displays the <b>Print Preview</b> of the link's document using the specified owner and title.

</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.FrameworkElement"/> instance specifying the owner of the <b>Print Preview</b>.

            </param>
            <param name="title">
		A <see cref="T:System.String"/> value specifying the title of the <b>Print Preview</b>.

            </param>
            <returns>A FloatingContainer object representing the <b>Print Preview</b> floating container.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ShowPrintPreview(System.Windows.Window,System.String)">
            <summary>
                <para>Displays the <b>Print Preview</b> of the link's document using the specified owner and title.


</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.FrameworkElement"/> instance specifying the owner of the <b>Print Preview</b>.

            </param>
            <param name="title">
		A <see cref="T:System.String"/> value specifying the title of the <b>Print Preview</b>.

            </param>
            <returns>A <see cref="T:System.Windows.Window"/> instance, representing the <b>Print Preview</b> window.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ShowPrintPreviewDialog(System.Windows.Window)">
            <summary>
                <para>Displays the modal <b>Print Preview</b> of the link's document using the specified owner.


</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.FrameworkElement"/> instance specifying the owner of the <b>Print Preview</b>.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.ShowPrintPreviewDialog(System.Windows.Window,System.String)">
            <summary>
                <para>Displays the modal <b>Print Preview</b> of the link's document using the specified owner and title.


</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.FrameworkElement"/> instance specifying the owner of the <b>Print Preview</b>.

            </param>
            <param name="title">
		A <see cref="T:System.String"/> value specifying the title of the <b>Print Preview</b>.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LinkBase.StopPageBuilding">
            <summary>
                <para>Finishes generating document pages.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Printing.LinkBase.SuppressAutoRebuildOnPageSettingsChange">
            <summary>
                <para>Gets or sets a value indicating whether it is necessary to suppress rebuilding the document every time a link's page settings are changed. 
</para>
            </summary>
            <value><b>true</b> to suppress automatic rebuilding the document; otherwise, <b>false</b>. 
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.LinkBase.VerticalContentSplitting">
            <summary>
                <para>Gets or sets a value indicating whether content bricks, which are outside the right page margin, should be split across pages, or moved in their entirety to the next page.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.VerticalContentSplitting"/> enumeration value, which specifies the way of splitting method of content bricks in the vertical direction. The default is <b>VerticalContentSplitting.Exact</b>.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Printing.ExportOptionsContainer">

            <summary>
                <para>Represents different options which are used when exporting a document.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.ExportOptionsContainer.#ctor">
            <summary>
                <para>Initializes a new instance of the ExportOptionsContainer class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Printing.ExportOptionsContainer.Xps">
            <summary>
                <para>Gets the settings used to specify export parameters when a document is exported to XPS format.



</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.XpsExportOptions"/> object which contains XPS export settings.

</value>


        </member>
        <member name="T:DevExpress.Xpf.Printing.GroupInfoCollection">

            <summary>
                <para>Represents a collection of group information objects.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.GroupInfoCollection.#ctor">
            <summary>
                <para>Initializes a new instance of the GroupInfoCollection class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Printing.GroupInfo">

            <summary>
                <para>Represents an object, which contains information about a group.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.GroupInfo.#ctor(System.Windows.DataTemplate)">
            <summary>
                <para>Initializes a new instance of the GroupInfo class with the specified header template.
</para>
            </summary>
            <param name="headerTemplate">
		A <see cref="T:System.Windows.DataTemplate"/> object, which specifies the template for a header. This value is assigned to the <see cref="P:DevExpress.Xpf.Printing.GroupInfo.HeaderTemplate"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.GroupInfo.#ctor">
            <summary>
                <para>Initializes a new instance of the GroupInfo class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Printing.GroupInfo.#ctor(System.Windows.DataTemplate,System.Windows.DataTemplate)">
            <summary>
                <para>Initializes a new instance of the GroupInfo class with the specified header and footer templates.
</para>
            </summary>
            <param name="headerTemplate">
		A <see cref="T:System.Windows.DataTemplate"/> object, specifying the header template.

            </param>
            <param name="footerTemplate">
		A <see cref="T:System.Windows.DataTemplate"/> object, specifying the footer template.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Printing.GroupInfo.FooterTemplate">
            <summary>
                <para>Specifies the group footer template for GroupInfo.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.DataTemplate"/> object, specifying the group footer template.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.GroupInfo.HeaderTemplate">
            <summary>
                <para>Gets or sets a template which defines how data is represented in the group header area.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.DataTemplate"/> object, which represents a template for the group header area.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.GroupInfo.PageBreakAfter">
            <summary>
                <para>Specifies whether or not a new page starts immediately <i>after</i> the group area.
</para>
            </summary>
            <value><b>true</b> to insert page break after the group; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.GroupInfo.PageBreakBefore">
            <summary>
                <para>Specifies whether or not the group area starts on a new page.
</para>
            </summary>
            <value><b>true</b> to insert page break gefore the group; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.GroupInfo.RepeatHeaderEveryPage">
            <summary>
                <para>Specifies whether or not the group header is added to each document page where the corresponding group is printed.


</para>
            </summary>
            <value><b>true</b> to show the group header on each page; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.GroupInfo.Union">
            <summary>
                <para>Specifies how groups should be split across pages, if required.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.DataNodes.GroupUnion"/> enumeration value.

</value>


        </member>
        <member name="T:DevExpress.Xpf.Printing.CreateAreaEventArgs">

            <summary>
                <para>Provides data for all area creation events.

 
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.CreateAreaEventArgs.#ctor(System.Int32)">
            <summary>
                <para>Initializes a new instance of the CreateAreaEventArgs class with the specified detail index.

</para>
            </summary>
            <param name="detailIndex">
		An integer value which specifies the detail index for the event. This value is assigned to the <see cref="P:DevExpress.Xpf.Printing.CreateAreaEventArgs.DetailIndex"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Printing.CreateAreaEventArgs.Data">
            <summary>
                <para>Gets or sets the object, which represents data for the area creation event.

</para>
            </summary>
            <value>A <see cref="T:System.Object"/>, which represents event data.

</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.CreateAreaEventArgs.DetailIndex">
            <summary>
                <para>Gets or sets the index of a detail row for which the area creation event was called.

</para>
            </summary>
            <value>An integer value, which represents the index of a detail row.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Printing.CollectionViewLink">

            <summary>
                <para>A link to print objects which implement the <see cref="T:System.ComponentModel.ICollectionView"/> interface.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.CollectionViewLink.#ctor">
            <summary>
                <para>Initializes a new instance of the CollectionViewLink class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Printing.CollectionViewLink.CollectionView">
            <summary>
                <para>Gets or sets an object, which should be printed by the CollectionViewLink.
</para>
            </summary>
            <value>An object implementing the <see cref="T:System.ComponentModel.ICollectionView"/> interface.

</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.CollectionViewLink.DetailTemplate">
            <summary>
                <para>Specifies the template for the document's <i>detail</i> area.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.DataTemplate"/> object. 
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.CollectionViewLink.GroupInfos">
            <summary>
                <para>Provides access to a collection of objects, which store information about grouping.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Printing.GroupInfoCollection"/> instance, representing a collection of <see cref="T:DevExpress.Xpf.Printing.GroupInfo"/> objects.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Printing.SimpleLink">

            <summary>
                <para>A link to print linear data.



</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.SimpleLink.#ctor(System.Windows.DataTemplate,System.Int32)">
            <summary>
                <para>Initializes a new instance of the SimpleLink class with the specified detail template and detail count.
</para>
            </summary>
            <param name="detail">
		A <see cref="T:System.Windows.DataTemplate"/> object, which specifies the template for a detail area. This template is assigned to the <see cref="P:DevExpress.Xpf.Printing.SimpleLink.DetailTemplate"/> property.

            </param>
            <param name="detailCount">
		An integer value, which specifies the count of detail records. This value is assigned to the <see cref="P:DevExpress.Xpf.Printing.SimpleLink.DetailCount"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.SimpleLink.#ctor">
            <summary>
                <para>Initializes a new instance of the SimpleLink class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Printing.SimpleLink.#ctor(System.String)">
            <summary>
                <para>Initializes a new instance of the SimpleLink class with the specified document name.
</para>
            </summary>
            <param name="documentName">
		A <see cref="T:System.String"/> value that specifies a document name. This value is assigned to the <see cref="P:DevExpress.Xpf.Printing.LinkBase.DocumentName"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.SimpleLink.#ctor(System.Windows.DataTemplate,System.Int32,System.String)">
            <summary>
                <para>Initializes a new instance of the SimpleLink class with the specified document name.
</para>
            </summary>
            <param name="detailTemplate">
		A <see cref="T:System.Windows.DataTemplate"/> object, which specifies the template for a detail area. This template is assigned to the <see cref="P:DevExpress.Xpf.Printing.SimpleLink.DetailTemplate"/> property.

            </param>
            <param name="detailCount">
		An integer value, which specifies the count of detail records. This value is assigned to the <see cref="P:DevExpress.Xpf.Printing.SimpleLink.DetailCount"/> property.

            </param>
            <param name="documentName">
		A <see cref="T:System.String"/> value that specifies a document name. This value is assigned to the <see cref="P:DevExpress.Xpf.Printing.LinkBase.DocumentName"/> property.

            </param>


        </member>
        <member name="E:DevExpress.Xpf.Printing.SimpleLink.CreateDetail">
            <summary>
                <para>Occurs every time the detail area of a SimpleLink is created.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Printing.SimpleLink.DetailCount">
            <summary>
                <para>Gets or sets the number of times the <see cref="E:DevExpress.Xpf.Printing.SimpleLink.CreateDetail"/> event is raised to create the detail area.


</para>
            </summary>
            <value>An integer value specifying how many times the detail area should be generated.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.SimpleLink.DetailTemplate">
            <summary>
                <para>Specifies the template for the document's <i>detail</i> area.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.DataTemplate"/> object.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Printing.PrintingSystem">

            <summary>
                <para>Implements the basic functionality of the <b>DXPrinting Library</b>.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintingSystem.#ctor">
            <summary>
                <para>Initializes a new instance of the PrintingSystem class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PrintingSystem.ExportOptions">
            <summary>
                <para>Gets the settings used to specify export parameters when exporting a printing system's document. 

</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Printing.ExportOptionsContainer"/> object which contains the export settings for a printing system's document. 
</value>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintingSystem.ExportToXps(System.IO.Stream,DevExpress.XtraPrinting.XpsExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in XPS format using the specified XPS-specific options.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created XPS file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.XpsExportOptions"/> object which specifies the XPS export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintingSystem.ExportToXps(System.String,DevExpress.XtraPrinting.XpsExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in XPS format using the specified XPS-specific options.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created XPS file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.XpsExportOptions"/> object which specifies the XPS export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintingSystem.ExportToXps(System.String)">
            <summary>
                <para>Exports a report to the specified file path in XPS format.
</para>
            </summary>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created XPS file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintingSystem.GetCommandVisibility(DevExpress.XtraPrinting.PrintingSystemCommand)">
            <summary>
                <para>Gets the current visibility of the specified printing system command. 
</para>
            </summary>
            <param name="command">
		A <see cref="T:DevExpress.XtraPrinting.PrintingSystemCommand"/> object specifying the command whose visibility state is to be determined. 

            </param>
            <returns>A <see cref="T:DevExpress.XtraPrinting.CommandVisibility"/> enumeration value representing the command's visibility. 
</returns>


        </member>
        <member name="T:DevExpress.Xpf.Printing.TemplatedLink">

            <summary>
                <para>The base class for links that provide printing functionality for WPF controls.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Printing.TemplatedLink.BottomMarginData">
            <summary>
                <para>Specifies the content for the document's <i>bottom margin</i>. This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:System.Object"/> value.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Printing.TemplatedLink.BottomMarginDataProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Printing.TemplatedLink.BottomMarginTemplate">
            <summary>
                <para>Specifies the <i>bottom margin</i> template for the document.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.DataTemplate"/> object. 
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.TemplatedLink.ColumnLayout">
            <summary>
                <para>Gets or sets the column layout.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraPrinting.ColumnLayout"/> enumeration value. 
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.TemplatedLink.ColumnWidth">
            <summary>
                <para>Gets or sets the width of a single column.
</para>
            </summary>
            <value>A <see cref="T:System.Single"/> value. 
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.TemplatedLink.PageFooterData">
            <summary>
                <para>Specifies the content for the document's <i>page footer</i>. This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:System.Object"/> value. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Printing.TemplatedLink.PageFooterDataProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Printing.TemplatedLink.PageFooterTemplate">
            <summary>
                <para>Specifies the <i>page footer</i> template for the document.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.DataTemplate"/> object.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.TemplatedLink.PageHeaderData">
            <summary>
                <para>Specifies the content for the document's <i>page header</i>. This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:System.Object"/> value. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Printing.TemplatedLink.PageHeaderDataProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Printing.TemplatedLink.PageHeaderTemplate">
            <summary>
                <para>Specifies the <i>page header</i> template for the document.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.DataTemplate"/> object. 
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.TemplatedLink.PrintReportFooterAtBottom">
            <summary>
                <para>Specifies whether the report footer is printed at the bottom of the page, or immediately after the report content.
</para>
            </summary>
            <value><b>true</b> to always print the report footer at the bottom of the document last page; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.TemplatedLink.ReportFooterData">
            <summary>
                <para>Specifies the content for the document's <i>report footer</i>. This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:System.Object"/> value. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Printing.TemplatedLink.ReportFooterDataProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Printing.TemplatedLink.ReportFooterTemplate">
            <summary>
                <para>Specifies the <i>report footer</i> template for the document.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.DataTemplate"/> object. 
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.TemplatedLink.ReportHeaderData">
            <summary>
                <para>Specifies the content for the document's <i>report header</i>. This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:System.Object"/> value. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Printing.TemplatedLink.ReportHeaderDataProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Printing.TemplatedLink.ReportHeaderTemplate">
            <summary>
                <para>Specifies the <i>report header</i> template for the document.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.DataTemplate"/> object. 
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.TemplatedLink.TopMarginData">
            <summary>
                <para>Specifies the content for the document's <i>top margin</i>. This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:System.Object"/> value. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Printing.TemplatedLink.TopMarginDataProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Printing.TemplatedLink.TopMarginTemplate">
            <summary>
                <para>Specifies the <i>top margin</i> template for the document.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.DataTemplate"/> object. 
</value>


        </member>
        <member name="T:DevExpress.Xpf.Printing.LegacyPrintableComponentLink">

            <summary>
                <para>A link to print Windows Forms controls that implement the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.LegacyPrintableComponentLink.#ctor(DevExpress.XtraPrinting.IPrintable)">
            <summary>
                <para>Initializes a new instance of the LegacyPrintableComponentLink class with the specified settings.
</para>
            </summary>
            <param name="printableComponent">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LegacyPrintableComponentLink.#ctor(DevExpress.XtraPrinting.IPrintable,System.String)">
            <summary>
                <para>Initializes a new instance of the LegacyPrintableComponentLink class with the specified settings.
</para>
            </summary>
            <param name="printableComponent">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface.

            </param>
            <param name="documentName">
		A <see cref="T:System.String"/> value, specifying the document name.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Printing.LegacyPrintableComponentLink.PrintableComponent">
            <summary>
                <para>Gets the component that is to be printed via LegacyPrintableComponentLink.
</para>
            </summary>
            <value>An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Printing.CustomCursor">

            <summary>
                <para>A custom mouse cursor, corresponding to a specific action in a Silverlight Report Designer.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.CustomCursor.#ctor">
            <summary>
                <para>Initializes a new instance of the CustomCursor class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Printing.CustomCursor.HotSpot">
            <summary>
                <para>Gets the cursor hot spot.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Point"/> structure.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.CustomCursor.ImageSource">
            <summary>
                <para>Gets the cursor image source.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Media.ImageSource"/> object.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Printing.LegacyLinkPreviewModel">

            <summary>
                <para>Provides the Preview Model functionality for printing Windows Forms controls in WPF applications.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.LegacyLinkPreviewModel.#ctor(DevExpress.XtraPrinting.ILink)">
            <summary>
                <para>Initializes a new instance of the LegacyLinkPreviewModel class with the specified link.
</para>
            </summary>
            <param name="link">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.ILink"/> interface.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.LegacyLinkPreviewModel.#ctor">
            <summary>
                <para>Initializes a new instance of the LegacyLinkPreviewModel class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Printing.LegacyLinkPreviewModel.IsEmptyDocument">
            <summary>
                <para>Gets the value specifying whether or not the document has any pages.
</para>
            </summary>
            <value><b>true</b> if the document is empty; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.LegacyLinkPreviewModel.Link">
            <summary>
                <para>Specifies the link that is associated with this LegacyLinkPreviewModel.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Printing.LinkBase"/> descendant.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.LegacyLinkPreviewModel.ParametersPanelContent">
            <summary>
                <para>Provides access to the elements of the <b>Parameters</b> panel.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.FrameworkElement"/> object that stores parameter editors available in the panel.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Printing.ICursorService">

            <summary>
                <para>Implements a service that is used to display different mouse cursors on the client.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.ICursorService.BlockService(System.String)">
            <summary>
                <para>For internal use.
</para>
            </summary>
            <param name="id">
		A <see cref="T:System.String"/> value.

            </param>
            <returns>A Boolean value.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.ICursorService.HideCustomCursor">
            <summary>
                <para>Hides a custom cursor.
</para>
            </summary>
            <returns><b>true</b> to hide the custom cursor; otherwise <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.ICursorService.HideCustomCursor(System.String)">
            <summary>
                <para>For internal use. Hides a custom cursor.
</para>
            </summary>
            <param name="id">
		A <see cref="T:System.String"/> value, identifying the cursor.

            </param>
            <returns><b>true</b> to hide the custom cursor; otherwise <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.ICursorService.SetCursor(System.Windows.FrameworkElement,DevExpress.Xpf.Printing.CustomCursor)">
            <summary>
                <para>Specifies a custom cursor for an element.
</para>
            </summary>
            <param name="control">
		A <see cref="T:System.Windows.FrameworkElement"/> object.

            </param>
            <param name="customCursor">
		A <see cref="T:DevExpress.Xpf.Printing.CustomCursor"/> object.

            </param>
            <returns><b>true</b> to set the cursor; otherwise <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.ICursorService.SetCursor(System.Windows.FrameworkElement,DevExpress.Xpf.Printing.CustomCursor,System.String)">
            <summary>
                <para>For internal use. Specifies a custom cursor for an element.
</para>
            </summary>
            <param name="control">
		A <see cref="T:System.Windows.FrameworkElement"/> object.

            </param>
            <param name="customCursor">
		A <see cref="T:DevExpress.Xpf.Printing.CustomCursor"/> object.

            </param>
            <param name="id">
		A <see cref="T:System.String"/> value, identifying the cursor.

            </param>
            <returns><b>true</b> to set the cursor; otherwise <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.ICursorService.SetCursor(System.Windows.FrameworkElement,System.Windows.Input.Cursor)">
            <summary>
                <para>Specifies a standard cursor for an element.
</para>
            </summary>
            <param name="control">
		A <see cref="T:System.Windows.FrameworkElement"/> object.

            </param>
            <param name="cursor">
		A <see cref="T:System.Windows.Input.Cursor"/> object.

            </param>
            <returns><b>true</b> to set the cursor; otherwise <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.ICursorService.SetCursor(System.Windows.FrameworkElement,System.Windows.Input.Cursor,System.String)">
            <summary>
                <para>For internal use. Specifies a standard cursor for an element.
</para>
            </summary>
            <param name="control">
		A <see cref="T:System.Windows.FrameworkElement"/> object.

            </param>
            <param name="cursor">
		A <see cref="T:System.Windows.Input.Cursor"/> object.

            </param>
            <param name="id">
		A <see cref="T:System.String"/> value, identifying the cursor.

            </param>
            <returns><b>true</b> to set the cursor; otherwise <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.ICursorService.SetCursorPosition(System.Windows.Point,System.Windows.FrameworkElement,System.String)">
            <summary>
                <para>For internal use. Specifies a cursor's position.
</para>
            </summary>
            <param name="relativePosition">
		A <see cref="T:System.Windows.Point"/> structure.

            </param>
            <param name="relativeTo">
		A <see cref="T:System.Windows.FrameworkElement"/> object.

            </param>
            <param name="id">
		A <see cref="T:System.String"/> value, identifying the cursor.

            </param>
            <returns><b>true</b> to set the cursor position; otherwise <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.ICursorService.SetCursorPosition(System.Windows.Point,System.Windows.FrameworkElement)">
            <summary>
                <para>Specifies a cursor's position.
</para>
            </summary>
            <param name="relativePosition">
		A <see cref="T:System.Windows.Point"/> structure.

            </param>
            <param name="relativeTo">
		A <see cref="T:System.Windows.FrameworkElement"/> object.

            </param>
            <returns><b>true</b> to set the cursor position; otherwise <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.ICursorService.SetSuppressCursorChanging(System.Boolean)">
            <summary>
                <para>For internal use.
</para>
            </summary>
            <param name="value">
		A Boolean value.

            </param>
            <returns>A Boolean value.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.ICursorService.UnblockService(System.String)">
            <summary>
                <para>For internal use.
</para>
            </summary>
            <param name="id">
		A <see cref="T:System.String"/> value.

            </param>
            <returns>A Boolean value.
</returns>


        </member>
        <member name="T:DevExpress.Xpf.Printing.InputController">

            <summary>
                <para>Handles keyboard and mouse events on the client.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.InputController.#ctor">
            <summary>
                <para>Initializes a new instance of the InputController class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Printing.InputController.AreModifiersPressed">
            <summary>
                <para>Gets a value indicating whether or not key modifies (CTRL, SHIFT, ALT, WIN) are pressed.
</para>
            </summary>
            <value><b>true</b> if the modifiers are pressed; otherwise <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Printing.InputController.HandleKeyDown(System.Windows.Input.Key)">
            <summary>
                <para>Handles a keyboard command.
</para>
            </summary>
            <param name="key">
		A <see cref="T:System.Windows.Input.Key"/> enumeration value.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.InputController.HandleMouseDown(System.Windows.Input.MouseButton)">
            <summary>
                <para>Handles a mouse button command.
</para>
            </summary>
            <param name="mouseButton">
		A <see cref="T:System.Windows.Input.MouseButton"/> enumeration value.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.InputController.HandleMouseWheel(System.Int32)">
            <summary>
                <para>Handles a mouse wheel command.
</para>
            </summary>
            <param name="delta">
		An integer value.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Printing.InputController.Model">
            <summary>
                <para>Specifies the preview model associated with the InputController.
</para>
            </summary>
            <value>An object implementing the <see cref="T:DevExpress.Xpf.Printing.IPreviewModel"/> interface.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Printing.PreviewModelBase">

            <summary>
                <para>The base class for classes that provide the Preview Model functionality.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.PreviewModelBase.#ctor">
            <summary>
                <para>Initializes a new instance of the PreviewModelBase class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PreviewModelBase.CursorService">
            <summary>
                <para>Specifies the service that is used to customize the mouse pointer settings on the client.
</para>
            </summary>
            <value>An object implementing the <see cref="T:DevExpress.Xpf.Printing.ICursorService"/> interface.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PreviewModelBase.DialogService">
            <summary>
                <para>Specifies the service that is used to invoke dialog windows on the client.
An object implementing the <see cref="T:DevExpress.Xpf.Printing.IDialogService"/> interface.
</para>
            </summary>
            <value>An object implementing the <see cref="T:DevExpress.Xpf.Printing.IDialogService"/> interface.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PreviewModelBase.HandlePreviewDoubleClick(System.Windows.Input.MouseEventArgs,System.Windows.FrameworkElement)">
            <summary>
                <para>For internal use.
</para>
            </summary>
            <param name="e">
		A <see cref="T:System.Windows.Input.MouseEventArgs"/> object.

            </param>
            <param name="source">
		A <see cref="T:System.Windows.FrameworkElement"/> object.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PreviewModelBase.HandlePreviewMouseLeftButtonDown(System.Windows.Input.MouseButtonEventArgs,System.Windows.FrameworkElement)">
            <summary>
                <para>For internal use.
</para>
            </summary>
            <param name="e">
		A <see cref="T:System.Windows.Input.MouseButtonEventArgs"/> object.

            </param>
            <param name="source">
		A <see cref="T:System.Windows.FrameworkElement"/> object.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PreviewModelBase.HandlePreviewMouseLeftButtonUp(System.Windows.Input.MouseButtonEventArgs,System.Windows.FrameworkElement)">
            <summary>
                <para>For internal use.
</para>
            </summary>
            <param name="e">
		A <see cref="T:System.Windows.Input.MouseButtonEventArgs"/> object.

            </param>
            <param name="source">
		A <see cref="T:System.Windows.FrameworkElement"/> object.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PreviewModelBase.HandlePreviewMouseMove(System.Windows.Input.MouseEventArgs,System.Windows.FrameworkElement)">
            <summary>
                <para>For internal use.
</para>
            </summary>
            <param name="e">
		A <see cref="T:System.Windows.Input.MouseEventArgs"/> object.

            </param>
            <param name="source">
		A <see cref="T:System.Windows.FrameworkElement"/> object.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PreviewModelBase.InputController">
            <summary>
                <para>Provides access to an object that stores keyboard and mouse shortcuts corresponding to appropriate Report Designer commands.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Printing.InputController"/> object.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PreviewModelBase.IsCreating">
            <summary>
                <para>Specifies whether or not a document is still being created.
</para>
            </summary>
            <value>A Boolean value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PreviewModelBase.IsIncorrectPageContent">
            <summary>
                <para>Returns a value specifying whether or not the document page content is rendered correctly.
</para>
            </summary>
            <value><b>true</b> if the document page content is incorrect; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PreviewModelBase.IsLoading">
            <summary>
                <para>Specifies whether or not a document is still being loaded.
</para>
            </summary>
            <value>A Boolean value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PreviewModelBase.PageContent">
            <summary>
                <para>Provides access to the content of report pages.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.FrameworkElement"/> object.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PreviewModelBase.PageCount">
            <summary>
                <para>Gets a value indicating the total number of pages in a report document.
</para>
            </summary>
            <value>An integer value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PreviewModelBase.PageViewHeight">
            <summary>
                <para>Gets a value indicating the height of a page as it appears on-screen.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PreviewModelBase.PageViewWidth">
            <summary>
                <para>Gets a value indicating the width of a page as it appears on-screen.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value.
</value>


        </member>
        <member name="E:DevExpress.Xpf.Printing.PreviewModelBase.PropertyChanged">
            <summary>
                <para>Occurs every time any of the PreviewModelBase class properties has changed its value.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PreviewModelBase.SetZoom(System.Double)">
            <summary>
                <para>Applies the specified zoom factor value to a document.
</para>
            </summary>
            <param name="value">
		A <see cref="T:System.Double"/> value, specifying the document zoom factor.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PreviewModelBase.UseSimpleScrolling">
            <summary>
                <para>Specifies whether or not a simple scrolling mode should be used for the document.
</para>
            </summary>
            <value><b>true</b> to use simple scrolling; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PreviewModelBase.Zoom">
            <summary>
                <para>Specifies the zoom factor of a report document.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value, specifying the document zoom factor.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PreviewModelBase.ZoomDisplayFormat">
            <summary>
                <para>Specifies the display format of available zoom factor values.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PreviewModelBase.ZoomDisplayText">
            <summary>
                <para>Specifies the display text for a zoom factor value.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PreviewModelBase.ZoomInCommand">
            <summary>
                <para>Increases the document zoom factor.
</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, defining the command.

</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PreviewModelBase.ZoomMode">
            <summary>
                <para>Specifies the zooming mode that is applied to a report document.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Printing.ZoomItemBase"/> descendant.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PreviewModelBase.ZoomModes">
            <summary>
                <para>Provides access to a collection of zooming modes available for a report document.
</para>
            </summary>
            <value>A collection of <see cref="T:DevExpress.Xpf.Printing.ZoomItemBase"/> descendants.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PreviewModelBase.ZoomOutCommand">
            <summary>
                <para>Decreases the document zoom factor.
</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, defining the command.

</value>


        </member>
        <member name="T:DevExpress.Xpf.Printing.DialogService">

            <summary>
                <para>A service that is used to show dialogs on the client.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.DialogService.#ctor(System.Windows.FrameworkElement)">
            <summary>
                <para>Initializes a new instance of the DialogService class with the specified view.
</para>
            </summary>
            <param name="view">
		A <see cref="T:System.Windows.FrameworkElement"/> object.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.DialogService.GetParentWindow">
            <summary>
                <para>Returns the dialog's parent window.
</para>
            </summary>
            <returns>A <see cref="T:System.Windows.Window"/> object.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.DialogService.ShowError(System.String,System.String)">
            <summary>
                <para>Shows the error dialog with the specified text and caption.




</para>
            </summary>
            <param name="text">
		A <see cref="T:System.String"/> value, specifying the dialog's text.

            </param>
            <param name="caption">
		A <see cref="T:System.String"/> value, specifying the dialog's caption.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.DialogService.ShowInformation(System.String,System.String)">
            <summary>
                <para>Shows the information dialog with the specified text and caption.




</para>
            </summary>
            <param name="text">
		A <see cref="T:System.String"/> value, specifying the dialog's text.

            </param>
            <param name="caption">
		A <see cref="T:System.String"/> value, specifying the dialog's caption.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.DialogService.ShowOpenFileDialog(System.String,System.String,System.String@)">
            <summary>
                <para>Shows the <b>Open</b> dialog with the specified settings.
</para>
            </summary>
            <param name="caption">
		A <see cref="T:System.String"/> value, specifying the dialog's caption.

            </param>
            <param name="filter">
		A <see cref="T:System.String"/> value, specifying the available file type extensions.

            </param>
            <param name="fileName">
		A <see cref="T:System.String"/> value, specifying the file name.

            </param>
            <returns>A <see cref="T:System.IO.Stream"/> object, specifying where the dialog is transmitted.

</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.DialogService.ShowOpenFileDialog(System.String,System.String)">
            <summary>
                <para>Shows the <b>Open</b> dialog with the specified settings.
</para>
            </summary>
            <param name="caption">
		A <see cref="T:System.String"/> value, specifying the dialog's caption.

            </param>
            <param name="filter">
		A <see cref="T:System.String"/> value, specifying the available file type extensions.

            </param>
            <returns>A <see cref="T:System.IO.Stream"/> object, specifying where the dialog is transmitted.

</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.DialogService.ShowSaveFileDialog(System.String,System.String,System.Int32,System.String,System.String)">
            <summary>
                <para>Shows the <b>Save File</b> dialog with the specified parameters.
</para>
            </summary>
            <param name="caption">
		A <see cref="T:System.String"/> value, specifying the dialog's caption.

            </param>
            <param name="filter">
		A <see cref="T:System.String"/> value, specifying the available file type extensions.

            </param>
            <param name="filterIndex">
		An integer value, specifying the file type extension that is selected by default.

            </param>
            <param name="initialDirectory">
		A <see cref="T:System.String"/> value, specifying the path to the folder opened by default.

            </param>
            <param name="fileName">
		A <see cref="T:System.String"/> value, specifying the file name.

            </param>
            <returns>A <see cref="T:System.IO.Stream"/> object, specifying where the dialog is transmitted.

</returns>


        </member>
        <member name="T:DevExpress.Xpf.Printing.IWatermarkService">

            <summary>
                <para>Implements a service that is used to customize a document's watermarks.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.IWatermarkService.Edit(System.Windows.Window,DevExpress.XtraPrinting.Page,System.Int32,DevExpress.Xpf.Printing.Native.XpfWatermark)">
            <summary>
                <para>For internal use. 
</para>
            </summary>
            <param name="ownerWindow">
		@nbsp;

            </param>
            <param name="currentPage">
		@nbsp;

            </param>
            <param name="pagesCount">
		@nbsp;

            </param>
            <param name="currentWatermark">
		@nbsp;

            </param>


        </member>
        <member name="E:DevExpress.Xpf.Printing.IWatermarkService.EditCompleted">
            <summary>
                <para>For internal use. Occurs after the customization of a document's watermark has completed.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Printing.IPreviewModel">

            <summary>
                <para>Provides the basic functionality for a document's Preview Model.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Printing.IPreviewModel.CursorService">
            <summary>
                <para>Specifies the service that is used to customize the mouse pointer settings on the client.
</para>
            </summary>
            <value>An object implementing the <see cref="T:DevExpress.Xpf.Printing.ICursorService"/> interface.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IPreviewModel.DialogService">
            <summary>
                <para>Specifies the service that is used to invoke dialog windows on the client.
</para>
            </summary>
            <value>An object implementing the <see cref="T:DevExpress.Xpf.Printing.IDialogService"/> interface.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Printing.IPreviewModel.HandlePreviewDoubleClick(System.Windows.Input.MouseEventArgs,System.Windows.FrameworkElement)">
            <summary>
                <para>For internal use.
</para>
            </summary>
            <param name="e">
		A <see cref="T:System.Windows.Input.MouseEventArgs"/> object.

            </param>
            <param name="source">
		A <see cref="T:System.Windows.FrameworkElement"/> object.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.IPreviewModel.HandlePreviewMouseLeftButtonDown(System.Windows.Input.MouseButtonEventArgs,System.Windows.FrameworkElement)">
            <summary>
                <para>For internal use.
</para>
            </summary>
            <param name="e">
		A <see cref="T:System.Windows.Input.MouseButtonEventArgs"/> object.

            </param>
            <param name="source">
		A <see cref="T:System.Windows.FrameworkElement"/> object.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.IPreviewModel.HandlePreviewMouseLeftButtonUp(System.Windows.Input.MouseButtonEventArgs,System.Windows.FrameworkElement)">
            <summary>
                <para>For internal use.
</para>
            </summary>
            <param name="e">
		A <see cref="T:System.Windows.Input.MouseButtonEventArgs"/> object.

            </param>
            <param name="source">
		A <see cref="T:System.Windows.FrameworkElement"/> object.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.IPreviewModel.HandlePreviewMouseMove(System.Windows.Input.MouseEventArgs,System.Windows.FrameworkElement)">
            <summary>
                <para>For internal use.
</para>
            </summary>
            <param name="e">
		A <see cref="T:System.Windows.Input.MouseEventArgs"/> object.

            </param>
            <param name="source">
		A <see cref="T:System.Windows.FrameworkElement"/> object.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IPreviewModel.InputController">
            <summary>
                <para>Provides access to the input controller associated with the IPreviewModel.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Printing.InputController"/> object.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IPreviewModel.IsCreating">
            <summary>
                <para>Specifies whether or not a document is still being created.
</para>
            </summary>
            <value>A Boolean value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IPreviewModel.IsIncorrectPageContent">
            <summary>
                <para>Returns a value specifying whether or not the document page content is rendered correctly.
</para>
            </summary>
            <value><b>true</b> if the document page content is incorrect; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IPreviewModel.IsLoading">
            <summary>
                <para>Specifies whether or not a document is still being loaded.
</para>
            </summary>
            <value>A Boolean value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IPreviewModel.PageContent">
            <summary>
                <para>Provides access to the content of report pages.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.FrameworkElement"/> object.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IPreviewModel.PageCount">
            <summary>
                <para>Gets a value indicating the total number of pages in a report document.
</para>
            </summary>
            <value>An integer value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IPreviewModel.PageViewHeight">
            <summary>
                <para>Gets a value indicating the height of a page as it appears on-screen.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IPreviewModel.PageViewWidth">
            <summary>
                <para>Gets a value indicating the width of a page as it appears on-screen.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Printing.IPreviewModel.SetZoom(System.Double)">
            <summary>
                <para>Applies the specified zoom factor value to a document.
</para>
            </summary>
            <param name="value">
		A <see cref="T:System.Double"/> value, specifying the document zoom factor.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IPreviewModel.UseSimpleScrolling">
            <summary>
                <para>Specifies whether or not a simple scrolling mode should be used for the document.
</para>
            </summary>
            <value><b>true</b> to use simple scrolling; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IPreviewModel.Zoom">
            <summary>
                <para>Specifies the zoom factor of a report document.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value, specifying the document zoom factor.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IPreviewModel.ZoomDisplayFormat">
            <summary>
                <para>Specifies the display format of available zoom factor values.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IPreviewModel.ZoomDisplayText">
            <summary>
                <para>Specifies the display text for a zoom factor value.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IPreviewModel.ZoomInCommand">
            <summary>
                <para>Increases the document zoom factor.
</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, defining the command.

</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IPreviewModel.ZoomMode">
            <summary>
                <para>Specifies the zooming mode that is applied to a report document.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Printing.ZoomItemBase"/> descendant.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IPreviewModel.ZoomModes">
            <summary>
                <para>Provides access to a collection of zooming modes available for a report document.
</para>
            </summary>
            <value>A collection of <see cref="T:DevExpress.Xpf.Printing.ZoomItemBase"/> descendants.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.IPreviewModel.ZoomOutCommand">
            <summary>
                <para>Decreases the document zoom factor.
</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, defining the command.

</value>


        </member>
        <member name="T:DevExpress.Xpf.Printing.PrintingResXLocalizer">

            <summary>
                <para>The default localizer to translate the DXPrinting's resources.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintingResXLocalizer.#ctor">
            <summary>
                <para>Initializes a new instance of the PrintingResXLocalizer class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Printing.ZoomFitModeItem">

            <summary>
                <para>An individual zoom fit mode.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.ZoomFitModeItem.#ctor(DevExpress.Xpf.Printing.ZoomFitMode)">
            <summary>
                <para>Initializes a new instance of the ZoomFitModeItem class with the specified zoom fit mode.
</para>
            </summary>
            <param name="zoomFitMode">
		A <see cref="T:DevExpress.Xpf.Printing.ZoomFitMode"/> enumeration value.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Printing.ZoomFitModeItem.DisplayedText">
            <summary>
                <para>Gets the text that is displayed in Document Preview for ZoomFitModeItem.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.ZoomFitModeItem.ZoomFitMode">
            <summary>
                <para>Gets the zoom fit mode of ZoomFitModeItem.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Printing.ZoomFitMode"/> enumeration value.

</value>


        </member>
        <member name="T:DevExpress.Xpf.Printing.ZoomFitMode">

            <summary>
                <para>Lists the available zoom fit modes.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Printing.ZoomFitMode.PageHeight">
            <summary>
                <para>Zoom the document to fit the entire page height.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.ZoomFitMode.PageWidth">
            <summary>
                <para>Zoom the document to fit the entire page width.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Printing.ZoomFitMode.WholePage">
            <summary>
                <para>Zoom the document to fit the entire page.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Printing.PrintableControlLink">

            <summary>
                <para>A link to print DevExpress controls and the base class for links to print other data.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintableControlLink.#ctor(DevExpress.Xpf.Printing.IPrintableControl,System.String)">
            <summary>
                <para>Initializes a new instance of the PrintableControlLink class with the specified control and document name. 
</para>
            </summary>
            <param name="printableControl">
		An object implementing the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface that is the control to be printed using the PrintableControlLink.

            </param>
            <param name="documentName">
		A <see cref="T:System.String"/> value, specifying the document name.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintableControlLink.#ctor(DevExpress.Xpf.Printing.IPrintableControl)">
            <summary>
                <para>Initializes a new instance of the PrintableControlLink class with the specified control. 
</para>
            </summary>
            <param name="printableControl">
		An object implementing the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface that is the control to be printed using the PrintableControlLink.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Printing.PreviewClickEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Printing.IDocumentPreviewModel.PreviewClick"/>, <see cref="E:DevExpress.Xpf.Printing.IDocumentPreviewModel.PreviewMouseMove"/> and <see cref="E:DevExpress.Xpf.Printing.IDocumentPreviewModel.PreviewMouseMove"/> events.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.PreviewClickEventArgs.#ctor(System.String,System.Windows.FrameworkElement)">
            <summary>
                <para>Initializes a new instance of the PreviewClickEventArgs class with the specified element and tag property value.
</para>
            </summary>
            <param name="elementTag">
		A <see cref="T:System.String"/> value. This value is assigner to the <see cref="P:DevExpress.Xpf.Printing.PreviewClickEventArgs.ElementTag"/> property.

            </param>
            <param name="element">
		A <see cref="T:System.Windows.FrameworkElement"/> object. This value is assigner to the <see cref="P:DevExpress.Xpf.Printing.PreviewClickEventArgs.Element"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PreviewClickEventArgs.Element">
            <summary>
                <para>Gets the element for which the corresponding event has been raised. 

</para>
            </summary>
            <value>A <see cref="T:System.Windows.FrameworkElement"/> object.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PreviewClickEventArgs.ElementTag">
            <summary>
                <para>Gets the <b>Tag</b> property value of the element, for which the corresponding event has been raised.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Printing.BarManagerCustomization">

            <summary>
                <para>Provides functionality for customization of the Document Preview toolbar.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.BarManagerCustomization.GetTemplate(System.Windows.DependencyObject)">
            <summary>
                <para>Gets the value of the <see cref="P:DevExpress.Xpf.Printing.BarManagerCustomization.Template"/> attached property from a given object.



</para>
            </summary>
            <param name="obj">
		An object whose Template property's value must be returned.

            </param>
            <returns>The value of the Template attached property for the specified object.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.BarManagerCustomization.SetTemplate(System.Windows.DependencyObject,System.Windows.DataTemplate)">
            <summary>
                <para>Sets the value of the <see cref="P:DevExpress.Xpf.Printing.BarManagerCustomization.Template"/> attached property for a given object.
</para>
            </summary>
            <param name="obj">
		An object for which the Template attached property is set.

            </param>
            <param name="value">
		The value for the Template attached property.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Printing.BarManagerCustomization.Template(System.Windows.DependencyObject)">
            <summary>
                <para>Gets or sets the <see cref="T:DevExpress.Xpf.Bars.BarManager"/>'s customization template. This is an attached property.

</para>
            </summary>
            <value>A <see cref="T:System.Windows.DataTemplate"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Printing.BarManagerCustomization.TemplateProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Printing.PrintHelper">

            <summary>
                <para>Contains methods to print and export links, grids and reports.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToCsv(DevExpress.Xpf.Printing.IPrintableControl,System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream in CSV format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to CSV.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created CSV file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToCsv(DevExpress.Xpf.Printing.IPrintableControl,System.String,DevExpress.XtraPrinting.CsvExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in CSV format using the specified CSV-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to CSV.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created CSV file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.CsvExportOptions"/> object which specifies the CSV export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToCsv(DevExpress.Xpf.Printing.IPrintableControl,System.String)">
            <summary>
                <para>Exports a report to the specified file path in CSV format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to CSV.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created CSV file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToCsv(DevExpress.Xpf.Printing.IPrintableControl,System.IO.Stream,DevExpress.XtraPrinting.CsvExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in CSV format using the specified CSV-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to CSV.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created CSV file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.CsvExportOptions"/> object which specifies the CSV export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToCsv(DevExpress.XtraPrinting.IPrintable,System.String)">
            <summary>
                <para>Exports a report to the specified file in CSV format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to CSV.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created CSV file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToCsv(DevExpress.XtraPrinting.IPrintable,System.IO.Stream,DevExpress.XtraPrinting.CsvExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in CSV format using the specified CSV-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to CSV.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created CSV file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.CsvExportOptions"/> object which specifies the CSV export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToCsv(DevExpress.XtraPrinting.IPrintable,System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream in CSV format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to CSV.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created CSV file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToCsv(DevExpress.XtraPrinting.IPrintable,System.String,DevExpress.XtraPrinting.CsvExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in CSV format using the specified CSV-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to CSV.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created CSV file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.CsvExportOptions"/> object which specifies the CSV export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToHtml(DevExpress.Xpf.Printing.IPrintableControl,System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream in HTML format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to HTML.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created HTML file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToHtml(DevExpress.Xpf.Printing.IPrintableControl,System.IO.Stream,DevExpress.XtraPrinting.HtmlExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in HTML format using the specified HTML-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to HTML.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created HTML file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.HtmlExportOptions"/> object which specifies the HTML export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToHtml(DevExpress.Xpf.Printing.IPrintableControl,System.String,DevExpress.XtraPrinting.HtmlExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in HTML format using the specified HTML-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to HTML.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created HTML file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.HtmlExportOptions"/> object which specifies the HTML export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToHtml(DevExpress.Xpf.Printing.IPrintableControl,System.String)">
            <summary>
                <para>Exports a report to the specified file path in HTML format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to HTML.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created HTML file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToHtml(DevExpress.XtraPrinting.IPrintable,System.IO.Stream,DevExpress.XtraPrinting.HtmlExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in HTML format using the specified HTML-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to HTML.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created HTML file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.HtmlExportOptions"/> object which specifies the HTML export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToHtml(DevExpress.XtraPrinting.IPrintable,System.String,DevExpress.XtraPrinting.HtmlExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in HTML format using the specified HTML-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to HTML.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created HTML file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.HtmlExportOptions"/> object which specifies the HTML export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToHtml(DevExpress.XtraPrinting.IPrintable,System.String)">
            <summary>
                <para>Exports a report to the specified file path in HTML format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to HTML.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created HTML file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToHtml(DevExpress.XtraPrinting.IPrintable,System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream in HTML format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to HTML.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created HTML file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToImage(DevExpress.Xpf.Printing.IPrintableControl,System.String,DevExpress.XtraPrinting.ImageExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in image format using the specified image-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to an image.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created image file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.ImageExportOptions"/> object which specifies the image export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToImage(DevExpress.Xpf.Printing.IPrintableControl,System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream as an image.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to an image.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created image file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToImage(DevExpress.Xpf.Printing.IPrintableControl,System.String)">
            <summary>
                <para>Exports a report to the specified file path in the specified image format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to an image.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created image file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToImage(DevExpress.Xpf.Printing.IPrintableControl,System.IO.Stream,DevExpress.XtraPrinting.ImageExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in image format using the specified image-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to an image.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created image file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.ImageExportOptions"/> object which specifies the image export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToImage(DevExpress.XtraPrinting.IPrintable,System.IO.Stream,DevExpress.XtraPrinting.ImageExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in image format using the specified image-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to image.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created image file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.ImageExportOptions"/> object which specifies the image export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToImage(DevExpress.XtraPrinting.IPrintable,System.String,DevExpress.XtraPrinting.ImageExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in image format using the specified image-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to image.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created image file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.ImageExportOptions"/> object which specifies the image export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToImage(DevExpress.XtraPrinting.IPrintable,System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream as an image.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to image.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created image file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToImage(DevExpress.XtraPrinting.IPrintable,System.String)">
            <summary>
                <para>Exports a report to the specified file path in the specified image format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to image.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created image file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToMht(DevExpress.Xpf.Printing.IPrintableControl,System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream in MHT format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to MHT.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created MHT file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToMht(DevExpress.Xpf.Printing.IPrintableControl,System.IO.Stream,DevExpress.XtraPrinting.MhtExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in MHT format using the specified MHT-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to MHT.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created MHT file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.MhtExportOptions"/> object which specifies the MHT export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToMht(DevExpress.Xpf.Printing.IPrintableControl,System.String)">
            <summary>
                <para>Exports a report to the specified file path in MHT format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to MHT.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created MHT file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToMht(DevExpress.Xpf.Printing.IPrintableControl,System.String,DevExpress.XtraPrinting.MhtExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in MHT format using the specified MHT-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to MHT.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created MHT file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.MhtExportOptions"/> object which specifies the MHT export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToMht(DevExpress.XtraPrinting.IPrintable,System.String)">
            <summary>
                <para>Exports a report to the specified file path in MHT format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to MHT.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created MHT file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToMht(DevExpress.XtraPrinting.IPrintable,System.String,DevExpress.XtraPrinting.MhtExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in MHT format using the specified MHT-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to MHT.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created MHT file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.MhtExportOptions"/> object which specifies the MHT export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToMht(DevExpress.XtraPrinting.IPrintable,System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream in MHT format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to MHT.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created MHT file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToMht(DevExpress.XtraPrinting.IPrintable,System.IO.Stream,DevExpress.XtraPrinting.MhtExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in MHT format using the specified MHT-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to MHT.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created MHT file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.MhtExportOptions"/> object which specifies the MHT export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToPdf(DevExpress.Xpf.Printing.IPrintableControl,System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream in PDF format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to PDF.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created PDF file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToPdf(DevExpress.Xpf.Printing.IPrintableControl,System.IO.Stream,DevExpress.XtraPrinting.PdfExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in PDF format using the specified PDF-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to PDF.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created PDF file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.PdfExportOptions"/> object which specifies the PDF export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToPdf(DevExpress.Xpf.Printing.IPrintableControl,System.String)">
            <summary>
                <para>Exports a report to the specified file path in PDF format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to PDF.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created PDF file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToPdf(DevExpress.Xpf.Printing.IPrintableControl,System.String,DevExpress.XtraPrinting.PdfExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in PDF format, using the specified PDF-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to PDF.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created PDF file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.PdfExportOptions"/> object which specifies the PDF export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToPdf(DevExpress.XtraPrinting.IPrintable,System.String,DevExpress.XtraPrinting.PdfExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in PDF format, using the specified PDF-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to PDF.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created PDF file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.PdfExportOptions"/> object which specifies the PDF export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToPdf(DevExpress.XtraPrinting.IPrintable,System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream in PDF format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to PDF.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created PDF file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToPdf(DevExpress.XtraPrinting.IPrintable,System.IO.Stream,DevExpress.XtraPrinting.PdfExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in PDF format using the specified PDF-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to PDF.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created PDF file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.PdfExportOptions"/> object which specifies the PDF export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToPdf(DevExpress.XtraPrinting.IPrintable,System.String)">
            <summary>
                <para>Exports a report to the specified file path in PDF format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to PDF.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created PDF file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToRtf(DevExpress.Xpf.Printing.IPrintableControl,System.IO.Stream,DevExpress.XtraPrinting.RtfExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in RTF format using the specified RTF-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to RTF.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created RTF file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.RtfExportOptions"/> object which specifies the RTF export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToRtf(DevExpress.Xpf.Printing.IPrintableControl,System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream in RTF format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to RTF.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created RTF file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToRtf(DevExpress.Xpf.Printing.IPrintableControl,System.String)">
            <summary>
                <para>Exports a report to the specified file path in RTF format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to RTF.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created RTF file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToRtf(DevExpress.Xpf.Printing.IPrintableControl,System.String,DevExpress.XtraPrinting.RtfExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in RTF format using the specified RTF-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to RTF.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created RTF file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.RtfExportOptions"/> object which specifies the RTF export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToRtf(DevExpress.XtraPrinting.IPrintable,System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream in RTF format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to RTF.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created RTF file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToRtf(DevExpress.XtraPrinting.IPrintable,System.IO.Stream,DevExpress.XtraPrinting.RtfExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in RTF format using the specified RTF-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to RTF.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created RTF file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.RtfExportOptions"/> object which specifies the RTF export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToRtf(DevExpress.XtraPrinting.IPrintable,System.String,DevExpress.XtraPrinting.RtfExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in RTF format using the specified RTF-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to RTF.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created RTF file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.RtfExportOptions"/> object which specifies the RTF export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToRtf(DevExpress.XtraPrinting.IPrintable,System.String)">
            <summary>
                <para>Exports a report to the specified file path in RTF format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to RTF.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created RTF file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToText(DevExpress.Xpf.Printing.IPrintableControl,System.IO.Stream,DevExpress.XtraPrinting.TextExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in text format using the specified TXT-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created text file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.TextExportOptions"/> object which specifies the TXT export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToText(DevExpress.Xpf.Printing.IPrintableControl,System.String)">
            <summary>
                <para>Exports a report to the specified file path in text format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created text file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToText(DevExpress.Xpf.Printing.IPrintableControl,System.String,DevExpress.XtraPrinting.TextExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in text format using the specified TXT-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created text file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.TextExportOptions"/> object which specifies the TXT export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToText(DevExpress.Xpf.Printing.IPrintableControl,System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream in Text format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created text file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToText(DevExpress.XtraPrinting.IPrintable,System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream in Text format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to text.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created text file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToText(DevExpress.XtraPrinting.IPrintable,System.String,DevExpress.XtraPrinting.TextExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in text format using the specified TXT-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to text.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created text file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.TextExportOptions"/> object which specifies the TXT export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToText(DevExpress.XtraPrinting.IPrintable,System.IO.Stream,DevExpress.XtraPrinting.TextExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in text format using the specified TXT-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to text.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created text file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.TextExportOptions"/> object which specifies the TXT export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToText(DevExpress.XtraPrinting.IPrintable,System.String)">
            <summary>
                <para>Exports a report to the specified file path in text format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to text.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created text file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToXls(DevExpress.Xpf.Printing.IPrintableControl,System.String,DevExpress.XtraPrinting.XlsExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in XLS format using the specified XLS-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to XLS.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created XLS file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.XlsExportOptions"/> object which specifies the XLS export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToXls(DevExpress.Xpf.Printing.IPrintableControl,System.String)">
            <summary>
                <para>Exports a report to the specified file path in XLS format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to XLS.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created XLS file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToXls(DevExpress.Xpf.Printing.IPrintableControl,System.IO.Stream,DevExpress.XtraPrinting.XlsExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in XLS format using the specified XLS-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to XLS.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created XLS file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.XlsExportOptions"/> object which specifies the XLS export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToXls(DevExpress.Xpf.Printing.IPrintableControl,System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream in XLS format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to XLS.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created XLS file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToXls(DevExpress.XtraPrinting.IPrintable,System.String)">
            <summary>
                <para>Exports a report to the specified file path in XLS format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to XLS.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created XLS file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToXls(DevExpress.XtraPrinting.IPrintable,System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream in XLS format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to XLS.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created XLS file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToXls(DevExpress.XtraPrinting.IPrintable,System.IO.Stream,DevExpress.XtraPrinting.XlsExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in XLS format using the specified XLS-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to XLS.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created XLS file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.XlsExportOptions"/> object which specifies the XLS export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToXls(DevExpress.XtraPrinting.IPrintable,System.String,DevExpress.XtraPrinting.XlsExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in XLS format using the specified XLS-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to XLS.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created XLS file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.XlsExportOptions"/> object which specifies the XLS export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToXlsx(DevExpress.Xpf.Printing.IPrintableControl,System.String)">
            <summary>
                <para>Exports a report to the specified file path in XLSX format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to XLSX.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created XLS file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToXlsx(DevExpress.Xpf.Printing.IPrintableControl,System.String,DevExpress.XtraPrinting.XlsxExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in XLSX format using the specified XLSX-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to XLSX.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created XLS file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.XlsxExportOptions"/> object which specifies the XLSX export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToXlsx(DevExpress.Xpf.Printing.IPrintableControl,System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream in XLSX format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to XLSX.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created XLSX file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToXlsx(DevExpress.Xpf.Printing.IPrintableControl,System.IO.Stream,DevExpress.XtraPrinting.XlsxExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in XLSX format using the specified XLSX-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to XLSX.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created XLSX file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.XlsxExportOptions"/> object which specifies the XLSX export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToXlsx(DevExpress.XtraPrinting.IPrintable,System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream in XLSX format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to XLSX.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created XLSX file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToXlsx(DevExpress.XtraPrinting.IPrintable,System.String)">
            <summary>
                <para>Exports a report to the specified file path in XLSX format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to XLSX.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created XLS file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToXlsx(DevExpress.XtraPrinting.IPrintable,System.IO.Stream,DevExpress.XtraPrinting.XlsxExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in XLSX format using the specified XLSX-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to XLSX.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created XLSX file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.XlsxExportOptions"/> object which specifies the XLSX export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToXlsx(DevExpress.XtraPrinting.IPrintable,System.String,DevExpress.XtraPrinting.XlsxExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in XLSX format using the specified XLSX-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to XLSX.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created XLS file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.XlsxExportOptions"/> object which specifies the XLSX export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToXps(DevExpress.Xpf.Printing.IPrintableControl,System.IO.Stream,DevExpress.XtraPrinting.XpsExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in XPS format using the specified XPS-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to XPS.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created XPS file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.XpsExportOptions"/> object which specifies the XPS export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToXps(DevExpress.Xpf.Printing.IPrintableControl,System.String,DevExpress.XtraPrinting.XpsExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in XPS format using the specified XPS-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to XPS.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created XPS file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.XpsExportOptions"/> object which specifies the XPS export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToXps(DevExpress.Xpf.Printing.IPrintableControl,System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream in XPS format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to XPS.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created XPS file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToXps(DevExpress.Xpf.Printing.IPrintableControl,System.String)">
            <summary>
                <para>Exports a report to the specified file path in XPS format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, specifying a control to be exported to XPS.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created XPS file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToXps(DevExpress.XtraPrinting.IPrintable,System.String,DevExpress.XtraPrinting.XpsExportOptions)">
            <summary>
                <para>Exports a report to the specified file path in XPS format using the specified XPS-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to XPS.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created XPS file.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.XpsExportOptions"/> object which specifies the XPS export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToXps(DevExpress.XtraPrinting.IPrintable,System.IO.Stream,DevExpress.XtraPrinting.XpsExportOptions)">
            <summary>
                <para>Exports a report to the specified stream in XPS format using the specified XPS-specific options.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to XPS.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created XPS file should be sent.

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraPrinting.XpsExportOptions"/> object which specifies the XPS export options to be applied when a report is exported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToXps(DevExpress.XtraPrinting.IPrintable,System.IO.Stream)">
            <summary>
                <para>Exports a report to the specified stream in XPS format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to XPS.

            </param>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> object to which the created XPS file should be sent.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ExportToXps(DevExpress.XtraPrinting.IPrintable,System.String)">
            <summary>
                <para>Exports a report to the specified file path in XPS format.
</para>
            </summary>
            <param name="source">
		An object that implements the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, specifying a component to be exported to XPS.

            </param>
            <param name="filePath">
		A <see cref="T:System.String"/> which specifies the file name (including the full path) for the created XPS file.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.Print(DevExpress.XtraReports.IReport)">
            <summary>
                <para>Prints the specified report. 
</para>
            </summary>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.XtraReports.IReport"/> interface, which represents the report to print.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.Print(DevExpress.Xpf.Printing.IPrintableControl)">
            <summary>
                <para>Prints the specified control. 
</para>
            </summary>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface, which is the control to print.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.Print(DevExpress.XtraPrinting.IPrintable)">
            <summary>
                <para>Prints the specified control. 
</para>
            </summary>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface, which is the control to print.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.PrintAsync(DevExpress.XtraPrinting.IPrintable)">
            <summary>
                <para>Prints the specified control, asynchronously.
</para>
            </summary>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.PrintAsync(DevExpress.Xpf.Printing.IPrintableControl)">
            <summary>
                <para>Prints the specified control, asynchronously.
</para>
            </summary>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface.

            </param>


        </member>
        <member name="E:DevExpress.Xpf.Printing.PrintHelper.PrintCompleted">
            <summary>
                <para>Occurs after the printing is completed.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.PrintDirect(DevExpress.XtraReports.IReport)">
            <summary>
                <para>Prints the specified report to a default printer.

</para>
            </summary>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.XtraReports.IReport"/> interface, which represents the report to print.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.PrintDirect(DevExpress.XtraReports.IReport,System.Printing.PrintQueue)">
            <summary>
                <para>Prints the specified report to a default printer.

</para>
            </summary>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.XtraReports.IReport"/> interface, which represents the report to print.

            </param>
            <param name="queue">
		A <see cref="T:System.Printing.PrintQueue"/> object.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.PrintDirect(DevExpress.Xpf.Printing.IPrintableControl)">
            <summary>
                <para>Prints the specified control to a default printer.

</para>
            </summary>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.Xpf.Printing.DataNodes.IPrintableControl"/> interface.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.PrintDirect(DevExpress.Xpf.Printing.IPrintableControl,System.Printing.PrintQueue)">
            <summary>
                <para>Prints the specified control to a default printer.

</para>
            </summary>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.Xpf.Printing.DataNodes.IPrintableControl"/> interface.

            </param>
            <param name="queue">
		A <see cref="T:System.Printing.PrintQueue"/> object.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.PrintDirect(DevExpress.XtraPrinting.IPrintable,System.Printing.PrintQueue)">
            <summary>
                <para>Prints the specified report to a default printer.

</para>
            </summary>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface.

            </param>
            <param name="queue">
		A <see cref="T:System.Printing.PrintQueue"/> object.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.PrintDirect(DevExpress.XtraPrinting.IPrintable)">
            <summary>
                <para>Prints the specified report to a default printer.

</para>
            </summary>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.PrintDirectAsync(DevExpress.XtraPrinting.IPrintable)">
            <summary>
                <para>Prints the specified control using the default system printer, asynchronously.
</para>
            </summary>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.PrintDirectAsync(DevExpress.XtraPrinting.IPrintable,System.Printing.PrintQueue)">
            <summary>
                <para>Prints the specified control using the default system printer, asynchronously.
</para>
            </summary>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface.

            </param>
            <param name="queue">
		A <see cref="T:System.Printing.PrintQueue"/> object.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.PrintDirectAsync(DevExpress.Xpf.Printing.IPrintableControl,System.Printing.PrintQueue)">
            <summary>
                <para>Prints the specified control using the default system printer, asynchronously.
</para>
            </summary>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface.

            </param>
            <param name="queue">
		A <see cref="T:System.Printing.PrintQueue"/> object.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.PrintDirectAsync(DevExpress.Xpf.Printing.IPrintableControl)">
            <summary>
                <para>Prints the specified control using the default system printer, asynchronously.
</para>
            </summary>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreview(System.Windows.Window,DevExpress.Xpf.Printing.LinkBase,System.String)">
            <summary>
                <para>Creates a document from the specified link, and shows it in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Window"/> object, which is the owner of the dialog.

            </param>
            <param name="link">
		A <see cref="T:DevExpress.Xpf.Printing.LinkBase"/> descendant.

            </param>
            <param name="title">
		A <see cref="T:System.String"/> value, specifying the Document Preview dialog's title. "Print Preview" by default.

            </param>
            <returns>A <see cref="T:System.Windows.Window"/> object, in which the Document Preview is shown.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreview(System.Windows.FrameworkElement,DevExpress.Xpf.Printing.LinkBase,System.String)">
            <summary>
                <para>Creates a document from the specified link, and shows it in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.FrameworkElement"/> object, which is the owner of the dialog.

            </param>
            <param name="link">
		A <see cref="T:DevExpress.Xpf.Printing.LinkBase"/> descendant.

            </param>
            <param name="title">
		A <see cref="T:System.String"/> value, specifying the Document Preview dialog's title. "Print Preview" by default.

            </param>
            <returns>A <see cref="T:DevExpress.Xpf.Core.FloatingContainer"/> object.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreview(System.Windows.Window,DevExpress.XtraReports.IReport)">
            <summary>
                <para>Creates a document from the specified report, and shows it in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Window"/> object, which is the owner of the dialog.

            </param>
            <param name="report">
		An object implementing the <see cref="T:DevExpress.XtraReports.IReport"/> interface, which represents the report to preview.

            </param>
            <returns>A <see cref="T:System.Windows.Window"/> object, in which the Document Preview is shown.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreview(System.Windows.Window,DevExpress.Xpf.Printing.LinkBase)">
            <summary>
                <para>Creates a document from the specified link, and shows it in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Window"/> object, which is the owner of the dialog.

            </param>
            <param name="link">
		A <see cref="T:DevExpress.Xpf.Printing.LinkBase"/> descendant.

            </param>
            <returns>A <see cref="T:System.Windows.Window"/> object, in which the Document Preview is shown.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreview(System.Windows.Window,DevExpress.XtraReports.IReport,System.String)">
            <summary>
                <para>Creates a document from the specified report, and shows it in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Window"/> object, which is the owner of the dialog.

            </param>
            <param name="report">
		An object implementing the <see cref="T:DevExpress.XtraReports.IReport"/> interface, which represents the report to preview.

            </param>
            <param name="title">
		A <see cref="T:System.String"/> value, specifying the Document Preview dialog's title. "Print Preview" by default.

            </param>
            <returns>A <see cref="T:System.Windows.Window"/> object, in which the Document Preview is shown.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreview(System.Windows.FrameworkElement,DevExpress.Xpf.Printing.LinkBase)">
            <summary>
                <para>Creates a document from the specified link, and shows it in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.FrameworkElement"/> object, which is the owner of the dialog.

            </param>
            <param name="link">
		A <see cref="T:DevExpress.Xpf.Printing.LinkBase"/> descendant.

            </param>
            <returns>A <see cref="T:DevExpress.Xpf.Core.FloatingContainer"/> object.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreview(System.Windows.FrameworkElement,DevExpress.Xpf.Printing.IPrintableControl,System.String,System.String)">
            <summary>
                <para>Creates a document from the specified control, and shows it in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.FrameworkElement"/> object, which is the owner of the dialog.

            </param>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.Xpf.Printing.DataNodes.IPrintableControl"/> interface.

            </param>
            <param name="documentName">
		A <see cref="T:System.String"/> value, specifying the document name.

            </param>
            <param name="title">
		A <see cref="T:System.String"/> value, specifying the Document Preview dialog's title. "Print Preview" by default.

            </param>
            <returns>A <see cref="T:DevExpress.Xpf.Core.FloatingContainer"/> object.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreview(System.Windows.FrameworkElement,DevExpress.Xpf.Printing.IPrintableControl,System.String)">
            <summary>
                <para>Creates a document from the specified control, and shows it in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.FrameworkElement"/> object, which is the owner of the dialog.

            </param>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.Xpf.Printing.DataNodes.IPrintableControl"/> interface.

            </param>
            <param name="documentName">
		A <see cref="T:System.String"/> value, specifying the document name.

            </param>
            <returns>A <see cref="T:DevExpress.Xpf.Core.FloatingContainer"/> object.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreview(System.Windows.FrameworkElement,DevExpress.Xpf.Printing.IPrintableControl)">
            <summary>
                <para>Creates a document from the specified control, and shows it in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.FrameworkElement"/> object, which is the owner of the dialog.

            </param>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.Xpf.Printing.DataNodes.IPrintableControl"/> interface.

            </param>
            <returns>A <see cref="T:DevExpress.Xpf.Core.FloatingContainer"/> object.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreview(System.Windows.Window,DevExpress.Xpf.Printing.IPrintableControl,System.String,System.String)">
            <summary>
                <para>Creates a document from the specified control, and shows it in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Window"/> object, which is the owner of the dialog.

            </param>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.Xpf.Printing.DataNodes.IPrintableControl"/> interface.

            </param>
            <param name="documentName">
		A <see cref="T:System.String"/> value, specifying the document name.

            </param>
            <param name="title">
		A <see cref="T:System.String"/> value, specifying the Document Preview dialog's title. "Print Preview" by default.

            </param>
            <returns>A <see cref="T:System.Windows.Window"/> object, in which the Document Preview is shown.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreview(System.Windows.Window,DevExpress.Xpf.Printing.IPrintableControl,System.String)">
            <summary>
                <para>Creates a document from the specified control, and shows it in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Window"/> object, which is the owner of the dialog.

            </param>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.Xpf.Printing.DataNodes.IPrintableControl"/> interface.

            </param>
            <param name="documentName">
		A <see cref="T:System.String"/> value, specifying the document name.

            </param>
            <returns>A <see cref="T:System.Windows.Window"/> object, in which the Document Preview is shown.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreview(System.Windows.Window,DevExpress.Xpf.Printing.IPrintableControl)">
            <summary>
                <para>Creates a document from the specified control, and shows it in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Window"/> object, which is the owner of the dialog.

            </param>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.Xpf.Printing.DataNodes.IPrintableControl"/> interface.

            </param>
            <returns>A <see cref="T:System.Windows.Window"/> object, in which the Document Preview is shown.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreview(System.Windows.FrameworkElement,DevExpress.XtraReports.IReport)">
            <summary>
                <para>Creates a document from the specified link, and shows it in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.FrameworkElement"/> object, which is the owner of the dialog.

            </param>
            <param name="report">
		An object implementing the <see cref="T:DevExpress.XtraReports.IReport"/> interface, which represents the report to preview.

            </param>
            <returns>A <see cref="T:DevExpress.Xpf.Core.FloatingContainer"/> object.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreview(System.Windows.FrameworkElement,DevExpress.XtraReports.IReport,System.String)">
            <summary>
                <para>Creates a document from the specified link, and shows it in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.FrameworkElement"/> object, which is the owner of the dialog.

            </param>
            <param name="report">
		An object implementing the <see cref="T:DevExpress.XtraReports.IReport"/> interface, which represents the report to preview.

            </param>
            <param name="title">
		A <see cref="T:System.String"/> value, specifying the dialog title.

            </param>
            <returns>A <see cref="T:DevExpress.Xpf.Core.FloatingContainer"/> object.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreview(System.Windows.Window,DevExpress.XtraPrinting.IPrintable,System.String)">
            <summary>
                <para>Creates a document from the specified control, and shows it in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Window"/> object, which is the owner of the dialog.

            </param>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface.

            </param>
            <param name="documentName">
		A <see cref="T:System.String"/> value, specifying the document name.

            </param>
            <returns>A <see cref="T:System.Windows.Window"/> object, in which the Document Preview is shown.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreview(System.Windows.FrameworkElement,DevExpress.XtraPrinting.IPrintable,System.String)">
            <summary>
                <para>Creates a document from the specified control, and shows it in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.FrameworkElement"/> object, which is the owner of the dialog.

            </param>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface.

            </param>
            <param name="documentName">
		A <see cref="T:System.String"/> value, specifying the document name.

            </param>
            <returns>A <see cref="T:DevExpress.Xpf.Core.FloatingContainer"/> object.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreview(System.Windows.FrameworkElement,DevExpress.XtraPrinting.IPrintable)">
            <summary>
                <para>Creates a document from the specified control, and shows it in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.FrameworkElement"/> object, which is the owner of the dialog.

            </param>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface.

            </param>
            <returns>A <see cref="T:DevExpress.Xpf.Core.FloatingContainer"/> object.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreview(System.Windows.Window,DevExpress.XtraPrinting.IPrintable)">
            <summary>
                <para>Creates a document from the specified control, and shows it in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Window"/> object, which is the owner of the dialog.

            </param>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface.

            </param>
            <returns>A <see cref="T:System.Windows.Window"/> object, in which the Document Preview is shown.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreview(System.Windows.FrameworkElement,DevExpress.XtraPrinting.IPrintable,System.String,System.String)">
            <summary>
                <para>Creates a document from the specified control, and shows it in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.FrameworkElement"/> object, which is the owner of the dialog.

            </param>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface.

            </param>
            <param name="documentName">
		A <see cref="T:System.String"/> value, specifying the document name.

            </param>
            <param name="title">
		A <see cref="T:System.String"/> value, specifying the Document Preview dialog's title. "Print Preview" by default.

            </param>
            <returns>A <see cref="T:DevExpress.Xpf.Core.FloatingContainer"/> object.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreview(System.Windows.Window,DevExpress.XtraPrinting.IPrintable,System.String,System.String)">
            <summary>
                <para>Creates a document from the specified control, and shows it in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Window"/> object, which is the owner of the dialog.

            </param>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.XtraPrinting.IPrintable"/> interface.

            </param>
            <param name="documentName">
		A <see cref="T:System.String"/> value, specifying the document name.

            </param>
            <param name="title">
		A <see cref="T:System.String"/> value, specifying the Document Preview dialog's title. "Print Preview" by default.

            </param>
            <returns>A <see cref="T:System.Windows.Window"/> object, in which the Document Preview is shown.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreviewDialog(System.Windows.Window,DevExpress.XtraReports.IReport)">
            <summary>
                <para>Creates a document from the specified report, and shows it <i>modally</i> in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Window"/> object, which is the owner of the dialog.

            </param>
            <param name="report">
		An object implementing the <see cref="T:DevExpress.XtraReports.IReport"/> interface, which represents the report to preview.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreviewDialog(System.Windows.Window,DevExpress.XtraReports.IReport,System.String)">
            <summary>
                <para>Creates a document from the specified report, and shows it <i>modally</i> in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Window"/> object, which is the owner of the dialog.

            </param>
            <param name="report">
		An object implementing the <see cref="T:DevExpress.XtraReports.IReport"/> interface, which represents the report to preview.

            </param>
            <param name="title">
		A <see cref="T:System.String"/> value, specifying the Document Preview dialog's title. "Print Preview" by default.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreviewDialog(System.Windows.Window,DevExpress.Xpf.Printing.LinkBase,System.String)">
            <summary>
                <para>Creates a document from the specified link, and shows it <i>modally</i> in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Window"/> object, which is the owner of the dialog.

            </param>
            <param name="link">
		A <see cref="T:DevExpress.Xpf.Printing.LinkBase"/> descendant.

            </param>
            <param name="title">
		A <see cref="T:System.String"/> value, specifying the Document Preview dialog's title. "Print Preview" by default.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreviewDialog(System.Windows.Window,DevExpress.Xpf.Printing.LinkBase)">
            <summary>
                <para>Creates a document from the specified link, and shows it <i>modally</i> in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Window"/> object, which is the owner of the dialog.

            </param>
            <param name="link">
		A <see cref="T:DevExpress.Xpf.Printing.LinkBase"/> descendant.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreviewDialog(System.Windows.Window,DevExpress.Xpf.Printing.IPrintableControl,System.String,System.String)">
            <summary>
                <para>Creates a document from the specified control, and shows it <i>modally</i> in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Window"/> object, which is the owner of the dialog.

            </param>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface.

            </param>
            <param name="documentName">
		A <see cref="T:System.String"/> value, specifying the document name.

            </param>
            <param name="title">
		A <see cref="T:System.String"/> value, specifying the Document Preview dialog's title. "Print Preview" by default.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreviewDialog(System.Windows.Window,DevExpress.Xpf.Printing.IPrintableControl)">
            <summary>
                <para>Creates a document from the specified control, and shows it <i>modally</i> in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Window"/> object, which is the owner of the dialog.

            </param>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreviewDialog(System.Windows.Window,DevExpress.Xpf.Printing.IPrintableControl,System.String)">
            <summary>
                <para>Creates a document from the specified control, and shows it <i>modally</i> in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Window"/> object, which is the owner of the dialog.

            </param>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface.

            </param>
            <param name="documentName">
		A <see cref="T:System.String"/> value, specifying the document name.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreviewDialog(System.Windows.Window,DevExpress.XtraPrinting.IPrintable)">
            <summary>
                <para>Creates a document from the specified control, and shows it <i>modally</i> in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Window"/> object, which is the owner of the dialog.

            </param>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreviewDialog(System.Windows.Window,DevExpress.XtraPrinting.IPrintable,System.String)">
            <summary>
                <para>Creates a document from the specified control, and shows it <i>modally</i> in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Window"/> object, which is the owner of the dialog.

            </param>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface.

            </param>
            <param name="documentName">
		A <see cref="T:System.String"/> value, specifying the document name.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintHelper.ShowPrintPreviewDialog(System.Windows.Window,DevExpress.XtraPrinting.IPrintable,System.String,System.String)">
            <summary>
                <para>Creates a document from the specified control, and shows it <i>modally</i> in the Document Preview dialog.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:System.Windows.Window"/> object, which is the owner of the dialog.

            </param>
            <param name="source">
		An object implementing the <see cref="T:DevExpress.Xpf.Printing.IPrintableControl"/> interface.

            </param>
            <param name="documentName">
		A <see cref="T:System.String"/> value, specifying the document name.

            </param>
            <param name="title">
		A <see cref="T:System.String"/> value, specifying the Document Preview dialog's title. "Print Preview" by default.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PrintHelper.UsePrintTickets">
            <summary>
                <para>Specifies whether or not to use print tickets when printing a document.
</para>
            </summary>
            <value><b>true</b> to use print tickets; otherwise <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Printing.DocumentPreviewModelBase">

            <summary>
                <para>The base class for classes that provide the Preview Model functionality.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.DocumentPreviewModelBase.#ctor">
            <summary>
                <para>Initializes a new instance of the DocumentPreviewModelBase class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.CurrentPageIndex">
            <summary>
                <para>Specifies the zero-based current page index.

</para>
            </summary>
            <value>A zero-based integer value specifying the current page index.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.CurrentPageNumber">
            <summary>
                <para>Specifies the current page number.


</para>
            </summary>
            <value>An integer value specifying the current page number.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.DocumentMapRootNode">
            <summary>
                <para>For internal use. Gets the root node of the Document Map.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.DocumentMapSelectedNode">
            <summary>
                <para>For internal use. Gets the currently selected node of the Document Map.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.ExportCommand">
            <summary>
                <para>Exports the current document.




</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.FirstPageCommand">
            <summary>
                <para>Navigates to the first page of the current document.

</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.FoundBrickInfo">
            <summary>
                <para>For internal use. Required to highlight an element found using the Search option.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="M:DevExpress.Xpf.Printing.DocumentPreviewModelBase.HandlePreviewDoubleClick(System.Windows.Input.MouseEventArgs,System.Windows.FrameworkElement)">
            <summary>
                <para>For internal use.
</para>
            </summary>
            <param name="e">
		A <see cref="T:System.Windows.Input.MouseEventArgs"/> object.

            </param>
            <param name="source">
		A <see cref="T:System.Windows.FrameworkElement"/> object.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.DocumentPreviewModelBase.HandlePreviewMouseLeftButtonDown(System.Windows.Input.MouseButtonEventArgs,System.Windows.FrameworkElement)">
            <summary>
                <para>For internal use.
</para>
            </summary>
            <param name="e">
		A <see cref="T:System.Windows.Input.MouseButtonEventArgs"/> object.

            </param>
            <param name="source">
		A <see cref="T:System.Windows.FrameworkElement"/> object.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.DocumentPreviewModelBase.HandlePreviewMouseLeftButtonUp(System.Windows.Input.MouseButtonEventArgs,System.Windows.FrameworkElement)">
            <summary>
                <para>For internal use.
</para>
            </summary>
            <param name="e">
		A <see cref="T:System.Windows.Input.MouseButtonEventArgs"/> object.

            </param>
            <param name="source">
		A <see cref="T:System.Windows.FrameworkElement"/> object.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.DocumentPreviewModelBase.HandlePreviewMouseMove(System.Windows.Input.MouseEventArgs,System.Windows.FrameworkElement)">
            <summary>
                <para>For internal use.
</para>
            </summary>
            <param name="e">
		A <see cref="T:System.Windows.Input.MouseEventArgs"/> object.

            </param>
            <param name="source">
		A <see cref="T:System.Windows.FrameworkElement"/> object.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.InputController">
            <summary>
                <para>Provides access to the input controller associated with the DocumentPreviewModelBase.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Printing.InputController"/> object.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.IsDocumentMapVisible">
            <summary>
                <para>Specifies whether or not the <b>Document Map</b> panel is visible.

</para>
            </summary>
            <value><b>true</b> to show the Document Map panel; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.IsEmptyDocument">
            <summary>
                <para>Gets the value specifying whether or not the document has any pages.
</para>
            </summary>
            <value><b>true</b> if the document is empty; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.IsOpenButtonVisible">
            <summary>
                <para>Gets the value indicating whether or not the <b>Open</b> button is available in the Bar Manager of the Document Preview.
</para>
            </summary>
            <value><b>true</b> if the button is available in the toolbar; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.IsParametersPanelVisible">
            <summary>
                <para>Specifies whether or not the <b>Parameters</b> panel is visible.
</para>
            </summary>
            <value><b>true</b> to show the Parameters panel; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.IsSaveButtonVisible">
            <summary>
                <para>Gets the value indicating whether or not the <b>Save</b> button is available in the Bar Manager of the Document Preview.
</para>
            </summary>
            <value><b>true</b> if the button is available in the toolbar; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.IsScaleVisible">
            <summary>
                <para>Gets the value indicating whether or not the <b>Scale</b> button is available in the Bar Manager of the Document Preview.
</para>
            </summary>
            <value><b>true</b> if scaling is available for the report; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.IsSearchPanelVisible">
            <summary>
                <para>Specifies whether or not the <b>Search</b> panel is visible in Document Preview.


</para>
            </summary>
            <value><b>true</b> to show the Search panel; otherwise <b>false</b>.

</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.IsSearchVisible">
            <summary>
                <para>Gets the value indicating whether or not the <b>Search</b> command is accessible in a Document Preview.
</para>
            </summary>
            <value><b>true</b> if searching is available for the report; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.IsSendVisible">
            <summary>
                <para>Gets the value indicating whether or not the <b>Send via e-mail...</b> button is available in the Bar Manager of the Document Preview.
</para>
            </summary>
            <value><b>true</b> if the button is available in the toolbar; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.IsSetWatermarkVisible">
            <summary>
                <para>Specifies whether or not the <b>Watermark</b> button is visible.

</para>
            </summary>
            <value><b>true</b> if the button is visible; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.LastPageCommand">
            <summary>
                <para>Navigates to the last page of the current document.


</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.NextPageCommand">
            <summary>
                <para>Navigates to the next page of the current document.


</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.OpenCommand">
            <summary>
                <para>Opens a document.


</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.PageSetupCommand">
            <summary>
                <para>Runs the <b>Page Setup</b> dialog for the current document.

</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.ParametersPanelContent">
            <summary>
                <para>Provides access to the elements of the <b>Parameters</b> panel.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.FrameworkElement"/> object that stores parameter editors available in the panel.
</value>


        </member>
        <member name="E:DevExpress.Xpf.Printing.DocumentPreviewModelBase.PreviewClick">
            <summary>
                <para>Occurs when the left mouse button is clicked in the document area in a Document Preview.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Printing.DocumentPreviewModelBase.PreviewDoubleClick">
            <summary>
                <para>Occurs when the left mouse button is double-clicked in the document area in a Document Preview.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Printing.DocumentPreviewModelBase.PreviewMouseMove">
            <summary>
                <para>Occurs when the mouse cursor moves over the document area in a Document Preview.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.PreviousPageCommand">
            <summary>
                <para>Navigates to the previous page of the current document.

</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.PrintCommand">
            <summary>
                <para>Prints the current document using the specified printer.

</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.PrintDirectCommand">
            <summary>
                <para>Prints the current document using the default printer.

</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.ProgressMaximum">
            <summary>
                <para>Gets the maximum value of the Progress Reflector.
</para>
            </summary>
            <value>An integer value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.ProgressValue">
            <summary>
                <para>Gets a value which reflects the state of a process being tracked by the Progress Reflector. 

</para>
            </summary>
            <value>An integer value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.ProgressVisibility">
            <summary>
                <para>Gets the Progress Reflector visibility state.
</para>
            </summary>
            <value><b>true</b> if the Progress Reflector is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.SaveCommand">
            <summary>
                <para>Saves the current document.
</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.ScaleCommand">
            <summary>
                <para>Adjusts the scale of the current report.



</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.SendCommand">
            <summary>
                <para>Exports and attaches the current document to an e-mail.

</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.StopCommand">
            <summary>
                <para>Interrupts the document's generation.

</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.ToggleDocumentMapCommand">
            <summary>
                <para>Executes the command, which shows or hides the Document Map panel.




</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface that identifies the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.ToggleParametersPanelCommand">
            <summary>
                <para>Executes the command, which shows or hides the Parameters panel.

</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface that identifies the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.ToggleSearchPanelCommand">
            <summary>
                <para>Executes the command, which shows or hides the <b>Search</b> panel.
</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface that identifies the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.DocumentPreviewModelBase.WatermarkCommand">
            <summary>
                <para>Shows the <b>Watermark</b> dialog.
</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Input.ICommand"/> interface, that defines the command.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Printing.PrintingSystemPreviewModel">

            <summary>
                <para>Provides the Preview Model functionality in WPF. 
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintingSystemPreviewModel.#ctor">
            <summary>
                <para>Initializes a new instance of the PrintingSystemPreviewModel class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintingSystemPreviewModel.Dispose">
            <summary>
                <para>Disposes of the PrintingSystemPreviewModel object.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PrintingSystemPreviewModel.DocumentMapRootNode">
            <summary>
                <para>For internal use. Specifies the root node for the report's Document Map.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PrintingSystemPreviewModel.IsCreating">
            <summary>
                <para>Gets a value indicating whether or not the document is being created.

</para>
            </summary>
            <value><b>true</b> if the document creation isn't finished; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PrintingSystemPreviewModel.IsScaleVisible">
            <summary>
                <para>Gets the value indicating whether or not the <b>Scale</b> button is available in the Bar Manager of the Document Preview.
</para>
            </summary>
            <value><b>true</b> if scaling is available for the report; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PrintingSystemPreviewModel.IsSearchVisible">
            <summary>
                <para>Specifies whether or not the <b>Search</b> command is accessible in a Document Preview.
</para>
            </summary>
            <value><b>true</b> to make the <b>Search</b> button available; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PrintingSystemPreviewModel.PageCount">
            <summary>
                <para>Specifies the number of pages in the report document shown in the Preview Model.
</para>
            </summary>
            <value>An integer value, specifying the number of pages.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintingSystemPreviewModel.PrintDialog">
            <summary>
                <para>Runs a print dialog box used for selecting a printer, setting the print options and printing the document.

</para>
            </summary>
            <returns><b>true</b> if the user clicks <b>OK</b> in the dialog box; <b>false</b> if the user clicks <b>Cancel</b>; otherwise <b>null</b> (<b>Nothing</b> in Visual Basic).
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintingSystemPreviewModel.PrintDirect">
            <summary>
                <para>Prints the document on the default printer. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintingSystemPreviewModel.PrintDirect(System.String)">
            <summary>
                <para>Prints the document on the specified printer. 
</para>
            </summary>
            <param name="printerName">
		A <see cref="T:System.String"/> value, specifying the printer name.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintingSystemPreviewModel.PrintDirect(System.Printing.PrintQueue)">
            <summary>
                <para>Prints the current document to a default printer.

</para>
            </summary>
            <param name="printQueue">
		A <see cref="T:System.Printing.PrintQueue"/> object specifying the print queue.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PrintingSystemPreviewModel.ProgressMaximum">
            <summary>
                <para>Specifies the maximum absolute value shown in the Progress Bar, which appears in the Print Preview while the document is being created.
</para>
            </summary>
            <value>An integer value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PrintingSystemPreviewModel.ProgressValue">
            <summary>
                <para>Gets the current progress value shown in the Progress Bar, which appears in the Print Preview while the document is being created.
</para>
            </summary>
            <value>An integer value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.PrintingSystemPreviewModel.ProgressVisibility">
            <summary>
                <para>Gets the current visibility of the Progress Bar, which appears in the Print Preview while the document is being created.
</para>
            </summary>
            <value><b>true</b> if the Progress Bar is shown; otherwise <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Printing.PrintingSystemPreviewModel.ShowPageSetupDialog(System.Windows.Window)">
            <summary>
                <para>Displays modally a <b>Page Setup</b> dialog window. 
</para>
            </summary>
            <param name="ownerWindow">
		A <see cref="T:System.Object"/>, representing the dialog's owner window.

            </param>
            <returns><b>true</b> if the user clicks <b>OK</b> in the dialog box; <b>false</b> if the user clicks <b>Cancel</b>;  otherwise <b>null</b> (<b>Nothing</b> in Visual Basic).
</returns>


        </member>
        <member name="T:DevExpress.Xpf.Printing.CustomizeParameterEditorsEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Printing.XtraReportPreviewModel.CustomizeParameterEditors"/> event.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Printing.CustomizeParameterEditorsEventArgs.#ctor(DevExpress.XtraReports.Parameters.Parameter)">
            <summary>
                <para>Initializes a new instance of the CustomizeParameterEditorsEventArgs class with the specified parameter. 
</para>
            </summary>
            <param name="parameter">
		A <see cref="T:DevExpress.XtraReports.Parameters.Parameter"/> object.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Printing.CustomizeParameterEditorsEventArgs.BoundDataConverter">
            <summary>
                <para>Provides a custom logic to the parameter's data binding.
</para>
            </summary>
            <value>An object implementing the <see cref="T:System.Windows.Data.IValueConverter"/> interface.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.CustomizeParameterEditorsEventArgs.BoundDataConverterParameter">
            <summary>
                <para>Specifies a parameter that can be used in the bound data converter's methods.
</para>
            </summary>
            <value>A <see cref="T:System.Object"/> value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.CustomizeParameterEditorsEventArgs.BoundDataMember">
            <summary>
                <para>Specifies the data member that is associated with the parameter.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.CustomizeParameterEditorsEventArgs.Editor">
            <summary>
                <para>Specifies a custom editor for the parameter.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Editors.BaseEdit"/> descendant representing the parameter's editor.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Printing.CustomizeParameterEditorsEventArgs.Parameter">
            <summary>
                <para>Gets the parameter, for which a custom editor is being provided.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraReports.Parameters.Parameter"/> object.
</value>


        </member>
    </members>
</doc>
