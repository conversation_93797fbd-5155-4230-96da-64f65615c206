﻿Imports Microsoft.Reporting.WinForms
Imports System.Windows.Forms

Public Class FormReportViewer

    Private ReportTitle As String
    Private ReportType As String
    Private VAT As Decimal
    Private Address As String
    Private TelephoneNumbers As String
    Private StopPreview As Boolean = False
    Private isReplacement As Boolean = False
    Private ClonedContractNumber As String
    Private LegalInfo As String
    Private InstallationOnly As String
    Private PrintedByUser As String
    Private DataObject As NovaData.Contract
    Private ReportDataSet As New DataSetContractReport

    Public Sub New _
        (ContractObject As NovaData.Contract,
         ReportTitleParameter As String,
         VATParameter As String,
         AddressParameter As String,
         TelephoneNumbersParameter As String,
         InstallationOnlyParameter As String,
         ReportTypeParameter As String,
         LegalInfoParameter As String,
         AppIcon As System.Drawing.Icon,
         ConString As String)

        ' This call is required by the designer.
        InitializeComponent()

        ' Setup the given variables.
        Icon = AppIcon
        DataObject = ContractObject

        ' Setup variables.
        InstallationOnly = InstallationOnlyParameter
        ReportTitle = ReportTitleParameter
        VAT = CDec(VATParameter)
        Address = AddressParameter
        TelephoneNumbers = TelephoneNumbersParameter
        LegalInfo = LegalInfoParameter
        ReportType = ReportTypeParameter
        isReplacement = DataObject.isReplacement
        ClonedContractNumber = If(DataObject.ClonedContractNumber, "None")


        Dim Builder As New SqlClient.SqlConnectionStringBuilder(My.Settings.DBConnection)
        PrintedByUser = Builder.UserID
        ' Load data required for the report.
        If CBool(CType(ContractObject.Type = NovaData.Contract.ContractType.Rental, NovaData.Contract.ContractType) Or
            NovaData.Contract.ContractType.InstallationOnly Or
            CType(ContractObject.Type = NovaData.Contract.ContractType.Distribution, NovaData.Contract.ContractType)) Then
            LoadRentalData(ConString)
        ElseIf ContractObject.Type = NovaData.Contract.ContractType.Research Then
            LoadResearchData(ConString)
        End If

    End Sub

    Private Sub LoadRentalData(ConString As String)

        ' Create table adapters.
        Dim ContractAdapter As New DataSetContractReportTableAdapters.ContractTableAdapter
        Dim BillingInstructionAdapter As New DataSetContractReportTableAdapters.BillingInstructionTableAdapter
        Dim BurstAdapter As New DataSetContractReportTableAdapters.BurstTableAdapter
        Dim ContractInventoryAdapter As New DataSetContractReportTableAdapters.ContractInventoryTableAdapter
        Dim MiscellaneousChargeAdapter As New DataSetContractReportTableAdapters.MiscellaneousChargeTableAdapter

        ' Setup the database connection for each adapter.
        Dim DBConnection As New SqlClient.SqlConnection(ConString)
        ContractAdapter.Connection = DBConnection
        BillingInstructionAdapter.Connection = DBConnection
        BurstAdapter.Connection = DBConnection
        ContractInventoryAdapter.Connection = DBConnection
        MiscellaneousChargeAdapter.Connection = DBConnection

        ' Fill the report's dataset and with data.
        ContractAdapter.Fill(ReportDataSet.Contract, DataObject.ContractID, PrintedByUser)
        BillingInstructionAdapter.Fill(ReportDataSet.BillingInstruction, DataObject.ContractID)
        Try
            BurstAdapter.Fill(ReportDataSet.Burst, DataObject.ContractID, My.User.CurrentPrincipal.Identity.Name)
        Catch ex As ConstraintException
            StopPreview = True
        End Try
        ContractInventoryAdapter.Fill(ReportDataSet.ContractInventory, DataObject.ContractID)
        MiscellaneousChargeAdapter.Fill(ReportDataSet.MiscellaneousCharge, DataObject.ContractID)

    End Sub

    Private Sub LoadResearchData(ConString As String)

        ' Create table adapters.
        Dim ResearchContractAdapter As New DataSetContractReportTableAdapters.ResearchContractTableAdapter
        Dim BillingInstructionAdapter As New DataSetContractReportTableAdapters.BillingInstructionTableAdapter
        Dim ResearchCategoryAdapter As New DataSetContractReportTableAdapters.ResearchCategoryTableAdapter
        Dim MiscellaneousChargeAdapter As New DataSetContractReportTableAdapters.MiscellaneousChargeTableAdapter

        ' Setup the database connection for each adapter.
        Dim DBConnection As New SqlClient.SqlConnection(ConString)
        ResearchContractAdapter.Connection = DBConnection
        BillingInstructionAdapter.Connection = DBConnection
        ResearchCategoryAdapter.Connection = DBConnection
        MiscellaneousChargeAdapter.Connection = DBConnection

        ' Fill the report's dataset and with data.
        ResearchContractAdapter.Fill(ReportDataSet.ResearchContract, DataObject.ContractID)
        BillingInstructionAdapter.Fill(ReportDataSet.BillingInstruction, DataObject.ContractID)
        ResearchCategoryAdapter.Fill(ReportDataSet.ResearchCategory, DataObject.ContractID)
        MiscellaneousChargeAdapter.Fill(ReportDataSet.MiscellaneousCharge, DataObject.ContractID)

    End Sub

    Private Sub FormReportViewer_Load(sender As System.Object, e As System.EventArgs) Handles MyBase.Load

        ' Stop if any errors occured.
        If StopPreview Then
            Exit Sub
        End If

        ' Process sub reports.
        AddHandler Viewer.LocalReport.SubreportProcessing, AddressOf RentalSubreportProcessingEventHandler

        ' Process report parameters.
        Dim TitleParameter As New Microsoft.Reporting.WinForms.ReportParameter("ReportTitle", ReportTitle.ToUpper)
        Dim VATParameter As New Microsoft.Reporting.WinForms.ReportParameter("VAT", VAT.ToString())
        Dim AddressParameter As New Microsoft.Reporting.WinForms.ReportParameter("Address", Address)
        Dim InstallationOnlyParameter As New Microsoft.Reporting.WinForms.ReportParameter("InstallationOnly", InstallationOnly.ToUpper)
        Dim TelephonesParameter As New Microsoft.Reporting.WinForms.ReportParameter("TelephoneNumbers", TelephoneNumbers)
        Dim LegalInfoParameter As New Microsoft.Reporting.WinForms.ReportParameter("LegalInfo", LegalInfo)
        Dim ReportTypeParameter As New Microsoft.Reporting.WinForms.ReportParameter("ReportType", ReportType)
        Dim ClonedContractNumberParameter As New Microsoft.Reporting.WinForms.ReportParameter("ClonedContractNumber", ClonedContractNumber)
        Dim isReplacementParameter As New Microsoft.Reporting.WinForms.ReportParameter("isReplacement", If(isReplacement, "true", "false"))
        Dim PrintedByUserParameter As New Microsoft.Reporting.WinForms.ReportParameter("PrintedByUser", PrintedByUser)

        Dim ParamaterList As New List(Of Microsoft.Reporting.WinForms.ReportParameter)
        ParamaterList.Add(TitleParameter)
        ParamaterList.Add(VATParameter)
        ParamaterList.Add(AddressParameter)
        ParamaterList.Add(InstallationOnlyParameter)
        ParamaterList.Add(TelephonesParameter)
        ParamaterList.Add(LegalInfoParameter)
        ParamaterList.Add(ReportTypeParameter)
        ParamaterList.Add(ClonedContractNumberParameter)
        ParamaterList.Add(isReplacementParameter)
        ParamaterList.Add(PrintedByUserParameter)

        ' Set report viewer properties for a research contract.
        If DataObject.Type = NovaData.Contract.ContractType.Research Then
            ' Set the embedded report definition file.
            Viewer.LocalReport.ReportEmbeddedResource = "NovaReports.ContractResearch.rdlc"
            ' Create a data source for the report viewer's local report.
            Dim ReportDataSource As New Microsoft.Reporting.WinForms.ReportDataSource("ResearchContractDataSet", CType(ReportDataSet.ResearchContract, DataTable))
            ' Add the data source to the local report of the report viewer.
            Viewer.LocalReport.DataSources.Add(ReportDataSource)
        End If

        ' Set report viewer properties for a research contract.
        If CBool(CType(DataObject.Type = NovaData.Contract.ContractType.Rental, NovaData.Contract.ContractType) Or NovaData.Contract.ContractType.InstallationOnly Or CType(DataObject.Type = NovaData.Contract.ContractType.Distribution, NovaData.Contract.ContractType)) Then
            ' Set the embedded report definition file.
            If DataObject.PrintAgencyComm Then
                Viewer.LocalReport.ReportEmbeddedResource = "NovaReports.ContractRentalWithComm.rdlc"
            Else
                Viewer.LocalReport.ReportEmbeddedResource = "NovaReports.ContractRentalFinal.rdlc"
            End If
            ' Create a data source for the report viewer's local report.
            Dim ReportDataSource As New Microsoft.Reporting.WinForms.ReportDataSource("ContractDataSet", CType(ReportDataSet.Contract, DataTable))
            Viewer.LocalReport.DataSources.Add(ReportDataSource)
        End If

        ' Add the parameters to the report viewer's local report object.
        Viewer.LocalReport.SetParameters(ParamaterList)

        ' Set the display mode and display the report.
        Viewer.SetDisplayMode(Microsoft.Reporting.WinForms.DisplayMode.PrintLayout)
        Viewer.ZoomMode = ZoomMode.Percent
        Viewer.ZoomPercent = 150
        Viewer.RefreshReport()

    End Sub

    Public Sub RentalSubreportProcessingEventHandler _
    (ByVal sender As Object, ByVal e As SubreportProcessingEventArgs)

        ' Get the tables needed for the data of the subreport.
        Dim BillingInstructionTable As DataTable = ReportDataSet.BillingInstruction
        Dim BurstTable As DataTable = ReportDataSet.Burst
        Dim ContractInventoryTable As DataTable = ReportDataSet.ContractInventory
        Dim MiscellaneousChargeTable As DataTable = ReportDataSet.MiscellaneousCharge
        Dim ResearchCategoryTable As DataTable = ReportDataSet.ResearchCategory

        ' Process the subreports.
        Dim Subreport As String = e.ReportPath
        If Subreport = "PONumbers" Then
            ' The Billing Instruction subreport is being processed. Set the correct datasource.
            e.DataSources.Add(New ReportDataSource _
            ("DataSetContractReport_BillingInstruction", BillingInstructionTable))
        ElseIf Subreport = "Bursts" Then
            ' The Bursts subreport is being processed. Set the correct datasource.
            e.DataSources.Add(New ReportDataSource _
            ("DataSetContractReport_Burst", BurstTable))
        ElseIf Subreport = "Production" Then
            ' The Bursts subreport is being processed. Set the correct datasource.
            e.DataSources.Add(New ReportDataSource _
            ("DataSetContractReport_ContractInventory", ContractInventoryTable))
        ElseIf Subreport = "MiscellaneousCharges" Then
            ' The Bursts subreport is being processed. Set the correct datasource.
            e.DataSources.Add(New ReportDataSource _
            ("DataSetContractReport_MiscellaneousCharge", MiscellaneousChargeTable))
        ElseIf Subreport = "ResearchCategories" Then
            ' The Bursts subreport is being processed. Set the correct datasource.
            e.DataSources.Add(New ReportDataSource _
            ("ResearchCategoryDataSet", ResearchCategoryTable))
        End If

    End Sub

End Class