﻿

using DataAccess;

namespace DataService.Security.Roles
{

    internal class GetUserRolesCommandExecutor : CommandExecutor<GetUserRolesCommand>
    {
        public override void Execute(GetUserRolesCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.GetUserRoles))
            {
                storedprocedure.AddInputParameter("userid", command.Userid);

                storedprocedure.Execute();

                command.ErrorMessage = storedprocedure.ErrorMessage;

                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    command.Table = storedprocedure.OutputTable;
                }
            }
        }
    }
}
