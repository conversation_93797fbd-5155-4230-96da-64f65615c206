﻿using Framework.Surfaces;
using System;

namespace LegacyApps
{
    public partial class AppChooserSurface : Surface
    {
        public AppChooserSurface()
        {
            InitializeComponent();
            flatButtonSales.Click += FlatButtonNova2_Click;
            flatButtonOps.Click += FlatButtonNova2Ops_Click;
            flatButtonFinance.Click += FlatButtonNova2Finance_Click;
        }

        private void FlatButtonNova2Finance_Click(object sender, EventArgs e)
        {
            Legacy.LaunchApp("Nova2Finance", Universal.Settings.CurrentSession.LegacyConnectionString);
        }

        private void FlatButtonNova2Ops_Click(object sender, EventArgs e)
        {
            Legacy.LaunchApp("Nova2Ops", Universal.Settings.CurrentSession.LegacyConnectionString);
        }

        private void FlatButtonNova2_Click(object sender, EventArgs e)
        {
            Legacy.LaunchApp("Nova2", Universal.Settings.CurrentSession.LegacyConnectionString);
        }
    }
}
