﻿using System;
using System.Data.SqlClient;

namespace DataAccess
{
    public sealed class CommandExecutionManager : IDisposable
    {
        private SqlConnection Connection;
        private SqlConnection ConnectionForPreCommandActions;


        #region Startup

        public CommandExecutionManager(ref string errormessage, string connectionstring)
        {
            Connection = new SqlConnection(connectionstring);
            UpdateConnectionForPreCommandActions(connectionstring);
            OpenConnection(ref errormessage, Connection);
            OpenConnection(ref errormessage, ConnectionForPreCommandActions);
        }

        private void UpdateConnectionForPreCommandActions(string connectionstring)
        {
            SqlConnectionStringBuilder builderforprecommandactions = new SqlConnectionStringBuilder(connectionstring);
            if (builderforprecommandactions.InitialCatalog != Universal.Settings.DATABASENAME)
            {
                ConnectionForPreCommandActions = new SqlConnection(Universal.Settings.CurrentSession.ConnectionString);
            }
            else
            {
                ConnectionForPreCommandActions = Connection;
            }
        }

        private void OpenConnection(ref string errormessage, SqlConnection connection)
        {
            if (string.IsNullOrEmpty(errormessage) && connection != null)
            {
                if (connection.State == System.Data.ConnectionState.Closed)
                {
                    try
                    {
                        connection.Open();
                    }
                    catch (Exception ex)
                    {
                        errormessage = ex.Message;
                    }
                }
            }
        }

        #endregion


        #region Execute Command

        public void ExecuteCommand<TCommand>(ref string errormessage, TCommand command, CommandExecutor<TCommand> commandexecutor)
        {
            Execute(ref errormessage, command, commandexecutor, Connection);
        }

        private void Execute<TCommand>(ref string errormessage, TCommand command, CommandExecutor<TCommand> commandexecutor, SqlConnection connection)
        {
            if (string.IsNullOrEmpty(errormessage))
            {
                // Use the database connection of this command execution manager for the command executor object.
                commandexecutor.Connection = connection;

                // Execute the specified command, using the specified command executor.
                try
                {
                    commandexecutor.Execute(command);
                    // Record any error message that may have resulted when the executor executed the command.
                    Command executedcommand = command as Command;
                    errormessage = executedcommand.ErrorMessage;
                }
                catch (Exception ex)
                {
                    Command failedcommand = command as Command;
                    failedcommand.ErrorMessage = ex.Message;
                    errormessage = ex.Message;
                }
            }
        }

        #endregion


        #region Shutdown

        public void Dispose()
        {
            ConnectionForPreCommandActions?.Dispose();
            Connection?.Dispose();
        }

        #endregion

    }
}
