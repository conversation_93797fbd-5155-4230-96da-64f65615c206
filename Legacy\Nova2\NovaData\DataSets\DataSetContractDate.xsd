﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DataSetContractDate" targetNamespace="http://tempuri.org/DataSetContractDate.xsd" xmlns:mstns="http://tempuri.org/DataSetContractDate.xsd" xmlns="http://tempuri.org/DataSetContractDate.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="DBConnection" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Public" Name="DBConnection (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.NovaData.My.MySettings.GlobalReference.Default.DBConnection" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ContractDateTableAdapter" GeneratorDataComponentClassName="ContractDateTableAdapter" Name="ContractDate" UserDataComponentName="ContractDateTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Sales.ContractDate" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Sales].[ContractDate] WHERE (([ContractID] = @Original_ContractID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Sales].[ContractDate] ([ContractID], [PONumber], [StoreList], [Artwork], [InstallationInstructions]) VALUES (@ContractID, @PONumber, @StoreList, @Artwork, @InstallationInstructions);
SELECT ContractID, PONumber, StoreList, Artwork, InstallationInstructions FROM Sales.ContractDate WHERE (ContractID = @ContractID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@PONumber" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="PONumber" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@StoreList" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="StoreList" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Artwork" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="Artwork" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@InstallationInstructions" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="InstallationInstructions" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT        ContractID, PONumber, StoreList, Artwork, InstallationInstructions
FROM            Sales.ContractDate
WHERE        (ContractID = @ContractID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.ContractDate" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Sales].[ContractDate] SET [ContractID] = @ContractID, [PONumber] = @PONumber, [StoreList] = @StoreList, [Artwork] = @Artwork, [InstallationInstructions] = @InstallationInstructions WHERE (([ContractID] = @Original_ContractID));
SELECT ContractID, PONumber, StoreList, Artwork, InstallationInstructions FROM Sales.ContractDate WHERE (ContractID = @ContractID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@PONumber" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="PONumber" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@StoreList" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="StoreList" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Artwork" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="Artwork" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@InstallationInstructions" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="InstallationInstructions" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ContractID" DataSetColumn="ContractID" />
              <Mapping SourceColumn="PONumber" DataSetColumn="PONumber" />
              <Mapping SourceColumn="StoreList" DataSetColumn="StoreList" />
              <Mapping SourceColumn="Artwork" DataSetColumn="Artwork" />
              <Mapping SourceColumn="InstallationInstructions" DataSetColumn="InstallationInstructions" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ContractActivityDateTableAdapter" GeneratorDataComponentClassName="ContractActivityDateTableAdapter" Name="ContractActivityDate" UserDataComponentName="ContractActivityDateTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Sales.vContractActivityDate" DbObjectType="View" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT        ContractID, ContractNumber, PONumberCaptured, InstallationInstructionsCaptured, StoreListConfirmed
FROM            Sales.vContractActivityDate
WHERE        (ContractID = @ContractID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.vContractActivityDate" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ContractID" DataSetColumn="ContractID" />
              <Mapping SourceColumn="ContractNumber" DataSetColumn="ContractNumber" />
              <Mapping SourceColumn="PONumberCaptured" DataSetColumn="PONumberCaptured" />
              <Mapping SourceColumn="InstallationInstructionsCaptured" DataSetColumn="InstallationInstructionsCaptured" />
              <Mapping SourceColumn="StoreListConfirmed" DataSetColumn="StoreListConfirmed" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DataSetContractDate" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="DataSetContractDate" msprop:Generator_UserDSName="DataSetContractDate">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="ContractDate" msprop:Generator_TableClassName="ContractDateDataTable" msprop:Generator_TableVarName="tableContractDate" msprop:Generator_RowChangedName="ContractDateRowChanged" msprop:Generator_TablePropName="ContractDate" msprop:Generator_RowDeletingName="ContractDateRowDeleting" msprop:Generator_RowChangingName="ContractDateRowChanging" msprop:Generator_RowEvHandlerName="ContractDateRowChangeEventHandler" msprop:Generator_RowDeletedName="ContractDateRowDeleted" msprop:Generator_RowClassName="ContractDateRow" msprop:Generator_UserTableName="ContractDate" msprop:Generator_RowEvArgName="ContractDateRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ContractID" msdata:DataType="System.Guid, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnContractID" msprop:Generator_ColumnPropNameInRow="ContractID" msprop:Generator_ColumnPropNameInTable="ContractIDColumn" msprop:Generator_UserColumnName="ContractID" type="xs:string" />
              <xs:element name="PONumber" msprop:Generator_ColumnVarNameInTable="columnPONumber" msprop:Generator_ColumnPropNameInRow="PONumber" msprop:Generator_ColumnPropNameInTable="PONumberColumn" msprop:Generator_UserColumnName="PONumber" type="xs:dateTime" minOccurs="0" />
              <xs:element name="StoreList" msprop:Generator_ColumnVarNameInTable="columnStoreList" msprop:Generator_ColumnPropNameInRow="StoreList" msprop:Generator_ColumnPropNameInTable="StoreListColumn" msprop:Generator_UserColumnName="StoreList" type="xs:dateTime" minOccurs="0" />
              <xs:element name="Artwork" msprop:Generator_ColumnVarNameInTable="columnArtwork" msprop:Generator_ColumnPropNameInRow="Artwork" msprop:Generator_ColumnPropNameInTable="ArtworkColumn" msprop:Generator_UserColumnName="Artwork" type="xs:dateTime" minOccurs="0" />
              <xs:element name="InstallationInstructions" msprop:Generator_ColumnVarNameInTable="columnInstallationInstructions" msprop:Generator_ColumnPropNameInRow="InstallationInstructions" msprop:Generator_ColumnPropNameInTable="InstallationInstructionsColumn" msprop:Generator_UserColumnName="InstallationInstructions" type="xs:dateTime" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ContractActivityDate" msprop:Generator_TableClassName="ContractActivityDateDataTable" msprop:Generator_TableVarName="tableContractActivityDate" msprop:Generator_RowChangedName="ContractActivityDateRowChanged" msprop:Generator_TablePropName="ContractActivityDate" msprop:Generator_RowDeletingName="ContractActivityDateRowDeleting" msprop:Generator_RowChangingName="ContractActivityDateRowChanging" msprop:Generator_RowEvHandlerName="ContractActivityDateRowChangeEventHandler" msprop:Generator_RowDeletedName="ContractActivityDateRowDeleted" msprop:Generator_RowClassName="ContractActivityDateRow" msprop:Generator_UserTableName="ContractActivityDate" msprop:Generator_RowEvArgName="ContractActivityDateRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ContractID" msdata:DataType="System.Guid, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnContractID" msprop:Generator_ColumnPropNameInRow="ContractID" msprop:Generator_ColumnPropNameInTable="ContractIDColumn" msprop:Generator_UserColumnName="ContractID" type="xs:string" />
              <xs:element name="ContractNumber" msprop:Generator_ColumnVarNameInTable="columnContractNumber" msprop:Generator_ColumnPropNameInRow="ContractNumber" msprop:Generator_ColumnPropNameInTable="ContractNumberColumn" msprop:Generator_UserColumnName="ContractNumber">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="PONumberCaptured" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnPONumberCaptured" msprop:Generator_ColumnPropNameInRow="PONumberCaptured" msprop:Generator_ColumnPropNameInTable="PONumberCapturedColumn" msprop:Generator_UserColumnName="PONumberCaptured" type="xs:boolean" minOccurs="0" />
              <xs:element name="InstallationInstructionsCaptured" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnInstallationInstructionsCaptured" msprop:Generator_ColumnPropNameInRow="InstallationInstructionsCaptured" msprop:Generator_ColumnPropNameInTable="InstallationInstructionsCapturedColumn" msprop:Generator_UserColumnName="InstallationInstructionsCaptured" type="xs:boolean" minOccurs="0" />
              <xs:element name="StoreListConfirmed" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnStoreListConfirmed" msprop:Generator_ColumnPropNameInRow="StoreListConfirmed" msprop:Generator_ColumnPropNameInTable="StoreListConfirmedColumn" msprop:Generator_UserColumnName="StoreListConfirmed" type="xs:boolean" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ContractDate" />
      <xs:field xpath="mstns:ContractID" />
    </xs:unique>
    <xs:unique name="ContractActivityDate_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ContractActivityDate" />
      <xs:field xpath="mstns:ContractID" />
    </xs:unique>
  </xs:element>
</xs:schema>