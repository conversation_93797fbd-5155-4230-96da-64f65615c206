﻿using DataAccess;
using System;
using System.Data;

namespace DataService.Security
{
    internal class GetRolesOfOwnerCandidatesCommand : Command
    {
        public Guid SessionId;
        public Guid UserId;
        public DataTable Table;

        public GetRolesOfOwnerCandidatesCommand(Guid sessionid, Guid userid)
        {
            SessionId = sessionid;
            UserId = userid;
        }
    }
}
