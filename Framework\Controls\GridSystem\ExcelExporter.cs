﻿using OfficeOpenXml;
using OfficeOpenXml.Style;
using System;
using System.Drawing;
using System.IO;
using System.Text;
using System.Windows.Forms;

namespace Framework.Controls.GridSystem
{
    public class ExcelExporter
    {
        Grid GridToExport;
        SaveFileDialog ExportSaveFileDialog = new SaveFileDialog();
        string NameOfUser = "the user";


        public ExcelExporter(string nameofuser)
        {
            NameOfUser = nameofuser;
            ExportSaveFileDialog.DefaultExt = "xlsx";
            ExportSaveFileDialog.Filter = "Excel Workbook|*.xlsx|All files (*.*)|*.*";
            ExportSaveFileDialog.FileOk += ExportSaveFileDialog_FileOk;
        }

        public void Export(Grid gridtoexport)
        {
            if (gridtoexport.DataSource == null)
            {
                MessageForm.Show("There is no data in this grid. No export can be done.", "No Data To Export");
            }
            else
            {
                GridToExport = gridtoexport;
                ExportSaveFileDialog.ShowDialog();
            }
        }

        private void ExportSaveFileDialog_FileOk(object sender, System.ComponentModel.CancelEventArgs e)
        {
            SaveExcelFile();
        }

        private void SaveExcelFile()
        {
            string pathandfilename = ExportSaveFileDialog.FileName;
            FileInfo newFile = new FileInfo(pathandfilename);
            if (newFile.Exists)
            {
                try
                {
                    // Ensure that a new workbook is created.
                    newFile.Delete();
                }
                catch (IOException ex)
                {
                    MessageForm.Show(ex.Message);
                    return;
                }
                newFile = new FileInfo(pathandfilename);
            }

            using (ExcelPackage package = new ExcelPackage(newFile))
            {
                string worksheetname = GridToExport.Title == null ? "Data Export" : GridToExport.Title;
                ExcelWorksheet worksheet = GetNewWorksheet(package, worksheetname);

                int headingsrowindex = 3;

                CreateWorksheetTitleAndHeadings(worksheet, headingsrowindex, worksheetname);
                PopulateExcelWorksheetUsingGridData(worksheet, headingsrowindex);
                MakeTheWorksheetLookPretty(worksheet, headingsrowindex);
                SetPrinterSettings(worksheet, headingsrowindex);
                CreateWorksheetFooter(worksheet);
                SetWorkbookProperties(package.Workbook, worksheet.Name);
                package.Save();
            }

        }

        private ExcelWorksheet GetNewWorksheet(ExcelPackage package, string worksheetname)
        {
            ExcelWorksheet worksheet = package.Workbook.Worksheets.Add(worksheetname);
            worksheet.Cells.Style.Font.Name = "Verdana";
            worksheet.Cells.Style.Font.Size = 8;

            return worksheet;
        }

        private void CreateWorksheetTitleAndHeadings(ExcelWorksheet worksheet, int headingsrowindex, string worksheetname)
        {

            // Add a title.
            if (headingsrowindex > 1)
            {
                // There is space for a title.  Add one.
                ExcelRange titlecell = worksheet.Cells[1, 1];
                titlecell.Value = worksheetname;

                // Format the title.
                titlecell.Style.Font.Bold = true;
                titlecell.Style.Font.Name = "Arial";
                titlecell.Style.Font.Size = 22;
            }

            // Add the text into the headings row of the worksheet.
            for (int gridcolumnindex = 0; gridcolumnindex < GridToExport.Columns.Count; gridcolumnindex++)
            {
                worksheet.Cells[headingsrowindex, gridcolumnindex + 1].Value = GridToExport.Columns[gridcolumnindex].HeaderText;
            }

            // Format the headings row of the worksheet.
            ExcelRange headercells = worksheet.Cells[headingsrowindex, 1, headingsrowindex, GridToExport.Columns.Count];
            worksheet.Row(headingsrowindex).Height = 20;
            headercells.Style.VerticalAlignment = ExcelVerticalAlignment.Center;
            headercells.Style.Font.Bold = true;
            headercells.Style.Fill.PatternType = ExcelFillStyle.Solid;

            Color backgroundcolor = FrameworkSettings.Colors.ThemeColor;
            headercells.Style.Fill.BackgroundColor.SetColor(backgroundcolor);
            headercells.Style.Font.Color.SetColor(FrameworkMethods.Methods.GetBestForeColor(backgroundcolor));

            headercells.AutoFilter = true;

        }

        /// <summary>
        /// Duplicate the data in the grid into the specified worksheet.
        /// </summary>
        /// <param name="worksheet">The target worksheet which will be populated with grid data.</param>
        private void PopulateExcelWorksheetUsingGridData(ExcelWorksheet worksheet, int headingsrowindex)
        {
            // Populate the worksheet with data from the grid's cells.
            for (int i = 0; i < GridToExport.Rows.Count; i++)
            {
                for (int j = 0; j < GridToExport.Columns.Count; j++)
                {
                    worksheet.Cells[i + headingsrowindex + 1, j + 1].Value = GridToExport[j, i].Value;
                }
                if (i == GridToExport.Rows.Count - 1)
                {
                    // This is the last row. Let's add a nice border to the bottom of the row.
                    int lastrowindex = i + headingsrowindex + 1;
                    ExcelRange lastrow = worksheet.Cells[lastrowindex, 1, lastrowindex, GridToExport.Columns.Count];
                    lastrow.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
                }
            }
        }

        private void MakeTheWorksheetLookPretty(ExcelWorksheet worksheet, int headingsrowindex)
        {
            worksheet.View.ShowGridLines = false;

            // Autofit the columns
            worksheet.Cells[headingsrowindex, 1, headingsrowindex + GridToExport.Rows.Count, GridToExport.Columns.Count].AutoFitColumns(9, 80);

            for (int i = 1; i < GridToExport.Columns.Count + 1; i++)
            {
                var gridcolumn = GridToExport.Columns[i - 1];
                var worksheetcolumn = worksheet.Column(i);

                // Add a bit more width because autofit doesn't make it quite wide enough.
                worksheetcolumn.Width += 4;

                // Format each column to match the corresponding column in the grid.
                if (GridToExport.DataSource.Columns[gridcolumn.DataPropertyName].DataType == typeof(DateTime))
                {
                    worksheetcolumn.Style.Numberformat.Format = "dd-MM-yyyy HH:mm";
                }
                if (gridcolumn.DefaultCellStyle.Format == "C")
                {
                    worksheetcolumn.Style.Numberformat.Format = "R #,##0.00";
                }

                // Setup alignment for the column.
                if (gridcolumn.DefaultCellStyle.Alignment == DataGridViewContentAlignment.BottomRight
                    | gridcolumn.DefaultCellStyle.Alignment == DataGridViewContentAlignment.MiddleRight
                    | gridcolumn.DefaultCellStyle.Alignment == DataGridViewContentAlignment.TopRight)
                {
                    worksheetcolumn.Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;
                }
                else
                {
                    if (gridcolumn.DefaultCellStyle.Alignment == DataGridViewContentAlignment.BottomCenter
                    | gridcolumn.DefaultCellStyle.Alignment == DataGridViewContentAlignment.MiddleCenter
                    | gridcolumn.DefaultCellStyle.Alignment == DataGridViewContentAlignment.TopCenter)
                    {
                        worksheetcolumn.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                    }
                    else
                    {
                        worksheetcolumn.Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
                    }
                }
            }
        }

        private void SetPrinterSettings(ExcelWorksheet worksheet, int headingsrowindex)
        {
            worksheet.PrinterSettings.BlackAndWhite = true;
            worksheet.PrinterSettings.PaperSize = ePaperSize.A4;
            worksheet.PrinterSettings.RepeatRows = worksheet.Cells[headingsrowindex.ToString() + ":" + headingsrowindex.ToString()];
        }

        private void CreateWorksheetFooter(ExcelWorksheet worksheet)
        {
            // Formatting options
            string formattingoptions = "&7&\"Verdana,Bold\"";

            // add the page number to the footer plus the total number of pages
            string rightfooter = string.Format("Page {0} of {1}", ExcelHeaderFooter.PageNumber, ExcelHeaderFooter.NumberOfPages);
            worksheet.HeaderFooter.OddFooter.RightAlignedText = formattingoptions + rightfooter.ToUpper();

            // add the file path to the footer
            var leftfooterbuilder = new StringBuilder();
            leftfooterbuilder.Append("Exported from " + Application.ProductName + " " + Application.ProductVersion);
            leftfooterbuilder.Append(" by " + NameOfUser);
            leftfooterbuilder.Append(" on " + DateTime.Now.ToLongDateString());
            leftfooterbuilder.Append(" at " + DateTime.Now.ToShortTimeString());
            string leftfooter = leftfooterbuilder.ToString();
            worksheet.HeaderFooter.OddFooter.LeftAlignedText = formattingoptions + leftfooter.ToUpper();
        }

        private void SetWorkbookProperties(ExcelWorkbook Workbook, string workbooktitle)
        {
            Workbook.Properties.Title = workbooktitle;
            Workbook.Properties.Company = FrameworkSettings.Strings.COMPANYNAME;
            Workbook.Properties.Author = Application.ProductName + " " + Application.ProductVersion;
            Workbook.Properties.Comments = "This file was created by " + NameOfUser + " by means of the "
                + Application.ProductName + " application.";
        }
    }
}
