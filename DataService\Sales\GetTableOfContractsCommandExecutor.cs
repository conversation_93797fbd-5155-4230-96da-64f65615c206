﻿using DataAccess;

namespace DataService.Sales
{
    class GetTableOfContractsCommandExecutor : CommandExecutor<GetTableOfContractsCommand>
    {
        public override void Execute(GetTableOfContractsCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.GetContractTable))
            {
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    command.Table = storedprocedure.OutputTable;
                }
            }
        }
    }
}
