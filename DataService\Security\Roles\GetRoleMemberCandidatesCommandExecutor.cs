﻿using DataAccess;

namespace DataService.Security
{
    internal class GetRoleMemberCandidatesCommandExecutor : CommandExecutor<GetRoleMemberCandidatesCommand>
    {
        public override void Execute(GetRoleMemberCandidatesCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.GetRoleMemberCandidates))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.AddInputParameter("roleid", command.RoleId);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    command.Table = storedprocedure.OutputTable;
                }
            }
        }
    }
}
