Public Class SubformBrand

    Private DataObject As Brand

#Region "Event Handlers"

    Private Sub Subform_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        ' Load values for other controls.
        TextEditBrandName.EditValue = DataObject.BrandName
        CheckEditDormant.Checked = DataObject.Dormant

        ' Load grid data.
        GridClientBrand.AutoGenerateColumns = False
        GridClientBrand.DataSource = DataObject.ClientBrandBindingSource

        ' Configure grid managers.
        Dim GridManagerClientBrand As New GridManager(GridClientBrand, TextEditSearchClientBrand, Nothing, Nothing, _
        PictureAdvancedSearchClientBrand, PictureClearSearchClientBrand, Nothing, ButtonRemoveClientBrand)

    End Sub

    Public Sub New(ByVal BrandObject As Brand)
        ' This call is required by the Windows Form Designer.
        InitializeComponent()
        ' Add any initialization after the InitializeComponent() call.
        DataObject = BrandObject
    End Sub

    Private Sub ButtonSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonSave.Click
        Dim CurrentControl As TextEdit = CType(TextEditBrandName, TextEdit)
        'If CheckIfBrandExists(CurrentControl) = True Then
        '    LiquidAgent.ControlValidation(TextEditBrandName, "A brand with that name already exists, please use a different name.")
        '    Exit Sub
        'End If
        ' Save and close.
        Save(True)

    End Sub

    Private Sub ButtonCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonCancel.Click
        DataObject.Close(Me)
    End Sub

    Private Sub TextEditBrandName_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles TextEditBrandName.EditValueChanged

        ' Create an object to represent the control. 'BvZ Test
        Dim CurrentControl As TextEdit = CType(sender, TextEdit)

        ' Update the form title as the user types a name.
        If Not String.IsNullOrEmpty(CurrentControl.Text) Then
            LabelTitle.Text = CurrentControl.Text
        End If

        ' Reset the error text of the control to an empty string.
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)

    End Sub

    Private Sub TextEditBrandName_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles TextEditBrandName.Validated
        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)
        ' Check for errors.
        If ValidatedControl.Text.Length = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "The name of the brand may not be blank.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

        ' Update the data object.
        DataObject.BrandName = CType(sender, TextEdit).EditValue

    End Sub

    Private Sub CheckEditDormant_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles CheckEditDormant.Validated
        ' Update the data object.
        DataObject.Dormant = CType(sender, CheckEdit).Checked
    End Sub

    Private Sub ButtonAddClientBrand_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonAddClientBrand.Click

        ' Save first if required.
        If LiquidShell.LiquidAgent.SaveAndProceed(DataObject.Row, Me, My.Settings.DBConnection, False) = False Then
            Exit Sub
        End If

        ' Get a selection of data rows from the user and add the selected items.
        Dim SelectedItems As List(Of DataRow) = LookupClient.SelectRowsByPermission_EditMyClients _
        (My.Settings.DBConnection, True, GridClientBrand)
        DataObject.AddClientBrand(Me, My.Settings.DBConnection, SelectedItems)

    End Sub

    Private Sub ButtonDeleteClientBrand_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonRemoveClientBrand.Click
        DataObject.DeleteChildRow(GridClientBrand, "BrandName")
    End Sub

#End Region

#Region "Methods"

    Protected Overrides Function Save() As Boolean

        ' Get the grids that need to be audited. These will be passed into the Save method.
        Dim GridsToAudit As New List(Of DataGridView)
        GridsToAudit.Add(GridClientBrand)

        ' Proceed with the save.
        DataObject.Save(My.Settings.DBConnection, GridsToAudit)
        Return True

    End Function


    Private Function CheckIfBrandExists(ByVal ValidatedControl As TextEdit) As Boolean
        Dim ErrorMessage As String = String.Empty
        Dim GridData As BindingSource = Lookup.GetBrands(My.Settings.DBConnection, ErrorMessage, Nothing)
        Dim dt As DataTable = Lookup.CheckIfBrandExists(My.Settings.DBConnection, ErrorMessage)

        For Each row As DataRow In dt.Rows
            If row.Item("BrandName").ToString().Trim().ToLower() = ValidatedControl.Text.Trim().ToLower() Then

                Return True
            End If
        Next
        Return False
    End Function

#End Region

End Class
