<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformProvisionalBooking
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformProvisionalBooking))
        Dim SuperToolTip1 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip
        Dim ToolTipTitleItem1 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem
        Dim ToolTipItem1 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem
        Dim SuperToolTip2 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip
        Dim ToolTipTitleItem2 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem
        Dim ToolTipItem2 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle
        Dim SuperToolTip3 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip
        Dim ToolTipTitleItem3 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem
        Dim ToolTipItem3 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem
        Dim SuperToolTip4 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip
        Dim ToolTipTitleItem4 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem
        Dim ToolTipItem4 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem
        Dim DataGridViewCellStyle4 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle
        Dim DataGridViewCellStyle5 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle
        Dim DataGridViewCellStyle6 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle
        Dim SuperToolTip5 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip
        Dim ToolTipTitleItem5 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem
        Dim ToolTipItem5 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem
        Dim SuperToolTip6 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip
        Dim ToolTipTitleItem6 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem
        Dim ToolTipItem6 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem
        Dim DataGridViewCellStyle7 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle
        Dim DataGridViewCellStyle8 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle
        Dim DataGridViewCellStyle9 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle
        Me.HyperlinkBrand = New DevExpress.XtraEditors.LabelControl
        Me.TextEditProvisionalBookingName = New DevExpress.XtraEditors.TextEdit
        Me.LabelProvisionalBookingName = New DevExpress.XtraEditors.LabelControl
        Me.LabelFirstWeek = New DevExpress.XtraEditors.LabelControl
        Me.LabelLastWeek = New DevExpress.XtraEditors.LabelControl
        Me.LabelBrand = New DevExpress.XtraEditors.LabelControl
        Me.TextEditWeeks = New DevExpress.XtraEditors.TextEdit
        Me.LabelWeeks = New DevExpress.XtraEditors.LabelControl
        Me.HyperlinkFirstWeek = New DevExpress.XtraEditors.LabelControl
        Me.HyperlinkLastWeek = New DevExpress.XtraEditors.LabelControl
        Me.ImageList16x16 = New System.Windows.Forms.ImageList(Me.components)
        Me.GroupControlMediaFamilies = New DevExpress.XtraEditors.GroupControl
        Me.LabelControl10 = New DevExpress.XtraEditors.LabelControl
        Me.ButtonAddMediaFamily = New DevExpress.XtraEditors.SimpleButton
        Me.ButtonRemoveMediaFamily = New DevExpress.XtraEditors.SimpleButton
        Me.TextEditSearchMediaFamilies = New DevExpress.XtraEditors.TextEdit
        Me.PictureClearSearchMediaFamilies = New DevExpress.XtraEditors.PictureEdit
        Me.PictureSearchMediaFamilies = New DevExpress.XtraEditors.PictureEdit
        Me.GridMediaFamilies = New System.Windows.Forms.DataGridView
        Me.MediaFamilyNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn
        Me.GroupControlChains = New DevExpress.XtraEditors.GroupControl
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl
        Me.ButtonAddChain = New DevExpress.XtraEditors.SimpleButton
        Me.ButtonRemoveChain = New DevExpress.XtraEditors.SimpleButton
        Me.TextEditSearchChains = New DevExpress.XtraEditors.TextEdit
        Me.PictureClearSearchChains = New DevExpress.XtraEditors.PictureEdit
        Me.PictureSearchChains = New DevExpress.XtraEditors.PictureEdit
        Me.GridChains = New System.Windows.Forms.DataGridView
        Me.ChainNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn
        Me.GroupControlCategories = New DevExpress.XtraEditors.GroupControl
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl
        Me.ButtonAddCategory = New DevExpress.XtraEditors.SimpleButton
        Me.ButtonRemoveCategory = New DevExpress.XtraEditors.SimpleButton
        Me.TextEditSearchCategories = New DevExpress.XtraEditors.TextEdit
        Me.PictureClearSearchCategories = New DevExpress.XtraEditors.PictureEdit
        Me.PictureSearchCategories = New DevExpress.XtraEditors.PictureEdit
        Me.GridCategories = New System.Windows.Forms.DataGridView
        Me.CategoryNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonSave = New DevExpress.XtraEditors.SimpleButton
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton
        CType(Me.TextEditProvisionalBookingName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditWeeks.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControlMediaFamilies, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlMediaFamilies.SuspendLayout()
        CType(Me.TextEditSearchMediaFamilies.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearchMediaFamilies.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureSearchMediaFamilies.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridMediaFamilies, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControlChains, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlChains.SuspendLayout()
        CType(Me.TextEditSearchChains.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearchChains.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureSearchChains.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridChains, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControlCategories, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlCategories.SuspendLayout()
        CType(Me.TextEditSearchCategories.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearchCategories.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureSearchCategories.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridCategories, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!)
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Size = New System.Drawing.Size(229, 30)
        Me.LabelTitle.TabIndex = 0
        Me.LabelTitle.Text = "Provisional Booking"
        '
        'HyperlinkBrand
        '
        Me.HyperlinkBrand.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkBrand.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkBrand.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkBrand.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkBrand.Location = New System.Drawing.Point(116, 60)
        Me.HyperlinkBrand.Margin = New System.Windows.Forms.Padding(3, 3, 3, 6)
        Me.HyperlinkBrand.Name = "HyperlinkBrand"
        Me.HyperlinkBrand.Size = New System.Drawing.Size(47, 13)
        Me.HyperlinkBrand.TabIndex = 2
        Me.HyperlinkBrand.Text = "Select..."
        '
        'TextEditProvisionalBookingName
        '
        Me.TextEditProvisionalBookingName.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditProvisionalBookingName.Location = New System.Drawing.Point(116, 161)
        Me.TextEditProvisionalBookingName.Name = "TextEditProvisionalBookingName"
        Me.TextEditProvisionalBookingName.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditProvisionalBookingName.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditProvisionalBookingName.Properties.Appearance.Options.UseFont = True
        Me.TextEditProvisionalBookingName.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditProvisionalBookingName.Properties.MaxLength = 200
        Me.TextEditProvisionalBookingName.Size = New System.Drawing.Size(287, 20)
        Me.TextEditProvisionalBookingName.TabIndex = 10
        '
        'LabelProvisionalBookingName
        '
        Me.LabelProvisionalBookingName.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelProvisionalBookingName.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelProvisionalBookingName.Location = New System.Drawing.Point(12, 164)
        Me.LabelProvisionalBookingName.Name = "LabelProvisionalBookingName"
        Me.LabelProvisionalBookingName.Size = New System.Drawing.Size(69, 13)
        Me.LabelProvisionalBookingName.TabIndex = 9
        Me.LabelProvisionalBookingName.Text = "Description:"
        '
        'LabelFirstWeek
        '
        Me.LabelFirstWeek.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelFirstWeek.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelFirstWeek.Location = New System.Drawing.Point(12, 86)
        Me.LabelFirstWeek.Name = "LabelFirstWeek"
        Me.LabelFirstWeek.Size = New System.Drawing.Size(65, 13)
        Me.LabelFirstWeek.TabIndex = 3
        Me.LabelFirstWeek.Text = "First Week:"
        '
        'LabelLastWeek
        '
        Me.LabelLastWeek.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelLastWeek.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelLastWeek.Location = New System.Drawing.Point(12, 112)
        Me.LabelLastWeek.Name = "LabelLastWeek"
        Me.LabelLastWeek.Size = New System.Drawing.Size(64, 13)
        Me.LabelLastWeek.TabIndex = 5
        Me.LabelLastWeek.Text = "Last Week:"
        '
        'LabelBrand
        '
        Me.LabelBrand.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelBrand.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelBrand.Location = New System.Drawing.Point(12, 60)
        Me.LabelBrand.Name = "LabelBrand"
        Me.LabelBrand.Size = New System.Drawing.Size(39, 13)
        Me.LabelBrand.TabIndex = 1
        Me.LabelBrand.Text = "Brand:"
        '
        'TextEditWeeks
        '
        Me.TextEditWeeks.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditWeeks.EditValue = 1
        Me.TextEditWeeks.Location = New System.Drawing.Point(116, 135)
        Me.TextEditWeeks.Name = "TextEditWeeks"
        Me.TextEditWeeks.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditWeeks.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditWeeks.Properties.Appearance.Options.UseFont = True
        Me.TextEditWeeks.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditWeeks.Properties.Mask.EditMask = "n0"
        Me.TextEditWeeks.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditWeeks.Properties.MaxLength = 3
        Me.TextEditWeeks.Size = New System.Drawing.Size(41, 20)
        Me.TextEditWeeks.TabIndex = 8
        '
        'LabelWeeks
        '
        Me.LabelWeeks.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelWeeks.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelWeeks.Location = New System.Drawing.Point(12, 138)
        Me.LabelWeeks.Name = "LabelWeeks"
        Me.LabelWeeks.Size = New System.Drawing.Size(75, 13)
        Me.LabelWeeks.TabIndex = 7
        Me.LabelWeeks.Text = "Total Weeks:"
        '
        'HyperlinkFirstWeek
        '
        Me.HyperlinkFirstWeek.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkFirstWeek.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkFirstWeek.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkFirstWeek.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkFirstWeek.Location = New System.Drawing.Point(116, 86)
        Me.HyperlinkFirstWeek.Margin = New System.Windows.Forms.Padding(3, 3, 3, 6)
        Me.HyperlinkFirstWeek.Name = "HyperlinkFirstWeek"
        Me.HyperlinkFirstWeek.Size = New System.Drawing.Size(47, 13)
        Me.HyperlinkFirstWeek.TabIndex = 4
        Me.HyperlinkFirstWeek.Text = "Select..."
        '
        'HyperlinkLastWeek
        '
        Me.HyperlinkLastWeek.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkLastWeek.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkLastWeek.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkLastWeek.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkLastWeek.Location = New System.Drawing.Point(116, 112)
        Me.HyperlinkLastWeek.Margin = New System.Windows.Forms.Padding(3, 3, 3, 6)
        Me.HyperlinkLastWeek.Name = "HyperlinkLastWeek"
        Me.HyperlinkLastWeek.Size = New System.Drawing.Size(47, 13)
        Me.HyperlinkLastWeek.TabIndex = 6
        Me.HyperlinkLastWeek.Text = "Select..."
        '
        'ImageList16x16
        '
        Me.ImageList16x16.ImageStream = CType(resources.GetObject("ImageList16x16.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList16x16.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList16x16.Images.SetKeyName(0, "add.png")
        Me.ImageList16x16.Images.SetKeyName(1, "delete.png")
        '
        'GroupControlMediaFamilies
        '
        Me.GroupControlMediaFamilies.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlMediaFamilies.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlMediaFamilies.Appearance.Options.UseFont = True
        Me.GroupControlMediaFamilies.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlMediaFamilies.AppearanceCaption.Options.UseFont = True
        Me.GroupControlMediaFamilies.Controls.Add(Me.LabelControl10)
        Me.GroupControlMediaFamilies.Controls.Add(Me.ButtonAddMediaFamily)
        Me.GroupControlMediaFamilies.Controls.Add(Me.ButtonRemoveMediaFamily)
        Me.GroupControlMediaFamilies.Controls.Add(Me.TextEditSearchMediaFamilies)
        Me.GroupControlMediaFamilies.Controls.Add(Me.PictureClearSearchMediaFamilies)
        Me.GroupControlMediaFamilies.Controls.Add(Me.PictureSearchMediaFamilies)
        Me.GroupControlMediaFamilies.Controls.Add(Me.GridMediaFamilies)
        Me.GroupControlMediaFamilies.Location = New System.Drawing.Point(0, 0)
        Me.GroupControlMediaFamilies.LookAndFeel.SkinName = "Black"
        Me.GroupControlMediaFamilies.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlMediaFamilies.Margin = New System.Windows.Forms.Padding(0)
        Me.GroupControlMediaFamilies.Name = "GroupControlMediaFamilies"
        Me.GroupControlMediaFamilies.Size = New System.Drawing.Size(391, 137)
        Me.GroupControlMediaFamilies.TabIndex = 0
        Me.GroupControlMediaFamilies.Text = "Media Families to Book"
        '
        'LabelControl10
        '
        Me.LabelControl10.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl10.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl10.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl10.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl10.Location = New System.Drawing.Point(233, 113)
        Me.LabelControl10.Name = "LabelControl10"
        Me.LabelControl10.Size = New System.Drawing.Size(45, 13)
        Me.LabelControl10.TabIndex = 4
        Me.LabelControl10.Text = "Search:"
        '
        'ButtonAddMediaFamily
        '
        Me.ButtonAddMediaFamily.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAddMediaFamily.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAddMediaFamily.Appearance.Options.UseFont = True
        Me.ButtonAddMediaFamily.ImageIndex = 0
        Me.ButtonAddMediaFamily.ImageList = Me.ImageList16x16
        Me.ButtonAddMediaFamily.Location = New System.Drawing.Point(5, 109)
        Me.ButtonAddMediaFamily.LookAndFeel.SkinName = "Black"
        Me.ButtonAddMediaFamily.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAddMediaFamily.Name = "ButtonAddMediaFamily"
        Me.ButtonAddMediaFamily.Size = New System.Drawing.Size(75, 23)
        Me.ButtonAddMediaFamily.TabIndex = 2
        Me.ButtonAddMediaFamily.Text = "Add"
        '
        'ButtonRemoveMediaFamily
        '
        Me.ButtonRemoveMediaFamily.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonRemoveMediaFamily.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonRemoveMediaFamily.Appearance.Options.UseFont = True
        Me.ButtonRemoveMediaFamily.ImageIndex = 1
        Me.ButtonRemoveMediaFamily.ImageList = Me.ImageList16x16
        Me.ButtonRemoveMediaFamily.Location = New System.Drawing.Point(86, 109)
        Me.ButtonRemoveMediaFamily.LookAndFeel.SkinName = "Black"
        Me.ButtonRemoveMediaFamily.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonRemoveMediaFamily.Name = "ButtonRemoveMediaFamily"
        Me.ButtonRemoveMediaFamily.Size = New System.Drawing.Size(75, 23)
        Me.ButtonRemoveMediaFamily.TabIndex = 3
        Me.ButtonRemoveMediaFamily.Text = "Remove"
        '
        'TextEditSearchMediaFamilies
        '
        Me.TextEditSearchMediaFamilies.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchMediaFamilies.EditValue = ""
        Me.TextEditSearchMediaFamilies.Location = New System.Drawing.Point(284, 110)
        Me.TextEditSearchMediaFamilies.Margin = New System.Windows.Forms.Padding(3, 4, 3, 5)
        Me.TextEditSearchMediaFamilies.Name = "TextEditSearchMediaFamilies"
        Me.TextEditSearchMediaFamilies.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchMediaFamilies.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchMediaFamilies.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchMediaFamilies.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchMediaFamilies.Size = New System.Drawing.Size(80, 20)
        Me.TextEditSearchMediaFamilies.TabIndex = 5
        '
        'PictureClearSearchMediaFamilies
        '
        Me.PictureClearSearchMediaFamilies.AllowDrop = True
        Me.PictureClearSearchMediaFamilies.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchMediaFamilies.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchMediaFamilies.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchMediaFamilies.Location = New System.Drawing.Point(370, 3)
        Me.PictureClearSearchMediaFamilies.Name = "PictureClearSearchMediaFamilies"
        Me.PictureClearSearchMediaFamilies.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchMediaFamilies.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchMediaFamilies.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchMediaFamilies.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem1.Text = "Clear Search"
        ToolTipItem1.LeftIndent = 6
        ToolTipItem1.Text = "Click here to clear all search boxes."
        SuperToolTip1.Items.Add(ToolTipTitleItem1)
        SuperToolTip1.Items.Add(ToolTipItem1)
        Me.PictureClearSearchMediaFamilies.SuperTip = SuperToolTip1
        Me.PictureClearSearchMediaFamilies.TabIndex = 0
        Me.PictureClearSearchMediaFamilies.TabStop = True
        '
        'PictureSearchMediaFamilies
        '
        Me.PictureSearchMediaFamilies.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureSearchMediaFamilies.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureSearchMediaFamilies.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureSearchMediaFamilies.Location = New System.Drawing.Point(370, 112)
        Me.PictureSearchMediaFamilies.Margin = New System.Windows.Forms.Padding(3, 6, 3, 7)
        Me.PictureSearchMediaFamilies.Name = "PictureSearchMediaFamilies"
        Me.PictureSearchMediaFamilies.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureSearchMediaFamilies.Properties.Appearance.Options.UseBackColor = True
        Me.PictureSearchMediaFamilies.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureSearchMediaFamilies.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem2.Text = "Advanced Search"
        ToolTipItem2.LeftIndent = 6
        ToolTipItem2.Text = "Click here to search individual column values."
        SuperToolTip2.Items.Add(ToolTipTitleItem2)
        SuperToolTip2.Items.Add(ToolTipItem2)
        Me.PictureSearchMediaFamilies.SuperTip = SuperToolTip2
        Me.PictureSearchMediaFamilies.TabIndex = 6
        Me.PictureSearchMediaFamilies.TabStop = True
        '
        'GridMediaFamilies
        '
        Me.GridMediaFamilies.AllowUserToAddRows = False
        Me.GridMediaFamilies.AllowUserToDeleteRows = False
        Me.GridMediaFamilies.AllowUserToOrderColumns = True
        Me.GridMediaFamilies.AllowUserToResizeRows = False
        DataGridViewCellStyle1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridMediaFamilies.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle1
        Me.GridMediaFamilies.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridMediaFamilies.BackgroundColor = System.Drawing.Color.White
        Me.GridMediaFamilies.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridMediaFamilies.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridMediaFamilies.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle2.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle2.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridMediaFamilies.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle2
        Me.GridMediaFamilies.ColumnHeadersHeight = 22
        Me.GridMediaFamilies.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridMediaFamilies.ColumnHeadersVisible = False
        Me.GridMediaFamilies.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.MediaFamilyNameColumn})
        Me.GridMediaFamilies.EnableHeadersVisualStyles = False
        Me.GridMediaFamilies.GridColor = System.Drawing.Color.White
        Me.GridMediaFamilies.Location = New System.Drawing.Point(2, 22)
        Me.GridMediaFamilies.Name = "GridMediaFamilies"
        Me.GridMediaFamilies.ReadOnly = True
        Me.GridMediaFamilies.RowHeadersVisible = False
        DataGridViewCellStyle3.ForeColor = System.Drawing.Color.DimGray
        Me.GridMediaFamilies.RowsDefaultCellStyle = DataGridViewCellStyle3
        Me.GridMediaFamilies.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridMediaFamilies.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridMediaFamilies.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridMediaFamilies.RowTemplate.Height = 19
        Me.GridMediaFamilies.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridMediaFamilies.ShowCellToolTips = False
        Me.GridMediaFamilies.Size = New System.Drawing.Size(387, 81)
        Me.GridMediaFamilies.StandardTab = True
        Me.GridMediaFamilies.TabIndex = 1
        '
        'MediaFamilyNameColumn
        '
        Me.MediaFamilyNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.MediaFamilyNameColumn.DataPropertyName = "MediaFamilyName"
        Me.MediaFamilyNameColumn.HeaderText = "Media Family"
        Me.MediaFamilyNameColumn.Name = "MediaFamilyNameColumn"
        Me.MediaFamilyNameColumn.ReadOnly = True
        '
        'GroupControlChains
        '
        Me.GroupControlChains.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlChains.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlChains.Appearance.Options.UseFont = True
        Me.GroupControlChains.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlChains.AppearanceCaption.Options.UseFont = True
        Me.GroupControlChains.Controls.Add(Me.LabelControl1)
        Me.GroupControlChains.Controls.Add(Me.ButtonAddChain)
        Me.GroupControlChains.Controls.Add(Me.ButtonRemoveChain)
        Me.GroupControlChains.Controls.Add(Me.TextEditSearchChains)
        Me.GroupControlChains.Controls.Add(Me.PictureClearSearchChains)
        Me.GroupControlChains.Controls.Add(Me.PictureSearchChains)
        Me.GroupControlChains.Controls.Add(Me.GridChains)
        Me.GroupControlChains.Location = New System.Drawing.Point(406, 0)
        Me.GroupControlChains.LookAndFeel.SkinName = "Black"
        Me.GroupControlChains.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlChains.Margin = New System.Windows.Forms.Padding(0)
        Me.GroupControlChains.Name = "GroupControlChains"
        Me.TableLayoutPanel1.SetRowSpan(Me.GroupControlChains, 3)
        Me.GroupControlChains.Size = New System.Drawing.Size(391, 289)
        Me.GroupControlChains.TabIndex = 2
        Me.GroupControlChains.Text = "Chains to Book"
        '
        'LabelControl1
        '
        Me.LabelControl1.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl1.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl1.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl1.Location = New System.Drawing.Point(233, 265)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(45, 13)
        Me.LabelControl1.TabIndex = 4
        Me.LabelControl1.Text = "Search:"
        '
        'ButtonAddChain
        '
        Me.ButtonAddChain.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAddChain.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAddChain.Appearance.Options.UseFont = True
        Me.ButtonAddChain.ImageIndex = 0
        Me.ButtonAddChain.ImageList = Me.ImageList16x16
        Me.ButtonAddChain.Location = New System.Drawing.Point(5, 261)
        Me.ButtonAddChain.LookAndFeel.SkinName = "Black"
        Me.ButtonAddChain.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAddChain.Name = "ButtonAddChain"
        Me.ButtonAddChain.Size = New System.Drawing.Size(75, 23)
        Me.ButtonAddChain.TabIndex = 2
        Me.ButtonAddChain.Text = "Add"
        '
        'ButtonRemoveChain
        '
        Me.ButtonRemoveChain.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonRemoveChain.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonRemoveChain.Appearance.Options.UseFont = True
        Me.ButtonRemoveChain.ImageIndex = 1
        Me.ButtonRemoveChain.ImageList = Me.ImageList16x16
        Me.ButtonRemoveChain.Location = New System.Drawing.Point(86, 261)
        Me.ButtonRemoveChain.LookAndFeel.SkinName = "Black"
        Me.ButtonRemoveChain.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonRemoveChain.Name = "ButtonRemoveChain"
        Me.ButtonRemoveChain.Size = New System.Drawing.Size(75, 23)
        Me.ButtonRemoveChain.TabIndex = 3
        Me.ButtonRemoveChain.Text = "Remove"
        '
        'TextEditSearchChains
        '
        Me.TextEditSearchChains.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchChains.EditValue = ""
        Me.TextEditSearchChains.Location = New System.Drawing.Point(284, 262)
        Me.TextEditSearchChains.Margin = New System.Windows.Forms.Padding(3, 4, 3, 5)
        Me.TextEditSearchChains.Name = "TextEditSearchChains"
        Me.TextEditSearchChains.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchChains.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchChains.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchChains.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchChains.Size = New System.Drawing.Size(80, 20)
        Me.TextEditSearchChains.TabIndex = 5
        '
        'PictureClearSearchChains
        '
        Me.PictureClearSearchChains.AllowDrop = True
        Me.PictureClearSearchChains.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchChains.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchChains.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchChains.Location = New System.Drawing.Point(370, 3)
        Me.PictureClearSearchChains.Name = "PictureClearSearchChains"
        Me.PictureClearSearchChains.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchChains.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchChains.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchChains.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem3.Text = "Clear Search"
        ToolTipItem3.LeftIndent = 6
        ToolTipItem3.Text = "Click here to clear all search boxes."
        SuperToolTip3.Items.Add(ToolTipTitleItem3)
        SuperToolTip3.Items.Add(ToolTipItem3)
        Me.PictureClearSearchChains.SuperTip = SuperToolTip3
        Me.PictureClearSearchChains.TabIndex = 0
        Me.PictureClearSearchChains.TabStop = True
        '
        'PictureSearchChains
        '
        Me.PictureSearchChains.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureSearchChains.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureSearchChains.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureSearchChains.Location = New System.Drawing.Point(370, 264)
        Me.PictureSearchChains.Margin = New System.Windows.Forms.Padding(3, 6, 3, 7)
        Me.PictureSearchChains.Name = "PictureSearchChains"
        Me.PictureSearchChains.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureSearchChains.Properties.Appearance.Options.UseBackColor = True
        Me.PictureSearchChains.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureSearchChains.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem4.Text = "Advanced Search"
        ToolTipItem4.LeftIndent = 6
        ToolTipItem4.Text = "Click here to search individual column values."
        SuperToolTip4.Items.Add(ToolTipTitleItem4)
        SuperToolTip4.Items.Add(ToolTipItem4)
        Me.PictureSearchChains.SuperTip = SuperToolTip4
        Me.PictureSearchChains.TabIndex = 6
        Me.PictureSearchChains.TabStop = True
        '
        'GridChains
        '
        Me.GridChains.AllowUserToAddRows = False
        Me.GridChains.AllowUserToDeleteRows = False
        Me.GridChains.AllowUserToOrderColumns = True
        Me.GridChains.AllowUserToResizeRows = False
        DataGridViewCellStyle4.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridChains.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle4
        Me.GridChains.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridChains.BackgroundColor = System.Drawing.Color.White
        Me.GridChains.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridChains.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridChains.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle5.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle5.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle5.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle5.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle5.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle5.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle5.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridChains.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle5
        Me.GridChains.ColumnHeadersHeight = 22
        Me.GridChains.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridChains.ColumnHeadersVisible = False
        Me.GridChains.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.ChainNameColumn})
        Me.GridChains.EnableHeadersVisualStyles = False
        Me.GridChains.GridColor = System.Drawing.Color.White
        Me.GridChains.Location = New System.Drawing.Point(2, 22)
        Me.GridChains.Name = "GridChains"
        Me.GridChains.ReadOnly = True
        Me.GridChains.RowHeadersVisible = False
        DataGridViewCellStyle6.ForeColor = System.Drawing.Color.DimGray
        Me.GridChains.RowsDefaultCellStyle = DataGridViewCellStyle6
        Me.GridChains.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridChains.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridChains.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridChains.RowTemplate.Height = 19
        Me.GridChains.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridChains.ShowCellToolTips = False
        Me.GridChains.Size = New System.Drawing.Size(387, 233)
        Me.GridChains.StandardTab = True
        Me.GridChains.TabIndex = 1
        '
        'ChainNameColumn
        '
        Me.ChainNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.ChainNameColumn.DataPropertyName = "ChainName"
        Me.ChainNameColumn.HeaderText = "Chain"
        Me.ChainNameColumn.Name = "ChainNameColumn"
        Me.ChainNameColumn.ReadOnly = True
        '
        'GroupControlCategories
        '
        Me.GroupControlCategories.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlCategories.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlCategories.Appearance.Options.UseFont = True
        Me.GroupControlCategories.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlCategories.AppearanceCaption.Options.UseFont = True
        Me.GroupControlCategories.Controls.Add(Me.LabelControl2)
        Me.GroupControlCategories.Controls.Add(Me.ButtonAddCategory)
        Me.GroupControlCategories.Controls.Add(Me.ButtonRemoveCategory)
        Me.GroupControlCategories.Controls.Add(Me.TextEditSearchCategories)
        Me.GroupControlCategories.Controls.Add(Me.PictureClearSearchCategories)
        Me.GroupControlCategories.Controls.Add(Me.PictureSearchCategories)
        Me.GroupControlCategories.Controls.Add(Me.GridCategories)
        Me.GroupControlCategories.Location = New System.Drawing.Point(0, 152)
        Me.GroupControlCategories.LookAndFeel.SkinName = "Black"
        Me.GroupControlCategories.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlCategories.Margin = New System.Windows.Forms.Padding(0)
        Me.GroupControlCategories.Name = "GroupControlCategories"
        Me.GroupControlCategories.Size = New System.Drawing.Size(391, 137)
        Me.GroupControlCategories.TabIndex = 1
        Me.GroupControlCategories.Text = "Categories to Book"
        '
        'LabelControl2
        '
        Me.LabelControl2.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl2.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl2.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl2.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl2.Location = New System.Drawing.Point(233, 113)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.Size = New System.Drawing.Size(45, 13)
        Me.LabelControl2.TabIndex = 4
        Me.LabelControl2.Text = "Search:"
        '
        'ButtonAddCategory
        '
        Me.ButtonAddCategory.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAddCategory.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAddCategory.Appearance.Options.UseFont = True
        Me.ButtonAddCategory.ImageIndex = 0
        Me.ButtonAddCategory.ImageList = Me.ImageList16x16
        Me.ButtonAddCategory.Location = New System.Drawing.Point(5, 109)
        Me.ButtonAddCategory.LookAndFeel.SkinName = "Black"
        Me.ButtonAddCategory.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAddCategory.Name = "ButtonAddCategory"
        Me.ButtonAddCategory.Size = New System.Drawing.Size(75, 23)
        Me.ButtonAddCategory.TabIndex = 2
        Me.ButtonAddCategory.Text = "Add"
        '
        'ButtonRemoveCategory
        '
        Me.ButtonRemoveCategory.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonRemoveCategory.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonRemoveCategory.Appearance.Options.UseFont = True
        Me.ButtonRemoveCategory.ImageIndex = 1
        Me.ButtonRemoveCategory.ImageList = Me.ImageList16x16
        Me.ButtonRemoveCategory.Location = New System.Drawing.Point(86, 109)
        Me.ButtonRemoveCategory.LookAndFeel.SkinName = "Black"
        Me.ButtonRemoveCategory.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonRemoveCategory.Name = "ButtonRemoveCategory"
        Me.ButtonRemoveCategory.Size = New System.Drawing.Size(75, 23)
        Me.ButtonRemoveCategory.TabIndex = 3
        Me.ButtonRemoveCategory.Text = "Remove"
        '
        'TextEditSearchCategories
        '
        Me.TextEditSearchCategories.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchCategories.EditValue = ""
        Me.TextEditSearchCategories.Location = New System.Drawing.Point(284, 110)
        Me.TextEditSearchCategories.Margin = New System.Windows.Forms.Padding(3, 4, 3, 5)
        Me.TextEditSearchCategories.Name = "TextEditSearchCategories"
        Me.TextEditSearchCategories.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchCategories.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchCategories.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchCategories.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchCategories.Size = New System.Drawing.Size(80, 20)
        Me.TextEditSearchCategories.TabIndex = 5
        '
        'PictureClearSearchCategories
        '
        Me.PictureClearSearchCategories.AllowDrop = True
        Me.PictureClearSearchCategories.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchCategories.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchCategories.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchCategories.Location = New System.Drawing.Point(370, 3)
        Me.PictureClearSearchCategories.Name = "PictureClearSearchCategories"
        Me.PictureClearSearchCategories.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchCategories.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchCategories.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchCategories.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem5.Text = "Clear Search"
        ToolTipItem5.LeftIndent = 6
        ToolTipItem5.Text = "Click here to clear all search boxes."
        SuperToolTip5.Items.Add(ToolTipTitleItem5)
        SuperToolTip5.Items.Add(ToolTipItem5)
        Me.PictureClearSearchCategories.SuperTip = SuperToolTip5
        Me.PictureClearSearchCategories.TabIndex = 0
        Me.PictureClearSearchCategories.TabStop = True
        '
        'PictureSearchCategories
        '
        Me.PictureSearchCategories.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureSearchCategories.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureSearchCategories.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureSearchCategories.Location = New System.Drawing.Point(370, 112)
        Me.PictureSearchCategories.Margin = New System.Windows.Forms.Padding(3, 6, 3, 7)
        Me.PictureSearchCategories.Name = "PictureSearchCategories"
        Me.PictureSearchCategories.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureSearchCategories.Properties.Appearance.Options.UseBackColor = True
        Me.PictureSearchCategories.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureSearchCategories.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem6.Text = "Advanced Search"
        ToolTipItem6.LeftIndent = 6
        ToolTipItem6.Text = "Click here to search individual column values."
        SuperToolTip6.Items.Add(ToolTipTitleItem6)
        SuperToolTip6.Items.Add(ToolTipItem6)
        Me.PictureSearchCategories.SuperTip = SuperToolTip6
        Me.PictureSearchCategories.TabIndex = 6
        Me.PictureSearchCategories.TabStop = True
        '
        'GridCategories
        '
        Me.GridCategories.AllowUserToAddRows = False
        Me.GridCategories.AllowUserToDeleteRows = False
        Me.GridCategories.AllowUserToOrderColumns = True
        Me.GridCategories.AllowUserToResizeRows = False
        DataGridViewCellStyle7.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridCategories.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle7
        Me.GridCategories.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridCategories.BackgroundColor = System.Drawing.Color.White
        Me.GridCategories.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridCategories.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridCategories.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle8.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle8.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle8.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle8.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle8.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle8.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle8.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridCategories.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle8
        Me.GridCategories.ColumnHeadersHeight = 22
        Me.GridCategories.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridCategories.ColumnHeadersVisible = False
        Me.GridCategories.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.CategoryNameColumn})
        Me.GridCategories.EnableHeadersVisualStyles = False
        Me.GridCategories.GridColor = System.Drawing.Color.White
        Me.GridCategories.Location = New System.Drawing.Point(2, 22)
        Me.GridCategories.Name = "GridCategories"
        Me.GridCategories.ReadOnly = True
        Me.GridCategories.RowHeadersVisible = False
        DataGridViewCellStyle9.ForeColor = System.Drawing.Color.DimGray
        Me.GridCategories.RowsDefaultCellStyle = DataGridViewCellStyle9
        Me.GridCategories.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridCategories.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridCategories.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridCategories.RowTemplate.Height = 19
        Me.GridCategories.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridCategories.ShowCellToolTips = False
        Me.GridCategories.Size = New System.Drawing.Size(387, 81)
        Me.GridCategories.StandardTab = True
        Me.GridCategories.TabIndex = 1
        '
        'CategoryNameColumn
        '
        Me.CategoryNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.CategoryNameColumn.DataPropertyName = "CategoryName"
        Me.CategoryNameColumn.HeaderText = "Category"
        Me.CategoryNameColumn.Name = "CategoryNameColumn"
        Me.CategoryNameColumn.ReadOnly = True
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanel1.ColumnCount = 3
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 15.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.GroupControlChains, 2, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.GroupControlMediaFamilies, 0, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.GroupControlCategories, 0, 2)
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(12, 199)
        Me.TableLayoutPanel1.Margin = New System.Windows.Forms.Padding(3, 15, 3, 3)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 3
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 15.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(797, 289)
        Me.TableLayoutPanel1.TabIndex = 11
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        '
        'ButtonSave
        '
        Me.ButtonSave.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonSave.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonSave.Appearance.Options.UseFont = True
        Me.ButtonSave.ImageIndex = 1
        Me.ButtonSave.ImageList = Me.ImageList24x24
        Me.ButtonSave.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonSave.Location = New System.Drawing.Point(603, 503)
        Me.ButtonSave.LookAndFeel.SkinName = "Black"
        Me.ButtonSave.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonSave.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonSave.Name = "ButtonSave"
        Me.ButtonSave.Size = New System.Drawing.Size(100, 28)
        Me.ButtonSave.TabIndex = 12
        Me.ButtonSave.Text = "Save"
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(709, 503)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(100, 28)
        Me.ButtonCancel.TabIndex = 13
        Me.ButtonCancel.Text = "Cancel"
        '
        'SubformProvisionalBooking
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.Controls.Add(Me.ButtonSave)
        Me.Controls.Add(Me.ButtonCancel)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.Controls.Add(Me.TextEditWeeks)
        Me.Controls.Add(Me.LabelWeeks)
        Me.Controls.Add(Me.LabelBrand)
        Me.Controls.Add(Me.LabelLastWeek)
        Me.Controls.Add(Me.LabelFirstWeek)
        Me.Controls.Add(Me.TextEditProvisionalBookingName)
        Me.Controls.Add(Me.LabelProvisionalBookingName)
        Me.Controls.Add(Me.HyperlinkLastWeek)
        Me.Controls.Add(Me.HyperlinkFirstWeek)
        Me.Controls.Add(Me.HyperlinkBrand)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Name = "SubformProvisionalBooking"
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkBrand, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkFirstWeek, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkLastWeek, 0)
        Me.Controls.SetChildIndex(Me.LabelProvisionalBookingName, 0)
        Me.Controls.SetChildIndex(Me.TextEditProvisionalBookingName, 0)
        Me.Controls.SetChildIndex(Me.LabelFirstWeek, 0)
        Me.Controls.SetChildIndex(Me.LabelLastWeek, 0)
        Me.Controls.SetChildIndex(Me.LabelBrand, 0)
        Me.Controls.SetChildIndex(Me.LabelWeeks, 0)
        Me.Controls.SetChildIndex(Me.TextEditWeeks, 0)
        Me.Controls.SetChildIndex(Me.TableLayoutPanel1, 0)
        Me.Controls.SetChildIndex(Me.ButtonCancel, 0)
        Me.Controls.SetChildIndex(Me.ButtonSave, 0)
        CType(Me.TextEditProvisionalBookingName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditWeeks.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControlMediaFamilies, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlMediaFamilies.ResumeLayout(False)
        CType(Me.TextEditSearchMediaFamilies.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearchMediaFamilies.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureSearchMediaFamilies.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridMediaFamilies, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControlChains, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlChains.ResumeLayout(False)
        CType(Me.TextEditSearchChains.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearchChains.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureSearchChains.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridChains, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControlCategories, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlCategories.ResumeLayout(False)
        CType(Me.TextEditSearchCategories.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearchCategories.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureSearchCategories.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridCategories, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents HyperlinkBrand As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditProvisionalBookingName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelProvisionalBookingName As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelFirstWeek As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelLastWeek As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelBrand As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditWeeks As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelWeeks As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkFirstWeek As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkLastWeek As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ImageList16x16 As System.Windows.Forms.ImageList
    Friend WithEvents GroupControlMediaFamilies As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelControl10 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ButtonRemoveMediaFamily As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents TextEditSearchMediaFamilies As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearchMediaFamilies As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureSearchMediaFamilies As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents GridMediaFamilies As System.Windows.Forms.DataGridView
    Friend WithEvents ButtonAddMediaFamily As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GroupControlChains As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ButtonAddChain As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonRemoveChain As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents TextEditSearchChains As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearchChains As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureSearchChains As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents GridChains As System.Windows.Forms.DataGridView
    Friend WithEvents GroupControlCategories As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ButtonAddCategory As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonRemoveCategory As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents TextEditSearchCategories As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearchCategories As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureSearchCategories As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents GridCategories As System.Windows.Forms.DataGridView
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents ButtonSave As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents CategoryNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents MediaFamilyNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents ChainNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn

End Class
