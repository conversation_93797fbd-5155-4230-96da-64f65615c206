﻿Public Class BaseDataObject

    Protected OriginalTable As DataTable = Nothing            ' A backup to allow us to audit changes to this row.
    Protected ConnectionString As String = String.Empty

    Protected Sub AuditChanges _
        (ByRef AuditTable As DataSetAudit.AuditLogDataTable, _
         Row As DataRow, _
         IDColumnName As String, _
         RowType As String, _
         RowDescription As String, _
         Optional ActionPrefix As String = "")
        ' Compare all values for the current row to the original unchanged row and if there are any differences, add
        ' an entry for it to the audit table.

        ' Get the table containing this row.
        Dim RowTable As DataTable = Row.Table

        ' Cycle through the values for this row. Any column with a caption of "DontAudit" will be skipped.
        For i As Integer = 0 To RowTable.Columns.Count - 1
            If Not String.Compare(RowTable.Columns(i).Caption, "DontAudit") = 0 Then
                ' This row must be audited.
                ' Get the original copy of this row.
                Dim OriginalRow As DataRow = OriginalTable.Rows.Find(Row(IDColumnName))
                '  Compare each column value of the original row with the corresponding column value of this one.
                If Not Object.Equals(Row(i), OriginalRow(i)) Then
                    ' The data in this column has changed.

                    ' If the original value was an empty string, change it to something more meaningful for the audit log.
                    Dim OriginalValue As String = String.Empty
                    Dim NewValue As String = String.Empty
                    If RowTable.Columns(i).DataType = GetType(String) Then
                        ' This column's data type is STRING. Check if the values are null or empty.
                        OriginalValue = OriginalRow(i).ToString.Trim.Replace(vbCrLf, " ")
                        NewValue = Row(i).ToString.Trim.Replace(vbCrLf, " ")
                        If String.IsNullOrEmpty(OriginalValue) Then
                            ' The OriginalValue is null or empty, change it to something more meaningful for the audit log.
                            OriginalValue = "an empty string"
                        End If
                        If String.IsNullOrEmpty(NewValue) Then
                            ' The NewValue is null or empty, change it to something more meaningful for the audit log.
                            NewValue = "an empty string"
                        End If
                    Else
                        OriginalValue = OriginalRow(i).ToString
                        NewValue = Row(i).ToString
                    End If

                    ' Let's now check what the variables contain.
                    If Not String.Compare(OriginalValue, "an empty string") = 0 Then
                        ' This variable contains actual data. Add inverted commas for the audit log.
                        OriginalValue = "'" & OriginalValue & "'"
                    End If
                    If Not String.Compare(NewValue, "an empty string") = 0 Then
                        ' This variable contains actual data. Add inverted commas for the audit log.
                        NewValue = "'" & NewValue & "'"
                    End If

                    ' Build the value of the Action field that will be logged.
                    Dim Prefix As String = String.Empty
                    If String.IsNullOrEmpty(ActionPrefix) Then
                        Prefix = "C"
                    Else
                        Prefix = ActionPrefix & "c"
                    End If
                    Dim Action As String = Prefix & "hanged the value of " & RowTable.Columns(i).Caption.ToUpper _
                                           & " from " & OriginalValue & " to " & NewValue & ""

                    ' Log an entry for the changed data.
                    LiquidShell.LiquidAgent.AddAuditLogEntry(AuditTable, RowType, RowDescription, Action)

                End If
            End If
        Next

    End Sub

End Class
