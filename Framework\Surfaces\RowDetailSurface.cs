﻿using Framework.Controls.Data;
using Framework.Controls.TabSystem;
using System;
using System.Data;
using System.Windows.Forms;

namespace Framework.Surfaces
{
    public partial class RowDetailSurface : Surface
    {
        protected Func<DataRow, string> SaveMethod;
        private BindingSource BindingSource;
        protected bool HideSaveButton = false;

        private bool IsNewRow
        {
            get
            {
                DataRowView rowview = (DataRowView)BindingSource.Current;
                return rowview.IsNew;
            }
        }

        private RowSurface ParentRowSurface
        {
            get
            {
                TabGroup parenttabgroup = (TabGroup)ParentTab.Parent;
                return (RowSurface)parenttabgroup.Parent;
            }
        }


        #region Startup

        public RowDetailSurface()
        {
            // This constructor must not be used. It exists only to enable the use of the visual designer for derived classes.
            InitializeComponent();
        }

        public RowDetailSurface(BindingSource bindingsource, Func<DataRow, string> savemethod)
        {
            InitializeComponent();
            BindingSource = bindingsource;
            flatButtonSave.Text = FrameworkSettings.Strings.SAVEBUTTONTEXT;
            flatButtonDiscardChanges.Text = IsNewRow ? "Cancel" : FrameworkSettings.Strings.UNDOBUTTONTEXT;
            SaveMethod = savemethod;
            SubscribeToEvents();
        }

        private void SubscribeToEvents()
        {
            flatButtonDiscardChanges.Click += FlatButtonUndo_Click;
            flatButtonSave.Click += FlatButtonSave_Click;
            DirtyStateManager.IsDirtyChanged += RowDetailSurface_IsDirtyChanged;
            ErrorStateManager.HasErrorChanged += ErrorStateManager_HasErrorChanged;
            BindingSource.CurrentChanged += BindingSource_CurrentChanged;
            Load += RowDetailSurface_Load;
        }

        private void BindingSource_CurrentChanged(object sender, EventArgs e)
        {
            DirtyStateManager.AcceptChanges();
        }

        private bool DataBindingsAdded = false;
        private void RowDetailSurface_Load(object sender, EventArgs e)
        {
            if (DataBindingsAdded == false)
            {
                AddDataBindingsToControls(this);
                DataBindingsAdded = true;
            }

            if (IsNewRow)
            {
                InitializeDataBoxControls(this);
                DirtyStateManager.AcceptChanges();
            }
        }

        private void InitializeDataBoxControls(Control parentcontrol)
        {
            for (int i = 0; i < parentcontrol.Controls.Count; i++)
            {
                Control childcontrol = parentcontrol.Controls[i];
                if (childcontrol is DataBox)
                {
                    if (childcontrol is TextDataBox)
                    {
                        ((TextDataBox)childcontrol).Value = string.Empty;
                    }
                    ((DataBox)childcontrol).ErrorStateManager.ErrorMessage = string.Empty;
                }
                else
                {
                    if (childcontrol.HasChildren)
                    {
                        InitializeDataBoxControls(childcontrol);
                    }
                }
            }
        }

        #endregion


        #region Data bindings

        protected void AddDataBindingsToControls(Control parentcontrol)
        {
            for (int i = 0; i < Controls.Count; i++)
            {
                if (Controls[i] is DataBox)
                {
                    DataBox databox = (DataBox)Controls[i];
                    if (string.IsNullOrEmpty(databox.DataPropertyName) == false)
                    {
                        DataTable table = ((DataView)BindingSource.List).Table;
                        if (table.Columns[databox.DataPropertyName].ReadOnly == false)
                        {
                            databox.DataBindings.Add("Value", BindingSource, databox.DataPropertyName, false, DataSourceUpdateMode.OnPropertyChanged);
                        }
                        else
                        {
                            databox.DataBindings.Add("Value", BindingSource, databox.DataPropertyName);
                            databox.Enabled = false;
                        }
                    }
                }
                else
                {
                    if (Controls[i].HasChildren)
                    {
                        AddDataBindingsToControls(Controls[i]);
                    }
                }
            }
        }

        #endregion


        #region Save the row

        private void FlatButtonSave_Click(object sender, EventArgs e)
        {
            Save();
        }

        private void Save()
        {
            DataRow row = ((DataRowView)BindingSource.Current).Row;
            string errormessage = SaveMethod?.Invoke(row);

            if (string.IsNullOrEmpty(errormessage))
            {
                BindingSource.EndEdit();
                row.AcceptChanges();
                DirtyStateManager.AcceptChanges();
                ParentRowSurface.CreateTabs();
            }
            else
            {
                MessageForm.Show(errormessage, "Save Operation Failed");
            }
        }

        #endregion


        #region Discard changes

        private void FlatButtonUndo_Click(object sender, System.EventArgs e)
        {
            DiscardChanges();
        }

        private void DiscardChanges()
        {
            DataRowView rowview = (DataRowView)BindingSource.Current;

            if (rowview.IsNew)
            {
                // I know it looks strange, but we have to accept changes so that when we call GoBack on the parent RowSurface, we don't
                // get blocked because we have unsaved changes.
                DirtyStateManager.AcceptChanges();

                RowSurface parentrowsurface = (RowSurface)Parent.Parent;
                parentrowsurface.GoBack();
            }
            else
            {
                BindingSource.CancelEdit();
                DataRow row = rowview.Row;
                row.RejectChanges();
                DirtyStateManager.RejectChanges();
            }
        }

        #endregion


        #region Appearance

        private void RowDetailSurface_IsDirtyChanged(object sender, EventArgs e)
        {
            UpdateSaveButtonVisibility();
            UpdateUndoButtonVisibility();
        }

        private void ErrorStateManager_HasErrorChanged(object sender, EventArgs e)
        {
            UpdateSaveButtonVisibility();
        }

        private void UpdateSaveButtonVisibility()
        {
            flatButtonSave.Visible = DirtyStateManager.IsDirty && ErrorStateManager.HasError == false && HideSaveButton != true;
        }

        private void UpdateUndoButtonVisibility()
        {
            flatButtonDiscardChanges.Visible = DirtyStateManager.IsDirty;
        }

        #endregion

    }
}
