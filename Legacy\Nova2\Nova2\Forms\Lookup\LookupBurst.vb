Public Class LookupBurst

    Public Sub New(ByVal GridData As BindingSource, ByVal AllowMultiSelect As Boolean)

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        GridController = New GridManager _
        (GridItems, _
        TextEditSearch, _
        GridData, _
        My.Settings.DBConnection, _
        PictureAdvancedSearch, _
        PictureClearSearch, _
        Nothing, _
        Nothing)

        ' Apply multi-select restrictions if necessary.
        GridItems.MultiSelect = AllowMultiSelect

    End Sub

    Public Overloads Shared Function SelectRows _
    (ByVal ConnectionString As String, _
    ByVal AllowMultiSelect As Boolean, _
    ByVal RowsToExclude As Object, _
    ByVal BurstIDToExclude As Guid, _
    ByVal ChainID As Integer, _
    ByVal ColumnsToHide As List(Of String)) _
    As List(Of DataRow)

        ' A string variable to hold any potential errors during data retrieval.
        Dim ErrorMessage As String = String.Empty

        ' Create a data source for the list.
        Dim GridData As BindingSource = Nothing
        GridData = Lookup.GetBursts(ConnectionString, ErrorMessage, BurstIDToExclude, ChainID)

        ' Create an instance of this lookup list form and return the user-selected rows.
        Using Selector As New LookupBurst(GridData, AllowMultiSelect)
            Return SelectRows(Selector, ErrorMessage, ColumnsToHide)
        End Using

    End Function

    Public Shared Function SelectPeers _
    (ByVal SourceListDataView As DataView, _
    ByVal AllowMultiSelect As Boolean, _
    ByVal ColumnsToHide As List(Of String)) _
    As List(Of DataRow)

        ' A string variable to hold any potential errors during data retrieval.
        Dim ErrorMessage As String = String.Empty

        ' Create a data source for the list.
        Dim GridData As New BindingSource(SourceListDataView, String.Empty)

        ' Create an instance of this lookup list form and return the user-selected rows.
        Using Selector As New LookupBurst(GridData, AllowMultiSelect)
            Return SelectRows(Selector, ErrorMessage, ColumnsToHide)
        End Using

    End Function

End Class
