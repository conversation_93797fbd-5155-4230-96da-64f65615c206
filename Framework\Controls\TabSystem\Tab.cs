﻿using Framework.Surfaces;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace Framework.Controls.TabSystem
{
    public partial class Tab : UserControl
    {

        const int TEXTONLYTABHEIGHT = 60;
        const int IMAGEANDTEXTTABHEIGHT = TEXTONLYTABHEIGHT * 2;
        const int LEFTINDENT = 0;


        #region Startup

        public Tab(Control contentdisplayarea, Control content, string tabtext)
        {
            Construct(contentdisplayarea, content, tabtext);
        }

        public Tab(Control contentdisplayarea, Control content, string tabtext, Bitmap image)
        {
            Construct(contentdisplayarea, content, tabtext);
            Image = image;
        }

        private void Construct(Control contentdisplayarea, Control content, string tabtext)
        {
            InitializeComponent();
            ContentDisplayArea = contentdisplayarea;
            Text = tabtext;
            label1.Font = FrameworkSettings.Fonts.TABFONT;
            ForeColor = FrameworkSettings.Colors.TABFORECOLOR;
            MouseOverBackColor = FrameworkSettings.Colors.TABMOUSEOVERBACKCOLOR;
            TabStop = false;

            SetElementPosition();
            SubscribeToEvents();

            AddContent(content);
        }

        private void SubscribeToEvents()
        {
            Load += Tab_Load;
            ParentChanged += Tab_ParentChanged;
            BackColorChanged += FrameworkMethods.EventHandlers.Control_BackColorChanged;

            label1.Click += Component_Click;
            label1.MouseEnter += Component_MouseEnter;
            label1.MouseLeave += Component_MouseLeave;

            pictureBox1.Click += Component_Click;
            pictureBox1.MouseEnter += Component_MouseEnter;
            pictureBox1.MouseLeave += Component_MouseLeave;

            tableLayoutPanel1.Click += Component_Click;
            tableLayoutPanel1.MouseEnter += Component_MouseEnter;
            tableLayoutPanel1.MouseLeave += Component_MouseLeave;
        }

        private void Tab_Load(object sender, EventArgs e)
        {
            AddIndentation();
            VerifyConfiguration();
        }

        private void VerifyConfiguration()
        {
            string errormessage = string.Empty;
            if (ContentList.Count == 0)
            {
                errormessage = "The " + Name + " requires at least one content control. Specify the content in the constructor when "
                    + "creating a new tab.";
            }
            else
            {
                if (ContentDisplayArea == null)
                {
                    errormessage = "The ContentDisplayArea of the \"" + Text + "\" tab is null.";
                }
            }

            if (string.IsNullOrEmpty(errormessage) == false)
            {
                MessageForm.Show(errormessage, "Danger! Danger!");
                throw new Exception(errormessage);
            }
        }

        #endregion


        #region Appearance

        [Browsable(true)]
        [EditorBrowsable(EditorBrowsableState.Always)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        [Bindable(true)]
        public override string Text
        {
            get { return label1.Text; }
            set
            {
                if (label1.Text != value)
                {
                    label1.Text = value;
                }
            }
        }

        [Browsable(true)]
        [EditorBrowsable(EditorBrowsableState.Always)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        [Bindable(true)]
        public override Color ForeColor
        {
            get { return label1.ForeColor; }
            set
            {
                if (label1.ForeColor != value)
                {
                    label1.ForeColor = value;
                }
            }
        }

        private void SetSize(int parentwidth)
        {
            if (Image == null)
            {
                Height = TEXTONLYTABHEIGHT;
                Width = parentwidth - LEFTINDENT;
            }
            else
            {
                Height = IMAGEANDTEXTTABHEIGHT;
                Width = parentwidth;
            }
        }

        private void AddIndentation()
        {
            if (Image == null)
            {
                Margin = new Padding(LEFTINDENT, 0, 0, 0);
            }
        }

        public Bitmap Image
        {
            get { return (Bitmap)pictureBox1.Image; }
            set
            {
                if (pictureBox1.Image != value)
                {
                    pictureBox1.Image = value;
                    SetElementPosition();
                }
            }
        }

        private void SetElementPosition()
        {
            if (Image == null)
            {
                pictureBox1.Margin = new Padding(0);
                label1.Margin = new Padding(15, 0, 0, 0);
                pictureBox1.Height = 0;
                label1.TextAlign = ContentAlignment.MiddleLeft;
            }
            else
            {
                pictureBox1.Margin = new Padding(0, 0, 0, 5);
                label1.Margin = new Padding(0, 5, 0, 0);
                pictureBox1.Height = Image.Height;
                label1.TextAlign = ContentAlignment.TopCenter;
            }
        }

        private Color _MouseOverBackColor;
        public Color MouseOverBackColor
        {
            get { return _MouseOverBackColor; }
            set
            {
                if (_MouseOverBackColor != value)
                {
                    _MouseOverBackColor = value;
                }
            }
        }

        public Color SelectedBackColor
        {
            get
            {
                Color returnvalue = Color.Firebrick;
                if (CurrentContent != null)
                {
                    returnvalue = CurrentContent.BackColor;
                }
                return returnvalue;
            }
        }

        public Color UnselectedBackColor
        {
            get
            {
                Color returnvalue = Color.FromArgb(60, 60, 60);
                if (Parent != null)
                {
                    returnvalue = Parent.BackColor;
                }
                return returnvalue;
            }
        }

        #endregion


        #region Event handlers

        private void Component_Click(object sender, EventArgs e)
        {
            Selected = true;
        }

        private void Component_MouseEnter(object sender, EventArgs e)
        {
            if (Selected == false)
            {
                BackColor = MouseOverBackColor;
            }
        }

        private void Component_MouseLeave(object sender, EventArgs e)
        {
            UpdateBackColor();
        }

        private void Tab_ParentChanged(object sender, EventArgs e)
        {
            int parentwidth = Parent == null ? 100 : Parent.Width;
            SetSize(parentwidth);
            if (Parent != null)
            {
                Parent.BackColorChanged += Parent_BackColorChanged;
                UpdateBackColor();
            }
        }

        private void Parent_BackColorChanged(object sender, EventArgs e)
        {
            UpdateBackColor();
        }

        #endregion


        #region Selected state

        private bool _Selected = false;
        public bool Selected
        {
            get { return _Selected; }
            set
            {
                if (_Selected != value)
                {
                    _Selected = value;
                    Cursor = (Selected ? Cursors.Default : Cursors.Hand);
                    UpdateBackColor();
                    if (value)
                    {
                        DisplayCurrentContent();
                        DeselectSiblings();
                    }
                }
            }
        }

        private void DeselectSiblings()
        {
            if (Parent != null)
            {
                for (int i = 0; i < Parent.Controls.Count; i++)
                {
                    if (Parent.Controls[i] is Tab)
                    {
                        Tab siblingtab = (Tab)Parent.Controls[i];
                        if (siblingtab.Equals(this) == false)
                        {
                            siblingtab.Selected = false;
                        }
                    }
                }
            }
        }

        #endregion


        #region Display content

        private Control ContentDisplayArea;
        // NEVER add to this list directly. Always use the 'AddContent' method.
        private List<Control> ContentList = new List<Control>();

        // ONLY use this method to add items to the ContentList. DO NOT add items directly.
        private void AddContent(Control content)
        {
            if (content != null)
            {
                ContentList.Add(content);
                content.BackColorChanged += Content_BackColorChanged;
                if (content is Surface)
                {
                    ((Surface)content).ParentTab = this;
                }
            }
        }

        private void Content_BackColorChanged(object sender, EventArgs e)
        {
            Control content = (Control)sender;
            if (Selected)
            {
                if (content.Equals(CurrentContent))
                {
                    UpdateBackColor();
                }
            }
        }

        private void UpdateBackColor()
        {
            BackColor = (Selected ? SelectedBackColor : UnselectedBackColor);
        }

        public Control CurrentContent
        {
            get
            {
                Control returnvalue = null;
                if (ContentList.Count > 0)
                {
                    // The current content will be the last control that was added to the list.
                    returnvalue = ContentList[ContentList.Count - 1];
                }
                return returnvalue;
            }
        }

        private Control PreviousContent
        {
            get
            {
                Control returnvalue = null;
                if (ContentList.Count > 1)
                {
                    returnvalue = ContentList[ContentList.Count - 2];
                }
                return returnvalue;
            }
        }

        private void DisplayCurrentContent()
        {
            if (CurrentContent != null)
            {
                DisplayContent(CurrentContent);
                if (CurrentContent is TabGroup)
                {
                    TabGroup tabgroup = (TabGroup)CurrentContent;
                    tabgroup.SelectedTab.Selected = true;
                    tabgroup.SelectedTab.DisplayCurrentContent();
                }
            }
        }

        public void DisplayPreviousContent()
        {
            if (PreviousContent != null)
            {
                var currentcontent = CurrentContent;
                ContentList.Remove(currentcontent);

                DisplayContent(CurrentContent);
            }
        }

        public void DisplayContent(Control content)
        {
            if (ContentList.Contains(content) == false)
            {
                AddContent(content);
            }
            ContentDisplayArea.Controls.Clear();
            ContentDisplayArea.Controls.Add(content);
            content.Dock = DockStyle.Fill;
            UpdateBackColor();
            content.Focus();
        }

        #endregion

    }
}
