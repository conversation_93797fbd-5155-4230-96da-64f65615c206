﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
NovaFinance
</name>
</assembly>
<members>
<member name="T:NovaFinance.My.Resources.Resources">
<summary>
  A strongly-typed resource class, for looking up localized strings, etc.
</summary>
</member>
<member name="P:NovaFinance.My.Resources.Resources.ResourceManager">
<summary>
  Returns the cached ResourceManager instance used by this class.
</summary>
</member>
<member name="P:NovaFinance.My.Resources.Resources.Culture">
<summary>
  Overrides the current thread's CurrentUICulture property for all
  resource lookups using this strongly typed resource class.
</summary>
</member>
<member name="P:NovaFinance.My.Resources.Resources.accept16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:NovaFinance.My.Resources.Resources.Book_openHS">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:NovaFinance.My.Resources.Resources.cancel_16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:NovaFinance.My.Resources.Resources.coffee256">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:NovaFinance.My.Resources.Resources.DatabaseIcon">
<summary>
  Looks up a localized resource of type System.Drawing.Icon similar to (Icon).
</summary>
</member>
<member name="P:NovaFinance.My.Resources.Resources.delete16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:NovaFinance.My.Resources.Resources.EditTableHS">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:NovaFinance.My.Resources.Resources.Logo_PNG_159x39">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:NovaFinance.My.Resources.Resources.maximum16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:NovaFinance.My.Resources.Resources.minimum16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:NovaFinance.My.Resources.Resources.news128">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:NovaFinance.My.Resources.Resources.OpenSelectedItemHS">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:NovaFinance.My.Resources.Resources.PrintHS">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:NovaFinance.My.Resources.Resources.RadialChartHS">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:NovaFinance.My.Resources.Resources.search16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:NovaFinance.My.Resources.Resources.search161">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:NovaFinance.My.Resources.Resources.shutdown2">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:NovaFinance.My.Resources.Resources.SychronizeListHS">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:NovaFinance.My.Resources.Resources.TaskHS">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:NovaFinance.My.Resources.Resources.tools32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:NovaFinance.My.Resources.Resources.user32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:NovaFinance.My.Resources.Resources.ZoomHS">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="T:NovaFinance.Commissions">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:NovaFinance.Commissions.Commissions_HeaderDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaFinance.Commissions.CommissionDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaFinance.Commissions.AccountManagerDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaFinance.Commissions.Commissions_HeaderRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaFinance.Commissions.CommissionRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaFinance.Commissions.AccountManagerRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaFinance.Commissions.Commissions_HeaderRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaFinance.Commissions.CommissionRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaFinance.Commissions.AccountManagerRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaFinance.CommissionsTableAdapters.tbCommissions_Header">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaFinance.CommissionsTableAdapters.taCommission">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaFinance.CommissionsTableAdapters.taAccountManaget">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaFinance.CommissionsTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:NovaFinance.CommissionsTableAdapters.TableAdapterManager.UpdateUpdatedRows(NovaFinance.Commissions,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:NovaFinance.CommissionsTableAdapters.TableAdapterManager.UpdateInsertedRows(NovaFinance.Commissions,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:NovaFinance.CommissionsTableAdapters.TableAdapterManager.UpdateDeletedRows(NovaFinance.Commissions,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:NovaFinance.CommissionsTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:NovaFinance.CommissionsTableAdapters.TableAdapterManager.UpdateAll(NovaFinance.Commissions)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:NovaFinance.CommissionsTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:NovaFinance.CommissionsTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:NovaFinance.CreditNote">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:NovaFinance.CreditNote.CreditNoteDetailDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaFinance.CreditNote.CreditNoteHeaderDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaFinance.CreditNote.CreditNoteDetailRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaFinance.CreditNote.CreditNoteHeaderRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaFinance.CreditNote.CreditNoteDetailRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaFinance.CreditNote.CreditNoteHeaderRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaFinance.CreditNoteTableAdapters.taCreditNoteDetail">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaFinance.CreditNoteTableAdapters.CreditNoteHeaderTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaFinance.CreditNoteTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:NovaFinance.CreditNoteTableAdapters.TableAdapterManager.UpdateUpdatedRows(NovaFinance.CreditNote,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:NovaFinance.CreditNoteTableAdapters.TableAdapterManager.UpdateInsertedRows(NovaFinance.CreditNote,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:NovaFinance.CreditNoteTableAdapters.TableAdapterManager.UpdateDeletedRows(NovaFinance.CreditNote,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:NovaFinance.CreditNoteTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:NovaFinance.CreditNoteTableAdapters.TableAdapterManager.UpdateAll(NovaFinance.CreditNote)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:NovaFinance.CreditNoteTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:NovaFinance.CreditNoteTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:NovaFinance.DataSetAuditLog">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:NovaFinance.DataSetAuditLog.AuditLogDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaFinance.DataSetAuditLog.AuditLogRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaFinance.DataSetAuditLog.AuditLogRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaFinance.DataSetAuditLogTableAdapters.AuditLogTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaFinance.DataSetAuditLogTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:NovaFinance.DataSetAuditLogTableAdapters.TableAdapterManager.UpdateUpdatedRows(NovaFinance.DataSetAuditLog,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:NovaFinance.DataSetAuditLogTableAdapters.TableAdapterManager.UpdateInsertedRows(NovaFinance.DataSetAuditLog,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:NovaFinance.DataSetAuditLogTableAdapters.TableAdapterManager.UpdateDeletedRows(NovaFinance.DataSetAuditLog,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:NovaFinance.DataSetAuditLogTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:NovaFinance.DataSetAuditLogTableAdapters.TableAdapterManager.UpdateAll(NovaFinance.DataSetAuditLog)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:NovaFinance.DataSetAuditLogTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:NovaFinance.DataSetAuditLogTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:NovaFinance.DataSetClient">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:NovaFinance.DataSetClient.ClientDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaFinance.DataSetClient.ClientRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaFinance.DataSetClient.ClientRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaFinance.DataSetClientTableAdapters.ClientTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaFinance.DataSetClientTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:NovaFinance.DataSetClientTableAdapters.TableAdapterManager.UpdateUpdatedRows(NovaFinance.DataSetClient,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:NovaFinance.DataSetClientTableAdapters.TableAdapterManager.UpdateInsertedRows(NovaFinance.DataSetClient,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:NovaFinance.DataSetClientTableAdapters.TableAdapterManager.UpdateDeletedRows(NovaFinance.DataSetClient,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:NovaFinance.DataSetClientTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:NovaFinance.DataSetClientTableAdapters.TableAdapterManager.UpdateAll(NovaFinance.DataSetClient)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:NovaFinance.DataSetClientTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:NovaFinance.DataSetClientTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:NovaFinance.DataSetSetting">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:NovaFinance.DataSetSetting.SettingDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaFinance.DataSetSetting.SettingGroupDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaFinance.DataSetSetting.SettingRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaFinance.DataSetSetting.SettingGroupRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaFinance.DataSetSetting.SettingRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaFinance.DataSetSetting.SettingGroupRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaFinance.DataSetSettingTableAdapters.SettingTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaFinance.DataSetSettingTableAdapters.SettingGroupTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaFinance.DataSetSettingTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:NovaFinance.DataSetSettingTableAdapters.TableAdapterManager.UpdateUpdatedRows(NovaFinance.DataSetSetting,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:NovaFinance.DataSetSettingTableAdapters.TableAdapterManager.UpdateInsertedRows(NovaFinance.DataSetSetting,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:NovaFinance.DataSetSettingTableAdapters.TableAdapterManager.UpdateDeletedRows(NovaFinance.DataSetSetting,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:NovaFinance.DataSetSettingTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:NovaFinance.DataSetSettingTableAdapters.TableAdapterManager.UpdateAll(NovaFinance.DataSetSetting)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:NovaFinance.DataSetSettingTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:NovaFinance.DataSetSettingTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:NovaFinance.ExportBatch">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:NovaFinance.ExportBatch.BatchHeaderDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaFinance.ExportBatch.BatchHeaderRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaFinance.ExportBatch.BatchHeaderRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaFinance.ExportBatchTableAdapters.BatchHeaderTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaFinance.ExportBatchTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:NovaFinance.ExportBatchTableAdapters.TableAdapterManager.UpdateUpdatedRows(NovaFinance.ExportBatch,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:NovaFinance.ExportBatchTableAdapters.TableAdapterManager.UpdateInsertedRows(NovaFinance.ExportBatch,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:NovaFinance.ExportBatchTableAdapters.TableAdapterManager.UpdateDeletedRows(NovaFinance.ExportBatch,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:NovaFinance.ExportBatchTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:NovaFinance.ExportBatchTableAdapters.TableAdapterManager.UpdateAll(NovaFinance.ExportBatch)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:NovaFinance.ExportBatchTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:NovaFinance.ExportBatchTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:NovaFinance.Invoicing">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:NovaFinance.Invoicing.InvoicingDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaFinance.Invoicing.PeriodDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaFinance.Invoicing.ContractTypeDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaFinance.Invoicing.InvoicingRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaFinance.Invoicing.PeriodRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaFinance.Invoicing.ContractTypeRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaFinance.Invoicing.InvoicingRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaFinance.Invoicing.PeriodRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaFinance.Invoicing.ContractTypeRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaFinance.InvoicingTableAdapters.taInvoicing">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaFinance.InvoicingTableAdapters.taPeriod">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaFinance.InvoicingTableAdapters.taContractType">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaFinance.InvoicingTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:NovaFinance.InvoicingTableAdapters.TableAdapterManager.UpdateUpdatedRows(NovaFinance.Invoicing,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:NovaFinance.InvoicingTableAdapters.TableAdapterManager.UpdateInsertedRows(NovaFinance.Invoicing,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:NovaFinance.InvoicingTableAdapters.TableAdapterManager.UpdateDeletedRows(NovaFinance.Invoicing,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:NovaFinance.InvoicingTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:NovaFinance.InvoicingTableAdapters.TableAdapterManager.UpdateAll(NovaFinance.Invoicing)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:NovaFinance.InvoicingTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:NovaFinance.InvoicingTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:NovaFinance.Product">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:NovaFinance.Product.ProductDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaFinance.Product.ProductRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaFinance.Product.ProductRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaFinance.ProductTableAdapters.ProductTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaFinance.ProductTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:NovaFinance.ProductTableAdapters.TableAdapterManager.UpdateUpdatedRows(NovaFinance.Product,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:NovaFinance.ProductTableAdapters.TableAdapterManager.UpdateInsertedRows(NovaFinance.Product,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:NovaFinance.ProductTableAdapters.TableAdapterManager.UpdateDeletedRows(NovaFinance.Product,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:NovaFinance.ProductTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:NovaFinance.ProductTableAdapters.TableAdapterManager.UpdateAll(NovaFinance.Product)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:NovaFinance.ProductTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:NovaFinance.ProductTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:NovaFinance.Royalty">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:NovaFinance.Royalty.RoyaltysDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaFinance.Royalty.RoyaltyDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaFinance.Royalty.RoyaltysRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaFinance.Royalty.RoyaltyRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaFinance.Royalty.RoyaltysRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaFinance.Royalty.RoyaltyRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaFinance.RoyaltyTableAdapters.RoyaltysTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaFinance.RoyaltyTableAdapters.RoyaltyTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaFinance.RoyaltyTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:NovaFinance.RoyaltyTableAdapters.TableAdapterManager.UpdateUpdatedRows(NovaFinance.Royalty,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:NovaFinance.RoyaltyTableAdapters.TableAdapterManager.UpdateInsertedRows(NovaFinance.Royalty,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:NovaFinance.RoyaltyTableAdapters.TableAdapterManager.UpdateDeletedRows(NovaFinance.Royalty,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:NovaFinance.RoyaltyTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:NovaFinance.RoyaltyTableAdapters.TableAdapterManager.UpdateAll(NovaFinance.Royalty)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:NovaFinance.RoyaltyTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:NovaFinance.RoyaltyTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:NovaFinance.StorePayment">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:NovaFinance.StorePayment.YearDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaFinance.StorePayment.MonthsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaFinance.StorePayment.StorePaymentDueDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaFinance.StorePayment.InvoiceDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaFinance.StorePayment.InvoiceDetailDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaFinance.StorePayment.InvoicePaymentScheduleDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaFinance.StorePayment.InvoiceOptionalFieldsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaFinance.StorePayment.InvoiceDetailOptionalFieldsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaFinance.StorePayment.HeadOfficeDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaFinance.StorePayment.YearRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaFinance.StorePayment.MonthsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaFinance.StorePayment.StorePaymentDueRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaFinance.StorePayment.InvoiceRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaFinance.StorePayment.InvoiceDetailRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaFinance.StorePayment.InvoicePaymentScheduleRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaFinance.StorePayment.InvoiceOptionalFieldsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaFinance.StorePayment.InvoiceDetailOptionalFieldsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaFinance.StorePayment.HeadOfficeRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaFinance.StorePayment.YearRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaFinance.StorePayment.MonthsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaFinance.StorePayment.StorePaymentDueRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaFinance.StorePayment.InvoiceRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaFinance.StorePayment.InvoiceDetailRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaFinance.StorePayment.InvoicePaymentScheduleRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaFinance.StorePayment.InvoiceOptionalFieldsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaFinance.StorePayment.InvoiceDetailOptionalFieldsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaFinance.StorePayment.HeadOfficeRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaFinance.StorePaymentTableAdapters.taYear">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaFinance.StorePaymentTableAdapters.taMonths">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaFinance.StorePaymentTableAdapters.taStorePaymentDue">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaFinance.StorePaymentTableAdapters.taInvoice">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaFinance.StorePaymentTableAdapters.taInvoiceDetail">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaFinance.StorePaymentTableAdapters.taInvoicePaymentSchedule">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaFinance.StorePaymentTableAdapters.taInvoiceOptionalFields">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaFinance.StorePaymentTableAdapters.taInvoiceDetailOptionalFields">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaFinance.StorePaymentTableAdapters.taHeadOffice">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaFinance.StorePaymentTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:NovaFinance.StorePaymentTableAdapters.TableAdapterManager.UpdateUpdatedRows(NovaFinance.StorePayment,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:NovaFinance.StorePaymentTableAdapters.TableAdapterManager.UpdateInsertedRows(NovaFinance.StorePayment,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:NovaFinance.StorePaymentTableAdapters.TableAdapterManager.UpdateDeletedRows(NovaFinance.StorePayment,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:NovaFinance.StorePaymentTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:NovaFinance.StorePaymentTableAdapters.TableAdapterManager.UpdateAll(NovaFinance.StorePayment)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:NovaFinance.StorePaymentTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:NovaFinance.StorePaymentTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:NovaFinance.Terms">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:NovaFinance.Terms.TermsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaFinance.Terms.TermsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaFinance.Terms.TermsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaFinance.TermsTableAdapters.TermsTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaFinance.TermsTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:NovaFinance.TermsTableAdapters.TableAdapterManager.UpdateUpdatedRows(NovaFinance.Terms,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:NovaFinance.TermsTableAdapters.TableAdapterManager.UpdateInsertedRows(NovaFinance.Terms,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:NovaFinance.TermsTableAdapters.TableAdapterManager.UpdateDeletedRows(NovaFinance.Terms,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:NovaFinance.TermsTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:NovaFinance.TermsTableAdapters.TableAdapterManager.UpdateAll(NovaFinance.Terms)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:NovaFinance.TermsTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:NovaFinance.TermsTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
</members>
</doc>
