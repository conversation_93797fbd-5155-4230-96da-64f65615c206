﻿using DataAccess;
using DataService.Security;
using DataService.Security.Roles;
using DataService.Security.Users;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using Universal.Entities;

namespace DataService
{
    public static class SecurityServices
    {

        #region API services

        public static Session GetNewSession(ref string errormessage, string username, string password)
        {
            return ApiClient.GetNewSession(ref errormessage, username, password);
        }

        public static void SendPasswordResetCode(ref string errormessage, string usernameoremailaddress)
        {
            ApiClient.SendPasswordResetCode(ref errormessage, usernameoremailaddress);
        }

        public static void ResetPassword(ref string errormessage, string usernameoremailaddress, string passwordresetcode, string newpassword)
        {
            ApiClient.ResetPassword(ref errormessage, usernameoremailaddress, passwordresetcode, newpassword);
        }

        public static void ChangePassword(ref string errormessage, string currentpassword, string newpassword)
        {
            Guid sessionid = Universal.Settings.CurrentSession.SessionId;
            ApiClient.ChangePassword(ref errormessage, sessionid, currentpassword, newpassword);
        }

        public static void SendForgottenUsername(ref string errormessage, string emailaddress)
        {
            ApiClient.SendForgottenUsername(ref errormessage, emailaddress);
        }

        public static void SendEmailVerificationCode(ref string errormessage, string emailaddress)
        {
            ApiClient.SendEmailVerificationCode(ref errormessage, emailaddress);
        }

        public static void CreateUser
            (
            ref string errormessage,
            string emailaddress,
            string emailverificationcode,
            string username,
            string password,
            string firstname,
            string lastname,
            string mobilephone,
            bool gender
            )
        {
            ApiClient.CreateUser(ref errormessage, emailaddress, emailverificationcode, username, password, firstname, lastname, mobilephone, gender);
        }

        #endregion


        #region Application startup and shutdown services

        public static void GetTermsOfUse(ref string errormessage, ref Guid termsofuseid, ref string termsofusetext)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new GetTermsOfUseCommand();
                manager.ExecuteCommand(ref errormessage, command, new GetTermsOfUseCommandExecutor());
                termsofuseid = command.TermsOfUseId;
                termsofusetext = command.TermsOfUseText;
            }
        }

        public static void SaveAcceptanceOfTermsOfUse(ref string errormessage, Guid termsofuseid)
        {
            if (termsofuseid == Guid.Empty)
            {
                errormessage = "Invalid ID for the Terms of Use was specified in DataAccess.Security.Services.SaveAcceptanceOfTermsOfUse.";
                return;
            }

            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new SaveAcceptanceOfTermsOfUseCommand(Universal.Settings.CurrentSession.User.UserId, termsofuseid);
                manager.ExecuteCommand(ref errormessage, command, new SaveAcceptanceOfTermsOfUseCommandExecutor());
            }
        }

        public static void UpdateThemeColor(ref string errormessage, Color color)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new UpdateThemeColorCommand(Universal.Settings.CurrentSession.SessionId, color);
                manager.ExecuteCommand(ref errormessage, command, new UpdateThemeColorCommandExecutor());
            }
        }

        public static void TerminateSession(ref string errormessage)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new TerminateSessionCommand(Universal.Settings.CurrentSession.SessionId);
                manager.ExecuteCommand(ref errormessage, command, new TerminateSessionCommandExecutor());
            }
        }

        #endregion


        #region Users

        public static DataTable GetTableOfUsers(ref string errormessage, bool getdeletedrows)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                Guid sessionid = Universal.Settings.CurrentSession.SessionId;
                var command = new GetTableOfUsersCommand(sessionid, getdeletedrows);
                manager.ExecuteCommand(ref errormessage, command, new GetTableOfUsersCommandExecutor());
                return command.Table;
            }
        }

        public static DataTable GetRolesOfMemberCandidates(ref string errormessage, Guid userid)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new GetRolesOfMemberCandidatesCommand(Universal.Settings.CurrentSession.SessionId, userid);
                manager.ExecuteCommand(ref errormessage, command, new GetRolesOfMemberCandidatesCommandExecutor());
                return command.Table;
            }
        }

        public static DataTable GetUserHistory(ref string errormessage, Guid userid)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new GetUserHistoryCommand(Universal.Settings.CurrentSession.SessionId, userid);
                manager.ExecuteCommand(ref errormessage, command, new GetUserHistoryCommandExecutor());
                return command.Table;
            }
        }

        public static DataTable GetUserAuditTrail(ref string errormessage, Guid userid)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new GetUserAuditTrailCommand(Universal.Settings.CurrentSession.SessionId, userid);
                manager.ExecuteCommand(ref errormessage, command, new GetUserAuditTrailCommandExecutor());
                return command.Table;
            }
        }

        public static DataTable GetRolesOfMember(ref string errormessage, Guid userid)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new GetRolesOfMemberCommand(Universal.Settings.CurrentSession.SessionId, userid);
                manager.ExecuteCommand(ref errormessage, command, new GetRolesOfMemberCommandExecutor());
                return command.Table;
            }
        }

        public static void AddRolesOfMember(ref string errormessage, Guid userid, List<DataRow> newroleslist)
        {
            string username = string.Empty;
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new AddRolesOfMemberCommand(Universal.Settings.CurrentSession.SessionId, userid, newroleslist);
                manager.ExecuteCommand(ref errormessage, command, new AddRolesOfMemberCommandExecutor());
                username = command.Username;
            }

            // Add legacy database roles to legacy usernames.
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.LegacyConnectionString))
            {
                var command = new AddLegacyRolesOfMemberCommand(username, newroleslist);
                manager.ExecuteCommand(ref errormessage, command, new AddLegacyRolesOfMemberCommandExecutor());
            }
        }

        public static void RemoveRolesOfMember(ref string errormessage, Guid userid, List<DataRow> roleslist)
        {
            string username = string.Empty;
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new RemoveRolesOfMemberCommand(Universal.Settings.CurrentSession.SessionId, userid, roleslist);
                manager.ExecuteCommand(ref errormessage, command, new RemoveRolesOfMemberCommandExecutor());
                username = command.Username;
            }

            // Remove legacy usernames from legacy database roles.
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.LegacyConnectionString))
            {
                var command = new RemoveLegacyRolesOfMemberCommand(username, roleslist);
                manager.ExecuteCommand(ref errormessage, command, new RemoveLegacyRolesOfMemberCommandExecutor());
            }
        }

        public static DataTable GetRolesOfOwner(ref string errormessage, Guid userid)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new GetRolesOfOwnerCommand(Universal.Settings.CurrentSession.SessionId, userid);
                manager.ExecuteCommand(ref errormessage, command, new GetRolesOfOwnerCommandExecutor());
                return command.Table;
            }
        }

        public static DataTable GetRolesOfOwnerCandidates(ref string errormessage, Guid userid)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new GetRolesOfOwnerCandidatesCommand(Universal.Settings.CurrentSession.SessionId, userid);
                manager.ExecuteCommand(ref errormessage, command, new GetRolesOfOwnerCandidatesCommandExecutor());
                return command.Table;
            }
        }

        public static void AddRolesOfOwner(ref string errormessage, Guid userid, List<DataRow> newroleslist)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new AddRolesOfOwnerCommand(Universal.Settings.CurrentSession.SessionId, userid, newroleslist);
                manager.ExecuteCommand(ref errormessage, command, new AddRolesOfOwnerCommandExecutor());
            }
        }

        public static void RemoveRolesOfOwner(ref string errormessage, Guid userid, List<DataRow> roleslist)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new RemoveRolesOfOwnerCommand(Universal.Settings.CurrentSession.SessionId, userid, roleslist);
                manager.ExecuteCommand(ref errormessage, command, new RemoveRolesOfOwnerCommandExecutor());
            }
        }


        public static DataTable GetUserChainPermissions(ref string errormessage, Guid UserId)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.LegacyConnectionString))
            {
                var command = new GetUserChainPermissionsCommand(UserId);
                manager.ExecuteCommand(ref errormessage, command, new GetUserChainPermissionsCommandExecutor());
                return command.Table;
            }
        }


        public static DataTable GetUserRoles(ref string errormessage, Guid UserId)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new GetUserRolesCommand(UserId);
                manager.ExecuteCommand(ref errormessage, command, new GetUserRolesCommandExecutor());
                return command.Table;
            }
        }

        public static void SetUserChainPermission(ref string errormessage, Guid UserId, List<DataRow> ChainID)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.LegacyConnectionString))
            {
                var command = new SetUserChainPermissionCommand(UserId, ChainID);
                manager.ExecuteCommand(ref errormessage, command, new SetUserChainPermissionCommandExecutor());
            }
        }

        public static void DeleteUserChainPermissions(ref string errormessage, Guid UserId, List<DataRow> ChainID)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.LegacyConnectionString))
            {
                var command = new DeleteUserChainPermissionsCommand(UserId, ChainID);
                manager.ExecuteCommand(ref errormessage, command, new DeleteUserChainPermissionsCommandExecutor());
            }
        }


        public static DataTable GetChainPermissionCandidates(ref string errormessage, Guid UserId)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.LegacyConnectionString))
            {
                var command = new GetChainPermissionCandidatesCommand(UserId);
                manager.ExecuteCommand(ref errormessage, command, new GetChainPermissionCandidatesCommandExecutor());

                return command.Table;
            }
        }

        public static void SaveUser(ref string errormessage, DataRow userrow)
        {
            Guid sessionid = Universal.Settings.CurrentSession.SessionId;
            Guid userid = (Guid)userrow["userid"];
            string firstname = userrow["firstname"].ToString();
            string lastname = userrow["lastname"].ToString();
            string emailaddress = userrow["email"].ToString();
            string mobilephone = userrow["mobilephone"].ToString();
            bool gender = (bool)userrow["gender"];
            string notes = userrow["notes"].ToString();
            bool deleted = (bool)userrow["deleted"];
            string username = userrow["username"].ToString();
            bool enabled = (bool)userrow["enabled"];

            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new UpdateUserCommand(sessionid, userid, firstname, lastname, emailaddress, mobilephone, gender, notes, deleted, username, enabled);
                manager.ExecuteCommand(ref errormessage, command, new UpdateUserCommandExecutor());
            }

            if (enabled)
            {
                // Enable legacy logins.
                using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.LegacyConnectionString))
                {
                    var command = new EnableLegacyLoginsCommand(new List<DataRow>() { userrow });
                    manager.ExecuteCommand(ref errormessage, command, new EnableLegacyLoginsCommandExecutor());
                }
            }
            else
            {
                // Disable legacy logins.
                using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.LegacyConnectionString))
                {
                    var command = new DisableLegacyLoginsCommand(new List<DataRow>() { userrow });
                    manager.ExecuteCommand(ref errormessage, command, new DisableLegacyLoginsCommandExecutor());
                }
            }
        }

        public static void DeleteUsers(ref string errormessage, List<DataRow> userslist)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new DeleteUsersCommand(Universal.Settings.CurrentSession.SessionId, userslist);
                manager.ExecuteCommand(ref errormessage, command, new DeleteUsersCommandExecutor());
            }

            // Disable legacy logins.
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.LegacyConnectionString))
            {
                var command = new DisableLegacyLoginsCommand(userslist);
                manager.ExecuteCommand(ref errormessage, command, new DisableLegacyLoginsCommandExecutor());
            }
        }

        #endregion


        #region Roles

        public static DataTable GetTableOfRoles(ref string errormessage)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                Guid sessionid = Universal.Settings.CurrentSession.SessionId;
                var command = new GetTableOfRolesCommand(sessionid);
                manager.ExecuteCommand(ref errormessage, command, new GetTableOfRolesCommandExecutor());
                return command.Table;
            }
        }

        public static DataTable GetRoleMembers(ref string errormessage, Guid roleid)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new GetRoleMembersCommand(Universal.Settings.CurrentSession.SessionId, roleid);
                manager.ExecuteCommand(ref errormessage, command, new GetRoleMembersCommandExecutor());
                return command.Table;
            }
        }

        public static DataTable GetRoleMemberCandidates(ref string errormessage, Guid roleid)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new GetRoleMemberCandidatesCommand(Universal.Settings.CurrentSession.SessionId, roleid);
                manager.ExecuteCommand(ref errormessage, command, new GetRoleMemberCandidatesCommandExecutor());
                return command.Table;
            }
        }

        public static DataTable GetRoleHistory(ref string errormessage, Guid roleid)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new GetRoleHistoryCommand(Universal.Settings.CurrentSession.SessionId, roleid);
                manager.ExecuteCommand(ref errormessage, command, new GetRoleHistoryCommandExecutor());
                return command.Table;
            }
        }

        public static void AddRoleMembers(ref string errormessage, Guid roleid, List<DataRow> newmemberslist)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new AddRoleMembersCommand(Universal.Settings.CurrentSession.SessionId, roleid, newmemberslist);
                manager.ExecuteCommand(ref errormessage, command, new AddRoleMembersCommandExecutor());
            }

            // Add legacy usernames to legacy database roles.
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.LegacyConnectionString))
            {
                var command = new AddLegacyRoleMembersCommand(roleid, newmemberslist);
                manager.ExecuteCommand(ref errormessage, command, new AddLegacyRoleMembersCommandExecutor());
            }
        }

        public static void RemoveRoleMembers(ref string errormessage, Guid roleid, List<DataRow> memberslist)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new RemoveRoleMembersCommand(Universal.Settings.CurrentSession.SessionId, roleid, memberslist);
                manager.ExecuteCommand(ref errormessage, command, new RemoveRoleMembersCommandExecutor());
            }

            // Remove legacy usernames from legacy database roles.
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.LegacyConnectionString))
            {
                var command = new RemoveLegacyRoleMembersCommand(roleid, memberslist);
                manager.ExecuteCommand(ref errormessage, command, new RemoveLegacyRoleMembersCommandExecutor());
            }
        }

        public static DataTable GetRoleOwners(ref string errormessage, Guid roleid)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new GetRoleOwnersCommand(Universal.Settings.CurrentSession.SessionId, roleid);
                manager.ExecuteCommand(ref errormessage, command, new GetRoleOwnersCommandExecutor());
                return command.Table;
            }
        }

        public static DataTable GetRoleOwnerCandidates(ref string errormessage, Guid roleid)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new GetRoleOwnerCandidatesCommand(Universal.Settings.CurrentSession.SessionId, roleid);
                manager.ExecuteCommand(ref errormessage, command, new GetRoleOwnerCandidatesCommandExecutor());
                return command.Table;
            }
        }

        public static void AddRoleOwners(ref string errormessage, Guid roleid, List<DataRow> newOwnerslist)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new AddRoleOwnersCommand(Universal.Settings.CurrentSession.SessionId, roleid, newOwnerslist);
                manager.ExecuteCommand(ref errormessage, command, new AddRoleOwnersCommandExecutor());
            }
        }

        public static void RemoveRoleOwners(ref string errormessage, Guid roleid, List<DataRow> Ownerslist)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new RemoveRoleOwnersCommand(Universal.Settings.CurrentSession.SessionId, roleid, Ownerslist);
                manager.ExecuteCommand(ref errormessage, command, new RemoveRoleOwnersCommandExecutor());
            }
        }

        public static bool IsInRole(ref string errormessage, string rolename)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new CheckRoleMembershipOfUserCommand(Universal.Settings.CurrentSession.User.UserId, rolename);
                manager.ExecuteCommand(ref errormessage, command, new CheckRoleMembershipOfUserCommandExecutor());
                return command.IsMember;
            }
        }

        public static void SaveRole(ref string errormessage, DataRow rolerow)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                Guid sessionid = Universal.Settings.CurrentSession.SessionId;
                Guid roleid = Guid.Empty;
                Guid.TryParse(rolerow["roleid"].ToString(), out roleid);
                string rolename = rolerow["rolename"].ToString();
                string roledescription = rolerow["roledescription"].ToString();

                if (roleid == Guid.Empty)
                {
                    // We're trying to create a new row.
                    var command = new CreateRoleCommand(sessionid, rolename, roledescription);
                    manager.ExecuteCommand(ref errormessage, command, new CreateRoleCommandExecutor());
                    rolerow["roleid"] = command.RoleId;
                }
                else
                {
                    // We're trying to update an existing row.
                    var command = new UpdateRoleCommand(sessionid, roleid, rolename, roledescription);
                    manager.ExecuteCommand(ref errormessage, command, new UpdateRoleCommandExecutor());
                }
            }
        }

        public static void DeleteRoles(ref string errormessage, List<DataRow> rowstodelete)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new DeleteRolesCommand(Universal.Settings.CurrentSession.SessionId, rowstodelete);
                manager.ExecuteCommand(ref errormessage, command, new DeleteRolesCommandExecutor());
            }
        }

        #endregion

    }
}
