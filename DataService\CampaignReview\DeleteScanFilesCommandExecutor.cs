﻿using DataAccess;

namespace DataService.CampaignReview
{
    class DeleteScanFilesCommandExecutor : CommandExecutor<DeleteScanFilesCommand>
    {

        public override void Execute(DeleteScanFilesCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.DeleteScanFiles))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.AddInputParameter("scanimportfileids", command.ScanFiles);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
            }
        }

    }
}
