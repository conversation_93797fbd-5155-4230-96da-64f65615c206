﻿using System;
using System.ComponentModel;
using System.Data;
using Framework.Forms;
using System.Windows.Forms;
using System.Drawing;

namespace Framework.Controls.GridSystem
{
    public partial class GridFiller : DataForm
    {
        private BackgroundWorker FillWorker;

        protected Color DefaultRowBackColor = FrameworkSettings.Colors.GRIDCELLBACKCOLOR;
        protected Color DefaultRowSelectionBackColor = FrameworkSettings.Colors.GRIDCELLSELECTIONBACKCOLOR;
        protected Color DefaultRowForeColor = FrameworkSettings.Colors.GRIDCELLFORECOLOR;
        protected Color DefaultRowSelectionForeColor = FrameworkSettings.Colors.GRIDCELLSELECTIONFORECOLOR;

        protected Color DisabledRowBackColor = FrameworkSettings.Colors.GRIDCELLDISABLEDBACKCOLOR;
        protected Color DisabledRowSelectionBackColor = FrameworkSettings.Colors.GRIDCELLDISABLEDSELECTIONBACKCOLOR;
        protected Color DisabledRowForeColor = FrameworkSettings.Colors.GRIDCELLDISABLEDFORECOLOR;
        protected Color DisabledRowSelectionForeColor = FrameworkSettings.Colors.GRIDCELLDISABLEDSELECTIONFORECOLOR;


        #region Startup

        public GridFiller()
        {
            InitializeComponent();
            FillWorker = new BackgroundWorker();
            SubscribeToEvents();
        }

        private void SubscribeToEvents()
        {
            flatButtonSearch.Click += FlatButtonSearch_Click;
            FillWorker.DoWork += FillWorker_DoWork;
            FillWorker.RunWorkerCompleted += FillWorker_RunWorkerCompleted;
            flatButtonClear.Click += FlatButtonClear_Click;
            DirtyStateManager.IsDirtyChanged += DirtyStateManager_IsDirtyChanged;
            Load += GridFiller_Load;
        }

        private void GridFiller_Load(object sender, EventArgs e)
        {
            UpdateClearButtonVisibility();
        }

        #endregion


        #region Search button

        private void FlatButtonSearch_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.OK;
            Close();
        }

        #endregion


        #region Clear button

        private void FlatButtonClear_Click(object sender, EventArgs e)
        {
            DirtyStateManager.RejectChanges();
        }

        private void UpdateClearButtonVisibility()
        {
            flatButtonClear.Visible = DirtyStateManager.IsDirty;
        }

        private void DirtyStateManager_IsDirtyChanged(object sender, EventArgs e)
        {
            UpdateClearButtonVisibility();
        }

        #endregion


        #region Error message

        private string _ErrorMessage = string.Empty;
        public string ErrorMessage
        {
            get { return _ErrorMessage; }
            set
            {
                if (_ErrorMessage != value)
                {
                    _ErrorMessage = value;
                }
            }
        }

        #endregion


        #region Fill the grid

        protected Func<object[], DataTable> GetGridDataMethod;
        protected object[] ArgumentsForGetGridDataMethod;
        protected Action UpdateArgumentsForGetGridDataMethod;
        protected bool GetDataAsynchronously = false;
        protected Action SetTitleMethod;
        protected Action AddColumnsMethod;
        protected DataTable GridData;

        private Grid _Grid;
        public Grid Grid
        {
            get { return _Grid; }
            set
            {
                _Grid = value;
                SetTitleMethod?.Invoke();
                AddColumnsMethod?.Invoke();
            }
        }

        protected void Fill(bool displaysearchcriteria)
        {
            if (displaysearchcriteria)
            {
                ShowDialog();
            }
            else
            {
                UpdateArgumentsForGetGridDataMethod?.Invoke();
                GetData();
            }
        }

        private void GetData()
        {
            // Raise the grid fill attempt started event.
            OnGridFillAttemptStarted(EventArgs.Empty);

            // Clear any existing error message.
            ErrorMessage = string.Empty;

            if (GetDataAsynchronously)
            {
                // Build an array of arguments to pass to the background worker.
                object[] args = { GetGridDataMethod, ArgumentsForGetGridDataMethod };

                // Start the background worker.
                FillWorker.RunWorkerAsync(args);
            }
            else
            {
                // Get a table of data by invoking the method and passing the remaining arguments in the array.
                var griddata = GetGridDataMethod.Invoke(ArgumentsForGetGridDataMethod);

                // Set the table as the data source of the grid.
                SetGridDataSource(griddata);
            }
        }

        private void FillWorker_DoWork(object sender, DoWorkEventArgs e)
        {
            // Get the array of arguments provided.
            var workerargs = (object[])e.Argument;

            // The first object in the array is the method to be called.
            var getdatamethod = (Func<object[], DataTable>)workerargs[0];

            // The remaining objects in the array are the arguments for the method.
            object[] methodargs = (object[])workerargs[1];

            // Get a table of data by invoking the method and passing the remaining arguments in the array.
            var griddata = getdatamethod.Invoke(methodargs);

            // Set the table as the result of the background operation.
            e.Result = griddata;
        }

        private void FillWorker_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            SetGridDataSource((DataTable)e.Result);
        }

        private void SetGridDataSource(DataTable griddata)
        {
            if (griddata != null)
            {
                Grid.DataSource = griddata;
                if (griddata.Rows.Count > 0)
                {
                    Grid.Visible = true;
                }
            }
            OnGridFillAttemptCompleted(EventArgs.Empty);
        }

        /// <summary>
        /// Occures when an attempt to fill the grid with data begins.
        /// </summary>
        public event EventHandler GridFillAttemptStarted;

        protected virtual void OnGridFillAttemptStarted(EventArgs e)
        {
            GridFillAttemptStarted?.Invoke(this, e);
        }

        public event EventHandler GridFillAttemptCompleted;

        protected virtual void OnGridFillAttemptCompleted(EventArgs e)
        {
            GridFillAttemptCompleted?.Invoke(this, e);
        }

        #endregion

    }
}
