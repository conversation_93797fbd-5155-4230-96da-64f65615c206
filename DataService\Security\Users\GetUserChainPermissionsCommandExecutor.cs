﻿using DataAccess;

namespace DataService.Security.Users
{
   internal class GetUserChainPermissionsCommandExecutor : CommandExecutor<GetUserChainPermissionsCommand>
    {
        public override void Execute(GetUserChainPermissionsCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.GetUserChainPermissions))
            {
                storedprocedure.AddInputParameter("Userid", command.Userid);

                storedprocedure.Execute();

                command.ErrorMessage = storedprocedure.ErrorMessage;

                if (storedprocedure.ErrorMessage.Contains("permission"))
                {
                    command.ErrorMessage = "You do not have permssions to view or make changes to Chains.";
                }
                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    command.Table = storedprocedure.OutputTable; 
                }
            }
        }
    }
}
