﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DataSetStoreGroups" targetNamespace="http://tempuri.org/DataSetGroupChains.xsd" xmlns:mstns="http://tempuri.org/DataSetGroupChains.xsd" xmlns="http://tempuri.org/DataSetGroupChains.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="1" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="NovaDBConnectionString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Public" Name="NovaDBConnectionString (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.NovaData.My.MySettings.GlobalReference.Default.NovaDBConnectionString" Provider="System.Data.SqlClient" />
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="DBConnection" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="DBConnection (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.NovaData.My.MySettings.GlobalReference.Default.DBConnection" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="GroupChainTableAdapter" GeneratorDataComponentClassName="GroupChainTableAdapter" Name="GroupChain" UserDataComponentName="GroupChainTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="NovaDBConnectionString (MySettings)" DbObjectName="NovaDB.Store.GroupChain" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Store].[GroupChain] WHERE (([GroupChainID] = @Original_GroupChainID) AND ([GroupChainName] = @Original_GroupChainName) AND ([Dormant] = @Original_Dormant))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_GroupChainID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="GroupChainID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_GroupChainName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="GroupChainName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Dormant" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Store].[GroupChain] ([GroupChainName], [Dormant]) VALUES (@GroupChainName, @Dormant);
SELECT GroupChainID, GroupChainName, Dormant FROM Store.GroupChain WHERE (GroupChainID = SCOPE_IDENTITY()) ORDER BY GroupChainID</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@GroupChainName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="GroupChainName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Dormant" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT GroupChainID, GroupChainName, Dormant
FROM     Store.GroupChain
ORDER by GroupChainId asc</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Store].[GroupChain] SET [GroupChainName] = @GroupChainName, [Dormant] = @Dormant WHERE (([GroupChainID] = @Original_GroupChainID) AND ([GroupChainName] = @Original_GroupChainName) AND ([Dormant] = @Original_Dormant));
SELECT GroupChainID, GroupChainName, Dormant FROM Store.GroupChain WHERE (GroupChainID = @GroupChainID) ORDER BY GroupChainID</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@GroupChainName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="GroupChainName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Dormant" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_GroupChainID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="GroupChainID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_GroupChainName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="GroupChainName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Dormant" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="GroupChainID" ColumnName="GroupChainID" DataSourceName="NovaDB.Store.GroupChain" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@GroupChainID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="GroupChainID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="GroupChainID" DataSetColumn="GroupChainID" />
              <Mapping SourceColumn="GroupChainName" DataSetColumn="GroupChainName" />
              <Mapping SourceColumn="Dormant" DataSetColumn="Dormant" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="StoreTableAdapter" GeneratorDataComponentClassName="StoreTableAdapter" Name="Store" UserDataComponentName="StoreTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Store].[Store] WHERE (([StoreID] = @Original_StoreID) AND ([StoreName] = @Original_StoreName))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_StoreID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StoreID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_StoreName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="StoreName" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT StoreID, StoreName
FROM     Store.Store
where store.storeid not in
(select distinct storeid from store.ChainGroupStore)
</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Store].[Store] SET [StoreName] = @StoreName WHERE (([StoreID] = @Original_StoreID) AND ([StoreName] = @Original_StoreName));
SELECT StoreID, StoreName FROM Store.Store WHERE (StoreID = @StoreID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@StoreName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="StoreName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_StoreID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StoreID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_StoreName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="StoreName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="StoreID" ColumnName="StoreID" DataSourceName="NovaDB.Store.Store" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@StoreID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="StoreID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="StoreID" DataSetColumn="StoreID" />
              <Mapping SourceColumn="StoreName" DataSetColumn="StoreName" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ChainGroupStoreTableAdapter" GeneratorDataComponentClassName="ChainGroupStoreTableAdapter" Name="ChainGroupStore" UserDataComponentName="ChainGroupStoreTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="NovaDBConnectionString (MySettings)" DbObjectName="NovaDB.Store.ChainGroupStore" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Store].[ChainGroupStore] WHERE (([StoreID] = @Original_StoreID) AND ([GroupChainID] = @Original_GroupChainID) AND ([StoreName] = @Original_StoreName))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_StoreID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StoreID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_GroupChainID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="GroupChainID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_StoreName" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="StoreName" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Store].[ChainGroupStore] ([StoreID], [GroupChainID], [StoreName]) VALUES (@StoreID, @GroupChainID, @StoreName);
SELECT StoreID, GroupChainID, StoreName FROM Store.ChainGroupStore WHERE (GroupChainID = @GroupChainID) AND (StoreID = @StoreID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@StoreID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StoreID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@GroupChainID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="GroupChainID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@StoreName" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="StoreName" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT StoreID, GroupChainID, StoreName
FROM     Store.ChainGroupStore
WHERE  (GroupChainID = @GroupChainId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="GroupChainId" ColumnName="GroupChainID" DataSourceName="NovaDB.Store.ChainGroupStore" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@GroupChainId" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="GroupChainID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Store].[ChainGroupStore] SET [StoreID] = @StoreID, [GroupChainID] = @GroupChainID, [StoreName] = @StoreName WHERE (([StoreID] = @Original_StoreID) AND ([GroupChainID] = @Original_GroupChainID) AND ([StoreName] = @Original_StoreName));
SELECT StoreID, GroupChainID, StoreName FROM Store.ChainGroupStore WHERE (GroupChainID = @GroupChainID) AND (StoreID = @StoreID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@StoreID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StoreID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@GroupChainID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="GroupChainID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@StoreName" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="StoreName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_StoreID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StoreID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_GroupChainID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="GroupChainID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_StoreName" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="StoreName" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="StoreID" DataSetColumn="StoreID" />
              <Mapping SourceColumn="GroupChainID" DataSetColumn="GroupChainID" />
              <Mapping SourceColumn="StoreName" DataSetColumn="StoreName" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DataSetStoreGroups" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="False" msprop:Generator_DataSetName="DataSetStoreGroups" msprop:Generator_UserDSName="DataSetStoreGroups">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="GroupChain" msprop:Generator_TableClassName="GroupChainDataTable" msprop:Generator_TableVarName="tableGroupChain" msprop:Generator_RowChangedName="GroupChainRowChanged" msprop:Generator_TablePropName="GroupChain" msprop:Generator_RowDeletingName="GroupChainRowDeleting" msprop:Generator_RowChangingName="GroupChainRowChanging" msprop:Generator_RowEvHandlerName="GroupChainRowChangeEventHandler" msprop:Generator_RowDeletedName="GroupChainRowDeleted" msprop:Generator_RowClassName="GroupChainRow" msprop:Generator_UserTableName="GroupChain" msprop:Generator_RowEvArgName="GroupChainRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="GroupChainID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msprop:Generator_ColumnVarNameInTable="columnGroupChainID" msprop:Generator_ColumnPropNameInRow="GroupChainID" msprop:Generator_ColumnPropNameInTable="GroupChainIDColumn" msprop:Generator_UserColumnName="GroupChainID" type="xs:int" />
              <xs:element name="GroupChainName" msprop:Generator_ColumnVarNameInTable="columnGroupChainName" msprop:Generator_ColumnPropNameInRow="GroupChainName" msprop:Generator_ColumnPropNameInTable="GroupChainNameColumn" msprop:Generator_UserColumnName="GroupChainName" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Dormant" msprop:Generator_ColumnVarNameInTable="columnDormant" msprop:Generator_ColumnPropNameInRow="Dormant" msprop:Generator_ColumnPropNameInTable="DormantColumn" msprop:Generator_UserColumnName="Dormant" type="xs:boolean" default="false" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Store" msprop:Generator_TableClassName="StoreDataTable" msprop:Generator_TableVarName="tableStore" msprop:Generator_TablePropName="Store" msprop:Generator_RowDeletingName="StoreRowDeleting" msprop:Generator_RowChangingName="StoreRowChanging" msprop:Generator_RowEvHandlerName="StoreRowChangeEventHandler" msprop:Generator_RowDeletedName="StoreRowDeleted" msprop:Generator_UserTableName="Store" msprop:Generator_RowChangedName="StoreRowChanged" msprop:Generator_RowEvArgName="StoreRowChangeEvent" msprop:Generator_RowClassName="StoreRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="StoreID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnStoreID" msprop:Generator_ColumnPropNameInRow="StoreID" msprop:Generator_ColumnPropNameInTable="StoreIDColumn" msprop:Generator_UserColumnName="StoreID" type="xs:int" />
              <xs:element name="StoreName" msprop:Generator_ColumnVarNameInTable="columnStoreName" msprop:Generator_ColumnPropNameInRow="StoreName" msprop:Generator_ColumnPropNameInTable="StoreNameColumn" msprop:Generator_UserColumnName="StoreName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ChainGroupStore" msprop:Generator_TableClassName="ChainGroupStoreDataTable" msprop:Generator_TableVarName="tableChainGroupStore" msprop:Generator_TablePropName="ChainGroupStore" msprop:Generator_RowDeletingName="ChainGroupStoreRowDeleting" msprop:Generator_RowChangingName="ChainGroupStoreRowChanging" msprop:Generator_RowEvHandlerName="ChainGroupStoreRowChangeEventHandler" msprop:Generator_RowDeletedName="ChainGroupStoreRowDeleted" msprop:Generator_UserTableName="ChainGroupStore" msprop:Generator_RowChangedName="ChainGroupStoreRowChanged" msprop:Generator_RowEvArgName="ChainGroupStoreRowChangeEvent" msprop:Generator_RowClassName="ChainGroupStoreRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="StoreID" msprop:Generator_ColumnVarNameInTable="columnStoreID" msprop:Generator_ColumnPropNameInRow="StoreID" msprop:Generator_ColumnPropNameInTable="StoreIDColumn" msprop:Generator_UserColumnName="StoreID" type="xs:int" />
              <xs:element name="GroupChainID" msprop:Generator_ColumnVarNameInTable="columnGroupChainID" msprop:Generator_ColumnPropNameInRow="GroupChainID" msprop:Generator_ColumnPropNameInTable="GroupChainIDColumn" msprop:Generator_UserColumnName="GroupChainID" type="xs:int" />
              <xs:element name="StoreName" msprop:Generator_ColumnVarNameInTable="columnStoreName" msprop:Generator_ColumnPropNameInRow="StoreName" msprop:Generator_ColumnPropNameInTable="StoreNameColumn" msprop:Generator_UserColumnName="StoreName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:GroupChain" />
      <xs:field xpath="mstns:GroupChainID" />
    </xs:unique>
    <xs:unique name="Store_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Store" />
      <xs:field xpath="mstns:StoreID" />
    </xs:unique>
    <xs:unique name="ChainGroupStore_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ChainGroupStore" />
      <xs:field xpath="mstns:StoreID" />
      <xs:field xpath="mstns:GroupChainID" />
    </xs:unique>
  </xs:element>
  <xs:annotation>
    <xs:appinfo>
      <msdata:Relationship name="FK_ChainGroupStore_Chain" msdata:parent="Store" msdata:child="ChainGroupStore" msdata:parentkey="StoreID" msdata:childkey="StoreID" msprop:Generator_UserChildTable="ChainGroupStore" msprop:Generator_ChildPropName="GetChainGroupStoreRows" msprop:Generator_UserRelationName="FK_ChainGroupStore_Chain" msprop:Generator_ParentPropName="StoreRow" msprop:Generator_RelationVarName="relationFK_ChainGroupStore_Chain" msprop:Generator_UserParentTable="Store" />
      <msdata:Relationship name="FK_ChainGroupStore_GroupChain" msdata:parent="GroupChain" msdata:child="ChainGroupStore" msdata:parentkey="GroupChainID" msdata:childkey="GroupChainID" msprop:Generator_UserChildTable="ChainGroupStore" msprop:Generator_ChildPropName="GetChainGroupStoreRows" msprop:Generator_UserRelationName="FK_ChainGroupStore_GroupChain" msprop:Generator_ParentPropName="GroupChainRow" msprop:Generator_RelationVarName="relationFK_ChainGroupStore_GroupChain" msprop:Generator_UserParentTable="GroupChain" />
    </xs:appinfo>
  </xs:annotation>
</xs:schema>