﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
Nova2
</name>
</assembly>
<members>
<member name="T:Nova2.My.Resources.Resources">
<summary>
  A strongly-typed resource class, for looking up localized strings, etc.
</summary>
</member>
<member name="P:Nova2.My.Resources.Resources.ResourceManager">
<summary>
  Returns the cached ResourceManager instance used by this class.
</summary>
</member>
<member name="P:Nova2.My.Resources.Resources.Culture">
<summary>
  Overrides the current thread's CurrentUICulture property for all
  resource lookups using this strongly typed resource class.
</summary>
</member>
<member name="P:Nova2.My.Resources.Resources.accept16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Nova2.My.Resources.Resources.coffee256">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Nova2.My.Resources.Resources.DatabaseIcon">
<summary>
  Looks up a localized resource of type System.Drawing.Icon similar to (Icon).
</summary>
</member>
<member name="P:Nova2.My.Resources.Resources.delete16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Nova2.My.Resources.Resources.Logo_PNG_159x39">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Nova2.My.Resources.Resources.maximum16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Nova2.My.Resources.Resources.minimum16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Nova2.My.Resources.Resources.news128">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Nova2.My.Resources.Resources.search16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Nova2.My.Resources.Resources.tools32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Nova2.My.Resources.Resources.user32">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
</members>
</doc>
