﻿using DataAccess;

namespace DataService.Security
{
    internal class GetUserAuditTrailCommandExecutor : CommandExecutor<GetUserAuditTrailCommand>
    {
        public override void Execute(GetUserAuditTrailCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.GetUserAuditTrail))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.AddInputParameter("userid", command.UserId);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    command.Table = storedprocedure.OutputTable;
                }
            }
        }
    }
}
