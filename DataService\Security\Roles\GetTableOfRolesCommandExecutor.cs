﻿using DataAccess;
using System.Data;

namespace DataService.Security
{
    class GetTableOfRolesCommandExecutor : CommandExecutor<GetTableOfRolesCommand>
    {
        public override void Execute(GetTableOfRolesCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.GetRoleTable))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    command.Table = storedprocedure.OutputTable;

                    command.Table.PrimaryKey = new DataColumn[] { command.Table.Columns[0] };
                    for (int i = 0; i < command.Table.Columns.Count; i++)
                    {
                        command.Table.Columns[i].ReadOnly = false;
                    }

                    command.Table.Columns["ownedbycurrentuser"].DefaultValue = false;
                }
            }
        }
    }
}
