Public Class LiquidAgent

    Public Shared Function GetErrorMessage(ByVal ex As Exception) As String

        ' Build the error message.
        Dim MessageBuilder As New System.Text.StringBuilder

        MessageBuilder.Append(ex.Message)
        If IsNothing(ex.InnerException) = False Then
            If String.IsNullOrEmpty(ex.InnerException.Message) = False Then
                MessageBuilder.Append(vbCrLf & ex.InnerException.Message)
                If IsNothing(ex.InnerException.InnerException) = False Then
                    If String.IsNullOrEmpty(ex.InnerException.InnerException.Message) = False Then
                        MessageBuilder.Append(vbCrLf & ex.InnerException.InnerException.Message)
                        If IsNothing(ex.InnerException.InnerException.InnerException) = False Then
                            If String.IsNullOrEmpty(ex.InnerException.InnerException.InnerException.Message) = False Then
                                MessageBuilder.Append(vbCrLf & ex.InnerException.InnerException.InnerException.Message)
                            End If
                        End If
                    End If
                End If
            End If
        End If

        Return MessageBuilder.ToString

    End Function

    Public Shared Function GetInput(ByVal Prompt As String, ByVal AppIcon As Icon) As String
        Dim Box As New InputBox(Prompt, AppIcon)
        Return Box.GetInput
        Box.Dispose()
    End Function

    Public Shared Function GetInput(ByVal Prompt As String, ByVal CurrentValue As String, ByVal AppIcon As Icon) As String
        Dim Box As New InputBox(Prompt, CurrentValue, AppIcon, False)
        Return Box.GetInput
        Box.Dispose()
    End Function

    Public Shared Function GetInputLarge(ByVal Prompt As String, ByVal CurrentValue As String, ByVal AppIcon As Icon) As String
        Dim Box As New InputBox(Prompt, CurrentValue, AppIcon, True)
        Return Box.GetInput
        Box.Dispose()
    End Function

    Public Shared Function UserLoginSuccessful _
    (ByVal AppName As String, ByRef ConnectionString As String) As Boolean

        Dim FormLogin As New FormLogin(AppName, ConnectionString)
        If FormLogin.ShowDialog() = DialogResult.OK Then
            ' User was authenticated. Update the connection string.
            ConnectionString = FormLogin.ConnectionString
            Return True
        Else
            Return False
        End If
        FormLogin.Dispose()

    End Function

    Public Shared Function UpdatePrincipal(ByRef ConnectionString As String) As Boolean
        ' This function was added to facilitate the migration to a new authentication system.

        Dim Initializer As New PrincipalInitializer(ConnectionString)
        ConnectionString = Initializer.ConnectionString
        Return True

    End Function

    Public Shared Sub ChangeUserPassword(ByRef ConString As String, ByVal AppIcon As Icon, AutoInsertOldPassword As Boolean)
        FormChangePassword.ChangePassword(ConString, AppIcon, AutoInsertOldPassword)
    End Sub

    Public Shared Function ActionConfirmed(ByVal ConsumingForm As BaseForm, ByVal Action As String) As Boolean
        ' Display a confirmation prompt to the user and then return true
        ' if the user has confirmed the action, or false if he hasn't.

        ' Prompt the user to confirm action.
        Dim PromptResult As DialogResult = ConsumingForm.ShowMessage _
        ("Are you sure you would like to " & Action.ToLower & " the selected row?", "Confirmation", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question)

        ' Evaluate response.
        If PromptResult = DialogResult.Yes Then
            Return True
        Else
            Return False
        End If

    End Function

    Public Shared Function ActionConfirmed(ByVal Grid As DataGridView, ByVal Action As String) As Boolean
        ' Display a confirmation prompt to the user and then return true
        ' if the user has confirmed the action, or false if he hasn't.

        ' Get the form that is consuming this method.
        Dim Consumingform As LiquidShell.BaseForm = CType(Grid.TopLevelControl, LiquidShell.BaseForm)

        ' Count how many rows were selected for deletion.
        Dim SelectionCount As Integer = Grid.SelectedRows.Count

        ' Create a variable to hold the user's response.
        Dim PromptResult As DialogResult = DialogResult.No

        ' Create a stringbuilder to build the prompt's message.
        Dim Message As New System.Text.StringBuilder

        If SelectionCount < 1 Then
            ' No rows were selected. Nothing will be deleted.
            Consumingform.ShowMessage("Please select the rows to " & Action.ToLower & " before using the '" & Action & "' button.")
        ElseIf SelectionCount = 1 Then
            ' Only one row was selected for deletion.
            Message.Append("Are you sure you would like to " & Action.ToLower & " the selected row?")
            PromptResult = Consumingform.ShowMessage _
            (Message.ToString, Action & " 1 Row", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question)
        Else
            ' Multiple rows were selected for deletion.
            Message.Append("Are you sure you would like to " & Action.ToLower & " all " & SelectionCount.ToString & " selected rows?")
            PromptResult = Consumingform.ShowMessage _
            (Message.ToString, Action & " " & SelectionCount.ToString & " Rows", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question)
        End If

        ' Return a result based on the user's response.
        If PromptResult = DialogResult.Yes Then
            Return True
        Else
            Return False
        End If

    End Function

    Public Shared Function SaveAndProceed _
        (ByVal Row As DataRow, ByVal Consumingform As LiquidShell.BaseForm, ByVal ConnectionString As String, ByVal ForceSave As Boolean) _
        As Boolean

        ' If this is a new row being added, save it first.
        If Row.RowState = DataRowState.Detached Or ForceSave Then
            ' Request permission to continue.
            Dim Message As String = "The current item is new and needs to be saved before you can continue." _
            & vbCrLf & vbCrLf & "Would you like to save now?"
            Dim Response As DialogResult = Consumingform.ShowMessage _
            (Message, "Save Required", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question)
            If Response = DialogResult.Yes Then
                If Consumingform.SaveAndClose(False) Then
                    Return True
                Else
                    Return False
                End If
            Else
                Return False
            End If
        Else
            Return True
        End If

    End Function

    Public Shared Function SaveAndProceed _
        (ByVal Row As DataRow, ByVal ConsumingSubform As LiquidShell.Subform, ByVal ConnectionString As String, ByVal ForceSave As Boolean) _
        As Boolean

        ' If this is a new row being added, save it first.
        If Row.RowState = DataRowState.Detached Or ForceSave Then
            ' Get the parent form so that we can show a message to the user.
            Dim ConsumingForm As LiquidShell.BaseForm = CType(ConsumingSubform.TopLevelControl, LiquidShell.BaseForm)
            ' Request permission to continue.
            Dim Message As String = "The current item is new and needs to be saved before you can continue." _
            & vbCrLf & vbCrLf & "Would you like to save now?"
            Dim Response As DialogResult = ConsumingForm.ShowMessage _
            (Message, "Save Required", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question)
            If Response = DialogResult.Yes Then
                If ConsumingSubform.Save(False) Then
                    Return True
                Else
                    Return False
                End If
            Else
                Return False
            End If
        Else
            Return True
        End If

    End Function

    Public Shared Function ReaderToTable(ByVal reader As SqlClient.SqlDataReader) As DataTable
        ' Copied from:
        ' ms-help://MS.VSCC.v80/MS.MSDN.v80/MS.VisualStudio.v80.en/dv_raddata/html/5ba8d931-274f-457c-9f54-5396c61d43fa.htm

        Dim newTable As New DataTable()
        Dim col As DataColumn
        Dim row As DataRow
        Dim i As Integer

        For i = 0 To reader.FieldCount - 1

            col = New DataColumn()
            col.ColumnName = reader.GetName(i)
            col.DataType = reader.GetFieldType(i)

            newTable.Columns.Add(col)
        Next

        While reader.Read

            row = newTable.NewRow()
            For i = 0 To reader.FieldCount - 1
                row(i) = reader.Item(i)
            Next

            newTable.Rows.Add(row)
        End While

        Return newTable
    End Function

    Public Shared Function IsValidEmailAddress(ByVal StringToTest As String) As Boolean
        ' Test the given string to see if it is a valid email address.

        ' Check for an empty string.
        If String.IsNullOrEmpty(StringToTest) Then
            Return False
        End If

        ' Create a list of valid non-alphanumeric characters that are allowed in the string.
        Dim ValidCharacters As New List(Of String)
        ValidCharacters.Add("@")
        ValidCharacters.Add(".")
        ValidCharacters.Add("-")
        ValidCharacters.Add("_")

        ' Remember the quantity of '@' signs in the string.
        Dim AtSignCount As Integer = 0

        ' Remember the index positions of all the "." characters in the string.
        Dim DotIndexes As New List(Of Integer)

        ' Cycle the the characters in the string.
        For i As Integer = 0 To StringToTest.Length - 1
            ' Check for illegal characters.
            If Char.IsLetterOrDigit(StringToTest.Chars(i)) = False _
            AndAlso ValidCharacters.Contains(StringToTest.Chars(i).ToString) = False Then
                Return False
            End If
            ' Check for the '@' sign.
            If String.Compare(StringToTest.Chars(i).ToString, "@") = 0 Then
                AtSignCount += 1
            End If
            ' Check for the '.' character.
            If String.Compare(StringToTest.Chars(i).ToString, ".") = 0 Then
                DotIndexes.Add(i)
            End If
        Next

        ' Check that there is exactly one '@' sign in the string.
        If Not AtSignCount = 1 Then
            Return False
        End If

        ' Check if the '@' sign is in a valid position within the string.
        Dim AtSignIndex As Integer = StringToTest.IndexOf("@")
        If AtSignIndex < 1 Or AtSignIndex > StringToTest.Length - 4 Then
            ' The '@' sign is either the first character in the string or it is so far back in the
            ' string that it doesn't allow for the inclusion of a domain name.
            Return False
        End If

        ' Check if the string contains at least one '.' in a valid position.
        If DotIndexes.Count = 0 Then
            ' The string doesn't contain a '.' character.
            Return False
        End If
        If DotIndexes.Contains(0) Then
            ' The '.' is the first character in the string.
            Return False
        End If
        If DotIndexes.Contains(StringToTest.Length - 1) Then
            ' The '.' is the last character in the string.
            Return False
        End If
        ' Check if there's at least one '.' after the '@' sign.
        Dim DotAfterAt As Boolean = False
        For Each DotIndex As Integer In DotIndexes
            If AtSignIndex - DotIndex = 1 Or DotIndex - AtSignIndex = 1 Then
                ' The '@' sign is adjacent to a '.' sign.
                Return False
            End If
            If AtSignIndex < DotIndex Then
                ' Found one '.' after the '@'.
                DotAfterAt = True
            End If
        Next
        If DotAfterAt = False Then
            Return False
        End If

        Return True

    End Function

    Public Shared Function GetBindingSourceTable(ByVal bs As BindingSource) As DataTable
        ' Get the underlying data table that feeds the given binding source, even if it's at the end
        ' of a string of connected binding sources.

        ' If this binding source doesn't have a datasource, return nothing.
        If IsNothing(bs.DataSource) Then
            Return Nothing
        End If

        ' If the data source of the given binding source is a data set, then this is easy...
        If TypeOf bs.DataSource Is DataSet Then
            Return CType(bs.List, DataView).Table
        End If

        ' If the data source of the given binding source is a data table, then this is easy...
        If TypeOf bs.DataSource Is DataTable Then
            Return CType(bs.DataSource, DataTable)
        End If

        ' Create a list to hold the names of all the data relations of the given binding source.
        Dim RelationNameList As New List(Of String)

        ' Cycle through all chained binding sources and record the relation names.
        Dim Source As Object = bs
        Dim ParentTableName As String = String.Empty
        While TypeOf Source Is BindingSource
            ' Get the binding source that we're working with.
            Dim CurrentBindingSource As BindingSource = CType(Source, BindingSource)
            ' If the data source of 'Source' is a data set, then save the name of the table.
            If TypeOf CurrentBindingSource.DataSource Is DataSet Then
                ParentTableName = CurrentBindingSource.DataMember
            Else
                ' The data source of 'Source' is another binding source. Add the relation name to the list.
                RelationNameList.Add(CurrentBindingSource.DataMember)
            End If
            ' Get the data source of the current binding source.
            Source = CurrentBindingSource.DataSource
        End While

        ' The value of 'Source' should now be of type 'DataSet'. Cycle through all relation names to
        ' get the original binding source's data table.
        Dim ReturnTable As DataTable = CType(Source, DataSet).Tables(ParentTableName)
        For Each RelationName As String In RelationNameList
            Dim ChildRelation As DataRelation = ReturnTable.ChildRelations(RelationName)
            If IsNothing(ChildRelation) = False Then
                Dim ChildDataTable As DataTable = ChildRelation.ChildTable
                ReturnTable = ChildDataTable
            End If
        Next

        Return ReturnTable

    End Function

    Public Shared Sub EnableHyperlink(ByRef Hyperlink As LabelControl, ByVal Enabled As Boolean)
        If Enabled Then
            Hyperlink.Enabled = True
            Hyperlink.ForeColor = Color.SteelBlue
            Hyperlink.Font = New Font(Hyperlink.Font, FontStyle.Underline)
        Else
            Hyperlink.Enabled = False
            Hyperlink.ForeColor = Color.DimGray
            Hyperlink.Font = New Font(Hyperlink.Font, FontStyle.Regular)
        End If
    End Sub

    Public Shared Function GetSqlFriendlyDate(ByVal DateToConvert As Date) As String

        ' Convert a date into the format 'YYYYMMDD' to send to a SQL server.
        Dim YearString As String = DateToConvert.Year.ToString("####")
        Dim MonthString As String = DateToConvert.Month.ToString("0#")
        Dim DayString As String = DateToConvert.Day.ToString("0#")
        Return YearString & MonthString & DayString

    End Function

    Public Shared Function GetNextMonday(ConnectionString As String, ConsumingForm As LiquidShell.BaseForm) As Date
        ' If today is a Monday, return today's date. Otherwise, return the date of the next Monday that will
        ' occur after today.

        ' Get the date and time on the server.
        Dim ServerTime As Date = GetServerTime(ConnectionString, ConsumingForm)

        ' Keep adding one day to today's date until it becomes a Monday.
        While Not ServerTime.DayOfWeek = DayOfWeek.Monday
            ServerTime = ServerTime.AddDays(1)
        End While

        Return ServerTime

    End Function

    Public Shared Function GetNextMonday(ConnectionString As String) As Date
        ' If today is a Monday, return today's date. Otherwise, return the date of the next Monday that will
        ' occur after today.

        ' Get the date and time on the server.
        Dim ServerTime As Date = GetServerTime(ConnectionString, Nothing)

        ' Keep adding one day to today's date until it becomes a Monday.
        While Not ServerTime.DayOfWeek = DayOfWeek.Monday
            ServerTime = ServerTime.AddDays(1)
        End While

        Return ServerTime

    End Function

    Public Shared Function GetLastMonday(ConnectionString As String, ConsumingForm As LiquidShell.BaseForm)
        Return DateAdd(DateInterval.DayOfYear, -7, GetNextMonday(ConnectionString, ConsumingForm))
    End Function

    Public Shared Function GetServerTime(ConnectionString As String, ConsumingForm As LiquidShell.BaseForm) As Date
        ' Get the date and time on the SQL server to avoid unpredictability if using the client
        ' PC's time and the time isn't correct.

        ' Create a string to hold possible errors.
        Dim Errors As String = String.Empty

        ' Execute the command to fetch the data.
        Dim ServerTime As Date = CDate(LiquidShell.LiquidAgent.GetSqlScalarValue(ConnectionString, "SELECT GETDATE()", Errors))

        ' Display error if something went wrong.
        If String.IsNullOrEmpty(Errors) = False Then
            If IsNothing(ConsumingForm) = False Then
                ConsumingForm.ShowMessage("Rats!  Something went wrong while trying to read the time from the server." _
                & vbCrLf & vbCrLf & Errors, "Data Load Error")
            End If
            Return Nothing
        Else
            Return ServerTime
        End If

    End Function

    Public Shared Sub ToggleCheckEdit(ByVal CheckedChangingControl As CheckEdit, ByVal ToggleControl As CheckEdit)
        ' Make the given check edits behave like radio buttons: only one can be checked at a time.
        ToggleControl.Checked = Not CheckedChangingControl.Checked
    End Sub

    Public Shared Function GetDaysBeforePasswordExpiration(ConnectionString As String, ConsumingForm As LiquidShell.BaseForm) As Integer
        ' Get the number of days left before the current user's password expires.

        Dim SQLText As String = "SELECT isnull(LOGINPROPERTY('" & My.User.Name & "','DaysUntilExpiration'),33)"
        Dim Errors As String = String.Empty
        Dim DaysLeft As Integer = CInt(GetSqlScalarValue(ConnectionString, SQLText, Errors))

        ' Display error if something went wrong.
        If String.IsNullOrEmpty(Errors) = False Then
            ConsumingForm.ShowMessage("Er... Something went wrong while trying to get the days left before password expiration." _
            & vbCrLf & vbCrLf & Errors, "Data Query Error")
            Return Nothing
        Else
            Return DaysLeft
        End If

    End Function

#Region "Methods To Delete Grid Rows"

    Private Shared Function GetRowDescriptions(ByVal Grid As DataGridView) As Dictionary(Of DataRow, String)
        ' Build a dictionary containing a key column of datarows bound to all the selected datagridviewrows in the grid,
        ' and a value column of string descriptions stored in the Tag property of each datagridviewrow.

        ' Create the dictionary object to return.
        Dim ReturnDictionary As New Dictionary(Of DataRow, String)

        ' Populate the dictionary.
        For Each SelectedGridRow As DataGridViewRow In Grid.SelectedRows
            ReturnDictionary.Add(CType(SelectedGridRow.DataBoundItem, DataRowView).Row, SelectedGridRow.Tag)
        Next

        ' Return the resulting dictionary.
        Return ReturnDictionary

    End Function

    Public Shared Sub DeleteParentRows _
    (ByVal Grid As DataGridView,
    ByVal RelationNameOfChildRowsThatMustPreventDeleteAction As String,
    ByVal DeletableChildRelationNames As List(Of String),
    ByRef AuditTable As DataTable,
    ByVal TypeOfChildRowsThatMustPreventDeleteAction As String)
        ' Delete one or more rows from the database.

        ' Get collections of rows that may be deleted, cannot be deleted, and total selected rows.
        Dim GridRowLists() As List(Of DataRow) = LiquidShell.LiquidAgent.GetGridRowDeleteLists(Grid, RelationNameOfChildRowsThatMustPreventDeleteAction)
        Dim DeletableRows As List(Of DataRow) = GridRowLists(0)
        Dim ImmortalRows As List(Of DataRow) = GridRowLists(1)
        Dim SelectedRows As List(Of DataRow) = GridRowLists(2)

        ' Get a dictionary containing selected datarows with a string description for each one.
        Dim RowDescriptionList As Dictionary(Of DataRow, String) = GetRowDescriptions(Grid)

        ' Build a warning message based on the quantities in the row lists.
        Dim MessageText As String = LiquidShell.LiquidAgent.BuildDeleteRowsConnectedRecordsWarning _
        (DeletableRows, ImmortalRows, SelectedRows, RowDescriptionList, TypeOfChildRowsThatMustPreventDeleteAction)

        ' Get the form that is consuming this method so that the ShowMessage method can be used.
        Dim Consumingform As LiquidShell.BaseForm = CType(Grid.TopLevelControl, LiquidShell.BaseForm)

        ' Create a variable to indicate whether the delete action has been confirmed by the user.
        Dim DeleteActionConfirmed As DialogResult

        ' Get delete confirmation from the user.
        If String.IsNullOrEmpty(MessageText) Then
            ' All rows can safely be deleted. Request a regular confirmation from the user.
            If LiquidShell.LiquidAgent.ActionConfirmed(Grid, "Delete") Then
                DeleteActionConfirmed = DialogResult.Yes
            End If
        Else
            If ImmortalRows.Count = SelectedRows.Count Then
                ' All of the selected rows have connected child rows and cannot be deleted. Cancel without prompting
                ' the user for confirmation.
                Consumingform.ShowMessage(MessageText, "Selected Row(s) Cannot Be Deleted")
            Else
                ' Some selected rows can be deleted, but some cannot. Show the user which rows cannot be deleted
                ' and ask if they want to delete the others.
                DeleteActionConfirmed = Consumingform.ShowMessage _
                (MessageText, ImmortalRows.Count.ToString & " Row(s) Cannot Be Deleted", MessageBoxButtons.YesNoCancel)
            End If
        End If

        ' Exit if the user hasn't agreed to proceed.
        If Not DeleteActionConfirmed = DialogResult.Yes Then
            Exit Sub
        End If

        ' Delete the rows that don't have connected child rows.
        For Each DeletableRow As DataRow In DeletableRows

            ' First delete all child rows connected to this row.
            If IsNothing(DeletableChildRelationNames) = False AndAlso DeletableChildRelationNames.Count > 0 Then
                For Each DeletableChildRelationName As String In DeletableChildRelationNames
                    For Each ChildRow As DataRow In DeletableRow.GetChildRows(DeletableChildRelationName)
                        ChildRow.Delete()
                    Next
                Next
            End If

            ' Add an audit log entry for the deleted row.
            AddAuditLogEntry(AuditTable, DeletableRow.Table.TableName, RowDescriptionList(DeletableRow), "Deleted")

            ' Next, delete the row.
            DeletableRow.Delete()

        Next

    End Sub

    Public Shared Sub AddAuditLogEntry _
    (ByRef AuditTable As DataTable, ByVal ObjectType As String, ByVal ObjectName As String, ByVal Action As String)

        ' Add an audit log entry for the deleted row.
        If IsNothing(AuditTable) = False Then
            Dim NewAuditRow As DataRow = AuditTable.NewRow
            With NewAuditRow
                .Item("ObjectType") = ObjectType
                .Item("ObjectName") = ObjectName
                .Item("Action") = Action
            End With
            AuditTable.Rows.Add(NewAuditRow)
        End If

    End Sub

    Private Shared Function GetGridRowDeleteLists _
    (ByVal Grid As DataGridView, ByVal CriteriaChildRelationName As String) As List(Of DataRow)()
        ' Of all the rows selected by the user for deletion, split the selected rows in different collections:
        ' Get collections of rows that may be deleted, cannot be deleted, and total selected rows.

        ' Create lists to hold each collection.
        Dim DeletableRows As New List(Of DataRow)
        Dim ImmortalRows As New List(Of DataRow)
        Dim SelectedRows As New List(Of DataRow)

        ' Cycle through the grid's selected rows and populate the lists.
        For Each SelectedGridRow As DataGridViewRow In Grid.SelectedRows

            ' Get the associated datarow of this selcted grid row.
            Dim SelectedRow As DataRow = CType(SelectedGridRow.DataBoundItem, DataRowView).Row
            ' Add the associated data row of this grid row to the list of selected data rows.
            SelectedRows.Add(SelectedRow)

            ' Check if this datarow has any connected child rows that must prevent deletion.
            If SelectedRow.GetChildRows(CriteriaChildRelationName).Length > 0 Then
                ' This row has connected child rows so cannot be deleted.
                ImmortalRows.Add(SelectedRow)
            Else
                ' This row does not have connected child rows. It can safely be deleted.
                DeletableRows.Add(SelectedRow)
            End If

        Next

        ' Return an array of the lists.
        Return New List(Of DataRow)() {DeletableRows, ImmortalRows, SelectedRows}

    End Function

    Private Shared Function BuildDeleteRowsConnectedRecordsWarning _
    (ByVal DeletableRows As List(Of DataRow),
    ByVal ImmortalRows As List(Of DataRow),
    ByVal SelectedRows As List(Of DataRow),
    ByVal RowDescriptionList As Dictionary(Of DataRow, String),
    ByVal ChildRowType As String) _
    As String

        ' Handle the case where none of the selected rows have connected records and can all be deleted.
        If DeletableRows.Count = SelectedRows.Count Then
            Return String.Empty
        End If

        ' Handle the case where all of the selected rows have connected records and cannot be deleted.
        If ImmortalRows.Count = SelectedRows.Count Then
            If SelectedRows.Count = 1 Then
                ' Only one row was selected for deletion.
                Return "The selected row cannot be deleted because it is linked" & vbCrLf _
                & "to at least one " & ChildRowType.ToLower & "."
            ElseIf SelectedRows.Count = 2 Then
                ' Two rows were selected for deletion.
                Return "The selected rows cannot be deleted because both " _
                & "of them are linked" & vbCrLf & "to at least one " & ChildRowType.ToLower & "."
            Else
                ' More than one row was selected for deletion.
                Return "The selected rows cannot be deleted because all " & SelectedRows.Count.ToString _
                & " of them are linked" & vbCrLf & "to at least one " & ChildRowType.ToLower & "."
            End If
        End If

        ' Handle the case where some rows can be deleted and some can't.
        Dim MessageText As New System.Text.StringBuilder

        ' Warn that rows cannot be deleted.
        If ImmortalRows.Count = 1 Then
            ' Only one row cannot be deleted.
            MessageText.Append("One of the selected " & SelectedRows.Count.ToString & " rows cannot be deleted " _
            & "because it is linked" & vbCrLf & "to at least one " & ChildRowType.ToLower & ". The row that cannot be deleted is:")
        Else
            ' More than one row cannot be deleted.
            MessageText.Append(ImmortalRows.Count.ToString & " of the selected " & SelectedRows.Count.ToString _
            & " rows cannot be deleted because they are linked" & vbCrLf & "to at least one " & ChildRowType.ToLower & ". " _
            & "The rows that cannot be deleted are:")
        End If

        ' List rows that cannot be deleted.
        MessageText.Append(vbCrLf)
        For Each ImmortalRow As DataRow In ImmortalRows
            MessageText.Append(vbCrLf & RowDescriptionList(ImmortalRow))
        Next
        MessageText.Append(vbCrLf & vbCrLf)

        ' Ask if remaining deletable rows should be deleted.
        If DeletableRows.Count = 1 Then
            ' Only one row can still be deleted.
            MessageText.Append("However, the remaining row can still be deleted. Would you like to delete this row?")
        Else
            ' More than one row can still be deleted.
            MessageText.Append("However, the remaining " & DeletableRows.Count.ToString & " rows can still be " _
            & "deleted. Would you like to delete these rows?")
        End If

        Return MessageText.ToString

    End Function

#End Region

#Region "Control Validation"

    Public Shared Sub ControlValidation(ByVal ValidatedControl As Control, ByVal ErrorMessage As String)

        ' Get the error manager of the base form.
        Dim ErrorManager As ErrorProvider = GetErrorManager(ValidatedControl)
        If IsNothing(ErrorManager) Then
            Exit Sub
        End If

        ' Set error manager properties.
        ErrorManager.SetIconPadding(ValidatedControl, 5)
        ErrorManager.SetIconAlignment(ValidatedControl, ErrorIconAlignment.MiddleLeft)

        ' Set error message and formatting for a control that is validated.
        ErrorManager.SetError(ValidatedControl, ErrorMessage)
        FormatValidatedControl(ValidatedControl, String.IsNullOrEmpty(ErrorMessage))

    End Sub

    Private Shared Function GetErrorManager(ByVal Control As Control) As ErrorProvider
        ' Get the error manager of the base form.
        If IsNothing(Control.TopLevelControl) = False Then
            Return CType(Control.TopLevelControl, BaseForm).ErrorManager
        Else
            Return Nothing
        End If
    End Function

    Private Shared Sub FormatValidatedControl(ByVal ValidatedControl As Control, ByVal ValidationSucceeded As Boolean)
        ' Change the appearance of the control to indicate whether or not validation succeeded.

        ' Textbox formatting.
        If TypeOf ValidatedControl Is DevExpress.XtraEditors.TextEdit Then
            Try
                If ValidationSucceeded Then
                    ' Change colours.
                    ValidatedControl.BackColor = Color.White
                    ValidatedControl.ForeColor = Color.DimGray
                Else
                    ValidatedControl.BackColor = Color.IndianRed
                    ValidatedControl.ForeColor = Color.White
                End If
            Catch ex As InvalidOperationException
            End Try
        End If

        ' Label formatting.
        If TypeOf ValidatedControl Is DevExpress.XtraEditors.LabelControl Then
            Try
                If ValidationSucceeded Then
                    ' Change colours.
                    If String.Compare(CStr(ValidatedControl.Tag), "Forecolor=Backcolor") = 0 Then
                        ' This label must be invisible when not in the error state.
                        ValidatedControl.ForeColor = ValidatedControl.ForeColor
                    Else
                        ' This label must be the normal color when not in the error state.
                        ValidatedControl.ForeColor = Color.SteelBlue
                    End If
                Else
                    ValidatedControl.ForeColor = Color.IndianRed
                End If
            Catch ex As InvalidOperationException
            End Try
        End If

    End Sub

#End Region

#Region "Database Command Execution"

    Public Shared Sub RunCommand _
    (ByVal SqlCon As SqlClient.SqlConnection, ByVal CommandString As String, ByRef ErrorMessage As String)

        ' Get the state of the SQL connection so that it can be restored when we're done here.
        Dim ConnectionWasOpen As Boolean = False
        If SqlCon.State = ConnectionState.Open Then
            ConnectionWasOpen = True
        End If

        ' Try and collect the data.
        Try
            ' Open the connection if it isn't open already.
            If ConnectionWasOpen = False Then
                SqlCon.Open()
            End If
            ' Use a data adapter to collect the data.
            Using Command As New SqlClient.SqlCommand(CommandString, SqlCon)
                Command.CommandTimeout = 0
                Command.ExecuteNonQuery()
            End Using
        Catch ex As Exception
            ' Something went wrong. Provide error details.
            ErrorMessage = LiquidShell.LiquidAgent.GetErrorMessage(ex)
        Finally
            If ConnectionWasOpen = False Then
                ' The connection was closed when we started. Close it again before we leave. Because it's courteous.
                SqlCon.Close()
            End If
        End Try

    End Sub

    Public Shared Function GetSqlScalarValue _
    (ByVal ConnectionString As String, ByVal SelectStatement As String, ByRef ErrorMessage As String) _
    As Object

        ' Create a data table to hold the data.
        Dim Data As Object = Nothing

        ' Create a connection to the SQL server.
        Dim SqlConnection As New SqlClient.SqlConnection(ConnectionString)

        ' Try and collect the data.
        Try
            SqlConnection.Open()
            ' Use a data adapter to collect the data.
            Data = GetSqlScalarValue(SqlConnection, SelectStatement, ErrorMessage)
        Catch ex As Exception
            ' Something went wrong. Provide error details.
            ErrorMessage = LiquidShell.LiquidAgent.GetErrorMessage(ex)
        Finally
            SqlConnection.Close()
            SqlConnection.Dispose()
        End Try

        Return Data

    End Function

    Public Shared Function GetSqlScalarValue _
    (ByVal SqlCon As SqlClient.SqlConnection, ByVal SelectStatement As String, ByRef ErrorMessage As String) _
    As Object

        ' Create a data table to hold the data.
        Dim Data As Object = Nothing

        ' Try and collect the data.
        Try
            ' Use a data adapter to collect the data.
            Using Command As New SqlClient.SqlCommand(SelectStatement, SqlCon)
                Command.CommandTimeout = 0
                Data = Command.ExecuteScalar
            End Using
        Catch ex As Exception
            ' Something went wrong. Provide error details.
            ErrorMessage = LiquidShell.LiquidAgent.GetErrorMessage(ex)
        End Try

        Return Data

    End Function

    Public Shared Function GetSqlDataTable _
    (ByVal ConnectionString As String, ByVal SelectStatement As String, ByRef ErrorMessage As String) _
    As DataTable

        ' Create a data table to hold the data.
        Dim TableOfData As New DataTable

        ' Create a connection to the SQL server.
        Dim SqlConnection As New SqlClient.SqlConnection(ConnectionString)
        ' Try and collect the data.
        Try
            SqlConnection.Open()
            ' Use a data adapter to collect the data.
            Using Adapter As New SqlClient.SqlDataAdapter(SelectStatement, SqlConnection)
                Adapter.Fill(TableOfData)
            End Using
        Catch ex As Exception
            ' Something went wrong. Provide error details.
            ErrorMessage = LiquidShell.LiquidAgent.GetErrorMessage(ex)
        Finally
            SqlConnection.Close()
            SqlConnection.Dispose()
        End Try

        ' Return a data table containing the retrieved data.
        Return TableOfData

    End Function

    Public Shared Function GetSqlDataBindingSource _
    (ByVal ConnectionString As String, ByVal SelectStatement As String, ByRef ErrorMessage As String) _
    As BindingSource

        ' Create a data table to hold the data.
        Dim LookupTable As New DataTable
        Dim LookupDataSet As New DataSet
        LookupDataSet.Tables.Add(LookupTable)

        ' Create a connection to the SQL server.
        Dim SqlConnection As New SqlClient.SqlConnection(ConnectionString)

        ' Try and collect the data.
        Try
            SqlConnection.Open()
            ' Use a data adapter to collect the data.
            Using Adapter As New SqlClient.SqlDataAdapter(SelectStatement, SqlConnection)
                Adapter.Fill(LookupTable)
            End Using
        Catch ex As Exception
            ' Something went wrong. Provide error details.
            ErrorMessage = LiquidShell.LiquidAgent.GetErrorMessage(ex)
        Finally
            SqlConnection.Close()
            SqlConnection.Dispose()
        End Try

        ' Return a binding source linked to the retrieved data.
        Return New BindingSource(LookupTable.DataSet, LookupTable.TableName)

    End Function

#End Region

End Class



Class PrincipalInitializer

    Public ConnectionString As String = String.Empty

    Public Sub New(constring As String)

        ConnectionString = constring

        Dim stringbuilder As New SqlClient.SqlConnectionStringBuilder(constring)
        stringbuilder.ConnectTimeout = 0
        Dim server As String = stringbuilder.DataSource
        Dim database As String = stringbuilder.InitialCatalog
        Dim username As String = stringbuilder.UserID
        Dim password As String = stringbuilder.Password
        My.User.CurrentPrincipal = New SqlPrincipal(server, database, username, password)

    End Sub

End Class