<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DevExpress.XtraScheduler.v12.2</name>
    </assembly>
    <members>
        <member name="T:DevExpress.XtraScheduler.AppointmentDependencyStorage">

            <summary>
                <para>A storage which holds a collection of appointment dependencies.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.AppointmentDependencyStorage.#ctor(DevExpress.XtraScheduler.SchedulerStorage)">
            <summary>
                <para>Initializes a new instance of the AppointmentDependencyStorage class with the specified scheduler storage.
</para>
            </summary>
            <param name="storage">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerStorage"/> value that specifies the scheduler storage to store appointment dependencies.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.AppointmentDependencyStorage.CustomFieldMappings">
            <summary>
                <para>Provides access to the collection of objects specifying how the custom properties of an appointment dependency map to the corresponding data fields. 
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.AppointmentDependencyCustomFieldMappingCollection"/> object.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.AppointmentDependencyStorage.Mappings">
            <summary>
                <para>Gets an object that specifies bindings established between persistent properties of the appointment dependencies maintained by the current storage and appropriate fields in the data source.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.AppointmentDependencyMappingInfo"/> object containing information on the mapping of the dependency's properties to the appropriate data fields.

</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.AppointmentDependencyFormEventHandler">

            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.EditAppointmentDependencyFormShowing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.AppointmentDependencyFormEventHandler.Invoke(System.Object,DevExpress.XtraScheduler.AppointmentDependencyFormEventArgs)">
            <summary>
                <para>Specifies a method that will handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.EditAppointmentDependencyFormShowing"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> which raised the event.


            </param>
            <param name="e">
		An <see cref="T:DevExpress.XtraScheduler.AppointmentDependencyFormEventArgs"/> object which contains event data.


            </param>


        </member>
        <member name="T:DevExpress.XtraScheduler.ISchedulerInplaceEditor">

            <summary>
                <para>Provides methods to customize properties of an in-place editor.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.XtraScheduler.ISchedulerInplaceEditor.BackColor">
            <summary>
                <para>Gets or sets the background color of the control.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Color"/> object that represents the background color of the control.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.ISchedulerInplaceEditor.Bounds">
            <summary>
                <para>Gets or sets the size and location of the control.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Rectangle"/> object that represents  the size and location of the control, in pixels.
</value>


        </member>
        <member name="E:DevExpress.XtraScheduler.ISchedulerInplaceEditor.CommitChanges">
            <summary>
                <para>Occurs before a modified value is saved to the underlying data source.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.ISchedulerInplaceEditor.Focus">
            <summary>
                <para>Sets input focus to the control.
</para>
            </summary>
            <returns><b>true</b> if the input focus request was successful; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.XtraScheduler.ISchedulerInplaceEditor.Font">
            <summary>
                <para>Gets or sets the font of the text displayed by the control.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Font"/> to apply to the text displayed by the control.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.ISchedulerInplaceEditor.ForeColor">
            <summary>
                <para>Gets or sets the foreground color of the control.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Color"/> object that represents the foreground color of the control.

</value>


        </member>
        <member name="E:DevExpress.XtraScheduler.ISchedulerInplaceEditor.LostFocus">
            <summary>
                <para>Occurs when the control loses focus.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.ISchedulerInplaceEditor.Parent">
            <summary>
                <para>Gets or sets the parent container of the control.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Forms.Control"/> that represents the parent or container of the control.
</value>


        </member>
        <member name="E:DevExpress.XtraScheduler.ISchedulerInplaceEditor.RollbackChanges">
            <summary>
                <para>Occurs before changes are cancelled and the modified value is replaced with the former value (a value before modification).
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.ISchedulerInplaceEditor.SelectAll">
            <summary>
                <para>Selects all text in the control.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.ISchedulerInplaceEditor.SetPositionToTheEndOfText">
            <summary>
                <para>Sets the selection start to the end of text.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.ISchedulerInplaceEditor.Text">
            <summary>
                <para>Gets or sets the current text in the control.
</para>
            </summary>
            <value>A <see cref="T:System.String"/>, representing a control's text.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.ISchedulerInplaceEditor.Visible">
            <summary>
                <para>Gets or sets a value indicating whether the control is displayed.
</para>
            </summary>
            <value><b>true</b> if the control is displayed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.PopupMenuShowingEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.PopupMenuShowing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.PopupMenuShowingEventArgs.#ctor(DevExpress.XtraScheduler.SchedulerPopupMenu)">
            <summary>
                <para>Initializes a new instance of the PopupMenuShowingEventArgs class with the specified settings.
</para>
            </summary>
            <param name="menu">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerPopupMenu"/> which represents the popup menu. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.PopupMenuShowingEventArgs.Menu"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.PopupMenuShowingEventArgs.Menu">
            <summary>
                <para>Gets or sets the popup (context) menu for which this event was raised.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerPopupMenu"/> object, which is the popup menu.


</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.RecurrentAppointmentActionFormEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.DeleteRecurrentAppointmentFormShowing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.RecurrentAppointmentActionFormEventArgs.#ctor(DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Initializes a new instance of the RecurrentAppointmentActionFormEventArgs class with specified settings.
</para>
            </summary>
            <param name="apt">
		An <see cref="T:DevExpress.XtraScheduler.Appointment"/> object.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.RecurrentAppointmentActionFormEventArgs.QueryResult">
            <summary>
                <para>Gets or sets the result provided by asking the user whether the entire series or just a single appointment should be deleted.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.RecurrentAppointmentAction"/> enumeration member specifying an action to perform.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.Services.Implementation.HeaderCaptionService">

            <summary>
                <para>Provides a wrapper for overriding methods used for custom formatting the header captions in different Scheduler views.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.Services.Implementation.HeaderCaptionService.#ctor">
            <summary>
                <para>Initializes a new instance of the HeaderCaptionService class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.Services.Implementation.HeaderCaptionService.GetDayColumnHeaderCaption(DevExpress.XtraScheduler.Drawing.DayHeader)">
            <summary>
                <para>Override this method to change the format of the column header caption in the <b>Day View</b>.
</para>
            </summary>
            <param name="header">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.DayHeader"/> object representing a header in the Day View.

            </param>
            <returns>A format string.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.Services.Implementation.HeaderCaptionService.GetDayOfWeekAbbreviatedHeaderCaption(DevExpress.XtraScheduler.Drawing.DayOfWeekHeader)">
            <summary>
                <para>Override this method to return a format string for displaying short day of week captions in the <b>Month View</b>.
</para>
            </summary>
            <param name="header">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.DayOfWeekHeader"/> object representing a header.

            </param>
            <returns>A format string.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.Services.Implementation.HeaderCaptionService.GetDayOfWeekHeaderCaption(DevExpress.XtraScheduler.Drawing.DayOfWeekHeader)">
            <summary>
                <para>Override this method to return a format string for displaying the DayOfWeek header caption in  a <b>Month View</b>.
</para>
            </summary>
            <param name="header">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.DayOfWeekHeader"/> object representing a header.

            </param>
            <returns>A format string.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.Services.Implementation.HeaderCaptionService.GetHorizontalWeekCellHeaderCaption(DevExpress.XtraScheduler.Drawing.SchedulerHeader)">
            <summary>
                <para>Override this method to return a format string for displaying the HorizontalWeekCell header caption in a <b>Month View</b>.
</para>
            </summary>
            <param name="header">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerHeader"/> object, representing a header.

            </param>
            <returns>A format string.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.Services.Implementation.HeaderCaptionService.GetTimeScaleHeaderCaption(DevExpress.XtraScheduler.Drawing.TimeScaleHeader)">
            <summary>
                <para>Override this method to return a format string for displaying the TimeScale header caption in  a <b>Timescale View</b>.
</para>
            </summary>
            <param name="header">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.TimeScaleHeader"/> object, representing a header.

            </param>
            <returns>A format string.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.Services.Implementation.HeaderCaptionService.GetVerticalWeekCellHeaderCaption(DevExpress.XtraScheduler.Drawing.SchedulerHeader)">
            <summary>
                <para>Override this method to return a format string for displaying the VerticalWeekCell header caption in a <b>Week View</b>.

</para>
            </summary>
            <param name="header">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerHeader"/> object, representing a header.

            </param>
            <returns>A format string.
</returns>


        </member>
        <member name="T:DevExpress.XtraScheduler.Drawing.ExtendedCell">

            <summary>
                <para>Repesents a cell shown at the bottom of the DayView report, intended to display appointments which do not fit the visible area.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.ExtendedCell.#ctor(System.String,DevExpress.XtraScheduler.TimeInterval)">
            <summary>
                <para>Initializes a new instance of the ExtendedCell class at the specified interval with the specified text.
</para>
            </summary>
            <param name="text">
		A text to display in an extended cell.

            </param>
            <param name="interval">
		A <see cref="T:DevExpress.XtraScheduler.TimeInterval"/> object representing the time interval to which the cell belongs.


            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.ExtendedCell.#ctor">
            <summary>
                <para>Initializes a new instance of the ExtendedCell class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.ExtendedCell.Text">
            <summary>
                <para>Gets the text displayed in the extended cell.
</para>
            </summary>
            <value>A string representing the text displayed in the cell.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.AppointmentImagesEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.InitAppointmentImages"/> event. 
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.AppointmentImagesEventHandler.Invoke(System.Object,DevExpress.XtraScheduler.AppointmentImagesEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.InitAppointmentImages"/> event.

</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> which raised the event.

            </param>
            <param name="e">
		An <see cref="T:DevExpress.XtraScheduler.AppointmentImagesEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraScheduler.AppointmentImagesEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.InitAppointmentImages"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.AppointmentImagesEventArgs.#ctor(DevExpress.XtraScheduler.Drawing.IAppointmentViewInfo,DevExpress.XtraScheduler.Drawing.AppointmentImageInfoCollection)">
            <summary>
                <para>Initializes a new instance of the AppointmentImagesEventArgs class with specified settings.
</para>
            </summary>
            <param name="viewInfo">
		An <see cref="T:DevExpress.XtraScheduler.Drawing.IAppointmentViewInfo"/> interface defining view information for the appointment.

            </param>
            <param name="imageInfos">
		An <see cref="T:DevExpress.XtraScheduler.Drawing.AppointmentImageInfoCollection"/> collection, representing the collection of images used to display an appointment.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.AppointmentImagesEventArgs.Appointment">
            <summary>
                <para>Provides access to the appointment for which the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.InitAppointmentImages"/> event is fired.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.Appointment"/> that is prepared for display.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.AppointmentImagesEventArgs.ImageInfoList">
            <summary>
                <para>Gets a collection of <see cref="T:DevExpress.XtraScheduler.Drawing.AppointmentImageInfo"/> objects.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.Drawing.AppointmentImageInfoCollection"/> object which represents the event's collection of images.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.AppointmentImagesEventArgs.ViewInfo">
            <summary>
                <para>Provides access to the characteristics of the appointment prepared for display.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.Drawing.IAppointmentViewInfo"/> interface defining view characteristics for the appointment.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.Drawing.ResourceHeader">

            <summary>
                <para>Serves as the base class for classes that represent the Resource Headers visual elements of the Scheduler.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.ResourceHeader.HitTestType">
            <summary>
                <para>Gets the enumeration member, which identifies the ResourceHeader element when it is hit.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerHitTest"/> enumeration member, indicating the element being hit.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.Drawing.DayOfWeekHeader">

            <summary>
                <para>Represents a scheduler's visual element, located above the time cells area and used to identify days of the week in the Month (Multi-Week) View.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.DayOfWeekHeader.#ctor(DevExpress.XtraScheduler.BaseHeaderAppearance,System.DayOfWeek)">
            <summary>
                <para>Initializes a new instance of the DayOfWeekHeader class with default settings.
</para>
            </summary>
            <param name="appearance">
		A <see cref="T:DevExpress.XtraScheduler.BaseHeaderAppearance"/> object, that provides appearance settings for the visual element. 

            </param>
            <param name="dayOfWeek">
		A System.DayOfWeek enumeration member, specifying a day of the week.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.DayOfWeekHeader.DayOfWeek">
            <summary>
                <para>Gets the day of the week, associated with the current <b>DayOfWeekHeader</b>
</para>
            </summary>
            <value>A <see cref="T:System.DayOfWeek"/> object
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.DayOfWeekHeader.HitTestType">
            <summary>
                <para>Gets the enumeration member, which identifies the DayOfWeekHeader element when it is hit.
</para>
            </summary>
            <value>A <see cref="F:DevExpress.XtraScheduler.Drawing.SchedulerHitTest.DayOfWeekHeader"/> value.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.Drawing.DayHeader">

            <summary>
                <para>Represents a scheduler's visual element, located above the time cells area and used to identify days in the Day View.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.DayHeader.#ctor(DevExpress.XtraScheduler.BaseHeaderAppearance)">
            <summary>
                <para>Initializes a new instance of the DayHeader class with default settings.
</para>
            </summary>
            <param name="appearance">
		A <see cref="T:DevExpress.XtraScheduler.BaseHeaderAppearance"/> object, that provides appearance settings for the visual element. 

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.DayHeader.HitTestType">
            <summary>
                <para>Gets the enumeration member, which identifies the DayHeader element when it is hit.
</para>
            </summary>
            <value>A <see cref="F:DevExpress.XtraScheduler.Drawing.SchedulerHitTest.DayHeader"/> value.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.UI.AppointmentResourcesEdit">

            <summary>
                <para>Represents the pop-up checked list box control used to select multiple resources to assign them to an appointment.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.UI.AppointmentResourcesEdit.#ctor">
            <summary>
                <para>Initializes a new instance of the AppointmentResourcesEdit class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.AppointmentResourcesEdit.ResourceIds">
            <summary>
                <para>Provides access to the collection of resource identifiers (resource id's) of the <see cref="T:DevExpress.XtraScheduler.UI.AppointmentResourcesEdit"/> control.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.AppointmentResourceIdCollection"/> object.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.SchedulerOptionsPrint">

            <summary>
                <para>Provides print options for the Scheduler control.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerOptionsPrint.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerOptionsPrint class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerOptionsPrint.PrintStyle">
            <summary>
                <para>Gets or sets the scheduler's printing style.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Printing.SchedulerPrintStyleKind"/> enumeration value which specifies the scheduler's printing style.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerOptionsPrint.ToString">
            <summary>
                <para>Returns the textual representation of printing options.

</para>
            </summary>
            <returns>A <see cref="T:System.String"/> value which is the textual representation of printing options.

</returns>


        </member>
        <member name="T:DevExpress.XtraScheduler.AppointmentViewInfoCustomizingEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.AppointmentViewInfoCustomizing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.AppointmentViewInfoCustomizingEventHandler.Invoke(System.Object,DevExpress.XtraScheduler.AppointmentViewInfoCustomizingEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.AppointmentViewInfoCustomizing"/> event.

</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> which raised the event.

            </param>
            <param name="e">
		An <see cref="T:DevExpress.XtraScheduler.AppointmentViewInfoCustomizingEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraScheduler.AppointmentViewInfoCustomizingEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.AppointmentViewInfoCustomizing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.AppointmentViewInfoCustomizingEventArgs.#ctor(DevExpress.XtraScheduler.Drawing.AppointmentViewInfo)">
            <summary>
                <para>Initializes a new instance of the AppointmentViewInfoCustomizingEventArgs class with the specified settings.

</para>
            </summary>
            <param name="viewInfo">
		An <see cref="T:DevExpress.XtraScheduler.Drawing.AppointmentViewInfo"/> object which represents the event's view information. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.AppointmentViewInfoCustomizingEventArgs.ViewInfo"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.AppointmentViewInfoCustomizingEventArgs.ViewInfo">
            <summary>
                <para>Gets or sets the object which contains the information used to render the appointment.

</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.Drawing.AppointmentViewInfo"/> object.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.Printing.SchedulerPrintStyle">

            <summary>
                <para>Represents a print style used to print the Scheduler's data.

</para>
            </summary>

        </member>
        <member name="P:DevExpress.XtraScheduler.Printing.SchedulerPrintStyle.AppointmentFont">
            <summary>
                <para>Gets or sets the font used to print appointments.

</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Font"/> object specifying the font used to print the contents of appointments.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Printing.SchedulerPrintStyle.AutoScaleHeadingsFont">
            <summary>
                <para>Gets or sets whether auto-adjustment of the print heading font size can be performed.

</para>
            </summary>
            <value><b>true</b> to enable font auto-scaling; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Printing.SchedulerPrintStyle.BaseStyle">
            <summary>
                <para>Gets a value indicating if this print style is one of the base styles.
</para>
            </summary>
            <value><b>true</b> if this is one of the base styles; otherwise, <b>false</b>.

</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.Printing.SchedulerPrintStyle.Clone">
            <summary>
                <para>Creates a copy of the current SchedulerPrintStyle object.
</para>
            </summary>
            <returns>A SchedulerPrintStyle object which is a copy of the current object.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.Printing.SchedulerPrintStyle.Clone(System.Boolean)">
            <summary>
                <para>Creates a copy of the current SchedulerPrintStyle object, and if specified, preserves the option that indicates if this is a base style or not.

</para>
            </summary>
            <param name="keepBase">
		<b>true</b> to preserve the <see cref="P:DevExpress.XtraScheduler.Printing.SchedulerPrintStyle.BaseStyle"/> property value in the new print style object; otherwise, <b>false</b>.

            </param>
            <returns>A <see cref="T:DevExpress.XtraScheduler.Printing.SchedulerPrintStyle"/> object which is a copy of the current object. 
</returns>


        </member>
        <member name="P:DevExpress.XtraScheduler.Printing.SchedulerPrintStyle.Color">
            <summary>
                <para>Overrides the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.Color"/> property.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Color"/> value which specifies the object's color. 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Printing.SchedulerPrintStyle.ColorConverter">
            <summary>
                <para>Gets or sets a color converter used by this print style when converting colors.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Printing.PrintColorConverter"/> object which represents the color converter for this print style.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.Printing.SchedulerPrintStyle.CreateBitmap(System.Int32,System.Int32)">
            <summary>
                <para>Creates an image with the specified dimensions specific to this print style.
</para>
            </summary>
            <param name="width">
		An integer value which specifies the width of the image.

            </param>
            <param name="height">
		An integer value which specifies the height of the image.

            </param>
            <returns>A <see cref="T:System.Drawing.Bitmap"/> object representing the image which corresponds to the current print style.
</returns>


        </member>
        <member name="P:DevExpress.XtraScheduler.Printing.SchedulerPrintStyle.HeadingsFont">
            <summary>
                <para>Gets or sets the font used to print headings.

</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Font"/> object specifying the font used to print the headings.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Printing.SchedulerPrintStyle.Kind">
            <summary>
                <para>Gets the kind of a print style.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Printing.SchedulerPrintStyleKind"/> enumeration value which represents the kind of print style. 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Printing.SchedulerPrintStyle.PageSettings">
            <summary>
                <para>Gets or sets the page settings for this print style.

</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Printing.PageSettings"/> object which contains the page settings for this print style.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.Printing.SchedulerPrintStyle.Reset">
            <summary>
                <para>Resets all print style options to their default values.

</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraScheduler.Printing.SchedulerPrintStyleKind">

            <summary>
                <para>Lists the styles available for printing the scheduler's data.

</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraScheduler.Printing.SchedulerPrintStyleKind.CalendarDetails">
            <summary>
                <para><para>Specifies the style which prints the scheduler's data in the specified interval in a column defined by the date.</para>



</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraScheduler.Printing.SchedulerPrintStyleKind.Daily">
            <summary>
                <para><para>Specifies the style which prints the scheduler's data in the specified interval so that each <b>day</b> is printed on a separate page.</para>


</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraScheduler.Printing.SchedulerPrintStyleKind.Default">
            <summary>
                <para>Specifies the default style for the current <see cref="P:DevExpress.XtraScheduler.SchedulerControl.ActiveView"/>. For instance, if the type of the active view is <b>MonthView</b>, then the default print style will be <see cref="F:DevExpress.XtraScheduler.Printing.SchedulerPrintStyleKind.Monthly"/>.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraScheduler.Printing.SchedulerPrintStyleKind.Memo">
            <summary>
                <para><para>Specifies the style which prints all the data of the selected appointments in a memo-like style.</para>


</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraScheduler.Printing.SchedulerPrintStyleKind.Monthly">
            <summary>
                <para><para>Specifies the style which prints the scheduler's data in the specified interval, so that each <b>month</b> is printed on a separate page or two separate pages.</para>



</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraScheduler.Printing.SchedulerPrintStyleKind.TriFold">
            <summary>
                <para><para>Specifies the style which prints the scheduler's data in the specified interval of three columns using the three print styles specified.</para>



</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraScheduler.Printing.SchedulerPrintStyleKind.Weekly">
            <summary>
                <para><para>Specifies the style which prints the scheduler's data in the specified interval, so that each <b>week</b> is printed on a separate page or two separate pages.</para>



</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraScheduler.PreparePopupMenuEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.PreparePopupMenu"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.PreparePopupMenuEventHandler.Invoke(System.Object,DevExpress.XtraScheduler.PreparePopupMenuEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.PreparePopupMenu"/> event. 
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> which raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraScheduler.PreparePopupMenuEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraScheduler.PreparePopupMenuEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.PreparePopupMenu"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.PreparePopupMenuEventArgs.#ctor(DevExpress.XtraScheduler.SchedulerPopupMenu)">
            <summary>
                <para>Initializes a new instance of the PreparePopupMenuEventArgs class with the specified settings.
</para>
            </summary>
            <param name="menu">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerPopupMenu"/> object which represents a popup menu.

            </param>


        </member>
        <member name="T:DevExpress.XtraScheduler.WorkWeekViewAppearance">

            <summary>
                <para>Provides the appearance settings used to paint a Work Week View.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.WorkWeekViewAppearance.#ctor">
            <summary>
                <para>Initializes a new instance of the WorkWeekViewAppearance class with the default settings.

</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraScheduler.MonthViewAppearance">

            <summary>
                <para>Provides the appearance settings used to paint a Month View.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.MonthViewAppearance.#ctor">
            <summary>
                <para>Initializes a new instance of the MonthViewAppearance class with the default settings.

</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraScheduler.TimelineViewAppearance">

            <summary>
                <para>Provides the appearance settings used to paint a Timeline View.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.TimelineViewAppearance.#ctor">
            <summary>
                <para>Initializes a new instance of the TimelineViewAppearance class with the default settings.

</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraScheduler.TimelineView">

            <summary>
                <para>Represents a Timeline View.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.TimelineView.#ctor(DevExpress.XtraScheduler.SchedulerControl)">
            <summary>
                <para>Initializes a new instance of the TimelineView class with the specified <b>Scheduler</b> control.
</para>
            </summary>
            <param name="control">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> value that specifies the <b>Scheduler</b> control assigned to the timeline view. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.SchedulerViewBase.Control"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.TimelineView.Appearance">
            <summary>
                <para>Provides access to the properties that control the appearance of the TimelineView's elements.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimelineViewAppearance"/> object that provides the appearance settings for TimelineView elements.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.TimelineView.AppointmentDisplayOptions">
            <summary>
                <para>Provides access to the <b>Timeline View</b> appointments display options.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimelineViewAppointmentDisplayOptions"/> object containing settings to display the appointments in the Timeline View.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.TimelineView.CellsAutoHeight">
            <summary>
                <para>Gets or sets whether the height of a time cell can be automatically adjusted to fit appointments.
</para>
            </summary>
            <value><b>true</b> to switch auto height adjustment on; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.TimelineView.CellsAutoHeightOptions">
            <summary>
                <para>Provides access to options for specifying cell auto height behavior in the Timeline or Gantt view.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.CellsAutoHeightOptions"/> object containing options.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.TimelineView.DeferredScrolling">
            <summary>
                <para>Provides access to parameters that control deferred scrolling.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerDeferredScrollingOption"/> instance that specifies parameters for deferred scrolling.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.TimelineView.GetBaseTimeScale">
            <summary>
                <para>Gets the time scale, which has the minimum time interval among enabled scales.

</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraScheduler.TimeScale"/> object.
</returns>


        </member>
        <member name="P:DevExpress.XtraScheduler.TimelineView.MenuItemId">
            <summary>
                <para>Gets the id of the scheduler menu item corresponding to the <b>SwitchToTimelineView</b> command.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerMenuItemId"/> enumeration member, representing the id of the scheduler menu.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.TimelineView.OptionsSelectionBehavior">
            <summary>
                <para>Provides access to properties which specify how the time cell selection changes when the Timeline scale is changed.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.OptionsSelectionBehavior"/> class instance.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.TimelineView.Scales">
            <summary>
                <para>Provides access to a collection of time scales displayed in the Timeline view.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeScaleCollection"/> object containing time scales for this view.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.TimelineView.SelectionBar">
            <summary>
                <para>Provides access to the selection bar options.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SelectionBarOptions"/> object specifying the appearance of the selection bar.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.TimelineView.ShowResourceHeaders">
            <summary>
                <para>Gets or sets whether resource headers are displayed.
</para>
            </summary>
            <value><b>true</b> to show resource headers; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.TimelineView.TimelineScrollBarVisible">
            <summary>
                <para>Specifies whether a vertical row scrollbar is visible, and the vertical scrolling is enabled in the Timeline View.
</para>
            </summary>
            <value><b>true</b> to enable a vertical scrollbar; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.TimelineView.Type">
            <summary>
                <para>Gets the view's type.
</para>
            </summary>
            <value>The <see cref="F:DevExpress.XtraScheduler.SchedulerViewType.Timeline"/> value. 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.TimelineView.ViewInfo">
            <summary>
                <para>Gets the information on visual representation of the object.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Drawing.TimelineViewInfo"/> object providing information on visual representation of the TimelineView's elements.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.TimelineView.WorkTime">
            <summary>
                <para>Gets or sets the work time interval for a <b>Timeline View</b>.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeOfDayInterval"/> value representing the work time interval.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.UI.ResourcesPopupCheckedListBoxControl">

            <summary>
                <para>Represents the pop-up checked list box control used to filter resources within the <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/>.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.UI.ResourcesPopupCheckedListBoxControl.#ctor">
            <summary>
                <para>Initializes a new instance of the ResourcesPopupCheckedListBoxControl class with the default settings.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.ResourcesPopupCheckedListBoxControl.PopupControl">
            <summary>
                <para>Gets access to the popup control that is a part of the current control and performs a container role.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.PopupContainerControl"/> object that contains the current control.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.ResourcesPopupCheckedListBoxControl.ResetResourcesItems(DevExpress.XtraScheduler.ResourceBaseCollection)">
            <summary>
                <para>Resets all resource items and their visibility state in the check list according to the specified <i>resources</i> collection.

</para>
            </summary>
            <param name="resources">
		A <see cref="T:DevExpress.XtraScheduler.ResourceBaseCollection"/> object which contains resources to be used when updating the check list.


            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.ResourcesPopupCheckedListBoxControl.ResourcesCheckedListBoxControl">
            <summary>
                <para>Gets access to the checked list box control that is a part of the current control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.UI.ResourcesCheckedListBoxControl"/> object that forms the current control.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.ResourcesPopupCheckedListBoxControl.ResourceVisibleChanged(DevExpress.XtraScheduler.ResourceBaseCollection)">
            <summary>
                <para>Resets all resource items and their visibility state in the list, according to the specified <i>resources</i> collection.

</para>
            </summary>
            <param name="resources">
		A <see cref="T:DevExpress.XtraScheduler.ResourceBaseCollection"/> object which contains resources to be used when updating.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.ResourcesPopupCheckedListBoxControl.SchedulerControl">
            <summary>
                <para>Gets or sets the scheduler control which is assigned to the ResourcesPopupCheckedListBoxControl.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> object representing the scheduler whose resources will be shown in this checked list box.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.CustomDrawObjectEventHandler">

            <summary>
                <para>Represents a method that will handle the <b>custom draw</b> events of the <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/>.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.CustomDrawObjectEventHandler.Invoke(System.Object,DevExpress.XtraScheduler.CustomDrawObjectEventArgs)">
            <summary>
                <para>Represents a method that will handle the <b>custom draw</b> events of the <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/>.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> which raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraScheduler.CustomDrawObjectEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraScheduler.DateNavigator">

            <summary>
                <para>Represents a Date Navigator.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.DateNavigator.#ctor">
            <summary>
                <para>Initializes a new instance of the DateNavigator class with the default settings.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.DateNavigator.AllowDrop">
            <summary>
                <para>Overrides the <see cref="P:System.Windows.Forms.Control.AllowDrop"/> property.

</para>
            </summary>
            <value><b>true</b> if drag-and-drop operations are allowed in the control; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.DateNavigator.BeginInit">
            <summary>
                <para>Starts the <b>Date Navigator</b>'s initialization. Initialization occurs at runtime.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.DateNavigator.BeginUpdate">
            <summary>
                <para>Locks the DateNavigator object by disallowing visual updates until the <b>EndUpdate</b> or <b>CancelUpdate</b> method is called.


</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.DateNavigator.BoldAppointmentDates">
            <summary>
                <para>Gets or sets a value indicating if the dates which contain appointments should be shown bold.


</para>
            </summary>
            <value><b>true</b> to bold the dates which contain at least one scheduled appointment; otherwise, <b>false</b>.

</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.DateNavigator.CancelUpdate">
            <summary>
                <para>Unlocks the DateNavigator object after it has been locked by the <b>BeginUpdate</b> method, without causing an immediate visual update.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.DateNavigator.EndInit">
            <summary>
                <para>Ends the <b>Date Navigator</b>'s initialization.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.DateNavigator.EndUpdate">
            <summary>
                <para>Unlocks the DateNavigator object after a call to the <b>BeginUpdate</b> method and causes an immediate visual update.


</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.DateNavigator.FirstDayOfWeek">
            <summary>
                <para>Gets the day which the <b>Date Navigator</b>'s week starts from.

</para>
            </summary>
            <value>A <see cref="T:System.DayOfWeek"/> enumeration value specifying the start day of the week for the <b>Date Navigator</b>.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DateNavigator.HighlightHolidays">
            <summary>
                <para>Gets or sets a value indicating if holiday dates should be highlighted with Red color.


</para>
            </summary>
            <value><b>true</b> to highlight holiday dates; otherwise, <b>false</b>.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DateNavigator.IsUpdateLocked">
            <summary>
                <para>Gets whether the object has been locked for updating.
</para>
            </summary>
            <value><b>true</b> if the control is locked; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DateNavigator.Multiselect">
            <summary>
                <para>Gets or sets a value indicating if end-users can select several dates at the same time in the <b>Date Navigator</b>.

</para>
            </summary>
            <value><b>true</b> to allow multiple dates to be selected at the same time; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.DateNavigator.Refresh">
            <summary>
                <para>Updates a Date Navigator control to display selected dates.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.DateNavigator.SchedulerControl">
            <summary>
                <para>Gets or sets the scheduler control assigned to the <b>Date Navigator</b>.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> object representing the scheduler which will be controlled by this <b>Date Navigator</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DateNavigator.ShowTodayButton">
            <summary>
                <para>Gets or sets a value which specifies whether the Today Button is visible.

</para>
            </summary>
            <value><b>true</b> to show the Today button; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DateNavigator.ShowWeekNumbers">
            <summary>
                <para>Gets or sets a value indicating if week numbers will be shown in the <b>Date Navigator</b>.

</para>
            </summary>
            <value><b>true</b> to show week numbers; otherwise, <b>false</b>.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DateNavigator.WeekNumberRule">
            <summary>
                <para>Gets or sets the rule which specifies the first week of the year.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.Controls.WeekNumberRule"/> enumeration value which represents the rule for the first week of the year.

</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.CustomDrawObjectEventArgs">

            <summary>
                <para>Provides data for the <b>custom draw</b> events of the <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/>.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.CustomDrawObjectEventArgs.#ctor(DevExpress.Utils.Drawing.ObjectInfoArgs,System.Drawing.Rectangle)">
            <summary>
                <para>Initializes a new instance of the CustomDrawObjectEventArgs class with the specified settings.

</para>
            </summary>
            <param name="objectInfo">
		A <see cref="T:DevExpress.Utils.Drawing.ObjectInfoArgs"/> value that provides information on the object. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.CustomDrawObjectEventArgs.ObjectInfo"/> property.


            </param>
            <param name="bounds">
		A <see cref="T:System.Drawing.Rectangle"/> value that specifies the object's bounding rectangle. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.CustomDrawObjectEventArgs.Bounds"/> property.


            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.CustomDrawObjectEventArgs.#ctor(DevExpress.Utils.Drawing.ObjectInfoArgs,System.Drawing.Rectangle,DevExpress.XtraScheduler.DefaultDrawDelegate)">
            <summary>
                <para>Initializes a new instance of the CustomDrawObjectEventArgs class with specified settings.
</para>
            </summary>
            <param name="objectInfo">
		A <see cref="T:DevExpress.Utils.Drawing.ObjectInfoArgs"/> value that provides information on the object.

            </param>
            <param name="bounds">
		A <see cref="T:System.Drawing.Rectangle"/> value that specifies the object's bounding rectangle.

            </param>
            <param name="defaultDrawDelegate">
		A delegate method used to perform default object drawing.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.CustomDrawObjectEventArgs.Bounds">
            <summary>
                <para>Returns a value which specifies the bounding rectangle of the drawing area.

</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Rectangle"/> value which specifies the object's bounding rectangle. 

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.CustomDrawObjectEventArgs.Cache">
            <summary>
                <para>Gets an object which specifies the storage for the most  used pens, fonts and brushes.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object.

</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.CustomDrawObjectEventArgs.DrawDefault">
            <summary>
                <para>Renders the element using the default drawing mechanism.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.CustomDrawObjectEventArgs.Graphics">
            <summary>
                <para>Gets an object used for painting.


</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Graphics"/> object which provides a means for painting.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.CustomDrawObjectEventArgs.Handled">
            <summary>
                <para>Gets or sets whether an event was handled. If it was handled, the default actions are not required.


</para>
            </summary>
            <value><b>true</b> if default painting isn't required; otherwise, <b>false</b>.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.CustomDrawObjectEventArgs.ObjectInfo">
            <summary>
                <para>Gets information on the painted element.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.Drawing.ObjectInfoArgs"/> class which provides information on the painted element.

</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.ActiveViewChangingEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.ActiveViewChanging"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.ActiveViewChangingEventHandler.Invoke(System.Object,DevExpress.XtraScheduler.ActiveViewChangingEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.ActiveViewChanging"/> event.

</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> which raised the event.

            </param>
            <param name="e">
		An <see cref="T:DevExpress.XtraScheduler.ActiveViewChangingEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraScheduler.ActiveViewChangingEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.ActiveViewChanging"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.ActiveViewChangingEventArgs.#ctor(DevExpress.XtraScheduler.SchedulerViewBase,DevExpress.XtraScheduler.SchedulerViewBase)">
            <summary>
                <para>Initializes a new instance of the ActiveViewChangingEventArgs class with the specified settings.

</para>
            </summary>
            <param name="oldView">
		An object of the <see cref="T:DevExpress.XtraScheduler.SchedulerViewBase"/> class descendant. This object represents the previous active scheduler view. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.ActiveViewChangingEventArgs.OldView"/> property.

            </param>
            <param name="newView">
		An object of the <see cref="T:DevExpress.XtraScheduler.SchedulerViewBase"/> class descendant. This object represents the new active scheduler view. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.ActiveViewChangingEventArgs.NewView"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.ActiveViewChangingEventArgs.Cancel">
            <summary>
                <para>Gets or sets whether the operation performed on the processed event should be cancelled.
</para>
            </summary>
            <value><b>true</b> to cancel the operation performed on the event; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.ActiveViewChangingEventArgs.NewView">
            <summary>
                <para>Gets the new value of the <see cref="P:DevExpress.XtraScheduler.SchedulerControl.ActiveView"/> property.
</para>
            </summary>
            <value>An object of the <see cref="T:DevExpress.XtraScheduler.SchedulerViewBase"/> class descendant.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.ActiveViewChangingEventArgs.OldView">
            <summary>
                <para>Gets the old value of the <see cref="P:DevExpress.XtraScheduler.SchedulerControl.ActiveView"/> property.
</para>
            </summary>
            <value>An object of the <see cref="T:DevExpress.XtraScheduler.SchedulerViewBase"/> class descendant.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.UI.ResourcesComboBoxControl">

            <summary>
                <para>Represents the combo box control used to filter resources within the <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/>.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.UI.ResourcesComboBoxControl.#ctor">
            <summary>
                <para>Initializes a new instance of the ResourcesComboBoxControl class with the default settings.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.ResourcesComboBoxControl.EditorTypeName">
            <summary>
                <para>Gets the class name of the current editor.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> identifying the class name of the current editor. 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.ResourcesComboBoxControl.EditValue">
            <summary>
                <para>Gets or sets the combo box value that provides access to the <see cref="T:DevExpress.XtraScheduler.Resource"/> class item.
</para>
            </summary>
            <value>The object representing the edit value. 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.ResourcesComboBoxControl.Properties">
            <summary>
                <para>Gets an object providing properties specific to the combo box editor.
</para>
            </summary>
            <value>A <b>RepositoryItemResourcesComboBox</b> object providing settings specific to this editor.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.ResourcesComboBoxControl.ResetResourcesItems(DevExpress.XtraScheduler.ResourceBaseCollection)">
            <summary>
                <para>Resets all resource items and their visibility state in the list according to the specified <i>resources</i> collection.

</para>
            </summary>
            <param name="resources">
		A <see cref="T:DevExpress.XtraScheduler.ResourceBaseCollection"/> object which contains resources to be used when updating.


            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.ResourcesComboBoxControl.ResourceVisibleChanged(DevExpress.XtraScheduler.ResourceBaseCollection)">
            <summary>
                <para>Resets all resource items and their visibility state in the list according to the specified <i>resources</i> collection.
</para>
            </summary>
            <param name="resources">
		A <see cref="T:DevExpress.XtraScheduler.ResourceBaseCollection"/> object which contains resources to be used when updating.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.ResourcesComboBoxControl.SchedulerControl">
            <summary>
                <para>Gets or sets the scheduler control assigned to the <b>ResourcesComboBoxControl</b>.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> object representing the scheduler whose resources will be shown in this combo box.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.ResourcesComboBoxControl.ShowAllResourcesItem">
            <summary>
                <para>Specifies whether the value corresponding to all of the items will be shown in the combo box.
</para>
            </summary>
            <value><b>true</b> if the value signifying "all of the items" is present in the combo box items list; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.ResourcesComboBoxControl.ShowNoneResourcesItem">
            <summary>
                <para>Specifies whether the value corresponding to none of the items will be shown in the combo box.
</para>
            </summary>
            <value><b>true</b> if the value signifying "none of the items" is present in the combo box items list; otherwise <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.DragDropMode">

            <summary>
                <para>Lists the values used to specify the drag-and-drop mode of the DragDropMode.

</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraScheduler.DragDropMode.Manual">
            <summary>
                <para>This mode is suitable for mutli-thread applications, when no <b>Drag~</b> events of the Scheduler Control can be raised.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraScheduler.DragDropMode.Standard">
            <summary>
                <para>This mode is suitable for single-thread applications. If this mode is enabled, all <b>Drag~</b> events of the Scheduler Control are correctly raised when appointments are dragged @amp; dropped.

</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraScheduler.UI.StorageBindedImageComboBoxEdit">

            <summary>
                <para>Represents a base class for the controls used to select items contained in the scheduler storage.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.XtraScheduler.UI.StorageBindedImageComboBoxEdit.Storage">
            <summary>
                <para>Gets or sets the <b>SchedulerStorage</b> object that contains appointments and resources to fill an editor.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerStorage"/> object representing a storage for the <b>Scheduler</b> control.

</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.UI.YearlyRecurrenceControl">

            <summary>
                <para>Represents the control used to set the recurrence options for yearly recurrent appointments.


</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.UI.YearlyRecurrenceControl.#ctor">
            <summary>
                <para>Initializes a new instance of the YearlyRecurrenceControl class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.YearlyRecurrenceControl.ValidateValues(DevExpress.XtraScheduler.UI.ValidationArgs)">
            <summary>
                <para>Checks if the user input is valid for the control.
</para>
            </summary>
            <param name="args">
		A <see cref="T:DevExpress.XtraScheduler.UI.ValidationArgs"/> object that may contain the check result and an error message.

            </param>


        </member>
        <member name="T:DevExpress.XtraScheduler.UI.WeekOfMonthEdit">

            <summary>
                <para>Represents a combo box used to select a week of the month.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.UI.WeekOfMonthEdit.#ctor">
            <summary>
                <para>Initializes a new instance of the WeekOfMonthEdit class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.WeekOfMonthEdit.EditorTypeName">
            <summary>
                <para>Gets the class name of the editor.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> object identifying the class name.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.WeekOfMonthEdit.Properties">
            <summary>
                <para>Gets an object that contains settings specific to the editor.
</para>
            </summary>
            <value>A <b>RepositoryItemWeekOfMonth</b> object that contains editor settings.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.WeekOfMonthEdit.WeekOfMonth">
            <summary>
                <para>Gets or sets the selected value in the editor - the occurrence number of the week in a month.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.WeekOfMonth"/> enumeration value that specifies a particular week in every month. 
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.UI.WeeklyRecurrenceControl">

            <summary>
                <para>Represents a control used to set the recurrence options for weekly recurrent appointments.


</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.UI.WeeklyRecurrenceControl.#ctor">
            <summary>
                <para>Initializes a new instance of the WeeklyRecurrenceControl class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.WeeklyRecurrenceControl.FirstDayOfWeek">
            <summary>
                <para>Gets or sets the day which starts the <b>WeeklyRecurrenceControl</b>'s week.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.FirstDayOfWeek"/> enumeration value specifying the first day of the week for the <b>WeeklyRecurrenceControl</b>.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.WeeklyRecurrenceControl.ValidateValues(DevExpress.XtraScheduler.UI.ValidationArgs)">
            <summary>
                <para>Checks if the user input is valid for the control.
</para>
            </summary>
            <param name="args">
		A <see cref="T:DevExpress.XtraScheduler.UI.ValidationArgs"/> object that may contain the check result and an error message.

            </param>


        </member>
        <member name="T:DevExpress.XtraScheduler.UI.WeekDaysEdit">

            <summary>
                <para>Represents a combo box used to select days of the week.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.UI.WeekDaysEdit.#ctor">
            <summary>
                <para>Initializes a new instance of the WeekDaysEdit class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.WeekDaysEdit.DayOfWeek">
            <summary>
                <para>Gets or sets the value selected in the control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.WeekDays"/> enumeration member, representing a day of the week, or a standard combination of days.


</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.WeekDaysEdit.EditorTypeName">
            <summary>
                <para>Gets the class name of the editor.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> object identifying the class name.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.WeekDaysEdit.Properties">
            <summary>
                <para>Gets an object that contains settings specific to the editor.
</para>
            </summary>
            <value>A <b>RepositoryItemDayOfWeek</b> object that contains editor settings.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.UI.WeekDaysCheckEdit">

            <summary>
                <para>Represents a control that allows selection of days of the week by checking the corresponding boxes.


</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.UI.WeekDaysCheckEdit.#ctor">
            <summary>
                <para>Initializes a new instance of the WeekDaysCheckEdit class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.WeekDaysCheckEdit.BeginInit">
            <summary>
                <para>Starts the <b>WeekDaysCheckEdit</b> control's runtime initialization. 

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.WeekDaysCheckEdit.BeginUpdate">
            <summary>
                <para>Locks the WeekDaysCheckEdit object by disallowing visual updates until the <b>EndUpdate</b> or <b>CancelUpdate</b> method is called.


</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.WeekDaysCheckEdit.CancelUpdate">
            <summary>
                <para>Unlocks the WeekDaysCheckEdit object after it has been locked by the <b>BeginUpdate</b> method, without causing an immediate visual update.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.WeekDaysCheckEdit.EndInit">
            <summary>
                <para>Ends the <b>WeekDaysCheckEdit</b> control's initialization.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.WeekDaysCheckEdit.EndUpdate">
            <summary>
                <para>Unlocks the WeekDaysCheckEdit object after a call to the <b>BeginUpdate</b> method and causes an immediate visual update.


</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.WeekDaysCheckEdit.FirstDayOfWeek">
            <summary>
                <para>Gets or sets the day which starts the <b>WeekDaysCheckEdit</b> control's week.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.FirstDayOfWeek"/> enumeration value specifying the first day of the week for the <b>WeekDaysCheckEdit</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.WeekDaysCheckEdit.IsUpdateLocked">
            <summary>
                <para>Notifies whether the <b>WeekDaysCheckEdit</b> control is locked for update.
</para>
            </summary>
            <value><b>true</b> if the control is locked; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.WeekDaysCheckEdit.UseAbbreviatedDayNames">
            <summary>
                <para>Specifies whether the control displays short names of week days .

</para>
            </summary>
            <value><b>True</b> if the control displays abbreviated names for week days.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.WeekDaysCheckEdit.VisibleWeekDays">
            <summary>
                <para>Gets or sets days of the week visible in the <b>WeekDaysCheckEdit</b> control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.WeekDays"/> enumeration member, representing a day or a group of days.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.WeekDaysCheckEdit.WeekDays">
            <summary>
                <para>Gets or sets the day of the week or a specific group of days that is selected in the editor.
</para>
            </summary>
            <value>The <see cref="T:DevExpress.XtraScheduler.WeekDays"/> enumeration value specifying the day/days in a week.
</value>


        </member>
        <member name="E:DevExpress.XtraScheduler.UI.WeekDaysCheckEdit.WeekDaysChanged">
            <summary>
                <para>Fires when  the control's weekday checkbox changes its state.

</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraScheduler.UI.RecurrenceControlBase">

            <summary>
                <para>Represents the base class for inherited recurrent controls available in the <b>XtraScheduler</b> library.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.UI.RecurrenceControlBase.#ctor">
            <summary>
                <para>Initializes a new instance of the RecurrenceControlBase class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.RecurrenceControlBase.BeginUpdate">
            <summary>
                <para>Locks the RecurrenceControlBase object by disallowing visual updates until the <b>EndUpdate</b> or <b>CancelUpdate</b> method is called.


</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.RecurrenceControlBase.CancelUpdate">
            <summary>
                <para>Unlocks the RecurrenceControlBase object after it has been locked by the <b>BeginUpdate</b> method, without causing an immediate visual update.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.RecurrenceControlBase.CheckForWarnings(DevExpress.XtraScheduler.UI.ValidationArgs)">
            <summary>
                <para>Validates control settings when overridden . 

</para>
            </summary>
            <param name="args">
		A <b>ValidationArgs</b> object that will contain a check result and an error message if applicable.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.RecurrenceControlBase.EndUpdate">
            <summary>
                <para>Unlocks the RecurrenceControlBase object after a call to the <b>BeginUpdate</b> method and causes an immediate visual update.


</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.RecurrenceControlBase.IsUpdateLocked">
            <summary>
                <para>Gets whether the control has been locked for updating.

</para>
            </summary>
            <value><b>true</b> if the control is locked; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.RecurrenceControlBase.RecurrenceInfo">
            <summary>
                <para>Gets or sets an object containing information about reoccurrences of the current appointment. 

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.RecurrenceInfo"/> object containing information about the appointment's reoccurrences. 

</value>


        </member>
        <member name="E:DevExpress.XtraScheduler.UI.RecurrenceControlBase.RecurrenceInfoChanged">
            <summary>
                <para><para>Fires when an end user changes the information on a recurrence control.
</para>

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.RecurrenceControlBase.UpdateControls">
            <summary>
                <para>Make the control's appearance consistent with property values when overridden.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.RecurrenceControlBase.ValidateValues(DevExpress.XtraScheduler.UI.ValidationArgs)">
            <summary>
                <para>Checks if the user input is valid for the control.
</para>
            </summary>
            <param name="args">
		A <see cref="T:DevExpress.XtraScheduler.UI.ValidationArgs"/> object that may contain the check result and an error message.

            </param>


        </member>
        <member name="T:DevExpress.XtraScheduler.UI.MonthlyRecurrenceControl">

            <summary>
                <para>Represents a control used to set the recurrence options for monthly recurrent appointments.


</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.UI.MonthlyRecurrenceControl.#ctor">
            <summary>
                <para>Initializes a new instance of the MonthlyRecurrenceControl class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.MonthlyRecurrenceControl.CheckForWarnings(DevExpress.XtraScheduler.UI.ValidationArgs)">
            <summary>
                <para>Used to validate control settings. 


</para>
            </summary>
            <param name="args">
		A <b>ValidationArgs</b> object that will contain a check result and an error message if applicable.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.MonthlyRecurrenceControl.ValidateValues(DevExpress.XtraScheduler.UI.ValidationArgs)">
            <summary>
                <para>Checks if the user input is valid for the control.
</para>
            </summary>
            <param name="args">
		A <see cref="T:DevExpress.XtraScheduler.UI.ValidationArgs"/> object that may contain the check result and an error message.

            </param>


        </member>
        <member name="T:DevExpress.XtraScheduler.UI.MonthEdit">

            <summary>
                <para>Represents the combo box used to select a month.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.UI.MonthEdit.#ctor">
            <summary>
                <para>Initializes a new instance of the MonthEdit class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.MonthEdit.EditorTypeName">
            <summary>
                <para>Gets the class name of the editor.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> object identifying the class name.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.MonthEdit.Month">
            <summary>
                <para>Gets or sets the number of the selected month.
</para>
            </summary>
            <value>An integer value that is the month number.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.MonthEdit.Properties">
            <summary>
                <para>Gets an object that contains settings specific to the editor.
</para>
            </summary>
            <value>A <b>RepositoryItemMonth</b> object that contains editor settings.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.UI.DurationEdit">

            <summary>
                <para>Represents a combo box used to specify time intervals (durations).
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.UI.DurationEdit.#ctor">
            <summary>
                <para>Initializes a new instance of the DurationEdit class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.DurationEdit.Duration">
            <summary>
                <para>Gets or sets the value edited in the control.
</para>
            </summary>
            <value>A <see cref="T:System.TimeSpan"/> value representing the time interval.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.DurationEdit.EditorTypeName">
            <summary>
                <para>Gets the class name of the editor.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> object identifying the class name.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.DurationEdit.LoadDefaults">
            <summary>
                <para>Fills in the control with predefined time intervals from 0 minutes to 2 days.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.DurationEdit.LoadDefaults(System.TimeSpan)">
            <summary>
                <para>Fills in the control with predefined time intervals.
</para>
            </summary>
            <param name="maxDuration">
		A <see cref="T:System.TimeSpan"/> value that is the upper boundary for time intervals being loaded.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.DurationEdit.Properties">
            <summary>
                <para>Gets an object that contains settings specific to the editor.
</para>
            </summary>
            <value>A <b>RepositoryItemDuration</b> object that contains editor settings.

</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.UI.DailyRecurrenceControl">

            <summary>
                <para>Represents a control used to set the recurrence options for daily recurrent appointments.


</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.UI.DailyRecurrenceControl.#ctor">
            <summary>
                <para>Initializes a new instance of the DailyRecurrenceControl class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.DailyRecurrenceControl.ValidateValues(DevExpress.XtraScheduler.UI.ValidationArgs)">
            <summary>
                <para>Checks if the user input is valid for the control.

</para>
            </summary>
            <param name="args">
		A <see cref="T:DevExpress.XtraScheduler.UI.ValidationArgs"/> object that may contain the check result and an error message.


            </param>


        </member>
        <member name="T:DevExpress.XtraScheduler.UI.AppointmentStatusEdit">

            <summary>
                <para>Represents an image combo box control used to select appointment status. It facilitates the creation of custom appointment editor forms.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.UI.AppointmentStatusEdit.#ctor">
            <summary>
                <para>Initializes a new instance of the AppointmentStatusEdit class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.AppointmentStatusEdit.EditorTypeName">
            <summary>
                <para>Gets the class name of the editor.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> object identifying the class name.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.AppointmentStatusEdit.EditValue">
            <summary>
                <para>Gets or sets the editor's value.
</para>
            </summary>
            <value>A <see cref="T:System.Object"/> value.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.AppointmentStatusEdit.Properties">
            <summary>
                <para>Gets an object that contains editor specific settings.

</para>
            </summary>
            <value>A <b>RepositoryItemAppointmentStatus</b> object that contains editor settings.

</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.AppointmentStatusEdit.RefreshData">
            <summary>
                <para>Forces the control to reload its combo box item values. 
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.AppointmentStatusEdit.Status">
            <summary>
                <para>Gets or sets the currently selected appointment status.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.AppointmentStatus"/> that is currently selected in the control.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.AppointmentStatusEdit.Storage">
            <summary>
                <para>Gets or sets the <b>SchedulerStorage</b> object that contains items to fill an editor.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerStorage"/> object representing a storage for the <b>Scheduler</b> control.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.UI.AppointmentResourceEdit">

            <summary>
                <para>Represents an image combo box control used to select resources for an appointment. It facilitates the creation of custom appointment editor forms.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.UI.AppointmentResourceEdit.#ctor">
            <summary>
                <para>Initializes a new instance of the AppointmentResourceEdit class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.AppointmentResourceEdit.EditorTypeName">
            <summary>
                <para>Gets the class name of the editor.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> object identifying the class name.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.AppointmentResourceEdit.EditValue">
            <summary>
                <para>Gets or sets the editor's value.
</para>
            </summary>
            <value>A <see cref="T:System.Object"/> value.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.AppointmentResourceEdit.Properties">
            <summary>
                <para>Gets an object that contains specific editor settings.

</para>
            </summary>
            <value>A <b>RepositoryItemAppointmentResource</b> object that contains editor settings.

</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.AppointmentResourceEdit.RefreshData">
            <summary>
                <para>Updates the <see cref="T:DevExpress.XtraScheduler.UI.AppointmentResourceEdit"/> control to reflect changes of visible resources and re-creates the colored rectangles of the resource list.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.AppointmentResourceEdit.ResourceId">
            <summary>
                <para>Gets or sets the unique identifier for the resource of the currently selected appointment.

</para>
            </summary>
            <value>A <see cref="T:System.Object"/> value that specifies the resource's unique identifier. 

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.AppointmentResourceEdit.SchedulerControl">
            <summary>
                <para>Gets or sets the scheduler control assigned to the <b>AppointmentResourceEdit</b>.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> object representing the scheduler whose resources will be shown in this combo box.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.AppointmentResourceEdit.Storage">
            <summary>
                <para>Gets or sets the <b>SchedulerStorage</b> object that contains items to fill an editor.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerStorage"/> object representing a storage for the <b>Scheduler</b> control.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.UI.AppointmentLabelEdit">

            <summary>
                <para>Represents an image combo box control used to select appointment labels. It facilitates the creation of custom appointment editor forms.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.UI.AppointmentLabelEdit.#ctor">
            <summary>
                <para>Initializes a new instance of the AppointmentLabelEdit class with default settings.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.AppointmentLabelEdit.EditorTypeName">
            <summary>
                <para>Gets the class name of the editor.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> object identifying the class name.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.AppointmentLabelEdit.EditValue">
            <summary>
                <para>Gets or sets the editor's value.
</para>
            </summary>
            <value>A <see cref="T:System.Object"/> value.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.AppointmentLabelEdit.Label">
            <summary>
                <para>Gets or sets the currently selected appointment label.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.AppointmentLabel"/> that is currently selected in the control.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.AppointmentLabelEdit.Properties">
            <summary>
                <para>Gets an object that contains settings specific to the appointment label editor.

</para>
            </summary>
            <value>A <b>RepositoryItemAppointmentLabel</b> object that contains editor settings.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.AppointmentLabelEdit.RefreshData">
            <summary>
                <para>Forces the control to reload its combo box item values. 
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.AppointmentLabelEdit.Storage">
            <summary>
                <para>Gets or sets the <b>SchedulerStorage</b> object that contains items to fill an editor.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerStorage"/> object representing a storage for the <b>Scheduler</b> control.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.Drawing.SchedulerHitInfo">

            <summary>
                <para>Contains information about a specific point within a scheduler.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.SchedulerHitInfo.#ctor(DevExpress.XtraScheduler.Drawing.SelectableIntervalViewInfo,DevExpress.XtraScheduler.Drawing.SchedulerHitTest,DevExpress.XtraScheduler.Drawing.SchedulerHitInfo)">
            <summary>
                <para>Initializes a new instance of the SchedulerHitInfo class with the specified view and hit testing information, and in addition, hit information about the next scheduler element under the test point.

</para>
            </summary>
            <param name="viewInfo">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.SelectableIntervalViewInfo"/> object which contains view information. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.Drawing.SchedulerHitInfo.ViewInfo"/> property.

            </param>
            <param name="hitTest">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerHitTest"/> enumeration value which specifies the scheduler element under the test point. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.Drawing.SchedulerHitInfo.HitTest"/> property.

            </param>
            <param name="nextHitInfo">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerHitInfo"/> object which contains information about the next (underlying) scheduler element under the test point. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.Drawing.SchedulerHitInfo.NextHitInfo"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.SchedulerHitInfo.#ctor(DevExpress.XtraScheduler.Drawing.SelectableIntervalViewInfo,DevExpress.XtraScheduler.Drawing.SchedulerHitTest)">
            <summary>
                <para>Initializes a new instance of the SchedulerHitInfo class with the specified view and hit testing information.
</para>
            </summary>
            <param name="viewInfo">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.SelectableIntervalViewInfo"/> object which contains view information. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.Drawing.SchedulerHitInfo.ViewInfo"/> property.

            </param>
            <param name="hitTest">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerHitTest"/> enumeration value which specifies the scheduler element under the test point. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.Drawing.SchedulerHitInfo.HitTest"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.SchedulerHitInfo.Contains(DevExpress.XtraScheduler.Drawing.SchedulerHitTest)">
            <summary>
                <para>Determines whether the current hit information contains the specified Scheduler element.

</para>
            </summary>
            <param name="types">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerHitTest"/> enumeration value which specifies the scheduler element to locate.


            </param>
            <returns><b>true</b> if the specified Scheduler element is located under the test point; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.SchedulerHitInfo.FindFirstLayoutHitInfo">
            <summary>
                <para>Finds the first SchedulerHitInfo object that is not null for the current hit.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerHitInfo"/> object.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.SchedulerHitInfo.FindHitInfo(DevExpress.XtraScheduler.Drawing.SchedulerHitTest)">
            <summary>
                <para>Searches the hit information for the specified type(s) of the element.
</para>
            </summary>
            <param name="types">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerHitTest"/> enumeration value.


            </param>
            <returns>A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerHitInfo"/> object.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.SchedulerHitInfo.FindHitInfo(DevExpress.XtraScheduler.Drawing.SchedulerHitTest,DevExpress.XtraScheduler.Drawing.SchedulerHitTest)">
            <summary>
                <para>Searches the hit information for the specified type(s) of the element, traversing to the depth of the specified stop type(s).


</para>
            </summary>
            <param name="types">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerHitTest"/> enumeration value.


            </param>
            <param name="stopTypes">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerHitTest"/> enumeration value.


            </param>
            <returns>A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerHitInfo"/> object.
</returns>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.SchedulerHitInfo.HitTest">
            <summary>
                <para>Gets a value identifying the type of the visual element located under the test point.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerHitTest"/> enumeration value which identifies the type of the visual element that contains the test point.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.SchedulerHitInfo.NextHitInfo">
            <summary>
                <para>Provides the hit information about the element that is positioned next below the current hit element.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerHitInfo"/> object.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.SchedulerHitInfo.None">
            <summary>
                <para>Gets the hit information that specifies that the test point does not belong to any part of the scheduler.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerHitInfo"/> object that contains empty hit information.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.SchedulerHitInfo.ViewInfo">
            <summary>
                <para>Gets the object which contains the information used to render the visual element located under the test point.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Drawing.SelectableIntervalViewInfo"/> object providing view information on the visual element located under the test point.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.Drawing.SchedulerViewInfoBase">

            <summary>
                <para>Serves as a base for classes which provide view information for a selected element within a scheduler.

</para>
            </summary>

        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.SchedulerViewInfoBase.AppointmentAutoHeight">
            <summary>
                <para>Gets or sets whether an appointment should change its height automatically for the current view.
</para>
            </summary>
            <value><b>true</b> if an appointment's height is changed automatically to fit the text; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.SchedulerViewInfoBase.AppointmentDisplayOptions">
            <summary>
                <para>Provides access to the appointment's display options.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.AppointmentDisplayOptions"/> object containing options for displaying appointments.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.SchedulerViewInfoBase.AppointmentHeight">
            <summary>
                <para>Gets or sets the height of a single appointment for the current View (in pixels).
</para>
            </summary>
            <value>An integer value which represents the appointment height measured in pixels.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.SchedulerViewInfoBase.AppointmentViewInfos">
            <summary>
                <para>Provides access to the collection of objects containing information on appointments and their visual representation.
</para>
            </summary>
            <value>A <b>DevExpress.XtraScheduling.Drawing.AppointmentViewInfoCollection</b> object, representing a collection of <b>DevExpress.XtraScheduling.Drawing.AppointmentViewInfo</b> objects.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.SchedulerViewInfoBase.Bounds">
            <summary>
                <para>Gets the bounds contained in the view information.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Rectangle"/> value.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.SchedulerViewInfoBase.CalcFinalLayout">
            <summary>
                <para>Calculates the final layout for display of all visible elements of the current scheduler view on the screen.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.SchedulerViewInfoBase.CalcHitInfo(System.Drawing.Point,System.Boolean)">
            <summary>
                <para>Returns information on scheduler elements located at the specified point.
</para>
            </summary>
            <param name="pt">
		A <see cref="T:System.Drawing.Point"/> structure which specifies the test point coordinates relative to the scheduler's top-left corner.

            </param>
            <param name="layoutOnly">
		<b>true</b> if the appointments are ignored and only the scheduler's layout is taken into consideration; otherwise, <b>false</b>.

            </param>
            <returns>A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerHitInfo"/> object which contains information about scheduler elements located at the test point.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.SchedulerViewInfoBase.CalcPreliminaryLayout">
            <summary>
                <para>Calculates the preliminary layout for display of all visible elements of the current scheduler view on the screen.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.SchedulerViewInfoBase.CellContainers">
            <summary>
                <para>Provides access to visible time cells in the current View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerViewCellContainerCollection"/> class instance, representing a container for visible cells.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.SchedulerViewInfoBase.Dispose">
            <summary>
                <para>Disposes of the SchedulerViewInfoBase object.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.SchedulerViewInfoBase.GroupSeparators">
            <summary>
                <para>Gets the group separators settings.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerHeaderCollection"/> object. 
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.SchedulerViewInfoBase.MakeAppointmentVisibleInScrollContainers(DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Scrolls the appointment container so the specified appointment becomes visible.
</para>
            </summary>
            <param name="appointment">
		An <see cref="T:DevExpress.XtraScheduler.Appointment"/> object representing the appointment to show.

            </param>
            <returns><b>true</b> if the method succeeds; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.SchedulerViewInfoBase.MoreButtons">
            <summary>
                <para>Gets the collection of all More Buttons shown in the current scheduler view. 
</para>
            </summary>
            <value>A <b>MoreButtonCollection</b> instance, that is the collection of <b>DevExpress.XtraScheduler.Native.MoreButton</b> objects.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.SchedulerViewInfoBase.NavigationButtons">
            <summary>
                <para>Provides access to the Navigation Buttons collection of the current view.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Drawing.NavigationButtonCollection"/> object that represents navigation buttons for the view.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.SchedulerViewInfoBase.PaintAppearance">
            <summary>
                <para>Gets the appearance settings applied to the current scheduler view.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.BaseViewAppearance"/> object.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.SchedulerViewInfoBase.Painter">
            <summary>
                <para>Gets an object that provides the painting functionality of the scheduler control's Views.
</para>
            </summary>
            <value>A <b>DevExpress.XtraScheduler.Drawing.ViewPainterBase</b> class instance.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.SchedulerViewInfoBase.ResourceHeaders">
            <summary>
                <para>Gets the resource headers currently shown in the current scheduler view.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerHeaderCollection"/> object.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.SchedulerViewInfoBase.View">
            <summary>
                <para>Gets the current scheduler view.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerViewBase"/> object.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.SchedulerViewInfoBase.VisibleIntervals">
            <summary>
                <para>Gets the visible intervals of the current view.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeIntervalCollection"/> object.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.SchedulerViewInfoBase.VisibleResources">
            <summary>
                <para>Gets a collection of visible resources for the current view.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.ResourceBaseCollection"/> object.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.SchedulerResourceHeaderOptions">

            <summary>
                <para>Provides resource header options for the Scheduler control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerResourceHeaderOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerResourceHeaderOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerResourceHeaderOptions.Height">
            <summary>
                <para>Gets or sets the height of the resource header.

</para>
            </summary>
            <value>An integer value which represents the height of the resource header.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerResourceHeaderOptions.ImageAlign">
            <summary>
                <para>Gets or sets the image alignment within a resource header.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.HeaderImageAlign"/> enumeration value which specifies how the image is aligned.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerResourceHeaderOptions.ImageInterpolationMode">
            <summary>
                <para>Gets or sets the algorithm used for image scaling in the headers.
</para>
            </summary>
            <value>An <see cref="T:System.Drawing.Drawing2D.InterpolationMode"/> enumeration member specifying the algorithm that is used when images are scaled. 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerResourceHeaderOptions.ImageSize">
            <summary>
                <para>Gets or sets the size of an image which is shown within a resource header.

</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Size"/> value which represents the size of an image.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerResourceHeaderOptions.ImageSizeMode">
            <summary>
                <para>Gets or sets the size mode of an image which is shown within a resource header.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.HeaderImageSizeMode"/> enumeration value which represents an image's size mode.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerResourceHeaderOptions.RotateCaption">
            <summary>
                <para>Gets or sets the value indicating whether to rotate the caption's text.
</para>
            </summary>
            <value><b>true</b> to rotate the caption's text; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.HeaderImageSizeMode">

            <summary>
                <para>Specifies how an image is positioned within a resource header.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraScheduler.HeaderImageSizeMode.CenterImage">
            <summary>
                <para>The image is displayed in the center of the resource header. If the image is larger than the resource header, the outside edges are clipped.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraScheduler.HeaderImageSizeMode.Normal">
            <summary>
                <para>The image is placed within the resource header in its ordinary manner using its own height and width. Note that in this case the image is clipped if it's larger than the resource header which contains it.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraScheduler.HeaderImageSizeMode.StretchImage">
            <summary>
                <para>The image within the resource header is stretched or shrunk as appropriate to fit the size of the resource header and according to its position relative to the header's text.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraScheduler.HeaderImageSizeMode.ZoomImage">
            <summary>
                <para>The image is sized proportionally (without clipping), so that it's best fitted to the resource header. For instance, if the resource header contains no text, and if the height and width ratio of the resource header is the same as the image's ratio it will be resized to exactly fit into the resource header. Otherwise the closest fitting side (height or width) of the image will be sized to the resource header and the other side (height or width) of the image sized proportionally (leaving empty space). 

</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraScheduler.HeaderImageAlign">

            <summary>
                <para>Specifies how an image is positioned relative to the text within a resource header.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraScheduler.HeaderImageAlign.Bottom">
            <summary>
                <para><para>A resource image is aligned to the bottom of the text.</para>


</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraScheduler.HeaderImageAlign.Left">
            <summary>
                <para><para>A resource image is aligned to the left of the text.</para>


</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraScheduler.HeaderImageAlign.Right">
            <summary>
                <para><para>A resource image is aligned to the right of the text.</para>


</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraScheduler.HeaderImageAlign.Top">
            <summary>
                <para><para>A resource image is aligned to the top of the text.</para>


</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraScheduler.UI.ResourcesCheckedListBoxControl">

            <summary>
                <para>Represents the checked list box control used to filter resources within the <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/>.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.UI.ResourcesCheckedListBoxControl.#ctor(DevExpress.XtraScheduler.UI.ResourceFilterController)">
            <summary>
                <para>Initializes a new instance of the ResourcesCheckedListBoxControl class with the specified filter controller.
</para>
            </summary>
            <param name="controller">
		A <see cref="T:DevExpress.XtraScheduler.UI.ResourceFilterController"/> object which represents the filter controller of the resources checked list box control. 

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.ResourcesCheckedListBoxControl.#ctor">
            <summary>
                <para>Initializes a new instance of the ResourcesCheckedListBoxControl class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.ResourcesCheckedListBoxControl.ResetResourcesItems(DevExpress.XtraScheduler.ResourceBaseCollection)">
            <summary>
                <para>Resets all the resource items and their visibility state in the check list according to the specified <i>resources</i> collection.

</para>
            </summary>
            <param name="resources">
		A <see cref="T:DevExpress.XtraScheduler.ResourceBaseCollection"/> object which contains the resources to be used when updating the check list.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.ResourcesCheckedListBoxControl.ResourceVisibleChanged(DevExpress.XtraScheduler.ResourceBaseCollection)">
            <summary>
                <para>Resets all the resource items and their visibility state in the check list according to the specified <i>resources</i> collection and preserves the index of currently selected item.

</para>
            </summary>
            <param name="resources">
		A <see cref="T:DevExpress.XtraScheduler.ResourceBaseCollection"/> object which contains the resources to be used when updating the check list.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.ResourcesCheckedListBoxControl.SchedulerControl">
            <summary>
                <para>Gets or sets the scheduler control which is assigned to the ResourcesCheckedListBoxControl.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> object representing the scheduler whose resources will be filtered by this checked list box.

</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.InplaceEditorEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.InplaceEditorShowing"/> event. 
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.InplaceEditorEventHandler.Invoke(System.Object,DevExpress.XtraScheduler.InplaceEditorEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.InplaceEditorShowing"/> event.

</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> which raised the event.

            </param>
            <param name="e">
		An <see cref="T:DevExpress.XtraScheduler.InplaceEditorEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraScheduler.InplaceEditorEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.InplaceEditorShowing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.InplaceEditorEventArgs.#ctor(DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Initializes a new instance of the InplaceEditorEventArgs class with the specified appointment.
</para>
            </summary>
            <param name="apt">
		A <see cref="T:DevExpress.XtraScheduler.Appointment"/> value which represents the event's appointment. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.AppointmentEventArgs.Appointment"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.InplaceEditorEventArgs.InplaceEditor">
            <summary>
                <para>Gets or sets the in-place editor which is invoked when an end-user adds a new appointment or edits "in place" an existing one.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.ISchedulerInplaceEditor"/> object which represents the in-place editor.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.InplaceEditorEventArgs.InplaceEditorEx">
            <summary>
                <para>Gets or sets the in-place editor which is invoked when an end-user adds a new appointment or edits "in place" an existing one.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.ISchedulerInplaceEditorEx"/> object which represents the in-place editor.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.InplaceEditorEventArgs.SchedulerInplaceEditorEventArgs">
            <summary>
                <para>Gets or sets the object that specifies the scheduler control instance and basic characteristics used to customize the inplace editor appearance and layout.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerInplaceEditorEventArgs"/> object.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.ResourceNavigator">

            <summary>
                <para>Represents the Resource Navigator control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.ResourceNavigator.#ctor(DevExpress.XtraScheduler.SchedulerControl)">
            <summary>
                <para>Initializes a new instance of the ResourceNavigator class with the specified <b>Scheduler Control</b>.

</para>
            </summary>
            <param name="control">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> object which specifies the owner of this resource navigator.


            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.ResourceNavigator.Buttons">
            <summary>
                <para>Provides access to buttons displayed in a scheduler resource navigator.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.ControlNavigatorButtons"/> object representing buttons of a scheduler resource navigator. 
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.ResourceNavigator.Dispose">
            <summary>
                <para>Disposes of the ResourceNavigator object.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.ResourceNavigator.ShowToolTips">
            <summary>
                <para>Gets or sets a value which specifies whether the navigator can display hints.
</para>
            </summary>
            <value><b>true</b> if the navigator can display hints; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.ResourceNavigator.Visibility">
            <summary>
                <para>Gets or sets the visibility behaviour of the ResourceNavigator.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.ResourceNavigatorVisibility"/> enumeration value which specifies the visibility behaviour of the resource navigator.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.Services.IHeaderToolTipService">

            <summary>
                <para>Provides methods for custom formatting the tooltips of the header captions in different Scheduler views.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.Services.IHeaderToolTipService.GetDayColumnHeaderToolTip(DevExpress.XtraScheduler.Drawing.DayHeader)">
            <summary>
                <para>Implements a method which returns a format string for displaying the tooltip for the DayColumnHeader in a <b>Day View</b>.
</para>
            </summary>
            <param name="header">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.DayHeader"/> object representing a header in the Day View.

            </param>
            <returns>A format string.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.Services.IHeaderToolTipService.GetDayOfWeekHeaderToolTip(DevExpress.XtraScheduler.Drawing.DayOfWeekHeader)">
            <summary>
                <para>Implements a method which returns a format string for displaying the tooltip for the DayOfWeekHeader in a <b>Month View</b>.
</para>
            </summary>
            <param name="header">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.DayOfWeekHeader"/> object representing a header in the Month View.

            </param>
            <returns>A format string.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.Services.IHeaderToolTipService.GetTimeScaleHeaderToolTip(DevExpress.XtraScheduler.Drawing.TimeScaleHeader)">
            <summary>
                <para>Implements a method which returns a format string for displaying the tooltip for the TimeScaleHeader in  a <b>Timescale View</b>.
</para>
            </summary>
            <param name="header">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.TimeScaleHeader"/> object representing a header in the Timescale View.

            </param>
            <returns>A format string.
</returns>


        </member>
        <member name="T:DevExpress.XtraScheduler.Services.IHeaderCaptionService">

            <summary>
                <para>Provides methods for custom formatting the header captions in different Scheduler views.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.Services.IHeaderCaptionService.GetDayColumnHeaderCaption(DevExpress.XtraScheduler.Drawing.DayHeader)">
            <summary>
                <para>Implements a method which returns the format of the column header caption in the <b>Day View</b>.
</para>
            </summary>
            <param name="header">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.DayHeader"/> object representing a header in the Day View.

            </param>
            <returns>A format string.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.Services.IHeaderCaptionService.GetDayOfWeekAbbreviatedHeaderCaption(DevExpress.XtraScheduler.Drawing.DayOfWeekHeader)">
            <summary>
                <para>Implements a method which returns a format string for displaying short day of week captions in the <b>Month View</b>.
</para>
            </summary>
            <param name="header">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.DayOfWeekHeader"/> object representing a header.

            </param>
            <returns>A format string.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.Services.IHeaderCaptionService.GetDayOfWeekHeaderCaption(DevExpress.XtraScheduler.Drawing.DayOfWeekHeader)">
            <summary>
                <para>Implements a method which returns a format string for displaying the DayOfWeek header caption in a <b>Month View</b>.
</para>
            </summary>
            <param name="header">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.DayOfWeekHeader"/> object representing a header.

            </param>
            <returns>A format string.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.Services.IHeaderCaptionService.GetHorizontalWeekCellHeaderCaption(DevExpress.XtraScheduler.Drawing.SchedulerHeader)">
            <summary>
                <para>Implements a method which returns a format string for displaying the HorizontalWeekCell header caption in a <b>Month View</b>.
</para>
            </summary>
            <param name="header">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerHeader"/> object, representing a header.

            </param>
            <returns>A format string.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.Services.IHeaderCaptionService.GetTimeScaleHeaderCaption(DevExpress.XtraScheduler.Drawing.TimeScaleHeader)">
            <summary>
                <para>Implements a method which returns a format string for displaying the TimeScale header caption in a <b>Timescale View</b>.
</para>
            </summary>
            <param name="header">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.TimeScaleHeader"/> object, representing a header.

            </param>
            <returns>A format string.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.Services.IHeaderCaptionService.GetVerticalWeekCellHeaderCaption(DevExpress.XtraScheduler.Drawing.SchedulerHeader)">
            <summary>
                <para>Implements a method which returns a format string for displaying the VerticalWeekCell header caption in <b>Week View</b>.
</para>
            </summary>
            <param name="header">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerHeader"/> object, representing a header.

            </param>
            <returns>A format string.
</returns>


        </member>
        <member name="T:DevExpress.XtraScheduler.Services.Implementation.HeaderToolTipService">

            <summary>
                <para>Provides a wrapper for overriding methods used for custom formatting of the tooltips for header captions in different Scheduler views.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.Services.Implementation.HeaderToolTipService.#ctor">
            <summary>
                <para>Initializes a new instance of the HeaderToolTipService class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.Services.Implementation.HeaderToolTipService.GetDayColumnHeaderToolTip(DevExpress.XtraScheduler.Drawing.DayHeader)">
            <summary>
                <para>Override this method to return a format string for displaying the tooltip for the DayColumnHeader in  a <b>Day View</b>.
</para>
            </summary>
            <param name="header">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.DayHeader"/> object representing a header in the Day View.

            </param>
            <returns>A format string.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.Services.Implementation.HeaderToolTipService.GetDayOfWeekHeaderToolTip(DevExpress.XtraScheduler.Drawing.DayOfWeekHeader)">
            <summary>
                <para>Override this method to return a format string for displaying the tooltip for the DayOfWeekHeader in a <b>Month View</b>.
</para>
            </summary>
            <param name="header">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.DayOfWeekHeader"/> object representing a header in the Month View.

            </param>
            <returns>A format string.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.Services.Implementation.HeaderToolTipService.GetTimeScaleHeaderToolTip(DevExpress.XtraScheduler.Drawing.TimeScaleHeader)">
            <summary>
                <para>Override this method to return a format string for displaying the tooltip for the TimeScaleHeader in  a <b>Timescale View</b>.
</para>
            </summary>
            <param name="header">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.TimeScaleHeader"/> object representing a header in the Timescale View.

            </param>
            <returns>A format string.
</returns>


        </member>
        <member name="T:DevExpress.XtraScheduler.Drawing.AppointmentImageInfoCollection">

            <summary>
                <para>Represents a collection of objects that contain information on appointment images.


</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.AppointmentImageInfoCollection.#ctor(DevExpress.XtraScheduler.Drawing.AppointmentImageProvider)">
            <summary>
                <para>Initializes a new instance of the AppointmentImageInfoCollection class with the specified image provider.
</para>
            </summary>
            <param name="imageProvider">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.AppointmentImageProvider"/> object which provides images for the created collection.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.AppointmentImageInfoCollection.Images">
            <summary>
                <para>Gets or sets the source of images that can be displayed within appointments.

</para>
            </summary>
            <value>An object that provides images for appointments.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.AppointmentImageInfoCollection.InnerImages">
            <summary>
                <para>Provides access to the collection of images used to draw an appointment. Intended for internal use.
</para>
            </summary>
            <value>An object that provides images for appointments.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.Drawing.AppointmentImageInfo">

            <summary>
                <para>Provides information on the image object displayed within the appointment.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.AppointmentImageInfo.#ctor">
            <summary>
                <para>Initializes a new instance of the AppointmentImageInfo class with default settings.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.AppointmentImageInfo.Image">
            <summary>
                <para>Gets or sets the image to be displayed within the appointment.

</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Image"/> object which represents the appointment's image.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.UI.AppointmentFormController">

            <summary>
                <para>Provides all the settings which are required to edit a particular appointment in an <b>Edit Appointment</b> form.


</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.UI.AppointmentFormController.#ctor(DevExpress.XtraScheduler.SchedulerControl,DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Initializes a new instance of the AppointmentFormController class with the specified appointment and scheduler control.
</para>
            </summary>
            <param name="control">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> object which represents the scheduler control of the appointment form controller.

            </param>
            <param name="apt">
		An <see cref="T:DevExpress.XtraScheduler.Appointment"/> object which represents the appointment of the appointment form controller.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.AppointmentFormController.GetLabel">
            <summary>
                <para>Returns the current label of the appointment currently being edited in the form.

</para>
            </summary>
            <returns>An <see cref="T:DevExpress.XtraScheduler.AppointmentLabel"/> object which represents the label of the appointment.

</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.AppointmentFormController.GetStatus">
            <summary>
                <para>Returns the current status of the appointment currently being edited in the form.

</para>
            </summary>
            <returns>An <see cref="T:DevExpress.XtraScheduler.AppointmentStatus"/> object which represents the status of the appointment.

</returns>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.AppointmentFormController.PercentComplete">
            <summary>
                <para>Gets or sets the <b>PercentComplete</b> property value of the appointment currently being edited in the form.
</para>
            </summary>
            <value>An integer that is the percentage of completion for the task currently being edited in the form.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.AppointmentFormController.ShouldEditTaskProgress">
            <summary>
                <para>Gets whether the form should enable modifying the <b>PercentComplete</b> property value by end-users.
</para>
            </summary>
            <value><b>true</b> to allow task progress editing within the form; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.AppointmentFormController.UpdateAppointmentStatus(DevExpress.XtraScheduler.AppointmentStatus)">
            <summary>
                <para>Recalculates and updates the status of the appointment according to certain rules.
</para>
            </summary>
            <param name="currentStatus">
		An <see cref="T:DevExpress.XtraScheduler.AppointmentStatus"/> object, representing the original status.

            </param>
            <returns>An <see cref="T:DevExpress.XtraScheduler.AppointmentStatus"/> object, representing the new status.
</returns>


        </member>
        <member name="T:DevExpress.XtraScheduler.GotoDateFormEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.GotoDateFormShowing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.GotoDateFormEventHandler.Invoke(System.Object,DevExpress.XtraScheduler.GotoDateFormEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.GotoDateFormShowing"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> which raised the event.


            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraScheduler.GotoDateFormEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraScheduler.GotoDateFormEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.GotoDateFormShowing"/> event.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.GotoDateFormEventArgs.#ctor">
            <summary>
                <para>Initializes a new instance of the GotoDateFormEventArgs class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.GotoDateFormEventArgs.Date">
            <summary>
                <para>Gets or sets the date shown in the <b>Go To Date</b> dialog window.
</para>
            </summary>
            <value>A <see cref="T:System.DateTime"/> object representing the date shown in the dialog.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.GotoDateFormEventArgs.SchedulerViewType">
            <summary>
                <para>Gets or sets the <b>View</b> type shown in the <b>Go To Date</b> dialog window.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerViewType"/> enumeration value representing the <b>View</b> type.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.ShowFormEventArgs">

            <summary>
                <para>Provides data for the events which show dialogs in the XtraScheduler.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.ShowFormEventArgs.#ctor">
            <summary>
                <para>Initializes a new instance of the ShowFormEventArgs class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.ShowFormEventArgs.DialogResult">
            <summary>
                <para>Gets or sets the return value of a dialog box.

</para>
            </summary>
            <value>A <see cref="T:System.Windows.Forms.DialogResult"/> enumeration value that specifies the value that is returned by the dialog box.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.ShowFormEventArgs.Handled">
            <summary>
                <para>Gets or sets whether an event was handled, if it was handled the default actions are not required.

</para>
            </summary>
            <value><b>true</b> if it was handled and the default dialog doesn't need to be shown; otherwise, <b>false</b>.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.ShowFormEventArgs.Parent">
            <summary>
                <para>Gets or sets a parent of the form being shown.
</para>
            </summary>
            <value>An <see cref="T:System.Windows.Forms.IWin32Window"/> object.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.RemindersFormEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.RemindersFormShowing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.RemindersFormEventHandler.Invoke(System.Object,DevExpress.XtraScheduler.RemindersFormEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.RemindersFormShowing"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> which raised the event.


            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraScheduler.RemindersFormEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraScheduler.RemindersFormEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.RemindersFormShowing"/> event.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.RemindersFormEventArgs.#ctor(DevExpress.XtraScheduler.ReminderAlertNotificationCollection)">
            <summary>
                <para>Initializes a new instance of the RemindersFormEventArgs class with the specified settings.
</para>
            </summary>
            <param name="alerts">
		A <see cref="T:DevExpress.XtraScheduler.ReminderAlertNotificationCollection"/> value which represents the event's collection of reminder alerts. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.RemindersFormEventArgs.AlertNotifications"/> property. 

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.RemindersFormEventArgs.AlertNotifications">
            <summary>
                <para>Gets any reminders currently triggered.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.ReminderAlertNotificationCollection"/> object which holds a collection of notifications.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.DeleteRecurrentAppointmentFormEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.DeleteRecurrentAppointmentFormShowing"/> event.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.DeleteRecurrentAppointmentFormEventHandler.Invoke(System.Object,DevExpress.XtraScheduler.DeleteRecurrentAppointmentFormEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.DeleteRecurrentAppointmentFormShowing"/> event.

</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> which raised the event.


            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraScheduler.DeleteRecurrentAppointmentFormEventArgs"/> object which contains event data.


            </param>


        </member>
        <member name="T:DevExpress.XtraScheduler.DeleteRecurrentAppointmentFormEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.DeleteRecurrentAppointmentFormShowing"/> event.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.DeleteRecurrentAppointmentFormEventArgs.#ctor(DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Initializes a new instance of the DeleteRecurrentAppointmentFormEventArgs class with the specified settings.
</para>
            </summary>
            <param name="apt">
		An <see cref="T:DevExpress.XtraScheduler.Appointment"/> value which represents the event's appointment. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.AppointmentFormEventArgs.Appointment"/> property. 

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.DeleteRecurrentAppointmentFormEventArgs.DeleteSeries">
            <summary>
                <para>Gets or sets a value indicating if the entire series or just the recurrent appointment should be deleted.
</para>
            </summary>
            <value><b>true</b> if the entire series should be deleted; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.AppointmentFormEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.EditAppointmentFormShowing"/> event.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.AppointmentFormEventHandler.Invoke(System.Object,DevExpress.XtraScheduler.AppointmentFormEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.EditAppointmentFormShowing"/> event.

</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> which raised the event.


            </param>
            <param name="e">
		An <see cref="T:DevExpress.XtraScheduler.AppointmentFormEventArgs"/> object which contains event data.


            </param>


        </member>
        <member name="T:DevExpress.XtraScheduler.AppointmentFormEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.EditAppointmentFormShowing"/> event.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.AppointmentFormEventArgs.#ctor(DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Initializes a new instance of the AppointmentFormEventArgs class with the specified settings.

</para>
            </summary>
            <param name="apt">
		An <see cref="T:DevExpress.XtraScheduler.Appointment"/> value which represents the event's appointment. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.AppointmentFormEventArgs.Appointment"/> property. 

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.AppointmentFormEventArgs.Appointment">
            <summary>
                <para>Gets the appointment which the dialog will be shown for.


</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.Appointment"/> object representing the appointment currently being processed.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.AppointmentFormEventArgs.CommandSourceType">
            <summary>
                <para>Indicates the mechanism of the command input, e.g. keyboard, mouse, menu.

</para>
            </summary>
            <value>A <b>DevExpress.Utils.Commands.CommandSource</b> enumeration member, specifying the command input method.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.AppointmentFormEventArgs.OpenRecurrenceForm">
            <summary>
                <para>Gets a value indicating whether the <b>Appointment Recurrence</b> form is displayed on the top of the <b>Edit Appointment</b> form.
</para>
            </summary>
            <value><b>true</b> if the <b>Appointment Recurrence</b> form is displayed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.AppointmentFormEventArgs.ReadOnly">
            <summary>
                <para>Gets a value indicating whether an appointment is read-only.
</para>
            </summary>
            <value><b>true</b> if the appointment is read-only; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.WorkWeekView">

            <summary>
                <para>Represents a Work Week View.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.WorkWeekView.#ctor(DevExpress.XtraScheduler.SchedulerControl)">
            <summary>
                <para>Initializes a new instance of the WorkWeekView class with the specified <b>Scheduler</b> control.
</para>
            </summary>
            <param name="control">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> value that specifies the <b>Scheduler</b> control assigned to the work week view. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.SchedulerViewBase.Control"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.WorkWeekView.Appearance">
            <summary>
                <para>Provides access to the properties that control the appearance of the WorkWeekView's elements.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.WorkWeekViewAppearance"/> object that provides the appearance settings for WorkWeekView elements.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.WorkWeekView.MenuItemId">
            <summary>
                <para>Gets the ID of the menu item which corresponds to the <b>Work Week View</b>.
</para>
            </summary>
            <value>The <see cref="F:DevExpress.XtraScheduler.SchedulerMenuItemId.SwitchToWorkWeekView"/> value.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.WorkWeekView.ShowFullWeek">
            <summary>
                <para>Gets or sets a value indicating whether this view should show all the days of the week.

</para>
            </summary>
            <value><b>true</b> to show the full week; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.WorkWeekView.Type">
            <summary>
                <para>Gets the view's type.
</para>
            </summary>
            <value>The <see cref="F:DevExpress.XtraScheduler.SchedulerViewType.WorkWeek"/> value.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.SchedulerViewRepository">

            <summary>
                <para>Represents the view repository.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerViewRepository.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerViewRepository class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewRepository.DayView">
            <summary>
                <para>Gets an object that defines the settings of the scheduler's Day View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.DayView"/> object representing the Day View in the scheduling area.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewRepository.GanttView">
            <summary>
                <para>Gets an object that defines the settings of the scheduler's Gantt view.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.GanttView"/> object that is the Gantt View in the scheduling area.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewRepository.MonthView">
            <summary>
                <para>Gets an object that defines the settings of the scheduler's Month View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.MonthView"/> object representing the Month View in the scheduling area.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewRepository.TimelineView">
            <summary>
                <para>Gets an object that defines the settings of the scheduler's Timeline View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimelineView"/> object representing the Timeline View in the scheduling area.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewRepository.WeekView">
            <summary>
                <para>Gets an object that defines the settings of the scheduler's Week View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.WeekView"/> object representing the Week View in the scheduling area.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewRepository.WorkWeekView">
            <summary>
                <para>Gets an object that defines the settings of the scheduler's Work Week View.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.WorkWeekView"/> object representing the Work Week View in the scheduling area.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.AppointmentStorage">

            <summary>
                <para>Represents a storage which holds a collection of appointments.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.AppointmentStorage.#ctor(DevExpress.XtraScheduler.SchedulerStorage)">
            <summary>
                <para>Initializes a new instance of the AppointmentStorage class with the specified scheduler storage.
</para>
            </summary>
            <param name="storage">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerStorage"/> value that specifies the scheduler storage of the appointment storage.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.AppointmentStorage.CommitIdToDataSource">
            <summary>
                <para>Gets or sets whether the appointment Id value should be passed to the data source.
</para>
            </summary>
            <value><b>true</b> to pass the <see cref="P:DevExpress.XtraScheduler.Appointment.Id"/> value to the mapped field in the data source; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.AppointmentStorage.CustomFieldMappings">
            <summary>
                <para>Provides access to the collection of objects, representing mappings of the appointments' custom properties to appropriate data fields. 
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.AppointmentCustomFieldMappingCollection"/> object.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.AppointmentStorage.Filter">
            <summary>
                <para>Gets or set criteria to filter appointments in the storage.
</para>
            </summary>
            <value>A string containing a logical expression that is the filter criteria.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.AppointmentStorage.Labels">
            <summary>
                <para>Gets the collection of appointment labels. 

</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.AppointmentLabelCollection"/> which represents the collection of appointment labels. 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.AppointmentStorage.Mappings">
            <summary>
                <para>Gets an object that allows the persistent properties of the appointments maintained by the current storage to be bound to appropriate fields in the data source.

</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.AppointmentMappingInfo"/> object that provides information on the mapping of the appointment's properties to the appropriate data fields.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.AppointmentStorage.Statuses">
            <summary>
                <para>Gets the collection of appointment statuses. 
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.AppointmentStatusCollection"/> which represents the collection of appointment statuses. 
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.WeekViewAppearance">

            <summary>
                <para>Provides the appearance settings used to paint a Week View.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.WeekViewAppearance.#ctor">
            <summary>
                <para>Initializes a new instance of the WeekViewAppearance class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.WeekViewAppearance.AlternateHeaderCaption">
            <summary>
                <para>Gets the appearance settings used to paint the alternate header.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the alternate header.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.WeekViewAppearance.AlternateHeaderCaptionLine">
            <summary>
                <para>Gets the appearance settings used to paint the horizontal line under the alternate header.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the horizontal line under the alternate header.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.WeekViewAppearance.CellHeaderCaption">
            <summary>
                <para>Gets the appearance settings used to paint a day cell's header within the Week or Month View. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint a day cell's header. 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.WeekViewAppearance.CellHeaderCaptionLine">
            <summary>
                <para>Gets the appearance settings used to paint the line at the bottom of a day cell's header within the Week or Month View. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint the bottom line of a day cell's header. 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.WeekViewAppearance.TodayCellHeaderCaption">
            <summary>
                <para>Gets the appearance settings used to paint the header of the day cell representing the current date within the Week or Month View. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint the today cell's header. 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.WeekViewAppearance.TodayCellHeaderCaptionLine">
            <summary>
                <para>Gets the appearance settings used to paint the line at the bottom of the today cell's header within the Week or Month View. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint the line at the bottom of the today cell's header. 
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.SchedulerPopupMenu">

            <summary>
                <para>Represents a popup (context) menu of the <b>Scheduler</b>.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerPopupMenu.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerPopupMenu class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerPopupMenu.#ctor(System.EventHandler)">
            <summary>
                <para>Initializes a new instance of the SchedulerPopupMenu class with the specified before popup event handler.
</para>
            </summary>
            <param name="beforePopup">
		A <see cref="T:System.EventHandler"/> object that specifies the event handler for the <see cref="E:DevExpress.Utils.Menu.DXSubMenuItem.BeforePopup"/> event.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerPopupMenu.GetMenuCheckItemById(DevExpress.XtraScheduler.SchedulerMenuItemId,System.Boolean)">
            <summary>
                <para>Gets a menu check item by its ID value. Also optionally recursively searches for this menu check item in all submenus, if it isn't found in the main popup menu.


</para>
            </summary>
            <param name="id">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerMenuItemId"/> enumeration value which specifies the ID value of the menu check item to search.

            </param>
            <param name="recursive">
		<b>true</b> to search for the menu check item recursively in all submenus; otherwise, <b>false</b>.

            </param>
            <returns>A <see cref="T:DevExpress.XtraScheduler.SchedulerMenuCheckItem"/> object whose ID is equal to the specified ID value. If a check item with the specified ID isn't found, then the <b>null</b> (<b>Nothing</b> in Visual Basic) value will be returned.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerPopupMenu.GetMenuCheckItemById(DevExpress.XtraScheduler.SchedulerMenuItemId)">
            <summary>
                <para>Gets a menu check item by its ID value.

</para>
            </summary>
            <param name="id">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerMenuItemId"/> enumeration value which specifies the ID value of the menu check item to search.

            </param>
            <returns>A <see cref="T:DevExpress.XtraScheduler.SchedulerMenuCheckItem"/> object whose ID is equal to the specified ID value. If a check item with the specified ID isn't found, then the <b>null</b> (<b>Nothing</b> in Visual Basic) value will be returned.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerPopupMenu.GetMenuCheckItemById(DevExpress.Utils.Menu.DXPopupMenu,DevExpress.XtraScheduler.SchedulerMenuItemId,System.Boolean)">
            <summary>
                <para>Gets the menu check item within the specified popup (context) menu by its ID value. Also optionally recursively searches for this check item in all submenus, if it isn't found in the main popup menu.

</para>
            </summary>
            <param name="menu">
		A <see cref="T:DevExpress.Utils.Menu.DXPopupMenu"/> object which represents the popup menu to search in.

            </param>
            <param name="id">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerMenuItemId"/> enumeration value which specifies the ID value of the menu check item to search.

            </param>
            <param name="recursive">
		<b>true</b> to recursively search for the menu check item in all submenus; otherwise, <b>false</b>.


            </param>
            <returns>A <see cref="T:DevExpress.XtraScheduler.SchedulerMenuCheckItem"/> object whose ID is equal to the specified ID value. If a check item with the specified ID isn't found, then the <b>null</b> (<b>Nothing</b> in Visual Basic) value will be returned.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerPopupMenu.GetMenuItemById(DevExpress.XtraScheduler.SchedulerMenuItemId,System.Boolean)">
            <summary>
                <para>Gets a menu item by its ID value. Also optionally recursively searches for this menu item in all submenus, if it isn't found in the main popup menu.


</para>
            </summary>
            <param name="id">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerMenuItemId"/> enumeration value which specifies the ID value of the menu item to search.

            </param>
            <param name="recursive">
		<b>true</b> to recursively search for the menu item in all submenus; otherwise, <b>false</b>.


            </param>
            <returns>A <see cref="T:DevExpress.XtraScheduler.SchedulerMenuItem"/> object whose ID is equal to the specified ID value. If a menu item with the  specified ID isn't found, then the <b>null</b> (<b>Nothing</b> in Visual Basic) value will be returned.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerPopupMenu.GetMenuItemById(DevExpress.Utils.Menu.DXPopupMenu,DevExpress.XtraScheduler.SchedulerMenuItemId,System.Boolean)">
            <summary>
                <para>Gets the menu item within the specified popup (context) menu by its ID value. Also optionally recursively searches for this menu item in all submenus, if it isn't found in the main popup menu.

</para>
            </summary>
            <param name="menu">
		A <see cref="T:DevExpress.Utils.Menu.DXPopupMenu"/> object which represents the popup menu to search in.

            </param>
            <param name="id">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerMenuItemId"/> enumeration value which specifies the ID value of the menu item to search.

            </param>
            <param name="recursive">
		<b>true</b> to recursively search for the menu item in all submenus; otherwise, <b>false</b>.


            </param>
            <returns>A <see cref="T:DevExpress.XtraScheduler.SchedulerMenuItem"/> object whose ID is equal to the specified ID value. If a menu item with the  specified ID isn't found, then the <b>null</b> (<b>Nothing</b> in Visual Basic) value will be returned.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerPopupMenu.GetMenuItemById(DevExpress.XtraScheduler.SchedulerMenuItemId)">
            <summary>
                <para>Gets a menu item by its ID value.
</para>
            </summary>
            <param name="id">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerMenuItemId"/> enumeration value which specifies the ID value of the menu item to be searched for.


            </param>
            <returns>A <see cref="T:DevExpress.XtraScheduler.SchedulerMenuItem"/> object whose ID is equal to the specified ID value. If a menu item with the  specified ID isn't found, then the <b>null</b> (<b>Nothing</b> in Visual Basic) value will be returned.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerPopupMenu.GetPopupMenuById(DevExpress.XtraScheduler.SchedulerMenuItemId)">
            <summary>
                <para>Gets a popup menu (menu item which contains a submenu) by its ID value.

</para>
            </summary>
            <param name="id">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerMenuItemId"/> enumeration value which specifies the ID value of the popup menu to search.

            </param>
            <returns>A <see cref="T:DevExpress.XtraScheduler.SchedulerPopupMenu"/> object whose ID is equal to the specified ID value. If a popup menu item with the specified ID isn't found, then the <b>null</b> (<b>Nothing</b> in Visual Basic) value will be returned.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerPopupMenu.GetPopupMenuById(DevExpress.XtraScheduler.SchedulerMenuItemId,System.Boolean)">
            <summary>
                <para>Gets a popup menu (menu item which contains a submenu) by its ID value. Also optionally recursively searches for this popup menu in all submenus, if it isn't found in the main popup menu.


</para>
            </summary>
            <param name="id">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerMenuItemId"/> enumeration value which specifies the ID value of the popup menu to search.

            </param>
            <param name="recursive">
		<b>true</b> to recursively search for the popup menu in all submenus; otherwise, <b>false</b>.


            </param>
            <returns>A <see cref="T:DevExpress.XtraScheduler.SchedulerPopupMenu"/> object whose ID is equal to the specified ID value. If a popup menu item with the specified ID isn't found, then the <b>null</b> (<b>Nothing</b> in Visual Basic) value will be returned.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerPopupMenu.GetPopupMenuById(DevExpress.Utils.Menu.DXPopupMenu,DevExpress.XtraScheduler.SchedulerMenuItemId,System.Boolean)">
            <summary>
                <para>Gets a popup menu (a menu item which contains a submenu) by its ID value within the specified popup menu. Also optionally recursively searches for this popup menu in all submenus, if it isn't found in the main popup menu.

</para>
            </summary>
            <param name="menu">
		A <see cref="T:DevExpress.Utils.Menu.DXPopupMenu"/> object which represents the popup menu to search in.

            </param>
            <param name="id">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerMenuItemId"/> enumeration value which specifies the ID value of the popup menu to search.

            </param>
            <param name="recursive">
		<b>true</b> to recursively search for the popup menu in all submenus; otherwise, <b>false</b>.


            </param>
            <returns>A <see cref="T:DevExpress.XtraScheduler.SchedulerPopupMenu"/> object whose ID is equal to the specified ID value. If a popup menu item with the specified ID isn't found, then the <b>null</b> (<b>Nothing</b> in Visual Basic) value will be returned.
</returns>


        </member>
        <member name="T:DevExpress.XtraScheduler.SchedulerMenuItem">

            <summary>
                <para>Represents an individual item that is displayed within a <b>Scheduler</b>'s popup (context) menu.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerMenuItem.#ctor(System.String,System.EventHandler)">
            <summary>
                <para>Initializes a new instance of the SchedulerMenuItem class with the specified caption and click event handler.

</para>
            </summary>
            <param name="caption">
		A <see cref="T:System.String"/> value that specifies the caption of the menu item. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Caption"/> property.

            </param>
            <param name="click">
		A <see cref="T:System.EventHandler"/> object that specifies the event handler for the <see cref="E:DevExpress.Utils.Menu.DXMenuItem.Click"/> event.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerMenuItem.#ctor(System.String)">
            <summary>
                <para>Initializes a new instance of the SchedulerMenuItem class with the specified caption.
</para>
            </summary>
            <param name="caption">
		A <see cref="T:System.String"/> value that specifies the caption of the menu item. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Caption"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerMenuItem.#ctor(System.String,System.EventHandler,System.Drawing.Image,System.EventHandler)">
            <summary>
                <para>Initializes a new instance of the SchedulerMenuItem class with the specified caption, image, and event handlers for clicking and updating.
</para>
            </summary>
            <param name="caption">
		A <see cref="T:System.String"/> value that specifies the caption of the menu item. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Caption"/> property.

            </param>
            <param name="click">
		A <see cref="T:System.EventHandler"/> object that specifies the event handler for the <see cref="E:DevExpress.Utils.Menu.DXMenuItem.Click"/> event.

            </param>
            <param name="image">
		A <see cref="T:System.Drawing.Image"/> value that specifies the image of the menu item. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Image"/> property.

            </param>
            <param name="update">
		A <see cref="T:System.EventHandler"/> object that specifies the event handler for the<b> DevExpress.Utils.Menu.CommandMenuItem@lt;T@gt;.Update</b> event.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerMenuItem.#ctor(System.String,System.EventHandler,System.Drawing.Image)">
            <summary>
                <para>Initializes a new instance of the SchedulerMenuItem class with the specified caption, image, and click event handler.

</para>
            </summary>
            <param name="caption">
		A <see cref="T:System.String"/> value that specifies the caption of the menu item. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Caption"/> property.

            </param>
            <param name="click">
		A <see cref="T:System.EventHandler"/> object that specifies the event handler for the <see cref="E:DevExpress.Utils.Menu.DXMenuItem.Click"/> event.

            </param>
            <param name="image">
		A <see cref="T:System.Drawing.Image"/> value that specifies the image of the menu item. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Image"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerMenuItem.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerMenuItem class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraScheduler.SchedulerMenuCheckItem">

            <summary>
                <para>Represents an individual check item that is displayed within a <b>Scheduler</b>'s popup (context) menu.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerMenuCheckItem.#ctor(System.String,System.Boolean,System.Drawing.Image,System.EventHandler)">
            <summary>
                <para>Initializes a new instance of the SchedulerMenuCheckItem class with the specified caption, check state, image, and an event handler for check state changing.
</para>
            </summary>
            <param name="caption">
		A <see cref="T:System.String"/> value that specifies the caption of the menu item. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Caption"/> property.

            </param>
            <param name="check">
		<b>true</b> if the menu item is checked; <b>otherwise</b>, false. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuCheckItem.Checked"/> property.


            </param>
            <param name="image">
		A <see cref="T:System.Drawing.Image"/> value that specifies the image of the menu item. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Image"/> property.

            </param>
            <param name="checkedChanged">
		A <see cref="T:System.EventHandler"/> object that specifies the event handler for the <see cref="E:DevExpress.Utils.Menu.DXMenuCheckItem.CheckedChanged"/> event.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerMenuCheckItem.#ctor(System.String)">
            <summary>
                <para>Initializes a new instance of the SchedulerMenuCheckItem class with the specified caption.
</para>
            </summary>
            <param name="caption">
		A <see cref="T:System.String"/> value that specifies the caption of the menu item. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Caption"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerMenuCheckItem.#ctor(System.String,System.Boolean)">
            <summary>
                <para>Initializes a new instance of the SchedulerMenuCheckItem class with the specified caption and check state.
</para>
            </summary>
            <param name="caption">
		A <see cref="T:System.String"/> value that specifies the caption of the menu item. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Caption"/> property.

            </param>
            <param name="check">
		<b>true</b> if the menu item is checked; <b>otherwise</b>, false. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuCheckItem.Checked"/> property.


            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerMenuCheckItem.#ctor(System.String,System.Boolean,System.EventHandler)">
            <summary>
                <para>Initializes a new instance of the SchedulerMenuCheckItem class with the specified caption, check state, and the event handler for updating.
</para>
            </summary>
            <param name="caption">
		A <see cref="T:System.String"/> value that specifies the caption of the menu item. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Caption"/> property.

            </param>
            <param name="check">
		<b>true</b> if the menu item is checked; <b>otherwise</b>, false. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuCheckItem.Checked"/> property.


            </param>
            <param name="update">
		A <see cref="T:System.EventHandler"/> object that specifies the event handler for the <b>DevExpress.Utils.Menu.CommandMenuCheckItem@lt;T@gt;.Update</b> event.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerMenuCheckItem.#ctor(System.String,System.Boolean,System.Drawing.Image,System.EventHandler,System.EventHandler)">
            <summary>
                <para>Initializes a new instance of the SchedulerMenuCheckItem class with the specified caption, check state, image, and event handlers for check state changing and updating.
</para>
            </summary>
            <param name="caption">
		A <see cref="T:System.String"/> value that specifies the caption of the menu item. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Caption"/> property.

            </param>
            <param name="check">
		<b>true</b> if the menu item is checked; <b>otherwise</b>, false. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuCheckItem.Checked"/> property.


            </param>
            <param name="image">
		A <see cref="T:System.Drawing.Image"/> value that specifies the image of the menu item. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Image"/> property.

            </param>
            <param name="checkedChanged">
		A <see cref="T:System.EventHandler"/> object that specifies the event handler for the <see cref="E:DevExpress.Utils.Menu.DXMenuCheckItem.CheckedChanged"/> event.


            </param>
            <param name="update">
		A <see cref="T:System.EventHandler"/> object that specifies the event handler for the <b>DevExpress.Utils.Menu.CommandMenuCheckItem@lt;T@gt;.Update</b> event.


            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerMenuCheckItem.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerMenuCheckItem class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraScheduler.SchedulerAppearance">

            <summary>
                <para>Provides the appearance settings used to paint a scheduler control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerAppearance.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerAppearance class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraScheduler.DayViewAppearance">

            <summary>
                <para>Provides the appearance settings used to paint a Day View.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.DayViewAppearance.#ctor">
            <summary>
                <para>Initializes a new instance of the DayViewAppearance class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayViewAppearance.AllDayArea">
            <summary>
                <para>Gets the appearance settings used to paint an all-day area within the Day View. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint an all-day area. 

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayViewAppearance.AllDayAreaSeparator">
            <summary>
                <para>Gets the appearance settings used to paint the all-day area's separator. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint the all-day area's separator. 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayViewAppearance.SelectedAllDayArea">
            <summary>
                <para>Gets the appearance settings used to paint the all-day area being selected within the current Day View. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint the selected all-day area. 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayViewAppearance.TimeRuler">
            <summary>
                <para>Gets the appearance settings used to paint the time ruler within the Day View. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint the time ruler. 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayViewAppearance.TimeRulerHourLine">
            <summary>
                <para>Gets the appearance settings used to paint the hour lines displayed within the Day View's time ruler. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint the time ruler's hour lines. 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayViewAppearance.TimeRulerLine">
            <summary>
                <para>Gets the appearance settings used to paint the lines displayed between hours within the Day View's time ruler. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint the time ruler's lines. 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayViewAppearance.TimeRulerNowArea">
            <summary>
                <para>Gets the appearance settings used to paint the area that represents the current time within the Day View's time ruler. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint the time ruler's now area. 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayViewAppearance.TimeRulerNowLine">
            <summary>
                <para>Gets the appearance settings used to paint the now area's line that points to the current time within the Day View's time ruler. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint the time ruler's now line. 
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.BaseViewAppearance">

            <summary>
                <para>Serves as the base class for classes that provide the appearance settings used to paint view elements.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.BaseViewAppearance.#ctor">
            <summary>
                <para>Initializes a new instance of the BaseViewAppearance class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.BaseViewAppearance.Appointment">
            <summary>
                <para>Gets the appearance settings used to paint an appointment within the View. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint an appointment. 

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.BaseViewAppearance.NavigationButton">
            <summary>
                <para>Provides access to an object that specifies the appearance settings for the Navigation button.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> class instance.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.BaseViewAppearance.NavigationButtonDisabled">
            <summary>
                <para>Provides access to an object that specifies the appearance settings for the disabled Navigation button.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> class instance.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.BaseViewAppearance.ResourceHeaderCaption">
            <summary>
                <para>Gets the appearance settings used to paint resource headers.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint resource headers.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.BaseViewAppearance.ResourceHeaderCaptionLine">
            <summary>
                <para>Gets the appearance settings used to paint the horizontal line under the resource header.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the horizontal line under the resource header.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.BaseHeaderAppearance">

            <summary>
                <para>Serves as the base class for classes that provide the appearance settings for the scheduler's elements.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.BaseHeaderAppearance.#ctor">
            <summary>
                <para>Initializes a new instance of the BaseHeaderAppearance class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.BaseHeaderAppearance.AlternateHeaderCaption">
            <summary>
                <para>Gets the appearance settings used to paint the alternate header.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the alternate header.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.BaseHeaderAppearance.AlternateHeaderCaptionLine">
            <summary>
                <para>Gets the appearance settings used to paint the horizontal line under the alternate header.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the horizontal line under the alternate header.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.BaseHeaderAppearance.HeaderCaption">
            <summary>
                <para>Gets the appearance settings used to paint headers.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint headers.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.BaseHeaderAppearance.HeaderCaptionLine">
            <summary>
                <para>Gets the appearance settings used to paint the horizontal line under the header.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the horizontal line under the header.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.BaseHeaderAppearance.Selection">
            <summary>
                <para>Gets the appearance settings used to paint a selection within the View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint a selection. 
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.SchedulerOptionsBehavior">

            <summary>
                <para>Provides behavior options for the Scheduler control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerOptionsBehavior.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraScheduler.SchedulerOptionsBehavior"/> class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraScheduler.PrepareContextMenuEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.PrepareContextMenu"/> event. 
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.PrepareContextMenuEventHandler.Invoke(System.Object,DevExpress.XtraScheduler.PrepareContextMenuEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.PrepareContextMenu"/> event. 
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> which raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraScheduler.PrepareContextMenuEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraScheduler.PrepareContextMenuEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.PrepareContextMenu"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.PrepareContextMenuEventArgs.#ctor(DevExpress.XtraScheduler.SchedulerPopupMenu)">
            <summary>
                <para>Initializes a new instance of the PrepareContextMenuEventArgs class with the specified settings.
</para>
            </summary>
            <param name="menu">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerPopupMenu"/> value which represents the event's popup menu.

            </param>


        </member>
        <member name="T:DevExpress.XtraScheduler.AppointmentStatusCollection">

            <summary>
                <para>Represents a collection of appointment statuses.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.AppointmentStatusCollection.#ctor">
            <summary>
                <para>Initializes a new instance of the AppointmentStatusCollection class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.AppointmentStatusCollection.Item(DevExpress.XtraScheduler.AppointmentStatusType)">
            <summary>
                <para>Gets the <see cref="T:DevExpress.XtraScheduler.AppointmentStatus"/> object specified by the appointment status type.
</para>
            </summary>
            <param name="type">
		An <see cref="T:DevExpress.XtraScheduler.AppointmentStatusType"/> enumeration value specifying the type of the required <see cref="T:DevExpress.XtraScheduler.AppointmentStatus"/> object.

            </param>
            <value>An <see cref="T:DevExpress.XtraScheduler.AppointmentStatus"/> object which represents the appointment status of the specified type.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.AppointmentStatusCollection.Item(System.Int32)">
            <summary>
                <para>Provides indexed access to individual items in the collection.
</para>
            </summary>
            <param name="index">
		A zero-based integer specifying the desired item's position within the collection. If it's negative or exceeds the last available index, an exception is raised. 

            </param>
            <value>An <see cref="T:DevExpress.XtraScheduler.AppointmentStatus"/> object which represents the appointment status at the specified position.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.AppointmentStatus">

            <summary>
                <para>Represents an appointment's availability status.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.AppointmentStatus.#ctor(DevExpress.XtraScheduler.AppointmentStatusType,System.Drawing.Color,System.String,System.String)">
            <summary>
                <para>Initializes a new instance of the AppointmentStatus class with the specified type, color, display name and menu caption.
</para>
            </summary>
            <param name="type">
		An <see cref="T:DevExpress.XtraScheduler.AppointmentStatusType"/> enumeration value that specifies the type of the appointment status. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.AppointmentStatusBase.Type"/> property.

            </param>
            <param name="color">
		A <see cref="T:System.Drawing.Color"/> value that specifies the color of the appointment status. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.Color"/> property.

            </param>
            <param name="displayName">
		A <see cref="T:System.String"/> value which represents the display name of the appointment status. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.DisplayName"/> property.


            </param>
            <param name="menuCaption">
		A <see cref="T:System.String"/> value which represents the menu caption of the appointment status. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.MenuCaption"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.AppointmentStatus.#ctor">
            <summary>
                <para>Initializes a new instance of the AppointmentStatus class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.AppointmentStatus.#ctor(DevExpress.XtraScheduler.AppointmentStatusType,System.Drawing.Color,System.String)">
            <summary>
                <para>Initializes a new instance of the AppointmentStatus class with the specified type, color and display name.
</para>
            </summary>
            <param name="type">
		An <see cref="T:DevExpress.XtraScheduler.AppointmentStatusType"/> enumeration value that specifies the type of the appointment status. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.AppointmentStatusBase.Type"/> property.

            </param>
            <param name="color">
		A <see cref="T:System.Drawing.Color"/> value that specifies the color of the appointment status. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.Color"/> property.

            </param>
            <param name="displayName">
		A <see cref="T:System.String"/> value which represents the display name of the appointment status. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.DisplayName"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.AppointmentStatus.#ctor(DevExpress.XtraScheduler.AppointmentStatusType,System.String)">
            <summary>
                <para>Initializes a new instance of the AppointmentStatus class with the specified type and display name.
</para>
            </summary>
            <param name="type">
		An <see cref="T:DevExpress.XtraScheduler.AppointmentStatusType"/> enumeration value that specifies the type of the appointment status. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.AppointmentStatusBase.Type"/> property.

            </param>
            <param name="displayName">
		A <see cref="T:System.String"/> value which represents the display name of the appointment status. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.DisplayName"/> property.


            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.AppointmentStatus.#ctor(DevExpress.XtraScheduler.AppointmentStatusType,System.String,System.String)">
            <summary>
                <para>Initializes a new instance of the AppointmentStatus class with the specified type, display name and menu caption.
</para>
            </summary>
            <param name="type">
		An <see cref="T:DevExpress.XtraScheduler.AppointmentStatusType"/> enumeration value that specifies the type of the appointment status. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.AppointmentStatusBase.Type"/> property.

            </param>
            <param name="displayName">
		A <see cref="T:System.String"/> value which represents the display name of the appointment status. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.DisplayName"/> property.


            </param>
            <param name="menuCaption">
		A <see cref="T:System.String"/> value which represents the menu caption of the appointment status. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.MenuCaption"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.AppointmentStatus.CreateInstance(DevExpress.XtraScheduler.AppointmentStatusType)">
            <summary>
                <para>Creates a new instance of the AppointmentStatus class and initializes it with the specified type.

</para>
            </summary>
            <param name="type">
		An <see cref="T:DevExpress.XtraScheduler.AppointmentStatusType"/> enumeration value that specifies the type of the appointment status. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.AppointmentStatusBase.Type"/> property.

            </param>
            <returns>An AppointmentStatus object of the specified type.
</returns>


        </member>
        <member name="T:DevExpress.XtraScheduler.SchedulerStorage">

            <summary>
                <para>Represents a storage which holds data for the XtraScheduler control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerStorage.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerStorage class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerStorage.#ctor(System.ComponentModel.IContainer)">
            <summary>
                <para>Initializes a new instance of the SchedulerStorage class with the specified container.
</para>
            </summary>
            <param name="components">
		A <see cref="T:System.ComponentModel.IContainer"/> that represents the container for the default <b>Scheduler storage</b>.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerStorage.AppointmentDependencies">
            <summary>
                <para>Gets a storage object which manages dependencies between appointments.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.AppointmentDependencyStorage"/> object that manages information on appointment dependencies for the scheduler.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerStorage.Appointments">
            <summary>
                <para>Gets a storage object that contains appointment related information.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.AppointmentStorage"/> object that represents the storage for appointment related information.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerStorage.GetFilteredComponents">
            <summary>
                <para>Gets a collection of filtered appointments and resources.
</para>
            </summary>
            <returns>A collection that supports the <see cref="T:System.Collections.ICollection"/> interface, containing filtered objects.
</returns>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerStorage.PrepareAppointmentFilterColumn">
            <summary>
                <para>Fires when a column, representing an appointment's field, is added to the collection of filter columns contained within the <see cref="T:DevExpress.XtraEditors.FilterControl"/>.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerStorage.PrepareResourceFilterColumn">
            <summary>
                <para>Fires when a column, representing a resource's field, is added to the collection of filter columns contained within the <see cref="T:DevExpress.XtraEditors.FilterControl"/>.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerStorage.Resources">
            <summary>
                <para>Gets the object which manages resources for appointments.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.ResourceStorage"/> object which manages resources for appointments.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerStorage.SetAppointmentId(DevExpress.XtraScheduler.Appointment,System.Object)">
            <summary>
                <para>Sets the Id property of the specified appointment to a specified value.
</para>
            </summary>
            <param name="apt">
		An <see cref="T:DevExpress.XtraScheduler.Appointment"/> object.

            </param>
            <param name="id">
		An object that is the appointment identifier to assign.

            </param>


        </member>
        <member name="T:DevExpress.XtraScheduler.ResourceStorage">

            <summary>
                <para>Represents a storage which holds appointment resources.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.ResourceStorage.#ctor(DevExpress.XtraScheduler.SchedulerStorage)">
            <summary>
                <para>Initializes a new instance of the ResourceStorage class with the specified scheduler storage.
</para>
            </summary>
            <param name="storage">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerStorage"/> value that specifies the scheduler storage of the resource storage.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.ResourceStorage.CustomFieldMappings">
            <summary>
                <para>Provides access to the collection of objects, representing mappings of the resources' custom properties to appropriate data fields. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.ResourceCustomFieldMappingCollection"/> object.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.ResourceStorage.Filter">
            <summary>
                <para>Gets or set criteria to filter resources in the storage.
</para>
            </summary>
            <value>A string containing a logical expression that is the filter criteria.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.ResourceStorage.Mappings">
            <summary>
                <para>Gets an object that allows the persistent properties of the resources maintained by the current storage to be bound to appropriate fields in the data source.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.ResourceMappingInfo"/> object that provides functionality for mapping the properties of the resources to appropriate data fields.

</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.WeekView">

            <summary>
                <para>Represents a Week View.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.WeekView.#ctor(DevExpress.XtraScheduler.SchedulerControl)">
            <summary>
                <para>Initializes a new instance of the WeekView class with the specified <b>Scheduler</b> control.
</para>
            </summary>
            <param name="control">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> value that specifies the <b>Scheduler</b> control assigned to the week view. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.SchedulerViewBase.Control"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.WeekView.Appearance">
            <summary>
                <para>Provides access to the properties that control the appearance of the WeekView's elements.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.WeekViewAppearance"/> object that provides the appearance settings for WeekView elements.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.WeekView.AppointmentDisplayOptions">
            <summary>
                <para>Provides access to the appointment's display options pertaining to the Week View.
</para>
            </summary>
            <value>A <see cref="P:DevExpress.XtraScheduler.WeekView.AppointmentDisplayOptions"/> object containing options for displaying appointments. 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.WeekView.DeferredScrolling">
            <summary>
                <para>Provides access to parameters that control deferred scrolling.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerDeferredScrollingOption"/> instance that specifies parameters for deferred scrolling.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.WeekView.MenuItemId">
            <summary>
                <para>Gets the ID of the menu item which corresponds to the <b>Week View</b>.
</para>
            </summary>
            <value>The <see cref="F:DevExpress.XtraScheduler.SchedulerMenuItemId.SwitchToWeekView"/> value.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.WeekView.ShowBordersForSameDayAppointments">
            <summary>
                <para>Gets or sets a value which specifies if left and right borders are shown for same day appointments in the <b>Week</b> view.


</para>
            </summary>
            <value><b>true</b> to show left and right borders for same day appointments; otherwise, <b>false</b>.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.WeekView.ShowEndTime">
            <summary>
                <para>Gets or sets a value specifying whether the end time of appointments should be shown.

</para>
            </summary>
            <value><b>true</b> to show the end time of appointments; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.WeekView.ShowStartTime">
            <summary>
                <para>Gets or sets a value which specifies whether the start time of appointments should be shown.

</para>
            </summary>
            <value><b>true</b> to show the start time of appointments; otherwise, <b>false</b>.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.WeekView.ShowTimeAsClock">
            <summary>
                <para>Gets or sets a value specifying whether the start and end time of appointments are shown using clocks.

</para>
            </summary>
            <value><b>true</b> to show the time as clock images; otherwise, <b>false</b>.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.WeekView.Type">
            <summary>
                <para>Gets the view's type.
</para>
            </summary>
            <value>The <see cref="F:DevExpress.XtraScheduler.SchedulerViewType.Week"/> value.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.WeekView.ViewInfo">
            <summary>
                <para>Gets the current object's view information.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Drawing.WeekViewInfo"/> object providing view information on all the WeekView's elements.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.SchedulerViewBase">

            <summary>
                <para>Represents the scheduling area where various time Views are shown.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerViewBase.AddAppointmentSelection(DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Selects the specified appointment.

</para>
            </summary>
            <param name="apt">
		An <see cref="T:DevExpress.XtraScheduler.Appointment"/> object that specifies the appointment to be selected.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewBase.Appearance">
            <summary>
                <para>Provides access to the properties that control the appearance of the SchedulerViewBase's elements.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.BaseViewAppearance"/> object that provides the appearance settings for SchedulerViewBase elements.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewBase.AppointmentDisplayOptions">
            <summary>
                <para>Provides access to the appointment's display options.

</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.AppointmentDisplayOptions"/> object containing options for displaying appointments.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewBase.AppointmentHeight">
            <summary>
                <para>Gets or sets the height of a single appointment for the current View (in pixels).
</para>
            </summary>
            <value>An integer value which represents the appointment height measured in pixels.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerViewBase.BeginUpdate">
            <summary>
                <para>Locks the SchedulerViewBase object by preventing visual updates until the <b>EndUpdate</b> method is called.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewBase.Bounds">
            <summary>
                <para>Gets or sets the bounds of the current View.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Rectangle"/> structure that specifies the View's boundaries.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerViewBase.ChangeAppointmentSelection(DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Makes the specified appointment the only selected appointment.

</para>
            </summary>
            <param name="apt">
		An <see cref="T:DevExpress.XtraScheduler.Appointment"/> object that specifies the appointment to be selected.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewBase.Control">
            <summary>
                <para>Gets the scheduler control which the current View belongs to.
</para>
            </summary>
            <value>The <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> object which the View belongs to.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewBase.DateTimeScrollbarVisible">
            <summary>
                <para>Gets or sets a value indicating whether the date-time scrollbar is visible.
</para>
            </summary>
            <value><b>true</b> if the date-time scrollbar is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewBase.DisplayName">
            <summary>
                <para>Gets or sets the string to display to indicate the scheduler's view.
</para>
            </summary>
            <value>A string, specifying the view's name. The default is the view's name with the word "Calendar" appended.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerViewBase.Dispose">
            <summary>
                <para>Disposes of the SchedulerViewBase object.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerViewBase.Draw(DevExpress.Utils.Drawing.GraphicsInfoArgs)">
            <summary>
                <para>Draws the <b>Scheduler</b>'s data according to the current view.

</para>
            </summary>
            <param name="args">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsInfoArgs"/> object representing the graphics information to draw.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewBase.Enabled">
            <summary>
                <para>Gets or sets if the view is enabled for the <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/>.

</para>
            </summary>
            <value><b>true</b> if the View is enabled; otherwise, <b>false</b>.

</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerViewBase.EndUpdate">
            <summary>
                <para>Unlocks the SchedulerViewBase object after a call to the <b>BeginUpdate</b> method and causes an immediate visual update.


</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewBase.FirstVisibleResourceIndex">
            <summary>
                <para>Gets or sets the index of a resource which is displayed first within the current scheduler view.
</para>
            </summary>
            <value>An integer value which represents the zero-based index of the first visible resource.

</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerViewBase.GetAppointments">
            <summary>
                <para>Gets the collection of appointments displayed in the current Scheduler view.

</para>
            </summary>
            <returns>An <see cref="T:DevExpress.XtraScheduler.AppointmentBaseCollection"/>  object, representing an appointments' collection.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerViewBase.GetFilteredResources">
            <summary>
                <para>Obtains a collection of filtered resources from the Storage.
</para>
            </summary>
            <returns>An <see cref="T:DevExpress.XtraScheduler.ResourceBaseCollection"/>  object, that is a collection of filtered resources.

</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerViewBase.GetResources">
            <summary>
                <para>Gets a collection of visible resources for the current Scheduler view.

</para>
            </summary>
            <returns>An <see cref="T:DevExpress.XtraScheduler.ResourceBaseCollection"/>  object, representing a collection of visible resources.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerViewBase.GetVisibleIntervals">
            <summary>
                <para>Returns a copy of the visible time interval collection for the current view.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraScheduler.TimeIntervalCollection"/> object containing the information on visible intervals for the current view.

</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerViewBase.GotoTimeInterval(DevExpress.XtraScheduler.TimeInterval)">
            <summary>
                <para>Selects the specified time interval and scrolls the View to it if it's not currently visible.
</para>
            </summary>
            <param name="interval">
		A <see cref="T:DevExpress.XtraScheduler.TimeInterval"/> object that specifies the required time interval.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewBase.GroupSeparatorWidth">
            <summary>
                <para>Gets or sets the width of a separator bar between groups (measured in pixels).

</para>
            </summary>
            <value>An integer value which represents the width of a group separator in pixels.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewBase.GroupType">
            <summary>
                <para>Gets or sets a value that specifies the type of grouping applied to the View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerGroupType"/> enumeration value that specifies how appointments are grouped within the View.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerViewBase.Invalidate">
            <summary>
                <para>Invalidates the region occupied by the current View (adds it to the control's update region which will be repainted during the next paint operation), and causes a paint message to be sent to the scheduler control.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewBase.IsUpdateLocked">
            <summary>
                <para>Gets whether the scheduler's view is locked while it is updated.

</para>
            </summary>
            <value><b>true</b> if the scheduler's view is locked; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerViewBase.LayoutChanged">
            <summary>
                <para>Updates the View and forces the scheduler control to mirror any changes made to the View's layout.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewBase.LayoutLocked">
            <summary>
                <para>Gets a value that indicates whether the layout logic of the View is suspended by a call to the <see cref="M:DevExpress.XtraScheduler.SchedulerViewBase.SuspendLayout"/> method.
</para>
            </summary>
            <value><b>true</b> if the normal layout logic of the View is suspended; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewBase.MenuCaption">
            <summary>
                <para>Gets or sets the menu caption string to indicate the View.

</para>
            </summary>
            <value>A string, representing the menu caption for a scheduler's view.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewBase.MenuItemId">
            <summary>
                <para>Gets the ID of the menu item which corresponds to the current View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerMenuItemId"/> enumeration value.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewBase.NavigationButtonAppointmentSearchInterval">
            <summary>
                <para>Specifies the time span used to search for appointments by Navigation Buttons.
</para>
            </summary>
            <value>A <see cref="T:System.TimeSpan"/> value, reprsenting the searched time interval.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewBase.NavigationButtonVisibility">
            <summary>
                <para>Gets or sets the condition for display of the Navigation Buttons.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.NavigationButtonVisibility"/> enumeration value which specifies when the Navigation Buttons are visible.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewBase.Painter">
            <summary>
                <para>Gets an object that provides the painting functionality of the scheduler control's Views.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Drawing.ViewPainterBase"/> object implementing the View's base painting functionality.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewBase.ResourcesPerPage">
            <summary>
                <para>Gets or sets the number of resources shown at a time on a screen.

</para>
            </summary>
            <value>An integer value which represents the number of resources.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerViewBase.ResumeLayout">
            <summary>
                <para>Resumes normal layout logic for the View.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerViewBase.ReverseAppointmentSelection(DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Switches the selection status of the specified appointment.

</para>
            </summary>
            <param name="apt">
		An <see cref="T:DevExpress.XtraScheduler.Appointment"/> object that specifies the required appointment.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerViewBase.SelectAppointment(DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Makes the specified appointment the only selected appointment within the View and scrolls to it.

</para>
            </summary>
            <param name="apt">
		An <see cref="T:DevExpress.XtraScheduler.Appointment"/> object that specifies the appointment to be selected.


            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerViewBase.SelectAppointment(DevExpress.XtraScheduler.Appointment,DevExpress.XtraScheduler.Resource)">
            <summary>
                <para>Makes the specified appointment on the specified resource the only selected appointment within the View and scrolls to it.

</para>
            </summary>
            <param name="apt">
		An <see cref="T:DevExpress.XtraScheduler.Appointment"/> object that specifies the appointment to be selected.

            </param>
            <param name="resource">
		A <see cref="T:DevExpress.XtraScheduler.Resource"/> object that specifies the resource which contains an appointment to be selected.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewBase.SelectedInterval">
            <summary>
                <para>Gets the time interval currently selected in the scheduler's active view by an end-user.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeInterval"/> object representing the selected time interval.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewBase.SelectedResource">
            <summary>
                <para>Gets the resource which contains the time interval currently selected in the scheduler's active view by an end-user.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Resource"/> object which represents the selected resource.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerViewBase.SetSelection(DevExpress.XtraScheduler.TimeInterval,DevExpress.XtraScheduler.Resource)">
            <summary>
                <para>Makes a specific time interval selected within the View.
</para>
            </summary>
            <param name="interval">
		A <see cref="T:DevExpress.XtraScheduler.TimeInterval"/> object that specifies the time interval to be selected.

            </param>
            <param name="resource">
		A <see cref="T:DevExpress.XtraScheduler.Resource"/> object that specifies which resource the specified time interval belong to.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerViewBase.SetVisibleIntervals(DevExpress.XtraScheduler.TimeIntervalCollection)">
            <summary>
                <para>Fills the visible time interval collection with new items.
</para>
            </summary>
            <param name="intervals">
		A <see cref="T:DevExpress.XtraScheduler.TimeIntervalCollection"/> object representing a collection of the <b>SchedulerViewBase</b> visible intervals.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewBase.ShortDisplayName">
            <summary>
                <para>Gets or sets a short name of the current view.

</para>
            </summary>
            <value>A <see cref="T:System.String"/> value which represents the view's short name.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewBase.ShowMoreButtons">
            <summary>
                <para>Gets or sets a value which specifies if the More buttons should be shown in the current View.


</para>
            </summary>
            <value><b>true</b> if the <b>'More'</b> buttons should be shown; otherwise, <b>false</b>.

</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerViewBase.SuspendLayout">
            <summary>
                <para>Temporarily suspends the layout logic for the View.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewBase.Type">
            <summary>
                <para>Gets the View's type.
</para>
            </summary>
            <value>One of the <see cref="T:DevExpress.XtraScheduler.SchedulerViewType"/> enumeration's values that specifies the View's type.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewBase.ViewInfo">
            <summary>
                <para>Gets the current object's view information.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerViewInfoBase"/> object providing view information on all the SchedulerViewBase's elements.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerViewBase.VisibleIntervals">
            <summary>
                <para>Gets the collection of visible intervals (for instance, days or weeks) displayed by the View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeIntervalCollection"/> object that represents the collection of time intervals which are displayed within the View.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerViewBase.ZoomIn">
            <summary>
                <para>Performs scaling up to display content in more detail.


</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerViewBase.ZoomOut">
            <summary>
                <para>Performs scaling down to display a broader look of the View.

</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraScheduler.SchedulerOptionsView">

            <summary>
                <para>Provides view options for the Scheduler control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerOptionsView.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerOptionsView class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerOptionsView.EnableAnimation">
            <summary>
                <para>Gets or sets whether a specific animation effect should be applied when an end-user navigates though dates and scrolls resources.
</para>
            </summary>
            <value><b>true</b> if an animation effect is applied; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerOptionsView.HideSelection">
            <summary>
                <para>Gets or sets a value that specifies whether the selected time slot remains highlighted when the scheduler control loses focus.

</para>
            </summary>
            <value><b>true</b> to hide the highlighting of the selected time slot when the scheduler loses focus; <b>false</b> to keep the selected time slot highlighted.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerOptionsView.ResourceHeaders">
            <summary>
                <para>Gets the object which combines appearance options for a scheduler's resource headers.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerResourceHeaderOptions"/> object that represents the resource headers options.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerOptionsView.ToolTipVisibility">
            <summary>
                <para>Gets or sets the visibility of the scheduler's tooltips.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.ToolTipVisibility"/> enumeration value which specifies the visibility of the tool tips.

</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.MonthView">

            <summary>
                <para>Represents a Month (Multi-Week) View.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.MonthView.#ctor(DevExpress.XtraScheduler.SchedulerControl)">
            <summary>
                <para>Initializes a new instance of the MonthView class with the specified <b>Scheduler</b> control.
</para>
            </summary>
            <param name="control">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> value that specifies the <b>Scheduler</b> control assigned to the month view. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.SchedulerViewBase.Control"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.MonthView.Appearance">
            <summary>
                <para>Provides access to the properties that control the appearance of the MonthView's elements.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.MonthViewAppearance"/> object that provides the appearance settings for MonthView elements.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.MonthView.AppointmentDisplayOptions">
            <summary>
                <para>Provides access to the appointment's display options pertaining to the Month View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.MonthViewAppointmentDisplayOptions"/> object containing options for displaying appointments. 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.MonthView.CompressWeekend">
            <summary>
                <para>Gets or sets a value indicating if the weekend days (<b>Saturday</b> and <b>Sunday</b>) should be displayed as one day.


</para>
            </summary>
            <value><b>true</b> to compress weekends; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.MonthView.MenuItemId">
            <summary>
                <para>Gets the ID of the menu item which corresponds to the <b>Month View</b>.
</para>
            </summary>
            <value>The <see cref="F:DevExpress.XtraScheduler.SchedulerMenuItemId.SwitchToMonthView"/> value.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.MonthView.ShowWeekend">
            <summary>
                <para>Gets or sets a value indicating if the scheduler should also show its data for the weekend days (<b>Saturday</b> and <b>Sunday</b>) in a <b>Month View</b>.
</para>
            </summary>
            <value><b>true</b> to show data for weekend days; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.MonthView.Type">
            <summary>
                <para>Gets the view's type.
</para>
            </summary>
            <value>The <see cref="F:DevExpress.XtraScheduler.SchedulerViewType.Month"/> value.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.MonthView.WeekCount">
            <summary>
                <para>Gets or sets the number of weeks that are simultaneously displayed within the Month View.
</para>
            </summary>
            <value>A positive integer value that specifies the number of weeks displayed by the View.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.DayView">

            <summary>
                <para>Represents a Day View.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.DayView.#ctor(DevExpress.XtraScheduler.SchedulerControl)">
            <summary>
                <para>Initializes a new instance of the DayView class with the specified <b>Scheduler</b> control.
</para>
            </summary>
            <param name="control">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> value that specifies the <b>Scheduler</b> control assigned to the day view. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.SchedulerViewBase.Control"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayView.AllDayAreaScrollBarVisible">
            <summary>
                <para>Specifies whether the scrolling of the All-Day Area is enabled, and the corresponding scrollbar is visible.
</para>
            </summary>
            <value><b>true</b> if the scrollbar for the all-day area is enabled and visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayView.Appearance">
            <summary>
                <para>Provides access to the properties that control the appearance of the DayView's elements.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.DayViewAppearance"/> object that provides the appearance settings for DayView elements.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayView.AppointmentDisplayOptions">
            <summary>
                <para>Provides access to the <b>Day View</b> appointments display options.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.AppointmentDisplayOptions"/> object containing settings to display the appointments in a Day View.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayView.AppointmentShadows">
            <summary>
                <para>Gets or sets a value indicating if shadows are shown for appointments.

</para>
            </summary>
            <value><b>true</b> to show shadows for appointment; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayView.DayCount">
            <summary>
                <para>Gets or sets the number of days that are simultaneously displayed within the Day View.

</para>
            </summary>
            <value>A positive integer value that specifies the number of days displayed by the View.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayView.MenuItemId">
            <summary>
                <para>Gets the ID of the menu item which corresponds to the <b>Day View</b>.
</para>
            </summary>
            <value>The <see cref="F:DevExpress.XtraScheduler.SchedulerMenuItemId.SwitchToDayView"/> value.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayView.RowHeight">
            <summary>
                <para>Gets or sets the height of time cells in the working area.
</para>
            </summary>
            <value>An integer, specifying the cell height, in pixels.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayView.ShowAllAppointmentsAtTimeCells">
            <summary>
                <para>Gets or sets whether all-day appointments should be shown at time cells rather than in a special area.

</para>
            </summary>
            <value><b>true</b> if all-day appointments are shown at time cells along with other appointments; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayView.ShowAllDayArea">
            <summary>
                <para>Gets or sets a value which specifies if the All-Day Area is shown when a <b>Scheduler</b> shows its data in the <b>Day</b> view.


</para>
            </summary>
            <value><b>true</b> to show the <b>All-Day</b> area; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayView.ShowDayHeaders">
            <summary>
                <para>Gets or sets a value which specifies if day headers are shown when a scheduler shows its data in the <b>Day</b> or the <b>Work-Week</b> views.

</para>
            </summary>
            <value><b>true</b> to show the day headers; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayView.ShowMoreButtonsOnEachColumn">
            <summary>
                <para>Gets or sets a value indicating whether to show the more buttons on each column or only on the Time Ruler in the Day View.


</para>
            </summary>
            <value><b>true</b> to show "more" buttons on each column; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayView.ShowWorkTimeOnly">
            <summary>
                <para>Gets or sets a value indicating if the scheduler should show its data only for the working hours in a <b>Day View</b>.


</para>
            </summary>
            <value><b>true</b> to show data for working hours only; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayView.StatusLineWidth">
            <summary>
                <para>Gets or sets the width of Status Lines in the Scheduler's <b>Day View</b> (measured in pixels).
</para>
            </summary>
            <value>An integer that specifies the status line width in pixels.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayView.TimeRulers">
            <summary>
                <para>Gets the View's collection of time rulers.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeRulerCollection"/> object that represents a time ruler collection.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayView.TimeScale">
            <summary>
                <para>Gets or sets the time interval for the time slots in the scheduling area.
</para>
            </summary>
            <value>A <see cref="T:System.TimeSpan"/> value representing the time interval for the time slots.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayView.TimeSlots">
            <summary>
                <para>Gets the View's collection of time slots.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeSlotCollection"/> object that represents a time slot collection.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayView.TopRowTime">
            <summary>
                <para>Gets or sets the time of the topmost row which is currently shown in the Day View.

</para>
            </summary>
            <value>A <see cref="T:System.TimeSpan"/> value which represents the time value for the top row.
</value>


        </member>
        <member name="E:DevExpress.XtraScheduler.DayView.TopRowTimeChanged">
            <summary>
                <para>Occurs after the <see cref="P:DevExpress.XtraScheduler.DayView.TopRowTime"/> property value was changed.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayView.Type">
            <summary>
                <para>Gets the view's type.
</para>
            </summary>
            <value>The <see cref="F:DevExpress.XtraScheduler.SchedulerViewType.Day"/> value.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayView.ViewInfo">
            <summary>
                <para>Gets the current object's view information.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Drawing.DayViewInfo"/> object providing view information on all the DayView's elements.
</value>


        </member>
        <member name="E:DevExpress.XtraScheduler.DayView.VisibleRowCountChanged">
            <summary>
                <para>Occurs after the number of visible rows is changed within a Day View.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayView.VisibleTime">
            <summary>
                <para>Gets or sets the time of the view's day interval.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeOfDayInterval"/> object which specifies the time of the view's day interval.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayView.VisibleTimeSnapMode">
            <summary>
                <para>Enables display of the specified start of the scheduler visible interval while a custom time ruler is applied in the Day or Work-Week view.

</para>
            </summary>
            <value><b>true</b> if the start of the scheduler visible interval should be shown as specified without stretching it to the time slot of the ruler; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.DayView.WorkTime">
            <summary>
                <para>Gets or sets the work time interval for a <b>Day View</b>.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeOfDayInterval"/> value representing the work time interval.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.SchedulerDeferredScrollingOption">

            <summary>
                <para>Provides access to an object that specifies how deferred scrolling is performed.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerDeferredScrollingOption.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerDeferredScrollingOption class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerDeferredScrollingOption.Allow">
            <summary>
                <para>Gets or sets whether the deferred scrolling feature is enabled.
</para>
            </summary>
            <value><b>true</b> to enable deferred scrolling; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.Drawing.TimeCell">

            <summary>
                <para>Represents an ordinary time cell in a scheduler's layout when it is drawn.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.TimeCell.#ctor">
            <summary>
                <para>Initializes a new instance of the TimeCell class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.TimeCell.EndOfHour">
            <summary>
                <para>Determines if the cell border is drawn with a darker color.
</para>
            </summary>
            <value> <b>true</b> to use the dark color; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.TimeCell.IsWorkTime">
            <summary>
                <para>Determines if the cell belongs to the working time range and should be colored accordingly.
</para>
            </summary>
            <value><b>true</b> if the cell belongs to the working time range; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.SchedulerControl">

            <summary>
                <para>A control that represents scheduled data in an appointment form.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerControl class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.#ctor(DevExpress.XtraScheduler.SchedulerStorage)">
            <summary>
                <para>Initializes a new instance of the SchedulerControl class with the specified scheduler storage.
</para>
            </summary>
            <param name="storage">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerStorage"/> value which represents the scheduler storage of the scheduler control. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.SchedulerControl.Storage"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.About">
            <summary>
                <para>Invokes the scheduler's <b>About</b> dialog box.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.ActivePrintStyle">
            <summary>
                <para>Gets or sets the print style currently used to print the scheduler's data.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Printing.SchedulerPrintStyle"/> object which represents the print style used to print the scheduler's data.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.ActiveView">
            <summary>
                <para>Gets the <b>View</b> currently used by the <b>Scheduler</b> to show its data.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerViewBase"/> object which is one of the views listed in the <see cref="T:DevExpress.XtraScheduler.SchedulerViewType"/> enumeration.

</value>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.ActiveViewChanged">
            <summary>
                <para>Occurs after the active view of the <b>Scheduler</b> control has been changed.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.ActiveViewChanging">
            <summary>
                <para>Occurs when the <b>Scheduler</b> control is changing its active view.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.ActiveViewType">
            <summary>
                <para>Gets or sets the type of the <b>View</b> which is currently used by the <b>Scheduler</b> to show its data.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerViewType"/> enumeration value specifying the active <b>View</b> type.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.AddService(System.Type,System.Object,System.Boolean)">
            <summary>
                <para>Adds the specified service to the service container.
</para>
            </summary>
            <param name="serviceType">
		The type of service to add.

            </param>
            <param name="serviceInstance">
		An instance of the service type to add. This object must implement or inherit from the type indicated by the serviceType parameter.

            </param>
            <param name="promote">
		<b>true</b> to promote this request to any parent service containers; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.AddService(System.Type,System.Object)">
            <summary>
                <para>Adds the specified service to the service container.
</para>
            </summary>
            <param name="serviceType">
		The type of service to add.

            </param>
            <param name="serviceInstance">
		An instance of the service type to add. This object must implement or inherit from the type indicated by the serviceType parameter.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.AddService(System.Type,System.ComponentModel.Design.ServiceCreatorCallback)">
            <summary>
                <para>Adds the specified service to the service container.
</para>
            </summary>
            <param name="serviceType">
		The type of service to add.

            </param>
            <param name="callback">
		A callback object that is used to create the service. This allows a service to be declared as available, but delays the creation of the object until the service is requested. 

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.AddService(System.Type,System.ComponentModel.Design.ServiceCreatorCallback,System.Boolean)">
            <summary>
                <para>Adds the specified service to the service container.
</para>
            </summary>
            <param name="serviceType">
		The type of service to add.

            </param>
            <param name="callback">
		A callback object that is used to create the service. This allows a service to be declared as available, but delays the creation of the object until the service is requested. 

            </param>
            <param name="promote">
		<b>true</b> to promote this request to any parent service containers; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.AllowAppointmentConflicts">
            <summary>
                <para>Occurs when the scheduler finds appointments that are in conflict, and the <see cref="P:DevExpress.XtraScheduler.SchedulerOptionsCustomization.AllowAppointmentConflicts"/> property is set to <b>Custom</b>.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.AllowAppointmentCopy">
            <summary>
                <para>Occurs when an end-user tries to copy an appointment and the <see cref="P:DevExpress.XtraScheduler.SchedulerOptionsCustomization.AllowAppointmentCopy"/> property is set to <b>Custom</b>.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.AllowAppointmentCreate">
            <summary>
                <para>Occurs when an end-user tries to create a new appointment and the <see cref="P:DevExpress.XtraScheduler.SchedulerOptionsCustomization.AllowAppointmentCreate"/> property is set to <b>Custom</b>.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.AllowAppointmentDelete">
            <summary>
                <para>Occurs when an end-user tries to delete an appointment and the <see cref="P:DevExpress.XtraScheduler.SchedulerOptionsCustomization.AllowAppointmentDelete"/> property is set to <b>Custom</b>.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.AllowAppointmentDrag">
            <summary>
                <para>Occurs when an end-user tries to drag an appointment and the <see cref="P:DevExpress.XtraScheduler.SchedulerOptionsCustomization.AllowAppointmentDrag"/> property is set to <b>Custom</b>.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.AllowAppointmentDragBetweenResources">
            <summary>
                <para>Occurs when an end-user tries to drag an drop an appointment between resources and the <see cref="P:DevExpress.XtraScheduler.SchedulerOptionsCustomization.AllowAppointmentDragBetweenResources"/> property is set to <b>Custom</b>.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.AllowAppointmentEdit">
            <summary>
                <para>Occurs when an end-user tries to edit an appointment and the <see cref="P:DevExpress.XtraScheduler.SchedulerOptionsCustomization.AllowAppointmentEdit"/> property is set to <b>Custom</b>.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.AllowAppointmentResize">
            <summary>
                <para>Occurs when an end-user tries to resize an appointment and the <see cref="P:DevExpress.XtraScheduler.SchedulerOptionsCustomization.AllowAppointmentResize"/> property is set to <b>Custom</b>.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.AllowDrop">
            <summary>
                <para>Overrides the <see cref="P:System.Windows.Forms.Control.AllowDrop"/> property.
</para>
            </summary>
            <value><b>true</b> if drag-and-drop operations are allowed in the control; otherwise, <b>false</b>.
</value>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.AllowInplaceEditor">
            <summary>
                <para>Occurs when an end-user tries to invoke an appointment's in-place editor and the <see cref="P:DevExpress.XtraScheduler.SchedulerOptionsCustomization.AllowInplaceEditor"/> property is set to <b>Custom</b>.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.Appearance">
            <summary>
                <para>Provides access to the properties that control the appearance of the SchedulerControl's elements.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerAppearance"/> object that provides the appearance settings for specific elements of the SchedulerControl.
</value>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.AppointmentDrag">
            <summary>
                <para>Occurs when appointment is dragged in the <b>Scheduler</b> control.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.AppointmentDrop">
            <summary>
                <para>Fires when you drop the appointment dragged with the mouse.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.AppointmentImages">
            <summary>
                <para>Gets or sets the source of the images that can be displayed within appointments.

</para>
            </summary>
            <value>An object providing images for the SchedulerControl.
</value>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.AppointmentResized">
            <summary>
                <para>Occurs after the user modifies the appointment's interval by dragging its border with the mouse.


</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.AppointmentResizing">
            <summary>
                <para>Occurs when the user starts modifying the appointment's interval by dragging its border with the mouse.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.AppointmentViewInfoCustomizing">
            <summary>
                <para>Use this event to customize the appointment's appearance by modifying the style elements when it is painted.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.BeforeLoadLayout">
            <summary>
                <para>Occurs before a layout is restored from storage (a stream, xml file or the system registry).
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.BeginInit">
            <summary>
                <para>Starts the <b>Scheduler Control</b>'s initialization. Initialization occurs at runtime.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.BeginUpdate">
            <summary>
                <para>Locks the SchedulerControl, preventing visual updates of the object and its elements until the <b>EndUpdate</b> or <b>CancelUpdate</b> method is called.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.BorderStyle">
            <summary>
                <para>Gets or sets the border style for the Scheduler control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.Controls.BorderStyles"/> enumeration value which specifies the border style of the Scheduler control.

</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.CancelUpdate">
            <summary>
                <para>Unlocks the SchedulerControl object after it has been locked by the <b>BeginUpdate</b> method, without causing an immediate visual update.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.CreateAppointment(System.Boolean,System.Boolean)">
            <summary>
                <para>Creates a new appointment with the specified <b>All-Day</b> and <b>Recurring</b> settings, shows it in the <b>Edit Appointment</b> dialog, and then adds it to the <see cref="P:DevExpress.XtraScheduler.SchedulerStorage.Appointments"/> collection of the control's storage.

</para>
            </summary>
            <param name="allDay">
		<b>true</b> if an appointment to create will be All-Day; otherwise, <b>false</b>.

            </param>
            <param name="recurring">
		<b>true</b> if an appointment to create will be Recurring; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.CreateNewAllDayEvent">
            <summary>
                <para>Creates a new <b>All-Day</b> appointment, displays it in the <b>Edit Appointment</b> dialog, and then adds it to the <see cref="P:DevExpress.XtraScheduler.SchedulerStorage.Appointments"/> collection of the control's storage.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.CreateNewAppointment">
            <summary>
                <para>Creates a new appointment, displays it in the <b>Edit Appointment</b> dialog, and then adds it to the <see cref="P:DevExpress.XtraScheduler.SchedulerStorage.Appointments"/> collection of the control's storage.


</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.CreateNewRecurringAppointment">
            <summary>
                <para>Creates a new <b>recurring</b> appointment, shows it in the <b>Edit Appointment</b> dialog, and then adds it to the <see cref="P:DevExpress.XtraScheduler.SchedulerStorage.Appointments"/> collection of the control's storage.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.CreateNewRecurringEvent">
            <summary>
                <para>Creates a new <b>recurring All-Day</b> appointment, displays it in the <b>Edit Appointment</b> dialog, and then adds it to the <see cref="P:DevExpress.XtraScheduler.SchedulerStorage.Appointments"/> collection of the control's storage.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.CustomDrawAppointment">
            <summary>
                <para>Enables appointments to be painted manually.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.CustomDrawAppointmentBackground">
            <summary>
                <para>Enables the backgrounds of appointments to be painted manually.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.CustomDrawDayHeader">
            <summary>
                <para>Enables day headers to be painted manually.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.CustomDrawDayOfWeekHeader">
            <summary>
                <para>Enables day of week headers to be painted manually.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.CustomDrawDayViewAllDayArea">
            <summary>
                <para>Enables the All-Day Area to be painted manually.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.CustomDrawDayViewTimeRuler">
            <summary>
                <para>Enables the time ruler to be painted manually.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.CustomDrawDependency">
            <summary>
                <para>Enables dependencies to be painted manually.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.CustomDrawGroupSeparator">
            <summary>
                <para>Enables group separators to be painted manually.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.CustomDrawNavigationButton">
            <summary>
                <para>Enables navigation buttons to be painted manually.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.CustomDrawResourceHeader">
            <summary>
                <para>Enables resource headers to be painted manually.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.CustomDrawTimeCell">
            <summary>
                <para>Enables time cells to be painted manually.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.CustomDrawWeekViewTopLeftCorner">
            <summary>
                <para>Enables the top left corner of the week view to be painted manually.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.DateNavigatorQueryActiveViewType">
            <summary>
                <para>Enables you to specify the active view type of the Scheduler when the user selects dates in the bound DateNavigator.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.DateTimeScrollBar">
            <summary>
                <para>Gets the date-time scroll bar of the Scheduler Control.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.ScrollBarBase"/> object.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.DayView">
            <summary>
                <para>Gets an object that defines the settings of the scheduler's Day View.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.DayView"/> object representing the Day View in the scheduling area.
</value>


        </member>
        <member name="F:DevExpress.XtraScheduler.SchedulerControl.DefaultPaintStyleName">
            <summary>
                <para>Gets the default paint style name.
</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.DeleteAppointment(DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Deletes the specified appointment from the scheduler's storage.

</para>
            </summary>
            <param name="apt">
		An <see cref="T:DevExpress.XtraScheduler.Appointment"/> object which represents the appointment to delete.

            </param>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.DeleteRecurrentAppointmentFormShowing">
            <summary>
                <para>Occurs before the <b>Confirm Delete</b> dialog window is invoked.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.DeleteSelectedAppointments">
            <summary>
                <para>Deletes the selected appointments.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.DragDropMode">
            <summary>
                <para>Gets or sets the drag-and-drop mode which is active in the SchedulerControl.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.DragDropMode"/> enumeration value.
</value>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.EditAppointmentDependencyFormShowing">
            <summary>
                <para>Occurs before the Appointment Dependency dialog window is invoked. 
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.EditAppointmentFormShowing">
            <summary>
                <para>Occurs before the Edit Appointment dialog window is invoked. 
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.EditRecurrentAppointmentFormShowing">
            <summary>
                <para>Occurs before the <b>Open Recurring Item</b> dialog window is invoked.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.EndInit">
            <summary>
                <para>Ends the <b>Scheduler Control</b>'s initialization.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.EndUpdate">
            <summary>
                <para>Unlocks the SchedulerControl object after a call to the <b>BeginUpdate</b> method and causes an immediate visual update.


</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.FirstDayOfWeek">
            <summary>
                <para>Gets the day which the <b>Scheduler Control</b>'s week starts from.

</para>
            </summary>
            <value>A <see cref="T:System.DayOfWeek"/> enumeration value specifying the start day of the week for the <b>Scheduler</b>.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.GanttView">
            <summary>
                <para>Gets an object that defines the settings of the scheduler's <b>Gantt View</b>.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.GanttView"/> object that is the Gantt View in the scheduling area.

</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.GetPaintStyle">
            <summary>
                <para>Returns the paint style currently used to paint a scheduler control.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerPaintStyle"/> object representing the paint style currently used.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.GetResourceColorSchemasCopy">
            <summary>
                <para>Returns copies of color schemas that are currently used to paint visible resources.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraScheduler.SchedulerColorSchemaCollection"/> object.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.GetService(System.Type)">
            <summary>
                <para>Gets the service object of the specified type.
</para>
            </summary>
            <param name="serviceType">
		An object that specifies the type of service object to get. 

            </param>
            <returns>A service object of the specified type, or a null reference (<b>Nothing</b> in Visual Basic) if there is no service object of this type. 
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.GetService``1">
            <summary>
                <para>Gets the service object of the specified generic type.
</para>
            </summary>
            <returns>A service object of the specified generic type, or a null reference (Nothing in Visual Basic) if there is no service object of this type.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.GetToolTipController">
            <summary>
                <para>Returns the tooltip controller component that controls the appearance, position and the content of the hints displayed by the Scheduler control. 

</para>
            </summary>
            <returns>A <see cref="T:DevExpress.Utils.ToolTipController"/> component which controls the appearance and behavior of the hints displayed by the Scheduler control.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.GoToDate(System.DateTime)">
            <summary>
                <para>Sets the specified date as the start date of the scheduler, meaning that on this date the scheduler will show its data.



</para>
            </summary>
            <param name="date">
		A <see cref="T:System.DateTime"/> value specifying the new start date.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.GoToDate(System.DateTime,DevExpress.XtraScheduler.SchedulerViewType)">
            <summary>
                <para>Sets the specified date as the start date of the scheduler and makes the scheduler show its data for this date using the specified type of view.

</para>
            </summary>
            <param name="date">
		A <see cref="T:System.DateTime"/> value specifying the new start date.

            </param>
            <param name="viewType">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerViewType"/> enumeration value specifying the view in which the scheduler will show its data.


            </param>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.GotoDateFormShowing">
            <summary>
                <para>Occurs before the <b>Go To Date</b> dialog window is invoked.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.GoToToday">
            <summary>
                <para>Sets the start date of the scheduler control to the current date on the local machine.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.GroupType">
            <summary>
                <para>Gets or sets a value that specifies the type of grouping applied to the View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerGroupType"/> enumeration value that specifies how appointments are grouped within the scheduler control.
</value>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.InitAppointmentDisplayText">
            <summary>
                <para>Enables custom text and a description to be displayed within appointments.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.InitAppointmentImages">
            <summary>
                <para>Enables custom images to be displayed within appointments.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.InitNewAppointment">
            <summary>
                <para>Occurs before a new appointment is created in the Scheduler.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.InplaceEditorShowing">
            <summary>
                <para>Occurs every time an in-place editor is invoked in place of the edited appointment.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.IsPrintingAvailable">
            <summary>
                <para>Indicates whether the <b>Scheduler</b> control can be printed.

</para>
            </summary>
            <value><b>true</b> if the scheduler can be printed; otherwise, <b>false</b>. 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.IsUpdateLocked">
            <summary>
                <para>Gets whether the scheduler control has been locked for updating.

</para>
            </summary>
            <value><b>true</b> if the scheduler control is locked; otherwise, <b>false</b>.
</value>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.LayoutUpgrade">
            <summary>
                <para>Occurs when a layout is restored from a data store (a stream, xml file or system registry), and its version differs from the version of the current layout.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.LimitInterval">
            <summary>
                <para>Gets or sets the time interval available for end-users.


</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeInterval"/> object.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.LookAndFeel">
            <summary>
                <para>Provides access to the settings that specify the Scheduler control's look and feel.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object whose properties specify the Scheduler control's look and feel.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.MenuManager">
            <summary>
                <para>Gets or sets the menu manager which controls the look and feel of the context menus.
</para>
            </summary>
            <value>An object which implements the <see cref="T:DevExpress.Utils.Menu.IDXMenuManager"/> interface.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.MonthView">
            <summary>
                <para>Gets an object that defines the settings of the scheduler's Month View.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.MonthView"/> object representing the Month View in the scheduling area.
</value>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.MoreButtonClicked">
            <summary>
                <para>Occurs when the More Button is clicked.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.OptionsBehavior">
            <summary>
                <para>Provides access to the scheduler's behavior options.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerOptionsBehavior"/> object which contains the scheduler's behavior options.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.OptionsCustomization">
            <summary>
                <para>Provides access to the scheduler's customization options.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerOptionsCustomization"/> object which provides the scheduler's customization options.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.OptionsLayout">
            <summary>
                <para>Provides access to the property specifying the layout's version.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.OptionsLayoutBase"/> object, containing layout characteristics.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.OptionsPrint">
            <summary>
                <para>Provides access to the scheduler's printing options.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerOptionsPrint"/> object which provides access to the scheduler's printing options.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.OptionsRangeControl">
            <summary>
                <para>Provides access to interaction options that specify the <see cref="T:DevExpress.XtraEditors.RangeControl"/> and <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> appearance and behavior when these controls are integrated.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerOptionsRangeControl"/> object which contains interaction options for RangeControl and SchedulerControl.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.OptionsView">
            <summary>
                <para>Provides access to the scheduler's view options.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerOptionsView"/> object which provides access to the scheduler's view options.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.PaintStyleName">
            <summary>
                <para>Gets or sets the paint scheme used, by its name.

</para>
            </summary>
            <value>A <see cref="T:System.String"/> specifying the name of the paint style used for the scheduler control.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.PaintStyles">
            <summary>
                <para>Contains paint information on the available paint schemes.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerPaintStyleCollection"/> object representing the collection of paint styles. 

</value>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.PopupMenuShowing">
            <summary>
                <para>Occurs before a popup menu is created for a <b>Scheduler</b> every time a context menu is invoked.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.PrepareContextMenu">
            <summary>
                <para>This member is obsolete. Handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.PopupMenuShowing"/> event instead.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.PreparePopupMenu">
            <summary>
                <para>This member is obsolete. Handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.PopupMenuShowing"/> event instead.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.Print">
            <summary>
                <para>Prints the Scheduler control's data.


</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.Print(DevExpress.XtraScheduler.Printing.SchedulerPrintStyle)">
            <summary>
                <para>Prints the Scheduler control's data using the specified print style.

</para>
            </summary>
            <param name="printStyle">
		A <see cref="T:DevExpress.XtraScheduler.Printing.SchedulerPrintStyle"/> object which specifies the print style to be used when printing the Scheduler's data.


            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.PrintStyles">
            <summary>
                <para>Gets the collection of styles used when printing the Scheduler control.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Printing.SchedulerPrintStyleCollection"/> object representing the collection of printing styles.
</value>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.QueryResourceColorSchema">
            <summary>
                <para>Enables visible resources to be painted according to certain conditions.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.QueryWorkTime">
            <summary>
                <para>Occurs when the scheduler's view calculates the work time interval for the specific resource.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.RangeControlAutoAdjusting">
            <summary>
                <para>Occurs before the RangeControl has been automatically adjusted when the scheduler active view or start date is changed.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.Refresh">
            <summary>
                <para>Updates the Scheduler and forces the control to reflect any changes made to the View's layout. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.RefreshData">
            <summary>
                <para>Updates the XtraScheduler control to reflect any changes made in the data sources which store appointments and appointment resources.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.RemindersEnabled">
            <summary>
                <para>Gets whether the reminders are enabled.
</para>
            </summary>
            <value><b>true</b> if the reminders are enabled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.RemindersFormDefaultAction">
            <summary>
                <para>Occurs when an end-user doesn't click the Dismiss or Snooze button on the <b>Reminders Form</b>, but simply closes it. The <see cref="P:DevExpress.XtraScheduler.SchedulerOptionsBehaviorBase.RemindersFormDefaultAction"/> property should be set to <b>Custom</b>.


</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.RemindersFormShowing">
            <summary>
                <para>Occurs before the Reminders form is displayed.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.RemoveService(System.Type)">
            <summary>
                <para>Removes the service of specified type from the service container.
</para>
            </summary>
            <param name="serviceType">
		The type of service to remove.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.RemoveService(System.Type,System.Boolean)">
            <summary>
                <para>Removes the service of specified type from the service container.
</para>
            </summary>
            <param name="serviceType">
		The type of service to remove.

            </param>
            <param name="promote">
		<b>true</b> to promote this request to any parent service containers; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.ResourceColorSchemas">
            <summary>
                <para>Gets the color schemas used to paint a scheduler's resources.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerColorSchemaCollection"/> object.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.ResourceNavigator">
            <summary>
                <para>Gets the resource navigator control used to navigate through resources by end-users.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.ResourceNavigator"/> object representing the scheduler control's resource navigator.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.ResourceSharing">
            <summary>
                <para>Gets a value indicating whether an appointment can be shared between multiple resources.
</para>
            </summary>
            <value><b>true</b> if the resource sharing is enabled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.RestoreLayoutFromRegistry(System.String)">
            <summary>
                <para>Restores the control's layout from the layout stored at the specified system registry path.

</para>
            </summary>
            <param name="path">
		A <see cref="T:System.String"/> value specifying the system registry path. If the specified path doesn't exist, calling this method has no effect.


            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.RestoreLayoutFromStream(System.IO.Stream)">
            <summary>
                <para>Restores the control's layout from the specified stream.

</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> descendant from which settings are read.


            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.RestoreLayoutFromXml(System.String)">
            <summary>
                <para>Restores the control's layout from the specified XML file.

</para>
            </summary>
            <param name="xmlFile">
		A <see cref="T:System.String"/> value specifying the path to the XML file to read settings from. If the specified file doesn't exist a <b>System.IO.FileNotFoundException</b> type exception is raised. 

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.SaveLayoutToRegistry(System.String)">
            <summary>
                <para>Saves the control's layout to the system registry.

</para>
            </summary>
            <param name="path">
		A <see cref="T:System.String"/> value specifying the system registry path in which to save the layout.


            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.SaveLayoutToStream(System.IO.Stream)">
            <summary>
                <para>Saves the control's layout to the specified stream.

</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> descendant to which the control's layout is written.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.SaveLayoutToXml(System.String)">
            <summary>
                <para>Saves the control's layout to the specified XML file.

</para>
            </summary>
            <param name="xmlFile">
		A <see cref="T:System.String"/> value specifying the path to the file where the layout is to be saved. If an empty string is specified, an exception is raised.


            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.SelectedAppointments">
            <summary>
                <para>Provides access to the collection of selected appointments.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.AppointmentBaseCollection"/> descendant which represents the collection of selected appointments.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.SelectedDependencies">
            <summary>
                <para>Provides access to the collection of selected appointment dependencies.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.AppointmentDependencyBaseCollection"/> descendant which is the collection of selected dependencies.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.SelectedInterval">
            <summary>
                <para>Gets the time interval currently selected in the scheduler's active view by an end-user.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeInterval"/> object representing the selected time interval.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.SelectedResource">
            <summary>
                <para>Gets the resource which contains the time interval currently selected in the scheduler's active view by an end-user.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Resource"/> object which represents the selected resource.
</value>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.SelectionChanged">
            <summary>
                <para>Fires after the selection has been changed.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.SelectNextAppointment">
            <summary>
                <para>Selects the next appointment within the visible area of the view.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.SelectPrevAppointment">
            <summary>
                <para>Selects the previous appointment  within the visible area of the view.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.Services">
            <summary>
                <para>Provides access to the object which contains all implemented services and service-oriented methods. Facilitates the use of Scheduler services.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Services.SchedulerServices"/> object, which provides access to implemented services and related methods.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.ShowDeleteRecurrentAppointmentForm(DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Invokes the dialog window which prompts the user for an action on deleting the recurrent appointment.
</para>
            </summary>
            <param name="apt">
		An <see cref="T:DevExpress.XtraScheduler.Appointment"/> for which the delete command is processed.

            </param>
            <returns>A <see cref="T:DevExpress.XtraScheduler.RecurrentAppointmentAction"/> enumeration value, representing a type of action being performed.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.ShowEditAppointmentDependencyForm(DevExpress.XtraScheduler.AppointmentDependency,System.Boolean)">
            <summary>
                <para>Invokes the <b>Appointment Dependency</b> form used to modify or delete a specified dependency.
</para>
            </summary>
            <param name="dependency">
		An <see cref="T:DevExpress.XtraScheduler.AppointmentDependency"/> to modify or delete.

            </param>
            <param name="readOnly">
		Specifies whether the form should be displayed as read-only.

            </param>
            <returns>A <see cref="T:System.Windows.Forms.DialogResult[]"/> enumeration member.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.ShowEditAppointmentDependencyForm(DevExpress.XtraScheduler.AppointmentDependency)">
            <summary>
                <para>Invokes the <b>Appointment Dependency</b> form used to modify or delete a specified dependency.
</para>
            </summary>
            <param name="dependency">
		An <see cref="T:DevExpress.XtraScheduler.AppointmentDependency"/> to modify or delete.

            </param>
            <returns>A <see cref="T:System.Windows.Forms.DialogResult[]"/> enumeration member.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.ShowEditAppointmentDependencyForm(DevExpress.XtraScheduler.AppointmentDependency,System.Boolean,DevExpress.Utils.Commands.CommandSourceType)">
            <summary>
                <para>Invokes the <b>Appointment Dependency</b> form used to modify or delete a specified dependency.
</para>
            </summary>
            <param name="dependency">
		An <see cref="T:DevExpress.XtraScheduler.AppointmentDependency"/> to modify or delete.

            </param>
            <param name="readOnly">
		Specifies whether the form should be displayed as read-only.

            </param>
            <param name="commandSourceType">
		A <see cref="T:DevExpress.Utils.Commands.CommandSourceType"/> enumeration member that specifies what type of action invokes the form.

            </param>
            <returns>A <see cref="T:System.Windows.Forms.DialogResult[]"/> enumeration member.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.ShowEditAppointmentForm(DevExpress.XtraScheduler.Appointment,System.Boolean)">
            <summary>
                <para>Invokes the <b>Edit Appointment</b> dialog for the specified appointment. Also optionally invokes the <b>Recurrence</b> dialog, if required.

</para>
            </summary>
            <param name="apt">
		An <see cref="T:DevExpress.XtraScheduler.Appointment"/> object which represents the appointment to be edited in the dialog.

            </param>
            <param name="openRecurrenceForm">
		<b>true</b> to open the <b>Recurrence</b> dialog; otherwise, <b>false</b>.

            </param>
            <returns>A <see cref="T:System.Windows.Forms.DialogResult"/> enumeration value representing the return value of the dialog.

</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.ShowEditAppointmentForm(DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Invokes the <b>Edit Appointment</b> dialog for the specified appointment.

</para>
            </summary>
            <param name="apt">
		An <see cref="T:DevExpress.XtraScheduler.Appointment"/> object which represents the appointment to be edited in the dialog.

            </param>
            <returns>A <see cref="T:System.Windows.Forms.DialogResult"/> enumeration value representing the return value of the dialog.

</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.ShowEditAppointmentForm(DevExpress.XtraScheduler.Appointment,System.Boolean,System.Boolean)">
            <summary>
                <para>Invokes the <b>Edit Appointment</b> dialog for the specified appointment. Also, optionally invokes the <b>Recurrence</b> dialog and disables all editors on this form, if required.


</para>
            </summary>
            <param name="apt">
		An <see cref="T:DevExpress.XtraScheduler.Appointment"/> object which represents the appointment to be edited in the dialog.

            </param>
            <param name="openRecurrenceForm">
		<b>true</b> to open the <b>Recurrence</b> dialog; otherwise, <b>false</b>.

            </param>
            <param name="readOnly">
		<b>true</b> to open this form to display properties of a <i>read-only</i> appointment (in this case all form editors are disabled); otherwise, <b>false</b>.

            </param>
            <returns>A <see cref="T:System.Windows.Forms.DialogResult"/> enumeration value representing the return value of the dialog.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.ShowEditRecurrentAppointmentForm(DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Invokes the dialog window which prompts the user for an action on editing the recurrent appointment.
</para>
            </summary>
            <param name="apt">
		An <see cref="T:DevExpress.XtraScheduler.Appointment"/> for which the delete command is processed.

            </param>
            <returns>A <see cref="T:DevExpress.XtraScheduler.RecurrentAppointmentAction"/> enumeration value, representing a type of action being performed.
</returns>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.ShowFeaturesIndicator">
            <summary>
                <para>Gets or sets a value indicating if the Features Indicator should be drawn over the <b>Scheduler Control</b> at design time.

</para>
            </summary>
            <value><b>true</b> to show the Features Indicator at design-time; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.ShowGotoDateForm(System.Windows.Forms.IWin32Window)">
            <summary>
                <para>Invokes the <b>Go To Date</b> dialog as a <i>child</i> of the specified <i>parent</i> window.

</para>
            </summary>
            <param name="parent">
		A <see cref="T:System.Windows.Forms.IWin32Window"/> object representing the <i>parent</i> window for this dialog.

            </param>
            <returns>A <see cref="T:System.Windows.Forms.DialogResult"/> enumeration value representing the return value of the dialog.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.ShowGotoDateForm">
            <summary>
                <para>Invokes the <b>Go To Date</b> dialog.
</para>
            </summary>
            <returns>A <see cref="T:System.Windows.Forms.DialogResult"/> enumeration value representing the return value of the dialog.
</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.ShowPrintOptionsForm">
            <summary>
                <para>Invokes the Print Options dialog.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.ShowPrintPreview">
            <summary>
                <para>Opens the <b>Print Preview</b> window for the Scheduler control.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.ShowPrintPreview(DevExpress.XtraScheduler.Printing.SchedulerPrintStyle)">
            <summary>
                <para>Opens the <b>Print Preview</b> window for the scheduler control's print output, using the specified print style.


</para>
            </summary>
            <param name="printStyle">
		A <see cref="T:DevExpress.XtraScheduler.Printing.SchedulerPrintStyle"/> object which specifies the print style to be used when printing the Scheduler's data.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.ShowRecurrentAppointmentDeleteForm(DevExpress.XtraScheduler.Appointment,System.Boolean@)">
            <summary>
                <para>Invokes the <b>Delete Recurrent Appointments</b> dialog for the specified appointment.

</para>
            </summary>
            <param name="apt">
		An <see cref="T:DevExpress.XtraScheduler.Appointment"/> object which represents the appointment to delete.

            </param>
            <param name="deleteSeries">
		<b>true</b> to delete the entire series of this appointment (only if it's a recurring appointment); otherwise, <b>false</b>.
<b>Note</b>: This parameter is passed as a reference.

            </param>
            <returns>A <see cref="T:System.Windows.Forms.DialogResult"/> enumeration value representing the return value of the dialog.

</returns>


        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerControl.ShowRecurrentAppointmentDeleteForm(DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Invokes the <b>Delete Recurrent Appointments</b> dialog for the specified appointment.

</para>
            </summary>
            <param name="apt">
		An <see cref="T:DevExpress.XtraScheduler.Appointment"/> object which represents the appointment to delete.

            </param>
            <returns>A <see cref="T:DevExpress.XtraScheduler.QueryDeleteAppointmentResult"/> enumeration value representing the return value of the dialog.

</returns>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.Start">
            <summary>
                <para>Gets or sets the first date of the time interval displayed by the scheduler view.
</para>
            </summary>
            <value>A <see cref="T:System.DateTime"/> value that is the start date of the scheduler.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.Storage">
            <summary>
                <para>Gets or sets the storage object for the <b>Scheduler</b> control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerStorage"/> object representing the storage for the <b>Scheduler</b> control.
</value>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.StorageChanged">
            <summary>
                <para>Fires after the <see cref="P:DevExpress.XtraScheduler.SchedulerControl.Storage"/> property's value has been changed.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.SupportsRecurrence">
            <summary>
                <para>Gets whether the information on recurring appointments is obtained from a data source.

</para>
            </summary>
            <value><b>true</b> if the information on appointment recurrences is obtained from a data source; otherwise, <b>false</b>.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.SupportsReminders">
            <summary>
                <para>Gets whether the information on appointment reminders is obtained from a data source.
</para>
            </summary>
            <value><b>true</b> if the information on appointment reminders is obtained from a data source; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.TimelineView">
            <summary>
                <para>Gets an object that defines the settings of the scheduler's Timeline View.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimelineView"/> object representing the Timeline View in the scheduling area.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.ToolTipController">
            <summary>
                <para>Gets or sets the tooltip controller component that controls the appearance, position and the content of the hints displayed by the Scheduler control.

</para>
            </summary>
            <value>The <see cref="T:DevExpress.Utils.ToolTipController"/> component which controls the appearance and behavior of the hints displayed by the Scheduler control.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.UnboundMode">
            <summary>
                <para>Gets a value indicating if the SchedulerControl is bound to data.

</para>
            </summary>
            <value><b>true</b> if the <b>Scheduler</b> isn't bound to appointments data source; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.ViewBounds">
            <summary>
                <para>Gets the bounds of the scheduler control's client region.

</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Rectangle"/> object which represents the bounding rectangle of the scheduler control's client region. 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.ViewRectangle">
            <summary>
                <para>Gets the bounds of the scheduler control's client region.

</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Rectangle"/> object which represents the bounding rectangle of the scheduler control's client region. 

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.Views">
            <summary>
                <para>Contains the settings of the Views that are used to represent information within the Scheduler Control.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerViewRepository"/> object which stores the settings of the calendar Views.

</value>


        </member>
        <member name="E:DevExpress.XtraScheduler.SchedulerControl.VisibleIntervalChanged">
            <summary>
                <para>Fires when the time interval represented by the control's scheduling area has been changed.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.VScrollBar">
            <summary>
                <para>Gets the vertical scrollbar.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.VScrollBar"/> object which represents the vertical scrollbar.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.WeekView">
            <summary>
                <para>Gets an object that defines the settings of the scheduler's Week View.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.WeekView"/> object representing the Week View in the scheduling area.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.WorkDays">
            <summary>
                <para>Provides access to the collection which identifies which days are assigned to a workweek.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.WorkDaysCollection"/> object which identifies work days. 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerControl.WorkWeekView">
            <summary>
                <para>Gets an object that defines the settings of the scheduler's Work Week View.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.WorkWeekView"/> object representing the Work Week View in the scheduling area.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.SchedulerInplaceEditorEventArgs">

            <summary>
                <para>Provides data for creation of an in-place editor within the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.InplaceEditorShowing"/> event handler.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerInplaceEditorEventArgs.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerInplaceEditorEventArgs class with the default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerInplaceEditorEventArgs.BackColor">
            <summary>
                <para>Gets or sets the background color of the inplace editor.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Color"/> object that represents the background color.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerInplaceEditorEventArgs.Bounds">
            <summary>
                <para>Gets or sets the size and location of the inplace editor.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Rectangle"/> object that represents  the size and location of the inplace editor control, in pixels.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerInplaceEditorEventArgs.Control">
            <summary>
                <para>Gets or sets the containing Scheduler control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> object, which is the container control for the inplace editor. 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerInplaceEditorEventArgs.Font">
            <summary>
                <para>Gets or sets the font of the text displayed by the inplace editor.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Font"/> to apply to the text displayed by the control.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerInplaceEditorEventArgs.ForeColor">
            <summary>
                <para>Gets or sets the foreground color of the inplace editor.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Color"/> object that represents the foreground color.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerInplaceEditorEventArgs.Handled">
            <summary>
                <para>Gets or sets whether an event was handled and no default actions are required.
</para>
            </summary>
            <value><b>true</b> if no default processing is required; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerInplaceEditorEventArgs.ViewInfo">
            <summary>
                <para>Gets or sets the object which contains the information used to render the appointment.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.Drawing.AppointmentViewInfo"/> object.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.EditRecurrentAppointmentFormEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.EditRecurrentAppointmentFormShowing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.EditRecurrentAppointmentFormEventHandler.Invoke(System.Object,DevExpress.XtraScheduler.EditRecurrentAppointmentFormEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.EditRecurrentAppointmentFormShowing"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> which raised the event.

            </param>
            <param name="e">
		An <see cref="T:DevExpress.XtraScheduler.EditRecurrentAppointmentFormEventArgs"/> object, which contains event data.


            </param>


        </member>
        <member name="T:DevExpress.XtraScheduler.EditRecurrentAppointmentFormEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.EditRecurrentAppointmentFormShowing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.EditRecurrentAppointmentFormEventArgs.#ctor(DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Initializes a new instance of the EditRecurrentAppointmentFormEventArgs class with the specified settings.
</para>
            </summary>
            <param name="apt">
		An <see cref="T:DevExpress.XtraScheduler.Appointment"/> value which represents the event's appointment. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.AppointmentFormEventArgs.Appointment"/> property. 

            </param>


        </member>
        <member name="T:DevExpress.XtraScheduler.RangeControlAdjustEventHandler">

            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.RangeControlAutoAdjusting"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.RangeControlAdjustEventHandler.Invoke(System.Object,DevExpress.XtraScheduler.RangeControlAdjustEventArgs)">
            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.RangeControlAutoAdjusting"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> which raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraScheduler.RangeControlAdjustEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraScheduler.RangeControlAdjustEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraScheduler.SchedulerControl.RangeControlAutoAdjusting"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.RangeControlAdjustEventArgs.#ctor">
            <summary>
                <para>Initializes a new instance of the RangeControlAdjustEventArgs class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.RangeControlAdjustEventArgs.RangeMaximum">
            <summary>
                <para>Gets or sets the end bound of the range that will be available in the RangeControl after it is automatically adjusted.
</para>
            </summary>
            <value>A <see cref="T:System.DateTime"/> object that is the end bound of the RangeControl's total range.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.RangeControlAdjustEventArgs.RangeMinimum">
            <summary>
                <para>Gets or sets the start bound of the range that will be available in the RangeControl after it is automatically adjusted.
</para>
            </summary>
            <value>A <see cref="T:System.DateTime"/> object that is start bound of the RangeControl's total range.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.RangeControlAdjustEventArgs.Scales">
            <summary>
                <para>Provides access to the collection of scales that will be visible in the RangeControl after it is automatically adjusted.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeScaleCollection"/> object specifying a set of RangeControl scales.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.RangeControlDataDisplayType">

            <summary>
                <para>Lists the values used to specify how appointment data should be displayed within a <see cref="T:DevExpress.XtraEditors.RangeControl"/> when it is bound to a <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/>. 
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraScheduler.RangeControlDataDisplayType.Auto">
            <summary>
                <para>Appointment data contained in a SchedulerControl is shown by the RangeControl as either appointment thumbnails or numbers of appointments in each interval. 

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraScheduler.RangeControlDataDisplayType.Number">
            <summary>
                <para>Each interval in the RangeControl shows a number of appointments contained in the corresponding time interval in a SchedulerControl.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraScheduler.RangeControlDataDisplayType.Thumbnail">
            <summary>
                <para>Appointment data contained in SchedulerControl is shown by RangeControl as appointment thumbnails, each of which is colored according to a label of the corresponding appointment.

</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraScheduler.ScaleBasedRangeControlClientOptions">

            <summary>
                <para>A base class for options that specify the interaction settings for a <see cref="T:DevExpress.XtraEditors.RangeControl"/> and <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> that are integrated.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.ScaleBasedRangeControlClientOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the ScaleBasedRangeControlClientOptions class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.ScaleBasedRangeControlClientOptions.AutoFormatScaleCaptions">
            <summary>
                <para>Specifies whether auto-formats should be applied to header captions of RangeControl scales.
</para>
            </summary>
            <value><b>true</b> if auto-formats are applied to header captions of scales; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.ScaleBasedRangeControlClientOptions.DataDisplayType">
            <summary>
                <para>Specifies how appointments contained in a <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> should be indicated in a <see cref="T:DevExpress.XtraEditors.RangeControl"/> - using thumbnails or numbers.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.RangeControlDataDisplayType"/> enumeration member.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.ScaleBasedRangeControlClientOptions.MaxIntervalWidth">
            <summary>
                <para>Gets or sets the RangeControl intervals' maximum width that can be set when resizing or zooming the RangeControl viewport (in pixels).
</para>
            </summary>
            <value>An integer value.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.ScaleBasedRangeControlClientOptions.MaxSelectedIntervalCount">
            <summary>
                <para>Gets or sets the maximum number of intervals that the selected range can include.
</para>
            </summary>
            <value>An integer value that specifies the maximum number of intervals that can be included in the selected range.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.ScaleBasedRangeControlClientOptions.MinIntervalWidth">
            <summary>
                <para>Gets or sets the RangeControl intervals' minimum width that can be set when resizing or zooming the RangeControl viewport (in pixels).
</para>
            </summary>
            <value>An integer value.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.ScaleBasedRangeControlClientOptions.RangeMaximum">
            <summary>
                <para>Gets or sets the maximum limit of the time range that is available in the <see cref="T:DevExpress.XtraEditors.RangeControl"/> to navigate within the SchedulerControl.
</para>
            </summary>
            <value>A <see cref="T:System.DateTime"/> value that specifies the maximum limit of the range.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.ScaleBasedRangeControlClientOptions.RangeMinimum">
            <summary>
                <para>Gets or sets the minimum limit of the time range that is available in the <see cref="T:DevExpress.XtraEditors.RangeControl"/> to navigate within the SchedulerControl.
</para>
            </summary>
            <value>A <see cref="T:System.DateTime"/> value that specifies the minimum limit of the range.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.ScaleBasedRangeControlClientOptions.Scales">
            <summary>
                <para>Provides access to a collection of scales displayed in the <see cref="T:DevExpress.XtraEditors.RangeControl"/> when it is bound to a <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/>.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeScaleCollection"/> object containing scales for a RangeControl.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.ScaleBasedRangeControlClientOptions.ThumbnailHeight">
            <summary>
                <para>Gets or sets the height of a single appointment thumbnail displayed in the <see cref="T:DevExpress.XtraEditors.RangeControl"/>.

</para>
            </summary>
            <value>An integer value which specifies the appointment thumbnail height measured in pixels.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.SchedulerOptionsRangeControl">

            <summary>
                <para>Provides interaction options for a <see cref="T:DevExpress.XtraEditors.RangeControl"/> and <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> that are integrated.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.SchedulerOptionsRangeControl.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerOptionsRangeControl class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerOptionsRangeControl.AllowChangeActiveView">
            <summary>
                <para>Gets or sets whether to automatically change the SchedulerControl's active view depending on which time range is selected in the <see cref="T:DevExpress.XtraEditors.RangeControl"/>.

</para>
            </summary>
            <value><b>true</b> to automatically switch between scheduler views; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.SchedulerOptionsRangeControl.AutoAdjustMode">
            <summary>
                <para>Gets or sets a value indicating if the RangeControl should be automatically adjusted after the scheduler visible interval or active view has been changed.
</para>
            </summary>
            <value><b>true</b> if the RangeControl is automatically adjusted ; otherwise, <b>false</b>. 
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.UI.TimeZoneEdit">

            <summary>
                <para>Represents a combo box used to specify a time zone.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.UI.TimeZoneEdit.#ctor">
            <summary>
                <para>Initializes a new instance of the TimeZoneEdit class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.TimeZoneEdit.EditorTypeName">
            <summary>
                <para>Gets the class name of the editor.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> object identifying the class name.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.TimeZoneEdit.Properties">
            <summary>
                <para>Gets an object that contains settings specific to the editor.
</para>
            </summary>
            <value>A <b>RepositoryItemTimeZone</b> object that contains editor settings.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.TimeZoneEdit.TimeZoneId">
            <summary>
                <para>Gets or sets the string identifier of the time zone selected in the control. 
</para>
            </summary>
            <value>A string that uniquely identifies a particular time zone and corresponds to the System.TimeZoneInfo.Id property value.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.Drawing.ConnectionPointsInfo">

            <summary>
                <para>For internal use.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.ConnectionPointsInfo.#ctor">
            <summary>
                <para>Initializes a new instance of the ConnectionPointsInfo class with default settings.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.ConnectionPointsInfo.BottomLeft">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.ConnectionPointsInfo.BottomRight">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.ConnectionPointsInfo.GetNextPoint(DevExpress.XtraScheduler.Drawing.ConnectorLocation,System.Drawing.Point)">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <param name="location">
		 

            </param>
            <param name="point">
		 

            </param>
            <returns> 
</returns>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.ConnectionPointsInfo.Left">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.ConnectionPointsInfo.ReturnNextPoint(DevExpress.XtraScheduler.Drawing.ConnectorLocation,System.Drawing.Point)">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <param name="location">
		 

            </param>
            <param name="point">
		 

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.ConnectionPointsInfo.Right">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.ConnectionPointsInfo.TopLeft">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.ConnectionPointsInfo.TopRight">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.Drawing.DependencyTableType">

            <summary>
                <para>Lists types of dependency connections to the appointment.

</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraScheduler.Drawing.DependencyTableType.IncomingInFinish">
            <summary>
                <para>Dependency line enters the appointment's end.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraScheduler.Drawing.DependencyTableType.IncomingInStart">
            <summary>
                <para>Dependency line enters the appointment's start.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraScheduler.Drawing.DependencyTableType.OutcomingFromFinish">
            <summary>
                <para>Dependency line goes off from the appointment's end.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraScheduler.Drawing.DependencyTableType.OutcomingFromStart">
            <summary>
                <para>Dependency line goes off from the appointment's start.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraScheduler.Drawing.DependencyViewInfo">

            <summary>
                <para>Provides information on the visual representation of the dependency.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.DependencyViewInfo.#ctor">
            <summary>
                <para>Initializes a new instance of the DependencyViewInfo class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.DependencyViewInfo.#ctor(System.Drawing.Point,System.Drawing.Point)">
            <summary>
                <para>Initializes a new instance of the DependencyViewInfo class with the specified start and end points.
</para>
            </summary>
            <param name="start">
		A <see cref="T:System.Drawing.Point"/> object specifying the point at which the dependency starts.


            </param>
            <param name="end">
		A <see cref="T:System.Drawing.Point"/> object specifying the point at which the dependency ends.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.DependencyViewInfo.Appearance">
            <summary>
                <para>Provides access to an object used to specify the appearance of the dependency object.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> instance specifying look and feel characteristics of a dependency.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.DependencyViewInfo.Dependencies">
            <summary>
                <para>Gets a collection of dependencies for which the current view info is calculated.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.AppointmentDependencyCollection"/> object that contains dependencies for the current view info. 
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.DependencyViewInfo.Dispose">
            <summary>
                <para>Disposes of the DependencyViewInfo object.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.DependencyViewInfo.End">
            <summary>
                <para>Gets or sets the ending point of a visual dependency object.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Point"/> object specifying the point at which the dependency ends.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.DependencyViewInfo.HitTestType">
            <summary>
                <para>Gets the value that indicates the test point type.
</para>
            </summary>
            <value>An <see cref="F:DevExpress.XtraScheduler.Drawing.SchedulerHitTest.AppointmentDependency"/> value.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.DependencyViewInfo.Interval">
            <summary>
                <para>Overrides the corresponding method of the base class to hide it.
</para>
            </summary>
            <value>An <see cref="P:DevExpress.XtraScheduler.TimeInterval.Empty"/> value.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.DependencyViewInfo.Items">
            <summary>
                <para>Provides access to a collection of graphic elements, such as roundings or arrows, drawn within a dependency object.
</para>
            </summary>
            <value>A <b>DevExpress.XtraScheduler.Drawing.ViewInfoItemCollection</b> object containing graphic elements.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.DependencyViewInfo.LineEnd">
            <summary>
                <para>Gets or sets the start point of a dependency line.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Point"/> object specifying the point at which the dependency line starts.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.DependencyViewInfo.LineStart">
            <summary>
                <para>Gets or sets the start point of a dependency line.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Point"/> object specifying the point at which the dependency line starts.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.DependencyViewInfo.Resource">
            <summary>
                <para>Overrides the corresponding value of the base class to hide it.
</para>
            </summary>
            <value>An <see cref="P:DevExpress.XtraScheduler.Resource.Empty"/> value.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.DependencyViewInfo.SelectedAppearance">
            <summary>
                <para>Provides access to an object used to specify the appearance of the selected dependency.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> instance specifying the look and feel characteristics of a dependency.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.DependencyViewInfo.Start">
            <summary>
                <para>Gets or sets the starting point of a visual dependency object.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Point"/> object specifying the point at which the dependency starts.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.DependencyViewInfo.ToString">
            <summary>
                <para>Returns the textual representation of the visual dependency location.
</para>
            </summary>
            <returns>A <see cref="T:System.String"/> value which is the textual representation of the location.
</returns>


        </member>
        <member name="T:DevExpress.XtraScheduler.GanttViewAppearance">

            <summary>
                <para>Provides appearance settings used to paint a Gantt View.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.GanttViewAppearance.#ctor">
            <summary>
                <para>Initializes a new instance of the GanttViewAppearance class with the default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.GanttViewAppearance.Dependency">
            <summary>
                <para>Gets the appearance settings used to paint a dependency within the View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint a Dependency. 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.GanttViewAppearance.SelectedDependency">
            <summary>
                <para>Gets the appearance settings used to paint a selected dependency within the View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint a selected Dependency. 
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.UI.AppointmentDependencyFormController">

            <summary>
                <para>Provides all the settings which are required to edit a particular dependency in the <b>Appointment Dependency</b> form.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.UI.AppointmentDependencyFormController.#ctor(DevExpress.XtraScheduler.Native.InnerSchedulerControl,DevExpress.XtraScheduler.AppointmentDependency)">
            <summary>
                <para>Initializes a new instance of the AppointmentDependencyFormController class with the specified appointment dependency and scheduler control.
</para>
            </summary>
            <param name="innerControl">
		An <see cref="T:DevExpress.XtraScheduler.Native.InnerSchedulerControl"/> object which is the scheduler control of the appointment dependency form controller.


            </param>
            <param name="dependency">
		An <see cref="T:DevExpress.XtraScheduler.AppointmentDependency"/> object which is the dependency currently being edited.


            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.AppointmentDependencyFormController.ApplyChanges">
            <summary>
                <para>Copies the Type value of the AppointmentDependency copy that is currently being edited in the form to the corresponding properties of the source appointment dependency.



</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.AppointmentDependencyFormController.DependencyType">
            <summary>
                <para>Gets or sets the type of the appointment dependency currently being edited.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.AppointmentDependencyType"/> enumeration value.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.AppointmentDependencyFormController.DependentTaskDescription">
            <summary>
                <para>Gets the Description property value of the appointment that corresponds to the dependent task in the current dependency.
</para>
            </summary>
            <value>A string that is the appointment (task) description.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.AppointmentDependencyFormController.ParentTaskDescription">
            <summary>
                <para>Gets the Description property value of the appointment that corresponds to the parent task in the current dependency.
</para>
            </summary>
            <value>A string that is the appointment (task) description.
</value>


        </member>
        <member name="E:DevExpress.XtraScheduler.UI.AppointmentDependencyFormController.PropertyChanged">
            <summary>
                <para>Occurs when a dependency type of the AppointmentDependency object handled by the <b>AppointmentDependencyFormController</b> changes.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraScheduler.UI.AppointmentDependencyForm">

            <summary>
                <para>Default form used to modify or delete the appointment dependency.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.UI.AppointmentDependencyForm.#ctor(DevExpress.XtraScheduler.SchedulerControl,DevExpress.XtraScheduler.AppointmentDependency)">
            <summary>
                <para>Initializes a new instance of the AppointmentDependencyForm class for the specified Scheduler control and appointment dependency.
</para>
            </summary>
            <param name="control">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> object specifying the owner of the form.

            </param>
            <param name="dep">
		An <see cref="T:DevExpress.XtraScheduler.AppointmentDependency"/> object whose properties are being edited.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.UI.AppointmentDependencyForm.#ctor">
            <summary>
                <para>Initializes a new instance of the AppointmentDependencyForm class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.AppointmentDependencyForm.ReadOnly">
            <summary>
                <para>Gets or sets whether the form allows editing.
</para>
            </summary>
            <value><b>true</b> to open a form in read-only mode; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.GanttView">

            <summary>
                <para>A view that shows tasks, the relationship among the tasks and task progress in relation to time.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.GanttView.#ctor(DevExpress.XtraScheduler.SchedulerControl)">
            <summary>
                <para>Initializes a new instance of the GanttView class with the specified <b>Scheduler</b> control.
</para>
            </summary>
            <param name="control">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> value that specifies the <b>Scheduler</b> control of the view. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.SchedulerViewBase.Control"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.GanttView.Appearance">
            <summary>
                <para>Provides access to the properties that control the appearance of the GanttView's elements.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.GanttViewAppearance"/> object that provides the appearance settings for GanttView elements.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.GanttView.AppointmentDisplayOptions">
            <summary>
                <para>Provides access to the <b>Gantt View</b> appointments display options.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.GanttViewAppointmentDisplayOptions"/> object containing settings to display the appointments (tasks) in the Gantt View.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.GanttView.ChangeDependencySelection(DevExpress.XtraScheduler.AppointmentDependency)">
            <summary>
                <para>Makes the specified appointment dependency the only selected dependency.
</para>
            </summary>
            <param name="dep">
		An <see cref="T:DevExpress.XtraScheduler.AppointmentDependency"/> object that specifies the dependency to be selected.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.GanttView.Scales">
            <summary>
                <para>Provides access to a collection of time scales displayed in the Gantt view.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeScaleCollection"/> object containing time scales for this view.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.GanttView.SelectDependency(DevExpress.XtraScheduler.AppointmentDependency)">
            <summary>
                <para>Makes the specified appointment dependency the only selected dependency within the View.
</para>
            </summary>
            <param name="dep">
		An <see cref="T:DevExpress.XtraScheduler.AppointmentDependency"/> object that specifies the appointment dependency to be selected.


            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.GanttView.TimelineScrollBarVisible">
            <summary>
                <para>Overrides the corresponding property of the base class, to hide it.
</para>
            </summary>
            <value>Always <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.GanttView.Type">
            <summary>
                <para>Gets the view's type.
</para>
            </summary>
            <value>The <see cref="F:DevExpress.XtraScheduler.SchedulerViewType.Gantt"/> value. 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.GanttView.ViewInfo">
            <summary>
                <para>Gets the information on the visual representation of the object.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Drawing.TimelineViewInfo"/> object providing information on the visual representation of the GanttView's elements.


</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.AppointmentDependencyFormEventArgs">

            <summary>
                <para>Provides data for the SchedulerControl.EditAppointmentDependencyFormShowing event.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.AppointmentDependencyFormEventArgs.#ctor(DevExpress.XtraScheduler.AppointmentDependency)">
            <summary>
                <para>Initializes a new instance of the AppointmentDependencyFormEventArgs class with the specified settings.
</para>
            </summary>
            <param name="dep">
		An <see cref="T:DevExpress.XtraScheduler.AppointmentDependency"/> value. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.AppointmentDependencyFormEventArgs.AppointmentDependency"/> property. 

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.AppointmentDependencyFormEventArgs.AppointmentDependency">
            <summary>
                <para>Gets the appointment dependency for which the dialog will be shown.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.AppointmentDependency"/> object that is the appointment dependency currently being processed.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.AppointmentDependencyFormEventArgs.CommandSourceType">
            <summary>
                <para>Indicates the mechanism of the command input, e.g. keyboard, mouse, menu.

</para>
            </summary>
            <value>A <b>DevExpress.Utils.Commands.CommandSource</b> enumeration member, specifying the command input method.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.AppointmentDependencyFormEventArgs.ReadOnly">
            <summary>
                <para>Gets a value indicating whether an appointment dependency is read-only.
</para>
            </summary>
            <value><b>true</b> if the dependency is read-only; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.PrepareFilterColumnEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraScheduler.SchedulerStorage.PrepareAppointmentFilterColumn"/> and <see cref="E:DevExpress.XtraScheduler.SchedulerStorage.PrepareResourceFilterColumn"/> events. 
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.PrepareFilterColumnEventHandler.Invoke(System.Object,DevExpress.XtraScheduler.PrepareFilterColumnEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="P:DevExpress.XtraScheduler.PrepareFilterColumnEventArgs.FilterColumn"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.XtraScheduler.SchedulerControl"/> which raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraScheduler.PrepareFilterColumnEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraScheduler.PrepareFilterColumnEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraScheduler.SchedulerStorage.PrepareAppointmentFilterColumn"/>  and <see cref="E:DevExpress.XtraScheduler.SchedulerStorage.PrepareResourceFilterColumn"/> events.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.PrepareFilterColumnEventArgs.#ctor(DevExpress.XtraEditors.Filtering.FilterColumn)">
            <summary>
                <para>Initializes a new instance of the PrepareFilterColumnEventArgs class with the specified arguments.
</para>
            </summary>
            <param name="filterColumn">
		A <see cref="T:DevExpress.XtraEditors.Filtering.FilterColumn"/> object, representing a field to be filtered.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.PrepareFilterColumnEventArgs.FilterColumn">
            <summary>
                <para>Gets or sets a field to which a filter criteria is applied.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.Filtering.FilterColumn"/> object, representing a field to be filtered.

</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.AppointmentLabelCollection">

            <summary>
                <para>Represents a collection of appointment labels.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.AppointmentLabelCollection.#ctor">
            <summary>
                <para>Initializes a new instance of the AppointmentLabelCollection class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.AppointmentLabelCollection.Item(System.Int32)">
            <summary>
                <para>Gets the AppointmentLabelCollection object specified by its index.
</para>
            </summary>
            <param name="index">
		An integer specifying the index of a label.

            </param>
            <value>A <see cref="T:DevExpress.XtraScheduler.AppointmentLabel"/> object which represents a label located at the specified index.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.AppointmentLabel">

            <summary>
                <para>Represents an appointment's identification label.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.AppointmentLabel.#ctor(System.Drawing.Color,System.String,System.String)">
            <summary>
                <para>Initializes a new instance of the AppointmentLabel class with the specified color, display name, and menu caption.
</para>
            </summary>
            <param name="color">
		A <see cref="T:System.Drawing.Color"/> value that specifies the color of the appointment label. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.Color"/> property.

            </param>
            <param name="displayName">
		A <see cref="T:System.String"/> value that specifies the text of the appointment label. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.DisplayName"/> property.

            </param>
            <param name="menuCaption">
		A <see cref="T:System.String"/> value that specifies the menu caption of the appointment label. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.MenuCaption"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.AppointmentLabel.#ctor">
            <summary>
                <para>Initializes a new instance of the AppointmentLabel class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraScheduler.AppointmentLabel.#ctor(System.Drawing.Color,System.String)">
            <summary>
                <para>Initializes a new instance of the AppointmentLabel class with the specified color and display name.
</para>
            </summary>
            <param name="color">
		A <see cref="T:System.Drawing.Color"/> value that specifies the color of the appointment label. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.Color"/> property.

            </param>
            <param name="displayName">
		A <see cref="T:System.String"/> value that specifies the text of the appointment label. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.DisplayName"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.AppointmentLabel.#ctor(System.String,System.String)">
            <summary>
                <para>Initializes a new instance of the AppointmentLabel class with the specified display name and menu caption.
</para>
            </summary>
            <param name="displayName">
		A <see cref="T:System.String"/> value that specifies the text of the appointment label. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.DisplayName"/> property.

            </param>
            <param name="menuCaption">
		A <see cref="T:System.String"/> value that specifies the menu caption of the appointment label. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.MenuCaption"/> property.

            </param>


        </member>
        <member name="M:DevExpress.XtraScheduler.AppointmentLabel.#ctor(System.String)">
            <summary>
                <para>Initializes a new instance of the AppointmentLabel class with the specified display name.
</para>
            </summary>
            <param name="displayName">
		A <see cref="T:System.String"/> value that specifies the text of the appointment label. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.DisplayName"/> property.

            </param>


        </member>
        <member name="T:DevExpress.XtraScheduler.Drawing.VerticalResourceHeader">

            <summary>
                <para>Represents the vertical Resource Header visual element of the Scheduler.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.VerticalResourceHeader.#ctor(DevExpress.XtraScheduler.BaseHeaderAppearance,DevExpress.XtraScheduler.SchedulerResourceHeaderOptions)">
            <summary>
                <para>Initializes a new instance of the VerticalResourceHeader class with the specified settings.
</para>
            </summary>
            <param name="appearance">
		A <see cref="T:DevExpress.XtraScheduler.BaseHeaderAppearance"/> object, which provides the appearance settings for the visual element. 

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerResourceHeaderOptions"/> object, providing various visual settings for the resource headers.

            </param>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.VerticalResourceHeader.RotateCaption">
            <summary>
                <para>Gets or sets the value indicating whether to rotate the caption's text.

</para>
            </summary>
            <value><b>true</b> to rotate the caption's text; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.Drawing.HorizontalResourceHeader">

            <summary>
                <para>Represents the horizontal Resource Header visual element of the Scheduler.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.HorizontalResourceHeader.#ctor(DevExpress.XtraScheduler.BaseHeaderAppearance,DevExpress.XtraScheduler.SchedulerResourceHeaderOptions)">
            <summary>
                <para>Initializes a new instance of the HorizontalResourceHeader class with the specified settings.
</para>
            </summary>
            <param name="appearance">
		A <see cref="T:DevExpress.XtraScheduler.BaseHeaderAppearance"/> object, which provides the appearance settings for the visual element. 

            </param>
            <param name="options">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerResourceHeaderOptions"/> object, providing various visual settings for the resource headers.

            </param>


        </member>
        <member name="T:DevExpress.XtraScheduler.UI.AppointmentDependencyTypeEdit">

            <summary>
                <para>A control used to select a type of appointment dependency.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraScheduler.UI.AppointmentDependencyTypeEdit.#ctor">
            <summary>
                <para>Initializes a new instance of the AppointmentDependencyTypeEdit class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.AppointmentDependencyTypeEdit.EditorTypeName">
            <summary>
                <para>Gets the class name of the editor.
</para>
            </summary>
            <value>A string identifying the class name.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.AppointmentDependencyTypeEdit.EditValue">
            <summary>
                <para>Gets or sets the editor's value.
</para>
            </summary>
            <value>A <see cref="T:System.Object"/> value.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.AppointmentDependencyTypeEdit.Properties">
            <summary>
                <para>Gets an object that contains editor specific settings.
</para>
            </summary>
            <value>A <b>RepositoryItemRegisterAppointmentDependencyType</b> object that contains editor settings.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.UI.AppointmentDependencyTypeEdit.Type">
            <summary>
                <para>Gets or sets a dependency type shown in the editor.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.AppointmentDependencyType"/> enumeration value.
</value>


        </member>
        <member name="T:DevExpress.XtraScheduler.Drawing.AppointmentViewInfo">

            <summary>
                <para>Provides information on the visual representation of the appointment.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.AppointmentViewInfo.Appearance">
            <summary>
                <para>Provides access to an object used to specify the appointment appearance.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> instance look and feel characteristics of an appointment.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.AppointmentViewInfo.Appointment">
            <summary>
                <para>Provides access to an appointment for which the visual representation has been built.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.Appointment"/> class instance.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.AppointmentViewInfo.AppointmentInterval">
            <summary>
                <para>Gets the time interval which the current appointment occupies.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeInterval"/> object representing the appointment time interval.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.AppointmentViewInfo.BackColor">
            <summary>
                <para>Gets or sets the appointment coloring. Use the <b>Appearance.BackColor</b> instead.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Color"/> object representing the appointment fill color.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.AppointmentViewInfo.CalculateHitInfo(System.Drawing.Point,DevExpress.XtraScheduler.Drawing.SchedulerHitInfo)">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="pt">
		A <see cref="T:System.Drawing.Point"/> object containing coordinates of the test point.

            </param>
            <param name="nextHitInfo">
		A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerHitInfo"/> object containing information on the scheduler element under the test point.

            </param>
            <returns>A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerHitInfo"/> object containing information on the scheduler element under the test point.
</returns>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.AppointmentViewInfo.Description">
            <summary>
                <para>Gets the appointment description.
</para>
            </summary>
            <value>A string representing the appointment description.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.AppointmentViewInfo.DisableDrop">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value> 
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.AppointmentViewInfo.DisplayText">
            <summary>
                <para>Gets the text of the appointment subject.
</para>
            </summary>
            <value>A string containing the text of the appointment subject.

</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.AppointmentViewInfo.HitTestType">
            <summary>
                <para>Gets a value used in hit testing.
</para>
            </summary>
            <value>A <see cref="F:DevExpress.XtraScheduler.Drawing.SchedulerHitTest.AppointmentContent"/> value.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.AppointmentViewInfo.InnerBounds">
            <summary>
                <para>Gets or sets the rectangle representing the appointment body.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Rectangle"/> object representing the appointment body for display.
</value>


        </member>
        <member name="M:DevExpress.XtraScheduler.Drawing.AppointmentViewInfo.IsLongTime">
            <summary>
                <para>Indicates whether an appointment spans across several days.
</para>
            </summary>
            <returns><b>true</b> if an appointment spans across several days; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.AppointmentViewInfo.Options">
            <summary>
                <para>Provides access to several options for the visual representation of an appointment.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.Drawing.AppointmentViewInfoOptions"/> object containing specific options.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.AppointmentViewInfo.ShowBell">
            <summary>
                <para>Gets or sets whether to display a bell icon indicating an associated reminder.
</para>
            </summary>
            <value><b>true</b> to display a bell icon; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.AppointmentViewInfo.ShowEndTime">
            <summary>
                <para>Gets or sets a value specifying whether the end time of the current appointment is displayed.
</para>
            </summary>
            <value><b>true</b> to show the appointment end time; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.AppointmentViewInfo.ShowRecurrence">
            <summary>
                <para>Gets or sets whether the recurrence symbol should be displayed for the recurrent appointment.
</para>
            </summary>
            <value><b>true</b> if a recurrence symbol should be displayed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.AppointmentViewInfo.ShowStartTime">
            <summary>
                <para>Gets or sets a value specifying whether the start time of the current appointment is displayed.
</para>
            </summary>
            <value><b>true</b> to show the appointment start time; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.AppointmentViewInfo.ShowTimeAsClock">
            <summary>
                <para>Gets or sets whether the start and end time are displayed as clock symbols.
</para>
            </summary>
            <value><b>true</b> to display time as clock symbols; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.AppointmentViewInfo.Status">
            <summary>
                <para>Gets or sets the visual status of the appointment for display.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.AppointmentStatusBase"/> object that represents an appointment status.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.AppointmentViewInfo.StatusDisplayType">
            <summary>
                <para>Gets or sets whether the border of an appointment should be colorized according to the status and appointment duration.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.AppointmentStatusDisplayType"/> enumeration specifying how the status is displayed.
</value>


        </member>
        <member name="P:DevExpress.XtraScheduler.Drawing.AppointmentViewInfo.StatusItems">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value> 
</value>


        </member>
    </members>
</doc>
