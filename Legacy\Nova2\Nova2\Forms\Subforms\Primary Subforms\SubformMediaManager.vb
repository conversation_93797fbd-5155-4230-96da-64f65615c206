Imports System.Data.SqlClient

Public Class SubformMediaManager

    Dim Media_GridBindingSource As BindingSource
    Dim Media_DataSet As DataSet

    Dim MediaGrouping_GridBindingSource As BindingSource
    Dim MediaGrouping_DataSet As DataSet

    Dim MediaChannel_GridBindingSource As BindingSource
    Dim MediaChannel_DataSet As DataSet

#Region "Public Methods"

    Public Sub New()

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        GridController = New GridManager _
        (GridItems,
        TextEditSearch,
        Media.GetListData(My.Settings.DBConnection, String.Empty, Nothing),
        My.Settings.DBConnection,
        PictureAdvancedSearch,
        PictureClearSearch,
        ButtonEdit,
        ButtonDelete)

        ' Add any initialization after the InitializeComponent() call.
        Dim GridController_MediaGrouping As GridManager = New GridManager _
        (GridMediaGrouping,
        TextEditSearchMediaGroup,
        MediaGroup.GetListData(My.Settings.DBConnection, String.Empty, Nothing),
        My.Settings.DBConnection,
        PictureAdvancedSearchMediaGroup,
        PictureClearSearchMediaGroup,
        ButtonEditMediaGroup,
        ButtonDeleteMediaGroup)

        ' Add any initialization after the InitializeComponent() call.
        Dim GridController_MediaChannel As GridManager = New GridManager _
        (GridMediaChannels,
        TextEditSearchMediaChannels,
        MediaChannelGroup.GetListData(My.Settings.DBConnection, String.Empty, Nothing),
        My.Settings.DBConnection,
        PictureAdvancedSearchMediaChannels,
        PictureClearSearchMediaChannels,
        ButtonEditMediaChannels,
        ButtonDeleteMediaChannels)

    End Sub

#End Region

#Region "Event Handlers"

    Private Sub Subform_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        ' Get the datasource of the grid.
        Media_GridBindingSource = GridItems.DataSource
        MediaGrouping_GridBindingSource = GridMediaGrouping.DataSource
        MediaChannel_GridBindingSource = GridMediaChannels.DataSource

        ' Get the dataset being used by the grid.
        Media_DataSet = CType(Media_GridBindingSource.List, DataView).Table.DataSet
        MediaGrouping_DataSet = CType(MediaGrouping_GridBindingSource.List, DataView).Table.DataSet
        MediaChannel_DataSet = CType(MediaChannel_GridBindingSource.List, DataView).Table.DataSet

    End Sub

    Private Sub ButtonAdd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonAdd.Click
        OpenDetailSubform(True)
    End Sub

    Private Sub ButtonEdit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonEdit.Click
        OpenDetailSubform(False)
    End Sub

    Private Sub ButtonDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonDelete.Click

        ' Load the BurstCount table with data.
        Dim BurstCountAdapter As New DataSetMediaTableAdapters.BurstCountTableAdapter
        Dim SqlCon As New SqlClient.SqlConnection(My.Settings.DBConnection)
        BurstCountAdapter.Connection = SqlCon

        Try
            BurstCountAdapter.Fill(Media_DataSet.Tables("BurstCount"))
        Catch ex As Exception
            CType(TopLevelControl, BaseForm).ShowMessage _
            ("Rats!  Some data failed to load correctly." & vbCrLf & vbCrLf _
            & LiquidShell.LiquidAgent.GetErrorMessage(ex), "Data Load Error", MessageBoxIcon.Error)
            Exit Sub
        Finally
            BurstCountAdapter.Dispose()
            SqlCon.Dispose()
        End Try

        ' Delete the selcted rows.
        Media.Delete(GridItems, My.Settings.DBConnection)

    End Sub

    Private Sub GridItems_CellMouseDoubleClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) Handles GridItems.CellMouseDoubleClick
        If e.RowIndex > -1 Then
            ' The row double-clicked is NOT the column header row. Handle the event.
            ButtonEdit_Click(sender, e)
        End If
    End Sub

#End Region

#Region "Private Methods"

    Private Sub OpenDetailSubform(ByVal NewItem As Boolean)

        If NewItem = False Then
            ' User clicked the Edit button.
            Dim ParentForm As BaseForm = CType(TopLevelControl, BaseForm)
            If GridItems.SelectedRows.Count < 1 Then
                ' Stop because no rows are selected.
                ParentForm.ShowMessage("Please select the row to edit before using the 'Edit' button.")
                Exit Sub
            ElseIf GridItems.SelectedRows.Count > 1 Then
                ' Stop because more than one row is selected.
                ParentForm.ShowMessage("Please select only one row to edit before using the 'Edit' button.")
                Exit Sub
            End If
        End If

        ' Create table adapters to populate related tables.
        Dim MediaFamilyAdapter As New DataSetMediaTableAdapters.MediaFamilyTableAdapter
        Dim CategoryAdapter As New DataSetMediaTableAdapters.CategoryTableAdapter
        Dim MediaCategoryAdapter As New DataSetMediaTableAdapters.MediaCategoryTableAdapter
        Dim MediaLifeCycleAdapter As New DataSetMediaTableAdapters.MediaLifeCycleTableAdapter
        Dim mediaRuleBindingSource As BindingSource = New BindingSource
        Dim MediaCostAdapter As New DataSetMediaTableAdapters.MediaCostTableAdapter

        ' Set the sql connection of the new adapters.
        Dim SqlCon As New SqlClient.SqlConnection(My.Settings.DBConnection)
        MediaFamilyAdapter.Connection = SqlCon
        CategoryAdapter.Connection = SqlCon
        MediaCategoryAdapter.Connection = SqlCon
        MediaLifeCycleAdapter.Connection = SqlCon
        MediaCostAdapter.Connection = SqlCon

        ' Populate tables needed for adding new rows and editing existing rows.
        Try
            MediaFamilyAdapter.Fill(Media_DataSet.Tables("MediaFamily"))
            CategoryAdapter.Fill(Media_DataSet.Tables("Category"))
        Catch ex As Exception
            CType(TopLevelControl, BaseForm).ShowMessage _
            ("Rats!  Some data failed to load correctly." & vbCrLf & vbCrLf _
            & LiquidShell.LiquidAgent.GetErrorMessage(ex), "Data Load Error", MessageBoxIcon.Error)
        Finally
            MediaFamilyAdapter.Dispose()
            CategoryAdapter.Dispose()
        End Try

        ' Populate tables needed for editing existing rows.
        If NewItem = False Then
            Dim MediaID As Integer = CType(Media_GridBindingSource.Current, DataRowView).Item("MediaID")
            Try
                'get media rules information from the database
                mediaRuleBindingSource.DataSource = GetMediaRulesList(MediaID, SqlCon.ConnectionString)

                MediaCategoryAdapter.Fill(Media_DataSet.Tables("MediaCategory"), MediaID)
                MediaLifeCycleAdapter.Fill(Media_DataSet.Tables("MediaLifeCycle"), MediaID)
                MediaCostAdapter.Fill(Media_DataSet.Tables("MediaCost"), MediaID)
            Catch ex As Exception
                CType(TopLevelControl, BaseForm).ShowMessage _
                ("Rats!  Some data failed to load correctly." & vbCrLf & vbCrLf _
                & LiquidShell.LiquidAgent.GetErrorMessage(ex), "Data Load Error", MessageBoxIcon.Error)
            Finally
                MediaCategoryAdapter.Dispose()
                MediaLifeCycleAdapter.Dispose()
                MediaCostAdapter.Dispose()
            End Try
        End If

        ' Dispose the connection.
        SqlCon.Dispose()

        ' Open the form to edit the item.
        AddChild(New SubformMedia(New Media(Media_GridBindingSource, NewItem, mediaRuleBindingSource)))

    End Sub




    Private Sub OpenMediaGroupDetailSubform(ByVal NewItem As Boolean)

        If NewItem = False Then
            ' User clicked the Edit button.
            Dim ParentForm As BaseForm = CType(TopLevelControl, BaseForm)
            If GridMediaGrouping.SelectedRows.Count < 1 Then
                ' Stop because no rows are selected.
                ParentForm.ShowMessage("Please select the row to edit before using the 'Edit' button.")
                Exit Sub
            ElseIf GridMediaGrouping.SelectedRows.Count > 1 Then
                ' Stop because more than one row is selected.
                ParentForm.ShowMessage("Please select only one row to edit before using the 'Edit' button.")
                Exit Sub
            End If
        End If

        If NewItem = False Then
            ' Create table adapters to populate related tables.
            Dim MediaGroupMemberAdapter As New DataSetMediaTableAdapters.MediaGroupMemberTableAdapter
            ' Set the sql connection of the new adapters.
            Dim SqlCon As New SqlClient.SqlConnection(My.Settings.DBConnection)
            MediaGroupMemberAdapter.Connection = SqlCon

            ' Populate tables needed for adding new rows and editing existing rows.
            Try
                MediaGroupMemberAdapter.Fill(MediaGrouping_DataSet.Tables("MediaGroupMember"))
            Catch ex As Exception
                CType(TopLevelControl, BaseForm).ShowMessage _
                ("Rats!  Some data failed to load correctly." & vbCrLf & vbCrLf _
                & LiquidShell.LiquidAgent.GetErrorMessage(ex), "Data Load Error", MessageBoxIcon.Error)
            Finally
                MediaGroupMemberAdapter.Dispose()
            End Try
            ' Dispose the connection.
            SqlCon.Dispose()
        End If

        ' Open the form to edit the item.
        AddChild(New SubFormMediaGroup(New MediaGroup(MediaGrouping_GridBindingSource, NewItem)))

    End Sub



    Private Sub OpenMediaGroupChannelDetailSubform(ByVal NewItem As Boolean)

        If NewItem = False Then
            ' User clicked the Edit button.
            Dim ParentForm As BaseForm = CType(TopLevelControl, BaseForm)
            If GridMediaChannels.SelectedRows.Count < 1 Then
                ' Stop because no rows are selected.
                ParentForm.ShowMessage("Please select the row to edit before using the 'Edit' button.")
                Exit Sub
            ElseIf GridMediaChannels.SelectedRows.Count > 1 Then
                ' Stop because more than one row is selected.
                ParentForm.ShowMessage("Please select only one row to edit before using the 'Edit' button.")
                Exit Sub
            End If
        End If

        If NewItem = False Then
            ' Create table adapters to populate related tables.
            Dim MediaChannelGroupMemberAdapter As New DataSetMediaTableAdapters.MediaChannelGroupMemberTableAdapter
            ' Set the sql connection of the new adapters.
            Dim SqlCon As New SqlClient.SqlConnection(My.Settings.DBConnection)
            MediaChannelGroupMemberAdapter.Connection = SqlCon

            ' Populate tables needed for adding new rows and editing existing rows.
            Try
                MediaChannelGroupMemberAdapter.Fill(MediaChannel_DataSet.Tables("MediaChannelGroupMember"))
            Catch ex As Exception
                CType(TopLevelControl, BaseForm).ShowMessage _
                ("Rats!  Some data failed to load correctly." & vbCrLf & vbCrLf _
                & LiquidShell.LiquidAgent.GetErrorMessage(ex), "Data Load Error", MessageBoxIcon.Error)
            Finally
                MediaChannelGroupMemberAdapter.Dispose()
            End Try
            ' Dispose the connection.
            SqlCon.Dispose()
        End If

        ' Open the form to edit the item.
        AddChild(New SubformMediaChannelGroup(New MediaChannelGroup(MediaChannel_GridBindingSource, NewItem)))

    End Sub

    Public Function GetMediaRulesList(ByVal MediaID As Integer, ByVal Con As String) As DataTable
        Try
            Dim tblMediaRules As DataTable = New DataTable
            Dim conn As SqlConnection = New SqlConnection("Data Source=primenovasql\Live;Initial Catalog=NovaDB;User ID=NovaApp;Password=******************;Connect Timeout=0")
            Dim query As String = String.Format("exec Media.proc_sel_GetRulesForSelectedMedia @MediaID = {0}", MediaID)
            Dim cmd As SqlCommand = New SqlCommand(query, conn)
            conn.Open()
            ' create data adapter
            Dim da As SqlDataAdapter = New SqlDataAdapter(cmd)
            ' this will query your database and return the result to your datatable
            da.Fill(tblMediaRules)

            Return tblMediaRules

        Catch ex As Exception
            CType(TopLevelControl, BaseForm).ShowMessage _
                ("Rats!  Some data failed to load correctly." & vbCrLf & vbCrLf _
                & LiquidShell.LiquidAgent.GetErrorMessage(ex), "Data Load Error", MessageBoxIcon.Error)
            Return Nothing
        End Try
    End Function

    Private Sub ButtonAddMediaGroup_Click(sender As Object, e As EventArgs) Handles ButtonAddMediaGroup.Click
        OpenMediaGroupDetailSubform(True)
    End Sub

    Private Sub ButtonEditMediaGroup_Click(sender As Object, e As EventArgs) Handles ButtonEditMediaGroup.Click
        OpenMediaGroupDetailSubform(False)
    End Sub

    Private Sub MediaGroup_CellMouseDoubleClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) Handles GridMediaGrouping.CellMouseDoubleClick
        If e.RowIndex > -1 Then
            ' The row double-clicked is NOT the column header row. Handle the event.
            ButtonEditMediaGroup_Click(sender, e)
        End If
    End Sub

    Private Sub ButtonDeleteMediaGroup_Click(sender As Object, e As EventArgs) Handles ButtonDeleteMediaGroup.Click

        ' Load the BurstCount table with data.
        Dim MediaGroupAdapter As New DataSetMediaTableAdapters.MediaGroupTableAdapter
        Dim SqlCon As New SqlClient.SqlConnection(My.Settings.DBConnection)
        MediaGroupAdapter.Connection = SqlCon

        Try
            MediaGroupAdapter.Fill(Media_DataSet.Tables("MediaGroup"))
        Catch ex As Exception
            CType(TopLevelControl, BaseForm).ShowMessage _
            ("Rats!  Some data failed to load correctly." & vbCrLf & vbCrLf _
            & LiquidShell.LiquidAgent.GetErrorMessage(ex), "Data Load Error", MessageBoxIcon.Error)
            Exit Sub
        Finally
            MediaGroupAdapter.Dispose()
            SqlCon.Dispose()
        End Try

        ' Delete the selcted rows.
        MediaGroup.Delete(GridMediaGrouping, My.Settings.DBConnection)
    End Sub


    Private Sub ButtonAddMediaChannels_Click(sender As Object, e As EventArgs) Handles ButtonAddMediaChannels.Click
        OpenMediaGroupChannelDetailSubform(True)
    End Sub

    Private Sub ButtonEditMediaChannels_Click(sender As Object, e As EventArgs) Handles ButtonEditMediaChannels.Click
        OpenMediaGroupChannelDetailSubform(False)
    End Sub

    Private Sub MediaChannels_CellMouseDoubleClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) Handles GridMediaChannels.CellMouseDoubleClick
        If e.RowIndex > -1 Then
            ' The row double-clicked is NOT the column header row. Handle the event.
            ButtonEditMediaChannels_Click(sender, e)
        End If
    End Sub

    Private Sub ButtonDeleteMediaChannels_Click(sender As Object, e As EventArgs) Handles ButtonDeleteMediaChannels.Click
        ' Load the BurstCount table with data.
        Dim MediaChannelAdapter As New DataSetMediaTableAdapters.MediaChannelTableAdapter
        Dim SqlCon As New SqlClient.SqlConnection(My.Settings.DBConnection)
        MediaChannelAdapter.Connection = SqlCon

        Try
            MediaChannelAdapter.Fill(MediaChannel_DataSet.Tables("MediaChannel"))
        Catch ex As Exception
            CType(TopLevelControl, BaseForm).ShowMessage _
            ("Rats!  Some data failed to load correctly." & vbCrLf & vbCrLf _
            & LiquidShell.LiquidAgent.GetErrorMessage(ex), "Data Load Error", MessageBoxIcon.Error)
            Exit Sub
        Finally
            MediaChannelAdapter.Dispose()
            SqlCon.Dispose()
        End Try

        ' Delete the selcted rows.
        MediaChannelGroup.Delete(GridMediaChannels, My.Settings.DBConnection)
    End Sub

#End Region

End Class
