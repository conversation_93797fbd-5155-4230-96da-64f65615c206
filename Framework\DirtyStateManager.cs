﻿using Framework.Controls.Data;
using Framework.Forms;
using Framework.Surfaces;
using System;
using System.Windows.Forms;

namespace Framework
{
    public class DirtyStateManager
    {
        private object CleanValue;
        private bool StateInitalizationCompleted = false;


        private Control _ManagedControl;
        public Control ManagedControl
        {
            get { return _ManagedControl; }
        }

        public DirtyStateManager(Control managedcontrol)
        {
            _ManagedControl = managedcontrol;
        }

        public void InitializeState()
        {
            if (StateInitalizationCompleted == false)
            {
                SetInitialValue();
                SubscribeToEventsAffectingTheIsDirtyProperty();
                StateInitalizationCompleted = true;
            }
        }

        private void SetInitialValue()
        {
            if (ManagedControl is TextDataBox)
            {
                TextDataBox textdatabox = (TextDataBox)ManagedControl;
                CleanValue = textdatabox.Value;
            }
            else
            {
                if (ManagedControl is CheckDataBox)
                {
                    CheckDataBox checkdatabox = (CheckDataBox)ManagedControl;
                    checkdatabox.Value = false;
                    CleanValue = checkdatabox.Value;
                }
                else
                {
                    if (ManagedControl is DataBox)
                    {
                        DataBox databox = (DataBox)ManagedControl;
                        databox.Value = null;
                        CleanValue = databox.Value;
                    }
                }
            }
        }

        private void SubscribeToEventsAffectingTheIsDirtyProperty()
        {
            if (ManagedControl is DataBox)
            {
                DataBox databox = (DataBox)ManagedControl;
                databox.ValueChanged += ManagedControl_ValueChanged;
                databox.DataBindings.CollectionChanged += DataBindings_CollectionChanged;
            }
            else
            {
                if (ManagedControl is Surface | ManagedControl is DataForm)
                {
                    SubscribeToIsDirtyChangedEventOfChildControls(ManagedControl.Controls);
                }
            }
        }

        private void DataBindings_CollectionChanged(object sender, System.ComponentModel.CollectionChangeEventArgs e)
        {
            AcceptChanges();
        }

        private void SubscribeToIsDirtyChangedEventOfChildControls(Control.ControlCollection controls)
        {
            for (int i = 0; i < controls.Count; i++)
            {
                Control control = controls[i];
                if (control is DataBox)
                {
                    ((DataBox)control).DirtyStateManager.IsDirtyChanged += ChildControlDirtyStateManager_IsDirtyChanged;
                }
                else
                {
                    if (control is Surface)
                    {
                        ((Surface)control).DirtyStateManager.IsDirtyChanged += ChildControlDirtyStateManager_IsDirtyChanged;
                    }
                    else
                    {
                        if (control.HasChildren)
                        {
                            SubscribeToIsDirtyChangedEventOfChildControls(control.Controls);
                        }
                    }
                }
            }
        }

        private void ChildControlDirtyStateManager_IsDirtyChanged(object sender, EventArgs e)
        {
            DirtyStateManager manager = (DirtyStateManager)sender;
            if (manager.IsDirty)
            {
                IsDirty = true;
            }
            else
            {
                IsDirty = DirtyChildControlFound(ManagedControl.Controls);
            }
        }

        internal bool DirtyChildControlFound(Control.ControlCollection controls)
        {
            bool dirtychildfound = false;

            for (int i = 0; i < controls.Count; i++)
            {
                Control childcontrol = controls[i];
                if (childcontrol is DataBox)
                {
                    if (((DataBox)childcontrol).DirtyStateManager.IsDirty)
                    {
                        dirtychildfound = true;
                        break;
                    }
                }
                else
                {
                    if (childcontrol is Surface)
                    {
                        if (((Surface)childcontrol).DirtyStateManager.IsDirty)
                        {
                            dirtychildfound = true;
                            break;
                        }
                    }
                    else
                    {
                        if (childcontrol.HasChildren)
                        {
                            dirtychildfound = DirtyChildControlFound(childcontrol.Controls);
                            if (dirtychildfound)
                            {
                                break;
                            }
                        }
                    }
                }
            }

            return dirtychildfound;
        }

        private void ManagedControl_ValueChanged(object sender, EventArgs e)
        {
            bool isdirty = true;
            DataBox databox = (DataBox)ManagedControl;
            if (databox.Value == null)
            {
                if (CleanValue == null)
                {
                    isdirty = false;
                }
            }
            else
            {
                if (CleanValue != null)
                {
                    if (databox.Value.Equals(CleanValue))
                    {
                        isdirty = false;
                    }
                }
            }
            IsDirty = isdirty;
        }

        public event EventHandler IsDirtyChanged;
        private void OnIsDirtyChanged(EventArgs e)
        {
            IsDirtyChanged?.Invoke(this, e);
        }

        private bool _IsDirty = false;
        public bool IsDirty
        {
            get { return _IsDirty; }
            set
            {
                if (_IsDirty != value)
                {
                    _IsDirty = value;
                    OnIsDirtyChanged(EventArgs.Empty);
                }
            }
        }

        public void AcceptChanges()
        {
            if (ManagedControl is DataBox)
            {
                CleanValue = ((DataBox)ManagedControl).Value;
                IsDirty = false;
            }
            else
            {
                if (ManagedControl.HasChildren)
                {
                    AcceptChildControlChanges(ManagedControl.Controls);
                }
            }
        }

        public void RejectChanges()
        {
            if (ManagedControl is DataBox)
            {
                ((DataBox)ManagedControl).Value = CleanValue;
            }
            else
            {
                if (ManagedControl.HasChildren)
                {
                    RejectChildControlChanges(ManagedControl.Controls);
                }
            }
        }

        private void AcceptChildControlChanges(Control.ControlCollection controls)
        {
            for (int i = 0; i < controls.Count; i++)
            {
                Control control = controls[i];
                if (control is DataBox)
                {
                    ((DataBox)control).DirtyStateManager.AcceptChanges();
                }
                else
                {
                    if (control.HasChildren)
                    {
                        AcceptChildControlChanges(control.Controls);
                    }
                }
            }
        }

        private void RejectChildControlChanges(Control.ControlCollection controls)
        {
            for (int i = 0; i < controls.Count; i++)
            {
                Control control = controls[i];
                if (control is DataBox)
                {
                    ((DataBox)control).DirtyStateManager.RejectChanges();
                }
                else
                {
                    if (control.HasChildren)
                    {
                        RejectChildControlChanges(control.Controls);
                    }
                }
            }
        }

    }
}
