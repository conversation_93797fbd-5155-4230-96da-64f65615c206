﻿using DataAccess;

namespace DataService.Security
{
    internal class GetRolesOfMemberCommandExecutor : CommandExecutor<GetRolesOfMemberCommand>
    {
        public override void Execute(GetRolesOfMemberCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.GetRolesOfMember))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.AddInputParameter("userid", command.UserId);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    command.Table = storedprocedure.OutputTable;
                }
            }
        }
    }
}
