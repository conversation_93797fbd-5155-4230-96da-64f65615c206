<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class SubformContract
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformContract))
        Dim SuperToolTip1 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem1 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem1 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip2 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem2 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem2 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle5 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim SuperToolTip3 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem3 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem3 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip4 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem4 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem4 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle6 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle7 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle10 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle8 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle9 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim SuperToolTip5 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem5 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem5 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip6 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem6 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem6 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle11 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle12 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle14 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle13 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle15 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle16 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle19 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle17 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle18 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle20 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle21 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle24 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle22 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle23 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle25 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle26 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle30 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle27 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle28 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle29 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle4 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonSave = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.TabControl = New DevExpress.XtraTab.XtraTabControl()
        Me.TabPageSummary = New DevExpress.XtraTab.XtraTabPage()
        Me.PanelSummary = New System.Windows.Forms.Panel()
        Me.TableLayoutPanelSummary = New System.Windows.Forms.TableLayoutPanel()
        Me.PanelContractProperties = New System.Windows.Forms.Panel()
        Me.HyperlinkAccountManager = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkClient = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkContractProposalHeat = New DevExpress.XtraEditors.LabelControl()
        Me.LabelClient = New DevExpress.XtraEditors.LabelControl()
        Me.LabelContractProposalHeat = New DevExpress.XtraEditors.LabelControl()
        Me.LabelContractProperties = New DevExpress.XtraEditors.LabelControl()
        Me.LabelAccountManager = New DevExpress.XtraEditors.LabelControl()
        Me.PanelSignature = New System.Windows.Forms.Panel()
        Me.LabelSignedBy = New DevExpress.XtraEditors.LabelControl()
        Me.PictureSignatureStatus = New DevExpress.XtraEditors.PictureEdit()
        Me.LabelSignature = New DevExpress.XtraEditors.LabelControl()
        Me.LabelCancelledBy = New DevExpress.XtraEditors.LabelControl()
        Me.LabelCancelledByValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelSignedByValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelCancellationDate = New DevExpress.XtraEditors.LabelControl()
        Me.LabelCancellationDateValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelSignatureDate = New DevExpress.XtraEditors.LabelControl()
        Me.LabelSignatureDateValue = New DevExpress.XtraEditors.LabelControl()
        Me.PanelAgencySummary = New System.Windows.Forms.Panel()
        Me.LabelAgencyDetailsSummary = New DevExpress.XtraEditors.LabelControl()
        Me.LabelAgencySummary = New DevExpress.XtraEditors.LabelControl()
        Me.LabelAgencySummaryValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelAgencyCommission = New DevExpress.XtraEditors.LabelControl()
        Me.LabelAgencyCommissionValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelCommissionSummary = New DevExpress.XtraEditors.LabelControl()
        Me.LabelCommissionSummaryValue = New DevExpress.XtraEditors.LabelControl()
        Me.TabPageAgency = New DevExpress.XtraTab.XtraTabPage()
        Me.PanelAgency = New System.Windows.Forms.Panel()
        Me.PanelCommOptions = New System.Windows.Forms.Panel()
        Me.LabelControl9 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelRevenue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl5 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelCommLabel = New DevExpress.XtraEditors.LabelControl()
        Me.LabelNetRental = New DevExpress.XtraEditors.LabelControl()
        Me.LabelComm = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl4 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl3 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelTotalRental = New DevExpress.XtraEditors.LabelControl()
        Me.LabelDiscount = New DevExpress.XtraEditors.LabelControl()
        Me.LabelCommExample = New DevExpress.XtraEditors.LabelControl()
        Me.LabelAgency = New DevExpress.XtraEditors.LabelControl()
        Me.LabelCommission = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkAgency = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditCommission = New DevExpress.XtraEditors.TextEdit()
        Me.CheckEditPrintAgencyComm = New DevExpress.XtraEditors.CheckEdit()
        Me.CheckEditPercentageOfNetRental = New DevExpress.XtraEditors.CheckEdit()
        Me.CheckEditPercentageOf = New DevExpress.XtraEditors.CheckEdit()
        Me.CheckEditApplyAgencyComm = New DevExpress.XtraEditors.CheckEdit()
        Me.LabelAgencyDetailsTitle = New DevExpress.XtraEditors.LabelControl()
        Me.TabPageBursts = New DevExpress.XtraTab.XtraTabPage()
        Me.PanelBursts = New System.Windows.Forms.Panel()
        Me.PanelBurstInfo1 = New System.Windows.Forms.Panel()
        Me.LabelFirstWeek = New DevExpress.XtraEditors.LabelControl()
        Me.LabelFirstWeekValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelLastWeekValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelRental = New DevExpress.XtraEditors.LabelControl()
        Me.LabelLastWeek = New DevExpress.XtraEditors.LabelControl()
        Me.LabelRentalValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelTotalWeeksValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelTotalWeeks = New DevExpress.XtraEditors.LabelControl()
        Me.PanelBurstInfo2 = New System.Windows.Forms.Panel()
        Me.LabelMediaServices = New DevExpress.XtraEditors.LabelControl()
        Me.LabelMediaServicesValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelBrands = New DevExpress.XtraEditors.LabelControl()
        Me.LabelBrandsValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelCategories = New DevExpress.XtraEditors.LabelControl()
        Me.LabelCategoriesValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelChains = New DevExpress.XtraEditors.LabelControl()
        Me.LabelChainsValue = New DevExpress.XtraEditors.LabelControl()
        Me.GroupControlBursts = New DevExpress.XtraEditors.GroupControl()
        Me.ButtonEditBurst = New DevExpress.XtraEditors.SimpleButton()
        Me.ImageList16x16 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonDeleteBurst = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAddBurst = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelSearchBurst = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearchBurst = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearchBursts = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearchBurst = New DevExpress.XtraEditors.PictureEdit()
        Me.GridBursts = New System.Windows.Forms.DataGridView()
        Me.TabPageProduction = New DevExpress.XtraTab.XtraTabPage()
        Me.PanelProduction = New System.Windows.Forms.Panel()
        Me.LabelProductionChargesValue = New DevExpress.XtraEditors.LabelControl()
        Me.GroupControlProduction = New DevExpress.XtraEditors.GroupControl()
        Me.ButtonEditProduction = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonRemoveProduction = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAddProduction = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelSearchProduction = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearchProduction = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearchProduction = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearchProduction = New DevExpress.XtraEditors.PictureEdit()
        Me.GridProduction = New System.Windows.Forms.DataGridView()
        Me.ProductionDescriptionColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.ProductionQuantityColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.ProductionBrandNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.ProductionAmountColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.ProductionNotesColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.TabPageMiscellaneousCharges = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.LabelMiscellaneousChargesValue = New DevExpress.XtraEditors.LabelControl()
        Me.GroupControlMiscellaneousCharges = New DevExpress.XtraEditors.GroupControl()
        Me.ButtonEditMiscellaneousCharge = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonRemoveMiscellaneousCharge = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAddMiscellaneousCharge = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelSearchMiscellaneousCharge = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearchMiscellaneousCharge = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearchMiscellaneousCharges = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearchMiscellaneousCharge = New DevExpress.XtraEditors.PictureEdit()
        Me.GridMiscellaneousCharges = New System.Windows.Forms.DataGridView()
        Me.MiscellaneousChargeNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.MiscellaneousChargeAmountColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.TabPageMediaCosts = New DevExpress.XtraTab.XtraTabPage()
        Me.PanelMediaCosts = New System.Windows.Forms.Panel()
        Me.GroupControlCostEstimates = New DevExpress.XtraEditors.GroupControl()
        Me.LabelTotalCEAmount = New DevExpress.XtraEditors.LabelControl()
        Me.ButtonEditCostEstimate = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonDeleteCostEstimate = New DevExpress.XtraEditors.SimpleButton()
        Me.GridCostEstimates = New System.Windows.Forms.DataGridView()
        Me.DataGridViewTextBoxColumn4 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.ColumnMediaName = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.MediaId = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.ColumnCostEstimateAmount = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.ButtonAddCostEstimate = New DevExpress.XtraEditors.SimpleButton()
        Me.GroupControlInvoices = New DevExpress.XtraEditors.GroupControl()
        Me.LabelTotalInvoiceAmount = New DevExpress.XtraEditors.LabelControl()
        Me.ButtonEditInvoiceNumber = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonDeleteInvoiceNumber = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAddInvoiceNumber = New DevExpress.XtraEditors.SimpleButton()
        Me.GridInvoiceNumbers = New System.Windows.Forms.DataGridView()
        Me.DataGridViewTextBoxColumn1 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.MediaIdInvoices = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.ColumnMediaNameInvoices = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.ColumnInvoiceAmount = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.GroupControlMediaCosts = New DevExpress.XtraEditors.GroupControl()
        Me.LabelTotalMediaCostSystemCalculated = New DevExpress.XtraEditors.LabelControl()
        Me.LabelTotalMediaCostUserInputted = New DevExpress.XtraEditors.LabelControl()
        Me.ButtonEditMediaCost = New DevExpress.XtraEditors.SimpleButton()
        Me.GridMediaCosts = New System.Windows.Forms.DataGridView()
        Me.MeidaCostMediaNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.MediaCostSystemCalculatedColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.MediaCostUserInputedCostColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.TabPageErrors = New DevExpress.XtraTab.XtraTabPage()
        Me.PanelErrors = New System.Windows.Forms.Panel()
        Me.MemoEditErrors = New DevExpress.XtraEditors.MemoEdit()
        Me.LabelErrors = New DevExpress.XtraEditors.LabelControl()
        Me.MediaNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.ChainNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.CategoryListColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.InstallCategories = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.InstallStoreQtyColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.BurstWeeksColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.FirstWeekColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.LastWeekColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.BrandNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.ProductNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        CType(Me.TabControl, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabControl.SuspendLayout()
        Me.TabPageSummary.SuspendLayout()
        Me.PanelSummary.SuspendLayout()
        Me.TableLayoutPanelSummary.SuspendLayout()
        Me.PanelContractProperties.SuspendLayout()
        Me.PanelSignature.SuspendLayout()
        CType(Me.PictureSignatureStatus.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelAgencySummary.SuspendLayout()
        Me.TabPageAgency.SuspendLayout()
        Me.PanelAgency.SuspendLayout()
        Me.PanelCommOptions.SuspendLayout()
        CType(Me.TextEditCommission.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditPrintAgencyComm.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditPercentageOfNetRental.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditPercentageOf.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditApplyAgencyComm.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabPageBursts.SuspendLayout()
        Me.PanelBursts.SuspendLayout()
        Me.PanelBurstInfo1.SuspendLayout()
        Me.PanelBurstInfo2.SuspendLayout()
        CType(Me.GroupControlBursts, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlBursts.SuspendLayout()
        CType(Me.TextEditSearchBurst.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearchBursts.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearchBurst.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridBursts, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabPageProduction.SuspendLayout()
        Me.PanelProduction.SuspendLayout()
        CType(Me.GroupControlProduction, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlProduction.SuspendLayout()
        CType(Me.TextEditSearchProduction.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearchProduction.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearchProduction.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridProduction, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabPageMiscellaneousCharges.SuspendLayout()
        Me.Panel1.SuspendLayout()
        CType(Me.GroupControlMiscellaneousCharges, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlMiscellaneousCharges.SuspendLayout()
        CType(Me.TextEditSearchMiscellaneousCharge.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearchMiscellaneousCharges.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearchMiscellaneousCharge.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridMiscellaneousCharges, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabPageMediaCosts.SuspendLayout()
        Me.PanelMediaCosts.SuspendLayout()
        CType(Me.GroupControlCostEstimates, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlCostEstimates.SuspendLayout()
        CType(Me.GridCostEstimates, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControlInvoices, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlInvoices.SuspendLayout()
        CType(Me.GridInvoiceNumbers, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControlMediaCosts, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlMediaCosts.SuspendLayout()
        CType(Me.GridMediaCosts, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabPageErrors.SuspendLayout()
        Me.PanelErrors.SuspendLayout()
        CType(Me.MemoEditErrors.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!)
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Margin = New System.Windows.Forms.Padding(5, 5, 5, 21)
        Me.LabelTitle.Size = New System.Drawing.Size(130, 37)
        Me.LabelTitle.TabIndex = 0
        Me.LabelTitle.Text = "Contract"
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        Me.ImageList24x24.Images.SetKeyName(3, "print.png")
        Me.ImageList24x24.Images.SetKeyName(4, "remove.png")
        '
        'ButtonSave
        '
        Me.ButtonSave.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonSave.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonSave.Appearance.Options.UseFont = True
        Me.ButtonSave.ImageIndex = 1
        Me.ButtonSave.ImageList = Me.ImageList24x24
        Me.ButtonSave.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonSave.Location = New System.Drawing.Point(778, 666)
        Me.ButtonSave.LookAndFeel.SkinName = "Black"
        Me.ButtonSave.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonSave.Margin = New System.Windows.Forms.Padding(4, 16, 4, 4)
        Me.ButtonSave.Name = "ButtonSave"
        Me.ButtonSave.Size = New System.Drawing.Size(129, 37)
        Me.ButtonSave.TabIndex = 5
        Me.ButtonSave.Text = "Save"
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(914, 666)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Margin = New System.Windows.Forms.Padding(4, 16, 4, 4)
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(129, 37)
        Me.ButtonCancel.TabIndex = 6
        Me.ButtonCancel.Text = "Cancel"
        '
        'TabControl
        '
        Me.TabControl.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TabControl.AppearancePage.Header.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TabControl.AppearancePage.Header.Options.UseFont = True
        Me.TabControl.Location = New System.Drawing.Point(15, 75)
        Me.TabControl.LookAndFeel.SkinName = "Black"
        Me.TabControl.LookAndFeel.UseDefaultLookAndFeel = False
        Me.TabControl.Margin = New System.Windows.Forms.Padding(4)
        Me.TabControl.Name = "TabControl"
        Me.TabControl.SelectedTabPage = Me.TabPageSummary
        Me.TabControl.Size = New System.Drawing.Size(1027, 571)
        Me.TabControl.TabIndex = 1
        Me.TabControl.TabPages.AddRange(New DevExpress.XtraTab.XtraTabPage() {Me.TabPageSummary, Me.TabPageAgency, Me.TabPageBursts, Me.TabPageProduction, Me.TabPageMiscellaneousCharges, Me.TabPageMediaCosts, Me.TabPageErrors})
        '
        'TabPageSummary
        '
        Me.TabPageSummary.Controls.Add(Me.PanelSummary)
        Me.TabPageSummary.Margin = New System.Windows.Forms.Padding(4)
        Me.TabPageSummary.Name = "TabPageSummary"
        Me.TabPageSummary.Size = New System.Drawing.Size(1021, 539)
        Me.TabPageSummary.Text = "Summary"
        '
        'PanelSummary
        '
        Me.PanelSummary.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelSummary.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelSummary.Controls.Add(Me.TableLayoutPanelSummary)
        Me.PanelSummary.Location = New System.Drawing.Point(4, 4)
        Me.PanelSummary.Margin = New System.Windows.Forms.Padding(4)
        Me.PanelSummary.Name = "PanelSummary"
        Me.PanelSummary.Padding = New System.Windows.Forms.Padding(12)
        Me.PanelSummary.Size = New System.Drawing.Size(1012, 527)
        Me.PanelSummary.TabIndex = 0
        '
        'TableLayoutPanelSummary
        '
        Me.TableLayoutPanelSummary.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanelSummary.ColumnCount = 3
        Me.TableLayoutPanelSummary.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50.0!))
        Me.TableLayoutPanelSummary.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 19.0!))
        Me.TableLayoutPanelSummary.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50.0!))
        Me.TableLayoutPanelSummary.Controls.Add(Me.PanelContractProperties, 0, 0)
        Me.TableLayoutPanelSummary.Controls.Add(Me.PanelSignature, 0, 4)
        Me.TableLayoutPanelSummary.Controls.Add(Me.PanelAgencySummary, 2, 4)
        Me.TableLayoutPanelSummary.Location = New System.Drawing.Point(12, 12)
        Me.TableLayoutPanelSummary.Margin = New System.Windows.Forms.Padding(0)
        Me.TableLayoutPanelSummary.Name = "TableLayoutPanelSummary"
        Me.TableLayoutPanelSummary.RowCount = 5
        Me.TableLayoutPanelSummary.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 196.0!))
        Me.TableLayoutPanelSummary.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanelSummary.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 126.0!))
        Me.TableLayoutPanelSummary.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanelSummary.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanelSummary.Size = New System.Drawing.Size(989, 503)
        Me.TableLayoutPanelSummary.TabIndex = 0
        '
        'PanelContractProperties
        '
        Me.PanelContractProperties.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanelSummary.SetColumnSpan(Me.PanelContractProperties, 3)
        Me.PanelContractProperties.Controls.Add(Me.HyperlinkAccountManager)
        Me.PanelContractProperties.Controls.Add(Me.HyperlinkClient)
        Me.PanelContractProperties.Controls.Add(Me.HyperlinkContractProposalHeat)
        Me.PanelContractProperties.Controls.Add(Me.LabelClient)
        Me.PanelContractProperties.Controls.Add(Me.LabelContractProposalHeat)
        Me.PanelContractProperties.Controls.Add(Me.LabelContractProperties)
        Me.PanelContractProperties.Controls.Add(Me.LabelAccountManager)
        Me.PanelContractProperties.Location = New System.Drawing.Point(0, 0)
        Me.PanelContractProperties.Margin = New System.Windows.Forms.Padding(0)
        Me.PanelContractProperties.Name = "PanelContractProperties"
        Me.PanelContractProperties.Size = New System.Drawing.Size(989, 196)
        Me.PanelContractProperties.TabIndex = 0
        '
        'HyperlinkAccountManager
        '
        Me.HyperlinkAccountManager.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.HyperlinkAccountManager.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkAccountManager.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkAccountManager.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkAccountManager.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkAccountManager.AutoEllipsis = True
        Me.HyperlinkAccountManager.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.HyperlinkAccountManager.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkAccountManager.Location = New System.Drawing.Point(185, 43)
        Me.HyperlinkAccountManager.Margin = New System.Windows.Forms.Padding(4)
        Me.HyperlinkAccountManager.Name = "HyperlinkAccountManager"
        Me.HyperlinkAccountManager.Size = New System.Drawing.Size(804, 17)
        Me.HyperlinkAccountManager.TabIndex = 2
        Me.HyperlinkAccountManager.Text = "Select..."
        '
        'HyperlinkClient
        '
        Me.HyperlinkClient.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.HyperlinkClient.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkClient.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkClient.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkClient.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkClient.AutoEllipsis = True
        Me.HyperlinkClient.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.HyperlinkClient.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkClient.Location = New System.Drawing.Point(185, 68)
        Me.HyperlinkClient.Margin = New System.Windows.Forms.Padding(4)
        Me.HyperlinkClient.Name = "HyperlinkClient"
        Me.HyperlinkClient.Size = New System.Drawing.Size(804, 17)
        Me.HyperlinkClient.TabIndex = 4
        Me.HyperlinkClient.Text = "Select..."
        '
        'HyperlinkContractProposalHeat
        '
        Me.HyperlinkContractProposalHeat.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.HyperlinkContractProposalHeat.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkContractProposalHeat.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkContractProposalHeat.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkContractProposalHeat.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkContractProposalHeat.AutoEllipsis = True
        Me.HyperlinkContractProposalHeat.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.HyperlinkContractProposalHeat.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkContractProposalHeat.Location = New System.Drawing.Point(185, 93)
        Me.HyperlinkContractProposalHeat.Margin = New System.Windows.Forms.Padding(4)
        Me.HyperlinkContractProposalHeat.Name = "HyperlinkContractProposalHeat"
        Me.HyperlinkContractProposalHeat.Size = New System.Drawing.Size(804, 17)
        Me.HyperlinkContractProposalHeat.TabIndex = 4
        Me.HyperlinkContractProposalHeat.Text = "Select..."
        '
        'LabelClient
        '
        Me.LabelClient.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelClient.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelClient.Location = New System.Drawing.Point(4, 68)
        Me.LabelClient.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelClient.Name = "LabelClient"
        Me.LabelClient.Size = New System.Drawing.Size(45, 17)
        Me.LabelClient.TabIndex = 3
        Me.LabelClient.Text = "Client:"
        '
        'LabelContractProposalHeat
        '
        Me.LabelContractProposalHeat.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelContractProposalHeat.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelContractProposalHeat.Location = New System.Drawing.Point(4, 93)
        Me.LabelContractProposalHeat.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelContractProposalHeat.Name = "LabelContractProposalHeat"
        Me.LabelContractProposalHeat.Size = New System.Drawing.Size(103, 17)
        Me.LabelContractProposalHeat.TabIndex = 1
        Me.LabelContractProposalHeat.Text = "Proposal Heat:"
        '
        'LabelContractProperties
        '
        Me.LabelContractProperties.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelContractProperties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelContractProperties.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelContractProperties.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelContractProperties.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelContractProperties.LineVisible = True
        Me.LabelContractProperties.Location = New System.Drawing.Point(4, 4)
        Me.LabelContractProperties.Margin = New System.Windows.Forms.Padding(4, 4, 4, 12)
        Me.LabelContractProperties.Name = "LabelContractProperties"
        Me.LabelContractProperties.Size = New System.Drawing.Size(981, 24)
        Me.LabelContractProperties.TabIndex = 0
        Me.LabelContractProperties.Text = "Contract Properties "
        '
        'LabelAccountManager
        '
        Me.LabelAccountManager.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelAccountManager.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelAccountManager.Location = New System.Drawing.Point(4, 43)
        Me.LabelAccountManager.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelAccountManager.Name = "LabelAccountManager"
        Me.LabelAccountManager.Size = New System.Drawing.Size(127, 17)
        Me.LabelAccountManager.TabIndex = 1
        Me.LabelAccountManager.Text = "Account Manager:"
        '
        'PanelSignature
        '
        Me.PanelSignature.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelSignature.Controls.Add(Me.LabelSignedBy)
        Me.PanelSignature.Controls.Add(Me.PictureSignatureStatus)
        Me.PanelSignature.Controls.Add(Me.LabelSignature)
        Me.PanelSignature.Controls.Add(Me.LabelCancelledBy)
        Me.PanelSignature.Controls.Add(Me.LabelCancelledByValue)
        Me.PanelSignature.Controls.Add(Me.LabelSignedByValue)
        Me.PanelSignature.Controls.Add(Me.LabelCancellationDate)
        Me.PanelSignature.Controls.Add(Me.LabelCancellationDateValue)
        Me.PanelSignature.Controls.Add(Me.LabelSignatureDate)
        Me.PanelSignature.Controls.Add(Me.LabelSignatureDateValue)
        Me.PanelSignature.Location = New System.Drawing.Point(0, 362)
        Me.PanelSignature.Margin = New System.Windows.Forms.Padding(0)
        Me.PanelSignature.Name = "PanelSignature"
        Me.PanelSignature.Size = New System.Drawing.Size(485, 141)
        Me.PanelSignature.TabIndex = 2
        '
        'LabelSignedBy
        '
        Me.LabelSignedBy.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.LabelSignedBy.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSignedBy.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelSignedBy.Cursor = System.Windows.Forms.Cursors.Hand
        Me.LabelSignedBy.Location = New System.Drawing.Point(4, 43)
        Me.LabelSignedBy.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelSignedBy.Name = "LabelSignedBy"
        Me.LabelSignedBy.Size = New System.Drawing.Size(77, 17)
        Me.LabelSignedBy.TabIndex = 1
        Me.LabelSignedBy.Text = "Signed By:"
        '
        'PictureSignatureStatus
        '
        Me.PictureSignatureStatus.AllowDrop = True
        Me.PictureSignatureStatus.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureSignatureStatus.Location = New System.Drawing.Point(399, 43)
        Me.PictureSignatureStatus.Margin = New System.Windows.Forms.Padding(4)
        Me.PictureSignatureStatus.Name = "PictureSignatureStatus"
        Me.PictureSignatureStatus.Properties.AllowFocused = False
        Me.PictureSignatureStatus.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureSignatureStatus.Properties.Appearance.Options.UseBackColor = True
        Me.PictureSignatureStatus.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureSignatureStatus.Size = New System.Drawing.Size(82, 84)
        Me.PictureSignatureStatus.TabIndex = 9
        '
        'LabelSignature
        '
        Me.LabelSignature.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelSignature.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSignature.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelSignature.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelSignature.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelSignature.LineVisible = True
        Me.LabelSignature.Location = New System.Drawing.Point(4, 4)
        Me.LabelSignature.Margin = New System.Windows.Forms.Padding(4, 4, 4, 12)
        Me.LabelSignature.Name = "LabelSignature"
        Me.LabelSignature.Size = New System.Drawing.Size(477, 24)
        Me.LabelSignature.TabIndex = 0
        Me.LabelSignature.Text = "Signature Status"
        '
        'LabelCancelledBy
        '
        Me.LabelCancelledBy.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCancelledBy.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCancelledBy.Location = New System.Drawing.Point(4, 93)
        Me.LabelCancelledBy.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelCancelledBy.Name = "LabelCancelledBy"
        Me.LabelCancelledBy.Size = New System.Drawing.Size(94, 17)
        Me.LabelCancelledBy.TabIndex = 5
        Me.LabelCancelledBy.Text = "Cancelled By:"
        '
        'LabelCancelledByValue
        '
        Me.LabelCancelledByValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCancelledByValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCancelledByValue.Location = New System.Drawing.Point(185, 93)
        Me.LabelCancelledByValue.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelCancelledByValue.Name = "LabelCancelledByValue"
        Me.LabelCancelledByValue.Size = New System.Drawing.Size(0, 17)
        Me.LabelCancelledByValue.TabIndex = 6
        '
        'LabelSignedByValue
        '
        Me.LabelSignedByValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSignedByValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelSignedByValue.Location = New System.Drawing.Point(185, 43)
        Me.LabelSignedByValue.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelSignedByValue.Name = "LabelSignedByValue"
        Me.LabelSignedByValue.Size = New System.Drawing.Size(0, 17)
        Me.LabelSignedByValue.TabIndex = 2
        '
        'LabelCancellationDate
        '
        Me.LabelCancellationDate.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCancellationDate.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCancellationDate.Location = New System.Drawing.Point(4, 118)
        Me.LabelCancellationDate.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelCancellationDate.Name = "LabelCancellationDate"
        Me.LabelCancellationDate.Size = New System.Drawing.Size(127, 17)
        Me.LabelCancellationDate.TabIndex = 7
        Me.LabelCancellationDate.Text = "Cancellation Date:"
        '
        'LabelCancellationDateValue
        '
        Me.LabelCancellationDateValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCancellationDateValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCancellationDateValue.Location = New System.Drawing.Point(185, 118)
        Me.LabelCancellationDateValue.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelCancellationDateValue.Name = "LabelCancellationDateValue"
        Me.LabelCancellationDateValue.Size = New System.Drawing.Size(0, 17)
        Me.LabelCancellationDateValue.TabIndex = 8
        '
        'LabelSignatureDate
        '
        Me.LabelSignatureDate.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSignatureDate.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelSignatureDate.Location = New System.Drawing.Point(4, 68)
        Me.LabelSignatureDate.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelSignatureDate.Name = "LabelSignatureDate"
        Me.LabelSignatureDate.Size = New System.Drawing.Size(112, 17)
        Me.LabelSignatureDate.TabIndex = 3
        Me.LabelSignatureDate.Text = "Signature Date:"
        '
        'LabelSignatureDateValue
        '
        Me.LabelSignatureDateValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSignatureDateValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelSignatureDateValue.Location = New System.Drawing.Point(185, 68)
        Me.LabelSignatureDateValue.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelSignatureDateValue.Name = "LabelSignatureDateValue"
        Me.LabelSignatureDateValue.Size = New System.Drawing.Size(0, 17)
        Me.LabelSignatureDateValue.TabIndex = 4
        '
        'PanelAgencySummary
        '
        Me.PanelAgencySummary.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelAgencySummary.Controls.Add(Me.LabelAgencyDetailsSummary)
        Me.PanelAgencySummary.Controls.Add(Me.LabelAgencySummary)
        Me.PanelAgencySummary.Controls.Add(Me.LabelAgencySummaryValue)
        Me.PanelAgencySummary.Controls.Add(Me.LabelAgencyCommission)
        Me.PanelAgencySummary.Controls.Add(Me.LabelAgencyCommissionValue)
        Me.PanelAgencySummary.Controls.Add(Me.LabelCommissionSummary)
        Me.PanelAgencySummary.Controls.Add(Me.LabelCommissionSummaryValue)
        Me.PanelAgencySummary.Location = New System.Drawing.Point(504, 362)
        Me.PanelAgencySummary.Margin = New System.Windows.Forms.Padding(0)
        Me.PanelAgencySummary.Name = "PanelAgencySummary"
        Me.PanelAgencySummary.Size = New System.Drawing.Size(485, 141)
        Me.PanelAgencySummary.TabIndex = 4
        '
        'LabelAgencyDetailsSummary
        '
        Me.LabelAgencyDetailsSummary.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelAgencyDetailsSummary.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelAgencyDetailsSummary.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelAgencyDetailsSummary.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelAgencyDetailsSummary.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelAgencyDetailsSummary.LineVisible = True
        Me.LabelAgencyDetailsSummary.Location = New System.Drawing.Point(4, 4)
        Me.LabelAgencyDetailsSummary.Margin = New System.Windows.Forms.Padding(4, 4, 4, 12)
        Me.LabelAgencyDetailsSummary.Name = "LabelAgencyDetailsSummary"
        Me.LabelAgencyDetailsSummary.Size = New System.Drawing.Size(477, 24)
        Me.LabelAgencyDetailsSummary.TabIndex = 0
        Me.LabelAgencyDetailsSummary.Text = "Agency Details"
        '
        'LabelAgencySummary
        '
        Me.LabelAgencySummary.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelAgencySummary.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelAgencySummary.Location = New System.Drawing.Point(4, 43)
        Me.LabelAgencySummary.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelAgencySummary.Name = "LabelAgencySummary"
        Me.LabelAgencySummary.Size = New System.Drawing.Size(57, 17)
        Me.LabelAgencySummary.TabIndex = 1
        Me.LabelAgencySummary.Text = "Agency:"
        '
        'LabelAgencySummaryValue
        '
        Me.LabelAgencySummaryValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelAgencySummaryValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelAgencySummaryValue.Location = New System.Drawing.Point(180, 43)
        Me.LabelAgencySummaryValue.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelAgencySummaryValue.Name = "LabelAgencySummaryValue"
        Me.LabelAgencySummaryValue.Size = New System.Drawing.Size(31, 17)
        Me.LabelAgencySummaryValue.TabIndex = 2
        Me.LabelAgencySummaryValue.Text = "data"
        '
        'LabelAgencyCommission
        '
        Me.LabelAgencyCommission.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelAgencyCommission.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelAgencyCommission.Location = New System.Drawing.Point(4, 93)
        Me.LabelAgencyCommission.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelAgencyCommission.Name = "LabelAgencyCommission"
        Me.LabelAgencyCommission.Size = New System.Drawing.Size(147, 17)
        Me.LabelAgencyCommission.TabIndex = 21
        Me.LabelAgencyCommission.Text = "Agency Commission:"
        '
        'LabelAgencyCommissionValue
        '
        Me.LabelAgencyCommissionValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelAgencyCommissionValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelAgencyCommissionValue.Location = New System.Drawing.Point(180, 93)
        Me.LabelAgencyCommissionValue.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelAgencyCommissionValue.Name = "LabelAgencyCommissionValue"
        Me.LabelAgencyCommissionValue.Size = New System.Drawing.Size(97, 17)
        Me.LabelAgencyCommissionValue.TabIndex = 22
        Me.LabelAgencyCommissionValue.Text = "R 000,000.00"
        '
        'LabelCommissionSummary
        '
        Me.LabelCommissionSummary.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCommissionSummary.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCommissionSummary.Location = New System.Drawing.Point(4, 68)
        Me.LabelCommissionSummary.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelCommissionSummary.Name = "LabelCommissionSummary"
        Me.LabelCommissionSummary.Size = New System.Drawing.Size(91, 17)
        Me.LabelCommissionSummary.TabIndex = 3
        Me.LabelCommissionSummary.Text = "Commission:"
        '
        'LabelCommissionSummaryValue
        '
        Me.LabelCommissionSummaryValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCommissionSummaryValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCommissionSummaryValue.Location = New System.Drawing.Point(180, 68)
        Me.LabelCommissionSummaryValue.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelCommissionSummaryValue.Name = "LabelCommissionSummaryValue"
        Me.LabelCommissionSummaryValue.Size = New System.Drawing.Size(31, 17)
        Me.LabelCommissionSummaryValue.TabIndex = 4
        Me.LabelCommissionSummaryValue.Text = "data"
        '
        'TabPageAgency
        '
        Me.TabPageAgency.Controls.Add(Me.PanelAgency)
        Me.TabPageAgency.Margin = New System.Windows.Forms.Padding(4)
        Me.TabPageAgency.Name = "TabPageAgency"
        Me.TabPageAgency.Size = New System.Drawing.Size(1021, 539)
        Me.TabPageAgency.Text = "Agency"
        '
        'PanelAgency
        '
        Me.PanelAgency.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelAgency.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelAgency.Controls.Add(Me.PanelCommOptions)
        Me.PanelAgency.Controls.Add(Me.CheckEditApplyAgencyComm)
        Me.PanelAgency.Controls.Add(Me.LabelAgencyDetailsTitle)
        Me.PanelAgency.Location = New System.Drawing.Point(4, 4)
        Me.PanelAgency.Margin = New System.Windows.Forms.Padding(4)
        Me.PanelAgency.Name = "PanelAgency"
        Me.PanelAgency.Padding = New System.Windows.Forms.Padding(12)
        Me.PanelAgency.Size = New System.Drawing.Size(1012, 527)
        Me.PanelAgency.TabIndex = 1
        '
        'PanelCommOptions
        '
        Me.PanelCommOptions.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelCommOptions.Controls.Add(Me.LabelControl9)
        Me.PanelCommOptions.Controls.Add(Me.LabelRevenue)
        Me.PanelCommOptions.Controls.Add(Me.LabelControl5)
        Me.PanelCommOptions.Controls.Add(Me.LabelCommLabel)
        Me.PanelCommOptions.Controls.Add(Me.LabelNetRental)
        Me.PanelCommOptions.Controls.Add(Me.LabelComm)
        Me.PanelCommOptions.Controls.Add(Me.LabelControl4)
        Me.PanelCommOptions.Controls.Add(Me.LabelControl3)
        Me.PanelCommOptions.Controls.Add(Me.LabelTotalRental)
        Me.PanelCommOptions.Controls.Add(Me.LabelDiscount)
        Me.PanelCommOptions.Controls.Add(Me.LabelCommExample)
        Me.PanelCommOptions.Controls.Add(Me.LabelAgency)
        Me.PanelCommOptions.Controls.Add(Me.LabelCommission)
        Me.PanelCommOptions.Controls.Add(Me.HyperlinkAgency)
        Me.PanelCommOptions.Controls.Add(Me.TextEditCommission)
        Me.PanelCommOptions.Controls.Add(Me.CheckEditPrintAgencyComm)
        Me.PanelCommOptions.Controls.Add(Me.CheckEditPercentageOfNetRental)
        Me.PanelCommOptions.Controls.Add(Me.CheckEditPercentageOf)
        Me.PanelCommOptions.Location = New System.Drawing.Point(15, 89)
        Me.PanelCommOptions.Margin = New System.Windows.Forms.Padding(4)
        Me.PanelCommOptions.Name = "PanelCommOptions"
        Me.PanelCommOptions.Size = New System.Drawing.Size(981, 424)
        Me.PanelCommOptions.TabIndex = 9
        '
        'LabelControl9
        '
        Me.LabelControl9.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl9.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl9.Location = New System.Drawing.Point(4, 332)
        Me.LabelControl9.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelControl9.Name = "LabelControl9"
        Me.LabelControl9.Size = New System.Drawing.Size(147, 17)
        Me.LabelControl9.TabIndex = 21
        Me.LabelControl9.Text = "Recognised revenue:"
        '
        'LabelRevenue
        '
        Me.LabelRevenue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelRevenue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelRevenue.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.LabelRevenue.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelRevenue.Location = New System.Drawing.Point(266, 332)
        Me.LabelRevenue.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelRevenue.Name = "LabelRevenue"
        Me.LabelRevenue.Size = New System.Drawing.Size(108, 17)
        Me.LabelRevenue.TabIndex = 19
        Me.LabelRevenue.Text = "R 000,000.00"
        '
        'LabelControl5
        '
        Me.LabelControl5.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl5.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl5.Location = New System.Drawing.Point(4, 282)
        Me.LabelControl5.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelControl5.Name = "LabelControl5"
        Me.LabelControl5.Size = New System.Drawing.Size(75, 17)
        Me.LabelControl5.TabIndex = 17
        Me.LabelControl5.Text = "Net rental:"
        '
        'LabelCommLabel
        '
        Me.LabelCommLabel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCommLabel.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCommLabel.Location = New System.Drawing.Point(4, 307)
        Me.LabelCommLabel.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelCommLabel.Name = "LabelCommLabel"
        Me.LabelCommLabel.Size = New System.Drawing.Size(194, 17)
        Me.LabelCommLabel.TabIndex = 16
        Me.LabelCommLabel.Text = "Agency commission (10%):"
        '
        'LabelNetRental
        '
        Me.LabelNetRental.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelNetRental.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelNetRental.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.LabelNetRental.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelNetRental.Location = New System.Drawing.Point(266, 282)
        Me.LabelNetRental.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelNetRental.Name = "LabelNetRental"
        Me.LabelNetRental.Size = New System.Drawing.Size(108, 17)
        Me.LabelNetRental.TabIndex = 15
        Me.LabelNetRental.Text = "R 000,000.00"
        '
        'LabelComm
        '
        Me.LabelComm.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelComm.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelComm.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.LabelComm.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelComm.Location = New System.Drawing.Point(266, 307)
        Me.LabelComm.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelComm.Name = "LabelComm"
        Me.LabelComm.Size = New System.Drawing.Size(108, 17)
        Me.LabelComm.TabIndex = 14
        Me.LabelComm.Text = "R 000,000.00"
        '
        'LabelControl4
        '
        Me.LabelControl4.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl4.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl4.Location = New System.Drawing.Point(4, 233)
        Me.LabelControl4.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelControl4.Name = "LabelControl4"
        Me.LabelControl4.Size = New System.Drawing.Size(86, 17)
        Me.LabelControl4.TabIndex = 13
        Me.LabelControl4.Text = "Total rental:"
        '
        'LabelControl3
        '
        Me.LabelControl3.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl3.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl3.Location = New System.Drawing.Point(4, 258)
        Me.LabelControl3.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelControl3.Name = "LabelControl3"
        Me.LabelControl3.Size = New System.Drawing.Size(118, 17)
        Me.LabelControl3.TabIndex = 12
        Me.LabelControl3.Text = "Discount (20%):"
        '
        'LabelTotalRental
        '
        Me.LabelTotalRental.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelTotalRental.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelTotalRental.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.LabelTotalRental.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelTotalRental.Location = New System.Drawing.Point(266, 233)
        Me.LabelTotalRental.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelTotalRental.Name = "LabelTotalRental"
        Me.LabelTotalRental.Size = New System.Drawing.Size(108, 17)
        Me.LabelTotalRental.TabIndex = 11
        Me.LabelTotalRental.Text = "R 000,000.00"
        '
        'LabelDiscount
        '
        Me.LabelDiscount.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelDiscount.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelDiscount.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.LabelDiscount.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelDiscount.Location = New System.Drawing.Point(266, 258)
        Me.LabelDiscount.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelDiscount.Name = "LabelDiscount"
        Me.LabelDiscount.Size = New System.Drawing.Size(108, 17)
        Me.LabelDiscount.TabIndex = 10
        Me.LabelDiscount.Text = "R 000,000.00"
        '
        'LabelCommExample
        '
        Me.LabelCommExample.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCommExample.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCommExample.Location = New System.Drawing.Point(4, 208)
        Me.LabelCommExample.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelCommExample.Name = "LabelCommExample"
        Me.LabelCommExample.Size = New System.Drawing.Size(152, 17)
        Me.LabelCommExample.TabIndex = 9
        Me.LabelCommExample.Text = "Example calculation"
        '
        'LabelAgency
        '
        Me.LabelAgency.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelAgency.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelAgency.Location = New System.Drawing.Point(4, 4)
        Me.LabelAgency.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelAgency.Name = "LabelAgency"
        Me.LabelAgency.Size = New System.Drawing.Size(57, 17)
        Me.LabelAgency.TabIndex = 2
        Me.LabelAgency.Text = "Agency:"
        '
        'LabelCommission
        '
        Me.LabelCommission.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCommission.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCommission.Location = New System.Drawing.Point(4, 38)
        Me.LabelCommission.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelCommission.Name = "LabelCommission"
        Me.LabelCommission.Size = New System.Drawing.Size(91, 17)
        Me.LabelCommission.TabIndex = 4
        Me.LabelCommission.Text = "Commission:"
        '
        'HyperlinkAgency
        '
        Me.HyperlinkAgency.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkAgency.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkAgency.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkAgency.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkAgency.Location = New System.Drawing.Point(154, 4)
        Me.HyperlinkAgency.Margin = New System.Windows.Forms.Padding(4)
        Me.HyperlinkAgency.Name = "HyperlinkAgency"
        Me.HyperlinkAgency.Size = New System.Drawing.Size(57, 17)
        Me.HyperlinkAgency.TabIndex = 3
        Me.HyperlinkAgency.Text = "Select..."
        '
        'TextEditCommission
        '
        Me.TextEditCommission.EditValue = "0"
        Me.TextEditCommission.Location = New System.Drawing.Point(154, 34)
        Me.TextEditCommission.Margin = New System.Windows.Forms.Padding(4)
        Me.TextEditCommission.Name = "TextEditCommission"
        Me.TextEditCommission.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditCommission.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditCommission.Properties.Appearance.Options.UseFont = True
        Me.TextEditCommission.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditCommission.Properties.Mask.EditMask = "P2"
        Me.TextEditCommission.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditCommission.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TextEditCommission.Size = New System.Drawing.Size(82, 24)
        Me.TextEditCommission.TabIndex = 5
        '
        'CheckEditPrintAgencyComm
        '
        Me.CheckEditPrintAgencyComm.Location = New System.Drawing.Point(1, 136)
        Me.CheckEditPrintAgencyComm.Margin = New System.Windows.Forms.Padding(4, 4, 4, 5)
        Me.CheckEditPrintAgencyComm.Name = "CheckEditPrintAgencyComm"
        Me.CheckEditPrintAgencyComm.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditPrintAgencyComm.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditPrintAgencyComm.Properties.Appearance.Options.UseFont = True
        Me.CheckEditPrintAgencyComm.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditPrintAgencyComm.Properties.AutoWidth = True
        Me.CheckEditPrintAgencyComm.Properties.Caption = "Display agency commission on the printed contract"
        Me.CheckEditPrintAgencyComm.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditPrintAgencyComm.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditPrintAgencyComm.Size = New System.Drawing.Size(382, 21)
        Me.CheckEditPrintAgencyComm.TabIndex = 6
        '
        'CheckEditPercentageOfNetRental
        '
        Me.CheckEditPercentageOfNetRental.Location = New System.Drawing.Point(1, 68)
        Me.CheckEditPercentageOfNetRental.Margin = New System.Windows.Forms.Padding(4, 4, 4, 5)
        Me.CheckEditPercentageOfNetRental.Name = "CheckEditPercentageOfNetRental"
        Me.CheckEditPercentageOfNetRental.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditPercentageOfNetRental.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditPercentageOfNetRental.Properties.Appearance.Options.UseFont = True
        Me.CheckEditPercentageOfNetRental.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditPercentageOfNetRental.Properties.AutoWidth = True
        Me.CheckEditPercentageOfNetRental.Properties.Caption = "Agency commission is a percentage of the net rental after discount"
        Me.CheckEditPercentageOfNetRental.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Radio
        Me.CheckEditPercentageOfNetRental.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditPercentageOfNetRental.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditPercentageOfNetRental.Properties.RadioGroupIndex = 3
        Me.CheckEditPercentageOfNetRental.Size = New System.Drawing.Size(495, 21)
        Me.CheckEditPercentageOfNetRental.TabIndex = 7
        Me.CheckEditPercentageOfNetRental.TabStop = False
        '
        'CheckEditPercentageOf
        '
        Me.CheckEditPercentageOf.EditValue = True
        Me.CheckEditPercentageOf.Location = New System.Drawing.Point(1, 102)
        Me.CheckEditPercentageOf.Margin = New System.Windows.Forms.Padding(4, 4, 4, 5)
        Me.CheckEditPercentageOf.Name = "CheckEditPercentageOf"
        Me.CheckEditPercentageOf.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditPercentageOf.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditPercentageOf.Properties.Appearance.Options.UseFont = True
        Me.CheckEditPercentageOf.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditPercentageOf.Properties.AutoWidth = True
        Me.CheckEditPercentageOf.Properties.Caption = "Agency commission is a percentage of the recognised revenue (our portion of the n" &
    "et rental)"
        Me.CheckEditPercentageOf.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Radio
        Me.CheckEditPercentageOf.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditPercentageOf.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditPercentageOf.Properties.RadioGroupIndex = 3
        Me.CheckEditPercentageOf.Size = New System.Drawing.Size(678, 21)
        Me.CheckEditPercentageOf.TabIndex = 8
        '
        'CheckEditApplyAgencyComm
        '
        Me.CheckEditApplyAgencyComm.Location = New System.Drawing.Point(15, 55)
        Me.CheckEditApplyAgencyComm.Margin = New System.Windows.Forms.Padding(4)
        Me.CheckEditApplyAgencyComm.Name = "CheckEditApplyAgencyComm"
        Me.CheckEditApplyAgencyComm.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditApplyAgencyComm.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditApplyAgencyComm.Properties.Appearance.Options.UseFont = True
        Me.CheckEditApplyAgencyComm.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditApplyAgencyComm.Properties.AutoWidth = True
        Me.CheckEditApplyAgencyComm.Properties.Caption = "Apply agency commission to this contract"
        Me.CheckEditApplyAgencyComm.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditApplyAgencyComm.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditApplyAgencyComm.Size = New System.Drawing.Size(316, 21)
        Me.CheckEditApplyAgencyComm.TabIndex = 1
        '
        'LabelAgencyDetailsTitle
        '
        Me.LabelAgencyDetailsTitle.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelAgencyDetailsTitle.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelAgencyDetailsTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelAgencyDetailsTitle.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelAgencyDetailsTitle.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelAgencyDetailsTitle.LineVisible = True
        Me.LabelAgencyDetailsTitle.Location = New System.Drawing.Point(15, 16)
        Me.LabelAgencyDetailsTitle.Margin = New System.Windows.Forms.Padding(4, 4, 4, 12)
        Me.LabelAgencyDetailsTitle.Name = "LabelAgencyDetailsTitle"
        Me.LabelAgencyDetailsTitle.Size = New System.Drawing.Size(981, 24)
        Me.LabelAgencyDetailsTitle.TabIndex = 0
        Me.LabelAgencyDetailsTitle.Text = "Agency Details"
        '
        'TabPageBursts
        '
        Me.TabPageBursts.Appearance.PageClient.BackColor = System.Drawing.Color.DimGray
        Me.TabPageBursts.Appearance.PageClient.Options.UseBackColor = True
        Me.TabPageBursts.Controls.Add(Me.PanelBursts)
        Me.TabPageBursts.Margin = New System.Windows.Forms.Padding(4)
        Me.TabPageBursts.Name = "TabPageBursts"
        Me.TabPageBursts.Size = New System.Drawing.Size(1021, 539)
        Me.TabPageBursts.Text = "Bursts"
        '
        'PanelBursts
        '
        Me.PanelBursts.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelBursts.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelBursts.Controls.Add(Me.PanelBurstInfo1)
        Me.PanelBursts.Controls.Add(Me.PanelBurstInfo2)
        Me.PanelBursts.Controls.Add(Me.GroupControlBursts)
        Me.PanelBursts.Location = New System.Drawing.Point(4, 4)
        Me.PanelBursts.Margin = New System.Windows.Forms.Padding(4)
        Me.PanelBursts.Name = "PanelBursts"
        Me.PanelBursts.Padding = New System.Windows.Forms.Padding(12)
        Me.PanelBursts.Size = New System.Drawing.Size(1012, 527)
        Me.PanelBursts.TabIndex = 0
        '
        'PanelBurstInfo1
        '
        Me.PanelBurstInfo1.Controls.Add(Me.LabelFirstWeek)
        Me.PanelBurstInfo1.Controls.Add(Me.LabelFirstWeekValue)
        Me.PanelBurstInfo1.Controls.Add(Me.LabelLastWeekValue)
        Me.PanelBurstInfo1.Controls.Add(Me.LabelRental)
        Me.PanelBurstInfo1.Controls.Add(Me.LabelLastWeek)
        Me.PanelBurstInfo1.Controls.Add(Me.LabelRentalValue)
        Me.PanelBurstInfo1.Controls.Add(Me.LabelTotalWeeksValue)
        Me.PanelBurstInfo1.Controls.Add(Me.LabelTotalWeeks)
        Me.PanelBurstInfo1.Location = New System.Drawing.Point(15, 16)
        Me.PanelBurstInfo1.Margin = New System.Windows.Forms.Padding(4, 4, 4, 26)
        Me.PanelBurstInfo1.Name = "PanelBurstInfo1"
        Me.PanelBurstInfo1.Size = New System.Drawing.Size(292, 105)
        Me.PanelBurstInfo1.TabIndex = 5
        '
        'LabelFirstWeek
        '
        Me.LabelFirstWeek.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelFirstWeek.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelFirstWeek.Location = New System.Drawing.Point(4, 4)
        Me.LabelFirstWeek.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelFirstWeek.Name = "LabelFirstWeek"
        Me.LabelFirstWeek.Size = New System.Drawing.Size(81, 17)
        Me.LabelFirstWeek.TabIndex = 11
        Me.LabelFirstWeek.Text = "First Week:"
        '
        'LabelFirstWeekValue
        '
        Me.LabelFirstWeekValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelFirstWeekValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelFirstWeekValue.Location = New System.Drawing.Point(122, 4)
        Me.LabelFirstWeekValue.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelFirstWeekValue.Name = "LabelFirstWeekValue"
        Me.LabelFirstWeekValue.Size = New System.Drawing.Size(0, 17)
        Me.LabelFirstWeekValue.TabIndex = 12
        '
        'LabelLastWeekValue
        '
        Me.LabelLastWeekValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelLastWeekValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelLastWeekValue.Location = New System.Drawing.Point(122, 29)
        Me.LabelLastWeekValue.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelLastWeekValue.Name = "LabelLastWeekValue"
        Me.LabelLastWeekValue.Size = New System.Drawing.Size(0, 17)
        Me.LabelLastWeekValue.TabIndex = 14
        '
        'LabelRental
        '
        Me.LabelRental.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelRental.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelRental.Location = New System.Drawing.Point(4, 78)
        Me.LabelRental.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelRental.Name = "LabelRental"
        Me.LabelRental.Size = New System.Drawing.Size(50, 17)
        Me.LabelRental.TabIndex = 19
        Me.LabelRental.Text = "Rental:"
        '
        'LabelLastWeek
        '
        Me.LabelLastWeek.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelLastWeek.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelLastWeek.Location = New System.Drawing.Point(4, 29)
        Me.LabelLastWeek.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelLastWeek.Name = "LabelLastWeek"
        Me.LabelLastWeek.Size = New System.Drawing.Size(80, 17)
        Me.LabelLastWeek.TabIndex = 13
        Me.LabelLastWeek.Text = "Last Week:"
        '
        'LabelRentalValue
        '
        Me.LabelRentalValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelRentalValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelRentalValue.Location = New System.Drawing.Point(122, 78)
        Me.LabelRentalValue.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelRentalValue.Name = "LabelRentalValue"
        Me.LabelRentalValue.Size = New System.Drawing.Size(97, 17)
        Me.LabelRentalValue.TabIndex = 20
        Me.LabelRentalValue.Text = "R 000,000.00"
        '
        'LabelTotalWeeksValue
        '
        Me.LabelTotalWeeksValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelTotalWeeksValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelTotalWeeksValue.Location = New System.Drawing.Point(122, 54)
        Me.LabelTotalWeeksValue.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelTotalWeeksValue.Name = "LabelTotalWeeksValue"
        Me.LabelTotalWeeksValue.Size = New System.Drawing.Size(9, 17)
        Me.LabelTotalWeeksValue.TabIndex = 16
        Me.LabelTotalWeeksValue.Text = "0"
        '
        'LabelTotalWeeks
        '
        Me.LabelTotalWeeks.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelTotalWeeks.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelTotalWeeks.Location = New System.Drawing.Point(4, 54)
        Me.LabelTotalWeeks.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelTotalWeeks.Name = "LabelTotalWeeks"
        Me.LabelTotalWeeks.Size = New System.Drawing.Size(93, 17)
        Me.LabelTotalWeeks.TabIndex = 15
        Me.LabelTotalWeeks.Text = "Total Weeks:"
        '
        'PanelBurstInfo2
        '
        Me.PanelBurstInfo2.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelBurstInfo2.Controls.Add(Me.LabelMediaServices)
        Me.PanelBurstInfo2.Controls.Add(Me.LabelMediaServicesValue)
        Me.PanelBurstInfo2.Controls.Add(Me.LabelBrands)
        Me.PanelBurstInfo2.Controls.Add(Me.LabelBrandsValue)
        Me.PanelBurstInfo2.Controls.Add(Me.LabelCategories)
        Me.PanelBurstInfo2.Controls.Add(Me.LabelCategoriesValue)
        Me.PanelBurstInfo2.Controls.Add(Me.LabelChains)
        Me.PanelBurstInfo2.Controls.Add(Me.LabelChainsValue)
        Me.PanelBurstInfo2.Location = New System.Drawing.Point(315, 16)
        Me.PanelBurstInfo2.Margin = New System.Windows.Forms.Padding(4, 4, 4, 26)
        Me.PanelBurstInfo2.Name = "PanelBurstInfo2"
        Me.PanelBurstInfo2.Size = New System.Drawing.Size(681, 105)
        Me.PanelBurstInfo2.TabIndex = 4
        '
        'LabelMediaServices
        '
        Me.LabelMediaServices.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelMediaServices.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelMediaServices.Location = New System.Drawing.Point(4, 4)
        Me.LabelMediaServices.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelMediaServices.Name = "LabelMediaServices"
        Me.LabelMediaServices.Size = New System.Drawing.Size(108, 17)
        Me.LabelMediaServices.TabIndex = 1
        Me.LabelMediaServices.Text = "Media Services:"
        '
        'LabelMediaServicesValue
        '
        Me.LabelMediaServicesValue.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelMediaServicesValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelMediaServicesValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelMediaServicesValue.AutoEllipsis = True
        Me.LabelMediaServicesValue.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelMediaServicesValue.Location = New System.Drawing.Point(145, 4)
        Me.LabelMediaServicesValue.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelMediaServicesValue.Name = "LabelMediaServicesValue"
        Me.LabelMediaServicesValue.Size = New System.Drawing.Size(532, 17)
        Me.LabelMediaServicesValue.TabIndex = 2
        '
        'LabelBrands
        '
        Me.LabelBrands.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelBrands.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelBrands.Location = New System.Drawing.Point(4, 78)
        Me.LabelBrands.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelBrands.Name = "LabelBrands"
        Me.LabelBrands.Size = New System.Drawing.Size(56, 17)
        Me.LabelBrands.TabIndex = 7
        Me.LabelBrands.Text = "Brands:"
        '
        'LabelBrandsValue
        '
        Me.LabelBrandsValue.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelBrandsValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelBrandsValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelBrandsValue.AutoEllipsis = True
        Me.LabelBrandsValue.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelBrandsValue.Location = New System.Drawing.Point(145, 78)
        Me.LabelBrandsValue.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelBrandsValue.Name = "LabelBrandsValue"
        Me.LabelBrandsValue.Size = New System.Drawing.Size(532, 17)
        Me.LabelBrandsValue.TabIndex = 8
        '
        'LabelCategories
        '
        Me.LabelCategories.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCategories.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCategories.Location = New System.Drawing.Point(4, 54)
        Me.LabelCategories.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelCategories.Name = "LabelCategories"
        Me.LabelCategories.Size = New System.Drawing.Size(81, 17)
        Me.LabelCategories.TabIndex = 5
        Me.LabelCategories.Text = "Categories:"
        '
        'LabelCategoriesValue
        '
        Me.LabelCategoriesValue.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelCategoriesValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCategoriesValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCategoriesValue.AutoEllipsis = True
        Me.LabelCategoriesValue.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelCategoriesValue.Location = New System.Drawing.Point(145, 54)
        Me.LabelCategoriesValue.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelCategoriesValue.Name = "LabelCategoriesValue"
        Me.LabelCategoriesValue.Size = New System.Drawing.Size(532, 17)
        Me.LabelCategoriesValue.TabIndex = 6
        '
        'LabelChains
        '
        Me.LabelChains.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelChains.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelChains.Location = New System.Drawing.Point(4, 29)
        Me.LabelChains.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelChains.Name = "LabelChains"
        Me.LabelChains.Size = New System.Drawing.Size(53, 17)
        Me.LabelChains.TabIndex = 3
        Me.LabelChains.Text = "Chains:"
        '
        'LabelChainsValue
        '
        Me.LabelChainsValue.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelChainsValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelChainsValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelChainsValue.AutoEllipsis = True
        Me.LabelChainsValue.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelChainsValue.Location = New System.Drawing.Point(145, 29)
        Me.LabelChainsValue.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelChainsValue.Name = "LabelChainsValue"
        Me.LabelChainsValue.Size = New System.Drawing.Size(532, 17)
        Me.LabelChainsValue.TabIndex = 4
        '
        'GroupControlBursts
        '
        Me.GroupControlBursts.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlBursts.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlBursts.Appearance.Options.UseFont = True
        Me.GroupControlBursts.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlBursts.AppearanceCaption.ForeColor = System.Drawing.Color.DimGray
        Me.GroupControlBursts.AppearanceCaption.Options.UseFont = True
        Me.GroupControlBursts.AppearanceCaption.Options.UseForeColor = True
        Me.GroupControlBursts.Controls.Add(Me.ButtonEditBurst)
        Me.GroupControlBursts.Controls.Add(Me.ButtonDeleteBurst)
        Me.GroupControlBursts.Controls.Add(Me.ButtonAddBurst)
        Me.GroupControlBursts.Controls.Add(Me.LabelSearchBurst)
        Me.GroupControlBursts.Controls.Add(Me.TextEditSearchBurst)
        Me.GroupControlBursts.Controls.Add(Me.PictureClearSearchBursts)
        Me.GroupControlBursts.Controls.Add(Me.PictureAdvancedSearchBurst)
        Me.GroupControlBursts.Controls.Add(Me.GridBursts)
        Me.GroupControlBursts.Location = New System.Drawing.Point(15, 150)
        Me.GroupControlBursts.LookAndFeel.SkinName = "Black"
        Me.GroupControlBursts.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlBursts.Margin = New System.Windows.Forms.Padding(4)
        Me.GroupControlBursts.Name = "GroupControlBursts"
        Me.GroupControlBursts.Size = New System.Drawing.Size(981, 361)
        Me.GroupControlBursts.TabIndex = 0
        Me.GroupControlBursts.Tag = ""
        Me.GroupControlBursts.Text = "Burst List"
        '
        'ButtonEditBurst
        '
        Me.ButtonEditBurst.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonEditBurst.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonEditBurst.Appearance.Options.UseFont = True
        Me.ButtonEditBurst.ImageIndex = 1
        Me.ButtonEditBurst.ImageList = Me.ImageList16x16
        Me.ButtonEditBurst.Location = New System.Drawing.Point(111, 324)
        Me.ButtonEditBurst.LookAndFeel.SkinName = "Black"
        Me.ButtonEditBurst.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonEditBurst.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonEditBurst.Name = "ButtonEditBurst"
        Me.ButtonEditBurst.Size = New System.Drawing.Size(96, 30)
        Me.ButtonEditBurst.TabIndex = 3
        Me.ButtonEditBurst.Text = "Edit"
        '
        'ImageList16x16
        '
        Me.ImageList16x16.ImageStream = CType(resources.GetObject("ImageList16x16.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList16x16.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList16x16.Images.SetKeyName(0, "add.png")
        Me.ImageList16x16.Images.SetKeyName(1, "pencil.png")
        Me.ImageList16x16.Images.SetKeyName(2, "delete.png")
        Me.ImageList16x16.Images.SetKeyName(3, "shopping_cart.png")
        '
        'ButtonDeleteBurst
        '
        Me.ButtonDeleteBurst.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonDeleteBurst.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonDeleteBurst.Appearance.Options.UseFont = True
        Me.ButtonDeleteBurst.ImageIndex = 2
        Me.ButtonDeleteBurst.ImageList = Me.ImageList16x16
        Me.ButtonDeleteBurst.Location = New System.Drawing.Point(215, 324)
        Me.ButtonDeleteBurst.LookAndFeel.SkinName = "Black"
        Me.ButtonDeleteBurst.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonDeleteBurst.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonDeleteBurst.Name = "ButtonDeleteBurst"
        Me.ButtonDeleteBurst.Size = New System.Drawing.Size(96, 30)
        Me.ButtonDeleteBurst.TabIndex = 4
        Me.ButtonDeleteBurst.Text = "Delete"
        '
        'ButtonAddBurst
        '
        Me.ButtonAddBurst.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAddBurst.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAddBurst.Appearance.Options.UseFont = True
        Me.ButtonAddBurst.ImageIndex = 0
        Me.ButtonAddBurst.ImageList = Me.ImageList16x16
        Me.ButtonAddBurst.Location = New System.Drawing.Point(6, 324)
        Me.ButtonAddBurst.LookAndFeel.SkinName = "Black"
        Me.ButtonAddBurst.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAddBurst.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonAddBurst.Name = "ButtonAddBurst"
        Me.ButtonAddBurst.Size = New System.Drawing.Size(96, 30)
        Me.ButtonAddBurst.TabIndex = 2
        Me.ButtonAddBurst.Text = "Add"
        '
        'LabelSearchBurst
        '
        Me.LabelSearchBurst.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelSearchBurst.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSearchBurst.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelSearchBurst.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelSearchBurst.Location = New System.Drawing.Point(778, 330)
        Me.LabelSearchBurst.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelSearchBurst.Name = "LabelSearchBurst"
        Me.LabelSearchBurst.Size = New System.Drawing.Size(58, 17)
        Me.LabelSearchBurst.TabIndex = 5
        Me.LabelSearchBurst.Text = "Search:"
        '
        'TextEditSearchBurst
        '
        Me.TextEditSearchBurst.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchBurst.EditValue = ""
        Me.TextEditSearchBurst.Location = New System.Drawing.Point(843, 326)
        Me.TextEditSearchBurst.Margin = New System.Windows.Forms.Padding(4, 5, 4, 7)
        Me.TextEditSearchBurst.Name = "TextEditSearchBurst"
        Me.TextEditSearchBurst.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchBurst.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchBurst.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchBurst.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchBurst.Size = New System.Drawing.Size(103, 24)
        Me.TextEditSearchBurst.TabIndex = 6
        '
        'PictureClearSearchBursts
        '
        Me.PictureClearSearchBursts.AllowDrop = True
        Me.PictureClearSearchBursts.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchBursts.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchBursts.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchBursts.Location = New System.Drawing.Point(954, 4)
        Me.PictureClearSearchBursts.Margin = New System.Windows.Forms.Padding(4)
        Me.PictureClearSearchBursts.Name = "PictureClearSearchBursts"
        Me.PictureClearSearchBursts.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchBursts.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchBursts.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchBursts.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem1.Text = "Clear Search"
        ToolTipItem1.LeftIndent = 6
        ToolTipItem1.Text = "Click here to clear all search boxes."
        SuperToolTip1.Items.Add(ToolTipTitleItem1)
        SuperToolTip1.Items.Add(ToolTipItem1)
        Me.PictureClearSearchBursts.SuperTip = SuperToolTip1
        Me.PictureClearSearchBursts.TabIndex = 0
        Me.PictureClearSearchBursts.TabStop = True
        '
        'PictureAdvancedSearchBurst
        '
        Me.PictureAdvancedSearchBurst.AllowDrop = True
        Me.PictureAdvancedSearchBurst.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearchBurst.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearchBurst.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearchBurst.Location = New System.Drawing.Point(954, 328)
        Me.PictureAdvancedSearchBurst.Margin = New System.Windows.Forms.Padding(4, 8, 4, 9)
        Me.PictureAdvancedSearchBurst.Name = "PictureAdvancedSearchBurst"
        Me.PictureAdvancedSearchBurst.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearchBurst.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearchBurst.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearchBurst.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem2.Text = "Advanced Search"
        ToolTipItem2.LeftIndent = 6
        ToolTipItem2.Text = "Click here to search individual column values."
        SuperToolTip2.Items.Add(ToolTipTitleItem2)
        SuperToolTip2.Items.Add(ToolTipItem2)
        Me.PictureAdvancedSearchBurst.SuperTip = SuperToolTip2
        Me.PictureAdvancedSearchBurst.TabIndex = 7
        Me.PictureAdvancedSearchBurst.TabStop = True
        '
        'GridBursts
        '
        Me.GridBursts.AllowUserToAddRows = False
        Me.GridBursts.AllowUserToDeleteRows = False
        Me.GridBursts.AllowUserToOrderColumns = True
        Me.GridBursts.AllowUserToResizeRows = False
        DataGridViewCellStyle1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridBursts.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle1
        Me.GridBursts.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridBursts.BackgroundColor = System.Drawing.Color.White
        Me.GridBursts.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridBursts.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridBursts.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle2.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle2.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridBursts.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle2
        Me.GridBursts.ColumnHeadersHeight = 22
        Me.GridBursts.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridBursts.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.MediaNameColumn, Me.ChainNameColumn, Me.CategoryListColumn, Me.InstallCategories, Me.InstallStoreQtyColumn, Me.BurstWeeksColumn, Me.FirstWeekColumn, Me.LastWeekColumn, Me.BrandNameColumn, Me.ProductNameColumn})
        Me.GridBursts.EnableHeadersVisualStyles = False
        Me.GridBursts.GridColor = System.Drawing.Color.White
        Me.GridBursts.Location = New System.Drawing.Point(3, 29)
        Me.GridBursts.Margin = New System.Windows.Forms.Padding(4)
        Me.GridBursts.Name = "GridBursts"
        Me.GridBursts.ReadOnly = True
        Me.GridBursts.RowHeadersVisible = False
        Me.GridBursts.RowHeadersWidth = 51
        DataGridViewCellStyle5.ForeColor = System.Drawing.Color.DimGray
        Me.GridBursts.RowsDefaultCellStyle = DataGridViewCellStyle5
        Me.GridBursts.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridBursts.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridBursts.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridBursts.RowTemplate.Height = 19
        Me.GridBursts.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridBursts.ShowCellToolTips = False
        Me.GridBursts.Size = New System.Drawing.Size(976, 288)
        Me.GridBursts.StandardTab = True
        Me.GridBursts.TabIndex = 1
        '
        'TabPageProduction
        '
        Me.TabPageProduction.Controls.Add(Me.PanelProduction)
        Me.TabPageProduction.Margin = New System.Windows.Forms.Padding(4)
        Me.TabPageProduction.Name = "TabPageProduction"
        Me.TabPageProduction.Size = New System.Drawing.Size(1021, 539)
        Me.TabPageProduction.Text = "Production"
        '
        'PanelProduction
        '
        Me.PanelProduction.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelProduction.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelProduction.Controls.Add(Me.LabelProductionChargesValue)
        Me.PanelProduction.Controls.Add(Me.GroupControlProduction)
        Me.PanelProduction.Location = New System.Drawing.Point(4, 4)
        Me.PanelProduction.Margin = New System.Windows.Forms.Padding(4)
        Me.PanelProduction.Name = "PanelProduction"
        Me.PanelProduction.Padding = New System.Windows.Forms.Padding(12)
        Me.PanelProduction.Size = New System.Drawing.Size(1012, 527)
        Me.PanelProduction.TabIndex = 0
        '
        'LabelProductionChargesValue
        '
        Me.LabelProductionChargesValue.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelProductionChargesValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelProductionChargesValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelProductionChargesValue.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.LabelProductionChargesValue.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelProductionChargesValue.Location = New System.Drawing.Point(793, 494)
        Me.LabelProductionChargesValue.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelProductionChargesValue.Name = "LabelProductionChargesValue"
        Me.LabelProductionChargesValue.Size = New System.Drawing.Size(203, 17)
        Me.LabelProductionChargesValue.TabIndex = 3
        Me.LabelProductionChargesValue.Text = "R 000,000.00"
        '
        'GroupControlProduction
        '
        Me.GroupControlProduction.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlProduction.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlProduction.Appearance.Options.UseFont = True
        Me.GroupControlProduction.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlProduction.AppearanceCaption.ForeColor = System.Drawing.Color.DimGray
        Me.GroupControlProduction.AppearanceCaption.Options.UseFont = True
        Me.GroupControlProduction.AppearanceCaption.Options.UseForeColor = True
        Me.GroupControlProduction.Controls.Add(Me.ButtonEditProduction)
        Me.GroupControlProduction.Controls.Add(Me.ButtonRemoveProduction)
        Me.GroupControlProduction.Controls.Add(Me.ButtonAddProduction)
        Me.GroupControlProduction.Controls.Add(Me.LabelSearchProduction)
        Me.GroupControlProduction.Controls.Add(Me.TextEditSearchProduction)
        Me.GroupControlProduction.Controls.Add(Me.PictureClearSearchProduction)
        Me.GroupControlProduction.Controls.Add(Me.PictureAdvancedSearchProduction)
        Me.GroupControlProduction.Controls.Add(Me.GridProduction)
        Me.GroupControlProduction.Location = New System.Drawing.Point(15, 16)
        Me.GroupControlProduction.LookAndFeel.SkinName = "Black"
        Me.GroupControlProduction.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlProduction.Margin = New System.Windows.Forms.Padding(4, 4, 4, 12)
        Me.GroupControlProduction.Name = "GroupControlProduction"
        Me.GroupControlProduction.Size = New System.Drawing.Size(981, 463)
        Me.GroupControlProduction.TabIndex = 0
        Me.GroupControlProduction.Tag = ""
        Me.GroupControlProduction.Text = "Production Item List"
        '
        'ButtonEditProduction
        '
        Me.ButtonEditProduction.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonEditProduction.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonEditProduction.Appearance.Options.UseFont = True
        Me.ButtonEditProduction.ImageIndex = 1
        Me.ButtonEditProduction.ImageList = Me.ImageList16x16
        Me.ButtonEditProduction.Location = New System.Drawing.Point(111, 426)
        Me.ButtonEditProduction.LookAndFeel.SkinName = "Black"
        Me.ButtonEditProduction.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonEditProduction.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonEditProduction.Name = "ButtonEditProduction"
        Me.ButtonEditProduction.Size = New System.Drawing.Size(96, 30)
        Me.ButtonEditProduction.TabIndex = 3
        Me.ButtonEditProduction.Text = "Edit"
        '
        'ButtonRemoveProduction
        '
        Me.ButtonRemoveProduction.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonRemoveProduction.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonRemoveProduction.Appearance.Options.UseFont = True
        Me.ButtonRemoveProduction.ImageIndex = 2
        Me.ButtonRemoveProduction.ImageList = Me.ImageList16x16
        Me.ButtonRemoveProduction.Location = New System.Drawing.Point(215, 426)
        Me.ButtonRemoveProduction.LookAndFeel.SkinName = "Black"
        Me.ButtonRemoveProduction.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonRemoveProduction.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonRemoveProduction.Name = "ButtonRemoveProduction"
        Me.ButtonRemoveProduction.Size = New System.Drawing.Size(96, 30)
        Me.ButtonRemoveProduction.TabIndex = 4
        Me.ButtonRemoveProduction.Text = "Remove"
        '
        'ButtonAddProduction
        '
        Me.ButtonAddProduction.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAddProduction.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAddProduction.Appearance.Options.UseFont = True
        Me.ButtonAddProduction.ImageIndex = 0
        Me.ButtonAddProduction.ImageList = Me.ImageList16x16
        Me.ButtonAddProduction.Location = New System.Drawing.Point(6, 426)
        Me.ButtonAddProduction.LookAndFeel.SkinName = "Black"
        Me.ButtonAddProduction.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAddProduction.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonAddProduction.Name = "ButtonAddProduction"
        Me.ButtonAddProduction.Size = New System.Drawing.Size(96, 30)
        Me.ButtonAddProduction.TabIndex = 2
        Me.ButtonAddProduction.Text = "Add"
        '
        'LabelSearchProduction
        '
        Me.LabelSearchProduction.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelSearchProduction.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSearchProduction.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelSearchProduction.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelSearchProduction.Location = New System.Drawing.Point(778, 432)
        Me.LabelSearchProduction.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelSearchProduction.Name = "LabelSearchProduction"
        Me.LabelSearchProduction.Size = New System.Drawing.Size(58, 17)
        Me.LabelSearchProduction.TabIndex = 5
        Me.LabelSearchProduction.Text = "Search:"
        '
        'TextEditSearchProduction
        '
        Me.TextEditSearchProduction.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchProduction.EditValue = ""
        Me.TextEditSearchProduction.Location = New System.Drawing.Point(843, 428)
        Me.TextEditSearchProduction.Margin = New System.Windows.Forms.Padding(4, 5, 4, 7)
        Me.TextEditSearchProduction.Name = "TextEditSearchProduction"
        Me.TextEditSearchProduction.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchProduction.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchProduction.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchProduction.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchProduction.Size = New System.Drawing.Size(103, 24)
        Me.TextEditSearchProduction.TabIndex = 6
        '
        'PictureClearSearchProduction
        '
        Me.PictureClearSearchProduction.AllowDrop = True
        Me.PictureClearSearchProduction.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchProduction.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchProduction.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchProduction.Location = New System.Drawing.Point(954, 4)
        Me.PictureClearSearchProduction.Margin = New System.Windows.Forms.Padding(4)
        Me.PictureClearSearchProduction.Name = "PictureClearSearchProduction"
        Me.PictureClearSearchProduction.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchProduction.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchProduction.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchProduction.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem3.Text = "Clear Search"
        ToolTipItem3.LeftIndent = 6
        ToolTipItem3.Text = "Click here to clear all search boxes."
        SuperToolTip3.Items.Add(ToolTipTitleItem3)
        SuperToolTip3.Items.Add(ToolTipItem3)
        Me.PictureClearSearchProduction.SuperTip = SuperToolTip3
        Me.PictureClearSearchProduction.TabIndex = 0
        Me.PictureClearSearchProduction.TabStop = True
        '
        'PictureAdvancedSearchProduction
        '
        Me.PictureAdvancedSearchProduction.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearchProduction.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearchProduction.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearchProduction.Location = New System.Drawing.Point(954, 430)
        Me.PictureAdvancedSearchProduction.Margin = New System.Windows.Forms.Padding(4, 8, 4, 9)
        Me.PictureAdvancedSearchProduction.Name = "PictureAdvancedSearchProduction"
        Me.PictureAdvancedSearchProduction.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearchProduction.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearchProduction.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearchProduction.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem4.Text = "Advanced Search"
        ToolTipItem4.LeftIndent = 6
        ToolTipItem4.Text = "Click here to search individual column values."
        SuperToolTip4.Items.Add(ToolTipTitleItem4)
        SuperToolTip4.Items.Add(ToolTipItem4)
        Me.PictureAdvancedSearchProduction.SuperTip = SuperToolTip4
        Me.PictureAdvancedSearchProduction.TabIndex = 7
        Me.PictureAdvancedSearchProduction.TabStop = True
        '
        'GridProduction
        '
        Me.GridProduction.AllowUserToAddRows = False
        Me.GridProduction.AllowUserToDeleteRows = False
        Me.GridProduction.AllowUserToOrderColumns = True
        Me.GridProduction.AllowUserToResizeRows = False
        DataGridViewCellStyle6.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridProduction.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle6
        Me.GridProduction.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridProduction.BackgroundColor = System.Drawing.Color.White
        Me.GridProduction.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridProduction.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridProduction.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle7.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle7.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle7.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle7.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle7.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle7.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle7.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridProduction.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle7
        Me.GridProduction.ColumnHeadersHeight = 22
        Me.GridProduction.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridProduction.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.ProductionDescriptionColumn, Me.ProductionQuantityColumn, Me.ProductionBrandNameColumn, Me.ProductionAmountColumn, Me.ProductionNotesColumn})
        Me.GridProduction.EnableHeadersVisualStyles = False
        Me.GridProduction.GridColor = System.Drawing.Color.White
        Me.GridProduction.Location = New System.Drawing.Point(3, 29)
        Me.GridProduction.Margin = New System.Windows.Forms.Padding(4)
        Me.GridProduction.Name = "GridProduction"
        Me.GridProduction.ReadOnly = True
        Me.GridProduction.RowHeadersVisible = False
        Me.GridProduction.RowHeadersWidth = 51
        DataGridViewCellStyle10.ForeColor = System.Drawing.Color.DimGray
        Me.GridProduction.RowsDefaultCellStyle = DataGridViewCellStyle10
        Me.GridProduction.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridProduction.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridProduction.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridProduction.RowTemplate.Height = 19
        Me.GridProduction.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridProduction.ShowCellToolTips = False
        Me.GridProduction.Size = New System.Drawing.Size(976, 390)
        Me.GridProduction.StandardTab = True
        Me.GridProduction.TabIndex = 1
        '
        'ProductionDescriptionColumn
        '
        Me.ProductionDescriptionColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.ProductionDescriptionColumn.DataPropertyName = "ItemName"
        Me.ProductionDescriptionColumn.HeaderText = "Description"
        Me.ProductionDescriptionColumn.MinimumWidth = 6
        Me.ProductionDescriptionColumn.Name = "ProductionDescriptionColumn"
        Me.ProductionDescriptionColumn.ReadOnly = True
        Me.ProductionDescriptionColumn.Width = 115
        '
        'ProductionQuantityColumn
        '
        Me.ProductionQuantityColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.ProductionQuantityColumn.DataPropertyName = "ItemQty"
        DataGridViewCellStyle8.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        Me.ProductionQuantityColumn.DefaultCellStyle = DataGridViewCellStyle8
        Me.ProductionQuantityColumn.HeaderText = "Quantity"
        Me.ProductionQuantityColumn.MinimumWidth = 6
        Me.ProductionQuantityColumn.Name = "ProductionQuantityColumn"
        Me.ProductionQuantityColumn.ReadOnly = True
        Me.ProductionQuantityColumn.Width = 96
        '
        'ProductionBrandNameColumn
        '
        Me.ProductionBrandNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.ProductionBrandNameColumn.DataPropertyName = "BrandName"
        Me.ProductionBrandNameColumn.HeaderText = "For Brand"
        Me.ProductionBrandNameColumn.MinimumWidth = 6
        Me.ProductionBrandNameColumn.Name = "ProductionBrandNameColumn"
        Me.ProductionBrandNameColumn.ReadOnly = True
        Me.ProductionBrandNameColumn.Width = 106
        '
        'ProductionAmountColumn
        '
        Me.ProductionAmountColumn.DataPropertyName = "SellPrice"
        DataGridViewCellStyle9.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleRight
        DataGridViewCellStyle9.Format = "C2"
        DataGridViewCellStyle9.NullValue = Nothing
        Me.ProductionAmountColumn.DefaultCellStyle = DataGridViewCellStyle9
        Me.ProductionAmountColumn.HeaderText = "Amount"
        Me.ProductionAmountColumn.MinimumWidth = 6
        Me.ProductionAmountColumn.Name = "ProductionAmountColumn"
        Me.ProductionAmountColumn.ReadOnly = True
        Me.ProductionAmountColumn.Width = 125
        '
        'ProductionNotesColumn
        '
        Me.ProductionNotesColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.ProductionNotesColumn.DataPropertyName = "Notes"
        Me.ProductionNotesColumn.HeaderText = "Notes"
        Me.ProductionNotesColumn.MinimumWidth = 6
        Me.ProductionNotesColumn.Name = "ProductionNotesColumn"
        Me.ProductionNotesColumn.ReadOnly = True
        '
        'TabPageMiscellaneousCharges
        '
        Me.TabPageMiscellaneousCharges.Controls.Add(Me.Panel1)
        Me.TabPageMiscellaneousCharges.Margin = New System.Windows.Forms.Padding(4)
        Me.TabPageMiscellaneousCharges.Name = "TabPageMiscellaneousCharges"
        Me.TabPageMiscellaneousCharges.Size = New System.Drawing.Size(1021, 539)
        Me.TabPageMiscellaneousCharges.Text = "Miscellaneous Charges"
        '
        'Panel1
        '
        Me.Panel1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Panel1.Controls.Add(Me.LabelMiscellaneousChargesValue)
        Me.Panel1.Controls.Add(Me.GroupControlMiscellaneousCharges)
        Me.Panel1.Location = New System.Drawing.Point(4, 4)
        Me.Panel1.Margin = New System.Windows.Forms.Padding(4)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Padding = New System.Windows.Forms.Padding(12)
        Me.Panel1.Size = New System.Drawing.Size(1012, 527)
        Me.Panel1.TabIndex = 1
        '
        'LabelMiscellaneousChargesValue
        '
        Me.LabelMiscellaneousChargesValue.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelMiscellaneousChargesValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelMiscellaneousChargesValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelMiscellaneousChargesValue.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.LabelMiscellaneousChargesValue.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelMiscellaneousChargesValue.Location = New System.Drawing.Point(793, 494)
        Me.LabelMiscellaneousChargesValue.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelMiscellaneousChargesValue.Name = "LabelMiscellaneousChargesValue"
        Me.LabelMiscellaneousChargesValue.Size = New System.Drawing.Size(203, 17)
        Me.LabelMiscellaneousChargesValue.TabIndex = 5
        Me.LabelMiscellaneousChargesValue.Text = "R 000,000.00"
        '
        'GroupControlMiscellaneousCharges
        '
        Me.GroupControlMiscellaneousCharges.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlMiscellaneousCharges.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlMiscellaneousCharges.Appearance.Options.UseFont = True
        Me.GroupControlMiscellaneousCharges.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlMiscellaneousCharges.AppearanceCaption.ForeColor = System.Drawing.Color.DimGray
        Me.GroupControlMiscellaneousCharges.AppearanceCaption.Options.UseFont = True
        Me.GroupControlMiscellaneousCharges.AppearanceCaption.Options.UseForeColor = True
        Me.GroupControlMiscellaneousCharges.Controls.Add(Me.ButtonEditMiscellaneousCharge)
        Me.GroupControlMiscellaneousCharges.Controls.Add(Me.ButtonRemoveMiscellaneousCharge)
        Me.GroupControlMiscellaneousCharges.Controls.Add(Me.ButtonAddMiscellaneousCharge)
        Me.GroupControlMiscellaneousCharges.Controls.Add(Me.LabelSearchMiscellaneousCharge)
        Me.GroupControlMiscellaneousCharges.Controls.Add(Me.TextEditSearchMiscellaneousCharge)
        Me.GroupControlMiscellaneousCharges.Controls.Add(Me.PictureClearSearchMiscellaneousCharges)
        Me.GroupControlMiscellaneousCharges.Controls.Add(Me.PictureAdvancedSearchMiscellaneousCharge)
        Me.GroupControlMiscellaneousCharges.Controls.Add(Me.GridMiscellaneousCharges)
        Me.GroupControlMiscellaneousCharges.Location = New System.Drawing.Point(15, 16)
        Me.GroupControlMiscellaneousCharges.LookAndFeel.SkinName = "Black"
        Me.GroupControlMiscellaneousCharges.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlMiscellaneousCharges.Margin = New System.Windows.Forms.Padding(4, 4, 4, 12)
        Me.GroupControlMiscellaneousCharges.Name = "GroupControlMiscellaneousCharges"
        Me.GroupControlMiscellaneousCharges.Size = New System.Drawing.Size(981, 463)
        Me.GroupControlMiscellaneousCharges.TabIndex = 2
        Me.GroupControlMiscellaneousCharges.Tag = ""
        Me.GroupControlMiscellaneousCharges.Text = "Miscellaneous Charges"
        '
        'ButtonEditMiscellaneousCharge
        '
        Me.ButtonEditMiscellaneousCharge.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonEditMiscellaneousCharge.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonEditMiscellaneousCharge.Appearance.Options.UseFont = True
        Me.ButtonEditMiscellaneousCharge.ImageIndex = 1
        Me.ButtonEditMiscellaneousCharge.ImageList = Me.ImageList16x16
        Me.ButtonEditMiscellaneousCharge.Location = New System.Drawing.Point(111, 426)
        Me.ButtonEditMiscellaneousCharge.LookAndFeel.SkinName = "Black"
        Me.ButtonEditMiscellaneousCharge.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonEditMiscellaneousCharge.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonEditMiscellaneousCharge.Name = "ButtonEditMiscellaneousCharge"
        Me.ButtonEditMiscellaneousCharge.Size = New System.Drawing.Size(96, 30)
        Me.ButtonEditMiscellaneousCharge.TabIndex = 3
        Me.ButtonEditMiscellaneousCharge.Text = "Edit"
        '
        'ButtonRemoveMiscellaneousCharge
        '
        Me.ButtonRemoveMiscellaneousCharge.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonRemoveMiscellaneousCharge.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonRemoveMiscellaneousCharge.Appearance.Options.UseFont = True
        Me.ButtonRemoveMiscellaneousCharge.ImageIndex = 2
        Me.ButtonRemoveMiscellaneousCharge.ImageList = Me.ImageList16x16
        Me.ButtonRemoveMiscellaneousCharge.Location = New System.Drawing.Point(215, 426)
        Me.ButtonRemoveMiscellaneousCharge.LookAndFeel.SkinName = "Black"
        Me.ButtonRemoveMiscellaneousCharge.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonRemoveMiscellaneousCharge.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonRemoveMiscellaneousCharge.Name = "ButtonRemoveMiscellaneousCharge"
        Me.ButtonRemoveMiscellaneousCharge.Size = New System.Drawing.Size(96, 30)
        Me.ButtonRemoveMiscellaneousCharge.TabIndex = 4
        Me.ButtonRemoveMiscellaneousCharge.Text = "Remove"
        '
        'ButtonAddMiscellaneousCharge
        '
        Me.ButtonAddMiscellaneousCharge.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAddMiscellaneousCharge.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAddMiscellaneousCharge.Appearance.Options.UseFont = True
        Me.ButtonAddMiscellaneousCharge.ImageIndex = 0
        Me.ButtonAddMiscellaneousCharge.ImageList = Me.ImageList16x16
        Me.ButtonAddMiscellaneousCharge.Location = New System.Drawing.Point(6, 426)
        Me.ButtonAddMiscellaneousCharge.LookAndFeel.SkinName = "Black"
        Me.ButtonAddMiscellaneousCharge.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAddMiscellaneousCharge.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonAddMiscellaneousCharge.Name = "ButtonAddMiscellaneousCharge"
        Me.ButtonAddMiscellaneousCharge.Size = New System.Drawing.Size(96, 30)
        Me.ButtonAddMiscellaneousCharge.TabIndex = 2
        Me.ButtonAddMiscellaneousCharge.Text = "Add"
        '
        'LabelSearchMiscellaneousCharge
        '
        Me.LabelSearchMiscellaneousCharge.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelSearchMiscellaneousCharge.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSearchMiscellaneousCharge.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelSearchMiscellaneousCharge.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelSearchMiscellaneousCharge.Location = New System.Drawing.Point(778, 432)
        Me.LabelSearchMiscellaneousCharge.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelSearchMiscellaneousCharge.Name = "LabelSearchMiscellaneousCharge"
        Me.LabelSearchMiscellaneousCharge.Size = New System.Drawing.Size(58, 17)
        Me.LabelSearchMiscellaneousCharge.TabIndex = 5
        Me.LabelSearchMiscellaneousCharge.Text = "Search:"
        '
        'TextEditSearchMiscellaneousCharge
        '
        Me.TextEditSearchMiscellaneousCharge.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchMiscellaneousCharge.EditValue = ""
        Me.TextEditSearchMiscellaneousCharge.Location = New System.Drawing.Point(843, 428)
        Me.TextEditSearchMiscellaneousCharge.Margin = New System.Windows.Forms.Padding(4, 5, 4, 7)
        Me.TextEditSearchMiscellaneousCharge.Name = "TextEditSearchMiscellaneousCharge"
        Me.TextEditSearchMiscellaneousCharge.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchMiscellaneousCharge.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchMiscellaneousCharge.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchMiscellaneousCharge.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchMiscellaneousCharge.Size = New System.Drawing.Size(103, 24)
        Me.TextEditSearchMiscellaneousCharge.TabIndex = 6
        '
        'PictureClearSearchMiscellaneousCharges
        '
        Me.PictureClearSearchMiscellaneousCharges.AllowDrop = True
        Me.PictureClearSearchMiscellaneousCharges.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchMiscellaneousCharges.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchMiscellaneousCharges.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchMiscellaneousCharges.Location = New System.Drawing.Point(954, 4)
        Me.PictureClearSearchMiscellaneousCharges.Margin = New System.Windows.Forms.Padding(4)
        Me.PictureClearSearchMiscellaneousCharges.Name = "PictureClearSearchMiscellaneousCharges"
        Me.PictureClearSearchMiscellaneousCharges.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchMiscellaneousCharges.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchMiscellaneousCharges.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchMiscellaneousCharges.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem5.Text = "Clear Search"
        ToolTipItem5.LeftIndent = 6
        ToolTipItem5.Text = "Click here to clear all search boxes."
        SuperToolTip5.Items.Add(ToolTipTitleItem5)
        SuperToolTip5.Items.Add(ToolTipItem5)
        Me.PictureClearSearchMiscellaneousCharges.SuperTip = SuperToolTip5
        Me.PictureClearSearchMiscellaneousCharges.TabIndex = 0
        Me.PictureClearSearchMiscellaneousCharges.TabStop = True
        '
        'PictureAdvancedSearchMiscellaneousCharge
        '
        Me.PictureAdvancedSearchMiscellaneousCharge.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearchMiscellaneousCharge.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearchMiscellaneousCharge.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearchMiscellaneousCharge.Location = New System.Drawing.Point(954, 430)
        Me.PictureAdvancedSearchMiscellaneousCharge.Margin = New System.Windows.Forms.Padding(4, 8, 4, 9)
        Me.PictureAdvancedSearchMiscellaneousCharge.Name = "PictureAdvancedSearchMiscellaneousCharge"
        Me.PictureAdvancedSearchMiscellaneousCharge.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearchMiscellaneousCharge.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearchMiscellaneousCharge.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearchMiscellaneousCharge.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem6.Text = "Advanced Search"
        ToolTipItem6.LeftIndent = 6
        ToolTipItem6.Text = "Click here to search individual column values."
        SuperToolTip6.Items.Add(ToolTipTitleItem6)
        SuperToolTip6.Items.Add(ToolTipItem6)
        Me.PictureAdvancedSearchMiscellaneousCharge.SuperTip = SuperToolTip6
        Me.PictureAdvancedSearchMiscellaneousCharge.TabIndex = 7
        Me.PictureAdvancedSearchMiscellaneousCharge.TabStop = True
        '
        'GridMiscellaneousCharges
        '
        Me.GridMiscellaneousCharges.AllowUserToAddRows = False
        Me.GridMiscellaneousCharges.AllowUserToDeleteRows = False
        Me.GridMiscellaneousCharges.AllowUserToOrderColumns = True
        Me.GridMiscellaneousCharges.AllowUserToResizeRows = False
        DataGridViewCellStyle11.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridMiscellaneousCharges.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle11
        Me.GridMiscellaneousCharges.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridMiscellaneousCharges.BackgroundColor = System.Drawing.Color.White
        Me.GridMiscellaneousCharges.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridMiscellaneousCharges.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridMiscellaneousCharges.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle12.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle12.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle12.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle12.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle12.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle12.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle12.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridMiscellaneousCharges.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle12
        Me.GridMiscellaneousCharges.ColumnHeadersHeight = 22
        Me.GridMiscellaneousCharges.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridMiscellaneousCharges.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.MiscellaneousChargeNameColumn, Me.MiscellaneousChargeAmountColumn})
        Me.GridMiscellaneousCharges.EnableHeadersVisualStyles = False
        Me.GridMiscellaneousCharges.GridColor = System.Drawing.Color.White
        Me.GridMiscellaneousCharges.Location = New System.Drawing.Point(3, 29)
        Me.GridMiscellaneousCharges.Margin = New System.Windows.Forms.Padding(4)
        Me.GridMiscellaneousCharges.Name = "GridMiscellaneousCharges"
        Me.GridMiscellaneousCharges.ReadOnly = True
        Me.GridMiscellaneousCharges.RowHeadersVisible = False
        Me.GridMiscellaneousCharges.RowHeadersWidth = 51
        DataGridViewCellStyle14.ForeColor = System.Drawing.Color.DimGray
        Me.GridMiscellaneousCharges.RowsDefaultCellStyle = DataGridViewCellStyle14
        Me.GridMiscellaneousCharges.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridMiscellaneousCharges.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridMiscellaneousCharges.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridMiscellaneousCharges.RowTemplate.Height = 19
        Me.GridMiscellaneousCharges.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridMiscellaneousCharges.ShowCellToolTips = False
        Me.GridMiscellaneousCharges.Size = New System.Drawing.Size(976, 390)
        Me.GridMiscellaneousCharges.StandardTab = True
        Me.GridMiscellaneousCharges.TabIndex = 1
        '
        'MiscellaneousChargeNameColumn
        '
        Me.MiscellaneousChargeNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.MiscellaneousChargeNameColumn.DataPropertyName = "MiscellaneousChargeName"
        Me.MiscellaneousChargeNameColumn.HeaderText = "Description"
        Me.MiscellaneousChargeNameColumn.MinimumWidth = 6
        Me.MiscellaneousChargeNameColumn.Name = "MiscellaneousChargeNameColumn"
        Me.MiscellaneousChargeNameColumn.ReadOnly = True
        '
        'MiscellaneousChargeAmountColumn
        '
        Me.MiscellaneousChargeAmountColumn.DataPropertyName = "MiscellaneousChargeAmount"
        DataGridViewCellStyle13.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleRight
        DataGridViewCellStyle13.Format = "C2"
        DataGridViewCellStyle13.NullValue = Nothing
        Me.MiscellaneousChargeAmountColumn.DefaultCellStyle = DataGridViewCellStyle13
        Me.MiscellaneousChargeAmountColumn.HeaderText = "Amount"
        Me.MiscellaneousChargeAmountColumn.MinimumWidth = 6
        Me.MiscellaneousChargeAmountColumn.Name = "MiscellaneousChargeAmountColumn"
        Me.MiscellaneousChargeAmountColumn.ReadOnly = True
        Me.MiscellaneousChargeAmountColumn.Width = 125
        '
        'TabPageMediaCosts
        '
        Me.TabPageMediaCosts.Controls.Add(Me.PanelMediaCosts)
        Me.TabPageMediaCosts.Name = "TabPageMediaCosts"
        Me.TabPageMediaCosts.Size = New System.Drawing.Size(1021, 539)
        Me.TabPageMediaCosts.Text = "Media Costs"
        '
        'PanelMediaCosts
        '
        Me.PanelMediaCosts.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelMediaCosts.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelMediaCosts.Controls.Add(Me.GroupControlCostEstimates)
        Me.PanelMediaCosts.Controls.Add(Me.GroupControlInvoices)
        Me.PanelMediaCosts.Controls.Add(Me.GroupControlMediaCosts)
        Me.PanelMediaCosts.Location = New System.Drawing.Point(4, 6)
        Me.PanelMediaCosts.Margin = New System.Windows.Forms.Padding(4)
        Me.PanelMediaCosts.Name = "PanelMediaCosts"
        Me.PanelMediaCosts.Padding = New System.Windows.Forms.Padding(12)
        Me.PanelMediaCosts.Size = New System.Drawing.Size(1012, 527)
        Me.PanelMediaCosts.TabIndex = 2
        '
        'GroupControlCostEstimates
        '
        Me.GroupControlCostEstimates.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlCostEstimates.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlCostEstimates.Appearance.Options.UseFont = True
        Me.GroupControlCostEstimates.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlCostEstimates.AppearanceCaption.ForeColor = System.Drawing.Color.DimGray
        Me.GroupControlCostEstimates.AppearanceCaption.Options.UseFont = True
        Me.GroupControlCostEstimates.AppearanceCaption.Options.UseForeColor = True
        Me.GroupControlCostEstimates.Controls.Add(Me.LabelTotalCEAmount)
        Me.GroupControlCostEstimates.Controls.Add(Me.ButtonEditCostEstimate)
        Me.GroupControlCostEstimates.Controls.Add(Me.ButtonDeleteCostEstimate)
        Me.GroupControlCostEstimates.Controls.Add(Me.GridCostEstimates)
        Me.GroupControlCostEstimates.Controls.Add(Me.ButtonAddCostEstimate)
        Me.GroupControlCostEstimates.Location = New System.Drawing.Point(515, 301)
        Me.GroupControlCostEstimates.LookAndFeel.SkinName = "Black"
        Me.GroupControlCostEstimates.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlCostEstimates.Margin = New System.Windows.Forms.Padding(4, 4, 4, 12)
        Me.GroupControlCostEstimates.Name = "GroupControlCostEstimates"
        Me.GroupControlCostEstimates.Size = New System.Drawing.Size(481, 220)
        Me.GroupControlCostEstimates.TabIndex = 8
        Me.GroupControlCostEstimates.Tag = ""
        Me.GroupControlCostEstimates.Text = "Cost Estimates"
        '
        'LabelTotalCEAmount
        '
        Me.LabelTotalCEAmount.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelTotalCEAmount.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelTotalCEAmount.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelTotalCEAmount.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.LabelTotalCEAmount.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelTotalCEAmount.Location = New System.Drawing.Point(371, 193)
        Me.LabelTotalCEAmount.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelTotalCEAmount.Name = "LabelTotalCEAmount"
        Me.LabelTotalCEAmount.Size = New System.Drawing.Size(103, 17)
        Me.LabelTotalCEAmount.TabIndex = 11
        Me.LabelTotalCEAmount.Text = "R 000,000.00"
        '
        'ButtonEditCostEstimate
        '
        Me.ButtonEditCostEstimate.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonEditCostEstimate.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonEditCostEstimate.Appearance.Options.UseFont = True
        Me.ButtonEditCostEstimate.ImageIndex = 1
        Me.ButtonEditCostEstimate.ImageList = Me.ImageList16x16
        Me.ButtonEditCostEstimate.Location = New System.Drawing.Point(110, 184)
        Me.ButtonEditCostEstimate.LookAndFeel.SkinName = "Black"
        Me.ButtonEditCostEstimate.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonEditCostEstimate.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonEditCostEstimate.Name = "ButtonEditCostEstimate"
        Me.ButtonEditCostEstimate.Size = New System.Drawing.Size(96, 30)
        Me.ButtonEditCostEstimate.TabIndex = 9
        Me.ButtonEditCostEstimate.Text = "Edit"
        '
        'ButtonDeleteCostEstimate
        '
        Me.ButtonDeleteCostEstimate.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonDeleteCostEstimate.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonDeleteCostEstimate.Appearance.Options.UseFont = True
        Me.ButtonDeleteCostEstimate.ImageIndex = 2
        Me.ButtonDeleteCostEstimate.ImageList = Me.ImageList16x16
        Me.ButtonDeleteCostEstimate.Location = New System.Drawing.Point(214, 184)
        Me.ButtonDeleteCostEstimate.LookAndFeel.SkinName = "Black"
        Me.ButtonDeleteCostEstimate.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonDeleteCostEstimate.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonDeleteCostEstimate.Name = "ButtonDeleteCostEstimate"
        Me.ButtonDeleteCostEstimate.Size = New System.Drawing.Size(96, 30)
        Me.ButtonDeleteCostEstimate.TabIndex = 10
        Me.ButtonDeleteCostEstimate.Text = "Remove"
        '
        'GridCostEstimates
        '
        Me.GridCostEstimates.AllowUserToAddRows = False
        Me.GridCostEstimates.AllowUserToDeleteRows = False
        Me.GridCostEstimates.AllowUserToOrderColumns = True
        Me.GridCostEstimates.AllowUserToResizeRows = False
        DataGridViewCellStyle15.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridCostEstimates.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle15
        Me.GridCostEstimates.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridCostEstimates.BackgroundColor = System.Drawing.Color.White
        Me.GridCostEstimates.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridCostEstimates.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridCostEstimates.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle16.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle16.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle16.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle16.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle16.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle16.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle16.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridCostEstimates.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle16
        Me.GridCostEstimates.ColumnHeadersHeight = 22
        Me.GridCostEstimates.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridCostEstimates.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.DataGridViewTextBoxColumn4, Me.ColumnMediaName, Me.MediaId, Me.ColumnCostEstimateAmount})
        Me.GridCostEstimates.EnableHeadersVisualStyles = False
        Me.GridCostEstimates.GridColor = System.Drawing.Color.White
        Me.GridCostEstimates.Location = New System.Drawing.Point(7, 29)
        Me.GridCostEstimates.Margin = New System.Windows.Forms.Padding(4)
        Me.GridCostEstimates.Name = "GridCostEstimates"
        Me.GridCostEstimates.ReadOnly = True
        Me.GridCostEstimates.RowHeadersVisible = False
        Me.GridCostEstimates.RowHeadersWidth = 51
        DataGridViewCellStyle19.ForeColor = System.Drawing.Color.DimGray
        Me.GridCostEstimates.RowsDefaultCellStyle = DataGridViewCellStyle19
        Me.GridCostEstimates.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridCostEstimates.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridCostEstimates.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridCostEstimates.RowTemplate.Height = 19
        Me.GridCostEstimates.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridCostEstimates.ShowCellToolTips = False
        Me.GridCostEstimates.Size = New System.Drawing.Size(467, 147)
        Me.GridCostEstimates.StandardTab = True
        Me.GridCostEstimates.TabIndex = 2
        '
        'DataGridViewTextBoxColumn4
        '
        Me.DataGridViewTextBoxColumn4.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.DataGridViewTextBoxColumn4.DataPropertyName = "CostEstimateNumber"
        DataGridViewCellStyle17.Format = "N2"
        DataGridViewCellStyle17.NullValue = Nothing
        Me.DataGridViewTextBoxColumn4.DefaultCellStyle = DataGridViewCellStyle17
        Me.DataGridViewTextBoxColumn4.HeaderText = "Cost Estimate Number"
        Me.DataGridViewTextBoxColumn4.MinimumWidth = 6
        Me.DataGridViewTextBoxColumn4.Name = "DataGridViewTextBoxColumn4"
        Me.DataGridViewTextBoxColumn4.ReadOnly = True
        '
        'ColumnMediaName
        '
        Me.ColumnMediaName.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.ColumnMediaName.DataPropertyName = "MediaName"
        Me.ColumnMediaName.HeaderText = "Media"
        Me.ColumnMediaName.Name = "ColumnMediaName"
        Me.ColumnMediaName.ReadOnly = True
        '
        'MediaId
        '
        Me.MediaId.DataPropertyName = "MediaId"
        Me.MediaId.HeaderText = "MediaIdColumn"
        Me.MediaId.Name = "MediaId"
        Me.MediaId.ReadOnly = True
        Me.MediaId.Visible = False
        '
        'ColumnCostEstimateAmount
        '
        Me.ColumnCostEstimateAmount.DataPropertyName = "CostEstimateAmount"
        DataGridViewCellStyle18.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleRight
        DataGridViewCellStyle18.Format = "c2"
        Me.ColumnCostEstimateAmount.DefaultCellStyle = DataGridViewCellStyle18
        Me.ColumnCostEstimateAmount.HeaderText = "Amount"
        Me.ColumnCostEstimateAmount.Name = "ColumnCostEstimateAmount"
        Me.ColumnCostEstimateAmount.ReadOnly = True
        '
        'ButtonAddCostEstimate
        '
        Me.ButtonAddCostEstimate.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAddCostEstimate.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAddCostEstimate.Appearance.Options.UseFont = True
        Me.ButtonAddCostEstimate.ImageIndex = 0
        Me.ButtonAddCostEstimate.ImageList = Me.ImageList16x16
        Me.ButtonAddCostEstimate.Location = New System.Drawing.Point(5, 184)
        Me.ButtonAddCostEstimate.LookAndFeel.SkinName = "Black"
        Me.ButtonAddCostEstimate.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAddCostEstimate.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonAddCostEstimate.Name = "ButtonAddCostEstimate"
        Me.ButtonAddCostEstimate.Size = New System.Drawing.Size(96, 30)
        Me.ButtonAddCostEstimate.TabIndex = 8
        Me.ButtonAddCostEstimate.Text = "Add"
        '
        'GroupControlInvoices
        '
        Me.GroupControlInvoices.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlInvoices.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlInvoices.Appearance.Options.UseFont = True
        Me.GroupControlInvoices.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlInvoices.AppearanceCaption.ForeColor = System.Drawing.Color.DimGray
        Me.GroupControlInvoices.AppearanceCaption.Options.UseFont = True
        Me.GroupControlInvoices.AppearanceCaption.Options.UseForeColor = True
        Me.GroupControlInvoices.Controls.Add(Me.LabelTotalInvoiceAmount)
        Me.GroupControlInvoices.Controls.Add(Me.ButtonEditInvoiceNumber)
        Me.GroupControlInvoices.Controls.Add(Me.ButtonDeleteInvoiceNumber)
        Me.GroupControlInvoices.Controls.Add(Me.ButtonAddInvoiceNumber)
        Me.GroupControlInvoices.Controls.Add(Me.GridInvoiceNumbers)
        Me.GroupControlInvoices.Location = New System.Drawing.Point(16, 301)
        Me.GroupControlInvoices.LookAndFeel.SkinName = "Black"
        Me.GroupControlInvoices.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlInvoices.Margin = New System.Windows.Forms.Padding(4, 4, 4, 12)
        Me.GroupControlInvoices.Name = "GroupControlInvoices"
        Me.GroupControlInvoices.Size = New System.Drawing.Size(491, 220)
        Me.GroupControlInvoices.TabIndex = 7
        Me.GroupControlInvoices.Tag = ""
        Me.GroupControlInvoices.Text = "Invoices"
        '
        'LabelTotalInvoiceAmount
        '
        Me.LabelTotalInvoiceAmount.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelTotalInvoiceAmount.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelTotalInvoiceAmount.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelTotalInvoiceAmount.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.LabelTotalInvoiceAmount.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelTotalInvoiceAmount.Location = New System.Drawing.Point(381, 193)
        Me.LabelTotalInvoiceAmount.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelTotalInvoiceAmount.Name = "LabelTotalInvoiceAmount"
        Me.LabelTotalInvoiceAmount.Size = New System.Drawing.Size(103, 17)
        Me.LabelTotalInvoiceAmount.TabIndex = 8
        Me.LabelTotalInvoiceAmount.Text = "R 000,000.00"
        '
        'ButtonEditInvoiceNumber
        '
        Me.ButtonEditInvoiceNumber.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonEditInvoiceNumber.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonEditInvoiceNumber.Appearance.Options.UseFont = True
        Me.ButtonEditInvoiceNumber.ImageIndex = 1
        Me.ButtonEditInvoiceNumber.ImageList = Me.ImageList16x16
        Me.ButtonEditInvoiceNumber.Location = New System.Drawing.Point(108, 184)
        Me.ButtonEditInvoiceNumber.LookAndFeel.SkinName = "Black"
        Me.ButtonEditInvoiceNumber.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonEditInvoiceNumber.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonEditInvoiceNumber.Name = "ButtonEditInvoiceNumber"
        Me.ButtonEditInvoiceNumber.Size = New System.Drawing.Size(96, 30)
        Me.ButtonEditInvoiceNumber.TabIndex = 6
        Me.ButtonEditInvoiceNumber.Text = "Edit"
        '
        'ButtonDeleteInvoiceNumber
        '
        Me.ButtonDeleteInvoiceNumber.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonDeleteInvoiceNumber.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonDeleteInvoiceNumber.Appearance.Options.UseFont = True
        Me.ButtonDeleteInvoiceNumber.ImageIndex = 2
        Me.ButtonDeleteInvoiceNumber.ImageList = Me.ImageList16x16
        Me.ButtonDeleteInvoiceNumber.Location = New System.Drawing.Point(212, 184)
        Me.ButtonDeleteInvoiceNumber.LookAndFeel.SkinName = "Black"
        Me.ButtonDeleteInvoiceNumber.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonDeleteInvoiceNumber.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonDeleteInvoiceNumber.Name = "ButtonDeleteInvoiceNumber"
        Me.ButtonDeleteInvoiceNumber.Size = New System.Drawing.Size(96, 30)
        Me.ButtonDeleteInvoiceNumber.TabIndex = 7
        Me.ButtonDeleteInvoiceNumber.Text = "Remove"
        '
        'ButtonAddInvoiceNumber
        '
        Me.ButtonAddInvoiceNumber.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAddInvoiceNumber.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAddInvoiceNumber.Appearance.Options.UseFont = True
        Me.ButtonAddInvoiceNumber.ImageIndex = 0
        Me.ButtonAddInvoiceNumber.ImageList = Me.ImageList16x16
        Me.ButtonAddInvoiceNumber.Location = New System.Drawing.Point(3, 184)
        Me.ButtonAddInvoiceNumber.LookAndFeel.SkinName = "Black"
        Me.ButtonAddInvoiceNumber.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAddInvoiceNumber.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonAddInvoiceNumber.Name = "ButtonAddInvoiceNumber"
        Me.ButtonAddInvoiceNumber.Size = New System.Drawing.Size(96, 30)
        Me.ButtonAddInvoiceNumber.TabIndex = 5
        Me.ButtonAddInvoiceNumber.Text = "Add"
        '
        'GridInvoiceNumbers
        '
        Me.GridInvoiceNumbers.AllowUserToAddRows = False
        Me.GridInvoiceNumbers.AllowUserToDeleteRows = False
        Me.GridInvoiceNumbers.AllowUserToOrderColumns = True
        Me.GridInvoiceNumbers.AllowUserToResizeRows = False
        DataGridViewCellStyle20.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridInvoiceNumbers.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle20
        Me.GridInvoiceNumbers.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridInvoiceNumbers.BackgroundColor = System.Drawing.Color.White
        Me.GridInvoiceNumbers.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridInvoiceNumbers.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridInvoiceNumbers.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle21.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle21.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle21.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle21.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle21.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle21.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle21.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridInvoiceNumbers.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle21
        Me.GridInvoiceNumbers.ColumnHeadersHeight = 22
        Me.GridInvoiceNumbers.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridInvoiceNumbers.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.DataGridViewTextBoxColumn1, Me.MediaIdInvoices, Me.ColumnMediaNameInvoices, Me.ColumnInvoiceAmount})
        Me.GridInvoiceNumbers.EnableHeadersVisualStyles = False
        Me.GridInvoiceNumbers.GridColor = System.Drawing.Color.White
        Me.GridInvoiceNumbers.Location = New System.Drawing.Point(7, 29)
        Me.GridInvoiceNumbers.Margin = New System.Windows.Forms.Padding(4)
        Me.GridInvoiceNumbers.Name = "GridInvoiceNumbers"
        Me.GridInvoiceNumbers.ReadOnly = True
        Me.GridInvoiceNumbers.RowHeadersVisible = False
        Me.GridInvoiceNumbers.RowHeadersWidth = 51
        DataGridViewCellStyle24.ForeColor = System.Drawing.Color.DimGray
        Me.GridInvoiceNumbers.RowsDefaultCellStyle = DataGridViewCellStyle24
        Me.GridInvoiceNumbers.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridInvoiceNumbers.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridInvoiceNumbers.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridInvoiceNumbers.RowTemplate.Height = 19
        Me.GridInvoiceNumbers.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridInvoiceNumbers.ShowCellToolTips = False
        Me.GridInvoiceNumbers.Size = New System.Drawing.Size(477, 147)
        Me.GridInvoiceNumbers.StandardTab = True
        Me.GridInvoiceNumbers.TabIndex = 2
        '
        'DataGridViewTextBoxColumn1
        '
        Me.DataGridViewTextBoxColumn1.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.DataGridViewTextBoxColumn1.DataPropertyName = "InvoiceNumber"
        DataGridViewCellStyle22.Format = "N2"
        DataGridViewCellStyle22.NullValue = Nothing
        Me.DataGridViewTextBoxColumn1.DefaultCellStyle = DataGridViewCellStyle22
        Me.DataGridViewTextBoxColumn1.HeaderText = "Invoice Number"
        Me.DataGridViewTextBoxColumn1.MinimumWidth = 6
        Me.DataGridViewTextBoxColumn1.Name = "DataGridViewTextBoxColumn1"
        Me.DataGridViewTextBoxColumn1.ReadOnly = True
        '
        'MediaIdInvoices
        '
        Me.MediaIdInvoices.DataPropertyName = "MediaId"
        Me.MediaIdInvoices.HeaderText = "MediaId"
        Me.MediaIdInvoices.Name = "MediaIdInvoices"
        Me.MediaIdInvoices.ReadOnly = True
        Me.MediaIdInvoices.Visible = False
        '
        'ColumnMediaNameInvoices
        '
        Me.ColumnMediaNameInvoices.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.ColumnMediaNameInvoices.DataPropertyName = "MediaName"
        Me.ColumnMediaNameInvoices.HeaderText = "Media"
        Me.ColumnMediaNameInvoices.Name = "ColumnMediaNameInvoices"
        Me.ColumnMediaNameInvoices.ReadOnly = True
        '
        'ColumnInvoiceAmount
        '
        Me.ColumnInvoiceAmount.DataPropertyName = "InvoiceAmount"
        DataGridViewCellStyle23.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleRight
        DataGridViewCellStyle23.Format = "c2"
        Me.ColumnInvoiceAmount.DefaultCellStyle = DataGridViewCellStyle23
        Me.ColumnInvoiceAmount.HeaderText = "Amount"
        Me.ColumnInvoiceAmount.Name = "ColumnInvoiceAmount"
        Me.ColumnInvoiceAmount.ReadOnly = True
        '
        'GroupControlMediaCosts
        '
        Me.GroupControlMediaCosts.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlMediaCosts.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlMediaCosts.Appearance.Options.UseFont = True
        Me.GroupControlMediaCosts.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlMediaCosts.AppearanceCaption.ForeColor = System.Drawing.Color.DimGray
        Me.GroupControlMediaCosts.AppearanceCaption.Options.UseFont = True
        Me.GroupControlMediaCosts.AppearanceCaption.Options.UseForeColor = True
        Me.GroupControlMediaCosts.Controls.Add(Me.LabelTotalMediaCostSystemCalculated)
        Me.GroupControlMediaCosts.Controls.Add(Me.LabelTotalMediaCostUserInputted)
        Me.GroupControlMediaCosts.Controls.Add(Me.ButtonEditMediaCost)
        Me.GroupControlMediaCosts.Controls.Add(Me.GridMediaCosts)
        Me.GroupControlMediaCosts.Location = New System.Drawing.Point(15, 16)
        Me.GroupControlMediaCosts.LookAndFeel.SkinName = "Black"
        Me.GroupControlMediaCosts.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlMediaCosts.Margin = New System.Windows.Forms.Padding(4, 4, 4, 12)
        Me.GroupControlMediaCosts.Name = "GroupControlMediaCosts"
        Me.GroupControlMediaCosts.Size = New System.Drawing.Size(981, 279)
        Me.GroupControlMediaCosts.TabIndex = 2
        Me.GroupControlMediaCosts.Tag = ""
        Me.GroupControlMediaCosts.Text = "Media Costs"
        '
        'LabelTotalMediaCostSystemCalculated
        '
        Me.LabelTotalMediaCostSystemCalculated.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelTotalMediaCostSystemCalculated.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelTotalMediaCostSystemCalculated.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelTotalMediaCostSystemCalculated.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.LabelTotalMediaCostSystemCalculated.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelTotalMediaCostSystemCalculated.Location = New System.Drawing.Point(620, 243)
        Me.LabelTotalMediaCostSystemCalculated.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelTotalMediaCostSystemCalculated.Name = "LabelTotalMediaCostSystemCalculated"
        Me.LabelTotalMediaCostSystemCalculated.Size = New System.Drawing.Size(173, 17)
        Me.LabelTotalMediaCostSystemCalculated.TabIndex = 7
        Me.LabelTotalMediaCostSystemCalculated.Text = "R 000,000.00"
        '
        'LabelTotalMediaCostUserInputted
        '
        Me.LabelTotalMediaCostUserInputted.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelTotalMediaCostUserInputted.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelTotalMediaCostUserInputted.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelTotalMediaCostUserInputted.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.LabelTotalMediaCostUserInputted.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelTotalMediaCostUserInputted.Location = New System.Drawing.Point(801, 243)
        Me.LabelTotalMediaCostUserInputted.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelTotalMediaCostUserInputted.Name = "LabelTotalMediaCostUserInputted"
        Me.LabelTotalMediaCostUserInputted.Size = New System.Drawing.Size(173, 17)
        Me.LabelTotalMediaCostUserInputted.TabIndex = 5
        Me.LabelTotalMediaCostUserInputted.Text = "R 000,000.00"
        '
        'ButtonEditMediaCost
        '
        Me.ButtonEditMediaCost.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonEditMediaCost.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonEditMediaCost.Appearance.Options.UseFont = True
        Me.ButtonEditMediaCost.ImageIndex = 1
        Me.ButtonEditMediaCost.ImageList = Me.ImageList16x16
        Me.ButtonEditMediaCost.Location = New System.Drawing.Point(7, 243)
        Me.ButtonEditMediaCost.LookAndFeel.SkinName = "Black"
        Me.ButtonEditMediaCost.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonEditMediaCost.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonEditMediaCost.Name = "ButtonEditMediaCost"
        Me.ButtonEditMediaCost.Size = New System.Drawing.Size(96, 30)
        Me.ButtonEditMediaCost.TabIndex = 6
        Me.ButtonEditMediaCost.Text = "Edit"
        '
        'GridMediaCosts
        '
        Me.GridMediaCosts.AllowUserToAddRows = False
        Me.GridMediaCosts.AllowUserToDeleteRows = False
        Me.GridMediaCosts.AllowUserToOrderColumns = True
        Me.GridMediaCosts.AllowUserToResizeRows = False
        DataGridViewCellStyle25.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridMediaCosts.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle25
        Me.GridMediaCosts.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridMediaCosts.BackgroundColor = System.Drawing.Color.White
        Me.GridMediaCosts.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridMediaCosts.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridMediaCosts.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle26.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle26.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle26.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle26.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle26.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle26.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle26.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridMediaCosts.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle26
        Me.GridMediaCosts.ColumnHeadersHeight = 22
        Me.GridMediaCosts.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridMediaCosts.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.MeidaCostMediaNameColumn, Me.MediaCostSystemCalculatedColumn, Me.MediaCostUserInputedCostColumn})
        Me.GridMediaCosts.EnableHeadersVisualStyles = False
        Me.GridMediaCosts.GridColor = System.Drawing.Color.White
        Me.GridMediaCosts.Location = New System.Drawing.Point(7, 29)
        Me.GridMediaCosts.Margin = New System.Windows.Forms.Padding(4)
        Me.GridMediaCosts.Name = "GridMediaCosts"
        Me.GridMediaCosts.ReadOnly = True
        Me.GridMediaCosts.RowHeadersVisible = False
        Me.GridMediaCosts.RowHeadersWidth = 51
        DataGridViewCellStyle30.ForeColor = System.Drawing.Color.DimGray
        Me.GridMediaCosts.RowsDefaultCellStyle = DataGridViewCellStyle30
        Me.GridMediaCosts.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridMediaCosts.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridMediaCosts.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridMediaCosts.RowTemplate.Height = 19
        Me.GridMediaCosts.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridMediaCosts.ShowCellToolTips = False
        Me.GridMediaCosts.Size = New System.Drawing.Size(967, 206)
        Me.GridMediaCosts.StandardTab = True
        Me.GridMediaCosts.TabIndex = 2
        '
        'MeidaCostMediaNameColumn
        '
        Me.MeidaCostMediaNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.MeidaCostMediaNameColumn.DataPropertyName = "MediaName"
        DataGridViewCellStyle27.Format = "N2"
        DataGridViewCellStyle27.NullValue = Nothing
        Me.MeidaCostMediaNameColumn.DefaultCellStyle = DataGridViewCellStyle27
        Me.MeidaCostMediaNameColumn.HeaderText = "Media"
        Me.MeidaCostMediaNameColumn.MinimumWidth = 6
        Me.MeidaCostMediaNameColumn.Name = "MeidaCostMediaNameColumn"
        Me.MeidaCostMediaNameColumn.ReadOnly = True
        '
        'MediaCostSystemCalculatedColumn
        '
        Me.MediaCostSystemCalculatedColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.ColumnHeader
        Me.MediaCostSystemCalculatedColumn.DataPropertyName = "MediaCostSystemCalculated"
        DataGridViewCellStyle28.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleRight
        DataGridViewCellStyle28.Format = "C2"
        DataGridViewCellStyle28.NullValue = Nothing
        Me.MediaCostSystemCalculatedColumn.DefaultCellStyle = DataGridViewCellStyle28
        Me.MediaCostSystemCalculatedColumn.HeaderText = "System Calculated Cost"
        Me.MediaCostSystemCalculatedColumn.MinimumWidth = 6
        Me.MediaCostSystemCalculatedColumn.Name = "MediaCostSystemCalculatedColumn"
        Me.MediaCostSystemCalculatedColumn.ReadOnly = True
        Me.MediaCostSystemCalculatedColumn.Resizable = System.Windows.Forms.DataGridViewTriState.[True]
        Me.MediaCostSystemCalculatedColumn.Width = 203
        '
        'MediaCostUserInputedCostColumn
        '
        Me.MediaCostUserInputedCostColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.ColumnHeader
        Me.MediaCostUserInputedCostColumn.DataPropertyName = "MediaCostInputed"
        DataGridViewCellStyle29.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleRight
        DataGridViewCellStyle29.Format = "c2"
        Me.MediaCostUserInputedCostColumn.DefaultCellStyle = DataGridViewCellStyle29
        Me.MediaCostUserInputedCostColumn.HeaderText = "User Inputted Cost"
        Me.MediaCostUserInputedCostColumn.Name = "MediaCostUserInputedCostColumn"
        Me.MediaCostUserInputedCostColumn.ReadOnly = True
        Me.MediaCostUserInputedCostColumn.Width = 172
        '
        'TabPageErrors
        '
        Me.TabPageErrors.Appearance.Header.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TabPageErrors.Appearance.Header.ForeColor = System.Drawing.Color.Firebrick
        Me.TabPageErrors.Appearance.Header.Options.UseFont = True
        Me.TabPageErrors.Appearance.Header.Options.UseForeColor = True
        Me.TabPageErrors.Appearance.HeaderActive.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TabPageErrors.Appearance.HeaderActive.ForeColor = System.Drawing.Color.Firebrick
        Me.TabPageErrors.Appearance.HeaderActive.Options.UseFont = True
        Me.TabPageErrors.Appearance.HeaderActive.Options.UseForeColor = True
        Me.TabPageErrors.Controls.Add(Me.PanelErrors)
        Me.TabPageErrors.Margin = New System.Windows.Forms.Padding(4)
        Me.TabPageErrors.Name = "TabPageErrors"
        Me.TabPageErrors.PageVisible = False
        Me.TabPageErrors.Size = New System.Drawing.Size(1021, 539)
        Me.TabPageErrors.Text = "Errors"
        '
        'PanelErrors
        '
        Me.PanelErrors.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelErrors.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelErrors.Controls.Add(Me.MemoEditErrors)
        Me.PanelErrors.Location = New System.Drawing.Point(4, 4)
        Me.PanelErrors.Margin = New System.Windows.Forms.Padding(4)
        Me.PanelErrors.Name = "PanelErrors"
        Me.PanelErrors.Padding = New System.Windows.Forms.Padding(12)
        Me.PanelErrors.Size = New System.Drawing.Size(1012, 527)
        Me.PanelErrors.TabIndex = 6
        '
        'MemoEditErrors
        '
        Me.MemoEditErrors.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.MemoEditErrors.Location = New System.Drawing.Point(15, 16)
        Me.MemoEditErrors.Margin = New System.Windows.Forms.Padding(4)
        Me.MemoEditErrors.Name = "MemoEditErrors"
        Me.MemoEditErrors.Properties.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.MemoEditErrors.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.MemoEditErrors.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.MemoEditErrors.Properties.Appearance.Options.UseBackColor = True
        Me.MemoEditErrors.Properties.Appearance.Options.UseFont = True
        Me.MemoEditErrors.Properties.Appearance.Options.UseForeColor = True
        Me.MemoEditErrors.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.MemoEditErrors.Properties.ReadOnly = True
        Me.MemoEditErrors.Properties.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.MemoEditErrors.Size = New System.Drawing.Size(981, 496)
        Me.MemoEditErrors.TabIndex = 0
        '
        'LabelErrors
        '
        Me.LabelErrors.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelErrors.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.LabelErrors.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelErrors.Appearance.ForeColor = System.Drawing.Color.WhiteSmoke
        Me.LabelErrors.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.LabelErrors.Location = New System.Drawing.Point(836, 16)
        Me.LabelErrors.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelErrors.Name = "LabelErrors"
        Me.LabelErrors.Size = New System.Drawing.Size(204, 17)
        Me.LabelErrors.TabIndex = 1
        Me.LabelErrors.Tag = "Forecolor=Backcolor"
        Me.LabelErrors.Text = "Errors found in the contract!"
        '
        'MediaNameColumn
        '
        Me.MediaNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.MediaNameColumn.DataPropertyName = "MediaName"
        Me.MediaNameColumn.HeaderText = "Media Service"
        Me.MediaNameColumn.MinimumWidth = 6
        Me.MediaNameColumn.Name = "MediaNameColumn"
        Me.MediaNameColumn.ReadOnly = True
        Me.MediaNameColumn.Width = 130
        '
        'ChainNameColumn
        '
        Me.ChainNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.ChainNameColumn.DataPropertyName = "ChainName"
        Me.ChainNameColumn.HeaderText = "Chain"
        Me.ChainNameColumn.MinimumWidth = 6
        Me.ChainNameColumn.Name = "ChainNameColumn"
        Me.ChainNameColumn.ReadOnly = True
        Me.ChainNameColumn.Width = 75
        '
        'CategoryListColumn
        '
        Me.CategoryListColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.CategoryListColumn.DataPropertyName = "Categories"
        Me.CategoryListColumn.HeaderText = "Installation Categories"
        Me.CategoryListColumn.MinimumWidth = 6
        Me.CategoryListColumn.Name = "CategoryListColumn"
        Me.CategoryListColumn.ReadOnly = True
        Me.CategoryListColumn.Width = 193
        '
        'InstallCategories
        '
        Me.InstallCategories.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.InstallCategories.DataPropertyName = "InstallCategories"
        Me.InstallCategories.HeaderText = "Homesite Category"
        Me.InstallCategories.Name = "InstallCategories"
        Me.InstallCategories.ReadOnly = True
        Me.InstallCategories.Width = 170
        '
        'InstallStoreQtyColumn
        '
        Me.InstallStoreQtyColumn.DataPropertyName = "InstallStoreQty"
        DataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        Me.InstallStoreQtyColumn.DefaultCellStyle = DataGridViewCellStyle3
        Me.InstallStoreQtyColumn.HeaderText = "Stores"
        Me.InstallStoreQtyColumn.MinimumWidth = 6
        Me.InstallStoreQtyColumn.Name = "InstallStoreQtyColumn"
        Me.InstallStoreQtyColumn.ReadOnly = True
        Me.InstallStoreQtyColumn.Width = 70
        '
        'BurstWeeksColumn
        '
        Me.BurstWeeksColumn.DataPropertyName = "InstallWeeks"
        DataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        Me.BurstWeeksColumn.DefaultCellStyle = DataGridViewCellStyle4
        Me.BurstWeeksColumn.HeaderText = "Weeks"
        Me.BurstWeeksColumn.MinimumWidth = 6
        Me.BurstWeeksColumn.Name = "BurstWeeksColumn"
        Me.BurstWeeksColumn.ReadOnly = True
        Me.BurstWeeksColumn.Width = 70
        '
        'FirstWeekColumn
        '
        Me.FirstWeekColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.FirstWeekColumn.DataPropertyName = "FirstWeek"
        Me.FirstWeekColumn.HeaderText = "First Week"
        Me.FirstWeekColumn.MinimumWidth = 6
        Me.FirstWeekColumn.Name = "FirstWeekColumn"
        Me.FirstWeekColumn.ReadOnly = True
        Me.FirstWeekColumn.Width = 110
        '
        'LastWeekColumn
        '
        Me.LastWeekColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.LastWeekColumn.DataPropertyName = "LastWeek"
        Me.LastWeekColumn.HeaderText = "Last Week"
        Me.LastWeekColumn.MinimumWidth = 6
        Me.LastWeekColumn.Name = "LastWeekColumn"
        Me.LastWeekColumn.ReadOnly = True
        Me.LastWeekColumn.Width = 109
        '
        'BrandNameColumn
        '
        Me.BrandNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.BrandNameColumn.DataPropertyName = "BrandName"
        Me.BrandNameColumn.HeaderText = "Brand"
        Me.BrandNameColumn.MinimumWidth = 6
        Me.BrandNameColumn.Name = "BrandNameColumn"
        Me.BrandNameColumn.ReadOnly = True
        Me.BrandNameColumn.Width = 78
        '
        'ProductNameColumn
        '
        Me.ProductNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.ProductNameColumn.DataPropertyName = "ProductName"
        Me.ProductNameColumn.HeaderText = "Product"
        Me.ProductNameColumn.MinimumWidth = 6
        Me.ProductNameColumn.Name = "ProductNameColumn"
        Me.ProductNameColumn.ReadOnly = True
        Me.ProductNameColumn.Width = 90
        '
        'SubformContract
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(9.0!, 17.0!)
        Me.Controls.Add(Me.TabControl)
        Me.Controls.Add(Me.LabelErrors)
        Me.Controls.Add(Me.ButtonSave)
        Me.Controls.Add(Me.ButtonCancel)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(5)
        Me.Name = "SubformContract"
        Me.Padding = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.Controls.SetChildIndex(Me.ButtonCancel, 0)
        Me.Controls.SetChildIndex(Me.ButtonSave, 0)
        Me.Controls.SetChildIndex(Me.LabelErrors, 0)
        Me.Controls.SetChildIndex(Me.TabControl, 0)
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        CType(Me.TabControl, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabControl.ResumeLayout(False)
        Me.TabPageSummary.ResumeLayout(False)
        Me.PanelSummary.ResumeLayout(False)
        Me.TableLayoutPanelSummary.ResumeLayout(False)
        Me.PanelContractProperties.ResumeLayout(False)
        Me.PanelContractProperties.PerformLayout()
        Me.PanelSignature.ResumeLayout(False)
        Me.PanelSignature.PerformLayout()
        CType(Me.PictureSignatureStatus.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelAgencySummary.ResumeLayout(False)
        Me.PanelAgencySummary.PerformLayout()
        Me.TabPageAgency.ResumeLayout(False)
        Me.PanelAgency.ResumeLayout(False)
        Me.PanelCommOptions.ResumeLayout(False)
        Me.PanelCommOptions.PerformLayout()
        CType(Me.TextEditCommission.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditPrintAgencyComm.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditPercentageOfNetRental.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditPercentageOf.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditApplyAgencyComm.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabPageBursts.ResumeLayout(False)
        Me.PanelBursts.ResumeLayout(False)
        Me.PanelBurstInfo1.ResumeLayout(False)
        Me.PanelBurstInfo1.PerformLayout()
        Me.PanelBurstInfo2.ResumeLayout(False)
        Me.PanelBurstInfo2.PerformLayout()
        CType(Me.GroupControlBursts, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlBursts.ResumeLayout(False)
        CType(Me.TextEditSearchBurst.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearchBursts.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearchBurst.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridBursts, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabPageProduction.ResumeLayout(False)
        Me.PanelProduction.ResumeLayout(False)
        CType(Me.GroupControlProduction, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlProduction.ResumeLayout(False)
        CType(Me.TextEditSearchProduction.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearchProduction.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearchProduction.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridProduction, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabPageMiscellaneousCharges.ResumeLayout(False)
        Me.Panel1.ResumeLayout(False)
        CType(Me.GroupControlMiscellaneousCharges, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlMiscellaneousCharges.ResumeLayout(False)
        CType(Me.TextEditSearchMiscellaneousCharge.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearchMiscellaneousCharges.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearchMiscellaneousCharge.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridMiscellaneousCharges, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabPageMediaCosts.ResumeLayout(False)
        Me.PanelMediaCosts.ResumeLayout(False)
        CType(Me.GroupControlCostEstimates, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlCostEstimates.ResumeLayout(False)
        CType(Me.GridCostEstimates, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControlInvoices, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlInvoices.ResumeLayout(False)
        CType(Me.GridInvoiceNumbers, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControlMediaCosts, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlMediaCosts.ResumeLayout(False)
        CType(Me.GridMediaCosts, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabPageErrors.ResumeLayout(False)
        Me.PanelErrors.ResumeLayout(False)
        CType(Me.MemoEditErrors.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents ButtonSave As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents TabControl As DevExpress.XtraTab.XtraTabControl
    Friend WithEvents TabPageSummary As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents PanelSummary As System.Windows.Forms.Panel
    Friend WithEvents TableLayoutPanelSummary As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents PanelContractProperties As System.Windows.Forms.Panel
    Friend WithEvents HyperlinkAccountManager As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkClient As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkContractProposalHeat As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelClient As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelContractProposalHeat As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelContractProperties As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelAccountManager As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TabPageBursts As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents TabPageProduction As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents PanelBursts As System.Windows.Forms.Panel
    Friend WithEvents PanelProduction As System.Windows.Forms.Panel
    Friend WithEvents TabPageAgency As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents PanelAgencySummary As System.Windows.Forms.Panel
    Friend WithEvents LabelAgencyDetailsSummary As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelAgencySummary As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelAgencySummaryValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelCommissionSummary As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelCommissionSummaryValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelAgencyCommission As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelAgencyCommissionValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ImageList16x16 As System.Windows.Forms.ImageList
    Friend WithEvents GroupControlBursts As DevExpress.XtraEditors.GroupControl
    Friend WithEvents ButtonEditBurst As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonDeleteBurst As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonAddBurst As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelSearchBurst As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditSearchBurst As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearchBursts As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearchBurst As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents GridBursts As System.Windows.Forms.DataGridView
    Friend WithEvents GroupControlProduction As DevExpress.XtraEditors.GroupControl
    Friend WithEvents ButtonEditProduction As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonRemoveProduction As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonAddProduction As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelSearchProduction As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditSearchProduction As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearchProduction As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearchProduction As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents GridProduction As System.Windows.Forms.DataGridView
    Friend WithEvents TabPageErrors As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents PanelErrors As System.Windows.Forms.Panel
    Friend WithEvents MemoEditErrors As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents PanelAgency As System.Windows.Forms.Panel
    Friend WithEvents CheckEditApplyAgencyComm As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents TextEditCommission As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelAgencyDetailsTitle As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkAgency As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelCommission As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelAgency As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelErrors As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ProductionDescriptionColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents ProductionQuantityColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents ProductionBrandNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents ProductionAmountColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents ProductionNotesColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents TabPageMiscellaneousCharges As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents GroupControlMiscellaneousCharges As DevExpress.XtraEditors.GroupControl
    Friend WithEvents ButtonEditMiscellaneousCharge As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonRemoveMiscellaneousCharge As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonAddMiscellaneousCharge As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelSearchMiscellaneousCharge As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditSearchMiscellaneousCharge As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearchMiscellaneousCharges As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearchMiscellaneousCharge As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents GridMiscellaneousCharges As System.Windows.Forms.DataGridView
    Friend WithEvents MiscellaneousChargeNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents MiscellaneousChargeAmountColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents PanelBurstInfo2 As System.Windows.Forms.Panel
    Friend WithEvents LabelMediaServices As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelMediaServicesValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelRental As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelRentalValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelTotalWeeks As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelTotalWeeksValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelLastWeek As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelLastWeekValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelFirstWeek As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelFirstWeekValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelBrands As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelBrandsValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelCategories As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelCategoriesValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelChains As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelChainsValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents PanelBurstInfo1 As System.Windows.Forms.Panel
    Friend WithEvents LabelProductionChargesValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelMiscellaneousChargesValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents PanelSignature As System.Windows.Forms.Panel
    Friend WithEvents LabelSignedBy As DevExpress.XtraEditors.LabelControl
    Friend WithEvents PictureSignatureStatus As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents LabelSignature As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelCancelledBy As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelCancelledByValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelSignedByValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelCancellationDate As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelCancellationDateValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelSignatureDate As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelSignatureDateValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents PanelCommOptions As System.Windows.Forms.Panel
    Friend WithEvents CheckEditPercentageOf As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents CheckEditPercentageOfNetRental As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents CheckEditPrintAgencyComm As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents LabelControl9 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelRevenue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl5 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelCommLabel As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelNetRental As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelComm As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl4 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl3 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelTotalRental As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelDiscount As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelCommExample As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TabPageMediaCosts As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents PanelMediaCosts As Panel
    Friend WithEvents LabelTotalMediaCostUserInputted As LabelControl
    Friend WithEvents GroupControlMediaCosts As GroupControl
    Friend WithEvents ButtonEditMediaCost As SimpleButton
    Friend WithEvents GridMediaCosts As DataGridView
    Friend WithEvents GroupControlInvoices As GroupControl
    Friend WithEvents GridInvoiceNumbers As DataGridView
    Friend WithEvents GroupControlCostEstimates As GroupControl
    Friend WithEvents GridCostEstimates As DataGridView
    Friend WithEvents LabelTotalMediaCostSystemCalculated As LabelControl
    Friend WithEvents MeidaCostMediaNameColumn As DataGridViewTextBoxColumn
    Friend WithEvents MediaCostSystemCalculatedColumn As DataGridViewTextBoxColumn
    Friend WithEvents MediaCostUserInputedCostColumn As DataGridViewTextBoxColumn
    Friend WithEvents ButtonEditInvoiceNumber As SimpleButton
    Friend WithEvents ButtonDeleteInvoiceNumber As SimpleButton
    Friend WithEvents ButtonAddInvoiceNumber As SimpleButton
    Friend WithEvents ButtonEditCostEstimate As SimpleButton
    Friend WithEvents ButtonDeleteCostEstimate As SimpleButton
    Friend WithEvents ButtonAddCostEstimate As SimpleButton
    Friend WithEvents LabelTotalCEAmount As LabelControl
    Friend WithEvents LabelTotalInvoiceAmount As LabelControl
    Friend WithEvents DataGridViewTextBoxColumn4 As DataGridViewTextBoxColumn
    Friend WithEvents ColumnMediaName As DataGridViewTextBoxColumn
    Friend WithEvents MediaId As DataGridViewTextBoxColumn
    Friend WithEvents ColumnCostEstimateAmount As DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn1 As DataGridViewTextBoxColumn
    Friend WithEvents MediaIdInvoices As DataGridViewTextBoxColumn
    Friend WithEvents ColumnMediaNameInvoices As DataGridViewTextBoxColumn
    Friend WithEvents ColumnInvoiceAmount As DataGridViewTextBoxColumn
    Friend WithEvents MediaNameColumn As DataGridViewTextBoxColumn
    Friend WithEvents ChainNameColumn As DataGridViewTextBoxColumn
    Friend WithEvents CategoryListColumn As DataGridViewTextBoxColumn
    Friend WithEvents InstallCategories As DataGridViewTextBoxColumn
    Friend WithEvents InstallStoreQtyColumn As DataGridViewTextBoxColumn
    Friend WithEvents BurstWeeksColumn As DataGridViewTextBoxColumn
    Friend WithEvents FirstWeekColumn As DataGridViewTextBoxColumn
    Friend WithEvents LastWeekColumn As DataGridViewTextBoxColumn
    Friend WithEvents BrandNameColumn As DataGridViewTextBoxColumn
    Friend WithEvents ProductNameColumn As DataGridViewTextBoxColumn
End Class
