﻿using DataAccess;

namespace DataService.Security
{
    class DisableLegacyLoginsCommandExecutor : CommandExecutor<DisableLegacyLoginsCommand>
    {

        public override void Execute(DisableLegacyLoginsCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.DisableLegacyLogins))
            {
                storedprocedure.AddInputParameter("logins", command.Logins);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
            }
        }

    }
}
