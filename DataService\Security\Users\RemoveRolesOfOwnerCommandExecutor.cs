﻿using DataAccess;

namespace DataService.Security
{
    class RemoveRolesOfOwnerCommandExecutor : CommandExecutor<RemoveRolesOfOwnerCommand>
    {

        public override void Execute(RemoveRolesOfOwnerCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.RemoveRolesOfOwner))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.AddInputParameter("userid", command.UserId);
                storedprocedure.AddInputParameter("roles", command.Roles);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
            }
        }

    }
}
