﻿using DataAccess;

namespace DataService.Security
{
    class RemoveRoleOwnersCommandExecutor : CommandExecutor<RemoveRoleOwnersCommand>
    {

        public override void Execute(RemoveRoleOwnersCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.RemoveRoleOwners))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.AddInputParameter("roleid", command.RoleId);
                storedprocedure.AddInputParameter("owners", command.Owners);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
            }
        }

    }
}
