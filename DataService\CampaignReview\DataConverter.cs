﻿using Microsoft.VisualBasic.FileIO;
using System;
using System.Data;
using System.IO;

namespace DataService.CampaignReview
{
    static class DataConverter
    {

        internal static DataTable ConvertClicksCsvToDatatable(string filenameandpath)
        {
            DataTable clickstable = NewClicksDataTable();

            // Grabbed from:
            // http://www.c-sharpcorner.com/blogs/read-csv-file-and-get-record-in-datatable-using-textfieldparser-in-c-sharp1


            using (TextFieldParser csvreader = new TextFieldParser(filenameandpath))
            {
                csvreader.SetDelimiters(new string[] { "," });
                csvreader.HasFieldsEnclosedInQuotes = false;
                string[] columnnames = csvreader.ReadFields();

                while (!csvreader.EndOfData)
                {
                    DataRow newrow = clickstable.NewRow();
                    string[] rowdata = csvreader.ReadFields();
                    for (int i = 0; i < rowdata.Length; i++)
                    {
                        DataColumn column = clickstable.Columns[columnnames[i]];
                        if (column != null)
                        {
                            newrow[column] = rowdata[i];
                        }
                    }
                    clickstable.Rows.Add(newrow);
                }
            }

            return clickstable;
        }

        private static DataTable NewClicksDataTable()
        {
            DataTable newtable = new DataTable();

            newtable.Columns.Add(new DataColumn("Barcode", typeof(string)));
            newtable.Columns.Add(new DataColumn("Description", typeof(string)));
            newtable.Columns.Add(new DataColumn("Brand", typeof(string)));
            newtable.Columns.Add(new DataColumn("StoreCode", typeof(string)));
            newtable.Columns.Add(new DataColumn("StoreName", typeof(string)));
            newtable.Columns.Add(new DataColumn("WeekEndDate", typeof(DateTime)));
            newtable.Columns.Add(new DataColumn("Sales", typeof(decimal)));
            newtable.Columns.Add(new DataColumn("Units", typeof(decimal)));

            return newtable;
        }

    }
}
