﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;
using System.Windows.Input;

namespace Framework.Controls.GridSystem
{
    public partial class Grid : UserControl
    {

        #region Constants

        protected Color BACKCOLOR = FrameworkSettings.Colors.GRIDBACKCOLOR;
        protected Color LINECOLOR = FrameworkSettings.Colors.GRIDLINECOLOR;
        protected Color CELLFORECOLOR = FrameworkSettings.Colors.GRIDCELLFORECOLOR;
        protected Color CELLBACKCOLOR = FrameworkSettings.Colors.GRIDCELLBACKCOLOR;
        protected Color HEADERFORECOLOR = FrameworkSettings.Colors.GRIDHEADERFORECOLOR;
        protected Color HEADERBACKCOLOR = FrameworkSettings.Colors.GRIDHEADERBACKCOLOR;
        protected Color CELLSELECTIONFORECOLOR = FrameworkSettings.Colors.GRIDCELLSELECTIONFORECOLOR;
        protected Color CELLSELECTIONBACKCOLOR = FrameworkSettings.Colors.GRIDCELLSELECTIONBACKCOLOR;

        #endregion

        public string Title { get; set; }


        #region Startup

        public Grid()
        {
            InitializeComponent();
            UpdateGridFilterHeight();
            Font = FrameworkSettings.Fonts.FORMFONT;
            ForeColor = FrameworkSettings.Colors.FORMFORECOLOR;
            InitializeGrid();
            SubscribeToEvents();
            AddDataBindings();
        }

        private void AddDataBindings()
        {
            gridFilter1.DataBindings.Add("Text", this, "FilterText", false, DataSourceUpdateMode.OnPropertyChanged);
        }

        private void InitializeGrid()
        {
            thedatagridview.AutoGenerateColumns = false;
            thedatagridview.BackgroundColor = BACKCOLOR;
            thedatagridview.ColumnHeadersDefaultCellStyle.BackColor = HEADERBACKCOLOR;
            thedatagridview.ColumnHeadersDefaultCellStyle.ForeColor = HEADERFORECOLOR;
            thedatagridview.DefaultCellStyle.BackColor = CELLBACKCOLOR;
            thedatagridview.DefaultCellStyle.ForeColor = CELLFORECOLOR;
            thedatagridview.DefaultCellStyle.SelectionBackColor = CELLSELECTIONBACKCOLOR;
            thedatagridview.DefaultCellStyle.SelectionForeColor = CELLSELECTIONFORECOLOR;
            thedatagridview.GridColor = LINECOLOR;
        }

        private void SubscribeToEvents()
        {
            gridFilter1.TextChanged += GridFilter1_TextChanged;
            thedatagridview.KeyDown += Grid1_KeyDown;
            thedatagridview.KeyPress += Grid1_KeyPress;
            thedatagridview.DoubleClick += Grid1_DoubleClick;
            thedatagridview.DataSourceChanged += Grid1_DataSourceChanged;
            thedatagridview.CellDoubleClick += Thedatagridview_CellDoubleClick;
            thedatagridview.CellFormatting += Thedatagridview_CellFormatting;
            thedatagridview.SelectionChanged += Thedatagridview_SelectionChanged;
        }

        #endregion


        #region Grid events

        private void Thedatagridview_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            // Don't raise the RowDoubleClick event if it was the column headers row which was double-clicked.
            if (e.RowIndex > -1)
            {
                OnRowDoubleClick(e);
            }
        }

        /// <summary>
        /// Occurs when the user double-clicks anywhere in a row.
        /// </summary>
        public event DataGridViewCellEventHandler RowDoubleClick;
        protected virtual void OnRowDoubleClick(DataGridViewCellEventArgs e)
        {
            RowDoubleClick?.Invoke(this, e);
        }

        private void Thedatagridview_SelectionChanged(object sender, EventArgs e)
        {
            OnSelectionChanged(e);
        }

        public event EventHandler SelectionChanged;
        private void OnSelectionChanged(EventArgs e)
        {
            SelectionChanged?.Invoke(this, e);
        }


        /// <summary>
        /// Occures when the filter text of the grid has changed.
        /// </summary>
        public event EventHandler GridFilterChanged;
        protected virtual void OnGridFilterChanged(EventArgs e)
        {
            GridFilterChanged?.Invoke(this, e);
        }

        private void Grid1_DataSourceChanged(object sender, EventArgs e)
        {
            OnDataSourceChanged(e);
        }

        /// <summary>
        /// Occurs when the data source of the grid has changed.
        /// </summary>
        public event EventHandler DataSourceChanged;
        protected virtual void OnDataSourceChanged(EventArgs e)
        {
            DataSourceChanged?.Invoke(this, e);
        }

        private void Grid1_DoubleClick(object sender, EventArgs e)
        {
            OnDoubleClick(e);
        }

        #endregion


        #region Grid properties

        /// <summary>
        /// Gets a collection that contains all the columns in the Grid control.
        /// </summary>
        public DataGridViewColumnCollection Columns
        {
            get
            {
                return thedatagridview.Columns;
            }
        }

        /// <summary>
        /// Gets a collection that contains all the rows in the Grid control.
        /// </summary>
        public DataGridViewRowCollection Rows
        {
            get
            {
                return thedatagridview.Rows;
            }
        }

        /// <summary>
        /// Provides an indexer to get or set the cell located at the intersection of the column and row with the specified indexes.
        /// </summary>
        /// <param name="columnIndex">The index of the column containing the cell.</param>
        /// <param name="rowIndex">The index of the row containing the cell.</param>
        /// <returns>The DataGridViewCell at the specified location.</returns>
        [Browsable(false)]
        public DataGridViewCell this[int columnIndex, int rowIndex]
        {
            get
            {
                return thedatagridview[columnIndex, rowIndex];
            }
            set
            {
                thedatagridview[columnIndex, rowIndex] = value;
            }
        }

        /// <summary>
        /// Gets or sets the DataTable that the Grid is displaying data for.
        /// </summary>
        public DataTable DataSource
        {
            get
            {
                if (BindingSource != null)
                {
                    return (DataTable)BindingSource.DataSource;
                }
                else
                {
                    return null;
                }
            }
            set
            {
                FilterText = string.Empty;
                var bindingsource = new BindingSource();
                bindingsource.DataSource = value;
                thedatagridview.DataSource = bindingsource;
            }
        }

        /// <summary>
        /// Gets the BindingSource used for the data binding of row detail controls.
        /// </summary>
        public BindingSource BindingSource
        {
            get { return (BindingSource)thedatagridview.DataSource; }
        }

        public override Color BackColor
        {
            get { return thedatagridview.BackgroundColor; }
            set
            {
                if (thedatagridview.BackgroundColor != value)
                {
                    base.BackColor = value;
                    thedatagridview.BackgroundColor = value;
                }
            }
        }

        public bool ColumnHeadersVisible
        {
            get { return thedatagridview.ColumnHeadersVisible; }
            set { thedatagridview.ColumnHeadersVisible = value; }
        }

        public Color ColumnHeadersBackColor
        {
            get { return thedatagridview.ColumnHeadersDefaultCellStyle.BackColor; }
            set { thedatagridview.ColumnHeadersDefaultCellStyle.BackColor = value; }
        }

        /// <summary>
        /// Indicates whether the user is allowed to select more than one row at a time.
        /// </summary>
        public bool MultiSelect
        {
            get { return thedatagridview.MultiSelect; }
            set { thedatagridview.MultiSelect = value; }
        }

        /// <summary>
        /// Gets or sets the currently selected row.
        /// </summary>
        public DataGridViewRow CurrentRow
        {
            get
            {
                return thedatagridview.CurrentRow;
            }
            set
            {
                if (thedatagridview.Rows.Count > 0)
                {
                    if (thedatagridview.Columns.Count > 0)
                    {
                        int rowindex = thedatagridview.Rows.IndexOf(value);
                        thedatagridview.CurrentCell = thedatagridview[0, rowindex];
                    }
                }
            }
        }

        /// <summary>
        /// Gets or sets the index of the row that is the first row displayed on the grid.
        /// </summary>
        public int FirstDisplayedScrollingRowIndex
        {
            get { return thedatagridview.FirstDisplayedScrollingRowIndex; }
            set
            {
                if (thedatagridview.Rows.Count > 0)
                {
                    thedatagridview.FirstDisplayedScrollingRowIndex = value;
                }
            }
        }

        #endregion


        #region Cell formatting

        // This is the event handler for the CELLFORMATTING event of the embedded DataGridView component on this surface.
        // All it does is raises the identically named event of this control.
        private void Thedatagridview_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            OnCellFormatting(e);
        }

        // This is the CELLFORMATTING event of this control.
        public event DataGridViewCellFormattingEventHandler CellFormatting;
        private void OnCellFormatting(DataGridViewCellFormattingEventArgs e)
        {
            // Do some standard formatting before passing execution to any event subscriptions.
            DataGridViewColumn column = thedatagridview.Columns[e.ColumnIndex];
            if (column != null)
            {
                string datapropertyname = column.DataPropertyName;
                if (column is DataGridViewTextBoxColumn)
                {
                    DataGridViewTextBoxColumn textboxcolumn = (DataGridViewTextBoxColumn)column;
                    if (datapropertyname == "telephone" || datapropertyname == "mobilephone" || datapropertyname == "phone")
                    {
                        e.Value = ToolBox.FormatStringAsPhoneNumber(e.Value.ToString());
                    }
                }
            }

            // Execute any methods that have subscribed to this event.
            CellFormatting?.Invoke(this, e);
        }

        #endregion


        #region Grid filter

        private string _FilterText = string.Empty;
        public string FilterText
        {
            get
            {
                return _FilterText;
            }
            set
            {
                if (_FilterText != value)
                {
                    _FilterText = value;
                    gridFilter1.Text = value;
                    FilterRows();
                    OnGridFilterChanged(EventArgs.Empty);
                }
            }
        }

        private string RowFilter
        {
            get
            {
                if (string.IsNullOrEmpty(FilterText))
                {
                    return string.Empty;
                }
                else
                {
                    StringBuilder rowdatabuilder = new StringBuilder();
                    for (int i = 0; i < Columns.Count; i++)
                    {
                        DataGridViewColumn gridcolumn = Columns[i];
                        if (gridcolumn.Visible)
                        {
                            if (rowdatabuilder.Length > 0)
                                rowdatabuilder.Append(" + ' ' + ");

                            string columnname = string.Empty;
                            if (gridcolumn.ValueType != typeof(string))
                                columnname = "ISNULL(CONVERT(" + gridcolumn.DataPropertyName + ", 'System.String'),'')";
                            else
                                columnname = gridcolumn.DataPropertyName;

                            rowdatabuilder.Append(columnname);
                        }
                    }

                    string rowdata = rowdatabuilder.ToString();
                    string cleanfiltertext = FilterText.Replace("'", "''");
                    string filter = rowdata + " LIKE '%" + cleanfiltertext + "%'";
                    return filter;
                }
            }
        }

        private void FilterRows()
        {
            DataSource.DefaultView.RowFilter = RowFilter;
            gridFilter1.FilterResultsText = DataSource.DefaultView.Count.ToString() + " of " + DataSource.Rows.Count.ToString() + " rows";
        }

        private void Grid1_KeyDown(object sender, System.Windows.Forms.KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Back)
            {
                e.SuppressKeyPress = true;
            }
        }

        private void Grid1_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (Keyboard.IsKeyDown(Key.LeftCtrl) == false && Keyboard.IsKeyDown(Key.RightCtrl) == false)
            {
                if (gridFilter1.InvalidCharacterList.Contains(e.KeyChar))
                {
                    e.Handled = true;
                }
                else
                {
                    gridFilter1.Text = e.KeyChar.ToString();
                    gridFilter1.Focus();
                    gridFilter1.SelectionStart = gridFilter1.Text.Length;
                }
            }
        }

        private void GridFilter1_TextChanged(object sender, EventArgs e)
        {
            FilterText = gridFilter1.Text;
            UpdateGridFilterHeight();
        }

        private void UpdateGridFilterHeight()
        {
            RowStyle gridfilterstyle = tableLayoutPanel1.RowStyles[0];
            if (string.IsNullOrEmpty(FilterText))
            {
                gridfilterstyle.Height = 0;
                thedatagridview.ColumnHeadersHeight = 38;
                thedatagridview.Focus();
            }
            else
            {
                gridfilterstyle.Height = 33;
                thedatagridview.ColumnHeadersHeight = 38;
            }
        }

        #endregion


        #region SelectedRows

        /// <summary>
        /// Gets the collection of rows selected by the user.
        /// </summary>
        public DataGridViewSelectedRowCollection SelectedRows
        {
            get
            {
                return thedatagridview.SelectedRows;
            }
        }

        private List<DataRow> _SelectedDataRows = new List<DataRow>();
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public List<DataRow> SelectedDataRows
        {
            get
            {
                _SelectedDataRows.Clear();
                for (int i = 0; i < SelectedRows.Count; i++)
                {
                    var selecteddatarow = ((DataRowView)SelectedRows[i].DataBoundItem).Row;
                    _SelectedDataRows.Add(selecteddatarow);
                }
                return _SelectedDataRows;
            }
            set
            {
                SelectDataRows(value);
            }
        }

        private List<DataRow> _AllDataRows = new List<DataRow>();
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public List<DataRow> AllDataRows
        {
            get
            {
                _AllDataRows.Clear();
                for (int i = 0; i < thedatagridview.Rows.Count; i++)
                {
                    var row = ((DataRowView)thedatagridview.Rows[i].DataBoundItem).Row;
                    _AllDataRows.Add(row);
                }
                return _AllDataRows;
            }
        }

        private void SelectDataRows(List<DataRow> rowstoselect)
        {
            if (rowstoselect == null)
            {
                return;
            }
            if (rowstoselect.Count == 0)
            {
                return;
            }

            thedatagridview.ClearSelection();
            for (int h = 0; h < rowstoselect.Count; h++)
            {
                DataGridViewRow gridrowtoselect = null;
                DataRow targetrow = rowstoselect[h];
                DataColumn[] primarykey = targetrow.Table.PrimaryKey;

                // Find the grid row to select by comparing each grid row's data bound item to the specified data row.
                for (int i = 0; i < thedatagridview.Rows.Count; i++)
                {
                    DataGridViewRow gridrowtotest = thedatagridview.Rows[i];
                    DataRow rowtotest = ((DataRowView)gridrowtotest.DataBoundItem).Row;

                    bool testfailed = false;
                    for (int j = 0; j < primarykey.Length; j++)
                    {
                        string primarykeycolumnname = primarykey[j].ColumnName;
                        var testkeyvalue = rowtotest[primarykeycolumnname];
                        var targetkeyvalue = targetrow[primarykeycolumnname];

                        if (testkeyvalue.Equals(targetkeyvalue) == false)
                        {
                            testfailed = true;
                        }
                    }

                    if (testfailed == false)
                    {
                        gridrowtoselect = gridrowtotest;
                        break;
                    }
                }

                // If the grid row to select was found, select it.
                if (gridrowtoselect != null)
                {
                    gridrowtoselect.Selected = true;
                }
            }
        }

        #endregion

    }
}
