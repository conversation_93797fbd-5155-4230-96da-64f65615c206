﻿Public Class SubformBillingInstructions

    Private DataObject As BillingInstructionSet
    Private ParentContract As Contract
    Private GridTable As DataTable
    Private PeriodTable As DataTable

#Region "Properties"

    Public ReadOnly Property TitleText As String
        Get
            Return "Contract " & ParentContract.ContractNumber & " - Billing Instructions"
        End Get
    End Property

    Public ReadOnly Property BillingInstructionsTotal As Decimal
        Get
            ' Return the sum of all the amounts in the billing instructions grid.
            Dim Total As Decimal = 0
            For i As Integer = 0 To Grid.Rows.Count - 1
                Total += Math.Round(CDec(Grid(1, i).Value), 2)
            Next
            Return Total
        End Get
    End Property

    ReadOnly Property ContractTotal As Decimal
        Get
            If ParentContract.PrintAgencyComm Then
                Return ParentContract.ValueExcludingAgencyComm
            Else
                Return ParentContract.Value
            End If
        End Get
    End Property

    Public ReadOnly Property Difference As Decimal
        Get
            ' Return the difference between the sum of all the amounts in the billing instructions grid and the contract total.
            Return ContractTotal - BillingInstructionsTotal
        End Get
    End Property

    Public ReadOnly Property DifferenceColor As Color
        Get
            If Difference = 0 Then
                Return Color.Green
            Else
                Return Color.Red
            End If
        End Get
    End Property

    Public ReadOnly Property ContractNotSigned As Boolean
        Get
            Return Not ParentContract.Signed
        End Get
    End Property

    Public ReadOnly Property ChangesPermitted As Boolean
        Get
            Return Not ParentContract.Approved
        End Get
    End Property

#End Region

#Region "Public Methods"

    Public Sub New(ContractObject As Contract)

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        ParentContract = ContractObject
        If ParentContract.Type = Contract.ContractType.InstallationOnly Then
            'Installation-Only Contract Will only be on Upfront-Billing
            CheckEditSplit.Visible = False
            CheckEditCustom.Visible = False
        End If

        ' Create the billing instruction set data object.
        DataObject = New BillingInstructionSet(ParentContract, CType(TopLevelControl, BaseForm))

    End Sub

#End Region

#Region "Event Handlers"

    Private Sub SubformBillingInstructions_Load(sender As System.Object, e As System.EventArgs) Handles MyBase.Load

        AddDataBindings()
        BuildGridDataTable()
        InitialiseBillingMethodControls()
        InitialisePONumberControls()

    End Sub

    Private Sub ButtonBack_Click(sender As System.Object, e As System.EventArgs) Handles ButtonBack.Click

        Save(True)
        CType(ParentSubform, SubformContractTasks).RefreshWarnings()

    End Sub

    Private Sub CheckEditOnePONumber_CheckedChanged(sender As System.Object, e As System.EventArgs) Handles CheckEditOnePONumber.CheckedChanged

        ' Enable or disable the text edit box.
        TextEditPONumber.Enabled = CheckEditOnePONumber.Checked

        ' Enable or disable the PO number grid column.
        PONumberColumn.ReadOnly = CheckEditOnePONumber.Checked

        ' Set all PO numbers in the grid to the one entered in the text box.
        If CheckEditOnePONumber.Checked Then
            For i As Integer = 0 To Grid.Rows.Count - 1
                If Grid(1, i).Value > 0 Then
                    ' This period has a value. Add a PO number.
                    Grid(2, i).Value = TextEditPONumber.EditValue.ToString
                Else
                    ' This period has no value. Remove the PO number.
                    Grid(2, i).Value = String.Empty
                End If
            Next
        End If

    End Sub

    Private Sub TextEditPONumber_EditValueChanged(sender As System.Object, e As System.EventArgs) Handles TextEditPONumber.EditValueChanged

        If Not IsNothing(GridTable) Then
            If CheckEditOnePONumber.Checked Then
                ' One PO number must be used for all periods. Fill the PO Number column of the grid with the entered value.
                For Each GridRow As DataRow In GridTable.Rows
                    ' Only enter the PO number for periods which have an amount greater than zero.
                    If GridRow("Amount") > 0 Then
                        GridRow("PONumber") = TextEditPONumber.EditValue
                    End If
                Next
            End If
        End If

    End Sub

    Private Sub HyperlinkClearPONumbers_Click(sender As System.Object, e As System.EventArgs) Handles HyperlinkClearPONumbers.Click

        If Not IsNothing(GridTable) Then
            If CheckEditOnePONumber.Checked Then
                TextEditPONumber.Text = String.Empty
            Else
                For Each BillingInstructionRow As DataRow In GridTable.Rows
                    BillingInstructionRow("PONumber") = String.Empty
                Next
            End If
        End If

    End Sub

    Private Sub CheckEditCustom_CheckedChanged(sender As System.Object, e As System.EventArgs) Handles CheckEditCustom.CheckedChanged

        ' Change grid column readonly properties.
        AmountColumn.ReadOnly = Not CheckEditCustom.Checked

        ' Refresh the info label display.
        LabelBillingInstructionsTotalValue.DataBindings("Text").ReadValue()
        LabelDifferenceValue.DataBindings("Text").ReadValue()
        LabelDifferenceValue.DataBindings("ForeColor").ReadValue()

    End Sub

    Private Sub Grid_CellFormatting(sender As Object, e As System.Windows.Forms.DataGridViewCellFormattingEventArgs) Handles Grid.CellFormatting

        ' Flag amount cells of closed periods.
        If e.ColumnIndex = 1 Then
            If Grid(3, e.RowIndex).Value Then
                Grid(1, e.RowIndex).Style.BackColor = Color.LightCoral
                Grid(1, e.RowIndex).Style.SelectionBackColor = Color.LightCoral
                Grid(1, e.RowIndex).Style.ForeColor = Color.Black
            End If
        End If

    End Sub

    Private Sub Grid_CellValueChanged(sender As Object, e As System.Windows.Forms.DataGridViewCellEventArgs) Handles Grid.CellValueChanged

        ' Refresh the info label display if the amount column was changed and bindings exist for the billing instructions
        ' total label (this means that the form has loaded already).
        If e.ColumnIndex = 1 AndAlso LabelBillingInstructionsTotalValue.DataBindings.Count > 0 Then
            ' Check if the amount was changed to zero.
            If Grid(e.ColumnIndex, e.RowIndex).Value = 0 Then
                ' The amount is now zero. Split billing does not apply and having a PO number wouldn't make sense.
                Grid(2, e.RowIndex).Value = String.Empty
            Else
                ' The amount is not zero. If the same PO number is used for all periods then update the PO number column.
                If CheckEditOnePONumber.Checked Then
                    Grid(2, e.RowIndex).Value = TextEditPONumber.EditValue
                End If
            End If
            ' Refresh amount controls.
            LabelBillingInstructionsTotalValue.DataBindings("Text").ReadValue()
            LabelDifferenceValue.DataBindings("Text").ReadValue()
            LabelDifferenceValue.DataBindings("ForeColor").ReadValue()
        End If

    End Sub

    Private Sub Grid_DataError(sender As Object, e As System.Windows.Forms.DataGridViewDataErrorEventArgs) Handles Grid.DataError
        CType(TopLevelControl, BaseForm).ShowMessage("Only money values can be entered in the 'Amount' column.", "Invalid Data")
    End Sub

    Private Sub CheckEditUpfront_CheckedChanged(sender As System.Object, e As System.EventArgs) Handles CheckEditUpfront.CheckedChanged
        ' Bill everything in the first month if this check edit is true.

        If CheckEditUpfront.Checked = False Then
            ' Upfront billing is not selected.
            Exit Sub
        End If

        ' Remove all amounts from all rows, but remember any PO number that may have been specified.
        Dim SavedPONumber As String = String.Empty
        For Each BillingRow As DataRow In GridTable.Rows
            If BillingRow("PeriodClosed") = False Then
                If String.IsNullOrEmpty(SavedPONumber) Then
                    SavedPONumber = BillingRow("PONumber")
                End If
                BillingRow("Amount") = 0
                BillingRow("PONumber") = String.Empty
            End If
        Next

        ' Add up what's already been billed in closed months.
        Dim AlreadyBilled As Decimal = 0
        For Each BillingRow As DataRow In GridTable.Rows
            If BillingRow("PeriodClosed") Then
                AlreadyBilled += BillingRow("Amount")
            End If
        Next

        ' Get the contract value, taking agency comm into consideration.
        Dim ContractValue As Decimal = ParentContract.Value
        If ParentContract.PrintAgencyComm Then
            ContractValue = ParentContract.ValueExcludingAgencyComm
        End If

        ' Add the remainder of the contract value to the first billing instruction.
        Dim StillToBill As Decimal = ContractValue - AlreadyBilled
        For Each BillingRow As DataRow In GridTable.Rows
            If BillingRow("PeriodClosed") = False Then
                BillingRow("Amount") = StillToBill
                BillingRow("PONumber") = SavedPONumber
                If CheckEditOnePONumber.Checked Then
                    TextEditPONumber.Text = SavedPONumber
                End If
                Exit For
            End If
        Next

        ' Add PO numners to billing instructions if the user opted to use one PO for all instructions.
        If CheckEditOnePONumber.Checked Then
            For Each BillingRow As DataRow In GridTable.Rows
                If BillingRow("Amount") > 0 Then
                    BillingRow("PONumber") = TextEditPONumber.Text
                End If
            Next
        End If

        ' Refresh the info label display if the amount column was changed.
        LabelBillingInstructionsTotalValue.DataBindings("Text").ReadValue()
        LabelDifferenceValue.DataBindings("Text").ReadValue()
        LabelDifferenceValue.DataBindings("ForeColor").ReadValue()

    End Sub

    Private Sub CheckEditSplit_CheckedChanged(sender As System.Object, e As System.EventArgs) Handles CheckEditSplit.CheckedChanged

        ' Exit if this option is not enabled.
        If CheckEditSplit.Checked = False Then
            Exit Sub
        End If

        SplitBilling(True)

        ' Refresh the info label display if the amount column was changed.
        LabelBillingInstructionsTotalValue.DataBindings("Text").ReadValue()
        LabelDifferenceValue.DataBindings("Text").ReadValue()
        LabelDifferenceValue.DataBindings("ForeColor").ReadValue()

    End Sub

    Private Sub LabelDifferenceValue_Validated(sender As Object, e As System.EventArgs) Handles LabelDifferenceValue.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        If Not Difference = 0 Then
            LiquidAgent.ControlValidation _
            (ValidatedControl, "The amounts of all billing instructions in the grid must match the total contract value.")
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkFirstMonth_Click(sender As System.Object, e As System.EventArgs) Handles HyperlinkFirstMonth.Click

        ' Prevent changes to the contract if it has already been approved.
        If ChangesPermitted = False Then
            ' The contract is approved. Editing is not permitted.
            CType(TopLevelControl, BaseForm).ShowMessage _
                ("Changes to a contract which is already approved by Finance are not permitted.", "Contract Is Approved")
            Exit Sub
        End If

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a date selection from the user.
        Dim CurrentDate As Date = DataObject.FirstBillingMonth
        If CurrentDate.Year = 1000 Then
            CurrentDate = Now.Date
        End If
        Dim SelectedDate As Nullable(Of Date) = DateSelector.GetDate(False, CurrentDate)

        ' Update the data object and the label with the selection.
        If SelectedDate.HasValue Then
            DataObject.FirstBillingMonth = SelectedDate.Value
            CurrentControl.DataBindings("Text").ReadValue()
        End If

        ' Reset the error text of the related control to an empty string.
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)

    End Sub

    Private Sub ButtonApply_Click(sender As System.Object, e As System.EventArgs) Handles ButtonApply.Click

        ' Prevent changes to the contract if it has already been signed.
        Dim userhasneededrolemembership = My.User.IsInRole("signedcontractmodifier")
        If ContractNotSigned = False AndAlso userhasneededrolemembership = False Then
            ' The contract is signed. Editing is not permitted.
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If

        ' Check for valid First Billing Month selection.
        If DataObject.FirstBillingMonth.Year = 1000 Then
            ' First billing month has not been selected.
            CType(TopLevelControl, BaseForm).ShowMessage("Please select a valid month when billing should start.", "First Billing Month is Required")
            Exit Sub
        End If

        ' Check for valid number of months.
        Dim SpecifiedMonths As Integer = 0
        If Integer.TryParse(DataObject.BillingMonths, SpecifiedMonths) = False Then
            ' Parsing failed.
            CType(TopLevelControl, BaseForm).ShowMessage("Please enter a valid number of months that need to be billed.", "Number of Months is Required")
            Exit Sub
        Else
            ' Parsing was successful. Check for zero value.
            If SpecifiedMonths = 0 Then
                CType(TopLevelControl, BaseForm).ShowMessage("The number of billing months may not be zero.", "Number of Months is Zero")
                Exit Sub
            End If
            ' Check for 5 year billing.
            If SpecifiedMonths > 60 Then
                CType(TopLevelControl, BaseForm).ShowMessage("The number of billing months may not exceed 60.", "Number of Months is Too Large")
                Exit Sub
            End If
        End If

        ' Confirm whether the user wants to go ahead and reset all months.
        Dim ConfirmMessage As String = "If you continue, all billing months will be deleted and new ones will be created based on your" & vbCrLf _
                                       & "choices above (billing months which have already been closed by finance will not be affected)." & vbCrLf & vbCrLf _
                                       & "Are you sure you want to reset all billing months?"
        Dim UserResponse As DialogResult = CType(TopLevelControl, BaseForm).ShowMessage(ConfirmMessage, "Are You Sure?", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question)
        If Not UserResponse = DialogResult.Yes Then
            Exit Sub
        End If

        ' Delete all billing instructions except ones of closed months (closed months with an amount of zero may safely be deleted).
        Using GridTableCopy As DataTable = GridTable.Copy
            For Each Instruction As DataRow In GridTableCopy.Rows
                Dim GridTableRow As DataRow = GridTable.Rows.Find(Instruction("PeriodID"))
                If Not Instruction("PeriodClosed") Then
                    ' This period is not closed. It's safe to delete it.
                    GridTableRow.Delete()
                ElseIf Instruction("Amount") = 0 Then
                    ' This period is closed but the amount is zero. It's safe to delete.
                    GridTableRow.Delete()
                ElseIf Not DataObject.ParentContract.Signed Then
                    ' This period is closed and the amount is greater than zero but the contract is not signed. It's safe to delete.
                    GridTableRow.Delete()
                ElseIf My.User.IsInRole("sales_manager") Then
                    ' This period is closed and the amount is greater than zero and the contract is signed but the user is a sales
                    ' manager. It's safe to delete.
                    GridTableRow.Delete()
                End If
            Next
        End Using

        ' Add new billing instructions starting at the month selected by the user.
        For i As Integer = 0 To DataObject.BillingMonths - 1

            ' Get the period details for this billing instruction.
            Dim Period As Date = DataObject.FirstBillingMonth.AddMonths(i)
            Dim PeriodID As Integer = Period.Year * 100 + Period.Month
            Dim PeriodName As String = PeriodTable.Rows.Find(PeriodID).Item("PeriodName")

            ''Chech if this period occurs no more 3 months after the contract has been completed.
            If PeriodID - DataObject.ParentContract.LastPeriod > 90 AndAlso My.User.IsInRole("sales_manager") = False Then
                ' This period extends past the end of the contract. This may not happen (unless the user is a sales manager).
                Dim LastMonth As String = DataObject.ParentContract.LastWeek.ToString("MMMM") & " " & DataObject.ParentContract.LastWeek.Year.ToString
                CType(TopLevelControl, BaseForm).ShowMessage("You have selected billing periods which extend beyond the completion " _
                                                            & "date of the contract (" & LastMonth & "). The excess periods have been dropped" _
                                                            & vbCrLf & "to prevent billing after the contract has ended.", "Excessive Billing Periods " _
                                                            & "Dropped")
                Exit For
            End If

            ' Check if the table doesn't already contain an existing row for this period.
            If Not GridTable.Rows.Contains(PeriodID) Then
                ' No row for this period exists yet. Add it.

                ' Create and add the new billing instruction row.
                Dim NewRow As DataRow = GridTable.NewRow
                NewRow("PeriodID") = PeriodID
                NewRow("PeriodName") = PeriodName
                NewRow("Amount") = 0
                NewRow("PONumber") = String.Empty
                NewRow("PeriodClosed") = DataObject.PeriodClosed(PeriodID)
                GridTable.Rows.Add(NewRow)

            End If
        Next

        ' Reconfigure all billing amounts according to the currently selected option.
        CheckEditUpfront_CheckedChanged(sender, e)
        CheckEditSplit_CheckedChanged(sender, e)

        ' Refresh amount controls.
        LabelBillingInstructionsTotalValue.DataBindings("Text").ReadValue()
        LabelDifferenceValue.DataBindings("Text").ReadValue()
        LabelDifferenceValue.DataBindings("ForeColor").ReadValue()

    End Sub

#End Region

#Region "Protected Methods"

    Protected Overrides Function Save() As Boolean
        Return DataObject.Save(My.Settings.DBConnection, CType(TopLevelControl, BaseForm), GridTable)
    End Function

#End Region

#Region "Private Methods"

    Private Sub SplitBilling(ByVal ForceAllMonths As Boolean)
        ' ForceAllMonths is only true if the user clicked the Split Billing radio button option.

        ' Add up the amounts already contained in closed (and thus unmodifiable) periods.
        ' A variable to add up all biling amounts in months that are already closed.
        Dim ClosedMonthsAmount As Decimal = 0
        ' A variable to count how many remaining months must be included to receive a billing instruction.
        Dim BillingMonths As Integer = 0
        For Each BillingRow As DataRow In GridTable.Rows
            If BillingRow("PeriodClosed") Then
                ' Add up all biling amounts in months that are already closed.
                ClosedMonthsAmount += BillingRow("Amount")
            Else
                BillingMonths += 1
            End If
        Next

        ' If the total of all previously billed periods is greater than zero then it means this contract has already been billed
        ' and we'll thus assume that all production charges have already been billed too. So we'll remove those charges now
        ' so that the calculation later on remains accurate.
        If ClosedMonthsAmount > 0 Then
            ClosedMonthsAmount -= ParentContract.InventoryValue
            ClosedMonthsAmount -= ParentContract.MiscellaneousChargesValue
        End If

        ' Get the total monthly amount that must be billed for all research and rental fees.
        Dim MonthlyFee As Decimal = 0
        If BillingMonths > 0 Then
            MonthlyFee = Math.Round((ParentContract.RentalValue + ParentContract.ResearchValue - ClosedMonthsAmount) / BillingMonths, 2)
        End If

        ' A variable to remember if production has been added to the billing amount of any period.
        Dim ProductionCharged As Boolean = False

        ' Split rental and research fees across all billing periods.
        For Each BillingRow As DataRow In GridTable.Rows
            ' Only months which haven't been closed may have their amounts modified.
            If BillingRow("PeriodClosed") = False Then
                ' If this row's amount is zero and ForceAllMonths is false, then we must leave this row unchanged
                ' because it means that the user does not want this month to be billed, and wants the total contract value
                ' spread over the months that MUST be billed.
                If Not (BillingRow("Amount") = 0 AndAlso ForceAllMonths = False) Then

                    ' This month must be included in the billing schedule.
                    BillingRow("Amount") = MonthlyFee

                    ' If this is the first period, we may want to include production charges.
                    If ProductionCharged = False AndAlso ClosedMonthsAmount = 0 Then
                        ' Production has not yet been charged. Let's do it now.
                        BillingRow("Amount") += ParentContract.InventoryValue
                        BillingRow("Amount") += ParentContract.MiscellaneousChargesValue
                        ProductionCharged = True
                    End If

                    ' Add the PO number if one common PO number has been specified.
                    If CheckEditOnePONumber.Checked AndAlso BillingRow("Amount") > 0 Then
                        If String.IsNullOrEmpty(TextEditPONumber.EditValue) Then
                            TextEditPONumber.EditValue = BillingRow("PONumber")
                        Else
                            BillingRow("PONumber") = TextEditPONumber.EditValue
                        End If
                    End If

                End If
            End If
        Next


        ' If, after all the above, there is a difference between the billed amounts and the contract value due to rounding, add
        ' the difference to the first month.
        Dim MissingAmount As Decimal = Difference
        If Not MissingAmount = 0 Then
            For Each BillingRow As DataRow In GridTable.Rows
                If BillingRow("PeriodClosed") = False Then
                    BillingRow("Amount") += MissingAmount
                    Exit For
                End If
            Next
        End If

    End Sub

    Private Sub AddDataBindings()

        ' General data bindings.
        LabelTitle.DataBindings.Add("Text", Me, "TitleText")
        LabelContractTotalValue.DataBindings.Add("Text", Me, "ContractTotal", True, DataSourceUpdateMode.Never, 0, "C2")
        LabelBillingInstructionsTotalValue.DataBindings.Add("Text", Me, "BillingInstructionsTotal", True, DataSourceUpdateMode.Never, 0, "C2")
        LabelDifferenceValue.DataBindings.Add("Text", Me, "Difference", True, DataSourceUpdateMode.Never, 0, "C2")
        LabelDifferenceValue.DataBindings.Add("ForeColor", Me, "DifferenceColor")
        HyperlinkFirstMonth.DataBindings.Add("Text", DataObject, "FirstBillingMonthString")
        TextEditMonths.DataBindings.Add("Text", DataObject, "BillingMonths")

        ' Disable controls if contract is signed.
        CheckEditUpfront.DataBindings.Add("Enabled", Me, "ChangesPermitted")
        CheckEditSplit.DataBindings.Add("Enabled", Me, "ChangesPermitted")
        CheckEditCustom.DataBindings.Add("Enabled", Me, "ChangesPermitted")

    End Sub

    Private Sub BuildGridDataTable()
        ' Create an instance for the GridTable variable and set it as the grid's data source.

        ' Define the select statement that will retrieve the periods.
        Dim SelectStatement As String = "SELECT PeriodID, PeriodName FROM Finance.Period"

        ' Create a string to hold possible errors while running the SQL command to retrieve the periods.
        Dim Errors As String = String.Empty

        ' Get a table of financial period definitions (these normally correspond to months).
        PeriodTable = LiquidAgent.GetSqlDataTable(My.Settings.DBConnection, SelectStatement, Errors)
        Dim PeriodTableKey As DataColumn() = {PeriodTable.Columns(0)}
        PeriodTable.PrimaryKey = PeriodTableKey

        ' Display an error if anything went wrong.
        If Not String.IsNullOrEmpty(Errors) Then
            CType(TopLevelControl, BaseForm).ShowMessage("An error occured while collecting period data. " _
                                                        & "Please report this to somebody important.", "Data Collection Error", _
                                                        MessageBoxIcon.Error)
            Exit Sub
        End If

        ' Define the needed columns for the table that will become the grid's data source.
        Dim Columns(4) As DataColumn
        Columns(0) = New DataColumn("PeriodID", GetType(Integer))
        Columns(1) = New DataColumn("PeriodName", GetType(String))
        Columns(2) = New DataColumn("Amount", GetType(Decimal))
        Columns(3) = New DataColumn("PONumber", GetType(String))
        Columns(4) = New DataColumn("PeriodClosed", GetType(Boolean))
        Dim Key() As DataColumn = {Columns(0)}

        ' Create a data table instance for the grid's data source and add the columns and primary key.
        GridTable = New DataTable
        GridTable.Columns.AddRange(Columns)
        GridTable.PrimaryKey = Key
        GridTable.DefaultView.Sort = "PeriodID"

        ' Populate the grid table with existing billing instructions.
        For Each ExistingInstruction As DataSetContract.BillingInstructionRow In DataObject.BillingInstructionTable.Rows
            Dim NewRow As DataRow = GridTable.NewRow
            NewRow("PeriodID") = ExistingInstruction.PeriodID
            NewRow("PeriodName") = ExistingInstruction.PeriodName
            NewRow("Amount") = ExistingInstruction.Amount
            NewRow("PONumber") = ExistingInstruction.PONumber
            NewRow("PeriodClosed") = DataObject.PeriodClosed(ExistingInstruction.PeriodID)
            ' Add the new row to the grid table.
            GridTable.Rows.Add(NewRow)
        Next

        ' Build and add new rows to the table.
        If ParentContract.Type = Contract.ContractType.Rental Or
            ParentContract.Type = Contract.ContractType.InstallationOnly Then
            CreateRentalBillingInstructions(PeriodTable)
        ElseIf ParentContract.Type = Contract.ContractType.Research Then
            CreateResearchBillingInstructions(PeriodTable)
        End If

        Grid.AutoGenerateColumns = False
        Grid.DataSource = GridTable

    End Sub

    Private Sub CreateRentalBillingInstructions(PeriodTable As DataTable)

        ' Exit if the parent contract is not of the correct type.
        If Not ParentContract.Type = Contract.ContractType.Rental Or
            ParentContract.Type = Contract.ContractType.InstallationOnly Then
            Exit Sub
        End If

        ' Check for the existence of bursts before proceeding.
        If Year(ParentContract.FirstWeek) = 1000 Then
            ' This is a rental contract with no bursts. Billing cannot be configured yet.
            CType(TopLevelControl, BaseForm).ShowMessage("Please add bursts to your contract before configuring the " _
                                                        & "billing instructions.", "Bursts Required")
            RevertToParentSubform()
        Else
            ' Build rows for periods between the First Week and Last Week of the contract.
            For Each Period As DataRow In ParentContract.Periods.Rows
                Dim PeriodID As Integer = Period("PeriodID")
                ' Check if a row with this primary key value already exists in the table.
                If Not GridTable.Rows.Contains(PeriodID) Then

                    ' The table doesn't contain a row for the financial period represented by the current week's year and month values.
                    ' Get the name of this period.
                    Dim PeriodKey() As Object = {PeriodID}
                    Dim PeriodRow As DataRow = PeriodTable.Rows.Find(PeriodKey)
                    Dim PeriodName As String = "(undefined)"
                    If Not IsNothing(PeriodRow) Then
                        PeriodName = PeriodRow("PeriodName")
                    End If

                    ' Build a row for this period.
                    Dim NewRow As DataRow = GridTable.NewRow
                    NewRow("PeriodID") = PeriodID
                    NewRow("PeriodName") = PeriodName
                    NewRow("Amount") = 0
                    NewRow("PONumber") = String.Empty
                    NewRow("PeriodClosed") = DataObject.PeriodClosed(PeriodID)

                    ' Add the new row to the grid table.
                    GridTable.Rows.Add(NewRow)

                End If
            Next
        End If

    End Sub

    Private Sub CreateResearchBillingInstructions(PeriodTable As DataTable)

        ' Exit if the parent contract is not of the correct type.
        If Not ParentContract.Type = Contract.ContractType.Research Then
            Exit Sub
        End If

        ' Check for the existence of research categories before proceeding.
        If String.IsNullOrEmpty(ParentContract.Row.ResearchCategories) Then
            ' This is a research contract with no categories. Billing cannot be configured yet.
            CType(TopLevelControl, BaseForm).ShowMessage("Please add research categories to your contract before configuring the " _
                                                        & "billing instructions.", "Research Categories Required")
            RevertToParentSubform()
        Else
            ' Build rows for periods between the First Week and Last Week of the contract.
            For Each Period As DataRow In ParentContract.Periods.Rows
                Dim PeriodID As Integer = Period("PeriodID")
                ' Check if a row with this primary key value already exists in the table.
                If Not GridTable.Rows.Contains(PeriodID) Then

                    ' The table doesn't contain a row for the financial period represented by the current week's year and month values.
                    ' Get the name of this period.
                    Dim PeriodKey() As Object = {PeriodID}
                    Dim PeriodRow As DataRow = PeriodTable.Rows.Find(PeriodKey)
                    Dim PeriodName As String = "(undefined)"
                    If Not IsNothing(PeriodRow) Then
                        PeriodName = PeriodRow("PeriodName")
                    End If

                    ' Build a row for this period.
                    Dim NewRow As DataRow = GridTable.NewRow
                    NewRow("PeriodID") = PeriodID
                    NewRow("PeriodName") = PeriodName
                    NewRow("Amount") = 0
                    NewRow("PONumber") = String.Empty
                    NewRow("PeriodClosed") = DataObject.PeriodClosed(PeriodID)

                    ' Add the new row to the grid table.
                    GridTable.Rows.Add(NewRow)

                End If
            Next
        End If

    End Sub

    Private Sub InitialiseBillingMethodControls()

        ' Get the table of existing billing instructions.
        Dim ExistingBillingInstructionTable As DataSetContract.BillingInstructionDataTable = ParentContract.Row.Table.DataSet.Tables("BillingInstruction")
        ' Sort the rows of the table by PeriodID.
        ExistingBillingInstructionTable.DefaultView.Sort = ExistingBillingInstructionTable.PeriodIDColumn.ColumnName
        ' Check if billing instructions exist.
        If ExistingBillingInstructionTable.Rows.Count = 0 Then
            ' No billing instructions exist.
            Exit Sub
        End If


        ' Check if billing is setup as upfront billing
        ' --------------------------------------------
        ' First check if there is only one billing instruction for this contract.
        If ExistingBillingInstructionTable.Rows.Count = 1 Then
            ' Okay, this might be upfront billing. Let's check if the single billing instruction found actually matches
            ' the first billing period of the contract.
            Dim FirstBillingPeriodID As Integer = CInt(Year(ParentContract.FirstWeek).ToString & Month(ParentContract.FirstWeek).ToString("0#"))
            Dim BillingInstruction As DataSetContract.BillingInstructionRow = ExistingBillingInstructionTable.Rows(0)
            If FirstBillingPeriodID = BillingInstruction.PeriodID Then
                ' Okay, so the billing periods are the same. This could be upfront billing. Now we need to just check whether or not the
                ' amounts are equal, then we can be sure.
                If ParentContract.Value = Math.Round(BillingInstruction.Amount, 2) Then
                    ' Booyah!!  We have a match. This is definitely upfront billing. Let's set the option and exit.
                    CheckEditUpfront.Checked = True
                    Exit Sub
                End If
            End If
        End If


        ' Check if billing is setup as split billing
        ' ------------------------------------------
        ' We know there is more than one existing billing instruction in the table. We need to determine whether the
        ' number of instructions equals the number of periods covered by this contract, and whether the sum of their
        ' amounts match the total contract value. If they do, we have split billing.
        Dim InstructionCount As Integer = 0
        Dim InstructionTotal As Decimal = 0
        For Each BillingInstruction As DataRow In GridTable.Rows
            If BillingInstruction("Amount") > 0 Then
                ' This is a valid billing instruction with an amount. Count it and add it.
                InstructionCount += 1
                InstructionTotal += BillingInstruction("Amount")
            End If
        Next
        ' Get a copy of the periods table for this contract.
        Using ContractPeriods As DataTable = ParentContract.Periods.Copy
            If ContractPeriods.Rows.Count = InstructionCount Then
                ' Cool. This could be split billing because the number of instructions equals the number of periods
                ' in this contract. Let's check the amounts.
                If ParentContract.Value = InstructionTotal Then
                    ' Bingo!  This is definitely a split billing setup.
                    CheckEditSplit.Checked = True
                    Exit Sub
                End If
            End If
        End Using


        ' Check if billing is setup as custom billing
        ' -------------------------------------------
        ' This is a no brainer. If the billing isn't upfront or split, it is custom.
        CheckEditCustom.Checked = True

    End Sub

    Private Sub InitialisePONumberControls()
        ' A variable to hold the PO number common to all billing periods.
        Dim CommonPONumber As String = String.Empty

        ' Check each row in the grid.
        For Each GridRow As DataRow In GridTable.Rows
            ' Only check rows which have amounts greater than zero.
            If GridRow("Amount") > 0 Then
                If String.IsNullOrEmpty(CommonPONumber) Then
                    ' This is the first row being checked (or all previous rows had no PO number)
                    CommonPONumber = GridRow("PONumber")
                Else
                    ' A previous row already has a PO number. We need to check if it's the same as this row's PO number.
                    If Not String.Compare(CommonPONumber, GridRow("PONumber").ToString) = 0 Then
                        ' This grid row's PO number is different to a previous row's PO number. This means a single PO number
                        ' has not been issued for this contract. Instead, multiple PO numbers have been issued.
                        CheckEditOnePONumber.Checked = True
                        TextEditPONumber.EditValue = String.Empty
                        Exit Sub
                    End If
                End If
            End If
        Next
        ' The same PO number is used for all billing periods.
        CheckEditOnePONumber.Checked = True
        TextEditPONumber.EditValue = CommonPONumber

    End Sub

#End Region

End Class
