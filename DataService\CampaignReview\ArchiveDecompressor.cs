﻿using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;

namespace DataService.CampaignReview
{
    internal static class ArchiveDecompressor
    {

        internal static void ExtractFiles(ref string errormessage, Dictionary<string, int> archivefileids, string destinationrootfolderpath)
        {
            if (string.IsNullOrEmpty(errormessage) == false)
            {
                return;
            }

            try
            {
                foreach (string archivefilename in archivefileids.Keys)
                {
                    string destinationfolderpath = destinationrootfolderpath + "\\" + archivefileids[archivefilename].ToString();
                    Directory.CreateDirectory(destinationfolderpath);

                    using (ZipArchive archive = ZipFile.OpenRead(archivefilename))
                    {
                        foreach (ZipArchiveEntry entry in archive.Entries)
                        {
                            if (entry.FullName.EndsWith(".csv", StringComparison.OrdinalIgnoreCase))
                            {
                                entry.ExtractToFile(Path.Combine(destinationfolderpath, entry.FullName));
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                errormessage = ex.Message;
            }
        }
    }
}
