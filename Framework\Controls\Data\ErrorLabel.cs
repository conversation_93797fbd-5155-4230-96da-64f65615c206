﻿using System.Windows.Forms;

namespace Framework.Controls.Data
{

    internal class ErrorLabel : Label
    {
        public ErrorLabel()
        {
            Font = FrameworkSettings.Fonts.ERRORFONT;
            ForeColor = FrameworkSettings.Colors.ERRORFORECOLOR;
            Margin = new Padding(0);
        }
        
        public override string Text
        {
            get
            {
                return base.Text;
            }
            set
            {
                string trimmedvalue = value.Trim().ToUpper();
                if (base.Text != trimmedvalue)
                {
                    base.Text = trimmedvalue;
                }
            }
        }
    }
    
}
