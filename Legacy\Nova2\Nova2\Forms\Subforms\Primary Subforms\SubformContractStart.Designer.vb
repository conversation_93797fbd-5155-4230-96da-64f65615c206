﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformContractStart
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformContractStart))
        Me.ImageList24x24 = New System.Windows.Forms.ImageList()
        Me.ButtonNew = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonSearch = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelSearchTitle = New DevExpress.XtraEditors.LabelControl()
        Me.CheckEditSearchHistory = New DevExpress.XtraEditors.CheckEdit()
        Me.HyperlinkClient = New DevExpress.XtraEditors.LabelControl()
        Me.LabelProject = New DevExpress.XtraEditors.LabelControl()
        Me.LabelContract = New DevExpress.XtraEditors.LabelControl()
        Me.LabelClient = New DevExpress.XtraEditors.LabelControl()
        Me.CheckEditSearchCancelled = New DevExpress.XtraEditors.CheckEdit()
        Me.TextEditProject = New DevExpress.XtraEditors.TextEdit()
        Me.ToolTipControllerDefault = New DevExpress.Utils.ToolTipController()
        Me.TextEditContract = New DevExpress.XtraEditors.TextEdit()
        Me.ImageList16x16 = New System.Windows.Forms.ImageList()
        Me.LabelMediaService = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkMediaService = New DevExpress.XtraEditors.LabelControl()
        Me.LabelBrand = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkBrand = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditBrand = New DevExpress.XtraEditors.TextEdit()
        Me.TextEditClient = New DevExpress.XtraEditors.TextEdit()
        Me.TextEditMediaService = New DevExpress.XtraEditors.TextEdit()
        CType(Me.CheckEditSearchHistory.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditSearchCancelled.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditProject.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditContract.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditBrand.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditClient.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditMediaService.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Margin = New System.Windows.Forms.Padding(3, 3, 3, 40)
        Me.LabelTitle.Size = New System.Drawing.Size(412, 44)
        Me.LabelTitle.TabIndex = 15
        Me.LabelTitle.Text = "Create a new contract"
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "add.png")
        Me.ImageList24x24.Images.SetKeyName(1, "pencil.png")
        Me.ImageList24x24.Images.SetKeyName(2, "delete.png")
        '
        'ButtonNew
        '
        Me.ButtonNew.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonNew.Appearance.Options.UseFont = True
        Me.ButtonNew.ImageIndex = 0
        Me.ButtonNew.ImageList = Me.ImageList24x24
        Me.ButtonNew.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonNew.Location = New System.Drawing.Point(133, 74)
        Me.ButtonNew.LookAndFeel.SkinName = "Black"
        Me.ButtonNew.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonNew.Margin = New System.Windows.Forms.Padding(3, 3, 3, 40)
        Me.ButtonNew.Name = "ButtonNew"
        Me.ButtonNew.Size = New System.Drawing.Size(130, 32)
        Me.ButtonNew.TabIndex = 16
        Me.ButtonNew.Text = "New Contract"
        '
        'ButtonSearch
        '
        Me.ButtonSearch.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonSearch.Appearance.Options.UseFont = True
        Me.ButtonSearch.ImageIndex = 1
        Me.ButtonSearch.ImageList = Me.ImageList24x24
        Me.ButtonSearch.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonSearch.Location = New System.Drawing.Point(133, 405)
        Me.ButtonSearch.LookAndFeel.SkinName = "Black"
        Me.ButtonSearch.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonSearch.Margin = New System.Windows.Forms.Padding(3, 3, 3, 15)
        Me.ButtonSearch.Name = "ButtonSearch"
        Me.ButtonSearch.Size = New System.Drawing.Size(130, 32)
        Me.ButtonSearch.TabIndex = 14
        Me.ButtonSearch.Text = "Search"
        '
        'LabelSearchTitle
        '
        Me.LabelSearchTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSearchTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelSearchTitle.Location = New System.Drawing.Point(12, 149)
        Me.LabelSearchTitle.Margin = New System.Windows.Forms.Padding(3, 3, 3, 15)
        Me.LabelSearchTitle.Name = "LabelSearchTitle"
        Me.LabelSearchTitle.Size = New System.Drawing.Size(436, 44)
        Me.LabelSearchTitle.TabIndex = 17
        Me.LabelSearchTitle.Text = "Find an existing contract"
        '
        'CheckEditSearchHistory
        '
        Me.CheckEditSearchHistory.Location = New System.Drawing.Point(131, 341)
        Me.CheckEditSearchHistory.Margin = New System.Windows.Forms.Padding(3, 3, 9, 3)
        Me.CheckEditSearchHistory.Name = "CheckEditSearchHistory"
        Me.CheckEditSearchHistory.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditSearchHistory.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditSearchHistory.Properties.Appearance.Options.UseFont = True
        Me.CheckEditSearchHistory.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditSearchHistory.Properties.AutoWidth = True
        Me.CheckEditSearchHistory.Properties.Caption = "Search history contracts"
        Me.CheckEditSearchHistory.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditSearchHistory.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditSearchHistory.Size = New System.Drawing.Size(161, 19)
        Me.CheckEditSearchHistory.TabIndex = 12
        '
        'HyperlinkClient
        '
        Me.HyperlinkClient.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkClient.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkClient.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkClient.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkClient.AutoEllipsis = True
        Me.HyperlinkClient.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkClient.Location = New System.Drawing.Point(325, 240)
        Me.HyperlinkClient.Margin = New System.Windows.Forms.Padding(3, 3, 9, 10)
        Me.HyperlinkClient.Name = "HyperlinkClient"
        Me.HyperlinkClient.Size = New System.Drawing.Size(47, 13)
        Me.HyperlinkClient.TabIndex = 3
        Me.HyperlinkClient.Text = "Select..."
        '
        'LabelProject
        '
        Me.LabelProject.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelProject.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelProject.Location = New System.Drawing.Point(12, 266)
        Me.LabelProject.Margin = New System.Windows.Forms.Padding(3, 3, 9, 3)
        Me.LabelProject.Name = "LabelProject"
        Me.LabelProject.Size = New System.Drawing.Size(45, 13)
        Me.LabelProject.TabIndex = 4
        Me.LabelProject.Text = "Project:"
        '
        'LabelContract
        '
        Me.LabelContract.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelContract.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelContract.Location = New System.Drawing.Point(12, 214)
        Me.LabelContract.Margin = New System.Windows.Forms.Padding(3, 3, 15, 3)
        Me.LabelContract.Name = "LabelContract"
        Me.LabelContract.Size = New System.Drawing.Size(103, 13)
        Me.LabelContract.TabIndex = 18
        Me.LabelContract.Text = "Contract Number:"
        '
        'LabelClient
        '
        Me.LabelClient.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelClient.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelClient.Location = New System.Drawing.Point(12, 240)
        Me.LabelClient.Margin = New System.Windows.Forms.Padding(3, 3, 9, 3)
        Me.LabelClient.Name = "LabelClient"
        Me.LabelClient.Size = New System.Drawing.Size(38, 13)
        Me.LabelClient.TabIndex = 1
        Me.LabelClient.Text = "Client:"
        '
        'CheckEditSearchCancelled
        '
        Me.CheckEditSearchCancelled.Location = New System.Drawing.Point(131, 367)
        Me.CheckEditSearchCancelled.Margin = New System.Windows.Forms.Padding(3, 3, 9, 15)
        Me.CheckEditSearchCancelled.Name = "CheckEditSearchCancelled"
        Me.CheckEditSearchCancelled.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditSearchCancelled.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditSearchCancelled.Properties.Appearance.Options.UseFont = True
        Me.CheckEditSearchCancelled.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditSearchCancelled.Properties.AutoWidth = True
        Me.CheckEditSearchCancelled.Properties.Caption = "Search cancelled contracts"
        Me.CheckEditSearchCancelled.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditSearchCancelled.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditSearchCancelled.Size = New System.Drawing.Size(175, 19)
        Me.CheckEditSearchCancelled.TabIndex = 13
        '
        'TextEditProject
        '
        Me.TextEditProject.EditValue = ""
        Me.TextEditProject.Location = New System.Drawing.Point(133, 263)
        Me.TextEditProject.Margin = New System.Windows.Forms.Padding(3, 3, 15, 3)
        Me.TextEditProject.Name = "TextEditProject"
        Me.TextEditProject.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditProject.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditProject.Properties.Appearance.Options.UseFont = True
        Me.TextEditProject.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditProject.Size = New System.Drawing.Size(174, 20)
        Me.TextEditProject.TabIndex = 5
        Me.TextEditProject.ToolTip = "Quickly start the search by pressing" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "the Enter key after typing in this box."
        Me.TextEditProject.ToolTipController = Me.ToolTipControllerDefault
        Me.TextEditProject.ToolTipIconType = DevExpress.Utils.ToolTipIconType.Information
        '
        'ToolTipControllerDefault
        '
        Me.ToolTipControllerDefault.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(255, Byte), Integer), CType(CType(128, Byte), Integer), CType(CType(0, Byte), Integer))
        Me.ToolTipControllerDefault.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ToolTipControllerDefault.Appearance.Options.UseBackColor = True
        Me.ToolTipControllerDefault.Appearance.Options.UseFont = True
        Me.ToolTipControllerDefault.AutoPopDelay = 8000
        Me.ToolTipControllerDefault.InitialDelay = 100
        Me.ToolTipControllerDefault.Rounded = True
        Me.ToolTipControllerDefault.ShowBeak = True
        '
        'TextEditContract
        '
        Me.TextEditContract.EditValue = ""
        Me.TextEditContract.Location = New System.Drawing.Point(133, 211)
        Me.TextEditContract.Margin = New System.Windows.Forms.Padding(3, 3, 15, 3)
        Me.TextEditContract.Name = "TextEditContract"
        Me.TextEditContract.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditContract.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditContract.Properties.Appearance.Options.UseFont = True
        Me.TextEditContract.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditContract.Size = New System.Drawing.Size(174, 20)
        Me.TextEditContract.TabIndex = 0
        Me.TextEditContract.ToolTip = "Quickly start the search by pressing" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "the Enter key after typing in this box."
        Me.TextEditContract.ToolTipController = Me.ToolTipControllerDefault
        Me.TextEditContract.ToolTipIconType = DevExpress.Utils.ToolTipIconType.Information
        '
        'ImageList16x16
        '
        Me.ImageList16x16.ImageStream = CType(resources.GetObject("ImageList16x16.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList16x16.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList16x16.Images.SetKeyName(0, "add.png")
        Me.ImageList16x16.Images.SetKeyName(1, "pencil.png")
        Me.ImageList16x16.Images.SetKeyName(2, "delete.png")
        Me.ImageList16x16.Images.SetKeyName(3, "accept.png")
        Me.ImageList16x16.Images.SetKeyName(4, "print.png")
        Me.ImageList16x16.Images.SetKeyName(5, "remove.png")
        Me.ImageList16x16.Images.SetKeyName(6, "refresh.png")
        Me.ImageList16x16.Images.SetKeyName(7, "page.png")
        '
        'LabelMediaService
        '
        Me.LabelMediaService.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelMediaService.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelMediaService.Location = New System.Drawing.Point(12, 318)
        Me.LabelMediaService.Margin = New System.Windows.Forms.Padding(3, 3, 9, 3)
        Me.LabelMediaService.Name = "LabelMediaService"
        Me.LabelMediaService.Size = New System.Drawing.Size(85, 13)
        Me.LabelMediaService.TabIndex = 9
        Me.LabelMediaService.Text = "Media Service:"
        '
        'HyperlinkMediaService
        '
        Me.HyperlinkMediaService.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkMediaService.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkMediaService.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkMediaService.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkMediaService.AutoEllipsis = True
        Me.HyperlinkMediaService.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkMediaService.Location = New System.Drawing.Point(325, 318)
        Me.HyperlinkMediaService.Margin = New System.Windows.Forms.Padding(3, 3, 9, 10)
        Me.HyperlinkMediaService.Name = "HyperlinkMediaService"
        Me.HyperlinkMediaService.Size = New System.Drawing.Size(47, 13)
        Me.HyperlinkMediaService.TabIndex = 11
        Me.HyperlinkMediaService.Text = "Select..."
        '
        'LabelBrand
        '
        Me.LabelBrand.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelBrand.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelBrand.Location = New System.Drawing.Point(12, 292)
        Me.LabelBrand.Margin = New System.Windows.Forms.Padding(3, 3, 9, 3)
        Me.LabelBrand.Name = "LabelBrand"
        Me.LabelBrand.Size = New System.Drawing.Size(39, 13)
        Me.LabelBrand.TabIndex = 6
        Me.LabelBrand.Text = "Brand:"
        '
        'HyperlinkBrand
        '
        Me.HyperlinkBrand.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkBrand.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkBrand.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkBrand.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkBrand.AutoEllipsis = True
        Me.HyperlinkBrand.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkBrand.Location = New System.Drawing.Point(325, 292)
        Me.HyperlinkBrand.Margin = New System.Windows.Forms.Padding(3, 3, 9, 10)
        Me.HyperlinkBrand.Name = "HyperlinkBrand"
        Me.HyperlinkBrand.Size = New System.Drawing.Size(47, 13)
        Me.HyperlinkBrand.TabIndex = 8
        Me.HyperlinkBrand.Text = "Select..."
        '
        'TextEditBrand
        '
        Me.TextEditBrand.EditValue = ""
        Me.TextEditBrand.Location = New System.Drawing.Point(133, 289)
        Me.TextEditBrand.Margin = New System.Windows.Forms.Padding(3, 3, 15, 3)
        Me.TextEditBrand.Name = "TextEditBrand"
        Me.TextEditBrand.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditBrand.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditBrand.Properties.Appearance.Options.UseFont = True
        Me.TextEditBrand.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditBrand.Size = New System.Drawing.Size(174, 20)
        Me.TextEditBrand.TabIndex = 7
        Me.TextEditBrand.ToolTip = "Quickly start the search by pressing" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "the Enter key after typing in this box."
        Me.TextEditBrand.ToolTipController = Me.ToolTipControllerDefault
        Me.TextEditBrand.ToolTipIconType = DevExpress.Utils.ToolTipIconType.Information
        '
        'TextEditClient
        '
        Me.TextEditClient.EditValue = ""
        Me.TextEditClient.Location = New System.Drawing.Point(133, 237)
        Me.TextEditClient.Margin = New System.Windows.Forms.Padding(3, 3, 15, 3)
        Me.TextEditClient.Name = "TextEditClient"
        Me.TextEditClient.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditClient.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditClient.Properties.Appearance.Options.UseFont = True
        Me.TextEditClient.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditClient.Size = New System.Drawing.Size(174, 20)
        Me.TextEditClient.TabIndex = 2
        Me.TextEditClient.ToolTip = "Quickly start the search by pressing" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "the Enter key after typing in this box."
        Me.TextEditClient.ToolTipController = Me.ToolTipControllerDefault
        Me.TextEditClient.ToolTipIconType = DevExpress.Utils.ToolTipIconType.Information
        '
        'TextEditMediaService
        '
        Me.TextEditMediaService.EditValue = ""
        Me.TextEditMediaService.Location = New System.Drawing.Point(133, 315)
        Me.TextEditMediaService.Margin = New System.Windows.Forms.Padding(3, 3, 15, 3)
        Me.TextEditMediaService.Name = "TextEditMediaService"
        Me.TextEditMediaService.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditMediaService.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditMediaService.Properties.Appearance.Options.UseFont = True
        Me.TextEditMediaService.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditMediaService.Size = New System.Drawing.Size(174, 20)
        Me.TextEditMediaService.TabIndex = 10
        Me.TextEditMediaService.ToolTip = "Quickly start the search by pressing" & Global.Microsoft.VisualBasic.ChrW(13) & Global.Microsoft.VisualBasic.ChrW(10) & "the Enter key after typing in this box."
        Me.TextEditMediaService.ToolTipController = Me.ToolTipControllerDefault
        Me.TextEditMediaService.ToolTipIconType = DevExpress.Utils.ToolTipIconType.Information
        '
        'SubformContractStart
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.Controls.Add(Me.TextEditMediaService)
        Me.Controls.Add(Me.TextEditBrand)
        Me.Controls.Add(Me.TextEditClient)
        Me.Controls.Add(Me.CheckEditSearchHistory)
        Me.Controls.Add(Me.HyperlinkBrand)
        Me.Controls.Add(Me.HyperlinkMediaService)
        Me.Controls.Add(Me.HyperlinkClient)
        Me.Controls.Add(Me.LabelBrand)
        Me.Controls.Add(Me.LabelProject)
        Me.Controls.Add(Me.LabelMediaService)
        Me.Controls.Add(Me.LabelContract)
        Me.Controls.Add(Me.LabelClient)
        Me.Controls.Add(Me.CheckEditSearchCancelled)
        Me.Controls.Add(Me.TextEditProject)
        Me.Controls.Add(Me.TextEditContract)
        Me.Controls.Add(Me.LabelSearchTitle)
        Me.Controls.Add(Me.ButtonSearch)
        Me.Controls.Add(Me.ButtonNew)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Name = "SubformContractStart"
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        Me.Controls.SetChildIndex(Me.ButtonNew, 0)
        Me.Controls.SetChildIndex(Me.ButtonSearch, 0)
        Me.Controls.SetChildIndex(Me.LabelSearchTitle, 0)
        Me.Controls.SetChildIndex(Me.TextEditContract, 0)
        Me.Controls.SetChildIndex(Me.TextEditProject, 0)
        Me.Controls.SetChildIndex(Me.CheckEditSearchCancelled, 0)
        Me.Controls.SetChildIndex(Me.LabelClient, 0)
        Me.Controls.SetChildIndex(Me.LabelContract, 0)
        Me.Controls.SetChildIndex(Me.LabelMediaService, 0)
        Me.Controls.SetChildIndex(Me.LabelProject, 0)
        Me.Controls.SetChildIndex(Me.LabelBrand, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkClient, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkMediaService, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkBrand, 0)
        Me.Controls.SetChildIndex(Me.CheckEditSearchHistory, 0)
        Me.Controls.SetChildIndex(Me.TextEditClient, 0)
        Me.Controls.SetChildIndex(Me.TextEditBrand, 0)
        Me.Controls.SetChildIndex(Me.TextEditMediaService, 0)
        CType(Me.CheckEditSearchHistory.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditSearchCancelled.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditProject.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditContract.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditBrand.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditClient.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditMediaService.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents ButtonNew As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonSearch As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelSearchTitle As DevExpress.XtraEditors.LabelControl
    Friend WithEvents CheckEditSearchHistory As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents HyperlinkClient As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelProject As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelContract As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelClient As DevExpress.XtraEditors.LabelControl
    Friend WithEvents CheckEditSearchCancelled As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents TextEditProject As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEditContract As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ImageList16x16 As System.Windows.Forms.ImageList
    Friend WithEvents LabelMediaService As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkMediaService As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelBrand As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkBrand As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditBrand As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEditClient As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEditMediaService As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ToolTipControllerDefault As DevExpress.Utils.ToolTipController

End Class
