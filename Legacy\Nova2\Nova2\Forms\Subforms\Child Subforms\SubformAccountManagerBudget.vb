Public Class SubformAccountManagerBudget

    Private _DataBindingSource As BindingSource

#Region "Properties"

    Private Property DataBindingSource() As BindingSource
        Get
            Return _DataBindingSource
        End Get
        Set(ByVal value As BindingSource)
            _DataBindingSource = value
        End Set
    End Property

    Private ReadOnly Property Row() As DataRow
        Get
            Return CType(DataBindingSource.Current, DataRowView).Row
        End Get
    End Property

#End Region

#Region "Event Handlers"

    Private Sub ButtonCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonCancel.Click
        DataBindingSource.CancelEdit()
        RevertToParentSubform()
    End Sub

    Private Sub ButtonOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonOK.Click
        Save(True)
    End Sub

    Private Sub TextEditBudget_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles TextEditBudget.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Update the data object.
        Row("Budget") = CDec(ValidatedControl.EditValue)

    End Sub

    Private Sub HyperlinkFiscal_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles HyperlinkFiscal.Click

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupFiscal.SelectRows(My.Settings.DBConnection, True, Nothing)

        ' Update the data object and the label with the selection.
        If IsNothing(SelectedItems) = False Then
            If SelectedItems.Count > 0 Then
                Row("FiscalID") = SelectedItems(0).Item("FiscalID")
                CurrentControl.Text = SelectedItems(0).Item("FiscalName").ToString
                ' Reset the error text of the control to an empty string.
                LiquidAgent.ControlValidation(CurrentControl, String.Empty)
            End If
        End If

    End Sub

    Private Sub HyperlinkFiscal_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles HyperlinkFiscal.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        If String.Compare(ValidatedControl.Text, "Select...") = 0 _
        Or String.Compare(ValidatedControl.Text, "(unknown)") = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "The financial year must be selected.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

#End Region

#Region "Public Methods"

    Public Sub New(ByVal Grid As DataGridView, ByVal NewItem As Boolean)

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.

        ' Get the binding source of the supplied grid.
        DataBindingSource = CType(Grid.DataSource, BindingSource)

        ' Add a new row to the binding source list if required.
        If NewItem Then
            DataBindingSource.AddNew()
        End If

        ' Do data binding.
        HyperlinkFiscal.DataBindings.Add("Text", DataBindingSource, "FiscalName")
        TextEditBudget.DataBindings.Add("EditValue", DataBindingSource, "Budget")

    End Sub

#End Region

#Region "Protected Methods"

    Protected Overrides Function Save() As Boolean
        DataBindingSource.EndEdit()
        Return True
    End Function

#End Region

End Class
