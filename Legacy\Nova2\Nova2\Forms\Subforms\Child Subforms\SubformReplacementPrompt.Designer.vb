﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformReplacementPrompt
    Inherits LiquidShell.EditorForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformReplacementPrompt))
        Me.CheckedListBoxReasonsForCancelAndReplace = New System.Windows.Forms.CheckedListBox()
        Me.ButtonOK = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.chkIsReplacement = New System.Windows.Forms.CheckBox()
        Me.LabelProposalHeat = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkProposalHeat = New DevExpress.XtraEditors.LabelControl()
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelButtonBar.SuspendLayout()
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'PanelButtonBar
        '
        Me.PanelButtonBar.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelButtonBar.Appearance.BackColor2 = System.Drawing.Color.LightSteelBlue
        Me.PanelButtonBar.Appearance.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical
        Me.PanelButtonBar.Appearance.Options.UseBackColor = True
        Me.PanelButtonBar.Controls.Add(Me.ButtonCancel)
        Me.PanelButtonBar.Location = New System.Drawing.Point(0, 216)
        Me.PanelButtonBar.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Office2003
        Me.PanelButtonBar.LookAndFeel.UseDefaultLookAndFeel = False
        Me.PanelButtonBar.Margin = New System.Windows.Forms.Padding(5, 0, 5, 5)
        Me.PanelButtonBar.Padding = New System.Windows.Forms.Padding(0, 16, 0, 0)
        Me.PanelButtonBar.Size = New System.Drawing.Size(665, 68)
        '
        'CheckedListBoxReasonsForCancelAndReplace
        '
        Me.CheckedListBoxReasonsForCancelAndReplace.CheckOnClick = True
        Me.CheckedListBoxReasonsForCancelAndReplace.FormattingEnabled = True
        Me.CheckedListBoxReasonsForCancelAndReplace.Items.AddRange(New Object() {"Saving-Time on Creating a new contract", "Date changes on contract", "Media changes (bursts)", "Incorrect Billing Instructions", "Signed incorrect version of contract", "Value of contract changed"})
        Me.CheckedListBoxReasonsForCancelAndReplace.Location = New System.Drawing.Point(4, 7)
        Me.CheckedListBoxReasonsForCancelAndReplace.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.CheckedListBoxReasonsForCancelAndReplace.Name = "CheckedListBoxReasonsForCancelAndReplace"
        Me.CheckedListBoxReasonsForCancelAndReplace.Size = New System.Drawing.Size(655, 118)
        Me.CheckedListBoxReasonsForCancelAndReplace.TabIndex = 0
        '
        'ButtonOK
        '
        Me.ButtonOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonOK.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonOK.Appearance.Options.UseFont = True
        Me.ButtonOK.DialogResult = System.Windows.Forms.DialogResult.OK
        Me.ButtonOK.Image = Global.Nova2.My.Resources.Resources.accept16
        Me.ButtonOK.ImageIndex = 2
        Me.ButtonOK.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonOK.Location = New System.Drawing.Point(384, 229)
        Me.ButtonOK.LookAndFeel.SkinName = "Black"
        Me.ButtonOK.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonOK.Margin = New System.Windows.Forms.Padding(4, 16, 4, 4)
        Me.ButtonOK.Name = "ButtonOK"
        Me.ButtonOK.Size = New System.Drawing.Size(130, 39)
        Me.ButtonOK.TabIndex = 2
        Me.ButtonOK.Text = "OK"
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.ButtonCancel.Image = Global.Nova2.My.Resources.Resources.delete16
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.Location = New System.Drawing.Point(522, 13)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Margin = New System.Windows.Forms.Padding(4, 16, 4, 4)
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(130, 39)
        Me.ButtonCancel.TabIndex = 3
        Me.ButtonCancel.Text = "Cancel"
        '
        'chkIsReplacement
        '
        Me.chkIsReplacement.AutoSize = True
        Me.chkIsReplacement.BackColor = System.Drawing.SystemColors.Control
        Me.chkIsReplacement.Font = New System.Drawing.Font("Segoe UI", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.chkIsReplacement.ForeColor = System.Drawing.SystemColors.Desktop
        Me.chkIsReplacement.Location = New System.Drawing.Point(9, 149)
        Me.chkIsReplacement.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.chkIsReplacement.Name = "chkIsReplacement"
        Me.chkIsReplacement.Size = New System.Drawing.Size(270, 23)
        Me.chkIsReplacement.TabIndex = 4
        Me.chkIsReplacement.Text = "This Is To Replace Selected Contract"
        Me.chkIsReplacement.UseVisualStyleBackColor = False
        '
        'LabelProposalHeat
        '
        Me.LabelProposalHeat.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelProposalHeat.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelProposalHeat.Location = New System.Drawing.Point(9, 184)
        Me.LabelProposalHeat.Margin = New System.Windows.Forms.Padding(4, 4, 13, 4)
        Me.LabelProposalHeat.Name = "LabelProposalHeat"
        Me.LabelProposalHeat.Size = New System.Drawing.Size(134, 17)
        Me.LabelProposalHeat.TabIndex = 5
        Me.LabelProposalHeat.Text = "Proposal Strength:"
        '
        'HyperlinkProposalHeat
        '
        Me.HyperlinkProposalHeat.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkProposalHeat.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkProposalHeat.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkProposalHeat.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkProposalHeat.AutoEllipsis = True
        Me.HyperlinkProposalHeat.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkProposalHeat.Location = New System.Drawing.Point(177, 184)
        Me.HyperlinkProposalHeat.Margin = New System.Windows.Forms.Padding(4, 4, 13, 14)
        Me.HyperlinkProposalHeat.Name = "HyperlinkProposalHeat"
        Me.HyperlinkProposalHeat.Size = New System.Drawing.Size(57, 17)
        Me.HyperlinkProposalHeat.TabIndex = 6
        Me.HyperlinkProposalHeat.Text = "Select..."
        '
        'SubformReplacementPrompt
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(9.0!, 17.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(665, 284)
        Me.Controls.Add(Me.HyperlinkProposalHeat)
        Me.Controls.Add(Me.LabelProposalHeat)
        Me.Controls.Add(Me.chkIsReplacement)
        Me.Controls.Add(Me.ButtonOK)
        Me.Controls.Add(Me.CheckedListBoxReasonsForCancelAndReplace)
        Me.Icon = CType(resources.GetObject("$this.Icon"), System.Drawing.Icon)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(6, 7, 6, 7)
        Me.MaximizeBox = False
        Me.Name = "SubformReplacementPrompt"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent
        Me.Text = "Select Reason(s) For Cloning Contract"
        Me.TopMost = True
        Me.Controls.SetChildIndex(Me.PanelButtonBar, 0)
        Me.Controls.SetChildIndex(Me.CheckedListBoxReasonsForCancelAndReplace, 0)
        Me.Controls.SetChildIndex(Me.ButtonOK, 0)
        Me.Controls.SetChildIndex(Me.chkIsReplacement, 0)
        Me.Controls.SetChildIndex(Me.LabelProposalHeat, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkProposalHeat, 0)
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelButtonBar.ResumeLayout(False)
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub

    Friend WithEvents CheckedListBoxReasonsForCancelAndReplace As CheckedListBox
    Friend WithEvents ButtonOK As SimpleButton
    Friend WithEvents ButtonCancel As SimpleButton
    Friend WithEvents chkIsReplacement As CheckBox
    Friend WithEvents LabelProposalHeat As LabelControl
    Friend WithEvents HyperlinkProposalHeat As LabelControl
End Class
