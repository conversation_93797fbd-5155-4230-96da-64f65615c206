﻿using DataAccess;

namespace DataService.Security
{
    class CheckRoleMembershipOfUserCommandExecutor : CommandExecutor<CheckRoleMembershipOfUserCommand>
    {

        public override void Execute(CheckRoleMembershipOfUserCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.CheckRoleMembershipOfUser))
            {
                storedprocedure.AddInputParameter("userid", command.UserId);
                storedprocedure.AddInputParameter("rolename", command.Rolename);
                storedprocedure.AddOutputParameter<bool>("ismember");
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    command.IsMember = (bool)storedprocedure.GetOutputParameterValue("ismember");
                }
            }
        }

    }
}
