Public Class SubformCostEstimate

    Private _DataBindingSource As BindingSource
    Private ParentContract As OldContract
    Private _GridOfCostEstimates As DataGridView
    Private _ContractNumber As String
    Private _MediaId As Int32
#Region "Properties"
    Private Property DataBindingSource() As BindingSource
        Get
            Return _DataBindingSource
        End Get
        Set(ByVal value As BindingSource)
            _DataBindingSource = value
        End Set
    End Property
    Private ReadOnly Property Row() As DataRow
        Get
            Return CType(DataBindingSource.Current, DataRowView).Row
        End Get
    End Property
#End Region
#Region "Event Handlers"
    Private Sub ButtonCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonCancel.Click
        DataBindingSource.CancelEdit()
        RevertToParentSubform()
    End Sub
    Private Sub ButtonOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonOK.Click
        Save(True)
    End Sub
#End Region
#Region "Public Methods"
    Public Sub New(ByVal Grid As DataGridView, ByVal NewItem As Boolean, ByVal ContractNumber As String)

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.

        ' Get the binding source of the supplied grid.
        DataBindingSource = CType(Grid.DataSource, BindingSource)
        _ContractNumber = ContractNumber
        ' Add a new row to the binding source list if required.
        If NewItem Then
            DataBindingSource.AddNew()
        End If
        _GridOfCostEstimates = Grid
        ' Do data binding.
        TextEditCostEstimate.DataBindings.Add("EditValue", DataBindingSource, "CostEstimateNumber", False, DataSourceUpdateMode.OnPropertyChanged)
        TextEditCostEstimateAmount.DataBindings.Add("EditValue", DataBindingSource, "CostEstimateAmount", False, DataSourceUpdateMode.OnPropertyChanged)
        HyperlinkMediaService.DataBindings.Add("Text", DataBindingSource, "MediaName", False, DataSourceUpdateMode.OnValidation)


    End Sub
#End Region
#Region "Protected Methods"
    Protected Overrides Function Save() As Boolean
        DataBindingSource.EndEdit()
        Return True
    End Function

    Private Sub TextEditCostEstimate_EditValueChanged(sender As Object, e As EventArgs) Handles TextEditCostEstimate.EditValueChanged
        ' Create an object to represent the control.
        Dim CurrentControl As TextEdit = CType(sender, TextEdit)


        ' Reset the error text of the control to an empty string.
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)
    End Sub

    Private Sub TextEditCostEstimateAmount_EditValueChanged(sender As Object, e As EventArgs) Handles TextEditCostEstimateAmount.EditValueChanged
        ' Create an object to represent the control.
        Dim CurrentControl As TextEdit = CType(sender, TextEdit)


        ' Reset the error text of the control to an empty string.
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)
    End Sub

    Private Sub TextEditCostEstimate_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
   Handles TextEditCostEstimate.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If ValidatedControl.Text.Length = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "You must enter a cost estimate number.")
            Exit Sub
        Else

            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If



    End Sub

    Private Sub HyperlinkMediaService_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles HyperlinkMediaService.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        If String.Compare(ValidatedControl.Text, "Select...") = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "A media service must be selected.")

        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub


    Private Sub HyperlinkMediaService_Click(sender As Object, e As EventArgs) Handles HyperlinkMediaService.Click
        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupMedia.SelectRowsByContractMedia(My.Settings.DBConnection, False, Nothing, _ContractNumber)

        ' Update the data object and the label with the selection.
        If IsNothing(SelectedItems) = False Then
            If SelectedItems.Count > 0 Then
                'DataObject.principal_id = SelectedItems(0).Item("principal_id")
                'DataObject.Username = SelectedItems(0).Item("name")
                CurrentControl.Text = SelectedItems(0).Item("MediaName").ToString
                _MediaId = SelectedItems(0).Item("MediaId")
                _GridOfCostEstimates.CurrentRow.Cells("MediaId").Value = _MediaId
                ' Reset the error text of the control to an empty string.
                LiquidAgent.ControlValidation(CurrentControl, String.Empty)
            End If
        End If
    End Sub

    Private Sub LabelUser_Click(sender As Object, e As EventArgs) Handles LabelUser.Click

    End Sub
#End Region


End Class