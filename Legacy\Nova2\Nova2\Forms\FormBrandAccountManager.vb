Public Class FormBrandAccountManager

    Private FirstName As String = "NOBODY"
    Private SelectedBrands As List(Of DataRow)
    Private GridTable As DataTable
    Private ExistingBrandAccountManagerTable As DataTable

    Public Sub New(ByVal AccountManagerFirstName As String, ByVal SelectedBrandList As List(Of DataRow))

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        FirstName = AccountManagerFirstName
        SelectedBrands = SelectedBrandList

        ' Configure grid manager.
        SetupGridDataSource()
        GridController = New GridManager(Grid, TextEditSearch, Nothing, Nothing,
        PictureAdvancedSearch, PictureClearSearch, Nothing, Nothing)

        ' Update the table of existing account managers of this client.
        For Each SelectedBrand As DataRow In SelectedBrandList
            AccountManager.UpdateExistingBrandAccountManagers _
            (Me, My.Settings.DBConnection, SelectedBrand("BrandID"), ExistingBrandAccountManagerTable)
        Next

    End Sub

    Private Sub FormBrandAccountManager_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing

        ' Exit if cancel button was clicked.
        If Not DialogResult = Windows.Forms.DialogResult.OK Then
            Exit Sub
        End If

        ' Update the care taking columns of the grid table with value of the CheckEditCareTaker check box.
        If CheckEditCareTaker.Checked Then

            ' Validate CareTakerWeeks input.
            Dim CareTakerWeeks As Integer = 0
            If Integer.TryParse(TextEditCareTakerWeeks.Text, CareTakerWeeks) = False Then
                ' Validation failed.
                LiquidAgent.ControlValidation(TextEditCareTakerWeeks, "The care taking weeks quantity must be a valid number.")
                e.Cancel = True
                Exit Sub
            End If

            ' Update the table with selected care taking values.
            For Each ClientAccount As DataRow In GridTable.Rows
                ClientAccount("CareTaker") = True
                ClientAccount("CareTakerWeeks") = CareTakerWeeks
            Next

        Else

            ' Create a list of warnings about existing rows with the same client / effective date combination because
            ' these existing rows will break the primary key in the database.
            Dim MessageText As New System.Text.StringBuilder

            ' Check if selected client / date combinations don't conflict with existing rows.
            For Each GridRow As DataGridViewRow In Grid.Rows
                ' Get the data bound item of the grid row.
                Dim GridRowView As DataRowView = CType(GridRow.DataBoundItem, DataRowView)
                ' Build a key to find an existing row in the ExistingVrandAccountManagerTable table.
                Dim Keys(1) As Object
                Keys(0) = GridRowView("BrandID")
                Keys(1) = GridRowView("EffectiveDate")
                ' Find an existing row that will prevent the current grid row from being saved.
                Dim ExistingBrandAccountManager As DataRow = ExistingBrandAccountManagerTable.Rows.Find(Keys)
                If Not IsNothing(ExistingBrandAccountManager) Then
                    ' An existing account manager has this grid rows effective date and so this row may not be saved.
                    MessageText.Append(GridRowView("BrandName") & " has already been assigned to " _
                    & ExistingBrandAccountManager("AccountManagerFullName") & " from the selected date.")
                    MessageText.AppendLine()
                End If
            Next

            ' Display warning and cancel event if existing rows were found.
            If MessageText.Length > 0 Then
                MessageText.AppendLine()
                MessageText.Append("To correct this, select a different date for the account manager to ")
                MessageText.Append("start managing the above brand(s).")
                ShowMessage(MessageText.ToString, "Data Conflict Detected", MessageBoxIcon.Error)
                e.Cancel = True
                Exit Sub
            End If

        End If

        ' Select all grid rows so that the base form's GetSelection method will return all rows.
        Grid.SelectAll()

    End Sub

    Private Sub TextEdit_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles TextEditCareTakerWeeks.EditValueChanged

        ' Create an object to represent the control.
        Dim CurrentControl As TextEdit = CType(sender, TextEdit)

        ' Reset the error text of the control to an empty string.
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)

    End Sub

    Private Sub TextEditCareTakerWeeks_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles TextEditCareTakerWeeks.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        Dim CareTakerWeeks As Integer = 0
        If Integer.TryParse(TextEditCareTakerWeeks.Text, CareTakerWeeks) = False Then
            ' Validation failed.
            LiquidAgent.ControlValidation(TextEditCareTakerWeeks, "The care taking weeks quantity must be a valid number.")
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub FormBrandAccountManager_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        ' Update the checkbox text with the selected account manager's name.
        CheckEditCareTaker.Text = FirstName & CheckEditCareTaker.Text

    End Sub

    Private Sub ButtonDate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonDate.Click
        ' This event handler changes the Effective Date of all selected rows in the grid to one selected by
        ' the user.

        ' Check that at least one grid row is selected..
        Dim ParentForm As BaseForm = CType(TopLevelControl, BaseForm)
        If Grid.SelectedRows.Count < 1 Then
            ' Stop because no rows are selected.
            ParentForm.ShowMessage("Please select one or more clients before using the 'Date' button.")
            Exit Sub
        End If

        ' Get the current date of the first selected row (to pass into the date selector later on).
        Dim CurrentDate As Date = CType(Grid.SelectedRows(0).DataBoundItem, DataRowView).Row("EffectiveDate")

        ' Obtain a date to apply to selected rows.
        Dim SelectedDate As Nullable(Of Date) = DateSelector.GetDate(True, CurrentDate)

        ' Aply the selected date to all selected rows.
        If SelectedDate.HasValue Then
            For Each GridRow As DataGridViewRow In Grid.SelectedRows
                CType(GridRow.DataBoundItem, DataRowView).Row("EffectiveDate") = SelectedDate.Value
            Next
        End If

    End Sub

    Private Sub SetupGridDataSource()
        ' Take the list of datarows and make a binding source to use as the data source of the grid.

        ' Create a data table to feed the binding source.
        Dim Columns(5) As DataColumn
        Columns(0) = New DataColumn("BrandID", GetType(Guid))
        Columns(1) = New DataColumn("BrandName", GetType(String))
        Columns(2) = New DataColumn("EffectiveDate", GetType(Date))
        Columns(3) = New DataColumn("CareTaker", GetType(Boolean))
        Columns(4) = New DataColumn("CareTakerWeeks", GetType(Integer))
        Columns(5) = New DataColumn("CurrentAccountManager", GetType(String))
        GridTable = New DataTable("GridTable")
        GridTable.Columns.AddRange(Columns)
        Dim BrandAccountManagerDataSet As New DataSet
        BrandAccountManagerDataSet.Tables.Add(GridTable)

        ' Populate the table using the given clients.
        For Each SelectedBrand As DataRow In SelectedBrands
            ' Create a new row.
            Dim NewGridRow As DataRow = GridTable.NewRow
            ' Create the default EffectiveDate value for the row (it must be the next Monday).
            Dim DefaultDate As Date = Today.Date
            While Not DefaultDate.DayOfWeek = DayOfWeek.Monday
                DefaultDate = DefaultDate.AddDays(1)
            End While
            ' Set default values for the row.
            NewGridRow("BrandID") = SelectedBrand("BrandID")
            NewGridRow("BrandName") = SelectedBrand("BrandName")
            NewGridRow("EffectiveDate") = DefaultDate
            NewGridRow("CareTaker") = False
            NewGridRow("CareTakerWeeks") = 0
            NewGridRow("CurrentAccountManager") = SelectedBrand("AccountManagerName")
            ' Add the row to the table.
            GridTable.Rows.Add(NewGridRow)
        Next

        ' Create the binding source.
        Dim GridBindingSource As New BindingSource(BrandAccountManagerDataSet, "GridTable")
        Grid.AutoGenerateColumns = False
        Grid.DataSource = GridBindingSource

    End Sub

End Class
