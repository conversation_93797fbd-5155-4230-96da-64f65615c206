<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="ImageList24x24.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>144, 17</value>
  </metadata>
  <data name="ImageList24x24.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj00LjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAABi
        FAAAAk1TRnQBSQFMAgEBAwEAATgBAAE4AQABGAEAARgBAAT/AREBAAj/AUIBTQE2BwABNgMAASgDAAFg
        AwABGAMAAQEBAAEQBgABEhgAAf8BfwGdAXMBewFvAZwBcwH/AX8QAAH/AX8BnAFzAXsBbwGdAXMB/wF/
        GAAB/wF/Af8BfwH/AX98AAH/AX8BewFvAf8BewH/AXsB/wF7AZwBcwH/AX8MAAH/AX8BnAFzAf8BewH/
        AXsB/wJ7AW8B/wF/FgABvQF3AXsBbwGcAW8B3gF7GgAB/wF/Ab0BdwGcAXMBnAFzAZwBcwGcAXMBnAFz
        AZwBcwGcAXMBnAFzAZwBcwGcAXMBnAFzAZwBcwGcAXMBnAFzAZwBcwGcAXMBnAFzAd4BezYAAf8BfwF7
        AW8B/wF7ARgBawHGAUAB7gFVAf8BewGcAXMB/wF/CAAB/wF/AZwBbwH/AXsB7gFVAcYBQAEYAWsB/wJ7
        AW8B/wF/EAAB/wF/AXsBbwG/AXsB3wF/Ad8BfwGcAXMB/wF/FgAB/wF/AVoBawHeAXMB/wFzAf8BcwH/
        AXMB/wFzAf8BcwH/AXMB/wFzAf8BcwH/AXMB/wFzAf8BcwH/AXMB/wFzAf8BcwH/AXMB/wFzAf8BcwF7
        AW8B3gF7MgAB/wF/AXsBbwH/AXsB1gFmAQABNAEAATQBAAE0AWoBTQH/AXsBnQFzAf8BfwQAAf8BfwGd
        AXMB/wF7AWoBTQEAATQBAAE0AQABNAHWAWYB/wJ7AW8B/wF/DAAB/wF/AXsBbwHfAX8BeAFnAWgBOgGu
        AUoB3wJ7AW8B/wF/EgAB/wF/AZwBbwHfAXMB9QFyAU0BcgFNAXIBTQFyAU0BcgFNAXIBTQFyAU0BcgFN
        AXIBTQFyAU0BcgFNAXIBTQFyAU0BcgFNAXIBTQFyAU0BcgGxAXIB3gFzAb4BcwH/AX8wAAG9AXcB/wF7
        AfcBagEAATgBQQE8AWMBQAFjAUABAAE4AYwBUQH/AX8BnAFzAf8BfwH/AX8BnAFzAf8BfwGMAVEBAAE4
        AWMBQAFjAUABQQE8AQABOAH3AWoB/wF7Ab0BdwoAAf8BfwF7AW8B3wF/AVYBYwEBASYBAQEmAeABHQHw
        AU4B3wF/AZwBcxIAAZwBcwH/AXcBTgFyAcABcAEAAXEBAAFxAQABcQEAAXEBAAFxAQABcQEAAXEB4AFw
        AcABbAEAAXEBIAFxAQABcQEAAXEBAAFxAQABcQEAAXEB4AFwAccBcQH/AXcBnAFvMAABfAFvAf8BewFj
        AUABIAE8AYMBQAFjAUABYwFAAWIBQAEAATgBjAFVAf8BewF8AW8BfAFvAf8BfwGsAVUBAAE4AWIBQAFj
        AUABYwFAAYMBQAEgATwBYwFAAf8BewGcAW8IAAH/AX8BewFvAd8BfwF5AWsBIQEqAUMBLgFEATIBRAEu
        ASIBKgGcAXMBvgF3Af8BfxAAAZwBcwH/AXcBYwFxAUABcQFiAXEBYgFxAWIBcQFiAXEBYgFxAWIBcQFA
        AXEBTQFyAY8BcgFhAXEBYgFxAWIBcQFiAXEBYgFxAWIBcQFiAXEBYQFxASABcQG9AXcBfAFvMAABnAFz
        Af8BewHmAUwBAAFAAYMBRAFjAUQBYwFEAWMBRAFiAUQBAAE8AWsBVQH/AX8B/wF/AWsBVQEAATwBYgFE
        AWMBRAFjAUQBYwFEAYMBRAEAAUAB5gFMAf8BewGcAXMGAAH/AX8BewFvAd8BfwF4AWcBQgEuAUMBLgFk
        ATIBZAEyAWQBMgFBASoBywFGAf8BfwF7AW8QAAG9AXcB/wF3AQkBcgFAAXEBggFxAYIBcQGCAXEBggFx
        AYIBcQFAAXEBKgFyAf8BdwH/AXcBjwFyAUABcQGCAXEBggFxAYIBcQGCAXEBggFxAUABcQGlAXEB/wF3
        AZwBczAAAf8BfwH/AXsBvQF3AWIBRAEgAUQBgwFIAWMBSAFjAUgBYwFIAWMBSAEAAUAB7wFdAe8BXQEA
        AUABYgFIAWMBSAFjAUgBYwFIAYMBSAEgAUQBYgFEAb0BdwH/AXsB/wF/BAAB/wF/AXsBbwHfAX8BeQFr
        AUEBLgFjATIBhAE2AYQBNgGEATYBhAE2AWQBNgFAASoBdwFnAd8BfwHeAXsOAAH/AX8B3wF3AXoBdwFA
        AXEBggFxAYIBcQGCAXEBggFxAYIBcQFgAXEBCQFyAf8BdwH/AXcBbQFyAUABcQGCAXEBggFxAYIBcQGC
        AXEBggFxASABcQEVAXcB/wF3Af8BfzIAAd4BewH/AXsBnAF3AYMBSAEgAUQBgwFIAYMBSAGDAUgBgwFI
        AWMBSAEgAUQBIAFEAWMBSAGDAUgBgwFIAYMBSAGDAUgBIAFEAYMBSAGcAXcB/wF7Ad4BewQAAf8BfwGc
        AXMB3wF/AZoBbwGDATIBgwEyAYQBNgGEATYBhAE2AYQBNgGEATYBhAE2AYMBMgGlAToB3wF7AXwBcwH/
        AX8OAAGcAXMB/wF3AUsBdgFAAXEBogFxAaIBcQGiAXEBogFxAaIBcQFgAXEBBwFyASkBcgFgAXEBggFx
        AaIBcQGiAXEBogFxAaIBcQFgAXEB5gFxAf8BdwGcAXM2AAHeAXsB/wF/Ad4BdwGDAUwBIAFIAYMBTAGD
        AUwBgwFMAYMBTAGDAUwBgwFMAYMBTAGDAUwBgwFMAYMBTAEgAUgBgwFMAd4BdwH/AX8B3gF7BgABnAFz
        Af8BfwGaAW8BggEyAYMBNgGkAToBpAE6AaQBOgGkAToBpAE6AaQBOgGkAToBpAE6AYABLgEvAVMB/wF/
        AZwBcw4AAf8BfwG+AXcB3gF3AYABcQGhAXEBogFxAaIBcQGiAXEBogFxAYABcQF6AXcBnAF3AaEBcQGh
        AXEBogFxAaIBcQGiAXEBogFxAWABcQF6AXcB/wF3Af8BfzgAAd4BewH/AX8BvQF3AWIBTAEgAUgBgwFQ
        AYMBUAGDAVABgwFQAYMBUAGDAVABgwFQAYMBUAEgAUgBYgFMAb0BdwH/AX8B3gF7BgAB3gF7Ad8BfwG7
        AXMBogE2AaIBNgHEAToBxAE6AcQBOgHEAToBogE2AaIBNgHEAToBxAE6AcQBOgGkAToBogE2AbwBdwHf
        AX8B/wF/DgAB3gF7Af8BewHyAXYBYAFxAcIBcQHCAXEBwgFxAaEBcQHkAXEB/wF7Af8BewEGAXIBgAFx
        AcIBcQHCAXEBwgFxAWABcQGNAXYB/wF7Ab0BdzwAAd4BewH/AXsB3gF7AeYBVAFBAVABgwFQAYMBUAGD
        AVABgwFQAYMBUAGDAVABQQFQAeYBVAHeAXsB/wF7Ad4BewgAAZwBcwHfAX8B5gE+AcIBNgHEAT4BxAE+
        AcQBPgHEAT4BoAEyAVABVwFQAVsBoQE2AcQBPgHEAT4BxAE+AcIBNgHoAUYB/wF/AXwBbwH/AX8OAAG9
        AXMB/wF7AQUBdgGgAXEBwgFxAcIBcQGgAXEBBgF2Af8BewH/AXsBSQF2AaABcQHCAXEBwgFxAaEBcQHB
        AXEB/wF7Ad4Bdz4AAf8BfwG9AXMB/wF/Ac4BYQEgAVABgwFUAYMBVAGDAVQBgwFUAYMBVAGDAVQBIAFQ
        Ac4BYQH/AX8BvQFzAf8BfwgAAZwBcwH/AX8BBgFDAcEBNgHlAT4B5AE+AeQBPgHAATYBLAFTAf8BfwH/
        AX8B5AE+AeMBOgHkAT4B5AE+AeQBPgHAATYBcQFfAf8BfwGcAXMOAAH/AX8B/wF7AXkBewGAAXEBwgF1
        AeIBdQGgAXEBJwF2Af8BewH/AXsBawF2AaABcQHiAXUB4gF1AYABcQEUAXcB/wF7Ad4BezwAAf8BfwG9
        AXMB/wF/Aa0BYQEAAVABYwFUAYMBVAGDAVQBgwFUAYMBVAGDAVQBgwFUAWMBVAEAAVABrQFhAf8BfwG9
        AXMB/wF/BgAB/wF/Af8BfwHdAXcB5AE+AeIBOgEEAUMBwAE2ASsBUwH/AX8BvgF3Ad8BfwG4AW8B4AE2
        AeQBQgEEAUMBBAFDAeQBPgHhAToBugFzAf8BfwG9AXcOAAG9AXMB/wF7AYsBdgGgAXEB4gF1AcABcQFp
        AXYB/wF7Af8BewGuAXYBoAFxAeIBdQHAAXEBJQF2Af8BewGcAXM8AAH/AX8B3gF3Af8BfwHNAWUBAAFQ
        AYMBWAGDAVgBgwFYAYMBWAGDAVgBgwFYAYMBWAGDAVgBgwFYAYMBWAEAAVABzQFlAf8BfwG+AXcB/wF/
        BgAB3gF7Af8BfwHdAXcB4wFCAcABNgEqAVMB/wF/Ad4BewH/AX8BvQF3Af8BfwFNAVcB4AE6AQQBQwEE
        AUMBBAFDAeIBPgEFAUcB/wF/Ad8BfwH/AX8MAAH/AX8B3gF3Ad4BewHgAXEB4QF1AcABcQGLAXYB/wF/
        Af8BfwHwAXYBwAFxAeIBdQGgAXEBmgF7Af8BewH/AX86AAH/AX8BvQFzAf8BfwHOAWkBAAFUAYMBWAGD
        AVwBgwFcAYMBXAGDAVwBYgFYAWIBWAGDAVwBgwFcAYMBXAGDAVwBgwFYAQABVAHOAWkB/wF/Ab0BcwH/
        AX8GAAHeAXsB/wF/Ad0BewGWAWsB/wF/Ad8BfwH/AX8CAAH/AX8B3gF7Af8BfwEDAUMBAgFDAQQBRwEE
        AUcBBAFHAeABPgEqAVMB/wF/Ab0BdwH/AX8MAAHeAXsB/wF/ARIBewHAAXEBwAF1AawBdgH/AX8B/wF/
        ARIBewHAAXUBwAF1AawBdgH/AX8BvQF3OgAB/wF/Ab0BdwH/AX8BrAFpAQABWAGDAVwBgwFcAYMBXAGD
        AVwBgwFcASABXAHGAWAB5gFgASABXAGDAVwBgwFcAYMBXAGDAVwBgwFcAQABWAGsAWkB/wF/Ab0BdwH/
        AX8GAAHeAXsBvgF7Af8BfwG9AXcB/wF/BgAB3gF7Af8BfwG4AW8B4AE+AQMBRwEDAUcBAwFHAQMBRwHg
        AT4BTAFXAf8BfwG9AXcB/wF/DAABvQF3Af8BfwEjAXYBwAF1AWcBdgH/AX8B/wF/AawBdgHAAXUBAQF2
        Af8BfwH/AXsB/wF/OgABnAFzAf8BfwEPAW4BAAFYAYMBYAGDAWABgwFgAYMBYAGDAWABIAFcAYMBYAH/
        AX8B/wF/AWIBYAEgAVwBgwFgAYMBYAGDAWABgwFgAYMBYAEAAVgBDwFuAf8BfwGcAXMYAAG9AXcB/wF/
        AW4BXwHgAT4BAwFHAQMBRwEDAUcBAwFHAeABPgFvAV8B/wF/AZwBcwwAAf8BfwH/AX8BdwF7AeABdQEA
        AXYBZgF2AakBegEAAXYB4AF1ATIBewH/AX8B3gF7PAABnAFzAf8BfwFBAWABQQFgAYMBYAGDAWABgwFg
        AYMBYAEgAWABgwFgAb0BewH/AX8B/wF/Ab0BewGDAWABIAFgAYMBYAGDAWABgwFgAYMBYAFBAWABQQFg
        Af8BfwGcAXMaAAG9AXcB/wF/AUgBUwEAAUMBAwFLAQMBSwEDAUsBAAFDAQABRwH/AX8BnAFzDgABvQF3
        Af8BfwGqAXoBAAF2ASABdgEAAXYBAAF2AWUBdgH/AX8BvQFzPgABvQF3Af8BfwHvAW0BAAFgAYMBZAGD
        AWQBgwFkASABYAGDAWQB3gF/Af8BfwHeAXsB3gF7Af8BfwHeAX8BgwFkASABYAGDAWQBgwFkAYMBZAEA
        AWABDwFuAf8BfwG9AXcaAAH/AX8B/wF/Af8BfwEiAUsBAAFHASIBSwEAAUcBAAFHAbYBbwH/AX8B3gF7
        DgAB/wF/Af8BfwH/AX8BIAF2ASABdgEgAXYBAAF2AbsBfwH/AX8B/wF/PgAB/wF/Ad4BewH/AX8BjAFt
        AQABZAFiAWQBIAFkAWIBZAHeAX8B/wF/Ad4BewQAAd4BewH/AX8BvQF/AWEBZAEgAWQBYgFkAQABZAGM
        AW0B/wF/Ad4BewH/AX8cAAHeAXsB/wF/AfwBewEiAU8BAAFDAUgBVwH9AXsB/wF/Ab0BdxIAAd4BewH/
        AX8BmQF/AUABdgEgAXYBUwF7Af8BfwG9AXdCAAH/AX8B3gF7Af8BfwHOAXEBAAFkAYQBaAG9AX8B/wF/
        Ad4BewgAAd4BewH/AX8BvQF/AaQBaAEAAWQBzgFxAf8BfwHeAXsB/wF/IAAB3gF7Af8BfwH/AX8B2wF3
        Af8BfwH/AX8B3gF7FgABvQF3Af8BfwH/AX8B/wF/Af8BfwG9AXdGAAH/AX8B3gF7Af8BfwG9AX8B/wF/
        Af8BfwHeAXsMAAHeAXsB/wF/Af8BfwG9AX8B/wF/Ad4BewH/AX8kAAHeAXsB3gF7Af8BfwG9AXccAAHe
        AXsBvQF3Ab0BdwHeAXtMAAG9AXcBvQF3Ab0BdwHeAXsQAAHeAXsBvQF3Ab0BdwG9AXcqAAH/AX8B/wF/
        Af8Bf2gAAUIBTQE+BwABPgMAASgDAAFgAwABGAMAAQEBAAEBBQABIAEBFgAD/wEAAeAB/wEHAf8BjwT/
        AwABwAF+AQMB/wGHAf8BwAEAAQMDAAGAATwBAQH+AQMB/wGAAQABAQQAARgBAAH8AQEB/wkAAfgBAQH/
        CQAB8AEAAf8JAAHgAQAB/wkAAcABAAF/BgABgAEAAQEBgAEAAT8BgAEAAQEDAAHAAQABAwGAAQABPwGA
        AQABAQMAAeABAAEHAgABHwHAAQABAwMAAfABAAEPAgABDwHgAQABBwMAAfABAAEPAgABDwHgAQABBwMA
        AeABAAEHAgABBwHwAQABDwMAAcABAAEDAYABAAEDAfABAAEPAwABgAEAAQEBwAFAAQEB+AEAAR8GAALg
        AQAB/AEAAR8GAAH/AfABAAH8AQABPwYAAf8B+AEAAf4BAAF/BgAB/wH4AQAB/gEAAX8EAAEYAQAB/wH8
        AQEB/wEAAf8DAAGAATwBAQH/Af4BAwH/AYEB/wMAAcABfgEDAv8BDwH/AcMB/wMAAfAB/wEPAv8BjwP/
        AwAL
</value>
  </data>
  <metadata name="ErrorManager.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
</root>