<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformCategoryManager
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformCategoryManager))
        Dim SuperToolTip1 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem1 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem1 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip2 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem2 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem2 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Me.ImageList16x16 = New System.Windows.Forms.ImageList(Me.components)
        Me.GroupControlItems = New DevExpress.XtraEditors.GroupControl()
        Me.LabelControl11 = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearch = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearch = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearch = New DevExpress.XtraEditors.PictureEdit()
        Me.ButtonDelete = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAdd = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonEdit = New DevExpress.XtraEditors.SimpleButton()
        Me.GridItems = New System.Windows.Forms.DataGridView()
        Me.CategoryNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DormantColumn = New System.Windows.Forms.DataGridViewCheckBoxColumn()
        Me.CrossoverAllowed = New System.Windows.Forms.DataGridViewCheckBoxColumn()
        CType(Me.GroupControlItems, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlItems.SuspendLayout()
        CType(Me.TextEditSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridItems, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Margin = New System.Windows.Forms.Padding(5, 5, 5, 21)
        Me.LabelTitle.Size = New System.Drawing.Size(283, 37)
        Me.LabelTitle.Text = "Category Manager"
        '
        'ImageList16x16
        '
        Me.ImageList16x16.ImageStream = CType(resources.GetObject("ImageList16x16.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList16x16.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList16x16.Images.SetKeyName(0, "add.png")
        Me.ImageList16x16.Images.SetKeyName(1, "pencil.png")
        Me.ImageList16x16.Images.SetKeyName(2, "delete.png")
        '
        'GroupControlItems
        '
        Me.GroupControlItems.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlItems.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlItems.Appearance.Options.UseFont = True
        Me.GroupControlItems.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlItems.AppearanceCaption.Options.UseFont = True
        Me.GroupControlItems.Controls.Add(Me.LabelControl11)
        Me.GroupControlItems.Controls.Add(Me.TextEditSearch)
        Me.GroupControlItems.Controls.Add(Me.PictureClearSearch)
        Me.GroupControlItems.Controls.Add(Me.PictureAdvancedSearch)
        Me.GroupControlItems.Controls.Add(Me.ButtonDelete)
        Me.GroupControlItems.Controls.Add(Me.ButtonAdd)
        Me.GroupControlItems.Controls.Add(Me.ButtonEdit)
        Me.GroupControlItems.Controls.Add(Me.GridItems)
        Me.GroupControlItems.Location = New System.Drawing.Point(15, 75)
        Me.GroupControlItems.LookAndFeel.SkinName = "Black"
        Me.GroupControlItems.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlItems.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.GroupControlItems.Name = "GroupControlItems"
        Me.GroupControlItems.Size = New System.Drawing.Size(1025, 620)
        Me.GroupControlItems.TabIndex = 26
        Me.GroupControlItems.Text = "Category List"
        '
        'LabelControl11
        '
        Me.LabelControl11.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl11.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl11.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl11.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl11.Location = New System.Drawing.Point(822, 588)
        Me.LabelControl11.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.LabelControl11.Name = "LabelControl11"
        Me.LabelControl11.Size = New System.Drawing.Size(58, 17)
        Me.LabelControl11.TabIndex = 5
        Me.LabelControl11.Text = "Search:"
        '
        'TextEditSearch
        '
        Me.TextEditSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearch.EditValue = ""
        Me.TextEditSearch.Location = New System.Drawing.Point(887, 585)
        Me.TextEditSearch.Margin = New System.Windows.Forms.Padding(4, 5, 4, 7)
        Me.TextEditSearch.Name = "TextEditSearch"
        Me.TextEditSearch.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearch.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearch.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearch.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearch.Size = New System.Drawing.Size(103, 24)
        Me.TextEditSearch.TabIndex = 6
        '
        'PictureClearSearch
        '
        Me.PictureClearSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearch.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearch.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearch.Location = New System.Drawing.Point(998, 4)
        Me.PictureClearSearch.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.PictureClearSearch.Name = "PictureClearSearch"
        Me.PictureClearSearch.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearch.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearch.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearch.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem1.Text = "Clear Search"
        ToolTipItem1.LeftIndent = 6
        ToolTipItem1.Text = "Click here to clear all search boxes."
        SuperToolTip1.Items.Add(ToolTipTitleItem1)
        SuperToolTip1.Items.Add(ToolTipItem1)
        Me.PictureClearSearch.SuperTip = SuperToolTip1
        Me.PictureClearSearch.TabIndex = 0
        Me.PictureClearSearch.TabStop = True
        '
        'PictureAdvancedSearch
        '
        Me.PictureAdvancedSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearch.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearch.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearch.Location = New System.Drawing.Point(998, 587)
        Me.PictureAdvancedSearch.Margin = New System.Windows.Forms.Padding(4, 8, 4, 9)
        Me.PictureAdvancedSearch.Name = "PictureAdvancedSearch"
        Me.PictureAdvancedSearch.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearch.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearch.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearch.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem2.Text = "Advanced Search"
        ToolTipItem2.LeftIndent = 6
        ToolTipItem2.Text = "Click here to search individual column values."
        SuperToolTip2.Items.Add(ToolTipTitleItem2)
        SuperToolTip2.Items.Add(ToolTipItem2)
        Me.PictureAdvancedSearch.SuperTip = SuperToolTip2
        Me.PictureAdvancedSearch.TabIndex = 7
        Me.PictureAdvancedSearch.TabStop = True
        '
        'ButtonDelete
        '
        Me.ButtonDelete.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonDelete.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonDelete.Appearance.Options.UseFont = True
        Me.ButtonDelete.ImageIndex = 2
        Me.ButtonDelete.ImageList = Me.ImageList16x16
        Me.ButtonDelete.Location = New System.Drawing.Point(215, 583)
        Me.ButtonDelete.LookAndFeel.SkinName = "Black"
        Me.ButtonDelete.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonDelete.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.ButtonDelete.Name = "ButtonDelete"
        Me.ButtonDelete.Size = New System.Drawing.Size(96, 30)
        Me.ButtonDelete.TabIndex = 4
        Me.ButtonDelete.Text = "Delete"
        '
        'ButtonAdd
        '
        Me.ButtonAdd.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAdd.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAdd.Appearance.Options.UseFont = True
        Me.ButtonAdd.ImageIndex = 0
        Me.ButtonAdd.ImageList = Me.ImageList16x16
        Me.ButtonAdd.Location = New System.Drawing.Point(6, 583)
        Me.ButtonAdd.LookAndFeel.SkinName = "Black"
        Me.ButtonAdd.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAdd.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.ButtonAdd.Name = "ButtonAdd"
        Me.ButtonAdd.Size = New System.Drawing.Size(96, 30)
        Me.ButtonAdd.TabIndex = 2
        Me.ButtonAdd.Text = "Add"
        '
        'ButtonEdit
        '
        Me.ButtonEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonEdit.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonEdit.Appearance.Options.UseFont = True
        Me.ButtonEdit.ImageIndex = 1
        Me.ButtonEdit.ImageList = Me.ImageList16x16
        Me.ButtonEdit.Location = New System.Drawing.Point(111, 583)
        Me.ButtonEdit.LookAndFeel.SkinName = "Black"
        Me.ButtonEdit.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonEdit.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.ButtonEdit.Name = "ButtonEdit"
        Me.ButtonEdit.Size = New System.Drawing.Size(96, 30)
        Me.ButtonEdit.TabIndex = 3
        Me.ButtonEdit.Text = "Edit"
        '
        'GridItems
        '
        Me.GridItems.AllowUserToAddRows = False
        Me.GridItems.AllowUserToDeleteRows = False
        Me.GridItems.AllowUserToOrderColumns = True
        Me.GridItems.AllowUserToResizeRows = False
        DataGridViewCellStyle1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridItems.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle1
        Me.GridItems.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridItems.BackgroundColor = System.Drawing.Color.White
        Me.GridItems.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridItems.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridItems.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle2.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle2.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridItems.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle2
        Me.GridItems.ColumnHeadersHeight = 22
        Me.GridItems.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridItems.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.CategoryNameColumn, Me.DormantColumn, Me.CrossoverAllowed})
        Me.GridItems.EnableHeadersVisualStyles = False
        Me.GridItems.GridColor = System.Drawing.Color.White
        Me.GridItems.Location = New System.Drawing.Point(3, 29)
        Me.GridItems.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.GridItems.Name = "GridItems"
        Me.GridItems.ReadOnly = True
        Me.GridItems.RowHeadersVisible = False
        Me.GridItems.RowHeadersWidth = 51
        DataGridViewCellStyle3.ForeColor = System.Drawing.Color.DimGray
        Me.GridItems.RowsDefaultCellStyle = DataGridViewCellStyle3
        Me.GridItems.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridItems.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridItems.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridItems.RowTemplate.Height = 19
        Me.GridItems.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridItems.ShowCellToolTips = False
        Me.GridItems.Size = New System.Drawing.Size(1020, 547)
        Me.GridItems.StandardTab = True
        Me.GridItems.TabIndex = 1
        '
        'CategoryNameColumn
        '
        Me.CategoryNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.CategoryNameColumn.DataPropertyName = "CategoryName"
        Me.CategoryNameColumn.HeaderText = "Category"
        Me.CategoryNameColumn.MinimumWidth = 6
        Me.CategoryNameColumn.Name = "CategoryNameColumn"
        Me.CategoryNameColumn.ReadOnly = True
        '
        'DormantColumn
        '
        Me.DormantColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None
        Me.DormantColumn.DataPropertyName = "Dormant"
        Me.DormantColumn.HeaderText = "Dormant"
        Me.DormantColumn.MinimumWidth = 6
        Me.DormantColumn.Name = "DormantColumn"
        Me.DormantColumn.ReadOnly = True
        Me.DormantColumn.Resizable = System.Windows.Forms.DataGridViewTriState.[True]
        Me.DormantColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Automatic
        Me.DormantColumn.Width = 125
        '
        'CrossoverAllowed
        '
        Me.CrossoverAllowed.DataPropertyName = "CrossoverAllowed"
        Me.CrossoverAllowed.HeaderText = "Crossover Allowed"
        Me.CrossoverAllowed.MinimumWidth = 6
        Me.CrossoverAllowed.Name = "CrossoverAllowed"
        Me.CrossoverAllowed.ReadOnly = True
        Me.CrossoverAllowed.Resizable = System.Windows.Forms.DataGridViewTriState.[True]
        Me.CrossoverAllowed.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Automatic
        Me.CrossoverAllowed.Width = 125
        '
        'SubformCategoryManager
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(9.0!, 17.0!)
        Me.Controls.Add(Me.GroupControlItems)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(5, 5, 5, 5)
        Me.Name = "SubformCategoryManager"
        Me.Padding = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        Me.Controls.SetChildIndex(Me.GroupControlItems, 0)
        CType(Me.GroupControlItems, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlItems.ResumeLayout(False)
        CType(Me.TextEditSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridItems, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents ImageList16x16 As System.Windows.Forms.ImageList
    Friend WithEvents GroupControlItems As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelControl11 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditSearch As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearch As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearch As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents ButtonDelete As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonAdd As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonEdit As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GridItems As System.Windows.Forms.DataGridView
    Friend WithEvents CategoryNameColumn As DataGridViewTextBoxColumn
    Friend WithEvents DormantColumn As DataGridViewCheckBoxColumn
    Friend WithEvents CrossoverAllowed As DataGridViewCheckBoxColumn
End Class
