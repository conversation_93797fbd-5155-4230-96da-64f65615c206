<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DataSetBrand" targetNamespace="http://tempuri.org/DataSetBrand.xsd" xmlns:mstns="http://tempuri.org/DataSetBrand.xsd" xmlns="http://tempuri.org/DataSetBrand.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="DBConnection" ConnectionStringObject="" IsAppSettingsProperty="True" Modifier="Public" Name="DBConnection (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.NovaData.My.MySettings.GlobalReference.Default.DBConnection" Provider="System.Data.SqlClient">
          </Connection>
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="BrandTableAdapter" GeneratorDataComponentClassName="BrandTableAdapter" Name="Brand" UserDataComponentName="BrandTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Client.Brand" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [Client].[Brand] WHERE (([BrandID] = @Original_BrandID) AND ([BrandName] = @Original_BrandName) AND ([Dormant] = @Original_Dormant))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BrandID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_BrandName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="BrandName" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Dormant" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [Client].[Brand] ([BrandID], [BrandName], [Dormant]) VALUES (@BrandID, @BrandName, @Dormant);
SELECT BrandID, BrandName, Dormant FROM Client.Brand WHERE (BrandID = @BrandID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BrandID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@BrandName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="BrandName" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Dormant" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>SELECT        Client.Brand.BrandID, Client.Brand.BrandName, Client.Brand.Dormant
FROM            Client.Brand INNER JOIN
                         Client.vBrandsByPermission_EditMyClients ON Client.Brand.BrandID = Client.vBrandsByPermission_EditMyClients.BrandID</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [Client].[Brand] SET [BrandID] = @BrandID, [BrandName] = @BrandName, [Dormant] = @Dormant WHERE (([BrandID] = @Original_BrandID) AND ([BrandName] = @Original_BrandName) AND ([Dormant] = @Original_Dormant));
SELECT BrandID, BrandName, Dormant FROM Client.Brand WHERE (BrandID = @BrandID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BrandID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@BrandName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="BrandName" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Dormant" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BrandID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_BrandName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="BrandName" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Dormant" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="BrandID" DataSetColumn="BrandID" />
              <Mapping SourceColumn="BrandName" DataSetColumn="BrandName" />
              <Mapping SourceColumn="Dormant" DataSetColumn="Dormant" />
            </Mappings>
            <Sources>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ClientBrandTableAdapter" GeneratorDataComponentClassName="ClientBrandTableAdapter" Name="ClientBrand" UserDataComponentName="ClientBrandTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Client.ClientBrand" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>DELETE FROM [Client].[ClientBrand] WHERE (([ClientID] = @Original_ClientID) AND ([BrandID] = @Original_BrandID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ClientID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BrandID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>INSERT INTO [Client].[ClientBrand] ([ClientID], [BrandID]) VALUES (@ClientID, @BrandID);
SELECT ClientID, BrandID FROM Client.ClientBrand WHERE (BrandID = @BrandID) AND (ClientID = @ClientID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ClientID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BrandID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>SELECT        ClientID, BrandID
FROM            Client.ClientBrand
WHERE        (BrandID = @BrandID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="BrandID" ColumnName="BrandID" DataSourceName="NovaDB.Client.ClientBrand" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="BrandID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [Client].[ClientBrand] SET [ClientID] = @ClientID, [BrandID] = @BrandID WHERE (([ClientID] = @Original_ClientID) AND ([BrandID] = @Original_BrandID));
SELECT ClientID, BrandID FROM Client.ClientBrand WHERE (BrandID = @BrandID) AND (ClientID = @ClientID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ClientID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BrandID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ClientID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BrandID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ClientID" DataSetColumn="ClientID" />
              <Mapping SourceColumn="BrandID" DataSetColumn="BrandID" />
            </Mappings>
            <Sources>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ClientTableAdapter" GeneratorDataComponentClassName="ClientTableAdapter" Name="Client" UserDataComponentName="ClientTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Client.Client" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="False" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT        ClientID, ClientName
FROM            Client.Client</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ClientID" DataSetColumn="ClientID" />
              <Mapping SourceColumn="ClientName" DataSetColumn="ClientName" />
            </Mappings>
            <Sources>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="BurstCountByBrandTableAdapter" GeneratorDataComponentClassName="BurstCountByBrandTableAdapter" Name="BurstCountByBrand" UserDataComponentName="BurstCountByBrandTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Sales.Burst" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="False" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>SELECT        BrandID, COUNT(BurstID) AS BurstCount
FROM            Sales.Burst
GROUP BY BrandID</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="BrandID" DataSetColumn="BrandID" />
              <Mapping SourceColumn="BurstCount" DataSetColumn="BurstCount" />
            </Mappings>
            <Sources>
            </Sources>
          </TableAdapter>
        </Tables>
        <Sources>
        </Sources>
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DataSetBrand" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="DataSetBrand" msprop:Generator_DataSetName="DataSetBrand">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="Brand" msprop:Generator_UserTableName="Brand" msprop:Generator_RowDeletedName="BrandRowDeleted" msprop:Generator_RowChangedName="BrandRowChanged" msprop:Generator_RowClassName="BrandRow" msprop:Generator_RowChangingName="BrandRowChanging" msprop:Generator_RowEvArgName="BrandRowChangeEvent" msprop:Generator_RowEvHandlerName="BrandRowChangeEventHandler" msprop:Generator_TableClassName="BrandDataTable" msprop:Generator_TableVarName="tableBrand" msprop:Generator_RowDeletingName="BrandRowDeleting" msprop:Generator_TablePropName="Brand">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="BrandID" msdata:DataType="System.Guid, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_UserColumnName="BrandID" msprop:Generator_ColumnVarNameInTable="columnBrandID" msprop:Generator_ColumnPropNameInRow="BrandID" msprop:Generator_ColumnPropNameInTable="BrandIDColumn" type="xs:string" />
              <xs:element name="BrandName" msprop:Generator_UserColumnName="BrandName" msprop:Generator_ColumnVarNameInTable="columnBrandName" msprop:Generator_ColumnPropNameInRow="BrandName" msprop:Generator_ColumnPropNameInTable="BrandNameColumn" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Dormant" msprop:Generator_UserColumnName="Dormant" msprop:Generator_ColumnPropNameInRow="Dormant" msprop:Generator_ColumnVarNameInTable="columnDormant" msprop:Generator_ColumnPropNameInTable="DormantColumn" type="xs:boolean" default="false" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ClientBrand" msprop:Generator_UserTableName="ClientBrand" msprop:Generator_RowDeletedName="ClientBrandRowDeleted" msprop:Generator_RowChangedName="ClientBrandRowChanged" msprop:Generator_RowClassName="ClientBrandRow" msprop:Generator_RowChangingName="ClientBrandRowChanging" msprop:Generator_RowEvArgName="ClientBrandRowChangeEvent" msprop:Generator_RowEvHandlerName="ClientBrandRowChangeEventHandler" msprop:Generator_TableClassName="ClientBrandDataTable" msprop:Generator_TableVarName="tableClientBrand" msprop:Generator_RowDeletingName="ClientBrandRowDeleting" msprop:Generator_TablePropName="ClientBrand">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ClientID" msprop:Generator_UserColumnName="ClientID" msprop:Generator_ColumnVarNameInTable="columnClientID" msprop:Generator_ColumnPropNameInRow="ClientID" msprop:Generator_ColumnPropNameInTable="ClientIDColumn" type="xs:int" />
              <xs:element name="BrandID" msdata:DataType="System.Guid, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_UserColumnName="BrandID" msprop:Generator_ColumnVarNameInTable="columnBrandID" msprop:Generator_ColumnPropNameInRow="BrandID" msprop:Generator_ColumnPropNameInTable="BrandIDColumn" type="xs:string" />
              <xs:element name="ClientName" msdata:ReadOnly="true" msdata:Expression="Parent(FK_ClientBrand_Client).ClientName" msprop:Generator_UserColumnName="ClientName" msprop:Generator_ColumnVarNameInTable="columnClientName" msprop:Generator_ColumnPropNameInRow="ClientName" msprop:Generator_ColumnPropNameInTable="ClientNameColumn" type="xs:string" default="" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Client" msprop:Generator_UserTableName="Client" msprop:Generator_RowDeletedName="ClientRowDeleted" msprop:Generator_RowChangedName="ClientRowChanged" msprop:Generator_RowClassName="ClientRow" msprop:Generator_RowChangingName="ClientRowChanging" msprop:Generator_RowEvArgName="ClientRowChangeEvent" msprop:Generator_RowEvHandlerName="ClientRowChangeEventHandler" msprop:Generator_TableClassName="ClientDataTable" msprop:Generator_TableVarName="tableClient" msprop:Generator_RowDeletingName="ClientRowDeleting" msprop:Generator_TablePropName="Client">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ClientID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msprop:Generator_UserColumnName="ClientID" msprop:Generator_ColumnVarNameInTable="columnClientID" msprop:Generator_ColumnPropNameInRow="ClientID" msprop:Generator_ColumnPropNameInTable="ClientIDColumn" type="xs:int" />
              <xs:element name="ClientName" msprop:Generator_UserColumnName="ClientName" msprop:Generator_ColumnVarNameInTable="columnClientName" msprop:Generator_ColumnPropNameInRow="ClientName" msprop:Generator_ColumnPropNameInTable="ClientNameColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="BurstCountByBrand" msprop:Generator_UserTableName="BurstCountByBrand" msprop:Generator_RowDeletedName="BurstCountByBrandRowDeleted" msprop:Generator_RowChangedName="BurstCountByBrandRowChanged" msprop:Generator_RowClassName="BurstCountByBrandRow" msprop:Generator_RowChangingName="BurstCountByBrandRowChanging" msprop:Generator_RowEvArgName="BurstCountByBrandRowChangeEvent" msprop:Generator_RowEvHandlerName="BurstCountByBrandRowChangeEventHandler" msprop:Generator_TableClassName="BurstCountByBrandDataTable" msprop:Generator_TableVarName="tableBurstCountByBrand" msprop:Generator_RowDeletingName="BurstCountByBrandRowDeleting" msprop:Generator_TablePropName="BurstCountByBrand">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="BrandID" msdata:DataType="System.Guid, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_UserColumnName="BrandID" msprop:Generator_ColumnVarNameInTable="columnBrandID" msprop:Generator_ColumnPropNameInRow="BrandID" msprop:Generator_ColumnPropNameInTable="BrandIDColumn" type="xs:string" />
              <xs:element name="BurstCount" msdata:ReadOnly="true" msprop:Generator_UserColumnName="BurstCount" msprop:Generator_ColumnVarNameInTable="columnBurstCount" msprop:Generator_ColumnPropNameInRow="BurstCount" msprop:Generator_ColumnPropNameInTable="BurstCountColumn" type="xs:int" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Brand" />
      <xs:field xpath="mstns:BrandID" />
    </xs:unique>
    <xs:unique name="ClientBrand_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ClientBrand" />
      <xs:field xpath="mstns:ClientID" />
      <xs:field xpath="mstns:BrandID" />
    </xs:unique>
    <xs:unique name="Client_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Client" />
      <xs:field xpath="mstns:ClientID" />
    </xs:unique>
  </xs:element>
  <xs:annotation>
    <xs:appinfo>
      <msdata:Relationship name="Brand_ClientBrand" msdata:parent="Brand" msdata:child="ClientBrand" msdata:parentkey="BrandID" msdata:childkey="BrandID" msprop:Generator_UserRelationName="Brand_ClientBrand" msprop:Generator_RelationVarName="relationBrand_ClientBrand" msprop:Generator_UserChildTable="ClientBrand" msprop:Generator_UserParentTable="Brand" msprop:Generator_ParentPropName="BrandRow" msprop:Generator_ChildPropName="GetClientBrandRows" />
      <msdata:Relationship name="FK_ClientBrand_Client" msdata:parent="Client" msdata:child="ClientBrand" msdata:parentkey="ClientID" msdata:childkey="ClientID" msprop:Generator_UserRelationName="FK_ClientBrand_Client" msprop:Generator_RelationVarName="relationFK_ClientBrand_Client" msprop:Generator_UserChildTable="ClientBrand" msprop:Generator_UserParentTable="Client" msprop:Generator_ParentPropName="ClientRow" msprop:Generator_ChildPropName="GetClientBrandRows" />
      <msdata:Relationship name="Brand_BurstCount" msdata:parent="Brand" msdata:child="BurstCountByBrand" msdata:parentkey="BrandID" msdata:childkey="BrandID" msprop:Generator_UserRelationName="Brand_BurstCount" msprop:Generator_RelationVarName="relationBrand_BurstCount" msprop:Generator_UserChildTable="BurstCountByBrand" msprop:Generator_UserParentTable="Brand" msprop:Generator_ParentPropName="BrandRow" msprop:Generator_ChildPropName="GetBurstCountByBrandRows" />
    </xs:appinfo>
  </xs:annotation>
</xs:schema>