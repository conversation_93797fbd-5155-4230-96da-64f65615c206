﻿using DataAccess;
using System;
using System.Collections.Generic;
using System.Data;

namespace DataService.Security
{
    class AddRoleOwnersCommand : Command
    {
        public Guid SessionId { get; set; }
        public Guid RoleId { get; set; }
        public DataTable NewOwners { get; set; }

        public AddRoleOwnersCommand(Guid sessionid, Guid roleid, List<DataRow> newownerslist)
        {
            SessionId = sessionid;
            RoleId = roleid;

            // Create a new table.
            NewOwners = new DataTable();
            NewOwners.Columns.Add("id", typeof(Guid));

            // Convert the rows in the specified list to rows that match the schema of the table and then add them to the table.
            if (newownerslist != null && newownerslist.Count > 0)
            {
                for (int i = 0; i < newownerslist.Count; i++)
                {
                    DataRow newrow = NewOwners.NewRow();
                    newrow["id"] = newownerslist[i]["userid"];
                    NewOwners.Rows.Add(newrow);
                }
            }
        }
    }
}
