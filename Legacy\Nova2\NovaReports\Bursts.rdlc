<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DBConnection">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString>Data Source=sqlserv_3;Initial Catalog=NovaDB;User ID=alan</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>e78db224-9858-4187-ab39-8432d1757933</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="DataSetContractReport_Burst">
      <Query>
        <DataSourceName>DBConnection</DataSourceName>
        <CommandText>SELECT        Sales.Burst.BurstID, Sales.Burst.ContractID, Sales.Burst.ChainID, Sales.Burst.ChainName, Sales.Burst.StorePoolID, Sales.Burst.MediaID, Sales.Burst.MediaName, 
                         Sales.Burst.BrandID, Sales.Burst.BrandName, Sales.Burst.ProductName, Sales.Burst.FirstWeek, DATEADD(wk, Sales.Burst.InstallWeeks - 1, Sales.Burst.FirstWeek) 
                         AS LastWeek, Sales.Burst.InstallWeeks, Sales.Burst.InstallStoreQty, Sales.Burst.BillableStoreQty, Sales.Burst.RentalRate, Sales.Burst.BillableWeeks, 
                         Sales.Burst.Discount, Sales.Burst.CrossoverQty, Sales.Burst.InstallAtHomesite, Sales.Burst.InstallationInstructions, Sales.Burst.StoreListConfirmed, 
                         Sales.Burst.CreatedBy, Sales.Burst.CreationDate, ISNULL(dtLoadingPercentage.LoadingPercentage, 0) AS LoadingPercentage, 
                         ISNULL(dtLoadingAmount.LoadingAmount, 0) AS LoadingAmount, dtFreeStoreTotal.TotalFreeStores, ISNULL(dtHomesite.Homesite, N'no homesite selected') 
                         AS Homesite, dbo.udfCategoryListByBurst(Sales.Burst.BurstID) AS Categories
FROM            Sales.Burst INNER JOIN 
                             (SELECT        ContractID, SUM(InstallStoreQty - BillableStoreQty) AS TotalFreeStores
                               FROM            Sales.Burst AS Burst_2
                               GROUP BY ContractID) AS dtFreeStoreTotal ON Sales.Burst.ContractID = dtFreeStoreTotal.ContractID LEFT OUTER JOIN
                             (SELECT        Sales.BurstCategory.BurstID, Store.Category.CategoryName AS Homesite
                               FROM            Sales.BurstCategory INNER JOIN
                                                         Store.Category ON Sales.BurstCategory.CategoryID = Store.Category.CategoryID
                               WHERE        (Sales.BurstCategory.Priority = 0)) AS dtHomesite ON Sales.Burst.BurstID = dtHomesite.BurstID LEFT OUTER JOIN
                             (SELECT        Burst_1.BurstID, 
                                                         BurstLoadingFee_1.Percentage / 100 * Burst_1.RentalRate * Burst_1.BillableStoreQty * Burst_1.BillableWeeks AS LoadingAmount
                               FROM            Sales.BurstLoadingFee AS BurstLoadingFee_1 INNER JOIN
                                                         Sales.Burst AS Burst_1 ON BurstLoadingFee_1.BurstID = Burst_1.BurstID) AS dtLoadingAmount ON 
                         Sales.Burst.BurstID = dtLoadingAmount.BurstID LEFT OUTER JOIN
                             (SELECT        BurstID, SUM(Percentage) AS LoadingPercentage
                               FROM            Sales.BurstLoadingFee
                               GROUP BY BurstID) AS dtLoadingPercentage ON Sales.Burst.BurstID = dtLoadingPercentage.BurstID
WHERE        (Sales.Burst.ContractID = @ContractID)
ORDER BY Sales.Burst.MediaName, Sales.Burst.BrandName, Sales.Burst.ChainName</CommandText>
        <rd:UseGenericDesigner>true</rd:UseGenericDesigner>
      </Query>
      <Fields>
        <Field Name="BurstID">
          <DataField>BurstID</DataField>
          <rd:TypeName>System.Guid</rd:TypeName>
        </Field>
        <Field Name="ContractID">
          <DataField>ContractID</DataField>
          <rd:TypeName>System.Guid</rd:TypeName>
        </Field>
        <Field Name="ChainID">
          <DataField>ChainID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="ChainName">
          <DataField>ChainName</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="StorePoolID">
          <DataField>StorePoolID</DataField>
          <rd:TypeName>System.Guid</rd:TypeName>
        </Field>
        <Field Name="MediaID">
          <DataField>MediaID</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="MediaName">
          <DataField>MediaName</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="BrandID">
          <DataField>BrandID</DataField>
          <rd:TypeName>System.Guid</rd:TypeName>
        </Field>
        <Field Name="BrandName">
          <DataField>BrandName</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="ProductName">
          <DataField>ProductName</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FirstWeek">
          <DataField>FirstWeek</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="InstallWeeks">
          <DataField>InstallWeeks</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="InstallStoreQty">
          <DataField>InstallStoreQty</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="BillableStoreQty">
          <DataField>BillableStoreQty</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="RentalRate">
          <DataField>RentalRate</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="BillableWeeks">
          <DataField>BillableWeeks</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="Discount">
          <DataField>Discount</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="CrossoverQty">
          <DataField>CrossoverQty</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="InstallAtHomesite">
          <DataField>InstallAtHomesite</DataField>
          <rd:TypeName>System.Boolean</rd:TypeName>
        </Field>
        <Field Name="InstallationInstructions">
          <DataField>InstallationInstructions</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="StoreListConfirmed">
          <DataField>StoreListConfirmed</DataField>
          <rd:TypeName>System.Boolean</rd:TypeName>
        </Field>
        <Field Name="CreatedBy">
          <DataField>CreatedBy</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="CreationDate">
          <DataField>CreationDate</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="LastWeek">
          <DataField>LastWeek</DataField>
          <rd:TypeName>System.DateTime</rd:TypeName>
        </Field>
        <Field Name="LoadingPercentage">
          <DataField>LoadingPercentage</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="LoadingAmount">
          <DataField>LoadingAmount</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="TotalFreeStores">
          <DataField>TotalFreeStores</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="Homesite">
          <DataField>Homesite</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="GroupDefinition">
          <DataField>GroupDefinition</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="DiscountAmount">
          <DataField>DiscountAmount</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="TotalFreeWeeks">
          <DataField>TotalFreeWeeks</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>DataSetContractReport</rd:DataSetName>
        <rd:SchemaPath>D:\Documents\Projects\Visual Studio\Nova2\NovaReports\DataSetContractReport.xsd</rd:SchemaPath>
        <rd:TableName>Burst</rd:TableName>
        <rd:TableAdapterFillMethod>Fill</rd:TableAdapterFillMethod>
        <rd:TableAdapterGetDataMethod>GetData</rd:TableAdapterGetDataMethod>
        <rd:TableAdapterName>BurstTableAdapter</rd:TableAdapterName>
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
 
      <Body>
        <ReportItems>
          <Tablix Name="Table_Bursts">
            <TablixBody>
              <TablixColumns>
                <TablixColumn>
                  <Width>3.79999cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.9cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.9cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.9cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.9cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.9cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>0.9cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>1.8cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2cm</Width>
                </TablixColumn>
                <TablixColumn>
                  <Width>2cm</Width>
                </TablixColumn>
              </TablixColumns>
              <TablixRows>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox2">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>BURST DETAILS</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>5pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>=iif(Parameters!ReportType.Value = "Logo", "Black", iif(Parameters!ReportType.Value = "PNP", "White", "Black"))</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox2</rd:DefaultName>
                          <ZIndex>67</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <BackgroundColor>=iif(Parameters!ReportType.Value = "Logo", "LightGrey", iif(Parameters!ReportType.Value = "PNP", "#133459", "LightGrey"))</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Label_PayWeeks">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>= IIf(Fields!TotalFreeWeeks.Value = 0, "", "PAID WEEKS")</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>5pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>=iif(Parameters!ReportType.Value = "Logo", "Black", iif(Parameters!ReportType.Value = "PNP", "White", "Black"))</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>64</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Gray</Color>
                            </LeftBorder>
                            <BackgroundColor>=iif(Parameters!ReportType.Value = "Logo", "LightGrey", iif(Parameters!ReportType.Value = "PNP", "#133459", "LightGrey"))</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Label_FreeWeeks">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>= IIf(Fields!TotalFreeWeeks.Value = 0, "TOTAL WEEKS", "FREE WEEKS")</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>5pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>=iif(Parameters!ReportType.Value = "Logo", "Black", iif(Parameters!ReportType.Value = "PNP", "White", "Black"))</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>63</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <BackgroundColor>=iif(Parameters!ReportType.Value = "Logo", "LightGrey", iif(Parameters!ReportType.Value = "PNP", "#133459", "LightGrey"))</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Label_TotalWeeks">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>= IIf(Fields!TotalFreeWeeks.Value = 0, "", "TOTAL WEEKS")</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>5pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>=iif(Parameters!ReportType.Value = "Logo", "Black", iif(Parameters!ReportType.Value = "PNP", "White", "Black"))</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>62</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <BackgroundColor>=iif(Parameters!ReportType.Value = "Logo", "LightGrey", iif(Parameters!ReportType.Value = "PNP", "#133459", "LightGrey"))</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Label_PayStores">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>= IIf(Fields!TotalFreeStores.Value = 0, "", "PAID STORES")</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>5pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>=iif(Parameters!ReportType.Value = "Logo", "Black", iif(Parameters!ReportType.Value = "PNP", "White", "Black"))</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>61</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Gray</Color>
                            </LeftBorder>
                            <BackgroundColor>=iif(Parameters!ReportType.Value = "Logo", "LightGrey", iif(Parameters!ReportType.Value = "PNP", "#133459", "LightGrey"))</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Label_FreeStores">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>= IIf(Fields!TotalFreeStores.Value = 0, "TOTAL STORES", "BUFFER STORES")</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>5pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>=iif(Parameters!ReportType.Value = "Logo", "Black", iif(Parameters!ReportType.Value = "PNP", "White", "Black"))</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>60</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <BackgroundColor>=iif(Parameters!ReportType.Value = "Logo", "LightGrey", iif(Parameters!ReportType.Value = "PNP", "#133459", "LightGrey"))</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Label_TotalStores">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>= IIf(Fields!TotalFreeStores.Value = 0, "", "TOTAL STORES")</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>5pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>=iif(Parameters!ReportType.Value = "Logo", "Black", iif(Parameters!ReportType.Value = "PNP", "White", "Black"))</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>59</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <RightBorder>
                              <Color>Gray</Color>
                            </RightBorder>
                            <BackgroundColor>=iif(Parameters!ReportType.Value = "Logo", "LightGrey", iif(Parameters!ReportType.Value = "PNP", "#133459", "LightGrey"))</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Label_FreeWeeksValue">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>VALUE OF</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>5pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>=iif(Parameters!ReportType.Value = "Logo", "Black", iif(Parameters!ReportType.Value = "PNP", "White", "Black"))</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>FREE TIME</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>5pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>=iif(Parameters!ReportType.Value = "Logo", "Black", iif(Parameters!ReportType.Value = "PNP", "White", "Black"))</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>58</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Gray</Color>
                              <Style>None</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Gray</Color>
                              <Style>None</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <BackgroundColor>=iif(Parameters!ReportType.Value = "Logo", "LightGrey", iif(Parameters!ReportType.Value = "PNP", "#133459", "LightGrey"))</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Label_Loading">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>LOADING FEE</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>5pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>=iif(Parameters!ReportType.Value = "Logo", "Black", iif(Parameters!ReportType.Value = "PNP", "White", "Black"))</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>57</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Gray</Color>
                            </LeftBorder>
                            <BackgroundColor>=iif(Parameters!ReportType.Value = "Logo", "LightGrey", iif(Parameters!ReportType.Value = "PNP", "#133459", "LightGrey"))</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox1">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>GROSS RENTAL</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>5pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>=iif(Parameters!ReportType.Value = "Logo", "Black", iif(Parameters!ReportType.Value = "PNP", "White", "Black"))</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox1</rd:DefaultName>
                          <ZIndex>56</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Gray</Color>
                            </LeftBorder>
                            <BackgroundColor>=iif(Parameters!ReportType.Value = "Logo", "LightGrey", iif(Parameters!ReportType.Value = "PNP", "#133459", "LightGrey"))</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox10">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>DISCOUNT</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>5pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>=iif(Parameters!ReportType.Value = "Logo", "Black", iif(Parameters!ReportType.Value = "PNP", "White", "Black"))</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox10</rd:DefaultName>
                          <ZIndex>55</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Gray</Color>
                            </LeftBorder>
                            <BackgroundColor>=iif(Parameters!ReportType.Value = "Logo", "LightGrey", iif(Parameters!ReportType.Value = "PNP", "#133459", "LightGrey"))</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Label_PeriodTotal">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>NET RENTAL</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>5pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                    <Color>=iif(Parameters!ReportType.Value = "Logo", "Black", iif(Parameters!ReportType.Value = "PNP", "White", "Black"))</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>54</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Color>Gray</Color>
                            </LeftBorder>
                            <BackgroundColor>=iif(Parameters!ReportType.Value = "Logo", "LightGrey", iif(Parameters!ReportType.Value = "PNP", "#133459", "LightGrey"))</BackgroundColor>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.6cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox23">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>="Brand: " &amp; Fields!BrandName.Value &amp; " - " &amp; Fields!Homesite.Value</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>6pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox23</rd:DefaultName>
                          <ZIndex>53</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>7</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox34">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontSize>6pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox34</rd:DefaultName>
                          <ZIndex>52</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <LeftBorder>
                              <Color>Gray</Color>
                              <Style>None</Style>
                              <Width>1pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Gray</Color>
                              <Style>None</Style>
                              <Width>1pt</Width>
                            </RightBorder>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox35">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontSize>6pt</FontSize>
                                    <FontWeight>Normal</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>#000000</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox35</rd:DefaultName>
                          <ZIndex>51</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox8">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontSize>6pt</FontSize>
                                    <FontWeight>Normal</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>#000000</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox8</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox33">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontSize>6pt</FontSize>
                                    <FontWeight>Normal</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>#000000</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox33</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox38">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontStyle>Normal</FontStyle>
                                    <FontSize>6pt</FontSize>
                                    <FontWeight>Normal</FontWeight>
                                    <TextDecoration>None</TextDecoration>
                                    <Color>#000000</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox38</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <VerticalAlign>Bottom</VerticalAlign>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.7cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="GroupDefinition">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>= "From " &amp; CDate(Fields!FirstWeek.Value).ToString("d MMMM yyyy") &amp; " to " &amp; DateAdd("d",7,CDate(Fields!LastWeek.Value)).ToString("d MMMM yyyy") &amp; ":  " &amp; Fields!GroupDefinition.Value</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>6pt</FontSize>
                                    <TextDecoration>Underline</TextDecoration>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>GroupDefinition</rd:DefaultName>
                          <ZIndex>47</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>None</Style>
                            </BottomBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                        <ColSpan>12</ColSpan>
                      </CellContents>
                    </TablixCell>
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                    <TablixCell />
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.3cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox37">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!ChainName.Value</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>6pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox37</rd:DefaultName>
                          <ZIndex>13</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox_PayWeeks">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!BillableWeeks.Value</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>6pt</FontSize>
                                    <Color>= IIf(Fields!TotalFreeWeeks.Value = 0, "White", "Black")</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>10</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <LeftBorder>
                              <Color>Gray</Color>
                              <Style>None</Style>
                              <Width>0.5pt</Width>
                            </LeftBorder>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox_FreeWeeks">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>= IIf(Fields!TotalFreeWeeks.Value = 0, 
ReportItems!Textbox_TotalWeeks.Value, 
Fields!InstallWeeks.Value-Fields!BillableWeeks.Value)</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>6pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>9</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox_TotalWeeks">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!InstallWeeks.Value</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>6pt</FontSize>
                                    <Color>= IIf(Fields!TotalFreeWeeks.Value = 0, "White", "Black")</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>8</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox_PayStores">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!BillableStoreQty.Value</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>6pt</FontSize>
                                    <Color>= IIf(Fields!TotalFreeStores.Value = 0, "White", "Black")</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>7</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <LeftBorder>
                              <Color>Gray</Color>
                              <Style>None</Style>
                              <Width>0.5pt</Width>
                            </LeftBorder>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox_FreeStores">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>= IIf(Fields!TotalFreeStores.Value = 0, 
ReportItems!Textbox_TotalStores.Value, 
Fields!InstallStoreQty.Value - Fields!BillableStoreQty.Value)</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>6pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>6</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox_TotalStores">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!InstallStoreQty.Value</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>6pt</FontSize>
                                    <Color>= IIf(Fields!TotalFreeStores.Value = 0, "White", "Black")</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>5</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <RightBorder>
                              <Width>0.5pt</Width>
                            </RightBorder>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox_FreeWeeksValue">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Left
(
	FormatCurrency((Fields!RentalRate.Value + (Fields!LoadingPercentage.Value / 100 * Fields!RentalRate.Value)) * Fields!InstallStoreQty.Value * Fields!InstallWeeks.Value - (Fields!RentalRate.Value + (Fields!LoadingPercentage.Value / 100 * Fields!RentalRate.Value)) * Fields!BillableStoreQty.Value * Fields!BillableWeeks.Value,2).Replace("R","R ").Replace("R  ","R "),
	Len
	(
		FormatCurrency((Fields!RentalRate.Value + (Fields!LoadingPercentage.Value / 100 * Fields!RentalRate.Value)) * Fields!InstallStoreQty.Value * Fields!InstallWeeks.Value - (Fields!RentalRate.Value + (Fields!LoadingPercentage.Value / 100 * Fields!RentalRate.Value)) * Fields!BillableStoreQty.Value * Fields!BillableWeeks.Value,2).Replace("R","R ").Replace("R  ","R ")
	)
	-3
)
+
Right
(
	FormatCurrency((Fields!RentalRate.Value + (Fields!LoadingPercentage.Value / 100 * Fields!RentalRate.Value)) * Fields!InstallStoreQty.Value * Fields!InstallWeeks.Value - (Fields!RentalRate.Value + (Fields!LoadingPercentage.Value / 100 * Fields!RentalRate.Value)) * Fields!BillableStoreQty.Value * Fields!BillableWeeks.Value,2).Replace("R","R ").Replace("R  ","R "),3
)
.Replace(",",".")</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>6pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>4</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <LeftBorder>
                              <Style>None</Style>
                              <Width>0.5pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                              <Width>0.5pt</Width>
                            </RightBorder>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox_LoadingFee">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Left
(
	FormatCurrency(Fields!LoadingAmount.Value,2).Replace("R","R ").Replace("R  ","R "),
	Len
	(
		FormatCurrency(Fields!LoadingAmount.Value,2).Replace("R","R ").Replace("R  ","R ")
	)
	-3
)
+
Right
(
	FormatCurrency(Fields!LoadingAmount.Value,2).Replace("R","R ").Replace("R  ","R "),3
)
.Replace(",",".")</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>6pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>3</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <LeftBorder>
                              <Width>0.5pt</Width>
                            </LeftBorder>
                            <RightBorder>
                              <Color>Gray</Color>
                              <Style>None</Style>
                            </RightBorder>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="TextBox_GrossRental">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Left
(
	FormatCurrency(Fields!RentalRate.Value * Fields!BillableStoreQty.Value * Fields!BillableWeeks.Value,2).Replace("R","R ").Replace("R  ","R "),
	Len
	(
		FormatCurrency(Fields!RentalRate.Value * Fields!BillableStoreQty.Value * Fields!BillableWeeks.Value,2).Replace("R","R ").Replace("R  ","R ")
	)
	-3
)
+
Right
(
	FormatCurrency(Fields!RentalRate.Value * Fields!BillableStoreQty.Value * Fields!BillableWeeks.Value,2).Replace("R","R ").Replace("R  ","R "),3
)
.Replace(",",".")</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>6pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>2</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox13">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Fields!Discount.Value.ToString().Replace(",",".") &amp; "%"</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>6pt</FontSize>
                                    <Format>0.00</Format>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox13</rd:DefaultName>
                          <ZIndex>1</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox_PeriodTotal">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Left
(
	FormatCurrency(((1 - Fields!Discount.Value / 100) * Fields!LoadingAmount.Value + Fields!RentalRate.Value * Fields!BillableWeeks.Value * Fields!BillableStoreQty.Value) - (Fields!Discount.Value / 100) * Fields!RentalRate.Value * Fields!BillableWeeks.Value * Fields!BillableStoreQty.Value,2).Replace("R","R ").Replace("R  ","R "),
	Len
	(
		FormatCurrency(((1 - Fields!Discount.Value / 100) * Fields!LoadingAmount.Value + Fields!RentalRate.Value * Fields!BillableWeeks.Value * Fields!BillableStoreQty.Value) - (Fields!Discount.Value / 100) * Fields!RentalRate.Value * Fields!BillableWeeks.Value * Fields!BillableStoreQty.Value,2).Replace("R","R ").Replace("R  ","R ")
	)
	-3
)
+
Right
(
	FormatCurrency(((1 - Fields!Discount.Value / 100) * Fields!LoadingAmount.Value + Fields!RentalRate.Value * Fields!BillableWeeks.Value * Fields!BillableStoreQty.Value) - (Fields!Discount.Value / 100) * Fields!RentalRate.Value * Fields!BillableWeeks.Value * Fields!BillableStoreQty.Value,2).Replace("R","R ").Replace("R  ","R "),3
)
.Replace(",",".")</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>6pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.1cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox6">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox6</rd:DefaultName>
                          <ZIndex>27</ZIndex>
                          <Style>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox18">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox18</rd:DefaultName>
                          <ZIndex>24</ZIndex>
                          <Style>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox19">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox19</rd:DefaultName>
                          <ZIndex>23</ZIndex>
                          <Style>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox24">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox24</rd:DefaultName>
                          <ZIndex>22</ZIndex>
                          <Style>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox25">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox25</rd:DefaultName>
                          <ZIndex>21</ZIndex>
                          <Style>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox26">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox26</rd:DefaultName>
                          <ZIndex>20</ZIndex>
                          <Style>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox27">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox27</rd:DefaultName>
                          <ZIndex>19</ZIndex>
                          <Style>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox28">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox28</rd:DefaultName>
                          <ZIndex>18</ZIndex>
                          <Style>
                            <Border>
                              <Style>None</Style>
                            </Border>
                            <TopBorder>
                              <Color>Gray</Color>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox29">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox29</rd:DefaultName>
                          <ZIndex>17</ZIndex>
                          <Style>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox31">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox31</rd:DefaultName>
                          <ZIndex>16</ZIndex>
                          <Style>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox14">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox14</rd:DefaultName>
                          <ZIndex>15</ZIndex>
                          <Style>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox32">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style />
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox32</rd:DefaultName>
                          <ZIndex>14</ZIndex>
                          <Style>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>None</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
                <TablixRow>
                  <Height>0.4cm</Height>
                  <TablixCells>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Textbox39">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>6pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Left</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>Textbox39</rd:DefaultName>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>None</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox15">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontSize>6pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox15</rd:DefaultName>
                          <ZIndex>38</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox16">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontSize>6pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox16</rd:DefaultName>
                          <ZIndex>37</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox17">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value />
                                  <Style>
                                    <FontSize>6pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style />
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox17</rd:DefaultName>
                          <ZIndex>36</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <PaddingLeft>2pt</PaddingLeft>
                            <PaddingRight>2pt</PaddingRight>
                            <PaddingTop>2pt</PaddingTop>
                            <PaddingBottom>2pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Footer_PayStores">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Sum(Fields!BillableStoreQty.Value)</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>6pt</FontSize>
                                    <Color>= IIf(Fields!TotalFreeStores.Value = 0, "White", "Black")</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>35</ZIndex>
                          <Visibility>
                            <Hidden>true</Hidden>
                          </Visibility>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <TopBorder>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Footer_FreeStores">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>= IIf(Fields!TotalFreeStores.Value = 0, 
ReportItems!Footer_TotalStores.Value, 
Sum(Fields!InstallStoreQty.Value - Fields!BillableStoreQty.Value))</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>6pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>34</ZIndex>
                          <Visibility>
                            <Hidden>true</Hidden>
                          </Visibility>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <TopBorder>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Footer_TotalStores">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Sum(Fields!InstallStoreQty.Value)</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>6pt</FontSize>
                                    <Color>= IIf(Fields!TotalFreeStores.Value = 0, "White", "Black")</Color>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Center</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>33</ZIndex>
                          <Visibility>
                            <Hidden>true</Hidden>
                          </Visibility>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <TopBorder>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <RightBorder>
                              <Width>0.5pt</Width>
                            </RightBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Footer_FreeWeeksValue">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Left
(
	FormatCurrency((Sum((Fields!RentalRate.Value + (Fields!LoadingPercentage.Value / 100 * Fields!RentalRate.Value)) * Fields!InstallStoreQty.Value * Fields!InstallWeeks.Value - (Fields!RentalRate.Value + (Fields!LoadingPercentage.Value / 100 * Fields!RentalRate.Value)) * Fields!BillableStoreQty.Value * Fields!BillableWeeks.Value)),2).Replace("R","R ").Replace("R  ","R "),
	Len
	(
		FormatCurrency((Sum((Fields!RentalRate.Value + (Fields!LoadingPercentage.Value / 100 * Fields!RentalRate.Value)) * Fields!InstallStoreQty.Value * Fields!InstallWeeks.Value - (Fields!RentalRate.Value + (Fields!LoadingPercentage.Value / 100 * Fields!RentalRate.Value)) * Fields!BillableStoreQty.Value * Fields!BillableWeeks.Value)),2).Replace("R","R ").Replace("R  ","R ")
	)
	-3
)
+
Right
(
	FormatCurrency((Sum((Fields!RentalRate.Value + (Fields!LoadingPercentage.Value / 100 * Fields!RentalRate.Value)) * Fields!InstallStoreQty.Value * Fields!InstallWeeks.Value - (Fields!RentalRate.Value + (Fields!LoadingPercentage.Value / 100 * Fields!RentalRate.Value)) * Fields!BillableStoreQty.Value * Fields!BillableWeeks.Value)),2).Replace("R","R ").Replace("R  ","R "),3
)
.Replace(",",".")</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>6pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>32</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                              <Width>0.5pt</Width>
                            </Border>
                            <TopBorder>
                              <Style>Solid</Style>
                            </TopBorder>
                            <BottomBorder>
                              <Style>Solid</Style>
                            </BottomBorder>
                            <LeftBorder>
                              <Style>None</Style>
                            </LeftBorder>
                            <RightBorder>
                              <Style>None</Style>
                            </RightBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Footer_Load">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Left
(
	FormatCurrency(Sum(Fields!LoadingAmount.Value),2).Replace("R","R ").Replace("R  ","R "),
	Len
	(
		FormatCurrency(Sum(Fields!LoadingAmount.Value),2).Replace("R","R ").Replace("R  ","R ")
	)
	-3
)
+
Right
(
	FormatCurrency(Sum(Fields!LoadingAmount.Value),2).Replace("R","R ").Replace("R  ","R "),3
)
.Replace(",",".")</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>6pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>31</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <LeftBorder>
                              <Width>0.5pt</Width>
                            </LeftBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingRight>1pt</PaddingRight>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Footer_GrossRentalTotal">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Left
(
	FormatCurrency(Sum(Fields!RentalRate.Value * Fields!BillableStoreQty.Value * Fields!BillableWeeks.Value),2).Replace("R","R ").Replace("R  ","R "),
	Len
	(
		FormatCurrency(Sum(Fields!RentalRate.Value * Fields!BillableStoreQty.Value * Fields!BillableWeeks.Value),2).Replace("R","R ").Replace("R  ","R ")
	)
	-3
)
+
Right
(
	FormatCurrency(Sum(Fields!RentalRate.Value * Fields!BillableStoreQty.Value * Fields!BillableWeeks.Value),2).Replace("R","R ").Replace("R  ","R "),3
)
.Replace(",",".")</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>6pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>30</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="textbox30">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Left
(
	FormatCurrency(Sum(Fields!DiscountAmount.Value),2).Replace("R","R ").Replace("R  ","R "),
	Len
	(
		FormatCurrency(Sum(Fields!DiscountAmount.Value),2).Replace("R","R ").Replace("R  ","R ")
	)
	-3
)
+
Right
(
	FormatCurrency(Sum(Fields!DiscountAmount.Value),2).Replace("R","R ").Replace("R  ","R "),3
)
.Replace(",",".")</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>6pt</FontSize>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <rd:DefaultName>textbox30</rd:DefaultName>
                          <ZIndex>29</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <TopBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Color>Gray</Color>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                    <TablixCell>
                      <CellContents>
                        <Textbox Name="Footer_PeriodTotal">
                          <CanGrow>true</CanGrow>
                          <KeepTogether>true</KeepTogether>
                          <Paragraphs>
                            <Paragraph>
                              <TextRuns>
                                <TextRun>
                                  <Value>=Left
(
	FormatCurrency(Sum((Fields!LoadingAmount.Value + Fields!RentalRate.Value * Fields!BillableWeeks.Value * Fields!BillableStoreQty.Value) - Fields!Discount.Value / 100 * (Fields!LoadingAmount.Value + Fields!RentalRate.Value * Fields!BillableWeeks.Value * Fields!BillableStoreQty.Value)),2).Replace("R","R ").Replace("R  ","R "),
	Len
	(
		FormatCurrency(Sum((Fields!LoadingAmount.Value + Fields!RentalRate.Value * Fields!BillableWeeks.Value * Fields!BillableStoreQty.Value) - Fields!Discount.Value / 100 * (Fields!LoadingAmount.Value + Fields!RentalRate.Value * Fields!BillableWeeks.Value * Fields!BillableStoreQty.Value)),2).Replace("R","R ").Replace("R  ","R ")
	)
	-3
)
+
Right
(
	FormatCurrency(Sum((Fields!LoadingAmount.Value + Fields!RentalRate.Value * Fields!BillableWeeks.Value * Fields!BillableStoreQty.Value) - Fields!Discount.Value / 100 * (Fields!LoadingAmount.Value + Fields!RentalRate.Value * Fields!BillableWeeks.Value * Fields!BillableStoreQty.Value)),2).Replace("R","R ").Replace("R  ","R "),3
)
.Replace(",",".")</Value>
                                  <Style>
                                    <FontFamily>Verdana</FontFamily>
                                    <FontSize>6pt</FontSize>
                                    <FontWeight>Bold</FontWeight>
                                  </Style>
                                </TextRun>
                              </TextRuns>
                              <Style>
                                <TextAlign>Right</TextAlign>
                              </Style>
                            </Paragraph>
                          </Paragraphs>
                          <ZIndex>28</ZIndex>
                          <Style>
                            <Border>
                              <Color>Gray</Color>
                            </Border>
                            <TopBorder>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </TopBorder>
                            <BottomBorder>
                              <Style>Solid</Style>
                              <Width>0.5pt</Width>
                            </BottomBorder>
                            <VerticalAlign>Middle</VerticalAlign>
                            <PaddingLeft>1pt</PaddingLeft>
                            <PaddingTop>1pt</PaddingTop>
                            <PaddingBottom>1pt</PaddingBottom>
                          </Style>
                        </Textbox>
                      </CellContents>
                    </TablixCell>
                  </TablixCells>
                </TablixRow>
              </TablixRows>
            </TablixBody>
            <TablixColumnHierarchy>
              <TablixMembers>
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
                <TablixMember />
              </TablixMembers>
            </TablixColumnHierarchy>
            <TablixRowHierarchy>
              <TablixMembers>
                <TablixMember>
                  <KeepWithGroup>After</KeepWithGroup>
                  <KeepTogether>true</KeepTogether>
                </TablixMember>
                <TablixMember>
                  <Group Name="Table_Bursts_BrandNameGroup">
                    <GroupExpressions>
                      <GroupExpression>=Fields!BrandName.Value</GroupExpression>
                    </GroupExpressions>
                  </Group>
                  <TablixMembers>
                    <TablixMember>
                      <KeepWithGroup>After</KeepWithGroup>
                      <KeepTogether>true</KeepTogether>
                    </TablixMember>
                    <TablixMember>
                      <Group Name="Table_Bursts_MediaNameGroup">
                        <GroupExpressions>
                          <GroupExpression>=Fields!GroupDefinition.Value &amp; Fields!FirstWeek.Value &amp; Fields!LastWeek.Value</GroupExpression>
                        </GroupExpressions>
                      </Group>
                      <TablixMembers>
                        <TablixMember>
                          <KeepWithGroup>After</KeepWithGroup>
                          <KeepTogether>true</KeepTogether>
                        </TablixMember>
                        <TablixMember>
                          <Group Name="Table_Bursts_Details_Group">
                            <DataElementName>Detail</DataElementName>
                          </Group>
                          <TablixMembers>
                            <TablixMember />
                          </TablixMembers>
                          <DataElementName>Detail_Collection</DataElementName>
                          <DataElementOutput>Output</DataElementOutput>
                          <KeepTogether>true</KeepTogether>
                        </TablixMember>
                      </TablixMembers>
                    </TablixMember>
                    <TablixMember>
                      <KeepWithGroup>Before</KeepWithGroup>
                      <KeepTogether>true</KeepTogether>
                    </TablixMember>
                  </TablixMembers>
                </TablixMember>
                <TablixMember>
                  <KeepWithGroup>Before</KeepWithGroup>
                  <KeepTogether>true</KeepTogether>
                </TablixMember>
              </TablixMembers>
            </TablixRowHierarchy>
            <DataSetName>DataSetContractReport_Burst</DataSetName>
            <Height>2.7cm</Height>
            <Width>18.99999cm</Width>
            <Style>
              <FontFamily>Verdana</FontFamily>
              <FontSize>6pt</FontSize>
            </Style>
          </Tablix>
        </ReportItems>
        <Height>2.8cm</Height>
        <Style>
          <RightBorder>
            <Width>0.5pt</Width>
          </RightBorder>
        </Style>
      </Body>
      <Width>19cm</Width>
      <Page>
        <PageHeight>29.5cm</PageHeight>
        <PageWidth>21cm</PageWidth>
        <LeftMargin>0.5cm</LeftMargin>
        <RightMargin>0.5cm</RightMargin>
        <TopMargin>0.5cm</TopMargin>
        <BottomMargin>0.5cm</BottomMargin>
        <ColumnSpacing>1cm</ColumnSpacing>
        <Style />
      </Page>
    
  <ReportParameters>
    <ReportParameter Name="ContractID">
      <DataType>String</DataType>
      <Prompt>ContractID</Prompt>
    </ReportParameter>
    <ReportParameter Name="ReportType">
      <DataType>String</DataType>
      <Prompt>ReportParameter1</Prompt>
    </ReportParameter>
  </ReportParameters>
  
  <Code>    Public Function DisplayAsCurrency(Number As Object) As String
        ' This function displays a numeric value as currency
        ' in this format: R 1 000 000.00

        ' First convert the passed object into a decimal.
        Dim NumberValue As Decimal = 0
        If Decimal.TryParse(Number.ToString, NumberValue) = False Then
            Return "--error--"
        End If

        ' Format the decimal the way we want.
        NumberValue = Math.Round(NumberValue, 2)
        Dim NumberString As String = NumberValue.ToString
        Dim PointPosition As Integer = NumberString.IndexOf(".")
        Dim DecimalPart As String = NumberString.Substring(PointPosition + 1, 2)

        ' Drop the fractions from NumberString.
        NumberString = NumberString.Substring(0, PointPosition)

        ' Loop through the number string's characters inserting a comma at every third position.
        Dim InsertPosition As Integer = PointPosition - 3
        While InsertPosition &gt; 0
            NumberString = NumberString.Insert(InsertPosition, " ")
            InsertPosition -= 3
        End While

        Return "R " &amp; NumberString &amp; "." &amp; DecimalPart

    End Function</Code>
  <Language>en-ZA</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>996bdd0f-c4a4-45b4-b5c7-4e4d9744ac5b</rd:ReportID>
</Report>