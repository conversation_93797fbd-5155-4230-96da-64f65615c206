﻿using Framework.Controls.TabSystem;
using System;
using System.Collections.Generic;
using System.Data;
using System.Windows.Forms;

namespace Framework.Surfaces
{
    public partial class RowSurface : Surface
    {
        private BindingSource BindingSource;
        private List<ITabContentSurfaceFactory> TabContentFactories;


        #region Properties

        private bool IsNewRow
        {
            get
            {
                DataRowView rowview = (DataRowView)BindingSource.Current;
                return rowview.IsNew;
            }
        }

        public Tab RowDetailTab
        {
            get
            {
                Tab rowdetailtab = null;
                if (tabGroupPrimary.Controls.Count > 0 && tabGroupPrimary.Controls[0] is Tab)
                {
                    rowdetailtab = (Tab)tabGroupPrimary.Controls[0];
                }
                return rowdetailtab;
            }
        }

        public RowDetailSurface RowDetailSurface
        {
            get
            {
                RowDetailSurface rowdetailsurface = null;
                if (RowDetailTab != null && RowDetailTab.CurrentContent is RowDetailSurface)
                {
                    rowdetailsurface = (RowDetailSurface)RowDetailTab.CurrentContent;
                }
                return rowdetailsurface;
            }
        }

        public string UnsavedChangesErrorMessage
        {
            get
            {
                string errormessage = string.Empty;
                if (RowDetailSurface != null && RowDetailSurface.flatButtonSave.Visible)
                {
                    string savebuttontext = FrameworkSettings.Strings.SAVEBUTTONTEXT.ToUpper();
                    string undobuttontext = FrameworkSettings.Strings.UNDOBUTTONTEXT.ToUpper();
                    string rowtitle = IsNewRow ? "this item" : labelRowTitle.Text;
                    errormessage = "You've started modifying the details for " + rowtitle + ". Please select either " + savebuttontext
                        + " or " + undobuttontext + " to indicate whether or not you want to save your changes.";
                }
                return errormessage;
            }
        }

        #endregion


        #region Startup

        public RowSurface(BindingSource bindingsource, string rowtitlecolumnname, List<ITabContentSurfaceFactory> listofcontentfactoriesfortabsinthisrow)
        {
            InitializeComponent();
            BindingSource = bindingsource;
            TabContentFactories = listofcontentfactoriesfortabsinthisrow;
            AddDataBindings(rowtitlecolumnname);
            SubscribeToEvents();
            CreateTabs();
            SelectTheFirstTab();
        }

        private void AddDataBindings(string rowtitlecolumnname)
        {
            labelRowTitle.DataBindings.Add("Text", BindingSource, rowtitlecolumnname);
        }

        private void SubscribeToEvents()
        {
            flatButtonBack.Click += FlatButtonBack_Click;
        }

        public void CreateTabs()
        {
            if (IsNewRow)
            {
                if (tabGroupPrimary.Controls.Count == 0)
                {
                    CreateTab(0);
                }
            }
            else
            {
                for (int i = tabGroupPrimary.Controls.Count; i < TabContentFactories.Count; i++)
                {
                    CreateTab(i);
                }
            }
        }

        private void CreateTab(int tabindex)
        {
            if (TabContentFactories != null)
            {
                ITabContentSurfaceFactory surfacefactory = TabContentFactories[tabindex];
                Control content = surfacefactory.NewSurface(BindingSource);
                string tabtext = surfacefactory.TabText;
                var newtab = new Tab(panelTabContent, content, tabtext);
                tabGroupPrimary.Controls.Add(newtab);
            }
        }

        private void SelectTheFirstTab()
        {
            if (tabGroupPrimary.Controls.Count > 0)
            {
                Tab tabtoselect = (Tab)tabGroupPrimary.Controls[0];
                tabtoselect.Selected = true;
            }
        }

        #endregion


        #region BACK button

        private void FlatButtonBack_Click(object sender, EventArgs e)
        {
            GoBack();
        }

        public void GoBack()
        {
            bool gobackpermitted = true;

            if (DirtyStateManager.IsDirty)
            {
                // Changes have been made. First check if the unsaved changes error message exists before going back.
                if (string.IsNullOrEmpty(UnsavedChangesErrorMessage) == false)
                {
                    gobackpermitted = false;
                    RowDetailTab.Selected = true;
                    MessageForm.Show(UnsavedChangesErrorMessage, "Unsaved Data Warning");
                }
            }

            if (gobackpermitted)
            {
                if (IsNewRow)
                {
                    BindingSource.CancelEdit();
                    BindingSource.ResetBindings(false);
                }
                RowDetailSurface.DirtyStateManager.RejectChanges();
                ParentTab.DisplayPreviousContent();
                Dispose();
            }
        }

        #endregion

    }
}
