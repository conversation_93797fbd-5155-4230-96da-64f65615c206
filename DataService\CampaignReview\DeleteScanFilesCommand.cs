﻿using DataAccess;
using System;
using System.Collections.Generic;
using System.Data;

namespace DataService.CampaignReview
{
    class DeleteScanFilesCommand : Command
    {
        public Guid SessionId { get; set; }
        public DataTable ScanFiles { get; set; }

        public DeleteScanFilesCommand(Guid sessionid, List<DataRow> scanfileslist)
        {
            SessionId = sessionid;

            // Create a new table.
            ScanFiles = new DataTable();
            ScanFiles.Columns.Add("id", typeof(int));

            // Convert the rows in the specified list to rows that match the schema of the table and then add them to the table.
            if (scanfileslist != null && scanfileslist.Count > 0)
            {
                for (int i = 0; i < scanfileslist.Count; i++)
                {
                    DataRow newrow = ScanFiles.NewRow();
                    newrow["id"] = scanfileslist[i]["scanimportfileid"];
                    ScanFiles.Rows.Add(newrow);
                }
            }
        }
    }
}