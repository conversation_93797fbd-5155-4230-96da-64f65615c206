﻿Public Class MediaGroup
    Inherits OldBaseObject

#Region "Fields"

    Private _MediaGroupMemberBindingSource As BindingSource
    Private WithEvents MediaGroupMemberTable As DataTable

#End Region

#Region "Database Column Properties"
    Public ReadOnly Property MediaGroupID() As Integer
        ' This object's identifier.
        Get
            Return Row("MediaGroupID")
        End Get
    End Property


    Public Property MediaGroupName() As String
        ' Descriptive name of the media.
        Get
            Return Row("MediaGroupName")
        End Get
        Set(ByVal value As String)
            ' Clean the proposed new value.
            value = value.Trim
            If Not String.Compare(Row("MediaGroupName"), value, False) = 0 Then
                ' New value is different than existing value. Proceed with update.
                AddLog("MediaGroupName", "MediaGroupName", value.ToString)
                Row("MediaGroupName") = value
                IsDirty = True
            End If
        End Set
    End Property

    Public Property Dormant() As Boolean
        ' Is this media allowed to be used at a homesite category?
        Get
            Return Row("Dormant")
        End Get
        Set(ByVal value As Boolean)
            If Not Row("Dormant") = value Then
                ' New value is different than existing value. Proceed with update.
                AddLog("Dormant", "Dormant", value.ToString)
                Row("Dormant") = value
                IsDirty = True
            End If
        End Set
    End Property

#End Region

#Region "Custom Properties"
    Public ReadOnly Property MediaGroupMemberBindingSource() As BindingSource
        Get
            Return _MediaGroupMemberBindingSource
        End Get
    End Property

#End Region

#Region "Public Shared Methods"
    Public Shared Function GetListData _
    (ByVal ConString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) As System.Windows.Forms.BindingSource

        ' Instantiate a new data set and create a sql connection to use to populate it.
        Dim ListDataSet As New DataSetMedia
        Dim SqlCon As New SqlClient.SqlConnection(ConString)

        ' Create table adapters to collect list data and related data.
        Dim ListAdapter As New DataSetMediaTableAdapters.MediaGroupTableAdapter
        Dim MediaGroupMemberAdapter As New DataSetMediaTableAdapters.MediaGroupMemberTableAdapter
        ListAdapter.Connection = SqlCon
        MediaGroupMemberAdapter.Connection = SqlCon

        ' Populate tables in the dataset.
        Try
            ListAdapter.Fill(ListDataSet.Tables("MediaGroup"))
            MediaGroupMemberAdapter.Fill(ListDataSet.Tables("MediaGroupMember"))
        Catch ex As Exception
            ErrorMessage += LiquidShell.LiquidAgent.GetErrorMessage(ex)
            Return Nothing
        Finally
            ListAdapter.Dispose()
            MediaGroupMemberAdapter.Dispose()
            SqlCon.Dispose()
        End Try

        ' Return a binding source linked to the retrieved data.
        Dim ReturnBindingSource As BindingSource = New BindingSource(ListDataSet, "MediaGroup")
        ReturnBindingSource.Sort = "MediaGroupName"
        Return ReturnBindingSource

    End Function

    Public Shared Sub Delete(ByVal Grid As DataGridView, ByVal ConnectionString As String)
        ' Delete one or more rows from the database.

        ' Build a list of data relation names of child rows that must be deleted with the parent row.
        Dim DeletableChildRelationNames As New List(Of String)
        DeletableChildRelationNames.Add("FK_MediaGroupMember_MediaGroup")

        ' Create an audit table to record this action.
        Dim AuditLog As New DataSetAudit.AuditLogDataTable

        ' Create a list of names of the columns that will be used to describe each row being deleted.
        Dim ColumnNames As New List(Of String)
        ColumnNames.Add("MediaGroupName")

        ' Add row descriptions to the Tag properties of all selected grid rows (these will be used as the object name
        ' in the audit log entries for rows that are deleted.)
        For Each SelectedGridRow As DataGridViewRow In Grid.SelectedRows
            Dim Row As DataRow = CType(SelectedGridRow.DataBoundItem, DataRowView).Row
            SelectedGridRow.Tag = Row("MediaGroupName")
        Next

        ' Delete rows from the dataset.
        LiquidShell.LiquidAgent.DeleteParentRows _
        (Grid, String.Empty, DeletableChildRelationNames, AuditLog, "MediaGroup")

        ' Get the tables and data set from which rows are being deleted to use as a parameter for the table adapters.
        Dim GridTable As DataTable = CType(CType(Grid.DataSource, BindingSource).List, DataView).Table
        Dim DataSet As DataSet = GridTable.DataSet

        ' If any rows were deleted from the data set, delete them from the database too.
        Using DeletedRows As New DataView(GridTable, "", "", DataViewRowState.Deleted)
            If IsNothing(DeletedRows) = False AndAlso DeletedRows.Count > 0 Then

                ' Create and configure table adapters to connect to the database.
                Dim MediaGroupAdapter As New DataSetMediaTableAdapters.MediaGroupTableAdapter
                Dim MediaGroupMemberAdapter As New DataSetMediaTableAdapters.MediaGroupMemberTableAdapter
                Dim AuditAdapter As New DataSetAuditTableAdapters.AuditLogTableAdapter

                ' Create a new connection for the table adapters to use.
                Dim SqlCon As New SqlClient.SqlConnection(ConnectionString)
                MediaGroupAdapter.Connection = SqlCon
                MediaGroupMemberAdapter.Connection = SqlCon
                AuditAdapter.Connection = SqlCon

                ' Get the form that is consuming this method so that the ShowMessage method can be used.
                Dim Consumingform As LiquidShell.BaseForm = CType(Grid.TopLevelControl, LiquidShell.BaseForm)

                ' Perform the delete operation.
                Try
                    MediaGroupAdapter.Update(GridTable)
                    MediaGroupMemberAdapter.Update(DataSet.Tables("MediaGroupMember"))
                    AuditAdapter.Update(AuditLog)
                Catch ex As Exception
                    Consumingform.ShowMessage("Aaaagghh!!!  An error occured while trying to delete. The technical description of the error is:" _
                    & vbCrLf & vbCrLf & LiquidShell.LiquidAgent.GetErrorMessage(ex))
                Finally
                    ' Dispose all data adapters.
                    MediaGroupAdapter.Dispose()
                    MediaGroupMemberAdapter.Dispose()
                    AuditAdapter.Dispose()
                    ' Close all connections.
                    SqlCon.Dispose()
                End Try

            End If
        End Using

    End Sub
#End Region

#Region "Public Methods"

    Public Sub New(ByVal DataSource As BindingSource, ByVal AddNew As Boolean)
        ' This constructor takes a binding source and a boolean indicating whether a new
        ' row must be added or whether this is an existing row being edited.
        If AddNew Then
            ' This is a new row being added.
            DataSource.AddNew()
        End If

        ' Update the base class binding source variable.
        DataBindingSource = DataSource
    End Sub


    Public Sub Save(ByVal ConnectionString As String, ByVal GridsToAudit As List(Of DataGridView))
        ' Save this object to the database.

        ' Create and configure table adapters to connect to the database.
        Dim MediaGroupAdapter As New DataSetMediaTableAdapters.MediaGroupTableAdapter
        Dim MediaGroupMemberAdapter As New DataSetMediaTableAdapters.MediaGroupMemberTableAdapter
        Dim AuditAdapter As New DataSetAuditTableAdapters.AuditLogTableAdapter

        ' Create a new connection for the table adapters to use.
        Dim SqlCon As New SqlClient.SqlConnection(ConnectionString)
        MediaGroupAdapter.Connection = SqlCon
        MediaGroupMemberAdapter.Connection = SqlCon
        AuditAdapter.Connection = SqlCon

        ' Check if this is a new row being added.
        If Row.RowState = DataRowState.Detached Then
            ' This is a new row being added. Clear the audit log and create only one entry for the new row.
            AuditLog.Clear()
            AddLog("MediaGroupName")
        Else
            ' This row was edited. Update audit log with child row addition details.
            For Each Grid As DataGridView In GridsToAudit
                ' Check each row in this grid.
                For Each GridRow As DataGridViewRow In Grid.Rows
                    ' Get the datarow feeding this grid row.
                    Dim Row As DataRow = CType(GridRow.DataBoundItem, DataRowView).Row
                    If Row.RowState = DataRowState.Added Then
                        ' This row is newly added. Add an entry for it into the audit log.
                        Dim ActionText As String = BuildChildRowActionText(Grid, GridRow, ChildRowAction.Added)
                        LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, Table.TableName, MediaGroupName, ActionText)
                    End If
                    If Row.RowState = DataRowState.Modified Then
                        ' This row has been modified. Add an entry for it into the audit log.
                        Dim ActionText As String = BuildChildRowActionText(Grid, GridRow, ChildRowAction.Modified)
                        LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, Table.TableName, MediaGroupName, ActionText)
                    End If
                Next
            Next
        End If

        ' Perform the save operation.
        Try
            ' Remember the current item in the binding source list.
            Dim CurrentItem As Object = DataBindingSource.Current
            ' Commit changes to the dataset.
            DataBindingSource.EndEdit()     ' This line changes the binding source's current item.
            ' Restore the current item in the binding source list.
            DataBindingSource.Position = DataBindingSource.IndexOf(CurrentItem)
            ' Update the database.
            MediaGroupAdapter.Update(Row)
            MediaGroupMemberAdapter.Update(DataSet.Tables("MediaGroupMember"))
            IsDirty = False
            AuditAdapter.Update(AuditLog)
        Finally
            ' Dispose all data adapters.
            MediaGroupAdapter.Dispose()
            MediaGroupMemberAdapter.Dispose()
            AuditAdapter.Dispose()
            ' Dispose the connection.
            SqlCon.Close()
        End Try

    End Sub


    Public Sub AddMediaToGroupMembership _
(ByVal ConsumingSubform As LiquidShell.Subform,
ByVal ConnectionString As String,
ByVal SelectedItems As List(Of DataRow))
        ' Make this media a member of the given media families.

        ' Check for legal selection by user.
        If IsNothing(SelectedItems) Then
            Exit Sub
        Else
            If SelectedItems.Count = 0 Then
                Exit Sub
            End If
        End If

        ' Add a new row to the table for each item that was selected.
        For Each SelectedItem As DataRow In SelectedItems

            ' Create a new row for the table.
            Dim NewRow As DataRow = DataSet.Tables("MediaGroupMember").NewRow
            NewRow("MediaGroupID") = MediaGroupID
            NewRow("MediaName") = SelectedItem("MediaName")
            NewRow("MediaID") = SelectedItem("MediaID")
            DataSet.Tables("MediaGroupMember").Rows.Add(NewRow)

            ' Flag the object as dirty.
            IsDirty = True

        Next
    End Sub

#End Region

#Region "Private Methods"
    Protected Overrides Sub UpdateProperties()
        ' Update properties to match the current Row.
        UpdateCustomProperties()
    End Sub

    Private Sub UpdateCustomProperties()
        _MediaGroupMemberBindingSource = New BindingSource(DataBindingSource, "FK_MediaGroupMember_MediaGroup")
        MediaGroupMemberTable = CType(_MediaGroupMemberBindingSource.List, DataView).Table
    End Sub
#End Region

End Class
