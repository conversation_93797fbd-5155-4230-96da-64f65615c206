Public Class CommandCentre

    Public Sub New()

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.

    End Sub

    Public Sub New(connectionstring As String)

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.

        ' Update the user principal
        LiquidAgent.UpdatePrincipal(connectionstring)

        ' Update the app's connection string.
        My.Settings("DBConnection") = connectionstring

    End Sub

    Private Sub CommandCentre_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load

        ' Display the server name.
        Dim Builder As New SqlClient.SqlConnectionStringBuilder(My.Settings.DBConnection)
        LabelLoginInfo.Text = "Connected to server '" & Builder.DataSource & "' as '" & Builder.UserID & "'"

        ' Load buttons if user has required permission.
        ContentSwitcherMaster.AddButton("Contract Manager", New SubformContractStart)
        ContentSwitcherMaster.AddButton("Media Gap", New SubformMediaGap)
        If My.User.IsInRole("accountmanager") Then
            ContentSwitcherMaster.AddButton("Clients && Brands", New SubformClientsAndBrands)
        Else
            ContentSwitcherMaster.AddButton("Clients && Brands", New SubformClientsAndBrands)
        End If
        If My.User.IsInRole("sales_manager") Then
            ContentSwitcherMaster.AddButton("Sales Team", New SubformSalesTeam)
        End If
        If My.User.IsInRole("category_manager") Then
            ContentSwitcherMaster.AddButton("Category Manager", New SubformCategoryManager)
        End If
        If My.User.IsInRole("media_manager") Then
            ContentSwitcherMaster.AddButton("Media Manager", New SubformMediaManager)
        End If

        'ContentSwitcherMaster.AddButton("Store Group Manager", New SubformStoreGroupManager)

    End Sub

    Private Sub HyperlinkUsername_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)

        ' Declare a variable to hold the needed form.
        Dim AccountOptionsForm As FormAccountOptions = Nothing

        ' Check if the user has already opened this form.
        For Each OpenForm As Form In My.Application.OpenForms
            If TypeOf OpenForm Is FormAccountOptions Then
                AccountOptionsForm = OpenForm
            End If
        Next

        ' Check if we found the account options form.
        If IsNothing(AccountOptionsForm) = False Then
            ' Form was found to be open. Give it focus.
            AccountOptionsForm.Focus()
        Else
            ' Form doesn't exist. Create it.
            AccountOptionsForm = New FormAccountOptions()
            AccountOptionsForm.Show()
        End If

    End Sub

    Private Sub HyperlinkSettings_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles HyperlinkSettings.Click

        ' Declare a variable to hold the needed form.
        Dim AccountOptionsForm As FormSettings = Nothing

        ' Check if the user has already opened this form.
        For Each OpenForm As Form In My.Application.OpenForms
            If TypeOf OpenForm Is FormSettings Then
                AccountOptionsForm = OpenForm
            End If
        Next

        ' Check if we found the account options form.
        If IsNothing(AccountOptionsForm) = False Then
            ' Form was found to be open. Give it focus.
            AccountOptionsForm.Focus()
        Else
            ' Form doesn't exist. Create it.
            AccountOptionsForm = New FormSettings()
            AccountOptionsForm.Show()
        End If

    End Sub

End Class
