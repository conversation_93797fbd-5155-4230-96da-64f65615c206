﻿using DataAccess;
using System;

namespace DataService.Security
{
    class CreateRoleCommand : Command
    {
        public Guid SessionId { get; set; }
        public Guid RoleId { get; set; }
        public string RoleName { get; set; }
        public string RoleDescription { get; set; }

        public CreateRoleCommand(Guid sessionid, string rolename, string roledescription)
        {
            SessionId = sessionid;
            RoleName = rolename;
            RoleDescription = roledescription;
        }
    }
}
