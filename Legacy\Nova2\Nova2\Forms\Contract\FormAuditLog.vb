﻿Public Class FormAuditLog

    Public Sub New(ObjectName As String)

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.

        ' Create a dataset to hold the data.
        Dim AuditDataSet As New DataSetAudit

        ' Create a table adapter to load the data.
        Dim AuditAdapter As New DataSetAuditTableAdapters.AuditLogTableAdapter

        ' Create a new connection for the table adapter.
        Dim SqlCon As New SqlClient.SqlConnection(My.Settings.DBConnection)
        AuditAdapter.Connection = SqlCon

        ' Load data into the dataset.
        AuditAdapter.FillByObjectName(AuditDataSet.AuditLog, ObjectName)

        ' Configure grid.
        Grid.AutoGenerateColumns = False
        Grid.DataSource = New BindingSource(AuditDataSet, AuditDataSet.AuditLog.TableName)
        Dim GridManagerResearchCategories As New GridManager(Grid, TextEditSearch, Nothing, Nothing, _
        PictureAdvancedSearch, PictureClearSearch, Nothing, Nothing)

    End Sub

    Public Shared Sub ShowLogByContract(ContractNumber As String)

        ' Instantiate the form.
        Dim AuditLogForm As New FormAuditLog(ContractNumber)

        ' Display the form.
        AuditLogForm.Show()

    End Sub

End Class
