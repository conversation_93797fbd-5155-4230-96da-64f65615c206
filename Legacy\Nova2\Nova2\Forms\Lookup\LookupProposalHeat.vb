Public Class LookupProposalHeat

    Public Sub New(ByVal GridData As BindingSource, ByVal AllowMultiSelect As Boolean)

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        GridController = New GridManager _
        (GridItems,
        TextEditSearch,
        GridData,
        My.Settings.DBConnection,
        PictureAdvancedSearch,
        PictureClearSearch,
        Nothing,
        Nothing)

        ' Apply multi-select restrictions if necessary.
        GridItems.MultiSelect = AllowMultiSelect

    End Sub

    Public Overloads Shared Function SelectRows _
    (ByVal ConnectionString As String,
    ByVal AllowMultiSelect As Boolean,
    ByVal GridToExclude As DataGridView) _
    As List(Of DataRow)

        ' A string variable to hold any potential errors during data retrieval.
        Dim ErrorMessage As String = String.Empty

        ' Create a data source for the list.
        Dim GridData As BindingSource = Lookup.GetProposalHeat(ConnectionString, ErrorMessage, GridToExclude)

        ' Create an instance of this lookup list form and return the user-selected rows.
        Using Selector As New LookupProposalHeat(GridData, AllowMultiSelect)
            Return SelectRows(Selector, ErrorMessage)
        End Using

    End Function


End Class
