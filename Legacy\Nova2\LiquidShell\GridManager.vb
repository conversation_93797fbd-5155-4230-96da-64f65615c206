Public Class GridManager

    Private ConsumingForm As BaseForm
    Private WithEvents TheGrid As DataGridView
    Private WithEvents SearchBox As TextEdit
    Private _GridBindingSource As BindingSource
    Private WithEvents GridDataView As DataView
    Private ErrorMessage As String = String.Empty
    Private WithEvents AdvancedSearchButton As PictureEdit
    Private WithEvents ClearSearchButton As PictureEdit
    Private WithEvents AdvancedSearchForm As GridSearchForm
    Private EditButton As Control
    Private DeleteButton As Control
    Private _TotalRows As Integer = 0
    Private PreExistingFilter As String = String.Empty
    Private _CustomFilter As String

#Region "Properties"

    Private Property TotalRows As Integer
        Get
            Return _TotalRows
        End Get
        Set(value As Integer)
            _TotalRows = value
        End Set
    End Property

    Public ReadOnly Property Grid() As DataGridView
        Get
            Return TheGrid
        End Get
    End Property

    Public Property GridBindingSource() As BindingSource
        Get
            Return _GridBindingSource
        End Get
        Set(ByVal value As BindingSource)

            If IsNothing(value) Then
                ' No data source was supplied for the grid.
                If IsNothing(TheGrid.DataSource) Then
                    ' The supplied grid had no pre-configured data source. Unable to proceed.
                    TotalRows = 0
                    Exit Property
                Else
                    ' The supplied grid was pre-configured with a data source. Save it to the field.
                    _GridBindingSource = CType(TheGrid.DataSource, BindingSource)
                End If
            Else
                ' Update the value of the field.
                _GridBindingSource = value
                ' Update the data source property of the grid.
                TheGrid.AutoGenerateColumns = False
                TheGrid.DataSource = value
            End If

            ' Update the value of the GridDataView property.
            If TypeOf _GridBindingSource.DataSource Is DataSet _
            Or TypeOf _GridBindingSource.DataSource Is BindingSource Then
                ' The data source of the binding source is a dataset.
                GridDataView = LiquidAgent.GetBindingSourceTable(_GridBindingSource).DefaultView
            ElseIf TypeOf _GridBindingSource.DataSource Is DataTable Then
                ' The data source of the binding source is a dataTable.
                GridDataView = CType(_GridBindingSource.DataSource, DataTable).DefaultView
            Else
                ' The data source of the binding source must be a dataview.
                GridDataView = CType(_GridBindingSource.DataSource, DataView)
            End If

            ' Update the value of the TotalStores variable.
            TotalRows = _GridBindingSource.Count

            ' Check for existing filters on the binding source that need to be preserved.
            If TypeOf GridBindingSource.DataSource Is DataView Then
                ' The data source of the grid's binding source is a dataview. This likely means that there's already a filter in place.
                If Not String.IsNullOrEmpty(CType(GridBindingSource.DataSource, DataView).RowFilter) Then
                    ' The dataview which is the data source of the binding source has a filter. Save it so we can use it later.
                    PreExistingFilter = CType(GridBindingSource.DataSource, DataView).RowFilter
                End If
            End If

        End Set
    End Property

#End Region

#Region "Methods"

    Public Sub New _
    (ByVal _Grid As DataGridView, _
    ByVal _SearchBox As TextEdit, _
    ByVal GridData As BindingSource, _
    ByVal ConnectionString As String, _
    ByVal SearchButton As PictureEdit, _
    ByVal ClearButton As PictureEdit, _
    ByVal RowEditButton As Control, _
    ByVal RowDeleteButton As Control)

        ' Assign local variables with instance objects.
        ConsumingForm = CType(_Grid.TopLevelControl, BaseForm)
        TheGrid = _Grid
        TheGrid.Tag = Me
        SearchBox = _SearchBox
        AdvancedSearchButton = SearchButton
        ClearSearchButton = ClearButton
        EditButton = RowEditButton
        DeleteButton = RowDeleteButton
        SetupSearchForm()

        ' Configure settings.
        If Not IsNothing(ClearSearchButton) Then
            ClearSearchButton.Visible = False
        End If

        ' Fill the grid with data.
        GridBindingSource = GridData
        UpdateButtons()
        UpdateGroupBoxElements()

    End Sub

    Private Sub SetupSearchForm()

        ' Set the grid's tag property to this grid manager instance so that the ClearSearch method inside the SearchForm
        ' can easily call this object's clearserach method.
        TheGrid.Tag = Me

        ' Create a new search form to search individual columns in the grid.
        AdvancedSearchForm = New GridSearchForm(TheGrid)
        ' Setup the event handlers for the search controls on the search form.
        For Each SearchBox As TextEdit In AdvancedSearchForm.SearchBoxes
            AddHandler SearchBox.KeyUp, AddressOf SearchBox_KeyUp
        Next
        For Each SearchCheckBox As CheckEdit In AdvancedSearchForm.SearchCheckBoxes
            AddHandler SearchCheckBox.CheckStateChanged, AddressOf SearchCheckBox_CheckedStateChanged
        Next

    End Sub

    Private Sub UpdateGroupBoxElements()

        ' Get the GroupControl container of the grid.
        Dim Box As DevExpress.XtraEditors.GroupControl = CType(TheGrid.Parent, DevExpress.XtraEditors.GroupControl)

        ' Get the length of the title excluding everything after the first occurance of "(".
        Dim TitleLength As Integer = Box.Text.IndexOf("(")

        ' Get the title of the grid.
        Dim TitleText As String
        If TitleLength < 1 Then
            TitleText = Box.Text
        Else
            TitleText = Trim(Box.Text.Substring(0, TitleLength))
        End If

        ' Check if a filter is applied to the binding source.
        If FilterIsApplied() = False Then
            ' No filter is applied. Set the container's text to the title and the number of rows.
            Box.Text = TitleText & " (" & TheGrid.Rows.Count & ")"
            ' Hide the Clear Search button.
            If Not IsNothing(ClearSearchButton) Then
                ClearSearchButton.Visible = False
            End If
        Else
            ' A filter is applied. Show visible row quantity and total row quantity in the container's text.
            Box.Text = TitleText & " (" & TheGrid.Rows.Count & " of " & TotalRows.ToString & ")"
            ' Display the Clear Search button.
            If Not IsNothing(ClearSearchButton) Then
                ClearSearchButton.Visible = True
            End If
        End If

    End Sub

    Private Sub UpdateButtons()
        If TheGrid.Rows.Count = 0 Then
            ' The grid is empty. Disable the Edit and Delete buttons.
            If IsNothing(EditButton) = False Then
                EditButton.Enabled = False
            End If
            If IsNothing(DeleteButton) = False Then
                DeleteButton.Enabled = False
            End If
        Else
            ' The grid contains at least one row. Enable the Edit and Delete buttons.
            If IsNothing(EditButton) = False Then
                EditButton.Enabled = True
            End If
            If IsNothing(DeleteButton) = False Then
                DeleteButton.Enabled = True
            End If
        End If
    End Sub

    Private Sub ApplyFilter(ByVal Filter As String)

        ' Apply the filter to the binding source.
        Try

            ' Before simply applying the filter to the binding source, check for a pre-existing filter that must also be applied
            ' together with our new search filter.
            If Not String.IsNullOrEmpty(PreExistingFilter) Then
                ' A pre-existing filter must be applied.
                If Not String.IsNullOrEmpty(Filter) Then
                    ' The search filter is not an empty string. Apply both the search filter AND the pre-existing filter.
                    Filter = "(" & PreExistingFilter & ") AND (" & Filter & ")"
                Else
                    ' The search filter is an empty string. Apply only the pre-existing filter.
                    Filter = PreExistingFilter
                End If
            End If

            ' Apply the filter to the binding source now.
            GridBindingSource.Filter = Filter

        Catch ex As Exception
            ' Something went wrong. Display an error message.
            ConsumingForm.ShowMessage("Oops!  A search box error occured. Sorry about that. The technical description of the error is:" _
            & vbCrLf & vbCrLf & LiquidAgent.GetErrorMessage(ex))
        End Try

    End Sub

    Private Sub BuildGeneralFilter(ByRef Filter As System.Text.StringBuilder, ByVal SearchText As String)

        ' Add column names by which to filter into the string builder.
        For Each Column As DataGridViewColumn In TheGrid.Columns
            If Not TypeOf Column Is DataGridViewCheckBoxColumn Then
                ' Checkbox columns are excluded from the filter, but this isn't one, so no problem.
                If Filter.ToString.Length > 0 Then
                    Filter.Append(" + ' ' + ")
                End If
                Filter.Append("ISNULL(" & Column.DataPropertyName & ", '')")
            End If
        Next

        ' Add the search text specified by the user to the string builder.
        Filter.Append(" LIKE '%" & SearchText & "%'")

    End Sub

    Private Sub BuildColumnFilter(ByRef Filter As System.Text.StringBuilder)

        ' Check all search boxes for content.
        For Each ColumnSearchBox As TextEdit In AdvancedSearchForm.SearchBoxes
            If String.IsNullOrEmpty(ColumnSearchBox.Text) = False Then
                ' User typed something into this search box. Include it in the filter.
                If Filter.ToString.Length > 0 Then
                    Filter.Append(" AND ")
                End If
                Filter.Append(ColumnSearchBox.Name & " + ' '" & " LIKE '%" & ColumnSearchBox.Text.Replace("'", "''") & "%'")
            End If
        Next

        ' Check all check boxes for content.
        For Each ColumnSearchCheckBox As CheckEdit In AdvancedSearchForm.SearchCheckBoxes
            If Not ColumnSearchCheckBox.CheckState = CheckState.Indeterminate Then
                ' User clicked this check box. Include it in the filter.
                If Filter.ToString.Length > 0 Then
                    Filter.Append(" AND ")
                End If
                Filter.Append(ColumnSearchCheckBox.Name & " = " & ColumnSearchCheckBox.Checked.ToString)
            End If
        Next

    End Sub

    Public Function FilterIsApplied() As Boolean
        ' Check if the binding source is currently filtered or not.
        If IsNothing(SearchBox) Then
            ' Search box doesn't exist.
            Return False
        End If
        If String.IsNullOrEmpty(SearchBox.Text) AndAlso AdvancedSearchForm.SearchBoxesAreEmpty Then
            Return False
        Else
            Return True
        End If
    End Function

    Public Sub ClearSearch()

        ' Clear the quick search box.
        If IsNothing(SearchBox) = False Then
            SearchBox.Text = String.Empty
        End If
        ' Clear the advanced search form.
        If IsNothing(AdvancedSearchForm) = False Then
            AdvancedSearchForm.ClearSearchValues()
        End If
        ' Apply an ampty filter.
        ApplyFilter(String.Empty)
        ' Update row count figures in the grid title.
        UpdateGroupBoxElements()

    End Sub

    Private Sub BuildFilter(SendingSearchBox As TextEdit)

        ' Create a string builder to hold the filter command for the binding source.
        Dim Filter As New System.Text.StringBuilder

        ' Build the filter text.
        If AdvancedSearchForm.SearchBoxNames.Contains(SendingSearchBox.Name) Then
            ' User is searching specific columns.
            BuildColumnFilter(Filter)
        Else
            ' User is searching the entire grid. Clear the advanced search form.
            If IsNothing(AdvancedSearchForm) = False Then
                AdvancedSearchForm.ClearSearchValues()
            End If
            If Not String.IsNullOrEmpty(SendingSearchBox.Text) Then
                ' The search box is not empty. Build the filter.
                BuildGeneralFilter(Filter, SearchBox.Text.Replace("'", "''"))
            End If
        End If

        ' Apply the filter.
        ApplyFilter(Filter.ToString)

        ' Update row count figures in the grid title.
        UpdateGroupBoxElements()

    End Sub

    Public Sub ApplyCustomColumnFilter(Column As DataGridViewColumn, value As Object)


        For Each SearchBox As TextEdit In AdvancedSearchForm.SearchBoxes
            If String.Compare(SearchBox.Name, Column.DataPropertyName) = 0 Then
                ' This searchbox matches the DataPropertyName of the supplied column.
                SearchBox.Text = CStr(value)
                ' Build a filter based on the contents of this text edit.
                BuildFilter(SearchBox)
            End If
        Next
        For Each SearchCheckBox As CheckEdit In AdvancedSearchForm.SearchCheckBoxes
            If String.Compare(SearchCheckBox.Name, Column.DataPropertyName) = 0 Then
                ' This checkbox matches the DataPropertyName of the supplied column.
                SearchCheckBox.Checked = CBool(value)
            End If
        Next








        '_CustomFilter = value



        '' Create a string builder to hold the filter command for the binding source.
        'Dim Filter As New System.Text.StringBuilder(_CustomFilter)

        'BuildGeneralFilter(Filter, SearchBox.Text)

        '' Apply the filter.
        'ApplyFilter(Filter.ToString)


    End Sub

#End Region

#Region "Event Handlers"

    Private Sub Grid_RowsAdded(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewRowsAddedEventArgs) Handles TheGrid.RowsAdded
        ' Update the total rows count.
        If TotalRows < TheGrid.RowCount Then
            TotalRows = TheGrid.RowCount
        End If
        ' Update visual elements.
        UpdateGroupBoxElements()
        UpdateButtons()
    End Sub

    Private Sub Grid_RowsRemoved(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewRowsRemovedEventArgs) Handles TheGrid.RowsRemoved
        ' Update visual elements.
        UpdateGroupBoxElements()
        UpdateButtons()
    End Sub

    Private Sub SearchCheckBox_CheckedStateChanged(ByVal sender As System.Object, ByVal e As System.EventArgs)

        ' Change the text depending on current state.
        Dim SendingCheckEdit As CheckEdit = CType(sender, CheckEdit)
        If SendingCheckEdit.CheckState = CheckState.Checked Then
            SendingCheckEdit.Text = " (must be TRUE)"
        ElseIf SendingCheckEdit.CheckState = CheckState.Unchecked Then
            SendingCheckEdit.Text = " (must be FALSE)"
        Else
            SendingCheckEdit.Text = " (can be any value)"
        End If

        ' Exit if grid is empty.
        If IsNothing(GridBindingSource) Then
            Exit Sub
        Else
            Dim LookupTableView As DataView = CType(GridBindingSource.List, DataView)
            If LookupTableView.Table.Rows.Count = 0 Then
                Exit Sub
            End If
        End If

        ' Create a string builder to hold the filter command for the binding source.
        Dim Filter As New System.Text.StringBuilder

        ' Build the filter text.
        BuildColumnFilter(Filter)

        ' Apply the filter.
        ApplyFilter(Filter.ToString)

    End Sub

    Private Sub AdvancedSearchButton_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles AdvancedSearchButton.Click
        ' Reset the search box text.
        SearchBox.Text = String.Empty
        ' Position the search form.
        AdvancedSearchForm.Location = New Point _
        (Cursor.Position.X - AdvancedSearchForm.Width, Cursor.Position.Y - AdvancedSearchForm.Height - 10)
        ' Display the form.
        AdvancedSearchForm.ShowDialog()
    End Sub

    Private Sub ClearSearchButton_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles ClearSearchButton.Click
        ClearSearch()
    End Sub

    Private Sub SearchBox_KeyUp(sender As Object, e As System.Windows.Forms.KeyEventArgs) Handles SearchBox.KeyUp
        ' Get the text edit that raised this event.
        Dim SendingSearchBox As TextEdit = CType(sender, TextEdit)
        ' Build a filter based on the contents of this text edit.
        BuildFilter(SendingSearchBox)
    End Sub

#End Region

End Class
