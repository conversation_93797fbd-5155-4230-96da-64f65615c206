Public Class FormMediaGapContractInfo

#Region "Public Shared Methods"

    Public Shared Sub ShowInfo(ByVal ContractID As Guid)
        ' Display the form.
        Dim ErrorMessage As String = String.Empty
        Using ContractInfo As New FormMediaGapContractInfo(ContractID, ErrorMessage)
            If String.IsNullOrEmpty(ErrorMessage) Then
                ContractInfo.ShowDialog()
            End If
        End Using
    End Sub

#End Region

#Region "Public Methods"

    Public Sub New(ByVal ContractID As Guid, ByRef ErrorMessage As String)

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.

        ' Create a data set and the related table adapters.
        Dim ContractInfoDataSet As New DataSetContractInfo
        Dim ContractInfoAdapter As New DataSetContractInfoTableAdapters.ContractInfoTableAdapter
        Dim BurstInfoAdapter As New DataSetContractInfoTableAdapters.BurstInfoTableAdapter

        ' Create a connection for the table adapters.
        Using SqlCon As New SqlClient.SqlConnection(My.Settings.DBConnection)
            ContractInfoAdapter.Connection = SqlCon
            BurstInfoAdapter.Connection = SqlCon
            ' Fill the data set.
            Try
                ContractInfoAdapter.Fill(ContractInfoDataSet.Tables("ContractInfo"), ContractID)
                BurstInfoAdapter.Fill(ContractInfoDataSet.Tables("BurstInfo"), ContractID)
            Catch ex As Exception
                ErrorMessage = LiquidAgent.GetErrorMessage(ex)
                ShowMessage _
                ("A monkey slipped on a banana while trying to load data." & vbCrLf & vbCrLf _
                & ErrorMessage, "Data Load Error", MessageBoxIcon.Error)
                Exit Sub
            End Try
        End Using

        ' Destroy the table adapters.
        ContractInfoAdapter.Dispose()
        BurstInfoAdapter.Dispose()

        ' Create binding sources.
        Dim ContractInfoBindingSource As New BindingSource(ContractInfoDataSet, "ContractInfo")
        Dim BurstInfoBindingSource As New BindingSource(ContractInfoBindingSource, "ContractInfo_BurstInfo")

        ' Bind labels.
        LabelClientNameValue.DataBindings.Add("Text", ContractInfoBindingSource, "ClientName", False, DataSourceUpdateMode.Never, String.Empty)
        LabelProjectNameValue.DataBindings.Add("Text", ContractInfoBindingSource, "ProjectName", False, DataSourceUpdateMode.Never, String.Empty)
        LabelAccountManagerValue.DataBindings.Add("Text", ContractInfoBindingSource, "AccountManager", False, DataSourceUpdateMode.Never, String.Empty)
        LabelSignedByValue.DataBindings.Add("Text", ContractInfoBindingSource, "SignedBy", False, DataSourceUpdateMode.Never, String.Empty)
        LabelSignDateValue.DataBindings.Add("Text", ContractInfoBindingSource, "SignDate", True, DataSourceUpdateMode.Never, String.Empty)
        LabelCreatedByValue.DataBindings.Add("Text", ContractInfoBindingSource, "CreatedBy", False, DataSourceUpdateMode.Never, String.Empty)
        LabelCreationDateValue.DataBindings.Add("Text", ContractInfoBindingSource, "CreationDate", True, DataSourceUpdateMode.Never, String.Empty)
        LabelContractNumberValue.DataBindings.Add("Text", ContractInfoBindingSource, "ContractNumber", False, DataSourceUpdateMode.Never, String.Empty)

        ' Apply formatting.
        LabelSignDateValue.DataBindings("Text").FormatString = "d MMMM yyyy 'at' HH:mm"
        LabelCreationDateValue.DataBindings("Text").FormatString = "d MMMM yyyy 'at' HH:mm"

        ' Bind grid.
        GridItems.AutoGenerateColumns = False
        GridItems.DataSource = BurstInfoBindingSource

        ' Update the form title with the contract number.
        Text = "Contract Information - " & CType(ContractInfoBindingSource.Current, DataRowView).Item("ContractNumber")

        ' Setup a grid manager.
        Dim GridController As New GridManager _
        (GridItems, TextEditSearch, Nothing, String.Empty, PictureAdvancedSearch, PictureClearSearch, Nothing, Nothing)

    End Sub

#End Region

End Class
