﻿using DataAccess;

namespace DataService.Security
{
    class DeleteRolesCommandExecutor : CommandExecutor<DeleteRolesCommand>
    {

        public override void Execute(DeleteRolesCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.DeleteRoles))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.AddInputParameter("roles", command.Roles);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
            }
        }

    }
}
