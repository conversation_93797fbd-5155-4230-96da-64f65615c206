﻿namespace Framework.Controls.GridSystem
{
    partial class GridFiller
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.flatButtonSearch = new Framework.Controls.FlatButton();
            this.flatButtonClear = new Framework.Controls.FlatButton();
            this.SuspendLayout();
            // 
            // flatButtonSearch
            // 
            this.flatButtonSearch.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.flatButtonSearch.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            this.flatButtonSearch.Cursor = System.Windows.Forms.Cursors.Hand;
            this.flatButtonSearch.ErrorMessage = "";
            this.flatButtonSearch.FlatAppearance.BorderSize = 0;
            this.flatButtonSearch.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(30)))), ((int)(((byte)(158)))), ((int)(((byte)(158)))));
            this.flatButtonSearch.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Teal;
            this.flatButtonSearch.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.flatButtonSearch.ForeColor = System.Drawing.Color.White;
            this.flatButtonSearch.Location = new System.Drawing.Point(20, 291);
            this.flatButtonSearch.Margin = new System.Windows.Forms.Padding(11, 0, 20, 11);
            this.flatButtonSearch.Name = "flatButtonSearch";
            this.flatButtonSearch.Size = new System.Drawing.Size(180, 60);
            this.flatButtonSearch.TabIndex = 100;
            this.flatButtonSearch.Text = "Search";
            this.flatButtonSearch.UseVisualStyleBackColor = false;
            // 
            // flatButtonClear
            // 
            this.flatButtonClear.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.flatButtonClear.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            this.flatButtonClear.Cursor = System.Windows.Forms.Cursors.Hand;
            this.flatButtonClear.ErrorMessage = "";
            this.flatButtonClear.FlatAppearance.BorderSize = 0;
            this.flatButtonClear.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(30)))), ((int)(((byte)(158)))), ((int)(((byte)(158)))));
            this.flatButtonClear.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Teal;
            this.flatButtonClear.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.flatButtonClear.ForeColor = System.Drawing.Color.White;
            this.flatButtonClear.Location = new System.Drawing.Point(220, 291);
            this.flatButtonClear.Margin = new System.Windows.Forms.Padding(0, 0, 11, 11);
            this.flatButtonClear.Name = "flatButtonClear";
            this.flatButtonClear.Size = new System.Drawing.Size(180, 60);
            this.flatButtonClear.TabIndex = 101;
            this.flatButtonClear.Text = "Clear Search Criteria";
            this.flatButtonClear.UseVisualStyleBackColor = false;
            // 
            // GridFiller
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 13F);
            this.ClientSize = new System.Drawing.Size(420, 371);
            this.Controls.Add(this.flatButtonClear);
            this.Controls.Add(this.flatButtonSearch);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.Name = "GridFiller";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Search";
            this.ResumeLayout(false);

        }

        #endregion
        protected Controls.FlatButton flatButtonSearch;
        protected FlatButton flatButtonClear;
    }
}
