﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformMediaChannelGroup
    Inherits LiquidShell.Subform

    'UserControl overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer.  
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim SuperToolTip3 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem3 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem3 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip1 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem1 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem1 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle4 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle5 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle6 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Me.XtraTabControl1 = New DevExpress.XtraTab.XtraTabControl()
        Me.TabPageDetails = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.TableLayoutPanelDetails = New System.Windows.Forms.TableLayoutPanel()
        Me.PanelDetails = New System.Windows.Forms.Panel()
        Me.CheckEditDormant = New DevExpress.XtraEditors.CheckEdit()
        Me.TextEditMediaChannelName = New DevExpress.XtraEditors.TextEdit()
        Me.LabelGroupChainName = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl4 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl3 = New DevExpress.XtraEditors.LabelControl()
        Me.TabPageMediaGroups = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel3 = New System.Windows.Forms.Panel()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.GroupControlMediaCategory = New DevExpress.XtraEditors.GroupControl()
        Me.LabelControl14 = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearchMediaChannel = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearchMediaChannel = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearchMediaChannel = New DevExpress.XtraEditors.PictureEdit()
        Me.ButtonRemoveMediaGroupFromChannel = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAddMediaGroupToChannel = New DevExpress.XtraEditors.SimpleButton()
        Me.GridMediaGroup = New System.Windows.Forms.DataGridView()
        Me.MediaGroupNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.LabelControl9 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl10 = New DevExpress.XtraEditors.LabelControl()
        Me.ButtonSave = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton()
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabControl1.SuspendLayout()
        Me.TabPageDetails.SuspendLayout()
        Me.Panel1.SuspendLayout()
        Me.TableLayoutPanelDetails.SuspendLayout()
        Me.PanelDetails.SuspendLayout()
        CType(Me.CheckEditDormant.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditMediaChannelName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabPageMediaGroups.SuspendLayout()
        Me.Panel3.SuspendLayout()
        Me.TableLayoutPanel1.SuspendLayout()
        CType(Me.GroupControlMediaCategory, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlMediaCategory.SuspendLayout()
        CType(Me.TextEditSearchMediaChannel.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearchMediaChannel.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearchMediaChannel.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridMediaGroup, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!)
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Size = New System.Drawing.Size(347, 30)
        Me.LabelTitle.Text = "(New Media Channel Group)"
        '
        'XtraTabControl1
        '
        Me.XtraTabControl1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.XtraTabControl1.AppearancePage.Header.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.XtraTabControl1.AppearancePage.Header.Options.UseFont = True
        Me.XtraTabControl1.Location = New System.Drawing.Point(12, 57)
        Me.XtraTabControl1.LookAndFeel.SkinName = "Black"
        Me.XtraTabControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.XtraTabControl1.Name = "XtraTabControl1"
        Me.XtraTabControl1.SelectedTabPage = Me.TabPageDetails
        Me.XtraTabControl1.Size = New System.Drawing.Size(884, 437)
        Me.XtraTabControl1.TabIndex = 2
        Me.XtraTabControl1.TabPages.AddRange(New DevExpress.XtraTab.XtraTabPage() {Me.TabPageDetails, Me.TabPageMediaGroups})
        '
        'TabPageDetails
        '
        Me.TabPageDetails.Controls.Add(Me.Panel1)
        Me.TabPageDetails.Name = "TabPageDetails"
        Me.TabPageDetails.Size = New System.Drawing.Size(878, 409)
        Me.TabPageDetails.Text = "Details"
        '
        'Panel1
        '
        Me.Panel1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Panel1.Controls.Add(Me.TableLayoutPanelDetails)
        Me.Panel1.Location = New System.Drawing.Point(3, 18)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(861, 370)
        Me.Panel1.TabIndex = 3
        '
        'TableLayoutPanelDetails
        '
        Me.TableLayoutPanelDetails.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanelDetails.BackColor = System.Drawing.Color.Transparent
        Me.TableLayoutPanelDetails.ColumnCount = 1
        Me.TableLayoutPanelDetails.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanelDetails.Controls.Add(Me.PanelDetails, 0, 2)
        Me.TableLayoutPanelDetails.Controls.Add(Me.LabelControl4, 0, 0)
        Me.TableLayoutPanelDetails.Controls.Add(Me.LabelControl3, 0, 1)
        Me.TableLayoutPanelDetails.Location = New System.Drawing.Point(12, 12)
        Me.TableLayoutPanelDetails.Margin = New System.Windows.Forms.Padding(12)
        Me.TableLayoutPanelDetails.Name = "TableLayoutPanelDetails"
        Me.TableLayoutPanelDetails.RowCount = 3
        Me.TableLayoutPanelDetails.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanelDetails.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanelDetails.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanelDetails.Size = New System.Drawing.Size(837, 346)
        Me.TableLayoutPanelDetails.TabIndex = 3
        '
        'PanelDetails
        '
        Me.PanelDetails.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelDetails.Controls.Add(Me.CheckEditDormant)
        Me.PanelDetails.Controls.Add(Me.TextEditMediaChannelName)
        Me.PanelDetails.Controls.Add(Me.LabelGroupChainName)
        Me.PanelDetails.Location = New System.Drawing.Point(0, 48)
        Me.PanelDetails.Margin = New System.Windows.Forms.Padding(0)
        Me.PanelDetails.Name = "PanelDetails"
        Me.PanelDetails.Size = New System.Drawing.Size(837, 298)
        Me.PanelDetails.TabIndex = 2
        '
        'CheckEditDormant
        '
        Me.CheckEditDormant.Location = New System.Drawing.Point(135, 29)
        Me.CheckEditDormant.Name = "CheckEditDormant"
        Me.CheckEditDormant.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditDormant.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditDormant.Properties.Appearance.Options.UseFont = True
        Me.CheckEditDormant.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditDormant.Properties.AutoWidth = True
        Me.CheckEditDormant.Properties.Caption = "This media channel is dormant"
        Me.CheckEditDormant.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditDormant.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditDormant.Size = New System.Drawing.Size(197, 19)
        Me.CheckEditDormant.TabIndex = 2
        '
        'TextEditMediaChannelName
        '
        Me.TextEditMediaChannelName.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditMediaChannelName.Location = New System.Drawing.Point(137, 3)
        Me.TextEditMediaChannelName.Name = "TextEditMediaChannelName"
        Me.TextEditMediaChannelName.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditMediaChannelName.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditMediaChannelName.Properties.Appearance.Options.UseFont = True
        Me.TextEditMediaChannelName.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditMediaChannelName.Properties.MaxLength = 200
        Me.TextEditMediaChannelName.Size = New System.Drawing.Size(697, 20)
        Me.TextEditMediaChannelName.TabIndex = 1
        '
        'LabelGroupChainName
        '
        Me.LabelGroupChainName.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelGroupChainName.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelGroupChainName.Location = New System.Drawing.Point(5, 6)
        Me.LabelGroupChainName.Name = "LabelGroupChainName"
        Me.LabelGroupChainName.Size = New System.Drawing.Size(126, 13)
        Me.LabelGroupChainName.TabIndex = 0
        Me.LabelGroupChainName.Text = "Media Channel Name:"
        '
        'LabelControl4
        '
        Me.LabelControl4.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl4.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl4.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelControl4.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl4.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelControl4.LineVisible = True
        Me.LabelControl4.Location = New System.Drawing.Point(3, 3)
        Me.LabelControl4.Margin = New System.Windows.Forms.Padding(3, 3, 3, 12)
        Me.LabelControl4.Name = "LabelControl4"
        Me.LabelControl4.Size = New System.Drawing.Size(831, 18)
        Me.LabelControl4.TabIndex = 0
        Me.LabelControl4.Text = "Media Channel Details"
        '
        'LabelControl3
        '
        Me.LabelControl3.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl3.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl3.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl3.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top
        Me.LabelControl3.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap
        Me.LabelControl3.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelControl3.Location = New System.Drawing.Point(3, 36)
        Me.LabelControl3.Margin = New System.Windows.Forms.Padding(3, 3, 3, 12)
        Me.LabelControl3.Name = "LabelControl3"
        Me.LabelControl3.Size = New System.Drawing.Size(831, 0)
        Me.LabelControl3.TabIndex = 1
        '
        'TabPageMediaGroups
        '
        Me.TabPageMediaGroups.Controls.Add(Me.Panel3)
        Me.TabPageMediaGroups.Name = "TabPageMediaGroups"
        Me.TabPageMediaGroups.Size = New System.Drawing.Size(878, 409)
        Me.TabPageMediaGroups.Text = "Media Groups (Add To Channel)"
        '
        'Panel3
        '
        Me.Panel3.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel3.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Panel3.Controls.Add(Me.TableLayoutPanel1)
        Me.Panel3.Location = New System.Drawing.Point(3, 3)
        Me.Panel3.Name = "Panel3"
        Me.Panel3.Size = New System.Drawing.Size(884, 399)
        Me.Panel3.TabIndex = 4
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanel1.BackColor = System.Drawing.Color.Transparent
        Me.TableLayoutPanel1.ColumnCount = 1
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.LabelControl1, 0, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.GroupControlMediaCategory, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.LabelControl9, 0, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.LabelControl10, 0, 1)
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(12, 12)
        Me.TableLayoutPanel1.Margin = New System.Windows.Forms.Padding(12)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 4
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 15.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(860, 375)
        Me.TableLayoutPanel1.TabIndex = 3
        '
        'LabelControl1
        '
        Me.LabelControl1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl1.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl1.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top
        Me.LabelControl1.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap
        Me.LabelControl1.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelControl1.Location = New System.Drawing.Point(3, 363)
        Me.LabelControl1.Margin = New System.Windows.Forms.Padding(3, 3, 3, 12)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(854, 13)
        Me.LabelControl1.TabIndex = 6
        Me.LabelControl1.Text = "This section allows you to add or remove the Media Group to channel."
        '
        'GroupControlMediaCategory
        '
        Me.GroupControlMediaCategory.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlMediaCategory.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlMediaCategory.Appearance.Options.UseFont = True
        Me.GroupControlMediaCategory.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlMediaCategory.AppearanceCaption.Options.UseFont = True
        Me.GroupControlMediaCategory.Controls.Add(Me.LabelControl14)
        Me.GroupControlMediaCategory.Controls.Add(Me.TextEditSearchMediaChannel)
        Me.GroupControlMediaCategory.Controls.Add(Me.PictureClearSearchMediaChannel)
        Me.GroupControlMediaCategory.Controls.Add(Me.PictureAdvancedSearchMediaChannel)
        Me.GroupControlMediaCategory.Controls.Add(Me.ButtonRemoveMediaGroupFromChannel)
        Me.GroupControlMediaCategory.Controls.Add(Me.ButtonAddMediaGroupToChannel)
        Me.GroupControlMediaCategory.Controls.Add(Me.GridMediaGroup)
        Me.GroupControlMediaCategory.Location = New System.Drawing.Point(3, 51)
        Me.GroupControlMediaCategory.LookAndFeel.SkinName = "Black"
        Me.GroupControlMediaCategory.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlMediaCategory.Name = "GroupControlMediaCategory"
        Me.GroupControlMediaCategory.Size = New System.Drawing.Size(854, 306)
        Me.GroupControlMediaCategory.TabIndex = 5
        Me.GroupControlMediaCategory.Text = "Media Groups in this Channel"
        '
        'LabelControl14
        '
        Me.LabelControl14.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl14.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl14.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl14.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl14.Location = New System.Drawing.Point(696, 282)
        Me.LabelControl14.Name = "LabelControl14"
        Me.LabelControl14.Size = New System.Drawing.Size(45, 13)
        Me.LabelControl14.TabIndex = 4
        Me.LabelControl14.Text = "Search:"
        '
        'TextEditSearchMediaChannel
        '
        Me.TextEditSearchMediaChannel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchMediaChannel.EditValue = ""
        Me.TextEditSearchMediaChannel.Location = New System.Drawing.Point(747, 279)
        Me.TextEditSearchMediaChannel.Margin = New System.Windows.Forms.Padding(3, 4, 3, 5)
        Me.TextEditSearchMediaChannel.Name = "TextEditSearchMediaChannel"
        Me.TextEditSearchMediaChannel.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchMediaChannel.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchMediaChannel.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchMediaChannel.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchMediaChannel.Size = New System.Drawing.Size(80, 20)
        Me.TextEditSearchMediaChannel.TabIndex = 5
        '
        'PictureClearSearchMediaChannel
        '
        Me.PictureClearSearchMediaChannel.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchMediaChannel.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchMediaChannel.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchMediaChannel.Location = New System.Drawing.Point(833, 3)
        Me.PictureClearSearchMediaChannel.Name = "PictureClearSearchMediaChannel"
        Me.PictureClearSearchMediaChannel.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchMediaChannel.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchMediaChannel.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchMediaChannel.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem3.Text = "Clear Search"
        ToolTipItem3.LeftIndent = 6
        ToolTipItem3.Text = "Click here to clear all search boxes."
        SuperToolTip3.Items.Add(ToolTipTitleItem3)
        SuperToolTip3.Items.Add(ToolTipItem3)
        Me.PictureClearSearchMediaChannel.SuperTip = SuperToolTip3
        Me.PictureClearSearchMediaChannel.TabIndex = 0
        Me.PictureClearSearchMediaChannel.TabStop = True
        '
        'PictureAdvancedSearchMediaChannel
        '
        Me.PictureAdvancedSearchMediaChannel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearchMediaChannel.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearchMediaChannel.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearchMediaChannel.Location = New System.Drawing.Point(833, 281)
        Me.PictureAdvancedSearchMediaChannel.Margin = New System.Windows.Forms.Padding(3, 6, 3, 7)
        Me.PictureAdvancedSearchMediaChannel.Name = "PictureAdvancedSearchMediaChannel"
        Me.PictureAdvancedSearchMediaChannel.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearchMediaChannel.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearchMediaChannel.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearchMediaChannel.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem1.Text = "Advanced Search"
        ToolTipItem1.LeftIndent = 6
        ToolTipItem1.Text = "Click here to search individual column values."
        SuperToolTip1.Items.Add(ToolTipTitleItem1)
        SuperToolTip1.Items.Add(ToolTipItem1)
        Me.PictureAdvancedSearchMediaChannel.SuperTip = SuperToolTip1
        Me.PictureAdvancedSearchMediaChannel.TabIndex = 6
        Me.PictureAdvancedSearchMediaChannel.TabStop = True
        '
        'ButtonRemoveMediaGroupFromChannel
        '
        Me.ButtonRemoveMediaGroupFromChannel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonRemoveMediaGroupFromChannel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonRemoveMediaGroupFromChannel.Appearance.Options.UseFont = True
        Me.ButtonRemoveMediaGroupFromChannel.ImageIndex = 2
        Me.ButtonRemoveMediaGroupFromChannel.Location = New System.Drawing.Point(86, 278)
        Me.ButtonRemoveMediaGroupFromChannel.LookAndFeel.SkinName = "Black"
        Me.ButtonRemoveMediaGroupFromChannel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonRemoveMediaGroupFromChannel.Name = "ButtonRemoveMediaGroupFromChannel"
        Me.ButtonRemoveMediaGroupFromChannel.Size = New System.Drawing.Size(75, 23)
        Me.ButtonRemoveMediaGroupFromChannel.TabIndex = 3
        Me.ButtonRemoveMediaGroupFromChannel.Text = "Remove"
        '
        'ButtonAddMediaGroupToChannel
        '
        Me.ButtonAddMediaGroupToChannel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAddMediaGroupToChannel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAddMediaGroupToChannel.Appearance.Options.UseFont = True
        Me.ButtonAddMediaGroupToChannel.ImageIndex = 0
        Me.ButtonAddMediaGroupToChannel.Location = New System.Drawing.Point(5, 278)
        Me.ButtonAddMediaGroupToChannel.LookAndFeel.SkinName = "Black"
        Me.ButtonAddMediaGroupToChannel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAddMediaGroupToChannel.Name = "ButtonAddMediaGroupToChannel"
        Me.ButtonAddMediaGroupToChannel.Size = New System.Drawing.Size(75, 23)
        Me.ButtonAddMediaGroupToChannel.TabIndex = 2
        Me.ButtonAddMediaGroupToChannel.Text = "Add"
        '
        'GridMediaGroup
        '
        Me.GridMediaGroup.AllowUserToAddRows = False
        Me.GridMediaGroup.AllowUserToDeleteRows = False
        Me.GridMediaGroup.AllowUserToOrderColumns = True
        Me.GridMediaGroup.AllowUserToResizeRows = False
        DataGridViewCellStyle4.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridMediaGroup.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle4
        Me.GridMediaGroup.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridMediaGroup.BackgroundColor = System.Drawing.Color.White
        Me.GridMediaGroup.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridMediaGroup.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridMediaGroup.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle5.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle5.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle5.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle5.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle5.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle5.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle5.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridMediaGroup.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle5
        Me.GridMediaGroup.ColumnHeadersHeight = 22
        Me.GridMediaGroup.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridMediaGroup.ColumnHeadersVisible = False
        Me.GridMediaGroup.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.MediaGroupNameColumn})
        Me.GridMediaGroup.EnableHeadersVisualStyles = False
        Me.GridMediaGroup.GridColor = System.Drawing.Color.White
        Me.GridMediaGroup.Location = New System.Drawing.Point(2, 22)
        Me.GridMediaGroup.Name = "GridMediaGroup"
        Me.GridMediaGroup.ReadOnly = True
        Me.GridMediaGroup.RowHeadersVisible = False
        DataGridViewCellStyle6.ForeColor = System.Drawing.Color.DimGray
        Me.GridMediaGroup.RowsDefaultCellStyle = DataGridViewCellStyle6
        Me.GridMediaGroup.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridMediaGroup.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridMediaGroup.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridMediaGroup.RowTemplate.Height = 19
        Me.GridMediaGroup.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridMediaGroup.ShowCellToolTips = False
        Me.GridMediaGroup.Size = New System.Drawing.Size(850, 251)
        Me.GridMediaGroup.StandardTab = True
        Me.GridMediaGroup.TabIndex = 1
        '
        'MediaGroupNameColumn
        '
        Me.MediaGroupNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.MediaGroupNameColumn.DataPropertyName = "MediaGroupName"
        Me.MediaGroupNameColumn.HeaderText = "Media Group Name"
        Me.MediaGroupNameColumn.Name = "MediaGroupNameColumn"
        Me.MediaGroupNameColumn.ReadOnly = True
        '
        'LabelControl9
        '
        Me.LabelControl9.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl9.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl9.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelControl9.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl9.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelControl9.LineVisible = True
        Me.LabelControl9.Location = New System.Drawing.Point(3, 3)
        Me.LabelControl9.Margin = New System.Windows.Forms.Padding(3, 3, 3, 12)
        Me.LabelControl9.Name = "LabelControl9"
        Me.LabelControl9.Size = New System.Drawing.Size(854, 18)
        Me.LabelControl9.TabIndex = 3
        Me.LabelControl9.Text = "Media Channel Management"
        '
        'LabelControl10
        '
        Me.LabelControl10.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl10.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl10.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl10.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top
        Me.LabelControl10.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap
        Me.LabelControl10.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelControl10.Location = New System.Drawing.Point(3, 36)
        Me.LabelControl10.Margin = New System.Windows.Forms.Padding(3, 3, 3, 12)
        Me.LabelControl10.Name = "LabelControl10"
        Me.LabelControl10.Size = New System.Drawing.Size(854, 0)
        Me.LabelControl10.TabIndex = 4
        '
        'ButtonSave
        '
        Me.ButtonSave.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonSave.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonSave.Appearance.Options.UseFont = True
        Me.ButtonSave.Image = Global.Nova2.My.Resources.Resources.accept16
        Me.ButtonSave.ImageIndex = 1
        Me.ButtonSave.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonSave.Location = New System.Drawing.Point(686, 504)
        Me.ButtonSave.LookAndFeel.SkinName = "Black"
        Me.ButtonSave.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonSave.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonSave.Name = "ButtonSave"
        Me.ButtonSave.Size = New System.Drawing.Size(100, 28)
        Me.ButtonSave.TabIndex = 29
        Me.ButtonSave.Text = "Save"
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.Image = Global.Nova2.My.Resources.Resources.delete16
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.Location = New System.Drawing.Point(792, 504)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(100, 28)
        Me.ButtonCancel.TabIndex = 30
        Me.ButtonCancel.Text = "Cancel"
        '
        'SubformMediaChannelGroup
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.Controls.Add(Me.ButtonSave)
        Me.Controls.Add(Me.ButtonCancel)
        Me.Controls.Add(Me.XtraTabControl1)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Name = "SubformMediaChannelGroup"
        Me.Size = New System.Drawing.Size(908, 549)
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        Me.Controls.SetChildIndex(Me.XtraTabControl1, 0)
        Me.Controls.SetChildIndex(Me.ButtonCancel, 0)
        Me.Controls.SetChildIndex(Me.ButtonSave, 0)
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabControl1.ResumeLayout(False)
        Me.TabPageDetails.ResumeLayout(False)
        Me.Panel1.ResumeLayout(False)
        Me.TableLayoutPanelDetails.ResumeLayout(False)
        Me.PanelDetails.ResumeLayout(False)
        Me.PanelDetails.PerformLayout()
        CType(Me.CheckEditDormant.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditMediaChannelName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabPageMediaGroups.ResumeLayout(False)
        Me.Panel3.ResumeLayout(False)
        Me.TableLayoutPanel1.ResumeLayout(False)
        CType(Me.GroupControlMediaCategory, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlMediaCategory.ResumeLayout(False)
        CType(Me.TextEditSearchMediaChannel.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearchMediaChannel.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearchMediaChannel.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridMediaGroup, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub

    Friend WithEvents XtraTabControl1 As DevExpress.XtraTab.XtraTabControl
    Friend WithEvents TabPageDetails As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel1 As Panel
    Friend WithEvents TableLayoutPanelDetails As TableLayoutPanel
    Friend WithEvents PanelDetails As Panel
    Friend WithEvents CheckEditDormant As CheckEdit
    Friend WithEvents TextEditMediaChannelName As TextEdit
    Friend WithEvents LabelGroupChainName As LabelControl
    Friend WithEvents LabelControl4 As LabelControl
    Friend WithEvents LabelControl3 As LabelControl
    Friend WithEvents TabPageMediaGroups As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel3 As Panel
    Friend WithEvents TableLayoutPanel1 As TableLayoutPanel
    Friend WithEvents LabelControl1 As LabelControl
    Friend WithEvents GroupControlMediaCategory As GroupControl
    Friend WithEvents LabelControl14 As LabelControl
    Friend WithEvents TextEditSearchMediaChannel As TextEdit
    Friend WithEvents PictureClearSearchMediaChannel As PictureEdit
    Friend WithEvents PictureAdvancedSearchMediaChannel As PictureEdit
    Friend WithEvents ButtonRemoveMediaGroupFromChannel As SimpleButton
    Friend WithEvents ButtonAddMediaGroupToChannel As SimpleButton
    Friend WithEvents GridMediaGroup As DataGridView
    Friend WithEvents LabelControl9 As LabelControl
    Friend WithEvents LabelControl10 As LabelControl
    Friend WithEvents ButtonSave As SimpleButton
    Friend WithEvents ButtonCancel As SimpleButton
    Friend WithEvents MediaGroupNameColumn As DataGridViewTextBoxColumn
End Class
