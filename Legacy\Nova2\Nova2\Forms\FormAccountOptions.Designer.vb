﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class FormAccountOptions
    Inherits EditorForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(FormAccountOptions))
        Me.ImageList16x16 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonPassword = New DevExpress.XtraEditors.SimpleButton
        Me.LabelPassword = New DevExpress.XtraEditors.LabelControl
        Me.LabelControlHeadingTasks = New DevExpress.XtraEditors.LabelControl
        Me.ButtonDetails = New DevExpress.XtraEditors.SimpleButton
        Me.LabelDetails = New DevExpress.XtraEditors.LabelControl
        Me.ButtonPermissions = New DevExpress.XtraEditors.SimpleButton
        Me.LabelPermissions = New DevExpress.XtraEditors.LabelControl
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'PanelButtonBar
        '
        Me.PanelButtonBar.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelButtonBar.Appearance.BackColor2 = System.Drawing.Color.LightSteelBlue
        Me.PanelButtonBar.Appearance.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical
        Me.PanelButtonBar.Appearance.Options.UseBackColor = True
        Me.PanelButtonBar.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Office2003
        Me.PanelButtonBar.LookAndFeel.UseDefaultLookAndFeel = False
        Me.PanelButtonBar.Size = New System.Drawing.Size(519, 52)
        Me.PanelButtonBar.TabIndex = 7
        '
        'ImageList16x16
        '
        Me.ImageList16x16.ImageStream = CType(resources.GetObject("ImageList16x16.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList16x16.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList16x16.Images.SetKeyName(0, "key.png")
        Me.ImageList16x16.Images.SetKeyName(1, "pencil.png")
        Me.ImageList16x16.Images.SetKeyName(2, "security.png")
        Me.ImageList16x16.Images.SetKeyName(3, "unlock.png")
        '
        'ButtonPassword
        '
        Me.ButtonPassword.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonPassword.Appearance.Options.UseFont = True
        Me.ButtonPassword.ImageIndex = 0
        Me.ButtonPassword.ImageList = Me.ImageList16x16
        Me.ButtonPassword.Location = New System.Drawing.Point(12, 45)
        Me.ButtonPassword.LookAndFeel.SkinName = "Black"
        Me.ButtonPassword.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonPassword.Name = "ButtonPassword"
        Me.ButtonPassword.Size = New System.Drawing.Size(100, 23)
        Me.ButtonPassword.TabIndex = 1
        Me.ButtonPassword.Text = "Password"
        '
        'LabelPassword
        '
        Me.LabelPassword.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelPassword.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelPassword.Location = New System.Drawing.Point(127, 50)
        Me.LabelPassword.Margin = New System.Windows.Forms.Padding(12, 3, 3, 3)
        Me.LabelPassword.Name = "LabelPassword"
        Me.LabelPassword.Size = New System.Drawing.Size(128, 13)
        Me.LabelPassword.TabIndex = 2
        Me.LabelPassword.Text = "Change my password."
        '
        'LabelControlHeadingTasks
        '
        Me.LabelControlHeadingTasks.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControlHeadingTasks.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControlHeadingTasks.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelControlHeadingTasks.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControlHeadingTasks.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelControlHeadingTasks.LineVisible = True
        Me.LabelControlHeadingTasks.Location = New System.Drawing.Point(12, 12)
        Me.LabelControlHeadingTasks.Margin = New System.Windows.Forms.Padding(3, 3, 3, 12)
        Me.LabelControlHeadingTasks.Name = "LabelControlHeadingTasks"
        Me.LabelControlHeadingTasks.Size = New System.Drawing.Size(495, 18)
        Me.LabelControlHeadingTasks.TabIndex = 0
        Me.LabelControlHeadingTasks.Text = "Select a task to perform"
        '
        'ButtonDetails
        '
        Me.ButtonDetails.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonDetails.Appearance.Options.UseFont = True
        Me.ButtonDetails.ImageIndex = 1
        Me.ButtonDetails.ImageList = Me.ImageList16x16
        Me.ButtonDetails.Location = New System.Drawing.Point(12, 74)
        Me.ButtonDetails.LookAndFeel.SkinName = "Black"
        Me.ButtonDetails.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonDetails.Name = "ButtonDetails"
        Me.ButtonDetails.Size = New System.Drawing.Size(100, 23)
        Me.ButtonDetails.TabIndex = 3
        Me.ButtonDetails.Text = "Details"
        Me.ButtonDetails.Visible = False
        '
        'LabelDetails
        '
        Me.LabelDetails.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelDetails.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelDetails.Location = New System.Drawing.Point(127, 79)
        Me.LabelDetails.Margin = New System.Windows.Forms.Padding(12, 3, 3, 3)
        Me.LabelDetails.Name = "LabelDetails"
        Me.LabelDetails.Size = New System.Drawing.Size(337, 13)
        Me.LabelDetails.TabIndex = 4
        Me.LabelDetails.Text = "Modify my name, email address or account manager code."
        Me.LabelDetails.Visible = False
        '
        'ButtonPermissions
        '
        Me.ButtonPermissions.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonPermissions.Appearance.Options.UseFont = True
        Me.ButtonPermissions.ImageIndex = 3
        Me.ButtonPermissions.ImageList = Me.ImageList16x16
        Me.ButtonPermissions.Location = New System.Drawing.Point(12, 103)
        Me.ButtonPermissions.LookAndFeel.SkinName = "Black"
        Me.ButtonPermissions.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonPermissions.Name = "ButtonPermissions"
        Me.ButtonPermissions.Size = New System.Drawing.Size(100, 23)
        Me.ButtonPermissions.TabIndex = 5
        Me.ButtonPermissions.Text = "Permissions"
        Me.ButtonPermissions.Visible = False
        '
        'LabelPermissions
        '
        Me.LabelPermissions.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelPermissions.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelPermissions.Location = New System.Drawing.Point(127, 108)
        Me.LabelPermissions.Margin = New System.Windows.Forms.Padding(12, 3, 3, 3)
        Me.LabelPermissions.Name = "LabelPermissions"
        Me.LabelPermissions.Size = New System.Drawing.Size(218, 13)
        Me.LabelPermissions.TabIndex = 6
        Me.LabelPermissions.Text = "Manage permissions for my contracts."
        Me.LabelPermissions.Visible = False
        '
        'FormAccountOptions
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(519, 316)
        Me.Controls.Add(Me.LabelControlHeadingTasks)
        Me.Controls.Add(Me.LabelPermissions)
        Me.Controls.Add(Me.LabelDetails)
        Me.Controls.Add(Me.LabelPassword)
        Me.Controls.Add(Me.ButtonPermissions)
        Me.Controls.Add(Me.ButtonDetails)
        Me.Controls.Add(Me.ButtonPassword)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.MaximizeBox = False
        Me.MinimumSize = New System.Drawing.Size(401, 215)
        Me.Name = "FormAccountOptions"
        Me.Text = "Account Options"
        Me.Controls.SetChildIndex(Me.PanelButtonBar, 0)
        Me.Controls.SetChildIndex(Me.ButtonPassword, 0)
        Me.Controls.SetChildIndex(Me.ButtonDetails, 0)
        Me.Controls.SetChildIndex(Me.ButtonPermissions, 0)
        Me.Controls.SetChildIndex(Me.LabelPassword, 0)
        Me.Controls.SetChildIndex(Me.LabelDetails, 0)
        Me.Controls.SetChildIndex(Me.LabelPermissions, 0)
        Me.Controls.SetChildIndex(Me.LabelControlHeadingTasks, 0)
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents ImageList16x16 As System.Windows.Forms.ImageList
    Friend WithEvents ButtonPassword As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelPassword As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControlHeadingTasks As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ButtonDetails As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelDetails As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ButtonPermissions As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelPermissions As DevExpress.XtraEditors.LabelControl
End Class
