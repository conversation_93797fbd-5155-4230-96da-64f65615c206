﻿namespace DataAccess
{

    /// <summary>
    /// This class serves as a handy storage area for all the stored procedures that are needed on the SQL server.
    /// </summary>
    public static class StoredProcedureNames
    {

        #region API

        public static string GetCorrectPasswordHash { get { return "security.sp_getuserpasswordhash"; } }
        public static string GetUserAccount { get { return "security.sp_getuseraccount"; } }
        public static string CreateNewSession { get { return "security.sp_getnewsessionid"; } }
        public static string GetPasswordResetInfo { get { return "security.sp_getpasswordresetinfo"; } }
        public static string ResetPassword { get { return "security.sp_resetpassword"; } }
        public static string GetEmailVerificationCode { get { return "security.sp_getemailverificationcode"; } }
        public static string GetForgottenUsername { get { return "security.sp_getforgottenusername"; } }
        public static string GetUsernameFromSession { get { return "security.sp_getusernamefromsession"; } }
        public static string CreateUser { get { return "security.sp_createuser"; } }
        public static string ChangePassword { get { return "security.sp_changepassword"; } }
        public static string GetPreviouslyUsedPasswords { get { return "security.sp_getpreviouslyusedpasswords"; } }
        public static string GetMailServer { get { return "system.sp_getmailserver"; } }
    

        #endregion


        #region Security

        public static string GetTermsOfUseText { get { return "security.sp_getlatesttermsofuse"; } }
        public static string SaveAcceptanceOfTermsOfUse { get { return "security.sp_saveacceptanceoftermsofuse"; } }
        public static string CheckRoleMembershipOfUser { get { return "security.sp_isinrole"; } }
        public static string GetUserTable { get { return "security.sp_getusers"; } }
        public static string GetRolesOfMember { get { return "security.sp_getrolesofmember"; } }
        public static string GetRolesOfMemberCandidates { get { return "security.sp_getroles_asrolesofmembercandidates"; } }
        public static string AddRolesOfMember { get { return "security.sp_addrolesofmember"; } }
        public static string RemoveRolesOfMember { get { return "security.sp_removerolesofmember"; } }
        public static string GetRolesOfOwner { get { return "security.sp_getrolesofowner"; } }

        public static string GetChainPermissionCandidates { get { return "security.GetChainPermissionCandidates"; } }


        public static string GetRolesOfOwnerCandidates { get { return "security.sp_getroles_asrolesofownercandidates"; } }
        public static string AddRolesOfOwner { get { return "security.sp_addrolesofowner"; } }
        public static string RemoveRolesOfOwner { get { return "security.sp_removerolesofowner"; } }
        public static string GetUserHistory { get { return "security.sp_getuserhistory"; } }
        public static string GetUserAuditTrail { get { return "security.sp_getuseraudittrail"; } }
        public static string UpdateUser { get { return "security.sp_updateuser"; } }
        public static string UpdateUserThemeColor { get { return "security.sp_updatethemecolor"; } }
        public static string DeleteUsers { get { return "security.sp_deleteusers"; } }

        public static string CreateRole { get { return "security.sp_createrole"; } }
        public static string UpdateRole { get { return "security.sp_updaterole"; } }
        public static string GetRoleTable { get { return "security.sp_getroles"; } }
        public static string DeleteRoles { get { return "security.sp_deleteroles"; } }
        public static string GetRoleHistory { get { return "security.sp_getrolehistory"; } }
        public static string GetRoleMembers { get { return "security.sp_getrolemembers"; } }
        public static string GetRoleMemberCandidates { get { return "security.sp_getusers_asrolemembercandidates"; } }
        public static string AddRoleMembers { get { return "security.sp_addrolemembers"; } }
        public static string RemoveRoleMembers { get { return "security.sp_removerolemembers"; } }

        public static string DeleteUserChainPermissions { get { return "security.DeleteUserChainPermissions"; } }

        public static string GetRoleOwners { get { return "security.sp_getroleowners"; } }
        public static string GetRoleOwnerCandidates { get { return "security.sp_getusers_asroleownercandidates"; } }
        public static string AddRoleOwners { get { return "security.sp_addroleowners"; } }
        public static string RemoveRoleOwners { get { return "security.sp_removeroleowners"; } }
        public static string TerminateSession { get { return "security.sp_terminatesession"; } }

        public static string GetUserChainPermissions { get { return "security.GetUserChainPermissions"; } }
        public static string SetUserChainPermissions { get { return "security.SetUserChainPermissions"; } }

        public static string GetUserRoles { get { return "security.proc_Scanner_sel_GetUserRoles_Nova"; } }

        #endregion


        #region System

        public static string GetSystemSettingsTable { get { return "system.sp_getsystemsettings"; } }
        public static string UpdateSystemSetting { get { return "system.sp_updatesystemsetting"; } }

        #endregion


        #region Finance

        public static string GetStorePaymentTable { get { return "Finance.sp_getstorepayments"; } }

        #endregion


        #region Sales

        public static string GetMediaServiceTable { get { return "Sales.sp_getmediaservices"; } }
        public static string GetCategoryTable { get { return "Sales.sp_getcategories"; } }
        public static string GetContractTable { get { return "Sales.sp_getcontracts"; } }

        #endregion


        #region Campaign Review

        public static string ImportScannerData { get { return "campaignreview.sp_importclicksscans"; } }
        public static string SetScanImportFileAsCompleted { get { return "campaignreview.sp_setscanimportfilecompleted"; } }
        public static string SaveScannerDataImportFileProperties { get { return "campaignreview.sp_savescanimportfileproperties"; } }
        public static string CancelScannerDataFilesImport { get { return "campaignreview.sp_cancelscanfilesimport"; } }
        public static string GetScanFileTable { get { return "campaignreview.sp_getscanfiles"; } }
        public static string DeleteScanFiles { get { return "campaignreview.sp_deletescanfiles"; } }

        #endregion


        #region Store Universe

        public static string GetStoreTable { get { return "TradeRights.sp_getstores"; } }
        public static string GetChainTable { get { return "TradeRights.sp_getchains"; } }
        public static string GetHeadOfficeTable { get { return "TradeRights.sp_getheadoffices"; } }

        #endregion


        #region Legacy stored procedures

        public static string CreateSqlLogin { get { return "security.sp_create_sqllogin"; } }

        public static string AddLegacyRoleMembers { get { return "integration.sp_addrolemembers"; } }
        public static string RemoveLegacyRoleMembers { get { return "integration.sp_removerolemembers"; } }

        public static string AddLegacyRolesOfMember { get { return "integration.sp_addrolesofmember"; } }
        public static string RemoveLegacyRolesOfMember { get { return "integration.sp_removerolesofmember"; } }

        public static string DisableLegacyLogins { get { return "integration.sp_disablelogins"; } }
        public static string EnableLegacyLogins { get { return "integration.sp_enablelogins"; } }

        public static string GetContractStatus { get { return "Sales.sp_getcontractstatus"; } }

        #endregion

    }


}
