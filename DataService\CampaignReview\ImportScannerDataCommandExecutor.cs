﻿using DataAccess;
using System.Collections.Generic;
using System.IO;
using System.ComponentModel;
using System;
using System.Data;

namespace DataService.CampaignReview
{
    class ImportScannerDataCommandExecutor : CommandExecutor<ImportScannerDataCommand>
    {
        private BackgroundWorker Worker;

        public ImportScannerDataCommandExecutor(BackgroundWorker worker)
        {
            Worker = worker;
        }

        public override void Execute(ImportScannerDataCommand command)
        {
            Worker.ReportProgress(1);

            SaveFilePropertiesOfArchives(command);
            Worker.ReportProgress(2);
            if (Worker.CancellationPending) CancelScannerDataImport(command);

            ExtractDataFilesFromSpecifiedArchives(command);
            Worker.ReportProgress(3);
            if (Worker.CancellationPending) CancelScannerDataImport(command);

            ConvertEachDataFileToDataTableAndSaveItInTheDatabase(command, 4);
            if (Worker.CancellationPending) CancelScannerDataImport(command);

            SetCompletedToTrueForAllArchiveFiles(command);
            Worker.ReportProgress(100);
        }

        private void SaveFilePropertiesOfArchives(ImportScannerDataCommand command)
        {
            if (string.IsNullOrEmpty(command.ErrorMessage) == false)
            {
                return;
            }

            foreach (string filenameandpath in command.ArchiveFileNames)
            {
                string filename = Path.GetFileName(filenameandpath);
                string filepath = Path.GetDirectoryName(filenameandpath);
                int filesize = command.ArchiveFileSizes[filenameandpath];

                using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.SaveScannerDataImportFileProperties))
                {
                    storedprocedure.AddInputParameter("sessionid", command.SessionId);
                    storedprocedure.AddInputParameter("scanimportfilename", filename);
                    storedprocedure.AddInputParameter("scanimportfilepath", filepath);
                    storedprocedure.AddInputParameter("scanimportfilesize", filesize);
                    storedprocedure.AddOutputParameter<int>("scanimportfileid");
                    storedprocedure.Execute();
                    command.ErrorMessage = storedprocedure.ErrorMessage;

                    if (string.IsNullOrEmpty(command.ErrorMessage))
                    {
                        command.ArchiveFileIds[filenameandpath] = (int)storedprocedure.GetOutputParameterValue("scanimportfileid");
                    }
                }
            }
        }

        private void ExtractDataFilesFromSpecifiedArchives(ImportScannerDataCommand command)
        {
            if (string.IsNullOrEmpty(command.ErrorMessage) == false)
            {
                return;
            }

            string destinationfolderpath = command.TempFilePathForAchiveDecompression;
            if (Directory.Exists(destinationfolderpath))
            {
                Directory.Delete(destinationfolderpath, true);
            }
            Directory.CreateDirectory(destinationfolderpath);
            ArchiveDecompressor.ExtractFiles(ref command.ErrorMessage, command.ArchiveFileIds, destinationfolderpath);
            if (Worker.CancellationPending) CancelScannerDataImport(command);
        }

        private void ConvertEachDataFileToDataTableAndSaveItInTheDatabase(ImportScannerDataCommand command, float workerprogressindicator)
        {
            if (string.IsNullOrEmpty(command.ErrorMessage) == false)
            {
                return;
            }

            string errormessage = string.Empty;
            List<string> extractedfilepathlist = GetExtractedFilePathList(ref errormessage, command.TempFilePathForAchiveDecompression);
            float workerprogressincrement = (float)(100 - workerprogressindicator) / extractedfilepathlist.Count;

            foreach (string extractedfilepath in extractedfilepathlist)
            {
                if (string.IsNullOrEmpty(command.ErrorMessage) == false)
                {
                    return;
                }

                // Get the archive file's ID.
                int archivefileid = GetArchiveFileIdFromExtractedFilePath(command, extractedfilepath);

                // Convert this file into a data table.
                var clicksdatatable = DataConverter.ConvertClicksCsvToDatatable(extractedfilepath);

                // Divide the progress increment in half so that the user receives more frequent progress updates.
                float workerprogresssmallincrement = workerprogressincrement / 2;
                workerprogressindicator += workerprogresssmallincrement;
                Worker.ReportProgress((int)workerprogressindicator);

                // Save the data table into the database.
                if (string.IsNullOrEmpty(command.ErrorMessage))
                {
                    using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.ImportScannerData))
                    {
                        storedprocedure.AddInputParameter("sessionid", command.SessionId);
                        storedprocedure.AddInputParameter("scanimportfileid", archivefileid);
                        storedprocedure.AddInputParameter("newscannerdata", clicksdatatable);
                        storedprocedure.Execute();
                        command.ErrorMessage = storedprocedure.ErrorMessage;
                    }
                }

                workerprogressindicator += workerprogresssmallincrement;
                Worker.ReportProgress((int)workerprogressindicator);
                if (Worker.CancellationPending) CancelScannerDataImport(command);
            }
        }

        private int GetArchiveFileIdFromExtractedFilePath(ImportScannerDataCommand command, string extractedfilepath)
        {
            // Get the ID of the original archive file by checking the last folder in the extracted file path.
            string sectionofextractedfilepathcontainingfilenameandarchivefileid = extractedfilepath.Replace(command.TempFilePathForAchiveDecompression, string.Empty);
            string sectionofextractedfilepathcontainingarchivefileid = sectionofextractedfilepathcontainingfilenameandarchivefileid.Replace(Path.GetFileName(extractedfilepath), string.Empty);
            string archivefileidstring = sectionofextractedfilepathcontainingarchivefileid.Replace("\\", string.Empty);
            int archivefileid;
            bool scanimportfileidrecoverysucceeded = int.TryParse(archivefileidstring, out archivefileid);

            if (scanimportfileidrecoverysucceeded == false)
            {
                command.ErrorMessage = "(ImportScannerDataCommandExecutor) - Failed to read the import file ID from the file path.";
                return -1;
            }
            else
            {
                return archivefileid;
            }
        }

        private void SetCompletedToTrueForAllArchiveFiles(ImportScannerDataCommand command)
        {
            if (string.IsNullOrEmpty(command.ErrorMessage) == false)
            {
                return;
            }

            // Create a data table containing the IDs of all the archive files to pass to the stored procedure that will mark them as complete.
            DataTable scanimportfileidtable = new DataTable();
            DataColumn idcolumn = new DataColumn("id", typeof(int));
            scanimportfileidtable.Columns.Add(idcolumn);
            foreach (int archivefileid in command.ArchiveFileIds.Values)
            {
                DataRow newrow = scanimportfileidtable.NewRow();
                newrow[0] = archivefileid;
                scanimportfileidtable.Rows.Add(newrow);
            }

            // Execute the stored procedure that will mark the archive files as imported completely.
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.SetScanImportFileAsCompleted))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.AddInputParameter("scanimportfileids", scanimportfileidtable);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
            }
            scanimportfileidtable.Dispose();
        }

        private List<string> GetExtractedFilePathList(ref string errormessage, string sourcefolderpath)
        {
            List<string> extractedfilepathlist = new List<string>();
            foreach (string path in Directory.GetDirectories(sourcefolderpath))
            {
                foreach (string filenameandpath in Directory.GetFiles(path))
                {
                    extractedfilepathlist.Add(filenameandpath);
                }
            }
            return extractedfilepathlist;
        }

        private void CancelScannerDataImport(ImportScannerDataCommand command)
        {
            if (string.IsNullOrEmpty(command.ErrorMessage) == false)
            {
                return;
            }

            // Build a table, containing all file ids that need to be deleted, to send to the import cancellation stored procedure.
            DataTable idsoffilestodelete = new DataTable();
            DataColumn idcolumn = new DataColumn("id", Type.GetType("System.Int32"));
            idsoffilestodelete.Columns.Add(idcolumn);

            // Add rows to the import cancellation file table.
            foreach (string filename in command.ArchiveFileNames)
            {
                int fileid = command.ArchiveFileIds[filename];
                if (fileid > -1)
                {
                    DataRow newrow = idsoffilestodelete.NewRow();
                    newrow[0] = fileid;
                    idsoffilestodelete.Rows.Add(newrow);
                }
            }

            // Run the stored procedure to delete the files that may already have been imported before the cancellation request
            // was issued.
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.CancelScannerDataFilesImport))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.AddInputParameter("scanimportfileids", idsoffilestodelete);
                storedprocedure.Execute();
                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    command.ErrorMessage = "Data import cancelled.";
                    foreach (string filenameandpath in command.ArchiveFileNames)
                    {
                        command.ArchiveFileIds[filenameandpath] = -1;
                    }
                }
                else
                {
                    command.ErrorMessage = "An attempt was made to cancel the data import. However, the following error occurred while trying "
                            + "to clean up the partially imported files: " + Environment.NewLine + Environment.NewLine
                            + storedprocedure.ErrorMessage;
                }
            }
        }
    }
}
