﻿using DataAccess;
using System;
using System.Collections.Generic;
using System.Data;

namespace DataService.Security
{
    class AddRoleMembersCommand : Command
    {
        public Guid SessionId { get; set; }
        public Guid RoleId { get; set; }
        public DataTable NewMembers { get; set; }

        public AddRoleMembersCommand(Guid sessionid, Guid roleid, List<DataRow> newmemberslist)
        {
            SessionId = sessionid;
            RoleId = roleid;

            // Create a new table.
            NewMembers = new DataTable();
            NewMembers.Columns.Add("id", typeof(Guid));
            
            // Convert the rows in the specified list to rows that match the schema of the table and then add them to the table.
            if (newmemberslist != null && newmemberslist.Count > 0)
            {
                for (int i = 0; i < newmemberslist.Count; i++)
                {
                    DataRow newrow = NewMembers.NewRow();
                    newrow["id"] = newmemberslist[i]["userid"];
                    NewMembers.Rows.Add(newrow);
                }
            }
        }
    }
}
