﻿using DataAccess;

namespace DataService.Legacy
{
    class GetContractStatusCommandExecutor : CommandExecutor<GetContractStatusCommand>
    {
        public override void Execute(GetContractStatusCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.GetContractStatus))
            {
                storedprocedure.AddInputParameter("contractid", command.ContractID);
                storedprocedure.AddOutputParameter<bool>("approvedbyfinance");
                storedprocedure.AddOutputParameter<bool>("clashingwithothercontracts");
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    bool approvedbyfinance = (bool)storedprocedure.GetOutputParameterValue("approvedbyfinance");
                    bool clashingwithothercontracts = (bool)storedprocedure.GetOutputParameterValue("clashingwithothercontracts");
                    command.ContractStatus = new Universal.Entities.ContractStatus(approvedbyfinance, clashingwithothercontracts);
                }
            }
        }
    }
}
