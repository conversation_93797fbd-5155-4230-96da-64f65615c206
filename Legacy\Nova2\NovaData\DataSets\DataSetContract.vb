﻿Partial Class DataSetContract
    Partial Public Class ContractInvoicesDataTable


    End Class

    Partial Class BillingInstructionDataTable
        Private Sub BillingInstructionDataTable_TableNewRow(sender As Object, e As System.Data.DataTableNewRowEventArgs) Handles Me.TableNewRow
            ' Generate an ID for the new row.
            e.Row("BillingInstructionID") = Guid.NewGuid
        End Sub
    End Class

    Partial Class ResearchCategoryDataTable
        Private Sub ResearchCategoryDataTable_TableNewRow(sender As Object, e As System.Data.DataTableNewRowEventArgs) Handles Me.TableNewRow
            ' Generate an ID for the new row.
            e.Row("ResearchCategoryID") = Guid.NewGuid
        End Sub
    End Class

    Partial Class IndependentStoreListDataTable
        Private Sub IndependentStoreListDataTable_IndependentStoreListRowChanging(sender As System.Object, e As DataTableNewRowEventArgs) Handles Me.TableNewRow
            e.Row("IndependentStoreListID") = Guid.NewGuid
        End Sub
    End Class

    Partial Class StorePoolDataTable
        Private Sub StorePoolDataTable_TableNewRow(ByVal sender As Object, ByVal e As System.Data.DataTableNewRowEventArgs) Handles Me.TableNewRow
            e.Row("StorePoolID") = Guid.NewGuid
        End Sub
    End Class

    Partial Class BurstDataTable
        Private Sub BurstDataTable_TableNewRow(ByVal sender As Object, ByVal e As System.Data.DataTableNewRowEventArgs) Handles Me.TableNewRow
            ' Generate an ID for the new row.
            e.Row("BurstID") = Guid.NewGuid
        End Sub
    End Class

    Partial Class ContractDataTable
        Private Sub ContractDataTable_TableNewRow(ByVal sender As Object, ByVal e As System.Data.DataTableNewRowEventArgs) Handles Me.TableNewRow
            ' Generate an ID for the new row.
            e.Row("ContractID") = Guid.NewGuid
        End Sub

        Private Sub ContractDataTable_ColumnChanging(sender As Object, e As DataColumnChangeEventArgs) Handles Me.ColumnChanging
            If (e.Column.ColumnName = Me.ContractProposalHeatNameColumn.ColumnName) Then
                'Add user code here
            End If

        End Sub

    End Class

End Class


Namespace DataSetContractTableAdapters

    Partial Public Class BurstInstallationDayTableAdapter
    End Class
End Namespace
