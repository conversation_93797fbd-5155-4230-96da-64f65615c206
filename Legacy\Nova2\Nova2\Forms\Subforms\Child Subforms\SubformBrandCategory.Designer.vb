<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformBrandCategory
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformBrandCategory))
        Dim SuperToolTip2 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem2 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem2 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip3 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem3 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem3 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle16 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle17 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle18 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Me.ImageList16x16 = New System.Windows.Forms.ImageList(Me.components)
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.TextEditBrandCategoryName = New DevExpress.XtraEditors.TextEdit()
        Me.LabelBrandFamilyName = New DevExpress.XtraEditors.LabelControl()
        Me.GroupControlBrandCategoryMember = New DevExpress.XtraEditors.GroupControl()
        Me.LabelControl14 = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearch = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearchClientBrand = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearch = New DevExpress.XtraEditors.PictureEdit()
        Me.ButtonRemove = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAdd = New DevExpress.XtraEditors.SimpleButton()
        Me.GridBrandCategoryMember = New System.Windows.Forms.DataGridView()
        Me.BrandNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.ButtonSave = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl()
        CType(Me.TextEditBrandCategoryName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControlBrandCategoryMember, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlBrandCategoryMember.SuspendLayout()
        CType(Me.TextEditSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearchClientBrand.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridBrandCategoryMember, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!)
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Margin = New System.Windows.Forms.Padding(5, 5, 5, 21)
        Me.LabelTitle.Size = New System.Drawing.Size(274, 37)
        Me.LabelTitle.TabIndex = 0
        Me.LabelTitle.Text = "(new brand group)"
        '
        'ImageList16x16
        '
        Me.ImageList16x16.ImageStream = CType(resources.GetObject("ImageList16x16.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList16x16.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList16x16.Images.SetKeyName(0, "add.png")
        Me.ImageList16x16.Images.SetKeyName(1, "pencil.png")
        Me.ImageList16x16.Images.SetKeyName(2, "delete.png")
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        '
        'TextEditBrandCategoryName
        '
        Me.TextEditBrandCategoryName.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditBrandCategoryName.Location = New System.Drawing.Point(220, 118)
        Me.TextEditBrandCategoryName.Margin = New System.Windows.Forms.Padding(4, 4, 4, 20)
        Me.TextEditBrandCategoryName.Name = "TextEditBrandCategoryName"
        Me.TextEditBrandCategoryName.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditBrandCategoryName.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditBrandCategoryName.Properties.Appearance.Options.UseFont = True
        Me.TextEditBrandCategoryName.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditBrandCategoryName.Properties.MaxLength = 200
        Me.TextEditBrandCategoryName.Size = New System.Drawing.Size(379, 24)
        Me.TextEditBrandCategoryName.TabIndex = 3
        '
        'LabelBrandFamilyName
        '
        Me.LabelBrandFamilyName.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelBrandFamilyName.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelBrandFamilyName.Location = New System.Drawing.Point(15, 122)
        Me.LabelBrandFamilyName.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelBrandFamilyName.Name = "LabelBrandFamilyName"
        Me.LabelBrandFamilyName.Size = New System.Drawing.Size(161, 17)
        Me.LabelBrandFamilyName.TabIndex = 2
        Me.LabelBrandFamilyName.Text = "Brand Category Name:"
        '
        'GroupControlBrandCategoryMember
        '
        Me.GroupControlBrandCategoryMember.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlBrandCategoryMember.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlBrandCategoryMember.Appearance.Options.UseFont = True
        Me.GroupControlBrandCategoryMember.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlBrandCategoryMember.AppearanceCaption.Options.UseFont = True
        Me.GroupControlBrandCategoryMember.Controls.Add(Me.LabelControl14)
        Me.GroupControlBrandCategoryMember.Controls.Add(Me.TextEditSearch)
        Me.GroupControlBrandCategoryMember.Controls.Add(Me.PictureClearSearchClientBrand)
        Me.GroupControlBrandCategoryMember.Controls.Add(Me.PictureAdvancedSearch)
        Me.GroupControlBrandCategoryMember.Controls.Add(Me.ButtonRemove)
        Me.GroupControlBrandCategoryMember.Controls.Add(Me.ButtonAdd)
        Me.GroupControlBrandCategoryMember.Controls.Add(Me.GridBrandCategoryMember)
        Me.GroupControlBrandCategoryMember.Location = New System.Drawing.Point(15, 211)
        Me.GroupControlBrandCategoryMember.LookAndFeel.SkinName = "Black"
        Me.GroupControlBrandCategoryMember.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlBrandCategoryMember.Margin = New System.Windows.Forms.Padding(4)
        Me.GroupControlBrandCategoryMember.Name = "GroupControlBrandCategoryMember"
        Me.GroupControlBrandCategoryMember.Size = New System.Drawing.Size(1027, 435)
        Me.GroupControlBrandCategoryMember.TabIndex = 5
        Me.GroupControlBrandCategoryMember.Text = "Brands in this Brand Group"
        '
        'LabelControl14
        '
        Me.LabelControl14.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl14.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl14.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl14.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl14.Location = New System.Drawing.Point(824, 404)
        Me.LabelControl14.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelControl14.Name = "LabelControl14"
        Me.LabelControl14.Size = New System.Drawing.Size(58, 17)
        Me.LabelControl14.TabIndex = 4
        Me.LabelControl14.Text = "Search:"
        '
        'TextEditSearch
        '
        Me.TextEditSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearch.EditValue = ""
        Me.TextEditSearch.Location = New System.Drawing.Point(890, 400)
        Me.TextEditSearch.Margin = New System.Windows.Forms.Padding(4, 5, 4, 7)
        Me.TextEditSearch.Name = "TextEditSearch"
        Me.TextEditSearch.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearch.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearch.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearch.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearch.Size = New System.Drawing.Size(103, 24)
        Me.TextEditSearch.TabIndex = 5
        '
        'PictureClearSearchClientBrand
        '
        Me.PictureClearSearchClientBrand.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchClientBrand.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchClientBrand.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchClientBrand.Location = New System.Drawing.Point(1000, 4)
        Me.PictureClearSearchClientBrand.Margin = New System.Windows.Forms.Padding(4)
        Me.PictureClearSearchClientBrand.Name = "PictureClearSearchClientBrand"
        Me.PictureClearSearchClientBrand.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchClientBrand.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchClientBrand.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchClientBrand.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem2.Text = "Clear Search"
        ToolTipItem2.LeftIndent = 6
        ToolTipItem2.Text = "Click here to clear all search boxes."
        SuperToolTip2.Items.Add(ToolTipTitleItem2)
        SuperToolTip2.Items.Add(ToolTipItem2)
        Me.PictureClearSearchClientBrand.SuperTip = SuperToolTip2
        Me.PictureClearSearchClientBrand.TabIndex = 0
        Me.PictureClearSearchClientBrand.TabStop = True
        '
        'PictureAdvancedSearch
        '
        Me.PictureAdvancedSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearch.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearch.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearch.Location = New System.Drawing.Point(1000, 403)
        Me.PictureAdvancedSearch.Margin = New System.Windows.Forms.Padding(4, 8, 4, 9)
        Me.PictureAdvancedSearch.Name = "PictureAdvancedSearch"
        Me.PictureAdvancedSearch.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearch.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearch.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearch.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem3.Text = "Advanced Search"
        ToolTipItem3.LeftIndent = 6
        ToolTipItem3.Text = "Click here to search individual column values."
        SuperToolTip3.Items.Add(ToolTipTitleItem3)
        SuperToolTip3.Items.Add(ToolTipItem3)
        Me.PictureAdvancedSearch.SuperTip = SuperToolTip3
        Me.PictureAdvancedSearch.TabIndex = 6
        Me.PictureAdvancedSearch.TabStop = True
        '
        'ButtonRemove
        '
        Me.ButtonRemove.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonRemove.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonRemove.Appearance.Options.UseFont = True
        Me.ButtonRemove.ImageIndex = 2
        Me.ButtonRemove.ImageList = Me.ImageList16x16
        Me.ButtonRemove.Location = New System.Drawing.Point(111, 399)
        Me.ButtonRemove.LookAndFeel.SkinName = "Black"
        Me.ButtonRemove.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonRemove.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonRemove.Name = "ButtonRemove"
        Me.ButtonRemove.Size = New System.Drawing.Size(96, 30)
        Me.ButtonRemove.TabIndex = 3
        Me.ButtonRemove.Text = "Remove"
        '
        'ButtonAdd
        '
        Me.ButtonAdd.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAdd.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAdd.Appearance.Options.UseFont = True
        Me.ButtonAdd.ImageIndex = 0
        Me.ButtonAdd.ImageList = Me.ImageList16x16
        Me.ButtonAdd.Location = New System.Drawing.Point(6, 399)
        Me.ButtonAdd.LookAndFeel.SkinName = "Black"
        Me.ButtonAdd.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAdd.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonAdd.Name = "ButtonAdd"
        Me.ButtonAdd.Size = New System.Drawing.Size(96, 30)
        Me.ButtonAdd.TabIndex = 2
        Me.ButtonAdd.Text = "Add"
        '
        'GridBrandCategoryMember
        '
        Me.GridBrandCategoryMember.AllowUserToAddRows = False
        Me.GridBrandCategoryMember.AllowUserToDeleteRows = False
        Me.GridBrandCategoryMember.AllowUserToOrderColumns = True
        Me.GridBrandCategoryMember.AllowUserToResizeRows = False
        DataGridViewCellStyle16.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridBrandCategoryMember.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle16
        Me.GridBrandCategoryMember.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridBrandCategoryMember.BackgroundColor = System.Drawing.Color.White
        Me.GridBrandCategoryMember.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridBrandCategoryMember.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridBrandCategoryMember.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle17.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle17.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle17.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle17.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle17.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle17.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle17.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridBrandCategoryMember.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle17
        Me.GridBrandCategoryMember.ColumnHeadersHeight = 22
        Me.GridBrandCategoryMember.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridBrandCategoryMember.ColumnHeadersVisible = False
        Me.GridBrandCategoryMember.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.BrandNameColumn})
        Me.GridBrandCategoryMember.EnableHeadersVisualStyles = False
        Me.GridBrandCategoryMember.GridColor = System.Drawing.Color.White
        Me.GridBrandCategoryMember.Location = New System.Drawing.Point(3, 29)
        Me.GridBrandCategoryMember.Margin = New System.Windows.Forms.Padding(4)
        Me.GridBrandCategoryMember.Name = "GridBrandCategoryMember"
        Me.GridBrandCategoryMember.ReadOnly = True
        Me.GridBrandCategoryMember.RowHeadersVisible = False
        DataGridViewCellStyle18.ForeColor = System.Drawing.Color.DimGray
        Me.GridBrandCategoryMember.RowsDefaultCellStyle = DataGridViewCellStyle18
        Me.GridBrandCategoryMember.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridBrandCategoryMember.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridBrandCategoryMember.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridBrandCategoryMember.RowTemplate.Height = 19
        Me.GridBrandCategoryMember.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridBrandCategoryMember.ShowCellToolTips = False
        Me.GridBrandCategoryMember.Size = New System.Drawing.Size(1022, 362)
        Me.GridBrandCategoryMember.StandardTab = True
        Me.GridBrandCategoryMember.TabIndex = 1
        '
        'BrandNameColumn
        '
        Me.BrandNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.BrandNameColumn.DataPropertyName = "BrandName"
        Me.BrandNameColumn.HeaderText = "Brand"
        Me.BrandNameColumn.Name = "BrandNameColumn"
        Me.BrandNameColumn.ReadOnly = True
        '
        'ButtonSave
        '
        Me.ButtonSave.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonSave.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonSave.Appearance.Options.UseFont = True
        Me.ButtonSave.ImageIndex = 1
        Me.ButtonSave.ImageList = Me.ImageList24x24
        Me.ButtonSave.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonSave.Location = New System.Drawing.Point(778, 666)
        Me.ButtonSave.LookAndFeel.SkinName = "Black"
        Me.ButtonSave.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonSave.Margin = New System.Windows.Forms.Padding(4, 16, 4, 4)
        Me.ButtonSave.Name = "ButtonSave"
        Me.ButtonSave.Size = New System.Drawing.Size(129, 37)
        Me.ButtonSave.TabIndex = 6
        Me.ButtonSave.Text = "Save"
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(914, 666)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Margin = New System.Windows.Forms.Padding(4, 16, 4, 4)
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(129, 37)
        Me.ButtonCancel.TabIndex = 7
        Me.ButtonCancel.Text = "Cancel"
        '
        'LabelControl1
        '
        Me.LabelControl1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl1.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelControl1.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl1.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelControl1.LineVisible = True
        Me.LabelControl1.Location = New System.Drawing.Point(15, 75)
        Me.LabelControl1.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(1027, 24)
        Me.LabelControl1.TabIndex = 1
        Me.LabelControl1.Text = "Brand Group Details"
        '
        'LabelControl2
        '
        Me.LabelControl2.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl2.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl2.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelControl2.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl2.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelControl2.LineVisible = True
        Me.LabelControl2.Location = New System.Drawing.Point(15, 167)
        Me.LabelControl2.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.Size = New System.Drawing.Size(1027, 24)
        Me.LabelControl2.TabIndex = 4
        Me.LabelControl2.Text = "Brand Group Memberships"
        '
        'SubformBrandCategory
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(9.0!, 17.0!)
        Me.Controls.Add(Me.GroupControlBrandCategoryMember)
        Me.Controls.Add(Me.TextEditBrandCategoryName)
        Me.Controls.Add(Me.LabelControl2)
        Me.Controls.Add(Me.LabelControl1)
        Me.Controls.Add(Me.LabelBrandFamilyName)
        Me.Controls.Add(Me.ButtonSave)
        Me.Controls.Add(Me.ButtonCancel)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(5)
        Me.Name = "SubformBrandCategory"
        Me.Padding = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        Me.Controls.SetChildIndex(Me.ButtonCancel, 0)
        Me.Controls.SetChildIndex(Me.ButtonSave, 0)
        Me.Controls.SetChildIndex(Me.LabelBrandFamilyName, 0)
        Me.Controls.SetChildIndex(Me.LabelControl1, 0)
        Me.Controls.SetChildIndex(Me.LabelControl2, 0)
        Me.Controls.SetChildIndex(Me.TextEditBrandCategoryName, 0)
        Me.Controls.SetChildIndex(Me.GroupControlBrandCategoryMember, 0)
        CType(Me.TextEditBrandCategoryName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControlBrandCategoryMember, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlBrandCategoryMember.ResumeLayout(False)
        CType(Me.TextEditSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearchClientBrand.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridBrandCategoryMember, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents ImageList16x16 As System.Windows.Forms.ImageList
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents TextEditBrandCategoryName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelBrandFamilyName As DevExpress.XtraEditors.LabelControl
    Friend WithEvents GroupControlBrandCategoryMember As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelControl14 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditSearch As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearchClientBrand As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearch As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents ButtonRemove As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonAdd As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GridBrandCategoryMember As System.Windows.Forms.DataGridView
    Friend WithEvents BrandNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents ButtonSave As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl

End Class
