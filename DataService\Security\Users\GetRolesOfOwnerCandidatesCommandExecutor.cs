﻿using DataAccess;

namespace DataService.Security
{
    internal class GetRolesOfOwnerCandidatesCommandExecutor : CommandExecutor<GetRolesOfOwnerCandidatesCommand>
    {
        public override void Execute(GetRolesOfOwnerCandidatesCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.GetRolesOfOwnerCandidates))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.AddInputParameter("userid", command.UserId);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    command.Table = storedprocedure.OutputTable;
                }
            }
        }
    }
}
