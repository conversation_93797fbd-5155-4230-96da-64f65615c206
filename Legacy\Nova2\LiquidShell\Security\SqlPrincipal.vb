Public Class SqlPrincipal
    Implements System.Security.Principal.IPrincipal

    Private _Identity As SqlIdentity

    Public ReadOnly Property Identity() As System.Security.Principal.IIdentity Implements System.Security.Principal.IPrincipal.Identity
        Get
            Return _Identity
        End Get
    End Property

    Public Function IsInRole(ByVal role As String) As Boolean Implements System.Security.Principal.IPrincipal.IsInRole
        If _Identity.Roles.Contains(role) Then
            Return True
        Else
            Return False
        End If
    End Function

    Public Sub New(ByVal Server As String, ByVal Database As String, ByVal Name As String, ByVal Password As String)
        _Identity = New SqlIdentity(Server, Database, Name, Password)
    End Sub

End Class
