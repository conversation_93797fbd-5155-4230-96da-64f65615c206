<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="ImageList24x24.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="ImageList24x24.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj0yLjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAAD+
        FgAAAk1TRnQBSQFMAgEBAwEAARABAAEQAQABGAEAARgBAAT/AREBAAj/AUIBTQE2BwABNgMAASgDAAFg
        AwABGAMAAQEBAAEQBgABEhgAAf8BfwGdAXMBewFvAZwBcwH/AX8QAAH/AX8BnAFzAXsBbwGdAXMB/wF/
        CgAB/wF/AZwBcwF7AW8BewFvAXsBbwF7AW8BewFvAXsBbwF7AW8BewFvAXsBbwF7AW8BewFvAXsBbwF7
        AW8BewFvAXsBbwF7AW8BnAFzAf8BfxYAAf8BfwH/AX8B/wF/TAAB/wF/AXsBbwH/AXsB/wF7Af8BewGc
        AXMB/wF/DAAB/wF/AZwBcwH/AXsB/wF7Af8CewFvAf8BfwYAAf8BfwF7AW8B/gF/Af4BfwH+AX8B/gF/
        Af4BfwH+AX8B/gF/Af4BfwH+AX8B/gF/Af4BfwH+AX8B/gF/Af4BfwH+AX8B/gF/Af4BfwH+AX8BewFv
        Ad4BexQAAb0BdwF7AW8BnAFvAd4Be0gAAf8BfwF7AW8B/wF7ARgBawHGAUAB7gFVAf8BewGcAXMB/wF/
        CAAB/wF/AZwBbwH/AXsB7gFVAcYBQAEYAWsB/wJ7AW8B/wF/AgAB/wF/AXsBbwH+AX8BuQFCAdUBGQH2
        ARkB9gEZAfYBGQH2ARkB9gEZAfYBGQH2ARkB9gEZAdUBGQHVARkB1QEZAdUBGQHVARkB1QEZAZQBFQFX
        ATYB3gF/AZwBdwH/AX8OAAH/AX8BewFvAb8BewHfAX8B3wF/AZwBcwH/AX9EAAH/AX8BewFvAf8BewHW
        AWYBAAE0AQABNAEAATQBagFNAf8BewGdAXMB/wF/BAAB/wF/AZ0BcwH/AXsBagFNAQABNAEAATQBAAE0
        AdYBZgH/AnsBbwH/AX8BvQF3Af4BfwGYAToB0gEAAVMBAQEzAQEBMwEBATMBAQEzAQEBMwEBATMBAQEz
        AQEBMwEBARMBAQESAQEBEgEBARIBAQHyAQAB8QEAARIBAQGPAQAB9QEhAf4BfwGcAXMMAAH/AX8BewFv
        Ad8BfwF4AWcBaAE6Aa4BSgHfAnsBbwH/AX9CAAG9AXcB/wF7AfcBagEAATgBQQE8AWMBQAFjAUABAAE4
        AYwBUQH/AX8BnAFzAf8BfwH/AX8BnAFzAf8BfwGMAVEBAAE4AWMBQAFjAUABQQE8AQABOAH3AWoB/wF7
        Ab0BdwGcAXMB/gF/AXQBAQF0AQEB/wF/AZgBPgE0AQEBdAEBAXQBAQF0AQEBdAEBAXQBAQF0AQEBdAEB
        AXQBAQFTAQEBUwEBARIBAQEWASYB/wF/AZQBEQHQAQABvQF3AXsBbwoAAf8BfwF7AW8B3wF/AVYBYwEB
        ASYBAQEmAeABHQHwAU4B3wF/AZwBc0IAAXwBbwH/AXsBYwFAASABPAGDAUABYwFAAWMBQAFiAUABAAE4
        AYwBVQH/AXsBfAFvAXwBbwH/AX8BrAFVAQABOAFiAUABYwFAAWMBQAGDAUABIAE8AWMBQAH/AXsBnAFv
        AZwBcwH+AX8BlQEFAVQBAQG6AT4B9gERAXQBAQGVAQEBlQEBAZUBAQGVAQEBdQEBAXQBAQF0AQEBdAEB
        AXQBAQFUAQEBUwEBAZQBCQG5AUIBUwEBARIBAQG9AXcBewFvCAAB/wF/AXsBbwHfAX8BeQFrASEBKgFD
        AS4BRAEyAUQBLgEiASoBnAFzAb4BdwH/AX9AAAGcAXMB/wF7AeYBTAEAAUABgwFEAWMBRAFjAUQBYwFE
        AWIBRAEAATwBawFVAf8BfwH/AX8BawFVAQABPAFiAUQBYwFEAWMBRAFjAUQBgwFEAQABQAHmAUwB/wF7
        AZwBcwGcAXMB/gF/AbYBCQF1AQEBVAEBAXUBAQGVAQEBlQEBAZUBAQGVAQEBlQEBAZUBAQGVAQEBdQEB
        AXQBAQF0AQEBdAEBAXQBAQFTAQEBEgEBARIBAQEyAQEBvQF3AXsBbwYAAf8BfwF7AW8B3wF/AXgBZwFC
        AS4BQwEuAWQBMgFkATIBZAEyAUEBKgHLAUYB/wF/AXsBb0AAAf8BfwH/AXsBvQF3AWIBRAEgAUQBgwFI
        AWMBSAFjAUgBYwFIAWMBSAEAAUAB7wFdAe8BXQEAAUABYgFIAWMBSAFjAUgBYwFIAYMBSAEgAUQBYgFE
        Ab0BdwH/AXsB/wF/AZwBcwH+AX8B1gEJAXUBAQG2AQEBtgEBAbYBAQG2AQEBlgEBAZYBAQGWAQEBlQEB
        AZUBAQGVAQEBlQEBAXUBAQF0AQEBdAEBAXQBAQFTAQEBMwEBATMBAQHeAnsBbwQAAf8BfwF7AW8B3wF/
        AXkBawFBAS4BYwEyAYQBNgGEATYBhAE2AYQBNgFkATYBQAEqAXcBZwHfAX8B3gF7QAAB3gF7Af8BewGc
        AXcBgwFIASABRAGDAUgBgwFIAYMBSAGDAUgBYwFIASABRAEgAUQBYwFIAYMBSAGDAUgBgwFIAYMBSAEg
        AUQBgwFIAZwBdwH/AXsB3gF7AgABnAFzAf4BfwHXAQkBlgEBAbYBAQG2AQEBtgEBAbYBAQG2AQEBtgEB
        AbYBAQG2AQEBlgEBAZUBAQGVAQEBlQEBAXUBAQF0AQEBdAEBAXQBAQFTAQEBMwEBAd4CewFzAgAB/wF/
        AZwBcwHfAX8BmgFvAYMBMgGDATIBhAE2AYQBNgGEATYBhAE2AYQBNgGEATYBgwEyAaUBOgHfAXsBfAFz
        Af8Bf0AAAd4BewH/AX8B3gF3AYMBTAEgAUgBgwFMAYMBTAGDAUwBgwFMAYMBTAGDAUwBgwFMAYMBTAGD
        AUwBgwFMASABSAGDAUwB3gF3Af8BfwHeAXsEAAGcAXMB/gF/AdcBCQGWAQEBtwEBAbcBAQG3AQEBtwEB
        AbcBAQG2AQEBtgEBAbYBAQG2AQEBtgEBAZUBAQGVAQEBlQEBAXUBAQF0AQEBdAEBAVMBAQFTAQEB3gF7
        AZsBcwIAAZwBcwH/AX8BmgFvAYIBMgGDATYBpAE6AaQBOgGkAToBpAE6AaQBOgGkAToBpAE6AaQBOgGA
        AS4BLwFTAf8BfwGcAXNCAAHeAXsB/wF/Ab0BdwFiAUwBIAFIAYMBUAGDAVABgwFQAYMBUAGDAVABgwFQ
        AYMBUAGDAVABIAFIAWIBTAG9AXcB/wF/Ad4BewYAAZwBcwH+AX8B9wEJAbYBAQHXAQEB1wEBAdcBAQHX
        AQEB1wEBAdcBAQG3AQEBtgEBAbYBAQG2AQEBtgEBAZYBAQGVAQEBlQEBAXUBAQF0AQEBVAEBAVMBAQHe
        AXsBmwFzAd4BewHfAX8BuwFzAaIBNgGiATYBxAE6AcQBOgHEAToBxAE6AaIBNgGiATYBxAE6AcQBOgHE
        AToBpAE6AaIBNgG8AXcB3wF/Af8Bf0IAAd4BewH/AXsB3gF7AeYBVAFBAVABgwFQAYMBUAGDAVABgwFQ
        AYMBUAGDAVABQQFQAeYBVAHeAXsB/wF7Ad4BewgAAZwBcwH/AX8B+AEJAbcBAQHXAQEB1wEBAdcBAQHX
        AQEB1wEBAdcBAQHXAQEB1wEBAbcBAQG2AQEBtgEBAbYBAQGWAQEBlQEBAZUBAQF1AQEBVAEBAVMBAQHe
        AXsBnAFzAZwBcwHfAX8B5gE+AcIBNgHEAT4BxAE+AcQBPgHEAT4BoAEyAVABVwFQAVsBoQE2AcQBPgHE
        AT4BxAE+AcIBNgHoAUYB/wF/AXwBbwH/AX9AAAH/AX8BvQFzAf8BfwHOAWEBIAFQAYMBVAGDAVQBgwFU
        AYMBVAGDAVQBgwFUASABUAHOAWEB/wF/Ab0BcwH/AX8IAAGcAXMB/wF/AfgBCQG3AQEB+AEBAdgBAQHY
        AQEB2AEBAdcBAQG3AQEBtwEBAbcBAQG3AQEBlgEBAZYBAQGWAQEBdQEBAXUBAQGVAQEBlQEBAXQBAQFU
        AQEB3gF/AZwBcwGcAXMB/wF/AQYBQwHBATYB5QE+AeQBPgHkAT4BwAE2ASwBUwH/AX8B/wF/AeQBPgHj
        AToB5AE+AeQBPgHkAT4BwAE2AXEBXwH/AX8BnAFzPgAB/wF/Ab0BcwH/AX8BrQFhAQABUAFjAVQBgwFU
        AYMBVAGDAVQBgwFUAYMBVAGDAVQBYwFUAQABUAGtAWEB/wF/Ab0BcwH/AX8GAAGcAXMB/wF/ARgBCgHY
        AQEB+AEBAdgBAQEZAQoBOQESATkBDgE5AQ4BGAEOARgBDgEYAQ4BGAEOAfgBDQH3AQ0B9wERAdYBCQFV
        AQEBdQEBAXQBAQFUAQEB/gF/AZwBcwH/AX8B/wF/Ad0BdwHkAT4B4gE6AQQBQwHAATYBKwFTAf8BfwG+
        AXcB3wF/AbgBbwHgATYB5AFCAQQBQwEEAUMB5AE+AeEBOgG6AXMB/wF/Ab0BdzoAAf8BfwHeAXcB/wF/
        Ac0BZQEAAVABgwFYAYMBWAGDAVgBgwFYAYMBWAGDAVgBgwFYAYMBWAGDAVgBgwFYAQABUAHNAWUB/wF/
        Ab4BdwH/AX8EAAGcAXMB/wF/ARkBCgHYAQEB2AEBAdsBOgH9AX8B3QF/Ad0BfwHdAX8B3QF/Ad0BfwHd
        AX8B/QF/Af4BfwH/AX8B/wF/Af4BfwGZATYBVAEBAXQBAQF0AQEB/gF/AZwBcwIAAd4BewH/AX8B3QF3
        AeMBQgHAATYBKgFTAf8BfwHeAXsB/wF/Ab0BdwH/AX8BTQFXAeABOgEEAUMBBAFDAQQBQwHiAT4BBQFH
        Af8BfwHfAX8B/wF/NgAB/wF/Ab0BcwH/AX8BzgFpAQABVAGDAVgBgwFcAYMBXAGDAVwBgwFcAWIBWAFi
        AVgBgwFcAYMBXAGDAVwBgwFcAYMBWAEAAVQBzgFpAf8BfwG9AXMB/wF/AgABnAFzAf8BfwEZAQoB2AEB
        AfkBAQG9AXcB3QF/Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Af4BfwG5AUYBlAEdATYBLgH+AX8BnQFz
        AVQBAQF0AQEBdAEBAf8BfwGcAXMEAAHeAXsB/wF/Ad0BewGWAWsB/wF/Ad8BfwH/AX8CAAH/AX8B3gF7
        Af8BfwEDAUMBAgFDAQQBRwEEAUcBBAFHAeABPgEqAVMB/wF/Ab0BdwH/AX8yAAH/AX8BvQF3Af8BfwGs
        AWkBAAFYAYMBXAGDAVwBgwFcAYMBXAGDAVwBIAFcAcYBYAHmAWABIAFcAYMBXAGDAVwBgwFcAYMBXAGD
        AVwBAAFYAawBaQH/AX8BvQF3Af8BfwGcAXMB/wF/ATkBCgH5AQEB+QEBAb0BdwHeAX8BvQF3Ab0BdwG9
        AXcBvQF3Ab0BdwG9AXcB/wF/AfYBGQHRAQABMgEBAf4BfwGdAXMBdQEBAXUBAQF0AQEB/wF/AZwBcwYA
        Ad4BewG+AXsB/wF/Ab0BdwH/AX8GAAHeAXsB/wF/AbgBbwHgAT4BAwFHAQMBRwEDAUcBAwFHAeABPgFM
        AVcB/wF/Ab0BdwH/AX8wAAGcAXMB/wF/AQ8BbgEAAVgBgwFgAYMBYAGDAWABgwFgAYMBYAEgAVwBgwFg
        Af8BfwH/AX8BYgFgASABXAGDAWABgwFgAYMBYAGDAWABgwFgAQABWAEPAW4B/wF/AZwBcwGcAXMB/wF/
        ATkBCgH5AQEB+QEBAb4BdwHeAX8B3gF7Ad4BewHeAXsB3gF7Ad4BewHeAXsB/wF/AVgBIgFUAQEBlQEB
        Af8BfwG9AXMBdQEBAXUBAQF0AQEB/wF/AZwBcxgAAb0BdwH/AX8BbgFfAeABPgEDAUcBAwFHAQMBRwED
        AUcB4AE+AW8BXwH/AX8BnAFzMAABnAFzAf8BfwFBAWABQQFgAYMBYAGDAWABgwFgAYMBYAEgAWABgwFg
        Ab0BewH/AX8B/wF/Ab0BewGDAWABIAFgAYMBYAGDAWABgwFgAYMBYAFBAWABQQFgAf8BfwGcAXMBnAFz
        Af8BfwE6AQoB+QEBARoBAgHeAXcB/gF/Ad4BewHeAXsB3gF7Ad4BewHeAXsB3gF7Af8BfwF6ASIBlwEB
        AdcBAQH/AX8BvgFzAXUBAQF1AQEBdAEBAf8BfwGcAXMaAAG9AXcB/wF/AUgBUwEAAUMBAwFLAQMBSwED
        AUsBAAFDAQABRwH/AX8BnAFzMAABvQF3Af8BfwHvAW0BAAFgAYMBZAGDAWQBgwFkASABYAGDAWQB3gF/
        Af8BfwHeAXsB3gF7Af8BfwHeAX8BgwFkASABYAGDAWQBgwFkAYMBZAEAAWABDwFuAf8BfwG9AXcBnAFz
        Af8BfwE6AQYB+QEBARoBAgHeAXcB/wF/Ad4BewHeAXsB3gF7Ad4BewHeAXsB3gF7Af8BfwGbARoBuAEB
        AbcBAQH/AX8BvgF3AXUBAQF1AQEBdAEBAf8BfwGcAXMaAAH/AX8B/wF/Af8BfwEiAUsBAAFHASIBSwEA
        AUcBAAFHAbYBbwH/AX8B3gF7MAAB/wF/Ad4BewH/AX8BjAFtAQABZAFiAWQBIAFkAWIBZAHeAX8B/wF/
        Ad4BewQAAd4BewH/AX8BvQF/AWEBZAEgAWQBYgFkAQABZAGMAW0B/wF/Ad4BewH/AX8BvQF3Af8BfwF6
        ARoB2QEBARoBAgHfAXsB/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwEdAUMBOgEOAXoBJgH/
        AX8BvgFzAXUBAQE0AQEBlQEBAf8BfwGcAXMcAAHeAXsB/wF/AfwBewEiAU8BAAFDAUgBVwH9AXsB/wF/
        Ab0BdzQAAf8BfwHeAXsB/wF/Ac4BcQEAAWQBhAFoAb0BfwH/AX8B3gF7CAAB3gF7Af8BfwG9AX8BpAFo
        AQABZAHOAXEB/wF/Ad4BewH/AX8CAAH/AX8B/wF/Ad8BdwE6AQYB2QEBAd8BdwH/AX8B/wF/Af8BfwH/
        AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwG+AXMBFAEBAXQBAQF9AWcB/wF/Ad4Bex4A
        Ad4BewH/AX8B/wF/AdsBdwH/AX8B/wF/Ad4BezgAAf8BfwHeAXsB/wF/Ab0BfwH/AX8B/wF/Ad4BewwA
        Ad4BewH/AX8B/wF/Ab0BfwH/AX8B3gF7Af8BfwYAAd4BewH/AX8B/wF/Ab8BbwH/AX8B/wF/Af8BfwH/
        AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Ab4BbwH/AX8B/wF/Ab0BdyIA
        Ad4BewHeAXsB/wF/Ab0Bd0AAAb0BdwG9AXcBvQF3Ad4BexAAAd4BewG9AXcBvQF3Ab0BdwwAAf8BfwG9
        AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9
        AXcBvQF3Ab0BdwHeAXsmAAH/AX8B/wF/Af8BfzgAAUIBTQE+BwABPgMAASgDAAFgAwABGAMAAQEBAAEB
        BQABIAEBFgAD/wEAAeAB/wEHAcABAAEDAf8BjwH/AwABwAF+AQMBgAEAAQEB/wGHAf8DAAGAATwBAQMA
        Af4BAwH/BAABGAQAAfwBAQH/CQAB+AEBAf8JAAHwAQAB/wkAAeABAAH/CQABwAEAAX8DAAGAAQABAQMA
        AYABAAE/AwABwAEAAQMDAAGAAQABPwMAAeABAAEHBQABHwMAAfABAAEPBQABDwMAAfABAAEPBQABDwMA
        AeABAAEHBQABBwMAAcABAAEDAwABgAEAAQMDAAGAAQABAQMAAcABQAEBCQAC4AoAAf8B8AoAAf8B+AoA
        Af8B+AUAARgEAAH/AfwBAQMAAYABPAEBAwAB/wH+AQMDAAHAAX4BAwGAAQABAQL/AQ8DAAHwAf8BDwHA
        AQABAwL/AY8DAAs=
</value>
  </data>
</root>