﻿Public Class SubFormMediaGroup

    Private DataObject As MediaGroup

    Private Sub Subform_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        ' Load values for all controls.
        TextEditMediaGroupName.Text = DataObject.MediaGroupName
        CheckEditDormant.Checked = DataObject.Dormant

        ' Load grid data.
        GridMediaGroup.AutoGenerateColumns = False
        GridMediaGroup.DataSource = DataObject.MediaGroupMemberBindingSource

        ' Configure grid managers.
        Dim GridManagerMediaGroupMembership As New GridManager(GridMediaGroup, TextEditSearchMedia, Nothing, Nothing,
        PictureAdvancedSearchMedia, PictureClearSearchMedia, Nothing, ButtonRemoveMediaFromGroup)
    End Sub

    Public Sub New(ByVal MediaObject As MediaGroup)
        ' This call is required by the Windows Form Designer.
        InitializeComponent()
        ' Add any initialization after the InitializeComponent() call.
        DataObject = MediaObject
    End Sub


    Private Sub TextEditMediaGroupName_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles TextEditMediaGroupName.EditValueChanged

        ' Create an object to represent the control.
        Dim CurrentControl As TextEdit = CType(sender, TextEdit)

        ' Update the form title as the user types a name.
        If Not String.IsNullOrEmpty(CurrentControl.Text) Then
            LabelTitle.Text = CurrentControl.Text
        End If

        ' Reset the error text of the control to an empty string.
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)

    End Sub


    Private Sub TextEditMediaName_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles TextEditMediaGroupName.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If ValidatedControl.Text.Length = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "The name of the media goup may not be blank.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

        ' Update the data object.
        DataObject.MediaGroupName = ValidatedControl.Text

    End Sub


    Private Sub CheckEditDormant_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles CheckEditDormant.Validated
        ' Update the data object.
        DataObject.Dormant = CType(sender, CheckEdit).Checked
    End Sub

    Private Sub ButtonAddMediaToGroup_Click(sender As Object, e As EventArgs) Handles ButtonAddMediaToGroup.Click
        ' Save first if required.
        If LiquidShell.LiquidAgent.SaveAndProceed(DataObject.Row, Me, My.Settings.DBConnection, False) = False Then
            Exit Sub
        End If

        ' Get a selection of data rows from the user and add the selected items.
        Dim SelectedItems As List(Of DataRow) = LookupMedia.SelectRows(My.Settings.DBConnection, True, GridMediaGroup)

        If SelectedItems.Count > 0 Then
            DataObject.AddMediaToGroupMembership(Me, My.Settings.DBConnection, SelectedItems)
        End If


    End Sub

    Private Sub ButtonRemoveMediaFromGroup_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles ButtonRemoveMediaFromGroup.Click
        DataObject.DeleteChildRow(GridMediaGroup, "MediaGroupName")
    End Sub


    Private Sub ButtonSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonSave.Click
        Save(True)
    End Sub

    Private Sub ButtonCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonCancel.Click

        If DataObject.IsDirty Then
            ' Verify that user wants to discard changes.
            Dim Message As New System.Text.StringBuilder
            Message.Append("You have made some modifications to Media Group." + vbCrLf + vbCrLf + "Are you sure you would like ")
            Message.Append("to close it and discard all your changes?")
            Dim ParentForm As LiquidShell.BaseForm = CType(TopLevelControl, LiquidShell.BaseForm)
            Dim UserResponse As DialogResult = ParentForm.ShowMessage(Message.ToString, MessageBoxButtons.YesNoCancel)
            If Not UserResponse = DialogResult.Yes Then
                ' User doesn't want to discard changes.
                Exit Sub
            Else
                DataObject.Close(Me)
            End If
        End If

     DataObject.Close(Me)
    End Sub


#Region "Methods"

    Protected Overrides Function Save() As Boolean
        ' Save the current object.
        If DataObject.IsDirty Then
            Dim GridsToAudit As New List(Of DataGridView)
            GridsToAudit.Add(GridMediaGroup)
            DataObject.Save(My.Settings.DBConnection, GridsToAudit)
        End If
        Return True
    End Function


#End Region

End Class
