<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class FormLogin
    Inherits EditorForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        Try
            If disposing AndAlso components IsNot Nothing Then
                components.Dispose()
            End If
        Finally
            MyBase.Dispose(disposing)
        End Try
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(FormLogin))
        Me.TextBoxUsername = New DevExpress.XtraEditors.TextEdit()
        Me.TextBoxPassword = New DevExpress.XtraEditors.TextEdit()
        Me.ButtonLogin = New DevExpress.XtraEditors.SimpleButton()
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.LabelControlUsername = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControlPassword = New DevExpress.XtraEditors.LabelControl()
        Me.Table = New System.Windows.Forms.TableLayoutPanel()
        Me.LabelConnectionOptions = New DevExpress.XtraEditors.LabelControl()
        Me.PanelCredentials = New System.Windows.Forms.Panel()
        Me.PanelOptions = New System.Windows.Forms.Panel()
        Me.CheckEditTestNetwork = New DevExpress.XtraEditors.CheckEdit()
        Me.TextEditDatabaseName = New DevExpress.XtraEditors.TextEdit()
        Me.TextEditServerAddress = New DevExpress.XtraEditors.TextEdit()
        Me.LabelDatabaseName = New DevExpress.XtraEditors.LabelControl()
        Me.LabelServerAddress = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkOptions = New DevExpress.XtraEditors.LabelControl()
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelButtonBar.SuspendLayout()
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBoxUsername.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextBoxPassword.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.Table.SuspendLayout()
        Me.PanelCredentials.SuspendLayout()
        Me.PanelOptions.SuspendLayout()
        CType(Me.CheckEditTestNetwork.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditDatabaseName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditServerAddress.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'PanelButtonBar
        '
        Me.PanelButtonBar.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelButtonBar.Appearance.BackColor2 = System.Drawing.Color.LightSteelBlue
        Me.PanelButtonBar.Appearance.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical
        Me.PanelButtonBar.Appearance.Options.UseBackColor = True
        Me.PanelButtonBar.Controls.Add(Me.ButtonLogin)
        Me.PanelButtonBar.Controls.Add(Me.HyperlinkOptions)
        Me.PanelButtonBar.Location = New System.Drawing.Point(0, 187)
        Me.PanelButtonBar.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Office2003
        Me.PanelButtonBar.LookAndFeel.UseDefaultLookAndFeel = False
        Me.PanelButtonBar.Size = New System.Drawing.Size(287, 52)
        Me.PanelButtonBar.TabIndex = 1
        '
        'TextBoxUsername
        '
        Me.TextBoxUsername.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextBoxUsername.EditValue = ""
        Me.TextBoxUsername.Location = New System.Drawing.Point(126, 3)
        Me.TextBoxUsername.Margin = New System.Windows.Forms.Padding(24, 3, 3, 3)
        Me.TextBoxUsername.Name = "TextBoxUsername"
        Me.TextBoxUsername.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBoxUsername.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextBoxUsername.Properties.Appearance.Options.UseFont = True
        Me.TextBoxUsername.Properties.Appearance.Options.UseForeColor = True
        Me.TextBoxUsername.Size = New System.Drawing.Size(143, 20)
        Me.TextBoxUsername.TabIndex = 1
        '
        'TextBoxPassword
        '
        Me.TextBoxPassword.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextBoxPassword.EditValue = ""
        Me.TextBoxPassword.Location = New System.Drawing.Point(126, 29)
        Me.TextBoxPassword.Margin = New System.Windows.Forms.Padding(24, 3, 3, 3)
        Me.TextBoxPassword.Name = "TextBoxPassword"
        Me.TextBoxPassword.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextBoxPassword.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextBoxPassword.Properties.Appearance.Options.UseFont = True
        Me.TextBoxPassword.Properties.Appearance.Options.UseForeColor = True
        Me.TextBoxPassword.Properties.PasswordChar = Global.Microsoft.VisualBasic.ChrW(9679)
        Me.TextBoxPassword.Size = New System.Drawing.Size(143, 20)
        Me.TextBoxPassword.TabIndex = 3
        '
        'ButtonLogin
        '
        Me.ButtonLogin.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonLogin.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonLogin.Appearance.Options.UseFont = True
        Me.ButtonLogin.ImageIndex = 0
        Me.ButtonLogin.ImageList = Me.ImageList24x24
        Me.ButtonLogin.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonLogin.Location = New System.Drawing.Point(175, 12)
        Me.ButtonLogin.Name = "ButtonLogin"
        Me.ButtonLogin.Size = New System.Drawing.Size(100, 28)
        Me.ButtonLogin.TabIndex = 1
        Me.ButtonLogin.Text = "Login"
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "unlock.png")
        Me.ImageList24x24.Images.SetKeyName(1, "key.png")
        '
        'LabelControlUsername
        '
        Me.LabelControlUsername.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControlUsername.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControlUsername.Location = New System.Drawing.Point(3, 6)
        Me.LabelControlUsername.Name = "LabelControlUsername"
        Me.LabelControlUsername.Size = New System.Drawing.Size(63, 13)
        Me.LabelControlUsername.TabIndex = 0
        Me.LabelControlUsername.Text = "Username:"
        '
        'LabelControlPassword
        '
        Me.LabelControlPassword.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControlPassword.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControlPassword.Location = New System.Drawing.Point(3, 32)
        Me.LabelControlPassword.Name = "LabelControlPassword"
        Me.LabelControlPassword.Size = New System.Drawing.Size(59, 13)
        Me.LabelControlPassword.TabIndex = 2
        Me.LabelControlPassword.Text = "Password:"
        '
        'Table
        '
        Me.Table.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Table.ColumnCount = 1
        Me.Table.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.Table.Controls.Add(Me.LabelConnectionOptions, 0, 2)
        Me.Table.Controls.Add(Me.PanelCredentials, 0, 0)
        Me.Table.Controls.Add(Me.PanelOptions, 0, 3)
        Me.Table.Location = New System.Drawing.Point(9, 9)
        Me.Table.Margin = New System.Windows.Forms.Padding(0)
        Me.Table.Name = "Table"
        Me.Table.RowCount = 4
        Me.Table.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.Table.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 15.0!))
        Me.Table.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.Table.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 100.0!))
        Me.Table.Size = New System.Drawing.Size(269, 178)
        Me.Table.TabIndex = 0
        '
        'LabelConnectionOptions
        '
        Me.LabelConnectionOptions.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelConnectionOptions.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelConnectionOptions.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelConnectionOptions.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelConnectionOptions.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelConnectionOptions.LineVisible = True
        Me.LabelConnectionOptions.Location = New System.Drawing.Point(3, 70)
        Me.LabelConnectionOptions.Margin = New System.Windows.Forms.Padding(3, 3, 3, 9)
        Me.LabelConnectionOptions.Name = "LabelConnectionOptions"
        Me.LabelConnectionOptions.Size = New System.Drawing.Size(263, 18)
        Me.LabelConnectionOptions.TabIndex = 1
        Me.LabelConnectionOptions.Text = "Connection Options"
        '
        'PanelCredentials
        '
        Me.PanelCredentials.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelCredentials.Controls.Add(Me.TextBoxUsername)
        Me.PanelCredentials.Controls.Add(Me.TextBoxPassword)
        Me.PanelCredentials.Controls.Add(Me.LabelControlPassword)
        Me.PanelCredentials.Controls.Add(Me.LabelControlUsername)
        Me.PanelCredentials.Location = New System.Drawing.Point(0, 0)
        Me.PanelCredentials.Margin = New System.Windows.Forms.Padding(0)
        Me.PanelCredentials.Name = "PanelCredentials"
        Me.PanelCredentials.Size = New System.Drawing.Size(269, 52)
        Me.PanelCredentials.TabIndex = 0
        '
        'PanelOptions
        '
        Me.PanelOptions.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelOptions.Controls.Add(Me.CheckEditTestNetwork)
        Me.PanelOptions.Controls.Add(Me.TextEditDatabaseName)
        Me.PanelOptions.Controls.Add(Me.TextEditServerAddress)
        Me.PanelOptions.Controls.Add(Me.LabelDatabaseName)
        Me.PanelOptions.Controls.Add(Me.LabelServerAddress)
        Me.PanelOptions.Location = New System.Drawing.Point(0, 97)
        Me.PanelOptions.Margin = New System.Windows.Forms.Padding(0)
        Me.PanelOptions.Name = "PanelOptions"
        Me.PanelOptions.Size = New System.Drawing.Size(269, 100)
        Me.PanelOptions.TabIndex = 2
        '
        'CheckEditTestNetwork
        '
        Me.CheckEditTestNetwork.Location = New System.Drawing.Point(3, 55)
        Me.CheckEditTestNetwork.Name = "CheckEditTestNetwork"
        Me.CheckEditTestNetwork.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditTestNetwork.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditTestNetwork.Properties.Appearance.Options.UseFont = True
        Me.CheckEditTestNetwork.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditTestNetwork.Properties.AutoWidth = True
        Me.CheckEditTestNetwork.Properties.Caption = "Test network connection"
        Me.CheckEditTestNetwork.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditTestNetwork.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditTestNetwork.Size = New System.Drawing.Size(161, 19)
        Me.CheckEditTestNetwork.TabIndex = 4
        '
        'TextEditDatabaseName
        '
        Me.TextEditDatabaseName.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditDatabaseName.EditValue = ""
        Me.TextEditDatabaseName.Location = New System.Drawing.Point(126, 29)
        Me.TextEditDatabaseName.Margin = New System.Windows.Forms.Padding(24, 3, 3, 3)
        Me.TextEditDatabaseName.Name = "TextEditDatabaseName"
        Me.TextEditDatabaseName.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditDatabaseName.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditDatabaseName.Properties.Appearance.Options.UseFont = True
        Me.TextEditDatabaseName.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditDatabaseName.Size = New System.Drawing.Size(143, 20)
        Me.TextEditDatabaseName.TabIndex = 3
        '
        'TextEditServerAddress
        '
        Me.TextEditServerAddress.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditServerAddress.EditValue = ""
        Me.TextEditServerAddress.Location = New System.Drawing.Point(126, 3)
        Me.TextEditServerAddress.Margin = New System.Windows.Forms.Padding(24, 3, 3, 3)
        Me.TextEditServerAddress.Name = "TextEditServerAddress"
        Me.TextEditServerAddress.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditServerAddress.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditServerAddress.Properties.Appearance.Options.UseFont = True
        Me.TextEditServerAddress.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditServerAddress.Size = New System.Drawing.Size(143, 20)
        Me.TextEditServerAddress.TabIndex = 1
        '
        'LabelDatabaseName
        '
        Me.LabelDatabaseName.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelDatabaseName.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelDatabaseName.Location = New System.Drawing.Point(3, 32)
        Me.LabelDatabaseName.Name = "LabelDatabaseName"
        Me.LabelDatabaseName.Size = New System.Drawing.Size(96, 13)
        Me.LabelDatabaseName.TabIndex = 2
        Me.LabelDatabaseName.Text = "Database Name:"
        '
        'LabelServerAddress
        '
        Me.LabelServerAddress.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelServerAddress.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelServerAddress.Location = New System.Drawing.Point(3, 6)
        Me.LabelServerAddress.Name = "LabelServerAddress"
        Me.LabelServerAddress.Size = New System.Drawing.Size(94, 13)
        Me.LabelServerAddress.TabIndex = 0
        Me.LabelServerAddress.Text = "Server Address:"
        '
        'HyperlinkOptions
        '
        Me.HyperlinkOptions.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkOptions.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkOptions.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkOptions.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkOptions.Location = New System.Drawing.Point(12, 20)
        Me.HyperlinkOptions.Margin = New System.Windows.Forms.Padding(3, 3, 3, 10)
        Me.HyperlinkOptions.Name = "HyperlinkOptions"
        Me.HyperlinkOptions.Size = New System.Drawing.Size(135, 13)
        Me.HyperlinkOptions.TabIndex = 0
        Me.HyperlinkOptions.Text = "Hide connection options"
        '
        'FormLogin
        '
        Me.AcceptButton = Me.ButtonLogin
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(287, 239)
        Me.Controls.Add(Me.Table)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "FormLogin"
        Me.Tag = "341, 146"
        Me.Text = "Login"
        Me.Controls.SetChildIndex(Me.PanelButtonBar, 0)
        Me.Controls.SetChildIndex(Me.Table, 0)
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelButtonBar.ResumeLayout(False)
        Me.PanelButtonBar.PerformLayout()
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBoxUsername.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextBoxPassword.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.Table.ResumeLayout(False)
        Me.PanelCredentials.ResumeLayout(False)
        Me.PanelCredentials.PerformLayout()
        Me.PanelOptions.ResumeLayout(False)
        Me.PanelOptions.PerformLayout()
        CType(Me.CheckEditTestNetwork.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditDatabaseName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditServerAddress.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents ButtonLogin As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents TextBoxUsername As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextBoxPassword As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControlUsername As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControlPassword As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents Table As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents PanelCredentials As System.Windows.Forms.Panel
    Friend WithEvents PanelOptions As System.Windows.Forms.Panel
    Friend WithEvents TextEditDatabaseName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEditServerAddress As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelDatabaseName As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelServerAddress As DevExpress.XtraEditors.LabelControl
    Friend WithEvents CheckEditTestNetwork As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents HyperlinkOptions As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelConnectionOptions As DevExpress.XtraEditors.LabelControl

End Class
