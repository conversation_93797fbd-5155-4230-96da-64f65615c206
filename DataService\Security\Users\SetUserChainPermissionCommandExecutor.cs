﻿using DataAccess;

namespace DataService.Security.Users
{
    internal class SetUserChainPermissionCommandExecutor : CommandExecutor<SetUserChainPermissionCommand>
    {

        public override void Execute(SetUserChainPermissionCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.SetUserChainPermissions))
            {
                storedprocedure.AddInputParameter("Userid", command.UserId);
                storedprocedure.AddInputParameter("ChainID", command.SelectedChains);
   
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
            }
        }

    }
}
