﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformBillingInstructions
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformBillingInstructions))
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle4 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Me.ImageList16x16 = New System.Windows.Forms.ImageList(Me.components)
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.GroupItems = New DevExpress.XtraEditors.GroupControl()
        Me.Grid = New System.Windows.Forms.DataGridView()
        Me.PeriodNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.AmountColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.PONumberColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.PeriodClosedColumn = New System.Windows.Forms.DataGridViewCheckBoxColumn()
        Me.ButtonBack = New DevExpress.XtraEditors.SimpleButton()
        Me.CheckEditOnePONumber = New DevExpress.XtraEditors.CheckEdit()
        Me.TextEditPONumber = New DevExpress.XtraEditors.TextEdit()
        Me.CheckEditSplit = New DevExpress.XtraEditors.CheckEdit()
        Me.CheckEditUpfront = New DevExpress.XtraEditors.CheckEdit()
        Me.CheckEditCustom = New DevExpress.XtraEditors.CheckEdit()
        Me.LabelBasic = New DevExpress.XtraEditors.LabelControl()
        Me.LabelList = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkClearPONumbers = New DevExpress.XtraEditors.LabelControl()
        Me.LabelContractTotal = New DevExpress.XtraEditors.LabelControl()
        Me.LabelContractTotalValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelBillingInstructionsTotal = New DevExpress.XtraEditors.LabelControl()
        Me.LabelBillingInstructionsTotalValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelDifference = New DevExpress.XtraEditors.LabelControl()
        Me.LabelDifferenceValue = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditLegendClosed = New DevExpress.XtraEditors.TextEdit()
        Me.LabelLegendClosed = New DevExpress.XtraEditors.LabelControl()
        Me.LabelReset = New DevExpress.XtraEditors.LabelControl()
        Me.LabelFirstMonth = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkFirstMonth = New DevExpress.XtraEditors.LabelControl()
        Me.LabelMonths = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditMonths = New DevExpress.XtraEditors.TextEdit()
        Me.ButtonApply = New DevExpress.XtraEditors.SimpleButton()
        CType(Me.GroupItems, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupItems.SuspendLayout()
        CType(Me.Grid, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditOnePONumber.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditPONumber.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditSplit.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditUpfront.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditCustom.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditLegendClosed.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditMonths.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!)
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Size = New System.Drawing.Size(455, 30)
        Me.LabelTitle.TabIndex = 0
        Me.LabelTitle.Text = "Contract AV105263 - Billing Instructions"
        '
        'ImageList16x16
        '
        Me.ImageList16x16.ImageStream = CType(resources.GetObject("ImageList16x16.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList16x16.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList16x16.Images.SetKeyName(0, "delete.png")
        Me.ImageList16x16.Images.SetKeyName(1, "add.png")
        Me.ImageList16x16.Images.SetKeyName(2, "accept.png")
        Me.ImageList16x16.Images.SetKeyName(3, "process.png")
        Me.ImageList16x16.Images.SetKeyName(4, "download.png")
        Me.ImageList16x16.Images.SetKeyName(5, "refresh.png")
        Me.ImageList16x16.Images.SetKeyName(6, "pencil.png")
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "back.png")
        '
        'GroupItems
        '
        Me.GroupItems.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupItems.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupItems.Appearance.Options.UseFont = True
        Me.GroupItems.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupItems.AppearanceCaption.ForeColor = System.Drawing.Color.DimGray
        Me.GroupItems.AppearanceCaption.Options.UseFont = True
        Me.GroupItems.AppearanceCaption.Options.UseForeColor = True
        Me.GroupItems.Controls.Add(Me.Grid)
        Me.GroupItems.Location = New System.Drawing.Point(12, 245)
        Me.GroupItems.LookAndFeel.SkinName = "Black"
        Me.GroupItems.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupItems.Margin = New System.Windows.Forms.Padding(3, 3, 15, 3)
        Me.GroupItems.Name = "GroupItems"
        Me.GroupItems.Size = New System.Drawing.Size(494, 242)
        Me.GroupItems.TabIndex = 15
        Me.GroupItems.Text = "Billing Instruction List"
        '
        'Grid
        '
        Me.Grid.AllowUserToAddRows = False
        Me.Grid.AllowUserToDeleteRows = False
        Me.Grid.AllowUserToOrderColumns = True
        Me.Grid.AllowUserToResizeRows = False
        DataGridViewCellStyle1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Grid.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle1
        Me.Grid.BackgroundColor = System.Drawing.Color.White
        Me.Grid.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.Grid.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.Grid.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle2.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle2.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.Grid.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle2
        Me.Grid.ColumnHeadersHeight = 22
        Me.Grid.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.Grid.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.PeriodNameColumn, Me.AmountColumn, Me.PONumberColumn, Me.PeriodClosedColumn})
        Me.Grid.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Grid.EnableHeadersVisualStyles = False
        Me.Grid.GridColor = System.Drawing.Color.White
        Me.Grid.Location = New System.Drawing.Point(2, 21)
        Me.Grid.Margin = New System.Windows.Forms.Padding(0, 0, 0, 3)
        Me.Grid.Name = "Grid"
        Me.Grid.RowHeadersVisible = False
        DataGridViewCellStyle4.ForeColor = System.Drawing.Color.DimGray
        Me.Grid.RowsDefaultCellStyle = DataGridViewCellStyle4
        Me.Grid.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.Grid.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.Grid.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.Grid.RowTemplate.Height = 19
        Me.Grid.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.Grid.ShowCellToolTips = False
        Me.Grid.Size = New System.Drawing.Size(490, 219)
        Me.Grid.StandardTab = True
        Me.Grid.TabIndex = 0
        '
        'PeriodNameColumn
        '
        Me.PeriodNameColumn.DataPropertyName = "PeriodName"
        Me.PeriodNameColumn.FillWeight = 50.0!
        Me.PeriodNameColumn.HeaderText = "Period"
        Me.PeriodNameColumn.Name = "PeriodNameColumn"
        Me.PeriodNameColumn.ReadOnly = True
        Me.PeriodNameColumn.Width = 110
        '
        'AmountColumn
        '
        Me.AmountColumn.DataPropertyName = "Amount"
        DataGridViewCellStyle3.Format = "C2"
        DataGridViewCellStyle3.NullValue = Nothing
        Me.AmountColumn.DefaultCellStyle = DataGridViewCellStyle3
        Me.AmountColumn.FillWeight = 50.0!
        Me.AmountColumn.HeaderText = "Amount"
        Me.AmountColumn.Name = "AmountColumn"
        Me.AmountColumn.ReadOnly = True
        Me.AmountColumn.Width = 120
        '
        'PONumberColumn
        '
        Me.PONumberColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.PONumberColumn.DataPropertyName = "PONumber"
        Me.PONumberColumn.HeaderText = "PO Number"
        Me.PONumberColumn.Name = "PONumberColumn"
        Me.PONumberColumn.ReadOnly = True
        '
        'PeriodClosedColumn
        '
        Me.PeriodClosedColumn.DataPropertyName = "PeriodClosed"
        Me.PeriodClosedColumn.HeaderText = "PeriodClosed"
        Me.PeriodClosedColumn.Name = "PeriodClosedColumn"
        Me.PeriodClosedColumn.Visible = False
        '
        'ButtonBack
        '
        Me.ButtonBack.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonBack.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonBack.Appearance.Options.UseFont = True
        Me.ButtonBack.ImageIndex = 0
        Me.ButtonBack.ImageList = Me.ImageList24x24
        Me.ButtonBack.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonBack.Location = New System.Drawing.Point(711, 509)
        Me.ButtonBack.LookAndFeel.SkinName = "Black"
        Me.ButtonBack.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonBack.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonBack.Name = "ButtonBack"
        Me.ButtonBack.Size = New System.Drawing.Size(100, 28)
        Me.ButtonBack.TabIndex = 99
        Me.ButtonBack.Text = "Back"
        '
        'CheckEditOnePONumber
        '
        Me.CheckEditOnePONumber.EditValue = True
        Me.CheckEditOnePONumber.Location = New System.Drawing.Point(10, 212)
        Me.CheckEditOnePONumber.Margin = New System.Windows.Forms.Padding(3, 3, 10, 3)
        Me.CheckEditOnePONumber.Name = "CheckEditOnePONumber"
        Me.CheckEditOnePONumber.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditOnePONumber.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditOnePONumber.Properties.Appearance.Options.UseFont = True
        Me.CheckEditOnePONumber.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditOnePONumber.Properties.AutoWidth = True
        Me.CheckEditOnePONumber.Properties.Caption = "Use this purchase order number for all periods:"
        Me.CheckEditOnePONumber.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditOnePONumber.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditOnePONumber.Size = New System.Drawing.Size(295, 19)
        Me.CheckEditOnePONumber.TabIndex = 12
        '
        'TextEditPONumber
        '
        Me.TextEditPONumber.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditPONumber.EditValue = ""
        Me.TextEditPONumber.Location = New System.Drawing.Point(320, 212)
        Me.TextEditPONumber.Margin = New System.Windows.Forms.Padding(3, 3, 3, 10)
        Me.TextEditPONumber.Name = "TextEditPONumber"
        Me.TextEditPONumber.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditPONumber.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditPONumber.Properties.Appearance.Options.UseFont = True
        Me.TextEditPONumber.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditPONumber.Size = New System.Drawing.Size(186, 20)
        Me.TextEditPONumber.TabIndex = 13
        '
        'CheckEditSplit
        '
        Me.CheckEditSplit.Location = New System.Drawing.Point(29, 112)
        Me.CheckEditSplit.Margin = New System.Windows.Forms.Padding(20, 3, 3, 4)
        Me.CheckEditSplit.Name = "CheckEditSplit"
        Me.CheckEditSplit.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditSplit.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditSplit.Properties.Appearance.Options.UseFont = True
        Me.CheckEditSplit.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditSplit.Properties.AutoWidth = True
        Me.CheckEditSplit.Properties.Caption = "Split billing"
        Me.CheckEditSplit.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Radio
        Me.CheckEditSplit.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditSplit.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditSplit.Properties.RadioGroupIndex = 1
        Me.CheckEditSplit.Size = New System.Drawing.Size(85, 19)
        Me.CheckEditSplit.TabIndex = 3
        Me.CheckEditSplit.TabStop = False
        '
        'CheckEditUpfront
        '
        Me.CheckEditUpfront.Location = New System.Drawing.Point(29, 86)
        Me.CheckEditUpfront.Margin = New System.Windows.Forms.Padding(20, 3, 3, 4)
        Me.CheckEditUpfront.Name = "CheckEditUpfront"
        Me.CheckEditUpfront.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditUpfront.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditUpfront.Properties.Appearance.Options.UseFont = True
        Me.CheckEditUpfront.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditUpfront.Properties.AutoWidth = True
        Me.CheckEditUpfront.Properties.Caption = "Upfront billing"
        Me.CheckEditUpfront.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Radio
        Me.CheckEditUpfront.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditUpfront.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditUpfront.Properties.RadioGroupIndex = 1
        Me.CheckEditUpfront.Size = New System.Drawing.Size(102, 19)
        Me.CheckEditUpfront.TabIndex = 2
        Me.CheckEditUpfront.TabStop = False
        '
        'CheckEditCustom
        '
        Me.CheckEditCustom.Location = New System.Drawing.Point(29, 138)
        Me.CheckEditCustom.Margin = New System.Windows.Forms.Padding(20, 3, 3, 20)
        Me.CheckEditCustom.Name = "CheckEditCustom"
        Me.CheckEditCustom.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditCustom.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditCustom.Properties.Appearance.Options.UseFont = True
        Me.CheckEditCustom.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditCustom.Properties.AutoWidth = True
        Me.CheckEditCustom.Properties.Caption = "Custom billing"
        Me.CheckEditCustom.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Radio
        Me.CheckEditCustom.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditCustom.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditCustom.Properties.RadioGroupIndex = 1
        Me.CheckEditCustom.Size = New System.Drawing.Size(104, 19)
        Me.CheckEditCustom.TabIndex = 4
        Me.CheckEditCustom.TabStop = False
        '
        'LabelBasic
        '
        Me.LabelBasic.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelBasic.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelBasic.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelBasic.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelBasic.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelBasic.LineVisible = True
        Me.LabelBasic.Location = New System.Drawing.Point(12, 58)
        Me.LabelBasic.Margin = New System.Windows.Forms.Padding(3, 4, 3, 10)
        Me.LabelBasic.Name = "LabelBasic"
        Me.LabelBasic.Size = New System.Drawing.Size(494, 18)
        Me.LabelBasic.TabIndex = 1
        Me.LabelBasic.Text = "Basic Billing Options"
        '
        'LabelList
        '
        Me.LabelList.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelList.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelList.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelList.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelList.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelList.LineVisible = True
        Me.LabelList.Location = New System.Drawing.Point(12, 181)
        Me.LabelList.Margin = New System.Windows.Forms.Padding(3, 4, 3, 10)
        Me.LabelList.Name = "LabelList"
        Me.LabelList.Size = New System.Drawing.Size(799, 18)
        Me.LabelList.TabIndex = 11
        Me.LabelList.Text = "Billing Instruction List"
        '
        'HyperlinkClearPONumbers
        '
        Me.HyperlinkClearPONumbers.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.HyperlinkClearPONumbers.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkClearPONumbers.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkClearPONumbers.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkClearPONumbers.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkClearPONumbers.AutoEllipsis = True
        Me.HyperlinkClearPONumbers.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkClearPONumbers.Location = New System.Drawing.Point(524, 215)
        Me.HyperlinkClearPONumbers.Margin = New System.Windows.Forms.Padding(3, 3, 3, 10)
        Me.HyperlinkClearPONumbers.Name = "HyperlinkClearPONumbers"
        Me.HyperlinkClearPONumbers.Size = New System.Drawing.Size(193, 13)
        Me.HyperlinkClearPONumbers.TabIndex = 14
        Me.HyperlinkClearPONumbers.Text = "Clear all purchase order numbers"
        '
        'LabelContractTotal
        '
        Me.LabelContractTotal.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelContractTotal.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelContractTotal.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelContractTotal.Location = New System.Drawing.Point(524, 422)
        Me.LabelContractTotal.Margin = New System.Windows.Forms.Padding(3, 3, 20, 10)
        Me.LabelContractTotal.Name = "LabelContractTotal"
        Me.LabelContractTotal.Size = New System.Drawing.Size(86, 13)
        Me.LabelContractTotal.TabIndex = 16
        Me.LabelContractTotal.Text = "Contract Total:"
        '
        'LabelContractTotalValue
        '
        Me.LabelContractTotalValue.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelContractTotalValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelContractTotalValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelContractTotalValue.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.LabelContractTotalValue.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelContractTotalValue.Location = New System.Drawing.Point(700, 422)
        Me.LabelContractTotalValue.Margin = New System.Windows.Forms.Padding(3, 3, 3, 10)
        Me.LabelContractTotalValue.Name = "LabelContractTotalValue"
        Me.LabelContractTotalValue.Size = New System.Drawing.Size(111, 13)
        Me.LabelContractTotalValue.TabIndex = 17
        Me.LabelContractTotalValue.Text = "R 000,000,000.00"
        '
        'LabelBillingInstructionsTotal
        '
        Me.LabelBillingInstructionsTotal.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelBillingInstructionsTotal.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelBillingInstructionsTotal.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelBillingInstructionsTotal.Location = New System.Drawing.Point(524, 448)
        Me.LabelBillingInstructionsTotal.Margin = New System.Windows.Forms.Padding(3, 3, 20, 10)
        Me.LabelBillingInstructionsTotal.Name = "LabelBillingInstructionsTotal"
        Me.LabelBillingInstructionsTotal.Size = New System.Drawing.Size(142, 13)
        Me.LabelBillingInstructionsTotal.TabIndex = 18
        Me.LabelBillingInstructionsTotal.Text = "Billing Instructions Total:"
        '
        'LabelBillingInstructionsTotalValue
        '
        Me.LabelBillingInstructionsTotalValue.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelBillingInstructionsTotalValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelBillingInstructionsTotalValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelBillingInstructionsTotalValue.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.LabelBillingInstructionsTotalValue.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelBillingInstructionsTotalValue.Location = New System.Drawing.Point(700, 448)
        Me.LabelBillingInstructionsTotalValue.Margin = New System.Windows.Forms.Padding(3, 3, 3, 10)
        Me.LabelBillingInstructionsTotalValue.Name = "LabelBillingInstructionsTotalValue"
        Me.LabelBillingInstructionsTotalValue.Size = New System.Drawing.Size(111, 13)
        Me.LabelBillingInstructionsTotalValue.TabIndex = 19
        Me.LabelBillingInstructionsTotalValue.Text = "R 000,000,000.00"
        '
        'LabelDifference
        '
        Me.LabelDifference.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelDifference.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelDifference.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelDifference.Location = New System.Drawing.Point(524, 474)
        Me.LabelDifference.Margin = New System.Windows.Forms.Padding(3, 3, 20, 10)
        Me.LabelDifference.Name = "LabelDifference"
        Me.LabelDifference.Size = New System.Drawing.Size(153, 13)
        Me.LabelDifference.TabIndex = 20
        Me.LabelDifference.Text = "Difference (must be zero):"
        '
        'LabelDifferenceValue
        '
        Me.LabelDifferenceValue.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelDifferenceValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelDifferenceValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelDifferenceValue.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.LabelDifferenceValue.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelDifferenceValue.Location = New System.Drawing.Point(700, 474)
        Me.LabelDifferenceValue.Margin = New System.Windows.Forms.Padding(3, 3, 3, 10)
        Me.LabelDifferenceValue.Name = "LabelDifferenceValue"
        Me.LabelDifferenceValue.Size = New System.Drawing.Size(111, 13)
        Me.LabelDifferenceValue.TabIndex = 21
        Me.LabelDifferenceValue.Text = "R 000,000,000.00"
        '
        'TextEditLegendClosed
        '
        Me.TextEditLegendClosed.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.TextEditLegendClosed.EditValue = ""
        Me.TextEditLegendClosed.Enabled = False
        Me.TextEditLegendClosed.Location = New System.Drawing.Point(12, 498)
        Me.TextEditLegendClosed.Margin = New System.Windows.Forms.Padding(0, 10, 9, 3)
        Me.TextEditLegendClosed.Name = "TextEditLegendClosed"
        Me.TextEditLegendClosed.Properties.Appearance.BackColor = System.Drawing.Color.LightCoral
        Me.TextEditLegendClosed.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditLegendClosed.Properties.Appearance.Options.UseBackColor = True
        Me.TextEditLegendClosed.Properties.Appearance.Options.UseFont = True
        Me.TextEditLegendClosed.Properties.AutoHeight = False
        Me.TextEditLegendClosed.Size = New System.Drawing.Size(13, 13)
        Me.TextEditLegendClosed.TabIndex = 22
        '
        'LabelLegendClosed
        '
        Me.LabelLegendClosed.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.LabelLegendClosed.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelLegendClosed.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelLegendClosed.Location = New System.Drawing.Point(34, 498)
        Me.LabelLegendClosed.Margin = New System.Windows.Forms.Padding(0, 10, 3, 3)
        Me.LabelLegendClosed.Name = "LabelLegendClosed"
        Me.LabelLegendClosed.Size = New System.Drawing.Size(328, 13)
        Me.LabelLegendClosed.TabIndex = 23
        Me.LabelLegendClosed.Text = "RED:  Amounts are locked because the period has closed"
        '
        'LabelReset
        '
        Me.LabelReset.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelReset.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelReset.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelReset.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelReset.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelReset.LineVisible = True
        Me.LabelReset.Location = New System.Drawing.Point(524, 58)
        Me.LabelReset.Margin = New System.Windows.Forms.Padding(3, 4, 3, 10)
        Me.LabelReset.Name = "LabelReset"
        Me.LabelReset.Size = New System.Drawing.Size(287, 18)
        Me.LabelReset.TabIndex = 5
        Me.LabelReset.Text = "Reset Billing Months"
        '
        'LabelFirstMonth
        '
        Me.LabelFirstMonth.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelFirstMonth.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelFirstMonth.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelFirstMonth.Location = New System.Drawing.Point(524, 89)
        Me.LabelFirstMonth.Margin = New System.Windows.Forms.Padding(3, 3, 20, 10)
        Me.LabelFirstMonth.Name = "LabelFirstMonth"
        Me.LabelFirstMonth.Size = New System.Drawing.Size(105, 13)
        Me.LabelFirstMonth.TabIndex = 6
        Me.LabelFirstMonth.Text = "First Billing Month:"
        '
        'HyperlinkFirstMonth
        '
        Me.HyperlinkFirstMonth.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.HyperlinkFirstMonth.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkFirstMonth.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkFirstMonth.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkFirstMonth.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkFirstMonth.AutoEllipsis = True
        Me.HyperlinkFirstMonth.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkFirstMonth.Location = New System.Drawing.Point(652, 89)
        Me.HyperlinkFirstMonth.Margin = New System.Windows.Forms.Padding(3, 3, 9, 10)
        Me.HyperlinkFirstMonth.Name = "HyperlinkFirstMonth"
        Me.HyperlinkFirstMonth.Size = New System.Drawing.Size(47, 13)
        Me.HyperlinkFirstMonth.TabIndex = 7
        Me.HyperlinkFirstMonth.Text = "Select..."
        '
        'LabelMonths
        '
        Me.LabelMonths.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelMonths.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelMonths.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelMonths.Location = New System.Drawing.Point(524, 115)
        Me.LabelMonths.Margin = New System.Windows.Forms.Padding(3, 3, 20, 10)
        Me.LabelMonths.Name = "LabelMonths"
        Me.LabelMonths.Size = New System.Drawing.Size(81, 13)
        Me.LabelMonths.TabIndex = 8
        Me.LabelMonths.Text = "Months to Bill:"
        '
        'TextEditMonths
        '
        Me.TextEditMonths.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditMonths.EditValue = ""
        Me.TextEditMonths.Location = New System.Drawing.Point(652, 112)
        Me.TextEditMonths.Margin = New System.Windows.Forms.Padding(3, 3, 3, 10)
        Me.TextEditMonths.Name = "TextEditMonths"
        Me.TextEditMonths.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditMonths.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditMonths.Properties.Appearance.Options.UseFont = True
        Me.TextEditMonths.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditMonths.Properties.Mask.EditMask = "n0"
        Me.TextEditMonths.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditMonths.Size = New System.Drawing.Size(159, 20)
        Me.TextEditMonths.TabIndex = 9
        '
        'ButtonApply
        '
        Me.ButtonApply.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonApply.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonApply.Appearance.Options.UseFont = True
        Me.ButtonApply.ImageIndex = 2
        Me.ButtonApply.ImageList = Me.ImageList16x16
        Me.ButtonApply.Location = New System.Drawing.Point(736, 145)
        Me.ButtonApply.LookAndFeel.SkinName = "Black"
        Me.ButtonApply.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonApply.Name = "ButtonApply"
        Me.ButtonApply.Size = New System.Drawing.Size(75, 23)
        Me.ButtonApply.TabIndex = 10
        Me.ButtonApply.Text = "Apply"
        '
        'SubformBillingInstructions
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.Controls.Add(Me.ButtonApply)
        Me.Controls.Add(Me.TextEditMonths)
        Me.Controls.Add(Me.HyperlinkFirstMonth)
        Me.Controls.Add(Me.TextEditLegendClosed)
        Me.Controls.Add(Me.LabelLegendClosed)
        Me.Controls.Add(Me.LabelDifferenceValue)
        Me.Controls.Add(Me.LabelDifference)
        Me.Controls.Add(Me.LabelBillingInstructionsTotalValue)
        Me.Controls.Add(Me.LabelBillingInstructionsTotal)
        Me.Controls.Add(Me.LabelContractTotalValue)
        Me.Controls.Add(Me.LabelMonths)
        Me.Controls.Add(Me.LabelFirstMonth)
        Me.Controls.Add(Me.LabelContractTotal)
        Me.Controls.Add(Me.HyperlinkClearPONumbers)
        Me.Controls.Add(Me.LabelList)
        Me.Controls.Add(Me.LabelReset)
        Me.Controls.Add(Me.LabelBasic)
        Me.Controls.Add(Me.CheckEditCustom)
        Me.Controls.Add(Me.CheckEditSplit)
        Me.Controls.Add(Me.CheckEditUpfront)
        Me.Controls.Add(Me.TextEditPONumber)
        Me.Controls.Add(Me.CheckEditOnePONumber)
        Me.Controls.Add(Me.ButtonBack)
        Me.Controls.Add(Me.GroupItems)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Name = "SubformBillingInstructions"
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        Me.Controls.SetChildIndex(Me.GroupItems, 0)
        Me.Controls.SetChildIndex(Me.ButtonBack, 0)
        Me.Controls.SetChildIndex(Me.CheckEditOnePONumber, 0)
        Me.Controls.SetChildIndex(Me.TextEditPONumber, 0)
        Me.Controls.SetChildIndex(Me.CheckEditUpfront, 0)
        Me.Controls.SetChildIndex(Me.CheckEditSplit, 0)
        Me.Controls.SetChildIndex(Me.CheckEditCustom, 0)
        Me.Controls.SetChildIndex(Me.LabelBasic, 0)
        Me.Controls.SetChildIndex(Me.LabelReset, 0)
        Me.Controls.SetChildIndex(Me.LabelList, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkClearPONumbers, 0)
        Me.Controls.SetChildIndex(Me.LabelContractTotal, 0)
        Me.Controls.SetChildIndex(Me.LabelFirstMonth, 0)
        Me.Controls.SetChildIndex(Me.LabelMonths, 0)
        Me.Controls.SetChildIndex(Me.LabelContractTotalValue, 0)
        Me.Controls.SetChildIndex(Me.LabelBillingInstructionsTotal, 0)
        Me.Controls.SetChildIndex(Me.LabelBillingInstructionsTotalValue, 0)
        Me.Controls.SetChildIndex(Me.LabelDifference, 0)
        Me.Controls.SetChildIndex(Me.LabelDifferenceValue, 0)
        Me.Controls.SetChildIndex(Me.LabelLegendClosed, 0)
        Me.Controls.SetChildIndex(Me.TextEditLegendClosed, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkFirstMonth, 0)
        Me.Controls.SetChildIndex(Me.TextEditMonths, 0)
        Me.Controls.SetChildIndex(Me.ButtonApply, 0)
        CType(Me.GroupItems, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupItems.ResumeLayout(False)
        CType(Me.Grid, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditOnePONumber.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditPONumber.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditSplit.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditUpfront.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditCustom.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditLegendClosed.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditMonths.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents ImageList16x16 As System.Windows.Forms.ImageList
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents GroupItems As DevExpress.XtraEditors.GroupControl
    Friend WithEvents Grid As System.Windows.Forms.DataGridView
    Friend WithEvents ButtonBack As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents CheckEditOnePONumber As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents TextEditPONumber As DevExpress.XtraEditors.TextEdit
    Friend WithEvents CheckEditSplit As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents CheckEditUpfront As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents CheckEditCustom As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents LabelBasic As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelList As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkClearPONumbers As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelContractTotal As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelContractTotalValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelBillingInstructionsTotal As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelBillingInstructionsTotalValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelDifference As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelDifferenceValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents PeriodNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents AmountColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents PONumberColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents PeriodClosedColumn As System.Windows.Forms.DataGridViewCheckBoxColumn
    Friend WithEvents TextEditLegendClosed As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelLegendClosed As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelReset As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelFirstMonth As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkFirstMonth As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelMonths As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditMonths As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ButtonApply As DevExpress.XtraEditors.SimpleButton

End Class
