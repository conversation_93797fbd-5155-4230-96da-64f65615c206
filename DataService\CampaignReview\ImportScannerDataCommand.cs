﻿using DataAccess;
using System;
using System.Collections.Generic;
using System.IO;

namespace DataService.CampaignReview
{
    class ImportScannerDataCommand : Command
    {
        public Guid SessionId { get; set; }
        public string TempFilePathForAchiveDecompression { get; set; }
        public List<string> ArchiveFileNames { get; set; }
        public Dictionary<string, int> ArchiveFileSizes { get; set; }
        public Dictionary<string, int> ArchiveFileIds { get; set; }

        public ImportScannerDataCommand(Guid sessionid, string tempfilepathforarchivedecompression, List<string> archivefilenames)
        {
            SessionId = sessionid;
            TempFilePathForAchiveDecompression = tempfilepathforarchivedecompression;
            ArchiveFileNames = archivefilenames;
            ArchiveFileSizes = new Dictionary<string, int>();
            ArchiveFileIds = new Dictionary<string, int>();

            foreach (string archivefilename in archivefilenames)
            {
                var fileinfo = new FileInfo(archivefilename);
                ArchiveFileSizes.Add(archivefilename, (int)fileinfo.Length);
                ArchiveFileIds.Add(archivefilename, -1);
            }
        }
    }
}
