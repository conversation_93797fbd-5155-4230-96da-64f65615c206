﻿Public Class InputBox

    Public Sub New(ByVal Prompt As String, ByVal AppIcon As Icon)
        ' This call is required by the Windows Form Designer.
        InitializeComponent()
        ' Add any initialization after the InitializeComponent() call.
        LabelPrompt.Text = Prompt & ":"
        Icon = AppIcon
    End Sub

    Public Sub New(ByVal Prompt As String, ByVal CurrentValue As String, ByVal AppIcon As Icon, ByVal LargeBox As Boolean)
        ' This call is required by the Windows Form Designer.
        InitializeComponent()
        ' Add any initialization after the InitializeComponent() call.
        LabelPrompt.Text = Prompt & ":"
        MemoEditInput.Text = CurrentValue
        Icon = AppIcon
        If LargeBox Then
            Size = MaximumSize
            MemoEditInput.Properties.AcceptsReturn = True
        End If

    End Sub

    Public Function GetInput() As String
        ShowDialog()
        If DialogResult = Windows.Forms.DialogResult.OK Then
            Return MemoEditInput.Text
        Else
            Return Nothing
        End If
    End Function

    Private Sub ButtonOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonOK.Click
        DialogResult = Windows.Forms.DialogResult.OK
        Me.Close()
    End Sub

    Private Sub TextEditInput_GotFocus(ByVal sender As Object, ByVal e As System.EventArgs)
        MemoEditInput.SelectAll()
    End Sub

End Class