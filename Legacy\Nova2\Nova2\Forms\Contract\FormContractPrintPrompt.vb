Public Class FormContractPrintPrompt

    ' The variable to remember what the user wants to print - a quotation or a contract.
    Public ReportTitle As String = "Contract"
    Public ReportType As String = "Logo"
    Public VAT As Decimal = 0


    Public Shared Sub Print _
    (ByVal ContractObject As Contract, _
    ByVal TelephoneNumbers As String)

        ' Create an instance of this form.
        Dim Prompt As New FormContractPrintPrompt
        If ContractObject.ContractClassificationName = "PNP Connect" Then
            Prompt.chPickNPay.Checked = True
        End If

        Dim PromptResult As DialogResult = Prompt.ShowDialog()
        Dim ReportTitle As String = Prompt.ReportTitle
        Dim ReportType As String = Prompt.ReportType
        Dim AppIcon As Icon = Prompt.Icon
        Dim VAT As Decimal = Prompt.VAT

        Dim InstallationOnly As String = String.Empty
        If ContractObject.Type = NovaData.Contract.ContractType.InstallationOnly Then
            InstallationOnly = "Installation-Only"
        ElseIf ContractObject.Type = NovaData.Contract.ContractType.Rental Then
            InstallationOnly = "Rental"
        End If

        ' Destroy the instance of this form.
        Prompt.Dispose()

        ' Configure the address parameter.
        Dim Address As String = String.Empty
        Address = "PRIMEDIA INSTORE, a Division of PRIMEDIA (Pty) Ltd, Registration Number: 2005/044403/07" _
        & vbCrLf _
        & "Primedia Place, 15 Fredman Drive, Sandown, Sandton, 2031  |  " _
        & "PO Box 2464, Florida Hills, 1716"

        ' Set legal info if this is a Primedia Africa contract.
        Dim LegalInfo As String = String.Empty
        If VAT = 0 Then
            LegalInfo = "The advertising service agreement entered into between the Advertiser and Primedia Instore, " _
                        & "Primedia Instore operates in the capacity of agent acting on behalf of Primedia Outdoor " _
                        & "Namibia (Pty) Ltd, Primedia Outdoor Botswana (Pty) Ltd and Primedia Outdoor Zambia Ltd. The " _
                        & "VAT effect is that Primedia Instore does not acquire any rights in a capacity of principal " _
                        & "to sell the advertising service, these rights remain with Primedia Outdoor Namibia (Pty) Ltd, " _
                        & "Primedia Outdoor Botswana (Pty) Ltd and Primedia Outdoor Zambia Ltd."
        End If
        ' Check the result of the dialog.
        If PromptResult = Windows.Forms.DialogResult.OK Then
            ' User clicked on OK button. Create an instance of the preview form.
            Dim ContractPreview As New NovaReports.FormReportViewer _
                (ContractObject, ReportTitle, VAT, Address, TelephoneNumbers, InstallationOnly, ReportType, LegalInfo, AppIcon, "Data Source=primenovasql\Live;Initial Catalog=NovaDB;User ID=NovaApp;Password=******************;Connect Timeout=0")
            ContractPreview.ShowDialog()
            ContractPreview.Dispose()
        End If
    End Sub

    Private Sub FormContractPrintPrompt_FormClosing(ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) Handles Me.FormClosing

        If chPickNPay.Checked Then
            ReportType = "PNP"
        End If
        ' Check if the user clicked the OK button.
        If DialogResult = Windows.Forms.DialogResult.OK Then
            ' User clicked the OK button. Remember which options were selected.
            VAT = CDec(TextEditVAT.EditValue)
            If CheckEditContract.Checked Then
                ' The user wants to print a contract.
                ReportTitle = "Contract"
            Else
                ' The user wants to print a quotation.
                ReportTitle = "Quotation"
            End If
        End If

    End Sub

End Class
