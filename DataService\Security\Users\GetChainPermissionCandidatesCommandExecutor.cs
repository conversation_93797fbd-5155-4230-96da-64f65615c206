﻿using DataAccess;

namespace DataService.Security.Users
{
    internal class GetChainPermissionCandidatesCommandExecutor : CommandExecutor<GetChainPermissionCandidatesCommand>
    {
        public override void Execute(GetChainPermissionCandidatesCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.GetChainPermissionCandidates))
            {
                storedprocedure.AddInputParameter("Userid", command.UserId);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    command.Table = storedprocedure.OutputTable;
                }
            }
        }
    }
}
