﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="ImageList16x16.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="ImageList16x16.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj00LjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAAB6
        FQAAAk1TRnQBSQFMAgEBBwEAAWQBBQFkAQUBEAEAARABAAT/AREBAAj/AUIBTQE2BwABNgMAASgDAAFA
        AwABIAMAAQEBAAEQBgABEB4AAf8BfwF7AW8BewFvAf8BfxoAAf8BfwF7AW8BnAFzDAAB/wF/Ad4CewFv
        AZwBcwH/AX9AAAH/AX8BvAF3AdkBTgHZAUoBvAF7Af8BfxYAAf8BfwF7AXMBewFfAZwBbwF7AW8B/wF/
        CAAB/wF/AZwBcwG1AVYBGAFjAd4CewFvAf8BfzoAAf8BfwG8AXcB+gFSAbABAAGwAQAB2QFKAbwBdwH/
        AX8QAAH/AX8BewFzAXsBdwHeAX8BOgFDAbcBEgGdAXMBewFzAf8BfwYAAf8BfwG9AXMB7gE9AgABCAEh
        AXoBcwHeAXcBewFrAf8BfzQAAf8BfwGcAXMBOwFbAfEBAAEyAQEBMgEBAfEBAAEaAVcBnAFzAf8BfwwA
        Ad4BewG9AXsBnAFnARkBOwHYASYB2AEeAbcBFgG3ARoBnAFrAXsBcwH/AX8EAAH/AX8BvQF3ARgBYwHn
        ARwBkAFeAVQBfwE1AXsBlwF3AZ0BbwH/AX8wAAH/AX8BmwFzAVwBZwEzAQEBMwEBAVMBAQFTAQEBMwEB
        ARIBAQFbAWMBnAFzAf8BfwgAAf8BfwHeAX8BWgFLAbcBEgG3ARYBuAEaAdgBHgHYAR4BtwEaAbcBDgGc
        AWsBvQF3BgAB3gF7Ab0BcwF3AXsBugF/AVgBfwEGAX8B4AF+AZgBdwF8AW8uAAH/AX8BnAFzAXwBZwEz
        AQEBMwEBAXQBAQF0AQEBdAEBAXQBAQEzAQEBEwEBAVwBYwGbAXMB/wF/BgABnAFzAXsBVwGWAQoBtwES
        AbcBFgG3ARYBtwEaAdgBIgG3ARYB2AEiAb0BcwHeAXsGAAH/AX8B3gF3AbwBfwGZAX8BSAF/AQABfwEA
        AX8BAAF/Ab0BdwGcAXMsAAH/AX8B/gF/AXgBLgG2AREBtgEFAXUBAQGVAQEBlQEBAXUBAQG2AQUBtgER
        ATgBJgH+AX8B/wF/BAAB3gF7Ad4BewG3ARYB+QEqAToBRwFbAVMBWwFTAfkBLgGXARIB+QEuAd4BfwG9
        AXcB/wF/CAABnQFzAboBfwGPAX8BagF/ASQBfwEAAX8B4AF+ASMBewHfAXcB3gF7LAAB3gF7Ab0BdwH/
        AX8B2gFCAVUBAQGWAQEBlgEBAVQBAQEbAVMB/wF/Ad0BewG9AXcB/wF/BAABnAF3AZwBYwFbAU8B3QF7
        AZwBcwH/AX8B/wF/AVsBTwH5AS4B/gF/Ad4CewFvAb0BdwHeAXsGAAG9AXMB3AF7AY8BfwGPAX8BawF/
        ASMBewEAAX8B4AF+AUoBewHfAXcB/wF/LgABvQF3ARsBTwF1AQEBtgEBAbYBAQF1AQEBXAFfAb0BdwoA
        Ad4BewHeAXsBvQF7Ad4BewG9AXcBWwFLAXwBVwH+AX8B/wF/AXsBbwF7AW8BnQFnAZ0BZwG9AXcIAAGd
        AXMB2gF7Aa8BfwGPAX8BSgF/ASMBewEAAX8B4AF+AY8BfwG9AXcB/wF/LAAB3gF7AZ4BbwGWAQEB1wEB
        AdcBAQGWAQEBfQFjAb0Bdw4AAb0BdwH/AX8BWwFTAdgBIgFbAUsB3gF3Ad4BcwG9AWsBfAFXAfkBLgHe
        AXcB3gF7CAAB/wF/Ad4BdwHYAX8BjwF/AY8BfwFJAX8BIAF/AQABfwHtAWIBfQFvAZwBcy4AAf4BfwFZ
        ARoBtwEBAfgBAQGXAQEBXQFXAb0BdwwAAf8BfwH/AX8BWwFPAfkBMgEaATsBGgE3ARkBMwEZATcBGQEz
        AdgBKgF8AVcBvQF3DAAB/wF/Af8BewG1AX8BjwF/AY0BfwFpAX8BVgFnAY4BUQFgAUwBvQF7Ab0BdwH/
        AX8qAAG9AXcBvgFvAbgBAQH4AQEBuAEBARwBQwGcAXMMAAG9AXcBvgFzAfkBMgE6AUMBOwFDATsBQwE6
        AUMBOgFDARoBOwFbAU8B/wF/Af8Bfw4AAd4BewH/AX8BsgF/AbQBewFbAWcBqgFdAaABWAFAAUwBagFZ
        Ab0BdwHeAXssAAG9AXcBXQFXAdgBAQHYAQEBegEaAd4BewH/AX8KAAH/AX8B/wF/Ab0BawE6AUMBOwFL
        ATsBRwE7AUcBWwFLAZ0BZwH/AX8B3gF7EgABvQF3Af8BfwEWAWsBhAFlASABZQEiAV0BQAFMAQ8BZgG9
        AXcB/wF/LAAB/wF/Ad4BewGeAWMBOgEKAbkBAQE9AUsB3gF7Ad4BewoAAf8BfwHeAXsB3gFzATsBRwGd
        AWMB/wF/Af8BfwG9AXcB/wF/FgABvQF3ATUBewEgAW0BIAFlAcABWAFPAWoB/wF/Ad4BezIAAb0BdwH/
        AX8BXgFTAXsBEgF+AVcBnAFzAf8BfwoAAf8BfwG9AXcB3gFzAd4BbwGcAXMcAAH/AX8B3gF7AfMBdgGF
        AWkBFgF3Ad4BewH/AX82AAH/AX8BvQF3Ab0BdwG9AXcB/wF/EAABvQF3Ad4BewH/AX8eAAH/AX8BvQF3
        Ab0BdwG9AXcB/wF/KgAB3gF7AZwBbwF8AW8B/wF/CAAB/wF/AXwBbwGcAW8B3gF7DgAB/wF/AXwBbwF8
        AW8BfAFvAXwBbwH/AX8WAAH/AX8B/wF/Af8BfxYAAf8BfwH/AX8B/wF/AXsBbwF7AW8B/wF/Af8BfwH/
        AX8KAAG9AXcB/wF7ARABVgHWAWYBvQFzAf8BfwQAAf8BfwG9AXMB1gFmARABVgH/AXsBvQF3CgAB/wF/
        AXwBbwE2AWMB0AFOAdABTgE1AV8BnQFzAf8BfxIAAb0BdwGdAXMBvgF3AXwBbxQAAf8BfwF8AW8BnAFz
        AZ0BcwH3AV4B9wFeAZ0BcwGcAXMBfAFvAf8BfwYAAd4BewH/AXsBawFNAQABMAEAATQB1gFmAb0BcwH/
        AX8B/wF/Ab0BcwHWAWYBAAE0AQABMAFrAU0B/wF7Ad4BewgAAf8BfwG+AXcBIwEqAeABIQEAASIBAQEm
        AZwBcwH/AX8QAAG9AXcB3wF/Aa0BRgFHATYBvQF3Ab0BdxIAAXwBbwHVAVYBUgFOAf8BfwGtATkBrQE5
        Af8BfwFzAU4BtQFaAXwBbwYAAZwBcwHOAVkBAAE0AWMBQAFBAUABAAE8AfcBagGcAXMBnAFzAfcBagEA
        ATwBQQFAAWMBQAEAATQBzgFZAZwBcwgAAf8BfwG+AXcBRAEyAUQBMgFEATIBQwEuAbwBcwH/AX8OAAHe
        AXsB3wF/AcwBRgEAASIBAAEiAcwBRgHeAXsB/wF/DAAB/wF/AXsBbwH/AX8BcwFSAYwBMQEQAUIBzgE5
        Ac4BPQEQAUIBjAExAXMBTgH/AX8BewFvAf8BfwIAAb0BcwExAWIBAAE8AWIBRAFjAUQBQQFEAQABQAE5
        AW8BOQFvAQABQAFBAUQBYwFEAWIBRAEAATwBMQFiAZwBcwIAAf8BfwH/AX8B/wF/Af8BfwHeAXsBZAE2
        AWMBMgFkATIBYwEyAbwBdwHeAXsB/wF/Af8BfwH/AX8GAAHeAXsB/wF/Ac0BSgEgASoBZAEyAWQBMgFB
        ASoBeQFrAZwBcwoAAf8BfwGcAXMB9wFeAfcBXgGUAVIB7gE9Ae8BPQExAUYBMQFGAe8BPQHvAT0BlAFS
        AfcBXgH3AV4BnAFzAf8BfwH/AX8B/wF7ARABYgEAAUABYgFIAYMBSAFCAUgBYgFIAWIBSAFCAUgBgwFI
        AWIBSAEAAUABEAFiAf8BewH/AX8B/wF/AXwBcwHfAXsB3wF7Ad8BfwHfAX8BpQE6AYMBNgGEATYBgwE2
        Ab4BewHfAX8B3wF7Ad8BewGdAXMB/wF/AgAB3gF7Af8BfwEOAU8BYAEqAYQBNgGEATYBhAE2AWIBMgGn
        AToB3wF/Af8BfwoAAb0BdwFSAUoBzgE5ARABQgFSAUoB1gFaAfcBXgH3AV4B1gFaAVIBSgEQAUIBzgE5
        AVIBSgG9AXcEAAH/AX8B/wF/ATABYgEAAUQBYgFMAYMBTAFiAUwBYgFMAYMBTAFiAUwBAAFEATABYgH/
        AX8B/wF/AgABnAFzAXYBZwGlAToBxgE+AccBPgHHAUIBpAE6AaQBOgGkAToBpAE2AccBPgHHAT4BxgE+
        AaQBOgFUAV8BfAFzAf8BfwH/AX8BLgFTAYABLgGkAToBpAE6AaMBNgGkATYBpAE6AYABLgFSAVsBnQF3
        Af8BfwYAAf8BfwG+AXcB3gF7ATEBSgExAUYB9wFeARgBYwHeAXcB3gF3ARgBYwH3AV4BMQFGATEBSgHe
        AXsBvgF3Af8BfwQAAf8BfwHeAXsBUgFmAUEBTAFjAVABgwFQAYMBUAFjAVABQQFMAVIBZgHeAXsB/wF/
        BAABnAFzAS0BUwGgATIBwwE6AcMBOgHDAToBxAE+AcQBPgHEAT4BxAE+AcMBOgHDAToBwwE6AaABMgEK
        AUsBnAFzAZwBcwFQAVsBoAEuAcQBOgHEAT4BoQE2ASwBTwHEAToBxAE6AcQBOgGiATYBvQF3AZwBcwYA
        AZwBcwFaAWsBlAJSAUoBlAFWARgBYwHeAXsB/wF/Af8BfwHeAXsBGAFjAZQCUgFKAZQBUgFaAWsBnAFz
        BAAB/wF/AZwBcwFaAXcBQQFQAWMBVAGDAVQBgwFUAWMBVAFBAVABWgF3AZwBcwH/AX8EAAGcAXMBTQFT
        AcEBNgHkAT4B5AE+AeQBPgHkAT4B5AE+AeQBPgHkAT4B5AE+AeQBPgHkAT4BwgE6ASsBTwGcAXMBnAFz
        AXIBXwHAATIB5AE+AcABNgFOAVcB/wF/AXIBXwHAATYB5QE+AeIBOgEJAUsB/wF/Af8BfwQAAZwBcwEY
        AWMBUQFKAXMBTgHWAVoBGAFnAd4BewH/AX8B/wF/Ad4BewE5AWcB1QFaAXMBTgFRAUoBGAFjAZwBcwIA
        Af8BfwGcAXMBOAFzASABVAFiAVQBgwFYAYMBWAGDAVgBgwFYAWIBVAEgAVQBOAFzAb0BdwH/AX8CAAGc
        AXMBcQFfAcABNgHhAToB4QE6AeIBPgHjAUIBBAFDAQQBQwHjAUIB4gE+AeEBOgHhAToBwAE2AU4BVwGc
        AXMB/wF/Af8BfwFwAVsBwAEyAUwBUwH/AX8B3gF7Af8BfwEGAUcB4gE+AQQBQwHAAToBcQFfAd4BewH/
        AX8CAAH/AX8B/wF/Ad4BewGUAVIBlAFWATkBZwF7AW8BvQF3Ab0BdwF7AW8BOQFnAZQBVgGUAVIB3gF7
        Af8BfwH/AX8B/wF/AZwBcwE4AXcBIAFYAUEBWAGDAVwBYgFcASABWAEgAVgBYgFcAYMBXAFBAVgBIAFY
        ATgBdwGcAXMB/wF/Af8BfwH/AX8B2wF3AdsBdwHcAXcB2wF3AQQBQwEDAUMBAwFDAQIBQwG6AXMB3AF3
        AdsBdwG7AXcB/wF/Ad4BewIAAf8BfwH/AX8BugFzAf8BfwHeAXsCAAG9AXcB2gFzAeABPgEDAUMBAwFD
        AeABOgG3AW8BnAFzAf8BfwH/AX8BvQF3ARgBYwGUAVIBlAFWAfcBXgFaAWsBWgFrAVoBawFaAWsB9wFe
        AZQBVgGUAVIBOQFjAb0BdwH/AX8BnAFzARgBdwEAAVwBQQFcAYMBYAFiAWABAAFcAXMBcgFzAXIBAAFc
        AWIBYAGDAWABQQFcAQABXAEYAXcBnAFzAgAB/wF/Ad4BewHeAXsB3gF7Af8BfwEDAUcBAgFHAQMBRwEC
        AUMB/wF/Ab0BdwHeAXsB3gF7Af8BfwgAAd4BewH/AX8GAAHeAXsBkgFjAeABPgEDAUcBAgFHAeABPgG4
        AW8BnAFzAf8BfwH/AX8BGAFjARgBYwEYAWMBtQFWAfcBXgEYAWMBGAFjAfcBXgG1AVYBGAFjARgBYwEY
        AWMB/wF/Af8BfwG9AXcBiwFpAQABXAGDAWQBYwFgAQABYAExAXIB3gF7Ad4BewExAXIBAAFgAYMBYAGD
        AWQBAAFcAYsBbQGcAXMIAAH/AX8B/wF/AQMBSwECAUcBAgFHAQEBRwH+AXsB/wF/GAAB/wF/Af8BfwFJ
        AVcBAAFDAQIBSwHgAT4BbQFfAZwBcwIAAd4BewHeAXsB/wF/AXsBbwHWAVoB9wFeAdYBXgHWAV4B9wFe
        AdYBWgF7AW8B/wF/Ad4BewHeAXsCAAG9AXcB3gF/AcUBaAEAAWQBAAFkATABcgH/AX8B/wF/Af8BfwH/
        AX8BMAFyAQABZAEAAWQBxQFoAd4BfwHeAXsIAAH/AX8B/wF/AQABRwEAAUcBAAFHAQABQwH+AXsB3gF7
        GgAB3gF7Af8BfwElAVMBAAFDAZEBZwH/AX8B3gF7BgABvQF3AVoBawEYAWMB3wF/ARgBYwEYAWMB/wF/
        ARgBYwFaAWsBvQF3CAABvQF3Af8BfwEIAW0BMAF2Af8BfwH/AX8EAAH/AX8B/wF/ATEBdgEIAW0B/wF/
        Ab0BdwoAAf8BfwH/AX8BjgFjAUUBUwFFAVMBawFfAf8BfwH/AX8cAAG9AXcB/wF/Af4BfwG9AXcB/wF/
        CAAB3gF7Af8BfwH/AX8B/wF/AXsBbwF7AXMB/wF/Af8BfwH/AX8B3gF7CgABvQF3Ab0BdwG9AXcB/wF/
        CAAB/wF/Ab0BdwG9AXcBvQF3DgAB/wF/Ab0BdwG9AXcBvQF3Ab0BdwHeAXsgAAHeAXsBvQF3Af8BfxAA
        Af8BfwG9AXcBvQF3Af8BfwH/AX8KAAFCAU0BPgcAAT4DAAEoAwABQAMAASADAAEBAQABAQYAAQEWAAP/
        AQAB/AE/Af4BPwEHAf8CAAH4AR8B/AEPAQEB/wIAAfABDwHwAQcBAAF/AgAB4AEHAeABAwEAAT8CAAHA
        AQMBwAEDAYABPwIAAYABAQHAAQMBgAEfAgABgAEBAYABAwHAAQ8CAAHAAQEBgAEBAcABBwIAAfABDwGA
        AQEB4AEDAgAB8AEPAeABAQHgAQMCAAH4AQ8BwAEDAfADAAH4AQ8BwAEDAfgDAAH8AQcBwAEHAfwDAAH8
        AQMB4AEPAf4BAQIAAf8BAQHwAX8B/gEDAgAB/wGDAfwBfwH/AQcCAALDAfgBHwH8AX8B8AEPAoEB8AEP
        AfgBfwHgAQcCAAHwAQ8B8AE/AeABBwIAAfABDwHgAR8BgAEBAgABgAEBAcABHwYAAYABDwGAAQEBgAEB
        AwABBwIAAcABAwMAAQcCAAHAAQMDAAEDAgABgAEBAwABAQYAAYIFAAGAAQEB5wUAAfABDwH/AQABgAEB
        AgAB8AEPAf8BgAHgAQcCgQHwAQ8B/wHBAeABBwLDAfgBHwH/AeMB/AEfCw==
</value>
  </data>
  <metadata name="CategoryNameColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="FirstMonthColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="LastMonthColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="MonthsColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="NetFeeColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="ImageList24x24.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>153, 17</value>
  </metadata>
  <data name="ImageList24x24.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj00LjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAACm
        BgAAAk1TRnQBSQFMAwEBAAHYAQAB2AEAARgBAAEYAQAE/wERAQAI/wFCAU0BNgcAATYDAAEoAwABYAMA
        ARgDAAEBAQABEAYAARLiAAH/AX8B/wF/Af8Bf7gAAf8BfwGcAXMBWwFvAXsBbwH/AX+0AAH/AX8BWwFr
        Ad8BewG9AXcBvgF3AZwBcwH/AX+wAAH/AX8BewFvAd8BfwETAVsB4AEhAYsBQgHfAX8BnAFzrgAB/wF/
        AXsBbwHfAX8BNQFfAQABJgECASYBRQEyAd8CewFvAf8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/
        AX8B/wF/mgAB/wF/AXsBbwHfAX8BNAFfASEBJgFEAS4BQwEuAWcBNgHfAX8BvgF3AZwBcwGdAXMBnQFz
        AZ0BcwGdAXMBnQFzAZ0BcwGdAXMBewFvAb0Bd5YAAf8BfwF7AW8B/wF/AVYBYwFAASoBYwEyAWQBNgFj
        ATIBhwE6AZwBcwGaAW8BmgFvAZoBbwGaAW8BmgFvAZoBbwGaAW8BmgFvAZoBbwG+AXcBvgF7Ad4Be5IA
        Af8BfwF7AW8B/wF/AXYBZwFhAS4BgwEyAYQBNgGEATYBhAE2AYQBNgFiAS4BYQEuAWEBLgFhAS4BYQEu
        AWEBLgFhAS4BYQEuAWEBLgFAASoBpwE+Ad8BewF8AW+SAAGcAXMB3wF/AXYBZwFhAS4BgwE2AaQBOgGk
        AToBpAE6AaQBOgGkAToBpAE6AaQBOgGkAToBpAE6AaQBOgGkAToBpAE6AaQBOgGkAToBhAE2AYMBMgG+
        AXcBfAFvkAAB/wF/Ad8BfwG7AXMBgQEyAaMBNgHEAToBxAE6AcQBOgHEAToBxAE6AcQBOgHEAToBxAE6
        AcQBOgHEAToBxAE6AcQBOgHEAToBxAE6AcQBOgGkAToBpAE6Ad4BewF8AW+QAAGcAXMB/wF/AQkBSwGh
        ATYBxAE+AcQBPgHEAT4BxAE+AcQBPgHEAT4BxAE+AcQBPgHEAT4BxAE+AcQBPgHEAT4BxAE+AcQBPgHE
        AT4BxAE+AcQBOgHEAToB3gF7AXwBc5AAAZwBcwH/AX8B5QFCAeIBOgHlAT4B5AE+AeQBPgHkAT4B5AE+
        AeQBPgHkAT4B5AE+AeQBPgHkAT4B5AE+AeQBPgHkAT4B5AE+AeQBPgHlAT4B5AE+AeQBPgHfAXsBnAFz
        kAAB3gF7Af8BfwFyAWMBwAE2AQQBQwEEAUMBBAFDAQQBQwEEAUMBBAFDAQQBQwEEAUMBBAFDAQQBQwEE
        AUMBBAFDAQQBQwEEAUMBBAFDAQQBQwHjAT4B5AE+Ad8BfwGcAXOSAAG9AXcB/wF/ASoBTwHgAToBBAFD
        AQQBQwEEAUMBBAFDAQQBQwEEAUMBBAFDAQQBQwEEAUMBBAFDAQQBQwEEAUMBBAFDAQQBQwEEAUMB4wFC
        AeMBQgHfAX8BnAFzkgAB/wF/Ad8BewH/AX8BKQFTAeABOgEDAUMBAwFHAQMBRwEEAUMBAwFDAeEBPgHh
        AT4B4QE+AeEBPgHhAT4B4QE+AeEBPgHhAT4B4QE+AeABPgHhAT4B/wF/AZwBc5QAAf8BfwH/AX8B/wF/
        ASkBUwHgAT4BAwFHAQMBRwECAUcBAwFHAUwBWwFLAVcBSwFXAUsBVwFLAVcBSwFXAUsBVwFLAVcBSwFX
        AUoBUwGVAWsB/wF/Ab0Bd5YAAf8BfwH/AX8B/wF/AScBUwHgAUIBAwFLAQEBRwEmAU8B/wF/Af8BfwH/
        AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwG9AXeaAAH/AX8B/wF/Af8BfwFHAVMBAAFD
        AQEBRwEkAU8B/wF/AZwBcwH/AX8B3gF7Ad4BewHeAXsB3gF7Ad4BewHeAXsB3gF7Af8Bf54AAf8BfwH/
        AX8B/wF/AUYBUwHgAT4BJQFTAf8BfwG9AXeyAAH/AX8B/wF/Af8BfwGRAWcB2gF3Af8BfwH/AX+0AAH/
        AX8B3gF7Af8BfwH/AX8B3gF7uAAB3gF7Ad4BewHeAXv/AGsAAUIBTQE+BwABPgMAASgDAAFgAwABGAMA
        AQEBAAEBBQABIAEBFgAD/wEAA/8JAAH/AR8B/wkAAf4BDwH/CQAB/AEHAf8JAAH4AQcB/wkAAfABAAED
        CQAB4AEAAQEJAAHACwABgAsAAYA7AAGACwABgAsAAcALAAHgAQABAQkAAfABAAEDCQAB+AEHAf8JAAH8
        AQcB/wkAAf4BDwH/CQAB/wEfAf8JAAP/CQAL
</value>
  </data>
</root>