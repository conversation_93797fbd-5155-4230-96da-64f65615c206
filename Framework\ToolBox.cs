﻿using System.Drawing;
using System.Linq;

namespace Framework
{
    public static class ToolBox
    {

        public static Color GetLighterShadeOfColor(Color color, int lightenfactor)
        {
            int R = color.R + lightenfactor;
            int G = color.G + lightenfactor;
            int B = color.B + lightenfactor;

            // None of the values may be less than zero or greater than 255.
            R = (R < 0 ? 0 : R);
            R = (R > 255 ? 255 : R);
            G = (G < 0 ? 0 : G);
            G = (G > 255 ? 255 : G);
            B = (B < 0 ? 0 : B);
            B = (B > 255 ? 255 : B);

            // Return the new color
            return Color.FromArgb(R, G, B);
        }

        public static string SplitStringByCapitalLetters(string stringtosplit)
        {
            return string.Concat(stringtosplit.Select(c => char.IsUpper(c) ? " " + c.ToString() : c.ToString())).TrimStart();
        }

        public static string FormatStringAsPhoneNumber(string phonenumber)
        {
            string returnvalue = "phone format error";
            long number = 0;
            if (long.TryParse(phonenumber, out number))
            {
                returnvalue = number.ToString("************");
            }
            return returnvalue;
        }

    }
}
