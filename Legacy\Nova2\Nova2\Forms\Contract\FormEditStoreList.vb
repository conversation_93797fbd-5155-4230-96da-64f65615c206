Public Class FormEditStoreList

    Private DataObject As Burst
    Private StoreUniverseBindingSource As BindingSource
    Private StorePoolBindingSource As BindingSource
    Private SourceBurstStoreListBindingSource As BindingSource
    Private IndependentStoreListBindingSource As BindingSource
    Private StorePoolView As DataView
    Private StoreListStoresView As DataView
    Private CheckEditConfirmed As CheckEdit
    ' Grid cell highlight color for stores that don't allow the selected media service.
    Private MediaNotAllowedStoreBackColor As Color = Color.LightSalmon
    Private MediaNotAllowedStoreSelectionBackColor As Color = Color.Coral
    Private MediaNotAllowedStoreForeColor As Color = Color.Black
    Private MediaNotAllowedStoreSelectionForeColor As Color = Color.White
    ' Grid cell highlight color for dormant stores.
    Private DormantStoreBackColor As Color = Color.LightCoral
    Private DormantStoreSelectionBackColor As Color = Color.IndianRed
    Private DormantStoreForeColor As Color = Color.Black
    Private DormantStoreSelectionForeColor As Color = Color.White
    ' Grid cell highlight color for stores already taken.
    Private TakenStoreBackColor As Color = Color.Khaki
    Private TakenStoreSelectionBackColor As Color = Color.Goldenrod
    Private TakenStoreForeColor As Color = Color.Black
    Private TakenStoreSelectionForeColor As Color = Color.Black
    ' Grid cell highlight color for media not allowed for selected category for the store.
    Private StoreMediaCategoryBackColor As Color = Color.Purple
    Private StoreMediaCategorySelectionBackColor As Color = Color.PaleVioletRed
    Private StoreMediaCategoryForeColor As Color = Color.White
    Private StoreMediaCategorySelectionForeColor As Color = Color.Black

#Region "Properties"

    Public ReadOnly Property RemainingStoreListQty() As Integer
        Get
            Return DataObject.InstallStoreQty - StoreListStoresView.Count
        End Get
    End Property

    Public ReadOnly Property RemainingStorePoolQty() As Integer
        Get
            If StorePoolView.Count <= RemainingStoreListQty Then
                Return StorePoolView.Count
            Else
                Return RemainingStoreListQty
            End If
        End Get
    End Property

    Public ReadOnly Property RemainingStoreUniverseQty() As Integer
        Get
            Dim SpaceLeftInTheStorePool As Integer = DataObject.StorePoolCapacity - StoreListStoresView.Count - StorePoolView.Count
            If SpaceLeftInTheStorePool >= RemainingStoreListQty Then
                Return RemainingStoreListQty
            Else
                Return SpaceLeftInTheStorePool
            End If
        End Get
    End Property

#End Region

#Region "Public Methods"

    Public Shared Function EditStoreList(ByVal CurrentBurst As Burst, CheckEditConfirmed As CheckEdit) As DialogResult

        ' Refresh the store universe list.
        CurrentBurst.UpdateStoreTable()

        ' Display the form.
        Dim EditForm As New FormEditStoreList(CurrentBurst, CheckEditConfirmed)
        Dim Result As DialogResult = EditForm.ShowDialog()

        ' Dispose the instance of the form.
        EditForm.Dispose()

        ' Return the result.
        Return Result

    End Function

    Public Sub New(ByVal CurrentBurst As Burst, CheckEditStoreListConfirmed As CheckEdit)

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        DataObject = CurrentBurst
        CheckEditConfirmed = CheckEditStoreListConfirmed

    End Sub

#End Region

#Region "Event Handlers"

    Private Sub FormEditStoreList_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        ' Update the form title.
        Text = "Edit the " & DataObject.ChainName & " Store List of " & DataObject.ParentContract.ContractNumber

        ' Load data.
        InitializeGridData()
        PerformDataBinding()

        ' Initialize the hyperlinks
        LiquidAgent.EnableHyperlink(HyperlinkStoreUniverse, False)

        ' Initialize the legend items.
        TextEditLegendDormant.BackColor = DormantStoreBackColor
        TextEditLegendDormant.ForeColor = DormantStoreForeColor
        TextEditLegendMediaNotAllwed.BackColor = MediaNotAllowedStoreBackColor
        TextEditLegendMediaNotAllwed.ForeColor = MediaNotAllowedStoreForeColor
        TextEditLegendTaken.BackColor = TakenStoreBackColor
        TextEditLegendTaken.ForeColor = TakenStoreForeColor
        TextEditLegendMediaNotAllowedForCategory.BackColor = StoreMediaCategoryBackColor
        TextEditLegendMediaNotAllowedForCategory.ForeColor = StoreMediaCategoryForeColor
        LabelLegendMediaNotAllwed.Text = "ORANGE:  Store doesn't allow '" & DataObject.MediaName & "'"

    End Sub

    Private Sub ButtonOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonOK.Click
        SaveAndClose()
    End Sub

    Private Sub HyperlinkStoreUniverse_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles HyperlinkStoreUniverse.Click

        ' Set the datasource of the available stores grid.
        CType(GridAvailableStores.Tag, GridManager).GridBindingSource = StoreUniverseBindingSource

        ' Modify the appearance of the selected hyperlink to show that it has been selected, and
        ' deselect any previously selected hyperlink.
        LiquidAgent.EnableHyperlink(HyperlinkStoreUniverse, False)
        LiquidAgent.EnableHyperlink(HyperlinkStorePool, True)
        LiquidAgent.EnableHyperlink(HyperlinkBurst, True)

    End Sub

    Private Sub HyperlinkStorePool_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles HyperlinkStorePool.Click

        ' Set the datasource of the available stores grid.
        CType(GridAvailableStores.Tag, GridManager).GridBindingSource = StorePoolBindingSource

        ' Modify the appearance of the selected hyperlink to show that it has been selected, and
        ' deselect any previously selected hyperlink.
        LiquidAgent.EnableHyperlink(HyperlinkStoreUniverse, True)
        LiquidAgent.EnableHyperlink(HyperlinkStorePool, False)
        LiquidAgent.EnableHyperlink(HyperlinkBurst, True)

    End Sub

    Private Sub HyperlinkBurst_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles HyperlinkBurst.Click

        ' Get a burst selection from the user.
        Dim ColumnsToHide As New List(Of String)
        ColumnsToHide.Add("ChainNameColumn")
        Dim SourceBurst As List(Of DataRow) = LookupBurst.SelectRows _
        (My.Settings.DBConnection, False, Nothing, DataObject.Row("BurstID"), DataObject.ChainID, ColumnsToHide)

        ' Exit if nothing was selected.
        If SourceBurst.Count = 0 Then
            Exit Sub
        End If

        ' A table to hold the source burst's store list.
        Dim SourceStoreList As New DataSetContract.StoreListDataTable

        ' Check if the selected burst is a sibling of the current bust or not.
        If Object.Equals(DataObject.ParentContract.ContractID, SourceBurst(0)("ContractID")) Then
            ' The selected burst is a sibling. Get the required storelist from the dataset.
            Dim Filter As String = "BurstID = '" & SourceBurst(0)("BurstID").ToString & "'"
            Dim SiblingStoreLists As DataTable = DataObject.Row.Table.DataSet.Tables("StoreList")
            Dim SiblingStoreListStores() As DataRow = SiblingStoreLists.Select(Filter, String.Empty)
            For Each SiblingStoreListStore As DataRow In SiblingStoreListStores
                Dim NewRow As DataRow = SourceStoreList.NewRow
                NewRow("BurstID") = SiblingStoreListStore("BurstID")
                NewRow("StoreID") = SiblingStoreListStore("StoreID")
                SourceStoreList.Rows.Add(NewRow)
            Next
            SourceStoreList.AcceptChanges()
        Else
            ' The selected burst is not a sibling. Get the required store list from the database.
            Dim SourceStoreListAdapter As New DataSetContractTableAdapters.StoreListTableAdapter
            Dim SqlCon As New SqlClient.SqlConnection(My.Settings.DBConnection)
            SourceStoreListAdapter.Connection = SqlCon
            Try
                SourceStoreListAdapter.FillByBurst(SourceStoreList, SourceBurst(0).Item("BurstID"))
            Catch ex As Exception
                ShowMessage(ex.Message, "Well, THAT Didn't Work Too Well", MessageBoxIcon.Error)
                Exit Sub
            End Try
        End If

        ' Clear the existing SourceBurstStoreListMember flags from all stores.
        For Each Store As DataRow In DataObject.StoreTable.Rows
            Store("SourceBurstStoreListMember") = False
        Next

        ' Flag all stores in the source store list as members of the store list.
        For Each SourceStore As DataRow In SourceStoreList.Rows
            Dim StoreRow As DataRow = DataObject.StoreTable.Rows.Find(SourceStore("StoreID"))
            If Not IsNothing(StoreRow) Then
                StoreRow("SourceBurstStoreListMember") = True
            End If
        Next

        ' Set the datasource of the available stores grid.
        CType(GridAvailableStores.Tag, GridManager).GridBindingSource = SourceBurstStoreListBindingSource

        ' Modify the appearance of the selected hyperlink to show that it has been selected, and
        ' deselect any previously selected hyperlink.
        LiquidAgent.EnableHyperlink(HyperlinkStoreUniverse, True)
        LiquidAgent.EnableHyperlink(HyperlinkStorePool, True)

    End Sub

    Private Sub ButtonAdd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonAdd.Click

        ' Stop if no rows were selected.
        If GridAvailableStores.SelectedRows.Count = 0 Then
            CType(TopLevelControl, BaseForm).ShowMessage("Please select at least one store from the list before using " _
            & "the 'Add' button.", "Nothing Selected")
            Exit Sub
        End If

        ' Stop if the store list has been confirmed already.
        If Not ConfirmedStatusValidated() Then
            Exit Sub
        End If

        ' Check how many stores have been selected.
        Dim SelectedStoreQty As Integer = GridAvailableStores.SelectedRows.Count
        ' Check how many stores can still be added to the store pool.
        Dim FreePoolSpace As Integer = DataObject.StorePoolCapacity - (StoreListStoresView.Count + StorePoolView.Count)
        ' Check if the selected stores already belong to the store pool.
        Dim SelectedStoresBelongToTheStorePool As Boolean = False
        If CType(GridAvailableStores.SelectedRows(0).DataBoundItem, DataRowView).Row("StorePoolMember") Then
            SelectedStoresBelongToTheStorePool = True
        End If

        ' Check if there is still room in the store pool and store list for more stores.
        If SelectedStoresBelongToTheStorePool = False AndAlso SelectedStoreQty > FreePoolSpace Then
            ' Too many stores selected for store pool.
            ShowMessage("You have selected " & SelectedStoreQty.ToString & " store(s) to add to the store list. However, " _
            & "there remains space for only " & FreePoolSpace.ToString & " more store(s) in the store pool.", "Store Pool Explosion Imminent", _
              MessageBoxIcon.Error)
            Exit Sub
        End If
        If SelectedStoreQty > RemainingStoreListQty Then
            ' Too many stores selected for store list.
            ShowMessage("You have selected " & SelectedStoreQty.ToString & " store(s) to add to the store list. However, " _
            & "there remains space for only " & RemainingStoreListQty.ToString _
            & " more store(s) in the store list.", "Store List Destruction Imminent", MessageBoxIcon.Error)
            Exit Sub
        End If

        ' Add all selected stores to the store list.
        For Each SelectedStoreRow As DataGridViewRow In GridAvailableStores.SelectedRows
            ' Flag the store row as being a store list member.
            Dim SelectedRow As DataRow = CType(SelectedStoreRow.DataBoundItem, DataRowView).Row
            SelectedRow("StoreListMember") = True
        Next

        LabelRemainingQtyValue.DataBindings("Text").ReadValue()
        LabelRemainingUniverseQtyValue.DataBindings("Text").ReadValue()
        LabelRemainingStorePoolQtyValue.DataBindings("Text").ReadValue()

    End Sub

    Private Sub ButtonRemove_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonRemove.Click

        ' Stop if no rows were selected.
        If GridStoreList.SelectedRows.Count = 0 Then
            CType(TopLevelControl, BaseForm).ShowMessage("Please select at least one store from the list before using " _
            & "the 'Remove' button.", "Nothing Selected")
            Exit Sub
        End If

        ' Stop if the store list has been confirmed already.
        If DataObject.StoreListConfirmed Then
            CType(TopLevelControl, BaseForm).ShowMessage("This store list has been confirmed and, therefore, may not " _
            & "be modified.", "Store List Confirmed")
            Exit Sub
        End If

        ' Remove all selected stores from the store list.
        For Each SelectedStoreRow As DataGridViewRow In GridStoreList.SelectedRows
            ' Flag the store row as not being a store list member.
            Dim SelectedRow As DataRow = CType(SelectedStoreRow.DataBoundItem, DataRowView).Row
            SelectedRow("StoreListMember") = False
        Next

        LabelRemainingQtyValue.DataBindings("Text").ReadValue()
        LabelRemainingUniverseQtyValue.DataBindings("Text").ReadValue()
        LabelRemainingStorePoolQtyValue.DataBindings("Text").ReadValue()
        LiquidAgent.ControlValidation(TableLayoutPanel1, String.Empty)

    End Sub

    Private Sub Grid_CellFormatting _
    (ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellFormattingEventArgs) _
    Handles GridAvailableStores.CellFormatting, GridStoreList.CellFormatting

        ' Get the grid that raised the event.
        Dim Grid As DataGridView = CType(sender, DataGridView)

        ' Get the datarow containing the cell being formatted.
        Dim GridRow As DataGridViewRow = Grid.Rows(e.RowIndex)

        ' Get the datarow.
        Dim Row As DataRow = CType(GridRow.DataBoundItem, DataRowView).Row

        ' Check row properties.
        If Row("Dormant") Then
            ' This store is dormant. Highlight it.
            GridRow.DefaultCellStyle.BackColor = DormantStoreBackColor
            GridRow.DefaultCellStyle.SelectionBackColor = DormantStoreSelectionBackColor
            GridRow.DefaultCellStyle.ForeColor = DormantStoreForeColor
            GridRow.DefaultCellStyle.SelectionForeColor = DormantStoreSelectionForeColor
        ElseIf Row("MediaAllowed") = False Then
            ' This store does not allow the selected media service. Highlight it.
            GridRow.DefaultCellStyle.BackColor = MediaNotAllowedStoreBackColor
            GridRow.DefaultCellStyle.SelectionBackColor = MediaNotAllowedStoreSelectionBackColor
            GridRow.DefaultCellStyle.ForeColor = MediaNotAllowedStoreForeColor
            GridRow.DefaultCellStyle.SelectionForeColor = MediaNotAllowedStoreSelectionForeColor
        ElseIf Row("Taken") Then
            ' This store does not allow the selected media service. Highlight it.
            GridRow.DefaultCellStyle.BackColor = TakenStoreBackColor
            GridRow.DefaultCellStyle.SelectionBackColor = TakenStoreSelectionBackColor
            GridRow.DefaultCellStyle.ForeColor = TakenStoreForeColor
            GridRow.DefaultCellStyle.SelectionForeColor = TakenStoreSelectionForeColor
        ElseIf Row("MediaAllowedInCategoryForStore") Then
            ' This store does not allow the selected media service. Highlight it.
            GridRow.DefaultCellStyle.BackColor = StoreMediaCategoryBackColor
            GridRow.DefaultCellStyle.SelectionBackColor = StoreMediaCategorySelectionBackColor
            GridRow.DefaultCellStyle.ForeColor = StoreMediaCategoryForeColor
            GridRow.DefaultCellStyle.SelectionForeColor = StoreMediaCategorySelectionForeColor
        End If

    End Sub

    Private Sub GridAvailableStores_CellMouseDoubleClick _
    (ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) _
    Handles GridAvailableStores.CellMouseDoubleClick
        If e.RowIndex > -1 Then
            ' The row double-clicked is NOT the column header row. Handle the event.
            ButtonAdd_Click(sender, e)
        End If
    End Sub

    Private Sub GridStoreList_CellMouseDoubleClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) Handles GridStoreList.CellMouseDoubleClick
        If e.RowIndex > -1 Then
            ' The row double-clicked is NOT the column header row. Handle the event.
            ButtonRemove_Click(sender, e)
        End If
    End Sub

    Private Sub TableLayoutPanel1_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles TableLayoutPanel1.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TableLayoutPanel = CType(sender, TableLayoutPanel)

        ' Check for errors.
        For Each SelectedStoreGridRow As DataGridViewRow In GridStoreList.Rows
            ' Get the datarow associated with this grid row.
            Dim Store As DataRow = CType(SelectedStoreGridRow.DataBoundItem, DataRowView).Row
            ' Test for errors.
            If Store("Dormant") Then
                ' This store is dormant. Validation failed.
                LiquidAgent.ControlValidation(TableLayoutPanel1, "Dormant stores may not be selected for the store list.")
                Exit Sub
            ElseIf Store("MediaAllowed") = False Then
                ' This store doesn't permit the selected media service. Validation failed.
                LiquidAgent.ControlValidation(TableLayoutPanel1, "Stores that do not permit the selected media service " _
                & "may not be selected for the store list.")
                Exit Sub
            ElseIf Store("Taken") Then
                ' This store has been taken by a different burst. Validation failed.
                LiquidAgent.ControlValidation(TableLayoutPanel1, "Stores that have been taken by a another burst " _
                & "which does not share a store pool with this burst may not be selected for the store list.")
                Exit Sub
            End If
        Next

        ' No errors found.
        LiquidAgent.ControlValidation(ValidatedControl, String.Empty)

    End Sub

    Private Sub HyperlinkIndependentStoreList_Click(sender As System.Object, e As System.EventArgs) Handles HyperlinkIndependentStoreList.Click

        ' Get an independent store list selection from the user.
        Dim SelectedIndependentStoreList As List(Of DataRow) = LookupIndependentStoreList.SelectRows _
        (My.Settings.DBConnection, False, DataObject.ChainID)

        ' Exit if nothing was selected.
        If SelectedIndependentStoreList.Count = 0 Then
            Exit Sub
        End If

        ' A table to hold the source burst's store list.
        Dim MemberTable As New DataSetContract.IndependentStoreListMemberDataTable

        ' Get the member store list from the database.
        Dim MemberAdapter As New DataSetContractTableAdapters.IndependentStoreListMemberTableAdapter
        Dim SqlCon As New SqlClient.SqlConnection(My.Settings.DBConnection)
        MemberAdapter.Connection = SqlCon
        Try
            MemberAdapter.Fill(MemberTable, SelectedIndependentStoreList(0).Item("IndependentStoreListID"))
        Catch ex As Exception
            ShowMessage(ex.Message, "Well, THAT Didn't Work Too Well", MessageBoxIcon.Error)
            Exit Sub
        End Try

        ' Clear the existing IndependentStoreListMember flags from all stores.
        For Each Store As DataRow In DataObject.StoreTable.Rows
            Store("IndependentStoreListMember") = False
        Next

        ' Flag all stores in the source store list as members of the store list.
        For Each IndependentStoreListMember As DataRow In MemberTable
            Dim StoreRow As DataRow = DataObject.StoreTable.Rows.Find(IndependentStoreListMember("StoreID"))
            If Not IsNothing(StoreRow) Then
                StoreRow("IndependentStoreListMember") = True
            End If
        Next

        ' Set the datasource of the available stores grid.
        CType(GridAvailableStores.Tag, GridManager).GridBindingSource = IndependentStoreListBindingSource

        ' Modify the appearance of the selected hyperlink to show that it has been selected, and
        ' deselect any previously selected hyperlink.
        LiquidAgent.EnableHyperlink(HyperlinkStoreUniverse, True)
        LiquidAgent.EnableHyperlink(HyperlinkStorePool, True)

    End Sub

    Private Sub ButtonNewIndependentStoreList_Click(sender As System.Object, e As System.EventArgs) Handles ButtonNewIndependentStoreList.Click

        ' Stop if the current store list is empty.
        If GridStoreList.RowCount = 0 Then
            ShowMessage("You cannot create an independent store list with no stores in it. Please add stores to your store list before creating " _
                        & " a new independent store list.", "No Stores Added")
            Exit Sub
        End If

        ' Get input from the user by displaying an input box.
        Dim NewIndependentStoreListName As String = LiquidAgent.GetInput _
        ("List Name", CType(TopLevelControl, BaseForm).Icon)

        ' Stop if the user clicked Cancel.
        If IsNothing(NewIndependentStoreListName) Then
            Exit Sub
        End If

        ' Create the tables and table adapters we'll need to create a new independent store list.
        Dim ListTable As New DataSetContract.IndependentStoreListDataTable
        Dim MemberTable As New DataSetContract.IndependentStoreListMemberDataTable
        Dim ListAdapter As New DataSetContractTableAdapters.IndependentStoreListTableAdapter
        Dim MemberAdapter As New DataSetContractTableAdapters.IndependentStoreListMemberTableAdapter

        ' Create a new connection for the table adapters.
        Dim SqlCon As New SqlClient.SqlConnection(My.Settings.DBConnection)
        ListAdapter.Connection = SqlCon
        MemberAdapter.Connection = SqlCon

        ' Create a new row in the IndependentStoreList table.
        Dim NewListRow As DataSetContract.IndependentStoreListRow = ListTable.NewRow
        NewListRow.IndependentStoreListName = NewIndependentStoreListName
        ListTable.Rows.Add(NewListRow)

        ' Create new rows for independent store list members in the IndependenetStoreListMember table.
        For Each SelectedStore As DataGridViewRow In GridStoreList.Rows
            Dim SelectedStoreRow As DataRow = CType(SelectedStore.DataBoundItem, DataRowView).Row
            Dim NewMemberRow As DataSetContract.IndependentStoreListMemberRow = MemberTable.NewRow
            NewMemberRow("IndependentStoreListID") = NewListRow("IndependentStoreListID")
            NewMemberRow("StoreID") = SelectedStoreRow("StoreID")
            MemberTable.Rows.Add(NewMemberRow)
        Next

        ' Save the new rows to the database.
        Try
            ListAdapter.Update(ListTable)
            MemberAdapter.Update(MemberTable)
        Catch ex As Exception
            ShowMessage("Something unexpected happened while trying to save the independent store list." _
                        & vbCrLf & vbCrLf & LiquidAgent.GetErrorMessage(ex))
        End Try

    End Sub

    Private Sub ButtonDeleteIndependentStoreList_Click(sender As System.Object, e As System.EventArgs) Handles ButtonDeleteIndependentStoreList.Click

        ' Get an independent store list selection from the user.
        Dim SelectedIndependentStoreLists As List(Of DataRow) = LookupIndependentStoreList.DeleteRows _
        (My.Settings.DBConnection, True, DataObject.ChainID)

        ' Exit if nothing was selected.
        If SelectedIndependentStoreLists.Count = 0 Then
            Exit Sub
        End If

        ' Create the tables and table adapters we'll need to create a new independent store list.
        Dim ListTable As New DataSetContract.IndependentStoreListDataTable
        Dim ListAdapter As New DataSetContractTableAdapters.IndependentStoreListTableAdapter
        Dim SqlCon As New SqlClient.SqlConnection(My.Settings.DBConnection)
        ListAdapter.Connection = SqlCon

        ' Delete all selected independent store lists.
        Try
            For Each SelectedList As DataRow In SelectedIndependentStoreLists
                ListAdapter.Delete(SelectedList("IndependentStoreListID"))
            Next
        Catch ex As Exception
            ShowMessage("Something unexpected happened while trying to delete the independent store list(s)." _
                        & vbCrLf & vbCrLf & LiquidAgent.GetErrorMessage(ex))
        End Try

    End Sub

    Private Sub TextEditSearchAvailableStores_KeyPress(sender As Object, e As System.Windows.Forms.KeyPressEventArgs) Handles TextEditSearchAvailableStores.KeyPress
        ' Check if the enter key was pressed.
        If e.KeyChar = Microsoft.VisualBasic.ChrW(Keys.Return) Then
            ' The enter key was pressed.
            For Each SelectedStore As DataGridViewRow In GridAvailableStores.SelectedRows
                ButtonAdd_Click(sender, e)
            Next
        End If
    End Sub

    Private Sub TextEditSearchStoreList_KeyPress(sender As Object, e As System.Windows.Forms.KeyPressEventArgs) Handles TextEditSearchStoreList.KeyPress
        ' Check if the enter key was pressed.
        If e.KeyChar = Microsoft.VisualBasic.ChrW(Keys.Return) Then
            ' The enter key was pressed.
            For Each SelectedStore As DataGridViewRow In GridStoreList.SelectedRows
                ButtonRemove_Click(sender, e)
            Next
        End If
    End Sub

#End Region

#Region "Private Methods"

    Private Function ConfirmedStatusValidated() As Boolean
        ' Check if the store list is confirmed. If not, return true.
        ' If so, ask user if he would like to flag the store list as 'unconfirmed'. If so, make the change and
        ' return true, otherwise return false.

        If DataObject.StoreListConfirmed = False Then
            Return True
        Else
            Dim PromptText As String = "This store list has already been confirmed. Changes may be made only to unconfirmed store lists. " _
                                       & "Would you like to make this store list 'unconfirmed' now?" & vbCrLf & vbCrLf & "(Don't forget to " _
                                       & "tick the box again when you're done.)"
            Dim PromptToUnconfirmStoreList As DialogResult = ShowMessage(PromptText, "Changing a Confirmed Store List", _
                                                                         MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question)
            If Not PromptToUnconfirmStoreList = Windows.Forms.DialogResult.Yes Then
                Return False
            Else
                DataObject.StoreListConfirmed = False
                CheckEditConfirmed.DataBindings("EditValue").ReadValue()
                Return True
            End If

        End If

    End Function

    Private Sub PerformDataBinding()
        LabelInstallStoreQtyValue.DataBindings.Add("Text", DataObject, "InstallStoreQty", False, DataSourceUpdateMode.Never)
        LabelRemainingQtyValue.DataBindings.Add("Text", Me, "RemainingStoreListQty", False, DataSourceUpdateMode.Never)
        LabelRemainingUniverseQtyValue.DataBindings.Add("Text", Me, "RemainingStoreUniverseQty", False, DataSourceUpdateMode.Never)
        LabelRemainingStorePoolQtyValue.DataBindings.Add("Text", Me, "RemainingStorePoolQty", False, DataSourceUpdateMode.Never)
    End Sub

    Protected Overrides Sub Save()
        Dim ChangesToStore As String = ""
        Dim ShouldSendEmail As Boolean = False

        ' Get the store list table.
        Dim StoreListTable As DataTable = DataObject.StoreListTable

        If DataObject.FirstWeek <= System.DateTime.Now And DataObject.LastWeek >= System.DateTime.Now And DataObject.ParentContract.Signed Then
            ShouldSendEmail = True
        End If

        ' Get all modified rows.
        Dim ModifiedStores As DataTable = DataObject.StoreTable.GetChanges(DataRowState.Modified)

        'first we need to check whether the contract is currently running

        ' Translate changes to the StoreList table.
        If Not IsNothing(ModifiedStores) Then
            For Each ModifiedStore As DataRow In ModifiedStores.Rows
                ' Get the modified row.
                Dim Keys() As Object = {DataObject.Row("BurstID"), ModifiedStore("StoreID")}
                Dim StoreListRow As DataRow = StoreListTable.Rows.Find(Keys)
                ' Check stores that are in the store list.
                If ModifiedStore("StoreListMember") Then
                    ' This store must be in the store list.
                    If IsNothing(StoreListRow) Then
                        StoreListRow = StoreListTable.NewRow
                        StoreListRow("BurstID") = DataObject.Row("BurstID")
                        StoreListRow("StoreID") = ModifiedStore("StoreID")
                        StoreListTable.Rows.Add(StoreListRow)
                        ChangesToStore += "The following Store has been added : " + ModifiedStore("StoreName") + vbCrLf
                    End If
                Else
                    ' This store must not be in the store list.
                    If Not IsNothing(StoreListRow) Then
                        ChangesToStore += "The following Store has been removed : " + ModifiedStore("StoreName") + vbCrLf
                        StoreListRow.Delete()
                    End If
                End If
                ''actually we need to do something else here rather.
                'here we can call the stored proc to write to db

            Next
            'only do this on a signed contract
            If ShouldSendEmail Then
                Dim ErrorMessage As String = String.Empty
                Dim cnn As SqlClient.SqlConnection = New SqlClient.SqlConnection(My.Settings.DBConnection)
                Dim InsertStatement As String = "Insert into Ops.EmailsToSend(BurstId,OriginalValue,TextToSend) " _
                & "Values " _
                & " ('" + DataObject.Row("BurstID").ToString() + "', '', '" + ChangesToStore + "' )"

                LiquidShell.LiquidAgent.RunCommand(cnn, InsertStatement, ErrorMessage)
            End If

            'here we can check to see if it needs to be added for emailing.
        End If

    End Sub

    Private Sub InitializeGridData()

        ' Setup grid managers.
        Dim GridStoreListManager As New GridManager _
        (GridStoreList, _
        TextEditSearchStoreList, _
        Nothing, _
        String.Empty, _
        PictureAdvancedSearchStoreList, _
        PictureClearSearchStoreList, _
        Nothing, _
        ButtonRemove)

        Dim GridAvailableStoresManager As New GridManager _
        (GridAvailableStores, _
        TextEditSearchAvailableStores, _
        Nothing, _
        String.Empty, _
        PictureAdvancedSearchAvailableStores, _
        PictureClearSearchAvailableStores, _
        ButtonAdd, _
        Nothing)

        ' Update the StoreTable with possible store pool member stores from sibling bursts that may not yet have been saved.
        For Each SiblingBurst As DataRow In DataObject.Row.Table.Rows
            If Not SiblingBurst.RowState = DataRowState.Deleted Then
                If Not Object.Equals(DataObject.Row("BurstID"), SiblingBurst("BurstID")) Then
                    ' This sibling burst is not the current burst. Proceed.
                    If Object.Equals(SiblingBurst("StorePoolID"), DataObject.StorePoolID) Then
                        ' This sibling burst and the current burst share the same store pool. Let's see if the sibling has any stores
                        ' in its store list.
                        For Each StoreListRow As DataRow In DataObject.DataSet.Tables("StoreList").Rows
                            If Not StoreListRow.RowState = DataRowState.Deleted Then
                                If Object.Equals(StoreListRow("BurstID"), SiblingBurst("BurstID")) Then
                                    ' This store belongs to the store list of the sibling that shares a store pool with the current burst.
                                    ' Flag this store as a member of the store pool.
                                    If DataObject.StoreTable.Rows.Contains(StoreListRow("StoreID")) Then
                                        DataObject.StoreTable.Rows.Find(StoreListRow("StoreID")).Item("StorePoolMember") = True
                                    End If
                                End If
                            End If
                        Next
                    End If
                End If
            End If
        Next

        ' Create a list to record all stores that were originally in the store list but which are not in the store list
        ' anymore because of changes made to the store universe.
        Dim MissingStoreList As New System.Text.StringBuilder

        ' Update the 'StoreListMember' column.
        For Each StoreListStore As DataRow In DataObject.StoreListTable.Rows
            ' Flag the StoreListMember column of this store in the StoreTable table.
            If Not StoreListStore.RowState = DataRowState.Deleted Then
                If DataObject.StoreTable.Rows.Contains(StoreListStore("StoreID")) Then
                    DataObject.StoreTable.Rows.Find(StoreListStore("StoreID")).Item("StoreListMember") = True
                Else
                    ' Add this store to the list.
                    MissingStoreList.Append(StoreListStore("StoreDescription") & vbCrLf)
                    ' Delete the store from the store list table in the dataset.
                    Dim Key() As Object = {StoreListStore("BurstID"), StoreListStore("StoreID")}
                    Dim RowToDelete As DataRow = DataObject.DataSet.Tables("StoreList").Rows.Find(Key)
                    RowToDelete.Delete()
                    ' Delete the store from the StoreListTable table in the CurrentBurst object.
                    StoreListStore.Delete()
                End If
            End If
        Next

        ' Check if any missing stores were found and deleted.
        If MissingStoreList.Length > 0 Then

            ' Missing stores were found and deleted. Save the changes (deleted stores) back to the database.
            Using SqlCon As New SqlClient.SqlConnection(My.Settings.DBConnection)
                Using StoreListAdapter As New DataSetContractTableAdapters.StoreListTableAdapter
                    StoreListAdapter.Connection = SqlCon
                    Dim TableToUpdate As DataTable = DataObject.DataSet.Tables("StoreList").GetChanges(DataRowState.Deleted)
                    Dim Updated As Integer = StoreListAdapter.Update(TableToUpdate)
                End Using
            End Using

            ' Display the list of stores which are no longer in the store list to the user so that the user can take whatever
            ' necessary action they want to (like replacing with new stores, for example).
            ShowMessage _
                ("The following stores which were selected for your contract have been removed because of changes made to the store " & _
                 "universe by the store universe manager." & vbCrLf & vbCrLf & MissingStoreList.ToString, "Removed Stopres")

        End If

        DataObject.StoreTable.AcceptChanges()

        ' Variables for the grid data sources.
        Dim StoreUniverseView As DataView

        ' Setup a view for the store universe.
        Dim StoreUniverseFilter As String = "StoreListMember = FALSE AND StorePoolMember = FALSE"
        StoreUniverseView = New DataView(DataObject.StoreTable, StoreUniverseFilter, String.Empty, DataViewRowState.CurrentRows)

        ' Setup a view for the store pool.
        Dim StorePoolFilter As String = "StoreListMember = FALSE AND StorePoolMember = TRUE"
        StorePoolView = New DataView(DataObject.StoreTable, StorePoolFilter, String.Empty, DataViewRowState.CurrentRows)

        ' Setup a view for the Source Burst store list.
        Dim SourceBurstStoreListFilter As String = "StoreListMember = FALSE AND SourceBurstStoreListMember = TRUE"
        Dim SourceBurstStoreListView As New DataView(DataObject.StoreTable, SourceBurstStoreListFilter, String.Empty, DataViewRowState.CurrentRows)

        ' Setup a view for the independent store list.
        Dim IndependentStoreListFilter As String = "StoreListMember = FALSE AND IndependentStoreListMember = TRUE"
        Dim IndependentStoreListView As New DataView(DataObject.StoreTable, IndependentStoreListFilter, String.Empty, DataViewRowState.CurrentRows)

        ' Create binding sources for GridAvailableStores.
        StoreUniverseBindingSource = New BindingSource(StoreUniverseView, String.Empty)
        StorePoolBindingSource = New BindingSource(StorePoolView, String.Empty)
        SourceBurstStoreListBindingSource = New BindingSource(SourceBurstStoreListView, String.Empty)
        IndependentStoreListBindingSource = New BindingSource(IndependentStoreListView, String.Empty)

        ' Setup storelist data
        Dim StoreListStoresFilter As String = "StoreListMember = TRUE"
        StoreListStoresView = New DataView(DataObject.StoreTable, StoreListStoresFilter, String.Empty, DataViewRowState.CurrentRows)

        ' Setup grid data sources.
        GridStoreListManager.GridBindingSource = New BindingSource(StoreListStoresView, String.Empty)
        GridAvailableStoresManager.GridBindingSource = StoreUniverseBindingSource

    End Sub

#End Region

End Class
