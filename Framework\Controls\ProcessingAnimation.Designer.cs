﻿namespace Framework.Controls
{
    partial class ProcessingAnimation
    {
        /// <summary> 
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary> 
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Component Designer generated code

        /// <summary> 
        /// Required method for Designer support - do not modify 
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.pictureBoxSendCode = new System.Windows.Forms.PictureBox();
            ((System.ComponentModel.ISupportInitialize)(this.pictureBoxSendCode)).BeginInit();
            this.SuspendLayout();
            // 
            // pictureBoxSendCode
            // 
            this.pictureBoxSendCode.Anchor = (System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left);
            this.pictureBoxSendCode.Image = Properties.Resources.processing_animation;
            this.pictureBoxSendCode.Location = new System.Drawing.Point(0, 0);
            this.pictureBoxSendCode.Margin = new System.Windows.Forms.Padding(0);
            this.pictureBoxSendCode.Name = "pictureBoxSendCode";
            this.pictureBoxSendCode.Size = new System.Drawing.Size(64, 64);
            this.pictureBoxSendCode.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize;
            this.pictureBoxSendCode.TabIndex = 12;
            this.pictureBoxSendCode.TabStop = false;
            // 
            // ProcessingAnimation
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.Controls.Add(this.pictureBoxSendCode);
            this.Margin = new System.Windows.Forms.Padding(0);
            this.Name = "ProcessingAnimation";
            this.Size = new System.Drawing.Size(64, 64);
            ((System.ComponentModel.ISupportInitialize)(this.pictureBoxSendCode)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private System.Windows.Forms.PictureBox pictureBoxSendCode;
    }
}
