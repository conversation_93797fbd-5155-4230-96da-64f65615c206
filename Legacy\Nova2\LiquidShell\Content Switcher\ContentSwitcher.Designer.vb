﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class ContentSwitcher
    Inherits DevExpress.XtraEditors.XtraUserControl

    'UserControl overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.PanelContent = New System.Windows.Forms.Panel
        Me.PanelButtons = New DevExpress.XtraEditors.PanelControl
        CType(Me.PanelButtons, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'PanelContent
        '
        Me.PanelContent.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelContent.Dock = System.Windows.Forms.DockStyle.Fill
        Me.PanelContent.Location = New System.Drawing.Point(161, 0)
        Me.PanelContent.Margin = New System.Windows.Forms.Padding(3, 6, 6, 6)
        Me.PanelContent.Name = "PanelContent"
        Me.PanelContent.Size = New System.Drawing.Size(325, 283)
        Me.PanelContent.TabIndex = 2
        '
        'PanelButtons
        '
        Me.PanelButtons.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelButtons.Appearance.BackColor2 = System.Drawing.Color.Silver
        Me.PanelButtons.Appearance.Options.UseBackColor = True
        Me.PanelButtons.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PanelButtons.Dock = System.Windows.Forms.DockStyle.Left
        Me.PanelButtons.Location = New System.Drawing.Point(0, 0)
        Me.PanelButtons.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat
        Me.PanelButtons.LookAndFeel.UseDefaultLookAndFeel = False
        Me.PanelButtons.Name = "PanelButtons"
        Me.PanelButtons.Size = New System.Drawing.Size(161, 283)
        Me.PanelButtons.TabIndex = 0
        '
        'ContentSwitcher
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.Controls.Add(Me.PanelContent)
        Me.Controls.Add(Me.PanelButtons)
        Me.Name = "ContentSwitcher"
        Me.Size = New System.Drawing.Size(486, 283)
        CType(Me.PanelButtons, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents PanelContent As System.Windows.Forms.Panel
    Friend WithEvents PanelButtons As DevExpress.XtraEditors.PanelControl

End Class
