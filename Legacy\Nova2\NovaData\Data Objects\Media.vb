Public Class Media
    Inherits OldBaseObject

#Region "Fields"

    Private _MediaFamilyMemberBindingSource As BindingSource
    Private _CompetingMediaBindingSource As BindingSource
    Private WithEvents MediaFamilyMemberTable As DataTable
    Private _MediaCategoryBindingSource As BindingSource
    Private _MediaLifeCycleBindingSource As BindingSource
    Private _MediaRuleBindingSource As BindingSource
    Private _MediaCostBindingSource As BindingSource

#End Region

#Region "Database Column Properties"

    Public ReadOnly Property MediaID() As Integer
        ' This object's identifier.
        Get
            Return Row("MediaID")
        End Get
    End Property

    Public Property MediaName() As String
        ' Descriptive name of the media.
        Get
            Return Row("MediaName")
        End Get
        Set(ByVal value As String)
            ' Clean the proposed new value.
            value = value.Trim
            If Not String.Compare(Row("MediaName"), value, False) = 0 Then
                ' New value is different than existing value. Proceed with update.
                AddLog("MediaName", "MediaName", value.ToString)
                Row("MediaName") = value
                IsDirty = True
            End If
        End Set
    End Property

    Public Property Stock() As Integer
        ' How much stock of this media is kept?
        Get
            Return Row("Stock")
        End Get
        Set(ByVal value As Integer)
            If Not Row("Stock") = value Then
                ' New value is different than existing value. Proceed with update.
                AddLog("MediaName", "Stock", value.ToString)
                Row("Stock") = value
                IsDirty = True
            End If
        End Set
    End Property

    Public Property Clutter() As Boolean
        ' Does this media contribute toward shelf clutter?
        Get
            Return Row("Clutter")
        End Get
        Set(ByVal value As Boolean)
            If Not Row("Clutter") = value Then
                ' New value is different than existing value. Proceed with update.
                AddLog("MediaName", "Clutter", value.ToString)
                Row("Clutter") = value
                IsDirty = True
            End If
        End Set
    End Property

    Public Property Notes() As String
        ' Additional information about this media.
        Get
            Return Row("Notes")
        End Get
        Set(ByVal value As String)
            ' Clean the proposed new value.
            value = value.Trim
            If Not String.Compare(Row("Notes"), value, False) = 0 Then
                ' New value is different than existing value. Proceed with update.
                AddLog("MediaName", "Notes", value.ToString)
                Row("Notes") = value
                IsDirty = True
            End If
        End Set
    End Property

    Public Property Homesite() As Boolean
        ' Is this media allowed to be used at a homesite category?
        Get
            Return Row("Homesite")
        End Get
        Set(ByVal value As Boolean)
            If Not Row("Homesite") = value Then
                ' New value is different than existing value. Proceed with update.
                AddLog("MediaName", "Homesite", value.ToString)
                Row("Homesite") = value
                IsDirty = True
            End If
        End Set
    End Property

    Public Property Crossover() As Boolean
        ' Is this media allowed to be used at a crossover category?
        Get
            Return Row("Crossover")
        End Get
        Set(ByVal value As Boolean)
            If Not Row("Crossover") = value Then
                ' New value is different than existing value. Proceed with update.
                AddLog("MediaName", "Crossover", value.ToString)
                Row("Crossover") = value
                IsDirty = True
            End If
        End Set
    End Property

    Public Property isPNPPcaStatus() As Boolean
        ' Is this media allowed to be used at a crossover category?
        Get
            Return Row("isPNPPcaStatus")
        End Get
        Set(ByVal value As Boolean)
            If Not Row("isPNPPcaStatus") = value Then
                ' New value is different than existing value. Proceed with update.
                AddLog("MediaName", "isPNPPcaStatus", value.ToString)
                Row("isPNPPcaStatus") = value
                IsDirty = True
            End If
        End Set
    End Property
    Public Property hasMediaCost() As Boolean
        ' Is this media allowed to be used at a crossover category?
        Get
            Return Row("hasMediaCost")
        End Get
        Set(ByVal value As Boolean)
            If Not Row("hasMediaCost") = value Then
                ' New value is different than existing value. Proceed with update.
                AddLog("MediaName", "hasMediaCost", value.ToString)
                Row("hasMediaCost") = value
                IsDirty = True
            End If
        End Set
    End Property
    Public Property isCostOverridable() As Boolean
        ' Is this media allowed to be used at a crossover category?
        Get
            Return Row("isCostOverridable")
        End Get
        Set(ByVal value As Boolean)
            If Not Row("isCostOverridable") = value Then
                ' New value is different than existing value. Proceed with update.
                AddLog("MediaName", "isCostOverridable", value.ToString)
                Row("isCostOverridable") = value
                IsDirty = True
            End If
        End Set
    End Property

#End Region

#Region "Custom Properties"

    Public ReadOnly Property MediaLifeCycleBindingSource() As BindingSource
        Get
            Return _MediaLifeCycleBindingSource
        End Get
    End Property

    Public ReadOnly Property MediaCategoryBindingSource() As BindingSource
        Get
            Return _MediaCategoryBindingSource
        End Get
    End Property

    Public ReadOnly Property CompetingMediaBindingSource() As BindingSource
        Get
            Return _CompetingMediaBindingSource
        End Get
    End Property

    Public ReadOnly Property MediaFamilyMemberBindingSource() As BindingSource
        Get
            Return _MediaFamilyMemberBindingSource
        End Get
    End Property

    Public ReadOnly Property MediaRuleBindingSource() As BindingSource
        Get
            Return _MediaRuleBindingSource
        End Get
    End Property
    Public ReadOnly Property MediaCostBindingSource() As BindingSource
        Get
            Return _MediaCostBindingSource
        End Get
    End Property

#End Region

#Region "Public Shared Methods"

    Public Shared Function GetListData _
    (ByVal ConString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) As System.Windows.Forms.BindingSource

        ' Instantiate a new data set and create a sql connection to use to populate it.
        Dim ListDataSet As New DataSetMedia
        Dim SqlCon As New SqlClient.SqlConnection(ConString)

        ' Create table adapters to collect list data and related data.
        Dim ListAdapter As New DataSetMediaTableAdapters.MediaTableAdapter
        Dim MediaFamilyMemberAdapter As New DataSetMediaTableAdapters.MediaFamilyMemberTableAdapter
        ListAdapter.Connection = SqlCon
        MediaFamilyMemberAdapter.Connection = SqlCon

        ' Populate tables in the dataset.
        Try
            ListAdapter.Fill(ListDataSet.Tables("Media"))
            MediaFamilyMemberAdapter.Fill(ListDataSet.Tables("MediaFamilyMember"))
        Catch ex As Exception
            ErrorMessage += LiquidShell.LiquidAgent.GetErrorMessage(ex)
            Return Nothing
        Finally
            ListAdapter.Dispose()
            MediaFamilyMemberAdapter.Dispose()
            SqlCon.Dispose()
        End Try

        ' Return a binding source linked to the retrieved data.
        Dim ReturnBindingSource As BindingSource = New BindingSource(ListDataSet, "Media")
        ReturnBindingSource.Sort = "MediaName"
        Return ReturnBindingSource

    End Function

    Public Shared Sub Delete(ByVal Grid As DataGridView, ByVal ConnectionString As String)
        ' Delete one or more rows from the database.

        ' Build a list of data relation names of child rows that must be deleted with the parent row.
        Dim DeletableChildRelationNames As New List(Of String)
        DeletableChildRelationNames.Add("FK_MediaFamilyMember_Media")
        DeletableChildRelationNames.Add("FK_MediaCategory_Media")
        DeletableChildRelationNames.Add("FK_MediaLifeCycle_Media")

        ' Create an audit table to record this action.
        Dim AuditLog As New DataSetAudit.AuditLogDataTable

        ' Create a list of names of the columns that will be used to describe each row being deleted.
        Dim ColumnNames As New List(Of String)
        ColumnNames.Add("MediaName")

        ' Add row descriptions to the Tag properties of all selected grid rows (these will be used as the object name
        ' in the audit log entries for rows that are deleted.)
        For Each SelectedGridRow As DataGridViewRow In Grid.SelectedRows
            Dim Row As DataRow = CType(SelectedGridRow.DataBoundItem, DataRowView).Row
            SelectedGridRow.Tag = Row("MediaName")
        Next

        ' Delete rows from the dataset.
        LiquidShell.LiquidAgent.DeleteParentRows _
        (Grid, "Media_BurstCount", DeletableChildRelationNames, AuditLog, "Contract")

        ' Get the tables and data set from which rows are being deleted to use as a parameter for the table adapters.
        Dim GridTable As DataTable = CType(CType(Grid.DataSource, BindingSource).List, DataView).Table
        Dim DataSet As DataSet = GridTable.DataSet

        ' If any rows were deleted from the data set, delete them from the database too.
        Using DeletedRows As New DataView(GridTable, "", "", DataViewRowState.Deleted)
            If IsNothing(DeletedRows) = False AndAlso DeletedRows.Count > 0 Then

                ' Create and configure table adapters to connect to the database.
                Dim Adapter As New DataSetMediaTableAdapters.MediaTableAdapter
                Dim MediaCategoryAdapter As New DataSetMediaTableAdapters.MediaCategoryTableAdapter
                Dim MediaFamilyMemberAdapter As New DataSetMediaTableAdapters.MediaFamilyMemberTableAdapter
                Dim MediaLifeCycleAdapter As New DataSetMediaTableAdapters.MediaLifeCycleTableAdapter
                Dim AuditAdapter As New DataSetAuditTableAdapters.AuditLogTableAdapter

                ' Create a new connection for the table adapters to use.
                Dim SqlCon As New SqlClient.SqlConnection(ConnectionString)
                Adapter.Connection = SqlCon
                MediaFamilyMemberAdapter.Connection = SqlCon
                MediaCategoryAdapter.Connection = SqlCon
                MediaLifeCycleAdapter.Connection = SqlCon
                AuditAdapter.Connection = SqlCon

                ' Get the form that is consuming this method so that the ShowMessage method can be used.
                Dim Consumingform As LiquidShell.BaseForm = CType(Grid.TopLevelControl, LiquidShell.BaseForm)

                ' Perform the delete operation.
                Try
                    MediaCategoryAdapter.Update(DataSet.Tables("MediaCategory"))
                    MediaFamilyMemberAdapter.Update(DataSet.Tables("MediaFamilyMember"))
                    MediaLifeCycleAdapter.Update(DataSet.Tables("MediaLifeCycle"))
                    Adapter.Update(GridTable)
                    AuditAdapter.Update(AuditLog)
                Catch ex As Exception
                    Consumingform.ShowMessage("Aaaagghh!!!  An error occured while trying to delete. The technical description of the error is:" _
                    & vbCrLf & vbCrLf & LiquidShell.LiquidAgent.GetErrorMessage(ex))
                Finally
                    ' Dispose all data adapters.
                    Adapter.Dispose()
                    MediaCategoryAdapter.Dispose()
                    MediaFamilyMemberAdapter.Dispose()
                    MediaLifeCycleAdapter.Dispose()
                    AuditAdapter.Dispose()
                    ' Close all connections.
                    SqlCon.Dispose()
                End Try

            End If
        End Using

    End Sub

#End Region

#Region "Public Methods"

    Public Sub New(ByVal DataSource As BindingSource, ByVal AddNew As Boolean, tblMediaRulesList As BindingSource)
        ' This constructor takes a binding source and a boolean indicating whether a new
        ' row must be added or whether this is an existing row being edited.

        If AddNew Then
            ' This is a new row being added.
            DataSource.AddNew()
        End If

        ' Update the base class binding source variable.
        DataBindingSource = DataSource
        _MediaRuleBindingSource = tblMediaRulesList

    End Sub

    Public Sub Save(ByVal ConnectionString As String, ByVal GridsToAudit As List(Of DataGridView))
        ' Save this object to the database.

        ' Create and configure table adapters to connect to the database.
        Dim SqlAdapter As New DataSetMediaTableAdapters.MediaTableAdapter
        Dim MediaFamilyMemberAdapter As New DataSetMediaTableAdapters.MediaFamilyMemberTableAdapter
        Dim MediaCategoryAdapter As New DataSetMediaTableAdapters.MediaCategoryTableAdapter
        Dim MediaLifeCycleAdapter As New DataSetMediaTableAdapters.MediaLifeCycleTableAdapter
        Dim MediaCostAdapter As New DataSetMediaTableAdapters.MediaCostTableAdapter
        Dim AuditAdapter As New DataSetAuditTableAdapters.AuditLogTableAdapter

        ' Create a new connection for the table adapters to use.
        Dim SqlCon As New SqlClient.SqlConnection(ConnectionString)
        SqlAdapter.Connection = SqlCon
        MediaFamilyMemberAdapter.Connection = SqlCon
        MediaCategoryAdapter.Connection = SqlCon
        MediaLifeCycleAdapter.Connection = SqlCon
        MediaCostAdapter.Connection = SqlCon
        AuditAdapter.Connection = SqlCon

        ' Check if this is a new row being added.
        If Row.RowState = DataRowState.Detached Then
            ' This is a new row being added. Clear the audit log and create only one entry for the new row.
            AuditLog.Clear()
            AddLog("MediaName")
        Else
            ' This row was edited. Update audit log with child row addition details.
            For Each Grid As DataGridView In GridsToAudit
                ' Check each row in this grid.
                For Each GridRow As DataGridViewRow In Grid.Rows
                    ' Get the datarow feeding this grid row.
                    Dim Row As DataRow = CType(GridRow.DataBoundItem, DataRowView).Row
                    If Row.RowState = DataRowState.Added Then
                        ' This row is newly added. Add an entry for it into the audit log.
                        Dim ActionText As String = BuildChildRowActionText(Grid, GridRow, ChildRowAction.Added)
                        LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, Table.TableName, MediaName, ActionText)
                    End If
                    If Row.RowState = DataRowState.Modified Then
                        ' This row has been modified. Add an entry for it into the audit log.
                        Dim ActionText As String = BuildChildRowActionText(Grid, GridRow, ChildRowAction.Modified)
                        LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, Table.TableName, MediaName, ActionText)
                    End If
                Next
            Next
        End If

        ' Perform the save operation.
        Try
            ' Remember the current item in the binding source list.
            Dim CurrentItem As Object = DataBindingSource.Current
            ' Commit changes to the dataset.
            DataBindingSource.EndEdit()     ' This line changes the binding source's current item.
            ' Restore the current item in the binding source list.
            DataBindingSource.Position = DataBindingSource.IndexOf(CurrentItem)
            ' Update the database.
            SqlAdapter.Update(Row)
            MediaFamilyMemberAdapter.Update(DataSet.Tables("MediaFamilyMember"))
            MediaCategoryAdapter.Update(DataSet.Tables("MediaCategory"))
            MediaLifeCycleAdapter.Update(DataSet.Tables("MediaLifeCycle"))
            MediaCostAdapter.Update(DataSet.Tables("MediaCost"))
            IsDirty = False
            AuditAdapter.Update(AuditLog)
        Finally
            ' Dispose all data adapters.
            SqlAdapter.Dispose()
            MediaFamilyMemberAdapter.Dispose()
            MediaCategoryAdapter.Dispose()
            MediaLifeCycleAdapter.Dispose()
            MediaCostAdapter.Dispose()
            AuditAdapter.Dispose()
            ' Dispose the connection.
            SqlCon.Close()
        End Try

    End Sub

    Public Sub AddMediaCategory _
    (ByVal ConsumingSubform As LiquidShell.Subform, _
    ByVal ConnectionString As String, _
    ByVal SelectedItems As List(Of DataRow))
        ' Add categories to the list of allowed categories for this media service.

        ' Check for legal selection by user.
        If IsNothing(SelectedItems) Then
            Exit Sub
        Else
            If SelectedItems.Count = 0 Then
                Exit Sub
            End If
        End If

        ' Add a new row to the table for each item that was selected.
        For Each SelectedItem As DataRow In SelectedItems

            ' Create a new row for the table.
            Dim NewRow As DataRow = DataSet.Tables("MediaCategory").NewRow
            NewRow("MediaID") = Row("MediaID")
            NewRow("CategoryID") = SelectedItem("CategoryID")
            DataSet.Tables("MediaCategory").Rows.Add(NewRow)

            ' Flag the object as dirty.
            IsDirty = True

        Next

    End Sub

    Public Sub AddMediaFamilyMembership _
    (ByVal ConsumingSubform As LiquidShell.Subform,
    ByVal ConnectionString As String,
    ByVal SelectedItems As List(Of DataRow))
        ' Make this media a member of the given media families.

        ' Check for legal selection by user.
        If IsNothing(SelectedItems) Then
            Exit Sub
        Else
            If SelectedItems.Count = 0 Then
                Exit Sub
            End If
        End If

        ' Add a new row to the table for each item that was selected.
        For Each SelectedItem As DataRow In SelectedItems

            ' Create a new row for the table.
            Dim NewRow As DataRow = DataSet.Tables("MediaFamilyMember").NewRow
            NewRow("MediaID") = Row("MediaID")
            NewRow("MediaFamilyID") = SelectedItem("MediaFamilyID")
            DataSet.Tables("MediaFamilyMember").Rows.Add(NewRow)

            ' Flag the object as dirty.
            IsDirty = True

        Next

    End Sub




#End Region

#Region "Private Methods"

    Protected Overrides Sub UpdateProperties()

        ' Update properties to match the current Row.
        UpdateCompetingMedia()
        UpdateCustomProperties()

    End Sub

    Private Sub UpdateCustomProperties()
        _MediaFamilyMemberBindingSource = New BindingSource(DataBindingSource, "FK_MediaFamilyMember_Media")
        ' Update the media family member table so that the competing media table can be updated
        ' when the rows added or rows removed events are raised.
        MediaFamilyMemberTable = CType(_MediaFamilyMemberBindingSource.List, DataView).Table
        _CompetingMediaBindingSource = New BindingSource(DataBindingSource, "Media_CompetingMedia")
        _CompetingMediaBindingSource.Sort = "CompetingMediaName"
        _MediaCategoryBindingSource = New BindingSource(DataBindingSource, "FK_MediaCategory_Media")
        _MediaLifeCycleBindingSource = New BindingSource(DataBindingSource, "FK_MediaLifeCycle_Media")
        _MediaCostBindingSource = New BindingSource(DataBindingSource, "FK_MediaCost_Media")
    End Sub

    Private Sub UpdateCompetingMedia()
        ' Update the table of media services that competes with this media service.

        ' Clear the table before starting.
        Dim CompetingMediaTable As DataTable = DataSet.Tables("CompetingMedia")
        Dim RowCount As Integer = CompetingMediaTable.Rows.Count
        For i As Integer = 0 To RowCount - 1
            CompetingMediaTable.Rows(0).Delete()
            CompetingMediaTable.AcceptChanges()
        Next

        ' Get an array of family memberships of this media service.
        Dim Filter As String = String.Empty
        If Not Row.RowState = DataRowState.Detached Then
            Filter = "MediaID = " & Row("MediaID").ToString
        End If
        Dim Memberships() As DataRow = DataSet.Tables("MediaFamilyMember").Select(Filter)

        ' Exit if this media service doesn't belong to any families.
        If Memberships.Length = 0 Then
            Exit Sub
        End If

        ' Get a list of IDs of all media families that this media service belongs to.
        Dim FamilyIDList As New List(Of Integer)
        For Each Membership As DataRow In Memberships
            FamilyIDList.Add(Membership("MediaFamilyID"))
        Next

        ' Cycle through the table of media family memberships to find any media service that is a member of
        ' any media family of which this media service is also a member.
        For Each CompetingMembership As DataRow In DataSet.Tables("MediaFamilyMember").Rows
            If Not CompetingMembership.RowState = DataRowState.Deleted AndAlso _
            FamilyIDList.Contains(CompetingMembership("MediaFamilyID")) Then
                ' This membership has a MediaFamilyID of a family of which this media service is a member.

                ' Get the name of the competing media service.
                Dim CompetingMediaID As Integer = CompetingMembership("MediaID")
                Dim CompetingMediaName As String = Table.Rows.Find(CompetingMediaID).Item("MediaName")

                ' Get the primary key for the new row.
                Dim Keys() As Object = {Row("MediaID"), CompetingMediaName}

                ' Check if the new row already exists in the table.
                Dim CompetingMediaRow As DataRow = CompetingMediaTable.Rows.Find(Keys)
                If IsNothing(CompetingMediaRow) Then
                    ' The row doesn't exist. Create it now.
                    CompetingMediaRow = CompetingMediaTable.NewRow()
                    CompetingMediaRow("MediaID") = CInt(Keys(0))
                    CompetingMediaRow("CompetingMediaName") = CStr(Keys(1))
                    CompetingMediaTable.Rows.Add(CompetingMediaRow)
                End If

            End If
        Next

    End Sub

#End Region

#Region "Event Handlers"

    Private Sub MediaFamilyMemberTable_RowChanged(ByVal sender As Object, ByVal e As System.Data.DataRowChangeEventArgs) Handles MediaFamilyMemberTable.RowChanged
        ' Refresh the list of competing media if this media is added as a member of a family.
        If e.Action = DataRowAction.Add Then
            UpdateCompetingMedia()
        End If
    End Sub

    Private Sub MediaFamilyMemberTable_RowDeleted(ByVal sender As Object, ByVal e As System.Data.DataRowChangeEventArgs) Handles MediaFamilyMemberTable.RowDeleted
        ' Refresh the list of competing media if this media is removed as a member of a family.
        UpdateCompetingMedia()
    End Sub

#End Region

End Class
