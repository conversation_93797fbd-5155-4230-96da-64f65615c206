<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="ImageList24x24.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 56</value>
  </metadata>
  <data name="ImageList24x24.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj00LjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAAD6
        CAAAAk1TRnQBSQFMAgEBAgEAAcwBBgHMAQYBFAEAARQBAAT/AREBAAj/AUIBTQE2BwABNgMAASgDAAFQ
        AwABFAMAAQEBAAEQBQABgAEMFAABOQFnAToBZwFaAWcBWgFnAVoBZwFaAWcBWgFnAVoBZwFaAWcBWgFn
        AVoBZwFaAWcBOQFnAVoBaxgAAVoBawE5AWcBWgFrAVoBawE5AWcBWgFrXgABWgFrATkBZwF8AW8BeQFz
        AXkBcwF5AXMBeQFzAXkBcwF5AXMBeQFzAXkBcwF5AXMBmwFzAVsBawE5AWcWAAFaAWsBOQFnAZsBbwEa
        AWcBWwFrAXsBbwE5AWdeAAE5AWcBnAFzAdEBcgEGAXIBBQFyAQUBcgEFAXIBBQFyAQUBcgEFAXIBBQFy
        AQUBcgFrAXIBegFzATkBZxYAAVoBawF7AW8BOgFnAQ8BKQEwASkBewFvAVoBawFaAWtcAAE5AWcBmwF3
        AQUBdgHBAXUB4QF1AeEBdQHhAXUB4QF1AeEBdQHhAXUB4QF1AeEBdQHhAXUB8gF2AVsBawFaAWsUAAFa
        AWsBewFvAZcBUgHOASABjAEYAXsBbwF7AW9eAAE5AWcBmgF3AQIBdgEBAXYBAgF2AQIBdgEkAXYBVgF3
        AawBdgECAXYBAgF2AQIBdgHhAXUB8AF2AXwBbwFaAWsUAAFaAWsBewFvAXcBUgHuASQB7gEgAd0BdwE5
        AWdeAAE6AWcBmgF3ASIBdgEBAXYBIgF2ASIBdgElAXYBmwF3Aa0BdgEBAXYBIgF2ASIBdgEBAXYB8QF2
        AXwBbwFaAWsUAAFaAWsBewFvAXcBUgEPASUBrQEcARkBYwGcAXMBOQFnXAABOgFnAZoBdwEiAXYBIQF2
        ASIBdgEiAXYBJAF2AVcBdwGrAXYBIQF2ASIBdgEiAXYBIQF2AREBdwGcAW8BWgFrFAABWgFrAXsBbwGX
        AVIBLwEpAQ8BJQH5AWIBnAFzAVoBa1wAAVoBZwGaAXcBQgF2ASEBdgFCAXYBIQF2AWUBdgHdAXcBDwF3
        ASEBdgFCAXYBQQF2ASABdgERAXcBnAFvAVoBaxQAAVoBawF7AW8BlwFWATABKQFQAS0BnAFzAZwBcwFa
        AWtcAAFaAWsBmgF7AUIBdgFBAXYBQQF2AUEBdgFjAXYB7gF2AYcBdgFBAXYBQQF2AUEBdgFAAXYBEQF3
        AZwBbwFaAWsBOQFnAVoBawE5AWcBWgFrDAABWgFrAXsBbwGYAVYBUAEtATABKQGcAXMBnAFzAVoBa1wA
        AVoBawG7AXsBpwF2AUABdgFAAXYBQAF2AUABdgFAAXYBQAF2AUABdgFhAXYBYQF2AWIBdgFUAnsBawE5
        AWcBewFvAZwBbwF7AW8BOQFnDAABWgFrAXsBbwG4AVYBUQExAXEBMQHeAXsBnAFzATkBZ1wAAVoBawG9
        AXcBdgF7AcsBegHJAXoByQF6AckBegHJAXoBygF6AckBegFgAXYBYgF2ATABewG8AXcBWgFrAVsBawGZ
        AXsB7AF6AZgBewFaAWsKAAFaAWsBOQFnAZwBcwG4AVYBkQE1AVEBMQE1AUoBvQF3AVoBawFaAWtcAAFa
        AWsBnAFzAd0BewHcAXsB3AF7AdwBewHcAXsB3AF7AbsBewFgAXoBpQF6AbwBewGcAXMBWgFrAd4BdwEy
        AXsBYAF2AVMBewFaAWsKAAE5AWcBnAFzATsBZwH0AUEBkgE1AZIBNQGzATkB2QFeAZ0BcwFaAWtcAAFa
        AWsBWgFrAVoBawFaAWsBWgFrAVoBawFaAWsBewFvAbsBewFgAXoBpQF6AbwBdwF7AW8BWgFrAd4BdwFR
        AXsBYAF6AVIBewFbAWsIAAFaAWsBewFvAZ0BcwHTAT0BkgE1AbIBOQGzATkBkgE1AbIBOQEaAWMBnAFz
        AVoBa2gAAXsBbwG7AXsBgAF6AcUBegG8AnsBbwE5AWcB3gF3AVIBewGAAXoBcgF7AVsBawgAAVoBawH/
        AX8BuAFWAZIBNQHTAT0B0wE9AdMBPQHTAT0BsgE5AVYBSgG+AXcBWgFraAABewFvAdwBewGgAXoBxAF6
        AbkCewFvATkBZwH/AX8BLQF7AaABegFzAnsBbwgAAXsBbwH/AX8BNQFKAbMBOQHTAT0BsgE5AbIBOQHT
        AT0B0wE9AfQBQQG9AXcBewFvaAABWgFrAd4BewEGAXsBoQF6AU0BewG6AXsB3AF7AbUBfwHCAXoBxAF6
        AbcBfwFbAWsIAAF7AW8B/wF/AbgBVgHTATkBFAFCAVwBbwHeAXsBNQFGAdMBOQFWAU4BvgJ7AW9oAAFa
        AWsB3gF3AbgBfwHBAXoBwQF6AQgBewErAXsB4wF6AcABegFMAX8B3AF7AVoBawgAAVoBawG9AXcBfQFz
        AfQBQQEVAUYBnQFzAd8BewFWAU4B0wE9ARoBYwG+AXcBWgFragABewFvAf8BfwGUAX8BBQF7AeABegHA
        AXoB4AF6AW4BfwHaAX8BnAFzAVoBawoAAVoBawHeAXsBOwFnAVYBSgFWAU4BlwFSATUBRgH5AV4BvgF3
        AXsBb2wAAVoBawFaAWsB3gF7AfwBfwHZAX8BuAF/AdsBfwH9AX8BnAFzAVoBaw4AAXsBbwG9AXcBvgF3
        AXwBbwF8AW8BvgF3Ad4BewGcAXMBWgFrcAABWgFrAXsBbwGcAXMBvQF3AXsBbwFaAWsUAAFaAWsBewFv
        AZwBcwG9AXcBewFvAVoBa14AAUIBTQE+BwABPgMAASgDAAFQAwABFAMAAQEBAAEBBQAB8BcAA/8BAAGA
        AQEB/wHgAX8IAAEBAf8BwAF/CAABAQH/AcABPwkAAf8BwAF/CQAB/wHAAX8JAAH/AcABPwkAAf8BwAE/
        CQAB/wHAAT8JAAEPAcABPwkAAQ8BwAE/CQABDwGAAR8HAAGAAQABDwGAAR8HAAGAAQABDwEAAQ8HAAH/
        AQABDwEAAQ8HAAH/AQABDwEAAQ8HAAH/AQABDwEAAQ8HAAH/AQABDwEAAQ8HAAH/AYABDwGAAR8HAAH/
        AYABHwHAAR8HAAH/AeABfwHgAX8HAAs=
</value>
  </data>
  <metadata name="ErrorManager.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
</root>