﻿using DataAccess;
using System;
using System.Data;

namespace DataService.Security
{
    internal class GetRolesOfMemberCandidatesCommand : Command
    {
        public Guid SessionId;
        public Guid UserId;
        public DataTable Table;

        public GetRolesOfMemberCandidatesCommand(Guid sessionid, Guid userid)
        {
            SessionId = sessionid;
            UserId = userid;
        }
    }
}
