<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformMediaCost
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformMediaCost))
        Me.LabelHeadingLifeCycleDetails = New DevExpress.XtraEditors.LabelControl()
        Me.LabelFirstWeek = New DevExpress.XtraEditors.LabelControl()
        Me.ButtonOK = New DevExpress.XtraEditors.SimpleButton()
        Me.ImageList24x24 = New System.Windows.Forms.ImageList()
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.HyperlinkEffectiveDate = New DevExpress.XtraEditors.LabelControl()
        Me.LabelAmount = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditPercentage = New DevExpress.XtraEditors.TextEdit()
        Me.LabelPercentage = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditAmount = New DevExpress.XtraEditors.TextEdit()
        Me.CheckEditMediaCostPercentage = New DevExpress.XtraEditors.CheckEdit()
        CType(Me.TextEditPercentage.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditAmount.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditMediaCostPercentage.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Margin = New System.Windows.Forms.Padding(5, 5, 5, 21)
        Me.LabelTitle.Size = New System.Drawing.Size(170, 37)
        Me.LabelTitle.TabIndex = 0
        Me.LabelTitle.Text = "Media Cost"
        '
        'LabelHeadingLifeCycleDetails
        '
        Me.LabelHeadingLifeCycleDetails.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelHeadingLifeCycleDetails.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelHeadingLifeCycleDetails.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelHeadingLifeCycleDetails.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelHeadingLifeCycleDetails.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelHeadingLifeCycleDetails.LineVisible = True
        Me.LabelHeadingLifeCycleDetails.Location = New System.Drawing.Point(15, 75)
        Me.LabelHeadingLifeCycleDetails.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelHeadingLifeCycleDetails.Name = "LabelHeadingLifeCycleDetails"
        Me.LabelHeadingLifeCycleDetails.Size = New System.Drawing.Size(867, 24)
        Me.LabelHeadingLifeCycleDetails.TabIndex = 1
        Me.LabelHeadingLifeCycleDetails.Text = "Media Cost Options"
        '
        'LabelFirstWeek
        '
        Me.LabelFirstWeek.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelFirstWeek.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelFirstWeek.Location = New System.Drawing.Point(15, 122)
        Me.LabelFirstWeek.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelFirstWeek.Name = "LabelFirstWeek"
        Me.LabelFirstWeek.Size = New System.Drawing.Size(108, 17)
        Me.LabelFirstWeek.TabIndex = 2
        Me.LabelFirstWeek.Text = "Effective Date :"
        '
        'ButtonOK
        '
        Me.ButtonOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonOK.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonOK.Appearance.Options.UseFont = True
        Me.ButtonOK.ImageIndex = 2
        Me.ButtonOK.ImageList = Me.ImageList24x24
        Me.ButtonOK.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonOK.Location = New System.Drawing.Point(617, 405)
        Me.ButtonOK.LookAndFeel.SkinName = "Black"
        Me.ButtonOK.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonOK.Margin = New System.Windows.Forms.Padding(4, 16, 4, 4)
        Me.ButtonOK.Name = "ButtonOK"
        Me.ButtonOK.Size = New System.Drawing.Size(129, 37)
        Me.ButtonOK.TabIndex = 9
        Me.ButtonOK.Text = "OK"
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(753, 405)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Margin = New System.Windows.Forms.Padding(4, 16, 4, 4)
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(129, 37)
        Me.ButtonCancel.TabIndex = 10
        Me.ButtonCancel.Text = "Cancel"
        '
        'HyperlinkEffectiveDate
        '
        Me.HyperlinkEffectiveDate.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkEffectiveDate.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkEffectiveDate.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkEffectiveDate.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkEffectiveDate.Location = New System.Drawing.Point(214, 122)
        Me.HyperlinkEffectiveDate.Margin = New System.Windows.Forms.Padding(4)
        Me.HyperlinkEffectiveDate.Name = "HyperlinkEffectiveDate"
        Me.HyperlinkEffectiveDate.Size = New System.Drawing.Size(57, 17)
        Me.HyperlinkEffectiveDate.TabIndex = 3
        Me.HyperlinkEffectiveDate.Text = "Select..."
        '
        'LabelAmount
        '
        Me.LabelAmount.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelAmount.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelAmount.Location = New System.Drawing.Point(14, 162)
        Me.LabelAmount.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelAmount.Name = "LabelAmount"
        Me.LabelAmount.Size = New System.Drawing.Size(62, 17)
        Me.LabelAmount.TabIndex = 4
        Me.LabelAmount.Text = "Amount:"
        '
        'TextEditPercentage
        '
        Me.TextEditPercentage.EditValue = "0"
        Me.TextEditPercentage.Location = New System.Drawing.Point(215, 203)
        Me.TextEditPercentage.Margin = New System.Windows.Forms.Padding(4, 4, 0, 4)
        Me.TextEditPercentage.Name = "TextEditPercentage"
        Me.TextEditPercentage.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditPercentage.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditPercentage.Properties.Appearance.Options.UseFont = True
        Me.TextEditPercentage.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditPercentage.Properties.Appearance.Options.UseTextOptions = True
        Me.TextEditPercentage.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.TextEditPercentage.Properties.AppearanceReadOnly.BackColor = System.Drawing.Color.Gainsboro
        Me.TextEditPercentage.Properties.AppearanceReadOnly.Options.UseBackColor = True
        Me.TextEditPercentage.Properties.Mask.EditMask = "P2"
        Me.TextEditPercentage.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditPercentage.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TextEditPercentage.Size = New System.Drawing.Size(157, 24)
        Me.TextEditPercentage.TabIndex = 7
        '
        'LabelPercentage
        '
        Me.LabelPercentage.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelPercentage.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelPercentage.Location = New System.Drawing.Point(15, 210)
        Me.LabelPercentage.Margin = New System.Windows.Forms.Padding(0, 4, 4, 13)
        Me.LabelPercentage.Name = "LabelPercentage"
        Me.LabelPercentage.Size = New System.Drawing.Size(83, 17)
        Me.LabelPercentage.TabIndex = 6
        Me.LabelPercentage.Text = "Percentage:"
        '
        'TextEditAmount
        '
        Me.TextEditAmount.EditValue = ""
        Me.TextEditAmount.Location = New System.Drawing.Point(215, 159)
        Me.TextEditAmount.Margin = New System.Windows.Forms.Padding(4, 4, 0, 4)
        Me.TextEditAmount.Name = "TextEditAmount"
        Me.TextEditAmount.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditAmount.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditAmount.Properties.Appearance.Options.UseFont = True
        Me.TextEditAmount.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditAmount.Properties.Appearance.Options.UseTextOptions = True
        Me.TextEditAmount.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.TextEditAmount.Properties.AppearanceReadOnly.BackColor = System.Drawing.Color.Gainsboro
        Me.TextEditAmount.Properties.AppearanceReadOnly.Options.UseBackColor = True
        Me.TextEditAmount.Properties.Mask.EditMask = "c4"
        Me.TextEditAmount.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditAmount.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TextEditAmount.Size = New System.Drawing.Size(157, 24)
        Me.TextEditAmount.TabIndex = 5
        '
        'CheckEditMediaCostPercentage
        '
        Me.CheckEditMediaCostPercentage.Location = New System.Drawing.Point(13, 244)
        Me.CheckEditMediaCostPercentage.Margin = New System.Windows.Forms.Padding(4)
        Me.CheckEditMediaCostPercentage.Name = "CheckEditMediaCostPercentage"
        Me.CheckEditMediaCostPercentage.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditMediaCostPercentage.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditMediaCostPercentage.Properties.Appearance.Options.UseFont = True
        Me.CheckEditMediaCostPercentage.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditMediaCostPercentage.Properties.AutoWidth = True
        Me.CheckEditMediaCostPercentage.Properties.Caption = "Percentage based media cost:"
        Me.CheckEditMediaCostPercentage.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditMediaCostPercentage.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditMediaCostPercentage.Size = New System.Drawing.Size(233, 21)
        Me.CheckEditMediaCostPercentage.TabIndex = 8
        '
        'SubformMediaCost
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(9.0!, 17.0!)
        Me.Controls.Add(Me.CheckEditMediaCostPercentage)
        Me.Controls.Add(Me.TextEditAmount)
        Me.Controls.Add(Me.TextEditPercentage)
        Me.Controls.Add(Me.LabelPercentage)
        Me.Controls.Add(Me.LabelAmount)
        Me.Controls.Add(Me.HyperlinkEffectiveDate)
        Me.Controls.Add(Me.ButtonOK)
        Me.Controls.Add(Me.ButtonCancel)
        Me.Controls.Add(Me.LabelFirstWeek)
        Me.Controls.Add(Me.LabelHeadingLifeCycleDetails)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(5)
        Me.Name = "SubformMediaCost"
        Me.Padding = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.Size = New System.Drawing.Size(897, 458)
        Me.Controls.SetChildIndex(Me.LabelHeadingLifeCycleDetails, 0)
        Me.Controls.SetChildIndex(Me.LabelFirstWeek, 0)
        Me.Controls.SetChildIndex(Me.ButtonCancel, 0)
        Me.Controls.SetChildIndex(Me.ButtonOK, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkEffectiveDate, 0)
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        Me.Controls.SetChildIndex(Me.LabelAmount, 0)
        Me.Controls.SetChildIndex(Me.LabelPercentage, 0)
        Me.Controls.SetChildIndex(Me.TextEditPercentage, 0)
        Me.Controls.SetChildIndex(Me.TextEditAmount, 0)
        Me.Controls.SetChildIndex(Me.CheckEditMediaCostPercentage, 0)
        CType(Me.TextEditPercentage.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditAmount.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditMediaCostPercentage.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents LabelHeadingLifeCycleDetails As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelFirstWeek As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ButtonOK As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents HyperlinkEffectiveDate As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents LabelAmount As LabelControl
    Friend WithEvents TextEditPercentage As TextEdit
    Friend WithEvents LabelPercentage As LabelControl
    Friend WithEvents TextEditAmount As TextEdit
    Friend WithEvents CheckEditMediaCostPercentage As CheckEdit
End Class
