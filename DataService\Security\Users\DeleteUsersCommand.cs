﻿using DataAccess;
using System;
using System.Collections.Generic;
using System.Data;

namespace DataService.Security
{
    class DeleteUsersCommand : Command
    {
        public Guid SessionId { get; set; }
        public DataTable Users { get; set; }

        public DeleteUsersCommand(Guid sessionid, List<DataRow> userslist)
        {
            SessionId = sessionid;

            // Create a new table.
            Users = new DataTable();
            Users.Columns.Add("id", typeof(Guid));

            // Convert the rows in the specified list to rows that match the schema of the table and then add them to the table.
            if (userslist != null && userslist.Count > 0)
            {
                for (int i = 0; i < userslist.Count; i++)
                {
                    DataRow newrow = Users.NewRow();
                    newrow["id"] = userslist[i]["userid"];
                    Users.Rows.Add(newrow);
                }
            }
        }
    }
}
