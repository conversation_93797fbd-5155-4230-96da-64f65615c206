﻿using Framework.Controls.GridSystem;
using System;
using System.Data;
using System.Windows.Forms;

namespace Framework.Surfaces
{
    public class RowHistorySurfaceFactory : ITabContentSurfaceFactory
    {
        ExcelExporter ExcelExporter;
        string IdColumnName = string.Empty;
        string GridTitle = string.Empty;
        Func<object[], DataTable> GetGridDataMethod;


        public RowHistorySurfaceFactory(ExcelExporter excelexporter, string idcolumnname, string gridtitle, Func<object[], DataTable> getgriddatamethod)
        {
            ExcelExporter = excelexporter;
            IdColumnName = idcolumnname;
            GridTitle = gridtitle;
            GetGridDataMethod = getgriddatamethod;
        }

        private string _TabText = "History";
        public string TabText
        {
            get
            {
                return _TabText;
            }
        }

        public Surface NewSurface(BindingSource bindingsource)
        {
            var selectedrow = ((DataRowView)bindingsource.Current).Row;
            if (selectedrow.RowState == DataRowState.Detached)
            {
                selectedrow[IdColumnName] = Guid.Empty;
                return null;
            }

            Guid selectedrowid = (Guid)selectedrow[IdColumnName];
            var gridfiller = new HistoryGridFiller(selectedrowid, GridTitle, GetGridDataMethod);
            var newsurface = new RowHistorySurface(gridfiller, ExcelExporter);
            newsurface.AutoFillTheGridOnLoad = true;

            return newsurface;
        }

    }
}
