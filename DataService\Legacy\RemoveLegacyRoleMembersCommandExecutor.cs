﻿using DataAccess;

namespace DataService.Security
{
    class RemoveLegacyRoleMembersCommandExecutor : CommandExecutor<RemoveLegacyRoleMembersCommand>
    {

        public override void Execute(RemoveLegacyRoleMembersCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.RemoveLegacyRoleMembers))
            {
                storedprocedure.AddInputParameter("roleid", command.RoleId);
                storedprocedure.AddInputParameter("members", command.Members);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
            }
        }

    }
}
