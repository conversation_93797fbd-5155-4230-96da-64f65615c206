<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="ErrorManager.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="ImageList16x16.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>144, 17</value>
  </metadata>
  <data name="ImageList16x16.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj0yLjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAACM
        BAAAAk1TRnQBSQFMAwEBAAFMAQEBTAEBARABAAEQAQAE/wERAQAI/wFCAU0BNgcAATYDAAEoAwABQAMA
        ARADAAEBAQABEAYAAQgUAAH/AX8BnAFzAXwBbwF8AW8BfAFvAXwBbwF8AW8BfAFvAXwBbwF8AW8BfAFv
        AXwBbwGcAXMB/wF/YgAB/wF/AZwBcwF5AWsB0QFOAfEBUgHxAVIB8QFSAfEBUgHxAVIB8QFSAfEBUgHx
        AVIB0AFOAVgBZwGdAXcB/wF/YAABnAFzAXkBawEBASIB4AEhAcABHQHAARkBwAEZAeABHQHgASEBwAEZ
        AcABGQHgAR0B4AEhAeABIQFWAWMBnAFzYAABnAFzAc4BSgEgASYBZQE2ATMBWwE0AV8BVgFjAREBVwFE
        ATIBNQFfAXkBawHLAUYBQgEuASEBKgGqAUIBnAFzYAABnAFzAe4BTgFAASoBpwE6Af8BfwHfAX8BmAFr
        ATMBWwGZAWsBvQF3AVQBXwH/AX8BqAE+AUABKgHLAUYBnAFzYAABnAFzAQ4BUwGBATIBgQEyAesBSgH/
        AX8BMQFbAUABJgG9AXcBVAFfAaUBOgH/AX8B6gFKAYABLgHrAUoBnAFzYAABnAFzAS4BUwGhATYBxAE6
        AcMBOgHnAUIB/wF/AVABWwELAU8B/wF/Af8BfwG8AXMBwgE2AaEBNgELAU8BnAFzYAABnAFzAU4BVwHA
        ATYBCAFLAf8BfwHjAT4BlgFnAd0BdwGXAWsBuQFvASoBSwH/AX8BBgFDAeEBNgErAU8BnAFzYAABnAFz
        AU4BVwHgAToB4gE+AboBcwH/AX8B/wF/AU4BVwErAVMB/wF/Af8BfwG6AXMB4gE+AeEBPgFLAVMBnAFz
        YAABnAFzAUwBVwHgAToB4AE+AeABPgEFAUsBAwFHAeABOgHgAToBAwFHASUBSwHgAT4B4AE+AeABPgEp
        AVMBnAFzYAABnAFzAZMBZwFKAVcBSwFXAUoBVwFJAVMBSQFXAUsBVwFKAVcBSQFTAUkBUwFKAVcBSwFX
        AUoBVwGRAWMBnAFzYAABnAFzAZMBawFJAVcBkAFjAbQBawGSAWcBkgFnAZIBZwG0AWsBtAFrAbQBawGS
        AWcBkQFnAUkBVwGRAWcBnAFzYAABnAFzAWoBXwHgAUIBSAFbAWwBYwFpAVsBagFfAWwBYwGPAWcBkAFn
        AWsBXwFrAV8BbAFfAeABQgFHAVcBnAFzYAABnAFzAbQBbwEAAUcBAAFLAUYBWwG0AW8BAAFHASABSwEA
        AUsBAAFHAbEBawFqAV8BAAFHAQABRwGQAWcBnAFzYAAB/wF/Af8BfwGwAWsBRAFXAWoBYwGyAW8BRAFb
        AWUBWwFlAVsBRAFbAbABawGMAWcBQwFXAY4BZwH/AX8B/wF/YgAB/wF/Ab0BdwG9AXcBvQF3Ab0BdwG9
        AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Af8Bf2IAAUIBTQE+BwABPgMAASgDAAFAAwABEAMA
        AQEBAAEBBQABgBcAA/8BAAGAAQF2AAGAAQEGAAs=
</value>
  </data>
  <metadata name="ImageList24x24.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>280, 17</value>
  </metadata>
  <data name="ImageList24x24.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj0yLjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAACk
        CgAAAk1TRnQBSQFMAgEBAgEAAeQBAAHkAQABFAEAARQBAAT/AREBAAj/AUIBTQE2BwABNgMAASgDAAFQ
        AwABFAMAAQEBAAEQBQABgAEMIAAB/wF/Ad4BewHeAXsB/wF/FAABWgFrAVoBawFaAWsBWgFrAVoBawFa
        AWsBWgFrCAABWgFrAVoBawFaAWsBewFrAVoBawFaAWsBWgFrXgAB/wF/Ab0BdwGdAXMBnQFzAb0BdwH/
        AX8SAAE5AWcBWgFnAZwBcwH3AWoBOAFvAZwBcwE5AWcBWgFrBAABWgFrATkBZwF7AW8BWQFvAbUBZgGc
        AXMBWgFrATkBZwFaAWtaAAH/AX8BnQF3AZwBcwE1AV8BFAFbAb4BdwG9AXcB/wF/DgABWgFrATkBZwF7
        AW8BEAFaAWMBPAHFAUABtQFmAb0BcwE5AWcBWgFrAgABOQFnAZwBbwH3AWoBCAFJAUIBOAGtAVEBewFv
        AVoBawFaAWtYAAH/AX8BnAFzAb0BdwHxAVIBJAEuAQIBKgETAVsBvQF3Ad4Bew4AATkBZwG9AXcBMQFe
        AUIBPAFCATwBQgE8AaQBQAHWAWoBvQFzATkBZwE5AWcBnAFzATkBbwHnAUgBQgE8AUIBPAFBATwBjAFV
        AZwBcwFaAWtWAAH/AX8BnAFzAd8BfwE0AV8BIgEqAUQBLgFEAS4BIgEqAZwBcwG9AXcB/wF/DAABOQFn
        AVoBcwFBAUABQQFAAWMBQAFjAUABQQFAAYQBRAH3AW4BnAFvAXsBawGbAXcBBwFJASEBPAFiAUABYwFA
        AWIBQAEhATwBlAFmAXsBa1QAAf8BfwG9AXcBvQF3ATMBWwFCAS4BZAEyAWQBMgFkATIBQgEuAe0BSgG9
        AXcBvgF3Af8BfwoAATkBZwF7AXcB5gFMAUEBRAFiAUQBYwFEAWIBRAFBAUQBpQFIARgBcwGcAXsB5wFM
        ASEBQAFiAUQBYwFEAWMBRAFCAUQBpAFIAfcBbgF7AWtSAAH/AX8BvQF3Ab0BdwEzAVsBYgEuAYQBMgGE
        ATYBhAE2AYQBNgGEATYBhAE2AXYBZwGdAXMB/wF/CgABWgFrAb0BdwH3AW4BxQFMAUEBSAFjAUgBYwFI
        AWMBSAFCAUQB5wFQASkBVQEhAUQBYgFIAWMBSAFjAUgBQQFIAYMBSAGUAWYB3gF3AXsBb1IAAb0BdwG8
        AXcBUgFbAaUBOgGkATYBpAE6AaQBOgGkAToBpAE6AaQBOgGDATYB6gFGAZsBbwG9AXcB/wF/CgABWgFr
        Ab0BdwEYAW8BxQFQAUEBSAGDAUwBgwFMAWMBTAFiAUgBQQFIAWMBTAGDAUwBgwFMAUEBSAFBAUgB1gFq
        Ab0BdwFaAWsBWgFrUAAB3gF7Ab4BdwF0AWMBxAE6AaQBOgHEAToBxAE6AaQBOgGjATYBpAE6AcQBOgHE
        AToBpAE6AVMBXwG+AXsB/wF/CgABWgFrAVoBawHeAXsBWgFzAUEBTAFBAUwBgwFQAYMBUAGDAVABgwFQ
        AYMBUAGDAVABQQFMAUEBTAEYAW8B3gJ7AW8BWgFrUgABnAFzAboBcwHEAToBwwE6AcQBPgHEAT4BwwE6
        AeYBQgFQAVsBxAE6AcQBPgHEAT4BwwE6AeUBPgG7AXMBfAFvAf8BfwwAAVoBawHeAXcB3gF7AcUBVAFB
        AVABgwFQAYMBUAGDAVABgwFQAWIBUAGkAVQBWQF3Af8BewE5AWdWAAG9AXcBuwF3AQcBRwHjAToB5AE+
        AeMBPgHlAUIBlgFrAf8BfwFNAVMB4gE6AeQBPgHkAT4B4gE6ASsBTwH+AX8BnAFzAf8BfwgAAVoBawE5
        AWcBnAFzAZwBewEpAV0BQQFUAYMBVAGDAVQBgwFUAYMBVAFiAVQB5wFYARgBdwG9AXcBWgFnAVoBa1QA
        Af8BfwG+AXsBtwFrAQcBRwHiAToBBAFDAZYBawHeAXsBvQF3AdsBdwEFAUMB4wE+AQQBQwEEAUMB4gE6
        AXEBXwHfAXsBvQF3Af8BfwQAAVoBawE6AWcBvQFzAVkBdwEIAV0BQQFUAWMBWAGDAVgBgwFYAYMBWAGD
        AVgBYwFYAUIBVAHGAVgBGAF3Ab0BdwFaAWsBWgFrVAAB3gF7Ad0BewG3AW8BTgFXAbcBbwHeAXsB3gF7
        Af8BfwHeAXsBuAFvAeIBPgEDAUMBBAFDAQMBQwHiAT4BlQFrAb4BewHeAXsB/wF/AgABOQFnAb0BcwE5
        AXcBCAFhAUEBVAGDAVwBgwFcAYMBXAFiAVgBYgFYAYMBXAGDAVwBgwFcAUIBWAGlAVwB9wF2Ab0BdwFa
        AWsBWgFrUgAB/wF/Ad4BewG9AXcB3QF7Ab0BdwH/AX8B/wF/AgAB3gF7Af8BfwFwAV8B4QFCAQMBRwED
        AUcBAgFDAQQBRwG2AWsBvQF3Ad4BewE5AWcBvQFzATkBdwEIAWUBYgFcAYMBXAGDAVwBgwFcAUEBXAGE
        AWABxQFgAUEBXAGDAVwBgwFcAYMBXAFiAVwBpQFgAdYBdgG9AXcBWgFrZAABvQF3AfwBewEoAVMBAgFD
        AQMBRwEDAUcBAgFHASYBSwHZAXMBnAFzARgBYwGcAX8BKQFlAUEBXAGDAWABgwFgAYMBYAFBAWABYQFg
        AVoBewH/AX8BQQFgAUEBYAGDAWABgwFgAYMBYAFiAWABxgFgATkCewFvZgABvQF3AdgBcwElAU8BAgFH
        AQIBSwEBAUcBAgFHAbUBawG9AXcBWgFrAVoBewFiAWQBYgFgAYMBZAGDAWQBYgFgAYMBZAHVAXYB3gF7
        Ad4BewFaAXsBxQFkAUEBYAGCAWABgwFkAWIBYAFiAWQBtAF2AZwBb2YAAf8BfwHeAXsBtAFrASMBTwEB
        AUcBJQFPAZEBZwHcAXcB3gF7AVoBawG9AX8B7wFxAWMBZAFiAWQBQQFkAYMBZAG1AXYB3gF7AVoBawFa
        AWsB3gF7ARgBewHFAWgBQQFkAWIBZAFiAWQBiwFtAZsCewFvZgAB/wF/Ad4BewHeAXsBtgFvAZABYwHY
        AXMB3gF7Ad4BewH/AX8BWgFrAZwBcwGcAXsBzgFxAUIBaAFCAWgBtQF2Ad4CewFvAVoBawIAAXsBbwG9
        AXsBFwF7AcUBbAEgAWQBawFxAVoBewG9AXcBWgFraAAB/wF/Ad4BewHeAXsB3gF7Ad4BewH/AX8B/wF/
        BAABWgFrAZwBcwG9AX8BGAF7AVoBewHeAX8BewFvAVoBawQAAVoBawF7AW8B3gJ7AX8B9wF6AZwBfwG9
        AXcBWgFrAVoBa2wAAf8BfwH/AX8B/wF/DAABewFvAb0BdwGcAXMBWgFrDAABWgFrAZwBcwG9AXcBewFv
        VgABQgFNAT4HAAE+AwABKAMAAVADAAEUAwABAQEAAQEFAAHwFwAD/wEAAf4BHwH4AQ8BAQcAAfwBDwH4
        AQYIAAH4AQcB8AECCAAB8AEHAfAJAAHgAQMB8AkAAcABAQHwCQABgAEBAfAJAAGAAQAB+AsAAfgBAAEB
        CQABfgEAAQcJAAE8AQABAwkAARgBAAEBBwABgAEAAQgJAAKACgAB/wHACgAB/wHgCgAB/wHgCgAB/wHg
        AQABAggAAf8B8AEYAQYIAAH/AfwBfgEfAYcHAAs=
</value>
  </data>
  <metadata name="ClientNameColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="EffectiveDateColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="CurrentAccountManagerColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
</root>