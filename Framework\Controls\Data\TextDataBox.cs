﻿using System;
using System.ComponentModel;
using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;
using System.Windows.Forms;

namespace Framework.Controls.Data
{
    public partial class TextDataBox : DataBox
    {

        #region Startup

        public TextDataBox()
        {
            InitializeComponent();
            ErrorLabel = errorLabel1;
            GetErrorMessageMethod = GetErrorMessage;
            textBox1.TextChanged += TextBox1_TextChanged;
            textBox1.GotFocus += TextBox1_GotFocus;
            textBox1.LostFocus += TextBox1_LostFocus;
            textBox1.KeyPress += TextBox1_KeyPress;
        }

        private void TextBox1_KeyPress(object sender, KeyPressEventArgs e)
        {
            OnKeyPress(e);
        }

        private void TextBox1_GotFocus(object sender, EventArgs e)
        {
            OnGotFocus(e);
        }

        private void TextBox1_LostFocus(object sender, EventArgs e)
        {
            OnLostFocus(e);
        }

        #endregion


        #region Value

        [Browsable(true)]
        [DefaultValue("")]
        [EditorBrowsable(EditorBrowsableState.Always)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        public override string Text
        {
            get { return textBox1.Text; }
            set
            {
                if (textBox1.Text != value)
                {
                    textBox1.Text = value;
                }
            }
        }

        private void TextBox1_TextChanged(object sender, EventArgs e)
        {
            Value = textBox1.Text;
        }

        protected override void OnValueChanged()
        {
            base.OnValueChanged();
            Text = Value == null ? string.Empty : Value.ToString();
        }

        public CharacterCasing CharacterCasing
        {
            get { return textBox1.CharacterCasing; }
            set { textBox1.CharacterCasing = value; }
        }

        public int MaxLength
        {
            get { return textBox1.MaxLength; }
            set { textBox1.MaxLength = value; }
        }

        #endregion


        #region Focus



        #endregion


        #region Error message

        private string GetErrorMessage()
        {
            string errormessage = string.Empty;
            
            if (DirtyStateManager.IsDirty)
            {
                string stringtotest = Value.ToString();

                TextType caseSwitch = TextFormat;
                switch (caseSwitch)
                {
                    case TextType.Name:
                        errormessage = TextFormatError.Name(stringtotest, AllowEmptyString);
                        break;
                    case TextType.ProperNoun:
                        errormessage = TextFormatError.ProperNoun(stringtotest, AllowEmptyString);
                        break;
                    case TextType.Username:
                        errormessage = TextFormatError.Username(stringtotest, AllowEmptyString);
                        break;
                    case TextType.Email:
                        errormessage = TextFormatError.Email(stringtotest, AllowEmptyString);
                        break;
                    case TextType.Password:
                        errormessage = TextFormatError.Password(stringtotest, AllowEmptyString);
                        break;
                    case TextType.Telephone:
                        errormessage = TextFormatError.Telephone(stringtotest, AllowEmptyString);
                        break;
                    case TextType.VerificationCode:
                        errormessage = TextFormatError.VerificationCode(stringtotest, AllowEmptyString);
                        break;
                }
            }

            return errormessage;
        }

        public enum TextType
        {
            None,
            Name,
            ProperNoun,
            Username,
            Email,
            Password,
            Telephone,
            VerificationCode
        }

        private TextType _TextFormat = TextType.None;
        public TextType TextFormat
        {
            get
            {
                return _TextFormat;
            }
            set
            {
                if (_TextFormat != value)
                {
                    _TextFormat = value;
                    if (TextFormat == TextType.Password)
                    {
                        textBox1.PasswordChar = '●';
                    }
                    else
                    {
                        textBox1.PasswordChar = '\0';
                    }
                }
            }
        }

        private bool _AllowEmptyString = false;
        public bool AllowEmptyString
        {
            get
            {
                return _AllowEmptyString;
            }
            set
            {
                if (_AllowEmptyString != value)
                {
                    _AllowEmptyString = value;
                }
            }
        }

        #endregion

    }




    internal class TextFormatError
    {
        public static string Name(string stringtotest, bool emptystringallowed)
        {
            string errormessage = string.Empty;
            if (emptystringallowed == false && string.IsNullOrEmpty(stringtotest))
            {
                errormessage = "May not be blank";
            }
            else
            {
                if (stringtotest.StartsWith(" "))
                {
                    errormessage = "May not start with a space";
                }
                else
                {
                    if (stringtotest.EndsWith(" "))
                    {
                        errormessage = "May not end with a space";
                    }
                }
            }
            return errormessage;
        }

        public static string ProperNoun(string stringtotest, bool emptystringallowed)
        {
            string errormessage = Name(stringtotest, emptystringallowed);
            if (string.IsNullOrEmpty(errormessage))
            {
                bool casingiscorrect = true;
                int lowercasecharacters = 0;
                int uppercasecharacters = 0;

                // Count the upper and lower case characters in the box.
                for (int i = 0; i < stringtotest.Length; i++)
                {
                    char character = stringtotest.ToCharArray()[i];
                    if (char.IsLower(character))
                    {
                        lowercasecharacters += 1;
                    }
                    else
                    {
                        if (char.IsUpper(character))
                        {
                            uppercasecharacters += 1;
                        }
                    }
                }

                // Test if any casing errors are present.
                if (lowercasecharacters == 0)
                {
                    casingiscorrect = false;
                }
                else
                {
                    if (uppercasecharacters == 0)
                    {
                        casingiscorrect = false;
                    }
                }

                // Display an error message if the casing is incorrect.
                if (casingiscorrect == false)
                {
                    errormessage = "Please capitalize correctly";
                }
            }
            return errormessage;
        }

        public static string Username(string stringtotest, bool emptystringallowed)
        {
            string errormessage = string.Empty;
            if (emptystringallowed == false && string.IsNullOrEmpty(stringtotest))
            {
                errormessage = "May not be blank";
            }
            else
            {
                // Test each character in the specified string.
                for (int i = 0; i < stringtotest.Length; i++)
                {
                    if (char.IsLetterOrDigit(stringtotest[i]) == false)
                    {
                        // A username cannot contain a character which is not a letter or a number.
                        errormessage = "Only letters and numbers allowed";
                        break;
                    }
                }
            }
            TestForCorrectLength(ref errormessage, stringtotest, 2, 40);
            return errormessage;
        }

        public static string Password(string stringtotest, bool emptystringallowed)
        {
            string errormessage = string.Empty;

            int minimumlength = 8;
            int maximumlength = 20;
            int uppercaselettersrequired = 1;
            int lowercaselettersrequired = 1;
            int digitsrequired = 1;
            int punctuationmarksrequired = 0;

            // Check for an empty string.
            if (emptystringallowed == false && string.IsNullOrEmpty(stringtotest))
            {
                errormessage = "May not be blank";
            }
            else
            {
                // Remember how many of each required character is found.
                int uppercaselettersfound = 0;
                int lowercaselettersfound = 0;
                int digitsfound = 0;
                int punctuationmarksfound = 0;

                // Test each character in the specified string.
                for (int i = 0; i < stringtotest.Length; i++)
                {
                    // Look for separator characters.
                    if (char.IsSeparator(stringtotest[i]))
                    {
                        errormessage = "May not contain separator characters";
                        break;
                    }
                    else
                    {
                        // Count required characters.
                        uppercaselettersfound += (char.IsUpper(stringtotest[i]) ? 1 : 0);
                        lowercaselettersfound += (char.IsLower(stringtotest[i]) ? 1 : 0);
                        digitsfound += (char.IsDigit(stringtotest[i]) ? 1 : 0);
                        punctuationmarksfound += (char.IsPunctuation(stringtotest[i]) ? 1 : 0);
                    }
                }
                TestForCorrectLength(ref errormessage, stringtotest, minimumlength, maximumlength);

                // Test for correct quantity of required characters.
                BuildCharacterRequirementErrorMessage(ref errormessage, uppercaselettersrequired, uppercaselettersfound, "uppercase letter");
                BuildCharacterRequirementErrorMessage(ref errormessage, lowercaselettersrequired, lowercaselettersfound, "lowercase letter");
                BuildCharacterRequirementErrorMessage(ref errormessage, digitsrequired, digitsfound, "digit");
                BuildCharacterRequirementErrorMessage(ref errormessage, punctuationmarksrequired, punctuationmarksfound, "punctuation mark");
            }
            return errormessage;
        }

        private static void BuildCharacterRequirementErrorMessage
            (ref string errormessage, int requiredcharacters, int foundcharacters, string charactername)
        {
            if (string.IsNullOrEmpty(errormessage))
            {
                if (foundcharacters < requiredcharacters)
                {
                    var errorbuilder = new StringBuilder();
                    errorbuilder.Append("Must contain ");
                    errorbuilder.Append(requiredcharacters.ToString() + " ");
                    errorbuilder.Append(charactername);
                    errorbuilder.Append(requiredcharacters > 1 ? "s" : string.Empty);
                    errormessage = errorbuilder.ToString();
                }
            }
        }

        public static string Telephone(string stringtotest, bool emptystringallowed)
        {
            string errormessage = string.Empty;
            if (emptystringallowed == false && string.IsNullOrEmpty(stringtotest))
            {
                errormessage = "May not be blank";
            }
            else
            {
                // Test each character in the specified string.
                for (int i = 0; i < stringtotest.Length; i++)
                {
                    if (char.IsDigit(stringtotest[i]) == false)
                    {
                        // A phone number must contain only digits.
                        errormessage = "May only contain digits";
                        break;
                    }
                }
                TestForCorrectLength(ref errormessage, stringtotest, 10, 10);
            }
            return errormessage;
        }

        public static string VerificationCode(string stringtotest, bool emptystringallowed)
        {
            string errormessage = string.Empty;
            // Test each character in the specified string.
            if (emptystringallowed == false && string.IsNullOrEmpty(stringtotest))
            {
                errormessage = "May not be blank";
            }
            else
            {
                for (int i = 0; i < stringtotest.Length; i++)
                {
                    if (char.IsDigit(stringtotest[i]) == false)
                    {
                        // A verification code must contain only digits.
                        errormessage = "May only contain digits";
                        break;
                    }
                }
                TestForCorrectLength(ref errormessage, stringtotest, 4, 4);
            }
            return errormessage;
        }

        public static string Email(string stringtotest, bool emptystringallowed)
        {
            string errormessage = string.Empty;
            if (emptystringallowed == false && string.IsNullOrEmpty(stringtotest))
            {
                errormessage = "May not be blank";
            }
            else
            {
                if (IsValidEmail(stringtotest) == false)
                {
                    errormessage = "Invalid email address format";
                }

            }
            return errormessage;
        }

        private static void TestForCorrectLength(ref string errormessage, string stringtotest, int min, int max)
        {
            // Proceed only if there have been no errors thus far.
            if (string.IsNullOrEmpty(errormessage) == false)
                return;

            if (stringtotest.Length < min)
            {
                errormessage = "Too short";
            }
            else
            {
                if (stringtotest.Length > max)
                {
                    errormessage = "Too long";
                }
            }
        }

        public static bool IsValidEmail(string emailaddress)
        {
            RegexUtilities emailchecker = new RegexUtilities();
            return emailchecker.IsValidEmail(emailaddress);
        }
    }




    /// <summary>
    /// Adopted from:
    /// https://msdn.microsoft.com/en-us/library/01escwtf%28v=vs.110%29.aspx
    /// </summary>
    internal class RegexUtilities
    {
        bool invalid = false;

        internal bool IsValidEmail(string strIn)
        {
            invalid = false;
            if (String.IsNullOrEmpty(strIn))
                return false;

            // Use IdnMapping class to convert Unicode domain names. 
            try
            {
                strIn = Regex.Replace(strIn, @"(@)(.+)$", DomainMapper,
                                      RegexOptions.None, TimeSpan.FromMilliseconds(200));
            }
            catch (RegexMatchTimeoutException)
            {
                return false;
            }

            if (invalid)
                return false;

            // Return true if strIn is in valid e-mail format. 
            try
            {
                return Regex.IsMatch(strIn,
                      @"^(?("")("".+?(?<!\\)""@)|(([0-9a-z]((\.(?!\.))|[-!#\$%&'\*\+/=\?\^`\{\}\|~\w])*)(?<=[0-9a-z])@))" +
                      @"(?(\[)(\[(\d{1,3}\.){3}\d{1,3}\])|(([0-9a-z][-\w]*[0-9a-z]*\.)+[a-z0-9][\-a-z0-9]{0,22}[a-z0-9]))$",
                      RegexOptions.IgnoreCase, TimeSpan.FromMilliseconds(250));
            }
            catch (RegexMatchTimeoutException)
            {
                return false;
            }
        }

        private string DomainMapper(Match match)
        {
            // IdnMapping class with default property values.
            IdnMapping idn = new IdnMapping();

            string domainName = match.Groups[2].Value;
            try
            {
                domainName = idn.GetAscii(domainName);
            }
            catch (ArgumentException)
            {
                invalid = true;
            }
            return match.Groups[1].Value + domainName;
        }

    }

}
