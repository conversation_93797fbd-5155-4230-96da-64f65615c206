﻿Public Class SubformResearchCategoryList

    Private DataObject As Contract

    Public ReadOnly Property TitleText As String
        Get
            Return "Contract " & DataObject.ContractNumber & " - Research Categories"
        End Get
    End Property

    Public Sub New(ContractObject As Contract)

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        DataObject = ContractObject

        ' Configure grid.
        Grid.AutoGenerateColumns = False
        Grid.DataSource = DataObject.ResearchCategoryBindingSource
        Dim GridManagerResearchCategories As New GridManager(Grid, TextEditSearch, Nothing, Nothing, _
        PictureAdvancedSearch, PictureClearSearch, ButtonEdit, ButtonDelete)

    End Sub

    Private Sub SubformResearchCategoryList_Load(sender As Object, e As System.EventArgs) Handles Me.Load
        AddDataBindings()
    End Sub

    Private Sub ButtonBack_Click(sender As System.Object, e As System.EventArgs) Handles ButtonBack.Click
        RevertToParentSubform()
        ' Refresh the related controls on the parent subform.
        If TypeOf ParentSubform Is SubformContractTasks Then
            CType(ParentSubform, SubformContractTasks).RefreshControls(ParentSubform)
            CType(ParentSubform, SubformContractTasks).RefreshWarnings()
        End If
    End Sub

    Private Sub ButtonAdd_Click(sender As System.Object, e As System.EventArgs) Handles ButtonAdd.Click

        ' Create a new research category row.
        Dim CategoryTable As DataSetContract.ResearchCategoryDataTable = DataObject.Row.Table.DataSet.Tables("ResearchCategory")
        Dim NewRow As DataSetContract.ResearchCategoryRow = CategoryTable.NewResearchCategoryRow
        NewRow.ContractID = DataObject.ContractID

        ' Create a new data object using the new row.
        Dim ResearchCategoryObject As New ResearchCategory(NewRow, My.Settings.DBConnection, DataObject)

        ' If other research categories already exist, copy some of their properties to assist the user.
        If CategoryTable.Rows.Count > 0 Then
            Dim ExistingRow As DataSetContract.ResearchCategoryRow = CategoryTable.Rows(0)
            Dim ExistingObject As New ResearchCategory(ExistingRow, My.Settings.DBConnection, DataObject)
            With NewRow
                .FromDate = ExistingRow.FromDate
                .Months = ExistingRow.Months
                .FirstMonth = ExistingObject.FirstMonth
                .LastMonth = ExistingObject.LastMonth
                If DataObject.Signed Then
                    ' This contract is signed. The user may not add another research category unless the fee is
                    ' R 0.00, otherwise they would be changing the value of a signed contract, which is not allowed.
                    .Fee = 0
                Else
                    .Fee = ExistingRow.Fee
                End If
            End With
        End If

        ' Open a child subform to modify the new data object.
        AddChild(New SubformResearchCategory(ResearchCategoryObject))

    End Sub

    Private Sub ButtonEdit_Click(sender As System.Object, e As System.EventArgs) Handles ButtonEdit.Click

        ' Exit if the number of rows selected is not 1.
        If Grid.SelectedRows.Count = 0 Then
            CType(TopLevelControl, BaseForm).ShowMessage("Invalid Selection", "Please select one and only one row to perform this action.")
            Exit Sub
        End If

        ' Get the row selected.
        Dim SelectedGridRow As DataGridViewRow = Grid.SelectedRows(0)
        Dim SelectedRow As DataSetContract.ResearchCategoryRow = CType(SelectedGridRow.DataBoundItem, DataRowView).Row

        ' Create a new data object using the new row.
        Dim ResearchCategoryObject As New ResearchCategory(SelectedRow, My.Settings.DBConnection, DataObject)

        ' Open a child subform to modify the new data object.
        AddChild(New SubformResearchCategory(ResearchCategoryObject))

    End Sub

    Private Sub Grid_CellMouseDoubleClick _
    (ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) _
    Handles Grid.CellMouseDoubleClick
        If e.RowIndex > -1 Then
            ' The row double-clicked is NOT the column header row. Handle the event.
            ButtonEdit_Click(sender, e)
        End If
    End Sub

    Private Sub ButtonDelete_Click(sender As System.Object, e As System.EventArgs) Handles ButtonDelete.Click
        ResearchCategory.Delete(Grid, My.Settings.DBConnection, DataObject)
    End Sub

    Private Sub AddDataBindings()
        LabelTitle.DataBindings.Add("Text", Me, "TitleText")
    End Sub

End Class
