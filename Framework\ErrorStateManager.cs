﻿using Framework.Controls.Data;
using Framework.Forms;
using Framework.Surfaces;
using System;
using System.Windows.Forms;

namespace Framework
{
    public class ErrorStateManager
    {
        private Control ManagedControl;
        private Label ErrorMessageLabel;
        private Func<string> GetErrorMessageMethod;
        private bool StateInitalizationCompleted = false;


        public ErrorStateManager(Control managedcontrol)
        {
            ManagedControl = managedcontrol;
        }

        /// <summary>
        /// Use this initializer for the error state manager of a DataForm or Surface.
        /// </summary>
        public void InitializeState()
        {
            InitializeState(null, null);
        }

        /// <summary>
        /// Use this initializer for the error state manager of a DataBox.
        /// </summary>
        public void InitializeState(Label errormessagelabel, Func<string> geterrormessagemethod)
        {
            if (StateInitalizationCompleted == false)
            {
                if (errormessagelabel != null)
                {
                    ErrorMessageLabel = errormessagelabel;
                    ErrorMessageLabel.Text = string.Empty;
                }
                GetErrorMessageMethod = geterrormessagemethod;
                SubscribeToEventsAffectingTheHasErrorProperty();
                StateInitalizationCompleted = true;
            }
        }

        private void SubscribeToEventsAffectingTheHasErrorProperty()
        {
            if (ManagedControl is DataBox)
            {
                DataBox databox = (DataBox)ManagedControl;
                databox.ValueChanged += ManagedControl_ValueChanged;
            }
            else
            {
                if (ManagedControl is Surface | ManagedControl is DataForm)
                {
                    SubscribeToHasErrorChangedEventOfChildControls(ManagedControl.Controls);
                }
            }
        }

        private void ManagedControl_ValueChanged(object sender, EventArgs e)
        {
            ErrorMessage = GetErrorMessageMethod?.Invoke();
        }

        private void SubscribeToHasErrorChangedEventOfChildControls(Control.ControlCollection controls)
        {
            for (int i = 0; i < controls.Count; i++)
            {
                Control control = controls[i];
                if (control is DataBox)
                {
                    ((DataBox)control).ErrorStateManager.HasErrorChanged += ChildControlErrorStateManager_HasErrorChanged;
                }
                else
                {
                    if (control is Surface)
                    {
                        ((Surface)control).ErrorStateManager.HasErrorChanged += ChildControlErrorStateManager_HasErrorChanged;
                    }
                    else
                    {
                        if (control.HasChildren)
                        {
                            SubscribeToHasErrorChangedEventOfChildControls(control.Controls);
                        }
                    }
                }
            }
        }

        private void ChildControlErrorStateManager_HasErrorChanged(object sender, EventArgs e)
        {
            ErrorStateManager manager = (ErrorStateManager)sender;
            if (manager.HasError)
            {
                HasError = true;
            }
            else
            {
                HasError = ChildControlWithErrorFound(ManagedControl.Controls);
            }
        }

        public void UpdateErrorMessage()
        {
            ErrorMessage = GetErrorMessageMethod?.Invoke();
        }

        internal bool ChildControlWithErrorFound(Control.ControlCollection controls)
        {
            bool errorchildfound = false;

            for (int i = 0; i < controls.Count; i++)
            {
                Control childcontrol = controls[i];
                if (childcontrol is DataBox)
                {
                    if (((DataBox)childcontrol).ErrorStateManager.HasError)
                    {
                        errorchildfound = true;
                        break;
                    }
                }
                else
                {
                    if (childcontrol is Surface)
                    {
                        if (((Surface)childcontrol).ErrorStateManager.HasError)
                        {
                            errorchildfound = true;
                            break;
                        }
                    }
                    else
                    {
                        if (childcontrol.HasChildren)
                        {
                            errorchildfound = ChildControlWithErrorFound(childcontrol.Controls);
                            if (errorchildfound)
                            {
                                break;
                            }
                        }
                    }
                }
            }

            return errorchildfound;
        }

        private bool _HasError = false;
        public bool HasError
        {
            get { return _HasError; }
            set
            {
                if (_HasError != value)
                {
                    _HasError = value;
                    OnHasErrorChanged(EventArgs.Empty);
                }
            }
        }

        public event EventHandler HasErrorChanged;
        private void OnHasErrorChanged(EventArgs e)
        {
            HasErrorChanged?.Invoke(this, e);
        }

        public string ErrorMessage
        {
            get { return ErrorMessageLabel.Text; }
            set
            {
                if (ErrorMessageLabel.Text != value)
                {
                    ErrorMessageLabel.Text = value;
                    HasError = string.IsNullOrEmpty(value) == false;
                }
            }
        }

    }
}
