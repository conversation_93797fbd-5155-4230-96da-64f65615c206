﻿using System.Drawing;
using System.Windows.Forms;

namespace Framework.Controls.TabSystem
{
    public class TabGroup : Panel
    {

        public TabGroup()
        {
            ControlAdded += TabGroup_ControlAdded;
        }

        private void TabGroup_ControlAdded(object sender, ControlEventArgs e)
        {
            if (e.Control is Tab == false)
            {
                Controls.Remove(e.Control);
            }
            else
            {
                e.Control.Location = new Point(0, 0);
                SetTabLocation(e.Control.Height);
            }
        }

        private void SetTabLocation(int defaulttabheight)
        {
            for (int i = 0; i < Controls.Count; i++)
            {
                int y = defaulttabheight * i;
                int x = Controls[i].Location.X;
                Controls[i].Location = new Point(x, y);
            }
        }

        public Tab SelectedTab
        {
            get
            {
                Tab selectedtab = null;

                // Try to find the tab whose SELECTED property is equal to TRUE.
                for (int i = 0; i < Controls.Count; i++)
                {
                    Tab tab = (Tab)Controls[i];
                    if (tab.Selected)
                    {
                        selectedtab = tab;
                        break;
                    }
                }

                // If no tab has a SELECTED property value of TRUE, then return the first tab.
                if (selectedtab == null)
                {
                    if (Controls.Count > 0)
                    {
                        selectedtab = (Tab)Controls[0];
                    }
                }

                return selectedtab;
            }
        }

    }
}
