﻿using DataAccess;
using System;

namespace DataService.Security
{
    class CreateRoleCommandExecutor : CommandExecutor<CreateRoleCommand>
    {

        public override void Execute(CreateRoleCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.CreateRole))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.AddInputParameter("rolename", command.RoleName);
                storedprocedure.AddInputParameter("roledescription", command.RoleDescription);
                storedprocedure.AddOutputParameter<Guid>("roleid");
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    command.RoleId = (Guid)storedprocedure.GetOutputParameterValue("roleid");
                }
            }
        }

    }
}
