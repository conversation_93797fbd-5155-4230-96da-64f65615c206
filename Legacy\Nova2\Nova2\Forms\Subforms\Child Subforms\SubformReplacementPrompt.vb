﻿Public Class SubformReplacementPrompt
    Private SelectedHeat As DataRow = Nothing
    Public Shared Sub AskUserToSelectReasonsForReplacementOfContract _
(ByVal ContractObject As Contract, ByRef errorMessage As String, ByRef NewContractNumber As String, ByRef Execute As Boolean)

        errorMessage = String.Empty
        NewContractNumber = String.Empty
        Execute = False

        ' Create an instance of this form.
        Dim Prompt As New SubformReplacementPrompt

        Prompt.ButtonOK.Enabled = False

        Dim PromptResult As DialogResult = Prompt.ShowDialog()

        ' Check the result of the dialog.
        If PromptResult = Windows.Forms.DialogResult.OK Then
            If Prompt.CheckedListBoxReasonsForCancelAndReplace.CheckedItems.Count <> 0 Then
                'get the current am first
                ' Get a new contract number for the clone.
                'we should only do this for pnp staff, and not for our own am's
                Dim CurrentAmCode As String = SubformReplacementPrompt.getCurrentAm()
                Dim AMCode As String = ContractObject.ContractNumber.Substring(0, 2)
                If Not IsNothing(CurrentAmCode) Then
                    If CurrentAmCode <> AMCode Then
                        AMCode = CurrentAmCode
                    End If
                End If
                NewContractNumber = Contract.CreateNewContractNumber(AMCode, My.Settings.DBConnection)

                Dim i As Integer
                Dim reasons As String
                reasons = "Selected Reasons:" & vbLf
                i = 0
                Do While (i _
                <= (Prompt.CheckedListBoxReasonsForCancelAndReplace.Items.Count - 1))
                    If Prompt.CheckedListBoxReasonsForCancelAndReplace.GetItemChecked(i) Then
                        reasons = (reasons + ("Reason " _
                        + ((i + 1).ToString + (" = " _
                        + (Prompt.CheckedListBoxReasonsForCancelAndReplace.Items(i).ToString + "" & vbLf)))))
                    End If
                    i = (i + 1)
                Loop
                ' Use the new contract number to clone the current contract.
                Dim Errors As String = ContractObject.Clone(NewContractNumber, Prompt.chkIsReplacement.Checked, reasons, Prompt.HyperlinkProposalHeat.Text)

                ' Stop if there were any errors.
                If String.IsNullOrEmpty(Errors) = False Then
                    errorMessage = Errors
                Else
                    Execute = True
                End If
            End If
        End If
    End Sub

    Private Sub CheckedListBoxReasonsForCancelAndReplace_ItemCheck(sender As Object, e As ItemCheckEventArgs) Handles CheckedListBoxReasonsForCancelAndReplace.ItemCheck

        If e.NewValue = CheckState.Checked Then
            If (CheckedListBoxReasonsForCancelAndReplace.CheckedItems.Count > 0) And (HyperlinkProposalHeat.Text <> "Select...") Then
                ButtonOK.Enabled = True
                Return
            End If
        End If



        'Last Item is uncheked
        If ((CheckedListBoxReasonsForCancelAndReplace.CheckedItems.Count = 1) _
            AndAlso (e.NewValue = CheckState.Unchecked)) Then
            ButtonOK.Enabled = False
            Return
        End If

        'First Item is checked
        If ((CheckedListBoxReasonsForCancelAndReplace.CheckedItems.Count = 0) _
            AndAlso (e.NewValue = CheckState.Checked) AndAlso (HyperlinkProposalHeat.Text <> "Select...")) Then
            ButtonOK.Enabled = True
            Return
        End If

    End Sub
    Private Function getCurrentAm() As String
        ' The SQL statement to execute.

        Dim Statement As String = String.Format("EXEC dbo.getCurrentAM '{0}'", My.User.Name)
        ' Get the result of the query and make a variable indicating whether or not the current user has permission to edit this contract.
        Dim Result As DataTable = LiquidShell.LiquidAgent.GetSqlDataTable("Data Source=primenovasql\Live;Initial Catalog=NovaDB;User ID=NovaApp;Password=******************;Connect Timeout=0", Statement, String.Empty)
        If IsNothing(Result) = False Then
            If Result.Rows.Count > 0 Then
                Return Result.Rows(0)("CODE").ToString()
            Else
                Return Nothing
            End If
        Else
            Return Nothing
        End If

    End Function


    Public ReadOnly Property SelectedHeatName As String
        Get
            If IsNothing(SelectedHeat) Then
                Return "Select..."
            Else
                Return SelectedHeat("ContractProposalHeatName")
            End If
        End Get
    End Property
    Private Sub HyperlinkProposalHeat_Click(sender As System.Object, e As System.EventArgs) Handles HyperlinkProposalHeat.Click

        ' Stop if no Account manager has been selected.

        Me.TopMost = False
        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupProposalHeat.SelectRows(My.Settings.DBConnection, False, Nothing)

        ' Update the variable and the label with the selection.
        If SelectedItems.Count > 0 Then
            SelectedHeat = SelectedItems(0)
            HyperlinkProposalHeat.DataBindings("Text").ReadValue()
            ' Reset the error text of the control to an empty string.
            'LiquidAgent.ControlValidation(HyperlinkProposalHeat, String.Empty)
            If (CheckedListBoxReasonsForCancelAndReplace.CheckedItems.Count > 0) And (HyperlinkProposalHeat.Text <> "Select...") Then
                ButtonOK.Enabled = True

            End If


            Me.TopMost = True
        End If

    End Sub

    Private Sub frmReplacementPrompt_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        HyperlinkProposalHeat.DataBindings.Add("Text", Me, "SelectedHeatName")
    End Sub

    Private Sub ButtonOK_Click(sender As Object, e As EventArgs) Handles ButtonOK.Click

    End Sub
End Class