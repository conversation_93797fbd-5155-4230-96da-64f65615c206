Imports System.Data.SqlClient

Public Class Category
    Inherits OldBaseObject


#Region "Fields"

    ' Custom fields
    Private _MediaCategoryBindingSource As BindingSource
    Private _StoreMediaCategoryPermissionBindingSource As BindingSource = New BindingSource()

#End Region

#Region "Database Column Properties"

    Public ReadOnly Property CategoryID() As Integer
        ' This object's identifier.
        Get
            Return Row("CategoryID")
        End Get
    End Property

    Public Property CategoryName() As String
        ' Descriptive name of the media.
        Get
            Return Row("CategoryName")
        End Get
        Set(ByVal value As String)
            ' Clean the proposed new value.
            value = value.Trim
            If Not String.Compare(Row("CategoryName"), value, False) = 0 Then
                ' New value is different than existing value. Proceed with update.
                AddLog("CategoryName", "CategoryName", value.ToString)
                Row("CategoryName") = value
                IsDirty = True
            End If
        End Set
    End Property

    Public Property Dormant() As Boolean
        ' Dormant media is inactive and may not be sold anymore.
        Get
            Return Row("Dormant")
        End Get
        Set(ByVal value As Boolean)
            If Not Row("Dormant") = value Then
                ' New value is different than existing value. Proceed with update.
                AddLog("CategoryName", "Dormant", value.ToString)
                Row("Dormant") = value
                IsDirty = True
            End If
        End Set
    End Property

    Public Property CrossoverAllowed() As Boolean
        ' Whether a category is allowed in a crossover or not.
        Get
            Return Row("CrossoverAllowed")
        End Get
        Set(ByVal value As Boolean)
            If Not Row("CrossoverAllowed") = value Then
                ' New value is different than existing value. Proceed with update.
                AddLog("CategoryName", "CrossoverAllowed", value.ToString)
                Row("CrossoverAllowed") = value
                IsDirty = True
            End If
        End Set
    End Property

#End Region

#Region "Custom Properties"

    Public ReadOnly Property StoreMediaCategoryPermissionBindingSource() As BindingSource
        Get
            Return _StoreMediaCategoryPermissionBindingSource
        End Get
    End Property

    Public ReadOnly Property MediaCategoryBindingSource() As BindingSource
        Get
            Return _MediaCategoryBindingSource
        End Get
    End Property

#End Region

#Region "Public Shared Methods"

    Public Shared Function GetListData _
    (ByVal ConString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) As System.Windows.Forms.BindingSource

        ' Instantiate a new data set and create a sql connection to use to populate it.
        Dim ListDataSet As New DataSetCategory
        Dim SqlCon As New SqlClient.SqlConnection(ConString)

        ' Create table adapters to collect list data and related data.
        Dim ListAdapter As New DataSetCategoryTableAdapters.CategoryTableAdapter
        ListAdapter.Connection = SqlCon

        ' Populate tables in the dataset.
        Try
            ListAdapter.Fill(ListDataSet.Tables("Category"))
        Catch ex As Exception
            ErrorMessage += LiquidShell.LiquidAgent.GetErrorMessage(ex)
            Return Nothing
        Finally
            ListAdapter.Dispose()
            SqlCon.Dispose()
        End Try

        ' Return a binding source linked to the retrieved data.
        Dim ReturnBindingSource As BindingSource = New BindingSource(ListDataSet, "Category")
        ReturnBindingSource.Sort = "CategoryName"
        Return ReturnBindingSource

    End Function

    Public Shared Sub Delete(ByVal Grid As DataGridView, ByVal ConnectionString As String)
        ' Delete one or more rows from the database.

        ' Build a list of data relation names of child rows that must be deleted with the parent row.
        Dim DeletableChildRelationNames As New List(Of String)
        DeletableChildRelationNames.Add("FK_MediaCategory_Category")

        ' Create an audit table to record this action.
        Dim AuditLog As New DataSetAudit.AuditLogDataTable

        ' Create a list of names of the columns that will be used to describe each row being deleted.
        Dim ColumnNames As New List(Of String)
        ColumnNames.Add("CategoryName")

        ' Add row descriptions to the Tag properties of all selected grid rows (these will be used as the object name
        ' in the audit log entries for rows that are deleted.)
        For Each SelectedGridRow As DataGridViewRow In Grid.SelectedRows
            Dim Row As DataRow = CType(SelectedGridRow.DataBoundItem, DataRowView).Row
            SelectedGridRow.Tag = Row("CategoryName")
        Next

        ' Delete rows from the dataset.
        LiquidShell.LiquidAgent.DeleteParentRows _
        (Grid, "Category_BurstCount", DeletableChildRelationNames, AuditLog, "Contract")

        ' Get the tables and data set from which rows are being deleted to use as a parameter for the table adapters.
        Dim GridTable As DataTable = CType(CType(Grid.DataSource, BindingSource).List, DataView).Table
        Dim DataSet As DataSet = GridTable.DataSet

        ' If any rows were deleted from the data set, delete them from the database too.
        Using DeletedRows As New DataView(GridTable, "", "", DataViewRowState.Deleted)
            If IsNothing(DeletedRows) = False AndAlso DeletedRows.Count > 0 Then

                ' Create and configure table adapters to connect to the database.
                Dim Adapter As New DataSetCategoryTableAdapters.CategoryTableAdapter
                Dim MediaCategoryAdapter As New DataSetCategoryTableAdapters.MediaCategoryTableAdapter
                Dim AuditAdapter As New DataSetAuditTableAdapters.AuditLogTableAdapter

                ' Create a new connection for the table adapters to use.
                Dim SqlCon As New SqlClient.SqlConnection(ConnectionString)
                Adapter.Connection = SqlCon
                MediaCategoryAdapter.Connection = SqlCon
                AuditAdapter.Connection = SqlCon

                ' Get the form that is consuming this method so that the ShowMessage method can be used.
                Dim Consumingform As LiquidShell.BaseForm = CType(Grid.TopLevelControl, LiquidShell.BaseForm)

                ' Perform the delete operation.
                Try
                    MediaCategoryAdapter.Update(DataSet.Tables("MediaCategory"))
                    Adapter.Update(GridTable)
                    AuditAdapter.Update(AuditLog)
                Catch ex As Exception
                    Consumingform.ShowMessage("Aaaagghh!!!  An error occured while trying to delete. The technical description of the error is:" _
                    & vbCrLf & vbCrLf & LiquidShell.LiquidAgent.GetErrorMessage(ex))
                Finally
                    ' Dispose all data adapters.
                    Adapter.Dispose()
                    MediaCategoryAdapter.Dispose()
                    AuditAdapter.Dispose()
                    ' Close all connections.
                    SqlCon.Dispose()
                End Try

            End If
        End Using

    End Sub

#End Region

#Region "Public Methods"

#Region "Store Media Category Permission"
    Public Function StoreMediaCategoryPermissionExecution_TrueForInsertFalseForDelete(ByVal Instruction As Boolean, ByVal CategoryID As Integer, ByVal StoreId As Integer, ByVal MediaID As Integer, ByVal ConnectionString As String, ByRef errorMessage As String) As Boolean
        errorMessage = String.Empty
        Dim DoneDeal As Boolean = False
        If (Instruction = True) Then
            DoneDeal = InsertStoreMediaCategoryPermission(CategoryID, StoreId, MediaID, ConnectionString, errorMessage)
        ElseIf (Instruction = False) Then
            DoneDeal = DeleteStoreMediaCategoryPermission(CategoryID, StoreId, MediaID, ConnectionString, errorMessage)
        End If
        Return DoneDeal
    End Function


    Private Function DeleteStoreMediaCategoryPermission(ByVal CategoryID As Integer, ByVal StoreId As Integer, ByVal MediaID As Integer, ByVal ConnectionString As String, ByRef errorMessage As String) As Boolean

        Try
            Remove_StoreMediaCategoryPermission_Selection(CategoryID, StoreId, MediaID, ConnectionString, errorMessage)

            If Not String.IsNullOrEmpty(errorMessage) Then
                Return False
            End If
            Return True
        Catch ex As Exception
            errorMessage += LiquidShell.LiquidAgent.GetErrorMessage(ex)
            Return False
        End Try
    End Function

    Private Function InsertStoreMediaCategoryPermission(ByVal CategoryID As Integer, ByVal StoreId As Integer, ByVal MediaID As Integer, ByVal ConnectionString As String, ByRef errorMessage As String) As Boolean

        Try
            Save_StoreMediaCategoryPermission_Selection(CategoryID, StoreId, MediaID, ConnectionString, errorMessage)

            If Not String.IsNullOrEmpty(errorMessage) Then
                Return False
            End If
            Return True
        Catch ex As Exception
            errorMessage += LiquidShell.LiquidAgent.GetErrorMessage(ex)
            Return False
        End Try
    End Function



    Private Sub Remove_StoreMediaCategoryPermission_Selection(ByVal CategoryID As Integer, ByVal StoreId As Integer, ByVal MediaID As Integer, ByVal ConnectionString As String, ByRef errorMessage As String)
        errorMessage = String.Empty
        Dim cnn As SqlConnection = New SqlConnection(ConnectionString)
        Dim cmd As SqlCommand = New SqlCommand("Store.proc_del_StoreMediaCategoryPermission", cnn)

        Dim retMsg As String = String.Empty
        Try
            cnn.Open()

            cmd.CommandType = CommandType.StoredProcedure
            Dim param() As SqlParameter = New SqlParameter((4) - 1) {}

            Dim SelectedStoreID = New SqlParameter("@StoreID", StoreId)
            param(0) = SelectedStoreID

            Dim SelectedMediaID = New SqlParameter("@MediaID", MediaID)
            param(1) = SelectedMediaID

            Dim SelectedCategoryID = New SqlParameter("@CategoryID", CategoryID)
            param(2) = SelectedCategoryID

            Dim retMsgOutput = New SqlParameter("@RetMsg", SqlDbType.VarChar, 255)
            retMsgOutput.Value = retMsg
            retMsgOutput.Direction = ParameterDirection.Output

            'the output parameter
            param(3) = retMsgOutput

            Dim i As Integer = 0
            Do While (param.Length > i)
                cmd.Parameters.Add(param(i))
                i = (i + 1)
            Loop

            'executes the stored procedure - select statement follows the insert statement
            cmd.ExecuteNonQuery()

        Catch ex As OperationCanceledException

        Catch ex As Exception
            errorMessage = ex.Message
        Finally
            If (Not (cnn) Is Nothing) Then
                cnn.Close()
            End If

            If (Not (cmd) Is Nothing) Then
                cmd.Dispose()
            End If

        End Try
    End Sub


    Private Sub Save_StoreMediaCategoryPermission_Selection(ByVal CategoryID As Integer, ByVal StoreId As Integer, ByVal MediaID As Integer, ByVal ConnectionString As String, ByRef errorMessage As String)
        errorMessage = String.Empty
        Dim cnn As SqlConnection = New SqlConnection(ConnectionString)
        Dim cmd As SqlCommand = New SqlCommand("Store.proc_ins_StoreMediaCategoryPermission", cnn)

        Dim retMsg As String = String.Empty
        Try
            cnn.Open()

            cmd.CommandType = CommandType.StoredProcedure
            Dim param() As SqlParameter = New SqlParameter((4) - 1) {}

            Dim SelectedStoreID = New SqlParameter("@StoreID", StoreId)
            param(0) = SelectedStoreID

            Dim SelectedMediaID = New SqlParameter("@MediaID", MediaID)
            param(1) = SelectedMediaID

            Dim SelectedCategoryID = New SqlParameter("@CategoryID", CategoryID)
            param(2) = SelectedCategoryID

            Dim retMsgOutput = New SqlParameter("@RetMsg", SqlDbType.VarChar, 255)
            retMsgOutput.Value = retMsg
            retMsgOutput.Direction = ParameterDirection.Output

            'the output parameter
            param(3) = retMsgOutput

            Dim i As Integer = 0
            Do While (param.Length > i)
                cmd.Parameters.Add(param(i))
                i = (i + 1)
            Loop

            'executes the stored procedure - select statement follows the insert statement
            cmd.ExecuteNonQuery()

        Catch ex As OperationCanceledException

        Catch ex As Exception
            errorMessage = ex.Message
        Finally
            If (Not (cnn) Is Nothing) Then
                cnn.Close()
            End If

            If (Not (cmd) Is Nothing) Then
                cmd.Dispose()
            End If

        End Try
    End Sub


    Public Function GetStoresForSelectedMedia(ByVal CategoryID As Integer, ByVal MediaID As Integer, ByVal ConnectionString As String, ByRef ErrorMessage As String) As Boolean

        ' Populate tables in the dataset.
        Try
            Dim tblStoreMediaCategoryPermissionList As DataTable = GetStoreMediaCategoryPermissionsList(CategoryID, MediaID, ConnectionString, ErrorMessage)

            If Not String.IsNullOrEmpty(ErrorMessage) Then
                Return False
            Else
                If tblStoreMediaCategoryPermissionList.Rows.Count > 0 Then
                    _StoreMediaCategoryPermissionBindingSource.DataSource = tblStoreMediaCategoryPermissionList
                    _StoreMediaCategoryPermissionBindingSource.Sort = "StoreName"
                End If
            End If
            Return True
        Catch ex As Exception
            ErrorMessage += LiquidShell.LiquidAgent.GetErrorMessage(ex)
            Return False
        End Try

    End Function


    Private Function GetStoreMediaCategoryPermissionsList(ByVal CategoryID As Integer, ByVal MediaID As Integer, ByVal ConnectionString As String, ByRef ErrorMessage As String) As DataTable
        Try
            Dim tblStoreMediaCategoryPermission As DataTable = New DataTable
            Dim conn As SqlConnection = New SqlConnection(ConnectionString)
            Dim query As String = String.Format("SELECT StoreID, StoreNumber, StoreName, RegionName, isProhibited FROM [dbo].[udfGetStoreMediaCategoryPermissions]({0},{1})", MediaID, CategoryID)
            Dim cmd As SqlCommand = New SqlCommand(query, conn)
            conn.Open()
            ' create data adapter
            Dim da As SqlDataAdapter = New SqlDataAdapter(cmd)
            ' this will query your database and return the result to your datatable
            da.Fill(tblStoreMediaCategoryPermission)

            Return tblStoreMediaCategoryPermission

        Catch ex As Exception
            ErrorMessage += LiquidShell.LiquidAgent.GetErrorMessage(ex)
            Return Nothing
        End Try
    End Function
#End Region


    Public Sub New(ByVal DataSource As BindingSource, ByVal AddNew As Boolean)
        ' This constructor takes a binding source and a boolean indicating whether a new
        ' row must be added or whether this is an existing row being edited.

        If AddNew Then
            ' This is a new row being added.
            DataSource.AddNew()
        End If

        ' Update the base class binding source variable.
        DataBindingSource = DataSource
    End Sub

    Public Sub Save(ByVal ConnectionString As String, ByVal GridsToAudit As List(Of DataGridView))
        ' Save this object to the database.

        ' Create and configure table adapters to connect to the database.
        Dim SqlAdapter As New DataSetCategoryTableAdapters.CategoryTableAdapter
        Dim MediaCategoryAdapter As New DataSetCategoryTableAdapters.MediaCategoryTableAdapter
        Dim AuditAdapter As New DataSetAuditTableAdapters.AuditLogTableAdapter


        ' Create a new connection for the table adapters to use.
        Dim SqlCon As New SqlClient.SqlConnection(ConnectionString)
        SqlAdapter.Connection = SqlCon
        MediaCategoryAdapter.Connection = SqlCon
        AuditAdapter.Connection = SqlCon


        ' Check if this is a new row being added.
        If Row.RowState = DataRowState.Detached Then
            ' This is a new row being added. Clear the audit log and create only one entry for the new row.
            AuditLog.Clear()
            AddLog("CategoryName")
        Else
            ' This row was edited. Update audit log with child row addition details.
            For Each Grid As DataGridView In GridsToAudit
                ' Check each row in this grid.
                For Each GridRow As DataGridViewRow In Grid.Rows
                    ' Get the datarow feeding this grid row.
                    Dim Row As DataRow = CType(GridRow.DataBoundItem, DataRowView).Row
                    If Row.RowState = DataRowState.Added Then
                        ' This row is newly added. Add an entry for it into the audit log.
                        Dim ActionText As String = BuildChildRowActionText(Grid, GridRow, ChildRowAction.Added)
                        LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, Table.TableName, CategoryName, ActionText)
                    End If
                    If Row.RowState = DataRowState.Modified Then
                        ' This row has been modified. Add an entry for it into the audit log.
                        Dim ActionText As String = BuildChildRowActionText(Grid, GridRow, ChildRowAction.Modified)
                        LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, Table.TableName, CategoryName, ActionText)
                    End If
                Next
            Next
        End If

        ' Perform the save operation.
        Try
            ' Remember the current item in the binding source list.
            Dim CurrentItem As Object = DataBindingSource.Current
            ' Commit changes to the dataset.
            DataBindingSource.EndEdit()     ' This line changes the binding source's current item.
            ' Restore the current item in the binding source list.
            DataBindingSource.Position = DataBindingSource.IndexOf(CurrentItem)
            ' Update the database.
            SqlAdapter.Update(Row)
            MediaCategoryAdapter.Update(DataSet.Tables("MediaCategory"))
            IsDirty = False
            AuditAdapter.Update(AuditLog)
        Finally
            ' Dispose all data adapters.
            SqlAdapter.Dispose()
            MediaCategoryAdapter.Dispose()
            AuditAdapter.Dispose()
            ' Dispose the connection.
            SqlCon.Close()
        End Try

    End Sub

    Public Sub AddMediaCategory _
    (ByVal ConsumingSubform As LiquidShell.Subform,
    ByVal ConnectionString As String,
    ByVal SelectedItems As List(Of DataRow))
        ' Add media services to the list of allowed media services for this category.

        ' Check for legal selection by user.
        If IsNothing(SelectedItems) Then
            Exit Sub
        Else
            If SelectedItems.Count = 0 Then
                Exit Sub
            End If
        End If

        ' Add a new row to the table for each item that was selected.
        For Each SelectedItem As DataRow In SelectedItems

            ' Create a new row for the table.
            Dim NewRow As DataRow = DataSet.Tables("MediaCategory").NewRow
            NewRow("CategoryID") = Row("CategoryID")
            NewRow("MediaID") = SelectedItem("MediaID")
            DataSet.Tables("MediaCategory").Rows.Add(NewRow)

            ' Flag the object as dirty.
            IsDirty = True

        Next

    End Sub

#End Region

#Region "Private Methods"

    Protected Overrides Sub UpdateProperties()

        ' Update properties to match the current Row.
        UpdateCustomProperties()

    End Sub

    Private Sub UpdateCustomProperties()
        _MediaCategoryBindingSource = New BindingSource(DataBindingSource, "FK_MediaCategory_Category")
    End Sub

#End Region

End Class
