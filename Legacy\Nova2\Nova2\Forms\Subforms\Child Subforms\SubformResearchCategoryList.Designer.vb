﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformResearchCategoryList
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformResearchCategoryList))
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle4 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim SuperToolTip1 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem1 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem1 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip2 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem2 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem2 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Me.ImageList16x16 = New System.Windows.Forms.ImageList(Me.components)
        Me.GroupItems = New DevExpress.XtraEditors.GroupControl()
        Me.ButtonDelete = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAdd = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonEdit = New DevExpress.XtraEditors.SimpleButton()
        Me.Grid = New System.Windows.Forms.DataGridView()
        Me.LabelSearch = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearch = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearch = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearch = New DevExpress.XtraEditors.PictureEdit()
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonBack = New DevExpress.XtraEditors.SimpleButton()
        Me.CategoryNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.FirstMonthColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.LastMonthColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.MonthsColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.NetFeeColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        CType(Me.GroupItems, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupItems.SuspendLayout()
        CType(Me.Grid, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!)
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Size = New System.Drawing.Size(496, 30)
        Me.LabelTitle.TabIndex = 0
        Me.LabelTitle.Text = "Contract AV105263 - Research Categories"
        '
        'ImageList16x16
        '
        Me.ImageList16x16.ImageStream = CType(resources.GetObject("ImageList16x16.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList16x16.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList16x16.Images.SetKeyName(0, "delete.png")
        Me.ImageList16x16.Images.SetKeyName(1, "add.png")
        Me.ImageList16x16.Images.SetKeyName(2, "accept.png")
        Me.ImageList16x16.Images.SetKeyName(3, "process.png")
        Me.ImageList16x16.Images.SetKeyName(4, "download.png")
        Me.ImageList16x16.Images.SetKeyName(5, "refresh.png")
        Me.ImageList16x16.Images.SetKeyName(6, "pencil.png")
        '
        'GroupItems
        '
        Me.GroupItems.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupItems.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupItems.Appearance.Options.UseFont = True
        Me.GroupItems.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupItems.AppearanceCaption.ForeColor = System.Drawing.Color.DimGray
        Me.GroupItems.AppearanceCaption.Options.UseFont = True
        Me.GroupItems.AppearanceCaption.Options.UseForeColor = True
        Me.GroupItems.Controls.Add(Me.ButtonDelete)
        Me.GroupItems.Controls.Add(Me.ButtonAdd)
        Me.GroupItems.Controls.Add(Me.ButtonEdit)
        Me.GroupItems.Controls.Add(Me.Grid)
        Me.GroupItems.Controls.Add(Me.LabelSearch)
        Me.GroupItems.Controls.Add(Me.TextEditSearch)
        Me.GroupItems.Controls.Add(Me.PictureClearSearch)
        Me.GroupItems.Controls.Add(Me.PictureAdvancedSearch)
        Me.GroupItems.Location = New System.Drawing.Point(12, 57)
        Me.GroupItems.LookAndFeel.SkinName = "Black"
        Me.GroupItems.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupItems.Name = "GroupItems"
        Me.GroupItems.Size = New System.Drawing.Size(799, 437)
        Me.GroupItems.TabIndex = 1
        Me.GroupItems.Text = "Research Category List"
        '
        'ButtonDelete
        '
        Me.ButtonDelete.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonDelete.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonDelete.Appearance.Options.UseFont = True
        Me.ButtonDelete.ImageIndex = 0
        Me.ButtonDelete.ImageList = Me.ImageList16x16
        Me.ButtonDelete.Location = New System.Drawing.Point(167, 409)
        Me.ButtonDelete.LookAndFeel.SkinName = "Black"
        Me.ButtonDelete.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonDelete.Margin = New System.Windows.Forms.Padding(3, 3, 15, 3)
        Me.ButtonDelete.Name = "ButtonDelete"
        Me.ButtonDelete.Size = New System.Drawing.Size(75, 23)
        Me.ButtonDelete.TabIndex = 4
        Me.ButtonDelete.Text = "Delete"
        '
        'ButtonAdd
        '
        Me.ButtonAdd.AccessibleRole = System.Windows.Forms.AccessibleRole.None
        Me.ButtonAdd.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAdd.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAdd.Appearance.Options.UseFont = True
        Me.ButtonAdd.ImageIndex = 1
        Me.ButtonAdd.ImageList = Me.ImageList16x16
        Me.ButtonAdd.Location = New System.Drawing.Point(5, 409)
        Me.ButtonAdd.LookAndFeel.SkinName = "Black"
        Me.ButtonAdd.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAdd.Name = "ButtonAdd"
        Me.ButtonAdd.Size = New System.Drawing.Size(75, 23)
        Me.ButtonAdd.TabIndex = 2
        Me.ButtonAdd.Text = "Add"
        '
        'ButtonEdit
        '
        Me.ButtonEdit.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonEdit.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonEdit.Appearance.Options.UseFont = True
        Me.ButtonEdit.ImageIndex = 6
        Me.ButtonEdit.ImageList = Me.ImageList16x16
        Me.ButtonEdit.Location = New System.Drawing.Point(86, 409)
        Me.ButtonEdit.LookAndFeel.SkinName = "Black"
        Me.ButtonEdit.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonEdit.Name = "ButtonEdit"
        Me.ButtonEdit.Size = New System.Drawing.Size(75, 23)
        Me.ButtonEdit.TabIndex = 3
        Me.ButtonEdit.Text = "Edit"
        '
        'Grid
        '
        Me.Grid.AllowUserToAddRows = False
        Me.Grid.AllowUserToDeleteRows = False
        Me.Grid.AllowUserToOrderColumns = True
        Me.Grid.AllowUserToResizeRows = False
        DataGridViewCellStyle1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Grid.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle1
        Me.Grid.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Grid.BackgroundColor = System.Drawing.Color.White
        Me.Grid.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.Grid.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.Grid.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle2.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle2.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.Grid.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle2
        Me.Grid.ColumnHeadersHeight = 22
        Me.Grid.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.Grid.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.CategoryNameColumn, Me.FirstMonthColumn, Me.LastMonthColumn, Me.MonthsColumn, Me.NetFeeColumn})
        Me.Grid.EnableHeadersVisualStyles = False
        Me.Grid.GridColor = System.Drawing.Color.White
        Me.Grid.Location = New System.Drawing.Point(2, 22)
        Me.Grid.Margin = New System.Windows.Forms.Padding(0, 0, 0, 3)
        Me.Grid.Name = "Grid"
        Me.Grid.ReadOnly = True
        Me.Grid.RowHeadersVisible = False
        DataGridViewCellStyle4.ForeColor = System.Drawing.Color.DimGray
        Me.Grid.RowsDefaultCellStyle = DataGridViewCellStyle4
        Me.Grid.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.Grid.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.Grid.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.Grid.RowTemplate.Height = 19
        Me.Grid.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.Grid.ShowCellToolTips = False
        Me.Grid.Size = New System.Drawing.Size(795, 381)
        Me.Grid.StandardTab = True
        Me.Grid.TabIndex = 1
        '
        'LabelSearch
        '
        Me.LabelSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelSearch.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSearch.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelSearch.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelSearch.Location = New System.Drawing.Point(641, 413)
        Me.LabelSearch.Name = "LabelSearch"
        Me.LabelSearch.Size = New System.Drawing.Size(45, 13)
        Me.LabelSearch.TabIndex = 5
        Me.LabelSearch.Text = "Search:"
        '
        'TextEditSearch
        '
        Me.TextEditSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearch.EditValue = ""
        Me.TextEditSearch.Location = New System.Drawing.Point(692, 410)
        Me.TextEditSearch.Margin = New System.Windows.Forms.Padding(3, 4, 3, 5)
        Me.TextEditSearch.Name = "TextEditSearch"
        Me.TextEditSearch.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearch.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearch.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearch.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearch.Size = New System.Drawing.Size(80, 20)
        Me.TextEditSearch.TabIndex = 6
        '
        'PictureClearSearch
        '
        Me.PictureClearSearch.AllowDrop = True
        Me.PictureClearSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearch.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearch.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearch.Location = New System.Drawing.Point(778, 3)
        Me.PictureClearSearch.Name = "PictureClearSearch"
        Me.PictureClearSearch.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearch.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearch.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearch.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem1.Text = "Clear Search"
        ToolTipItem1.LeftIndent = 6
        ToolTipItem1.Text = "Click here to clear all search boxes."
        SuperToolTip1.Items.Add(ToolTipTitleItem1)
        SuperToolTip1.Items.Add(ToolTipItem1)
        Me.PictureClearSearch.SuperTip = SuperToolTip1
        Me.PictureClearSearch.TabIndex = 0
        Me.PictureClearSearch.TabStop = True
        '
        'PictureAdvancedSearch
        '
        Me.PictureAdvancedSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearch.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearch.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearch.Location = New System.Drawing.Point(778, 412)
        Me.PictureAdvancedSearch.Margin = New System.Windows.Forms.Padding(3, 6, 3, 7)
        Me.PictureAdvancedSearch.Name = "PictureAdvancedSearch"
        Me.PictureAdvancedSearch.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearch.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearch.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearch.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem2.Text = "Advanced Search"
        ToolTipItem2.LeftIndent = 6
        ToolTipItem2.Text = "Click here to search individual column values."
        SuperToolTip2.Items.Add(ToolTipTitleItem2)
        SuperToolTip2.Items.Add(ToolTipItem2)
        Me.PictureAdvancedSearch.SuperTip = SuperToolTip2
        Me.PictureAdvancedSearch.TabIndex = 7
        Me.PictureAdvancedSearch.TabStop = True
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "back.png")
        '
        'ButtonBack
        '
        Me.ButtonBack.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonBack.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonBack.Appearance.Options.UseFont = True
        Me.ButtonBack.ImageIndex = 0
        Me.ButtonBack.ImageList = Me.ImageList24x24
        Me.ButtonBack.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonBack.Location = New System.Drawing.Point(711, 509)
        Me.ButtonBack.LookAndFeel.SkinName = "Black"
        Me.ButtonBack.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonBack.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonBack.Name = "ButtonBack"
        Me.ButtonBack.Size = New System.Drawing.Size(100, 28)
        Me.ButtonBack.TabIndex = 2
        Me.ButtonBack.Text = "Back"
        '
        'CategoryNameColumn
        '
        Me.CategoryNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.CategoryNameColumn.DataPropertyName = "CategoryName"
        Me.CategoryNameColumn.HeaderText = "Category"
        Me.CategoryNameColumn.Name = "CategoryNameColumn"
        Me.CategoryNameColumn.ReadOnly = True
        '
        'FirstMonthColumn
        '
        Me.FirstMonthColumn.DataPropertyName = "FirstMonth"
        Me.FirstMonthColumn.HeaderText = "First Month"
        Me.FirstMonthColumn.Name = "FirstMonthColumn"
        Me.FirstMonthColumn.ReadOnly = True
        Me.FirstMonthColumn.Width = 130
        '
        'LastMonthColumn
        '
        Me.LastMonthColumn.DataPropertyName = "LastMonth"
        Me.LastMonthColumn.HeaderText = "LastMonth"
        Me.LastMonthColumn.Name = "LastMonthColumn"
        Me.LastMonthColumn.ReadOnly = True
        Me.LastMonthColumn.Width = 130
        '
        'MonthsColumn
        '
        Me.MonthsColumn.DataPropertyName = "Months"
        Me.MonthsColumn.HeaderText = "Months"
        Me.MonthsColumn.Name = "MonthsColumn"
        Me.MonthsColumn.ReadOnly = True
        Me.MonthsColumn.Width = 70
        '
        'NetFeeColumn
        '
        Me.NetFeeColumn.DataPropertyName = "NetFee"
        DataGridViewCellStyle3.Format = "C2"
        DataGridViewCellStyle3.NullValue = Nothing
        Me.NetFeeColumn.DefaultCellStyle = DataGridViewCellStyle3
        Me.NetFeeColumn.HeaderText = "Net Fee"
        Me.NetFeeColumn.Name = "NetFeeColumn"
        Me.NetFeeColumn.ReadOnly = True
        Me.NetFeeColumn.Width = 140
        '
        'SubformResearchCategoryList
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.Controls.Add(Me.ButtonBack)
        Me.Controls.Add(Me.GroupItems)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Name = "SubformResearchCategoryList"
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        Me.Controls.SetChildIndex(Me.GroupItems, 0)
        Me.Controls.SetChildIndex(Me.ButtonBack, 0)
        CType(Me.GroupItems, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupItems.ResumeLayout(False)
        CType(Me.Grid, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents ImageList16x16 As System.Windows.Forms.ImageList
    Friend WithEvents GroupItems As DevExpress.XtraEditors.GroupControl
    Friend WithEvents ButtonDelete As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonAdd As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonEdit As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents Grid As System.Windows.Forms.DataGridView
    Friend WithEvents LabelSearch As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditSearch As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearch As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearch As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents ButtonBack As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents CategoryNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents FirstMonthColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents LastMonthColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents MonthsColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents NetFeeColumn As System.Windows.Forms.DataGridViewTextBoxColumn

End Class
