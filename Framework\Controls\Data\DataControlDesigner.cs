﻿using System.Collections;
using System.Windows.Forms.Design;
using System.Windows.Forms.Design.Behavior;

namespace Framework.Controls.Data
{
    class DataControlDesigner : ControlDesigner
    {
        public override IList SnapLines
        {
            get
            {
                IList snaplines = base.SnapLines; //get old snaplines and then add new ones
                snaplines.Add(new SnapLine(SnapLineType.Baseline, 15));
                return snaplines;
            }
        }
    }
}
