<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class DateSelector
    Inherits LiquidShell.EditorForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(DateSelector))
        Me.Calendar = New DevExpress.XtraScheduler.DateNavigator
        Me.ButtonSelect = New DevExpress.XtraEditors.SimpleButton
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelButtonBar.SuspendLayout()
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Calendar, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'PanelButtonBar
        '
        Me.PanelButtonBar.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelButtonBar.Appearance.BackColor2 = System.Drawing.Color.LightSteelBlue
        Me.PanelButtonBar.Appearance.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical
        Me.PanelButtonBar.Appearance.Options.UseBackColor = True
        Me.PanelButtonBar.Controls.Add(Me.ButtonSelect)
        Me.PanelButtonBar.Controls.Add(Me.ButtonCancel)
        Me.PanelButtonBar.Location = New System.Drawing.Point(0, 577)
        Me.PanelButtonBar.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Office2003
        Me.PanelButtonBar.LookAndFeel.UseDefaultLookAndFeel = False
        Me.PanelButtonBar.Size = New System.Drawing.Size(587, 52)
        '
        'Calendar
        '
        Me.Calendar.AppearanceCalendar.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Calendar.AppearanceCalendar.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Calendar.AppearanceCalendar.ForeColor = System.Drawing.Color.DimGray
        Me.Calendar.AppearanceCalendar.Options.UseBackColor = True
        Me.Calendar.AppearanceCalendar.Options.UseFont = True
        Me.Calendar.AppearanceCalendar.Options.UseForeColor = True
        Me.Calendar.AppearanceHeader.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Calendar.AppearanceHeader.ForeColor = System.Drawing.Color.DimGray
        Me.Calendar.AppearanceHeader.Options.UseFont = True
        Me.Calendar.AppearanceHeader.Options.UseForeColor = True
        Me.Calendar.DateTime = New Date(2010, 11, 11, 0, 0, 0, 0)
        Me.Calendar.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Calendar.HotDate = Nothing
        Me.Calendar.Location = New System.Drawing.Point(0, 0)
        Me.Calendar.Name = "Calendar"
        Me.Calendar.Size = New System.Drawing.Size(587, 577)
        Me.Calendar.TabIndex = 0
        '
        'ButtonSelect
        '
        Me.ButtonSelect.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonSelect.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonSelect.Appearance.Options.UseFont = True
        Me.ButtonSelect.ImageIndex = 1
        Me.ButtonSelect.ImageList = Me.ImageList24x24
        Me.ButtonSelect.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonSelect.Location = New System.Drawing.Point(369, 12)
        Me.ButtonSelect.LookAndFeel.SkinName = "Black"
        Me.ButtonSelect.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonSelect.Name = "ButtonSelect"
        Me.ButtonSelect.Size = New System.Drawing.Size(100, 28)
        Me.ButtonSelect.TabIndex = 5
        Me.ButtonSelect.Text = "Select"
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(475, 12)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(100, 28)
        Me.ButtonCancel.TabIndex = 6
        Me.ButtonCancel.Text = "Cancel"
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "accept.png")
        '
        'DateSelector
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.ClientSize = New System.Drawing.Size(587, 629)
        Me.ControlBox = False
        Me.Controls.Add(Me.Calendar)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.MaximizeBox = False
        Me.MaximumSize = New System.Drawing.Size(603, 667)
        Me.MinimizeBox = False
        Me.MinimumSize = New System.Drawing.Size(603, 667)
        Me.Name = "DateSelector"
        Me.Text = "Date Selector"
        Me.Controls.SetChildIndex(Me.PanelButtonBar, 0)
        Me.Controls.SetChildIndex(Me.Calendar, 0)
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelButtonBar.ResumeLayout(False)
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Calendar, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents Calendar As DevExpress.XtraScheduler.DateNavigator
    Friend WithEvents ButtonSelect As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList

End Class
