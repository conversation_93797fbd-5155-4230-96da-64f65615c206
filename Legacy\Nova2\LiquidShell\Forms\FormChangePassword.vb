﻿Public Class FormChangePassword

    Private ConnectionString As String
    Public NewPassword As String

    Public Shared Sub ChangePassword(ByRef ConString As String, ByVal AppIcon As Icon, AutoInsertOldPassword As Boolean)

        ' Create an instance of the form.
        Dim ChangePasswordForm As New FormChangePassword(ConString, AppIcon, AutoInsertOldPassword)

        ' Check the result.
        ChangePasswordForm.ShowDialog()
        If ChangePasswordForm.DialogResult = Windows.Forms.DialogResult.OK Then
            ' Password was successfully changed. Update the connection string.
            Dim ConStringBuilder As New SqlClient.SqlConnectionStringBuilder(ConString)
            ConStringBuilder.Password = ChangePasswordForm.NewPassword
            ConString = ConStringBuilder.ToString
            ' Inform the user of the good news.
            ChangePasswordForm.ShowMessage("Password successfully changed." & vbCrLf & "Go in peace.")
        End If

        ' Dispose of the form.
        ChangePasswordForm.Dispose()

    End Sub

    Public Sub New(ByVal ConString As String, ByVal AppIcon As Icon, AutoInsertOldPassword As Boolean)
        ' This call is required by the Windows Form Designer.
        InitializeComponent()
        ' Add any initialization after the InitializeComponent() call.
        ConnectionString = ConString
        Icon = AppIcon
        If AutoInsertOldPassword Then
            Dim ConStringBuilder As New SqlClient.SqlConnectionStringBuilder(ConString)
            TextEditCurrentPassword.EditValue = ConStringBuilder.Password
        End If
    End Sub

    Private Sub FormPasswordChange_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        LabelControlHeadingTasks.Text = "Please enter a new password for account '" & My.User.Name & "'"
    End Sub

    Protected Overrides Sub Save()

        ' Build the Sql command.
        NewPassword = TextEditNewPassword.Text
        Dim SqlCmdText As String = "ALTER LOGIN " & My.User.Name _
        & " WITH PASSWORD = '" & NewPassword _
        & "' OLD_PASSWORD = '" & TextEditCurrentPassword.Text & "'"
        Dim SqlConnection As New SqlClient.SqlConnection(ConnectionString)
        Dim SqlCmd As New SqlClient.SqlCommand(SqlCmdText, SqlConnection)

        ' Attempt execution of the command.
        Try
            SqlConnection.Open()
            SqlCmd.ExecuteNonQuery()
        Finally
            SqlConnection.Close()
        End Try

    End Sub

    Private Sub TextEditCurrentPassword_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles TextEditCurrentPassword.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Validate the supplied password using the current connection string.
        Dim ConStringBuilder As New SqlClient.SqlConnectionStringBuilder(ConnectionString)
        If Not String.Compare(ValidatedControl.Text, ConStringBuilder.Password, False) = 0 Then
            ' Validation failed.
            LiquidAgent.ControlValidation(ValidatedControl, "Your current password wasn't entered correctly.")
        End If

    End Sub

    Private Sub TextEditNewPassword_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
     Handles TextEditNewPassword.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If ValidatedControl.Text.Length = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "New passwords may not be blank.")
        ElseIf ValidatedControl.Text.IndexOf(" ") > -1 Then
            LiquidAgent.ControlValidation(ValidatedControl, "New passwords may not contain spaces.")
        End If

    End Sub

    Private Sub TextEditNewPasswordAgain_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles TextEditNewPasswordAgain.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If Not String.Compare(ValidatedControl.Text, TextEditNewPassword.Text, False) = 0 Then
            LiquidAgent.ControlValidation _
            (ValidatedControl, "Passwords don't match. Please re-type the new password accurately.")
        End If

    End Sub

    Private Sub TextEditPassword_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles TextEditCurrentPassword.TextChanged, TextEditNewPassword.TextChanged, TextEditNewPasswordAgain.TextChanged
        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)
        ' Reset the error status of the control.
        LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
    End Sub

    Private Sub ButtonOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonOK.Click
        SaveAndClose()
    End Sub

    Private Sub ButtonCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonCancel.Click
        Close()
    End Sub

    Private Sub FormChangePassword_Shown(sender As Object, e As EventArgs) Handles Me.Shown
        If TextEditCurrentPassword.Text.Length > 0 Then
            TextEditNewPassword.Focus()
        End If
    End Sub

End Class