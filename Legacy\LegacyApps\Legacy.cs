﻿using LiquidShell;
using System.Data.SqlClient;

namespace LegacyApps
{
    public static class Legacy
    {
        public static void LaunchApp(string appname, string authenticatedconnectionstring)
        {
            // Update the connection string with the correct database name.
            SqlConnectionStringBuilder stringbuilder = new SqlConnectionStringBuilder(authenticatedconnectionstring);
            stringbuilder.ConnectTimeout = 90;
            stringbuilder.InitialCatalog = "NovaDB";
            authenticatedconnectionstring = stringbuilder.ToString();

            if (appname == "Nova2")
            {
                BaseForm startupform = new Nova2.CommandCentre(authenticatedconnectionstring);
                LegacyApp legacyapp = new LegacyApp(startupform);
                legacyapp.Launch();
            }

            if (appname == "Nova2Ops")
            {
                BaseForm startupform = new Nova2Ops.CommandCentre(authenticatedconnectionstring);
                LegacyApp legacyapp = new LegacyApp(startupform);
                legacyapp.Launch();
            }

            if (appname == "Nova2Finance")
            {
                BaseForm startupform = new NovaFinance.CommandCentre(authenticatedconnectionstring);
                LegacyApp legacyapp = new LegacyApp(startupform);
                legacyapp.Launch();
            }
        }

    }
}
