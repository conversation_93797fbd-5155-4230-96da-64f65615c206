﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="12.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.50727</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{683D8B67-D21D-483B-9731-CB2DC28C3C71}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <StartupObject>Nova2.My.MyApplication</StartupObject>
    <RootNamespace>Nova2</RootNamespace>
    <AssemblyName>Nova2</AssemblyName>
    <MyType>WindowsForms</MyType>
    <ApplicationIcon>DatabaseIcon.ico</ApplicationIcon>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <ManifestCertificateThumbprint>3B9CACAD26DB9439D8A6C93F43652529502A07EA</ManifestCertificateThumbprint>
    <ManifestKeyFile>Nova2_TemporaryKey.pfx</ManifestKeyFile>
    <GenerateManifests>true</GenerateManifests>
    <SignManifests>false</SignManifests>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <OldToolsVersion>2.0</OldToolsVersion>
    <UpgradeBackupLocation />
    <TargetFrameworkProfile />
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <PublishUrl>\\************\Nova\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Unc</InstallFrom>
    <UpdateEnabled>true</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <InstallUrl>\\************\Nova\</InstallUrl>
    <UpdateUrl>http://localhost/Nova2/</UpdateUrl>
    <ProductName>Nova2</ProductName>
    <PublisherName>Primedia Instore</PublisherName>
    <OpenBrowserOnPublish>false</OpenBrowserOnPublish>
    <AutorunEnabled>true</AutorunEnabled>
    <ApplicationRevision>46</ApplicationRevision>
    <ApplicationVersion>********</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <CreateDesktopShortcut>true</CreateDesktopShortcut>
    <PublishWizardCompleted>true</PublishWizardCompleted>
    <BootstrapperEnabled>false</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>Nova2.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <PlatformTarget>AnyCPU</PlatformTarget>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>Nova2.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationManifest>My Project\app.manifest</ApplicationManifest>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>Off</OptionExplicit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevExpress.Data.v12.2, Version=12.2.18.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\..\..\..\..\..\..\..\..\Devexpress\Framework\DevExpress.Data.v12.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Utils.v12.2, Version=12.2.18.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\..\..\..\..\..\..\..\..\Devexpress\Framework\DevExpress.Utils.v12.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Xpf.Scheduler.v12.2">
      <HintPath>..\..\..\..\..\..\..\..\..\Devexpress\Framework\DevExpress.Xpf.Scheduler.v12.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraEditors.v12.2, Version=12.2.18.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\..\..\..\..\..\..\..\..\Devexpress\Framework\DevExpress.XtraEditors.v12.2.dll</HintPath>
    </Reference>
    <Reference Include="LegacyApps">
      <HintPath>..\..\LegacyApps\obj\Debug\LegacyApps.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="DevExpress.XtraEditors" />
    <Import Include="LiquidShell" />
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="NovaData" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Windows.Forms" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ApplicationEvents.vb" />
    <Compile Include="Forms\CommandCentre.designer.vb">
      <DependentUpon>CommandCentre.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\CommandCentre.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Contract\FormAuditLog.Designer.vb">
      <DependentUpon>FormAuditLog.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Contract\FormAuditLog.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Contract\FormContractPrintPrompt.Designer.vb">
      <DependentUpon>FormContractPrintPrompt.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Contract\FormContractPrintPrompt.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Contract\FormContractSearchResults.Designer.vb">
      <DependentUpon>FormContractSearchResults.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Contract\FormContractSearchResults.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Contract\FormEditStoreList.Designer.vb">
      <DependentUpon>FormEditStoreList.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Contract\FormEditStoreList.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Contract\FormNewContract.Designer.vb">
      <DependentUpon>FormNewContract.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Contract\FormNewContract.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormAccountManagerUserPermissions.Designer.vb">
      <DependentUpon>FormAccountManagerUserPermissions.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormAccountManagerUserPermissions.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormAccountOptions.Designer.vb">
      <DependentUpon>FormAccountOptions.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormAccountOptions.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormBrandAccountManager.Designer.vb">
      <DependentUpon>FormBrandAccountManager.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormBrandAccountManager.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormClientAccountManager.Designer.vb">
      <DependentUpon>FormClientAccountManager.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormClientAccountManager.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormSettings.Designer.vb">
      <DependentUpon>FormSettings.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormSettings.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Lookup\LookupBrand.Designer.vb">
      <DependentUpon>LookupBrand.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Lookup\LookupBrand.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Lookup\LookupBrandFamily.Designer.vb">
      <DependentUpon>LookupBrandFamily.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Lookup\LookupBrandFamily.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Lookup\LookupBurst.Designer.vb">
      <DependentUpon>LookupBurst.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Lookup\LookupBurst.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Lookup\LookupCategory.Designer.vb">
      <DependentUpon>LookupCategory.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Lookup\LookupCategory.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Lookup\LookupChain.Designer.vb">
      <DependentUpon>LookupChain.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Lookup\LookupChain.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Lookup\LookupPcaStatus.Designer.vb">
      <DependentUpon>LookupPcaStatus.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Lookup\LookupPcaStatus.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Lookup\LookupCity.Designer.vb">
      <DependentUpon>LookupCity.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Lookup\LookupCity.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Lookup\LookupClassification.Designer.vb">
      <DependentUpon>LookupClassification.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Lookup\LookupClassification.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Lookup\LookupInstallationDates.designer.vb">
      <DependentUpon>LookupInstallationDates.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Lookup\LookupInstallationDates.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Lookup\LookupMediaGroup.Designer.vb">
      <DependentUpon>LookupMediaGroup.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Lookup\LookupMediaGroup.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Lookup\LookupProposalHeat.designer.vb">
      <DependentUpon>LookupProposalHeat.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Lookup\LookupProposalHeat.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Lookup\LookupContractClassification.designer.vb">
      <DependentUpon>LookupContractClassification.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Lookup\LookupContractClassification.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Lookup\LookupClient.designer.vb">
      <DependentUpon>LookupClient.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Lookup\LookupClient.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Lookup\LookupAccountManager.designer.vb">
      <DependentUpon>LookupAccountManager.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Lookup\LookupAccountManager.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Lookup\LookupFiscal.Designer.vb">
      <DependentUpon>LookupFiscal.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Lookup\LookupFiscal.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Lookup\LookupInventory.Designer.vb">
      <DependentUpon>LookupInventory.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Lookup\LookupInventory.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Lookup\LookupInventoryQty.Designer.vb">
      <DependentUpon>LookupInventoryQty.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Lookup\LookupInventoryQty.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Lookup\LookupLoadingFee.Designer.vb">
      <DependentUpon>LookupLoadingFee.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Lookup\LookupLoadingFee.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Lookup\LookupMedia.Designer.vb">
      <DependentUpon>LookupMedia.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Lookup\LookupMedia.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Lookup\LookupMediaFamily.Designer.vb">
      <DependentUpon>LookupMediaFamily.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Lookup\LookupMediaFamily.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Lookup\LookupMiscellaneousCharge.Designer.vb">
      <DependentUpon>LookupMiscellaneousCharge.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Lookup\LookupMiscellaneousCharge.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Lookup\LookupIndependentStoreList.Designer.vb">
      <DependentUpon>LookupIndependentStoreList.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Lookup\LookupIndependentStoreList.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Lookup\LookupStoreByChain.designer.vb">
      <DependentUpon>LookupStoreByChain.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Lookup\LookupStoreByChain.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Lookup\LookupStores.Designer.vb">
      <DependentUpon>LookupStores.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Lookup\LookupStores.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Lookup\LookupTerms.Designer.vb">
      <DependentUpon>LookupTerms.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Lookup\LookupTerms.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Lookup\LookupUser.Designer.vb">
      <DependentUpon>LookupUser.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Lookup\LookupUser.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Media Gap\FormEditProvisionalBooking.Designer.vb">
      <DependentUpon>FormEditProvisionalBooking.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Media Gap\FormEditProvisionalBooking.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Media Gap\FormMediaGapContractInfo.Designer.vb">
      <DependentUpon>FormMediaGapContractInfo.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Media Gap\FormMediaGapContractInfo.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Media Gap\FormMediaGapSelectedCategories.Designer.vb">
      <DependentUpon>FormMediaGapSelectedCategories.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Media Gap\FormMediaGapSelectedCategories.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Media Gap\FormMediaGapSelectedChains.Designer.vb">
      <DependentUpon>FormMediaGapSelectedChains.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Media Gap\FormMediaGapSelectedChains.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Media Gap\FormMediaGapSelectedMediaFamilies.Designer.vb">
      <DependentUpon>FormMediaGapSelectedMediaFamilies.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Media Gap\FormMediaGapSelectedMediaFamilies.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformBrandCategoryManager.Designer.vb">
      <DependentUpon>SubformBrandCategoryManager.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformBrandCategoryManager.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformBrandCategory.Designer.vb">
      <DependentUpon>SubformBrandCategory.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformBrandCategory.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformCostEstimate.Designer.vb">
      <DependentUpon>SubformCostEstimate.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformCostEstimate.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformInvoiceNumber.Designer.vb">
      <DependentUpon>SubformInvoiceNumber.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformInvoiceNumber.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformMediaChannelGroup.Designer.vb">
      <DependentUpon>SubformMediaChannelGroup.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformMediaChannelGroup.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubFormMediaGroup.Designer.vb">
      <DependentUpon>SubFormMediaGroup.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubFormMediaGroup.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubFormMediaCost.Designer.vb">
      <DependentUpon>SubFormMediaCost.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubFormMediaCost.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformContractMediaCost.Designer.vb">
      <DependentUpon>SubformContractMediaCost.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformContractMediaCost.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformReplacementPrompt.Designer.vb">
      <DependentUpon>SubformReplacementPrompt.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformReplacementPrompt.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformAccountManager.Designer.vb">
      <DependentUpon>SubformAccountManager.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformAccountManager.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformAccountManagerBudget.Designer.vb">
      <DependentUpon>SubformAccountManagerBudget.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformAccountManagerBudget.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformBillingInstructions.Designer.vb">
      <DependentUpon>SubformBillingInstructions.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformBillingInstructions.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformBrand.Designer.vb">
      <DependentUpon>SubformBrand.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformBrand.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformBrandFamily.Designer.vb">
      <DependentUpon>SubformBrandFamily.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformBrandFamily.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformBrandFamilyManager.Designer.vb">
      <DependentUpon>SubformBrandFamilyManager.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformBrandFamilyManager.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformBrandManager.Designer.vb">
      <DependentUpon>SubformBrandManager.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformBrandManager.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformBurst.Designer.vb">
      <DependentUpon>SubformBurst.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformBurst.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformCategory.Designer.vb">
      <DependentUpon>SubformCategory.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformCategory.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformClient.Designer.vb">
      <DependentUpon>SubformClient.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformClient.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformContract.Designer.vb">
      <DependentUpon>SubformContract.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformContract.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformContractDate.Designer.vb">
      <DependentUpon>SubformContractDate.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformContractDate.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformStoreGroups.Designer.vb">
      <DependentUpon>SubformStoreGroups.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformStoreGroups.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformMedia.Designer.vb">
      <DependentUpon>SubformMedia.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformMedia.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformMediaLifeCycle.Designer.vb">
      <DependentUpon>SubformMediaLifeCycle.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformMediaLifeCycle.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformMiscellaneousCharge.Designer.vb">
      <DependentUpon>SubformMiscellaneousCharge.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformMiscellaneousCharge.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformProduction.Designer.vb">
      <DependentUpon>SubformProduction.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformProduction.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformProvisionalBooking.Designer.vb">
      <DependentUpon>SubformProvisionalBooking.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformProvisionalBooking.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformResearchCategory.Designer.vb">
      <DependentUpon>SubformResearchCategory.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformResearchCategory.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformResearchCategoryList.Designer.vb">
      <DependentUpon>SubformResearchCategoryList.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformResearchCategoryList.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Primary Subforms\SubformContractStart.Designer.vb">
      <DependentUpon>SubformContractStart.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Primary Subforms\SubformContractStart.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Primary Subforms\SubformCategoryManager.Designer.vb">
      <DependentUpon>SubformCategoryManager.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Primary Subforms\SubformCategoryManager.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformClientManager.Designer.vb">
      <DependentUpon>SubformClientManager.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformClientManager.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Primary Subforms\SubformClientsAndBrands.Designer.vb">
      <DependentUpon>SubformClientsAndBrands.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Primary Subforms\SubformClientsAndBrands.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformContractTasks.Designer.vb">
      <DependentUpon>SubformContractTasks.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Child Subforms\SubformContractTasks.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Primary Subforms\SubformStoreGroupManager.Designer.vb">
      <DependentUpon>SubformStoreGroupManager.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Primary Subforms\SubformStoreGroupManager.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Primary Subforms\SubformMediaGap.Designer.vb">
      <DependentUpon>SubformMediaGap.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Primary Subforms\SubformMediaGap.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Primary Subforms\SubformMediaManager.Designer.vb">
      <DependentUpon>SubformMediaManager.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Primary Subforms\SubformMediaManager.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Primary Subforms\SubformSalesTeam.Designer.vb">
      <DependentUpon>SubformSalesTeam.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Primary Subforms\SubformSalesTeam.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\Subforms\Primary Subforms\SubformWelcome.designer.vb">
      <DependentUpon>SubformWelcome.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\Subforms\Primary Subforms\SubformWelcome.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="Settings.vb" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Forms\CommandCentre.resx">
      <DependentUpon>CommandCentre.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Contract\FormAuditLog.resx">
      <DependentUpon>FormAuditLog.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Contract\FormContractPrintPrompt.resx">
      <SubType>Designer</SubType>
      <DependentUpon>FormContractPrintPrompt.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Contract\FormContractSearchResults.resx">
      <DependentUpon>FormContractSearchResults.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Contract\FormEditStoreList.resx">
      <SubType>Designer</SubType>
      <DependentUpon>FormEditStoreList.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Contract\FormNewContract.resx">
      <DependentUpon>FormNewContract.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormAccountManagerUserPermissions.resx">
      <SubType>Designer</SubType>
      <DependentUpon>FormAccountManagerUserPermissions.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormAccountOptions.resx">
      <DependentUpon>FormAccountOptions.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormBrandAccountManager.resx">
      <DependentUpon>FormBrandAccountManager.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormClientAccountManager.resx">
      <SubType>Designer</SubType>
      <DependentUpon>FormClientAccountManager.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormSettings.resx">
      <SubType>Designer</SubType>
      <DependentUpon>FormSettings.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Lookup\LookupBrand.resx">
      <SubType>Designer</SubType>
      <DependentUpon>LookupBrand.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Lookup\LookupBrandFamily.resx">
      <SubType>Designer</SubType>
      <DependentUpon>LookupBrandFamily.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Lookup\LookupBurst.resx">
      <SubType>Designer</SubType>
      <DependentUpon>LookupBurst.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Lookup\LookupCategory.resx">
      <SubType>Designer</SubType>
      <DependentUpon>LookupCategory.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Lookup\LookupChain.resx">
      <SubType>Designer</SubType>
      <DependentUpon>LookupChain.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Lookup\LookupPcaStatus.resx">
      <DependentUpon>LookupPcaStatus.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Lookup\LookupCity.resx">
      <SubType>Designer</SubType>
      <DependentUpon>LookupCity.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Lookup\LookupClassification.resx">
      <SubType>Designer</SubType>
      <DependentUpon>LookupClassification.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Lookup\LookupInstallationDates.resx">
      <DependentUpon>LookupInstallationDates.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Lookup\LookupMediaGroup.resx">
      <DependentUpon>LookupMediaGroup.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Lookup\LookupProposalHeat.resx">
      <DependentUpon>LookupProposalHeat.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Lookup\LookupContractClassification.resx">
      <DependentUpon>LookupContractClassification.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Lookup\LookupClient.resx">
      <DependentUpon>LookupClient.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Lookup\LookupAccountManager.resx">
      <DependentUpon>LookupAccountManager.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Lookup\LookupFiscal.resx">
      <SubType>Designer</SubType>
      <DependentUpon>LookupFiscal.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Lookup\LookupInventory.resx">
      <SubType>Designer</SubType>
      <DependentUpon>LookupInventory.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Lookup\LookupInventoryQty.resx">
      <SubType>Designer</SubType>
      <DependentUpon>LookupInventoryQty.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Lookup\LookupLoadingFee.resx">
      <SubType>Designer</SubType>
      <DependentUpon>LookupLoadingFee.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Lookup\LookupMedia.resx">
      <SubType>Designer</SubType>
      <DependentUpon>LookupMedia.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Lookup\LookupMediaFamily.resx">
      <SubType>Designer</SubType>
      <DependentUpon>LookupMediaFamily.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Lookup\LookupMiscellaneousCharge.resx">
      <SubType>Designer</SubType>
      <DependentUpon>LookupMiscellaneousCharge.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Lookup\LookupIndependentStoreList.resx">
      <DependentUpon>LookupIndependentStoreList.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Lookup\LookupStoreByChain.resx">
      <DependentUpon>LookupStoreByChain.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Lookup\LookupTerms.resx">
      <SubType>Designer</SubType>
      <DependentUpon>LookupTerms.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Lookup\LookupUser.resx">
      <SubType>Designer</SubType>
      <DependentUpon>LookupUser.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Media Gap\FormEditProvisionalBooking.resx">
      <SubType>Designer</SubType>
      <DependentUpon>FormEditProvisionalBooking.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Media Gap\FormMediaGapContractInfo.resx">
      <SubType>Designer</SubType>
      <DependentUpon>FormMediaGapContractInfo.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Media Gap\FormMediaGapSelectedCategories.resx">
      <SubType>Designer</SubType>
      <DependentUpon>FormMediaGapSelectedCategories.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Media Gap\FormMediaGapSelectedChains.resx">
      <SubType>Designer</SubType>
      <DependentUpon>FormMediaGapSelectedChains.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Media Gap\FormMediaGapSelectedMediaFamilies.resx">
      <SubType>Designer</SubType>
      <DependentUpon>FormMediaGapSelectedMediaFamilies.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformBrandCategoryManager.resx">
      <DependentUpon>SubformBrandCategoryManager.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformBrandCategory.resx">
      <DependentUpon>SubformBrandCategory.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformCostEstimate.resx">
      <DependentUpon>SubformCostEstimate.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformInvoiceNumber.resx">
      <DependentUpon>SubformInvoiceNumber.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformMediaChannelGroup.resx">
      <DependentUpon>SubformMediaChannelGroup.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubFormMediaGroup.resx">
      <DependentUpon>SubFormMediaGroup.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubFormMediaCost.resx">
      <DependentUpon>SubFormMediaCost.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformContractMediaCost.resx">
      <DependentUpon>SubformContractMediaCost.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformReplacementPrompt.resx">
      <DependentUpon>SubformReplacementPrompt.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformAccountManager.resx">
      <SubType>Designer</SubType>
      <DependentUpon>SubformAccountManager.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformAccountManagerBudget.resx">
      <SubType>Designer</SubType>
      <DependentUpon>SubformAccountManagerBudget.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformBillingInstructions.resx">
      <DependentUpon>SubformBillingInstructions.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformBrand.resx">
      <SubType>Designer</SubType>
      <DependentUpon>SubformBrand.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformBrandFamily.resx">
      <SubType>Designer</SubType>
      <DependentUpon>SubformBrandFamily.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformBrandFamilyManager.resx">
      <SubType>Designer</SubType>
      <DependentUpon>SubformBrandFamilyManager.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformBrandManager.resx">
      <SubType>Designer</SubType>
      <DependentUpon>SubformBrandManager.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformBurst.resx">
      <SubType>Designer</SubType>
      <DependentUpon>SubformBurst.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformCategory.resx">
      <SubType>Designer</SubType>
      <DependentUpon>SubformCategory.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformClient.resx">
      <SubType>Designer</SubType>
      <DependentUpon>SubformClient.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformContract.resx">
      <SubType>Designer</SubType>
      <DependentUpon>SubformContract.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformContractDate.resx">
      <DependentUpon>SubformContractDate.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformStoreGroups.resx">
      <DependentUpon>SubformStoreGroups.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformMedia.resx">
      <SubType>Designer</SubType>
      <DependentUpon>SubformMedia.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformMediaLifeCycle.resx">
      <SubType>Designer</SubType>
      <DependentUpon>SubformMediaLifeCycle.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformMiscellaneousCharge.resx">
      <SubType>Designer</SubType>
      <DependentUpon>SubformMiscellaneousCharge.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformProduction.resx">
      <SubType>Designer</SubType>
      <DependentUpon>SubformProduction.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformProvisionalBooking.resx">
      <SubType>Designer</SubType>
      <DependentUpon>SubformProvisionalBooking.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformResearchCategory.resx">
      <DependentUpon>SubformResearchCategory.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformResearchCategoryList.resx">
      <DependentUpon>SubformResearchCategoryList.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Primary Subforms\SubformContractStart.resx">
      <DependentUpon>SubformContractStart.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Primary Subforms\SubformCategoryManager.resx">
      <SubType>Designer</SubType>
      <DependentUpon>SubformCategoryManager.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformClientManager.resx">
      <SubType>Designer</SubType>
      <DependentUpon>SubformClientManager.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Primary Subforms\SubformClientsAndBrands.resx">
      <SubType>Designer</SubType>
      <DependentUpon>SubformClientsAndBrands.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Child Subforms\SubformContractTasks.resx">
      <DependentUpon>SubformContractTasks.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Primary Subforms\SubformStoreGroupManager.resx">
      <DependentUpon>SubformStoreGroupManager.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Primary Subforms\SubformMediaGap.resx">
      <SubType>Designer</SubType>
      <DependentUpon>SubformMediaGap.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Primary Subforms\SubformMediaManager.resx">
      <SubType>Designer</SubType>
      <DependentUpon>SubformMediaManager.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Primary Subforms\SubformSalesTeam.resx">
      <SubType>Designer</SubType>
      <DependentUpon>SubformSalesTeam.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Subforms\Primary Subforms\SubformWelcome.resx">
      <DependentUpon>SubformWelcome.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\licenses.licx" />
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="ClassDiagram1.cd" />
    <None Include="My Project\app.manifest" />
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.2.0">
      <Visible>False</Visible>
      <ProductName>.NET Framework 2.0</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <PublishFile Include="DevExpress.Printing.v12.2.Core">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Exclude</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="DevExpress.XtraScheduler.v12.2.Core">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.ReportViewer.DataVisualization">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.ReportViewer.ProcessingObjectModel">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.ReportViewer.WinForms">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
    <PublishFile Include="Microsoft.SqlServer.Types">
      <Visible>False</Visible>
      <Group>
      </Group>
      <TargetPath>
      </TargetPath>
      <PublishState>Include</PublishState>
      <IncludeHash>True</IncludeHash>
      <FileType>Assembly</FileType>
    </PublishFile>
  </ItemGroup>
  <ItemGroup>
    <Content Include="DatabaseIcon.ico" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Resources\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\LiquidShell\LiquidShell.vbproj">
      <Project>{3e5c068d-c18e-4b6d-a7d4-50c6c2365f3f}</Project>
      <Name>LiquidShell</Name>
    </ProjectReference>
    <ProjectReference Include="..\NovaData\NovaData.vbproj">
      <Project>{e31612d1-25f0-4999-9793-109a9ec3cbd8}</Project>
      <Name>NovaData</Name>
    </ProjectReference>
    <ProjectReference Include="..\NovaReports\NovaReports.vbproj">
      <Project>{e0dc4ee7-6331-4afe-a49d-294fd1f290bc}</Project>
      <Name>NovaReports</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>