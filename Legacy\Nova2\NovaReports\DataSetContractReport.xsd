﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DataSetContractReport" targetNamespace="http://tempuri.org/DataSetContractReport.xsd" xmlns:mstns="http://tempuri.org/DataSetContractReport.xsd" xmlns="http://tempuri.org/DataSetContractReport.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="DBConnection" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="DBConnection (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.NovaReports.My.MySettings.GlobalReference.Default.DBConnection" Provider="System.Data.SqlClient" />
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="NovaDBConnectionString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="NovaDBConnectionString (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.NovaReports.My.MySettings.GlobalReference.Default.NovaDBConnectionString" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ContractTableAdapter" GeneratorDataComponentClassName="ContractTableAdapter" Name="Contract" UserDataComponentName="ContractTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="NovaDBConnectionString (MySettings)" DbObjectName="NovaDB.reporting.udf_contracthardcopy" DbObjectType="Function" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT ContractID, AccountManagerID, ClientID, ClientName, ClientBillingAddress, ContractNumber, Signed, SignDate, SignedBy, SpecialConditions, ProjectName, ApplyAgencyComm, AgencyID, AgencyName, AgencyCommPercentage, 
                  Cancelled, CancelDate, CancelledBy, CreatedBy, CreationDate, BillingInstructions, ContractNotes, ClassificationName, DATEDIFF(wk, ISNULL(FirstWeek, CreationDate), ISNULL(LastWeek, CreationDate)) + 1 AS Weeks, ISNULL(FirstWeek, 
                  CreationDate) AS FirstWeek, ISNULL(LastWeek, CreationDate) AS LastWeek, 'Printed by ' + @PrintingUser + ' at ' + CONVERT(nvarchar(2), DATEPART(hour, GETDATE())) + N':' + CONVERT(nvarchar(2), DATEPART(minute, GETDATE())) 
                  + ' on ' + DATENAME(weekday, GETDATE()) + ', ' + CONVERT(nvarchar(2), DATEPART(day, GETDATE())) + ' ' + DATENAME(month, GETDATE()) + ' ' + CONVERT(nvarchar(4), DATEPART(year, GETDATE())) AS PrintInfo, NetRental, 
                  ISNULL(Production, 0) AS Production, DATEADD(wk, 1, ISNULL(LastWeek, CreationDate)) AS TerminationDate, DiscountAmount, PrintAgencyComm, AgencyCommAmount, dbo.udfGetClientAccountManagerCode(ClientID, CreationDate) 
                  AS ClientAccountManagerCode
FROM     reporting.udf_contracthardcopy(@ContractID) AS udf</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" Scale="0" Size="0" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="PrintingUser" ColumnName="" DataSourceName="" DataTypeServer="unknown" DbType="AnsiString" Direction="Input" ParameterName="@PrintingUser" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ContractID" DataSetColumn="ContractID" />
              <Mapping SourceColumn="AccountManagerID" DataSetColumn="AccountManagerID" />
              <Mapping SourceColumn="ClientID" DataSetColumn="ClientID" />
              <Mapping SourceColumn="ClientName" DataSetColumn="ClientName" />
              <Mapping SourceColumn="ClientBillingAddress" DataSetColumn="ClientBillingAddress" />
              <Mapping SourceColumn="ContractNumber" DataSetColumn="ContractNumber" />
              <Mapping SourceColumn="Signed" DataSetColumn="Signed" />
              <Mapping SourceColumn="SignDate" DataSetColumn="SignDate" />
              <Mapping SourceColumn="SignedBy" DataSetColumn="SignedBy" />
              <Mapping SourceColumn="SpecialConditions" DataSetColumn="SpecialConditions" />
              <Mapping SourceColumn="ProjectName" DataSetColumn="ProjectName" />
              <Mapping SourceColumn="ApplyAgencyComm" DataSetColumn="ApplyAgencyComm" />
              <Mapping SourceColumn="AgencyID" DataSetColumn="AgencyID" />
              <Mapping SourceColumn="AgencyName" DataSetColumn="AgencyName" />
              <Mapping SourceColumn="AgencyCommPercentage" DataSetColumn="AgencyCommPercentage" />
              <Mapping SourceColumn="Cancelled" DataSetColumn="Cancelled" />
              <Mapping SourceColumn="CancelDate" DataSetColumn="CancelDate" />
              <Mapping SourceColumn="CancelledBy" DataSetColumn="CancelledBy" />
              <Mapping SourceColumn="CreatedBy" DataSetColumn="CreatedBy" />
              <Mapping SourceColumn="CreationDate" DataSetColumn="CreationDate" />
              <Mapping SourceColumn="BillingInstructions" DataSetColumn="BillingInstructions" />
              <Mapping SourceColumn="ContractNotes" DataSetColumn="ContractNotes" />
              <Mapping SourceColumn="ClassificationName" DataSetColumn="ClassificationName" />
              <Mapping SourceColumn="Weeks" DataSetColumn="Weeks" />
              <Mapping SourceColumn="FirstWeek" DataSetColumn="FirstWeek" />
              <Mapping SourceColumn="LastWeek" DataSetColumn="LastWeek" />
              <Mapping SourceColumn="PrintInfo" DataSetColumn="PrintInfo" />
              <Mapping SourceColumn="NetRental" DataSetColumn="NetRental" />
              <Mapping SourceColumn="Production" DataSetColumn="Production" />
              <Mapping SourceColumn="TerminationDate" DataSetColumn="TerminationDate" />
              <Mapping SourceColumn="DiscountAmount" DataSetColumn="DiscountAmount" />
              <Mapping SourceColumn="PrintAgencyComm" DataSetColumn="PrintAgencyComm" />
              <Mapping SourceColumn="AgencyCommAmount" DataSetColumn="AgencyCommAmount" />
              <Mapping SourceColumn="ClientAccountManagerCode" DataSetColumn="ClientAccountManagerCode" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="BillingInstructionTableAdapter" GeneratorDataComponentClassName="BillingInstructionTableAdapter" Name="BillingInstruction" UserDataComponentName="BillingInstructionTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT        Sales.BillingInstruction.ContractID, Finance.Period.PeriodName, Sales.BillingInstruction.Amount, Sales.BillingInstruction.PONumber
FROM            Sales.BillingInstruction INNER JOIN
                         Finance.Period ON Sales.BillingInstruction.PeriodID = Finance.Period.PeriodID
WHERE        (Sales.BillingInstruction.ContractID = @ContractID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.BillingInstruction" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ContractID" DataSetColumn="ContractID" />
              <Mapping SourceColumn="PeriodName" DataSetColumn="PeriodName" />
              <Mapping SourceColumn="Amount" DataSetColumn="Amount" />
              <Mapping SourceColumn="PONumber" DataSetColumn="PONumber" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="BurstTableAdapter" GeneratorDataComponentClassName="BurstTableAdapter" Name="Burst" UserDataComponentName="BurstTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="NovaDBConnectionString (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT        TOP (100) PERCENT Sales.Burst.BurstID, Sales.Burst.ContractID, Sales.Burst.ChainID, Sales.Burst.ChainName, Sales.Burst.StorePoolID, Sales.Burst.MediaID, Sales.Burst.MediaName, Sales.Burst.BrandID, 
                         Sales.Burst.BrandName, Sales.Burst.ProductName, Sales.Burst.FirstWeek, DATEADD(wk, Sales.Burst.InstallWeeks - 1, Sales.Burst.FirstWeek) AS LastWeek, Sales.Burst.InstallWeeks, Sales.Burst.InstallStoreQty, 
                         Sales.Burst.BillableStoreQty, Sales.Burst.RentalRate, Sales.Burst.BillableWeeks, Sales.Burst.Discount, Sales.Burst.CrossoverQty, Sales.Burst.InstallAtHomesite, Sales.Burst.InstallationInstructions, 
                         Sales.Burst.StoreListConfirmed, Sales.Burst.CreatedBy, Sales.Burst.CreationDate, ISNULL(dtLoadingPercentage.LoadingPercentage, 0) AS LoadingPercentage, ISNULL(dtLoadingAmount.LoadingAmount, 0) AS LoadingAmount, 
                         dtFreeStoreTotal.TotalFreeStores, ISNULL(dtHomesite.Homesite, N'no homesite selected') AS Homesite, ISNULL(Sales.Burst.MediaName + N' in ' + dbo.udfCategoryListByBurst(Sales.Burst.BurstID), Sales.Burst.MediaName) 
                         AS GroupDefinition, ISNULL(Sales.vBase_Revenue_contracthardcopy.DiscountAmount, 0) AS DiscountAmount, dtFreeWeekTotal.TotalFreeWeeks
FROM            Sales.Burst INNER JOIN
                             (SELECT        ContractID, SUM(InstallStoreQty - BillableStoreQty) AS TotalFreeStores
                               FROM            Sales.Burst AS Burst_2
                               GROUP BY ContractID) AS dtFreeStoreTotal ON Sales.Burst.ContractID = dtFreeStoreTotal.ContractID INNER JOIN
                             (SELECT        ContractID, SUM(InstallWeeks - BillableWeeks) AS TotalFreeWeeks
                               FROM            Sales.Burst AS Burst_3
                               GROUP BY ContractID) AS dtFreeWeekTotal ON Sales.Burst.ContractID = dtFreeWeekTotal.ContractID LEFT OUTER JOIN
                         Sales.vBase_Revenue_contracthardcopy ON Sales.Burst.BurstID = Sales.vBase_Revenue_contracthardcopy.BurstID LEFT OUTER JOIN
                             (SELECT        Sales.BurstCategory.BurstID, Store.Category.CategoryName AS Homesite
                               FROM            Sales.BurstCategory INNER JOIN
                                                         Store.Category ON Sales.BurstCategory.CategoryID = Store.Category.CategoryID
                               WHERE        (Sales.BurstCategory.Priority = 0)) AS dtHomesite ON Sales.Burst.BurstID = dtHomesite.BurstID LEFT OUTER JOIN
                             (SELECT        Burst_1.BurstID, SUM(BurstLoadingFee_1.Percentage / 100 * Burst_1.RentalRate * Burst_1.BillableStoreQty * Burst_1.BillableWeeks) AS LoadingAmount
                               FROM            Sales.BurstLoadingFee AS BurstLoadingFee_1 INNER JOIN
                                                         Sales.Burst AS Burst_1 ON BurstLoadingFee_1.BurstID = Burst_1.BurstID
                               GROUP BY Burst_1.BurstID) AS dtLoadingAmount ON Sales.Burst.BurstID = dtLoadingAmount.BurstID LEFT OUTER JOIN
                             (SELECT        BurstID, SUM(Percentage) AS LoadingPercentage
                               FROM            Sales.BurstLoadingFee
                               GROUP BY BurstID) AS dtLoadingPercentage ON Sales.Burst.BurstID = dtLoadingPercentage.BurstID
WHERE        (Sales.Burst.ContractID = @ContractID) AND (Sales.Burst.ChainID IN
                             (SELECT        ChainID
                               FROM            dbo.udfChainPermission(@Username) AS udfChainPermission_1))
ORDER BY Sales.Burst.MediaName, Sales.Burst.BrandName, Sales.Burst.ChainName</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.Burst" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Username" ColumnName="" DataSourceName="" DataTypeServer="varchar(500)" DbType="AnsiString" Direction="Input" ParameterName="@Username" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="BurstID" DataSetColumn="BurstID" />
              <Mapping SourceColumn="ContractID" DataSetColumn="ContractID" />
              <Mapping SourceColumn="ChainID" DataSetColumn="ChainID" />
              <Mapping SourceColumn="ChainName" DataSetColumn="ChainName" />
              <Mapping SourceColumn="StorePoolID" DataSetColumn="StorePoolID" />
              <Mapping SourceColumn="MediaID" DataSetColumn="MediaID" />
              <Mapping SourceColumn="MediaName" DataSetColumn="MediaName" />
              <Mapping SourceColumn="BrandID" DataSetColumn="BrandID" />
              <Mapping SourceColumn="BrandName" DataSetColumn="BrandName" />
              <Mapping SourceColumn="ProductName" DataSetColumn="ProductName" />
              <Mapping SourceColumn="FirstWeek" DataSetColumn="FirstWeek" />
              <Mapping SourceColumn="InstallWeeks" DataSetColumn="InstallWeeks" />
              <Mapping SourceColumn="InstallStoreQty" DataSetColumn="InstallStoreQty" />
              <Mapping SourceColumn="BillableStoreQty" DataSetColumn="BillableStoreQty" />
              <Mapping SourceColumn="RentalRate" DataSetColumn="RentalRate" />
              <Mapping SourceColumn="BillableWeeks" DataSetColumn="BillableWeeks" />
              <Mapping SourceColumn="Discount" DataSetColumn="Discount" />
              <Mapping SourceColumn="CrossoverQty" DataSetColumn="CrossoverQty" />
              <Mapping SourceColumn="InstallAtHomesite" DataSetColumn="InstallAtHomesite" />
              <Mapping SourceColumn="InstallationInstructions" DataSetColumn="InstallationInstructions" />
              <Mapping SourceColumn="StoreListConfirmed" DataSetColumn="StoreListConfirmed" />
              <Mapping SourceColumn="CreatedBy" DataSetColumn="CreatedBy" />
              <Mapping SourceColumn="CreationDate" DataSetColumn="CreationDate" />
              <Mapping SourceColumn="LastWeek" DataSetColumn="LastWeek" />
              <Mapping SourceColumn="LoadingPercentage" DataSetColumn="LoadingPercentage" />
              <Mapping SourceColumn="LoadingAmount" DataSetColumn="LoadingAmount" />
              <Mapping SourceColumn="TotalFreeStores" DataSetColumn="TotalFreeStores" />
              <Mapping SourceColumn="Homesite" DataSetColumn="Homesite" />
              <Mapping SourceColumn="GroupDefinition" DataSetColumn="GroupDefinition" />
              <Mapping SourceColumn="DiscountAmount" DataSetColumn="DiscountAmount" />
              <Mapping SourceColumn="TotalFreeWeeks" DataSetColumn="TotalFreeWeeks" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ContractInventoryTableAdapter" GeneratorDataComponentClassName="ContractInventoryTableAdapter" Name="ContractInventory" UserDataComponentName="ContractInventoryTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT        Sales.ContractInventoryQty.ContractID, Ops.Inventory.ItemName, Ops.InventoryQty.ItemQty, Sales.ContractInventoryQty.SellPrice
FROM            Sales.ContractInventoryQty INNER JOIN
                         Ops.InventoryQty ON Sales.ContractInventoryQty.ItemQtyID = Ops.InventoryQty.ItemQtyID INNER JOIN
                         Ops.Inventory ON Ops.InventoryQty.ItemID = Ops.Inventory.ItemID
WHERE        (Sales.ContractInventoryQty.ContractID = @ContractID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.ContractInventoryQty" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ContractID" DataSetColumn="ContractID" />
              <Mapping SourceColumn="ItemName" DataSetColumn="ItemName" />
              <Mapping SourceColumn="ItemQty" DataSetColumn="ItemQty" />
              <Mapping SourceColumn="SellPrice" DataSetColumn="SellPrice" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="MiscellaneousChargeTableAdapter" GeneratorDataComponentClassName="MiscellaneousChargeTableAdapter" Name="MiscellaneousCharge" UserDataComponentName="MiscellaneousChargeTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT        Sales.ContractMiscellaneousCharge.ContractID, Sales.MiscellaneousCharge.MiscellaneousChargeName, 
                         Sales.ContractMiscellaneousCharge.MiscellaneousChargeAmount
FROM            Sales.ContractMiscellaneousCharge INNER JOIN
                         Sales.MiscellaneousCharge ON Sales.ContractMiscellaneousCharge.MiscellaneousChargeID = Sales.MiscellaneousCharge.MiscellaneousChargeID
WHERE        (Sales.ContractMiscellaneousCharge.ContractID = @ContractID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.ContractMiscellaneousCharge" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ContractID" DataSetColumn="ContractID" />
              <Mapping SourceColumn="MiscellaneousChargeName" DataSetColumn="MiscellaneousChargeName" />
              <Mapping SourceColumn="MiscellaneousChargeAmount" DataSetColumn="MiscellaneousChargeAmount" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ResearchContractTableAdapter" GeneratorDataComponentClassName="ResearchContractTableAdapter" Name="ResearchContract" UserDataComponentName="ResearchContractTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT        Sales.Contract.ContractID, Sales.Contract.AccountManagerID, Sales.Contract.ClientID, Sales.Contract.ClientName, Sales.Contract.ClientBillingAddress, 
                         Sales.Contract.ContractNumber, Sales.Contract.Signed, Sales.Contract.SignDate, Sales.Contract.SignedBy, Sales.Contract.SpecialConditions, 
                         Sales.Contract.ProjectName, Sales.Contract.ApplyAgencyComm, Sales.Contract.AgencyID, Sales.Contract.AgencyName, Sales.Contract.AgencyCommPercentage, 
                         Sales.Contract.Cancelled, Sales.Contract.CancelDate, Sales.Contract.CancelledBy, Sales.Contract.CreatedBy, Sales.Contract.CreationDate, 
                         Sales.Contract.BillingInstructions, Sales.Contract.ContractNotes, Client.Classification.ClassificationName, 'Printed by ' + SUSER_SNAME() 
                         + ' at ' + CONVERT(nvarchar(2), DATEPART(hour, GETDATE())) + N':' + CONVERT(nvarchar(2), DATEPART(minute, GETDATE())) + ' on ' + DATENAME(weekday, 
                         GETDATE()) + ', ' + CONVERT(nvarchar(2), DATEPART(day, GETDATE())) + ' ' + DATENAME(month, GETDATE()) + ' ' + CONVERT(nvarchar(4), DATEPART(year, 
                         GETDATE())) AS PrintInfo, { fn MONTHNAME(dtFromDate.EarliestFromDate) } + ' ' + CONVERT(nchar(4), YEAR(dtFromDate.EarliestFromDate)) AS FirstMonth, 
                         { fn MONTHNAME(dtToDate.LatestToDate) } + ' ' + CONVERT(nchar(4), YEAR(dtToDate.LatestToDate)) AS LastMonth, DATEDIFF(m, dtFromDate.EarliestFromDate, 
                         dtToDate.LatestToDate) + 1 AS Months, ISNULL(dtResearchFee.ResearchFee, 0) AS ResearchFee
FROM            Sales.Contract INNER JOIN
                         Client.Client ON Sales.Contract.ClientID = Client.Client.ClientID INNER JOIN
                         Client.Classification ON Client.Client.ClassificationID = Client.Classification.ClassificationID INNER JOIN
                             (SELECT        ContractID, MAX(DATEADD(m, Months - 1, FromDate)) AS LatestToDate
                               FROM            Sales.ResearchCategory AS ResearchCategory_2
                               GROUP BY ContractID) AS dtToDate ON Sales.Contract.ContractID = dtToDate.ContractID INNER JOIN
                             (SELECT        ContractID, MIN(FromDate) AS EarliestFromDate
                               FROM            Sales.ResearchCategory AS ResearchCategory_1
                               GROUP BY ContractID) AS dtFromDate ON Sales.Contract.ContractID = dtFromDate.ContractID LEFT OUTER JOIN
                             (SELECT        ContractID, SUM(Fee - Discount / 100 * Fee) AS ResearchFee
                               FROM            Sales.ResearchCategory
                               GROUP BY ContractID) AS dtResearchFee ON Sales.Contract.ContractID = dtResearchFee.ContractID
WHERE        (Sales.Contract.ContractID = @ContractID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.Contract" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ContractID" DataSetColumn="ContractID" />
              <Mapping SourceColumn="AccountManagerID" DataSetColumn="AccountManagerID" />
              <Mapping SourceColumn="ClientID" DataSetColumn="ClientID" />
              <Mapping SourceColumn="ClientName" DataSetColumn="ClientName" />
              <Mapping SourceColumn="ClientBillingAddress" DataSetColumn="ClientBillingAddress" />
              <Mapping SourceColumn="ContractNumber" DataSetColumn="ContractNumber" />
              <Mapping SourceColumn="Signed" DataSetColumn="Signed" />
              <Mapping SourceColumn="SignDate" DataSetColumn="SignDate" />
              <Mapping SourceColumn="SignedBy" DataSetColumn="SignedBy" />
              <Mapping SourceColumn="SpecialConditions" DataSetColumn="SpecialConditions" />
              <Mapping SourceColumn="ProjectName" DataSetColumn="ProjectName" />
              <Mapping SourceColumn="ApplyAgencyComm" DataSetColumn="ApplyAgencyComm" />
              <Mapping SourceColumn="AgencyID" DataSetColumn="AgencyID" />
              <Mapping SourceColumn="AgencyName" DataSetColumn="AgencyName" />
              <Mapping SourceColumn="AgencyCommPercentage" DataSetColumn="AgencyCommPercentage" />
              <Mapping SourceColumn="Cancelled" DataSetColumn="Cancelled" />
              <Mapping SourceColumn="CancelDate" DataSetColumn="CancelDate" />
              <Mapping SourceColumn="CancelledBy" DataSetColumn="CancelledBy" />
              <Mapping SourceColumn="CreatedBy" DataSetColumn="CreatedBy" />
              <Mapping SourceColumn="CreationDate" DataSetColumn="CreationDate" />
              <Mapping SourceColumn="BillingInstructions" DataSetColumn="BillingInstructions" />
              <Mapping SourceColumn="ContractNotes" DataSetColumn="ContractNotes" />
              <Mapping SourceColumn="ClassificationName" DataSetColumn="ClassificationName" />
              <Mapping SourceColumn="PrintInfo" DataSetColumn="PrintInfo" />
              <Mapping SourceColumn="FirstMonth" DataSetColumn="FirstMonth" />
              <Mapping SourceColumn="LastMonth" DataSetColumn="LastMonth" />
              <Mapping SourceColumn="Months" DataSetColumn="Months" />
              <Mapping SourceColumn="ResearchFee" DataSetColumn="ResearchFee" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ResearchCategoryTableAdapter" GeneratorDataComponentClassName="ResearchCategoryTableAdapter" Name="ResearchCategory" UserDataComponentName="ResearchCategoryTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT        Sales.ResearchCategory.ResearchCategoryID, Sales.ResearchCategory.ContractID, Store.Category.CategoryName, 
                         { fn MONTHNAME(Sales.ResearchCategory.FromDate) } + ' ' + CONVERT(nchar(4), YEAR(Sales.ResearchCategory.FromDate)) AS FirstMonth, 
                         { fn MONTHNAME(DATEADD(m, Sales.ResearchCategory.Months - 1, Sales.ResearchCategory.FromDate)) } + ' ' + CONVERT(nchar(4), YEAR(DATEADD(m, 
                         Sales.ResearchCategory.Months - 1, Sales.ResearchCategory.FromDate))) AS LastMonth, Sales.ResearchCategory.Months, Sales.ResearchCategory.Fee, 
                         Sales.ResearchCategory.Discount, Sales.ResearchCategory.Discount / 100 * Sales.ResearchCategory.Fee AS DiscountAmount
FROM            Sales.ResearchCategory INNER JOIN
                         Store.Category ON Sales.ResearchCategory.CategoryID = Store.Category.CategoryID
WHERE        (Sales.ResearchCategory.ContractID = @ContractID)
ORDER BY Store.Category.CategoryName</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.ResearchCategory" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ResearchCategoryID" DataSetColumn="ResearchCategoryID" />
              <Mapping SourceColumn="ContractID" DataSetColumn="ContractID" />
              <Mapping SourceColumn="CategoryName" DataSetColumn="CategoryName" />
              <Mapping SourceColumn="FirstMonth" DataSetColumn="FirstMonth" />
              <Mapping SourceColumn="LastMonth" DataSetColumn="LastMonth" />
              <Mapping SourceColumn="Months" DataSetColumn="Months" />
              <Mapping SourceColumn="Fee" DataSetColumn="Fee" />
              <Mapping SourceColumn="Discount" DataSetColumn="Discount" />
              <Mapping SourceColumn="DiscountAmount" DataSetColumn="DiscountAmount" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DataSetContractReport" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_DataSetName="DataSetContractReport" msprop:Generator_UserDSName="DataSetContractReport">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="Contract" msprop:Generator_UserTableName="Contract" msprop:Generator_RowEvArgName="ContractRowChangeEvent" msprop:Generator_TableVarName="tableContract" msprop:Generator_TablePropName="Contract" msprop:Generator_RowDeletingName="ContractRowDeleting" msprop:Generator_RowChangingName="ContractRowChanging" msprop:Generator_RowDeletedName="ContractRowDeleted" msprop:Generator_RowEvHandlerName="ContractRowChangeEventHandler" msprop:Generator_TableClassName="ContractDataTable" msprop:Generator_RowChangedName="ContractRowChanged" msprop:Generator_RowClassName="ContractRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ContractID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnContractID" msprop:Generator_ColumnPropNameInRow="ContractID" msprop:Generator_ColumnPropNameInTable="ContractIDColumn" msprop:Generator_UserColumnName="ContractID" type="xs:string" />
              <xs:element name="AccountManagerID" msprop:Generator_ColumnVarNameInTable="columnAccountManagerID" msprop:Generator_ColumnPropNameInRow="AccountManagerID" msprop:Generator_ColumnPropNameInTable="AccountManagerIDColumn" msprop:Generator_UserColumnName="AccountManagerID" type="xs:int" />
              <xs:element name="ClientID" msprop:Generator_ColumnVarNameInTable="columnClientID" msprop:Generator_ColumnPropNameInRow="ClientID" msprop:Generator_ColumnPropNameInTable="ClientIDColumn" msprop:Generator_UserColumnName="ClientID" type="xs:int" />
              <xs:element name="ClientName" msprop:Generator_ColumnVarNameInTable="columnClientName" msprop:Generator_ColumnPropNameInRow="ClientName" msprop:Generator_ColumnPropNameInTable="ClientNameColumn" msprop:Generator_UserColumnName="ClientName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ClientBillingAddress" msprop:Generator_ColumnVarNameInTable="columnClientBillingAddress" msprop:Generator_ColumnPropNameInRow="ClientBillingAddress" msprop:Generator_ColumnPropNameInTable="ClientBillingAddressColumn" msprop:Generator_UserColumnName="ClientBillingAddress">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ContractNumber" msprop:Generator_ColumnVarNameInTable="columnContractNumber" msprop:Generator_ColumnPropNameInRow="ContractNumber" msprop:Generator_ColumnPropNameInTable="ContractNumberColumn" msprop:Generator_UserColumnName="ContractNumber">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Signed" msprop:Generator_ColumnVarNameInTable="columnSigned" msprop:Generator_ColumnPropNameInRow="Signed" msprop:Generator_ColumnPropNameInTable="SignedColumn" msprop:Generator_UserColumnName="Signed" type="xs:boolean" />
              <xs:element name="SignDate" msprop:Generator_ColumnVarNameInTable="columnSignDate" msprop:Generator_ColumnPropNameInRow="SignDate" msprop:Generator_ColumnPropNameInTable="SignDateColumn" msprop:Generator_UserColumnName="SignDate" type="xs:dateTime" minOccurs="0" />
              <xs:element name="SignedBy" msprop:Generator_ColumnVarNameInTable="columnSignedBy" msprop:Generator_ColumnPropNameInRow="SignedBy" msprop:Generator_ColumnPropNameInTable="SignedByColumn" msprop:Generator_UserColumnName="SignedBy">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="SpecialConditions" msprop:Generator_ColumnVarNameInTable="columnSpecialConditions" msprop:Generator_ColumnPropNameInRow="SpecialConditions" msprop:Generator_ColumnPropNameInTable="SpecialConditionsColumn" msprop:Generator_UserColumnName="SpecialConditions">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ProjectName" msprop:Generator_ColumnVarNameInTable="columnProjectName" msprop:Generator_ColumnPropNameInRow="ProjectName" msprop:Generator_ColumnPropNameInTable="ProjectNameColumn" msprop:Generator_UserColumnName="ProjectName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ApplyAgencyComm" msprop:Generator_ColumnVarNameInTable="columnApplyAgencyComm" msprop:Generator_ColumnPropNameInRow="ApplyAgencyComm" msprop:Generator_ColumnPropNameInTable="ApplyAgencyCommColumn" msprop:Generator_UserColumnName="ApplyAgencyComm" type="xs:boolean" />
              <xs:element name="AgencyID" msprop:Generator_ColumnVarNameInTable="columnAgencyID" msprop:Generator_ColumnPropNameInRow="AgencyID" msprop:Generator_ColumnPropNameInTable="AgencyIDColumn" msprop:Generator_UserColumnName="AgencyID" type="xs:int" minOccurs="0" />
              <xs:element name="AgencyName" msprop:Generator_ColumnVarNameInTable="columnAgencyName" msprop:Generator_ColumnPropNameInRow="AgencyName" msprop:Generator_ColumnPropNameInTable="AgencyNameColumn" msprop:Generator_UserColumnName="AgencyName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="AgencyCommPercentage" msprop:Generator_ColumnVarNameInTable="columnAgencyCommPercentage" msprop:Generator_ColumnPropNameInRow="AgencyCommPercentage" msprop:Generator_ColumnPropNameInTable="AgencyCommPercentageColumn" msprop:Generator_UserColumnName="AgencyCommPercentage" type="xs:decimal" />
              <xs:element name="Cancelled" msprop:Generator_ColumnVarNameInTable="columnCancelled" msprop:Generator_ColumnPropNameInRow="Cancelled" msprop:Generator_ColumnPropNameInTable="CancelledColumn" msprop:Generator_UserColumnName="Cancelled" type="xs:boolean" />
              <xs:element name="CancelDate" msprop:Generator_ColumnVarNameInTable="columnCancelDate" msprop:Generator_ColumnPropNameInRow="CancelDate" msprop:Generator_ColumnPropNameInTable="CancelDateColumn" msprop:Generator_UserColumnName="CancelDate" type="xs:dateTime" minOccurs="0" />
              <xs:element name="CancelledBy" msprop:Generator_ColumnVarNameInTable="columnCancelledBy" msprop:Generator_ColumnPropNameInRow="CancelledBy" msprop:Generator_ColumnPropNameInTable="CancelledByColumn" msprop:Generator_UserColumnName="CancelledBy" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CreatedBy" msprop:Generator_ColumnVarNameInTable="columnCreatedBy" msprop:Generator_ColumnPropNameInRow="CreatedBy" msprop:Generator_ColumnPropNameInTable="CreatedByColumn" msprop:Generator_UserColumnName="CreatedBy">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CreationDate" msprop:Generator_ColumnVarNameInTable="columnCreationDate" msprop:Generator_ColumnPropNameInRow="CreationDate" msprop:Generator_ColumnPropNameInTable="CreationDateColumn" msprop:Generator_UserColumnName="CreationDate" type="xs:dateTime" />
              <xs:element name="BillingInstructions" msprop:Generator_ColumnVarNameInTable="columnBillingInstructions" msprop:Generator_ColumnPropNameInRow="BillingInstructions" msprop:Generator_ColumnPropNameInTable="BillingInstructionsColumn" msprop:Generator_UserColumnName="BillingInstructions">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ContractNotes" msprop:Generator_ColumnVarNameInTable="columnContractNotes" msprop:Generator_ColumnPropNameInRow="ContractNotes" msprop:Generator_ColumnPropNameInTable="ContractNotesColumn" msprop:Generator_UserColumnName="ContractNotes">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ClassificationName" msprop:Generator_ColumnVarNameInTable="columnClassificationName" msprop:Generator_ColumnPropNameInRow="ClassificationName" msprop:Generator_ColumnPropNameInTable="ClassificationNameColumn" msprop:Generator_UserColumnName="ClassificationName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Weeks" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnWeeks" msprop:Generator_ColumnPropNameInRow="Weeks" msprop:Generator_ColumnPropNameInTable="WeeksColumn" msprop:Generator_UserColumnName="Weeks" type="xs:int" minOccurs="0" />
              <xs:element name="FirstWeek" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnFirstWeek" msprop:Generator_ColumnPropNameInRow="FirstWeek" msprop:Generator_ColumnPropNameInTable="FirstWeekColumn" msprop:Generator_UserColumnName="FirstWeek" type="xs:dateTime" minOccurs="0" />
              <xs:element name="LastWeek" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnLastWeek" msprop:Generator_ColumnPropNameInRow="LastWeek" msprop:Generator_ColumnPropNameInTable="LastWeekColumn" msprop:Generator_UserColumnName="LastWeek" type="xs:dateTime" minOccurs="0" />
              <xs:element name="PrintInfo" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnPrintInfo" msprop:Generator_ColumnPropNameInRow="PrintInfo" msprop:Generator_ColumnPropNameInTable="PrintInfoColumn" msprop:Generator_UserColumnName="PrintInfo" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="222" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="NetRental" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnNetRental" msprop:Generator_ColumnPropNameInRow="NetRental" msprop:Generator_ColumnPropNameInTable="NetRentalColumn" msprop:Generator_UserColumnName="NetRental" type="xs:decimal" minOccurs="0" />
              <xs:element name="Production" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnProduction" msprop:Generator_ColumnPropNameInRow="Production" msprop:Generator_ColumnPropNameInTable="ProductionColumn" msprop:Generator_UserColumnName="Production" type="xs:decimal" minOccurs="0" />
              <xs:element name="TerminationDate" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnTerminationDate" msprop:Generator_ColumnPropNameInRow="TerminationDate" msprop:Generator_ColumnPropNameInTable="TerminationDateColumn" msprop:Generator_UserColumnName="TerminationDate" type="xs:dateTime" minOccurs="0" />
              <xs:element name="DiscountAmount" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnDiscountAmount" msprop:Generator_ColumnPropNameInRow="DiscountAmount" msprop:Generator_ColumnPropNameInTable="DiscountAmountColumn" msprop:Generator_UserColumnName="DiscountAmount" type="xs:decimal" minOccurs="0" />
              <xs:element name="PrintAgencyComm" msprop:Generator_ColumnVarNameInTable="columnPrintAgencyComm" msprop:Generator_ColumnPropNameInRow="PrintAgencyComm" msprop:Generator_ColumnPropNameInTable="PrintAgencyCommColumn" msprop:Generator_UserColumnName="PrintAgencyComm" type="xs:boolean" />
              <xs:element name="AgencyCommAmount" msprop:Generator_ColumnVarNameInTable="columnAgencyCommAmount" msprop:Generator_ColumnPropNameInRow="AgencyCommAmount" msprop:Generator_ColumnPropNameInTable="AgencyCommAmountColumn" msprop:Generator_UserColumnName="AgencyCommAmount" type="xs:decimal" minOccurs="0" />
              <xs:element name="ClientAccountManagerCode" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnClientAccountManagerCode" msprop:Generator_ColumnPropNameInRow="ClientAccountManagerCode" msprop:Generator_ColumnPropNameInTable="ClientAccountManagerCodeColumn" msprop:Generator_UserColumnName="ClientAccountManagerCode" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="**********" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="BillingInstruction" msprop:Generator_UserTableName="BillingInstruction" msprop:Generator_RowEvArgName="BillingInstructionRowChangeEvent" msprop:Generator_TableVarName="tableBillingInstruction" msprop:Generator_TablePropName="BillingInstruction" msprop:Generator_RowDeletingName="BillingInstructionRowDeleting" msprop:Generator_RowChangingName="BillingInstructionRowChanging" msprop:Generator_RowDeletedName="BillingInstructionRowDeleted" msprop:Generator_RowEvHandlerName="BillingInstructionRowChangeEventHandler" msprop:Generator_TableClassName="BillingInstructionDataTable" msprop:Generator_RowChangedName="BillingInstructionRowChanged" msprop:Generator_RowClassName="BillingInstructionRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ContractID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnContractID" msprop:Generator_ColumnPropNameInRow="ContractID" msprop:Generator_ColumnPropNameInTable="ContractIDColumn" msprop:Generator_UserColumnName="ContractID" type="xs:string" />
              <xs:element name="PeriodName" msprop:Generator_ColumnVarNameInTable="columnPeriodName" msprop:Generator_ColumnPropNameInRow="PeriodName" msprop:Generator_ColumnPropNameInTable="PeriodNameColumn" msprop:Generator_UserColumnName="PeriodName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Amount" msprop:Generator_ColumnVarNameInTable="columnAmount" msprop:Generator_ColumnPropNameInRow="Amount" msprop:Generator_ColumnPropNameInTable="AmountColumn" msprop:Generator_UserColumnName="Amount" type="xs:decimal" />
              <xs:element name="PONumber" msprop:Generator_ColumnVarNameInTable="columnPONumber" msprop:Generator_ColumnPropNameInRow="PONumber" msprop:Generator_ColumnPropNameInTable="PONumberColumn" msprop:Generator_UserColumnName="PONumber">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Burst" msprop:Generator_UserTableName="Burst" msprop:Generator_RowEvArgName="BurstRowChangeEvent" msprop:Generator_TableVarName="tableBurst" msprop:Generator_TablePropName="Burst" msprop:Generator_RowDeletingName="BurstRowDeleting" msprop:Generator_RowChangingName="BurstRowChanging" msprop:Generator_RowDeletedName="BurstRowDeleted" msprop:Generator_RowEvHandlerName="BurstRowChangeEventHandler" msprop:Generator_TableClassName="BurstDataTable" msprop:Generator_RowChangedName="BurstRowChanged" msprop:Generator_RowClassName="BurstRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="BurstID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnBurstID" msprop:Generator_ColumnPropNameInRow="BurstID" msprop:Generator_ColumnPropNameInTable="BurstIDColumn" msprop:Generator_UserColumnName="BurstID" type="xs:string" />
              <xs:element name="ContractID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnContractID" msprop:Generator_ColumnPropNameInRow="ContractID" msprop:Generator_ColumnPropNameInTable="ContractIDColumn" msprop:Generator_UserColumnName="ContractID" type="xs:string" />
              <xs:element name="ChainID" msprop:Generator_ColumnVarNameInTable="columnChainID" msprop:Generator_ColumnPropNameInRow="ChainID" msprop:Generator_ColumnPropNameInTable="ChainIDColumn" msprop:Generator_UserColumnName="ChainID" type="xs:int" />
              <xs:element name="ChainName" msprop:Generator_ColumnVarNameInTable="columnChainName" msprop:Generator_ColumnPropNameInRow="ChainName" msprop:Generator_ColumnPropNameInTable="ChainNameColumn" msprop:Generator_UserColumnName="ChainName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="StorePoolID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnStorePoolID" msprop:Generator_ColumnPropNameInRow="StorePoolID" msprop:Generator_ColumnPropNameInTable="StorePoolIDColumn" msprop:Generator_UserColumnName="StorePoolID" type="xs:string" />
              <xs:element name="MediaID" msprop:Generator_ColumnVarNameInTable="columnMediaID" msprop:Generator_ColumnPropNameInRow="MediaID" msprop:Generator_ColumnPropNameInTable="MediaIDColumn" msprop:Generator_UserColumnName="MediaID" type="xs:int" />
              <xs:element name="MediaName" msprop:Generator_ColumnVarNameInTable="columnMediaName" msprop:Generator_ColumnPropNameInRow="MediaName" msprop:Generator_ColumnPropNameInTable="MediaNameColumn" msprop:Generator_UserColumnName="MediaName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="BrandID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnBrandID" msprop:Generator_ColumnPropNameInRow="BrandID" msprop:Generator_ColumnPropNameInTable="BrandIDColumn" msprop:Generator_UserColumnName="BrandID" type="xs:string" />
              <xs:element name="BrandName" msprop:Generator_ColumnVarNameInTable="columnBrandName" msprop:Generator_ColumnPropNameInRow="BrandName" msprop:Generator_ColumnPropNameInTable="BrandNameColumn" msprop:Generator_UserColumnName="BrandName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ProductName" msprop:Generator_ColumnVarNameInTable="columnProductName" msprop:Generator_ColumnPropNameInRow="ProductName" msprop:Generator_ColumnPropNameInTable="ProductNameColumn" msprop:Generator_UserColumnName="ProductName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="FirstWeek" msprop:Generator_ColumnVarNameInTable="columnFirstWeek" msprop:Generator_ColumnPropNameInRow="FirstWeek" msprop:Generator_ColumnPropNameInTable="FirstWeekColumn" msprop:Generator_UserColumnName="FirstWeek" type="xs:dateTime" />
              <xs:element name="InstallWeeks" msprop:Generator_ColumnVarNameInTable="columnInstallWeeks" msprop:Generator_ColumnPropNameInRow="InstallWeeks" msprop:Generator_ColumnPropNameInTable="InstallWeeksColumn" msprop:Generator_UserColumnName="InstallWeeks" type="xs:int" />
              <xs:element name="InstallStoreQty" msprop:Generator_ColumnVarNameInTable="columnInstallStoreQty" msprop:Generator_ColumnPropNameInRow="InstallStoreQty" msprop:Generator_ColumnPropNameInTable="InstallStoreQtyColumn" msprop:Generator_UserColumnName="InstallStoreQty" type="xs:int" />
              <xs:element name="BillableStoreQty" msprop:Generator_ColumnVarNameInTable="columnBillableStoreQty" msprop:Generator_ColumnPropNameInRow="BillableStoreQty" msprop:Generator_ColumnPropNameInTable="BillableStoreQtyColumn" msprop:Generator_UserColumnName="BillableStoreQty" type="xs:int" />
              <xs:element name="RentalRate" msprop:Generator_ColumnVarNameInTable="columnRentalRate" msprop:Generator_ColumnPropNameInRow="RentalRate" msprop:Generator_ColumnPropNameInTable="RentalRateColumn" msprop:Generator_UserColumnName="RentalRate" type="xs:decimal" />
              <xs:element name="BillableWeeks" msprop:Generator_ColumnVarNameInTable="columnBillableWeeks" msprop:Generator_ColumnPropNameInRow="BillableWeeks" msprop:Generator_ColumnPropNameInTable="BillableWeeksColumn" msprop:Generator_UserColumnName="BillableWeeks" type="xs:int" />
              <xs:element name="Discount" msprop:Generator_ColumnVarNameInTable="columnDiscount" msprop:Generator_ColumnPropNameInRow="Discount" msprop:Generator_ColumnPropNameInTable="DiscountColumn" msprop:Generator_UserColumnName="Discount" type="xs:decimal" />
              <xs:element name="CrossoverQty" msprop:Generator_ColumnVarNameInTable="columnCrossoverQty" msprop:Generator_ColumnPropNameInRow="CrossoverQty" msprop:Generator_ColumnPropNameInTable="CrossoverQtyColumn" msprop:Generator_UserColumnName="CrossoverQty" type="xs:int" />
              <xs:element name="InstallAtHomesite" msprop:Generator_ColumnVarNameInTable="columnInstallAtHomesite" msprop:Generator_ColumnPropNameInRow="InstallAtHomesite" msprop:Generator_ColumnPropNameInTable="InstallAtHomesiteColumn" msprop:Generator_UserColumnName="InstallAtHomesite" type="xs:boolean" />
              <xs:element name="InstallationInstructions" msprop:Generator_ColumnVarNameInTable="columnInstallationInstructions" msprop:Generator_ColumnPropNameInRow="InstallationInstructions" msprop:Generator_ColumnPropNameInTable="InstallationInstructionsColumn" msprop:Generator_UserColumnName="InstallationInstructions">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="StoreListConfirmed" msprop:Generator_ColumnVarNameInTable="columnStoreListConfirmed" msprop:Generator_ColumnPropNameInRow="StoreListConfirmed" msprop:Generator_ColumnPropNameInTable="StoreListConfirmedColumn" msprop:Generator_UserColumnName="StoreListConfirmed" type="xs:boolean" />
              <xs:element name="CreatedBy" msprop:Generator_ColumnVarNameInTable="columnCreatedBy" msprop:Generator_ColumnPropNameInRow="CreatedBy" msprop:Generator_ColumnPropNameInTable="CreatedByColumn" msprop:Generator_UserColumnName="CreatedBy">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CreationDate" msprop:Generator_ColumnVarNameInTable="columnCreationDate" msprop:Generator_ColumnPropNameInRow="CreationDate" msprop:Generator_ColumnPropNameInTable="CreationDateColumn" msprop:Generator_UserColumnName="CreationDate" type="xs:dateTime" />
              <xs:element name="LastWeek" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnLastWeek" msprop:Generator_ColumnPropNameInRow="LastWeek" msprop:Generator_ColumnPropNameInTable="LastWeekColumn" msprop:Generator_UserColumnName="LastWeek" type="xs:dateTime" minOccurs="0" />
              <xs:element name="LoadingPercentage" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnLoadingPercentage" msprop:Generator_ColumnPropNameInRow="LoadingPercentage" msprop:Generator_ColumnPropNameInTable="LoadingPercentageColumn" msprop:Generator_UserColumnName="LoadingPercentage" type="xs:decimal" minOccurs="0" />
              <xs:element name="LoadingAmount" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnLoadingAmount" msprop:Generator_ColumnPropNameInRow="LoadingAmount" msprop:Generator_ColumnPropNameInTable="LoadingAmountColumn" msprop:Generator_UserColumnName="LoadingAmount" type="xs:decimal" minOccurs="0" />
              <xs:element name="TotalFreeStores" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnTotalFreeStores" msprop:Generator_ColumnPropNameInRow="TotalFreeStores" msprop:Generator_ColumnPropNameInTable="TotalFreeStoresColumn" msprop:Generator_UserColumnName="TotalFreeStores" type="xs:int" minOccurs="0" />
              <xs:element name="Homesite" msprop:Generator_ColumnVarNameInTable="columnHomesite" msprop:Generator_ColumnPropNameInRow="Homesite" msprop:Generator_ColumnPropNameInTable="HomesiteColumn" msprop:Generator_UserColumnName="Homesite" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="GroupDefinition" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnGroupDefinition" msprop:Generator_ColumnPropNameInRow="GroupDefinition" msprop:Generator_ColumnPropNameInTable="GroupDefinitionColumn" msprop:Generator_UserColumnName="GroupDefinition" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="**********" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="DiscountAmount" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnDiscountAmount" msprop:Generator_ColumnPropNameInRow="DiscountAmount" msprop:Generator_ColumnPropNameInTable="DiscountAmountColumn" msprop:Generator_UserColumnName="DiscountAmount" type="xs:decimal" minOccurs="0" />
              <xs:element name="TotalFreeWeeks" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnTotalFreeWeeks" msprop:Generator_ColumnPropNameInRow="TotalFreeWeeks" msprop:Generator_ColumnPropNameInTable="TotalFreeWeeksColumn" msprop:Generator_UserColumnName="TotalFreeWeeks" type="xs:int" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ContractInventory" msprop:Generator_UserTableName="ContractInventory" msprop:Generator_RowEvArgName="ContractInventoryRowChangeEvent" msprop:Generator_TableVarName="tableContractInventory" msprop:Generator_TablePropName="ContractInventory" msprop:Generator_RowDeletingName="ContractInventoryRowDeleting" msprop:Generator_RowChangingName="ContractInventoryRowChanging" msprop:Generator_RowDeletedName="ContractInventoryRowDeleted" msprop:Generator_RowEvHandlerName="ContractInventoryRowChangeEventHandler" msprop:Generator_TableClassName="ContractInventoryDataTable" msprop:Generator_RowChangedName="ContractInventoryRowChanged" msprop:Generator_RowClassName="ContractInventoryRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ContractID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnContractID" msprop:Generator_ColumnPropNameInRow="ContractID" msprop:Generator_ColumnPropNameInTable="ContractIDColumn" msprop:Generator_UserColumnName="ContractID" type="xs:string" />
              <xs:element name="ItemName" msprop:Generator_ColumnVarNameInTable="columnItemName" msprop:Generator_ColumnPropNameInRow="ItemName" msprop:Generator_ColumnPropNameInTable="ItemNameColumn" msprop:Generator_UserColumnName="ItemName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ItemQty" msprop:Generator_ColumnVarNameInTable="columnItemQty" msprop:Generator_ColumnPropNameInRow="ItemQty" msprop:Generator_ColumnPropNameInTable="ItemQtyColumn" msprop:Generator_UserColumnName="ItemQty" type="xs:int" />
              <xs:element name="SellPrice" msprop:Generator_ColumnVarNameInTable="columnSellPrice" msprop:Generator_ColumnPropNameInRow="SellPrice" msprop:Generator_ColumnPropNameInTable="SellPriceColumn" msprop:Generator_UserColumnName="SellPrice" type="xs:decimal" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="MiscellaneousCharge" msprop:Generator_UserTableName="MiscellaneousCharge" msprop:Generator_RowEvArgName="MiscellaneousChargeRowChangeEvent" msprop:Generator_TableVarName="tableMiscellaneousCharge" msprop:Generator_TablePropName="MiscellaneousCharge" msprop:Generator_RowDeletingName="MiscellaneousChargeRowDeleting" msprop:Generator_RowChangingName="MiscellaneousChargeRowChanging" msprop:Generator_RowDeletedName="MiscellaneousChargeRowDeleted" msprop:Generator_RowEvHandlerName="MiscellaneousChargeRowChangeEventHandler" msprop:Generator_TableClassName="MiscellaneousChargeDataTable" msprop:Generator_RowChangedName="MiscellaneousChargeRowChanged" msprop:Generator_RowClassName="MiscellaneousChargeRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ContractID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnContractID" msprop:Generator_ColumnPropNameInRow="ContractID" msprop:Generator_ColumnPropNameInTable="ContractIDColumn" msprop:Generator_UserColumnName="ContractID" type="xs:string" />
              <xs:element name="MiscellaneousChargeName" msprop:Generator_ColumnVarNameInTable="columnMiscellaneousChargeName" msprop:Generator_ColumnPropNameInRow="MiscellaneousChargeName" msprop:Generator_ColumnPropNameInTable="MiscellaneousChargeNameColumn" msprop:Generator_UserColumnName="MiscellaneousChargeName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="MiscellaneousChargeAmount" msprop:Generator_ColumnVarNameInTable="columnMiscellaneousChargeAmount" msprop:Generator_ColumnPropNameInRow="MiscellaneousChargeAmount" msprop:Generator_ColumnPropNameInTable="MiscellaneousChargeAmountColumn" msprop:Generator_UserColumnName="MiscellaneousChargeAmount" type="xs:decimal" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ResearchContract" msprop:Generator_TableClassName="ResearchContractDataTable" msprop:Generator_TableVarName="tableResearchContract" msprop:Generator_RowChangedName="ResearchContractRowChanged" msprop:Generator_TablePropName="ResearchContract" msprop:Generator_RowDeletingName="ResearchContractRowDeleting" msprop:Generator_RowChangingName="ResearchContractRowChanging" msprop:Generator_RowEvHandlerName="ResearchContractRowChangeEventHandler" msprop:Generator_RowDeletedName="ResearchContractRowDeleted" msprop:Generator_RowClassName="ResearchContractRow" msprop:Generator_UserTableName="ResearchContract" msprop:Generator_RowEvArgName="ResearchContractRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ContractID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnContractID" msprop:Generator_ColumnPropNameInRow="ContractID" msprop:Generator_ColumnPropNameInTable="ContractIDColumn" msprop:Generator_UserColumnName="ContractID" type="xs:string" />
              <xs:element name="AccountManagerID" msprop:Generator_ColumnVarNameInTable="columnAccountManagerID" msprop:Generator_ColumnPropNameInRow="AccountManagerID" msprop:Generator_ColumnPropNameInTable="AccountManagerIDColumn" msprop:Generator_UserColumnName="AccountManagerID" type="xs:int" />
              <xs:element name="ClientID" msprop:Generator_ColumnVarNameInTable="columnClientID" msprop:Generator_ColumnPropNameInRow="ClientID" msprop:Generator_ColumnPropNameInTable="ClientIDColumn" msprop:Generator_UserColumnName="ClientID" type="xs:int" />
              <xs:element name="ClientName" msprop:Generator_ColumnVarNameInTable="columnClientName" msprop:Generator_ColumnPropNameInRow="ClientName" msprop:Generator_ColumnPropNameInTable="ClientNameColumn" msprop:Generator_UserColumnName="ClientName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ClientBillingAddress" msprop:Generator_ColumnVarNameInTable="columnClientBillingAddress" msprop:Generator_ColumnPropNameInRow="ClientBillingAddress" msprop:Generator_ColumnPropNameInTable="ClientBillingAddressColumn" msprop:Generator_UserColumnName="ClientBillingAddress">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ContractNumber" msprop:Generator_ColumnVarNameInTable="columnContractNumber" msprop:Generator_ColumnPropNameInRow="ContractNumber" msprop:Generator_ColumnPropNameInTable="ContractNumberColumn" msprop:Generator_UserColumnName="ContractNumber">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Signed" msprop:Generator_ColumnVarNameInTable="columnSigned" msprop:Generator_ColumnPropNameInRow="Signed" msprop:Generator_ColumnPropNameInTable="SignedColumn" msprop:Generator_UserColumnName="Signed" type="xs:boolean" />
              <xs:element name="SignDate" msprop:Generator_ColumnVarNameInTable="columnSignDate" msprop:Generator_ColumnPropNameInRow="SignDate" msprop:Generator_ColumnPropNameInTable="SignDateColumn" msprop:Generator_UserColumnName="SignDate" type="xs:dateTime" minOccurs="0" />
              <xs:element name="SignedBy" msprop:Generator_ColumnVarNameInTable="columnSignedBy" msprop:Generator_ColumnPropNameInRow="SignedBy" msprop:Generator_ColumnPropNameInTable="SignedByColumn" msprop:Generator_UserColumnName="SignedBy">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="SpecialConditions" msprop:Generator_ColumnVarNameInTable="columnSpecialConditions" msprop:Generator_ColumnPropNameInRow="SpecialConditions" msprop:Generator_ColumnPropNameInTable="SpecialConditionsColumn" msprop:Generator_UserColumnName="SpecialConditions">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ProjectName" msprop:Generator_ColumnVarNameInTable="columnProjectName" msprop:Generator_ColumnPropNameInRow="ProjectName" msprop:Generator_ColumnPropNameInTable="ProjectNameColumn" msprop:Generator_UserColumnName="ProjectName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ApplyAgencyComm" msprop:Generator_ColumnVarNameInTable="columnApplyAgencyComm" msprop:Generator_ColumnPropNameInRow="ApplyAgencyComm" msprop:Generator_ColumnPropNameInTable="ApplyAgencyCommColumn" msprop:Generator_UserColumnName="ApplyAgencyComm" type="xs:boolean" />
              <xs:element name="AgencyID" msprop:Generator_ColumnVarNameInTable="columnAgencyID" msprop:Generator_ColumnPropNameInRow="AgencyID" msprop:Generator_ColumnPropNameInTable="AgencyIDColumn" msprop:Generator_UserColumnName="AgencyID" type="xs:int" minOccurs="0" />
              <xs:element name="AgencyName" msprop:Generator_ColumnVarNameInTable="columnAgencyName" msprop:Generator_ColumnPropNameInRow="AgencyName" msprop:Generator_ColumnPropNameInTable="AgencyNameColumn" msprop:Generator_UserColumnName="AgencyName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="AgencyCommPercentage" msprop:Generator_ColumnVarNameInTable="columnAgencyCommPercentage" msprop:Generator_ColumnPropNameInRow="AgencyCommPercentage" msprop:Generator_ColumnPropNameInTable="AgencyCommPercentageColumn" msprop:Generator_UserColumnName="AgencyCommPercentage" type="xs:decimal" />
              <xs:element name="Cancelled" msprop:Generator_ColumnVarNameInTable="columnCancelled" msprop:Generator_ColumnPropNameInRow="Cancelled" msprop:Generator_ColumnPropNameInTable="CancelledColumn" msprop:Generator_UserColumnName="Cancelled" type="xs:boolean" />
              <xs:element name="CancelDate" msprop:Generator_ColumnVarNameInTable="columnCancelDate" msprop:Generator_ColumnPropNameInRow="CancelDate" msprop:Generator_ColumnPropNameInTable="CancelDateColumn" msprop:Generator_UserColumnName="CancelDate" type="xs:dateTime" minOccurs="0" />
              <xs:element name="CancelledBy" msprop:Generator_ColumnVarNameInTable="columnCancelledBy" msprop:Generator_ColumnPropNameInRow="CancelledBy" msprop:Generator_ColumnPropNameInTable="CancelledByColumn" msprop:Generator_UserColumnName="CancelledBy" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CreatedBy" msprop:Generator_ColumnVarNameInTable="columnCreatedBy" msprop:Generator_ColumnPropNameInRow="CreatedBy" msprop:Generator_ColumnPropNameInTable="CreatedByColumn" msprop:Generator_UserColumnName="CreatedBy">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CreationDate" msprop:Generator_ColumnVarNameInTable="columnCreationDate" msprop:Generator_ColumnPropNameInRow="CreationDate" msprop:Generator_ColumnPropNameInTable="CreationDateColumn" msprop:Generator_UserColumnName="CreationDate" type="xs:dateTime" />
              <xs:element name="BillingInstructions" msprop:Generator_ColumnVarNameInTable="columnBillingInstructions" msprop:Generator_ColumnPropNameInRow="BillingInstructions" msprop:Generator_ColumnPropNameInTable="BillingInstructionsColumn" msprop:Generator_UserColumnName="BillingInstructions">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ContractNotes" msprop:Generator_ColumnVarNameInTable="columnContractNotes" msprop:Generator_ColumnPropNameInRow="ContractNotes" msprop:Generator_ColumnPropNameInTable="ContractNotesColumn" msprop:Generator_UserColumnName="ContractNotes">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ClassificationName" msprop:Generator_ColumnVarNameInTable="columnClassificationName" msprop:Generator_ColumnPropNameInRow="ClassificationName" msprop:Generator_ColumnPropNameInTable="ClassificationNameColumn" msprop:Generator_UserColumnName="ClassificationName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="PrintInfo" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnPrintInfo" msprop:Generator_ColumnPropNameInRow="PrintInfo" msprop:Generator_ColumnPropNameInTable="PrintInfoColumn" msprop:Generator_UserColumnName="PrintInfo" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="222" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="FirstMonth" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnFirstMonth" msprop:Generator_ColumnPropNameInRow="FirstMonth" msprop:Generator_ColumnPropNameInTable="FirstMonthColumn" msprop:Generator_UserColumnName="FirstMonth" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="35" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="LastMonth" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnLastMonth" msprop:Generator_ColumnPropNameInRow="LastMonth" msprop:Generator_ColumnPropNameInTable="LastMonthColumn" msprop:Generator_UserColumnName="LastMonth" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="35" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Months" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnMonths" msprop:Generator_ColumnPropNameInRow="Months" msprop:Generator_ColumnPropNameInTable="MonthsColumn" msprop:Generator_UserColumnName="Months" type="xs:int" minOccurs="0" />
              <xs:element name="ResearchFee" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnResearchFee" msprop:Generator_ColumnPropNameInRow="ResearchFee" msprop:Generator_ColumnPropNameInTable="ResearchFeeColumn" msprop:Generator_UserColumnName="ResearchFee" type="xs:decimal" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ResearchCategory" msprop:Generator_TableClassName="ResearchCategoryDataTable" msprop:Generator_TableVarName="tableResearchCategory" msprop:Generator_RowChangedName="ResearchCategoryRowChanged" msprop:Generator_TablePropName="ResearchCategory" msprop:Generator_RowDeletingName="ResearchCategoryRowDeleting" msprop:Generator_RowChangingName="ResearchCategoryRowChanging" msprop:Generator_RowEvHandlerName="ResearchCategoryRowChangeEventHandler" msprop:Generator_RowDeletedName="ResearchCategoryRowDeleted" msprop:Generator_RowClassName="ResearchCategoryRow" msprop:Generator_UserTableName="ResearchCategory" msprop:Generator_RowEvArgName="ResearchCategoryRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ResearchCategoryID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnResearchCategoryID" msprop:Generator_ColumnPropNameInRow="ResearchCategoryID" msprop:Generator_ColumnPropNameInTable="ResearchCategoryIDColumn" msprop:Generator_UserColumnName="ResearchCategoryID" type="xs:string" />
              <xs:element name="ContractID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnContractID" msprop:Generator_ColumnPropNameInRow="ContractID" msprop:Generator_ColumnPropNameInTable="ContractIDColumn" msprop:Generator_UserColumnName="ContractID" type="xs:string" />
              <xs:element name="CategoryName" msprop:Generator_ColumnVarNameInTable="columnCategoryName" msprop:Generator_ColumnPropNameInRow="CategoryName" msprop:Generator_ColumnPropNameInTable="CategoryNameColumn" msprop:Generator_UserColumnName="CategoryName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="FirstMonth" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnFirstMonth" msprop:Generator_ColumnPropNameInRow="FirstMonth" msprop:Generator_ColumnPropNameInTable="FirstMonthColumn" msprop:Generator_UserColumnName="FirstMonth" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="35" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="LastMonth" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnLastMonth" msprop:Generator_ColumnPropNameInRow="LastMonth" msprop:Generator_ColumnPropNameInTable="LastMonthColumn" msprop:Generator_UserColumnName="LastMonth" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="35" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Months" msprop:Generator_ColumnVarNameInTable="columnMonths" msprop:Generator_ColumnPropNameInRow="Months" msprop:Generator_ColumnPropNameInTable="MonthsColumn" msprop:Generator_UserColumnName="Months" type="xs:int" />
              <xs:element name="Fee" msprop:Generator_ColumnVarNameInTable="columnFee" msprop:Generator_ColumnPropNameInRow="Fee" msprop:Generator_ColumnPropNameInTable="FeeColumn" msprop:Generator_UserColumnName="Fee" type="xs:decimal" />
              <xs:element name="Discount" msprop:Generator_ColumnVarNameInTable="columnDiscount" msprop:Generator_ColumnPropNameInRow="Discount" msprop:Generator_ColumnPropNameInTable="DiscountColumn" msprop:Generator_UserColumnName="Discount" type="xs:decimal" />
              <xs:element name="DiscountAmount" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnDiscountAmount" msprop:Generator_ColumnPropNameInRow="DiscountAmount" msprop:Generator_ColumnPropNameInTable="DiscountAmountColumn" msprop:Generator_UserColumnName="DiscountAmount" type="xs:decimal" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Contract" />
      <xs:field xpath="mstns:ContractID" />
    </xs:unique>
    <xs:unique name="Burst_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Burst" />
      <xs:field xpath="mstns:BurstID" />
    </xs:unique>
    <xs:unique name="ResearchContract_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ResearchContract" />
      <xs:field xpath="mstns:ContractID" />
    </xs:unique>
    <xs:unique name="ResearchCategory_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ResearchCategory" />
      <xs:field xpath="mstns:ResearchCategoryID" />
    </xs:unique>
  </xs:element>
  <xs:annotation>
    <xs:appinfo>
      <msdata:Relationship name="Contract_PurchaseOrderNumber" msdata:parent="Contract" msdata:child="BillingInstruction" msdata:parentkey="ContractID" msdata:childkey="ContractID" msprop:Generator_UserChildTable="BillingInstruction" msprop:Generator_ChildPropName="GetBillingInstructionRows" msprop:Generator_UserRelationName="Contract_PurchaseOrderNumber" msprop:Generator_RelationVarName="relationContract_PurchaseOrderNumber" msprop:Generator_UserParentTable="Contract" msprop:Generator_ParentPropName="ContractRow" />
      <msdata:Relationship name="Contract_Burst" msdata:parent="Contract" msdata:child="Burst" msdata:parentkey="ContractID" msdata:childkey="ContractID" msprop:Generator_UserChildTable="Burst" msprop:Generator_ChildPropName="GetBurstRows" msprop:Generator_UserRelationName="Contract_Burst" msprop:Generator_RelationVarName="relationContract_Burst" msprop:Generator_UserParentTable="Contract" msprop:Generator_ParentPropName="ContractRow" />
      <msdata:Relationship name="Contract_ContractInventory" msdata:parent="Contract" msdata:child="ContractInventory" msdata:parentkey="ContractID" msdata:childkey="ContractID" msprop:Generator_UserChildTable="ContractInventory" msprop:Generator_ChildPropName="GetContractInventoryRows" msprop:Generator_UserRelationName="Contract_ContractInventory" msprop:Generator_RelationVarName="relationContract_ContractInventory" msprop:Generator_UserParentTable="Contract" msprop:Generator_ParentPropName="ContractRow" />
      <msdata:Relationship name="Contract_MiscellaneousCharge" msdata:parent="Contract" msdata:child="MiscellaneousCharge" msdata:parentkey="ContractID" msdata:childkey="ContractID" msprop:Generator_UserChildTable="MiscellaneousCharge" msprop:Generator_ChildPropName="GetMiscellaneousChargeRows" msprop:Generator_UserRelationName="Contract_MiscellaneousCharge" msprop:Generator_RelationVarName="relationContract_MiscellaneousCharge" msprop:Generator_UserParentTable="Contract" msprop:Generator_ParentPropName="ContractRow" />
      <msdata:Relationship name="ResearchContract_PurchaseOrderNumber" msdata:parent="ResearchContract" msdata:child="BillingInstruction" msdata:parentkey="ContractID" msdata:childkey="ContractID" msprop:Generator_UserChildTable="BillingInstruction" msprop:Generator_ChildPropName="GetPurchaseOrderNumberRows" msprop:Generator_UserRelationName="ResearchContract_PurchaseOrderNumber" msprop:Generator_RelationVarName="relationResearchContract_PurchaseOrderNumber" msprop:Generator_UserParentTable="ResearchContract" msprop:Generator_ParentPropName="ResearchContractRow" />
      <msdata:Relationship name="ResearchContract_MiscellaneousCharge" msdata:parent="ResearchContract" msdata:child="MiscellaneousCharge" msdata:parentkey="ContractID" msdata:childkey="ContractID" msprop:Generator_UserChildTable="MiscellaneousCharge" msprop:Generator_ChildPropName="GetMiscellaneousChargeRows" msprop:Generator_UserRelationName="ResearchContract_MiscellaneousCharge" msprop:Generator_RelationVarName="relationResearchContract_MiscellaneousCharge" msprop:Generator_UserParentTable="ResearchContract" msprop:Generator_ParentPropName="ResearchContractRow" />
      <msdata:Relationship name="ResearchContract_ResearchCategory" msdata:parent="ResearchContract" msdata:child="ResearchCategory" msdata:parentkey="ContractID" msdata:childkey="ContractID" msprop:Generator_UserChildTable="ResearchCategory" msprop:Generator_ChildPropName="GetResearchCategoryRows" msprop:Generator_UserRelationName="ResearchContract_ResearchCategory" msprop:Generator_RelationVarName="relationResearchContract_ResearchCategory" msprop:Generator_UserParentTable="ResearchContract" msprop:Generator_ParentPropName="ResearchContractRow" />
    </xs:appinfo>
  </xs:annotation>
</xs:schema>