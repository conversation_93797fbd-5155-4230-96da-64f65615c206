﻿using DataAccess;
using System;

namespace DataService.Security
{
    class GetTermsOfUseCommandExecutor : CommandExecutor<GetTermsOfUseCommand>
    {
        public override void Execute(GetTermsOfUseCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.GetTermsOfUseText))
            {
                storedprocedure.AddOutputParameter<Guid>("termsofuseid");
                storedprocedure.AddOutputParameter<string>("termsofusetext");
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    command.TermsOfUseId = (Guid)storedprocedure.GetOutputParameterValue("termsofuseid");
                    command.TermsOfUseText = storedprocedure.GetOutputParameterValue("termsofusetext").ToString();
                }
            }
        }
    }
}
