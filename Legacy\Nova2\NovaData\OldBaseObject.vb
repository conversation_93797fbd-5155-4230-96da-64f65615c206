Public MustInherit Class OldBaseObject

    Protected ConnectionString As String
    Protected _RowDescription As String = String.Empty

    ' A variable to indicate whether or not any properties have been modified.
    Private _IsDirty As Boolean = False

    ' A table to log changes for auditing purposes.
    Public AuditLog As New DataSetAudit.AuditLogDataTable

    ' The data row that is the backbone of this object.
    Private _Row As DataRow

    ' The binding source that connects to this object's data table.
    Protected _DataBindingSource As BindingSource

    Public ReadOnly Property RowDescription() As String
        Get
            Return _RowDescription
        End Get
    End Property

    Public Property IsDirty() As Boolean
        Get
            Return _IsDirty
        End Get
        Set(ByVal value As Boolean)
            _IsDirty = value
        End Set
    End Property

    Public ReadOnly Property Row() As DataRow
        Get
            Return _Row
        End Get
    End Property

    Public ReadOnly Property Table() As DataTable
        Get
            If IsNothing(Row) Then
                Return Nothing
            Else
                Return Row.Table
            End If
        End Get
    End Property

    Public ReadOnly Property DataSet() As DataSet
        Get
            If IsNothing(Table) Then
                Return Nothing
            Else
                Return Table.DataSet
            End If
        End Get
    End Property

    Protected Property DataBindingSource() As BindingSource
        Get
            Return _DataBindingSource
        End Get
        Set(ByVal value As BindingSource)
            _DataBindingSource = value
            _Row = CType(value.Current, DataRowView).Row
            UpdateProperties()
        End Set
    End Property

    Protected Overridable Sub UpdateProperties()
    End Sub

    Public Sub Close(ByVal Consumingform As LiquidShell.BaseForm)

        ' Check if changes have been made to this object.
        If IsDirty Then
            ' Verify that user wants to discard changes.
            Dim Message As New System.Text.StringBuilder
            Message.Append("You have made some modifications to this item." + vbCrLf + vbCrLf + "Are you sure you would like ")
            Message.Append("to close it and discard all your changes?")
            Dim UserResponse As DialogResult = Consumingform.ShowMessage(Message.ToString, MessageBoxButtons.YesNoCancel)
            If Not UserResponse = DialogResult.Yes Then
                ' User doesn't want to discard changes.
                Exit Sub
            End If
        End If

        ' Discard changes and close the subform.
        DataBindingSource.CancelEdit()
        If IsNothing(DataSet) = False Then
            DataSet.RejectChanges()
        End If

    End Sub

    Public Sub Close(ByVal ConsumingSubform As LiquidShell.Subform)

        ' Check if changes have been made to this object.
        If IsDirty Then
            ' Verify that user wants to discard changes.
            Dim Message As New System.Text.StringBuilder
            Message.Append("You have made some modifications to this item." + vbCrLf + vbCrLf + "Are you sure you would like ")
            Message.Append("to close it and discard all your changes?")
            Dim ParentForm As LiquidShell.BaseForm = CType(ConsumingSubform.TopLevelControl, LiquidShell.BaseForm)
            Dim UserResponse As DialogResult = ParentForm.ShowMessage(Message.ToString, MessageBoxButtons.YesNoCancel)
            If Not UserResponse = DialogResult.Yes Then
                ' User doesn't want to discard changes.
                Exit Sub
            End If
        End If

        ' Discard changes and close the subform.
        DataBindingSource.CancelEdit()
        If IsNothing(DataSet) = False Then
            DataSet.RejectChanges()
        End If
        ConsumingSubform.RevertToParentSubform()

    End Sub

    Protected Sub AddLog(ByVal RowDescriptionColumnName As String)
        ' Add an entry into the audit log table for a newly added row.

        ' Log the entry.
        LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, Table.TableName, Row(RowDescriptionColumnName), "Created")

    End Sub

    Protected Sub AddLog _
    (ByVal RowDescriptionColumnName As String, _
    ByVal ChangedPropertyName As String, _
    ByVal ChangedPropertyNewValue As String)
        ' Add an entry into the audit log table for a modified row.

        ' Stop if a new row is being created because only changes are audited.
        If Row.RowState = DataRowState.Detached Then
            Exit Sub
        End If

        ' Create a string variable to hold the log description.
        Dim Action As String = String.Empty

        ' Create a log description.
        If String.IsNullOrEmpty(ChangedPropertyNewValue) Then
            Action = "Changed the value of " & ChangedPropertyName.ToUpper & " to an EMPTY STRING"
        Else
            Action = "Changed the value of " & ChangedPropertyName.ToUpper & " to '" & ChangedPropertyNewValue & "'"
        End If

        ' Log the entry.
        LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, Table.TableName, Row(RowDescriptionColumnName), Action)

    End Sub

    Public Sub DeleteChildRow(ByVal Grid As DataGridView, ByVal ParentRowDescriptionColumnName As String)

        ' Confirm the delete action.
        If LiquidShell.LiquidAgent.ActionConfirmed(Grid, "Delete") = False Then
            Exit Sub
        End If

        ' Check if at least one row in the grid has been selected.
        If Grid.SelectedRows.Count > 0 Then
            ' Delete the selected rows from the data table.
            For Each SelectedGridRow As DataGridViewRow In Grid.SelectedRows
                ' Log the action.
                LiquidShell.LiquidAgent.AddAuditLogEntry _
                (AuditLog, Table.TableName, _
                Row(ParentRowDescriptionColumnName), _
                BuildChildRowActionText(Grid, _
                SelectedGridRow, _
                ChildRowAction.Deleted))
                ' Delete the row.
                CType(SelectedGridRow.DataBoundItem, DataRowView).Row.Delete()
            Next
            ' Flag the object as dirty.
            IsDirty = True
        End If

    End Sub

    Protected Enum ChildRowAction
        ' Possible first words of child row audit log entry action texts.
        Added
        Modified
        Deleted
    End Enum

    Protected Function BuildChildRowActionText _
    (ByVal Grid As DataGridView, ByVal GridRow As DataGridViewRow, ByVal Action As ChildRowAction) As String

        ' Get the datarow behind the grid row.
        Dim Row As DataRow = CType(GridRow.DataBoundItem, DataRowView).Row

        ' Get a description of the grid row using the columns of the grid.
        Dim DescriptionBuilder As New System.Text.StringBuilder()
        For Each Column As DataGridViewColumn In Grid.Columns
            If Column.Visible Then
                If DescriptionBuilder.Length > 0 Then
                    ' Separate items with a semi-colon.
                    DescriptionBuilder.Append("; ")
                End If
                DescriptionBuilder.Append(Column.HeaderText.ToUpper & ": " & Row(Column.DataPropertyName))
            End If
        Next
        ' Add the beginning of the action description to the beginning of the string.
        If Action = ChildRowAction.Added Then
            DescriptionBuilder.Insert(0, "Added child row: ")
        ElseIf Action = ChildRowAction.Deleted Then
            DescriptionBuilder.Insert(0, "Deleted child row: ")
        Else
            DescriptionBuilder.Insert(0, "Modified child row: ")
        End If

        Return DescriptionBuilder.ToString

    End Function

End Class
