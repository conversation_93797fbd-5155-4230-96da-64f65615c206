﻿using DataAccess;

namespace DataService.Finance
{
    class GetTableOfStorePaymentsCommandExecutor : CommandExecutor<GetTableOfStorePaymentsCommand>
    {
        public override void Execute(GetTableOfStorePaymentsCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.GetStorePaymentTable))
            {
                storedprocedure.AddInputParameter("startdate", command.StartDate);
                storedprocedure.AddInputParameter("enddate", command.EndDate);
                storedprocedure.AddInputParameter("chainid", command.ChainID);
                storedprocedure.AddInputParameter("headofficeid", command.HeadOfficeID);
                storedprocedure.AddInputParameter("storeid", command.StoreID);
                storedprocedure.AddInputParameter("contractid", command.ContractID);
                storedprocedure.AddInputParameter("mediaserviceid", command.MediaServiceID);
                storedprocedure.AddInputParameter("categoryid", command.CategoryID);
                storedprocedure.AddInputParameter("homesite", command.Homesite);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    command.Table = storedprocedure.OutputTable;
                }
            }
        }
    }
}
