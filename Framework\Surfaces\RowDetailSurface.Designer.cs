﻿namespace Framework.Surfaces
{
    partial class RowDetailSurface
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.flatButtonSave = new Framework.Controls.FlatButton();
            this.flatButtonDiscardChanges = new Framework.Controls.FlatButton();
            this.SuspendLayout();
            // 
            // flatButtonSave
            // 
            this.flatButtonSave.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.flatButtonSave.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            this.flatButtonSave.Cursor = System.Windows.Forms.Cursors.Hand;
            this.flatButtonSave.ErrorMessage = "";
            this.flatButtonSave.FlatAppearance.BorderSize = 0;
            this.flatButtonSave.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(137)))), ((int)(((byte)(172)))), ((int)(((byte)(65)))));
            this.flatButtonSave.FlatAppearance.MouseOverBackColor = System.Drawing.Color.OliveDrab;
            this.flatButtonSave.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.flatButtonSave.ForeColor = System.Drawing.Color.White;
            this.flatButtonSave.Location = new System.Drawing.Point(20, 312);
            this.flatButtonSave.Margin = new System.Windows.Forms.Padding(20, 0, 0, 20);
            this.flatButtonSave.Name = "flatButtonSave";
            this.flatButtonSave.Size = new System.Drawing.Size(180, 60);
            this.flatButtonSave.TabIndex = 500;
            this.flatButtonSave.Text = "Save";
            this.flatButtonSave.UseVisualStyleBackColor = false;
            this.flatButtonSave.Visible = false;
            // 
            // flatButtonDiscardChanges
            // 
            this.flatButtonDiscardChanges.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.flatButtonDiscardChanges.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            this.flatButtonDiscardChanges.Cursor = System.Windows.Forms.Cursors.Hand;
            this.flatButtonDiscardChanges.ErrorMessage = "";
            this.flatButtonDiscardChanges.FlatAppearance.BorderSize = 0;
            this.flatButtonDiscardChanges.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(137)))), ((int)(((byte)(172)))), ((int)(((byte)(65)))));
            this.flatButtonDiscardChanges.FlatAppearance.MouseOverBackColor = System.Drawing.Color.OliveDrab;
            this.flatButtonDiscardChanges.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.flatButtonDiscardChanges.ForeColor = System.Drawing.Color.White;
            this.flatButtonDiscardChanges.Location = new System.Drawing.Point(742, 312);
            this.flatButtonDiscardChanges.Margin = new System.Windows.Forms.Padding(0, 0, 20, 20);
            this.flatButtonDiscardChanges.Name = "flatButtonDiscardChanges";
            this.flatButtonDiscardChanges.Size = new System.Drawing.Size(180, 60);
            this.flatButtonDiscardChanges.TabIndex = 540;
            this.flatButtonDiscardChanges.Text = "Discard Changes";
            this.flatButtonDiscardChanges.UseVisualStyleBackColor = false;
            this.flatButtonDiscardChanges.Visible = false;
            // 
            // RowDetailSurface
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 13F);
            this.Controls.Add(this.flatButtonDiscardChanges);
            this.Controls.Add(this.flatButtonSave);
            this.Name = "RowDetailSurface";
            this.Size = new System.Drawing.Size(942, 392);
            this.ResumeLayout(false);

        }

        #endregion
        protected Controls.FlatButton flatButtonDiscardChanges;
        public Controls.FlatButton flatButtonSave;
    }
}
