﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
Nova2Ops
</name>
</assembly>
<members>
<member name="T:Nova2Ops.My.Resources.Resources">
<summary>
  A strongly-typed resource class, for looking up localized strings, etc.
</summary>
</member>
<member name="P:Nova2Ops.My.Resources.Resources.ResourceManager">
<summary>
  Returns the cached ResourceManager instance used by this class.
</summary>
</member>
<member name="P:Nova2Ops.My.Resources.Resources.Culture">
<summary>
  Overrides the current thread's CurrentUICulture property for all
  resource lookups using this strongly typed resource class.
</summary>
</member>
<member name="P:Nova2Ops.My.Resources.Resources.add16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Nova2Ops.My.Resources.Resources.delete16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Nova2Ops.My.Resources.Resources.pencil16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:Nova2Ops.My.Resources.Resources.search16">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="T:Nova2Ops.DataSetAudit">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetAudit.AuditLogDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetAudit.AuditLogRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetAudit.AuditLogRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetAuditTableAdapters.AuditLogTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetContract">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetContract.BurstDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetContract.BurstRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetContract.BurstRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetContractTableAdapters.BurstTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetContractTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:Nova2Ops.DataSetContractTableAdapters.TableAdapterManager.UpdateUpdatedRows(Nova2Ops.DataSetContract,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:Nova2Ops.DataSetContractTableAdapters.TableAdapterManager.UpdateInsertedRows(Nova2Ops.DataSetContract,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:Nova2Ops.DataSetContractTableAdapters.TableAdapterManager.UpdateDeletedRows(Nova2Ops.DataSetContract,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:Nova2Ops.DataSetContractTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:Nova2Ops.DataSetContractTableAdapters.TableAdapterManager.UpdateAll(Nova2Ops.DataSetContract)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:Nova2Ops.DataSetContractTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:Nova2Ops.DataSetContractTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreGrouping">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreGrouping.GroupChainDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreGrouping.ChainGroupStoreDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreGrouping.StoreDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreGrouping.RegionDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreGrouping.ChainDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreGrouping.GroupChainRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreGrouping.ChainGroupStoreRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreGrouping.StoreRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreGrouping.RegionRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreGrouping.ChainRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreGrouping.GroupChainRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreGrouping.ChainGroupStoreRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreGrouping.StoreRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreGrouping.RegionRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreGrouping.ChainRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreGroupingTableAdapters.GroupChainTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreGroupingTableAdapters.ChainGroupStoreTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreGroupingTableAdapters.StoreTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreGroupingTableAdapters.RegionTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreGroupingTableAdapters.ChainTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreGroupingTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:Nova2Ops.DataSetStoreGroupingTableAdapters.TableAdapterManager.UpdateUpdatedRows(Nova2Ops.DataSetStoreGrouping,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:Nova2Ops.DataSetStoreGroupingTableAdapters.TableAdapterManager.UpdateInsertedRows(Nova2Ops.DataSetStoreGrouping,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:Nova2Ops.DataSetStoreGroupingTableAdapters.TableAdapterManager.UpdateDeletedRows(Nova2Ops.DataSetStoreGrouping,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:Nova2Ops.DataSetStoreGroupingTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:Nova2Ops.DataSetStoreGroupingTableAdapters.TableAdapterManager.UpdateAll(Nova2Ops.DataSetStoreGrouping)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreGroupingTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreGroupingTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePayments">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePayments.ChainSelectedForPaymentClassificationDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePayments.PaymentClassificationDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePayments.TypeOfPaymentDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePayments.StoreSelectedForPaymentDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePayments.StoreSelectedForPaymentActualTableDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePayments.MediaRateDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePayments.MediaDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePayments.NoRatingsMediaDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePayments.ChainSelectedForPaymentClassificationRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePayments.PaymentClassificationRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePayments.TypeOfPaymentRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePayments.StoreSelectedForPaymentRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePayments.StoreSelectedForPaymentActualTableRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePayments.MediaRateRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePayments.MediaRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePayments.NoRatingsMediaRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePayments.ChainSelectedForPaymentClassificationRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePayments.PaymentClassificationRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePayments.TypeOfPaymentRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePayments.StoreSelectedForPaymentRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePayments.StoreSelectedForPaymentActualTableRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePayments.MediaRateRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePayments.MediaRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePayments.NoRatingsMediaRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePaymentsTableAdapters.ChainSelectedForPaymentClassificationTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePaymentsTableAdapters.PaymentClassificationTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePaymentsTableAdapters.TypeOfPaymentTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePaymentsTableAdapters.StoreSelectedForPaymentTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePaymentsTableAdapters.StoreSelectedForPaymentActualTableTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePaymentsTableAdapters.MediaRateTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePaymentsTableAdapters.MediaTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePaymentsTableAdapters.NoRatingsMediaTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePaymentsTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:Nova2Ops.DataSetStorePaymentsTableAdapters.TableAdapterManager.UpdateUpdatedRows(Nova2Ops.DataSetStorePayments,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:Nova2Ops.DataSetStorePaymentsTableAdapters.TableAdapterManager.UpdateInsertedRows(Nova2Ops.DataSetStorePayments,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:Nova2Ops.DataSetStorePaymentsTableAdapters.TableAdapterManager.UpdateDeletedRows(Nova2Ops.DataSetStorePayments,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:Nova2Ops.DataSetStorePaymentsTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:Nova2Ops.DataSetStorePaymentsTableAdapters.TableAdapterManager.UpdateAll(Nova2Ops.DataSetStorePayments)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePaymentsTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:Nova2Ops.DataSetStorePaymentsTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:Nova2Ops.DataSetSupplier">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetSupplier.SupplierDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetSupplier.SupplierInventoryDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetSupplier.InventoryDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetSupplier.SupplierRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetSupplier.SupplierInventoryRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetSupplier.InventoryRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetSupplier.SupplierRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetSupplier.SupplierInventoryRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetSupplier.InventoryRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetSupplierTableAdapters.SupplierTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetSupplierTableAdapters.SupplierInventoryTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetSupplierTableAdapters.InventoryTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetSupplierTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:Nova2Ops.DataSetSupplierTableAdapters.TableAdapterManager.UpdateUpdatedRows(Nova2Ops.DataSetSupplier,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:Nova2Ops.DataSetSupplierTableAdapters.TableAdapterManager.UpdateInsertedRows(Nova2Ops.DataSetSupplier,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:Nova2Ops.DataSetSupplierTableAdapters.TableAdapterManager.UpdateDeletedRows(Nova2Ops.DataSetSupplier,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:Nova2Ops.DataSetSupplierTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:Nova2Ops.DataSetSupplierTableAdapters.TableAdapterManager.UpdateAll(Nova2Ops.DataSetSupplier)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:Nova2Ops.DataSetSupplierTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:Nova2Ops.DataSetSupplierTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:Nova2Ops.DataSetStockTake">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStockTake.MediaDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStockTake.StockTakeDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStockTake.MediaRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStockTake.StockTakeRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStockTake.MediaRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetStockTake.StockTakeRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetStockTakeTableAdapters.MediaTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStockTakeTableAdapters.StockTakeTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStockTakeTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:Nova2Ops.DataSetStockTakeTableAdapters.TableAdapterManager.UpdateUpdatedRows(Nova2Ops.DataSetStockTake,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:Nova2Ops.DataSetStockTakeTableAdapters.TableAdapterManager.UpdateInsertedRows(Nova2Ops.DataSetStockTake,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:Nova2Ops.DataSetStockTakeTableAdapters.TableAdapterManager.UpdateDeletedRows(Nova2Ops.DataSetStockTake,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:Nova2Ops.DataSetStockTakeTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:Nova2Ops.DataSetStockTakeTableAdapters.TableAdapterManager.UpdateAll(Nova2Ops.DataSetStockTake)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStockTakeTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:Nova2Ops.DataSetStockTakeTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.PurchaseDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.PurchaseDetailDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.VanDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.ProductDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.PackSizeDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.BrandDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.UnitsPerShrinkDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.SaleDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.SaleDetailDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.StoreDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.DistributionCheckDetailDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.DistributionCheckDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.DistributionCheckStoreDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.DistributionCheckProductDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.DistributionCheckProductInstockDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.ServiceCallDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.PurchaseRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.PurchaseDetailRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.VanRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.ProductRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.PackSizeRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.BrandRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.UnitsPerShrinkRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.SaleRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.SaleDetailRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.StoreRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.DistributionCheckDetailRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.DistributionCheckRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.DistributionCheckStoreRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.DistributionCheckProductRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.DistributionCheckProductInstockRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.ServiceCallRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.PurchaseRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.PurchaseDetailRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.VanRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.ProductRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.PackSizeRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.BrandRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.UnitsPerShrinkRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.SaleRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.SaleDetailRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.StoreRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.DistributionCheckDetailRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.DistributionCheckRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.DistributionCheckStoreRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.DistributionCheckProductRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.DistributionCheckProductInstockRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistribution.ServiceCallRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistributionTableAdapters.PurchaseTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistributionTableAdapters.PurchaseDetailTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistributionTableAdapters.VanTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistributionTableAdapters.ProductTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistributionTableAdapters.PackSizeTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistributionTableAdapters.BrandTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistributionTableAdapters.UnitsPerShrinkTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistributionTableAdapters.SaleTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistributionTableAdapters.SaleDetailTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistributionTableAdapters.StoreTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistributionTableAdapters.DistributionCheckDetailTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistributionTableAdapters.DistributionCheckTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistributionTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:Nova2Ops.DataSetDistributionTableAdapters.TableAdapterManager.UpdateUpdatedRows(Nova2Ops.DataSetDistribution,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:Nova2Ops.DataSetDistributionTableAdapters.TableAdapterManager.UpdateInsertedRows(Nova2Ops.DataSetDistribution,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:Nova2Ops.DataSetDistributionTableAdapters.TableAdapterManager.UpdateDeletedRows(Nova2Ops.DataSetDistribution,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:Nova2Ops.DataSetDistributionTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:Nova2Ops.DataSetDistributionTableAdapters.TableAdapterManager.UpdateAll(Nova2Ops.DataSetDistribution)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistributionTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:Nova2Ops.DataSetDistributionTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventory">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventory.InventoryQtyDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventory.InventoryQtyPriceDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventory.InventoryDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventory.InventoryQtyPartDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventory.MediaInventoryDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventory.MediaDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventory.InventoryQtyRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventory.InventoryQtyPriceRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventory.InventoryRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventory.InventoryQtyPartRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventory.MediaInventoryRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventory.MediaRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventory.InventoryQtyRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventory.InventoryQtyPriceRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventory.InventoryRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventory.InventoryQtyPartRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventory.MediaInventoryRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventory.MediaRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventoryTableAdapters.InventoryQtyTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventoryTableAdapters.InventoryQtyPriceTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventoryTableAdapters.InventoryTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventoryTableAdapters.InventoryQtyPartTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventoryTableAdapters.MediaInventoryTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventoryTableAdapters.MediaTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventoryTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:Nova2Ops.DataSetInventoryTableAdapters.TableAdapterManager.UpdateUpdatedRows(Nova2Ops.DataSetInventory,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:Nova2Ops.DataSetInventoryTableAdapters.TableAdapterManager.UpdateInsertedRows(Nova2Ops.DataSetInventory,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:Nova2Ops.DataSetInventoryTableAdapters.TableAdapterManager.UpdateDeletedRows(Nova2Ops.DataSetInventory,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:Nova2Ops.DataSetInventoryTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:Nova2Ops.DataSetInventoryTableAdapters.TableAdapterManager.UpdateAll(Nova2Ops.DataSetInventory)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventoryTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:Nova2Ops.DataSetInventoryTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.ChainDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.RegionDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.WarehouseDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.StoreMediaDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.StoreDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.InstallationTeamDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.ChainGroupDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.ChainGroupChainDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.StoreSelectedForPaymentDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.PaymentClassificationDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.ChainTargetsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.PeriodDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.ChainRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.RegionRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.WarehouseRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.StoreMediaRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.StoreRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.InstallationTeamRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.ChainGroupRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.ChainGroupChainRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.StoreSelectedForPaymentRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.PaymentClassificationRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.ChainTargetsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.PeriodRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.ChainRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.RegionRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.WarehouseRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.StoreMediaRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.StoreRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.InstallationTeamRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.ChainGroupRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.ChainGroupChainRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.StoreSelectedForPaymentRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.PaymentClassificationRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.ChainTargetsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverse.PeriodRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverseTableAdapters.ChainTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverseTableAdapters.RegionTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverseTableAdapters.WarehouseTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverseTableAdapters.StoreMediaTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverseTableAdapters.StoreTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverseTableAdapters.InstallationTeamTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverseTableAdapters.ChainGroupTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverseTableAdapters.ChainGroupChainTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverseTableAdapters.StoreSelectedForPaymentTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverseTableAdapters.PaymentClassificationTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverseTableAdapters.ChainTargetsTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverseTableAdapters.PeriodTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverseTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:Nova2Ops.DataSetStoreUniverseTableAdapters.TableAdapterManager.UpdateUpdatedRows(Nova2Ops.DataSetStoreUniverse,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:Nova2Ops.DataSetStoreUniverseTableAdapters.TableAdapterManager.UpdateInsertedRows(Nova2Ops.DataSetStoreUniverse,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:Nova2Ops.DataSetStoreUniverseTableAdapters.TableAdapterManager.UpdateDeletedRows(Nova2Ops.DataSetStoreUniverse,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:Nova2Ops.DataSetStoreUniverseTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:Nova2Ops.DataSetStoreUniverseTableAdapters.TableAdapterManager.UpdateAll(Nova2Ops.DataSetStoreUniverse)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverseTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:Nova2Ops.DataSetStoreUniverseTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
</members>
</doc>
