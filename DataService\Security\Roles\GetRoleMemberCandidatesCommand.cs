﻿using DataAccess;
using System;
using System.Data;

namespace DataService.Security
{
    internal class GetRoleMemberCandidatesCommand : Command
    {
        public Guid SessionId;
        public Guid RoleId;
        public DataTable Table;

        public GetRoleMemberCandidatesCommand(Guid sessionid, Guid roleid)
        {
            SessionId = sessionid;
            RoleId = roleid;
        }
    }
}
