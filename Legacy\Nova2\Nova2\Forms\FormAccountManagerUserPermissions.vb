Public Class FormAccountManagerUserPermissions

    Private DataObject As AccountManager

    Public Sub New(ByVal AccountManagerObject As AccountManager)

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        DataObject = AccountManagerObject
        LabelTitle.Text = DataObject.FullName
        Grid.AutoGenerateColumns = False
        Grid.DataSource = DataObject.AccountManagerPermissionUserBindingSource
        Dim GridManagerPermissions As New GridManager(Grid, TextEditSearch, Nothing, Nothing, _
        PictureAdvancedSearch, PictureClearSearch, Nothing, Nothing)

    End Sub

    Private Sub ButtonCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonCancel.Click
        Close()
    End Sub

    Private Sub ButtonSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonSave.Click
        ' Set the dialog result to OK.
        DialogResult = Windows.Forms.DialogResult.OK
        ' Save the current object.
        Dim GridsToAudit As New List(Of DataGridView)
        GridsToAudit.Add(Grid)
        DataObject.Save(My.Settings.DBConnection, GridsToAudit)
        Close()
    End Sub

    Private Sub FormAccountManagerUserPermissions_FormClosing _
    (ByVal sender As Object, ByVal e As System.Windows.Forms.FormClosingEventArgs) _
    Handles Me.FormClosing

        ' Exit if user clicked OK.
        If DialogResult = Windows.Forms.DialogResult.OK Then
            Exit Sub
        Else
            ' The user is closing the form, but didn't click the OK button. If the user clicked the form close button
            ' then we need to move focus away from the grid so that any possible change made will commit to the data table,
            ' allowing us to be able to tell whether anything was changed.
            GroupControlPermissions.Focus()
        End If

        ' Get a table with changes.
        Dim PermissionsTable As DataTable = LiquidAgent.GetBindingSourceTable(DataObject.AccountManagerPermissionUserBindingSource)
        Dim ChangesMade As DataTable = PermissionsTable.GetChanges

        ' Check if changes have been made to this object.
        If IsNothing(ChangesMade) = False Then
            ' The table with changes has served its purpose. Destroy it.
            ChangesMade.Dispose()
            ' Verify that user wants to discard changes.
            Dim Message As New System.Text.StringBuilder
            Message.Append("You have made some modifications to this table." + vbCrLf + vbCrLf + "Are you sure you would like ")
            Message.Append("to close it and discard all your changes?")
            Dim UserResponse As DialogResult = ShowMessage(Message.ToString, MessageBoxButtons.YesNoCancel)
            If Not UserResponse = DialogResult.Yes Then
                ' User doesn't want to discard changes.
                e.Cancel = True
            End If
        End If

    End Sub

End Class
