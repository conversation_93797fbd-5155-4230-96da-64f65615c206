﻿using DataAccess;

namespace DataService.Security
{
    class RemoveLegacyRolesOfMemberCommandExecutor : CommandExecutor<RemoveLegacyRolesOfMemberCommand>
    {

        public override void Execute(RemoveLegacyRolesOfMemberCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.RemoveLegacyRolesOfMember))
            {
                storedprocedure.AddInputParameter("login", command.Login);
                storedprocedure.AddInputParameter("roles", command.Roles);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
            }
        }

    }
}
