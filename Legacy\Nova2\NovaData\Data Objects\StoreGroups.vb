﻿Public Class StoreGroups
    Inherits OldBaseObject

#Region "Fields"

    ' Custom fields
    Private _GroupChainBindingSource As BindingSource

#End Region


#Region "Database Column Properties"

    Public ReadOnly Property GroupChainId() As Integer
        ' This object's identifier.
        Get
            Return Row("GroupChainId")
        End Get
    End Property

    Public Property GroupChainName() As String
        ' Descriptive name of the Group.
        Get
            If Row("GroupChainName").ToString() = "" Then
                Return ""
            Else
                Return Row("GroupChainName")

            End If


        End Get
        Set(ByVal value As String)
            ' Clean the proposed new value.
            value = value.Trim
            If Not String.Compare(Row("GroupChainName"), value, False) = 0 Then
                ' New value is different than existing value. Proceed with update.
                AddLog("GroupChainName", "GroupChainName", value.ToString)
                Row("GroupChainName") = value
                IsDirty = True
            End If
        End Set
    End Property

    Public Property Dormant() As Boolean
        ' Dormant group is inactive and may not be used anymore.
        Get
            Return Row("Dormant")
        End Get
        Set(ByVal value As Boolean)
            If Not Row("Dormant") = value Then
                ' New value is different than existing value. Proceed with update.
                AddLog("GroupChainName", "Dormant", value.ToString)
                Row("Dormant") = value
                IsDirty = True
            End If
        End Set
    End Property

#End Region

#Region "Custom Properties"

    Public ReadOnly Property GroupChainBindingSource() As BindingSource
        Get
            Return _GroupChainBindingSource
        End Get
    End Property

#End Region

#Region "Public Shared Methods"

    Public Shared Function GetListData _
    (ByVal ConString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) As System.Windows.Forms.BindingSource

        ' Instantiate a new data set and create a sql connection to use to populate it.
        Dim ListDataSet As New DataSetStoreGroups
        Dim SqlCon As New SqlClient.SqlConnection(ConString)

        ' Create table adapters to collect list data and related data.
        Dim ListAdapter As New DataSetStoreGroupsTableAdapters.GroupChainTableAdapter
        ListAdapter.Connection = SqlCon

        ' Populate tables in the dataset.
        Try
            ListAdapter.Fill(ListDataSet.Tables("GroupChain"))
        Catch ex As Exception
            ErrorMessage += LiquidShell.LiquidAgent.GetErrorMessage(ex)
            Return Nothing
        Finally
            ListAdapter.Dispose()
            SqlCon.Dispose()
        End Try

        ' Return a binding source linked to the retrieved data.
        Dim ReturnBindingSource As BindingSource = New BindingSource(ListDataSet, "GroupChain")
        ReturnBindingSource.Sort = "GroupChainName"
        Return ReturnBindingSource

    End Function



#End Region

#Region "Public Methods"

    Public Sub New(ByVal DataSource As BindingSource, ByVal AddNew As Boolean)
        ' This constructor takes a binding source and a boolean indicating whether a new
        ' row must be added or whether this is an existing row being edited.

        If AddNew Then
            ' This is a new row being added.
            DataSource.AddNew()
        End If

        ' Update the base class binding source variable.
        DataBindingSource = DataSource

    End Sub

    Public Sub Save(ByVal ConnectionString As String, ByVal GridsToAudit As List(Of DataGridView))
        ' Save this object to the database.

        ' Create and configure table adapters to connect to the database.
        Dim SqlAdapter As New DataSetStoreGroupsTableAdapters.GroupChainTableAdapter
        Dim ChainStoresAdapter As New DataSetStoreGroupsTableAdapters.ChainGroupStoreTableAdapter
        Dim AuditAdapter As New DataSetAuditTableAdapters.AuditLogTableAdapter

        '' Create a new connection for the table adapters to use.
        Dim SqlCon As New SqlClient.SqlConnection(ConnectionString)
        SqlAdapter.Connection = SqlCon
        ChainStoresAdapter.Connection = SqlCon
        AuditAdapter.Connection = SqlCon

        '' Check if this is a new row being added.
        If Row.RowState = DataRowState.Detached Then
            ' This is a new row being added. Clear the audit log and create only one entry for the new row.
            AuditLog.Clear()
            AddLog("GroupChainName")
        Else
            ' This row was edited. Update audit log with child row addition details.
            For Each Grid As DataGridView In GridsToAudit
                ' Check each row in this grid.
                For Each GridRow As DataGridViewRow In Grid.Rows
                    ' Get the datarow feeding this grid row.
                    Dim Row As DataRow = CType(GridRow.DataBoundItem, DataRowView).Row
                    If Row.RowState = DataRowState.Added Then
                        ' This row is newly added. Add an entry for it into the audit log.
                        Dim ActionText As String = BuildChildRowActionText(Grid, GridRow, ChildRowAction.Added)
                        LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, Table.TableName, GroupChainName, ActionText)
                    End If
                    If Row.RowState = DataRowState.Modified Then
                        ' This row has been modified. Add an entry for it into the audit log.
                        Dim ActionText As String = BuildChildRowActionText(Grid, GridRow, ChildRowAction.Modified)
                        LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, Table.TableName, GroupChainName, ActionText)
                    End If
                Next
            Next
        End If

        '' Perform the save operation.
        Try
            ' Remember the current item in the binding source list.
            Dim CurrentItem As Object = DataBindingSource.Current
            ' Commit changes to the dataset.
            DataBindingSource.EndEdit()     ' This line changes the binding source's current item.
            ' Restore the current item in the binding source list.
            DataBindingSource.Position = DataBindingSource.IndexOf(CurrentItem)
            ' Update the database.
            SqlAdapter.Update(Row)
            ChainStoresAdapter.Update(DataSet.Tables("ChainGroupStore"))
            IsDirty = False
            AuditAdapter.Update(AuditLog)
        Finally
            ' Dispose all data adapters.
            SqlAdapter.Dispose()
            ' MediaCategoryAdapter.Dispose()
            AuditAdapter.Dispose()
            ' Dispose the connection.
            SqlCon.Close()
        End Try

    End Sub

    Public Sub AddStoreGroups _
    (ByVal ConsumingSubform As LiquidShell.Subform,
    ByVal ConnectionString As String,
    ByVal SelectedItems As List(Of DataRow))
        ' Add media services to the list of allowed media services for this category.

        ' Check for legal selection by user.
        If IsNothing(SelectedItems) Then
            Exit Sub
        Else
            If SelectedItems.Count = 0 Then
                Exit Sub
            End If
        End If

        ' Add a new row to the table for each item that was selected.
        For Each SelectedItem As DataRow In SelectedItems

            ' Create a new row for the table.
            Dim NewRow As DataRow = DataSet.Tables("ChainGroupStore").NewRow
            NewRow("GroupChainID") = Row("GroupChainID")
            NewRow("StoreID") = SelectedItem("StoreID")
            NewRow("StoreName") = SelectedItem("StoreName")
            DataSet.Tables("ChainGroupStore").Rows.Add(NewRow)

            ' Flag the object as dirty.
            IsDirty = True

        Next

    End Sub





#End Region


#Region "Private Methods"

    Protected Overrides Sub UpdateProperties()

        ' Update properties to match the current Row.
        UpdateCustomProperties()

    End Sub

    Private Sub UpdateCustomProperties()
        _GroupChainBindingSource = New BindingSource(DataBindingSource, "FK_ChainGroupStore_GroupChain")
    End Sub

#End Region
End Class
