<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="ImageList24x24.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>144, 17</value>
  </metadata>
  <data name="ImageList24x24.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj0yLjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAAD6
        DQAAAk1TRnQBSQFMAgEBAgEAARgBAAEYAQABGAEAARgBAAT/AREBAAj/AUIBTQE2BwABNgMAASgDAAFg
        AwABGAMAAQEBAAEQBgABEhgAAf8BfwGdAXMBewFvAZwBcwH/AX8QAAH/AX8BnAFzAXsBbwGdAXMB/wF/
        GAAB/wF/Af8BfwH/AX98AAH/AX8BewFvAf8BewH/AXsB/wF7AZwBcwH/AX8MAAH/AX8BnAFzAf8BewH/
        AXsB/wJ7AW8B/wF/FgABvQF3AXsBbwGcAW8B3gF7eAAB/wF/AXsBbwH/AXsBGAFrAcYBQAHuAVUB/wF7
        AZwBcwH/AX8IAAH/AX8BnAFvAf8BewHuAVUBxgFAARgBawH/AnsBbwH/AX8QAAH/AX8BewFvAb8BewHf
        AX8B3wF/AZwBcwH/AX90AAH/AX8BewFvAf8BewHWAWYBAAE0AQABNAEAATQBagFNAf8BewGdAXMB/wF/
        BAAB/wF/AZ0BcwH/AXsBagFNAQABNAEAATQBAAE0AdYBZgH/AnsBbwH/AX8MAAH/AX8BewFvAd8BfwF4
        AWcBaAE6Aa4BSgHfAnsBbwH/AX9yAAG9AXcB/wF7AfcBagEAATgBQQE8AWMBQAFjAUABAAE4AYwBUQH/
        AX8BnAFzAf8BfwH/AX8BnAFzAf8BfwGMAVEBAAE4AWMBQAFjAUABQQE8AQABOAH3AWoB/wF7Ab0BdwoA
        Af8BfwF7AW8B3wF/AVYBYwEBASYBAQEmAeABHQHwAU4B3wF/AZwBc3IAAXwBbwH/AXsBYwFAASABPAGD
        AUABYwFAAWMBQAFiAUABAAE4AYwBVQH/AXsBfAFvAXwBbwH/AX8BrAFVAQABOAFiAUABYwFAAWMBQAGD
        AUABIAE8AWMBQAH/AXsBnAFvCAAB/wF/AXsBbwHfAX8BeQFrASEBKgFDAS4BRAEyAUQBLgEiASoBnAFz
        Ab4BdwH/AX9wAAGcAXMB/wF7AeYBTAEAAUABgwFEAWMBRAFjAUQBYwFEAWIBRAEAATwBawFVAf8BfwH/
        AX8BawFVAQABPAFiAUQBYwFEAWMBRAFjAUQBgwFEAQABQAHmAUwB/wF7AZwBcwYAAf8BfwF7AW8B3wF/
        AXgBZwFCAS4BQwEuAWQBMgFkATIBZAEyAUEBKgHLAUYB/wF/AXsBb3AAAf8BfwH/AXsBvQF3AWIBRAEg
        AUQBgwFIAWMBSAFjAUgBYwFIAWMBSAEAAUAB7wFdAe8BXQEAAUABYgFIAWMBSAFjAUgBYwFIAYMBSAEg
        AUQBYgFEAb0BdwH/AXsB/wF/BAAB/wF/AXsBbwHfAX8BeQFrAUEBLgFjATIBhAE2AYQBNgGEATYBhAE2
        AWQBNgFAASoBdwFnAd8BfwHeAXtwAAHeAXsB/wF7AZwBdwGDAUgBIAFEAYMBSAGDAUgBgwFIAYMBSAFj
        AUgBIAFEASABRAFjAUgBgwFIAYMBSAGDAUgBgwFIASABRAGDAUgBnAF3Af8BewHeAXsEAAH/AX8BnAFz
        Ad8BfwGaAW8BgwEyAYMBMgGEATYBhAE2AYQBNgGEATYBhAE2AYQBNgGDATIBpQE6Ad8BewF8AXMB/wF/
        cAAB3gF7Af8BfwHeAXcBgwFMASABSAGDAUwBgwFMAYMBTAGDAUwBgwFMAYMBTAGDAUwBgwFMAYMBTAGD
        AUwBIAFIAYMBTAHeAXcB/wF/Ad4BewYAAZwBcwH/AX8BmgFvAYIBMgGDATYBpAE6AaQBOgGkAToBpAE6
        AaQBOgGkAToBpAE6AaQBOgGAAS4BLwFTAf8BfwGcAXNyAAHeAXsB/wF/Ab0BdwFiAUwBIAFIAYMBUAGD
        AVABgwFQAYMBUAGDAVABgwFQAYMBUAGDAVABIAFIAWIBTAG9AXcB/wF/Ad4BewYAAd4BewHfAX8BuwFz
        AaIBNgGiATYBxAE6AcQBOgHEAToBxAE6AaIBNgGiATYBxAE6AcQBOgHEAToBpAE6AaIBNgG8AXcB3wF/
        Af8Bf3IAAd4BewH/AXsB3gF7AeYBVAFBAVABgwFQAYMBUAGDAVABgwFQAYMBUAGDAVABQQFQAeYBVAHe
        AXsB/wF7Ad4BewgAAZwBcwHfAX8B5gE+AcIBNgHEAT4BxAE+AcQBPgHEAT4BoAEyAVABVwFQAVsBoQE2
        AcQBPgHEAT4BxAE+AcIBNgHoAUYB/wF/AXwBbwH/AX9wAAH/AX8BvQFzAf8BfwHOAWEBIAFQAYMBVAGD
        AVQBgwFUAYMBVAGDAVQBgwFUASABUAHOAWEB/wF/Ab0BcwH/AX8IAAGcAXMB/wF/AQYBQwHBATYB5QE+
        AeQBPgHkAT4BwAE2ASwBUwH/AX8B/wF/AeQBPgHjAToB5AE+AeQBPgHkAT4BwAE2AXEBXwH/AX8BnAFz
        bgAB/wF/Ab0BcwH/AX8BrQFhAQABUAFjAVQBgwFUAYMBVAGDAVQBgwFUAYMBVAGDAVQBYwFUAQABUAGt
        AWEB/wF/Ab0BcwH/AX8GAAH/AX8B/wF/Ad0BdwHkAT4B4gE6AQQBQwHAATYBKwFTAf8BfwG+AXcB3wF/
        AbgBbwHgATYB5AFCAQQBQwEEAUMB5AE+AeEBOgG6AXMB/wF/Ab0Bd2oAAf8BfwHeAXcB/wF/Ac0BZQEA
        AVABgwFYAYMBWAGDAVgBgwFYAYMBWAGDAVgBgwFYAYMBWAGDAVgBgwFYAQABUAHNAWUB/wF/Ab4BdwH/
        AX8GAAHeAXsB/wF/Ad0BdwHjAUIBwAE2ASoBUwH/AX8B3gF7Af8BfwG9AXcB/wF/AU0BVwHgAToBBAFD
        AQQBQwEEAUMB4gE+AQUBRwH/AX8B3wF/Af8Bf2YAAf8BfwG9AXMB/wF/Ac4BaQEAAVQBgwFYAYMBXAGD
        AVwBgwFcAYMBXAFiAVgBYgFYAYMBXAGDAVwBgwFcAYMBXAGDAVgBAAFUAc4BaQH/AX8BvQFzAf8BfwYA
        Ad4BewH/AX8B3QF7AZYBawH/AX8B3wF/Af8BfwIAAf8BfwHeAXsB/wF/AQMBQwECAUMBBAFHAQQBRwEE
        AUcB4AE+ASoBUwH/AX8BvQF3Af8Bf2IAAf8BfwG9AXcB/wF/AawBaQEAAVgBgwFcAYMBXAGDAVwBgwFc
        AYMBXAEgAVwBxgFgAeYBYAEgAVwBgwFcAYMBXAGDAVwBgwFcAYMBXAEAAVgBrAFpAf8BfwG9AXcB/wF/
        BgAB3gF7Ab4BewH/AX8BvQF3Af8BfwYAAd4BewH/AX8BuAFvAeABPgEDAUcBAwFHAQMBRwEDAUcB4AE+
        AUwBVwH/AX8BvQF3Af8Bf2AAAZwBcwH/AX8BDwFuAQABWAGDAWABgwFgAYMBYAGDAWABgwFgASABXAGD
        AWAB/wF/Af8BfwFiAWABIAFcAYMBYAGDAWABgwFgAYMBYAGDAWABAAFYAQ8BbgH/AX8BnAFzGAABvQF3
        Af8BfwFuAV8B4AE+AQMBRwEDAUcBAwFHAQMBRwHgAT4BbwFfAf8BfwGcAXNgAAGcAXMB/wF/AUEBYAFB
        AWABgwFgAYMBYAGDAWABgwFgASABYAGDAWABvQF7Af8BfwH/AX8BvQF7AYMBYAEgAWABgwFgAYMBYAGD
        AWABgwFgAUEBYAFBAWAB/wF/AZwBcxoAAb0BdwH/AX8BSAFTAQABQwEDAUsBAwFLAQMBSwEAAUMBAAFH
        Af8BfwGcAXNgAAG9AXcB/wF/Ae8BbQEAAWABgwFkAYMBZAGDAWQBIAFgAYMBZAHeAX8B/wF/Ad4BewHe
        AXsB/wF/Ad4BfwGDAWQBIAFgAYMBZAGDAWQBgwFkAQABYAEPAW4B/wF/Ab0BdxoAAf8BfwH/AX8B/wF/
        ASIBSwEAAUcBIgFLAQABRwEAAUcBtgFvAf8BfwHeAXtgAAH/AX8B3gF7Af8BfwGMAW0BAAFkAWIBZAEg
        AWQBYgFkAd4BfwH/AX8B3gF7BAAB3gF7Af8BfwG9AX8BYQFkASABZAFiAWQBAAFkAYwBbQH/AX8B3gF7
        Af8BfxwAAd4BewH/AX8B/AF7ASIBTwEAAUMBSAFXAf0BewH/AX8BvQF3ZAAB/wF/Ad4BewH/AX8BzgFx
        AQABZAGEAWgBvQF/Af8BfwHeAXsIAAHeAXsB/wF/Ab0BfwGkAWgBAAFkAc4BcQH/AX8B3gF7Af8BfyAA
        Ad4BewH/AX8B/wF/AdsBdwH/AX8B/wF/Ad4Be2gAAf8BfwHeAXsB/wF/Ab0BfwH/AX8B/wF/Ad4BewwA
        Ad4BewH/AX8B/wF/Ab0BfwH/AX8B3gF7Af8BfyQAAd4BewHeAXsB/wF/Ab0Bd3AAAb0BdwG9AXcBvQF3
        Ad4BexAAAd4BewG9AXcBvQF3Ab0BdyoAAf8BfwH/AX8B/wF/aAABQgFNAT4HAAE+AwABKAMAAWADAAEY
        AwABAQEAAQEFAAEgAQEWAAP/AQAB4AH/AQcB/wGPAf8GAAHAAX4BAwH/AYcB/wYAAYABPAEBAf4BAwH/
        BwABGAEAAfwBAQH/CQAB+AEBAf8JAAHwAQAB/wkAAeABAAH/CQABwAEAAX8GAAGAAQABAQGAAQABPwYA
        AcABAAEDAYABAAE/BgAB4AEAAQcCAAEfBgAB8AEAAQ8CAAEPBgAB8AEAAQ8CAAEPBgAB4AEAAQcCAAEH
        BgABwAEAAQMBgAEAAQMGAAGAAQABAQHAAUABAQkAAuAKAAH/AfAKAAH/AfgKAAH/AfgIAAEYAQAB/wH8
        AQEGAAGAATwBAQH/Af4BAwYAAcABfgEDAv8BDwYAAfAB/wEPAv8BjwYACw==
</value>
  </data>
  <metadata name="ErrorManager.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
</root>