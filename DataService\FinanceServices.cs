﻿using DataAccess;
using DataService.Finance;
using System;
using System.Data;

namespace DataService
{
    public static class FinanceServices
    {

        public static DataTable GetTableOfStorePayments(
            ref string errormessage,
            DateTime startdate,
            DateTime enddate,
            int? chainid,
            int? headofficeid,
            int? storeid,
            Guid? contractid,
            int? mediaserviceid,
            int? categoryid,
            bool? homesite)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.LegacyConnectionString))
            {
                var command = new GetTableOfStorePaymentsCommand
                    (startdate, enddate, chainid, headofficeid, storeid, contractid, mediaserviceid, categoryid, homesite);
                manager.ExecuteCommand(ref errormessage, command, new GetTableOfStorePaymentsCommandExecutor());
                return command.Table;
            }
        }

    }
}
