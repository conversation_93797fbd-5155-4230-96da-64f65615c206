Public Class SubformClientsAndBrands

    Private Sub ButtonClients_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonClients.Click
        AddChild(New SubformClientManager)
    End Sub

    Private Sub ButtonBrandFamilies_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonBrandFamilies.Click
        AddChild(New SubformBrandFamilyManager)
    End Sub

    Private Sub ButtonBrands_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonBrands.Click
        AddChild(New SubformBrandManager)
    End Sub

    Private Sub ButtonBrandCategories_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonBrandCategories.Click
        AddChild(New SubformBrandCategoryManager)
    End Sub

    Private Sub SubformClientsAndBrands_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        If My.User.IsInRole("sales_manager") Then
            Panel2.Visible = True
        Else
            Panel2.Visible = False
        End If
    End Sub


End Class
