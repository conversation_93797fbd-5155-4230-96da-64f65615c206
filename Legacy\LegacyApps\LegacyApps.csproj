﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{DDBC9A3A-28B8-451F-829E-0575BB794F9C}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>LegacyApps</RootNamespace>
    <AssemblyName>LegacyApps</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevExpress.Utils.v12.2, Version=12.2.18.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="System" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Data" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AppChooserSurface.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="AppChooserSurface.Designer.cs">
      <DependentUpon>AppChooserSurface.cs</DependentUpon>
    </Compile>
    <Compile Include="Legacy.cs" />
    <Compile Include="LegacyApp.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Framework\Framework.csproj">
      <Project>{6c57b7a1-a6ec-48ea-a1ea-6585b4206e6f}</Project>
      <Name>Framework</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Universal\Universal.csproj">
      <Project>{b6b01178-1f34-4c5d-8601-e666a10f675b}</Project>
      <Name>Universal</Name>
    </ProjectReference>
    <ProjectReference Include="..\Nova2Finance\NovaFinance\NovaFinance.vbproj">
      <Project>{a7f6b4ed-2675-4fbd-a2cb-2b4f6901840b}</Project>
      <Name>NovaFinance</Name>
    </ProjectReference>
    <ProjectReference Include="..\Nova2Ops\Nova2Ops\Nova2Ops.vbproj">
      <Project>{e58c080a-79ee-4e49-8ac6-8515acd9baa7}</Project>
      <Name>Nova2Ops</Name>
    </ProjectReference>
    <ProjectReference Include="..\Nova2\LiquidShell\LiquidShell.vbproj">
      <Project>{3e5c068d-c18e-4b6d-a7d4-50c6c2365f3f}</Project>
      <Name>LiquidShell</Name>
    </ProjectReference>
    <ProjectReference Include="..\Nova2\Nova2\Nova2.vbproj">
      <Project>{683d8b67-d21d-483b-9731-cb2dc28c3c71}</Project>
      <Name>Nova2</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="AppChooserSurface.resx">
      <DependentUpon>AppChooserSurface.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>