﻿Public Class BaseForm

    Public Delegate Sub BackgroundWorkMethod()
    Public Delegate Sub BackgroundWorkCompletedMethod()
    Private BackgroundWork As BackgroundWorkMethod
    Private BackgroundWorkCompleted As BackgroundWorkCompletedMethod
    Public ErrorManager As New LiquidErrorManager
    Private WaitAnimation As Form

#Region "Event Handlers"

    Private Sub Worker_DoWork(ByVal sender As Object, ByVal e As System.ComponentModel.DoWorkEventArgs) Handles Worker.DoWork
        BackgroundWork.Invoke()
    End Sub

    Private Sub Worker_RunWorkerCompleted(ByVal sender As Object, ByVal e As System.ComponentModel.RunWorkerCompletedEventArgs) Handles Worker.RunWorkerCompleted
        If IsNothing(BackgroundWorkCompleted) = False Then
            BackgroundWorkCompleted.Invoke()
        End If
        StopWaitAnimation()
    End Sub

#End Region

#Region "Internal Methods"

    Private Sub StartWaitAnimation()
        WaitAnimation = New FormPleaseWait
        WaitAnimation.ShowDialog()
    End Sub

    Private Sub StopWaitAnimation()
        If IsNothing(WaitAnimation) = False Then
            WaitAnimation.Dispose()
        End If
    End Sub

    Protected Sub Save(ByRef ErrorMessage As String)
        ' Try and save and catch exception details into an error message.
        Try
            Save()
        Catch ex As Exception
            ErrorMessage = LiquidAgent.GetErrorMessage(ex)
        End Try
    End Sub

#End Region

#Region "Protected Methods"

    Public Sub RunInBackground(ByVal AsyncMethod As BackgroundWorkMethod)
        RunInBackground(AsyncMethod, Nothing)
    End Sub

    Public Sub RunInBackground(ByVal AsyncMethod As BackgroundWorkMethod, ByVal UponCompletionMethod As BackgroundWorkCompletedMethod)

        ' Assign the supplied method value to the BackgroundWork variable.
        BackgroundWork = AsyncMethod
        BackgroundWorkCompleted = UponCompletionMethod

        ' Start processing in the background and display the animation.
        If Worker.IsBusy = False Then
            Worker.RunWorkerAsync()
            StartWaitAnimation()
        End If

    End Sub

    Protected Overridable Sub Save()
        ' This method is intended to be overridden.
    End Sub

    Protected Function SaveAndClose() As Boolean

        If ErrorManager.ValidationSuccessful(Me) Then
            ' Control validation was successful.

            ' Create a string to hold error messages resulting from an attempt at a save operation.
            Dim SaveError As String = String.Empty
            ' Try and save.
            Save(SaveError)
            If String.IsNullOrEmpty(SaveError) Then
                ' Save operation was successful.
                DialogResult = Windows.Forms.DialogResult.OK
                Close()
                Return True
            Else
                ' Save operation was unsuccessful.
                ShowMessage(SaveError)
                Return False
            End If

        Else
            Return False
        End If

    End Function

    Public Function SaveAndClose(CloseAfterSaving As Boolean) As Boolean

        If ErrorManager.ValidationSuccessful(Me) Then
            ' Control validation was successful.

            ' Create a string to hold error messages resulting from an attempt at a save operation.
            Dim SaveError As String = String.Empty
            ' Try and save.
            Save(SaveError)
            If String.IsNullOrEmpty(SaveError) Then
                ' Save operation was successful.
                DialogResult = Windows.Forms.DialogResult.OK
                If CloseAfterSaving Then
                    Close()
                End If
                Return True
            Else
                ' Save operation was unsuccessful.
                ShowMessage(SaveError)
                Return False
            End If

        Else
            Return False
        End If

    End Function

    Protected Shared Sub AddLog _
        (ByVal AuditedRow As DataRow, _
        ByVal RowDescription As String, _
        ByVal ChangedPropertyName As String, _
        ByVal ChangedPropertyOldValue As String, _
        ByVal ChangedPropertyNewValue As String, _
        ByVal AuditLog As DataTable)
        ' Add an entry into the audit log table for a modified row.

        ' Create a string builder to build the log description.
        Dim ActionBuilder As New System.Text.StringBuilder("Changed the value of " & ChangedPropertyName.ToUpper & " from ")

        ' Add the old value to the string builder.
        If String.IsNullOrEmpty(ChangedPropertyOldValue) Then
            ActionBuilder.Append("an empty string")
        Else
            ActionBuilder.Append("'" & ChangedPropertyOldValue & "'")
        End If
        ActionBuilder.Append(" to ")

        ' Add the new value to the string builder.
        If String.IsNullOrEmpty(ChangedPropertyNewValue) Then
            ActionBuilder.Append("an empty string")
        Else
            ActionBuilder.Append("'" & ChangedPropertyNewValue & "'")
        End If

        ' Log the entry.
        LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, AuditedRow.Table.TableName, RowDescription, ActionBuilder.ToString)

    End Sub

    Protected Function SelectedRowCountValidated(Grid As DataGridView, ButtonClicked As SimpleButton) As Boolean

        ' Verify that only one row is selected.
        If Grid.SelectedRows.Count = 0 Then
            ShowMessage("Please select a row before using the '" & ButtonClicked.Text & "' button.", "Select A Row")
            Return False
        ElseIf Grid.SelectedRows.Count > 1 AndAlso String.Compare(ButtonClicked.Text, "Edit") = 0 Then
            ShowMessage("Please select only one row before using the '" & ButtonClicked.Text & "' button.", "Select Only One Row")
            Return False
        Else
            Return True
        End If

    End Function

#End Region

#Region "ShowMessage"

    ' Define the look and feel of the form to use in message boxes.
    Private DefaultLookAndFeel As DevExpress.LookAndFeel.UserLookAndFeel = Me.LookAndFeel

    Public Function ShowMessage(ByVal Text As String) As DialogResult
        Return ShowMessage(Text, "Attention", MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
    End Function

    Public Function ShowMessage(ByVal Text As String, ByVal Caption As String) As DialogResult
        Return ShowMessage(Text, Caption, MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
    End Function

    Public Function ShowMessage(ByVal Text As String, ByVal Icon As MessageBoxIcon) As DialogResult
        Return ShowMessage(Text, "Attention", MessageBoxButtons.OK, Icon)
    End Function

    Public Function ShowMessage(ByVal Text As String, ByVal Buttons As MessageBoxButtons) As DialogResult
        Return ShowMessage(Text, "Attention", Buttons, MessageBoxIcon.Exclamation)
    End Function

    Public Function ShowMessage(ByVal Text As String, ByVal Caption As String, ByVal Buttons As MessageBoxButtons) As DialogResult
        Return ShowMessage(Text, Caption, Buttons, MessageBoxIcon.Exclamation)
    End Function

    Public Function ShowMessage(ByVal Text As String, ByVal Caption As String, ByVal Icon As MessageBoxIcon) As DialogResult
        Return ShowMessage(Text, Caption, MessageBoxButtons.OK, Icon)
    End Function

    Public Function ShowMessage _
    (ByVal Text As String, _
    ByVal Caption As String, _
    ByVal Buttons As MessageBoxButtons, _
    ByVal Icon As MessageBoxIcon) _
    As DialogResult
        Return DevExpress.XtraEditors.XtraMessageBox.Show(DefaultLookAndFeel, Text, Caption, Buttons, Icon)
    End Function

#End Region

End Class