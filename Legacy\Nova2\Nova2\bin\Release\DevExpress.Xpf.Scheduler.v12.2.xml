<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DevExpress.Xpf.Scheduler.v12.2</name>
    </assembly>
    <members>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualWeekViewGroupByDate">

            <summary>
                <para>Provides information on visual elements displayed within the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> when Week View is a currently active view and appointments are grouped by dates.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualWeekViewGroupByDate.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualWeekViewGroupByDate class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualWeekViewGroupByDate.DayOfWeekHeaders">
            <summary>
                <para>Provides access to the collection of objects containing the information on the visual representation of day of week headers.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.Drawing.VisualDayOfWeekHeaderContentCollection"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualWeekViewGroupByDate.DayOfWeekHeadersProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualVerticalAppointmentControl">

            <summary>
                <para>Provides information on the visual representation of an appointment displayed vertically in the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualVerticalAppointmentControl.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualVerticalAppointmentControl class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualHorizontalAppointmentControl">

            <summary>
                <para>Provides information on the visual representation of an appointment displayed horizontally in the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualHorizontalAppointmentControl.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualHorizontalAppointmentControl class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditor">

            <summary>
                <para>Represents an in-place editor.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditor.#ctor(DevExpress.Xpf.Scheduler.SchedulerControl,DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Initializes a new instance of the AppointmentInplaceEditor class with the specified appointment and scheduler control.
</para>
            </summary>
            <param name="control">
		A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> object which represents the <b>Scheduler</b> control containing the appointment to be edited via the in-place editor.


            </param>
            <param name="apt">
		A <see cref="T:DevExpress.XtraScheduler.Appointment"/> object which represents an appointment to be edited via the in-place editor.


            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditor.#ctor">
            <summary>
                <para>Initializes a new instance of the AppointmentInplaceEditor class with the default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditor.Activate">
            <summary>
                <para>Initializes the control, subscribes to its major events and sets the focus to it.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditor.InitializeComponent">
            <summary>
                <para>Initializes the AppointmentInplaceEditor from XAML.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualTimelineViewGroupByNone">

            <summary>
                <para>Provides information on visual elements displayed within the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>, when Timeline View is the currently active view and no grouping is applied to appointments.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualTimelineViewGroupByNone.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualTimelineViewGroupByNone class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualTimelineViewGroupByDate">

            <summary>
                <para>Provides information on visual elements displayed within the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>, when Timeline View is the currently active view and appointments are grouped by dates or resources.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualTimelineViewGroupByDate.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualTimelineViewGroupByDate class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualTimelineViewGroupBase">

            <summary>
                <para>Services as a base for classes that provide information on visual elements displayed in the Timeline View, depending on a type of grouping currently applied to appointments.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualTimelineViewGroupBase.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualTimelineViewGroupBase class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualTimelineViewGroupBase.Header">
            <summary>
                <para>Gets or sets the information on the visual representation of the Timeline View's header.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.Drawing.VisualTimelineHeader"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualTimelineViewGroupBase.HeaderProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualTimelineViewGroupBase.SelectionBarContainer">
            <summary>
                <para>Gets or sets the information on the visual representation of the selection bar displayed in the Timeline View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.Drawing.VisualTimelineSelectionBar"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualTimelineViewGroupBase.SelectionBarContainerProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualMonthViewGroupByResource">

            <summary>
                <para>Provides information on visual elements displayed within the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>, when Month View is the currently active view and appointments are grouped by resources.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualMonthViewGroupByResource.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualMonthViewGroupByResource class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualMonthViewGroupByNone">

            <summary>
                <para>Provides information on visual elements displayed within the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>, when Month View is the currently active view and no grouping is applied to appointments.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualMonthViewGroupByNone.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualMonthViewGroupByNone class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualMonthViewGroupByNone.DayOfWeekHeaders">
            <summary>
                <para>Provides access to the collection of objects containing information on the visual representation of day of week headers.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.Drawing.VisualDayOfWeekHeaderContentCollection"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualMonthViewGroupByNone.DayOfWeekHeadersProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualMonthViewGroupByDate">

            <summary>
                <para>Provides information on visual elements displayed within the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>, when Month View is the currently active view and appointments are grouped by dates.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualMonthViewGroupByDate.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualMonthViewGroupByDate class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewResourcesBasedViewInfo">

            <summary>
                <para>Serves as a base for classes that provide information on the visual representation of the Day or Work-Week view's elements displayed in the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>, based on resources.

</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewResourcesBasedViewInfo.AllDayAreaContainerGroups">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>The VisualResourceAllDayAreaContainerGroupCollection object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewResourcesBasedViewInfo.AllDayAreaContainerGroupsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewResourcesBasedViewInfo.DayViewScrollViewerName">
            <summary>
                <para>The name of the <see cref="T:DevExpress.Xpf.Scheduler.Drawing.DayViewScrollViewer"/> control.

</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewResourcesBasedViewInfo.MoreButtonsVisibility">
            <summary>
                <para>Specifies the display state of more buttons.
</para>
            </summary>
            <value>The <see cref="T:System.Windows.Visibility"/> enumeration member.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewResourcesBasedViewInfo.MoreButtonsVisibilityProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewResourcesBasedViewInfo.OnApplyTemplate">
            <summary>
                <para>Called after the template is completely generated and attached to the visual tree.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewResourcesBasedViewInfo.ShowDayHeaders">
            <summary>
                <para>Specifies whether to display day headers in the Day or Work-Week view when appointments are grouped by resources or no grouping is applied.
</para>
            </summary>
            <value><b>true</b> to display day headers; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewResourcesBasedViewInfo.ShowDayHeadersProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewResourcesBasedViewInfo.ShowTimeRulerHeader">
            <summary>
                <para>Specifies whether to display time ruler in the Day or Work-Week view when appointments are grouped by resources or no grouping is applied.
</para>
            </summary>
            <value><b>true</b> to display time ruler headers; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewResourcesBasedViewInfo.ShowTimeRulerHeaderProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewResourcesBasedViewInfo.ShowTimeRulers">
            <summary>
                <para>Specifies whether to display time rulers in the Day or Work-Week view when appointments are grouped by resources or no grouping is applied.
</para>
            </summary>
            <value><b>true</b> if the time rulers are displayed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewResourcesBasedViewInfo.ShowTimeRulersProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewResourcesBasedViewInfo.TimeRulers">
            <summary>
                <para>Gets the collection of objects providing information on the visual representation of time rulers displayed in the Day View or Work-Week View when appointments are grouped by resources or no grouping is applied.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.Drawing.VisualTimeRulerCollection"/> collection.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewResourcesBasedViewInfo.TimeRulersProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewGroupByResource">

            <summary>
                <para>Provides information on visual elements displayed within the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>, when Day View or Work-Week View is the currently active view and appointments are grouped by resources.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewGroupByResource.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualDayViewGroupByResource class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewGroupByNone">

            <summary>
                <para>Provides information on visual elements displayed within the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>, when the Day View or Work-Week View is the currently active view and no grouping is applied to appointments.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewGroupByNone.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualDayViewGroupByNone class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewGroupByDate">

            <summary>
                <para>Provides information on visual elements displayed within the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>, when Day View or Work-Week View is the currently active view and appointments are grouped by dates. 
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewGroupByDate.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualDayViewGroupByDate class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewGroupByDate.AllDayAreaContainerGroups">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>The VisualDayAllDayAreaContainerCollection object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewGroupByDate.AllDayAreaContainerGroupsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewGroupByDate.DayViewScrollViewerName">
            <summary>
                <para>The name of the <see cref="T:DevExpress.Xpf.Scheduler.Drawing.DayViewScrollViewer"/> control.

</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewGroupByDate.MoreButtonsVisibility">
            <summary>
                <para>Specifies the display state of more buttons.
</para>
            </summary>
            <value>The <see cref="T:System.Windows.Visibility"/> enumeration member.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewGroupByDate.MoreButtonsVisibilityProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewGroupByDate.OnApplyTemplate">
            <summary>
                <para>Called after the template is completely generated and attached to the visual tree.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewGroupByDate.ShowDayHeaders">
            <summary>
                <para>Specifies whether to display day headers in the Day or Work-Week view when appointments are grouped by dates.
</para>
            </summary>
            <value><b>true</b> to display day headers; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewGroupByDate.ShowDayHeadersProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewGroupByDate.ShowTimeRulerHeader">
            <summary>
                <para>Specifies whether to display time ruler in the Day or Work-Week view when appointments are grouped by dates.
</para>
            </summary>
            <value><b>true</b> to display time ruler headers; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewGroupByDate.ShowTimeRulerHeaderProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewGroupByDate.ShowTimeRulers">
            <summary>
                <para>Obtains whether time rulers are displayed in the Day or Work-Week view when appointments are grouped by dates.
</para>
            </summary>
            <value><b>true</b> if time rulers are displayed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewGroupByDate.ShowTimeRulersProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewGroupByDate.TimeRulers">
            <summary>
                <para>Gets the collection of objects providing information on the visual representation of time rulers displayed in the Day View or Work-Week View when appointments are grouped by dates.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.Drawing.VisualTimeRulerCollection"/> collection.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewGroupByDate.TimeRulersProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualIntervalsBasedViewInfo">

            <summary>
                <para>Serves as a base for classes that provide information on the visual representation of the view's elements displayed in the Scheduler Control, based on time intervals.

</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualIntervalsBasedViewInfo.Intervals">
            <summary>
                <para>Gets the collection of objects containing information on the visual representation on time intervals that are used as the base for displaying data in a Scheduler Control.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.Drawing.VisualIntervalsCollection"/> collection.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualIntervalsBasedViewInfo.IntervalsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualViewInfoBase">

            <summary>
                <para>Services as a base for classes that provide information on visual elements displayed in the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>, depending on the current active view and type of grouping currently applied to appointments.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualViewInfoBase.Control">
            <summary>
                <para>Gets information on the <b>Scheduler</b> control associated with the current object.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> object providing information on the <b>Scheduler</b> control.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualViewInfoBase.ControlProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualViewInfoBase.View">
            <summary>
                <para>Gets the current object's view information.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerViewBase"/> object, providing information on the view.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualViewInfoBase.ViewProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualResourceHeaderBase">

            <summary>
                <para>Serves as a base for classes providing information on the visual representation of resource headers.

</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualAppointmentControl">

            <summary>
                <para>Serves as a base for classes containing information on the visual representation of appointments.


</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualAppointmentControl.CreateCloneForDrag">
            <summary>
                <para>Returns a copy of the current VisualAppointmentControl object for the dragged appointment.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.Xpf.Scheduler.Drawing.VisualAppointmentControl"/> object which is a copy of the current object.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualAppointmentControl.GetAppointment">
            <summary>
                <para>Returns an object representing the appointment.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraScheduler.Appointment"/> object containing information on the appointment.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualAppointmentControl.LayoutViewInfo">
            <summary>
                <para>Gets or sets the information on the appointment layout.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.Drawing.VisualLayoutViewInfo"/> object containing information used to specify the appointment layout.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualAppointmentControl.LayoutViewInfoProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualAppointmentControl.OnApplyTemplate">
            <summary>
                <para>Called after the template is completely generated and attached to the visual tree.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualAppointmentControl.StatusDisplayType">
            <summary>
                <para>Gets or sets whether the border of an appointment should be colorized according to the time status and appointment duration.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.AppointmentStatusDisplayType"/> enumeration value specifying how the status is displayed.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualAppointmentControl.StatusDisplayTypeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualAppointmentControl.StyleSelector">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Controls.StyleSelector"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualAppointmentControl.StyleSelectorProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualAppointmentControl.ViewInfo">
            <summary>
                <para>Gets or sets the view information for the appointment.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.Drawing.VisualAppointmentViewInfo"/> object containing information on the appointment's visual representation. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualAppointmentControl.ViewInfoProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualWorkTimeCellBase">

            <summary>
                <para>Serves as a base for classes containing information on the visual representation of time cells, displayed in the working area of Day View, Work-Week View and Timeline View.
</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualResourceCellBase">

            <summary>
                <para>Serves as a base for classes containing information on the visual representation of elements associated with resources (time cells, day headers, resource headers).
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualResourceCellBase.BackgroundBrushSelector">
            <summary>
                <para>Gets or sets the background color of the visual element.  
</para>
            </summary>
            <value>An object implementing the <see cref="T:DevExpress.Xpf.Scheduler.Drawing.ICellBrushSelector"/> interface.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualResourceCellBase.BackgroundBrushSelectorProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualResourceCellBase.BorderBrushSelector">
            <summary>
                <para>Gets or sets the color of the visual element border.
</para>
            </summary>
            <value>An object implementing the <see cref="T:DevExpress.Xpf.Scheduler.Drawing.ICellBrushSelector"/> interface.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualResourceCellBase.BorderBrushSelectorProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualResourceCellBase.ContentElement">
            <summary>
                <para>Gets the element under the "PART_CONTENT" name in the visual tree of the template.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.UIElement"/> object. <b>null</b> (<b>Nothing</b> in Visual Basic) if no element under the "PART_CONTENT" name exists.


</value>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualResourceCellBase.CopyFrom(DevExpress.Xpf.Scheduler.Drawing.ResourceCellBase)">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="source">
		A <see cref="T:DevExpress.Xpf.Scheduler.Drawing.ResourceCellBase"/> object.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualResourceCellBase.OnApplyTemplate">
            <summary>
                <para>Called after the template is completely generated and attached to the visual tree.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualTimeCellBase">

            <summary>
                <para>Serves as a base for classes containing information on the visual representation of time cells, displayed in the working area of the <b>Scheduler</b> control's views.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualTimeCellBase.AppointmentPadding">
            <summary>
                <para>Gets or sets the amount of space in pixels between the time cell borders and appointments located within it.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Thickness"/> object specifying padding distances. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualTimeCellBase.AppointmentPaddingProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualTimeCellBase.GetCellContent">
            <summary>
                <para>Gets the element under the "PART_CONTENT" name in the visual tree of the template.
</para>
            </summary>
            <returns>A <see cref="T:System.Windows.FrameworkElement"/> object. <b>null</b> (<b>Nothing</b> in Visual Basic) if no element under the "PART_CONTENT" name exists.
</returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualNavigationButton">

            <summary>
                <para>Provides information on the visual representation of a navigation button.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualNavigationButton.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualNavigationButton class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualNavigationButton.ButtonInfo">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.Drawing.NavigationButtonViewModel"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualNavigationButton.ButtonInfoProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualNavigationButton.Direction">
            <summary>
                <para>Gets or sets a value specifying the direction of arrows within Navigation Buttons.
</para>
            </summary>
            <value> A <see cref="T:DevExpress.Xpf.Scheduler.Drawing.NavigationDirection"/> enumeration value.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualNavigationButton.DirectionProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualNavigationButton.GetNextNavigationButtonMargin(System.Windows.DependencyObject)">
            <summary>
                <para>Gets the value of the <see cref="P:DevExpress.Xpf.Scheduler.Drawing.VisualNavigationButton.NextNavigationButtonMargin"/> attached property for a specified <see cref="T:System.Windows.DependencyObject"/>.
</para>
            </summary>
            <param name="element">
		The object from which the property value is read.

            </param>
            <returns>The <see cref="T:System.Windows.Thickness"/> property value for the object.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualNavigationButton.GetPrevNavigationButtonMargin(System.Windows.DependencyObject)">
            <summary>
                <para>Gets the value of the <see cref="P:DevExpress.Xpf.Scheduler.Drawing.VisualNavigationButton.PrevNavigationButtonMargin"/> attached property for a specified <see cref="T:System.Windows.DependencyObject"/>.
</para>
            </summary>
            <param name="element">
		The object from which the property value is read.

            </param>
            <returns>The <see cref="T:System.Windows.Thickness"/> property value for the object.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualNavigationButton.Interval">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeInterval"/> object. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualNavigationButton.IntervalProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualNavigationButton.NextNavigationButtonMargin(System.Windows.DependencyObject)">
            <summary>
                <para>Gets or sets the thickness of a frame around a <b>Next Appointment</b> navigation button.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Thickness"/> value.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualNavigationButton.NextNavigationButtonMarginProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualNavigationButton.OnApplyTemplate">
            <summary>
                <para>Called after the template is completely generated and attached to the visual tree.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualNavigationButton.PrevNavigationButtonMargin(System.Windows.DependencyObject)">
            <summary>
                <para>Gets or sets the thickness of a frame around a <b>Previous Appointment</b> navigation button.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Thickness"/> value.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualNavigationButton.PrevNavigationButtonMarginProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualNavigationButton.Resource">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Resource"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualNavigationButton.ResourceProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualNavigationButton.SetNextNavigationButtonMargin(System.Windows.DependencyObject,System.Windows.Thickness)">
            <summary>
                <para>Sets the value of the <see cref="P:DevExpress.Xpf.Scheduler.Drawing.VisualNavigationButton.NextNavigationButtonMargin"/> attached property to a specified <see cref="T:System.Windows.DependencyObject"/>.
</para>
            </summary>
            <param name="element">
		The object to which the attached property is written.

            </param>
            <param name="value">
		The required <see cref="T:System.Windows.Thickness"/> value.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualNavigationButton.SetPrevNavigationButtonMargin(System.Windows.DependencyObject,System.Windows.Thickness)">
            <summary>
                <para>Sets the value of the <see cref="P:DevExpress.Xpf.Scheduler.Drawing.VisualNavigationButton.PrevNavigationButtonMargin"/> attached property to a specified <see cref="T:System.Windows.DependencyObject"/>.
</para>
            </summary>
            <param name="element">
		The object to which the attached property is written.

            </param>
            <param name="value">
		The required <see cref="T:System.Windows.Thickness"/> value.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualResourceHeader">

            <summary>
                <para>Provides information on the visual representation of a resource header.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualResourceHeader.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualResourceHeader class.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualDayOfWeekHeader">

            <summary>
                <para>Provides information on the visual representation of a day of week header.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualDayOfWeekHeader.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualDayOfWeekHeader class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualDayOfWeekHeader.CopyFrom(DevExpress.Xpf.Scheduler.Drawing.DayOfWeekHeader)">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="source">
		A <see cref="T:DevExpress.Xpf.Scheduler.Drawing.DayOfWeekHeader"/> object.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualDateCellHeader">

            <summary>
                <para>Provides information on the visual representation of a day header displayed in the Week View or Month View.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualDateCellHeader.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualDateCellHeader class.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualTimeScaleHeader">

            <summary>
                <para>Provides information on the visual representation of day headers, for time scales displayed in the Timeline View.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualTimeScaleHeader.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualTimeScaleHeader class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualTimeScaleHeader.EndOffset">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.Native.SingleTimelineHeaderCellOffset"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualTimeScaleHeader.EndOffsetProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualTimeScaleHeader.StartOffset">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.Native.SingleTimelineHeaderCellOffset"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualTimeScaleHeader.StartOffsetProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualDateHeader">

            <summary>
                <para>Provides information on the visual representation of a day header displayed in the Day View or Work-Week View.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualDateHeader.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualDateHeader class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualDateHeader.DisableResourceColor">
            <summary>
                <para>Indicates whether the resource's color should be used to paint a day header.
</para>
            </summary>
            <value><b>true</b> if the color associated with the resource will not be used to paint the day header; otherwise <b>false</b>.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualDateHeader.DisableResourceColorProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualDraggedAppointmentControl">

            <summary>
                <para>Provides information on the visual representation of time cells, over which an appointment is being dragged.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualDraggedAppointmentControl.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualDraggedAppointmentControl class.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualDateCell">

            <summary>
                <para>Serves as a base for classes providing information on the visual representation of date cells displayed in the Week and Month view, and day headers of the Day, Work-Week, <b>Week</b> and <b>Month</b> views.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualDateCell.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualDateCell class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualDateCell.AlternateTemplate">
            <summary>
                <para>Gets or sets the template used to define the visual presentation of the today's date header or today's date cell.
 
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Controls.ControlTemplate"/> object that is a template for a date header or date cell corresponding to today's date.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualDateCell.AlternateTemplateProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualDateCell.IsAlternate">
            <summary>
                <para>Gets or sets a value that specifies whether a data header or date cell that corresponds to today's date is highlighted within the <b>Scheduler</b> control.
</para>
            </summary>
            <value><b>true</b> if the today's date header or today's date cell is highlighted; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualDateCell.IsAlternateProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualDateCell.NormalTemplate">
            <summary>
                <para>Gets or sets the template used to define the visual presentation of the date header or date cell, corresponding to dates other than today's date.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Controls.ControlTemplate"/> object that is a template for a date header or date cell.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualDateCell.NormalTemplateProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualSingleTimelineCell">

            <summary>
                <para>Provides information on the visual representation of a time cell displayed in the Timeline View.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualSingleTimelineCell.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualSingleTimelineCell class.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualAllDayAreaCell">

            <summary>
                <para>Provides information on the visual representation of an all-day area cell.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualAllDayAreaCell.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualAllDayAreaCell class.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.UI.YearlyRecurrenceControl">

            <summary>
                <para>Represents a control used to set the recurrence options for yearly recurrent appointments.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.YearlyRecurrenceControl.#ctor">
            <summary>
                <para>Initializes a new instance of the YearlyRecurrenceControl class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.YearlyRecurrenceControl.DayNumber">
            <summary>
                <para>Gets or sets the day of the month when the appointment is scheduled.

</para>
            </summary>
            <value>An integer specifying the day of the month.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.YearlyRecurrenceControl.InitializeComponent">
            <summary>
                <para>Initializes the YearlyRecurrenceControl from XAML.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.YearlyRecurrenceControl.Month">
            <summary>
                <para>Gets or sets the month's number.
</para>
            </summary>
            <value>An integer specifying a month's number.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.YearlyRecurrenceControl.Periodicity">
            <summary>
                <para>Gets or sets the number of years between appointment occurrences.
</para>
            </summary>
            <value>An integer specifying the number of years.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.YearlyRecurrenceControl.RecurrenceInfo">
            <summary>
                <para>Gets or sets an object containing information about recurrences of the appointment.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.RecurrenceInfo"/> object containing information about appointment recurrences.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.YearlyRecurrenceControl.RecurrenceInfoProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.YearlyRecurrenceControl.WeekDays">
            <summary>
                <para>Gets or sets the days of the week to schedule a yearly recurrent appointment.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.WeekDays"/> enumeration member specifying the days of the week for appointment recurrences.

</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.YearlyRecurrenceControl.WeekOfMonth">
            <summary>
                <para>Gets or sets the number of the week in a month when an appointment is scheduled.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.WeekOfMonth"/> enumeration member specifying the week in a month.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.YearlyRecurrenceControl.WeekOfMonthNumber">
            <summary>
                <para>Gets or sets the specified number of the week in a month when the <b>Day of Week</b> type of yearly appointment recurrence is active in the YearlyRecurrenceControl control.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.WeekOfMonth"/> enumeration member.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.YearlyRecurrenceControl.YearlyRecurrenceType">
            <summary>
                <para>Gets or sets an object indicating what type of yearly recurrence is selected in the YearlyRecurrenceControl control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.WeekOfMonth"/> enumeration member.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.YearlyRecurrenceControl.YearlyRecurrenceTypes">
            <summary>
                <para>Gets the collection of objects specifying available types of yearly appointment recurrence.
</para>
            </summary>
            <value>A <see cref="T:System.Collections.IList"/> collection.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.UI.WeeklyRecurrenceControl">

            <summary>
                <para>Represents a control used to set the recurrence options for weekly recurrent appointments.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.WeeklyRecurrenceControl.#ctor">
            <summary>
                <para>Initializes a new instance of the WeeklyRecurrenceControl class with the default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.WeeklyRecurrenceControl.FirstDayOfWeek">
            <summary>
                <para>Gets or sets the day which starts the WeeklyRecurrenceControl control's week.
</para>
            </summary>
            <value>A <see cref="T:System.DayOfWeek"/> enumeration value specifying the first day of the week for the <b>WeeklyRecurrenceControl</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.WeeklyRecurrenceControl.FirstDayOfWeekProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.WeeklyRecurrenceControl.InitializeComponent">
            <summary>
                <para>Initializes the WeeklyRecurrenceControl from XAML.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.WeeklyRecurrenceControl.Periodicity">
            <summary>
                <para>Gets or sets the number of weeks between weeks containing scheduled appointments.
</para>
            </summary>
            <value>An integer specifying the number of weeks between appointment occurrences.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.WeeklyRecurrenceControl.RecurrenceInfo">
            <summary>
                <para>Gets or sets an object containing information about recurrences of the appointment.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.RecurrenceInfo"/> object containing information about appointment recurrences.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.WeeklyRecurrenceControl.RecurrenceInfoProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.WeeklyRecurrenceControl.WeekDays">
            <summary>
                <para>Gets or sets the days of the week to schedule a weekly recurrent appointment.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.WeekDays"/> enumeration member specifying the days of the week for appointment recurrences.

</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.UI.WeekDaysEdit">

            <summary>
                <para>Represents a combo box used to select days of the week.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.WeekDaysEdit.#ctor">
            <summary>
                <para>Initializes a new instance of the WeekDaysEdit class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.WeekDaysEdit.DayOfWeek">
            <summary>
                <para>Gets or sets the value selected in the WeekDaysEdit control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.WeekDays"/> enumeration member, specifying a day of the week or a standard combination of days.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.WeekDaysEdit.DayOfWeekProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.UI.MonthlyRecurrenceControl">

            <summary>
                <para>Represents a control used to set the recurrence options for monthly recurrent appointments.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.MonthlyRecurrenceControl.#ctor">
            <summary>
                <para>Initializes a new instance of the MonthlyRecurrenceControl class with the default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.MonthlyRecurrenceControl.DayNumber">
            <summary>
                <para>Gets or sets the day of the month when the appointment is scheduled.

</para>
            </summary>
            <value>An integer specifying the day of the month.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.MonthlyRecurrenceControl.InitializeComponent">
            <summary>
                <para>Initializes the MonthlyRecurrenceControl from XAML.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.MonthlyRecurrenceControl.MonthlyRecurrenceType">
            <summary>
                <para>Gets or sets an object indicating what type of monthly recurrence is selected in the MonthlyRecurrenceControl control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.WeekOfMonth"/> enumeration member.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.MonthlyRecurrenceControl.MonthlyRecurrenceTypes">
            <summary>
                <para>Gets the collection of objects specifying available types of monthly appointment recurrence.
</para>
            </summary>
            <value>A <see cref="T:System.Collections.IList"/> collection.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.MonthlyRecurrenceControl.Periodicity">
            <summary>
                <para>Gets or sets the number of months between appointment occurrences.
</para>
            </summary>
            <value>An integer specifying the number of months.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.MonthlyRecurrenceControl.RecurrenceInfo">
            <summary>
                <para>Gets or sets an object containing information about recurrences of the appointment. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.RecurrenceInfo"/> object containing information about appointment recurrences.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.MonthlyRecurrenceControl.RecurrenceInfoProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.MonthlyRecurrenceControl.WeekDays">
            <summary>
                <para>Gets or sets the days of the week to schedule a monthly recurrent appointment.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.WeekDays"/> enumeration member specifying the days of the week for appointment recurrences.

</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.MonthlyRecurrenceControl.WeekOfMonth">
            <summary>
                <para>Gets or sets the number of the week in a month when an appointment is scheduled.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.WeekOfMonth"/> enumeration member specifying the week in a month.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.MonthlyRecurrenceControl.WeekOfMonthNumber">
            <summary>
                <para>Gets or sets the specified number of the week in a month when the <b>Day of Week</b> type of monthly appointment recurrence is active in the MonthlyRecurrenceControl control.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.WeekOfMonth"/> enumeration member.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.UI.DailyRecurrenceControl">

            <summary>
                <para>Represents a control used to set the recurrence options for daily recurrent appointments.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.DailyRecurrenceControl.#ctor">
            <summary>
                <para>Initializes a new instance of the DailyRecurrenceControl class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.DailyRecurrenceControl.DailyRecurrenceTypes">
            <summary>
                <para>Gets the collection of objects specifying available types of daily appointment recurrence.
</para>
            </summary>
            <value>A <see cref="T:System.Collections.IList"/> collection.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.DailyRecurrenceControl.InitializeComponent">
            <summary>
                <para>Initializes the DailyRecurrenceControl from XAML.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.DailyRecurrenceControl.Periodicity">
            <summary>
                <para>Gets or sets the number of days between appointment occurrences.
</para>
            </summary>
            <value>An integer specifying the interval between appointment occurrences in days.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.DailyRecurrenceControl.RecurrenceInfo">
            <summary>
                <para>Gets or sets an object containing information about recurrences of the appointment.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.RecurrenceInfo"/> object containing information about appointment recurrences.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.DailyRecurrenceControl.RecurrenceInfoProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.DailyRecurrenceControl.WeekDays">
            <summary>
                <para>Gets or sets the days of the week to schedule a daily recurrent appointment.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.WeekDays"/> enumeration member specifying the days of the week for appointment recurrences.

</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.UI.MonthEdit">

            <summary>
                <para>Represents a combo box used to select a month.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.MonthEdit.#ctor">
            <summary>
                <para>Initializes a new instance of the MonthEdit class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.MonthEdit.Month">
            <summary>
                <para>Gets or sets the number of the selected month.
</para>
            </summary>
            <value>An integer value that is the month number.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.UI.WeekOfMonthEdit">

            <summary>
                <para>Represents a combo box used to select a week of the month.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.WeekOfMonthEdit.#ctor">
            <summary>
                <para>Initializes a new instance of the WeekOfMonthEdit class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.WeekOfMonthEdit.WeekOfMonth">
            <summary>
                <para>Gets or sets the value selected in the WeekOfMonthEdit control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.WeekOfMonth"/> enumeration value specifying a particular week in a month.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.WeekOfMonthEdit.WeekOfMonthProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.UI.WeekDaysCheckEdit">

            <summary>
                <para>Represents a control that allows the selection of days of the week, by checking the corresponding boxes.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.WeekDaysCheckEdit.#ctor">
            <summary>
                <para>Initializes a new instance of the WeekDaysCheckEdit class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.WeekDaysCheckEdit.FirstDayOfWeek">
            <summary>
                <para>Gets or sets the day which starts the WeekDaysCheckEdit control's week.
</para>
            </summary>
            <value>A <see cref="T:System.DayOfWeek"/> enumeration value specifying the first day of the week for the <b>WeekDaysCheckEdit</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.WeekDaysCheckEdit.FirstDayOfWeekProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.WeekDaysCheckEdit.FridayCaption">
            <summary>
                <para>Gets the caption of the check box corresponding to Friday.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value which specifies the text displayed next to the check box associated with Friday.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.WeekDaysCheckEdit.FridayValue">
            <summary>
                <para>Gets or set a value indicating whether the check box corresponding to Friday is selected.
</para>
            </summary>
            <value><b>true</b> if the check box is selected; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.WeekDaysCheckEdit.InitializeComponent">
            <summary>
                <para>Initializes the WeekDaysCheckEdit from XAML.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.WeekDaysCheckEdit.MondayCaption">
            <summary>
                <para>Gets the caption of the check box corresponding to Monday.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value which specifies the text displayed next to the check box associated with Monday.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.WeekDaysCheckEdit.MondayValue">
            <summary>
                <para>Gets or set a value indicating whether the check box corresponding to Monday is selected.
</para>
            </summary>
            <value><b>true</b> if the check box is selected; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.WeekDaysCheckEdit.SaturdayCaption">
            <summary>
                <para>Gets the caption of the check box corresponding to Saturday.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value which specifies the text displayed next to the check box associated with Saturday.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.WeekDaysCheckEdit.SaturdayValue">
            <summary>
                <para>Gets or set a value indicating whether the check box corresponding to Saturday is selected.
</para>
            </summary>
            <value><b>true</b> if the check box is selected; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.WeekDaysCheckEdit.SundayCaption">
            <summary>
                <para>Gets the caption of the check box corresponding to Sunday.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value which specifies the text displayed next to the check box associated with Sunday.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.WeekDaysCheckEdit.SundayValue">
            <summary>
                <para>Gets or set a value indicating whether the check box corresponding to Sunday is selected.
</para>
            </summary>
            <value><b>true</b> if the check box is selected; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.WeekDaysCheckEdit.ThursdayCaption">
            <summary>
                <para>Gets the caption of the check box corresponding to Thursday.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value which specifies the text displayed next to the check box associated with Thursday.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.WeekDaysCheckEdit.ThursdayValue">
            <summary>
                <para>Gets or set a value indicating whether the check box corresponding to Thursday is selected.
</para>
            </summary>
            <value><b>true</b> if the check box is selected; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.WeekDaysCheckEdit.TuesdayCaption">
            <summary>
                <para>Gets the caption of the check box corresponding to Tuesday.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value which specifies the text displayed next to the check box associated with Tuesday.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.WeekDaysCheckEdit.TuesdayValue">
            <summary>
                <para>Gets or set a value indicating whether the check box corresponding to Tuesday is selected.
</para>
            </summary>
            <value><b>true</b> if the check box is selected; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.WeekDaysCheckEdit.UseAbbreviatedDayNames">
            <summary>
                <para>Specifies whether the control displays short names for week days.

</para>
            </summary>
            <value><b>true</b> if the control displays abbreviated names for week days, otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.WeekDaysCheckEdit.WednesdayCaption">
            <summary>
                <para>Gets the caption of the check box corresponding to Wednesday.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value which specifies the text displayed next to the check box associated with Wednesday.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.WeekDaysCheckEdit.WednesdayValue">
            <summary>
                <para>Gets or set a value indicating whether the check box corresponding to Wednesday is selected.
</para>
            </summary>
            <value><b>true</b> if the check box is selected; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.WeekDaysCheckEdit.WeekDays">
            <summary>
                <para>Gets or sets the day of the week or a specific group of days that is selected in the editor.
</para>
            </summary>
            <value>The <see cref="T:DevExpress.XtraScheduler.WeekDays"/> enumeration value specifying the day/days in a week.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.WeekDaysCheckEdit.WeekDaysProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.UI.TimeZoneEdit">

            <summary>
                <para>Represents a combo box used to specify a time zone.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.TimeZoneEdit.#ctor">
            <summary>
                <para>Initializes a new instance of the TimeZoneEdit class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.TimeZoneEdit.TimeZoneId">
            <summary>
                <para>Gets the string identifier of the time zone selected in the control.
</para>
            </summary>
            <value>A string that uniquely identifies a particular time zone and corresponds to the System.TimeZoneInfo.Id property value.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.UI.AppointmentResourcesEdit">

            <summary>
                <para>Represents a popup checked list box control used to select multiple resources in order to assign them to an appointment. It facilitates the creation of custom <b>Edit Appointment</b> forms.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.AppointmentResourcesEdit.#ctor">
            <summary>
                <para>Initializes a new instance of the AppointmentResourcesEdit class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentResourcesEdit.ResourceIds">
            <summary>
                <para>Provides access to the collection of resource identifiers (resource IDs) of the AppointmentResourcesEdit control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.AppointmentResourceIdCollection"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.AppointmentResourcesEdit.ResourceIdsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.UI.AppointmentResourceEdit">

            <summary>
                <para>Represents a combo box control used to select a resource for an appointment. It facilitates the creation of custom <b>Edit Appointment</b> forms.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.AppointmentResourceEdit.#ctor">
            <summary>
                <para>Initializes a new instance of the AppointmentResourceEdit class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentResourceEdit.ShowEmptyResource">
            <summary>
                <para>Gets or sets a value indicating whether the AppointmentResourceEdit control should show empty resources.
</para>
            </summary>
            <value><b>true</b> to show empty resources; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.AppointmentResourceEdit.ShowEmptyResourceProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualTimeCell">

            <summary>
                <para>Provides information on the visual representation of a time cell, displayed in the Day View and Work-Week View, and selection bar.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualTimeCell.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualTimeCell class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.UI.AppointmentLabelEdit">

            <summary>
                <para>Represents a combo box control used to select appointment labels. It facilitates the creation of custom <b>Edit Appointment</b> forms.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.AppointmentLabelEdit.#ctor">
            <summary>
                <para>Initializes a new instance of the AppointmentLabelEdit class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.UI.AppointmentStatusEdit">

            <summary>
                <para>Represents a combo box control used to select appointment statuses. It facilitates the creation of custom <b>Edit Appointment</b> forms.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.AppointmentStatusEdit.#ctor">
            <summary>
                <para>Initializes a new instance of the AppointmentStatusEdit class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.ActiveViewChangingEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.ActiveViewChanging"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.ActiveViewChangingEventHandler.Invoke(System.Object,DevExpress.Xpf.Scheduler.ActiveViewChangingEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.ActiveViewChanging"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> which raised the event.

            </param>
            <param name="e">
		An <see cref="T:DevExpress.Xpf.Scheduler.ActiveViewChangingEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.ActiveViewChangingEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.ActiveViewChanging"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.ActiveViewChangingEventArgs.#ctor(DevExpress.Xpf.Scheduler.SchedulerViewBase,DevExpress.Xpf.Scheduler.SchedulerViewBase)">
            <summary>
                <para>Initializes a new instance of the ActiveViewChangingEventArgs class with the specified settings.
</para>
            </summary>
            <param name="oldView">
		An object of the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerViewBase"/> class descendant. This object represents the previous active  View of the <b>Scheduler</b> control. This value is assigned to the <see cref="P:DevExpress.Xpf.Scheduler.ActiveViewChangingEventArgs.OldView"/> property.

            </param>
            <param name="newView">
		An object of the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerViewBase"/> class descendant. This object represents the new active View of the <b>Scheduler</b> control. This value is assigned to the <see cref="P:DevExpress.Xpf.Scheduler.ActiveViewChangingEventArgs.NewView"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.ActiveViewChangingEventArgs.Cancel">
            <summary>
                <para>Gets or sets a value indicating whether changing of the currently active view should be canceled. 

</para>
            </summary>
            <value><b>true</b> to cancel the changing of the currently active view; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.ActiveViewChangingEventArgs.NewView">
            <summary>
                <para>Gets the new value of the <see cref="P:DevExpress.Xpf.Scheduler.SchedulerControl.ActiveView"/> property.
</para>
            </summary>
            <value>An object of the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerViewBase"/> class descendant.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.ActiveViewChangingEventArgs.OldView">
            <summary>
                <para>Gets the old value of the <see cref="P:DevExpress.Xpf.Scheduler.SchedulerControl.ActiveView"/> property.
</para>
            </summary>
            <value>An object of the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerViewBase"/> class descendant.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.FormShowingEventArgs">

            <summary>
                <para>Provides data for the events which show dialogs invoked by the <b>Scheduler</b> control.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Scheduler.FormShowingEventArgs.AllowResize">
            <summary>
                <para>Gets or sets a value indicating whether end-users are allowed to resize the form.
</para>
            </summary>
            <value><b>true</b> if end-users are allowed to resize the form; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.FormShowingEventArgs.Cancel">
            <summary>
                <para>Gets or sets the value indicating whether to cancel invoking the form.
</para>
            </summary>
            <value><b>true</b> to cancel showing the form; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.FormShowingEventArgs.Form">
            <summary>
                <para>Gets or sets the form which will be invoked.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Controls.UserControl"/> class descendant that represents the form that will be displayed.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.FormShowingEventArgs.SizeToContent">
            <summary>
                <para>This property is obsolete. 
</para>
            </summary>
            <value>A <see cref="T:System.Windows.SizeToContent"/> enumeration value.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.UI.ResourcesPopupCheckedListBoxControl">

            <summary>
                <para>The popup checked list box control used to filter resources within the Scheduler Control.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.ResourcesPopupCheckedListBoxControl.#ctor">
            <summary>
                <para>Initializes a new instance of the ResourcesPopupCheckedListBoxControl class with the default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.UI.ResourcesComboBoxControl">

            <summary>
                <para>Represents the combo box control used to filter resources within the Scheduler Control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.ResourcesComboBoxControl.#ctor">
            <summary>
                <para>Initializes a new instance of the ResourcesComboBoxControl class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.ResourcesComboBoxControl.ShowAllResourcesItem">
            <summary>
                <para>Specifies whether the <b>All</b> item should be shown in the combo box.
</para>
            </summary>
            <value><b>true</b> if the value signifying <b>All</b> is added to the list of the combo box items; otherwise <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.ResourcesComboBoxControl.ShowAllResourcesItemProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.ResourcesComboBoxControl.ShowNoneResourcesItem">
            <summary>
                <para>Specifies whether the <b>None</b> item should be shown in the combo box.
</para>
            </summary>
            <value><b>true</b> if the value signifying <b>None</b> is added to the list of the combo box items; otherwise <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.ResourcesComboBoxControl.ShowNoneResourcesItemProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.UI.ResourcesCheckedListBoxControl">

            <summary>
                <para>Represents the checked list box control used to filter resources within the Scheduler Control.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.ResourcesCheckedListBoxControl.#ctor">
            <summary>
                <para>Initializes a new instance of the ResourcesCheckedListBoxControl class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.ResourcesCheckedListBoxControl.BeginUpdate">
            <summary>
                <para>Locks the ResourcesCheckedListBoxControl, preventing visual updates of the object and its elements until the <b>EndUpdate</b> or <b>CancelUpdate</b> method is called.


</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.ResourcesCheckedListBoxControl.CancelUpdate">
            <summary>
                <para>Unlocks the ResourcesCheckedListBoxControl object after it has been locked by the <b>BeginUpdate</b> method, without causing an immediate visual update.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.ResourcesCheckedListBoxControl.EndUpdate">
            <summary>
                <para>Unlocks the ResourcesCheckedListBoxControl object after a call to the <b>BeginUpdate</b> method and causes an immediate visual update.


</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.ResourcesCheckedListBoxControl.IsUpdateLocked">
            <summary>
                <para>Notifies whether the ResourcesCheckedListBoxControl control is locked for update.
</para>
            </summary>
            <value><b>true</b> if the control is locked; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.ResourcesCheckedListBoxControl.SchedulerControl">
            <summary>
                <para>Gets or sets the <b>Scheduler</b> control which is assigned to the ResourcesCheckedListBoxControl.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> object representing the scheduler whose resources will be filtered by this checked list box.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.ResourcesCheckedListBoxControl.SchedulerControlProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Menu.SchedulerPopupMenu">

            <summary>
                <para>Represents a popup menu of the <b>Scheduler</b> control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Menu.SchedulerPopupMenu.#ctor(DevExpress.Xpf.Scheduler.SchedulerControl)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Scheduler.Menu.SchedulerPopupMenu"/> class with the specified owner scheduler control. 
</para>
            </summary>
            <param name="scheduler">
		A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> value that represents the scheduler control, which hosts this popup menu.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl">

            <summary>
                <para>Represents the Resource Navigator control. 

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.#ctor">
            <summary>
                <para>Initializes a new instance of the ResourceNavigatorControl class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.ButtonDecCountStyle">
            <summary>
                <para>Gets or sets a style applied to the resource navigator button, decreasing the number of resources to be displayed in the <b>Scheduler</b> control.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to the button of the resource navigator.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.ButtonDecCountStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.ButtonFirstStyle">
            <summary>
                <para>Gets or sets a style applied to the <b>First</b> button of the resource navigator displayed in the <b>Scheduler</b> control.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to the button of the resource navigator.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.ButtonFirstStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.ButtonIncCountStyle">
            <summary>
                <para>Gets or sets a style applied to the resource navigator button, increasing the number of resources to be displayed in the <b>Scheduler</b> control.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to the button of the resource navigator.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.ButtonIncCountStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.ButtonLastStyle">
            <summary>
                <para>Gets or sets a style applied to the <b>Last</b> button of the resource navigator displayed in the <b>Scheduler</b> control.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to the button of the resource navigator.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.ButtonLastStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.ButtonNextPageStyle">
            <summary>
                <para>Gets or sets a style applied to the <b>Next Page</b> button of the resource navigator displayed in the <b>Scheduler</b> control.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to the button of the resource navigator.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.ButtonNextPageStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.ButtonNextStyle">
            <summary>
                <para>Gets or sets a style applied to the <b>Next</b> button of the resource navigator displayed in the <b>Scheduler</b> control.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to the button of the resource navigator.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.ButtonNextStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.ButtonPrevPageStyle">
            <summary>
                <para>Gets or sets a style applied to the <b>Previous Page</b> button of the resource navigator displayed in the <b>Scheduler</b> control.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to the button of the resource navigator.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.ButtonPrevPageStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.ButtonPrevStyle">
            <summary>
                <para>Gets or sets a style applied to the <b>Previous</b> button of the resource navigator displayed in the <b>Scheduler</b> control.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to the button of the resource navigator.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.ButtonPrevStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.CommandDecCount">
            <summary>
                <para>Decrements (by one) the number of resources visible simultaneously in the Scheduler Control.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerUICommand"/> object that defines the command.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.CommandDecCountProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.CommandFirst">
            <summary>
                <para>Scrolls to the first resource available in the Scheduler Control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerUICommand"/> object that defines the command.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.CommandFirstProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.CommandIncCount">
            <summary>
                <para>Increments (by one) the number of resources visible simultaneously in the Scheduler Control.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerUICommand"/> object that defines the command.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.CommandIncCountProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.CommandLast">
            <summary>
                <para>Scrolls to the last resource available in the Scheduler Control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerUICommand"/> object that defines the command.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.CommandLastProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.CommandNext">
            <summary>
                <para>Scrolls to the next resource available in the Scheduler Control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerUICommand"/> object that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.CommandNextPage">
            <summary>
                <para>Scrolls to the next <see cref="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.ResourcesPerPage"/> resources available in the Scheduler Control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerUICommand"/> object that defines the command.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.CommandNextPageProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.CommandNextProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.CommandPrev">
            <summary>
                <para>Scrolls to the previous resource available in the Scheduler Control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerUICommand"/> object that defines the command.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.CommandPrevPage">
            <summary>
                <para>Scrolls to the previous <see cref="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.ResourcesPerPage"/> resources available in the Scheduler Control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerUICommand"/> object that defines the command.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.CommandPrevPageProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.CommandPrevProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.FilteredResourcesCount">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.FilteredResourcesCountProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.FirstVisibleResourceIndex">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.FirstVisibleResourceIndexProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.Maximum">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.OnApplyTemplate">
            <summary>
                <para>Called after the template is completely generated and attached to the visual tree.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.ResourcesPerPage">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.ResourcesPerPageProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.SchedulerControl">
            <summary>
                <para>Gets or sets the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> associated with the resource navigator.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> object which specifies the owner of this resource navigator.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.SchedulerControlProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.ScrollBarEnabled">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>A <see cref="T:System.Boolean"/> value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.ResourceNavigatorControl.ViewportSize">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.UI.AppointmentFormController">

            <summary>
                <para>Provides all the settings which are required to edit a particular appointment in a custom <b>Edit Appointment</b> form.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.AppointmentFormController.#ctor(DevExpress.Xpf.Scheduler.SchedulerControl,DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Initializes a new instance of the AppointmentFormController class with the specified appointment and scheduler control.
</para>
            </summary>
            <param name="control">
		A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> object which represents the scheduler control of the appointment form controller.

            </param>
            <param name="apt">
		A <see cref="T:DevExpress.XtraScheduler.Appointment"/> object which represents the appointment of the appointment form controller.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentFormController.AppointmentResource">
            <summary>
                <para>Gets or sets the resource associated with the appointment currently being edited in the form.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Resource"/> object representing the resource of the appointment.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentFormController.AppointmentResourceIds">
            <summary>
                <para>Gets or sets the identifiers of all resources associated with the appointment currently being edited in the form.

</para>
            </summary>
            <value>A <see cref="T:System.Collections.IList"/> object representing the collection of resource IDs.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentFormController.AppointmentResources">
            <summary>
                <para>Gets or sets the collection of resources stored in the data storage of the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>, which contains the appointment currently being edited in the form.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.ResourceBaseCollection"/> object, representing the collection of resources stored in the <b>Scheduler</b> control.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentFormController.Control">
            <summary>
                <para>Gets the Scheduler Control containing the appointment currently being edited in the form.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> object representing the <b>Scheduler</b> control.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentFormController.DisplayEndDate">
            <summary>
                <para>Gets or sets the appointment's end date to be displayed in the editing form.
</para>
            </summary>
            <value>A <see cref="T:System.DateTime"/>  value representing the date component of the appointment's end time.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentFormController.DisplayEndTime">
            <summary>
                <para>Gets or sets the appointment's end time to be displayed in the editing form.
</para>
            </summary>
            <value>A <see cref="T:System.TimeSpan"/>  value representing the time component of the appointment's end time.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentFormController.DisplayStartDate">
            <summary>
                <para>Gets or sets the appointment's start date to be displayed in the editing form.
</para>
            </summary>
            <value>A <see cref="T:System.DateTime"/>  value representing the date component of the appointment's start time.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentFormController.DisplayStartTime">
            <summary>
                <para>Gets or sets the appointment's start time to be displayed in the editing form.
</para>
            </summary>
            <value>A <see cref="T:System.TimeSpan"/>  value representing the time component of the appointment's start time.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentFormController.Label">
            <summary>
                <para>Gets or sets the label associated with the appointment currently being edited in the custom <b>Edit Appointment</b> form.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Scheduler.AppointmentLabel"/> object representing the label of the appointment.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentFormController.NoneString">
            <summary>
                <para>Returns a string which is used in combo-boxes when none of the available combo-box items are selected.

</para>
            </summary>
            <value>A <see cref="T:System.String"/> value. By default, it is set to "(None)" or to another value, according to the currently selected culture.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentFormController.PatternCopy">
            <summary>
                <para>Gets or sets the copy of the pattern appointment currently being edited in the form.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Appointment"/> object representing a copy of the recurrence pattern.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentFormController.PatternRecurrenceInfo">
            <summary>
                <para>Gets the recurrence information of the currently edited pattern appointment's copy.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.RecurrenceInfo"/> object containing information about the appointment's reoccurrences. 
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentFormController.ReminderSpan">
            <summary>
                <para>Gets or sets the string value used to display the <see cref="P:DevExpress.XtraScheduler.UI.AppointmentFormControllerBase.ReminderTimeBeforeStart"/> value in the editing form.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentFormController.ReminderSpans">
            <summary>
                <para>Gets the collection of string values used to display reminder time spans available for selection in the editing form.


</para>
            </summary>
            <value>A <see cref="T:System.Collections.IList"/> collection of string values that can be accessed by index.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentFormController.ShouldShowRecurrence">
            <summary>
                <para>Checks whether the button, which enables an end-user to edit the appointment recurrence, should be visible.
</para>
            </summary>
            <value><b>true</b> to show the recurrence button; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentFormController.Status">
            <summary>
                <para>Gets or sets the status associated with the appointment currently being edited in the custom <b>Edit Appointment</b> form.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Scheduler.AppointmentStatus"/> object, representing the status of the appointment.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentFormController.Storage">
            <summary>
                <para>Gets the Scheduler Storage holding data of the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>, which contains the appointment currently being edited in the form.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerStorage"/> object, which is the data storage of the Scheduler control.

</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentFormController.TimeZoneHelper">
            <summary>
                <para>Provides access to the time zone helper used to display <see cref="T:System.DateTime"/> values in the editing form.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeZoneHelper"/> object representing the time zone helper.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.SchedulerMenuEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.PopupMenuShowing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerMenuEventHandler.Invoke(System.Object,DevExpress.Xpf.Scheduler.SchedulerMenuEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.PopupMenuShowing"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> which raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerMenuEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.SchedulerMenuEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.PopupMenuShowing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerMenuEventArgs.#ctor(DevExpress.Xpf.Scheduler.Menu.SchedulerPopupMenu)">
            <summary>
                <para>Initializes a new instance of the SchedulerMenuEventArgs class with the specified popup menu.
</para>
            </summary>
            <param name="menu">
		A <see cref="T:DevExpress.Xpf.Scheduler.Menu.SchedulerPopupMenu"/> object which represents the popup menu.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerMenuEventArgs.Customizations">
            <summary>
                <para>Provides access to a collection of customizations of the popup menu, customized via the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.PopupMenuShowing"/> event handler.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Bars.BarManagerActionCollection"/> object.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerMenuEventArgs.Menu">
            <summary>
                <para>Gets the popup menu for which the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.PopupMenuShowing"/> event has been raised.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Bars.PopupMenu"/> object, which is the popup menu for the event.

</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.SchedulerSelectionBarOptions">

            <summary>
                <para>Provides options which define how the selection bar in the Timeline View should be painted.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerSelectionBarOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerSelectionBarOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerSelectionBarOptions.Height">
            <summary>
                <para>Gets or sets the height of the selection bar in pixels.
</para>
            </summary>
            <value>An integer value.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerSelectionBarOptions.HeightProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerSelectionBarOptions.Visible">
            <summary>
                <para>Gets or sets a value indicating whether the selection bar is visible.
</para>
            </summary>
            <value><b>true</b> if the selection bar is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerSelectionBarOptions.VisibleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.AppointmentStatusCollection">

            <summary>
                <para>Represents a collection of appointment statuses.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentStatusCollection.#ctor">
            <summary>
                <para>Initializes a new instance of the AppointmentStatusCollection class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.AppointmentLabelCollection">

            <summary>
                <para>Represents a collection of appointment labels.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentLabelCollection.#ctor">
            <summary>
                <para>Initializes a new instance of the AppointmentLabelCollection class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.AppointmentStatus">

            <summary>
                <para>Represents an appointment's availability status.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentStatus.#ctor(DevExpress.XtraScheduler.AppointmentStatusType,System.String,System.String)">
            <summary>
                <para>Initializes a new instance of the AppointmentStatus class with the specified type, display name and menu caption.
</para>
            </summary>
            <param name="type">
		An <see cref="T:DevExpress.XtraScheduler.AppointmentStatusType"/> enumeration value that specifies the type of the appointment status. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.AppointmentStatusBase.Type"/> property.

            </param>
            <param name="displayName">
		A <see cref="T:System.String"/> value which represents the display name of the appointment status. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.DisplayName"/> property.

            </param>
            <param name="menuCaption">
		A <see cref="T:System.String"/> value which represents the menu caption of the appointment status. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.MenuCaption"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentStatus.#ctor(DevExpress.XtraScheduler.AppointmentStatusType,System.Windows.Media.Color,System.String)">
            <summary>
                <para>Initializes a new instance of the AppointmentStatus class with the specified type, color and display name.
</para>
            </summary>
            <param name="type">
		An <see cref="T:DevExpress.XtraScheduler.AppointmentStatusType"/> enumeration value that specifies the type of the appointment status. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.AppointmentStatusBase.Type"/> property.

            </param>
            <param name="color">
		A <see cref="T:System.Windows.Media.Color"/> value that specifies the color of the appointment status. This value is assigned to the <see cref="P:DevExpress.Xpf.Scheduler.AppointmentStatus.Color"/> property.

            </param>
            <param name="displayName">
		A <see cref="T:System.String"/> value which represents the display name of the appointment status. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.DisplayName"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentStatus.#ctor">
            <summary>
                <para>Initializes a new instance of the AppointmentStatus class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentStatus.#ctor(DevExpress.XtraScheduler.AppointmentStatusType,System.String)">
            <summary>
                <para>Initializes a new instance of the AppointmentStatus class with the specified type and display name.
</para>
            </summary>
            <param name="type">
		A <see cref="T:DevExpress.XtraScheduler.AppointmentStatusType"/> enumeration value that specifies the type of the appointment status. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.AppointmentStatusBase.Type"/> property.

            </param>
            <param name="displayName">
		A <see cref="T:System.String"/> value which represents the display name of the appointment status. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.DisplayName"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentStatus.#ctor(DevExpress.XtraScheduler.AppointmentStatusType,System.Windows.Media.Color,System.String,System.String)">
            <summary>
                <para>Initializes a new instance of the AppointmentStatus class with the specified type, color, display name and menu caption.
</para>
            </summary>
            <param name="type">
		A <see cref="T:DevExpress.XtraScheduler.AppointmentStatusType"/> enumeration value that specifies the type of the appointment status. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.AppointmentStatusBase.Type"/> property.

            </param>
            <param name="color">
		A <see cref="T:System.Windows.Media.Color"/> value that specifies the color of the appointment status. This value is assigned to the <see cref="P:DevExpress.Xpf.Scheduler.AppointmentStatus.Color"/> property.

            </param>
            <param name="displayName">
		A <see cref="T:System.String"/> value which represents the display name of the appointment status. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.DisplayName"/> property.

            </param>
            <param name="menuCaption">
		A <see cref="T:System.String"/> value which represents the menu caption of the appointment status. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.MenuCaption"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentStatus.Brush">
            <summary>
                <para>Gets the brush used to fill the appointment status.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Media.Brush"/> object which represents the brush used for drawing an appointment status.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentStatus.Color">
            <summary>
                <para>Gets or sets the color of the appointment status.

</para>
            </summary>
            <value>A <see cref="T:System.Windows.Media.Color"/> value which represents the color used for an appointment status.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentStatus.CreateBitmap(System.Int32,System.Int32)">
            <summary>
                <para>Creates an image with the specified dimensions using the current <see cref="P:DevExpress.Xpf.Scheduler.AppointmentStatus.Brush"/> and <see cref="P:DevExpress.Xpf.Scheduler.AppointmentStatus.Color"/> settings.

</para>
            </summary>
            <param name="width">
		An integer value which specifies the width of the image.

            </param>
            <param name="height">
		An integer value which specifies the height of the image.


            </param>
            <returns>A <see cref="T:System.Drawing.Bitmap"/> object representing the image which corresponds to the current appointment status.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentStatus.CreateInstance(DevExpress.XtraScheduler.AppointmentStatusType)">
            <summary>
                <para>Creates a new instance of the AppointmentStatus class and initializes it with the specified type.

</para>
            </summary>
            <param name="type">
		A <see cref="T:DevExpress.XtraScheduler.AppointmentStatusType"/> enumeration value that specifies the type of the appointment status. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.AppointmentStatusBase.Type"/> property.

            </param>
            <returns>An AppointmentStatus object of the specified type.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentStatus.Empty">
            <summary>
                <para>Returns an empty appointment status.
</para>
            </summary>
            <value>An AppointmentStatus value representing an empty appointment status.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.AppointmentLabel">

            <summary>
                <para>Represents an appointment's identification label.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentLabel.#ctor(System.Windows.Media.Color,System.String)">
            <summary>
                <para>Initializes a new instance of the AppointmentLabel class with the specified color and display name.
</para>
            </summary>
            <param name="color">
		A <see cref="T:System.Windows.Media.Color"/> value that specifies the color of the appointment label. This value is assigned to the <see cref="P:DevExpress.Xpf.Scheduler.AppointmentLabel.Color"/> property.

            </param>
            <param name="displayName">
		A <see cref="T:System.String"/> value that specifies the text of the appointment label. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.DisplayName"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentLabel.#ctor(System.String,System.String)">
            <summary>
                <para>Initializes a new instance of the AppointmentLabel class with the specified display name and menu caption.
</para>
            </summary>
            <param name="displayName">
		A <see cref="T:System.String"/> value that specifies the text of the appointment label. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.DisplayName"/> property.

            </param>
            <param name="menuCaption">
		A <see cref="T:System.String"/> value that specifies the menu caption of the appointment label. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.MenuCaption"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentLabel.#ctor(System.String)">
            <summary>
                <para>Initializes a new instance of the AppointmentLabel class with the specified display name.
</para>
            </summary>
            <param name="displayName">
		A <see cref="T:System.String"/> value that specifies the text of the appointment label. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.DisplayName"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentLabel.#ctor(System.Windows.Media.Color,System.String,System.String)">
            <summary>
                <para>Initializes a new instance of the AppointmentLabel class with the specified color, display name, and menu caption.
</para>
            </summary>
            <param name="color">
		A <see cref="T:System.Windows.Media.Color"/> value that specifies the color of the appointment label. This value is assigned to the <see cref="P:DevExpress.Xpf.Scheduler.AppointmentLabel.Color"/> property.

            </param>
            <param name="displayName">
		A <see cref="T:System.String"/> value that specifies the text of the appointment label. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.DisplayName"/> property.

            </param>
            <param name="menuCaption">
		A <see cref="T:System.String"/> value that specifies the menu caption of the appointment label. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.UserInterfaceObject.MenuCaption"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentLabel.#ctor">
            <summary>
                <para>Initializes a new instance of the AppointmentLabel class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentLabel.Brush">
            <summary>
                <para>Gets the brush used to fill the appointment label.

</para>
            </summary>
            <value>A <see cref="T:System.Windows.Media.Brush"/> object which represents the brush used for drawing an appointment label.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentLabel.Color">
            <summary>
                <para>Gets or sets the color of the appointment label.

</para>
            </summary>
            <value>A <see cref="T:System.Windows.Media.Color"/> value which represents the color used for an appointment label.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentLabel.CreateBitmap(System.Int32,System.Int32)">
            <summary>
                <para>Creates an image with the specified dimensions using the current <see cref="P:DevExpress.Xpf.Scheduler.AppointmentLabel.Brush"/> and <see cref="P:DevExpress.Xpf.Scheduler.AppointmentLabel.Color"/> settings.

</para>
            </summary>
            <param name="width">
		An integer value which specifies the width of the image.

            </param>
            <param name="height">
		An integer value which specifies the height of the image.


            </param>
            <returns>A <see cref="T:System.Drawing.Bitmap"/> object representing the image which corresponds to the current appointment label.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentLabel.Empty">
            <summary>
                <para>Returns an empty appointment label.
</para>
            </summary>
            <value>An AppointmentLabel value representing an empty appointment label.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.SchedulerOptionsBehavior">

            <summary>
                <para>For internal use only.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerOptionsBehavior.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerOptionsBehavior class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.SchedulerOptionsView">

            <summary>
                <para>For internal use only.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerOptionsView.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerOptionsView class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.SchedulerDayViewAppointmentDisplayOptions">

            <summary>
                <para>Provides options which define how appointments should be displayed when the currently active view is the Day View or Work-Week View.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerDayViewAppointmentDisplayOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerDayViewAppointmentDisplayOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerDayViewAppointmentDisplayOptions.AllDayAppointmentsStatusDisplayType">
            <summary>
                <para>Gets or sets a value specifying how the appointment status should be displayed for all-day appointments.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.AppointmentStatusDisplayType"/> enumeration value specifying how to display the appointment status. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerDayViewAppointmentDisplayOptions.AllDayAppointmentsStatusDisplayTypeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerDayViewAppointmentDisplayOptions.DayViewInnerObject">
            <summary>
                <para>Provides access to options that define how appointments are painted in the DayView.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.DayViewAppointmentDisplayOptions"/> object containing specific options.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.SchedulerWeekViewAppointmentDisplayOptions">

            <summary>
                <para>Provides options which define how appointments should be displayed when the currently active view is the Week View.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerWeekViewAppointmentDisplayOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerWeekViewAppointmentDisplayOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerWeekViewAppointmentDisplayOptions.AppointmentAutoHeight">
            <summary>
                <para>Gets or sets whether an appointment should change its height to fit its text.

</para>
            </summary>
            <value><b>true</b>, if an appointment's height is changed automatically to fit the text; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerWeekViewAppointmentDisplayOptions.AppointmentAutoHeightProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.SchedulerViewRepository">

            <summary>
                <para>Represents the view repository.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerViewRepository.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerViewRepository class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewRepository.DayView">
            <summary>
                <para>Provides access to an object that defines the settings of the scheduler's Day View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.DayView"/> object representing the Day View in the scheduling area.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewRepository.GanttView">
            <summary>
                <para>Reserved for future use.
</para>
            </summary>
            <value>A GanttView object.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewRepository.MonthView">
            <summary>
                <para>Provides access to an object that defines the settings of the scheduler's Month View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.MonthView"/> object representing the Month View in the scheduling area.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewRepository.TimelineView">
            <summary>
                <para>Provides access to an object that defines the settings of the scheduler's Timeline View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.TimelineView"/> object representing the Timeline View in the scheduling area.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewRepository.WeekView">
            <summary>
                <para>Provides access to an object that defines the settings of the scheduler's Week View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.WeekView"/> object representing the Week View in the scheduling area.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewRepository.WorkWeekView">
            <summary>
                <para>Gets an object that defines the settings of the scheduler's Work-Week View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.WorkWeekView"/> object representing the Work-Week View in the scheduling area.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.SchedulerMonthViewAppointmentDisplayOptions">

            <summary>
                <para>Provides options which define how appointments should be displayed when the currently active view is the Month (Multi-Week) View.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerMonthViewAppointmentDisplayOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerMonthViewAppointmentDisplayOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerMonthViewAppointmentDisplayOptions.AppointmentAutoHeight">
            <summary>
                <para>Gets or sets whether an appointment should change its height to fit its text.

</para>
            </summary>
            <value><b>true</b>, if an appointment's height is changed automatically to fit the text; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerMonthViewAppointmentDisplayOptions.AppointmentAutoHeightProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.SchedulerTimelineViewAppointmentDisplayOptions">

            <summary>
                <para>Provides options which define how appointments should be displayed when the currently active view is the Timeline View.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerTimelineViewAppointmentDisplayOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerTimelineViewAppointmentDisplayOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerTimelineViewAppointmentDisplayOptions.AppointmentAutoHeight">
            <summary>
                <para>Gets or sets whether an appointment should change its height to fit its text.

</para>
            </summary>
            <value><b>true</b>, if an appointment's height is changed automatically to fit the text; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerTimelineViewAppointmentDisplayOptions.AppointmentAutoHeightProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerTimelineViewAppointmentDisplayOptions.TimelineViewInnerObject">
            <summary>
                <para>Provides access to options that define how appointments are painted in the TimelineView.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimelineViewAppointmentDisplayOptions"/> object containing specific options.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.SchedulerAppointmentDisplayOptions">

            <summary>
                <para>Serves as the base for classes which provide options to define how appointments will be displayed when a particular view is currently active.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerAppointmentDisplayOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerAppointmentDisplayOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerAppointmentDisplayOptions.EndTimeVisibility">
            <summary>
                <para>Specifies whether the end time should be visible for the appointment.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.AppointmentTimeVisibility"/> enumeration value, specifying whether the appointment end time should be displayed.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerAppointmentDisplayOptions.EndTimeVisibilityProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerAppointmentDisplayOptions.ShowRecurrence">
            <summary>
                <para>Specify whether the recurrence symbol  should be displayed for a recurrent appointment.
</para>
            </summary>
            <value><b>true</b> if a recurrence symbol should be displayed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerAppointmentDisplayOptions.ShowRecurrenceProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerAppointmentDisplayOptions.ShowReminder">
            <summary>
                <para>Specify whether the reminder symbol  should be displayed for an appointment with a reminder.
</para>
            </summary>
            <value><b>true</b> if a reminder symbol should be displayed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerAppointmentDisplayOptions.ShowReminderProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerAppointmentDisplayOptions.StartTimeVisibility">
            <summary>
                <para>Specifies whether the start time should be visible for the appointment.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.AppointmentTimeVisibility"/> enumeration specifying whether the appointment start time should be displayed.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerAppointmentDisplayOptions.StartTimeVisibilityProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerAppointmentDisplayOptions.StatusDisplayType">
            <summary>
                <para>Specifies whether the border of an appointment should be colorized according to the time status and appointment duration.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.AppointmentStatusDisplayType"/> enumeration value specifying how to display appointment status.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerAppointmentDisplayOptions.StatusDisplayTypeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerAppointmentDisplayOptions.TimeDisplayType">
            <summary>
                <para>Specifies how the start and end time of the appointment should be displayed - using icons or digits.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.AppointmentTimeDisplayType"/> enumeration value specifying how to display the appointment time.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerAppointmentDisplayOptions.TimeDisplayTypeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.EditRecurrentAppointmentFormEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.EditRecurrentAppointmentFormShowing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.EditRecurrentAppointmentFormEventHandler.Invoke(System.Object,DevExpress.Xpf.Scheduler.EditAppointmentFormEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.EditRecurrentAppointmentFormShowing"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> which raised the event.

            </param>
            <param name="e">
		An <see cref="T:DevExpress.Xpf.Scheduler.EditAppointmentFormEventArgs"/> object, which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.DeleteRecurrentAppointmentFormEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.DeleteRecurrentAppointmentFormShowing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.DeleteRecurrentAppointmentFormEventHandler.Invoke(System.Object,DevExpress.Xpf.Scheduler.DeleteRecurrentAppointmentFormEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.DeleteRecurrentAppointmentFormShowing"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> which raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Xpf.Scheduler.DeleteRecurrentAppointmentFormEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.DeleteRecurrentAppointmentFormEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.DeleteRecurrentAppointmentFormShowing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.DeleteRecurrentAppointmentFormEventArgs.#ctor(DevExpress.XtraScheduler.AppointmentBaseCollection)">
            <summary>
                <para>Initializes a new instance of the DeleteRecurrentAppointmentFormEventArgs class with the specified appointment.
</para>
            </summary>
            <param name="appointments">
		A <see cref="T:DevExpress.XtraScheduler.AppointmentBaseCollection"/> object which represents a collection of  the event's appointments. This value is assigned to the <see cref="P:DevExpress.Xpf.Scheduler.DeleteRecurrentAppointmentFormEventArgs.Appointments"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DeleteRecurrentAppointmentFormEventArgs.Appointments">
            <summary>
                <para>Gets the collection of appointments to be deleted.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.AppointmentBaseCollection"/> object representing a collection of appointments.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.EditAppointmentFormEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.EditAppointmentFormShowing"/> and <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.EditRecurrentAppointmentFormShowing"/> events.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.EditAppointmentFormEventArgs.#ctor(DevExpress.XtraScheduler.Appointment,System.Boolean)">
            <summary>
                <para>Initializes a new instance of the EditAppointmentFormEventArgs class with the specified settings.
</para>
            </summary>
            <param name="apt">
		A <see cref="T:DevExpress.XtraScheduler.Appointment"/> value which represents the event's appointment. This value is assigned to the <see cref="P:DevExpress.Xpf.Scheduler.EditAppointmentFormEventArgs.Appointment"/> property.

            </param>
            <param name="readOnly">
		A <see cref="T:System.Boolean"/> value which indicates whether the event's appointment is read-only.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.EditAppointmentFormEventArgs.Appointment">
            <summary>
                <para>Gets the appointment for which the form showing event has been raised.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Appointment"/> object representing the appointment currently being processed.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.CustomizeTimeRulerFormEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.CustomizeTimeRulerFormShowing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.CustomizeTimeRulerFormEventHandler.Invoke(System.Object,DevExpress.Xpf.Scheduler.CustomizeTimeRulerFormEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.CustomizeTimeRulerFormShowing"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> which raised the event.

            </param>
            <param name="e">
		An <see cref="T:DevExpress.Xpf.Scheduler.CustomizeTimeRulerFormEventArgs"/> object, which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.CustomizeTimeRulerFormEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.CustomizeTimeRulerFormShowing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.CustomizeTimeRulerFormEventArgs.#ctor(DevExpress.XtraScheduler.TimeRuler)">
            <summary>
                <para>Initializes a new instance of the CustomizeTimeRulerFormEventArgs class with the specified time ruler.
</para>
            </summary>
            <param name="timeRuler">
		A <see cref="T:DevExpress.XtraScheduler.TimeRuler"/> object which represents the time ruler. This value is assigned to the <see cref="P:DevExpress.Xpf.Scheduler.CustomizeTimeRulerFormEventArgs.TimeRuler"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.CustomizeTimeRulerFormEventArgs.TimeRuler">
            <summary>
                <para>Gets the Time Ruler that is customized via the <b>Time Ruler</b> dialog.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeRuler"/> object representing the time ruler.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.AppointmentViewInfoCustomizingEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.AppointmentViewInfoCustomizing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentViewInfoCustomizingEventHandler.Invoke(System.Object,DevExpress.Xpf.Scheduler.AppointmentViewInfoCustomizingEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.AppointmentViewInfoCustomizing"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> which raised the event.

            </param>
            <param name="e">
		An <see cref="T:DevExpress.Xpf.Scheduler.AppointmentViewInfoCustomizingEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.AppointmentFormEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.EditAppointmentFormShowing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentFormEventHandler.Invoke(System.Object,DevExpress.Xpf.Scheduler.EditAppointmentFormEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.EditAppointmentFormShowing"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> which raised the event.

            </param>
            <param name="e">
		An <see cref="T:DevExpress.Xpf.Scheduler.EditAppointmentFormEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.NavigationButtonOptions">

            <summary>
                <para>Provides options which define certain characteristics of navigation buttons.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.NavigationButtonOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the NavigationButtonOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.NavigationButtonOptions.AppointmentSearchInterval">
            <summary>
                <para>Specifies the time span used by navigation buttons to search for the nearest appointments (in days), so it is limited to a reasonable range.
</para>
            </summary>
            <value>A <see cref="T:System.TimeSpan"/> value, specifying the search range.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.NavigationButtonOptions.AppointmentSearchIntervalProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.NavigationButtonOptions.Visibility">
            <summary>
                <para>Specifies whether the navigation buttons are always visible, always hidden or their visibility depends on particular conditions.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.NavigationButtonVisibility"/> enumeration value defining the conditions for the <b>Navigation Buttons</b> being displayed.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.NavigationButtonOptions.VisibilityProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.RemindersFormEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.RemindersFormShowing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.RemindersFormEventHandler.Invoke(System.Object,DevExpress.Xpf.Scheduler.RemindersFormEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.RemindersFormShowing"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> which raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Xpf.Scheduler.RemindersFormEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.RemindersFormEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.RemindersFormShowing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.RemindersFormEventArgs.#ctor(DevExpress.XtraScheduler.ReminderAlertNotificationCollection)">
            <summary>
                <para>Initializes a new instance of the RemindersFormEventArgs class with the specified settings.
</para>
            </summary>
            <param name="alerts">
		A <see cref="T:DevExpress.XtraScheduler.ReminderAlertNotificationCollection"/> value which represents the event's collection of reminder alerts. This value is assigned to the <see cref="P:DevExpress.Xpf.Scheduler.RemindersFormEventArgs.AlertNotifications"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.RemindersFormEventArgs.AlertNotifications">
            <summary>
                <para>Gets a collection of notifications for reminders that are currently triggered and displayed in the <b>Reminders</b> form.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.ReminderAlertNotificationCollection"/> object holding alert notifications.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.RemindersFormEventArgs.Form">
            <summary>
                <para>Gets or sets the form which will be invoked.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.UI.RemindersFormBase"/> class descendant that specifies the form that will be displayed.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.SchedulerOptionsBase`1">

            <summary>
                <para>Serves as the base class for classes representing options of the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>.

</para>
            </summary>

        </member>
        <member name="T:DevExpress.Xpf.Scheduler.WeekViewBase">

            <summary>
                <para>The base class for the Week and Month views of the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>. 

</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Scheduler.WeekViewBase.DayOfWeekHeaderStyle">
            <summary>
                <para>Gets or sets a style of day of week headers displayed in the Week View, when appointments are grouped by date, and in the Month View.

</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to day of week headers.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.WeekViewBase.DayOfWeekHeaderStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.WeekViewBase.HorizontalWeekCellStyle">
            <summary>
                <para>Gets or sets a style of time cells displayed in the Week View when appointments are grouped by date, and always gets or sets the style for the Month View.

</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to time cells.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.WeekViewBase.HorizontalWeekCellStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.WeekViewBase.HorizontalWeekDateHeaderStyle">
            <summary>
                <para>Gets or sets a style of day headers displayed in the Week View when appointments are grouped by date, and always gets or sets the style of the Month View.

</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to day headers.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.WeekViewBase.HorizontalWeekDateHeaderStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.WeekViewBase.MoreButtonStyle">
            <summary>
                <para>Gets or sets a style applied to more buttons displayed in the Week View and Month View.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to "more" buttons.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.WeekViewBase.MoreButtonStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.SchedulerElement">

            <summary>
                <para>The base class for all Views family classes inherited from the <see cref="T:DevExpress.Xpf.Core.DXFrameworkContentElement"/> class.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerElement.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerElement class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerElement.Tag">
            <summary>
                <para>Overrides the  <see cref="P:System.Windows.FrameworkContentElement.Tag"/> property.
</para>
            </summary>
            <value>A <see cref="T:System.Object"/> object.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.WorkWeekView">

            <summary>
                <para>Represents a Work-Week View.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.WorkWeekView.#ctor">
            <summary>
                <para>Initializes a new instance of the WorkWeekView class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.WorkWeekView.ShowFullWeek">
            <summary>
                <para>Gets or sets a value indicating whether the Work-Week View should show all the days of the week.
</para>
            </summary>
            <value><b>true</b> to show the full week; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.WorkWeekView.ShowFullWeekProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.WorkWeekView.Type">
            <summary>
                <para>Gets the view's type.
</para>
            </summary>
            <value>The <see cref="F:DevExpress.XtraScheduler.SchedulerViewType.WorkWeek"/> value.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.GotoDateFormEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.GotoDateFormShowing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.GotoDateFormEventHandler.Invoke(System.Object,DevExpress.Xpf.Scheduler.GotoDateFormEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.GotoDateFormShowing"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> which raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Xpf.Scheduler.GotoDateFormEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.GotoDateFormEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.GotoDateFormShowing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.GotoDateFormEventArgs.#ctor(DevExpress.Xpf.Scheduler.SchedulerViewRepository,System.DateTime,DevExpress.XtraScheduler.SchedulerViewType)">
            <summary>
                <para>Initializes a new instance of the GotoDateFormEventArgs class with the specified repository of views, date and view type.
</para>
            </summary>
            <param name="views">
		A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerViewRepository"/> object that represents the storage of scheduler views shown in the <b>Go To Date</b> dialog. This value is assigned to the <see cref="P:DevExpress.Xpf.Scheduler.GotoDateFormEventArgs.Views"/> property.

            </param>
            <param name="date">
		A <see cref="T:System.DateTime"/> value which represents a date shown in the <b>Go To Date</b> dialog. This value is assigned to the <see cref="P:DevExpress.Xpf.Scheduler.GotoDateFormEventArgs.Date"/> property.

            </param>
            <param name="viewType">
		A <see cref="T:DevExpress.XtraScheduler.SchedulerViewType"/> enumeration value that represents the type of the scheduler view shown in the <b>Go To Date</b> dialog. This value is assigned to the <see cref="P:DevExpress.Xpf.Scheduler.GotoDateFormEventArgs.SchedulerViewType"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.GotoDateFormEventArgs.Date">
            <summary>
                <para>Gets the initial date shown in the <b>Go To Date</b> dialog.
</para>
            </summary>
            <value>A <see cref="T:System.DateTime"/> object that is the date shown in the dialog.

</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.GotoDateFormEventArgs.SchedulerViewType">
            <summary>
                <para>Gets the initial View type shown in the <b>Go To Date</b> dialog.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerViewType"/> enumeration value that is the <b>View</b> type.

</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.GotoDateFormEventArgs.Views">
            <summary>
                <para>Gets the repository of Views that can be selected in the <b>Go To Date</b> dialog.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerViewRepository"/> object representing the storage of <b>Views</b> accessed to be selected in the <b>Go To Date</b> dialog.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.AppointmentStorage">

            <summary>
                <para>Represents a storage which holds a collection of appointments.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentStorage.#ctor(DevExpress.Xpf.Scheduler.SchedulerStorage)">
            <summary>
                <para>Initializes a new instance of the AppointmentStorage class with the specified scheduler storage.
</para>
            </summary>
            <param name="schedulerStorage">
		A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerStorage"/> value that specifies the scheduler storage of the appointment storage.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentStorage.#ctor">
            <summary>
                <para>Initializes a new instance of the AppointmentStorage class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentStorage.Add(DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Appends the specified <see cref="T:DevExpress.XtraScheduler.Appointment"/> object to the collection which can be accessed via the storage's <see cref="P:DevExpress.Xpf.Scheduler.AppointmentStorage.Items"/> property.
</para>
            </summary>
            <param name="obj">
		A <see cref="T:DevExpress.XtraScheduler.Appointment"/> object to be appended to the collection.

            </param>
            <returns>An integer value indicating the position at which the new element has been inserted.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentStorage.AddRange(DevExpress.XtraScheduler.Appointment[])">
            <summary>
                <para>Appends an array of appointments to the storage's collection.
</para>
            </summary>
            <param name="obj">
		An array of <see cref="T:DevExpress.XtraScheduler.Appointment"/> objects to append to the collection.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentStorage.CommitIdToDataSource">
            <summary>
                <para>Gets or sets whether the appointment Id value should be passed to the data source.
</para>
            </summary>
            <value><b>true</b> to pass the <see cref="P:DevExpress.XtraScheduler.Appointment.Id"/> value to the mapped field in the data source; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.AppointmentStorage.CommitIdToDataSourceProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentStorage.Contains(DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Determines whether the storage's collection contains the specified appointment.
</para>
            </summary>
            <param name="obj">
		A <see cref="T:DevExpress.XtraScheduler.Appointment"/> object to be searched in the collection.

            </param>
            <returns><b>true</b> if the collection contains the specified appointment; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentStorage.DateSaving">
            <summary>
                <para>Gets or sets a value indicating how the time of scheduled appointments should be saved.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.DateSavingType"/> enumeration value specifying how the time should be saved. The default is <see cref="F:DevExpress.XtraScheduler.DateSavingType.LocalTime"/>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.AppointmentStorage.DateSavingProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentStorage.GetAppointmentById(System.Object)">
            <summary>
                <para>Gets an appointment by its identifier.
</para>
            </summary>
            <param name="id">
		An object that is the unique identifier of an appointment.

            </param>
            <returns>A <see cref="T:DevExpress.XtraScheduler.Appointment"/> object.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentStorage.IsNewAppointment(DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Determines whether the appointment is already contained either in the appointment storage or in a series of its recurring appointments.
</para>
            </summary>
            <param name="apt">
		<b>true</b> if the appointment isn't contained in this storage or in its recurrent series; otherwise, <b>false</b>.

            </param>
            <returns>A <see cref="T:DevExpress.XtraScheduler.Appointment"/> object which specifies the appointment to be checked.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentStorage.Items">
            <summary>
                <para>Gets the collection of appointments within the storage.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.AppointmentCollection"/> object that contains a collection of appointments.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentStorage.Labels">
            <summary>
                <para>Provides access to the collection of appointment labels.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Scheduler.AppointmentLabelCollection"/> which specifies the collection of appointment labels.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.AppointmentStorage.LabelsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentStorage.LoadFromXml(System.IO.Stream)">
            <summary>
                <para>Loads settings of appointments from the specified stream to the appointment storage.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> descendant from which appointments are loaded. If <b>null</b> (<b>Nothing</b> in Visual Basic), an exception is raised.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentStorage.LoadFromXml(System.String)">
            <summary>
                <para>Loads settings of appointments from the specified XML file to the appointment storage.
</para>
            </summary>
            <param name="fileName">
		A <see cref="T:System.String"/> value specifying the path to the file from which appointments should be loaded. If an empty string is specified, an exception is raised.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentStorage.Mappings">
            <summary>
                <para>Contains information on mappings of standard appointment properties to specific fields in a datasource.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Scheduler.AppointmentMapping"/> object, providing information on the mappings of the appointment properties to the appropriate data fields.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.AppointmentStorage.MappingsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentStorage.Remove(DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Removes the specified <see cref="T:DevExpress.XtraScheduler.Appointment"/> object from the collection.
</para>
            </summary>
            <param name="obj">
		A <see cref="T:DevExpress.XtraScheduler.Appointment"/> object specifying the appointment to remove.


            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentStorage.ResourceSharing">
            <summary>
                <para>Gets or sets a value indicating whether an appointment can be shared between multiple resources.

</para>
            </summary>
            <value><b>true</b> if resource sharing is enabled; otherwise, <b>false</b>.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.AppointmentStorage.ResourceSharingProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentStorage.SaveToXml(System.String)">
            <summary>
                <para>Saves appointments from the storage to an XML file.
</para>
            </summary>
            <param name="fileName">
		A <see cref="T:System.String"/> value specifying the path to the file where appointments should be written. If an empty string is specified, an exception is raised.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentStorage.SaveToXml(System.IO.Stream)">
            <summary>
                <para>Saves appointments from the storage to the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> descendant to which appointments should be written. If <b>null</b> (<b>Nothing</b> in Visual Basic), an exception is raised.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentStorage.SetAppointmentFactory(DevExpress.XtraScheduler.IAppointmentFactory)">
            <summary>
                <para>Assigns the specified appointment factory to the AppointmentStorage.
</para>
            </summary>
            <param name="factory">
		An object implementing the <see cref="T:DevExpress.XtraScheduler.IAppointmentFactory"/> interface which specifies the new appointment factory for the storage.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentStorage.Statuses">
            <summary>
                <para>Provides access to the collection of  appointment statuses.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Scheduler.AppointmentStatusCollection"/> which is the collection of appointment statuses.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.AppointmentStorage.StatusesProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentStorage.SupportsRecurrence">
            <summary>
                <para>Gets whether the information on appointment recurrence is obtained from a datasource.
</para>
            </summary>
            <value><b>true</b> if the information on appointment recurrence is obtained from a datasource; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentStorage.SupportsReminders">
            <summary>
                <para>Gets whether the information on appointment reminders is obtained from a datasource.
</para>
            </summary>
            <value><b>true</b> if the information on appointment reminders is obtained from a datasource; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.ResourceStorage">

            <summary>
                <para>Represents a storage which holds appointment resources.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.ResourceStorage.#ctor">
            <summary>
                <para>Initializes a new instance of the ResourceStorage class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.ResourceStorage.#ctor(DevExpress.Xpf.Scheduler.SchedulerStorage)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Scheduler.ResourceStorage"/> class with the specified scheduler storage.
</para>
            </summary>
            <param name="schedulerStorage">
		A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerStorage"/> value that specifies the scheduler storage of the resource storage.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.ResourceStorage.Add(DevExpress.XtraScheduler.Resource)">
            <summary>
                <para>Appends the specified <see cref="T:DevExpress.XtraScheduler.Resource"/> object to the collection of resources in the storage.
</para>
            </summary>
            <param name="obj">
		A <see cref="T:DevExpress.XtraScheduler.Resource"/> object to be appended to the resource collection.

            </param>
            <returns>An integer value indicating the position into which the new element has been inserted.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.ResourceStorage.AddRange(DevExpress.XtraScheduler.Resource[])">
            <summary>
                <para>Appends an array of resources to the storage's collection.
</para>
            </summary>
            <param name="obj">
		An array of <see cref="T:DevExpress.XtraScheduler.Resource"/> objects to be appended to the resource collection.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.ResourceStorage.ColorSaving">
            <summary>
                <para>Gets or sets the type of format in which to store the color information.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.ColorSavingType"/> enumeration value indicating the type in which the color is stored.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.ResourceStorage.ColorSavingProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.ResourceStorage.Contains(DevExpress.XtraScheduler.Resource)">
            <summary>
                <para>Determines whether the storage contains the specified resource.
</para>
            </summary>
            <param name="obj">
		A <see cref="T:DevExpress.XtraScheduler.Resource"/> object to be searched in the storage.

            </param>
            <returns><b>true</b> if the storage contains the specified resource; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.ResourceStorage.GetResourceById(System.Object)">
            <summary>
                <para>Gets a resource within the collection by its ID value.
</para>
            </summary>
            <param name="resourceId">
		A <see cref="T:System.Object"/> which is the resource ID.


            </param>
            <returns>A <see cref="T:DevExpress.XtraScheduler.Resource"/> object whose <see cref="P:DevExpress.XtraScheduler.Resource.Id"/> is equal to the specified resource ID. If no resource with the specified ID is found, then the <see cref="P:DevExpress.XtraScheduler.Resource.Empty"/> value will be returned.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.ResourceStorage.Items">
            <summary>
                <para>Gets the collection of resources within the storage.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.ResourceCollection"/> object that contains a collection of resources.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.ResourceStorage.LoadFromXml(System.IO.Stream)">
            <summary>
                <para>Loads settings of resources from the specified stream to the resource storage.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> descendant from which resources are loaded. If <b>null</b> (<b>Nothing</b> in Visual Basic), an exception is raised.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.ResourceStorage.LoadFromXml(System.String)">
            <summary>
                <para>Loads settings of resources from the specified XML file to the resource storage.
</para>
            </summary>
            <param name="fileName">
		A <see cref="T:System.String"/> value specifying the path to the file from which resources should be loaded. If an empty string is specified, an exception is raised.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.ResourceStorage.Mappings">
            <summary>
                <para>Contains information on mappings of standard resource properties to appropriate fields in a datasource.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.ResourceMapping"/> object providing functionality for mapping resource properties to appropriate data fields.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.ResourceStorage.MappingsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.ResourceStorage.Remove(DevExpress.XtraScheduler.Resource)">
            <summary>
                <para>Removes the specified <see cref="T:DevExpress.XtraScheduler.Resource"/> object from the storage.
</para>
            </summary>
            <param name="obj">
		A <see cref="T:DevExpress.XtraScheduler.Resource"/> object, specifying the resource to be removed.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.ResourceStorage.SaveToXml(System.String)">
            <summary>
                <para>Saves resources from the resource storage to an XML file.
</para>
            </summary>
            <param name="fileName">
		A <see cref="T:System.String"/> value specifying the path to the file where resources should be written. If an empty string is specified, an exception is raised.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.ResourceStorage.SaveToXml(System.IO.Stream)">
            <summary>
                <para>Saves resources from the resource storage to the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> descendant to which resources should be written. If <b>null</b> (<b>Nothing</b> in Visual Basic), an exception is raised.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.ResourceStorage.SetResourceFactory(DevExpress.XtraScheduler.IResourceFactory)">
            <summary>
                <para>Assigns the specified resource factory to the ResourceStorage.
</para>
            </summary>
            <param name="factory">
		An object implementing the <b>IResourceFactory</b> interface, which specifies the new resource factory for the storage.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.OptionsView">

            <summary>
                <para>Provides view options for the <b>Scheduler</b> control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.OptionsView.#ctor">
            <summary>
                <para>Initializes a new instance of the OptionsView class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.OptionsView.FirstDayOfWeek">
            <summary>
                <para>Gets or sets the value indicating the week day, from which weeks should start in the current view. This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.FirstDayOfWeek"/> enumeration value specifying the start day of the week.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.OptionsView.FirstDayOfWeekProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.OptionsView.NavigationButtonOptions">
            <summary>
                <para>Provides access to the navigation buttons options.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.NavigationButtonOptions"/> class, containing options for displaying navigation buttons.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.OptionsView.NavigationButtonOptionsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.OptionsView.ShowOnlyResourceAppointments">
            <summary>
                <para>Gets or sets a value indicating whether to show appointments that do not belong to any resource in the current scheduling area.
</para>
            </summary>
            <value><b>true</b> to display only the appointments that are associated with resources; <b>false</b> to display all appointments.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.OptionsView.ShowOnlyResourceAppointmentsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.OptionsCustomization">

            <summary>
                <para>Provides customization options for the <b>Scheduler</b> control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.OptionsCustomization.#ctor">
            <summary>
                <para>Initializes a new instance of the OptionsCustomization class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowAppointmentConflicts">
            <summary>
                <para>Gets or sets a value that specifies whether an end-user is allowed to share the schedule time between two or more appointments.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.AppointmentConflictsMode"/> enumeration value specifying whether the time interval of two or more appointments can intersect or not, if these appointments belong to the same resource(s).
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowAppointmentConflictsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowAppointmentCopy">
            <summary>
                <para>Gets or sets a value that specifies whether an end-user is allowed to copy appointments.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.UsedAppointmentType"/> enumeration value that specifies the type of appointment to which the action can be applied.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowAppointmentCopyProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowAppointmentCreate">
            <summary>
                <para>Gets or sets a value that specifies whether an end-user is allowed to create new appointments.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.UsedAppointmentType"/> enumeration value that specifies the type of appointment which the action can be applied to.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowAppointmentCreateProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowAppointmentDelete">
            <summary>
                <para>Gets or sets a value that specifies whether an end-user is allowed to delete appointments.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.UsedAppointmentType"/> enumeration value that specifies the types of appointment to which the action can be applied.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowAppointmentDeleteProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowAppointmentDrag">
            <summary>
                <para>Gets or sets a value that specifies whether an end-user is allowed to drag and drop appointments to another time slot or date.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.UsedAppointmentType"/> enumeration value that specifies the type of appointment which the action can be applied to.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowAppointmentDragBetweenResources">
            <summary>
                <para>Gets or sets a value that specifies whether an end-user is allowed to drag and drop appointments between resources.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.UsedAppointmentType"/> enumeration value that specifies the type of appointment that the action can be applied to.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowAppointmentDragBetweenResourcesProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowAppointmentDragProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowAppointmentEdit">
            <summary>
                <para>Gets or sets a value that specifies whether an end-user is allowed to edit appointments.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.UsedAppointmentType"/> enumeration value that specifies the type of appointment which the action can be applied to.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowAppointmentEditProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowAppointmentMultiSelect">
            <summary>
                <para>Gets or sets a value that specifies whether an end-user is allowed to select more than one appointment simultaneously.
</para>
            </summary>
            <value><b>true</b> if multiple appointments can be selected simultaneously; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowAppointmentMultiSelectProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowAppointmentResize">
            <summary>
                <para>Gets or set a value that specifies whether an end-user is allowed to change the time boundaries of appointments.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.UsedAppointmentType"/> enumeration value that specifies to which appointment's type the action can be applied.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowAppointmentResizeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowDisplayAppointmentForm">
            <summary>
                <para>Gets or sets the option specifying whether the <b>Edit Appointment </b> dialog can be invoked.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.AllowDisplayAppointmentForm"/> enumeration member.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowDisplayAppointmentFormProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowInplaceEditor">
            <summary>
                <para>Gets or sets a value that specifies whether an inplace editor can be activated for an appointment.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.UsedAppointmentType"/> enumeration value that specifies the type of appointment which the action can be applied to.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowInplaceEditorProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.OptionsBehavior">

            <summary>
                <para>Provides behavior options for the <b>Scheduler</b> control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.OptionsBehavior.#ctor">
            <summary>
                <para>Initializes a new instance of the OptionsBehavior class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.OptionsBehavior.ClientTimeZoneId">
            <summary>
                <para>Gets or sets the identifier of the time zone used by the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>.
</para>
            </summary>
            <value>A string that uniquely identifies a particular time zone and corresponds to the System.TimeZoneInfo.Id property value.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.OptionsBehavior.ClientTimeZoneIdProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.OptionsBehavior.RecurrentAppointmentDeleteAction">
            <summary>
                <para>Gets or sets a type of action being performed when a command is issued to delete a recurrent appointment.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.RecurrentAppointmentAction"/> enumeration value that is the type of an action.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.OptionsBehavior.RecurrentAppointmentDeleteActionProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.OptionsBehavior.RecurrentAppointmentEditAction">
            <summary>
                <para>Gets or sets a type of action being performed when a command is issued to edit a recurrent appointment.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.RecurrentAppointmentAction"/> enumeration value that specifies an action type.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.OptionsBehavior.RecurrentAppointmentEditActionProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.OptionsBehavior.RemindersFormDefaultAction">
            <summary>
                <para>Gets or sets the type of default action which is applied when the <b>Reminders</b> form is closed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.RemindersFormDefaultAction"/> enumeration value specifying the type of action to be applied after the <b>Reminders</b> form is closed.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.OptionsBehavior.RemindersFormDefaultActionProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.OptionsBehavior.SelectOnRightClick">
            <summary>
                <para>Specifies whether the right-click should select a time cell under the cursor.
</para>
            </summary>
            <value><b>true</b> to select a time cell with a right-click; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.OptionsBehavior.SelectOnRightClickProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.OptionsBehavior.ShowRemindersForm">
            <summary>
                <para>Gets or sets whether a <b>Reminders</b> form is shown, when the time has come for the reminder to alert.
</para>
            </summary>
            <value><b>true</b> if a <b>Reminders</b> form is shown; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.OptionsBehavior.ShowRemindersFormProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.WeekView">

            <summary>
                <para>Represents a Week View.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.WeekView.#ctor">
            <summary>
                <para>Initializes a new instance of the WeekView class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.WeekView.AppointmentDisplayOptions">
            <summary>
                <para>Provides access to the options specifying how appointments are displayed in a Week View.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerWeekViewAppointmentDisplayOptions"/> object containing options for displaying appointments.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.WeekView.AppointmentDisplayOptionsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.WeekView.DeferredScrolling">
            <summary>
                <para>Provides access to parameters that control deferred scrolling.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerDeferredScrollingOption"/> instance that specifies parameters for deferred scrolling.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.WeekView.DeferredScrollingProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.WeekView.Type">
            <summary>
                <para>Gets the view's type.
</para>
            </summary>
            <value>The <see cref="F:DevExpress.XtraScheduler.SchedulerViewType.Week"/> value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.WeekView.VerticalWeekCellStyle">
            <summary>
                <para>Gets or sets a style of time cells displayed in the Week View, when appointments are grouped by resources or no grouping is applied.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to time cells.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.WeekView.VerticalWeekCellStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.WeekView.VerticalWeekDateHeaderStyle">
            <summary>
                <para>Gets or sets a style of day headers displayed in the Week View, when appointments are grouped by resources or no grouping is applied.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to day headers.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.WeekView.VerticalWeekDateHeaderStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.TimelineView">

            <summary>
                <para>Represents a Timeline View.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.TimelineView.#ctor">
            <summary>
                <para>Initializes a new instance of the TimelineView class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.TimelineView.AppointmentDisplayOptions">
            <summary>
                <para>Provides access to the options specifying how appointments are displayed in a Timeline View.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerTimelineViewAppointmentDisplayOptions"/> object containing options for displaying appointments.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.TimelineView.AppointmentDisplayOptionsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.TimelineView.CellStyle">
            <summary>
                <para>Gets or sets a style of time cells displayed in the Timeline View.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to time cells.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.TimelineView.CellStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.TimelineView.DateHeaderStyle">
            <summary>
                <para>Gets or sets a style of time scale headers displayed in the Timeline View.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that is a style to be applied to time scale headers.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.TimelineView.DateHeaderStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.TimelineView.DeferredScrolling">
            <summary>
                <para>Provides access to parameters that control deferred scrolling.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerDeferredScrollingOption"/> instance that specifies parameters for deferred scrolling.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.TimelineView.DeferredScrollingProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.TimelineView.GetBaseTimeScale">
            <summary>
                <para>Gets the time scale, which has the minimum time interval among enabled scales.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraScheduler.TimeScale"/> object.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.TimelineView.IntervalCount">
            <summary>
                <para>Gets or sets the number of time intervals displayed in the Timeline View.
</para>
            </summary>
            <value>An integer, specifying how many time intervals are displayed at once.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.TimelineView.IntervalCountProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.TimelineView.MoreButtonStyle">
            <summary>
                <para>Gets or sets a style applied to more buttons displayed in the Timeline View.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to "more" buttons.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.TimelineView.MoreButtonStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.TimelineView.Scales">
            <summary>
                <para>Provides access to a collection of time scales displayed in the Timeline View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeScaleCollection"/> object containing time scales for the Timeline View.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.TimelineView.SelectionBar">
            <summary>
                <para>Provides access to the selection bar options.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerSelectionBarOptions"/> object specifying the appearance of the selection bar.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.TimelineView.SelectionBarCellStyle">
            <summary>
                <para>Gets or sets a style of selection bar displayed in the Timeline View.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to the selection bar.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.TimelineView.SelectionBarCellStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.TimelineView.SelectionBarProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.TimelineView.TimelineScrollBarVisible">
            <summary>
                <para>Specifies whether a vertical row scrollbar is visible, and the vertical scrolling is enabled in the Timeline View.
</para>
            </summary>
            <value><b>true</b> to enable a vertical scrollbar; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.TimelineView.TimelineScrollBarVisibleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.TimelineView.Type">
            <summary>
                <para>Gets the view's type.
</para>
            </summary>
            <value>The <see cref="F:DevExpress.XtraScheduler.SchedulerViewType.Timeline"/> value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.TimelineView.WorkTime">
            <summary>
                <para>Gets or sets the work time interval for the Timeline View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeOfDayInterval"/> value specifying the work time interval.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.TimelineView.WorkTimeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.SchedulerViewBase">

            <summary>
                <para>Serves as a base for classes which represent various Views that can be used to display information within the Scheduler Control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerViewBase.AddAppointmentSelection(DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Selects the specified appointment.
</para>
            </summary>
            <param name="apt">
		A <see cref="T:DevExpress.XtraScheduler.Appointment"/> object that specifies the appointment to be selected.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.AppointmentToolTipContentTemplate">
            <summary>
                <para>Gets or sets the template that defines the visual content of tooltips shown for appointments.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.DataTemplate"/> object that defines the content of appointment tooltips.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerViewBase.AppointmentToolTipContentTemplateProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.AppointmentToolTipVisibility">
            <summary>
                <para>Gets or sets the visibility of the appointment tooltips.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.ToolTipVisibility"/> enumeration value which specifies the visibility of the tooltips.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerViewBase.AppointmentToolTipVisibilityProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerViewBase.ChangeAppointmentSelection(DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Makes the specified appointment the only selected appointment.
</para>
            </summary>
            <param name="apt">
		An <see cref="T:DevExpress.XtraScheduler.Appointment"/> object that specifies the appointment to be selected.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.ContentStyleSelector">
            <summary>
                <para>Gets or sets an object that chooses the view style, depending on the group type selected in the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>.

</para>
            </summary>
            <value>A <see cref="T:System.Windows.Controls.StyleSelector"/> descendant that applies a style based on the group type.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerViewBase.ContentStyleSelectorProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.Control">
            <summary>
                <para>Gets the scheduler control to which the current View belongs. 
</para>
            </summary>
            <value>The <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> object to which the view belongs.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.DisplayName">
            <summary>
                <para>Gets or sets the string displayed to indicate the <b>Scheduler</b> control's view.
</para>
            </summary>
            <value>A string, specifying the view's name. The default is the view's name with the word "Calendar" appended. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerViewBase.DisplayNameProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.DragDropHoverTimeCellsStyle">
            <summary>
                <para>Gets or sets a style of time cells over which an appointment is being dragged. 
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to time cells.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerViewBase.DragDropHoverTimeCellsStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.Enabled">
            <summary>
                <para>Specifies whether the View is enabled for the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>.
</para>
            </summary>
            <value><b>true</b> if the <b>View</b> is enabled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerViewBase.EnabledProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerViewBase.GetAppointments">
            <summary>
                <para>Gets the collection of appointments displayed in the current view.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraScheduler.AppointmentBaseCollection"/>  object, representing a collection of appointments.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerViewBase.GetResources">
            <summary>
                <para>Gets a collection of visible resources for the current view.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraScheduler.ResourceBaseCollection"/>  object, representing a collection of visible resources.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerViewBase.GetVisibleIntervals">
            <summary>
                <para>Returns a copy of the visible time interval collection for the current view.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraScheduler.TimeIntervalCollection"/> object containing the information on visible intervals for the current view.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerViewBase.GotoTimeInterval(DevExpress.XtraScheduler.TimeInterval)">
            <summary>
                <para>Selects the specified time interval and scrolls the View to it, if it is not currently visible.
</para>
            </summary>
            <param name="interval">
		A <see cref="T:DevExpress.XtraScheduler.TimeInterval"/> object that specifies the required time interval.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.GroupType">
            <summary>
                <para>Gets or sets the type of grouping applied to the View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerGroupType"/> enumeration value that specifies how appointments are grouped within the <b>View</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerViewBase.GroupTypeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.HorizontalAppointmentContentTemplate">
            <summary>
                <para>Gets or sets the template that defines the visual content of appointments displayed horizontally in the <b>Scheduler</b> control's Views.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.DataTemplate"/> object that defines the content of appointments.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerViewBase.HorizontalAppointmentContentTemplateProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.HorizontalAppointmentContentTemplateSelector">
            <summary>
                <para>Gets or sets an object that chooses the horizontal appointment content template, based on custom logic.

</para>
            </summary>
            <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that applies a template, based on custom logic.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerViewBase.HorizontalAppointmentContentTemplateSelectorProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.HorizontalAppointmentStyleSelector">
            <summary>
                <para>Gets or sets an object that chooses the horizontal appointment style based on custom logic.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Controls.StyleSelector"/> descendant that applies a style based on custom logic.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerViewBase.HorizontalAppointmentStyleSelectorProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.HorizontalResourceHeaderStyle">
            <summary>
                <para>Gets or sets a style of resource headers displayed horizontally.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to resource headers.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerViewBase.HorizontalResourceHeaderStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerViewBase.LayoutChanged">
            <summary>
                <para>Updates the current View of the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.MenuCaption">
            <summary>
                <para>Gets or sets the menu caption string to indicate the View.
</para>
            </summary>
            <value>A string, representing the menu caption for a scheduler's view.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerViewBase.MenuCaptionProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.MoreButtonToolTipContentTemplate">
            <summary>
                <para>Gets or sets the template that defines the visual content of tooltips shown for more buttons.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.DataTemplate"/> object that defines the content of tooltips.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerViewBase.MoreButtonToolTipContentTemplateProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.NavigationButtonAppointmentSearchInterval">
            <summary>
                <para>Specifies the time span used to search for appointments by Navigation Buttons.
</para>
            </summary>
            <value>A <see cref="T:System.TimeSpan"/> value representing the time interval used by <b>Navigation Buttons</b> to search for appointments.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerViewBase.NavigationButtonAppointmentSearchIntervalProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.NavigationButtonNextStyle">
            <summary>
                <para>Gets or sets a style applied to the <b>Next Appointment</b> navigation button.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to the navigation button.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerViewBase.NavigationButtonNextStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.NavigationButtonPrevStyle">
            <summary>
                <para>Gets or sets a style applied to the <b>Previous Appointment</b> navigation button.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to the navigation button.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerViewBase.NavigationButtonPrevStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.NavigationButtonVisibility">
            <summary>
                <para>Gets or sets the condition for displaying the Navigation Buttons.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.NavigationButtonVisibility"/> enumeration value, which specifies when the <b>Navigation Buttons</b> are visible.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerViewBase.NavigationButtonVisibilityProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerViewBase.PropertyChanged">
            <summary>
                <para>Occurs every time any of the SchedulerViewBase class properties has changed its value.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerViewBase.PropertyChanging">
            <summary>
                <para>Occurs before a value of any of the SchedulerViewBase class properties is changed.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.ResourcesPerPage">
            <summary>
                <para>Gets or sets the number of resources shown at one time on a screen.

</para>
            </summary>
            <value>An integer value which represents the number of resources.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerViewBase.ResourcesPerPageProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerViewBase.ReverseAppointmentSelection(DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Switches the selection status of the specified appointment.
</para>
            </summary>
            <param name="apt">
		A <see cref="T:DevExpress.XtraScheduler.Appointment"/> object that specifies the required appointment.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerViewBase.SelectAppointment(DevExpress.XtraScheduler.Appointment,DevExpress.XtraScheduler.Resource)">
            <summary>
                <para>Makes the specified appointment on the specified resource the only selected appointment within the View, and scrolls to it.
</para>
            </summary>
            <param name="apt">
		A <see cref="T:DevExpress.XtraScheduler.Appointment"/> object that specifies the appointment to be selected.

            </param>
            <param name="resource">
		A <see cref="T:DevExpress.XtraScheduler.Resource"/> object that specifies the resource which contains an appointment to be selected.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerViewBase.SelectAppointment(DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Makes the specified appointment the only selected appointment within the View, and scrolls to it.
</para>
            </summary>
            <param name="apt">
		A <see cref="T:DevExpress.XtraScheduler.Appointment"/> object that specifies the appointment to be selected.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.SelectedInterval">
            <summary>
                <para>Gets the time interval currently selected in the <b>Scheduler</b> control's active view by an end-user.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeInterval"/> object representing the selected time interval.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.SelectedResource">
            <summary>
                <para>Gets the resource which contains the time interval currently selected in the <b>Scheduler</b> control's active view by an end-user.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Resource"/> object representing the selected resource.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.SelectionTemplate">
            <summary>
                <para>Gets or sets the template that defines the presentation of selected time cells.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Controls.ControlTemplate"/> object that represents the template to display selected time cells.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerViewBase.SelectionTemplateProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerViewBase.SetSelection(DevExpress.XtraScheduler.TimeInterval,DevExpress.XtraScheduler.Resource)">
            <summary>
                <para>Makes a specific time interval selected within the view.
</para>
            </summary>
            <param name="interval">
		A <see cref="T:DevExpress.XtraScheduler.TimeInterval"/> object that specifies the time interval to be selected.

            </param>
            <param name="resource">
		A <see cref="T:DevExpress.XtraScheduler.Resource"/> object that specifies to which resource the specified time interval belongs.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerViewBase.SetVisibleIntervals(DevExpress.XtraScheduler.TimeIntervalCollection)">
            <summary>
                <para>Fills the visible time interval collection with new items.
</para>
            </summary>
            <param name="intervals">
		A <see cref="T:DevExpress.XtraScheduler.TimeIntervalCollection"/> object specifying a collection of the SchedulerViewBase visible intervals.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.ShowMoreButtons">
            <summary>
                <para>Gets or sets a value which specifies if the More Buttons should be shown in the current View.
</para>
            </summary>
            <value><b>true</b> if the <b>'More'</b> buttons should be shown; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerViewBase.ShowMoreButtonsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.Type">
            <summary>
                <para>Gets the View's type.
</para>
            </summary>
            <value>One of the <see cref="T:DevExpress.XtraScheduler.SchedulerViewType"/> enumeration's values that specifies the View's type.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.VerticalResourceHeaderStyle">
            <summary>
                <para>Gets or sets a style of resource headers displayed vertically.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to resource headers.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerViewBase.VerticalResourceHeaderStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerViewBase.ViewInfoProperty">
            <summary>
                <para>For internal use only. 
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.VisualViewInfo">
            <summary>
                <para>Gets or sets an object providing view information on elements displayed within the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>, dependent on the current active view, and type of grouping.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.Drawing.VisualViewInfoBase"/> object, providing view information on the <b>Scheduler</b> control's visual elements.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerViewBase.VisualViewInfoProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerViewBase.ZoomIn">
            <summary>
                <para>Performs scaling up to display the view content in more detail.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerViewBase.ZoomOut">
            <summary>
                <para>Performs scaling down to display a broader look of the view.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.SchedulerStorage">

            <summary>
                <para>Represents a storage which holds data for the Scheduler Control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerStorage.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerStorage class with default settings.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.AppointmentChanging">
            <summary>
                <para>Fires when an appointment's property is changing.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.AppointmentCollectionAutoReloading">
            <summary>
                <para>Occurs when the data source which contains appointment records is modified, and appointments are set to be automatically reloaded.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.AppointmentCollectionCleared">
            <summary>
                <para>Fires after the appointment collection has been cleared.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.AppointmentCollectionLoaded">
            <summary>
                <para>Fires after appointments have been loaded into the <see cref="T:DevExpress.XtraScheduler.AppointmentCollection"/> collection.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.AppointmentDeleting">
            <summary>
                <para>Allows you to cancel the deletion of an appointment.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.AppointmentDependenciesChanged">
            <summary>
                <para>Reserved for future use.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.AppointmentDependenciesDeleted">
            <summary>
                <para>Reserved for future use. 
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.AppointmentDependenciesInserted">
            <summary>
                <para>Reserved for future use.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.AppointmentDependencyChanging">
            <summary>
                <para>Reserved for future use. 
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.AppointmentDependencyCollectionAutoReloading">
            <summary>
                <para>Reserved for future use. 
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.AppointmentDependencyCollectionCleared">
            <summary>
                <para>Reserved for future use. 
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.AppointmentDependencyCollectionLoaded">
            <summary>
                <para>Reserved for future use. 
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.AppointmentDependencyDeleting">
            <summary>
                <para>Reserved for future use. 
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.AppointmentDependencyInserting">
            <summary>
                <para>Reserved for future use. 
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerStorage.AppointmentDependencyStorage">
            <summary>
                <para>Reserved for future use.
</para>
            </summary>
            <value>An AppointmentDependencyStorage object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerStorage.AppointmentDependencyStorageProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.AppointmentInserting">
            <summary>
                <para>Allows you to cancel the insertion of an appointment.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.AppointmentsChanged">
            <summary>
                <para>Fires after properties of one or several appointments have been changed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.AppointmentsDeleted">
            <summary>
                <para>Fires after deletion of one or more appointments.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.AppointmentsInserted">
            <summary>
                <para>Fires after one or more appointments have been added to the collection.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerStorage.AppointmentStorage">
            <summary>
                <para>Provides access to a storage that contains information on appointments.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Scheduler.AppointmentStorage"/> object that specifies the storage for appointment related information.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerStorage.AppointmentStorageProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerStorage.BeginInit">
            <summary>
                <para>Starts the scheduler storage initialization. Initialization occurs at runtime.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerStorage.BeginUpdate">
            <summary>
                <para>Locks the SchedulerStorage, preventing visual updates of the object and its elements until the <b>EndUpdate</b> or <b>CancelUpdate</b> method is called.


</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerStorage.CancelUpdate">
            <summary>
                <para>Unlocks the SchedulerStorage object after it has been locked by the <b>BeginUpdate</b> method, without causing an immediate visual update.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerStorage.CreateAppointment(DevExpress.XtraScheduler.AppointmentType)">
            <summary>
                <para>Creates an appointment of the specified type.
</para>
            </summary>
            <param name="type">
		A <see cref="T:DevExpress.XtraScheduler.AppointmentType"/> enumeration value specifying the type of the created appointment.

            </param>
            <returns>A <see cref="T:DevExpress.XtraScheduler.Appointment"/> object, which specifies an appointment of the specified type.

</returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerStorage.CreateAppointmentDependency(System.Object,System.Object)">
            <summary>
                <para>Reserved for future use.
</para>
            </summary>
            <param name="parentId">
		An object that is the Id of the parent appointment.

            </param>
            <param name="dependentId">
		An object that is the Id of the dependent appointment.

            </param>
            <returns>A <see cref="T:DevExpress.XtraScheduler.AppointmentDependency"/> object.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerStorage.CreateResource(System.Object)">
            <summary>
                <para>Creates a new resource with the specified ID.
</para>
            </summary>
            <param name="resourceId">
		An object that specifies a unique resource identifier.

            </param>
            <returns>A <see cref="T:DevExpress.XtraScheduler.Resource"/> object.
</returns>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.DependencyChanging">
            <summary>
                <para>Reserved for future use. 
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerStorage.EnableReminders">
            <summary>
                <para>Gets or sets whether the reminder availability for appointments is enabled.
</para>
            </summary>
            <value><b>true</b> to enable reminders; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerStorage.EndInit">
            <summary>
                <para>Ends the scheduler storage initialization.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerStorage.EndUpdate">
            <summary>
                <para>Unlocks the SchedulerStorage object after a call to the <b>BeginUpdate</b> method and causes an immediate visual update.


</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerStorage.ExportToICalendar(System.String)">
            <summary>
                <para>Saves all appointments to a file in iCalendar format.

</para>
            </summary>
            <param name="path">
		A string that is a file path to which the appointments will be exported.


            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerStorage.ExportToICalendar(System.IO.Stream)">
            <summary>
                <para>Exports appointments in the scheduler to a stream in the iCalendar format.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/>  object which specifies the stream into which the scheduler's data will be exported.

            </param>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.FetchAppointments">
            <summary>
                <para>Occurs before the SchedulerStorage starts retrieving the appointments for the specified time interval. 
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.FilterAppointment">
            <summary>
                <para>Enables specific appointments to be hidden in the Scheduler Control.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.FilterDependency">
            <summary>
                <para>Reserved for future use. 
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.FilterResource">
            <summary>
                <para>Enables specific resources to be hidden in the Scheduler Control.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerStorage.GetAppointments(DevExpress.XtraScheduler.TimeIntervalCollection)">
            <summary>
                <para>Retrieves the collection of appointments that fall within the specified time interval.
</para>
            </summary>
            <param name="intervals">
		A <see cref="T:DevExpress.XtraScheduler.TimeIntervalCollection"/> object, whose <see cref="P:DevExpress.XtraScheduler.TimeIntervalCollection.Start"/> and <see cref="P:DevExpress.XtraScheduler.TimeIntervalCollection.End"/> members identify the required time interval.


            </param>
            <returns>A <see cref="T:DevExpress.XtraScheduler.AppointmentBaseCollection"/> collection of appointments which belong to the specified time interval.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerStorage.GetAppointments(System.DateTime,System.DateTime)">
            <summary>
                <para>Retrieves the collection of appointments that fall within the specified time interval.
</para>
            </summary>
            <param name="start">
		A <see cref="T:System.DateTime"/> value that specifies the start time of the required time interval.

            </param>
            <param name="end">
		A <see cref="T:System.DateTime"/> value that specifies the end time of the required time interval.

            </param>
            <returns>A <see cref="T:DevExpress.XtraScheduler.AppointmentBaseCollection"/> collection of appointments which belong to the specified time interval.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerStorage.GetAppointments(DevExpress.XtraScheduler.TimeInterval)">
            <summary>
                <para>Retrieves the collection of appointments that fall within the specified time interval.
</para>
            </summary>
            <param name="interval">
		A <see cref="T:DevExpress.XtraScheduler.TimeInterval"/> object which contains the required time interval.

            </param>
            <returns>A <see cref="T:DevExpress.XtraScheduler.AppointmentBaseCollection"/> collection of appointments which belong to the specified time interval.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerStorage.GetCoreStorage">
            <summary>
                <para>Gets the <see cref="T:DevExpress.XtraScheduler.SchedulerStorageBase"/> class descendant.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.Xpf.Scheduler.Native.SchedulerDataStorage"/> object.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerStorage.GetLabelColor(System.Int32)">
            <summary>
                <para>Returns the color of the specified label.
</para>
            </summary>
            <param name="labelId">
		A zero-based integer value which identifies a label.

            </param>
            <returns>A <see cref="T:System.Windows.Media.Color"/>  structure, which specifies the label's color.

</returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerStorage.GetObjectRow(DevExpress.XtraScheduler.PersistentObject)">
            <summary>
                <para>Returns the data row object that contains information about the specified persistent object.
</para>
            </summary>
            <param name="obj">
		A <see cref="T:DevExpress.XtraScheduler.PersistentObject"/> descendant instance which specifies the persistent object (an appointment or resource).

            </param>
            <returns>A <see cref="T:System.Object"/> which specifies the data row, containing information about the persistent object.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerStorage.GetObjectValue(DevExpress.XtraScheduler.PersistentObject,System.String)">
            <summary>
                <para>Returns the value of the specified field in the data row that contains information about the persistent object.
</para>
            </summary>
            <param name="obj">
		A <see cref="T:DevExpress.XtraScheduler.PersistentObject"/> descendant instance which specifies the persistent object (an appointment or resource).

            </param>
            <param name="columnName">
		A <see cref="T:System.String"/> which specifies the name of the required data field.

            </param>
            <returns>A <see cref="T:System.Object"/> which specifies the value of the required data field.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerStorage.ImportFromICalendar(System.String)">
            <summary>
                <para>Imports appointments from the file in iCalendar format.
</para>
            </summary>
            <param name="path">
		A string that specifies a file path from which the appointments will be imported.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerStorage.ImportFromICalendar(System.IO.Stream)">
            <summary>
                <para>Imports appointments from the file in iCalendar format.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/>  object which specifies the stream from which the appointment data is imported.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerStorage.IsUpdateLocked">
            <summary>
                <para>Gets whether the storage has been locked for updating.
</para>
            </summary>
            <value><b>true</b> if the storage is locked; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerStorage.RefreshData">
            <summary>
                <para>Updates scheduler control to reflect any changes made in the data sources which store the appointments and appointment resources.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.ReminderAlert">
            <summary>
                <para>Occurs when a reminder alert is invoked.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerStorage.RemindersCheckInterval">
            <summary>
                <para>Gets or sets the time interval between checks of the reminder alert time (in milliseconds).

</para>
            </summary>
            <value>An integer value which specifies the time interval between checks of the reminder alert.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerStorage.RemindersCheckIntervalProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerStorage.RemindersEnabled">
            <summary>
                <para>Gets whether the reminders are enabled.
</para>
            </summary>
            <value><b>true</b> if the reminders are enabled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.ResourceChanging">
            <summary>
                <para>Fires when a resource property is about to be changed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.ResourceCollectionAutoReloading">
            <summary>
                <para>Occurs when the data source which contains resources is modified and the automatic reloading of resources is enabled.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.ResourceCollectionCleared">
            <summary>
                <para>Fires after the resource collection has been cleared.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.ResourceCollectionLoaded">
            <summary>
                <para>Fires after resources have been loaded into the Scheduler Storage.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.ResourceDeleting">
            <summary>
                <para>Allows the deletion of a resource to be cancelled.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.ResourceInserting">
            <summary>
                <para>Allows you to cancel the addition of a resource.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.ResourcesChanged">
            <summary>
                <para>Occurs when a resource or several resources in a collection are changed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.ResourcesDeleted">
            <summary>
                <para>Occurs after a resource or several resources have been deleted.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerStorage.ResourceSharing">
            <summary>
                <para>Gets a value indicating whether an appointment could be shared between multiple resources.
</para>
            </summary>
            <value><b>true</b> if the resource sharing is enabled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerStorage.ResourcesInserted">
            <summary>
                <para>Occurs when new resources have been inserted into the scheduler storage.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerStorage.ResourceStorage">
            <summary>
                <para>Provides access to an object which manages resources for appointments.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.ResourceStorage"/> object which manages resources for appointments.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerStorage.ResourceStorageProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerStorage.SetAppointmentDependencyFactory(DevExpress.XtraScheduler.Native.IAppointmentDependencyFactory)">
            <summary>
                <para>Reserved for future use.
</para>
            </summary>
            <param name="appointmentDependencyFactory">
		An object that implements the IAppointmentDependencyFactory interface.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerStorage.SetAppointmentFactory(DevExpress.XtraScheduler.IAppointmentFactory)">
            <summary>
                <para>Assigns the specified appointment factory to the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerStorage"/>.
</para>
            </summary>
            <param name="appointmentFactory">
		An object implementing the <see cref="T:DevExpress.XtraScheduler.IAppointmentFactory"/> interface which specifies the new appointment factory for the scheduler storage.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerStorage.SetObjectValue(DevExpress.XtraScheduler.PersistentObject,System.String,System.Object)">
            <summary>
                <para>Assigns the specified value to a field in the data row that contains information about the specified persistent object.
</para>
            </summary>
            <param name="obj">
		A <see cref="T:DevExpress.XtraScheduler.PersistentObject"/> class descendant which specifies the persistent object (appointment or resource).

            </param>
            <param name="columnName">
		A <see cref="T:System.String"/> value which specifies the name of the required data field.

            </param>
            <param name="val">
		A <see cref="T:System.Object"/> which specifies the required data field's value.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerStorage.SetResourceFactory(DevExpress.XtraScheduler.IResourceFactory)">
            <summary>
                <para>Assigns the specified resource factory to the SchedulerStorage.
</para>
            </summary>
            <param name="resourceFactory">
		An object, implementing the <b>IResourceFactory</b> interface, which specifies the new resource factory for the storage.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerStorage.SupportsRecurrence">
            <summary>
                <para>Gets whether the information on appointment recurrence is obtained from a data source.
</para>
            </summary>
            <value><b>true</b> if the information on appointment recurrence is obtained from a data source; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerStorage.SupportsReminders">
            <summary>
                <para>Gets whether the information on appointment reminders is obtained from a data source.
</para>
            </summary>
            <value><b>true</b> if the information on appointment reminders is obtained from a data source; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerStorage.TriggerAlerts">
            <summary>
                <para>Invokes reminder alerts for the current time.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerStorage.UnboundMode">
            <summary>
                <para>Gets a value indicating if the scheduler storage is bound to data.

</para>
            </summary>
            <value><b>true</b> if the storage isn't bound to data; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.MonthView">

            <summary>
                <para>Represents a Month (Multi-Week) View.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.MonthView.#ctor">
            <summary>
                <para>Initializes a new instance of the MonthView class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.MonthView.AppointmentDisplayOptions">
            <summary>
                <para>Provides access to the options specifying how appointments are displayed in a Month View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerMonthViewAppointmentDisplayOptions"/> object containing options for displaying appointments.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.MonthView.AppointmentDisplayOptionsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.MonthView.CompressWeekend">
            <summary>
                <para>Gets or sets a value indicating if the weekend days (<b>Saturday</b> and <b>Sunday</b>) should be displayed as one day in a Month View.
</para>
            </summary>
            <value><b>true</b> to compress weekends; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.MonthView.CompressWeekendProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.MonthView.DeferredScrolling">
            <summary>
                <para>Provides access to parameters that control deferred scrolling.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerDeferredScrollingOption"/> instance that specifies parameters for deferred scrolling.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.MonthView.DeferredScrollingProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.MonthView.ShowWeekend">
            <summary>
                <para>Gets or sets a value indicating if the scheduler should also show its data for the weekend days (<b>Saturday</b> and <b>Sunday</b>) in a Month View.
</para>
            </summary>
            <value><b>true</b> to show data for weekend days; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.MonthView.ShowWeekendProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.MonthView.Type">
            <summary>
                <para>Gets the view's type.
</para>
            </summary>
            <value>The <see cref="F:DevExpress.XtraScheduler.SchedulerViewType.Month"/> value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.MonthView.WeekCount">
            <summary>
                <para>Gets or sets the number of weeks that are simultaneously displayed in the Month View.
</para>
            </summary>
            <value>A positive integer value that specifies the number of weeks displayed in the Month View.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.MonthView.WeekCountProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.DayView">

            <summary>
                <para>Represents a Day View.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.DayView.#ctor">
            <summary>
                <para>Initializes a new instance of the DayView class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DayView.AllDayAreaCellStyle">
            <summary>
                <para>Gets or sets a style of all-day area cells displayed in the Day View and Work-Week View.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to time cells.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.DayView.AllDayAreaCellStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DayView.AllDayAreaScrollBarVisible">
            <summary>
                <para>Specifies whether scrolling of the All-Day Area is enabled, and the corresponding scrollbar is visible in the Day View or Work-Week View.

</para>
            </summary>
            <value><b>true</b> if the scrollbar for the all-day area is enabled and visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.DayView.AllDayAreaScrollBarVisibleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DayView.AppointmentDisplayOptions">
            <summary>
                <para>Provides access to the options specifying how appointments are displayed in a Day View.


</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerDayViewAppointmentDisplayOptions"/> object containing options for displaying appointments.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.DayView.AppointmentDisplayOptionsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DayView.CellStyle">
            <summary>
                <para>Gets or sets a style of time cells displayed in the Day View and Work-Week View.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to time cells.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.DayView.CellStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DayView.CurrentTimeIndicatorStyle">
            <summary>
                <para>Gets or sets a style of current time indicator displayed in the Day View and Work-Week View.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to current time indicator.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.DayView.CurrentTimeIndicatorStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DayView.DateHeaderStyle">
            <summary>
                <para>Gets or sets a style of day headers displayed in the Day View and Work-Week View.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to day headers.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.DayView.DateHeaderStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DayView.DayCount">
            <summary>
                <para>Gets or sets the number of days that are simultaneously displayed within the Day View.
</para>
            </summary>
            <value>A positive integer value that specifies the number of days displayed by the Day View.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.DayView.DayCountProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DayView.DefaultTimeRulers">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerTimeRulerCollection"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.DayView.DefaultTimeRulersProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DayView.DefaultTimeSlots">
            <summary>
                <para>Provides access to the Day View's collection of default time slots.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeSlotCollection"/> object that represents a default time slot collection.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.DayView.DefaultTimeSlotsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DayView.DraggedAppointmentHeight">
            <summary>
                <para>Gets or sets the height of the appointment which is currently being dragged in the Day or Work-Week view. 
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value that specifies the dragged appointment height, in pixels.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.DayView.DraggedAppointmentHeightProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DayView.MoreButtonDownStyle">
            <summary>
                <para>Gets or sets a style applied to down arrow buttons displayed in the Day View and Work-Week View.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to "more" buttons.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.DayView.MoreButtonDownStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DayView.MoreButtonUpStyle">
            <summary>
                <para>Gets or sets a style applied to up arrow buttons displayed in the Day View and Work-Week View.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that represents a style to be applied to "more" buttons.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.DayView.MoreButtonUpStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DayView.ShowAllDayArea">
            <summary>
                <para>Gets or sets a value which specifies if the All-Day Area is shown when a Scheduler Control shows its data in the Day View or Work-Week View.
</para>
            </summary>
            <value><b>true</b> to show the <b>All-Day</b> area; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.DayView.ShowAllDayAreaProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DayView.ShowDayHeaders">
            <summary>
                <para>Gets or sets a value which specifies if day headers are shown when a Scheduler Control shows its data in the Day or the Work-Week views.
</para>
            </summary>
            <value><b>true</b> to show the day headers; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.DayView.ShowDayHeadersProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DayView.ShowMoreButtonsOnEachColumn">
            <summary>
                <para>Gets or sets a value indicating whether to show the More Buttons on each column, or only on the Time Ruler in the Day View.
</para>
            </summary>
            <value><b>true</b> to show "more" buttons on each column; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.DayView.ShowMoreButtonsOnEachColumnProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DayView.ShowWorkTimeOnly">
            <summary>
                <para>Gets or sets a value indicating if the Scheduler Control should show its data only for the working hours in a Day View.
</para>
            </summary>
            <value><b>true</b> to show data for working hours only; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.DayView.ShowWorkTimeOnlyProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DayView.TimeRulers">
            <summary>
                <para>Provides access to the View's collection of time rulers.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerTimeRulerCollection"/> object that represents a time ruler collection.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.DayView.TimeRulersProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DayView.TimeScale">
            <summary>
                <para>Gets or sets the time interval for the time slots in the scheduling area.
</para>
            </summary>
            <value>A <see cref="T:System.TimeSpan"/> value representing the time interval for the time slots.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.DayView.TimeScaleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DayView.TimeSlots">
            <summary>
                <para>Gets the Day View's collection of time slots.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeSlotCollection"/> object that represents a time slot collection.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.DayView.TimeSlotsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DayView.TopRowTime">
            <summary>
                <para>Gets or sets the time of the topmost row which is currently shown in the Day or Work-Week view.
</para>
            </summary>
            <value>A <see cref="T:System.TimeSpan"/> value which specifies the time value for the top row.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.DayView.TopRowTimeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DayView.Type">
            <summary>
                <para>Gets the view's type.
</para>
            </summary>
            <value>The <see cref="F:DevExpress.XtraScheduler.SchedulerViewType.Day"/> value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DayView.VerticalAppointmentContentTemplate">
            <summary>
                <para>Gets or sets the template that defines the visual content of appointments displayed vertically in the Day and Work-Week views.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.DataTemplate"/> object that defines the content of appointments.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.DayView.VerticalAppointmentContentTemplateProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DayView.VerticalAppointmentContentTemplateSelector">
            <summary>
                <para>Gets or sets an object that chooses the vertical appointment content template based on custom logic.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that applies a template based on custom logic.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.DayView.VerticalAppointmentContentTemplateSelectorProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DayView.VerticalAppointmentStyleSelector">
            <summary>
                <para>Gets or sets an object that chooses the vertical appointment style based on custom logic.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Controls.StyleSelector"/> descendant that applies a style based on custom logic.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.DayView.VerticalAppointmentStyleSelectorProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DayView.VerticalResourceHeaderStyle">
            <summary>
                <para>Overrides the <see cref="P:DevExpress.Xpf.Scheduler.SchedulerViewBase.VerticalResourceHeaderStyle"/> property.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DayView.VisibleTime">
            <summary>
                <para>Gets or sets the time of the Day or Work-Week view's day interval.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeOfDayInterval"/> object which specifies the time of the view's day interval.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.DayView.VisibleTimeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.DayView.WorkTime">
            <summary>
                <para>Gets or sets the work time interval for a Day View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeOfDayInterval"/> value representing the work time interval.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.DayView.WorkTimeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.SchedulerControl">

            <summary>
                <para>Represents a Scheduler Control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerControl.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerControl class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.ActiveView">
            <summary>
                <para>Provides access to the View currently used by the <b>Scheduler</b> to show its data.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerViewBase"/> class descendant, which represents the currently active view.

</value>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.ActiveViewChanged">
            <summary>
                <para>Occurs after the active View of the <b>Scheduler</b> control has been changed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.ActiveViewChanging">
            <summary>
                <para>Occurs when the <b>Scheduler</b> control is changing its active view.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerControl.ActiveViewProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.ActiveViewType">
            <summary>
                <para>Gets or sets the type of the View which is currently used by the SchedulerControl to show its data.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerViewType"/> enumeration value specifying the active <b>View</b> type.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerControl.ActiveViewTypeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerControl.AddService(System.Type,System.Object)">
            <summary>
                <para>Adds the specified service to the service container.
</para>
            </summary>
            <param name="serviceType">
		The type of service to add.

            </param>
            <param name="serviceInstance">
		An instance of the service type to add. This object must implement or inherit from the type indicated by the serviceType parameter.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerControl.AddService(System.Type,System.ComponentModel.Design.ServiceCreatorCallback,System.Boolean)">
            <summary>
                <para>Adds the specified service to the service container. 
</para>
            </summary>
            <param name="serviceType">
		The type of service to add.

            </param>
            <param name="callback">
		A callback object that is used to create the service. This allows a service to be declared as available, but delays the creation of the object until the service is requested.

            </param>
            <param name="promote">
		<b>true</b> to promote this request to any parent service containers; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerControl.AddService(System.Type,System.ComponentModel.Design.ServiceCreatorCallback)">
            <summary>
                <para>Adds the specified service to the service container.
</para>
            </summary>
            <param name="serviceType">
		The type of service to add.

            </param>
            <param name="callback">
		A callback object that is used to create the service. This allows a service to be declared as available, but delays the creation of the object until the service is requested.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerControl.AddService(System.Type,System.Object,System.Boolean)">
            <summary>
                <para>Adds the specified service to the service container.
</para>
            </summary>
            <param name="serviceType">
		The type of service to add.

            </param>
            <param name="serviceInstance">
		An instance of the service type to add. This object must implement or inherit from the type indicated by the serviceType parameter.

            </param>
            <param name="promote">
		<b>true</b> to promote this request to any parent service containers; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.AllowAppointmentConflicts">
            <summary>
                <para>Occurs when the scheduler finds appointments that are in conflict, and the <see cref="P:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowAppointmentConflicts"/> property is set to <b>Custom</b>.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.AllowAppointmentCopy">
            <summary>
                <para>Occurs when an end-user tries to copy an appointment and the <see cref="P:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowAppointmentCopy"/> property is set to <b>Custom</b>.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.AllowAppointmentCreate">
            <summary>
                <para>Occurs when an end-user tries to create a new appointment and the <see cref="P:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowAppointmentCreate"/> property is set to <b>Custom</b>.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.AllowAppointmentDelete">
            <summary>
                <para>Occurs when an end-user tries to delete an appointment and the <see cref="P:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowAppointmentDelete"/> property is set to <b>Custom</b>.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.AllowAppointmentDrag">
            <summary>
                <para>Occurs when an end-user tries to drag an appointment, and the <see cref="P:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowAppointmentDrag"/> property is set to <b>Custom</b>.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.AllowAppointmentDragBetweenResources">
            <summary>
                <para>Occurs when an end-user tries to drag and drop an appointment between resources and the <see cref="P:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowAppointmentDragBetweenResources"/> property is set to <b>Custom</b>.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.AllowAppointmentEdit">
            <summary>
                <para>Occurs when an end-user tries to edit an appointment and the <see cref="P:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowAppointmentEdit"/> property is set to <b>Custom</b>.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.AllowAppointmentResize">
            <summary>
                <para>Occurs when an end-user tries to resize an appointment and the <see cref="P:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowAppointmentResize"/> property is set to <b>Custom</b>.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.AllowInplaceEditor">
            <summary>
                <para>Occurs when an end-user tries to invoke an appointment's in-place editor, and the <see cref="P:DevExpress.Xpf.Scheduler.OptionsCustomization.AllowInplaceEditor"/> property is set to <b>Custom</b>.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.AppointmentDrag">
            <summary>
                <para>Occurs when an appointment is dragged above the <b>Scheduler</b> control.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.AppointmentDrop">
            <summary>
                <para>Occurs when an appointment is dropped onto the <b>Scheduler</b> control.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.AppointmentMenuCustomizations">
            <summary>
                <para>Allows you to customize an appointment popup menu of the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>, by adding new menu items or removing existing items.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Bars.BarManagerActionCollection"/> object.
</value>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.AppointmentResized">
            <summary>
                <para>Occurs after an end-user modifies an appointment's interval by dragging its border with the mouse.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.AppointmentResizing">
            <summary>
                <para>Occurs when an end-user begins modifying the appointment's interval, by dragging its border with the mouse.

</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.AppointmentViewInfoCustomizing">
            <summary>
                <para>Allows customizing the appointment's appearance by modifying the style elements when it is painted.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.BarManager">
            <summary>
                <para>Gets or sets the main component that provides Bars functionality for the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Bars.BarManager"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerControl.BarManagerProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerControl.BeginInit">
            <summary>
                <para>Starts the <b>Scheduler</b> control's initialization. Initialization occurs at runtime.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerControl.CreateCommand(DevExpress.XtraScheduler.Commands.SchedulerCommandId)">
            <summary>
                <para>Creates a <see cref="T:DevExpress.XtraScheduler.Commands.SchedulerCommand"/> object by the command identifier.
</para>
            </summary>
            <param name="commandId">
		A <see cref="T:DevExpress.XtraScheduler.Commands.SchedulerCommandId"/> structure member, which specifies a command.

            </param>
            <returns>A <see cref="T:DevExpress.XtraScheduler.Commands.SchedulerCommand"/> instance, representing a <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> command.
</returns>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.CustomizeTimeRulerFormShowing">
            <summary>
                <para>Occurs before the <b>Time Ruler</b> form is invoked to customize time ruler settings.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.CustomizeVisualViewInfo">
            <summary>
                <para>Enables to customize the visual representation of the currently active view's elements for the currently selected type of grouping appointments.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.DateNavigatorQueryActiveViewType">
            <summary>
                <para>Enables you to specify the active view type of the Scheduler when the user selects dates in the bound <see cref="T:DevExpress.Xpf.Editors.DateNavigator.DateNavigator"/>.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerControl.DateTimeScrollBarProperty">
            <summary>
                <para>For internal use only.

</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.DayView">
            <summary>
                <para>Provides access to an object that defines the settings of the scheduler's Day View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.DayView"/> object representing the Day View in the scheduling area.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerControl.DayViewProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.DefaultMenuCustomizations">
            <summary>
                <para>Allows you to customize a default popup menu of the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> by adding new menu items or removing existing items.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Bars.BarManagerActionCollection"/> object.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.DefaultResourceColorSchemas">
            <summary>
                <para>For internal use only.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerColorSchemaCollection"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerControl.DefaultResourceColorSchemasProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.DeleteRecurrentAppointmentFormShowing">
            <summary>
                <para>Occurs before the <b>Confirm Delete</b> form is invoked.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.DragDropOptions">
            <summary>
                <para>Provides access to the options specifying the drag-and-drop mode and movement type which are active in the SchedulerControl.
   
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.DragDropOptions"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerControl.DragDropOptionsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.EditAppointmentFormShowing">
            <summary>
                <para>Occurs before the <b>Edit Appointment</b> form is invoked.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.EditRecurrentAppointmentFormShowing">
            <summary>
                <para>Occurs before the <b>Open Recurring Item</b> form is invoked.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerControl.EndInit">
            <summary>
                <para>Ends the <b>Scheduler</b> control's initialization.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerControl.GetCoreStorage">
            <summary>
                <para>Gets the <see cref="T:DevExpress.XtraScheduler.SchedulerStorageBase"/> class descendant.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.Xpf.Scheduler.Native.SchedulerDataStorage"/> object.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerControl.GetHitTestType(System.Windows.DependencyObject)">
            <summary>
                <para>Gets the value of the <see cref="P:DevExpress.Xpf.Scheduler.SchedulerControl.HitTestType"/> attached property for the specified object.
</para>
            </summary>
            <param name="element">
		The <see cref="T:System.Windows.DependencyObject"/> element from which the attached property value is read.

            </param>
            <returns>The <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerHitTest"/> property value for the element.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerControl.GetResourceColorSchemasCopy">
            <summary>
                <para>Returns copies of color schemas that are currently used to paint visible resources.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.XtraScheduler.SchedulerColorSchemaCollection"/> object.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerControl.GetSelectableIntervalViewInfo(System.Windows.DependencyObject)">
            <summary>
                <para>Gets the value of the <see cref="P:DevExpress.Xpf.Scheduler.SchedulerControl.SelectableIntervalViewInfo"/> attached property for the specified object.
</para>
            </summary>
            <param name="element">
		The <see cref="T:System.Windows.DependencyObject"/> element from which the attached property value is read.

            </param>
            <returns>The object which implements the  <see cref="T:DevExpress.XtraScheduler.Native.ISelectableIntervalViewInfo"/> interface.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerControl.GetService(System.Type)">
            <summary>
                <para>Gets the service object of the specified type.
</para>
            </summary>
            <param name="serviceType">
		An object that specifies the type of service object to get.

            </param>
            <returns>A service object of the specified type, or a null reference (<b>Nothing</b> in Visual Basic) if there is no service object of this type.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerControl.GetService``1">
            <summary>
                <para>Gets the service object of the specified generic type.
</para>
            </summary>
            <returns>A service object of the specified generic type, or a null reference (<b>Nothing</b> in Visual Basic) if there is no service object of this type.
</returns>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.GotoDateFormShowing">
            <summary>
                <para>Occurs before the <b>Go To Date</b> form is invoked.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.GroupType">
            <summary>
                <para>Gets or sets a value that specifies the type of grouping applied to the View of the <b>Scheduler</b> control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerGroupType"/> enumeration value that specifies how appointments are grouped within the <b>Scheduler</b> control.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerControl.GroupTypeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.HitTestType(System.Windows.DependencyObject)">
            <summary>
                <para>Gets or sets a value which specifies the <b>Scheduler</b> control element being hit. This is an attached property.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerHitTest"/> enumeration value.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerControl.HitTestTypeProperty">
            <summary>
                <para>Identifies the  attached property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.InitNewAppointment">
            <summary>
                <para>Occurs before a new appointment is created in the <b>Scheduler</b> control.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.InplaceEditorShowing">
            <summary>
                <para>Occurs every time an in-place editor is invoked when an end-user adds a new appointment or edits an existing appointment "in place". 


</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.InplaceEditTemplate">
            <summary>
                <para>Gets or sets the template that defines the presentation of an appointment inplace editor.

</para>
            </summary>
            <value>A <see cref="T:System.Windows.DataTemplate"/> object that defines the presentation of an appointment inplace editor.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerControl.InplaceEditTemplateProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.IsLoading">
            <summary>
                <para>Gets whether the SchedulerControl is being initialized.
</para>
            </summary>
            <value><b>true</b> if the SchedulerControl is being initialized; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.LimitInterval">
            <summary>
                <para>Gets or sets the time interval available for end-users.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeInterval"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerControl.LimitIntervalProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.MonthView">
            <summary>
                <para>Provides access to an object that defines the settings of the scheduler's Month View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.MonthView"/> object representing the Month View in the scheduling area.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerControl.MonthViewProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerControl.OnApplyTemplate">
            <summary>
                <para>Called after the template is completely generated and attached to the visual tree.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.OptionsBehavior">
            <summary>
                <para>Provides access to the <b>Scheduler</b> control's behavior options.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Scheduler.OptionsBehavior"/> object which contains a set of properties specifying the <b>Scheduler</b> control's behavior.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerControl.OptionsBehaviorProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.OptionsCustomization">
            <summary>
                <para>Provides access to the Scheduler control's customization options.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Scheduler.OptionsCustomization"/> object which provides a set of properties specifying the functionality currently available to end-users.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerControl.OptionsCustomizationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.OptionsView">
            <summary>
                <para>Provides access to the <b>Scheduler</b> control's view options.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Scheduler.OptionsView"/> object which provides a set of properties specifying the <b>Scheduler</b> control's presentation. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerControl.OptionsViewProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.PopupMenuShowing">
            <summary>
                <para>Occurs before a popup menu is created for the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> every time a popup menu is invoked.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerControl.PopupMenuShowingEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.QueryResourceColorSchema">
            <summary>
                <para>Enables visible resources to be painted according to certain conditions.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.QueryWorkTime">
            <summary>
                <para>Occurs when the <b>Scheduler</b> control's view calculates the work time interval for the specific resource.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.RecurrenceFormShowing">
            <summary>
                <para>Occurs before the <b>Appointment Recurrence</b> form is invoked.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.RemindersEnabled">
            <summary>
                <para>Gets whether the reminders are enabled.
</para>
            </summary>
            <value><b>true</b> if the reminders are enabled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.RemindersFormDefaultAction">
            <summary>
                <para>Occurs when an end-user does not click the <b>Dismiss</b> or <b>Snooze</b> button on the <b>Reminders</b> form, but simply closes it. The <see cref="P:DevExpress.Xpf.Scheduler.OptionsBehavior.RemindersFormDefaultAction"/> property should be set to <see cref="F:DevExpress.XtraScheduler.RemindersFormDefaultAction.Custom"/>.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.RemindersFormShowing">
            <summary>
                <para>Occurs before the <b>Reminders</b> form is invoked to display alert notifications for the currently triggered reminders.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerControl.RemoveService(System.Type)">
            <summary>
                <para>Removes the service of the specified type from the service container.

</para>
            </summary>
            <param name="serviceType">
		The type of service to remove.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerControl.RemoveService(System.Type,System.Boolean)">
            <summary>
                <para>Removes the service of the specified type from the service container.
</para>
            </summary>
            <param name="serviceType">
		The type of service to remove.

            </param>
            <param name="promote">
		<b>true</b> to promote this request to any parent service containers; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.ResourceColorSchemas">
            <summary>
                <para>Gets the color schemas used to paint resources of the <b>Scheduler</b> control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerColorSchemaCollection"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerControl.ResourceColorSchemasProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.ResourceNavigator">
            <summary>
                <para>Get or sets an object which provides access to particular buttons of a scheduler resource navigator.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.ResourceNavigatorOptions"/> object.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.ResourceNavigatorHorizontalStyle">
            <summary>
                <para>Gets or sets a style of horizontal resource navigator displayed in the scheduler control when appointments are grouped by dates or resources. 
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that specifies a style to be applied to a horizontal resource navigator.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerControl.ResourceNavigatorHorizontalStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerControl.ResourceNavigatorProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.ResourceNavigatorVerticalStyle">
            <summary>
                <para>Gets or sets a style of vertical resource navigator displayed in the scheduler control when appointments are grouped by dates or resources.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Style"/> object that is a style to be applied to a vertical resource navigator.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerControl.ResourceNavigatorVerticalStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.ResourceSharing">
            <summary>
                <para>Gets a value indicating whether appointments can be shared between multiple resources.
</para>
            </summary>
            <value><b>true</b> if resource sharing is enabled; otherwise, <b>false</b>.

</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.SelectableIntervalViewInfo(System.Windows.DependencyObject)">
            <summary>
                <para>Gets or sets information on the visual element located under the hit test point, the resource and time interval associated with this element. This is an attached property.
</para>
            </summary>
            <value>An object which implements the <see cref="T:DevExpress.XtraScheduler.Native.ISelectableIntervalViewInfo"/> interface. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerControl.SelectableIntervalViewInfoProperty">
            <summary>
                <para>Identifies the  attached property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.SelectedAppointments">
            <summary>
                <para>Gets the collection of selected appointments.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.AppointmentBaseCollection"/> descendant which represents the collection of selected appointments.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.SelectedInterval">
            <summary>
                <para>Gets the time interval currently selected in the <b>Scheduler</b> control's active view by an end-user.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeInterval"/> object representing the selected time interval.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.SelectedResource">
            <summary>
                <para>Gets the resource which contains the time interval currently selected in the scheduler's active view by an end-user.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Resource"/> object which represents the selected resource.
</value>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.SelectionChanged">
            <summary>
                <para>Occurs after the selection has been changed.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerControl.SetHitTestType(System.Windows.DependencyObject,DevExpress.XtraScheduler.Drawing.SchedulerHitTest)">
            <summary>
                <para>Sets the value of the <see cref="P:DevExpress.Xpf.Scheduler.SchedulerControl.HitTestType"/> attached property for the specified object.
</para>
            </summary>
            <param name="element">
		The <see cref="T:System.Windows.DependencyObject"/> element to which the attached property is written.

            </param>
            <param name="value">
		The required <see cref="T:DevExpress.XtraScheduler.Drawing.SchedulerHitTest"/> value. 

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerControl.SetSelectableIntervalViewInfo(System.Windows.DependencyObject,DevExpress.XtraScheduler.Native.ISelectableIntervalViewInfo)">
            <summary>
                <para>Sets the value of the <see cref="P:DevExpress.Xpf.Scheduler.SchedulerControl.SelectableIntervalViewInfo"/> attached property for the specified object.
</para>
            </summary>
            <param name="element">
		The <see cref="T:System.Windows.DependencyObject"/> element to which the attached property is written.

            </param>
            <param name="value">
		The required <see cref="T:DevExpress.XtraScheduler.Native.ISelectableIntervalViewInfo"/> value.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.ShowBorder">
            <summary>
                <para>Gets or sets whether to show the border of the <b>Scheduler</b> control. This is a dependency property.
</para>
            </summary>
            <value><b>true</b> to show the border; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerControl.ShowBorderProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerControl.ShowCustomizeTimeRulerForm(DevExpress.XtraScheduler.TimeRuler)">
            <summary>
                <para>Invokes the <b>Time Ruler</b> dialog for the specified time ruler.
</para>
            </summary>
            <param name="ruler">
		A <see cref="T:DevExpress.XtraScheduler.TimeRuler"/> object which represents the time ruler to be customized in the dialog.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerControl.ShowEditAppointmentForm(DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Invokes the <b>Edit Appointment</b> dialog for the specified appointment.
</para>
            </summary>
            <param name="apt">
		A <see cref="T:DevExpress.XtraScheduler.Appointment"/> object which represents the appointment to be edited in the dialog.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerControl.ShowEditAppointmentForm(DevExpress.XtraScheduler.Appointment,System.Boolean)">
            <summary>
                <para>Invokes the <b>Edit Appointment</b> dialog for the specified appointment. Also, optionally disables all editors on dialog, if required.
</para>
            </summary>
            <param name="apt">
		A <see cref="T:DevExpress.XtraScheduler.Appointment"/> object which represents the appointment to be edited in the dialog.

            </param>
            <param name="readOnly">
		<b>true</b> to open this form to display properties of a <i>read-only</i> appointment (in this case all form editors are disabled); otherwise, <b>false</b>.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerControl.ShowEditRecurrentAppointmentForm(DevExpress.XtraScheduler.Appointment,System.Boolean)">
            <summary>
                <para>Invokes the <b>Open Recurring Item</b> form which prompts an end-user for an action while editing the recurrent appointment.

</para>
            </summary>
            <param name="appointment">
		A <see cref="T:DevExpress.XtraScheduler.Appointment"/> object that specifies a simple <b>Occurrence</b> or an exception (<b>ChangedOccurrence</b> or <b>DeletedOccurrence</b>) for which the edit command is processed.

            </param>
            <param name="readOnly">
		<b>true</b> to open the <b>Edit Appointment</b> and <b>Appointment Recurrence</b> forms to display properties of a <i>read-only</i> appointment (in this case all form editors are disabled); otherwise, <b>false</b>.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerControl.ShowGotoDateForm(System.DateTime)">
            <summary>
                <para>Invokes the <b>Go To Date</b> dialog with the specified date.
</para>
            </summary>
            <param name="date">
		A <see cref="T:System.DateTime"/> object representing the date to be shown in the dialog.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerControl.ShowRecurrenceForm(System.Windows.Controls.UserControl,System.Boolean)">
            <summary>
                <para>Invokes the <b>Appointment Recurrence</b> dialog for the specified recurring appointment.
</para>
            </summary>
            <param name="parentForm">
		A <see cref="T:System.Windows.Controls.UserControl"/> object that specifies an <b>Edit Appointment</b> form from which the <b>Appointment Recurrence</b> dialog is invoked.

            </param>
            <param name="readOnly">
		<b>true</b> to open this form to display properties of a <i>read-only</i> recurring appointment (in this case all form editors are disabled); otherwise, <b>false</b>.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.Start">
            <summary>
                <para>Gets or sets the start date of the <b>Scheduler</b> control.
</para>
            </summary>
            <value>A <see cref="T:System.DateTime"/> value which represents the start date of the Scheduler.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerControl.StartProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.Storage">
            <summary>
                <para>Gets or sets the data storage for the <b>Scheduler</b> control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerStorage"/> object specifying the storage for the <b>Scheduler</b> control.
</value>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.StorageChanged">
            <summary>
                <para>Occurs after the <see cref="P:DevExpress.Xpf.Scheduler.SchedulerControl.Storage"/> property value has been changed.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerControl.StorageProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.SupportsRecurrence">
            <summary>
                <para>Gets whether the information on recurring appointments is obtained from a datasource.
</para>
            </summary>
            <value><b>true</b> if the information on appointment recurrences is obtained from a datasource; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.SupportsReminders">
            <summary>
                <para>Gets whether the information on appointment reminders is obtained from a datasource.
</para>
            </summary>
            <value><b>true</b> if the information on appointment reminders is obtained from a datasource; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.TimelineView">
            <summary>
                <para>Provides access to an object that defines the settings of the scheduler's Timeline View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.TimelineView"/> object representing the Timeline View in the scheduling area.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerControl.TimelineViewProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.TimeRulerMenuCustomizations">
            <summary>
                <para>Allows you to customize a time ruler's popup menu, by adding new menu items or removing existing items.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Bars.BarManagerActionCollection"/> object.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.UnboundMode">
            <summary>
                <para>Gets a value indicating if the SchedulerControl is bound to data.
</para>
            </summary>
            <value><b>true</b> if the scheduler control isn't bound to an appointments data source; otherwise, <b>false</b>.

</value>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerControl.UpdateBarItemCommandUIState(DevExpress.Xpf.Bars.BarItem,DevExpress.Xpf.Scheduler.SchedulerUICommand)">
            <summary>
                <para>Updates the command state for a command associated with a bar item.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Bars.BarItem"/> object that provides the bar element functionality.

            </param>
            <param name="uiCommand">
		An object specifying a command.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.Views">
            <summary>
                <para>Contains the settings of the Views that are used to represent information within the <b>Scheduler</b> control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerViewRepository"/> object which stores the settings of the <b>Scheduler</b> control Views.
</value>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.SchedulerControl.VisibleIntervalChanged">
            <summary>
                <para>Occurs when the time interval represented by the <b>Scheduler</b> control's scheduling area has been changed.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.WeekView">
            <summary>
                <para>Provides access to an object that defines the settings of the scheduler's Week View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.WeekView"/> object representing the Week View in the scheduling area.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerControl.WeekViewProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.WorkDays">
            <summary>
                <para>Gets the collection of days assigned to a workweek.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.WorkDaysCollection"/> object which identifies workdays.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerControl.WorkWeekView">
            <summary>
                <para>Provides access to an object that defines the settings of the scheduler's Work-Week View.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.WorkWeekView"/> object representing the Work Week View in the scheduling area.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerControl.WorkWeekViewProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.AppointmentViewInfoCustomizingEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.AppointmentViewInfoCustomizing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentViewInfoCustomizingEventArgs.#ctor(DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo)">
            <summary>
                <para>Initializes a new instance of the AppointmentViewInfoCustomizingEventArgs class with the specified settings.
</para>
            </summary>
            <param name="viewInfo">
		An <see cref="T:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo"/> object which represents the event's view information. This value is assigned to the <see cref="P:DevExpress.Xpf.Scheduler.AppointmentViewInfoCustomizingEventArgs.ViewInfo"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentViewInfoCustomizingEventArgs.ViewInfo">
            <summary>
                <para>Gets the object which contains information used to render the appointment.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo"/> object.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.SchedulerDeferredScrollingOption">

            <summary>
                <para>Provides access to an object that specifies how deferred scrolling is performed.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerDeferredScrollingOption.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerDeferredScrollingOption class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerDeferredScrollingOption.Allow">
            <summary>
                <para>Gets or sets whether the deferred scrolling feature is enabled for a view.
</para>
            </summary>
            <value><b>true</b> to enable deferred scrolling; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.SchedulerDeferredScrollingOption.AllowProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.SchedulerTimeRulerCollection">

            <summary>
                <para>A collection of time rulers for the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerTimeRulerCollection.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerTimeRulerCollection class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.SchedulerTimeRuler">

            <summary>
                <para>A time ruler for <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.SchedulerTimeRuler.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerTimeRuler class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.SchedulerTimeRuler.AlwaysShowTopRowTime">
            <summary>
                <para>This property is intended to hide the corresponding property of the base class, because it is not appropriate for the SchedulerTimeRuler class.
</para>
            </summary>
            <value>A <see cref="T:System.Boolean"/> value.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Reporting.SchedulerSmartSyncOptions">

            <summary>
                <para>Contains options specific to the SmartSync Printing feature.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Reporting.SchedulerSmartSyncOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerSmartSyncOptions class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Reporting.SchedulerSmartSyncOptions.GroupType">
            <summary>
                <para>Gets or sets the type of grouping used for creating <b>tri-fold</b> scheduler report pages if the <see cref="P:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerPrintAdapter.EnableSmartSync"/> option is switched on.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerGroupType"/> enumeration member specifying the grouping for the SmartSync printing.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Reporting.SchedulerSmartSyncOptions.GroupTypeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.Reporting.SchedulerSmartSyncOptions.PropertyChanged">
            <summary>
                <para>Occurs every time any of the SchedulerSmartSyncOptions class properties have changed their value.

</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Reporting.SchedulerReportConfigurator">

            <summary>
                <para>Provides a method for adjusting the scheduler report.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Reporting.SchedulerReportConfigurator.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerReportConfigurator class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Reporting.SchedulerReportConfigurator.Configure(DevExpress.Xpf.Scheduler.Reporting.ISchedulerPrintingSettings)">
            <summary>
                <para>Configures a report provided by the specified object.
</para>
            </summary>
            <param name="settings">
		An object that implements the <see cref="T:DevExpress.Xpf.Scheduler.Reporting.ISchedulerPrintingSettings"/> interface.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Reporting.BaseSchedulerPrintingSettings">

            <summary>
                <para>A base class for settings that are required to preview a scheduler as it will appear when it's printed.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Reporting.BaseSchedulerPrintingSettings.GetReportTemplatePath">
            <summary>
                <para>When implemented in an inherited class, returns a string that contains a file name of the report template to be used for creating a scheduler report and path to this file.
</para>
            </summary>
            <returns>A <see cref="T:System.String"/> value.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Reporting.BaseSchedulerPrintingSettings.ReportInstance">
            <summary>
                <para>Gets or sets a report to preview and print scheduler information.

</para>
            </summary>
            <value>A report object that implements the ISchedulerReport interface.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Reporting.BaseSchedulerPrintingSettings.ReportInstanceProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Reporting.BaseSchedulerPrintingSettings.SchedulerPrintAdapter">
            <summary>
                <para>Specifies the print adapter composing scheduler data for a report.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerPrintAdapter"/> class descendant (the <see cref="T:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerControlPrintAdapter"/> or <see cref="T:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerStoragePrintAdapter"/> object).
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Reporting.BaseSchedulerPrintingSettings.SchedulerPrintAdapterProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerStoragePrintAdapter">

            <summary>
                <para>A print adapter bound to the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerStorage"/>.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerStoragePrintAdapter.#ctor">
            <summary>
                <para>Initializes a new instance of the DXSchedulerStoragePrintAdapter class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerStoragePrintAdapter.#ctor(DevExpress.Xpf.Scheduler.SchedulerStorage)">
            <summary>
                <para>Initializes a new instance of the DXSchedulerStoragePrintAdapter class with the specified storage.
</para>
            </summary>
            <param name="storage">
		A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerStorage"/> object specifying a storage for scheduler data.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerStoragePrintAdapter.SchedulerStorage">
            <summary>
                <para>Gets or sets the storage for the print adapter.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerStorage"/> object containing data to build a report.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerStoragePrintAdapter.SchedulerStorageProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerControlPrintAdapter">

            <summary>
                <para>A print adapter bound to the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerControlPrintAdapter.#ctor">
            <summary>
                <para>Initializes a new instance of the DXSchedulerControlPrintAdapter class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerControlPrintAdapter.#ctor(DevExpress.Xpf.Scheduler.SchedulerControl)">
            <summary>
                <para>Initializes a new instance of the DXSchedulerControlPrintAdapter class with the specified <b>SchedulerControl</b>.
</para>
            </summary>
            <param name="control">
		A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> instance which is the report's scheduling source.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerControlPrintAdapter.SchedulerControl">
            <summary>
                <para>Gets or sets the SchedulerControl for the print adapter.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> object specifying a scheduler control.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerControlPrintAdapter.SchedulerControlProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerPrintAdapter">

            <summary>
                <para>A base class for print adapters of the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> reports.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerPrintAdapter.ClientTimeZoneId">
            <summary>
                <para>Gets or sets the string identifier of the time zone used by the print adapter. 
</para>
            </summary>
            <value>A string that uniquely identifies the time zone. The value is equal to that returned by the System.TimeZoneInfo.Id property.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerPrintAdapter.ClientTimeZoneIdProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerPrintAdapter.EnableSmartSync">
            <summary>
                <para>Gets or sets whether different controls in a report should synchronize their data iterations (the SmartSync Printing feature).
</para>
            </summary>
            <value><b>true</b> to enable the SmartSync Printing feature; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerPrintAdapter.EnableSmartSyncProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerPrintAdapter.FirstDayOfWeek">
            <summary>
                <para>Gets or sets the start day of the week used by the print adapter to create a report.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.FirstDayOfWeek"/> enumeration value specifying the day of the week to be the first in a report week.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerPrintAdapter.FirstDayOfWeekProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerPrintAdapter.PropertyChanged">
            <summary>
                <para>Occurs every time any of the DXSchedulerPrintAdapter class properties has changed its value.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerPrintAdapter.PropertyChanging">
            <summary>
                <para>Occurs before a value of any of the DXSchedulerPrintAdapter class properties is changed.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerPrintAdapter.ResourceColorSchemas">
            <summary>
                <para>Gets the color schemas used to paint resources in the report.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.SchedulerColorSchemaCollection"/> object which contains color schemas used to display resources.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerPrintAdapter.ResourceColorSchemasProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerPrintAdapter.SchedulerAdapter">
            <summary>
                <para>Provides access to the scheduler print adapter.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Reporting.SchedulerPrintAdapter"/> object specifying a print adapter for the report.
</value>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerPrintAdapter.SchedulerSourceChanged">
            <summary>
                <para>Occurs when the scheduler source of the print adapter is changed.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerPrintAdapter.SmartSyncOptions">
            <summary>
                <para>Provides access to options specific to the SmartSync Printing feature.
</para>
            </summary>
            <value>An object exposing the <see cref="T:DevExpress.XtraScheduler.Reporting.ISmartSyncOptions"/> interface (<see cref="T:DevExpress.Xpf.Scheduler.Reporting.SchedulerSmartSyncOptions"/>), allowing you to specify SmartSync-specific options.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerPrintAdapter.SmartSyncOptionsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerPrintAdapter.TimeInterval">
            <summary>
                <para>Gets or sets the time interval used by the print adapter to create a report.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeInterval"/> object specifying the time interval.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerPrintAdapter.TimeIntervalProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerPrintAdapter.ValidateAppointments">
            <summary>
                <para>Occurs when appointments are retrieved to create a report document.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerPrintAdapter.ValidateResources">
            <summary>
                <para>Occurs when resources are obtained for use in the report.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerPrintAdapter.ValidateTimeIntervals">
            <summary>
                <para>Occurs when the print adapter retrieves information on time intervals required for creating a report.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerPrintAdapter.ValidateWorkTime">
            <summary>
                <para>Occurs when the print adapter retrieves the work time value for use in the report.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerPrintAdapter.WorkTime">
            <summary>
                <para>Gets or sets the work time interval for a day.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeOfDayInterval"/> value specifying the work time interval.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerPrintAdapter.WorkTimeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.RecurrenceFormEventHandler">

            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.RecurrenceFormShowing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.RecurrenceFormEventHandler.Invoke(System.Object,DevExpress.Xpf.Scheduler.RecurrenceFormEventArgs)">
            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.RecurrenceFormShowing"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> which raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Xpf.Scheduler.RecurrenceFormEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.RecurrenceFormEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.RecurrenceFormShowing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.RecurrenceFormEventArgs.#ctor(System.Windows.Controls.UserControl,System.Boolean)">
            <summary>
                <para>Initializes a new instance of the RecurrenceFormEventArgs class with the specified settings.
</para>
            </summary>
            <param name="parentForm">
		A <see cref="T:System.Windows.Controls.UserControl"/> object specifying the <b>Edit Appointment</b> form from which the <b>Appointment Recurrence</b> form is invoked. This value is assigned to the <see cref="P:DevExpress.Xpf.Scheduler.RecurrenceFormEventArgs.ParentForm"/> property.

            </param>
            <param name="readOnly">
		A <see cref="T:System.Boolean"/> value which indicates whether the event's appointment is read-only.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.RecurrenceFormEventArgs.Controller">
            <summary>
                <para>Gets or sets the controller providing settings which are required to edit appointment recurrence in the <b>Appointment Recurrence</b> form.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Scheduler.UI.AppointmentFormController"/> object.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.RecurrenceFormEventArgs.ParentForm">
            <summary>
                <para>Gets the appointment editing form from which the <b>Appointment Recurrence</b> form is invoked.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Controls.UserControl"/> object specifying the <b>Edit Appointment</b> form.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Reporting.SchedulerPrintingSettings">

            <summary>
                <para>Contains settings (report instance, print adapter and report layout template) that are required to preview a scheduler as it will appear if printed.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Reporting.SchedulerPrintingSettings.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerPrintingSettings class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Reporting.SchedulerPrintingSettings.GetReportTemplatePath">
            <summary>
                <para>Gets a string containing the file name of the report template to be used for creating a scheduler report and path to this file.
</para>
            </summary>
            <returns>A <see cref="T:System.String"/> value.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Reporting.SchedulerPrintingSettings.ReportTemplatePath">
            <summary>
                <para>Gets or sets a string containing the file name of the report template to be used for creating a scheduler report and path to this file.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Reporting.SchedulerPrintingSettings.ReportTemplatePathProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Reporting.ISchedulerPrintingSettings">

            <summary>
                <para>An interface that defines settings required to preview a scheduler as it will appear when it's printed.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Reporting.ISchedulerPrintingSettings.GetReportTemplatePath">
            <summary>
                <para>When implemented by a class, returns a string that contains a file name of the report template to be used for creating a scheduler report and path to this file.
</para>
            </summary>
            <returns>A <see cref="T:System.String"/> value.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Reporting.ISchedulerPrintingSettings.ReportInstance">
            <summary>
                <para>When implemented by a class, specifies a report to preview and print scheduler information.
</para>
            </summary>
            <value>A report object that implements the ISchedulerReport interface.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Reporting.ISchedulerPrintingSettings.SchedulerPrintAdapter">
            <summary>
                <para>When implemented by a class, specifies the print adapter composing scheduler data for a report.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerPrintAdapter"/> class descendant (the <see cref="T:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerControlPrintAdapter"/> or <see cref="T:DevExpress.Xpf.Scheduler.Reporting.DXSchedulerStoragePrintAdapter"/> object) specifying the print adapter that serves as the data source of the scheduling data for the report.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.UI.RecurrenceRangeControl">

            <summary>
                <para>A control used to specify an end condition for the series of recurring appointments.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.RecurrenceRangeControl.#ctor">
            <summary>
                <para>Initializes a new instance of the RecurrenceRangeControl class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.RecurrenceRangeControl.InitializeComponent">
            <summary>
                <para>Initializes the RecurrenceRangeControl from XAML.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.RecurrenceRangeControl.IsReadOnly">
            <summary>
                <para>Gets or sets whether end-users can change the recurrence range setting via the RecurrenceRangeControl control.

</para>
            </summary>
            <value><b>true</b> if the control is read-only; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.RecurrenceRangeControl.IsReadOnlyProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.RecurrenceRangeControl.LocalEnd">
            <summary>
                <para>Gets or sets the appointment recurrence end date.
</para>
            </summary>
            <value>A <see cref="T:System.DateTime"/> object.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.RecurrenceRangeControl.LocalStart">
            <summary>
                <para>Gets or sets the appointment recurrence start date.
</para>
            </summary>
            <value>A <see cref="T:System.DateTime"/> object.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.RecurrenceRangeControl.Pattern">
            <summary>
                <para>Gets or sets the copy of the pattern appointment.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Appointment"/> object specifying a copy of the recurrence pattern.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.RecurrenceRangeControl.PatternProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.RecurrenceRangeControl.RecurrenceInfo">
            <summary>
                <para>Gets or sets an object containing information about the appointment recurrence.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.RecurrenceInfo"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.RecurrenceRangeControl.RecurrenceInfoProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.RecurrenceRangeControl.RecurrenceRanges">
            <summary>
                <para>Gets the collection of objects specifying available types of the recurrence range.
</para>
            </summary>
            <value>A <see cref="T:System.Collections.IList"/> collection.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.RecurrenceRangeControl.TimeZoneHelper">
            <summary>
                <para>Provides access to the <see cref="T:DevExpress.XtraScheduler.TimeZoneHelper"/> instance used for date/time calculations.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeZoneHelper"/> class instance.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.RecurrenceRangeControl.TimeZoneHelperProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.ResourceMapping">

            <summary>
                <para>Provides information on the mapping of the resource properties to appropriate data fields.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.ResourceMapping.#ctor(DevExpress.Xpf.Scheduler.ResourceStorage)">
            <summary>
                <para>Initializes a new instance of the ResourceMapping class with the specified resource storage.
</para>
            </summary>
            <param name="dataStorage">
		A <see cref="T:DevExpress.Xpf.Scheduler.ResourceStorage"/> value that specifies the resource storage.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.ResourceMapping.#ctor">
            <summary>
                <para>Initializes a new instance of the ResourceMapping class with the default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.ResourceMapping.Caption">
            <summary>
                <para>Gets or sets the data field to which a resource's <see cref="P:DevExpress.XtraScheduler.Resource.Caption"/> property is bound.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the name of the bound data field.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.ResourceMapping.CaptionProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.ResourceMapping.Color">
            <summary>
                <para>Gets or sets the data field to which a resource's <see cref="P:DevExpress.XtraScheduler.Resource.Color"/> property is bound.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the name of the bound data field.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.ResourceMapping.ColorProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.ResourceMapping.Id">
            <summary>
                <para>Gets or sets the data field to which a resource's <see cref="P:DevExpress.XtraScheduler.Resource.Id"/> property is bound.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the name of the bound data field.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.ResourceMapping.IdProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.ResourceMapping.Image">
            <summary>
                <para>Gets or sets the data field to which a resource's <see cref="P:DevExpress.XtraScheduler.Resource.Image"/> property is bound.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the name of the bound data field.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.ResourceMapping.ImageProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.AppointmentMapping">

            <summary>
                <para>Provides information on the mapping of the appointment properties to the appropriate data fields.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentMapping.#ctor(DevExpress.Xpf.Scheduler.AppointmentStorage)">
            <summary>
                <para>Initializes a new instance of the AppointmentMapping class with the specified appointment storage.
</para>
            </summary>
            <param name="dataStorage">
		A <see cref="T:DevExpress.Xpf.Scheduler.AppointmentStorage"/> value that specifies the appointment storage.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.AppointmentMapping.#ctor">
            <summary>
                <para>Initializes a new instance of the AppointmentMapping class with the default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentMapping.AllDay">
            <summary>
                <para>Gets or sets the data field to which an appointment's <see cref="P:DevExpress.XtraScheduler.Appointment.AllDay"/> property is bound.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the name of the bound data field.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.AppointmentMapping.AllDayProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentMapping.AppointmentId">
            <summary>
                <para>Gets or sets the data field to which an appointment's <see cref="P:DevExpress.XtraScheduler.Appointment.Id"/> property is bound.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the name of the bound data field.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.AppointmentMapping.AppointmentIdProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentMapping.Description">
            <summary>
                <para>Gets or sets the data field to which an appointment's <see cref="P:DevExpress.XtraScheduler.Appointment.Description"/> property is bound.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the name of the bound data field.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.AppointmentMapping.DescriptionProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentMapping.End">
            <summary>
                <para>Gets or sets the data field to which an appointment's <see cref="P:DevExpress.XtraScheduler.Appointment.End"/> property is bound.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the name of the bound data field.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.AppointmentMapping.EndProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentMapping.Label">
            <summary>
                <para>Gets or sets the data field to which an appointment's  <see cref="P:DevExpress.XtraScheduler.Appointment.LabelId"/> property is bound.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the name of the bound data field.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.AppointmentMapping.LabelProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentMapping.Location">
            <summary>
                <para>Gets or sets the data field to which an appointment's <see cref="P:DevExpress.XtraScheduler.Appointment.Location"/> property is bound.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the name of the bound data field.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.AppointmentMapping.LocationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentMapping.RecurrenceInfo">
            <summary>
                <para>Gets or sets the data field to which an appointment's <see cref="P:DevExpress.XtraScheduler.Appointment.RecurrenceInfo"/> property is bound.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the name of the bound data field.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.AppointmentMapping.RecurrenceInfoProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentMapping.ReminderInfo">
            <summary>
                <para>Gets or sets the data field to which an appointment's <see cref="P:DevExpress.XtraScheduler.Appointment.Reminder"/> property is bound.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the name of the bound data field.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.AppointmentMapping.ReminderInfoProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentMapping.ResourceId">
            <summary>
                <para>Gets or sets the data field to which an appointment's <see cref="P:DevExpress.XtraScheduler.Appointment.ResourceId"/> property is bound.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the name of the bound data field.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.AppointmentMapping.ResourceIdProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentMapping.Start">
            <summary>
                <para>Gets or sets the data field to which an appointment's <see cref="P:DevExpress.XtraScheduler.Appointment.Start"/> property is bound.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the name of the bound data field.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.AppointmentMapping.StartProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentMapping.Status">
            <summary>
                <para>Gets or sets the data field to which an appointment's <see cref="P:DevExpress.XtraScheduler.Appointment.StatusId"/> property is bound.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the name of the bound data field.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.AppointmentMapping.StatusProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentMapping.Subject">
            <summary>
                <para>Gets or sets the data field to which an appointment's <see cref="P:DevExpress.XtraScheduler.Appointment.Subject"/> property is bound.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the name of the bound data field.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.AppointmentMapping.SubjectProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.AppointmentMapping.Type">
            <summary>
                <para>Gets or sets the data field to which an appointment's <see cref="P:DevExpress.XtraScheduler.Appointment.Type"/> property is bound.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the name of the bound data field.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.AppointmentMapping.TypeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewInterval">

            <summary>
                <para>Provides information on visual elements combined into time intervals displayed in the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewInterval.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualDayViewInterval class with the default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewInterval.CopyAppointmentsFrom(DevExpress.Xpf.Scheduler.Drawing.SingleDayViewInfo)">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="source">
		A <see cref="T:DevExpress.Xpf.Scheduler.Drawing.SingleDayViewInfo"/> object.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewInterval.FirstSimpleInterval">
            <summary>
                <para>Gets or sets the time interval corresponding to the first resource displayed within the current date.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.Drawing.VisualSimpleResourceInterval"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewInterval.FirstSimpleIntervalProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewInterval.IsAlternate">
            <summary>
                <para>Gets or sets a value that specifies whether a data header that corresponds to today's date is highlighted within the <b>Scheduler</b> control.
</para>
            </summary>
            <value><b>true</b> if the today's date header is highlighted; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualDayViewInterval.IsAlternateProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo">

            <summary>
                <para>Provides information on the visual representation of the appointment.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.#ctor(DevExpress.XtraScheduler.Appointment,DevExpress.XtraScheduler.TimeZoneHelper)">
            <summary>
                <para>Initializes a new instance of the AppointmentViewInfo class with the specified appointment and time zone helper.
</para>
            </summary>
            <param name="appointment">
		A <see cref="T:DevExpress.XtraScheduler.Appointment"/> object.

            </param>
            <param name="timeZoneHelper">
		A <see cref="T:DevExpress.XtraScheduler.TimeZoneHelper"/> object.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.Appointment">
            <summary>
                <para>Gets the appointment, whose visual representation information is accessed via the current object.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Appointment"/> object representing an appointment.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.AppointmentInterval">
            <summary>
                <para>Gets the time interval of the appointment.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeInterval"/> object representing the scheduled time.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.CustomViewInfo">
            <summary>
                <para>Gets or sets a custom object to be used for rendering the appointment.
</para>
            </summary>
            <value>An object used for customizing the appointment's visual representation.

</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.Description">
            <summary>
                <para>Gets or sets the description text displayed within the appointment.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the text of the appointment description.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.GetEndTimeText">
            <summary>
                <para>Returns the string containing the end date and time of the appointment.
</para>
            </summary>
            <returns>A <see cref="T:System.String"/> value.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.GetStartTimeText">
            <summary>
                <para>Returns the string containing the start date and time of the appointment.
</para>
            </summary>
            <returns>A <see cref="T:System.String"/> value.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.HasBottomBorder">
            <summary>
                <para>Determines whether the bottom border is displayed for the appointment.
</para>
            </summary>
            <value><b>true</b> if the bottom border is displayed for an appointment; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.HasLeftBorder">
            <summary>
                <para>Determines whether the left border is displayed for the appointment.
</para>
            </summary>
            <value><b>true</b> if the left border is displayed for an appointment; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.HasRightBorder">
            <summary>
                <para>Determines whether the right border is displayed for the appointment.
</para>
            </summary>
            <value><b>true</b> if the right border is displayed for an appointment; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.HasTopBorder">
            <summary>
                <para>Determines whether the top border is displayed for the appointment.
</para>
            </summary>
            <value><b>true</b> if the top border is displayed for an appointment; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.HitTestType">
            <summary>
                <para>Gets the enumeration member, which indicates the contents of the appointment when it is hit.
</para>
            </summary>
            <value>A <see cref="F:DevExpress.XtraScheduler.Drawing.SchedulerHitTest.AppointmentContent"/> value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.Interval">
            <summary>
                <para>Gets or sets the time interval of the time cells covered by the appointment.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.TimeInterval"/> object representing the time range of the time cells.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.IsEndVisible">
            <summary>
                <para>Indicates whether the appointment scheduled time ends within the visible interval of dates.
</para>
            </summary>
            <value><b>true</b> if an appointment end is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.IsLongTime">
            <summary>
                <para>Indicates whether the appointment extends over multiple days.
</para>
            </summary>
            <returns><b>true</b> if an appointment extends over several days; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.IsStartVisible">
            <summary>
                <para>Indicates whether the appointment scheduled time starts within the visible interval of dates.
</para>
            </summary>
            <value><b>true</b> if an appointment start is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.Label">
            <summary>
                <para>Gets or sets the label of the appointment.

</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Scheduler.AppointmentLabel"/> object that represents an appointment label.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.LabelBrush">
            <summary>
                <para>Gets the brush used to fill the appointment label.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Media.Brush"/> object which specifies the brush used for drawing an appointment label.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.LabelColor">
            <summary>
                <para>Gets the label color used to fill the region of appointment.

</para>
            </summary>
            <value>A <see cref="T:System.Windows.Media.Color"/> object that represents the color of an appointment background.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.Location">
            <summary>
                <para>Gets or sets the location text displayed within the appointment.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the text of the appointment location.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.Options">
            <summary>
                <para>Gets several characteristics determining how an appointment is displayed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Drawing.AppointmentViewInfoOptions"/> object containing certain appointment view characteristics.
</value>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.PropertyChanged">
            <summary>
                <para>Occurs every time any of the AppointmentViewInfo class properties has changed its value.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.Resource">
            <summary>
                <para>Gets or sets the resource associated with the current appointment view.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.Resource"/> object, representing a resource associated with an appointment.

</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.Selected">
            <summary>
                <para>Determines whether the appointment is currently selected.
</para>
            </summary>
            <value><b>true</b> if an appointment is selected; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.Status">
            <summary>
                <para>Gets or sets the status of the appointment.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Scheduler.AppointmentStatus"/> object that represents an appointment status.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.StatusBrush">
            <summary>
                <para>Gets the brush used to fill the appointment status.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Media.Brush"/> object which specifies the brush used for drawing an appointment status.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.StatusColor">
            <summary>
                <para>Gets the color of the appointment status.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Media.Color"/> value which specifies the color used for an appointment status.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.AppointmentViewInfo.Subject">
            <summary>
                <para>Gets or sets the subject text displayed within the appointment.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the text of the appointment subject.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.UI.SchedulerBindedComboBoxEdit">

            <summary>
                <para>Serves as a base for classes which represent combo box and checked list box controls for working with resources of the <b>Scheduler Control</b>.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.SchedulerBindedComboBoxEdit.BeginUpdate">
            <summary>
                <para>Locks the SchedulerBindedComboBoxEdit object by preventing visual updates until the <b>EndUpdate</b> method is called.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.SchedulerBindedComboBoxEdit.EndUpdate">
            <summary>
                <para>Unlocks the SchedulerBindedComboBoxEdit object after a call to the <b>BeginUpdate</b> method and causes an immediate visual update.


</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.SchedulerBindedComboBoxEdit.SchedulerControl">
            <summary>
                <para>Gets or sets the <b>Scheduler</b> control that is assigned to the SchedulerBindedComboBoxEdit class descendant.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> object specifying the scheduler whose resources will be shown in the combo box.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerBindedComboBoxEdit.SchedulerControlProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualResource">

            <summary>
                <para>Serves as the base for classes that provide information on visual elements (resource headers, navigation buttons, etc.) combined into resource containers in different views.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualResource.CopyAppointmentsFrom(DevExpress.Xpf.Scheduler.Drawing.SingleResourceViewInfo)">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="source">
		A <see cref="T:DevExpress.Xpf.Scheduler.Drawing.SingleResourceViewInfo"/> object.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualResource.NextNavButtonEnabled">
            <summary>
                <para>Gets or sets whether the <b>Next Appointment</b> navigation button displayed for a particular resource is enabled.
</para>
            </summary>
            <value><b>true</b> if the button is enabled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualResource.NextNavButtonEnabledProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualResource.NextNavButtonInfo">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.Drawing.NavigationButtonViewModel"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualResource.NextNavButtonInfoProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualResource.NextNavButtonVisibility">
            <summary>
                <para>Gets or sets the visibility state of the <b>Next Appointment</b> navigation button displayed for a particular resource.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Visibility"/> enumeration member.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualResource.NextNavButtonVisibilityProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualResource.PrevNavButtonEnabled">
            <summary>
                <para>Gets or sets whether the <b>Previous Appointment</b> navigation button displayed for a particular resource is enabled and end-users can perform actions on it.
</para>
            </summary>
            <value><b>true</b> if the button is enabled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualResource.PrevNavButtonEnabledProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualResource.PrevNavButtonInfo">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.Drawing.NavigationButtonViewModel"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualResource.PrevNavButtonInfoProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualResource.PrevNavButtonVisibility">
            <summary>
                <para>Gets or sets the visibility state of the <b>Previous Appointment</b> navigation button displayed for a particular resource.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Visibility"/> enumeration member.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualResource.PrevNavButtonVisibilityProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualResource.ResourceHeader">
            <summary>
                <para>Specifies an object which contains information on the resource header.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.Drawing.VisualResourceHeaderBaseContent"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualResource.ResourceHeaderProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualResource.SimpleIntervals">
            <summary>
                <para>Gets the collection of time intervals displayed for a particular resource.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.Drawing.VisualSimpleResourceIntervalCollection"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualResource.SimpleIntervalsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualResource.View">
            <summary>
                <para>Gets the view of the <b>Scheudler</b> control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerViewBase"/> class descendant.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualResource.ViewProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualVerticalWeekViewResource">

            <summary>
                <para>Provides information on visual elements combined into resource containers displayed in the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>, when the Week View is currently active and appointments are grouped by resources or no grouping is applied.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualVerticalWeekViewResource.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualVerticalWeekViewResource class with the default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.DayViewScrollViewer">

            <summary>
                <para>A scrollable area of the Day View or Work-Week View.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.DayViewScrollViewer.#ctor">
            <summary>
                <para>Initializes a new instance of the DayViewScrollViewer class with the default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.DayViewScrollViewer.BottomOffset">
            <summary>
                <para>Gets or sets the bottom offset of the Day View or Work-Week View scrolled content.
</para>
            </summary>
            <value>A <see cref="T:System.Double"/> value.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.DayViewScrollViewer.BottomOffsetProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.DayViewScrollViewer.Subscribe(DevExpress.Xpf.Scheduler.Drawing.SelectionPresenter)">
            <summary>
                <para> [To be supplied] </para>
            </summary>
            <param name="selectionPresenter">
		 [To be supplied] 
            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.DayViewScrollViewer.Unsubscribe(DevExpress.Xpf.Scheduler.Drawing.SelectionPresenter)">
            <summary>
                <para> [To be supplied] </para>
            </summary>
            <param name="selectionPresenter">
		 [To be supplied] 
            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.DayViewScrollViewer.ViewportBounds">
            <summary>
                <para>Gets the location and size of the Day View or Work-Week View content's viewport.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Rect"/> structure instance.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName">

            <summary>
                <para>Contains captions of popup menus and their default items.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.#ctor">
            <summary>
                <para>Initializes a new instance of the SchedulerMenuItemName class with the default settings.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.AppointmentMenu">
            <summary>
                <para>Returns "AppointmentMenu". Corresponds to the popup menu invoked after an appointment has been clicked with the right mouse button. 
</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.CustomizeCurrentView">
            <summary>
                <para>For internal use.
</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.CustomizeTimeRuler">
            <summary>
                <para>Returns "CustomizeTimeRuler". Corresponds to the menu item that invokes the <b>Time Ruler</b> dialog allowing time ruler editing.

</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.DefaultMenu">
            <summary>
                <para>Returns "DefaultMenu". Corresponds to the popup menu, which is invoked when the view's time cells that do not contain any appointments are right-clicked.
</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.DeleteAppointment">
            <summary>
                <para>Returns "DeleteAppointment". Corresponds to the menu item used to remove a simple appointment or invoke the <b>Confirm Delete</b> dialog for a recurring appointment.
</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.EditSeries">
            <summary>
                <para>Returns "EditSeries". Corresponds to the menu item that invokes the <b>Edit Appointment</b> dialog, allowing editing of the entire series of recurring appointments.
</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.GotoDate">
            <summary>
                <para>Returns "GotoDate". Corresponds to the menu item used to change the date to be displayed in the current view.
</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.GotoThisDay">
            <summary>
                <para>Returns "GotoThisDay". Corresponds to the menu item used to change the current Week or Month view to the Day view and set the date displayed in this view to the selected date.
</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.GotoToday">
            <summary>
                <para>Returns "GotoToday". Corresponds to the menu item used to change the date displayed in the current view to the current date on the local machine.


</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.LabelSubMenu">
            <summary>
                <para>Returns "LabelSubMenu". Corresponds to the menu item that invokes the submenu, including the complete list of available appointment labels.
</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.NewAllDayEvent">
            <summary>
                <para>Returns "NewAllDayEvent". Corresponds to the menu item that invokes the <b>Edit Appointment</b> dialog used to create a new all-day appointment (in this case, the <see cref="P:DevExpress.XtraScheduler.Appointment.AllDay"/> option in the dialog is checked).
</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.NewAppointment">
            <summary>
                <para>Returns "NewAppointment". Corresponds to the menu item that invokes the <b>Edit Appointment</b> dialog used to create a new appointment.
</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.NewRecurringAppointment">
            <summary>
                <para>Returns "NewRecurringAppointment". Corresponds to the menu item that invokes the <b>Edit Appointment</b> dialog used to create a new appointment and specify the recurrence pattern for it.

</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.NewRecurringEvent">
            <summary>
                <para>Returns "NewRecurringEvent". Corresponds to the menu item that invokes the <b>Edit Appointment</b> dialog used to create a new all-day appointment (in this case, the <see cref="P:DevExpress.XtraScheduler.Appointment.AllDay"/> option in the dialog is checked) and specify the recurrence pattern for this appointment.
</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.OpenAppointment">
            <summary>
                <para>Returns "OpenAppointment". Corresponds to the menu item that invokes the <b>Edit Appointment</b> dialog used for editing properties of the current appointment. Note that if the appointment is recurring, then this item only allows editing of the current appointment, not its entire series.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.OtherSettings">
            <summary>
                <para>Returns "OtherSettings". For internal use only. 
</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.PrintAppointment">
            <summary>
                <para>Returns "PrintAppointment". For internal use only.
</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.RestoreOccurrence">
            <summary>
                <para>Returns "RestoreOccurrence". Corresponds to the menu item used to restore the default state of the changed recurring appointment as it was generated by the recurrence pattern.
</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.RulerMenu">
            <summary>
                <para>Returns "RulerMenu". Corresponds to the popup menu invoked after a time ruler has been clicked with the right mouse button. 
</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.StatusSubMenu">
            <summary>
                <para>Returns "StatusSubMenu". Corresponds to the menu item that invokes the submenu including the complete list of available appointment statuses.
</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.SwitchTimeScale">
            <summary>
                <para>Returns "SwitchTimeScale". Corresponds to the menu item that invokes the submenu including the complete list of time scales available for display in the Timeline View.

</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.SwitchToDayView">
            <summary>
                <para>Returns "SwitchToDayView". Corresponds to the menu item used to change <see cref="P:DevExpress.Xpf.Scheduler.SchedulerControl.ActiveView"/> to Day View.
</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.SwitchToMonthView">
            <summary>
                <para>Returns "SwitchToMonthView". Corresponds to the menu item used to change <see cref="P:DevExpress.Xpf.Scheduler.SchedulerControl.ActiveView"/> to Month View.

</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.SwitchToTimelineView">
            <summary>
                <para>Returns "SwitchToTimelineView". Corresponds to the menu item used to change <see cref="P:DevExpress.Xpf.Scheduler.SchedulerControl.ActiveView"/> to Timeline View.

</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.SwitchToWeekView">
            <summary>
                <para>Returns "SwitchToWeekView". Corresponds to the menu item used to change <see cref="P:DevExpress.Xpf.Scheduler.SchedulerControl.ActiveView"/> to Week View.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.SwitchToWorkWeekView">
            <summary>
                <para>Returns "SwitchToWorkWeekView". Corresponds to the menu item used to change <see cref="P:DevExpress.Xpf.Scheduler.SchedulerControl.ActiveView"/> to Work-Week View.

</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.SwitchViewMenu">
            <summary>
                <para>Returns "SwitchViewMenu". Corresponds to the menu item that invokes the submenu, including the list of views available in the <b>Scheduler Control</b>.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.TimeScaleEnable">
            <summary>
                <para>Returns "TimeScaleEnable". Corresponds to the menu item used to enable the selected Timeline scale.
</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.SchedulerMenuItemName.TimeScaleVisible">
            <summary>
                <para>Returns "TimeScaleVisible". Corresponds to the menu item used to make the selected Timeline scale visible.
</para>
            </summary>
            <returns>$
</returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditorBase">

            <summary>
                <para>Provides all the settings which are required to edit a particular appointment in a custom in-place editor.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditorBase.#ctor(DevExpress.Xpf.Scheduler.SchedulerControl,DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Initializes a new instance of the AppointmentInplaceEditorBase class with the specified appointment and scheduler control.
</para>
            </summary>
            <param name="control">
		A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> object which represents the <b>Scheduler</b> control containing the appointment to be edited via the in-place editor.

            </param>
            <param name="apt">
		A <see cref="T:DevExpress.XtraScheduler.Appointment"/> object which represents an appointment to be edited via the in-place editor.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditorBase.#ctor">
            <summary>
                <para>Initializes a new instance of the AppointmentInplaceEditorBase class with the default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditorBase.Activate">
            <summary>
                <para>Initializes the control, subscribes to its major events and sets the focus to it.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditorBase.ApplyChanges">
            <summary>
                <para>Passes values obtained from the editor to the appointment, and performs an assignment to the appointment properties.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditorBase.Appointment">
            <summary>
                <para>Gets the appointment for which the in-place editor has been invoked.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.XtraScheduler.Appointment"/> object, which is the appointment currently being processed.

</value>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditorBase.CommitChanges">
            <summary>
                <para>Occurs before modified values are saved to the underlying data source.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditorBase.Deactivate">
            <summary>
                <para>Unsubscribes major events and releases resources, if necessary.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditorBase.Description">
            <summary>
                <para>Gets or sets the description of the appointment currently being edited in the custom in-place editor.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the text of the appointment description.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditorBase.DescriptionProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditorBase.Dispose">
            <summary>
                <para>Disposes of the current object.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditorBase.IsNewAppointment">
            <summary>
                <para>Determines if the currently edited appointment is new.
</para>
            </summary>
            <value><b>true</b> if the current appointment is new; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditorBase.Label">
            <summary>
                <para>Gets or sets the label associated with the appointment currently being edited in the custom in-place editor.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Scheduler.AppointmentLabel"/> object representing the label of the appointment.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditorBase.LabelProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditorBase.Location">
            <summary>
                <para>Gets or sets the location of the appointment currently being edited in the custom in-place editor.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the text of the appointment location.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditorBase.LocationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditorBase.RollbackChanges">
            <summary>
                <para>Occurs before changes are cancelled and modified values are replaced with the former values (values before modification).

</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditorBase.Status">
            <summary>
                <para>Gets or sets the status associated with the appointment currently being edited in the custom in-place editor.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Scheduler.AppointmentStatus"/> object, specifying the status of the appointment.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditorBase.StatusProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditorBase.Storage">
            <summary>
                <para>Gets the Scheduler Storage holding data of the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>, which contains the appointment currently being edited in the in-place editor.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.SchedulerStorage"/> object, which is the data storage of the <b>Scheduler</b> control.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditorBase.StorageProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditorBase.Subject">
            <summary>
                <para>Gets or sets the subject of the appointment currently being edited in the custom in-place editor.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value that specifies the text of the appointment subject.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.UI.AppointmentInplaceEditorBase.SubjectProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualResourceAllDayAreaCollection">

            <summary>
                <para>A collection of <see cref="T:DevExpress.Xpf.Scheduler.Drawing.VisualResourceAllDayArea"/> objects.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualResourceAllDayAreaCollection.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualResourceAllDayAreaCollection class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualResourceAllDayArea">

            <summary>
                <para>Provides information on the visual representation of an all-day area, displayed for a particular resource in the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualResourceAllDayArea.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualResourceAllDayArea class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.NavigationDirection">

            <summary>
                <para>Lists the values used to specify the direction of arrows within Navigation Buttons. 


</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.NavigationDirection.Backward">
            <summary>
                <para>An arrow points backwards.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.NavigationDirection.Forward">
            <summary>
                <para>An arrow points forwards.

</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.InplaceEditorEventHandler">

            <summary>
                <para>Represents the method that will handle the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.InplaceEditorShowing"/> event. 

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.InplaceEditorEventHandler.Invoke(System.Object,DevExpress.Xpf.Scheduler.InplaceEditorEventArgs)">
            <summary>
                <para>Represents the method that will handle the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.InplaceEditorShowing"/> event.

</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> which raised the event.

            </param>
            <param name="e">
		An <see cref="T:DevExpress.Xpf.Scheduler.InplaceEditorEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.InplaceEditorEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.InplaceEditorShowing"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.InplaceEditorEventArgs.#ctor(DevExpress.XtraScheduler.Appointment)">
            <summary>
                <para>Initializes a new instance of the InplaceEditorEventArgs class with the specified appointment.
</para>
            </summary>
            <param name="apt">
		A <see cref="T:DevExpress.XtraScheduler.Appointment"/> value which represents the event's appointment. This value is assigned to the <see cref="P:DevExpress.XtraScheduler.AppointmentEventArgs.Appointment"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.InplaceEditorEventArgs.Bounds">
            <summary>
                <para>Gets or sets the width, height and location of the in-place editor.
</para>
            </summary>
            <value>An instance of the <see cref="T:System.Windows.Rect"/> structure.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.InplaceEditorEventArgs.InplaceEditor">
            <summary>
                <para>Gets or sets the in-place editor which is invoked when an end-user adds a new appointment or edits an existing one "in place".

</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraScheduler.ISchedulerInplaceEditorEx"/> object which represents the in-place editor.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.CustomizeVisualViewInfoEventHandler">

            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.CustomizeVisualViewInfo"/> event.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.CustomizeVisualViewInfoEventHandler.Invoke(System.Object,DevExpress.Xpf.Scheduler.CustomizeVisualViewInfoEventArgs)">
            <summary>
                <para>A method that will handle the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.CustomizeVisualViewInfo"/> event.

</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> which raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Xpf.Scheduler.CustomizeVisualViewInfoEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.CustomizeVisualViewInfoEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Scheduler.SchedulerControl.CustomizeVisualViewInfo"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.CustomizeVisualViewInfoEventArgs.#ctor(DevExpress.Xpf.Scheduler.Drawing.VisualViewInfoBase)">
            <summary>
                <para>Initializes a new instance of the CustomizeVisualViewInfoEventArgs class with the specified visual view information.
</para>
            </summary>
            <param name="visualViewInfo">
		A <see cref="T:DevExpress.Xpf.Scheduler.Drawing.VisualViewInfoBase"/> class descendant.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Scheduler.CustomizeVisualViewInfoEventArgs.VisualViewInfo">
            <summary>
                <para>Gets an object which contains information on the visual representation of visual elements displayed in the Scheduler Control, depending on the current active view and group type currently applied to appointments.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.Drawing.VisualViewInfoBase"/> class descendant.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualResourcesBasedViewInfo">

            <summary>
                <para>Serves as a base for classes that provide information on the visual representation of the view's elements displayed in the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/>, based on resources.

</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Scheduler.Drawing.VisualResourcesBasedViewInfo.ResourceContainers">
            <summary>
                <para>Gets the collection on objects containing information on the visual representation of resource containers that are used as base for displaying data in a Scheduler Control.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Scheduler.Drawing.VisualResourcesCollection"/> collection.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Scheduler.Drawing.VisualResourcesBasedViewInfo.ResourceContainersProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualWeekViewGroupByResource">

            <summary>
                <para>Provides information on visual elements displayed within the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> when Week View is a currently active view and appointments are grouped by resources.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualWeekViewGroupByResource.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualWeekViewGroupByResource class with default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Scheduler.Drawing.VisualWeekViewGroupByNone">

            <summary>
                <para>Provides information on visual elements displayed within the <see cref="T:DevExpress.Xpf.Scheduler.SchedulerControl"/> when Week View is a currently active view and no grouping is applied to appointments.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Scheduler.Drawing.VisualWeekViewGroupByNone.#ctor">
            <summary>
                <para>Initializes a new instance of the VisualWeekViewGroupByNone class with default settings.
</para>
            </summary>


        </member>
    </members>
</doc>
