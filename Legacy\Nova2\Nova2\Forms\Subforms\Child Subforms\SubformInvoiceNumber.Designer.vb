<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformInvoiceNumber
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformInvoiceNumber))
        Me.LabelSellPrice = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditInvoiceNumber = New DevExpress.XtraEditors.TextEdit()
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonOK = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.TextEditInvoiceAmount = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelUser = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkMediaService = New DevExpress.XtraEditors.LabelControl()
        CType(Me.TextEditInvoiceNumber.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditInvoiceAmount.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!)
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Size = New System.Drawing.Size(233, 37)
        Me.LabelTitle.TabIndex = 0
        Me.LabelTitle.Text = "Invoice Number"
        '
        'LabelSellPrice
        '
        Me.LabelSellPrice.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSellPrice.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelSellPrice.Location = New System.Drawing.Point(19, 101)
        Me.LabelSellPrice.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelSellPrice.Name = "LabelSellPrice"
        Me.LabelSellPrice.Size = New System.Drawing.Size(115, 17)
        Me.LabelSellPrice.TabIndex = 7
        Me.LabelSellPrice.Text = "Invoice Number:"
        '
        'TextEditInvoiceNumber
        '
        Me.TextEditInvoiceNumber.EditValue = ""
        Me.TextEditInvoiceNumber.Location = New System.Drawing.Point(203, 97)
        Me.TextEditInvoiceNumber.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.TextEditInvoiceNumber.Name = "TextEditInvoiceNumber"
        Me.TextEditInvoiceNumber.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditInvoiceNumber.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditInvoiceNumber.Properties.Appearance.Options.UseFont = True
        Me.TextEditInvoiceNumber.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditInvoiceNumber.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TextEditInvoiceNumber.Size = New System.Drawing.Size(335, 24)
        Me.TextEditInvoiceNumber.TabIndex = 8
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        '
        'ButtonOK
        '
        Me.ButtonOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonOK.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonOK.Appearance.Options.UseFont = True
        Me.ButtonOK.ImageIndex = 2
        Me.ButtonOK.ImageList = Me.ImageList24x24
        Me.ButtonOK.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonOK.Location = New System.Drawing.Point(445, 167)
        Me.ButtonOK.LookAndFeel.SkinName = "Black"
        Me.ButtonOK.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonOK.Margin = New System.Windows.Forms.Padding(4, 16, 4, 4)
        Me.ButtonOK.Name = "ButtonOK"
        Me.ButtonOK.Size = New System.Drawing.Size(129, 37)
        Me.ButtonOK.TabIndex = 10
        Me.ButtonOK.Text = "OK"
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(581, 167)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Margin = New System.Windows.Forms.Padding(4, 16, 4, 4)
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(129, 37)
        Me.ButtonCancel.TabIndex = 11
        Me.ButtonCancel.Text = "Cancel"
        '
        'TextEditInvoiceAmount
        '
        Me.TextEditInvoiceAmount.EditValue = ""
        Me.TextEditInvoiceAmount.Location = New System.Drawing.Point(203, 128)
        Me.TextEditInvoiceAmount.Margin = New System.Windows.Forms.Padding(4, 4, 0, 4)
        Me.TextEditInvoiceAmount.Name = "TextEditInvoiceAmount"
        Me.TextEditInvoiceAmount.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditInvoiceAmount.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditInvoiceAmount.Properties.Appearance.Options.UseFont = True
        Me.TextEditInvoiceAmount.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditInvoiceAmount.Properties.Appearance.Options.UseTextOptions = True
        Me.TextEditInvoiceAmount.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.TextEditInvoiceAmount.Properties.AppearanceReadOnly.BackColor = System.Drawing.Color.Gainsboro
        Me.TextEditInvoiceAmount.Properties.AppearanceReadOnly.Options.UseBackColor = True
        Me.TextEditInvoiceAmount.Properties.Mask.EditMask = "c4"
        Me.TextEditInvoiceAmount.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditInvoiceAmount.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TextEditInvoiceAmount.Size = New System.Drawing.Size(335, 24)
        Me.TextEditInvoiceAmount.TabIndex = 15
        '
        'LabelControl1
        '
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl1.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl1.Location = New System.Drawing.Point(19, 135)
        Me.LabelControl1.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(116, 17)
        Me.LabelControl1.TabIndex = 14
        Me.LabelControl1.Text = "Invoice Amount:"
        '
        'LabelUser
        '
        Me.LabelUser.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelUser.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelUser.Location = New System.Drawing.Point(19, 62)
        Me.LabelUser.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelUser.Name = "LabelUser"
        Me.LabelUser.Size = New System.Drawing.Size(100, 17)
        Me.LabelUser.TabIndex = 16
        Me.LabelUser.Text = "Media Service:"
        '
        'HyperlinkMediaService
        '
        Me.HyperlinkMediaService.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkMediaService.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkMediaService.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkMediaService.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkMediaService.Location = New System.Drawing.Point(203, 62)
        Me.HyperlinkMediaService.Margin = New System.Windows.Forms.Padding(4)
        Me.HyperlinkMediaService.Name = "HyperlinkMediaService"
        Me.HyperlinkMediaService.Size = New System.Drawing.Size(57, 17)
        Me.HyperlinkMediaService.TabIndex = 17
        Me.HyperlinkMediaService.Text = "Select..."
        '
        'SubformInvoiceNumber
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(9.0!, 17.0!)
        Me.Controls.Add(Me.LabelUser)
        Me.Controls.Add(Me.HyperlinkMediaService)
        Me.Controls.Add(Me.TextEditInvoiceAmount)
        Me.Controls.Add(Me.LabelControl1)
        Me.Controls.Add(Me.ButtonOK)
        Me.Controls.Add(Me.ButtonCancel)
        Me.Controls.Add(Me.LabelSellPrice)
        Me.Controls.Add(Me.TextEditInvoiceNumber)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(5)
        Me.Name = "SubformInvoiceNumber"
        Me.Padding = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.Size = New System.Drawing.Size(725, 219)
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        Me.Controls.SetChildIndex(Me.TextEditInvoiceNumber, 0)
        Me.Controls.SetChildIndex(Me.LabelSellPrice, 0)
        Me.Controls.SetChildIndex(Me.ButtonCancel, 0)
        Me.Controls.SetChildIndex(Me.ButtonOK, 0)
        Me.Controls.SetChildIndex(Me.LabelControl1, 0)
        Me.Controls.SetChildIndex(Me.TextEditInvoiceAmount, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkMediaService, 0)
        Me.Controls.SetChildIndex(Me.LabelUser, 0)
        CType(Me.TextEditInvoiceNumber.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditInvoiceAmount.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents LabelSellPrice As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditInvoiceNumber As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents ButtonOK As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents TextEditInvoiceAmount As TextEdit
    Friend WithEvents LabelControl1 As LabelControl
    Friend WithEvents LabelUser As LabelControl
    Friend WithEvents HyperlinkMediaService As LabelControl
End Class
