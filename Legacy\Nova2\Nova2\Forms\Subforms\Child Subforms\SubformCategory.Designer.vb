<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformCategory
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformCategory))
        Dim SuperToolTip1 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem1 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem1 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip2 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem2 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem2 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim SuperToolTip3 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem3 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem3 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip4 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem4 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem4 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle4 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle5 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle6 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Me.ImageList16x16 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonSave = New DevExpress.XtraEditors.SimpleButton()
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.PanelDetails = New System.Windows.Forms.Panel()
        Me.CheckEditDormant = New DevExpress.XtraEditors.CheckEdit()
        Me.TextEditCategoryName = New DevExpress.XtraEditors.TextEdit()
        Me.LabelCategoryName = New DevExpress.XtraEditors.LabelControl()
        Me.GroupControlMediaCategory = New DevExpress.XtraEditors.GroupControl()
        Me.LabelControl14 = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearchMediaCategory = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearchMediaCategory = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearchMediaCategory = New DevExpress.XtraEditors.PictureEdit()
        Me.ButtonRemoveMediaCategory = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAddMediaCategory = New DevExpress.XtraEditors.SimpleButton()
        Me.GridMediaCategory = New System.Windows.Forms.DataGridView()
        Me.MediaNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.MediaID = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.LabelControl3 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl4 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl9 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl10 = New DevExpress.XtraEditors.LabelControl()
        Me.XtraTabControl1 = New DevExpress.XtraTab.XtraTabControl()
        Me.TabPageDetails = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.TableLayoutPanelDetails = New System.Windows.Forms.TableLayoutPanel()
        Me.TabPageMediaServices = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel3 = New System.Windows.Forms.Panel()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.GroupControl1 = New DevExpress.XtraEditors.GroupControl()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearchStoreMediaCategoryPermissions = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearStoreMediaCategoryPermissions = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearchStoreMediaCategoryPermissions = New DevExpress.XtraEditors.PictureEdit()
        Me.GridStoreMediaCategoryPermissions = New System.Windows.Forms.DataGridView()
        Me.StoreID = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.StoreName = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.StoreNumber = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.RegionName = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.isProhibited = New System.Windows.Forms.DataGridViewCheckBoxColumn()
        Me.CheckEditAllowsCrossover = New DevExpress.XtraEditors.CheckEdit()
        Me.PanelDetails.SuspendLayout()
        CType(Me.CheckEditDormant.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditCategoryName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControlMediaCategory, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlMediaCategory.SuspendLayout()
        CType(Me.TextEditSearchMediaCategory.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearchMediaCategory.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearchMediaCategory.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridMediaCategory, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabControl1.SuspendLayout()
        Me.TabPageDetails.SuspendLayout()
        Me.Panel1.SuspendLayout()
        Me.TableLayoutPanelDetails.SuspendLayout()
        Me.TabPageMediaServices.SuspendLayout()
        Me.Panel3.SuspendLayout()
        Me.TableLayoutPanel1.SuspendLayout()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl1.SuspendLayout()
        CType(Me.TextEditSearchStoreMediaCategoryPermissions.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearStoreMediaCategoryPermissions.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearchStoreMediaCategoryPermissions.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridStoreMediaCategoryPermissions, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditAllowsCrossover.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Margin = New System.Windows.Forms.Padding(5, 5, 5, 21)
        Me.LabelTitle.Size = New System.Drawing.Size(225, 37)
        Me.LabelTitle.TabIndex = 0
        Me.LabelTitle.Text = "(new category)"
        '
        'ImageList16x16
        '
        Me.ImageList16x16.ImageStream = CType(resources.GetObject("ImageList16x16.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList16x16.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList16x16.Images.SetKeyName(0, "add.png")
        Me.ImageList16x16.Images.SetKeyName(1, "pencil.png")
        Me.ImageList16x16.Images.SetKeyName(2, "delete.png")
        '
        'ButtonSave
        '
        Me.ButtonSave.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonSave.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonSave.Appearance.Options.UseFont = True
        Me.ButtonSave.ImageIndex = 1
        Me.ButtonSave.ImageList = Me.ImageList24x24
        Me.ButtonSave.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonSave.Location = New System.Drawing.Point(778, 666)
        Me.ButtonSave.LookAndFeel.SkinName = "Black"
        Me.ButtonSave.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonSave.Margin = New System.Windows.Forms.Padding(4, 16, 4, 4)
        Me.ButtonSave.Name = "ButtonSave"
        Me.ButtonSave.Size = New System.Drawing.Size(129, 37)
        Me.ButtonSave.TabIndex = 2
        Me.ButtonSave.Text = "Save"
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(914, 666)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Margin = New System.Windows.Forms.Padding(4, 16, 4, 4)
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(129, 37)
        Me.ButtonCancel.TabIndex = 3
        Me.ButtonCancel.Text = "Cancel"
        '
        'PanelDetails
        '
        Me.PanelDetails.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelDetails.Controls.Add(Me.CheckEditAllowsCrossover)
        Me.PanelDetails.Controls.Add(Me.CheckEditDormant)
        Me.PanelDetails.Controls.Add(Me.TextEditCategoryName)
        Me.PanelDetails.Controls.Add(Me.LabelCategoryName)
        Me.PanelDetails.Location = New System.Drawing.Point(0, 98)
        Me.PanelDetails.Margin = New System.Windows.Forms.Padding(0)
        Me.PanelDetails.Name = "PanelDetails"
        Me.PanelDetails.Size = New System.Drawing.Size(982, 400)
        Me.PanelDetails.TabIndex = 2
        '
        'CheckEditDormant
        '
        Me.CheckEditDormant.Location = New System.Drawing.Point(154, 38)
        Me.CheckEditDormant.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.CheckEditDormant.Name = "CheckEditDormant"
        Me.CheckEditDormant.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditDormant.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditDormant.Properties.Appearance.Options.UseFont = True
        Me.CheckEditDormant.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditDormant.Properties.AutoWidth = True
        Me.CheckEditDormant.Properties.Caption = "This category is dormant"
        Me.CheckEditDormant.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditDormant.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditDormant.Size = New System.Drawing.Size(198, 21)
        Me.CheckEditDormant.TabIndex = 2
        '
        'TextEditCategoryName
        '
        Me.TextEditCategoryName.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditCategoryName.Location = New System.Drawing.Point(157, 4)
        Me.TextEditCategoryName.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.TextEditCategoryName.Name = "TextEditCategoryName"
        Me.TextEditCategoryName.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditCategoryName.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditCategoryName.Properties.Appearance.Options.UseFont = True
        Me.TextEditCategoryName.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditCategoryName.Properties.MaxLength = 200
        Me.TextEditCategoryName.Size = New System.Drawing.Size(822, 24)
        Me.TextEditCategoryName.TabIndex = 1
        '
        'LabelCategoryName
        '
        Me.LabelCategoryName.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCategoryName.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCategoryName.Location = New System.Drawing.Point(0, 8)
        Me.LabelCategoryName.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.LabelCategoryName.Name = "LabelCategoryName"
        Me.LabelCategoryName.Size = New System.Drawing.Size(114, 17)
        Me.LabelCategoryName.TabIndex = 0
        Me.LabelCategoryName.Text = "Category Name:"
        '
        'GroupControlMediaCategory
        '
        Me.GroupControlMediaCategory.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlMediaCategory.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlMediaCategory.Appearance.Options.UseFont = True
        Me.GroupControlMediaCategory.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlMediaCategory.AppearanceCaption.Options.UseFont = True
        Me.GroupControlMediaCategory.Controls.Add(Me.LabelControl14)
        Me.GroupControlMediaCategory.Controls.Add(Me.TextEditSearchMediaCategory)
        Me.GroupControlMediaCategory.Controls.Add(Me.PictureClearSearchMediaCategory)
        Me.GroupControlMediaCategory.Controls.Add(Me.PictureAdvancedSearchMediaCategory)
        Me.GroupControlMediaCategory.Controls.Add(Me.ButtonRemoveMediaCategory)
        Me.GroupControlMediaCategory.Controls.Add(Me.ButtonAddMediaCategory)
        Me.GroupControlMediaCategory.Controls.Add(Me.GridMediaCategory)
        Me.GroupControlMediaCategory.Location = New System.Drawing.Point(4, 119)
        Me.GroupControlMediaCategory.LookAndFeel.SkinName = "Black"
        Me.GroupControlMediaCategory.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlMediaCategory.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.GroupControlMediaCategory.Name = "GroupControlMediaCategory"
        Me.GroupControlMediaCategory.Size = New System.Drawing.Size(972, 192)
        Me.GroupControlMediaCategory.TabIndex = 5
        Me.GroupControlMediaCategory.Text = "Media Services Permitted"
        '
        'LabelControl14
        '
        Me.LabelControl14.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl14.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl14.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl14.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl14.Location = New System.Drawing.Point(769, 161)
        Me.LabelControl14.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.LabelControl14.Name = "LabelControl14"
        Me.LabelControl14.Size = New System.Drawing.Size(58, 17)
        Me.LabelControl14.TabIndex = 4
        Me.LabelControl14.Text = "Search:"
        '
        'TextEditSearchMediaCategory
        '
        Me.TextEditSearchMediaCategory.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchMediaCategory.EditValue = ""
        Me.TextEditSearchMediaCategory.Location = New System.Drawing.Point(834, 157)
        Me.TextEditSearchMediaCategory.Margin = New System.Windows.Forms.Padding(4, 5, 4, 7)
        Me.TextEditSearchMediaCategory.Name = "TextEditSearchMediaCategory"
        Me.TextEditSearchMediaCategory.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchMediaCategory.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchMediaCategory.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchMediaCategory.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchMediaCategory.Size = New System.Drawing.Size(103, 24)
        Me.TextEditSearchMediaCategory.TabIndex = 5
        '
        'PictureClearSearchMediaCategory
        '
        Me.PictureClearSearchMediaCategory.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchMediaCategory.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchMediaCategory.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchMediaCategory.Location = New System.Drawing.Point(945, 4)
        Me.PictureClearSearchMediaCategory.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.PictureClearSearchMediaCategory.Name = "PictureClearSearchMediaCategory"
        Me.PictureClearSearchMediaCategory.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchMediaCategory.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchMediaCategory.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchMediaCategory.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem1.Text = "Clear Search"
        ToolTipItem1.LeftIndent = 6
        ToolTipItem1.Text = "Click here to clear all search boxes."
        SuperToolTip1.Items.Add(ToolTipTitleItem1)
        SuperToolTip1.Items.Add(ToolTipItem1)
        Me.PictureClearSearchMediaCategory.SuperTip = SuperToolTip1
        Me.PictureClearSearchMediaCategory.TabIndex = 0
        Me.PictureClearSearchMediaCategory.TabStop = True
        '
        'PictureAdvancedSearchMediaCategory
        '
        Me.PictureAdvancedSearchMediaCategory.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearchMediaCategory.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearchMediaCategory.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearchMediaCategory.Location = New System.Drawing.Point(945, 160)
        Me.PictureAdvancedSearchMediaCategory.Margin = New System.Windows.Forms.Padding(4, 8, 4, 9)
        Me.PictureAdvancedSearchMediaCategory.Name = "PictureAdvancedSearchMediaCategory"
        Me.PictureAdvancedSearchMediaCategory.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearchMediaCategory.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearchMediaCategory.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearchMediaCategory.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem2.Text = "Advanced Search"
        ToolTipItem2.LeftIndent = 6
        ToolTipItem2.Text = "Click here to search individual column values."
        SuperToolTip2.Items.Add(ToolTipTitleItem2)
        SuperToolTip2.Items.Add(ToolTipItem2)
        Me.PictureAdvancedSearchMediaCategory.SuperTip = SuperToolTip2
        Me.PictureAdvancedSearchMediaCategory.TabIndex = 6
        Me.PictureAdvancedSearchMediaCategory.TabStop = True
        '
        'ButtonRemoveMediaCategory
        '
        Me.ButtonRemoveMediaCategory.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonRemoveMediaCategory.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonRemoveMediaCategory.Appearance.Options.UseFont = True
        Me.ButtonRemoveMediaCategory.ImageIndex = 2
        Me.ButtonRemoveMediaCategory.ImageList = Me.ImageList16x16
        Me.ButtonRemoveMediaCategory.Location = New System.Drawing.Point(111, 156)
        Me.ButtonRemoveMediaCategory.LookAndFeel.SkinName = "Black"
        Me.ButtonRemoveMediaCategory.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonRemoveMediaCategory.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.ButtonRemoveMediaCategory.Name = "ButtonRemoveMediaCategory"
        Me.ButtonRemoveMediaCategory.Size = New System.Drawing.Size(96, 30)
        Me.ButtonRemoveMediaCategory.TabIndex = 3
        Me.ButtonRemoveMediaCategory.Text = "Remove"
        '
        'ButtonAddMediaCategory
        '
        Me.ButtonAddMediaCategory.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAddMediaCategory.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAddMediaCategory.Appearance.Options.UseFont = True
        Me.ButtonAddMediaCategory.ImageIndex = 0
        Me.ButtonAddMediaCategory.ImageList = Me.ImageList16x16
        Me.ButtonAddMediaCategory.Location = New System.Drawing.Point(6, 156)
        Me.ButtonAddMediaCategory.LookAndFeel.SkinName = "Black"
        Me.ButtonAddMediaCategory.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAddMediaCategory.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.ButtonAddMediaCategory.Name = "ButtonAddMediaCategory"
        Me.ButtonAddMediaCategory.Size = New System.Drawing.Size(96, 30)
        Me.ButtonAddMediaCategory.TabIndex = 2
        Me.ButtonAddMediaCategory.Text = "Add"
        '
        'GridMediaCategory
        '
        Me.GridMediaCategory.AllowUserToAddRows = False
        Me.GridMediaCategory.AllowUserToDeleteRows = False
        Me.GridMediaCategory.AllowUserToOrderColumns = True
        Me.GridMediaCategory.AllowUserToResizeRows = False
        DataGridViewCellStyle1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridMediaCategory.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle1
        Me.GridMediaCategory.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridMediaCategory.BackgroundColor = System.Drawing.Color.White
        Me.GridMediaCategory.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridMediaCategory.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridMediaCategory.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle2.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle2.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridMediaCategory.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle2
        Me.GridMediaCategory.ColumnHeadersHeight = 22
        Me.GridMediaCategory.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridMediaCategory.ColumnHeadersVisible = False
        Me.GridMediaCategory.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.MediaNameColumn, Me.MediaID})
        Me.GridMediaCategory.EnableHeadersVisualStyles = False
        Me.GridMediaCategory.GridColor = System.Drawing.Color.White
        Me.GridMediaCategory.Location = New System.Drawing.Point(3, 29)
        Me.GridMediaCategory.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.GridMediaCategory.Name = "GridMediaCategory"
        Me.GridMediaCategory.ReadOnly = True
        Me.GridMediaCategory.RowHeadersVisible = False
        Me.GridMediaCategory.RowHeadersWidth = 51
        DataGridViewCellStyle3.ForeColor = System.Drawing.Color.DimGray
        Me.GridMediaCategory.RowsDefaultCellStyle = DataGridViewCellStyle3
        Me.GridMediaCategory.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridMediaCategory.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridMediaCategory.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridMediaCategory.RowTemplate.Height = 19
        Me.GridMediaCategory.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridMediaCategory.ShowCellToolTips = False
        Me.GridMediaCategory.Size = New System.Drawing.Size(967, 119)
        Me.GridMediaCategory.StandardTab = True
        Me.GridMediaCategory.TabIndex = 1
        '
        'MediaNameColumn
        '
        Me.MediaNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.MediaNameColumn.DataPropertyName = "MediaName"
        Me.MediaNameColumn.HeaderText = "Media Service"
        Me.MediaNameColumn.MinimumWidth = 6
        Me.MediaNameColumn.Name = "MediaNameColumn"
        Me.MediaNameColumn.ReadOnly = True
        '
        'MediaID
        '
        Me.MediaID.DataPropertyName = "MediaID"
        Me.MediaID.HeaderText = "MediaID"
        Me.MediaID.MinimumWidth = 6
        Me.MediaID.Name = "MediaID"
        Me.MediaID.ReadOnly = True
        Me.MediaID.Visible = False
        Me.MediaID.Width = 125
        '
        'LabelControl3
        '
        Me.LabelControl3.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl3.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl3.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl3.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top
        Me.LabelControl3.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap
        Me.LabelControl3.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelControl3.Location = New System.Drawing.Point(4, 48)
        Me.LabelControl3.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelControl3.Name = "LabelControl3"
        Me.LabelControl3.Size = New System.Drawing.Size(974, 34)
        Me.LabelControl3.TabIndex = 1
        Me.LabelControl3.Text = resources.GetString("LabelControl3.Text")
        '
        'LabelControl4
        '
        Me.LabelControl4.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl4.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl4.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelControl4.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl4.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelControl4.LineVisible = True
        Me.LabelControl4.Location = New System.Drawing.Point(4, 4)
        Me.LabelControl4.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelControl4.Name = "LabelControl4"
        Me.LabelControl4.Size = New System.Drawing.Size(974, 24)
        Me.LabelControl4.TabIndex = 0
        Me.LabelControl4.Text = "Category Details"
        '
        'LabelControl9
        '
        Me.LabelControl9.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl9.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl9.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelControl9.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl9.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelControl9.LineVisible = True
        Me.LabelControl9.Location = New System.Drawing.Point(4, 4)
        Me.LabelControl9.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelControl9.Name = "LabelControl9"
        Me.LabelControl9.Size = New System.Drawing.Size(972, 24)
        Me.LabelControl9.TabIndex = 3
        Me.LabelControl9.Text = "Category Media Service Management"
        '
        'LabelControl10
        '
        Me.LabelControl10.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl10.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl10.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl10.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top
        Me.LabelControl10.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap
        Me.LabelControl10.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelControl10.Location = New System.Drawing.Point(4, 48)
        Me.LabelControl10.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelControl10.Name = "LabelControl10"
        Me.LabelControl10.Size = New System.Drawing.Size(972, 51)
        Me.LabelControl10.TabIndex = 4
        Me.LabelControl10.Text = resources.GetString("LabelControl10.Text")
        '
        'XtraTabControl1
        '
        Me.XtraTabControl1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.XtraTabControl1.AppearancePage.Header.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.XtraTabControl1.AppearancePage.Header.Options.UseFont = True
        Me.XtraTabControl1.Location = New System.Drawing.Point(15, 75)
        Me.XtraTabControl1.LookAndFeel.SkinName = "Black"
        Me.XtraTabControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.XtraTabControl1.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.XtraTabControl1.Name = "XtraTabControl1"
        Me.XtraTabControl1.SelectedTabPage = Me.TabPageDetails
        Me.XtraTabControl1.Size = New System.Drawing.Size(1027, 571)
        Me.XtraTabControl1.TabIndex = 1
        Me.XtraTabControl1.TabPages.AddRange(New DevExpress.XtraTab.XtraTabPage() {Me.TabPageDetails, Me.TabPageMediaServices})
        '
        'TabPageDetails
        '
        Me.TabPageDetails.Controls.Add(Me.Panel1)
        Me.TabPageDetails.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.TabPageDetails.Name = "TabPageDetails"
        Me.TabPageDetails.Size = New System.Drawing.Size(1021, 539)
        Me.TabPageDetails.Text = "Details"
        '
        'Panel1
        '
        Me.Panel1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Panel1.Controls.Add(Me.TableLayoutPanelDetails)
        Me.Panel1.Location = New System.Drawing.Point(4, 4)
        Me.Panel1.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(1013, 530)
        Me.Panel1.TabIndex = 3
        '
        'TableLayoutPanelDetails
        '
        Me.TableLayoutPanelDetails.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanelDetails.BackColor = System.Drawing.Color.Transparent
        Me.TableLayoutPanelDetails.ColumnCount = 1
        Me.TableLayoutPanelDetails.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanelDetails.Controls.Add(Me.PanelDetails, 0, 2)
        Me.TableLayoutPanelDetails.Controls.Add(Me.LabelControl4, 0, 0)
        Me.TableLayoutPanelDetails.Controls.Add(Me.LabelControl3, 0, 1)
        Me.TableLayoutPanelDetails.Location = New System.Drawing.Point(15, 16)
        Me.TableLayoutPanelDetails.Margin = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.TableLayoutPanelDetails.Name = "TableLayoutPanelDetails"
        Me.TableLayoutPanelDetails.RowCount = 3
        Me.TableLayoutPanelDetails.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanelDetails.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanelDetails.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanelDetails.Size = New System.Drawing.Size(982, 498)
        Me.TableLayoutPanelDetails.TabIndex = 3
        '
        'TabPageMediaServices
        '
        Me.TabPageMediaServices.Controls.Add(Me.Panel3)
        Me.TabPageMediaServices.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.TabPageMediaServices.Name = "TabPageMediaServices"
        Me.TabPageMediaServices.Size = New System.Drawing.Size(1021, 539)
        Me.TabPageMediaServices.Text = "Media Services"
        '
        'Panel3
        '
        Me.Panel3.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel3.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Panel3.Controls.Add(Me.TableLayoutPanel1)
        Me.Panel3.Location = New System.Drawing.Point(4, 4)
        Me.Panel3.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.Panel3.Name = "Panel3"
        Me.Panel3.Size = New System.Drawing.Size(1011, 522)
        Me.Panel3.TabIndex = 4
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanel1.BackColor = System.Drawing.Color.Transparent
        Me.TableLayoutPanel1.ColumnCount = 1
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.GroupControl1, 0, 3)
        Me.TableLayoutPanel1.Controls.Add(Me.GroupControlMediaCategory, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.LabelControl9, 0, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.LabelControl10, 0, 1)
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(15, 16)
        Me.TableLayoutPanel1.Margin = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 4
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(980, 490)
        Me.TableLayoutPanel1.TabIndex = 3
        '
        'GroupControl1
        '
        Me.GroupControl1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControl1.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControl1.Appearance.Options.UseFont = True
        Me.GroupControl1.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControl1.AppearanceCaption.Options.UseFont = True
        Me.GroupControl1.Controls.Add(Me.LabelControl1)
        Me.GroupControl1.Controls.Add(Me.TextEditSearchStoreMediaCategoryPermissions)
        Me.GroupControl1.Controls.Add(Me.PictureClearStoreMediaCategoryPermissions)
        Me.GroupControl1.Controls.Add(Me.PictureAdvancedSearchStoreMediaCategoryPermissions)
        Me.GroupControl1.Controls.Add(Me.GridStoreMediaCategoryPermissions)
        Me.GroupControl1.Location = New System.Drawing.Point(4, 319)
        Me.GroupControl1.LookAndFeel.SkinName = "Black"
        Me.GroupControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControl1.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.GroupControl1.Name = "GroupControl1"
        Me.GroupControl1.Size = New System.Drawing.Size(972, 167)
        Me.GroupControl1.TabIndex = 6
        Me.GroupControl1.Text = "Store Category Permissions on Selected Media (Ticked = Not Permitted)"
        '
        'LabelControl1
        '
        Me.LabelControl1.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl1.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl1.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl1.Location = New System.Drawing.Point(769, 135)
        Me.LabelControl1.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(58, 17)
        Me.LabelControl1.TabIndex = 4
        Me.LabelControl1.Text = "Search:"
        '
        'TextEditSearchStoreMediaCategoryPermissions
        '
        Me.TextEditSearchStoreMediaCategoryPermissions.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchStoreMediaCategoryPermissions.EditValue = ""
        Me.TextEditSearchStoreMediaCategoryPermissions.Location = New System.Drawing.Point(834, 131)
        Me.TextEditSearchStoreMediaCategoryPermissions.Margin = New System.Windows.Forms.Padding(4, 5, 4, 7)
        Me.TextEditSearchStoreMediaCategoryPermissions.Name = "TextEditSearchStoreMediaCategoryPermissions"
        Me.TextEditSearchStoreMediaCategoryPermissions.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchStoreMediaCategoryPermissions.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchStoreMediaCategoryPermissions.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchStoreMediaCategoryPermissions.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchStoreMediaCategoryPermissions.Size = New System.Drawing.Size(103, 24)
        Me.TextEditSearchStoreMediaCategoryPermissions.TabIndex = 5
        '
        'PictureClearStoreMediaCategoryPermissions
        '
        Me.PictureClearStoreMediaCategoryPermissions.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearStoreMediaCategoryPermissions.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearStoreMediaCategoryPermissions.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearStoreMediaCategoryPermissions.Location = New System.Drawing.Point(945, 4)
        Me.PictureClearStoreMediaCategoryPermissions.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.PictureClearStoreMediaCategoryPermissions.Name = "PictureClearStoreMediaCategoryPermissions"
        Me.PictureClearStoreMediaCategoryPermissions.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearStoreMediaCategoryPermissions.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearStoreMediaCategoryPermissions.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearStoreMediaCategoryPermissions.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem3.Text = "Clear Search"
        ToolTipItem3.LeftIndent = 6
        ToolTipItem3.Text = "Click here to clear all search boxes."
        SuperToolTip3.Items.Add(ToolTipTitleItem3)
        SuperToolTip3.Items.Add(ToolTipItem3)
        Me.PictureClearStoreMediaCategoryPermissions.SuperTip = SuperToolTip3
        Me.PictureClearStoreMediaCategoryPermissions.TabIndex = 0
        Me.PictureClearStoreMediaCategoryPermissions.TabStop = True
        '
        'PictureAdvancedSearchStoreMediaCategoryPermissions
        '
        Me.PictureAdvancedSearchStoreMediaCategoryPermissions.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearchStoreMediaCategoryPermissions.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearchStoreMediaCategoryPermissions.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearchStoreMediaCategoryPermissions.Location = New System.Drawing.Point(945, 134)
        Me.PictureAdvancedSearchStoreMediaCategoryPermissions.Margin = New System.Windows.Forms.Padding(4, 8, 4, 9)
        Me.PictureAdvancedSearchStoreMediaCategoryPermissions.Name = "PictureAdvancedSearchStoreMediaCategoryPermissions"
        Me.PictureAdvancedSearchStoreMediaCategoryPermissions.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearchStoreMediaCategoryPermissions.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearchStoreMediaCategoryPermissions.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearchStoreMediaCategoryPermissions.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem4.Text = "Advanced Search"
        ToolTipItem4.LeftIndent = 6
        ToolTipItem4.Text = "Click here to search individual column values."
        SuperToolTip4.Items.Add(ToolTipTitleItem4)
        SuperToolTip4.Items.Add(ToolTipItem4)
        Me.PictureAdvancedSearchStoreMediaCategoryPermissions.SuperTip = SuperToolTip4
        Me.PictureAdvancedSearchStoreMediaCategoryPermissions.TabIndex = 6
        Me.PictureAdvancedSearchStoreMediaCategoryPermissions.TabStop = True
        '
        'GridStoreMediaCategoryPermissions
        '
        Me.GridStoreMediaCategoryPermissions.AllowUserToAddRows = False
        Me.GridStoreMediaCategoryPermissions.AllowUserToDeleteRows = False
        Me.GridStoreMediaCategoryPermissions.AllowUserToOrderColumns = True
        Me.GridStoreMediaCategoryPermissions.AllowUserToResizeRows = False
        DataGridViewCellStyle4.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridStoreMediaCategoryPermissions.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle4
        Me.GridStoreMediaCategoryPermissions.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridStoreMediaCategoryPermissions.BackgroundColor = System.Drawing.Color.White
        Me.GridStoreMediaCategoryPermissions.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridStoreMediaCategoryPermissions.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridStoreMediaCategoryPermissions.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle5.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle5.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle5.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle5.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle5.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle5.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle5.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridStoreMediaCategoryPermissions.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle5
        Me.GridStoreMediaCategoryPermissions.ColumnHeadersHeight = 22
        Me.GridStoreMediaCategoryPermissions.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridStoreMediaCategoryPermissions.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.StoreID, Me.StoreName, Me.StoreNumber, Me.RegionName, Me.isProhibited})
        Me.GridStoreMediaCategoryPermissions.EnableHeadersVisualStyles = False
        Me.GridStoreMediaCategoryPermissions.GridColor = System.Drawing.Color.White
        Me.GridStoreMediaCategoryPermissions.Location = New System.Drawing.Point(3, 29)
        Me.GridStoreMediaCategoryPermissions.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.GridStoreMediaCategoryPermissions.Name = "GridStoreMediaCategoryPermissions"
        Me.GridStoreMediaCategoryPermissions.RowHeadersVisible = False
        Me.GridStoreMediaCategoryPermissions.RowHeadersWidth = 51
        DataGridViewCellStyle6.ForeColor = System.Drawing.Color.DimGray
        Me.GridStoreMediaCategoryPermissions.RowsDefaultCellStyle = DataGridViewCellStyle6
        Me.GridStoreMediaCategoryPermissions.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridStoreMediaCategoryPermissions.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridStoreMediaCategoryPermissions.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridStoreMediaCategoryPermissions.RowTemplate.Height = 19
        Me.GridStoreMediaCategoryPermissions.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridStoreMediaCategoryPermissions.ShowCellToolTips = False
        Me.GridStoreMediaCategoryPermissions.Size = New System.Drawing.Size(967, 99)
        Me.GridStoreMediaCategoryPermissions.StandardTab = True
        Me.GridStoreMediaCategoryPermissions.TabIndex = 1
        '
        'StoreID
        '
        Me.StoreID.DataPropertyName = "StoreID"
        Me.StoreID.HeaderText = "StoreID"
        Me.StoreID.MinimumWidth = 6
        Me.StoreID.Name = "StoreID"
        Me.StoreID.ReadOnly = True
        Me.StoreID.Visible = False
        Me.StoreID.Width = 6
        '
        'StoreName
        '
        Me.StoreName.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.StoreName.DataPropertyName = "StoreName"
        Me.StoreName.HeaderText = "Store Name"
        Me.StoreName.MinimumWidth = 6
        Me.StoreName.Name = "StoreName"
        Me.StoreName.ReadOnly = True
        '
        'StoreNumber
        '
        Me.StoreNumber.DataPropertyName = "StoreNumber"
        Me.StoreNumber.HeaderText = "Store Number"
        Me.StoreNumber.MinimumWidth = 6
        Me.StoreNumber.Name = "StoreNumber"
        Me.StoreNumber.ReadOnly = True
        Me.StoreNumber.Width = 125
        '
        'RegionName
        '
        Me.RegionName.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.RegionName.DataPropertyName = "RegionName"
        Me.RegionName.HeaderText = "Region"
        Me.RegionName.MinimumWidth = 6
        Me.RegionName.Name = "RegionName"
        Me.RegionName.ReadOnly = True
        '
        'isProhibited
        '
        Me.isProhibited.DataPropertyName = "isProhibited"
        Me.isProhibited.HeaderText = "Prohibited"
        Me.isProhibited.MinimumWidth = 6
        Me.isProhibited.Name = "isProhibited"
        Me.isProhibited.Width = 125
        '
        'CheckEditAllowsCrossover
        '
        Me.CheckEditAllowsCrossover.Location = New System.Drawing.Point(154, 67)
        Me.CheckEditAllowsCrossover.Margin = New System.Windows.Forms.Padding(4)
        Me.CheckEditAllowsCrossover.Name = "CheckEditAllowsCrossover"
        Me.CheckEditAllowsCrossover.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditAllowsCrossover.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditAllowsCrossover.Properties.Appearance.Options.UseFont = True
        Me.CheckEditAllowsCrossover.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditAllowsCrossover.Properties.AutoWidth = True
        Me.CheckEditAllowsCrossover.Properties.Caption = "This category allows crossovers"
        Me.CheckEditAllowsCrossover.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditAllowsCrossover.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditAllowsCrossover.Size = New System.Drawing.Size(246, 21)
        Me.CheckEditAllowsCrossover.TabIndex = 3
        '
        'SubformCategory
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(9.0!, 17.0!)
        Me.Controls.Add(Me.XtraTabControl1)
        Me.Controls.Add(Me.ButtonSave)
        Me.Controls.Add(Me.ButtonCancel)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(5, 5, 5, 5)
        Me.Name = "SubformCategory"
        Me.Padding = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.Tag = "821, 543"
        Me.Controls.SetChildIndex(Me.ButtonCancel, 0)
        Me.Controls.SetChildIndex(Me.ButtonSave, 0)
        Me.Controls.SetChildIndex(Me.XtraTabControl1, 0)
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        Me.PanelDetails.ResumeLayout(False)
        Me.PanelDetails.PerformLayout()
        CType(Me.CheckEditDormant.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditCategoryName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControlMediaCategory, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlMediaCategory.ResumeLayout(False)
        CType(Me.TextEditSearchMediaCategory.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearchMediaCategory.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearchMediaCategory.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridMediaCategory, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabControl1.ResumeLayout(False)
        Me.TabPageDetails.ResumeLayout(False)
        Me.Panel1.ResumeLayout(False)
        Me.TableLayoutPanelDetails.ResumeLayout(False)
        Me.TabPageMediaServices.ResumeLayout(False)
        Me.Panel3.ResumeLayout(False)
        Me.TableLayoutPanel1.ResumeLayout(False)
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl1.ResumeLayout(False)
        CType(Me.TextEditSearchStoreMediaCategoryPermissions.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearStoreMediaCategoryPermissions.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearchStoreMediaCategoryPermissions.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridStoreMediaCategoryPermissions, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditAllowsCrossover.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents ImageList16x16 As System.Windows.Forms.ImageList
    Friend WithEvents ButtonSave As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents PanelDetails As System.Windows.Forms.Panel
    Friend WithEvents CheckEditDormant As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents LabelCategoryName As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditCategoryName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents GroupControlMediaCategory As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelControl14 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditSearchMediaCategory As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearchMediaCategory As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearchMediaCategory As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents ButtonRemoveMediaCategory As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonAddMediaCategory As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GridMediaCategory As System.Windows.Forms.DataGridView
    Friend WithEvents LabelControl3 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl4 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl9 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl10 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents XtraTabControl1 As DevExpress.XtraTab.XtraTabControl
    Friend WithEvents TabPageDetails As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents TableLayoutPanelDetails As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents TabPageMediaServices As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel3 As System.Windows.Forms.Panel
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents GroupControl1 As GroupControl
    Friend WithEvents LabelControl1 As LabelControl
    Friend WithEvents TextEditSearchStoreMediaCategoryPermissions As TextEdit
    Friend WithEvents PictureClearStoreMediaCategoryPermissions As PictureEdit
    Friend WithEvents PictureAdvancedSearchStoreMediaCategoryPermissions As PictureEdit
    Friend WithEvents GridStoreMediaCategoryPermissions As DataGridView
    Friend WithEvents MediaNameColumn As DataGridViewTextBoxColumn
    Friend WithEvents MediaID As DataGridViewTextBoxColumn
    Friend WithEvents StoreID As DataGridViewTextBoxColumn
    Friend WithEvents StoreName As DataGridViewTextBoxColumn
    Friend WithEvents StoreNumber As DataGridViewTextBoxColumn
    Friend WithEvents RegionName As DataGridViewTextBoxColumn
    Friend WithEvents isProhibited As DataGridViewCheckBoxColumn
    Friend WithEvents CheckEditAllowsCrossover As CheckEdit
End Class
