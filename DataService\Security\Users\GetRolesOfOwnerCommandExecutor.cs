﻿using DataAccess;

namespace DataService.Security
{
    internal class GetRolesOfOwnerCommandExecutor : CommandExecutor<GetRolesOfOwnerCommand>
    {
        public override void Execute(GetRolesOfOwnerCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.GetRolesOfOwner))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.AddInputParameter("userid", command.UserId);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    command.Table = storedprocedure.OutputTable;
                }
            }
        }
    }
}
