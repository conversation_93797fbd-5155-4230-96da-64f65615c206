﻿using DataAccess;

namespace DataService.Security.Users
{
    class DeleteUserChainPermissionsCommandExecutor : CommandExecutor<DeleteUserChainPermissionsCommand>
    {

        public override void Execute(DeleteUserChainPermissionsCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.DeleteUserChainPermissions))
            {
                storedprocedure.AddInputParameter("Userid", command.UserId);
                storedprocedure.AddInputParameter("ChainID", command.Chains);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
            }
        }

    }
}
