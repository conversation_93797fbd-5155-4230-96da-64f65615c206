﻿using DataAccess;

namespace DataService.Security
{
    internal class GetRoleOwnerCandidatesCommandExecutor : CommandExecutor<GetRoleOwnerCandidatesCommand>
    {
        public override void Execute(GetRoleOwnerCandidatesCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.GetRoleOwnerCandidates))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.AddInputParameter("roleid", command.RoleId);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    command.Table = storedprocedure.OutputTable;
                }
            }
        }
    }
}
