﻿'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated by a tool.
'     Runtime Version:4.0.30319.42000
'
'     Changes to this file may cause incorrect behavior and will be lost if
'     the code is regenerated.
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict Off
Option Explicit On



'''<summary>
'''Represents a strongly typed in-memory cache of data.
'''</summary>
<Global.System.Serializable(),  _
 Global.System.ComponentModel.DesignerCategoryAttribute("code"),  _
 Global.System.ComponentModel.ToolboxItem(true),  _
 Global.System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedDataSetSchema"),  _
 Global.System.Xml.Serialization.XmlRootAttribute("DataSetMediaGap"),  _
 Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.DataSet")>  _
Partial Public Class DataSetMediaGap
    Inherits Global.System.Data.DataSet
    
    Private tableBurstData As BurstDataDataTable
    
    Private tableProvisionalBooking As ProvisionalBookingDataTable
    
    Private tableContractList As ContractListDataTable
    
    Private _schemaSerializationMode As Global.System.Data.SchemaSerializationMode = Global.System.Data.SchemaSerializationMode.IncludeSchema
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Sub New()
        MyBase.New
        Me.BeginInit
        Me.InitClass
        Dim schemaChangedHandler As Global.System.ComponentModel.CollectionChangeEventHandler = AddressOf Me.SchemaChanged
        AddHandler MyBase.Tables.CollectionChanged, schemaChangedHandler
        AddHandler MyBase.Relations.CollectionChanged, schemaChangedHandler
        Me.EndInit
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Protected Sub New(ByVal info As Global.System.Runtime.Serialization.SerializationInfo, ByVal context As Global.System.Runtime.Serialization.StreamingContext)
        MyBase.New(info, context, false)
        If (Me.IsBinarySerialized(info, context) = true) Then
            Me.InitVars(false)
            Dim schemaChangedHandler1 As Global.System.ComponentModel.CollectionChangeEventHandler = AddressOf Me.SchemaChanged
            AddHandler Me.Tables.CollectionChanged, schemaChangedHandler1
            AddHandler Me.Relations.CollectionChanged, schemaChangedHandler1
            Return
        End If
        Dim strSchema As String = CType(info.GetValue("XmlSchema", GetType(String)),String)
        If (Me.DetermineSchemaSerializationMode(info, context) = Global.System.Data.SchemaSerializationMode.IncludeSchema) Then
            Dim ds As Global.System.Data.DataSet = New Global.System.Data.DataSet()
            ds.ReadXmlSchema(New Global.System.Xml.XmlTextReader(New Global.System.IO.StringReader(strSchema)))
            If (Not (ds.Tables("BurstData")) Is Nothing) Then
                MyBase.Tables.Add(New BurstDataDataTable(ds.Tables("BurstData")))
            End If
            If (Not (ds.Tables("ProvisionalBooking")) Is Nothing) Then
                MyBase.Tables.Add(New ProvisionalBookingDataTable(ds.Tables("ProvisionalBooking")))
            End If
            If (Not (ds.Tables("ContractList")) Is Nothing) Then
                MyBase.Tables.Add(New ContractListDataTable(ds.Tables("ContractList")))
            End If
            Me.DataSetName = ds.DataSetName
            Me.Prefix = ds.Prefix
            Me.Namespace = ds.Namespace
            Me.Locale = ds.Locale
            Me.CaseSensitive = ds.CaseSensitive
            Me.EnforceConstraints = ds.EnforceConstraints
            Me.Merge(ds, false, Global.System.Data.MissingSchemaAction.Add)
            Me.InitVars
        Else
            Me.ReadXmlSchema(New Global.System.Xml.XmlTextReader(New Global.System.IO.StringReader(strSchema)))
        End If
        Me.GetSerializationData(info, context)
        Dim schemaChangedHandler As Global.System.ComponentModel.CollectionChangeEventHandler = AddressOf Me.SchemaChanged
        AddHandler MyBase.Tables.CollectionChanged, schemaChangedHandler
        AddHandler Me.Relations.CollectionChanged, schemaChangedHandler
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
     Global.System.ComponentModel.Browsable(false),  _
     Global.System.ComponentModel.DesignerSerializationVisibility(Global.System.ComponentModel.DesignerSerializationVisibility.Content)>  _
    Public ReadOnly Property BurstData() As BurstDataDataTable
        Get
            Return Me.tableBurstData
        End Get
    End Property
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
     Global.System.ComponentModel.Browsable(false),  _
     Global.System.ComponentModel.DesignerSerializationVisibility(Global.System.ComponentModel.DesignerSerializationVisibility.Content)>  _
    Public ReadOnly Property ProvisionalBooking() As ProvisionalBookingDataTable
        Get
            Return Me.tableProvisionalBooking
        End Get
    End Property
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
     Global.System.ComponentModel.Browsable(false),  _
     Global.System.ComponentModel.DesignerSerializationVisibility(Global.System.ComponentModel.DesignerSerializationVisibility.Content)>  _
    Public ReadOnly Property ContractList() As ContractListDataTable
        Get
            Return Me.tableContractList
        End Get
    End Property
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
     Global.System.ComponentModel.BrowsableAttribute(true),  _
     Global.System.ComponentModel.DesignerSerializationVisibilityAttribute(Global.System.ComponentModel.DesignerSerializationVisibility.Visible)>  _
    Public Overrides Property SchemaSerializationMode() As Global.System.Data.SchemaSerializationMode
        Get
            Return Me._schemaSerializationMode
        End Get
        Set
            Me._schemaSerializationMode = value
        End Set
    End Property
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
     Global.System.ComponentModel.DesignerSerializationVisibilityAttribute(Global.System.ComponentModel.DesignerSerializationVisibility.Hidden)>  _
    Public Shadows ReadOnly Property Tables() As Global.System.Data.DataTableCollection
        Get
            Return MyBase.Tables
        End Get
    End Property
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
     Global.System.ComponentModel.DesignerSerializationVisibilityAttribute(Global.System.ComponentModel.DesignerSerializationVisibility.Hidden)>  _
    Public Shadows ReadOnly Property Relations() As Global.System.Data.DataRelationCollection
        Get
            Return MyBase.Relations
        End Get
    End Property
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Protected Overrides Sub InitializeDerivedDataSet()
        Me.BeginInit
        Me.InitClass
        Me.EndInit
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Overrides Function Clone() As Global.System.Data.DataSet
        Dim cln As DataSetMediaGap = CType(MyBase.Clone,DataSetMediaGap)
        cln.InitVars
        cln.SchemaSerializationMode = Me.SchemaSerializationMode
        Return cln
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Protected Overrides Function ShouldSerializeTables() As Boolean
        Return false
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Protected Overrides Function ShouldSerializeRelations() As Boolean
        Return false
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Protected Overrides Sub ReadXmlSerializable(ByVal reader As Global.System.Xml.XmlReader)
        If (Me.DetermineSchemaSerializationMode(reader) = Global.System.Data.SchemaSerializationMode.IncludeSchema) Then
            Me.Reset
            Dim ds As Global.System.Data.DataSet = New Global.System.Data.DataSet()
            ds.ReadXml(reader)
            If (Not (ds.Tables("BurstData")) Is Nothing) Then
                MyBase.Tables.Add(New BurstDataDataTable(ds.Tables("BurstData")))
            End If
            If (Not (ds.Tables("ProvisionalBooking")) Is Nothing) Then
                MyBase.Tables.Add(New ProvisionalBookingDataTable(ds.Tables("ProvisionalBooking")))
            End If
            If (Not (ds.Tables("ContractList")) Is Nothing) Then
                MyBase.Tables.Add(New ContractListDataTable(ds.Tables("ContractList")))
            End If
            Me.DataSetName = ds.DataSetName
            Me.Prefix = ds.Prefix
            Me.Namespace = ds.Namespace
            Me.Locale = ds.Locale
            Me.CaseSensitive = ds.CaseSensitive
            Me.EnforceConstraints = ds.EnforceConstraints
            Me.Merge(ds, false, Global.System.Data.MissingSchemaAction.Add)
            Me.InitVars
        Else
            Me.ReadXml(reader)
            Me.InitVars
        End If
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Protected Overrides Function GetSchemaSerializable() As Global.System.Xml.Schema.XmlSchema
        Dim stream As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
        Me.WriteXmlSchema(New Global.System.Xml.XmlTextWriter(stream, Nothing))
        stream.Position = 0
        Return Global.System.Xml.Schema.XmlSchema.Read(New Global.System.Xml.XmlTextReader(stream), Nothing)
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Friend Overloads Sub InitVars()
        Me.InitVars(true)
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Friend Overloads Sub InitVars(ByVal initTable As Boolean)
        Me.tableBurstData = CType(MyBase.Tables("BurstData"),BurstDataDataTable)
        If (initTable = true) Then
            If (Not (Me.tableBurstData) Is Nothing) Then
                Me.tableBurstData.InitVars
            End If
        End If
        Me.tableProvisionalBooking = CType(MyBase.Tables("ProvisionalBooking"),ProvisionalBookingDataTable)
        If (initTable = true) Then
            If (Not (Me.tableProvisionalBooking) Is Nothing) Then
                Me.tableProvisionalBooking.InitVars
            End If
        End If
        Me.tableContractList = CType(MyBase.Tables("ContractList"),ContractListDataTable)
        If (initTable = true) Then
            If (Not (Me.tableContractList) Is Nothing) Then
                Me.tableContractList.InitVars
            End If
        End If
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Private Sub InitClass()
        Me.DataSetName = "DataSetMediaGap"
        Me.Prefix = ""
        Me.Namespace = "http://tempuri.org/DataSetMediaGap.xsd"
        Me.EnforceConstraints = true
        Me.SchemaSerializationMode = Global.System.Data.SchemaSerializationMode.IncludeSchema
        Me.tableBurstData = New BurstDataDataTable()
        MyBase.Tables.Add(Me.tableBurstData)
        Me.tableProvisionalBooking = New ProvisionalBookingDataTable()
        MyBase.Tables.Add(Me.tableProvisionalBooking)
        Me.tableContractList = New ContractListDataTable()
        MyBase.Tables.Add(Me.tableContractList)
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Private Function ShouldSerializeBurstData() As Boolean
        Return false
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Private Function ShouldSerializeProvisionalBooking() As Boolean
        Return false
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Private Function ShouldSerializeContractList() As Boolean
        Return false
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Private Sub SchemaChanged(ByVal sender As Object, ByVal e As Global.System.ComponentModel.CollectionChangeEventArgs)
        If (e.Action = Global.System.ComponentModel.CollectionChangeAction.Remove) Then
            Me.InitVars
        End If
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Shared Function GetTypedDataSetSchema(ByVal xs As Global.System.Xml.Schema.XmlSchemaSet) As Global.System.Xml.Schema.XmlSchemaComplexType
        Dim ds As DataSetMediaGap = New DataSetMediaGap()
        Dim type As Global.System.Xml.Schema.XmlSchemaComplexType = New Global.System.Xml.Schema.XmlSchemaComplexType()
        Dim sequence As Global.System.Xml.Schema.XmlSchemaSequence = New Global.System.Xml.Schema.XmlSchemaSequence()
        Dim any As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
        any.Namespace = ds.Namespace
        sequence.Items.Add(any)
        type.Particle = sequence
        Dim dsSchema As Global.System.Xml.Schema.XmlSchema = ds.GetSchemaSerializable
        If xs.Contains(dsSchema.TargetNamespace) Then
            Dim s1 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
            Dim s2 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
            Try 
                Dim schema As Global.System.Xml.Schema.XmlSchema = Nothing
                dsSchema.Write(s1)
                Dim schemas As Global.System.Collections.IEnumerator = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator
                Do While schemas.MoveNext
                    schema = CType(schemas.Current,Global.System.Xml.Schema.XmlSchema)
                    s2.SetLength(0)
                    schema.Write(s2)
                    If (s1.Length = s2.Length) Then
                        s1.Position = 0
                        s2.Position = 0
                        
                        Do While ((s1.Position <> s1.Length)  _
                                    AndAlso (s1.ReadByte = s2.ReadByte))
                            
                            
                        Loop
                        If (s1.Position = s1.Length) Then
                            Return type
                        End If
                    End If
                    
                Loop
            Finally
                If (Not (s1) Is Nothing) Then
                    s1.Close
                End If
                If (Not (s2) Is Nothing) Then
                    s2.Close
                End If
            End Try
        End If
        xs.Add(dsSchema)
        Return type
    End Function
    
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Delegate Sub BurstDataRowChangeEventHandler(ByVal sender As Object, ByVal e As BurstDataRowChangeEvent)
    
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Delegate Sub ProvisionalBookingRowChangeEventHandler(ByVal sender As Object, ByVal e As ProvisionalBookingRowChangeEvent)
    
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Delegate Sub ContractListRowChangeEventHandler(ByVal sender As Object, ByVal e As ContractListRowChangeEvent)
    
    '''<summary>
    '''Represents the strongly named DataTable class.
    '''</summary>
    <Global.System.Serializable(),  _
     Global.System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")>  _
    Partial Public Class BurstDataDataTable
        Inherits Global.System.Data.TypedTableBase(Of BurstDataRow)
        
        Private columnContractID As Global.System.Data.DataColumn
        
        Private columnContractNumber As Global.System.Data.DataColumn
        
        Private columnChainName As Global.System.Data.DataColumn
        
        Private columnMediaName As Global.System.Data.DataColumn
        
        Private columnInstallStoreQty As Global.System.Data.DataColumn
        
        Private columnFirstWeek As Global.System.Data.DataColumn
        
        Private columnLastWeek As Global.System.Data.DataColumn
        
        Private columnProductName As Global.System.Data.DataColumn
        
        Private columnBrandName As Global.System.Data.DataColumn
        
        Private columnCreatedBy As Global.System.Data.DataColumn
        
        Private columnCreationDate As Global.System.Data.DataColumn
        
        Private columnHomesite As Global.System.Data.DataColumn
        
        Private columnStorePoolID As Global.System.Data.DataColumn
        
        Private columnStorePoolCapacity As Global.System.Data.DataColumn
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New()
            MyBase.New
            Me.TableName = "BurstData"
            Me.BeginInit
            Me.InitClass
            Me.EndInit
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub New(ByVal table As Global.System.Data.DataTable)
            MyBase.New
            Me.TableName = table.TableName
            If (table.CaseSensitive <> table.DataSet.CaseSensitive) Then
                Me.CaseSensitive = table.CaseSensitive
            End If
            If (table.Locale.ToString <> table.DataSet.Locale.ToString) Then
                Me.Locale = table.Locale
            End If
            If (table.Namespace <> table.DataSet.Namespace) Then
                Me.Namespace = table.Namespace
            End If
            Me.Prefix = table.Prefix
            Me.MinimumCapacity = table.MinimumCapacity
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Sub New(ByVal info As Global.System.Runtime.Serialization.SerializationInfo, ByVal context As Global.System.Runtime.Serialization.StreamingContext)
            MyBase.New(info, context)
            Me.InitVars
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ContractIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnContractID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ContractNumberColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnContractNumber
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ChainNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnChainName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property MediaNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnMediaName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property InstallStoreQtyColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnInstallStoreQty
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property FirstWeekColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnFirstWeek
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property LastWeekColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnLastWeek
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ProductNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnProductName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property BrandNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnBrandName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property CreatedByColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnCreatedBy
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property CreationDateColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnCreationDate
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property HomesiteColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnHomesite
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property StorePoolIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnStorePoolID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property StorePoolCapacityColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnStorePoolCapacity
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Browsable(false)>  _
        Public ReadOnly Property Count() As Integer
            Get
                Return Me.Rows.Count
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Default ReadOnly Property Item(ByVal index As Integer) As BurstDataRow
            Get
                Return CType(Me.Rows(index),BurstDataRow)
            End Get
        End Property
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event BurstDataRowChanging As BurstDataRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event BurstDataRowChanged As BurstDataRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event BurstDataRowDeleting As BurstDataRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event BurstDataRowDeleted As BurstDataRowChangeEventHandler
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overloads Sub AddBurstDataRow(ByVal row As BurstDataRow)
            Me.Rows.Add(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overloads Function AddBurstDataRow(ByVal ContractID As System.Guid, ByVal ContractNumber As String, ByVal ChainName As String, ByVal MediaName As String, ByVal InstallStoreQty As Integer, ByVal FirstWeek As Date, ByVal LastWeek As Date, ByVal ProductName As String, ByVal BrandName As String, ByVal CreatedBy As String, ByVal CreationDate As Date, ByVal Homesite As Boolean, ByVal StorePoolID As System.Guid, ByVal StorePoolCapacity As Integer) As BurstDataRow
            Dim rowBurstDataRow As BurstDataRow = CType(Me.NewRow,BurstDataRow)
            Dim columnValuesArray() As Object = New Object() {ContractID, ContractNumber, ChainName, MediaName, InstallStoreQty, FirstWeek, LastWeek, ProductName, BrandName, CreatedBy, CreationDate, Homesite, StorePoolID, StorePoolCapacity}
            rowBurstDataRow.ItemArray = columnValuesArray
            Me.Rows.Add(rowBurstDataRow)
            Return rowBurstDataRow
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overrides Function Clone() As Global.System.Data.DataTable
            Dim cln As BurstDataDataTable = CType(MyBase.Clone,BurstDataDataTable)
            cln.InitVars
            Return cln
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function CreateInstance() As Global.System.Data.DataTable
            Return New BurstDataDataTable()
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub InitVars()
            Me.columnContractID = MyBase.Columns("ContractID")
            Me.columnContractNumber = MyBase.Columns("ContractNumber")
            Me.columnChainName = MyBase.Columns("ChainName")
            Me.columnMediaName = MyBase.Columns("MediaName")
            Me.columnInstallStoreQty = MyBase.Columns("InstallStoreQty")
            Me.columnFirstWeek = MyBase.Columns("FirstWeek")
            Me.columnLastWeek = MyBase.Columns("LastWeek")
            Me.columnProductName = MyBase.Columns("ProductName")
            Me.columnBrandName = MyBase.Columns("BrandName")
            Me.columnCreatedBy = MyBase.Columns("CreatedBy")
            Me.columnCreationDate = MyBase.Columns("CreationDate")
            Me.columnHomesite = MyBase.Columns("Homesite")
            Me.columnStorePoolID = MyBase.Columns("StorePoolID")
            Me.columnStorePoolCapacity = MyBase.Columns("StorePoolCapacity")
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitClass()
            Me.columnContractID = New Global.System.Data.DataColumn("ContractID", GetType(Global.System.Guid), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnContractID)
            Me.columnContractNumber = New Global.System.Data.DataColumn("ContractNumber", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnContractNumber)
            Me.columnChainName = New Global.System.Data.DataColumn("ChainName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnChainName)
            Me.columnMediaName = New Global.System.Data.DataColumn("MediaName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnMediaName)
            Me.columnInstallStoreQty = New Global.System.Data.DataColumn("InstallStoreQty", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnInstallStoreQty)
            Me.columnFirstWeek = New Global.System.Data.DataColumn("FirstWeek", GetType(Date), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnFirstWeek)
            Me.columnLastWeek = New Global.System.Data.DataColumn("LastWeek", GetType(Date), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnLastWeek)
            Me.columnProductName = New Global.System.Data.DataColumn("ProductName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnProductName)
            Me.columnBrandName = New Global.System.Data.DataColumn("BrandName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnBrandName)
            Me.columnCreatedBy = New Global.System.Data.DataColumn("CreatedBy", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnCreatedBy)
            Me.columnCreationDate = New Global.System.Data.DataColumn("CreationDate", GetType(Date), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnCreationDate)
            Me.columnHomesite = New Global.System.Data.DataColumn("Homesite", GetType(Boolean), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnHomesite)
            Me.columnStorePoolID = New Global.System.Data.DataColumn("StorePoolID", GetType(Global.System.Guid), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnStorePoolID)
            Me.columnStorePoolCapacity = New Global.System.Data.DataColumn("StorePoolCapacity", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnStorePoolCapacity)
            Me.columnContractID.AllowDBNull = false
            Me.columnContractNumber.AllowDBNull = false
            Me.columnContractNumber.MaxLength = 8
            Me.columnChainName.AllowDBNull = false
            Me.columnChainName.MaxLength = 200
            Me.columnMediaName.AllowDBNull = false
            Me.columnMediaName.MaxLength = 200
            Me.columnInstallStoreQty.AllowDBNull = false
            Me.columnFirstWeek.AllowDBNull = false
            Me.columnLastWeek.ReadOnly = true
            Me.columnProductName.MaxLength = 200
            Me.columnBrandName.MaxLength = 50
            Me.columnCreatedBy.AllowDBNull = false
            Me.columnCreatedBy.MaxLength = 50
            Me.columnCreationDate.AllowDBNull = false
            Me.columnHomesite.ReadOnly = true
            Me.columnStorePoolID.AllowDBNull = false
            Me.columnStorePoolCapacity.AllowDBNull = false
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function NewBurstDataRow() As BurstDataRow
            Return CType(Me.NewRow,BurstDataRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function NewRowFromBuilder(ByVal builder As Global.System.Data.DataRowBuilder) As Global.System.Data.DataRow
            Return New BurstDataRow(builder)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function GetRowType() As Global.System.Type
            Return GetType(BurstDataRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowChanged(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanged(e)
            If (Not (Me.BurstDataRowChangedEvent) Is Nothing) Then
                RaiseEvent BurstDataRowChanged(Me, New BurstDataRowChangeEvent(CType(e.Row,BurstDataRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowChanging(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanging(e)
            If (Not (Me.BurstDataRowChangingEvent) Is Nothing) Then
                RaiseEvent BurstDataRowChanging(Me, New BurstDataRowChangeEvent(CType(e.Row,BurstDataRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowDeleted(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleted(e)
            If (Not (Me.BurstDataRowDeletedEvent) Is Nothing) Then
                RaiseEvent BurstDataRowDeleted(Me, New BurstDataRowChangeEvent(CType(e.Row,BurstDataRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowDeleting(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleting(e)
            If (Not (Me.BurstDataRowDeletingEvent) Is Nothing) Then
                RaiseEvent BurstDataRowDeleting(Me, New BurstDataRowChangeEvent(CType(e.Row,BurstDataRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub RemoveBurstDataRow(ByVal row As BurstDataRow)
            Me.Rows.Remove(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Shared Function GetTypedTableSchema(ByVal xs As Global.System.Xml.Schema.XmlSchemaSet) As Global.System.Xml.Schema.XmlSchemaComplexType
            Dim type As Global.System.Xml.Schema.XmlSchemaComplexType = New Global.System.Xml.Schema.XmlSchemaComplexType()
            Dim sequence As Global.System.Xml.Schema.XmlSchemaSequence = New Global.System.Xml.Schema.XmlSchemaSequence()
            Dim ds As DataSetMediaGap = New DataSetMediaGap()
            Dim any1 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any1.Namespace = "http://www.w3.org/2001/XMLSchema"
            any1.MinOccurs = New Decimal(0)
            any1.MaxOccurs = Decimal.MaxValue
            any1.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any1)
            Dim any2 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1"
            any2.MinOccurs = New Decimal(1)
            any2.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any2)
            Dim attribute1 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute1.Name = "namespace"
            attribute1.FixedValue = ds.Namespace
            type.Attributes.Add(attribute1)
            Dim attribute2 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute2.Name = "tableTypeName"
            attribute2.FixedValue = "BurstDataDataTable"
            type.Attributes.Add(attribute2)
            type.Particle = sequence
            Dim dsSchema As Global.System.Xml.Schema.XmlSchema = ds.GetSchemaSerializable
            If xs.Contains(dsSchema.TargetNamespace) Then
                Dim s1 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Dim s2 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Try 
                    Dim schema As Global.System.Xml.Schema.XmlSchema = Nothing
                    dsSchema.Write(s1)
                    Dim schemas As Global.System.Collections.IEnumerator = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator
                    Do While schemas.MoveNext
                        schema = CType(schemas.Current,Global.System.Xml.Schema.XmlSchema)
                        s2.SetLength(0)
                        schema.Write(s2)
                        If (s1.Length = s2.Length) Then
                            s1.Position = 0
                            s2.Position = 0
                            
                            Do While ((s1.Position <> s1.Length)  _
                                        AndAlso (s1.ReadByte = s2.ReadByte))
                                
                                
                            Loop
                            If (s1.Position = s1.Length) Then
                                Return type
                            End If
                        End If
                        
                    Loop
                Finally
                    If (Not (s1) Is Nothing) Then
                        s1.Close
                    End If
                    If (Not (s2) Is Nothing) Then
                        s2.Close
                    End If
                End Try
            End If
            xs.Add(dsSchema)
            Return type
        End Function
    End Class
    
    '''<summary>
    '''Represents the strongly named DataTable class.
    '''</summary>
    <Global.System.Serializable(),  _
     Global.System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")>  _
    Partial Public Class ProvisionalBookingDataTable
        Inherits Global.System.Data.TypedTableBase(Of ProvisionalBookingRow)
        
        Private columnProvisionalBookingID As Global.System.Data.DataColumn
        
        Private columnProvisionalBookingName As Global.System.Data.DataColumn
        
        Private columnChainID As Global.System.Data.DataColumn
        
        Private columnCategoryID As Global.System.Data.DataColumn
        
        Private columnMediaFamilyID As Global.System.Data.DataColumn
        
        Private columnBrandID As Global.System.Data.DataColumn
        
        Private columnFirstWeek As Global.System.Data.DataColumn
        
        Private columnMediaFamilyName As Global.System.Data.DataColumn
        
        Private columnCategoryName As Global.System.Data.DataColumn
        
        Private columnChainName As Global.System.Data.DataColumn
        
        Private columnBrandName As Global.System.Data.DataColumn
        
        Private columnLastWeek As Global.System.Data.DataColumn
        
        Private columnBookedBy As Global.System.Data.DataColumn
        
        Private columnBookTime As Global.System.Data.DataColumn
        
        Private columnExpiryTime As Global.System.Data.DataColumn
        
        Private columnDuration As Global.System.Data.DataColumn
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New()
            MyBase.New
            Me.TableName = "ProvisionalBooking"
            Me.BeginInit
            Me.InitClass
            Me.EndInit
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub New(ByVal table As Global.System.Data.DataTable)
            MyBase.New
            Me.TableName = table.TableName
            If (table.CaseSensitive <> table.DataSet.CaseSensitive) Then
                Me.CaseSensitive = table.CaseSensitive
            End If
            If (table.Locale.ToString <> table.DataSet.Locale.ToString) Then
                Me.Locale = table.Locale
            End If
            If (table.Namespace <> table.DataSet.Namespace) Then
                Me.Namespace = table.Namespace
            End If
            Me.Prefix = table.Prefix
            Me.MinimumCapacity = table.MinimumCapacity
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Sub New(ByVal info As Global.System.Runtime.Serialization.SerializationInfo, ByVal context As Global.System.Runtime.Serialization.StreamingContext)
            MyBase.New(info, context)
            Me.InitVars
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ProvisionalBookingIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnProvisionalBookingID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ProvisionalBookingNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnProvisionalBookingName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ChainIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnChainID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property CategoryIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnCategoryID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property MediaFamilyIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnMediaFamilyID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property BrandIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnBrandID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property FirstWeekColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnFirstWeek
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property MediaFamilyNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnMediaFamilyName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property CategoryNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnCategoryName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ChainNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnChainName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property BrandNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnBrandName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property LastWeekColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnLastWeek
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property BookedByColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnBookedBy
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property BookTimeColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnBookTime
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ExpiryTimeColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnExpiryTime
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property DurationColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnDuration
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Browsable(false)>  _
        Public ReadOnly Property Count() As Integer
            Get
                Return Me.Rows.Count
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Default ReadOnly Property Item(ByVal index As Integer) As ProvisionalBookingRow
            Get
                Return CType(Me.Rows(index),ProvisionalBookingRow)
            End Get
        End Property
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ProvisionalBookingRowChanging As ProvisionalBookingRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ProvisionalBookingRowChanged As ProvisionalBookingRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ProvisionalBookingRowDeleting As ProvisionalBookingRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ProvisionalBookingRowDeleted As ProvisionalBookingRowChangeEventHandler
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overloads Sub AddProvisionalBookingRow(ByVal row As ProvisionalBookingRow)
            Me.Rows.Add(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overloads Function AddProvisionalBookingRow( _
                    ByVal ProvisionalBookingID As System.Guid,  _
                    ByVal ProvisionalBookingName As String,  _
                    ByVal ChainID As Integer,  _
                    ByVal CategoryID As Integer,  _
                    ByVal MediaFamilyID As Integer,  _
                    ByVal BrandID As System.Guid,  _
                    ByVal FirstWeek As Date,  _
                    ByVal MediaFamilyName As String,  _
                    ByVal CategoryName As String,  _
                    ByVal ChainName As String,  _
                    ByVal BrandName As String,  _
                    ByVal LastWeek As Date,  _
                    ByVal BookedBy As String,  _
                    ByVal BookTime As Date,  _
                    ByVal ExpiryTime As Date,  _
                    ByVal Duration As Integer) As ProvisionalBookingRow
            Dim rowProvisionalBookingRow As ProvisionalBookingRow = CType(Me.NewRow,ProvisionalBookingRow)
            Dim columnValuesArray() As Object = New Object() {ProvisionalBookingID, ProvisionalBookingName, ChainID, CategoryID, MediaFamilyID, BrandID, FirstWeek, MediaFamilyName, CategoryName, ChainName, BrandName, LastWeek, BookedBy, BookTime, ExpiryTime, Duration}
            rowProvisionalBookingRow.ItemArray = columnValuesArray
            Me.Rows.Add(rowProvisionalBookingRow)
            Return rowProvisionalBookingRow
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function FindByProvisionalBookingID(ByVal ProvisionalBookingID As System.Guid) As ProvisionalBookingRow
            Return CType(Me.Rows.Find(New Object() {ProvisionalBookingID}),ProvisionalBookingRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overrides Function Clone() As Global.System.Data.DataTable
            Dim cln As ProvisionalBookingDataTable = CType(MyBase.Clone,ProvisionalBookingDataTable)
            cln.InitVars
            Return cln
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function CreateInstance() As Global.System.Data.DataTable
            Return New ProvisionalBookingDataTable()
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub InitVars()
            Me.columnProvisionalBookingID = MyBase.Columns("ProvisionalBookingID")
            Me.columnProvisionalBookingName = MyBase.Columns("ProvisionalBookingName")
            Me.columnChainID = MyBase.Columns("ChainID")
            Me.columnCategoryID = MyBase.Columns("CategoryID")
            Me.columnMediaFamilyID = MyBase.Columns("MediaFamilyID")
            Me.columnBrandID = MyBase.Columns("BrandID")
            Me.columnFirstWeek = MyBase.Columns("FirstWeek")
            Me.columnMediaFamilyName = MyBase.Columns("MediaFamilyName")
            Me.columnCategoryName = MyBase.Columns("CategoryName")
            Me.columnChainName = MyBase.Columns("ChainName")
            Me.columnBrandName = MyBase.Columns("BrandName")
            Me.columnLastWeek = MyBase.Columns("LastWeek")
            Me.columnBookedBy = MyBase.Columns("BookedBy")
            Me.columnBookTime = MyBase.Columns("BookTime")
            Me.columnExpiryTime = MyBase.Columns("ExpiryTime")
            Me.columnDuration = MyBase.Columns("Duration")
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitClass()
            Me.columnProvisionalBookingID = New Global.System.Data.DataColumn("ProvisionalBookingID", GetType(Global.System.Guid), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnProvisionalBookingID)
            Me.columnProvisionalBookingName = New Global.System.Data.DataColumn("ProvisionalBookingName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnProvisionalBookingName)
            Me.columnChainID = New Global.System.Data.DataColumn("ChainID", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnChainID)
            Me.columnCategoryID = New Global.System.Data.DataColumn("CategoryID", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnCategoryID)
            Me.columnMediaFamilyID = New Global.System.Data.DataColumn("MediaFamilyID", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnMediaFamilyID)
            Me.columnBrandID = New Global.System.Data.DataColumn("BrandID", GetType(Global.System.Guid), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnBrandID)
            Me.columnFirstWeek = New Global.System.Data.DataColumn("FirstWeek", GetType(Date), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnFirstWeek)
            Me.columnMediaFamilyName = New Global.System.Data.DataColumn("MediaFamilyName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnMediaFamilyName)
            Me.columnCategoryName = New Global.System.Data.DataColumn("CategoryName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnCategoryName)
            Me.columnChainName = New Global.System.Data.DataColumn("ChainName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnChainName)
            Me.columnBrandName = New Global.System.Data.DataColumn("BrandName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnBrandName)
            Me.columnLastWeek = New Global.System.Data.DataColumn("LastWeek", GetType(Date), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnLastWeek)
            Me.columnBookedBy = New Global.System.Data.DataColumn("BookedBy", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnBookedBy)
            Me.columnBookTime = New Global.System.Data.DataColumn("BookTime", GetType(Date), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnBookTime)
            Me.columnExpiryTime = New Global.System.Data.DataColumn("ExpiryTime", GetType(Date), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnExpiryTime)
            Me.columnDuration = New Global.System.Data.DataColumn("Duration", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnDuration)
            Me.Constraints.Add(New Global.System.Data.UniqueConstraint("Constraint1", New Global.System.Data.DataColumn() {Me.columnProvisionalBookingID}, true))
            Me.columnProvisionalBookingID.AllowDBNull = false
            Me.columnProvisionalBookingID.Unique = true
            Me.columnProvisionalBookingName.DefaultValue = CType("",String)
            Me.columnProvisionalBookingName.MaxLength = 250
            Me.columnChainID.AllowDBNull = false
            Me.columnCategoryID.AllowDBNull = false
            Me.columnMediaFamilyID.AllowDBNull = false
            Me.columnBrandID.AllowDBNull = false
            Me.columnFirstWeek.AllowDBNull = false
            Me.columnMediaFamilyName.AllowDBNull = false
            Me.columnMediaFamilyName.DefaultValue = CType("",String)
            Me.columnMediaFamilyName.MaxLength = 250
            Me.columnCategoryName.AllowDBNull = false
            Me.columnCategoryName.DefaultValue = CType("",String)
            Me.columnCategoryName.MaxLength = 200
            Me.columnChainName.AllowDBNull = false
            Me.columnChainName.DefaultValue = CType("",String)
            Me.columnChainName.MaxLength = 150
            Me.columnBrandName.ReadOnly = true
            Me.columnBrandName.DefaultValue = CType("",String)
            Me.columnBrandName.MaxLength = 50
            Me.columnLastWeek.ReadOnly = true
            Me.columnBookedBy.AllowDBNull = false
            Me.columnBookedBy.DefaultValue = CType("",String)
            Me.columnBookedBy.MaxLength = 50
            Me.columnBookTime.AllowDBNull = false
            Me.columnExpiryTime.AllowDBNull = false
            Me.columnDuration.AllowDBNull = false
            Me.columnDuration.DefaultValue = CType(0,Integer)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function NewProvisionalBookingRow() As ProvisionalBookingRow
            Return CType(Me.NewRow,ProvisionalBookingRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function NewRowFromBuilder(ByVal builder As Global.System.Data.DataRowBuilder) As Global.System.Data.DataRow
            Return New ProvisionalBookingRow(builder)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function GetRowType() As Global.System.Type
            Return GetType(ProvisionalBookingRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowChanged(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanged(e)
            If (Not (Me.ProvisionalBookingRowChangedEvent) Is Nothing) Then
                RaiseEvent ProvisionalBookingRowChanged(Me, New ProvisionalBookingRowChangeEvent(CType(e.Row,ProvisionalBookingRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowChanging(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanging(e)
            If (Not (Me.ProvisionalBookingRowChangingEvent) Is Nothing) Then
                RaiseEvent ProvisionalBookingRowChanging(Me, New ProvisionalBookingRowChangeEvent(CType(e.Row,ProvisionalBookingRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowDeleted(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleted(e)
            If (Not (Me.ProvisionalBookingRowDeletedEvent) Is Nothing) Then
                RaiseEvent ProvisionalBookingRowDeleted(Me, New ProvisionalBookingRowChangeEvent(CType(e.Row,ProvisionalBookingRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowDeleting(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleting(e)
            If (Not (Me.ProvisionalBookingRowDeletingEvent) Is Nothing) Then
                RaiseEvent ProvisionalBookingRowDeleting(Me, New ProvisionalBookingRowChangeEvent(CType(e.Row,ProvisionalBookingRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub RemoveProvisionalBookingRow(ByVal row As ProvisionalBookingRow)
            Me.Rows.Remove(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Shared Function GetTypedTableSchema(ByVal xs As Global.System.Xml.Schema.XmlSchemaSet) As Global.System.Xml.Schema.XmlSchemaComplexType
            Dim type As Global.System.Xml.Schema.XmlSchemaComplexType = New Global.System.Xml.Schema.XmlSchemaComplexType()
            Dim sequence As Global.System.Xml.Schema.XmlSchemaSequence = New Global.System.Xml.Schema.XmlSchemaSequence()
            Dim ds As DataSetMediaGap = New DataSetMediaGap()
            Dim any1 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any1.Namespace = "http://www.w3.org/2001/XMLSchema"
            any1.MinOccurs = New Decimal(0)
            any1.MaxOccurs = Decimal.MaxValue
            any1.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any1)
            Dim any2 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1"
            any2.MinOccurs = New Decimal(1)
            any2.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any2)
            Dim attribute1 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute1.Name = "namespace"
            attribute1.FixedValue = ds.Namespace
            type.Attributes.Add(attribute1)
            Dim attribute2 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute2.Name = "tableTypeName"
            attribute2.FixedValue = "ProvisionalBookingDataTable"
            type.Attributes.Add(attribute2)
            type.Particle = sequence
            Dim dsSchema As Global.System.Xml.Schema.XmlSchema = ds.GetSchemaSerializable
            If xs.Contains(dsSchema.TargetNamespace) Then
                Dim s1 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Dim s2 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Try 
                    Dim schema As Global.System.Xml.Schema.XmlSchema = Nothing
                    dsSchema.Write(s1)
                    Dim schemas As Global.System.Collections.IEnumerator = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator
                    Do While schemas.MoveNext
                        schema = CType(schemas.Current,Global.System.Xml.Schema.XmlSchema)
                        s2.SetLength(0)
                        schema.Write(s2)
                        If (s1.Length = s2.Length) Then
                            s1.Position = 0
                            s2.Position = 0
                            
                            Do While ((s1.Position <> s1.Length)  _
                                        AndAlso (s1.ReadByte = s2.ReadByte))
                                
                                
                            Loop
                            If (s1.Position = s1.Length) Then
                                Return type
                            End If
                        End If
                        
                    Loop
                Finally
                    If (Not (s1) Is Nothing) Then
                        s1.Close
                    End If
                    If (Not (s2) Is Nothing) Then
                        s2.Close
                    End If
                End Try
            End If
            xs.Add(dsSchema)
            Return type
        End Function
    End Class
    
    '''<summary>
    '''Represents the strongly named DataTable class.
    '''</summary>
    <Global.System.Serializable(),  _
     Global.System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")>  _
    Partial Public Class ContractListDataTable
        Inherits Global.System.Data.TypedTableBase(Of ContractListRow)
        
        Private columnContractID As Global.System.Data.DataColumn
        
        Private columnContractNumber As Global.System.Data.DataColumn
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New()
            MyBase.New
            Me.TableName = "ContractList"
            Me.BeginInit
            Me.InitClass
            Me.EndInit
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub New(ByVal table As Global.System.Data.DataTable)
            MyBase.New
            Me.TableName = table.TableName
            If (table.CaseSensitive <> table.DataSet.CaseSensitive) Then
                Me.CaseSensitive = table.CaseSensitive
            End If
            If (table.Locale.ToString <> table.DataSet.Locale.ToString) Then
                Me.Locale = table.Locale
            End If
            If (table.Namespace <> table.DataSet.Namespace) Then
                Me.Namespace = table.Namespace
            End If
            Me.Prefix = table.Prefix
            Me.MinimumCapacity = table.MinimumCapacity
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Sub New(ByVal info As Global.System.Runtime.Serialization.SerializationInfo, ByVal context As Global.System.Runtime.Serialization.StreamingContext)
            MyBase.New(info, context)
            Me.InitVars
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ContractIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnContractID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ContractNumberColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnContractNumber
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Browsable(false)>  _
        Public ReadOnly Property Count() As Integer
            Get
                Return Me.Rows.Count
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Default ReadOnly Property Item(ByVal index As Integer) As ContractListRow
            Get
                Return CType(Me.Rows(index),ContractListRow)
            End Get
        End Property
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ContractListRowChanging As ContractListRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ContractListRowChanged As ContractListRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ContractListRowDeleting As ContractListRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ContractListRowDeleted As ContractListRowChangeEventHandler
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overloads Sub AddContractListRow(ByVal row As ContractListRow)
            Me.Rows.Add(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overloads Function AddContractListRow(ByVal ContractID As System.Guid, ByVal ContractNumber As String) As ContractListRow
            Dim rowContractListRow As ContractListRow = CType(Me.NewRow,ContractListRow)
            Dim columnValuesArray() As Object = New Object() {ContractID, ContractNumber}
            rowContractListRow.ItemArray = columnValuesArray
            Me.Rows.Add(rowContractListRow)
            Return rowContractListRow
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function FindByContractID(ByVal ContractID As System.Guid) As ContractListRow
            Return CType(Me.Rows.Find(New Object() {ContractID}),ContractListRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overrides Function Clone() As Global.System.Data.DataTable
            Dim cln As ContractListDataTable = CType(MyBase.Clone,ContractListDataTable)
            cln.InitVars
            Return cln
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function CreateInstance() As Global.System.Data.DataTable
            Return New ContractListDataTable()
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub InitVars()
            Me.columnContractID = MyBase.Columns("ContractID")
            Me.columnContractNumber = MyBase.Columns("ContractNumber")
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitClass()
            Me.columnContractID = New Global.System.Data.DataColumn("ContractID", GetType(Global.System.Guid), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnContractID)
            Me.columnContractNumber = New Global.System.Data.DataColumn("ContractNumber", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnContractNumber)
            Me.Constraints.Add(New Global.System.Data.UniqueConstraint("Constraint1", New Global.System.Data.DataColumn() {Me.columnContractID}, true))
            Me.columnContractID.AllowDBNull = false
            Me.columnContractID.Unique = true
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function NewContractListRow() As ContractListRow
            Return CType(Me.NewRow,ContractListRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function NewRowFromBuilder(ByVal builder As Global.System.Data.DataRowBuilder) As Global.System.Data.DataRow
            Return New ContractListRow(builder)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function GetRowType() As Global.System.Type
            Return GetType(ContractListRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowChanged(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanged(e)
            If (Not (Me.ContractListRowChangedEvent) Is Nothing) Then
                RaiseEvent ContractListRowChanged(Me, New ContractListRowChangeEvent(CType(e.Row,ContractListRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowChanging(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanging(e)
            If (Not (Me.ContractListRowChangingEvent) Is Nothing) Then
                RaiseEvent ContractListRowChanging(Me, New ContractListRowChangeEvent(CType(e.Row,ContractListRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowDeleted(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleted(e)
            If (Not (Me.ContractListRowDeletedEvent) Is Nothing) Then
                RaiseEvent ContractListRowDeleted(Me, New ContractListRowChangeEvent(CType(e.Row,ContractListRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowDeleting(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleting(e)
            If (Not (Me.ContractListRowDeletingEvent) Is Nothing) Then
                RaiseEvent ContractListRowDeleting(Me, New ContractListRowChangeEvent(CType(e.Row,ContractListRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub RemoveContractListRow(ByVal row As ContractListRow)
            Me.Rows.Remove(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Shared Function GetTypedTableSchema(ByVal xs As Global.System.Xml.Schema.XmlSchemaSet) As Global.System.Xml.Schema.XmlSchemaComplexType
            Dim type As Global.System.Xml.Schema.XmlSchemaComplexType = New Global.System.Xml.Schema.XmlSchemaComplexType()
            Dim sequence As Global.System.Xml.Schema.XmlSchemaSequence = New Global.System.Xml.Schema.XmlSchemaSequence()
            Dim ds As DataSetMediaGap = New DataSetMediaGap()
            Dim any1 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any1.Namespace = "http://www.w3.org/2001/XMLSchema"
            any1.MinOccurs = New Decimal(0)
            any1.MaxOccurs = Decimal.MaxValue
            any1.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any1)
            Dim any2 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1"
            any2.MinOccurs = New Decimal(1)
            any2.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any2)
            Dim attribute1 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute1.Name = "namespace"
            attribute1.FixedValue = ds.Namespace
            type.Attributes.Add(attribute1)
            Dim attribute2 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute2.Name = "tableTypeName"
            attribute2.FixedValue = "ContractListDataTable"
            type.Attributes.Add(attribute2)
            type.Particle = sequence
            Dim dsSchema As Global.System.Xml.Schema.XmlSchema = ds.GetSchemaSerializable
            If xs.Contains(dsSchema.TargetNamespace) Then
                Dim s1 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Dim s2 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Try 
                    Dim schema As Global.System.Xml.Schema.XmlSchema = Nothing
                    dsSchema.Write(s1)
                    Dim schemas As Global.System.Collections.IEnumerator = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator
                    Do While schemas.MoveNext
                        schema = CType(schemas.Current,Global.System.Xml.Schema.XmlSchema)
                        s2.SetLength(0)
                        schema.Write(s2)
                        If (s1.Length = s2.Length) Then
                            s1.Position = 0
                            s2.Position = 0
                            
                            Do While ((s1.Position <> s1.Length)  _
                                        AndAlso (s1.ReadByte = s2.ReadByte))
                                
                                
                            Loop
                            If (s1.Position = s1.Length) Then
                                Return type
                            End If
                        End If
                        
                    Loop
                Finally
                    If (Not (s1) Is Nothing) Then
                        s1.Close
                    End If
                    If (Not (s2) Is Nothing) Then
                        s2.Close
                    End If
                End Try
            End If
            xs.Add(dsSchema)
            Return type
        End Function
    End Class
    
    '''<summary>
    '''Represents strongly named DataRow class.
    '''</summary>
    Partial Public Class BurstDataRow
        Inherits Global.System.Data.DataRow
        
        Private tableBurstData As BurstDataDataTable
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub New(ByVal rb As Global.System.Data.DataRowBuilder)
            MyBase.New(rb)
            Me.tableBurstData = CType(Me.Table,BurstDataDataTable)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ContractID() As System.Guid
            Get
                Return CType(Me(Me.tableBurstData.ContractIDColumn),Global.System.Guid)
            End Get
            Set
                Me(Me.tableBurstData.ContractIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ContractNumber() As String
            Get
                Return CType(Me(Me.tableBurstData.ContractNumberColumn),String)
            End Get
            Set
                Me(Me.tableBurstData.ContractNumberColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ChainName() As String
            Get
                Return CType(Me(Me.tableBurstData.ChainNameColumn),String)
            End Get
            Set
                Me(Me.tableBurstData.ChainNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property MediaName() As String
            Get
                Return CType(Me(Me.tableBurstData.MediaNameColumn),String)
            End Get
            Set
                Me(Me.tableBurstData.MediaNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property InstallStoreQty() As Integer
            Get
                Return CType(Me(Me.tableBurstData.InstallStoreQtyColumn),Integer)
            End Get
            Set
                Me(Me.tableBurstData.InstallStoreQtyColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property FirstWeek() As Date
            Get
                Return CType(Me(Me.tableBurstData.FirstWeekColumn),Date)
            End Get
            Set
                Me(Me.tableBurstData.FirstWeekColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property LastWeek() As Date
            Get
                Try 
                    Return CType(Me(Me.tableBurstData.LastWeekColumn),Date)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'LastWeek' in table 'BurstData' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableBurstData.LastWeekColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ProductName() As String
            Get
                Try 
                    Return CType(Me(Me.tableBurstData.ProductNameColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'ProductName' in table 'BurstData' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableBurstData.ProductNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property BrandName() As String
            Get
                Try 
                    Return CType(Me(Me.tableBurstData.BrandNameColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'BrandName' in table 'BurstData' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableBurstData.BrandNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property CreatedBy() As String
            Get
                Return CType(Me(Me.tableBurstData.CreatedByColumn),String)
            End Get
            Set
                Me(Me.tableBurstData.CreatedByColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property CreationDate() As Date
            Get
                Return CType(Me(Me.tableBurstData.CreationDateColumn),Date)
            End Get
            Set
                Me(Me.tableBurstData.CreationDateColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property Homesite() As Boolean
            Get
                Try 
                    Return CType(Me(Me.tableBurstData.HomesiteColumn),Boolean)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'Homesite' in table 'BurstData' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableBurstData.HomesiteColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property StorePoolID() As System.Guid
            Get
                Return CType(Me(Me.tableBurstData.StorePoolIDColumn),Global.System.Guid)
            End Get
            Set
                Me(Me.tableBurstData.StorePoolIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property StorePoolCapacity() As Integer
            Get
                Return CType(Me(Me.tableBurstData.StorePoolCapacityColumn),Integer)
            End Get
            Set
                Me(Me.tableBurstData.StorePoolCapacityColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsLastWeekNull() As Boolean
            Return Me.IsNull(Me.tableBurstData.LastWeekColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetLastWeekNull()
            Me(Me.tableBurstData.LastWeekColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsProductNameNull() As Boolean
            Return Me.IsNull(Me.tableBurstData.ProductNameColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetProductNameNull()
            Me(Me.tableBurstData.ProductNameColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsBrandNameNull() As Boolean
            Return Me.IsNull(Me.tableBurstData.BrandNameColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetBrandNameNull()
            Me(Me.tableBurstData.BrandNameColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsHomesiteNull() As Boolean
            Return Me.IsNull(Me.tableBurstData.HomesiteColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetHomesiteNull()
            Me(Me.tableBurstData.HomesiteColumn) = Global.System.Convert.DBNull
        End Sub
    End Class
    
    '''<summary>
    '''Represents strongly named DataRow class.
    '''</summary>
    Partial Public Class ProvisionalBookingRow
        Inherits Global.System.Data.DataRow
        
        Private tableProvisionalBooking As ProvisionalBookingDataTable
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub New(ByVal rb As Global.System.Data.DataRowBuilder)
            MyBase.New(rb)
            Me.tableProvisionalBooking = CType(Me.Table,ProvisionalBookingDataTable)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ProvisionalBookingID() As System.Guid
            Get
                Return CType(Me(Me.tableProvisionalBooking.ProvisionalBookingIDColumn),Global.System.Guid)
            End Get
            Set
                Me(Me.tableProvisionalBooking.ProvisionalBookingIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ProvisionalBookingName() As String
            Get
                Try 
                    Return CType(Me(Me.tableProvisionalBooking.ProvisionalBookingNameColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'ProvisionalBookingName' in table 'ProvisionalBooking' is DB"& _ 
                            "Null.", e)
                End Try
            End Get
            Set
                Me(Me.tableProvisionalBooking.ProvisionalBookingNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ChainID() As Integer
            Get
                Return CType(Me(Me.tableProvisionalBooking.ChainIDColumn),Integer)
            End Get
            Set
                Me(Me.tableProvisionalBooking.ChainIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property CategoryID() As Integer
            Get
                Return CType(Me(Me.tableProvisionalBooking.CategoryIDColumn),Integer)
            End Get
            Set
                Me(Me.tableProvisionalBooking.CategoryIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property MediaFamilyID() As Integer
            Get
                Return CType(Me(Me.tableProvisionalBooking.MediaFamilyIDColumn),Integer)
            End Get
            Set
                Me(Me.tableProvisionalBooking.MediaFamilyIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property BrandID() As System.Guid
            Get
                Return CType(Me(Me.tableProvisionalBooking.BrandIDColumn),Global.System.Guid)
            End Get
            Set
                Me(Me.tableProvisionalBooking.BrandIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property FirstWeek() As Date
            Get
                Return CType(Me(Me.tableProvisionalBooking.FirstWeekColumn),Date)
            End Get
            Set
                Me(Me.tableProvisionalBooking.FirstWeekColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property MediaFamilyName() As String
            Get
                Return CType(Me(Me.tableProvisionalBooking.MediaFamilyNameColumn),String)
            End Get
            Set
                Me(Me.tableProvisionalBooking.MediaFamilyNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property CategoryName() As String
            Get
                Return CType(Me(Me.tableProvisionalBooking.CategoryNameColumn),String)
            End Get
            Set
                Me(Me.tableProvisionalBooking.CategoryNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ChainName() As String
            Get
                Return CType(Me(Me.tableProvisionalBooking.ChainNameColumn),String)
            End Get
            Set
                Me(Me.tableProvisionalBooking.ChainNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property BrandName() As String
            Get
                Try 
                    Return CType(Me(Me.tableProvisionalBooking.BrandNameColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'BrandName' in table 'ProvisionalBooking' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableProvisionalBooking.BrandNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property LastWeek() As Date
            Get
                Try 
                    Return CType(Me(Me.tableProvisionalBooking.LastWeekColumn),Date)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'LastWeek' in table 'ProvisionalBooking' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableProvisionalBooking.LastWeekColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property BookedBy() As String
            Get
                Return CType(Me(Me.tableProvisionalBooking.BookedByColumn),String)
            End Get
            Set
                Me(Me.tableProvisionalBooking.BookedByColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property BookTime() As Date
            Get
                Return CType(Me(Me.tableProvisionalBooking.BookTimeColumn),Date)
            End Get
            Set
                Me(Me.tableProvisionalBooking.BookTimeColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ExpiryTime() As Date
            Get
                Return CType(Me(Me.tableProvisionalBooking.ExpiryTimeColumn),Date)
            End Get
            Set
                Me(Me.tableProvisionalBooking.ExpiryTimeColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property Duration() As Integer
            Get
                Return CType(Me(Me.tableProvisionalBooking.DurationColumn),Integer)
            End Get
            Set
                Me(Me.tableProvisionalBooking.DurationColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsProvisionalBookingNameNull() As Boolean
            Return Me.IsNull(Me.tableProvisionalBooking.ProvisionalBookingNameColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetProvisionalBookingNameNull()
            Me(Me.tableProvisionalBooking.ProvisionalBookingNameColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsBrandNameNull() As Boolean
            Return Me.IsNull(Me.tableProvisionalBooking.BrandNameColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetBrandNameNull()
            Me(Me.tableProvisionalBooking.BrandNameColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsLastWeekNull() As Boolean
            Return Me.IsNull(Me.tableProvisionalBooking.LastWeekColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetLastWeekNull()
            Me(Me.tableProvisionalBooking.LastWeekColumn) = Global.System.Convert.DBNull
        End Sub
    End Class
    
    '''<summary>
    '''Represents strongly named DataRow class.
    '''</summary>
    Partial Public Class ContractListRow
        Inherits Global.System.Data.DataRow
        
        Private tableContractList As ContractListDataTable
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub New(ByVal rb As Global.System.Data.DataRowBuilder)
            MyBase.New(rb)
            Me.tableContractList = CType(Me.Table,ContractListDataTable)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ContractID() As System.Guid
            Get
                Return CType(Me(Me.tableContractList.ContractIDColumn),Global.System.Guid)
            End Get
            Set
                Me(Me.tableContractList.ContractIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ContractNumber() As String
            Get
                Try 
                    Return CType(Me(Me.tableContractList.ContractNumberColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'ContractNumber' in table 'ContractList' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableContractList.ContractNumberColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsContractNumberNull() As Boolean
            Return Me.IsNull(Me.tableContractList.ContractNumberColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetContractNumberNull()
            Me(Me.tableContractList.ContractNumberColumn) = Global.System.Convert.DBNull
        End Sub
    End Class
    
    '''<summary>
    '''Row event argument class
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Class BurstDataRowChangeEvent
        Inherits Global.System.EventArgs
        
        Private eventRow As BurstDataRow
        
        Private eventAction As Global.System.Data.DataRowAction
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New(ByVal row As BurstDataRow, ByVal action As Global.System.Data.DataRowAction)
            MyBase.New
            Me.eventRow = row
            Me.eventAction = action
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property Row() As BurstDataRow
            Get
                Return Me.eventRow
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property Action() As Global.System.Data.DataRowAction
            Get
                Return Me.eventAction
            End Get
        End Property
    End Class
    
    '''<summary>
    '''Row event argument class
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Class ProvisionalBookingRowChangeEvent
        Inherits Global.System.EventArgs
        
        Private eventRow As ProvisionalBookingRow
        
        Private eventAction As Global.System.Data.DataRowAction
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New(ByVal row As ProvisionalBookingRow, ByVal action As Global.System.Data.DataRowAction)
            MyBase.New
            Me.eventRow = row
            Me.eventAction = action
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property Row() As ProvisionalBookingRow
            Get
                Return Me.eventRow
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property Action() As Global.System.Data.DataRowAction
            Get
                Return Me.eventAction
            End Get
        End Property
    End Class
    
    '''<summary>
    '''Row event argument class
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Class ContractListRowChangeEvent
        Inherits Global.System.EventArgs
        
        Private eventRow As ContractListRow
        
        Private eventAction As Global.System.Data.DataRowAction
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New(ByVal row As ContractListRow, ByVal action As Global.System.Data.DataRowAction)
            MyBase.New
            Me.eventRow = row
            Me.eventAction = action
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property Row() As ContractListRow
            Get
                Return Me.eventRow
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property Action() As Global.System.Data.DataRowAction
            Get
                Return Me.eventAction
            End Get
        End Property
    End Class
End Class

Namespace DataSetMediaGapTableAdapters
    
    '''<summary>
    '''Represents the connection and commands used to retrieve and save data.
    '''</summary>
    <Global.System.ComponentModel.DesignerCategoryAttribute("code"),  _
     Global.System.ComponentModel.ToolboxItem(true),  _
     Global.System.ComponentModel.DataObjectAttribute(true),  _
     Global.System.ComponentModel.DesignerAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterDesigner, Microsoft.VSDesigner"& _ 
        ", Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"),  _
     Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
    Partial Public Class BurstDataTableAdapter
        Inherits Global.System.ComponentModel.Component
        
        Private WithEvents _adapter As Global.System.Data.SqlClient.SqlDataAdapter
        
        Private _connection As Global.System.Data.SqlClient.SqlConnection
        
        Private _commandCollection() As Global.System.Data.SqlClient.SqlCommand
        
        Private _clearBeforeFill As Boolean
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New()
            MyBase.New
            Me.ClearBeforeFill = true
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private ReadOnly Property Adapter() As Global.System.Data.SqlClient.SqlDataAdapter
            Get
                If (Me._adapter Is Nothing) Then
                    Me.InitAdapter
                End If
                Return Me._adapter
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property Connection() As Global.System.Data.SqlClient.SqlConnection
            Get
                If (Me._connection Is Nothing) Then
                    Me.InitConnection
                End If
                Return Me._connection
            End Get
            Set
                Me._connection = value
                If (Not (Me.Adapter.InsertCommand) Is Nothing) Then
                    Me.Adapter.InsertCommand.Connection = value
                End If
                If (Not (Me.Adapter.DeleteCommand) Is Nothing) Then
                    Me.Adapter.DeleteCommand.Connection = value
                End If
                If (Not (Me.Adapter.UpdateCommand) Is Nothing) Then
                    Me.Adapter.UpdateCommand.Connection = value
                End If
                Dim i As Integer = 0
                Do While (i < Me.CommandCollection.Length)
                    If (Not (Me.CommandCollection(i)) Is Nothing) Then
                        CType(Me.CommandCollection(i),Global.System.Data.SqlClient.SqlCommand).Connection = value
                    End If
                    i = (i + 1)
                Loop
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected ReadOnly Property CommandCollection() As Global.System.Data.SqlClient.SqlCommand()
            Get
                If (Me._commandCollection Is Nothing) Then
                    Me.InitCommandCollection
                End If
                Return Me._commandCollection
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ClearBeforeFill() As Boolean
            Get
                Return Me._clearBeforeFill
            End Get
            Set
                Me._clearBeforeFill = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitAdapter()
            Me._adapter = New Global.System.Data.SqlClient.SqlDataAdapter()
            Dim tableMapping As Global.System.Data.Common.DataTableMapping = New Global.System.Data.Common.DataTableMapping()
            tableMapping.SourceTable = "Table"
            tableMapping.DataSetTable = "BurstData"
            tableMapping.ColumnMappings.Add("ContractID", "ContractID")
            tableMapping.ColumnMappings.Add("ContractNumber", "ContractNumber")
            tableMapping.ColumnMappings.Add("ChainName", "ChainName")
            tableMapping.ColumnMappings.Add("MediaName", "MediaName")
            tableMapping.ColumnMappings.Add("InstallStoreQty", "InstallStoreQty")
            tableMapping.ColumnMappings.Add("FirstWeek", "FirstWeek")
            tableMapping.ColumnMappings.Add("LastInstallWeek", "LastWeek")
            tableMapping.ColumnMappings.Add("ProductName", "ProductName")
            tableMapping.ColumnMappings.Add("BrandName", "BrandName")
            tableMapping.ColumnMappings.Add("CreatedBy", "CreatedBy")
            tableMapping.ColumnMappings.Add("CreationDate", "CreationDate")
            tableMapping.ColumnMappings.Add("Homesite", "Homesite")
            tableMapping.ColumnMappings.Add("StorePoolID", "StorePoolID")
            tableMapping.ColumnMappings.Add("StorePoolCapacity", "StorePoolCapacity")
            Me._adapter.TableMappings.Add(tableMapping)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitConnection()
            Me._connection = New Global.System.Data.SqlClient.SqlConnection()
            Me._connection.ConnectionString = Global.NovaData.My.MySettings.Default.DBConnection
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitCommandCollection()
            Me._commandCollection = New Global.System.Data.SqlClient.SqlCommand(0) {}
            Me._commandCollection(0) = New Global.System.Data.SqlClient.SqlCommand()
            Me._commandCollection(0).Connection = Me.Connection
            Me._commandCollection(0).CommandText = "SELECT        ContractID, ContractNumber, ChainName, MediaName, InstallStoreQty, "& _ 
                "FirstWeek, LastInstallWeek, ProductName, BrandName, CreatedBy, CreationDate, Hom"& _ 
                "esite, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         StorePoolID, StorePoolCapacity"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"FROM          "& _ 
                "  dbo.udfMediaGapGridContractData(@FromDate, @ToDate, @ChainIDList, @CategoryIDL"& _ 
                "ist, @MediaFamilyIDList) AS MediaGapGridContractData"
            Me._commandCollection(0).CommandType = Global.System.Data.CommandType.Text
            Me._commandCollection(0).Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@FromDate", Global.System.Data.SqlDbType.DateTime, 0, Global.System.Data.ParameterDirection.Input, 0, 0, "", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._commandCollection(0).Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@ToDate", Global.System.Data.SqlDbType.DateTime, 0, Global.System.Data.ParameterDirection.Input, 0, 0, "", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._commandCollection(0).Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@ChainIDList", Global.System.Data.SqlDbType.NVarChar, 1024, Global.System.Data.ParameterDirection.Input, 0, 0, "", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._commandCollection(0).Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@CategoryIDList", Global.System.Data.SqlDbType.NVarChar, 1024, Global.System.Data.ParameterDirection.Input, 0, 0, "", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._commandCollection(0).Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@MediaFamilyIDList", Global.System.Data.SqlDbType.NVarChar, 1024, Global.System.Data.ParameterDirection.Input, 0, 0, "", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.Fill, true)>  _
        Public Overloads Overridable Function Fill(ByVal dataTable As DataSetMediaGap.BurstDataDataTable, ByVal FromDate As Date, ByVal ToDate As Date, ByVal ChainIDList As String, ByVal CategoryIDList As String, ByVal MediaFamilyIDList As String) As Integer
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            Me.Adapter.SelectCommand.Parameters(0).Value = CType(FromDate,Date)
            Me.Adapter.SelectCommand.Parameters(1).Value = CType(ToDate,Date)
            If (ChainIDList Is Nothing) Then
                Throw New Global.System.ArgumentNullException("ChainIDList")
            Else
                Me.Adapter.SelectCommand.Parameters(2).Value = CType(ChainIDList,String)
            End If
            If (CategoryIDList Is Nothing) Then
                Throw New Global.System.ArgumentNullException("CategoryIDList")
            Else
                Me.Adapter.SelectCommand.Parameters(3).Value = CType(CategoryIDList,String)
            End If
            If (MediaFamilyIDList Is Nothing) Then
                Throw New Global.System.ArgumentNullException("MediaFamilyIDList")
            Else
                Me.Adapter.SelectCommand.Parameters(4).Value = CType(MediaFamilyIDList,String)
            End If
            If (Me.ClearBeforeFill = true) Then
                dataTable.Clear
            End If
            Dim returnValue As Integer = Me.Adapter.Fill(dataTable)
            Return returnValue
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.[Select], true)>  _
        Public Overloads Overridable Function GetData(ByVal FromDate As Date, ByVal ToDate As Date, ByVal ChainIDList As String, ByVal CategoryIDList As String, ByVal MediaFamilyIDList As String) As DataSetMediaGap.BurstDataDataTable
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            Me.Adapter.SelectCommand.Parameters(0).Value = CType(FromDate,Date)
            Me.Adapter.SelectCommand.Parameters(1).Value = CType(ToDate,Date)
            If (ChainIDList Is Nothing) Then
                Throw New Global.System.ArgumentNullException("ChainIDList")
            Else
                Me.Adapter.SelectCommand.Parameters(2).Value = CType(ChainIDList,String)
            End If
            If (CategoryIDList Is Nothing) Then
                Throw New Global.System.ArgumentNullException("CategoryIDList")
            Else
                Me.Adapter.SelectCommand.Parameters(3).Value = CType(CategoryIDList,String)
            End If
            If (MediaFamilyIDList Is Nothing) Then
                Throw New Global.System.ArgumentNullException("MediaFamilyIDList")
            Else
                Me.Adapter.SelectCommand.Parameters(4).Value = CType(MediaFamilyIDList,String)
            End If
            Dim dataTable As DataSetMediaGap.BurstDataDataTable = New DataSetMediaGap.BurstDataDataTable()
            Me.Adapter.Fill(dataTable)
            Return dataTable
        End Function
    End Class
    
    '''<summary>
    '''Represents the connection and commands used to retrieve and save data.
    '''</summary>
    <Global.System.ComponentModel.DesignerCategoryAttribute("code"),  _
     Global.System.ComponentModel.ToolboxItem(true),  _
     Global.System.ComponentModel.DataObjectAttribute(true),  _
     Global.System.ComponentModel.DesignerAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterDesigner, Microsoft.VSDesigner"& _ 
        ", Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"),  _
     Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
    Partial Public Class ProvisionalBookingTableAdapter
        Inherits Global.System.ComponentModel.Component
        
        Private WithEvents _adapter As Global.System.Data.SqlClient.SqlDataAdapter
        
        Private _connection As Global.System.Data.SqlClient.SqlConnection
        
        Private _commandCollection() As Global.System.Data.SqlClient.SqlCommand
        
        Private _clearBeforeFill As Boolean
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New()
            MyBase.New
            Me.ClearBeforeFill = true
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private ReadOnly Property Adapter() As Global.System.Data.SqlClient.SqlDataAdapter
            Get
                If (Me._adapter Is Nothing) Then
                    Me.InitAdapter
                End If
                Return Me._adapter
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property Connection() As Global.System.Data.SqlClient.SqlConnection
            Get
                If (Me._connection Is Nothing) Then
                    Me.InitConnection
                End If
                Return Me._connection
            End Get
            Set
                Me._connection = value
                If (Not (Me.Adapter.InsertCommand) Is Nothing) Then
                    Me.Adapter.InsertCommand.Connection = value
                End If
                If (Not (Me.Adapter.DeleteCommand) Is Nothing) Then
                    Me.Adapter.DeleteCommand.Connection = value
                End If
                If (Not (Me.Adapter.UpdateCommand) Is Nothing) Then
                    Me.Adapter.UpdateCommand.Connection = value
                End If
                Dim i As Integer = 0
                Do While (i < Me.CommandCollection.Length)
                    If (Not (Me.CommandCollection(i)) Is Nothing) Then
                        CType(Me.CommandCollection(i),Global.System.Data.SqlClient.SqlCommand).Connection = value
                    End If
                    i = (i + 1)
                Loop
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected ReadOnly Property CommandCollection() As Global.System.Data.SqlClient.SqlCommand()
            Get
                If (Me._commandCollection Is Nothing) Then
                    Me.InitCommandCollection
                End If
                Return Me._commandCollection
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ClearBeforeFill() As Boolean
            Get
                Return Me._clearBeforeFill
            End Get
            Set
                Me._clearBeforeFill = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitAdapter()
            Me._adapter = New Global.System.Data.SqlClient.SqlDataAdapter()
            Dim tableMapping As Global.System.Data.Common.DataTableMapping = New Global.System.Data.Common.DataTableMapping()
            tableMapping.SourceTable = "Table"
            tableMapping.DataSetTable = "ProvisionalBooking"
            tableMapping.ColumnMappings.Add("ProvisionalBookingID", "ProvisionalBookingID")
            tableMapping.ColumnMappings.Add("ProvisionalBookingName", "ProvisionalBookingName")
            tableMapping.ColumnMappings.Add("ChainID", "ChainID")
            tableMapping.ColumnMappings.Add("CategoryID", "CategoryID")
            tableMapping.ColumnMappings.Add("MediaFamilyID", "MediaFamilyID")
            tableMapping.ColumnMappings.Add("BrandID", "BrandID")
            tableMapping.ColumnMappings.Add("FirstWeek", "FirstWeek")
            tableMapping.ColumnMappings.Add("MediaFamilyName", "MediaFamilyName")
            tableMapping.ColumnMappings.Add("CategoryName", "CategoryName")
            tableMapping.ColumnMappings.Add("ChainName", "ChainName")
            tableMapping.ColumnMappings.Add("BrandName", "BrandName")
            tableMapping.ColumnMappings.Add("LastWeek", "LastWeek")
            tableMapping.ColumnMappings.Add("BookedBy", "BookedBy")
            tableMapping.ColumnMappings.Add("BookTime", "BookTime")
            tableMapping.ColumnMappings.Add("ExpiryTime", "ExpiryTime")
            tableMapping.ColumnMappings.Add("Duration", "Duration")
            Me._adapter.TableMappings.Add(tableMapping)
            Me._adapter.DeleteCommand = New Global.System.Data.SqlClient.SqlCommand()
            Me._adapter.DeleteCommand.Connection = Me.Connection
            Me._adapter.DeleteCommand.CommandText = "DELETE FROM Sales.ProvisionalBooking"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"WHERE        (ProvisionalBookingID = @Origi"& _ 
                "nal_ProvisionalBookingID)"
            Me._adapter.DeleteCommand.CommandType = Global.System.Data.CommandType.Text
            Me._adapter.DeleteCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@Original_ProvisionalBookingID", Global.System.Data.SqlDbType.UniqueIdentifier, 16, Global.System.Data.ParameterDirection.Input, 0, 0, "ProvisionalBookingID", Global.System.Data.DataRowVersion.Original, false, Nothing, "", "", ""))
            Me._adapter.InsertCommand = New Global.System.Data.SqlClient.SqlCommand()
            Me._adapter.InsertCommand.Connection = Me.Connection
            Me._adapter.InsertCommand.CommandText = "INSERT INTO Sales.ProvisionalBooking"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         (ProvisionalBookin"& _ 
                "gID, ProvisionalBookingName, ChainID, CategoryID, MediaFamilyID, BrandID, FirstW"& _ 
                "eek, Duration, BookTime, ExpiryTime)"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"VALUES        (@ProvisionalBookingID,@Prov"& _ 
                "isionalBookingName,@ChainID,@CategoryID,@MediaFamilyID,@BrandID,@FirstWeek,@Dura"& _ 
                "tion,@BookTime,@ExpiryTime); "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"SELECT ProvisionalBookingID, ProvisionalBookingNa"& _ 
                "me, ChainID, CategoryID, MediaFamilyID, BrandID, FirstWeek, Duration FROM Sales."& _ 
                "ProvisionalBooking WHERE (ProvisionalBookingID = @ProvisionalBookingID)"
            Me._adapter.InsertCommand.CommandType = Global.System.Data.CommandType.Text
            Me._adapter.InsertCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@ProvisionalBookingID", Global.System.Data.SqlDbType.UniqueIdentifier, 16, Global.System.Data.ParameterDirection.Input, 0, 0, "ProvisionalBookingID", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._adapter.InsertCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@ProvisionalBookingName", Global.System.Data.SqlDbType.NVarChar, 250, Global.System.Data.ParameterDirection.Input, 0, 0, "ProvisionalBookingName", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._adapter.InsertCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@ChainID", Global.System.Data.SqlDbType.Int, 4, Global.System.Data.ParameterDirection.Input, 0, 0, "ChainID", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._adapter.InsertCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@CategoryID", Global.System.Data.SqlDbType.Int, 4, Global.System.Data.ParameterDirection.Input, 0, 0, "CategoryID", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._adapter.InsertCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@MediaFamilyID", Global.System.Data.SqlDbType.Int, 4, Global.System.Data.ParameterDirection.Input, 0, 0, "MediaFamilyID", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._adapter.InsertCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@BrandID", Global.System.Data.SqlDbType.UniqueIdentifier, 16, Global.System.Data.ParameterDirection.Input, 0, 0, "BrandID", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._adapter.InsertCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@FirstWeek", Global.System.Data.SqlDbType.DateTime, 8, Global.System.Data.ParameterDirection.Input, 0, 0, "FirstWeek", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._adapter.InsertCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@Duration", Global.System.Data.SqlDbType.Int, 4, Global.System.Data.ParameterDirection.Input, 0, 0, "Duration", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._adapter.InsertCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@BookTime", Global.System.Data.SqlDbType.DateTime, 8, Global.System.Data.ParameterDirection.Input, 0, 0, "BookTime", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._adapter.InsertCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@ExpiryTime", Global.System.Data.SqlDbType.DateTime, 8, Global.System.Data.ParameterDirection.Input, 0, 0, "ExpiryTime", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._adapter.UpdateCommand = New Global.System.Data.SqlClient.SqlCommand()
            Me._adapter.UpdateCommand.Connection = Me.Connection
            Me._adapter.UpdateCommand.CommandText = "UPDATE       Sales.ProvisionalBooking"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"SET                ProvisionalBookingID = "& _ 
                "@ProvisionalBookingID, ProvisionalBookingName = @ProvisionalBookingName, ChainID"& _ 
                " = @ChainID, CategoryID = @CategoryID, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         MediaFamilyID "& _ 
                "= @MediaFamilyID, BrandID = @BrandID, FirstWeek = @FirstWeek, Duration = @Durati"& _ 
                "on, BookTime = @BookTime, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         ExpiryTime = @ExpiryTime"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"W"& _ 
                "HERE        (ProvisionalBookingID = @Original_ProvisionalBookingID) AND (Provisi"& _ 
                "onalBookingName = @Original_ProvisionalBookingName) AND "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                      "& _ 
                "   (ChainID = @Original_ChainID) AND (CategoryID = @Original_CategoryID) AND (Me"& _ 
                "diaFamilyID = @Original_MediaFamilyID) AND (BrandID = @Original_BrandID) "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"     "& _ 
                "                    AND (FirstWeek = @Original_FirstWeek) AND (Duration = @Origi"& _ 
                "nal_Duration);  "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"SELECT ProvisionalBookingID, ProvisionalBookingName, ChainID, "& _ 
                "CategoryID, MediaFamilyID, BrandID, FirstWeek, Duration FROM Sales.ProvisionalBo"& _ 
                "oking WHERE (ProvisionalBookingID = @ProvisionalBookingID)"
            Me._adapter.UpdateCommand.CommandType = Global.System.Data.CommandType.Text
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@ProvisionalBookingID", Global.System.Data.SqlDbType.UniqueIdentifier, 16, Global.System.Data.ParameterDirection.Input, 0, 0, "ProvisionalBookingID", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@ProvisionalBookingName", Global.System.Data.SqlDbType.NVarChar, 250, Global.System.Data.ParameterDirection.Input, 0, 0, "ProvisionalBookingName", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@ChainID", Global.System.Data.SqlDbType.Int, 4, Global.System.Data.ParameterDirection.Input, 0, 0, "ChainID", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@CategoryID", Global.System.Data.SqlDbType.Int, 4, Global.System.Data.ParameterDirection.Input, 0, 0, "CategoryID", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@MediaFamilyID", Global.System.Data.SqlDbType.Int, 4, Global.System.Data.ParameterDirection.Input, 0, 0, "MediaFamilyID", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@BrandID", Global.System.Data.SqlDbType.UniqueIdentifier, 16, Global.System.Data.ParameterDirection.Input, 0, 0, "BrandID", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@FirstWeek", Global.System.Data.SqlDbType.DateTime, 8, Global.System.Data.ParameterDirection.Input, 0, 0, "FirstWeek", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@Duration", Global.System.Data.SqlDbType.Int, 4, Global.System.Data.ParameterDirection.Input, 0, 0, "Duration", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@BookTime", Global.System.Data.SqlDbType.DateTime, 8, Global.System.Data.ParameterDirection.Input, 0, 0, "BookTime", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@ExpiryTime", Global.System.Data.SqlDbType.DateTime, 8, Global.System.Data.ParameterDirection.Input, 0, 0, "ExpiryTime", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@Original_ProvisionalBookingID", Global.System.Data.SqlDbType.UniqueIdentifier, 16, Global.System.Data.ParameterDirection.Input, 0, 0, "ProvisionalBookingID", Global.System.Data.DataRowVersion.Original, false, Nothing, "", "", ""))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@Original_ProvisionalBookingName", Global.System.Data.SqlDbType.NVarChar, 250, Global.System.Data.ParameterDirection.Input, 0, 0, "ProvisionalBookingName", Global.System.Data.DataRowVersion.Original, false, Nothing, "", "", ""))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@Original_ChainID", Global.System.Data.SqlDbType.Int, 4, Global.System.Data.ParameterDirection.Input, 0, 0, "ChainID", Global.System.Data.DataRowVersion.Original, false, Nothing, "", "", ""))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@Original_CategoryID", Global.System.Data.SqlDbType.Int, 4, Global.System.Data.ParameterDirection.Input, 0, 0, "CategoryID", Global.System.Data.DataRowVersion.Original, false, Nothing, "", "", ""))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@Original_MediaFamilyID", Global.System.Data.SqlDbType.Int, 4, Global.System.Data.ParameterDirection.Input, 0, 0, "MediaFamilyID", Global.System.Data.DataRowVersion.Original, false, Nothing, "", "", ""))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@Original_BrandID", Global.System.Data.SqlDbType.UniqueIdentifier, 16, Global.System.Data.ParameterDirection.Input, 0, 0, "BrandID", Global.System.Data.DataRowVersion.Original, false, Nothing, "", "", ""))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@Original_FirstWeek", Global.System.Data.SqlDbType.DateTime, 8, Global.System.Data.ParameterDirection.Input, 0, 0, "FirstWeek", Global.System.Data.DataRowVersion.Original, false, Nothing, "", "", ""))
            Me._adapter.UpdateCommand.Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@Original_Duration", Global.System.Data.SqlDbType.Int, 4, Global.System.Data.ParameterDirection.Input, 0, 0, "Duration", Global.System.Data.DataRowVersion.Original, false, Nothing, "", "", ""))
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitConnection()
            Me._connection = New Global.System.Data.SqlClient.SqlConnection()
            Me._connection.ConnectionString = Global.NovaData.My.MySettings.Default.DBConnection
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitCommandCollection()
            Me._commandCollection = New Global.System.Data.SqlClient.SqlCommand(0) {}
            Me._commandCollection(0) = New Global.System.Data.SqlClient.SqlCommand()
            Me._commandCollection(0).Connection = Me.Connection
            Me._commandCollection(0).CommandText = "SELECT        ProvisionalBookingID, ProvisionalBookingName, MediaFamilyName, Cate"& _ 
                "goryName, ChainName, BrandName, FirstWeek, LastWeek, Weeks AS Duration, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"      "& _ 
                "                   BookedBy, BookTime, ExpiryTime, MediaFamilyID, CategoryID, Ch"& _ 
                "ainID, BrandID"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"FROM            dbo.udfMediaGapGridProvisionalBookingData(@FromD"& _ 
                "ate, @ToDate, @ChainIDList, @CategoryIDList, @MediaFamilyIDList) "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"             "& _ 
                "            AS MediaGapGridProvisionalBookingData"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"WHERE        (ExpiryTime > @C"& _ 
                "urrentTime)"
            Me._commandCollection(0).CommandType = Global.System.Data.CommandType.Text
            Me._commandCollection(0).Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@FromDate", Global.System.Data.SqlDbType.DateTime, 0, Global.System.Data.ParameterDirection.Input, 0, 0, "", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._commandCollection(0).Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@ToDate", Global.System.Data.SqlDbType.DateTime, 0, Global.System.Data.ParameterDirection.Input, 0, 0, "", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._commandCollection(0).Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@ChainIDList", Global.System.Data.SqlDbType.NVarChar, 1024, Global.System.Data.ParameterDirection.Input, 0, 0, "", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._commandCollection(0).Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@CategoryIDList", Global.System.Data.SqlDbType.NVarChar, 1024, Global.System.Data.ParameterDirection.Input, 0, 0, "", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._commandCollection(0).Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@MediaFamilyIDList", Global.System.Data.SqlDbType.NVarChar, 1024, Global.System.Data.ParameterDirection.Input, 0, 0, "", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._commandCollection(0).Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@CurrentTime", Global.System.Data.SqlDbType.DateTime, 8, Global.System.Data.ParameterDirection.Input, 0, 0, "ExpiryTime", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.Fill, true)>  _
        Public Overloads Overridable Function Fill(ByVal dataTable As DataSetMediaGap.ProvisionalBookingDataTable, ByVal FromDate As Date, ByVal ToDate As Date, ByVal ChainIDList As String, ByVal CategoryIDList As String, ByVal MediaFamilyIDList As String, ByVal CurrentTime As Date) As Integer
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            Me.Adapter.SelectCommand.Parameters(0).Value = CType(FromDate,Date)
            Me.Adapter.SelectCommand.Parameters(1).Value = CType(ToDate,Date)
            If (ChainIDList Is Nothing) Then
                Throw New Global.System.ArgumentNullException("ChainIDList")
            Else
                Me.Adapter.SelectCommand.Parameters(2).Value = CType(ChainIDList,String)
            End If
            If (CategoryIDList Is Nothing) Then
                Throw New Global.System.ArgumentNullException("CategoryIDList")
            Else
                Me.Adapter.SelectCommand.Parameters(3).Value = CType(CategoryIDList,String)
            End If
            If (MediaFamilyIDList Is Nothing) Then
                Throw New Global.System.ArgumentNullException("MediaFamilyIDList")
            Else
                Me.Adapter.SelectCommand.Parameters(4).Value = CType(MediaFamilyIDList,String)
            End If
            Me.Adapter.SelectCommand.Parameters(5).Value = CType(CurrentTime,Date)
            If (Me.ClearBeforeFill = true) Then
                dataTable.Clear
            End If
            Dim returnValue As Integer = Me.Adapter.Fill(dataTable)
            Return returnValue
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.[Select], true)>  _
        Public Overloads Overridable Function GetData(ByVal FromDate As Date, ByVal ToDate As Date, ByVal ChainIDList As String, ByVal CategoryIDList As String, ByVal MediaFamilyIDList As String, ByVal CurrentTime As Date) As DataSetMediaGap.ProvisionalBookingDataTable
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            Me.Adapter.SelectCommand.Parameters(0).Value = CType(FromDate,Date)
            Me.Adapter.SelectCommand.Parameters(1).Value = CType(ToDate,Date)
            If (ChainIDList Is Nothing) Then
                Throw New Global.System.ArgumentNullException("ChainIDList")
            Else
                Me.Adapter.SelectCommand.Parameters(2).Value = CType(ChainIDList,String)
            End If
            If (CategoryIDList Is Nothing) Then
                Throw New Global.System.ArgumentNullException("CategoryIDList")
            Else
                Me.Adapter.SelectCommand.Parameters(3).Value = CType(CategoryIDList,String)
            End If
            If (MediaFamilyIDList Is Nothing) Then
                Throw New Global.System.ArgumentNullException("MediaFamilyIDList")
            Else
                Me.Adapter.SelectCommand.Parameters(4).Value = CType(MediaFamilyIDList,String)
            End If
            Me.Adapter.SelectCommand.Parameters(5).Value = CType(CurrentTime,Date)
            Dim dataTable As DataSetMediaGap.ProvisionalBookingDataTable = New DataSetMediaGap.ProvisionalBookingDataTable()
            Me.Adapter.Fill(dataTable)
            Return dataTable
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
        Public Overloads Overridable Function Update(ByVal dataTable As DataSetMediaGap.ProvisionalBookingDataTable) As Integer
            Return Me.Adapter.Update(dataTable)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
        Public Overloads Overridable Function Update(ByVal dataSet As DataSetMediaGap) As Integer
            Return Me.Adapter.Update(dataSet, "ProvisionalBooking")
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
        Public Overloads Overridable Function Update(ByVal dataRow As Global.System.Data.DataRow) As Integer
            Return Me.Adapter.Update(New Global.System.Data.DataRow() {dataRow})
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
        Public Overloads Overridable Function Update(ByVal dataRows() As Global.System.Data.DataRow) As Integer
            Return Me.Adapter.Update(dataRows)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.Delete, true)>  _
        Public Overloads Overridable Function Delete(ByVal Original_ProvisionalBookingID As System.Guid) As Integer
            Me.Adapter.DeleteCommand.Parameters(0).Value = CType(Original_ProvisionalBookingID,System.Guid)
            Dim previousConnectionState As Global.System.Data.ConnectionState = Me.Adapter.DeleteCommand.Connection.State
            If ((Me.Adapter.DeleteCommand.Connection.State And Global.System.Data.ConnectionState.Open)  _
                        <> Global.System.Data.ConnectionState.Open) Then
                Me.Adapter.DeleteCommand.Connection.Open
            End If
            Try 
                Dim returnValue As Integer = Me.Adapter.DeleteCommand.ExecuteNonQuery
                Return returnValue
            Finally
                If (previousConnectionState = Global.System.Data.ConnectionState.Closed) Then
                    Me.Adapter.DeleteCommand.Connection.Close
                End If
            End Try
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.Insert, true)>  _
        Public Overloads Overridable Function Insert(ByVal ProvisionalBookingID As System.Guid, ByVal ProvisionalBookingName As String, ByVal ChainID As Integer, ByVal CategoryID As Integer, ByVal MediaFamilyID As Integer, ByVal BrandID As System.Guid, ByVal FirstWeek As Date, ByVal Duration As Integer, ByVal BookTime As Date, ByVal ExpiryTime As Date) As Integer
            Me.Adapter.InsertCommand.Parameters(0).Value = CType(ProvisionalBookingID,System.Guid)
            If (ProvisionalBookingName Is Nothing) Then
                Throw New Global.System.ArgumentNullException("ProvisionalBookingName")
            Else
                Me.Adapter.InsertCommand.Parameters(1).Value = CType(ProvisionalBookingName,String)
            End If
            Me.Adapter.InsertCommand.Parameters(2).Value = CType(ChainID,Integer)
            Me.Adapter.InsertCommand.Parameters(3).Value = CType(CategoryID,Integer)
            Me.Adapter.InsertCommand.Parameters(4).Value = CType(MediaFamilyID,Integer)
            Me.Adapter.InsertCommand.Parameters(5).Value = CType(BrandID,System.Guid)
            Me.Adapter.InsertCommand.Parameters(6).Value = CType(FirstWeek,Date)
            Me.Adapter.InsertCommand.Parameters(7).Value = CType(Duration,Integer)
            Me.Adapter.InsertCommand.Parameters(8).Value = CType(BookTime,Date)
            Me.Adapter.InsertCommand.Parameters(9).Value = CType(ExpiryTime,Date)
            Dim previousConnectionState As Global.System.Data.ConnectionState = Me.Adapter.InsertCommand.Connection.State
            If ((Me.Adapter.InsertCommand.Connection.State And Global.System.Data.ConnectionState.Open)  _
                        <> Global.System.Data.ConnectionState.Open) Then
                Me.Adapter.InsertCommand.Connection.Open
            End If
            Try 
                Dim returnValue As Integer = Me.Adapter.InsertCommand.ExecuteNonQuery
                Return returnValue
            Finally
                If (previousConnectionState = Global.System.Data.ConnectionState.Closed) Then
                    Me.Adapter.InsertCommand.Connection.Close
                End If
            End Try
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.Update, true)>  _
        Public Overloads Overridable Function Update( _
                    ByVal ProvisionalBookingID As System.Guid,  _
                    ByVal ProvisionalBookingName As String,  _
                    ByVal ChainID As Integer,  _
                    ByVal CategoryID As Integer,  _
                    ByVal MediaFamilyID As Integer,  _
                    ByVal BrandID As System.Guid,  _
                    ByVal FirstWeek As Date,  _
                    ByVal Duration As Integer,  _
                    ByVal BookTime As Date,  _
                    ByVal ExpiryTime As Date,  _
                    ByVal Original_ProvisionalBookingID As System.Guid,  _
                    ByVal Original_ProvisionalBookingName As String,  _
                    ByVal Original_ChainID As Integer,  _
                    ByVal Original_CategoryID As Integer,  _
                    ByVal Original_MediaFamilyID As Integer,  _
                    ByVal Original_BrandID As System.Guid,  _
                    ByVal Original_FirstWeek As Date,  _
                    ByVal Original_Duration As Integer) As Integer
            Me.Adapter.UpdateCommand.Parameters(0).Value = CType(ProvisionalBookingID,System.Guid)
            If (ProvisionalBookingName Is Nothing) Then
                Throw New Global.System.ArgumentNullException("ProvisionalBookingName")
            Else
                Me.Adapter.UpdateCommand.Parameters(1).Value = CType(ProvisionalBookingName,String)
            End If
            Me.Adapter.UpdateCommand.Parameters(2).Value = CType(ChainID,Integer)
            Me.Adapter.UpdateCommand.Parameters(3).Value = CType(CategoryID,Integer)
            Me.Adapter.UpdateCommand.Parameters(4).Value = CType(MediaFamilyID,Integer)
            Me.Adapter.UpdateCommand.Parameters(5).Value = CType(BrandID,System.Guid)
            Me.Adapter.UpdateCommand.Parameters(6).Value = CType(FirstWeek,Date)
            Me.Adapter.UpdateCommand.Parameters(7).Value = CType(Duration,Integer)
            Me.Adapter.UpdateCommand.Parameters(8).Value = CType(BookTime,Date)
            Me.Adapter.UpdateCommand.Parameters(9).Value = CType(ExpiryTime,Date)
            Me.Adapter.UpdateCommand.Parameters(10).Value = CType(Original_ProvisionalBookingID,System.Guid)
            If (Original_ProvisionalBookingName Is Nothing) Then
                Throw New Global.System.ArgumentNullException("Original_ProvisionalBookingName")
            Else
                Me.Adapter.UpdateCommand.Parameters(11).Value = CType(Original_ProvisionalBookingName,String)
            End If
            Me.Adapter.UpdateCommand.Parameters(12).Value = CType(Original_ChainID,Integer)
            Me.Adapter.UpdateCommand.Parameters(13).Value = CType(Original_CategoryID,Integer)
            Me.Adapter.UpdateCommand.Parameters(14).Value = CType(Original_MediaFamilyID,Integer)
            Me.Adapter.UpdateCommand.Parameters(15).Value = CType(Original_BrandID,System.Guid)
            Me.Adapter.UpdateCommand.Parameters(16).Value = CType(Original_FirstWeek,Date)
            Me.Adapter.UpdateCommand.Parameters(17).Value = CType(Original_Duration,Integer)
            Dim previousConnectionState As Global.System.Data.ConnectionState = Me.Adapter.UpdateCommand.Connection.State
            If ((Me.Adapter.UpdateCommand.Connection.State And Global.System.Data.ConnectionState.Open)  _
                        <> Global.System.Data.ConnectionState.Open) Then
                Me.Adapter.UpdateCommand.Connection.Open
            End If
            Try 
                Dim returnValue As Integer = Me.Adapter.UpdateCommand.ExecuteNonQuery
                Return returnValue
            Finally
                If (previousConnectionState = Global.System.Data.ConnectionState.Closed) Then
                    Me.Adapter.UpdateCommand.Connection.Close
                End If
            End Try
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.Update, true)>  _
        Public Overloads Overridable Function Update( _
                    ByVal ProvisionalBookingName As String,  _
                    ByVal ChainID As Integer,  _
                    ByVal CategoryID As Integer,  _
                    ByVal MediaFamilyID As Integer,  _
                    ByVal BrandID As System.Guid,  _
                    ByVal FirstWeek As Date,  _
                    ByVal Duration As Integer,  _
                    ByVal BookTime As Date,  _
                    ByVal ExpiryTime As Date,  _
                    ByVal Original_ProvisionalBookingID As System.Guid,  _
                    ByVal Original_ProvisionalBookingName As String,  _
                    ByVal Original_ChainID As Integer,  _
                    ByVal Original_CategoryID As Integer,  _
                    ByVal Original_MediaFamilyID As Integer,  _
                    ByVal Original_BrandID As System.Guid,  _
                    ByVal Original_FirstWeek As Date,  _
                    ByVal Original_Duration As Integer) As Integer
            Return Me.Update(Original_ProvisionalBookingID, ProvisionalBookingName, ChainID, CategoryID, MediaFamilyID, BrandID, FirstWeek, Duration, BookTime, ExpiryTime, Original_ProvisionalBookingID, Original_ProvisionalBookingName, Original_ChainID, Original_CategoryID, Original_MediaFamilyID, Original_BrandID, Original_FirstWeek, Original_Duration)
        End Function
    End Class
End Namespace
