﻿using DataAccess;

namespace DataService.Security
{
    class EnableLegacyLoginsCommandExecutor : CommandExecutor<EnableLegacyLoginsCommand>
    {

        public override void Execute(EnableLegacyLoginsCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.EnableLegacyLogins))
            {
                storedprocedure.AddInputParameter("logins", command.Logins);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
            }
        }

    }
}
