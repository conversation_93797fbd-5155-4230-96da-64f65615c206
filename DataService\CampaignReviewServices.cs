﻿using DataAccess;
using DataService.CampaignReview;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Text;

namespace DataService
{
    public static class CampaignReviewServices
    {

        public static void ImportScannerData(ref string errormessage, List<string> archivefilenames, BackgroundWorker worker)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var pathbuilder = new StringBuilder();
                pathbuilder.Append(Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData));
                pathbuilder.Append("\\" + Universal.Settings.APPLICATIONNAME + "\\scannerdataimport_");
                pathbuilder.Append(DateTime.Now.Year.ToString());
                pathbuilder.Append(DateTime.Now.Month.ToString("00"));
                pathbuilder.Append(DateTime.Now.Day.ToString("00"));
                pathbuilder.Append("-");
                pathbuilder.Append(DateTime.Now.Hour.ToString("00"));
                pathbuilder.Append(DateTime.Now.Minute.ToString("00"));

                var command = new ImportScannerDataCommand(Universal.Settings.CurrentSession.SessionId, pathbuilder.ToString(), archivefilenames);
                manager.ExecuteCommand(ref errormessage, command, new ImportScannerDataCommandExecutor(worker));
            }
        }

        public static DataTable GetTableOfScanFiles(ref string errormessage)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                Guid sessionid = Universal.Settings.CurrentSession.SessionId;
                var command = new GetTableOfScanFilesCommand(sessionid);
                manager.ExecuteCommand(ref errormessage, command, new GetTableOfScanFilesCommandExecutor());
                return command.Table;
            }
        }

        public static void DeleteScanFiles(ref string errormessage, List<DataRow> rowstodelete)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.ConnectionString))
            {
                var command = new DeleteScanFilesCommand(Universal.Settings.CurrentSession.SessionId, rowstodelete);
                manager.ExecuteCommand(ref errormessage, command, new DeleteScanFilesCommandExecutor());
            }
        }

    }
}
