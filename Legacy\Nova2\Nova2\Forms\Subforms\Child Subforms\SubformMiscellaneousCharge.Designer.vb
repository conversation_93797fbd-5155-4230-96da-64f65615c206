<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformMiscellaneousCharge
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformMiscellaneousCharge))
        Me.LabelMiscellanouesCharge = New DevExpress.XtraEditors.LabelControl
        Me.HyperlinkMiscellanouesCharge = New DevExpress.XtraEditors.LabelControl
        Me.LabelAmount = New DevExpress.XtraEditors.LabelControl
        Me.TextEditAmount = New DevExpress.XtraEditors.TextEdit
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonOK = New DevExpress.XtraEditors.SimpleButton
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton
        CType(Me.TextEditAmount.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!)
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.TabIndex = 0
        '
        'LabelMiscellanouesCharge
        '
        Me.LabelMiscellanouesCharge.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelMiscellanouesCharge.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelMiscellanouesCharge.Location = New System.Drawing.Point(12, 57)
        Me.LabelMiscellanouesCharge.Margin = New System.Windows.Forms.Padding(3, 3, 3, 10)
        Me.LabelMiscellanouesCharge.Name = "LabelMiscellanouesCharge"
        Me.LabelMiscellanouesCharge.Size = New System.Drawing.Size(129, 13)
        Me.LabelMiscellanouesCharge.TabIndex = 1
        Me.LabelMiscellanouesCharge.Text = "Miscellaneous Charge:"
        '
        'HyperlinkMiscellanouesCharge
        '
        Me.HyperlinkMiscellanouesCharge.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.HyperlinkMiscellanouesCharge.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkMiscellanouesCharge.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkMiscellanouesCharge.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkMiscellanouesCharge.AutoEllipsis = True
        Me.HyperlinkMiscellanouesCharge.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.HyperlinkMiscellanouesCharge.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkMiscellanouesCharge.Location = New System.Drawing.Point(167, 57)
        Me.HyperlinkMiscellanouesCharge.Margin = New System.Windows.Forms.Padding(3, 3, 3, 10)
        Me.HyperlinkMiscellanouesCharge.Name = "HyperlinkMiscellanouesCharge"
        Me.HyperlinkMiscellanouesCharge.Size = New System.Drawing.Size(652, 13)
        Me.HyperlinkMiscellanouesCharge.TabIndex = 2
        Me.HyperlinkMiscellanouesCharge.Text = "Select..."
        '
        'LabelAmount
        '
        Me.LabelAmount.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelAmount.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelAmount.Location = New System.Drawing.Point(12, 83)
        Me.LabelAmount.Margin = New System.Windows.Forms.Padding(3, 3, 3, 10)
        Me.LabelAmount.Name = "LabelAmount"
        Me.LabelAmount.Size = New System.Drawing.Size(49, 13)
        Me.LabelAmount.TabIndex = 3
        Me.LabelAmount.Text = "Amount:"
        '
        'TextEditAmount
        '
        Me.TextEditAmount.EditValue = 0
        Me.TextEditAmount.Location = New System.Drawing.Point(167, 80)
        Me.TextEditAmount.Name = "TextEditAmount"
        Me.TextEditAmount.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditAmount.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditAmount.Properties.Appearance.Options.UseFont = True
        Me.TextEditAmount.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditAmount.Properties.Mask.EditMask = "c"
        Me.TextEditAmount.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditAmount.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TextEditAmount.Size = New System.Drawing.Size(112, 20)
        Me.TextEditAmount.TabIndex = 4
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        '
        'ButtonOK
        '
        Me.ButtonOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonOK.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonOK.Appearance.Options.UseFont = True
        Me.ButtonOK.ImageIndex = 2
        Me.ButtonOK.ImageList = Me.ImageList24x24
        Me.ButtonOK.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonOK.Location = New System.Drawing.Point(605, 509)
        Me.ButtonOK.LookAndFeel.SkinName = "Black"
        Me.ButtonOK.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonOK.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonOK.Name = "ButtonOK"
        Me.ButtonOK.Size = New System.Drawing.Size(100, 28)
        Me.ButtonOK.TabIndex = 5
        Me.ButtonOK.Text = "OK"
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(711, 509)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(100, 28)
        Me.ButtonCancel.TabIndex = 6
        Me.ButtonCancel.Text = "Cancel"
        '
        'SubformMiscellaneousCharge
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.Controls.Add(Me.ButtonOK)
        Me.Controls.Add(Me.ButtonCancel)
        Me.Controls.Add(Me.LabelMiscellanouesCharge)
        Me.Controls.Add(Me.HyperlinkMiscellanouesCharge)
        Me.Controls.Add(Me.LabelAmount)
        Me.Controls.Add(Me.TextEditAmount)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Name = "SubformMiscellaneousCharge"
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        Me.Controls.SetChildIndex(Me.TextEditAmount, 0)
        Me.Controls.SetChildIndex(Me.LabelAmount, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkMiscellanouesCharge, 0)
        Me.Controls.SetChildIndex(Me.LabelMiscellanouesCharge, 0)
        Me.Controls.SetChildIndex(Me.ButtonCancel, 0)
        Me.Controls.SetChildIndex(Me.ButtonOK, 0)
        CType(Me.TextEditAmount.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents LabelMiscellanouesCharge As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkMiscellanouesCharge As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelAmount As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditAmount As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents ButtonOK As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton

End Class
