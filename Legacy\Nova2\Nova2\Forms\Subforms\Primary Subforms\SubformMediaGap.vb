Public Class SubformMediaGap

    Private MediaGapManager As MediaGap
    Private GridIsVisible As Boolean = True

#Region "Event Handlers"

    Private Sub SubformMediaGap_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        ' Create a new media gap object.
        Dim ConsumingForm As BaseForm = TopLevelControl
        MediaGapManager = New MediaGap _
        (GridMediaGapGrid, GridProvisionalBookings, ConsumingForm)

        ' Setup the contract list.
        GridContracts.AutoGenerateColumns = False
        GridContracts.DataSource = MediaGapManager.ContractListBindingSource

        ' Make the default FROM date a Monday.
        UpdateFromDate()

        'hide add/delete buttons if in ops_contractmodifier
        If My.User.IsInRole("ops_contractmodifier") Then
            ButtonAdd.Visible = False
            ButtonDelete.Visible = False
            ButtonEdit.Visible = False

        End If

        ' Create a grid manager for the provisional booking list.
        GridProvisionalBookings.Tag = New GridManager _
        (GridProvisionalBookings, _
        TextEditSearchProvisionalBooking, _
        Nothing, _
        Nothing, _
        PictureAdvancedSearchProvisionalBooking, _
        PictureClearSearchProvisionalBooking, _
        ButtonEdit, _
        ButtonDelete)

    End Sub

    Private Sub HyperlinkHyperlinkGridView_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles HyperlinkGridView.Click, HyperlinkBookingList.Click
        ShowHideGrid()
    End Sub

    Private Sub HyperlinkFromDate_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles HyperlinkFromDate.Click

        ' Get a date selection form the user.
        Dim SelectedDate As Nullable(Of Date) = DateSelector.GetDate(True, MediaGapManager.FirstWeekToView)

        ' If the user selected a valid date, assign it to the media gap object.
        If SelectedDate.HasValue Then
            MediaGapManager.FirstWeekToView = SelectedDate.Value
            UpdateFromDate()
        End If

    End Sub

    Private Sub ButtonQuery_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonQuery.Click

        ' Get the data.
        MediaGapManager.RefreshData(My.Settings.DBConnection)

    End Sub

    Private Sub HyperlinkChains_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles HyperlinkChains.Click

        ' Get the objects relevant to this event.
        Dim HyperLink As LabelControl = CType(sender, LabelControl)
        Dim Table As DataTable = MediaGapManager.ChainsToView

        ' Update the data.
        Table = FormMediaGapSelectedChains.UpdateSelectedChains(Table)

        ' Update the hyperlink.
        HyperLink.Text = "Chains (" & Table.Rows.Count.ToString & ")"

    End Sub

    Private Sub HyperlinkMediaFamilies_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles HyperlinkMediaFamilies.Click

        ' Get the objects relevant to this event.
        Dim HyperLink As LabelControl = CType(sender, LabelControl)
        Dim Table As DataTable = MediaGapManager.MediaFamiliesToView

        ' Update the data.
        Table = FormMediaGapSelectedMediaFamilies.UpdateSelectedMediaFamilies(Table)

        ' Update the hyperlink.
        HyperLink.Text = "Media Families (" & Table.Rows.Count.ToString & ")"

    End Sub

    Private Sub HyperlinkCategories_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles HyperlinkCategories.Click

        ' Get the objects relevant to this event.
        Dim HyperLink As LabelControl = CType(sender, LabelControl)
        Dim Table As DataTable = MediaGapManager.CategoriesToView

        ' Update the data.
        Table = FormMediaGapSelectedCategories.UpdateSelectedCategories(Table)

        ' Update the hyperlink.
        HyperLink.Text = "Categories (" & Table.Rows.Count.ToString & ")"

    End Sub

    Private Sub ButtonSelectAll_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonSelectAll.Click

        ' Update the data.
        MediaGapManager.ChainsToView = FormMediaGapSelectedCategories.SelectAll(MediaGapManager.ChainsToView)
        MediaGapManager.CategoriesToView = FormMediaGapSelectedCategories.SelectAll(MediaGapManager.CategoriesToView)
        MediaGapManager.MediaFamiliesToView = FormMediaGapSelectedMediaFamilies.SelectAll(MediaGapManager.MediaFamiliesToView)

        ' Update the hyperlink.
        HyperlinkChains.Text = "Chains (" & MediaGapManager.ChainsToView.Rows.Count.ToString & ")"
        HyperlinkCategories.Text = "Categories (" & MediaGapManager.CategoriesToView.Rows.Count.ToString & ")"
        HyperlinkMediaFamilies.Text = "Media Families (" & MediaGapManager.MediaFamiliesToView.Rows.Count.ToString & ")"

    End Sub

    Private Sub ButtonClear_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonClear.Click

        ' Reset media gap manager properties.
        MediaGapManager.ChainsToView = Nothing
        MediaGapManager.CategoriesToView = Nothing
        MediaGapManager.MediaFamiliesToView = Nothing

        ' Reset hyperlinks.
        HyperlinkCategories.Text = "Categories (0)"
        HyperlinkChains.Text = "Chains (0)"
        HyperlinkMediaFamilies.Text = "Media Families (0)"

        ' Clear the grid.
        While GridMediaGapGrid.Rows.Count > 0
            GridMediaGapGrid.Rows.RemoveAt(GridMediaGapGrid.Rows.GetLastRow(DataGridViewElementStates.None))
        End While

    End Sub

    Private Sub ButtonInfo_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonInfo.Click
        FormMediaGapContractInfo.ShowInfo(CType(GridContracts.SelectedRows(0).DataBoundItem, DataRowView).Item("ContractID"))
    End Sub

    Private Sub GridContracts_CellDoubleClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles GridContracts.CellDoubleClick
        If e.RowIndex > -1 Then
            ' The row double-clicked is NOT the column header row. Handle the event.
            ButtonInfo_Click(sender, e)
        End If
    End Sub

    Private Sub GridContracts_RowsAdded(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewRowsAddedEventArgs) Handles GridContracts.RowsAdded
        ButtonInfo.Enabled = True
    End Sub

    Private Sub GridContracts_RowsRemoved(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewRowsRemovedEventArgs) Handles GridContracts.RowsRemoved
        ButtonInfo.Enabled = False
    End Sub

    Private Sub ButtonAdd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonAdd.Click

        ' Create variables for preselected options for provisional booking creation.
        Dim ChainList As New List(Of DataRow)
        Dim FirstWeek As Nullable(Of Date)
        Dim FirstSelectedColumn As Nullable(Of Integer)
        Dim LastSelectedColumn As Nullable(Of Integer)

        ' Check all selected cells.
        For Each SelectedCell As DataGridViewCell In GridMediaGapGrid.SelectedCells
            If SelectedCell.ColumnIndex > 0 Then
                ' This cell is not a chain label in the first column. Safe to continue.

                ' Remember the column index of this cell so that the difference between the indexes of the first and
                ' last selected columns can later be determined so that we can obtain the number of weeks selected.
                If FirstSelectedColumn.HasValue Then
                    If SelectedCell.ColumnIndex < FirstSelectedColumn.Value Then
                        FirstSelectedColumn = SelectedCell.ColumnIndex
                    End If
                Else
                    FirstSelectedColumn = SelectedCell.ColumnIndex
                End If
                If LastSelectedColumn.HasValue Then
                    If SelectedCell.ColumnIndex > LastSelectedColumn.Value Then
                        LastSelectedColumn = SelectedCell.ColumnIndex
                    End If
                Else
                    LastSelectedColumn = SelectedCell.ColumnIndex
                End If

                ' Get the CellInfo for this cell.
                Dim Key As String = SelectedCell.ColumnIndex.ToString("000") & SelectedCell.RowIndex.ToString("000")
                Dim CellInformation As CellInfo = MediaGapManager.CellInfoCollection(Key)

                ' Get the first week of all the selected cells.
                If FirstWeek.HasValue Then
                    ' The FirstWeek variable has been assigned a value from a different selected cell.
                    If CellInformation.Week < FirstWeek Then
                        ' The value assigned to FirstWeek is a date later than the date of the current cell. Replace
                        ' the value of FirstWeek with the week of the current cell.
                        FirstWeek = CellInformation.Week
                    End If
                Else
                    ' The FirstWeek variable has no value. Assign it the value of the current cell's week.
                    FirstWeek = CellInformation.Week
                End If

                Dim RowIndex As Integer = SelectedCell.RowIndex
                ' The chain matching this row hasn't been added yet. Get the name of the chain.
                Dim ChainName As String = CType(GridMediaGapGrid.Rows(RowIndex).DataBoundItem, DataRowView).Row("ChainName")
                Dim FilterFriendlyChainName As String = ChainName.Replace("'", "''")
                ' Get the data row of the chain.
                Dim ChainRow() As DataRow = _
                MediaGapManager.ChainsToView.Select("ChainName = '" & FilterFriendlyChainName & "'")
                ' Add the data row to the list.
                If ChainList.Contains(ChainRow(0)) = False Then
                    ChainList.Add(ChainRow(0))
                End If
            End If
        Next

        ' Create new provisional bookings with preselected media families, categories and chains.
        If GridIsVisible AndAlso FirstWeek.HasValue Then
            ' Display Provisional Booking subform with preselected values.
            AddChild(New SubformProvisionalBooking _
            (MediaGapManager, _
            MediaGapManager.MediaFamiliesToView, _
            MediaGapManager.CategoriesToView, _
            ChainList, _
            FirstWeek, _
            LastSelectedColumn.Value - FirstSelectedColumn.Value + 1))
        Else
            ' Nothing has been selected in the grid. Display Provisional Booking subform without preselected values.
            AddChild(New SubformProvisionalBooking(MediaGapManager))
        End If

    End Sub

    Private Sub ButtonDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonDelete.Click
        MediaGap.Delete(GridProvisionalBookings, My.Settings.DBConnection, MediaGapManager)
    End Sub

    Private Sub ButtonEdit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonEdit.Click

        ' Get the form that is consuming this method so that the ShowMessage method can be used.
        Dim Consumingform As LiquidShell.BaseForm = CType(GridProvisionalBookings.TopLevelControl, LiquidShell.BaseForm)

        ' Stop if any selected rows have an empty brand name (i.e. the user doesn't have permission to modify the
        ' selected provisional bookings).
        For Each SelectedGridRow As DataGridViewRow In GridProvisionalBookings.SelectedRows
            Dim Row As DataRowView = CType(SelectedGridRow.DataBoundItem, DataRowView)
            If String.IsNullOrEmpty(Row("BrandName")) Then
                Dim MessageText As String = "You do not have permission to modify at least one of the selected provisional " _
                & "bookings."
                Consumingform.ShowMessage(MessageText, "Sadly, You Don't Have the Required Permissions", MessageBoxIcon.Error)
                Exit Sub
            End If
        Next

        ' Change the dates by displaying a form to the user.
        FormEditProvisionalBooking.ChangeDate _
        (GridProvisionalBookings, My.Settings.DBConnection, MediaGapManager.ProvisionalBookingData, MediaGapManager)

    End Sub

#End Region

#Region "Private Methods"

    Private Sub UpdateFromDate()
        HyperlinkFromDate.Text = "From " & MediaGapManager.FirstWeekToView.Date.ToShortDateString
    End Sub

    Private Sub ShowHideGrid()

        ' Change data member of the binding source for the provisional booking list.
        MediaGapManager.ToggleProvisionalBookingList()

        ' Check the current visibilty state of the grid.
        If GridIsVisible = False Then
            ' User opted to show the grid.
            LiquidAgent.EnableHyperlink(HyperlinkGridView, False)
            LiquidAgent.EnableHyperlink(HyperlinkBookingList, True)
            ' Display the grid.
            TableLayoutPanelMediaGap.RowStyles(0).Height = 60
            TableLayoutPanelMediaGap.RowStyles(1).Height = 15
            TableLayoutPanelMediaGap.ColumnStyles(0).Width = 120
            TableLayoutPanelMediaGap.ColumnStyles(1).Width = 15
        Else
            ' User opted to hide the grid.
            LiquidAgent.EnableHyperlink(HyperlinkBookingList, False)
            LiquidAgent.EnableHyperlink(HyperlinkGridView, True)
            ' Hide the grid.
            TableLayoutPanelMediaGap.RowStyles(0).Height = 0
            TableLayoutPanelMediaGap.RowStyles(1).Height = 0
            TableLayoutPanelMediaGap.ColumnStyles(0).Width = 0
            TableLayoutPanelMediaGap.ColumnStyles(1).Width = 0
        End If

        ' Update the grid visibilty variable.
        GridIsVisible = Not GridIsVisible

    End Sub

#End Region

End Class
