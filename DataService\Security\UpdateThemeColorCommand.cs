﻿using DataAccess;
using System;
using System.Drawing;

namespace DataService.Security
{
    class UpdateThemeColorCommand : Command
    {
        public Guid SessionId { get; set; }
        public int ThemeColorARGB { get; set; }

        public UpdateThemeColorCommand(Guid sessionid, Color themecolor)
        {
            SessionId = sessionid;
            ThemeColorARGB = themecolor.ToArgb();
        }
    }
}
