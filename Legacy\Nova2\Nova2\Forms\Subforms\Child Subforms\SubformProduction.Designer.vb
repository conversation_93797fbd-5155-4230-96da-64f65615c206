<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformProduction
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformProduction))
        Me.LabelInventoryItem = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkInventoryItem = New DevExpress.XtraEditors.LabelControl()
        Me.LabelQuantity = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkQty = New DevExpress.XtraEditors.LabelControl()
        Me.LabelSellPrice = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSellPrice = New DevExpress.XtraEditors.TextEdit()
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonOK = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.GroupNotes = New DevExpress.XtraEditors.GroupControl()
        Me.MemoEditNotes = New DevExpress.XtraEditors.MemoEdit()
        Me.HyperlinkBrand = New DevExpress.XtraEditors.LabelControl()
        Me.LabelBrand = New DevExpress.XtraEditors.LabelControl()
        Me.LabelSellPriceWarning = New DevExpress.XtraEditors.LabelControl()
        CType(Me.TextEditSellPrice.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupNotes, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupNotes.SuspendLayout()
        CType(Me.MemoEditNotes.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!)
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.TabIndex = 0
        '
        'LabelInventoryItem
        '
        Me.LabelInventoryItem.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelInventoryItem.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelInventoryItem.Location = New System.Drawing.Point(12, 57)
        Me.LabelInventoryItem.Margin = New System.Windows.Forms.Padding(3, 3, 3, 10)
        Me.LabelInventoryItem.Name = "LabelInventoryItem"
        Me.LabelInventoryItem.Size = New System.Drawing.Size(92, 13)
        Me.LabelInventoryItem.TabIndex = 1
        Me.LabelInventoryItem.Text = "Inventory Item:"
        '
        'HyperlinkInventoryItem
        '
        Me.HyperlinkInventoryItem.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.HyperlinkInventoryItem.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkInventoryItem.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkInventoryItem.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkInventoryItem.AutoEllipsis = True
        Me.HyperlinkInventoryItem.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.HyperlinkInventoryItem.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkInventoryItem.Location = New System.Drawing.Point(155, 57)
        Me.HyperlinkInventoryItem.Margin = New System.Windows.Forms.Padding(3, 3, 3, 10)
        Me.HyperlinkInventoryItem.Name = "HyperlinkInventoryItem"
        Me.HyperlinkInventoryItem.Size = New System.Drawing.Size(664, 13)
        Me.HyperlinkInventoryItem.TabIndex = 2
        Me.HyperlinkInventoryItem.Text = "Select..."
        '
        'LabelQuantity
        '
        Me.LabelQuantity.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelQuantity.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelQuantity.Location = New System.Drawing.Point(12, 83)
        Me.LabelQuantity.Margin = New System.Windows.Forms.Padding(3, 3, 3, 10)
        Me.LabelQuantity.Name = "LabelQuantity"
        Me.LabelQuantity.Size = New System.Drawing.Size(53, 13)
        Me.LabelQuantity.TabIndex = 3
        Me.LabelQuantity.Text = "Quantity:"
        '
        'HyperlinkQty
        '
        Me.HyperlinkQty.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkQty.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkQty.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkQty.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkQty.Location = New System.Drawing.Point(155, 83)
        Me.HyperlinkQty.Margin = New System.Windows.Forms.Padding(3, 3, 3, 10)
        Me.HyperlinkQty.Name = "HyperlinkQty"
        Me.HyperlinkQty.Size = New System.Drawing.Size(47, 13)
        Me.HyperlinkQty.TabIndex = 4
        Me.HyperlinkQty.Text = "Select..."
        '
        'LabelSellPrice
        '
        Me.LabelSellPrice.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSellPrice.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelSellPrice.Location = New System.Drawing.Point(12, 135)
        Me.LabelSellPrice.Margin = New System.Windows.Forms.Padding(3, 3, 3, 10)
        Me.LabelSellPrice.Name = "LabelSellPrice"
        Me.LabelSellPrice.Size = New System.Drawing.Size(58, 13)
        Me.LabelSellPrice.TabIndex = 7
        Me.LabelSellPrice.Text = "Sell Price:"
        '
        'TextEditSellPrice
        '
        Me.TextEditSellPrice.EditValue = 0
        Me.TextEditSellPrice.Location = New System.Drawing.Point(155, 132)
        Me.TextEditSellPrice.Margin = New System.Windows.Forms.Padding(3, 3, 3, 12)
        Me.TextEditSellPrice.Name = "TextEditSellPrice"
        Me.TextEditSellPrice.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSellPrice.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSellPrice.Properties.Appearance.Options.UseFont = True
        Me.TextEditSellPrice.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSellPrice.Properties.Mask.EditMask = "c"
        Me.TextEditSellPrice.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditSellPrice.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TextEditSellPrice.Size = New System.Drawing.Size(112, 20)
        Me.TextEditSellPrice.TabIndex = 8
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        '
        'ButtonOK
        '
        Me.ButtonOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonOK.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonOK.Appearance.Options.UseFont = True
        Me.ButtonOK.ImageIndex = 2
        Me.ButtonOK.ImageList = Me.ImageList24x24
        Me.ButtonOK.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonOK.Location = New System.Drawing.Point(605, 509)
        Me.ButtonOK.LookAndFeel.SkinName = "Black"
        Me.ButtonOK.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonOK.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonOK.Name = "ButtonOK"
        Me.ButtonOK.Size = New System.Drawing.Size(100, 28)
        Me.ButtonOK.TabIndex = 10
        Me.ButtonOK.Text = "OK"
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(711, 509)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(100, 28)
        Me.ButtonCancel.TabIndex = 11
        Me.ButtonCancel.Text = "Cancel"
        '
        'GroupNotes
        '
        Me.GroupNotes.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupNotes.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupNotes.Appearance.Options.UseFont = True
        Me.GroupNotes.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupNotes.AppearanceCaption.Options.UseFont = True
        Me.GroupNotes.Controls.Add(Me.MemoEditNotes)
        Me.GroupNotes.Location = New System.Drawing.Point(12, 167)
        Me.GroupNotes.LookAndFeel.SkinName = "Black"
        Me.GroupNotes.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupNotes.Name = "GroupNotes"
        Me.GroupNotes.Size = New System.Drawing.Size(799, 152)
        Me.GroupNotes.TabIndex = 9
        Me.GroupNotes.Tag = ""
        Me.GroupNotes.Text = "Notes"
        '
        'MemoEditNotes
        '
        Me.MemoEditNotes.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MemoEditNotes.EditValue = ""
        Me.MemoEditNotes.Location = New System.Drawing.Point(2, 21)
        Me.MemoEditNotes.Margin = New System.Windows.Forms.Padding(3, 3, 3, 0)
        Me.MemoEditNotes.Name = "MemoEditNotes"
        Me.MemoEditNotes.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.MemoEditNotes.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.MemoEditNotes.Properties.Appearance.Options.UseFont = True
        Me.MemoEditNotes.Properties.Appearance.Options.UseForeColor = True
        Me.MemoEditNotes.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.MemoEditNotes.Properties.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.MemoEditNotes.Size = New System.Drawing.Size(795, 129)
        Me.MemoEditNotes.TabIndex = 0
        '
        'HyperlinkBrand
        '
        Me.HyperlinkBrand.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkBrand.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkBrand.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkBrand.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkBrand.Location = New System.Drawing.Point(155, 109)
        Me.HyperlinkBrand.Margin = New System.Windows.Forms.Padding(3, 3, 3, 10)
        Me.HyperlinkBrand.Name = "HyperlinkBrand"
        Me.HyperlinkBrand.Size = New System.Drawing.Size(47, 13)
        Me.HyperlinkBrand.TabIndex = 6
        Me.HyperlinkBrand.Text = "Select..."
        '
        'LabelBrand
        '
        Me.LabelBrand.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelBrand.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelBrand.Location = New System.Drawing.Point(12, 109)
        Me.LabelBrand.Margin = New System.Windows.Forms.Padding(3, 3, 3, 10)
        Me.LabelBrand.Name = "LabelBrand"
        Me.LabelBrand.Size = New System.Drawing.Size(61, 13)
        Me.LabelBrand.TabIndex = 5
        Me.LabelBrand.Text = "For Brand:"
        '
        'LabelSellPriceWarning
        '
        Me.LabelSellPriceWarning.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSellPriceWarning.Appearance.ForeColor = System.Drawing.Color.Red
        Me.LabelSellPriceWarning.Location = New System.Drawing.Point(273, 135)
        Me.LabelSellPriceWarning.Margin = New System.Windows.Forms.Padding(3, 3, 3, 10)
        Me.LabelSellPriceWarning.Name = "LabelSellPriceWarning"
        Me.LabelSellPriceWarning.Size = New System.Drawing.Size(280, 13)
        Me.LabelSellPriceWarning.TabIndex = 12
        Me.LabelSellPriceWarning.Text = "WARNING: sell price is less than cost price!"
        Me.LabelSellPriceWarning.Visible = False
        '
        'SubformProduction
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.Controls.Add(Me.LabelSellPriceWarning)
        Me.Controls.Add(Me.GroupNotes)
        Me.Controls.Add(Me.ButtonOK)
        Me.Controls.Add(Me.ButtonCancel)
        Me.Controls.Add(Me.LabelInventoryItem)
        Me.Controls.Add(Me.HyperlinkInventoryItem)
        Me.Controls.Add(Me.LabelBrand)
        Me.Controls.Add(Me.HyperlinkBrand)
        Me.Controls.Add(Me.LabelQuantity)
        Me.Controls.Add(Me.HyperlinkQty)
        Me.Controls.Add(Me.LabelSellPrice)
        Me.Controls.Add(Me.TextEditSellPrice)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Name = "SubformProduction"
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        Me.Controls.SetChildIndex(Me.TextEditSellPrice, 0)
        Me.Controls.SetChildIndex(Me.LabelSellPrice, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkQty, 0)
        Me.Controls.SetChildIndex(Me.LabelQuantity, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkBrand, 0)
        Me.Controls.SetChildIndex(Me.LabelBrand, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkInventoryItem, 0)
        Me.Controls.SetChildIndex(Me.LabelInventoryItem, 0)
        Me.Controls.SetChildIndex(Me.ButtonCancel, 0)
        Me.Controls.SetChildIndex(Me.ButtonOK, 0)
        Me.Controls.SetChildIndex(Me.GroupNotes, 0)
        Me.Controls.SetChildIndex(Me.LabelSellPriceWarning, 0)
        CType(Me.TextEditSellPrice.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupNotes, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupNotes.ResumeLayout(False)
        CType(Me.MemoEditNotes.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents LabelInventoryItem As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkInventoryItem As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelQuantity As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkQty As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelSellPrice As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditSellPrice As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents ButtonOK As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GroupNotes As DevExpress.XtraEditors.GroupControl
    Friend WithEvents MemoEditNotes As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents HyperlinkBrand As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelBrand As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelSellPriceWarning As DevExpress.XtraEditors.LabelControl

End Class
