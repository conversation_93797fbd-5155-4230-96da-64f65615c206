﻿using System;
using System.Drawing;
using System.Windows.Forms;

namespace Framework.Controls
{
    public class FlatButton : Button
    {
        private Color DEFAULTBACKCOLOR = Color.FromArgb(60, 60, 60);
        public bool ThemeColorEnabled = true;


        #region Startup

        public FlatButton()
        {
            Cursor = Cursors.Hand;
            FlatStyle = FlatStyle.Flat;
            FlatAppearance.BorderSize = 0;
            Size = new Size(FrameworkSettings.Integers.BUTTONWIDTH, 60);
            BackColor = DEFAULTBACKCOLOR;
            ForeColor = Color.White;
            Margin = new Padding(0);
            ApplyTheme();
            SubscribeToEvents();
        }

        private void SubscribeToEvents()
        {
            BackColorChanged += FrameworkMethods.EventHandlers.Control_BackColorChanged;
        }

        #endregion


        #region Appearance

        public void ApplyTheme()
        {
            if (ThemeColorEnabled)
            {
                FlatAppearance.MouseOverBackColor = FrameworkSettings.Colors.ThemeColor;
                FlatAppearance.MouseDownBackColor = ToolBox.GetLighterShadeOfColor(FlatAppearance.MouseOverBackColor, 30);
            }
        }

        protected override void OnMouseEnter(EventArgs e)
        {
            base.OnMouseEnter(e);
            ForeColor = FrameworkMethods.Methods.GetBestForeColor(FlatAppearance.MouseOverBackColor);
        }

        protected override void OnMouseLeave(EventArgs e)
        {
            base.OnMouseLeave(e);
            ForeColor = FrameworkMethods.Methods.GetBestForeColor(BackColor);
        }

        protected override void OnEnabledChanged(EventArgs e)
        {
            base.OnEnabledChanged(e);
            if (Enabled == false)
            {
                BackColor = FrameworkSettings.Colors.BUTTONDISABLEDBACKCOLOR;
            }
            else
            {
                BackColor = DEFAULTBACKCOLOR;
            }
        }

        #endregion


        #region Error message

        private string OriginalText = string.Empty;
        private Color OriginalForeColor;
        private Color OriginalBackColor;
        private Cursor OriginalCursor;
        private Color OriginalMouseOverBackColor;
        private Color OriginalMouseDownBackColor;
        private Font OriginalFont;

        private string _ErrorMessage = string.Empty;
        public string ErrorMessage
        {
            get
            {
                return _ErrorMessage;
            }
            set
            {
                if (_ErrorMessage != value)
                {
                    _ErrorMessage = value;

                    if (string.IsNullOrEmpty(ErrorMessage) == false)
                    {
                        DisplayErrorMessage();
                    }
                    else
                    {
                        HideErrorMessage();
                    }
                }
            }
        }

        private void DisplayErrorMessage()
        {
            // Remember the normal settings so that we can change them back when the error message is cleared.
            OriginalText = Text;
            OriginalForeColor = ForeColor;
            OriginalBackColor = BackColor;
            OriginalCursor = Cursor;
            OriginalMouseOverBackColor = FlatAppearance.MouseOverBackColor;
            OriginalMouseDownBackColor = FlatAppearance.MouseDownBackColor;
            OriginalFont = Font;

            // Change the button appearance to display the error message.
            Text = ErrorMessage.ToUpper();
            ForeColor = Color.Firebrick;
            BackColor = (Parent == null ? BackColor : Parent.BackColor);
            Cursor = Cursors.Default;
            FlatAppearance.MouseOverBackColor = BackColor;
            FlatAppearance.MouseDownBackColor = BackColor;
            float fontsize = 6F;
            Font = new Font(Font.FontFamily, fontsize, FontStyle.Bold);
        }

        private void HideErrorMessage()
        {
            Text = OriginalText;
            ForeColor = OriginalForeColor;
            BackColor = OriginalBackColor;
            Cursor = OriginalCursor;
            FlatAppearance.MouseOverBackColor = OriginalMouseOverBackColor;
            FlatAppearance.MouseDownBackColor = OriginalMouseDownBackColor;
            Font = OriginalFont;
        }

        #endregion


        #region Method overrides

        protected override void OnClick(EventArgs e)
        {
            if (string.IsNullOrEmpty(ErrorMessage))
                base.OnClick(e);
        }

        #endregion


        #region Processing animation

        private ProcessingAnimation _Animation;
        private ProcessingAnimation Animation
        {
            get
            {
                if (_Animation == null)
                {
                    _Animation = new ProcessingAnimation();
                    _Animation.Size = new Size(_Animation.Width, 60);
                }
                return _Animation;
            }
        }

        public void StartAnimation()
        {
            if (Parent != null)
            {
                Animation.Parent = Parent;
                int xlocation = Location.X + Width / 2 - Animation.Width / 2;
                Animation.Location = new Point(xlocation, Location.Y);
                Visible = false;
                Animation.Visible = true;
            }
        }

        public void StopAnimation()
        {
            Animation.Visible = false;
            Visible = true;
        }

        #endregion

    }
}
