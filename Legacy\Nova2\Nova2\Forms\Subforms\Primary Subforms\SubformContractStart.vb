﻿Public Class SubformContractStart

    Private WaitMessage As FormPleaseWait
    Private WithEvents ContractSearcher As New System.ComponentModel.BackgroundWorker

#Region "Event Handlers"

    Private Sub ButtonNew_Click(sender As System.Object, e As System.EventArgs) Handles ButtonNew.Click

        ' Prompt the user for a new contract object.
        Dim NewContract As Contract = FormNewContract.GetNewContract

        ' Exit if the user cancelled.
        If IsNothing(NewContract) Then
            Exit Sub
        Else
            ' Save the new contract to the database.
            NewContract.Save(My.Settings.DBConnection, CType(TopLevelControl, BaseForm))
        End If

        ' Display the contract with task options.
        AddChild(New SubformContractTasks(NewContract))

    End Sub

    Private Sub HyperlinkClient_Click(sender As System.Object, e As System.EventArgs) Handles HyperlinkClient.Click

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupClient.SelectRowsByContractHistoryByPermission_ViewMyContracts _
        (My.Settings.DBConnection, False, Nothing)

        ' Update the data object and the label with the selection.
        If IsNothing(SelectedItems) = False Then
            If SelectedItems.Count > 0 Then
                TextEditClient.Text = SelectedItems(0).Item("ClientName")
            End If
        End If

    End Sub

    Private Sub HyperlinkBrand_Click(sender As System.Object, e As System.EventArgs) Handles HyperlinkBrand.Click

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupBrand.SelectRows _
        (My.Settings.DBConnection, False, Nothing)

        ' Update the data object and the label with the selection.
        If IsNothing(SelectedItems) = False Then
            If SelectedItems.Count > 0 Then
                TextEditBrand.Text = SelectedItems(0).Item("BrandName")
            End If
        End If

    End Sub

    Private Sub HyperlinkMediaService_Click(sender As System.Object, e As System.EventArgs) Handles HyperlinkMediaService.Click

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupMedia.SelectRows _
        (My.Settings.DBConnection, False, Nothing)

        ' Update the data object and the label with the selection.
        If IsNothing(SelectedItems) = False Then
            If SelectedItems.Count > 0 Then
                TextEditMediaService.Text = SelectedItems(0).Item("MediaName")
            End If
        End If

    End Sub

    Private Sub ButtonSearch_Click(sender As System.Object, e As System.EventArgs) Handles ButtonSearch.Click
        Search()
    End Sub

    Private Sub Search()

        ' Get the parent form of this subform.
        Dim ContainingForm As BaseForm = CType(TopLevelControl, BaseForm)

        ' Get the parameters needed to load data.
        Dim Cancelled As Nullable(Of Boolean)
        If Not CheckEditSearchCancelled.Checked Then
            Cancelled = False
        End If
        Dim SearchHistory As Nullable(Of Boolean)
        If Not CheckEditSearchHistory.Checked Then
            SearchHistory = False
        End If

        ' Get the parameters needed to run the query that'll get the data.
        Dim ContractNumber As String = TextEditContract.EditValue
        Dim Project As String = TextEditProject.EditValue
        Dim ClientName As String = TextEditClient.Text
        Dim BrandName As String = TextEditBrand.Text
        Dim MediaName As String = TextEditMediaService.Text

        ' Pack the parameters into an array for the background worker.
        Dim Argument(6) As Object
        Argument(0) = Cancelled
        Argument(1) = SearchHistory
        Argument(2) = ContractNumber
        Argument(3) = Project
        Argument(4) = ClientName
        Argument(5) = BrandName
        Argument(6) = MediaName

        ' Start the background worker.
        ContractSearcher.RunWorkerAsync(Argument)

        ' Display the Please Wait message.
        WaitMessage = New FormPleaseWait("Loading contract data...")
        WaitMessage.ShowDialog()

    End Sub

    Private Sub ContractDataLoader_DoWork(ByVal sender As Object, ByVal e As System.ComponentModel.DoWorkEventArgs) Handles ContractSearcher.DoWork

        ' Unpack the arguments needed to load the data.
        Dim Argument() As Object = e.Argument
        Dim Cancelled As Boolean? = Argument(0)
        Dim History As Boolean? = Argument(1)
        Dim ContractNumber As String = Argument(2)
        Dim Project As String = Argument(3)
        Dim ClientName As String = Argument(4)
        Dim BrandName As String = Argument(5)
        Dim MediaServiceName As String = Argument(6)

        ' Create a table to hold the search results.
        Dim ContractDataSet As New DataSetContract
        Dim ContractTable As DataSetContract.ContractDataTable = ContractDataSet.Contract

        ' Collect the data.
        OldContract.SearchContracts(My.Settings.DBConnection, String.Empty, Nothing, ContractTable, _
        Cancelled, ContractNumber, Project, ClientName, BrandName, MediaServiceName, History)

        ' Set the table as the result of this DoWork method.
        e.Result = ContractTable

    End Sub

    Private Sub ContractDataLoader_RunWorkerCompleted(ByVal sender As Object, ByVal e As System.ComponentModel.RunWorkerCompletedEventArgs) Handles ContractSearcher.RunWorkerCompleted

        ' Create a table to hold the search results.
        Dim ContractTable As DataSetContract.ContractDataTable = CType(e.Result, DataSetContract.ContractDataTable)

        ' Kill the wait message.
        WaitMessage.Dispose()

        ' Display a message if no contracts were found.
        If ContractTable.Rows.Count = 0 Then
            CType(TopLevelControl, BaseForm).ShowMessage("No contracts found.", "Search Results", MessageBoxIcon.Exclamation)
            Exit Sub
        End If

        ' If more than one contract was found, obtain a selection from the user.
        If ContractTable.Rows.Count > 1 Then
            ' Show all results to the user so that they can select one.
            Dim SelectedContract As DataSetContract.ContractRow = FormContractSearchResults.SelectedContract(ContractTable)
            If IsNothing(SelectedContract) Then
                ' The user clicked Cancel.
                Exit Sub
            Else
                ' The user selected a contract. Delete all other contracts from the table (but not from the database!!!).
                For Each Row As DataSetContract.ContractRow In ContractTable.Rows
                    If Not Row.ContractID = SelectedContract.ContractID Then
                        ' This is not the selected row. Delete it.
                        Row.Delete()
                    End If
                Next
                ' The only row left in the table at this point is the one selected by the user.
                ContractTable.AcceptChanges()
            End If
        End If

        ' Display the contract tasks subform for the selected contract.
        AddChild(New SubformContractTasks(ContractTable.Rows(0)))

    End Sub

    Private Sub TextEdit_KeyUp(sender As System.Object, e As System.Windows.Forms.KeyEventArgs) _
        Handles _
        TextEditContract.KeyUp, _
        TextEditBrand.KeyUp, _
        TextEditClient.KeyUp, _
        TextEditMediaService.KeyUp, _
        TextEditProject.KeyUp

        If e.KeyValue = Keys.Return Then
            ' The return key was pressed. Start the search.
            ButtonSearch_Click(sender, e)
        End If

    End Sub

#End Region

    Public Sub OpenContract(ContractNumber As String)
        ' Open the given contract as if the user searched for it.

        ' Set all search fields.
        TextEditContract.Text = ContractNumber
        TextEditProject.Text = String.Empty
        TextEditClient.Text = String.Empty
        TextEditBrand.Text = String.Empty
        TextEditMediaService.Text = String.Empty
        CheckEditSearchCancelled.Checked = False
        CheckEditSearchHistory.Checked = True

        ' Click the search button.
        Search()

    End Sub

End Class
