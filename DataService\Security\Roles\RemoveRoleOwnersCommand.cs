﻿using DataAccess;
using System;
using System.Collections.Generic;
using System.Data;

namespace DataService.Security
{
    class RemoveRoleOwnersCommand : Command
    {
        public Guid SessionId { get; set; }
        public Guid RoleId { get; set; }
        public DataTable Owners { get; set; }

        public RemoveRoleOwnersCommand(Guid sessionid, Guid roleid, List<DataRow> ownerslist)
        {
            SessionId = sessionid;
            RoleId = roleid;

            // Create a new table.
            Owners = new DataTable();
            Owners.Columns.Add("id", typeof(Guid));

            // Convert the rows in the specified list to rows that match the schema of the table and then add them to the table.
            if (ownerslist != null && ownerslist.Count > 0)
            {
                for (int i = 0; i < ownerslist.Count; i++)
                {
                    DataRow newrow = Owners.NewRow();
                    newrow["id"] = ownerslist[i]["userid"];
                    Owners.Rows.Add(newrow);
                }
            }
        }
    }
}
