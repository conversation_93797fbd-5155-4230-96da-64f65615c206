<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DevExpress.Xpf.Docking.v12.2</name>
    </assembly>
    <members>
        <member name="T:DevExpress.Xpf.Docking.ClosingBehavior">

            <summary>
                <para>Contains values that specify how panels are closed.

</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Docking.ClosingBehavior.Default">
            <summary>
                <para>When applied to a <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> via the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ClosingBehavior"/> property, acts as the <see cref="F:DevExpress.Xpf.Docking.ClosingBehavior.HideToClosedPanelsCollection"/> option.

<para>
When applied to a <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> via the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ClosingBehavior"/> property, gets a ClosingBehavior enumeration value of its parent <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> .
</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.ClosingBehavior.HideToClosedPanelsCollection">
            <summary>
                <para>When a panel is closed, it's hidden and moved to the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ClosedPanels"/> collection. The panel can be accessed via the Closed Panels bar (see <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ClosedPanelsBarVisibility"/>).
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.ClosingBehavior.ImmediatelyRemove">
            <summary>
                <para>When a panel is closed, it's hidden. No reference to the closed panel is kept.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.AutoHideExpandMode">

            <summary>
                <para>Contains values that specify how auto-hidden panels are expanded.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Docking.Base.AutoHideExpandMode.Default">
            <summary>
                <para>The same as the <see cref="F:DevExpress.Xpf.Docking.Base.AutoHideExpandMode.MouseHover"/> option.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.Base.AutoHideExpandMode.MouseDown">
            <summary>
                <para>An auto-hidden panel is expanded when clicked.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.Base.AutoHideExpandMode.MouseHover">
            <summary>
                <para>An auto-hidden panel is expanded when hovered over by the mouse.

</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Docking.AutoHideType">

            <summary>
                <para>Contains values that identify possible auto-hide positions for dock panels.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Docking.AutoHideType.Bottom">
            <summary>
                <para>A dock panel is auto-hidden at the bottom edge of the container.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.AutoHideType.Default">
            <summary>
                <para>Identifies the default location where a dock panel is auto-hidden. The default auto-hide location is calculated automatically, based on the panel's location and state.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.AutoHideType.Left">
            <summary>
                <para>A dock panel is auto-hidden at the left edge of the container.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.AutoHideType.Right">
            <summary>
                <para>A dock panel is auto-hidden at the right edge of the container.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.AutoHideType.Top">
            <summary>
                <para>A dock panel is auto-hidden at the top edge of the container.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Docking.FixedItem">

            <summary>
                <para>An ancestor for fixed items available via the Customization Form (the Empty Space Item, Label, Splitter and Separator).

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.FixedItem.#ctor">
            <summary>
                <para>Initializes a new instance of the FixedItem class.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Docking.FixedItemStyle">

            <summary>
                <para>Identifies the type a <see cref="T:DevExpress.Xpf.Docking.FixedItem"/> object.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Docking.FixedItemStyle.EmptySpace">
            <summary>
                <para>Represents an empty space item.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.FixedItemStyle.Label">
            <summary>
                <para>Represents a label.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.FixedItemStyle.Separator">
            <summary>
                <para>Represents a separator.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Docking.MDIStyle">

            <summary>
                <para>Contains values that specify how a <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/> represents its children.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Docking.MDIStyle.Default">
            <summary>
                <para>The same option as <see cref="F:DevExpress.Xpf.Docking.MDIStyle.Tabbed"/>.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.MDIStyle.MDI">
            <summary>
                <para>A DocumentGroup's children are represented as floating windows, that can float within the DocumentGroup's boundaries.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.MDIStyle.Tabbed">
            <summary>
                <para>A DocumentGroup is rendered as a tab container, where children are represented as tabs.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Docking.MDIState">

            <summary>
                <para>Enumerates available states for panels in a <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/> in MDI mode.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Docking.MDIState.Maximized">
            <summary>
                <para>A child MDI window is maximized.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.MDIState.Minimized">
            <summary>
                <para>A child MDI window is minimized.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.MDIState.Normal">
            <summary>
                <para>A child MDI window is in its normal state.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Docking.ContentItem">

            <summary>
                <para>An abstract class for objects capable of displaying content.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.Xpf.Docking.ContentItem.Content">
            <summary>
                <para>Gets or sets the item's content. This is a dependency property.
</para>
            </summary>
            <value>An object that specifies the item's content.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.ContentItem.ContentProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.ContentItem.ContentTemplate">
            <summary>
                <para>Gets or sets the template used to display the item's <see cref="P:DevExpress.Xpf.Docking.ContentItem.Content"/>. This is a dependency property.
</para>
            </summary>
            <value>The corresponding data template. The default is a null reference (Nothing in Visual Basic). 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.ContentItem.ContentTemplateProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.ContentItem.ContentTemplateSelector">
            <summary>
                <para>Gets or sets an object that chooses the item's <see cref="P:DevExpress.Xpf.Docking.ContentItem.ContentTemplate"/> based on custom logic.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that chooses a template based on custom logic. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.ContentItem.ContentTemplateSelectorProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.ContentItem.FocusContentOnActivating">
            <summary>
                <para> [To be supplied] </para>
            </summary>
            <value> [To be supplied] </value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.ContentItem.FocusContentOnActivatingProperty">
            <summary>
                <para> [To be supplied] </para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.ContentItem.IsDataBound">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.ContentItem.IsDataBoundProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Docking.ClosePageButtonShowMode">

            <summary>
                <para>Enumerates values that specify whether Close buttons are displayed in individual tab pages, the tab control's header, or in both. 
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Docking.ClosePageButtonShowMode.Default">
            <summary>
                <para>The same as the <see cref="F:DevExpress.Xpf.Docking.ClosePageButtonShowMode.InTabControlHeader"/> option.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.ClosePageButtonShowMode.InActiveTabPageAndTabControlHeader">
            <summary>
                <para>Close buttons are displayed in the tab container's header, and within the active page.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.ClosePageButtonShowMode.InActiveTabPageHeader">
            <summary>
                <para>A Close button is displayed in the active page. The Close button in the container's header is hidden. 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.ClosePageButtonShowMode.InAllTabPageHeaders">
            <summary>
                <para>Close buttons are displayed in all pages. The Close button in the container's header is hidden. 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.ClosePageButtonShowMode.InAllTabPagesAndTabControlHeader">
            <summary>
                <para>Close buttons are displayed in all pages and in the container's header.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.ClosePageButtonShowMode.InTabControlHeader">
            <summary>
                <para>The Close button is displayed in the tab container's header
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.ClosePageButtonShowMode.NoWhere">
            <summary>
                <para>Close buttons are not displayed.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.ShowingMenuEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.ShowingMenu"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.ShowingMenuEventHandler.Invoke(System.Object,DevExpress.Xpf.Docking.Base.ShowingMenuEventArgs)">
            <summary>
                <para>Represents the method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.ShowingMenu"/> event.
</para>
            </summary>
            <param name="sender">
		A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object that fires this event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Xpf.Docking.Base.ShowingMenuEventArgs"/> object that represents data for the event.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.DockOperationStartingEventHandler">

            <summary>
                <para>The method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationStarting"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.DockOperationStartingEventHandler.Invoke(System.Object,DevExpress.Xpf.Docking.Base.DockOperationStartingEventArgs)">
            <summary>
                <para>The method that handles the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationStarting"/> event.
</para>
            </summary>
            <param name="sender">
		An Object that raised the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationStarting"/> event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Xpf.Docking.Base.DockOperationStartingEventArgs"/> object that provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationStarting"/> event.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.DockOperationStartingEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationStarting"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.DockOperationStartingEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Docking.DockOperation)">
            <summary>
                <para>Initializes a new instance of the DockOperationStartingEventArgs class with the specified settings.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> that raised the event.

            </param>
            <param name="dockOperation">
		A <see cref="T:DevExpress.Xpf.Docking.DockOperation"/> enumerator value that specifies the current operation type.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.DockOperationStartingEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Docking.DockOperation)">
            <summary>
                <para>Initializes a new instance of the DockOperationStartingEventArgs class with the specified settings.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> that raised the event.

            </param>
            <param name="dockTarget">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> to which an <i>item</i> is to be docked.

            </param>
            <param name="dockOperation">
		A <see cref="T:DevExpress.Xpf.Docking.DockOperation"/> enumerator value that specifies the current operation type.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Docking.Base.DockOperationStartingEventArgs.DockOperation">
            <summary>
                <para>Gets the type of an operation that is processed within the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationStarting"/> event.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.DockOperation"/> enumerator value that specifies the docking operation type.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.Base.DockOperationStartingEventArgs.DockTarget">
            <summary>
                <para>Gets a Dock Item to which the current Dock Item is to be docked.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> to which the current Dock Item is to be docked.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.DockOperationCompletedEventHandler">

            <summary>
                <para>The method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationCompleted"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.DockOperationCompletedEventHandler.Invoke(System.Object,DevExpress.Xpf.Docking.Base.DockOperationCompletedEventArgs)">
            <summary>
                <para>The method that handles the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationCompleted"/> event.
</para>
            </summary>
            <param name="sender">
		An Object that raised the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationCompleted"/> event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Xpf.Docking.Base.DockOperationCompletedEventArgs"/> object that provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationCompleted"/> event.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.DockOperationCompletedEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationCompleted"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.DockOperationCompletedEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Docking.DockOperation)">
            <summary>
                <para>Initializes a new instance of the DockOperationCompletedEventArgs class with the specified settings.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> to which a dock operation was applied.

            </param>
            <param name="dockOperation">
		A <see cref="T:DevExpress.Xpf.Docking.DockOperation"/> enumerator value that specifies the current operation type.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Docking.Base.DockOperationCompletedEventArgs.DockOperation">
            <summary>
                <para>Gets the type of an operation that is processed within the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationCompleted"/> event.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.DockOperation"/> enumerator value that specifies the type of an operation that is processed within the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationCompleted"/> event.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.IsCustomizationChangedEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.IsCustomizationChanged"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.IsCustomizationChangedEventHandler.Invoke(System.Object,DevExpress.Xpf.Docking.Base.IsCustomizationChangedEventArgs)">
            <summary>
                <para>Represents the method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.IsCustomizationChanged"/> event.
</para>
            </summary>
            <param name="sender">
		A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object that fires this event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Xpf.Docking.Base.IsCustomizationChangedEventArgs"/> object that represents data for the event.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.IsCustomizationChangedEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.IsCustomizationChanged"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.IsCustomizationChangedEventArgs.#ctor(System.Boolean)">
            <summary>
                <para>Initializes a new instance of the IsCustomizationChangedEventArgs class with the specified settings.
</para>
            </summary>
            <param name="newValue">
		A new value of the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.IsCustomization"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Docking.Base.IsCustomizationChangedEventArgs.Value">
            <summary>
                <para>Gets the new value of the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.IsCustomization"/> property.
</para>
            </summary>
            <value>A Boolean value that specifies the new value of the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.IsCustomization"/> property.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.ShowingMenuEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.ShowingMenu"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.ShowingMenuEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutElementMenu)">
            <summary>
                <para>Initializes a new instance of the ShowingMenuEventArgs class with the specified settings.
</para>
            </summary>
            <param name="menu">
		A BaseLayoutElementMenu object that represents the menu to be displayed.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemSizeChangedEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSizeChanged"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.LayoutItemSizeChangedEventHandler.Invoke(System.Object,DevExpress.Xpf.Docking.Base.LayoutItemSizeChangedEventArgs)">
            <summary>
                <para>Represents the method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSizeChanged"/> event.
</para>
            </summary>
            <param name="sender">
		A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object that fires this event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Xpf.Docking.Base.LayoutItemSizeChangedEventArgs"/> object that represents data for the event.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemSizeChangedEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSizeChanged"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.LayoutItemSizeChangedEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem,System.Boolean,System.Windows.GridLength,System.Windows.GridLength)">
            <summary>
                <para>Initializes a new instance of the LayoutItemSizeChangedEventArgs class with the specified settings.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the current item.

            </param>
            <param name="isWidth">
		A Boolean value that specifies whether the item's width has been changed.

            </param>
            <param name="value">
		A new value of the item's width/height.

            </param>
            <param name="prevValue">
		The previous value of the item's width/height.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Docking.Base.LayoutItemSizeChangedEventArgs.HeightChanged">
            <summary>
                <para>Gets whether the item's height has been changed.
</para>
            </summary>
            <value><b>true</b> if the item's height has been changed; <b>false</b> if the item's width has been changed.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.Base.LayoutItemSizeChangedEventArgs.PrevValue">
            <summary>
                <para>Gets the previous value of the item's width/height.
</para>
            </summary>
            <value>A GridLength value that specifies the item's previous width/height.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.Base.LayoutItemSizeChangedEventArgs.Value">
            <summary>
                <para>Gets the current value of the item's width/height.
</para>
            </summary>
            <value>A GridLength value that specifies the item's new width/height.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.Base.LayoutItemSizeChangedEventArgs.WidthChanged">
            <summary>
                <para>Gets whether the item's width has been changed.
</para>
            </summary>
            <value><b>true</b> if the item's width has been changed; <b>false</b> if the item's height has been changed.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemSelectionChangingEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSelectionChanging"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.LayoutItemSelectionChangingEventHandler.Invoke(System.Object,DevExpress.Xpf.Docking.Base.LayoutItemSelectionChangingEventArgs)">
            <summary>
                <para>Represents the method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSelectionChanging"/> event.
</para>
            </summary>
            <param name="sender">
		A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object that fires this event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Xpf.Docking.Base.LayoutItemSelectionChangingEventArgs"/> object that represents data for the event.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemSelectionChangingEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSelectionChanging"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.LayoutItemSelectionChangingEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem,System.Boolean)">
            <summary>
                <para>Initializes a new instance of the LayoutItemSelectionChangingEventArgs class with the specified settings.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the current item.

            </param>
            <param name="selected">
		A Boolean value that specifies whether the item is selected.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Docking.Base.LayoutItemSelectionChangingEventArgs.Selected">
            <summary>
                <para>Gets whether the item is selected.
</para>
            </summary>
            <value><b>true</b> if the item is selected; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemSelectionChangedEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSelectionChanged"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.LayoutItemSelectionChangedEventHandler.Invoke(System.Object,DevExpress.Xpf.Docking.Base.LayoutItemSelectionChangedEventArgs)">
            <summary>
                <para>Represents the method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSelectionChanged"/> event.
</para>
            </summary>
            <param name="sender">
		A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object that fires this event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Xpf.Docking.Base.LayoutItemSelectionChangedEventArgs"/> object that represents data for the event.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemSelectionChangedEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSelectionChanged"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.LayoutItemSelectionChangedEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem,System.Boolean)">
            <summary>
                <para>Initializes a new instance of the LayoutItemSelectionChangedEventArgs class with the specified settings.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the current item.

            </param>
            <param name="selected">
		A Boolean value that specifies whether the item is selected.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Docking.Base.LayoutItemSelectionChangedEventArgs.Selected">
            <summary>
                <para>Gets whether the item is selected.
</para>
            </summary>
            <value><b>true</b> if the item is selected; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemRestoredEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemRestored"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.LayoutItemRestoredEventHandler.Invoke(System.Object,DevExpress.Xpf.Docking.Base.LayoutItemRestoredEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemRestored"/> event.

</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> which raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Xpf.Docking.Base.LayoutItemRestoredEventArgs"/> object which contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemRestoredEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemRestored"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.LayoutItemRestoredEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem)">
            <summary>
                <para>Initializes a new instance of the LayoutItemRestoredEventArgs class with the specified settings.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the current item.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemMovedEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemMoved"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.LayoutItemMovedEventHandler.Invoke(System.Object,DevExpress.Xpf.Docking.Base.LayoutItemMovedEventArgs)">
            <summary>
                <para>Represents the method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemMoved"/> event.
</para>
            </summary>
            <param name="sender">
		A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object that fires this event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Xpf.Docking.Base.LayoutItemMovedEventArgs"/> object that represents data for the event.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemMovedEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemMoved"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.LayoutItemMovedEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Layout.Core.MoveType)">
            <summary>
                <para>Initializes a new instance of the LayoutItemMovedEventArgs class with the specified settings.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the currently processed item.

            </param>
            <param name="target">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the item next to which the current item is inserted.

            </param>
            <param name="type">
		A MoveType value that specifies how the current item is moved.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Docking.Base.LayoutItemMovedEventArgs.Target">
            <summary>
                <para>Gets the item next to which the current item (<see cref="P:DevExpress.Xpf.Docking.Base.ItemEventArgs.Item"/>) is inserted.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that identifies the item next to which the current item (<see cref="P:DevExpress.Xpf.Docking.Base.ItemEventArgs.Item"/>) is inserted. 
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.Base.LayoutItemMovedEventArgs.Type">
            <summary>
                <para>Gets how the current item is inserted next to the target item.
</para>
            </summary>
            <value>A MoveType value that specifies how the current item is inserted next to the target item.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemHiddenEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemHidden"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.LayoutItemHiddenEventHandler.Invoke(System.Object,DevExpress.Xpf.Docking.Base.LayoutItemHiddenEventArgs)">
            <summary>
                <para>Represents the method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemHidden"/> event.
</para>
            </summary>
            <param name="sender">
		A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object that fires this event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Xpf.Docking.Base.LayoutItemHiddenEventArgs"/> object that represents data for the event.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemHiddenEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemHidden"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.LayoutItemHiddenEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem)">
            <summary>
                <para>Initializes a new instance of the LayoutItemHiddenEventArgs class with the specified settings.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the current item.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemActivatedEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemActivated"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.LayoutItemActivatedEventHandler.Invoke(System.Object,DevExpress.Xpf.Docking.Base.LayoutItemActivatedEventArgs)">
            <summary>
                <para>Represents the method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemActivated"/> event.
</para>
            </summary>
            <param name="sender">
		A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object that fires this event.

            </param>
            <param name="ea">
		A <see cref="T:DevExpress.Xpf.Docking.Base.LayoutItemActivatedEventArgs"/> object that represents data for the event.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.LayoutItemActivatedEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemActivated"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.LayoutItemActivatedEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Docking.BaseLayoutItem)">
            <summary>
                <para>Initializes a new instance of the LayoutItemActivatedEventArgs class with the specified settings.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the current item.

            </param>
            <param name="oldItem">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the previously activated item.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Docking.Base.LayoutItemActivatedEventArgs.Item">
            <summary>
                <para>Gets the activated layout item.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the activated layout item.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.Base.LayoutItemActivatedEventArgs.OldItem">
            <summary>
                <para>Gets the previously activated layout item.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the previously activated layout item.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.DockItemActivatedEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemActivated"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.DockItemActivatedEventHandler.Invoke(System.Object,DevExpress.Xpf.Docking.Base.DockItemActivatedEventArgs)">
            <summary>
                <para>Represents the method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemActivated"/> event.
</para>
            </summary>
            <param name="sender">
		A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object that fires this event.

            </param>
            <param name="ea">
		A <see cref="T:DevExpress.Xpf.Docking.Base.DockItemActivatedEventArgs"/> object that represents data for the event.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.DockItemActivatedEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemActivated"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.DockItemActivatedEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Docking.BaseLayoutItem)">
            <summary>
                <para>Initializes a new instance of the DockItemActivatedEventArgs class with the specified settings.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the current item.

            </param>
            <param name="oldItem">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the previously activated item.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Docking.Base.DockItemActivatedEventArgs.Item">
            <summary>
                <para>Gets the activated dock item.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the activated dock item.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.Base.DockItemActivatedEventArgs.OldItem">
            <summary>
                <para>Gets the previously activated dock item.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the previously activated dock item.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Docking.LayoutController">

            <summary>
                <para>Represents the object that provides methods to manage the layout of items.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutController.#ctor(DevExpress.Xpf.Docking.DockLayoutManager)">
            <summary>
                <para>Initializes a new instance of the LayoutController class.
</para>
            </summary>
            <param name="container">
		A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object, whose layout functionality will be controlled by the created LayoutController.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutController.Activate(DevExpress.Xpf.Docking.BaseLayoutItem)">
            <summary>
                <para>Activates the specified item. For a <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/> object, the associated control is focused.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents a layout item to be activated.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutController.Activate(DevExpress.Xpf.Docking.BaseLayoutItem,System.Boolean)">
            <summary>
                <para>Activates the specified item, and optionally focuses the item's associated control.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the layout item to be activated.

            </param>
            <param name="focus">
		<b>true</b> to move focus to the control associated with the item; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutController.ActiveItem">
            <summary>
                <para>Gets the active layout item.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> descendant that represents the active layout item.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutController.CancelRenaming">
            <summary>
                <para>Cancels the item renaming in progress.

</para>
            </summary>
            <returns><b>true</b> if item renaming has been canceled; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutController.ChangeGroupOrientation(DevExpress.Xpf.Docking.LayoutGroup,System.Windows.Controls.Orientation)">
            <summary>
                <para>Changes the <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/>'s <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.Orientation"/> to the specified value.
</para>
            </summary>
            <param name="group">
		A <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> whose orientation is to be changed.

            </param>
            <param name="orientation">
		A <see cref="T:System.Windows.Controls.Orientation"/> value that specifies the new orientation for the group.

            </param>
            <returns><b>true</b> if the group's orientation has been changed; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutController.Container">
            <summary>
                <para>Gets the DockLayoutManager container whose dock functionality is controlled by the current LayoutController.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutController.CreateCommand``1(DevExpress.Xpf.Docking.BaseLayoutItem[])">
            <summary>
                <para>Creates the specified layout command for the specified items. 

</para>
            </summary>
            <param name="items">
		An array of <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> objects with which the created command is associated. 

            </param>
            <returns>The created command.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutController.EndRenaming">
            <summary>
                <para>Finishes the item renaming in progress.

</para>
            </summary>
            <returns><b>true</b> if item renaming is finished; otherwise, <b>false</b>.

</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutController.FixedItems">
            <summary>
                <para>Gets a collection of the items that are always available in the Customization Window.
</para>
            </summary>
            <value>A collection of the items that are always available in the Customization Window.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutController.Group(DevExpress.Xpf.Docking.BaseLayoutItem[])">
            <summary>
                <para>Combines the specified adjacent items into a new group.
</para>
            </summary>
            <param name="items">
		An array of adjacent items to be combined into a group.

            </param>
            <returns><b>true</b> if the items have been combined into a group; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutController.HiddenItems">
            <summary>
                <para>Gets the collection of hidden items.
</para>
            </summary>
            <value>A HiddenItemsCollection collection storing hidden items.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutController.Hide(DevExpress.Xpf.Docking.BaseLayoutItem)">
            <summary>
                <para>Hides the specified layout item.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to be hidden.

            </param>
            <returns><b>true</b> if the item has been successfully hidden; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutController.HideSelectedItems">
            <summary>
                <para>Hides the selected items.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutController.IsCustomization">
            <summary>
                <para>Gets whether Customization Mode is enabled.
</para>
            </summary>
            <value><b>true</b> if Customization Mode is enabled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutController.Move(DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Layout.Core.MoveType)">
            <summary>
                <para>Moves the specified item to the position next to another item or into another group.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to be moved.

            </param>
            <param name="target">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to which (or next to which) the <i>item</i> is moved.

            </param>
            <param name="type">
		A <b>MoveType</b> value that specifies how the <i>item</i> is moved.

            </param>
            <returns>A Boolean value that specifies that the item has been successfully moved.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutController.Move(DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Layout.Core.MoveType,System.Int32)">
            <summary>
                <para>Moves the <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> next to another item or into another group at the precise position, specified by an <b>insertIndex</b> parameter.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> to move.

            </param>
            <param name="target">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> in relation to which the specified element is moved.

            </param>
            <param name="type">
		A DevExpress.Xpf.Layout.Core.MoveType enumeration value specifying how the <i>item</i> is moved.

            </param>
            <param name="insertIndex">
		An integer value that specifies the zero-based index at which the <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> should be inserted. If it is negative or exceeds the number of items within the target container, an exception is thrown.

            </param>
            <returns><b>true</b> if the item has been sucsessfully moved; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutController.Rename(DevExpress.Xpf.Docking.BaseLayoutItem)">
            <summary>
                <para>Starts layout item renaming.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> to be renamed.

            </param>
            <returns><b>true</b> if item renaming has been initiated; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutController.Restore(DevExpress.Xpf.Docking.BaseLayoutItem)">
            <summary>
                <para>Restores the specified hidden item, adding it to the layout.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents a hidden item to be restored.

            </param>
            <returns>A Boolean value that indicates that the item has been successfully restored.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutController.Selection">
            <summary>
                <para>Gets a list of items that are selected in Customization Mode.
</para>
            </summary>
            <value>A DevExpress.Xpf.Docking.Selection object that represents a list of selected <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> objects.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutController.SetGroupBorderStyle(DevExpress.Xpf.Docking.LayoutGroup,DevExpress.Xpf.Docking.GroupBorderStyle)">
            <summary>
                <para>Sets the specified style for a group.
</para>
            </summary>
            <param name="group">
		A <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> whose style is to be changed.

            </param>
            <param name="style">
		A <see cref="T:DevExpress.Xpf.Docking.GroupBorderStyle"/> value that specifies the new style.

            </param>
            <returns><b>true</b> if the group's style has been changed; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutController.Ungroup(DevExpress.Xpf.Docking.LayoutGroup)">
            <summary>
                <para>Ungroups the specified group, moving its children to the group's parent.
</para>
            </summary>
            <param name="group">
		A <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> to be ungrouped.

            </param>
            <returns><b>true</b> if the group has been ungouped; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="T:DevExpress.Xpf.Docking.ImageLocation">

            <summary>
                <para>Contains values that specify how an image is displayed relative to an adjacent text region.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Docking.ImageLocation.AfterText">
            <summary>
                <para>An image is displayed after text.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.ImageLocation.BeforeText">
            <summary>
                <para>An image is displayed before text.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.ImageLocation.Default">
            <summary>
                <para>The same as the <see cref="F:DevExpress.Xpf.Docking.ImageLocation.BeforeText"/> option.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Docking.GroupBorderStyle">

            <summary>
                <para>Contains values that specify how a group's borders are rendered.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Docking.GroupBorderStyle.Group">
            <summary>
                <para>A container is displayed with borders and caption.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.GroupBorderStyle.GroupBox">
            <summary>
                <para>A container is displayed with borders and title bar.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.GroupBorderStyle.NoBorder">
            <summary>
                <para>A container has no borders.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.GroupBorderStyle.Tabbed">
            <summary>
                <para>Child items are represented as tabs.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Docking.CaptionLocation">

            <summary>
                <para>Contains values that specify the position of an item's caption.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Docking.CaptionLocation.Bottom">
            <summary>
                <para>An item's caption is displayed at the bottom.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.CaptionLocation.Default">
            <summary>
                <para>An item's caption is displayed at the default position, which is different for different item types.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.CaptionLocation.Left">
            <summary>
                <para>An item's caption is displayed on the left.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.CaptionLocation.Right">
            <summary>
                <para>An item's caption is displayed on the right.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.CaptionLocation.Top">
            <summary>
                <para>An item's caption is displayed at the top.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Docking.CaptionAlignMode">

            <summary>
                <para>Enumerates the options that specify how the controls, displayed with the help of <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/>s, are aligned, and the corresponding labels are resized. 

</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Docking.CaptionAlignMode.AlignInGroup">
            <summary>
                <para>Controls displayed by means of <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/> objects are auto-aligned across a layout group and its nested groups that have the <b>CaptionAlignMode</b> property set to <b>Default</b>.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.CaptionAlignMode.AutoSize">
            <summary>
                <para>The auto-size feature is enabled. The captions of <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/>s are automatically resized to the minimum width that allows the text to be displayed in its entirety. 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.CaptionAlignMode.Custom">
            <summary>
                <para>Enables custom size mode, in which the size of a <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/>'s caption must be specified manually via the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionWidth"/> property
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.CaptionAlignMode.Default">
            <summary>
                <para>For nested layout items, this setting means that the alignment is controlled by the parent's <b>CaptionAlignMode</b> property.
For a root group, this setting means that controls of child <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/>s are auto-aligned across the root group, and nested groups that have the <b>CaptionAlignMode</b> property set to <b>Default</b>.

</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.DockItemDraggingEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemDragging"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.DockItemDraggingEventHandler.Invoke(System.Object,DevExpress.Xpf.Docking.Base.DockItemDraggingEventArgs)">
            <summary>
                <para>Represents the method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemDragging"/> event.
</para>
            </summary>
            <param name="sender">
		A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object that fires this event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Xpf.Docking.Base.DockItemDraggingEventArgs"/> object that represents data for the event.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.DockItemDraggingEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemDragging"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.DockItemDraggingEventArgs.#ctor(System.Windows.Point,DevExpress.Xpf.Docking.BaseLayoutItem)">
            <summary>
                <para>Initializes a new instance of the DockItemDraggingEventArgs class with the specified settings.
</para>
            </summary>
            <param name="screenPoint">
		A Point at which the item is being dragged.

            </param>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object being processed.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Docking.Base.DockItemDraggingEventArgs.ScreenPoint">
            <summary>
                <para>Gets or sets the current point at which the item is being dragged.
</para>
            </summary>
            <value>A Point structure that specifies the point where the item is being dragged.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.DockItemDockingEventHandler">

            <summary>
                <para>Represents a method that will handle the  <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemDocking"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.DockItemDockingEventHandler.Invoke(System.Object,DevExpress.Xpf.Docking.Base.DockItemDockingEventArgs)">
            <summary>
                <para>Represents the method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemDocking"/> event.
</para>
            </summary>
            <param name="sender">
		A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object that fires this event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Xpf.Docking.Base.DockItemDockingEventArgs"/> object that represents data for the event.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.DockItemDockingEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemDocking"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.DockItemDockingEventArgs.#ctor(System.Boolean,DevExpress.Xpf.Docking.BaseLayoutItem,System.Windows.Point,DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Layout.Core.DockType,System.Boolean)">
            <summary>
                <para>Initializes a new instance of the DockItemDockingEventArgs class with the specified settings.
</para>
            </summary>
            <param name="cancel">
		A Boolean value that specifies whether the current event must be canceled.

            </param>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object being docked.

            </param>
            <param name="pt">
		A Point at which the item is being docked.

            </param>
            <param name="target">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to which the current item is being docked.

            </param>
            <param name="type">
		A <see cref="T:DevExpress.Xpf.Layout.Core.DockType"/> value that specifies how the item is docked to another item.

            </param>
            <param name="isHiding">
		A Boolean value that specifies whether the item is being set to the auto-hide state.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.DockItemDockingEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem,System.Windows.Point,DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Layout.Core.DockType,System.Boolean)">
            <summary>
                <para>Initializes a new instance of the DockItemDockingEventArgs class with the specified settings.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object being docked.

            </param>
            <param name="pt">
		A Point at which the item is being docked.

            </param>
            <param name="target">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to which the current item is being docked.

            </param>
            <param name="type">
		A <see cref="T:DevExpress.Xpf.Layout.Core.DockType"/> value that specifies how the item is docked to another item.

            </param>
            <param name="isHiding">
		A Boolean value that specifies whether the item is being set to the auto-hide state.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Docking.Base.DockItemDockingEventArgs.DockTarget">
            <summary>
                <para>Gets the item to which the current item is being docked.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to which the current item is being docked.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.Base.DockItemDockingEventArgs.DockType">
            <summary>
                <para>Gets or sets how an item is being docked to another item.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Layout.Core.DockType"/> value that specifies how an item is being docked to another item.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.Base.DockItemDockingEventArgs.DragPoint">
            <summary>
                <para>Gets the point at which the item is being docked. The point is relative to the top left corner of the target item's root parent.
</para>
            </summary>
            <value>A Point structure that specifies the point at which the item is being docked.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.Base.DockItemDockingEventArgs.IsHiding">
            <summary>
                <para>Gets whether the item is being docked over a zone used to set the item to the auto-hide state.
</para>
            </summary>
            <value><b>true</b> if the item is being docked over a zone used to set the item to the auto-hide state; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.DockItemExpandedEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemExpanded"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.DockItemExpandedEventHandler.Invoke(System.Object,DevExpress.Xpf.Docking.Base.DockItemExpandedEventArgs)">
            <summary>
                <para>Represents the method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemExpanded"/> event.
</para>
            </summary>
            <param name="sender">
		A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object that fires this event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Xpf.Docking.Base.DockItemExpandedEventArgs"/> object that represents data for the event.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.DockItemExpandedEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemExpanded"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.DockItemExpandedEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem)">
            <summary>
                <para>Initializes a new instance of the DockItemExpandedEventArgs class.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object being processed.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.DockItemCollapsedEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemCollapsed"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.DockItemCollapsedEventHandler.Invoke(System.Object,DevExpress.Xpf.Docking.Base.DockItemCollapsedEventArgs)">
            <summary>
                <para>Represents the method that will handle the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemCollapsed"/> event.
</para>
            </summary>
            <param name="sender">
		A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object that fires this event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Xpf.Docking.Base.DockItemCollapsedEventArgs"/> object that represents data for the event.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.DockItemCollapsedEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemCollapsed"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.DockItemCollapsedEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem)">
            <summary>
                <para>Initializes a new instance of the DockItemCollapsedEventArgs class with the specified item.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object being processed.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Docking.LayoutControlItem">

            <summary>
                <para>Represents an object supporting the layout functionality, capable of displaying a control with a label.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutControlItem.#ctor">
            <summary>
                <para>Initializes a new instance of the LayoutControlItem class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutControlItem.ActualCaptionMargin">
            <summary>
                <para>Gets the calculated outer indents for the item's caption.
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Thickness"/> value that contains the outer indents of the item's caption. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutControlItem.ActualCaptionMarginProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutControlItem.CaptionToControlDistance">
            <summary>
                <para>Gets or sets the distance between the item's caption and control.
This is a dependency property.
</para>
            </summary>
            <value>A Double value that specifies the distance between the item's caption and control.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutControlItem.CaptionToControlDistanceProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutControlItem.Control">
            <summary>
                <para>Gets or sets the control displayed by the current item.
This is a dependency property.
</para>
            </summary>
            <value>A UIElement object that specifies the control displayed by the current item.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutControlItem.ControlHorizontalAlignment">
            <summary>
                <para>Gets or sets the horizontal alignment of the control within the current <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/>.
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.HorizontalAlignment"/> value that specifies the control's horizontal alignment.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutControlItem.ControlHorizontalAlignmentProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutControlItem.ControlProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutControlItem.ControlVerticalAlignment">
            <summary>
                <para>Gets or sets the vertical alignment of the control within the current <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/>.
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.VerticalAlignment"/> value that specifies the control's vertical alignment.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutControlItem.ControlVerticalAlignmentProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutControlItem.HasControl">
            <summary>
                <para>Gets whether a control is assigned to the <see cref="P:DevExpress.Xpf.Docking.LayoutControlItem.Control"/> property.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the <see cref="P:DevExpress.Xpf.Docking.LayoutControlItem.Control"/> property is initialized.; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutControlItem.HasControlProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutControlItem.HasDesiredSizeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutControlItem.ShowControl">
            <summary>
                <para>Gets or sets whether the <see cref="P:DevExpress.Xpf.Docking.LayoutControlItem.Control"/> is visible.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the <see cref="P:DevExpress.Xpf.Docking.LayoutControlItem.Control"/> is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutControlItem.ShowControlProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Docking.FloatingMode">

            <summary>
                <para>Contains values that specify how floating panels can be dragged.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Docking.FloatingMode.Desktop">
            <summary>
                <para>Floating panels are allowed to be dragged throughout the desktop.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.FloatingMode.Window">
            <summary>
                <para>Floating panels are allowed to be dragged only within the current window.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.ClosedPanelsBarVisibility">

            <summary>
                <para>Contains values that specify the visibility state of the Closed Panels bar, used to access closed panels.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Docking.Base.ClosedPanelsBarVisibility.Auto">
            <summary>
                <para>The Closed Panels bar is made visible if any closed panel exists. It's possible to hide and then restore the bar via a context menu.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.Base.ClosedPanelsBarVisibility.Default">
            <summary>
                <para>The same as the <see cref="F:DevExpress.Xpf.Docking.Base.ClosedPanelsBarVisibility.Manual"/> option.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.Base.ClosedPanelsBarVisibility.Manual">
            <summary>
                <para>The Closed Panels bar is visible if an end-user enabled it via a context menu.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.Base.ClosedPanelsBarVisibility.Never">
            <summary>
                <para>The Closed Panels bar is always hidden and cannot be made visible via a context menu.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.ItemEventArgs">

            <summary>
                <para>Provides data for the events used to process specific docking operations on items.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.ItemEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem)">
            <summary>
                <para>Initializes a new instance of the ItemEventArgs class with the specified item.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object being processed. This value is used to initialize the <see cref="P:DevExpress.Xpf.Docking.Base.ItemEventArgs.Item"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Docking.Base.ItemEventArgs.Item">
            <summary>
                <para>Gets the currently processed item.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object being currently processed.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Docking.Base.ItemCancelEventArgs">

            <summary>
                <para>Provides data for the events that can be handled to prevent specific docking operations.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.ItemCancelEventArgs.#ctor(System.Boolean,DevExpress.Xpf.Docking.BaseLayoutItem)">
            <summary>
                <para>Initializes a new instance of the ItemCancelEventArgs class with the specified settings.
</para>
            </summary>
            <param name="cancel">
		A Boolean value used to initialize the <see cref="P:DevExpress.Xpf.Docking.Base.ItemCancelEventArgs.Cancel"/> property.

            </param>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object being processed. This value is used to initialize the <see cref="P:DevExpress.Xpf.Docking.Base.ItemEventArgs.Item"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Docking.Base.ItemCancelEventArgs.#ctor(DevExpress.Xpf.Docking.BaseLayoutItem)">
            <summary>
                <para>Initializes a new instance of the ItemCancelEventArgs class with the specified item.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object being processed. This value is assigned to the <see cref="P:DevExpress.Xpf.Docking.Base.ItemEventArgs.Item"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Docking.Base.ItemCancelEventArgs.Cancel">
            <summary>
                <para>Gets or sets whether the current operation must be canceled.
</para>
            </summary>
            <value><b>true</b> if the current operation must be canceled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Docking.ClosedPanelCollection">

            <summary>
                <para>Represents a collection of closed (hidden) panels.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.ClosedPanelCollection.#ctor(DevExpress.Xpf.Docking.DockLayoutManager)">
            <summary>
                <para>Initializes a new instance of the ClosedPanelCollection class with the specified owner.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object which specifies the owner for the created collection.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Docking.ClosedPanelCollection.AddRange(DevExpress.Xpf.Docking.LayoutPanel[])">
            <summary>
                <para>Adds an array of LayoutPanel objects to the current collection.
</para>
            </summary>
            <param name="panels">
		An array of LayoutPanels to be added to the collection.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Docking.ClosedPanelCollection.Dispose">
            <summary>
                <para>Disposes of all the items in the collection and releases all the allocated resources.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Docking.ClosedPanelCollection.Item(System.String)">
            <summary>
                <para>Provides access to panels in the collection by name.

</para>
            </summary>
            <param name="name">
		A string that specifies the name of the panel to be returned. This value matches the value of the panel's <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.Name"/> property.

            </param>
            <value>A <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> object with the specified name.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Docking.ClosedPanelCollection.ToArray">
            <summary>
                <para>Returns the elements of the current collection as an array object.
</para>
            </summary>
            <returns>An array of <see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> objects.
</returns>


        </member>
        <member name="T:DevExpress.Xpf.Docking.DockController">

            <summary>
                <para>Provides methods to perform docking operations on panels.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.DockController.#ctor(DevExpress.Xpf.Docking.DockLayoutManager)">
            <summary>
                <para>Initializes a new instance of the DockController class with the specified <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/>.
</para>
            </summary>
            <param name="container">
		A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object, whose dock functionality will be controlled by the created DockController.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DockController.DockAsDocument(DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Layout.Core.DockType)">
            <summary>
                <para> [To be supplied] </para>
            </summary>
            <param name="item">
		 [To be supplied] 
            </param>
            <param name="target">
		 [To be supplied] 
            </param>
            <param name="type">
		 [To be supplied] 
            </param>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Docking.LayoutPanel">

            <summary>
                <para>Represents a dock panel.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutPanel.#ctor">
            <summary>
                <para>Initializes a new instance of the LayoutPanel class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutPanel.ActualTabBackgroundColor">
            <summary>
                <para>Gets the current color of the LayoutPanel's tab header.
</para>
            </summary>
            <value>A Color value that is the current color of the LayoutPanel's tab header.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutPanel.ActualTabBackgroundColorProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutPanel.AllowDockToDocumentGroup">
            <summary>
                <para>Gets or sets if the current LayoutPanel can be docked to <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/>s. This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the current LayoutPanel can be docked to <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/>s; otherwise, <b>false</b>. Default is <b>true</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutPanel.AllowDockToDocumentGroupProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutPanel.Control">
            <summary>
                <para>Gets a UIElement that has been assigned to the <see cref="P:DevExpress.Xpf.Docking.ContentItem.Content"/> property.
This is a dependency property.
</para>
            </summary>
            <value>The UIElement object that has been assigned to the <see cref="P:DevExpress.Xpf.Docking.ContentItem.Content"/> property. <b>null</b> if any other object has been assigned to the <see cref="P:DevExpress.Xpf.Docking.ContentItem.Content"/> property.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutPanel.ControlProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutPanel.HasBorder">
            <summary>
                <para>Gets whether the panel's border is visible.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the panel's border is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutPanel.HasBorderProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutPanel.HorizontalScrollBarVisibility">
            <summary>
                <para>Gets or sets a horizontal scroll bar's visibility mode. This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Controls.ScrollBarVisibility"/> value that specifies the scroll bar's visibility.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutPanel.HorizontalScrollBarVisibilityProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutPanel.IsPinButtonVisible">
            <summary>
                <para>Gets whether the Pin button is visible.
</para>
            </summary>
            <value><b>true</b> if the Pin button is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutPanel.Layout">
            <summary>
                <para>Gets a <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> object that has been assigned to the <see cref="P:DevExpress.Xpf.Docking.ContentItem.Content"/> property.
This is a dependency property.
</para>
            </summary>
            <value>The  <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> object that has been assigned to the <see cref="P:DevExpress.Xpf.Docking.ContentItem.Content"/> property. <b>null</b> if any other object has been assigned to the <see cref="P:DevExpress.Xpf.Docking.ContentItem.Content"/> property.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutPanel.LayoutProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutPanel.SerializableFloatingBounds">
            <summary>
                <para>Gets or sets the position and size of a floating LayoutPanel after it had been closed.

</para>
            </summary>
            <value>A <see cref="T:System.Windows.Rect"/> structure that stores the position and size of a floating LayoutPanel after it had been closed.

</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutPanel.SerializableFloatingOffset">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutPanel.ShowBorder">
            <summary>
                <para>Gets or sets whether the panel's border is visible.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the panel's border is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutPanel.ShowBorderProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutPanel.ShowPinButton">
            <summary>
                <para>Gets or sets whether the Pin button is visible.
</para>
            </summary>
            <value><b>true</b> if the Pin button is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutPanel.TabBackgroundColor">
            <summary>
                <para>Gets or sets the background color for the LayoutPanel's tab header.
</para>
            </summary>
            <value>A Color value that is the background color for the LayoutPanel's tab header.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutPanel.TabBackgroundColorProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutPanel.VerticalScrollBarVisibility">
            <summary>
                <para>Gets or sets a vertical scroll bar's visibility mode. This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Controls.ScrollBarVisibility"/> value that specifies the scroll bar's visibility.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutPanel.VerticalScrollBarVisibilityProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutPanel.XtraShouldSerializeSerializableFloatingBounds">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="T:DevExpress.Xpf.Docking.DocumentPanel">

            <summary>
                <para><b>DocumentPanel</b> objects represent child panels in a <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/>.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.DocumentPanel.#ctor">
            <summary>
                <para>Initializes a new instance of the DocumentPanel class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DocumentPanel.EnableMouseHoverWhenInactive">
            <summary>
                <para>Gets or sets whether the current DocumentPanel handles its child controls' events in the inactive state. This is a dependency property.

</para>
            </summary>
            <value><b>true</b> if the current DocumentPanel handles its child controls' events in the inactive state; otherwise, <b>false</b>. The default is <b>false</b>.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DocumentPanel.EnableMouseHoverWhenInactiveProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DocumentPanel.GetMDILocation(System.Windows.DependencyObject)">
            <summary>
                <para>Gets the value of the <see cref="P:DevExpress.Xpf.Docking.DocumentPanel.MDILocation"/> attached property from a given object.
</para>
            </summary>
            <param name="dObj">
		An object whose MDILocation property's value must be returned.

            </param>
            <returns>The value of the MDILocation attached property for the specified object.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DocumentPanel.GetMDIMergeStyle(System.Windows.DependencyObject)">
            <summary>
                <para>Gets the <see cref="P:DevExpress.Xpf.Docking.DocumentPanel.MDIMergeStyle"/> attached property value.
</para>
            </summary>
            <param name="target">
		A DocumentPanel whose MDIMergeStyle attached property value is to be obtained.

            </param>
            <returns>An MDIMergeStyle that is the MDIMergeStyle attached property value. 
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DocumentPanel.GetMDISize(System.Windows.DependencyObject)">
            <summary>
                <para>Gets the value of the <see cref="P:DevExpress.Xpf.Docking.DocumentPanel.MDISize"/> attached property from a given object.
</para>
            </summary>
            <param name="dObj">
		An object whose MDISize property's value must be returned.

            </param>
            <returns>The value of the MDISize attached property for the specified object.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DocumentPanel.GetMDIState(System.Windows.DependencyObject)">
            <summary>
                <para>Gets the value of the <see cref="P:DevExpress.Xpf.Docking.DocumentPanel.MDIState"/> attached property from a given object.
</para>
            </summary>
            <param name="dObj">
		An object whose MDIState property's value must be returned.

            </param>
            <returns>The value of the MDIState attached property for the specified object.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DocumentPanel.GetRestoreBounds(System.Windows.DependencyObject)">
            <summary>
                <para>Gets the value of the <see cref="P:DevExpress.Xpf.Docking.DocumentPanel.RestoreBounds"/> attached property from a given object.
</para>
            </summary>
            <param name="dObj">
		An object whose RestoreBounds property's value must be returned.

            </param>
            <returns>The value of the RestoreBounds attached property for the specified object.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DocumentPanel.IsMaximized">
            <summary>
                <para>Gets whether the DocumentPanel is maximized.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the DocumentPanel is maximized; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DocumentPanel.IsMaximizedProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DocumentPanel.IsMinimized">
            <summary>
                <para>Gets whether the DocumentPanel is minimized.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the DocumentPanel is minimized; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DocumentPanel.IsMinimizedProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DocumentPanel.MDILocation">
            <summary>
                <para>Gets or sets the location of an MDI child panel. This property is in effect when the group's <see cref="P:DevExpress.Xpf.Docking.DocumentGroup.MDIStyle"/> property is set to <b>MDI</b>. This is an attached property. 
</para>
            </summary>
            <value>A Point value that specifies the location of an MDI child panel 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DocumentPanel.MDILocationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DocumentPanel.MDIMergeStyle">
            <summary>
                <para>Gets or sets if and when the merging mechanism is invoked for the current panel.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Bars.MDIMergeStyle"/> enumerator value that specifies if and when the merge mechanism is invoked for the current panel.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DocumentPanel.MDIMergeStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DocumentPanel.MDISize">
            <summary>
                <para>Gets or sets the size of an MDI child panel. This property is in effect when the group's <see cref="P:DevExpress.Xpf.Docking.DocumentGroup.MDIStyle"/> property is set to <b>MDI</b>. This is an attached property.
</para>
            </summary>
            <value>A Size structure that specifies the size of an MDI child panel.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DocumentPanel.MDISizeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DocumentPanel.MDIState(System.Windows.DependencyObject)">
            <summary>
                <para>Gets or sets an MDI child panel's state. This property is in effect when the group's <see cref="P:DevExpress.Xpf.Docking.DocumentGroup.MDIStyle"/> property is set to <b>MDI</b>.
This is an attached property.
</para>
            </summary>
            <value>An MDIState value that specifies the panel's state.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DocumentPanel.MDIStateProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DocumentPanel.RestoreBounds(System.Windows.DependencyObject)">
            <summary>
                <para>Gets or sets the size of an MDI child panel before the panel has been maximized. This property is in effect when the group's <see cref="P:DevExpress.Xpf.Docking.DocumentGroup.MDIStyle"/> property is set to <b>MDI</b>.
This is an attached property.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Rect"/> value that specifies the size of an MDI child panel before the panel has been maximized.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DocumentPanel.RestoreBoundsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DocumentPanel.SetMDILocation(System.Windows.DependencyObject,System.Windows.Point)">
            <summary>
                <para>Sets the value of the <see cref="P:DevExpress.Xpf.Docking.DocumentPanel.MDILocation"/> attached property for a given object.
</para>
            </summary>
            <param name="dObj">
		An object for which the MDILocation attached property is set.

            </param>
            <param name="value">
		The value for the MDILocation attached property.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DocumentPanel.SetMDIMergeStyle(System.Windows.DependencyObject,DevExpress.Xpf.Bars.MDIMergeStyle)">
            <summary>
                <para>Sets the <see cref="P:DevExpress.Xpf.Docking.DocumentPanel.MDIMergeStyle"/> attached property value. 

</para>
            </summary>
            <param name="target">
		A DocumentPanel whose MDIMergeStyle attached property value is to be set. 

            </param>
            <param name="value">
		An MDIMergeStyle that is a new MDIMergeStyle attached property value.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DocumentPanel.SetMDISize(System.Windows.DependencyObject,System.Windows.Size)">
            <summary>
                <para>Sets the value of the <see cref="P:DevExpress.Xpf.Docking.DocumentPanel.MDISize"/> attached property for a given object.
</para>
            </summary>
            <param name="dObj">
		An object for which the MDISize attached property is set.

            </param>
            <param name="value">
		The value for the MDISize attached property.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DocumentPanel.SetMDIState(System.Windows.DependencyObject,DevExpress.Xpf.Docking.MDIState)">
            <summary>
                <para>Sets the value of the <see cref="P:DevExpress.Xpf.Docking.DocumentPanel.MDIState"/> attached property for a given object.
</para>
            </summary>
            <param name="dObj">
		An object for which the MDIState attached property is set.

            </param>
            <param name="value">
		The value for the MDIState attached property.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DocumentPanel.SetRestoreBounds(System.Windows.DependencyObject,System.Windows.Rect)">
            <summary>
                <para>Sets the value of the <see cref="P:DevExpress.Xpf.Docking.DocumentPanel.RestoreBounds"/> attached property for a given object.
</para>
            </summary>
            <param name="dObj">
		An object for which the RestoreBounds attached property is set.

            </param>
            <param name="value">
		The value for the RestoreBounds attached property.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DocumentPanel.ShowMaximizeButton">
            <summary>
                <para>Gets or sets whether the DocumentPanel contains the Maximize button, allowing an end-user to maximize the panel. This property is in effect when the panel is represented as a regular MDI child window within a DocumentGroup (the <see cref="P:DevExpress.Xpf.Docking.DocumentGroup.MDIStyle"/> property is set to <b>MDI</b>).
</para>
            </summary>
            <value><b>true</b> if the DocumentPanel contains the Maximize button; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DocumentPanel.ShowMinimizeButton">
            <summary>
                <para>Gets or sets whether the DocumentPanel contains the Minimize button, allowing an end-user to minimize the panel. This property is in effect when the panel is represented as a regular MDI child window within a DocumentGroup (the <see cref="P:DevExpress.Xpf.Docking.DocumentGroup.MDIStyle"/> property is set to <b>MDI</b>).
</para>
            </summary>
            <value><b>true</b> if the DocumentPanel contains the Minimize button; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DocumentPanel.ShowRestoreButton">
            <summary>
                <para>Gets or sets whether the DocumentPanel in the maximized contains the Restore button allowing an end-user to restore the panel to its previous size. This property is in effect when the panel is represented as a regular MDI child window within a DocumentGroup (the <see cref="P:DevExpress.Xpf.Docking.DocumentGroup.MDIStyle"/> property is set to <b>MDI</b>).
</para>
            </summary>
            <value><b>true</b> if when maximized, it contains the Restore button; otherwise, <b>false</b>.

</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DocumentPanel.Uri">
            <summary>
                <para>Gets the universal resource identifier (Uri) of an external Window, Page or UserControl whose contents have been loaded to the current DocumentPanel.
This is a dependency property.
</para>
            </summary>
            <value>The uniform resource identifier (Uri) of the XAML that defines a Window, Page or UserControl whose contents have been loaded into the current DocumentPanel. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DocumentPanel.UriProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Docking.FloatGroupCollection">

            <summary>
                <para>Represents a collection of <see cref="T:DevExpress.Xpf.Docking.FloatGroup"/> objects.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.FloatGroupCollection.#ctor">
            <summary>
                <para>Initializes a new instance of the FloatGroupCollection class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Docking.FloatGroupCollection.AddRange(DevExpress.Xpf.Docking.FloatGroup[])">
            <summary>
                <para>Adds an array of <see cref="T:DevExpress.Xpf.Docking.FloatGroup"/> objects to the current collection.
</para>
            </summary>
            <param name="items">
		An array of <see cref="T:DevExpress.Xpf.Docking.FloatGroup"/> objects to be added to the collection.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Docking.FloatGroupCollection.Dispose">
            <summary>
                <para>Disposes of all the items in the collection and releases all the allocated resources.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Docking.FloatGroupCollection.Item(System.String)">
            <summary>
                <para>Provides access to a FloatGroup's items and their nested items by names.
</para>
            </summary>
            <param name="name">
		A string that specifies the name of the item to be located.

            </param>
            <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object with the specified name. <b>null</b> if no item is found.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Docking.FloatGroupCollection.ToArray">
            <summary>
                <para>Returns float groups stored in the current collection as an array.
</para>
            </summary>
            <returns>An array of <see cref="T:DevExpress.Xpf.Docking.FloatGroup"/> objects.
</returns>


        </member>
        <member name="T:DevExpress.Xpf.Docking.FloatGroup">

            <summary>
                <para>Represents a floating group of dock panels.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.FloatGroup.#ctor">
            <summary>
                <para>Initializes a new instance of the FloatGroup class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Docking.FloatGroup.ActualVisibility">
            <summary>
                <para>Gets a visibility state for the current FloatGroup.
</para>
            </summary>
            <value>A System.Windows.Visibility enumerator value that indicates a visibility state for the current FloatGroup.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.FloatGroup.BorderStyle">
            <summary>
                <para>Gets the FloatGroup's border style.
This is a dependency property.
</para>
            </summary>
            <value>A FloatGroupBorderStyle value that identifies the group's border style.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.FloatGroup.BorderStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.FloatGroup.FloatLocation">
            <summary>
                <para>Gets or sets the location of the FloatGroup object, relative to the top left corner of the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> container.
This is a dependency property.
</para>
            </summary>
            <value>A Point structure that specifies the location of the FloatGroup object, relative to the top left corner of the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> container.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.FloatGroup.FloatLocationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.FloatGroup.IsMaximizable">
            <summary>
                <para>Gets whether the FloatGroup displays a panel that can be maximized to the window's size.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the FloatGroup displays a panel that can be maximized to the window's size; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.FloatGroup.IsMaximizableProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.FloatGroup.IsMaximized">
            <summary>
                <para>Gets whether the FloatGroup displays a panel that is currently maximized to the window's size.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the FloatGroup displays a panel that is currently maximized to the window's size; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.FloatGroup.IsMaximizedProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.FloatGroup.IsOpen">
            <summary>
                <para>Gets or sets whether the current FloatGroup object is visible.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the current FloatGroup object is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.FloatGroup.IsOpenProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.FloatGroup.ShouldRestoreOnActivate">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>A Boolean value.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Docking.DockLayoutManager">

            <summary>
                <para>Represents a container for dock and layout items.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.#ctor">
            <summary>
                <para>Initializes a new instance of the DockLayoutManager class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.Activate(DevExpress.Xpf.Docking.BaseLayoutItem)">
            <summary>
                <para>Activates the specified item. For a <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/> object, this method focuses the associated control.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to be activated.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ActiveDockItem">
            <summary>
                <para>Gets or sets the active dock item.
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that is the active dock item.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ActiveDockItemProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ActiveLayoutItem">
            <summary>
                <para>Gets or sets the active layout item. Setting this property doesn't move keyboard focus.
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that specifies the active layout item.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ActiveLayoutItemProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ActiveMDIItem">
            <summary>
                <para>Gets or sets the active MDI child panel. This property is in effect when the assigned item represents an MDI child panel (DocumentPanel) within a DocumentGroup, and the group's <see cref="P:DevExpress.Xpf.Docking.DocumentGroup.MDIStyle"/> property is set to MDI.
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the active MDI child panel.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ActiveMDIItemProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.AllowCustomization">
            <summary>
                <para>Gets or sets whether Customization Mode can be invoked.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if Customization Mode can be invoked; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.AllowCustomizationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.AllowDockItemRename">
            <summary>
                <para>Gets or sets whether dock items can be renamed.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if dock items can be renamed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.AllowDockItemRenameProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.AllowDocumentSelector">
            <summary>
                <para>Gets or sets whether the Document Selector feature is enabled.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the Document Selector feature is enabled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.AllowDocumentSelectorProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.AllowFloatGroupTransparency">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.AllowFloatGroupTransparencyProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.AllowLayoutItemRename">
            <summary>
                <para>Gets or sets whether layout items can be renamed.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if layout items can be renamed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.AllowLayoutItemRenameProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.AutoHideExpandMode">
            <summary>
                <para>Gets or sets how an auto-hidden panel is expanded.
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.Base.AutoHideExpandMode"/> value that specifies the way an auto-hidden panel is expanded.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.AutoHideExpandModeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.AutoHideGroups">
            <summary>
                <para>Provides access to the collection of <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects, containing auto-hidden panels. Allows you to create auto-hidden panels in XAML.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.AutoHideGroupCollection"/> collection of <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects, containing auto-hidden panels.
</value>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.BeforeItemAdded">
            <summary>
                <para>Fires before an item is added to the current DockLayoutManager object.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.BeforeItemAddedEvent">
            <summary>
                <para>Identifies the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.BeforeItemAdded"/> routed event. 
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.BeginCustomization">
            <summary>
                <para>Invokes Customization Mode and opens the Customization Window.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.BringToFront(DevExpress.Xpf.Docking.BaseLayoutItem)">
            <summary>
                <para>Makes the specified floating item topmost.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to be brought to the front.


            </param>
            <returns>A Boolean value that specifies whether the result is successful.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ClosedPanels">
            <summary>
                <para>Provides access to closed panels.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.ClosedPanelCollection"/> object that contains closed panels

</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ClosedPanelsBarPosition">
            <summary>
                <para>Gets or sets the Closed Panels bar's position. This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.LayoutControl.Dock"/> enumerator value specifying the closed panels bar's position.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ClosedPanelsBarPositionProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ClosedPanelsBarVisibility">
            <summary>
                <para>Gets or sets the visibility state for the Closed Panels bar.
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.Base.ClosedPanelsBarVisibility"/> value that specifies the visibility state for the Closed Panels bar.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ClosedPanelsBarVisibilityProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.CloseMenu">
            <summary>
                <para>Closes the active context menu.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ClosingBehavior">
            <summary>
                <para>Gets or sets the way in which a panel is closed.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.ClosingBehavior"/> value that specifies how a panel is closed.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.Collapse(System.Boolean)">
            <summary>
                <para>Minimizes the expanded auto-hidden panel.
</para>
            </summary>
            <param name="immediately">
		<b>true</b>, to minimize the panel without a sliding animation effect; otherwise, <b>false</b>, to minimize the panel with a sliding animation effect.

            </param>
            <returns><b>true</b> if the panel is successfully minimized; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ContextMenuCustomizations">
            <summary>
                <para>A collection of modification actions to be performed on context menus for dock panels.
</para>
            </summary>
            <value>A BarManagerActionCollection object that contains modification actions.
</value>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.CustomizationFormVisibleChanged">
            <summary>
                <para>Fires after the Customization Window has been displayed or hidden.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.CustomizationFormVisibleChangedEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.DefaultAutoHidePanelCaptionImage">
            <summary>
                <para>Gets or sets the image displayed within a dock panel's header when the panel in the auto-hide state, and if no caption and image are explicitly assigned to the panel.
This is a dependency property.
</para>
            </summary>
            <value>An ImageSource object that specifies the associated image.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DefaultAutoHidePanelCaptionImageProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.DefaultTabPageCaptionImage">
            <summary>
                <para>Gets or sets the image displayed within a dock panel's tab when the panel belongs to a tabbed group, and if no caption and image are explicitly assigned to the panel.
This is a dependency property.
</para>
            </summary>
            <value>An ImageSource object that specifies the associated image.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DefaultTabPageCaptionImageProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.DestroyLastDocumentGroup">
            <summary>
                <para>Gets or sets if the last group existent within the DockLayoutManager should be destroyed on closing its children.

</para>
            </summary>
            <value><b>true</b> if the last group existent within the DockLayoutManager should be destroyed on closing its children; otherwise, <b>false</b>. The default is <b>false</b>.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DestroyLastDocumentGroupProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.DisposeOnWindowClosing">
            <summary>
                <para>Gets or sets if the DockLayoutManager object should be disposed on its parent window closing.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the DockLayoutManager object should be disposed on its parent window closing; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DisposeOnWindowClosingProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.DockController">
            <summary>
                <para>Gets the controller that provides methods to perform docking operations on panels.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.DockController"/> object that provides methods to perform docking operations on panels.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.DockingStyle">
            <summary>
                <para>Gets or sets the docking style.
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.DockingStyle"/> value that is the current docking style.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockingStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemActivated">
            <summary>
                <para>Fires after a dock item has been activated.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemActivatedEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemActivating">
            <summary>
                <para>Fires before a dock item is activated, and allows you to prevent this action.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemActivatingEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemClosed">
            <summary>
                <para>Fires after a dock item has been closed (hidden). 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemClosedEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemClosing">
            <summary>
                <para>Fires before a dock item is closed (hidden), and allows you to prevent this action.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemClosingEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemCollapsed">
            <summary>
                <para>Fires after a visible auto-hidden dock panel has slid away.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemCollapsedEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemDocking">
            <summary>
                <para>Fires before a dock item is dragged over dock hints, and allows you to prevent dock zones from being displayed. 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemDockingEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemDragging">
            <summary>
                <para>Fires repeatedly while a dock panel is being dragged.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemDraggingEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemEndDocking">
            <summary>
                <para>Fires after a dock item has been dropped, and allows you to prevent this action. 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemEndDockingEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemExpanded">
            <summary>
                <para>Fires after a hidden auto-hidden dock panel has slid out.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemExpandedEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemHidden">
            <summary>
                <para>Fires after a dock item has been made auto-hidden.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemHiddenEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemHiding">
            <summary>
                <para>Fires before a dock item is auto-hidden, and allows you to prevent this action. 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemHidingEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemRestored">
            <summary>
                <para>Fires after a dock item has been restored from the closed (hidden) state. 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemRestoredEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemRestoring">
            <summary>
                <para>Fires before a dock item is restored from the closed (hidden) state, and allows you to prevent this action. 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemRestoringEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockItemStartDocking">
            <summary>
                <para>Fires when a docking operation starts, and allows you to prevent this operation. 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockItemStartDockingEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.DockLayoutManager(System.Windows.DependencyObject)">
            <summary>
                <para>Gets or sets a DockLayoutManager object to which a child element belongs. This is an attached property.
This is an attached property.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockLayoutManagerProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationCompleted">
            <summary>
                <para>Occurs immediately after a dock operation within the current DockLayoutManager is completed. 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationCompletedEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationStarting">
            <summary>
                <para>Occurs whenever a docking operation within the current DockLayoutManager is performed.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.DockOperationStartingEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.EnableWin32Compatibility">
            <summary>
                <para>Gets or sets whether auto-hide panels should be displayed over a WindowsFormsHost element. This is a dependency property.

</para>
            </summary>
            <value><b>true</b>, if auto-hide panels should be displayed over a WindowsFormsHost element; otherwise, <b>false</b>.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.EnableWin32CompatibilityProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.EndCustomization">
            <summary>
                <para>Finishes layout customization.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.ExtendSelectionToParent">
            <summary>
                <para>Selects the currently selected item's parent.
</para>
            </summary>
            <returns><b>true</b> if the item's parent has been selected; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.FloatGroups">
            <summary>
                <para>Provides access to floating groups of panels. Allows you to create floating panels in XAML.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.FloatGroupCollection"/> object which is a collection of floating groups.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.FloatingMode">
            <summary>
                <para>Gets or sets how floating panels can be dragged, within or outside the boundaries of the current window.
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.FloatingMode"/> value that specifies how floating panels can be dragged.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.FloatingModeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.GetDockLayoutManager(System.Windows.DependencyObject)">
            <summary>
                <para>Gets the value of the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.DockLayoutManager"/> attached property from a given object.
</para>
            </summary>
            <param name="obj">
		An object whose DockLayoutManager property value must be returned.

            </param>
            <returns>A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object associated with the specified object
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.GetLayoutItem(System.Windows.DependencyObject)">
            <summary>
                <para>Gets the value of the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItem"/> attached property from a given object.
</para>
            </summary>
            <param name="obj">
		An object whose LayoutItem property value must be returned.

            </param>
            <returns>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object associated with the specified object.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.GetUIScope(System.Windows.DependencyObject)">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="obj">
		@nbsp;

            </param>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.HiddenItems">
            <summary>
                <para>Provides access to a collection of hidden Layout Items.
</para>
            </summary>
            <value>A HiddenItemsCollection that stores hidden layout items.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.HideCustomizationForm">
            <summary>
                <para>Hides the Customization Window when Customization Mode is enabled.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.IsCustomization">
            <summary>
                <para>Gets whether Customization Mode is enabled.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if Customization Mode is enabled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.IsCustomizationChanged">
            <summary>
                <para>Fires when Customization Mode is enabled or disabled.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.IsCustomizationChangedEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.IsCustomizationFormVisible">
            <summary>
                <para>Gets whether the Customization Window is visible.
</para>
            </summary>
            <value><b>true</b> if the Customization Window is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.IsCustomizationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.IsRenaming">
            <summary>
                <para>Gets whether an item is being renamed.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if an item is being renamed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.IsRenamingProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.IsSynchronizedWithCurrentItem">
            <summary>
                <para>Gets or sets if a DockLayoutManager is synchronized with the currently selected child item.
</para>
            </summary>
            <value><b>true</b> if a DockLayoutManager is synchronized with the currently selected child item; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.IsSynchronizedWithCurrentItemProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.ItemIsVisibleChanged">
            <summary>
                <para>Fires when the item's <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsVisible"/> property is changed.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ItemIsVisibleChangedEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.Items">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ItemSelectorMenuCustomizations">
            <summary>
                <para>A collection of modification actions to be performed on selector menus for dock panels.

</para>
            </summary>
            <value>A BarManagerActionCollection object that contains menu modification actions. 
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ItemsSource">
            <summary>
                <para>Gets or sets a collection of objects providing information to generate and initialize groups and panels for the current DockLayoutManager container.
This is a dependency property. 
</para>
            </summary>
            <value>A source of objects to be visualized as panels and groups.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ItemsSourceProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.LayoutControlItemContextMenuCustomizations">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.LayoutControlItemCustomizationMenuCustomizations">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.LayoutController">
            <summary>
                <para>Gets the controller that provides methods to perform layout operations on layout items.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.LayoutController"/> object that provides methods to perform layout operations on items.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItem(System.Windows.DependencyObject)">
            <summary>
                <para>Gets or sets a <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to which a visual element belongs. This is an attached property.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to which a visual element belongs.
</value>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemActivated">
            <summary>
                <para>Fires after a layout item has been activated.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemActivatedEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemActivating">
            <summary>
                <para>Fires when a layout item is about to be activated.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemActivatingEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemEndRenaming">
            <summary>
                <para>Fires when layout item renaming is completed.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemEndRenamingEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemHidden">
            <summary>
                <para>Fires when a layout item is hidden.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemHiddenEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemMoved">
            <summary>
                <para>Fires after a layout item has been moved to a new position.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemMovedEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemRestored">
            <summary>
                <para>Fires when a hidden layout item is restored (added to the layout).
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemRestoredEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSelectionChanged">
            <summary>
                <para>Fires after the layout item's selection state has changed.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSelectionChangedEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSelectionChanging">
            <summary>
                <para>Fires when the layout item's selection state is about to be changed.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSelectionChangingEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSizeChanged">
            <summary>
                <para>Fires after a layout item's width/height has changed.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemSizeChangedEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemStartRenaming">
            <summary>
                <para>Fires when layout item renaming is initiated.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItemStartRenamingEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.LayoutRoot">
            <summary>
                <para>Gets or sets a root group for items (panels and other groups).
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> object that represents the root group.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.LayoutRootProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ManualClosedPanelsBarVisibility">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.MDIController">
            <summary>
                <para>Gets the controller that provides methods to perform operations on MDI panels.
</para>
            </summary>
            <value>A DevExpress.Xpf.Docking.MDIController object that provides methods to perform operations on MDI panels.
</value>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.MDIItemActivated">
            <summary>
                <para>Fires when an MDI child document has been activated.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.MDIItemActivatedEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.MDIItemActivating">
            <summary>
                <para>Fires before an MDI child panel is activated.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.MDIItemActivatingEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.MDIMergeStyle">
            <summary>
                <para>Gets or sets if and when the merge mechanism is invoked by the DockLayoutManager. 
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Bars.MDIMergeStyle"/> enumerator value that specifies if and when the merge mechanism is invoked.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.MDIMergeStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.Merge">
            <summary>
                <para>Allows you to customize menus and bars when the merging mechanism is invoked.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.MergeEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.OnApplyTemplate">
            <summary>
                <para>Called after the template is completely generated and attached to the visual tree.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.RedrawContentWhenResizing">
            <summary>
                <para>Gets or sets whether the current DockLayoutManager's content should be dynamically redrawn upon resizing. This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the current DockLayoutManager's content should be dynamically redrawn upon resizing; otherwise, <b>false</b>. Default is <b>true</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.RedrawContentWhenResizingProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.Rename(DevExpress.Xpf.Docking.BaseLayoutItem)">
            <summary>
                <para>Starts item renaming.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> to be renamed.

            </param>
            <returns><b>true</b> if item renaming has been initiated; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.RequestUniqueName">
            <summary>
                <para>Allows you to provide unique names for layout items, whose names conflict with existing names.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.RequestUniqueNameEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.RestoreLayoutFromStream(System.IO.Stream)">
            <summary>
                <para>Restores the layout of dock panes from a stream.
</para>
            </summary>
            <param name="stream">
		A stream from which the layout of dock panes is restored.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.RestoreLayoutFromXml(System.String)">
            <summary>
                <para>Restores the layout of dock panes from an XML file.
</para>
            </summary>
            <param name="path">
		An XML file from that contains the layout of dock panes to be loaded.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.SaveLayoutToStream(System.IO.Stream)">
            <summary>
                <para>Saves the layout of dock panes to a stream.
</para>
            </summary>
            <param name="stream">
		A stream to which the layout is stored.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.SaveLayoutToXml(System.String)">
            <summary>
                <para>Saves the layout of dock panes to an XML file.
</para>
            </summary>
            <param name="path">
		An XML file to which the layout is stored.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.SelectItem(DevExpress.Xpf.Docking.BaseLayoutItem)">
            <summary>
                <para>Selects the specified layout item, in Customization Mode.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents a layout item to be selected.

            </param>
            <returns><b>true</b> if the item was selected; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.SelectItem(DevExpress.Xpf.Docking.BaseLayoutItem,DevExpress.Xpf.Layout.Core.SelectionMode)">
            <summary>
                <para>Selects the specified <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> in Customization Mode with specified selection options. 
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> to be selected.

            </param>
            <param name="mode">
		A DevExpress.Xpf.Layout.Core.SelectionMode enumeration value specifying selection option.


            </param>
            <returns><b>true</b> if the specified <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> is successfully selected; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.SetDockLayoutManager(System.Windows.DependencyObject,DevExpress.Xpf.Docking.DockLayoutManager)">
            <summary>
                <para>Sets the value of the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.DockLayoutManager"/> attached property for a given object.
</para>
            </summary>
            <param name="obj">
		An object for which the DockLayoutManager attached property is set.

            </param>
            <param name="value">
		A <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/> object to set for the specified object

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.SetLayoutItem(System.Windows.DependencyObject,DevExpress.Xpf.Docking.BaseLayoutItem)">
            <summary>
                <para>Sets the value of the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.LayoutItem"/> attached property for a given object. 
</para>
            </summary>
            <param name="obj">
		An object for which the LayoutItem attached property is set.

            </param>
            <param name="value">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to set for the specified object.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.SetUIScope(System.Windows.DependencyObject,DevExpress.Xpf.Layout.Core.IUIElement)">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="obj">
		@nbsp;

            </param>
            <param name="value">
		@nbsp;

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.ShowContextMenu(DevExpress.Xpf.Docking.BaseLayoutItem)">
            <summary>
                <para>Displays a context menu with the dock commands related to the specified item.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> for which a context menu is to be displayed.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.ShowCustomizationForm">
            <summary>
                <para>Displays the Customization Window when Customization Mode is enabled.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.ShowingDockHints">
            <summary>
                <para>Allows you to hide and disable individual dock hints.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ShowingDockHintsEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.ShowingMenu">
            <summary>
                <para>Fires before showing a context menu, and allows it to be customized.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ShowingMenuEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ShowInvisibleItems">
            <summary>
                <para>Gets or sets whether invisible items are displayed within a layout.
This is a dependency property.
</para>
            </summary>
            <value>A Nullable Boolean value that specifies whether invisible items are displayed within the layout.
</value>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.ShowInvisibleItemsChanged">
            <summary>
                <para>Fires when the value of the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ShowInvisibleItems"/> property is changed.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ShowInvisibleItemsChangedEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ShowInvisibleItemsInCustomizationForm">
            <summary>
                <para>Gets or sets if invisible <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/>s should be displayed in the Customization Mode.

</para>
            </summary>
            <value><b>true</b> if invisible <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/>s should be displayed in the Customization Mode; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ShowInvisibleItemsInCustomizationFormProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ShowInvisibleItemsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.DockLayoutManager.ShowItemSelectorMenu(System.Windows.UIElement,DevExpress.Xpf.Docking.BaseLayoutItem[])">
            <summary>
                <para>Displays a panel selector menu, used to activate an auto-hidden panel or a child panel within a <see cref="T:DevExpress.Xpf.Docking.TabbedGroup"/> or a <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> within a <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/>.
</para>
            </summary>
            <param name="source">
		A visual element for which the menu is invoked.

            </param>
            <param name="items">
		An array of <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> objects for which menu items in the menu will be created. Clicking on any menu item will activate the corresponding BaseLayoutItem object.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.ShowMaximizedDocumentCaptionInWindowTitle">
            <summary>
                <para>Gets or sets whether the caption of a maximized <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> is displayed within the window's title. This property is in effect in <see cref="F:DevExpress.Xpf.Docking.MDIStyle.MDI"/> mode.
</para>
            </summary>
            <value><b>true</b> if the caption of a maximized <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> is displayed within the window's title; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.ShowMaximizedDocumentCaptionInWindowTitleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.UIScope(System.Windows.DependencyObject)">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.UIScopeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.DockLayoutManager.UnMerge">
            <summary>
                <para>Allows you to undo bars customizations performed via the <see cref="E:DevExpress.Xpf.Docking.DockLayoutManager.Merge"/> event. 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.UnMergeEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DockLayoutManager.WindowTitleFormat">
            <summary>
                <para>Gets or sets the format string used to format the window's title.
This is a dependency property.
</para>
            </summary>
            <value>The format string used to format the window's title.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockLayoutManager.WindowTitleFormatProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Docking.DockingStyle">

            <summary>
                <para>Contains values that specify the dock behavior of the <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/>.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Docking.DockingStyle.Default">
            <summary>
                <para>The default docking style, based on the Visual Studio 2008 dock behavior.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockingStyle.VS2010">
            <summary>
                <para>Emulates the docking capabilities found in Microsoft Visual Studio 2010 (including changing docking hints and restricting <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/>s from being docked anywhere other than <see cref="T:DevExpress.Xpf.Docking.DocumentGroup"/>s).
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Docking.DocumentGroup">

            <summary>
                <para>Represents child panels (<see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> objects) using either a tabbed or MDI UI.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.DocumentGroup.#ctor">
            <summary>
                <para>Initializes a new instance of the DocumentGroup class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DocumentGroup.ClosePageButtonShowMode">
            <summary>
                <para>Gets or sets whether Close buttons are displayed in individual tab pages and the tab container's header. 
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.ClosePageButtonShowMode"/> value that specifies the display mode for Close buttons.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DocumentGroup.ClosePageButtonShowModeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DocumentGroup.GroupBorderStyle">
            <summary>
                <para>This property is not supported by the DocumentGroup class. Use the <see cref="P:DevExpress.Xpf.Docking.DocumentGroup.MDIStyle"/> property instead.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DocumentGroup.IsDropDownButtonVisible">
            <summary>
                <para>Gets whether the DropDown button is displayed within the DocumentGroup's header.
</para>
            </summary>
            <value><b>true</b> if the DropDown button is displayed within the DocumentGroup's header; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DocumentGroup.IsMaximized">
            <summary>
                <para>Gets whether any child panel within the current DocumentGroup is maximized.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if any child panel within the current DocumentGroup is maximized; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DocumentGroup.IsMaximizedProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DocumentGroup.IsRestoreButtonVisible">
            <summary>
                <para>Gets whether the DocumentGroup displays the Restore button.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the DocumentGroup displays the Restore button; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DocumentGroup.MDIStyle">
            <summary>
                <para>Gets or sets how the DocumentGroup presents its child panels, as floaing windows, or using the tabbed UI.
This is a dependency property.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Docking.MDIStyle"/> value that specifies how the DocumentGroup presents its child panels, as floaing windows, or using the tabbed UI.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DocumentGroup.MDIStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DocumentGroup.ShowDropDownButton">
            <summary>
                <para>Gets or sets whether the DropDown button is visible within the current DocumentGroup.
</para>
            </summary>
            <value><b>true</b> if the DropDown button is visible within the current DocumentGroup; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.DocumentGroup.ShowRestoreButton">
            <summary>
                <para>Gets or sets whether the Restore button is displayed within the DocumentGroup's title, allowing an end-user to restore a maximized panel to its normal state.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the Restore button is displayed within the DocumentGroup's title; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Docking.TabbedGroup">

            <summary>
                <para>Represents a tabbed group of dock panels (<see cref="T:DevExpress.Xpf.Docking.LayoutPanel"/> objects).
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.TabbedGroup.#ctor">
            <summary>
                <para>Initializes a new instance of the TabbedGroup class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Docking.TabbedGroup.ShowTabForSinglePage">
            <summary>
                <para>Gets or sets whether the only dock panel within the TabbedGroup should display its tab.
</para>
            </summary>
            <value><b>true</b> if the only dock panel within the TabbedGroup should display its tab; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.TabbedGroup.ShowTabForSinglePageProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="T:DevExpress.Xpf.Docking.LayoutGroup">

            <summary>
                <para>Represents a group of items, arranging them side by side (either horizontally or vertically) or using the tabbed UI (the tabbed UI is only supported when combining layout items).
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutGroup.#ctor">
            <summary>
                <para>Initializes a new instance of the LayoutGroup class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutGroup.Accept(DevExpress.Xpf.Layout.Core.IVisitor`1)">
            <summary>
                <para>Invokes the <b>Visit</b> method of the specified visitor for each item that belongs to the current layout group.
</para>
            </summary>
            <param name="visitor">
		An object implementing the DevExpress.Xpf.Layout.Core.IVisitor interface.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutGroup.Accept(DevExpress.Xpf.Layout.Core.VisitDelegate`1)">
            <summary>
                <para>Invokes the specified delegate for each item that belongs to the current layout group.
</para>
            </summary>
            <param name="visit">
		A DevExpress.Xpf.Layout.Core.VisitDelegate delegate that will be invoked for the group's items.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ActualDockItemInterval">
            <summary>
                <para>Gets the actual distance between immediate child dock items.
This is a dependency property.
</para>
            </summary>
            <value>A Double value that specifies the actual distance between immediate child dock items.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ActualDockItemIntervalProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ActualGroupTemplateSelector">
            <summary>
                <para>Gets an object that chooses the group's template based on custom logic.
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that chooses a template based on custom logic. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ActualGroupTemplateSelectorProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ActualItemsAppearanceProperty">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ActualLayoutGroupInterval">
            <summary>
                <para>Gets the actual distance between immediate child <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> objects.
This is a dependency property.
</para>
            </summary>
            <value>A Double value that specifies the actual distance between immediate child <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> objects.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ActualLayoutGroupIntervalProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ActualLayoutItemInterval">
            <summary>
                <para>Gets the actual distance between immediate child <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/> objects.
This is a dependency property.
</para>
            </summary>
            <value>A Double value that specifies the actual distance between immediate child <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/> objects.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ActualLayoutItemIntervalProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutGroup.Add(DevExpress.Xpf.Docking.BaseLayoutItem)">
            <summary>
                <para>Adds the specified item as a child to the current group.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to be added to the group.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutGroup.Add(DevExpress.Xpf.Docking.BaseLayoutItem[])">
            <summary>
                <para>Adds multiple <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/>s as children to the current LayoutGroup.
</para>
            </summary>
            <param name="items">
		<see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/>s to be added to the current LayoutGroup.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutGroup.AddRange(DevExpress.Xpf.Docking.BaseLayoutItem[])">
            <summary>
                <para>Adds the specified array of items to the group.
</para>
            </summary>
            <param name="items">
		An array of items to be added to the group.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.AllowExpand">
            <summary>
                <para>Gets or sets whether a group can be expanded/collapsed.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if a group can be expanded/collapsed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.AllowExpandProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.AllowSplitters">
            <summary>
                <para>Gets or sets whether item resizing is enabled for the group's children.
This is a dependency property.
</para>
            </summary>
            <value>A Nullable Boolean value that specifies whether item resizing is enabled.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.AllowSplittersProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutGroup.BeginInit">
            <summary>
                <para>Starts the LayoutGroup's initialization. Initialization occurs at runtime.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.CaptionOrientation">
            <summary>
                <para>Gets or sets the orientation of the group's header.
This is a dependency property.
</para>
            </summary>
            <value>An Orientation value that specifies the orientation of the group's header.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.CaptionOrientationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutGroup.Clear">
            <summary>
                <para>Removes child items from the group.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ControlItemsHost">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code. This is a dependency property.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ControlItemsHostProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.DestroyContentOnTabSwitching">
            <summary>
                <para>Gets or sets if a tab item's content is destroyed when another tab item is selected. This is a dependency property.
</para>
            </summary>
            <value><b>true</b>, if a tab item's content is destroyed when another tab item is selected; otherwise, <b>false</b>. The default value is <b>true</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.DestroyContentOnTabSwitchingProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.DestroyOnClosingChildren">
            <summary>
                <para>Gets or sets whether the current group is destroyed when removing all its children.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the current group is destroyed when removing all its children; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.DestroyOnClosingChildrenProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.DockItemInterval">
            <summary>
                <para>Gets or sets the distance between immediate child dock items.
This is a dependency property.
</para>
            </summary>
            <value>A Double value that specifies the distance between immediate child dock items.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.DockItemIntervalProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutGroup.EndInit">
            <summary>
                <para>Ends the LayoutGroup's initialization. 

</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.Expanded">
            <summary>
                <para>Gets or sets whether the group is expanded.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the group is expanded; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ExpandedProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutGroup.GetChildrenCount">
            <summary>
                <para>Gets the number of all nested child items.
</para>
            </summary>
            <returns>An integer value that specifies the total number of nested child items owned by the LayoutGroup.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.GroupBorderStyle">
            <summary>
                <para>Gets or sets the group's border style. This option is in effect when the LayoutGroup is used to combine layout items, rather than dock items.
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.GroupBorderStyle"/> value that specifies the group's border style.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.GroupBorderStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.GroupTemplate">
            <summary>
                <para>Gets or sets the template used to render the LayoutGroup.
This is a dependency property.
</para>
            </summary>
            <value>Type: <see cref="T:System.Windows.DataTemplate"/>
A data template. The default is a <b>null</b> reference (<b>Nothing</b> in Visual Basic).</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.GroupTemplateProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.GroupTemplateSelector">
            <summary>
                <para>Gets or sets an object that chooses the group's template based on custom logic.
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that chooses a template based on custom logic. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.GroupTemplateSelectorProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.HasAccent">
            <summary>
                <para>Gets or sets whether the group is marked with a special flag (has a special accent) that makes the group painted with different outer indents.
This is a dependency property.
</para>
            </summary>
            <value>A Nullable Boolean type that specifies whether the group is marked with a special flag that affects its painting.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.HasAccentProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.HasNotCollapsedItemsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.HasSingleItem">
            <summary>
                <para>Gets whether the group owns a single item.
This is a dependency property.
</para>
            </summary>
            <value>A Boolean value that specifies whether the group owns a single item.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.HasSingleItemProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.HasVisibleItems">
            <summary>
                <para>Gets whether the current group contains items whose <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.Visibility"/> property is set to <b>true</b>.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the current group contains items whose <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.Visibility"/> property is set to <b>true</b>; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.HasVisibleItemsProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutGroup.Insert(System.Int32,DevExpress.Xpf.Docking.BaseLayoutItem)">
            <summary>
                <para>Inserts the specified item at the specified position.
</para>
            </summary>
            <param name="index">
		An integer value that specifies the postion at which the item is inserted.

            </param>
            <param name="item">
		A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object to be inserted.

            </param>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.IsAnimated">
            <summary>
                <para>Gets whether an animation is in progress.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if an animation is in progress; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.IsAnimatedProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.IsExpanded">
            <summary>
                <para>Gets whether the group is actually expanded.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the group is actually expanded; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.IsExpandedProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.IsLayoutRoot">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.IsLayoutRootProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.IsScrollNextButtonVisible">
            <summary>
                <para>Gets whether the Scroll Next button is visible.
</para>
            </summary>
            <value><b>true</b> if the Scroll Next button is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.IsScrollPrevButtonVisible">
            <summary>
                <para>Gets whether the Scroll Prev button is visible.
</para>
            </summary>
            <value><b>true</b> if the Scroll Prev button is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.IsSplittersEnabled">
            <summary>
                <para>Gets whether item resizing is actually currently enabled for the LayoutGroup's children.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if item resizing is actually currently enabled for the LayoutGroup's children; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.IsSplittersEnabledProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.Item(System.Int32)">
            <summary>
                <para>Provides indexed access to items in the collection.
</para>
            </summary>
            <param name="index">
		An integer value that specifies the index of the item to be returned.

            </param>
            <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object at the specified index.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.Item(System.String)">
            <summary>
                <para>Provides access to items in the collection by name.
</para>
            </summary>
            <param name="name">
		A string value that species the item's name. This value matches the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.Name"/> property's value.

            </param>
            <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object representing the item with the specified name.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ItemCaptionTemplate">
            <summary>
                <para>Gets or sets the template used to visualize captions of objects stored as elements in the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ItemsSource"/> collection.
This is a dependency property. 

</para>
            </summary>
            <value>A DataTemplate object that specifies the corresponding template.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ItemCaptionTemplateProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ItemCaptionTemplateSelector">
            <summary>
                <para>Gets or sets an object that chooses a template used to visualize captions of objects stored as elements in the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ItemsSource"/> collection.
 This is a dependency property. 
</para>
            </summary>
            <value>A System.Windows.Controls.DataTemplateSelector descendant that applies a <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ItemCaptionTemplate"/> based on custom logic.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ItemCaptionTemplateSelectorProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ItemContentTemplate">
            <summary>
                <para>Gets or sets the template used to visualize contents of objects stored as elements in the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ItemsSource"/> collection.
This is a dependency property. 
</para>
            </summary>
            <value>A DataTemplate object that specifies the corresponding template.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ItemContentTemplateProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ItemContentTemplateSelector">
            <summary>
                <para>Gets or sets an object that chooses a template used to visualize contents of objects stored as elements in the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ItemsSource"/> collection.
 This is a dependency property. 
</para>
            </summary>
            <value>A System.Windows.Controls.DataTemplateSelector descendant that applies a template based on custom logic.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ItemContentTemplateSelectorProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.Items">
            <summary>
                <para>Provides access to child items.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItemCollection"/> object that contains child items.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ItemsAppearance">
            <summary>
                <para>Gets or sets the settings used to customize the appearance of captions for the group's children.
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.Appearance"/> object that specifies the corresponding appearance settings.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ItemsAppearanceProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ItemsInternal">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ItemsSource">
            <summary>
                <para>Gets or sets a collection of objects providing information to generate and initialize groups, panels and layout items for the current LayoutGroup container.
This is a dependency property. 
</para>
            </summary>
            <value>A source of objects to be visualized as panels, groups and layout items.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ItemsSourceProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ItemStyle">
            <summary>
                <para>Gets or sets the style applied to items within a specified LayoutGroup container.
This is a dependency property. 
</para>
            </summary>
            <value>A Style object providing corresponding style settings.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.ItemStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.LastChildFill">
            <summary>
                <para>Gets or sets whether the last LayoutGroup's child should stretch to occupy the whole group. This is a dependency property.
</para>
            </summary>
            <value><b>true</b>, if the last LayoutGroup's child should stretch to occupy the whole group; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.LastChildFillProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="E:DevExpress.Xpf.Docking.LayoutGroup.LayoutChanged">
            <summary>
                <para>Fires when the layout of items within the current group is changed.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.LayoutGroupInterval">
            <summary>
                <para>Gets or sets the distance between immediate child <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> objects.
This is a dependency property.
</para>
            </summary>
            <value>A Double value that specifies the distance between immediate child <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> objects.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.LayoutGroupIntervalProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.LayoutItemInterval">
            <summary>
                <para>Gets or sets the distance between immediate child <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/> objects.
This is a dependency property.
</para>
            </summary>
            <value>A Double value that specifies the distance between immediate child <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/> objects.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.LayoutItemIntervalProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.Orientation">
            <summary>
                <para>Gets or sets whether child items are arranged horizontally or vertically within the group.
This is a dependency property.
</para>
            </summary>
            <value>An <see cref="T:System.Windows.Controls.Orientation"/> value that specifies whether child items are arranged horizontally or vertically within the group.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.OrientationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutGroup.Remove(DevExpress.Xpf.Docking.BaseLayoutItem)">
            <summary>
                <para>Removes the specified item from the collection.
</para>
            </summary>
            <param name="item">
		The item to be removed from the collection.

            </param>
            <returns><b>true</b> if the item is successfully removed; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutGroup.ScrollNext">
            <summary>
                <para>Scrolls forward through tab headers corresponding to the group's child items. This property is in effect if the current group represents child items as tabs.
</para>
            </summary>
            <returns><b>true</b> if the tab headers have been scrolled; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutGroup.ScrollPrev">
            <summary>
                <para>Scrolls backward through tab headers corresponding to the group's child items. This property is in effect if the current group represents child items as tabs.
</para>
            </summary>
            <returns><b>true</b> if the tab headers have been scrolled; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.SelectedItem">
            <summary>
                <para>Gets the selected child item within the current group (mostly, this property is used when the current group represents child items as tabs).
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object that represents the currently selected tab.
</value>


        </member>
        <member name="E:DevExpress.Xpf.Docking.LayoutGroup.SelectedItemChanged">
            <summary>
                <para>Fires when a new child item is selected within the current group. This event is supported for LayoutGroups representing its children as tabs.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.SelectedItemChangedEvent">
            <summary>
                <para>Identifies the  routed event.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.SelectedItemProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.SelectedTabIndex">
            <summary>
                <para>Gets or sets the index of the active child item. This property is supported for groups representing its children as tabs.
This is a dependency property.
</para>
            </summary>
            <value>An integer value that represents the index of the active tab.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.SelectedTabIndexProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.SerializableSelectedTabPageIndex">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ShowScrollNextButton">
            <summary>
                <para>Allows you to hide the Scroll Next button within the group's header. This property is in effect if the scroll buttons have been enabled via the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderLayoutType"/> property.
</para>
            </summary>
            <value><b>true</b> if the Scroll Next button is visible within the group's header; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.ShowScrollPrevButton">
            <summary>
                <para>Allows you to hide the Scroll Prev button within the group's header. This property is in effect if the scroll buttons have been enabled via the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderLayoutType"/> property.
</para>
            </summary>
            <value><b>true</b> if the Scroll Prev button is visible within the group's header; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderCanScrollNext">
            <summary>
                <para>Gets whether it's possible to scroll forward through tab headers corresponding to the group's child items. This property is in effect if the current group represents child items as tabs.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if scrolling forward through tab headers is possible.; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderCanScrollNextProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderCanScrollPrev">
            <summary>
                <para>Gets whether it's possible to scroll backward through tab headers corresponding to the group's child items. This property is in effect if the current group represents child items as tabs.

This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if scrolling backward through tab headers is possible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderCanScrollPrevProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderHasScroll">
            <summary>
                <para>Gets whether the group's header displays scroll buttons used to scroll through the tab headers corresponding to the group's child items. This property is in effect if the current group represents child items as tabs.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the group's header displays scroll buttons used to scroll through the tab headers corresponding to the group's child items; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderHasScrollProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderLayoutType">
            <summary>
                <para>Gets or sets how the LayoutGroup displays the tab headers corresponding to the group's child items. This property is in effect if the current group represents child items as tabs.
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Layout.Core.TabHeaderLayoutType"/> value that specifies how the LayoutGroup displays the tab headers corresponding to the group's child items.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderLayoutTypeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderMaxScrollIndex">
            <summary>
                <para>Gets the maximum tab header scroll position. This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>An integer value that specifies the maximum tab header scroll position.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderMaxScrollIndexProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.TabHeadersAutoFill">
            <summary>
                <para>Gets or sets whether the tab headers, corresponding to the group's child items, must be automatically stretched to fill the empty space in a tab row. This property is in effect if the current group represents child items as tabs.
This is a dependency property.
</para>
            </summary>
            <value>A Boolean value which specifies whether the tab headers must be automatically stretched to fill the empty space in a tab row.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.TabHeadersAutoFillProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderScrollIndex">
            <summary>
                <para>Gets the index that defines the tab header scroll position. This member supports the internal infrastructure, and is not intended to be used directly from your code.
This is a dependency property.
</para>
            </summary>
            <value>An integer value that specifies the tab header scroll position.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.TabHeaderScrollIndexProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.TabItemContainerStyle">
            <summary>
                <para>Gets or sets a style applied to the part of the LayoutGroup containing tab headers. This is a dependency property.
</para>
            </summary>
            <value>A Style object applied to the part of the LayoutGroup containing tab headers.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.TabItemContainerStyleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.TabItemContainerStyleSelector">
            <summary>
                <para>Gets or sets an object that chooses a style applied to the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.TabItemContainerStyle"/> property. This is a dependency property.
 
</para>
            </summary>
            <value>A System.Windows.Controls.StyleSelector descendant that applies a style based on custom logic.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.TabItemContainerStyleSelectorProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.VisiblePages">
            <summary>
                <para>Gets the collection of visible child items that are displayed in the current group, as tabs.

</para>
            </summary>
            <value>A collection of visible tab child items.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutGroup.VisiblePagesCount">
            <summary>
                <para>Gets the number of items in the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.VisiblePages"/> collection.
This is a dependency property.
</para>
            </summary>
            <value>An integer value that specifies the number of items in the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.VisiblePages"/> collection.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutGroup.VisiblePagesCountProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Docking.BaseLayoutItemCollection">

            <summary>
                <para>Represents a collection of <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> objects.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.BaseLayoutItemCollection.#ctor(DevExpress.Xpf.Docking.LayoutGroup)">
            <summary>
                <para>Initializes a new instance of the BaseLayoutItemCollection class with the specified owner.
</para>
            </summary>
            <param name="owner">
		A <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> object which specifies the owner for the created collection.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Docking.BaseLayoutItemCollection.FindItem(System.Collections.Generic.IList`1,System.String)">
            <summary>
                <para>Locates an item with the specified name in the specified list.
</para>
            </summary>
            <param name="items">
		A list of items to be searched through.

            </param>
            <param name="name">
		A string that specifies the item's name.

            </param>
            <returns>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object with the specified name. <b>null</b> if no item with the specified name is found.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItemCollection.Item(System.String)">
            <summary>
                <para>Provides access to items by names.
</para>
            </summary>
            <param name="name">
		A string that specifies the item's name.

            </param>
            <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> descendant which represents an item with the specified name. <b>null</b> if the item with this name is not found.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Docking.BaseLayoutItem">

            <summary>
                <para>Represents the base class for dock panels and groups.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.BaseLayoutItem.Accept(DevExpress.Xpf.Layout.Core.VisitDelegate`1)">
            <summary>
                <para>Invokes the specified delegate for each item that belongs to the current item.
</para>
            </summary>
            <param name="visit">
		A DevExpress.Xpf.Layout.Core.VisitDelegate delegate that will be invoked for the group's items.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Docking.BaseLayoutItem.Accept(DevExpress.Xpf.Layout.Core.IVisitor`1)">
            <summary>
                <para>Invokes the <b>Visit</b> method of the specified visitor for each item that belongs to the current BaseLayoutItem object.
</para>
            </summary>
            <param name="visitor">
		An object implementing the DevExpress.Xpf.Layout.Core.IVisitor interface.

            </param>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ActualAppearanceObjectProperty">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ActualAppearanceProperty">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ActualCaption">
            <summary>
                <para>Gets the item's actual caption, taking into account the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionFormat"/>.
This is a dependency property.
</para>
            </summary>
            <value>A string that specifies the item's actual caption.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ActualCaptionProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ActualCaptionWidth">
            <summary>
                <para>Gets the actual width of the item's caption.
This is a dependency property.
</para>
            </summary>
            <value>A Double value that specifies the actual width of the item's caption.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ActualCaptionWidthProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ActualMargin">
            <summary>
                <para>Gets the actual margins (outer indents) for the current item.
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Thickness"/> value that contains the actual outer indents of the layout item's borders. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ActualMarginProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ActualMaxSize">
            <summary>
                <para>Gets the actual maximum size for the current layout item.
This is a dependency property.
</para>
            </summary>
            <value>A Size structure that specifies the item's actual maximum size.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ActualMaxSizeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ActualMinSize">
            <summary>
                <para>Gets the actual minimum size for the current layout item.
This is a dependency property.
</para>
            </summary>
            <value>A Size structure that specifies the item's actual minimum size.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ActualMinSizeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ActualPadding">
            <summary>
                <para>Gets the actual padding (inner indents) for the current item.
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Thickness"/> value that specifies the actual padding for the current item.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ActualPaddingProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ActualTabCaption">
            <summary>
                <para>Gets the actual text displayed in the tab (when the current item is represented as a tab page).
</para>
            </summary>
            <value>A string that specifies the text displayed in a corresponding tab.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ActualTabCaptionProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowActivate">
            <summary>
                <para>Gets or sets whether the item can be activated.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the item can be activated; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowActivateProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowClose">
            <summary>
                <para>Gets or sets whether the current dock item can be closed.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the current dock item can be closed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowCloseProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowContextMenu">
            <summary>
                <para>Gets or sets whether a context menu is enabled for the current layout item.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if a context menu is enabled for the current layout item; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowContextMenuProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowDock">
            <summary>
                <para>Gets or sets whether the dock item can be docked to another item (panel or group).
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the dock item can be docked to another item; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowDockProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowDrag">
            <summary>
                <para>Gets or sets whether the layout item may be dragged.
This is a dependency property.

</para>
            </summary>
            <value><b>true</b> if the layout item may be dragged; otherwise, <b>false</b>.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowDragProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowFloat">
            <summary>
                <para>Gets or sets whether the dock item can float.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the dock item can float; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowFloatProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowHide">
            <summary>
                <para>Gets or sets whether the item can be hidden (auto-hidden, for dock items).
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the item can be hidden (auto-hidden, for dock items); otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowHideProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowMaximize">
            <summary>
                <para>Gets or sets whether the current item can be maximized. This property is supported for floating <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> objects.
</para>
            </summary>
            <value><b>true</b> if the current item can be maximized; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowMaximizeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowMinimize">
            <summary>
                <para>Gets or sets whether the current item can be minimized. This property is supported for floating <see cref="T:DevExpress.Xpf.Docking.DocumentPanel"/> objects.
</para>
            </summary>
            <value><b>true</b> if the current item can be minimized; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowMinimizeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowMove">
            <summary>
                <para>Gets or sets whether the item is allowed to be moved.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the item is allowed to be moved; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowMoveProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowRename">
            <summary>
                <para>Allows you to prevent an item's caption from being renamed when the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.AllowLayoutItemRename"/> option is enabled.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the item's caption can be renamed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowRenameProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowRestore">
            <summary>
                <para>Gets or sets whether the item can be restored from the hidden state.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the item can be restored from the hidden state; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowRestoreProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowSelection">
            <summary>
                <para>Gets or sets whether the current layout item can be selected in Customization Mode.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the current layout item can be selected in Customization Mode; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowSelectionProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.AllowSizing">
            <summary>
                <para>Gets or sets if the BaseLayoutItem resizing at runtime is enabled.
</para>
            </summary>
            <value><b>true</b> if the BaseLayoutItem resizing at runtime is enabled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AllowSizingProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.Appearance">
            <summary>
                <para>Gets or sets the object that provides appearance settings for the item's captions.
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.Appearance"/> object that specifies the appearance settings for the item's caption.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.AppearanceProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.Background">
            <summary>
                <para>Gets or sets the layout item's background color.

</para>
            </summary>
            <value>A <a href="#" onclick="dxHelpRedirect('T:System.Windows.Media.Brush')">Brush</a> object, set as a BaseLayoutItem's background color.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Docking.BaseLayoutItem.BeginInit">
            <summary>
                <para>Starts the BaseLayoutItem's initialization. Initialization occurs at runtime.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.BindableName">
            <summary>
                <para> [To be supplied] </para>
            </summary>
            <value> [To be supplied] </value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.BindableNameProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.Caption">
            <summary>
                <para>Gets or sets the layout item's caption.

</para>
            </summary>
            <value>A string value that specifies the layout item's caption.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionAlignMode">
            <summary>
                <para>Gets or sets the alignment settings of a control(s) displayed by a <see cref="T:DevExpress.Xpf.Docking.LayoutControlItem"/> object(s).
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.CaptionAlignMode"/> value that specifies the caption alignment settings.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionAlignModeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionFormat">
            <summary>
                <para>Gets or sets the format string used to format the layout item's caption.
This is a dependency property.
</para>
            </summary>
            <value>A string that specifies the caption format.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionFormatProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionHorizontalAlignment">
            <summary>
                <para>Gets or sets the horizontal alignment of the item's caption.
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.HorizontalAlignment"/> value that specifies the caption's horizontal alignment.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionHorizontalAlignmentProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionImage">
            <summary>
                <para>Gets or sets the image displayed within the item's caption.
This is a dependency property.
</para>
            </summary>
            <value>An ImageSource object that specifies the image displayed within the item's caption.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionImageLocation">
            <summary>
                <para>Gets or sets the relative position of an image within the item's caption. 
This is a dependency property.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Docking.ImageLocation"/> value that specifies the relative position of an image within the item's caption.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionImageLocationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionImageProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionLocation">
            <summary>
                <para>Gets or sets the position of the item's caption.
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.CaptionLocation"/> value that specifies the position where the caption is displayed.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionLocationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionTemplate">
            <summary>
                <para>Gets or sets the template used to visualize the current item's <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.Caption"/>.

</para>
            </summary>
            <value>A DataTemplate object that visualizes the current item's <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.Caption"/>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionTemplateProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionTemplateSelector">
            <summary>
                <para>Gets or sets an object that chooses a template applied to the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionTemplate"/> property. This is a dependency property.
</para>
            </summary>
            <value>A System.Windows.Controls.DataTemplateSelector descendant that applies a template based on custom logic.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionTemplateSelectorProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionVerticalAlignment">
            <summary>
                <para>Gets or sets the vertical alignment of the item's caption.
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.VerticalAlignment"/> value that specifies the caption's vertical alignment.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionVerticalAlignmentProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionWidth">
            <summary>
                <para>Gets or sets the width of the item's caption, which is in effect when the <b>CaptionAlignMode</b> property is set to <b>Custom</b>.
This is a dependency property.
</para>
            </summary>
            <value>A Double value that specifies the width of the item's caption.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionWidthProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.CloseCommand">
            <summary>
                <para>Gets or sets a command executed when an item is being closed. This is a dependency property.
</para>
            </summary>
            <value>A <a href="#" onclick="dxHelpRedirect('T:System.Windows.Input.ICommand')">ICommand</a> object executed when an item is being closed.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CloseCommandProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.Closed">
            <summary>
                <para>Gets or sets whether a Dock Item is closed.
</para>
            </summary>
            <value><b>true</b> if a dock item is closed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ClosedProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ClosingBehavior">
            <summary>
                <para>Gets or sets the way the current item acts if closed.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.ClosingBehavior"/> enumeration value.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ClosingBehaviorProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ControlBoxContent">
            <summary>
                <para>Gets or sets the content of the control box region.
This is a dependency property.
</para>
            </summary>
            <value>An object that represents the content of the control box region.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ControlBoxContentProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ControlBoxContentTemplate">
            <summary>
                <para>Gets or sets the template that defines how the object assigned to the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ControlBoxContent"/> property is represented onscreen. 
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.DataTemplate"/> object that defines the control box region's presentation.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ControlBoxContentTemplateProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.CustomizationCaption">
            <summary>
                <para>Gets or sets the caption that represents the current item within the Customization Window.
This is a dependency property.
</para>
            </summary>
            <value>A string that specifies the caption that represents the current item within the Customization Window
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.CustomizationCaptionProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.DataContext">
            <summary>
                <para>Gets or sets the data context for the current item, and its child items, if any, when they participate in data binding.
This is a dependency property.
</para>
            </summary>
            <value>The object to use as data context.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.DataContextProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.Description">
            <summary>
                <para>Gets or sets the item's description displayed within the header of the Document Selector window.
This is a dependency property.
</para>
            </summary>
            <value>A string that represents the item's description.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.DescriptionProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.DesiredCaptionWidth">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.DesiredCaptionWidthProperty">
            <summary>
                <para>Identifies the  dependency property.

</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.BaseLayoutItem.EndInit">
            <summary>
                <para>Ends the BaseLayoutItem's initialization. 
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.FloatOnDoubleClick">
            <summary>
                <para>Gets or sets whether an end-user can double-click the item's caption to float it.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if an end-user can double-click the item's caption to float it; otherwise <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.FloatOnDoubleClickProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.FloatSize">
            <summary>
                <para>Gets or sets the size of the item when it is floating.
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Size"/> structure representing the item's size, in pixels. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.FloatSizeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.FooterDescription">
            <summary>
                <para>Gets or sets the item's description displayed within the footer of the Document Selector window.
This is a dependency property.
</para>
            </summary>
            <value>A string that represents the item's description.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.FooterDescriptionProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.HasCaption">
            <summary>
                <para>Gets whether a non-empty caption is assigned to the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.Caption"/> property.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if a non-empty caption is assigned to the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.Caption"/> property; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.HasCaptionProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.HasDesiredCaptionWidth">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.HasDesiredCaptionWidthProperty">
            <summary>
                <para>Identifies the  dependency property.

</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.HasImage">
            <summary>
                <para>Gets whether a caption image is assigned to the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionImage"/> property.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if a caption image is assigned to the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionImage"/> property; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.HasImageProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.HasTabCaption">
            <summary>
                <para>Gets if the BaseLayoutItem object has a non-empty <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaption"/> property value.
</para>
            </summary>
            <value><b>true</b> if the BaseLayoutItem object has a non-empty <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaption"/> property value; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.HasTabCaptionProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.HeaderBarContainerControlAllowDrop">
            <summary>
                <para>Gets or sets whether a bar can be dropped onto the bar container displayed in the current panel.
This is a dependency property.
</para>
            </summary>
            <value>A Nullable Boolean value that specifies whether a bar can be dropped onto the bar container displayed in the current panel.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.HeaderBarContainerControlAllowDropProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.HeaderBarContainerControlName">
            <summary>
                <para>Gets or sets the name of the bar container, used to embed bars from the DXBars library.
This is a dependency property.
</para>
            </summary>
            <value>A string that specifies the name of the bar container.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.HeaderBarContainerControlNameProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ImageToTextDistance">
            <summary>
                <para>Gets or sets the distance between the item's caption and image. 
This is a dependency property.
</para>
            </summary>
            <value>A Double value that specifies the distance between the item's caption and image, in pixels.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ImageToTextDistanceProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsActive">
            <summary>
                <para>Gets whether the item is active.
This is a dependency property.
</para>
            </summary>
            <value>A Boolean value that specifies whether the item is active.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsActiveProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsAutoHidden">
            <summary>
                <para>Gets whether the item is auto-hidden.
</para>
            </summary>
            <value>A Boolean value that specifies whether the item is auto-hidden.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsCaptionImageVisible">
            <summary>
                <para>Gets whether the caption image (<see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionImage"/>) is visible.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the caption image is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsCaptionImageVisibleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsCaptionVisible">
            <summary>
                <para>Gets whether the item's caption is visible.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the item's caption is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsCaptionVisibleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsCloseButtonVisible">
            <summary>
                <para>Gets whether the Close ('x') button is visible for the current item.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the Close ('x') button is visible for the current item; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsCloseButtonVisibleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsClosed">
            <summary>
                <para>Gets whether the item is closed.
This is a dependency property.
</para>
            </summary>
            <value>A Boolean value that specifies whether the item is closed.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsClosedProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsControlBoxVisible">
            <summary>
                <para>Gets whether the control box region is visible.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the control box region is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsControlBoxVisibleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsControlItemsHost">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
This is a dependency property.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsControlItemsHostProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsDropDownButtonVisibleProperty">
            <summary>
                <para>Identifies the <b>IsDropDownButtonVisible</b> dependency property, published in <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> descendants.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsFloating">
            <summary>
                <para>Gets whether the item floats.
</para>
            </summary>
            <value>A Boolean value that specifies whether the item floats.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsFloatingRootItem">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>A Boolean value.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsFloatingRootItemProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsHidden">
            <summary>
                <para>Gets whether the current Layout Item is hidden.
This is a dependency property.

</para>
            </summary>
            <value><b>true</b> if the current layout item is hidden; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsHiddenProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsInitializing">
            <summary>
                <para>Gets whether the item is being initialized.
</para>
            </summary>
            <value>A Boolean value that specifies whether the item is being initialized.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsMaximizeButtonVisibleProperty">
            <summary>
                <para>Identifies the <b>IsMaximizeButtonVisible</b> dependency property, published in <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> descendants.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsMinimizeButtonVisibleProperty">
            <summary>
                <para>Identifies the <b>IsMinimizeButtonVisible</b> dependency property, published in <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> descendants.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsPinButtonVisibleProperty">
            <summary>
                <para>Identifies the <b>IsPinButtonVisible</b> dependency property, published in <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> descendants.

</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsRestoreButtonVisibleProperty">
            <summary>
                <para>Identifies the <b>IsRestoreButtonVisible</b> dependency property, published in <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> descendants.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsScrollNextButtonVisibleProperty">
            <summary>
                <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.IsScrollNextButtonVisible"/> dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsScrollPrevButtonVisibleProperty">
            <summary>
                <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.IsScrollPrevButtonVisible"/> dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsSelected">
            <summary>
                <para>Gets whether the item is selected in Customization Mode.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the item is selected in Customization Mode; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsSelectedItem">
            <summary>
                <para>Gets whether the current item is selected within any LayoutGroup. 
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the current item is selected within any LayoutGroup; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsSelectedItemProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsSelectedProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsTabPage">
            <summary>
                <para>Gets whether the current item is represented as a tab page.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the current item is represented as a tab page; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsTabPageProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.IsVisible">
            <summary>
                <para>Gets whether the current item is visible.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the current item is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.IsVisibleProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ItemHeight">
            <summary>
                <para>Gets or sets a height for the specified BaseLayoutItem object.
This is a dependency property.
</para>
            </summary>
            <value>A <a href="#" onclick="dxHelpRedirect('T:System.Windows.GridLength')">GridLength</a> object, which is the height for the specified BaseLayoutItem object.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ItemHeightProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ItemType">
            <summary>
                <para>Gets the current item's type.
</para>
            </summary>
            <value>A LayoutItemType enumeration value that specifies the item's type.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ItemWidth">
            <summary>
                <para>Gets or sets a width for the specified BaseLayoutItem object. This is a dependency property.
</para>
            </summary>
            <value>A <a href="#" onclick="dxHelpRedirect('T:System.Windows.GridLength')">GridLength</a> object, that is the width for the specified BaseLayoutItem object.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ItemWidthProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.LayoutSize">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.LayoutSizeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.Margin">
            <summary>
                <para>Gets or sets the outer indents of the item's borders. 
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Thickness"/> value that contains the outer indents of the layout item's borders. 
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.MarginProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.MaxSize">
            <summary>
                <para>Gets or sets the item's maximum size.
This is a dependency property.
</para>
            </summary>
            <value>A Size structure that specifies the item's maximum size.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.MaxSizeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.MinSize">
            <summary>
                <para>Gets or sets the item's minimum size.
This is a dependency property.
</para>
            </summary>
            <value>A Size structure that specifies the item's minimum size.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.MinSizeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.Name">
            <summary>
                <para>Gets or sets the item's name.
This is a dependency property.
</para>
            </summary>
            <value>A string that specifies the item's name.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.NameProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.BaseLayoutItem.OnApplyTemplate">
            <summary>
                <para>Called after the template is completely generated and attached to the visual tree.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.Padding">
            <summary>
                <para>Gets or sets the amount of space between the item's borders and its contents.
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Thickness"/> value that specifies the amount of space between the item's borders and its contents.

</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.PaddingProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.Parent">
            <summary>
                <para>Gets or sets the item's parent group.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Xpf.Docking.LayoutGroup"/> object which is the item's parent.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ParentCollectionName">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>A string value.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ParentName">
            <summary>
                <para>Gets or sets the name of the item's parent. This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>A string value that specifies the name of the item's parent.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ShowCaption">
            <summary>
                <para>Gets or sets whether the item's caption is visible.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the item's caption is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ShowCaptionImage">
            <summary>
                <para>Gets or sets whether the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionImage"/> is visible.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.CaptionImage"/> is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ShowCaptionImageProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ShowCaptionProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ShowCloseButton">
            <summary>
                <para>Allows you to hide the Close ('x') button for the current item.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the Close ('x') button is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ShowCloseButtonProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ShowControlBox">
            <summary>
                <para>Gets or sets whether the object assigned to the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ControlBoxContent"/> property is visible.
This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the object assigned to the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.ControlBoxContent"/> property is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ShowControlBoxProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ShowDropDownButtonProperty">
            <summary>
                <para>Identifies the <b>ShowDropDownButton</b> dependency property, published in <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> descendants.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ShowMaximizeButtonProperty">
            <summary>
                <para>Identifies the <b>ShowMaximizeButton</b> dependency property, published in <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> descendants.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ShowMinimizeButtonProperty">
            <summary>
                <para>Identifies the <b>ShowMinimizeButton</b> dependency property, published in <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> descendants.

</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ShowPinButtonProperty">
            <summary>
                <para>Identifies the <b>ShowPinButton</b> dependency property, published in <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> descendants.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ShowRestoreButtonProperty">
            <summary>
                <para>Identifies the <b>ShowRestoreButton</b> dependency property, published in <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> descendants.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ShowScrollNextButtonProperty">
            <summary>
                <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ShowScrollNextButton"/> dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ShowScrollPrevButtonProperty">
            <summary>
                <para>Identifies the <see cref="P:DevExpress.Xpf.Docking.LayoutGroup.ShowScrollPrevButton"/> dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaption">
            <summary>
                <para>Gets or sets the layout item's tab caption.
</para>
            </summary>
            <value>A string value that specifies the layout item's tab caption.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaptionFormat">
            <summary>
                <para>Gets or sets the format string used to format the layout item's tab caption.
This is a dependency property.
</para>
            </summary>
            <value>A string that specifies the tab caption format.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaptionFormatProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaptionProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaptionWidth">
            <summary>
                <para>Gets or sets the width of the corresponding tab. This property is in effect when the current object represents items as tabs or when it represents one of the tabs.
This is a dependency property.
</para>
            </summary>
            <value>A Double value that specifies the tab's width.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.TabCaptionWidthProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.TextTrimming">
            <summary>
                <para>Gets or sets text trimming options applied to the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.Caption"/>.
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.TextTrimming"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.TextTrimmingProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.TextWrapping">
            <summary>
                <para>Gets or sets text wrapping options applied to the <see cref="P:DevExpress.Xpf.Docking.BaseLayoutItem.Caption"/>.
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.TextWrapping"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.TextWrappingProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.ToolTip">
            <summary>
                <para>Gets or sets a tool tip, displayed at runtime when hovering a BaseLayoutItem's caption or tab caption. This is a dependency property.

</para>
            </summary>
            <value>A System.Object specifying a tool tip, displayed at runtime when hovering a BaseLayoutItem's caption or tab caption.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.ToolTipProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.TypeName">
            <summary>
                <para>Gets the name of the item's type.
</para>
            </summary>
            <value>A string that specifies the name of the item's type.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.BaseLayoutItem.Visibility">
            <summary>
                <para>Gets or sets whether the current item is visible.
This is a dependency property.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Visibility"/> value that specifies the item's visibility.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.BaseLayoutItem.VisibilityProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Xpf.Docking.AutoHideGroupCollection">

            <summary>
                <para>Represents a collection of <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.AutoHideGroupCollection.#ctor">
            <summary>
                <para>Initializes a new instance of the AutoHideGroupCollection class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Xpf.Docking.AutoHideGroupCollection.AddRange(DevExpress.Xpf.Docking.AutoHideGroup[])">
            <summary>
                <para>Adds an array of <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects to the current collection.

</para>
            </summary>
            <param name="items">
		An array of <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects to be added to the current collection.


            </param>


        </member>
        <member name="P:DevExpress.Xpf.Docking.AutoHideGroupCollection.BottomItems">
            <summary>
                <para>Gets the collection of <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects displayed along the bottom edge of the DockLayoutManager container.
</para>
            </summary>
            <value>An ObservableCollection of <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Docking.AutoHideGroupCollection.Dispose">
            <summary>
                <para>Disposes of all the items in the collection and releases all the allocated resources.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Docking.AutoHideGroupCollection.Item(System.String)">
            <summary>
                <para>Provides access to items in the AutoHideGroupCollection by name.
</para>
            </summary>
            <param name="name">
		A string value that specifies the name of the item to be located.

            </param>
            <value>A <see cref="T:DevExpress.Xpf.Docking.BaseLayoutItem"/> object with the specified name.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.AutoHideGroupCollection.LeftItems">
            <summary>
                <para>Gets the collection of <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects displayed along the left edge of the DockLayoutManager container.
</para>
            </summary>
            <value>An ObservableCollection of <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects.
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.AutoHideGroupCollection.RightItems">
            <summary>
                <para>Gets the collection of <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects displayed along the right edge of the DockLayoutManager container.
</para>
            </summary>
            <value>An ObservableCollection of <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects.
</value>


        </member>
        <member name="M:DevExpress.Xpf.Docking.AutoHideGroupCollection.ToArray">
            <summary>
                <para>Returns the elements of the current collection as an array object.
</para>
            </summary>
            <returns>An array of <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects.
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.AutoHideGroupCollection.TopItems">
            <summary>
                <para>Gets the collection of <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects displayed along the top edge of the DockLayoutManager container.
</para>
            </summary>
            <value>An ObservableCollection of <see cref="T:DevExpress.Xpf.Docking.AutoHideGroup"/> objects.
</value>


        </member>
        <member name="T:DevExpress.Xpf.Docking.AutoHideGroup">

            <summary>
                <para>A container for auto-hidden dock panels at a specific side of the DockLayoutManager.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.AutoHideGroup.#ctor">
            <summary>
                <para>Initializes a new instance of the AutoHideGroup class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Docking.AutoHideGroup.AutoHideSize">
            <summary>
                <para>Gets or sets the size of panels belonging to the current AutoHideGroup, in pixels.
This is a dependency property.
</para>
            </summary>
            <value>A Size structure that specifies the size of panels belonging to the current AutoHideGroup, in pixels.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.AutoHideGroup.AutoHideSizeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.AutoHideGroup.AutoHideSpeed">
            <summary>
                <para>Gets or sets the time, in milliseconds, required to open/close an auto-hidden panel belonging to the current group.
This is a dependency property.
</para>
            </summary>
            <value>An integer value that specifies the time, in milliseconds, required to open/close an auto-hidden panel belonging to the current group.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.AutoHideGroup.AutoHideSpeedProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.AutoHideGroup.AutoHideType(System.Windows.DependencyObject)">
            <summary>
                <para>Gets or sets the position where a panel is auto-hidden.
This is an attached property.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Xpf.Docking.AutoHideType"/> value that specifies the position where a panel is auto-hidden.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.AutoHideGroup.AutoHideTypeProperty">
            <summary>
                <para>Identifies the  attached property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.AutoHideGroup.DockType">
            <summary>
                <para>Gets or sets the side of the DockLayoutManager at which the current AutoHideGroup object is docked.
This is a dependency property.
</para>
            </summary>
            <value>A Dock value that specifies the side of the DockLayoutManager at which the current AutoHideGroup object is docked.
</value>


        </member>
        <member name="E:DevExpress.Xpf.Docking.AutoHideGroup.DockTypeChanged">
            <summary>
                <para>Fires when the value of the <see cref="P:DevExpress.Xpf.Docking.AutoHideGroup.DockType"/> property is changed.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.AutoHideGroup.DockTypeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.AutoHideGroup.GetAutoHideSize(System.Windows.DependencyObject)">
            <summary>
                <para>Gets the auto-hide size for a specific object.
</para>
            </summary>
            <param name="target">
		A DependencyObject whose auto-size is to be obtained.

            </param>
            <returns>A System.Windows.Size object that is the object's auto-hide size.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.AutoHideGroup.GetAutoHideType(System.Windows.DependencyObject)">
            <summary>
                <para>Gets the value of the <see cref="P:DevExpress.Xpf.Docking.AutoHideGroup.AutoHideType"/> property for the specified object.
</para>
            </summary>
            <param name="obj">
		An object whose <see cref="P:DevExpress.Xpf.Docking.AutoHideGroup.AutoHideType"/> property's value is to be returned.

            </param>
            <returns>The value of the <see cref="P:DevExpress.Xpf.Docking.AutoHideGroup.AutoHideType"/> property for the specified object.
</returns>


        </member>
        <member name="M:DevExpress.Xpf.Docking.AutoHideGroup.SetAutoHideSize(System.Windows.DependencyObject,System.Windows.Size)">
            <summary>
                <para>Sets the auto-hide size for a specific object.
</para>
            </summary>
            <param name="target">
		A DependencyObject whose auto-size is to be set.

            </param>
            <param name="value">
		A System.Windows.Size object that is the new object's auto-hide size.

            </param>


        </member>
        <member name="M:DevExpress.Xpf.Docking.AutoHideGroup.SetAutoHideType(System.Windows.DependencyObject,DevExpress.Xpf.Docking.AutoHideType)">
            <summary>
                <para>Sets the value of the <see cref="P:DevExpress.Xpf.Docking.AutoHideGroup.AutoHideType"/> property for the specified object.
</para>
            </summary>
            <param name="obj">
		An object whose <see cref="P:DevExpress.Xpf.Docking.AutoHideGroup.AutoHideType"/> property is to be set.

            </param>
            <param name="value">
		A new value of the <see cref="P:DevExpress.Xpf.Docking.AutoHideGroup.AutoHideType"/> property for the specified object.

            </param>


        </member>
        <member name="T:DevExpress.Xpf.Docking.SeparatorItem">

            <summary>
                <para>A visual separator between neighboring items.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.SeparatorItem.#ctor">
            <summary>
                <para>Initializes a new instance of the SeparatorItem class with the specified settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Docking.SeparatorItem.Orientation">
            <summary>
                <para>Gets a SeparatorItem's orientation. This is a dependency property.
</para>
            </summary>
            <value>A System.Windows.Control.Orientation enumerator value that specifies a SeparatorItem's orientation.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.SeparatorItem.OrientationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="T:DevExpress.Xpf.Docking.LayoutSplitter">

            <summary>
                <para>Provides runtime item resizing.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.LayoutSplitter.#ctor">
            <summary>
                <para>Initializes a new instance of the LayoutSplitter class with the specified settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutSplitter.IsEnabled">
            <summary>
                <para>Gets or sets if the current LayoutSplitter is enabled. This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the current LayoutSplitter is enabled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutSplitter.IsEnabledProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LayoutSplitter.Orientation">
            <summary>
                <para>Gets a LayoutSplitter's orientation. This is a dependency property.
</para>
            </summary>
            <value>A System.Windows.Control.Orientation enumerator value that specifies a LayoutSplitter's orientation.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LayoutSplitter.OrientationProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="T:DevExpress.Xpf.Docking.LabelItem">

            <summary>
                <para>A label displaying custom text.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.LabelItem.#ctor">
            <summary>
                <para>Initializes a new instance of the LabelItem class with the specified settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LabelItem.Content">
            <summary>
                <para>Gets or sets a LabelItem's caption. This is a dependency property.
</para>
            </summary>
            <value>An object that is a LabelItem's caption. 
</value>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LabelItem.ContentHorizontalAlignment">
            <summary>
                <para>Gets or sets a horizontal alignment for a Label Item's <see cref="P:DevExpress.Xpf.Docking.LabelItem.Content"/>. This is a dependency property.
</para>
            </summary>
            <value>A System.Windows.HorizontalAlignment enumerator value.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LabelItem.ContentHorizontalAlignmentProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LabelItem.ContentProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LabelItem.ContentTemplate">
            <summary>
                <para>Gets or sets a <b>DataTemplate</b> object to visualize a <see cref="P:DevExpress.Xpf.Docking.LabelItem.Content"/> object.
This is a dependency property.
</para>
            </summary>
            <value>A <a href="#" onclick="dxHelpRedirect('T:System.Windows.DataTemplate')">DataTemplate</a> object that visualizes a <see cref="P:DevExpress.Xpf.Docking.LabelItem.Content"/> object.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LabelItem.ContentTemplateProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LabelItem.ContentTemplateSelector">
            <summary>
                <para>Gets or sets an object that chooses a <see cref="P:DevExpress.Xpf.Docking.LabelItem.ContentTemplate"/> used to visualize objects defined as a Label Item's <see cref="P:DevExpress.Xpf.Docking.LabelItem.Content"/>.
 This is a dependency property. 
</para>
            </summary>
            <value>A System.Windows.Controls.DataTemplateSelector descendant that applies a template based on custom logic.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LabelItem.ContentTemplateSelectorProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LabelItem.ContentVerticalAlignment">
            <summary>
                <para>Gets or sets a vertical alignment for a Label Item's <see cref="P:DevExpress.Xpf.Docking.LabelItem.Content"/>. This is a dependency property.
</para>
            </summary>
            <value>A System.Windows.VerticalAlignment enumerator value.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LabelItem.ContentVerticalAlignmentProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LabelItem.DesiredSizeInternalProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Xpf.Docking.LabelItem.HasContent">
            <summary>
                <para>Gets if the current LabelItem has a caption. This is a dependency property.
</para>
            </summary>
            <value><b>true</b> if the current LabelItem has a caption; otherwise, <b>false</b>.
</value>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LabelItem.HasContentProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="F:DevExpress.Xpf.Docking.LabelItem.HasDesiredSizeProperty">
            <summary>
                <para>Identifies the  dependency property.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="T:DevExpress.Xpf.Docking.EmptySpaceItem">

            <summary>
                <para>Adds whitespace to a UI.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Xpf.Docking.EmptySpaceItem.#ctor">
            <summary>
                <para>Initializes a new instance of the EmptySpaceItem class with the specified settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Xpf.Docking.DockOperation">

            <summary>
                <para>Provides members to label different docking operation types.

</para>
            </summary>

        </member>
        <member name="F:DevExpress.Xpf.Docking.DockOperation.Close">
            <summary>
                <para>A target Dock Item is being closed.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockOperation.Dock">
            <summary>
                <para>A target Dock Item is being docked to a <see cref="T:DevExpress.Xpf.Docking.DockLayoutManager"/>'s edge or another Dock Item.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockOperation.Float">
            <summary>
                <para>A docked Dock Item is made floating.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockOperation.Hide">
            <summary>
                <para>A Dock Item is made auto-hidden.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Xpf.Docking.DockOperation.Restore">
            <summary>
                <para>A closed Dock Item is being restored (see the <see cref="P:DevExpress.Xpf.Docking.DockLayoutManager.ClosedPanels"/> property to learn more).
</para>
            </summary>


        </member>
    </members>
</doc>
