﻿using DataAccess;
using System;
using System.Data;

namespace DataService.Finance
{
    class GetTableOfStorePaymentsCommand : Command
    {
        public DateTime StartDate;
        public DateTime EndDate;
        public int? ChainID;
        public int? HeadOfficeID;
        public int? StoreID;
        public Guid? ContractID;
        public int? MediaServiceID;
        public int? CategoryID;
        public bool? Homesite;
        public DataTable Table;
        
        //Assign values 
        public GetTableOfStorePaymentsCommand(DateTime startdate, DateTime enddate, int? chainid, int? headofficeid, int? storeid, Guid? contractid, int? mediaserviceid, int? categoryid, bool? homesite)
        {
            StartDate = startdate; EndDate = enddate; ChainID = chainid; HeadOfficeID = headofficeid; StoreID = storeid; ContractID = contractid; MediaServiceID = mediaserviceid; CategoryID = categoryid; Homesite = homesite;
        }
    }
}
