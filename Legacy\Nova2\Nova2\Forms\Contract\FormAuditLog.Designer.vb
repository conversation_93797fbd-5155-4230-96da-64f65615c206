﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class FormAuditLog
    Inherits LiquidShell.BaseForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle4 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim SuperToolTip1 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem1 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem1 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip2 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem2 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem2 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Me.GroupItems = New DevExpress.XtraEditors.GroupControl()
        Me.Grid = New System.Windows.Forms.DataGridView()
        Me.LabelSearch = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearch = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearch = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearch = New DevExpress.XtraEditors.PictureEdit()
        Me.ObjectTypeColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.ObjectNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.UserColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.ActionDateColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.ActionColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupItems, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupItems.SuspendLayout()
        CType(Me.Grid, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'GroupItems
        '
        Me.GroupItems.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupItems.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupItems.Appearance.Options.UseFont = True
        Me.GroupItems.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupItems.AppearanceCaption.ForeColor = System.Drawing.Color.DimGray
        Me.GroupItems.AppearanceCaption.Options.UseFont = True
        Me.GroupItems.AppearanceCaption.Options.UseForeColor = True
        Me.GroupItems.Controls.Add(Me.Grid)
        Me.GroupItems.Controls.Add(Me.LabelSearch)
        Me.GroupItems.Controls.Add(Me.TextEditSearch)
        Me.GroupItems.Controls.Add(Me.PictureClearSearch)
        Me.GroupItems.Controls.Add(Me.PictureAdvancedSearch)
        Me.GroupItems.Location = New System.Drawing.Point(12, 12)
        Me.GroupItems.LookAndFeel.SkinName = "Black"
        Me.GroupItems.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupItems.Name = "GroupItems"
        Me.GroupItems.Size = New System.Drawing.Size(773, 519)
        Me.GroupItems.TabIndex = 200
        Me.GroupItems.Text = "Activity List"
        '
        'Grid
        '
        Me.Grid.AllowUserToAddRows = False
        Me.Grid.AllowUserToDeleteRows = False
        Me.Grid.AllowUserToOrderColumns = True
        Me.Grid.AllowUserToResizeRows = False
        DataGridViewCellStyle1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Grid.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle1
        Me.Grid.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Grid.BackgroundColor = System.Drawing.Color.White
        Me.Grid.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.Grid.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.Grid.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle2.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle2.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.Grid.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle2
        Me.Grid.ColumnHeadersHeight = 22
        Me.Grid.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.Grid.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.ObjectTypeColumn, Me.ObjectNameColumn, Me.UserColumn, Me.ActionDateColumn, Me.ActionColumn})
        Me.Grid.EnableHeadersVisualStyles = False
        Me.Grid.GridColor = System.Drawing.Color.White
        Me.Grid.Location = New System.Drawing.Point(2, 22)
        Me.Grid.Margin = New System.Windows.Forms.Padding(0, 0, 0, 3)
        Me.Grid.Name = "Grid"
        Me.Grid.ReadOnly = True
        Me.Grid.RowHeadersVisible = False
        DataGridViewCellStyle4.ForeColor = System.Drawing.Color.DimGray
        Me.Grid.RowsDefaultCellStyle = DataGridViewCellStyle4
        Me.Grid.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.Grid.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.Grid.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.Grid.RowTemplate.Height = 19
        Me.Grid.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.Grid.ShowCellToolTips = False
        Me.Grid.Size = New System.Drawing.Size(769, 463)
        Me.Grid.StandardTab = True
        Me.Grid.TabIndex = 1
        '
        'LabelSearch
        '
        Me.LabelSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelSearch.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSearch.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelSearch.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelSearch.Location = New System.Drawing.Point(615, 495)
        Me.LabelSearch.Name = "LabelSearch"
        Me.LabelSearch.Size = New System.Drawing.Size(45, 13)
        Me.LabelSearch.TabIndex = 5
        Me.LabelSearch.Text = "Search:"
        '
        'TextEditSearch
        '
        Me.TextEditSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearch.EditValue = ""
        Me.TextEditSearch.Location = New System.Drawing.Point(666, 492)
        Me.TextEditSearch.Margin = New System.Windows.Forms.Padding(3, 4, 3, 5)
        Me.TextEditSearch.Name = "TextEditSearch"
        Me.TextEditSearch.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearch.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearch.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearch.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearch.Size = New System.Drawing.Size(80, 20)
        Me.TextEditSearch.TabIndex = 6
        '
        'PictureClearSearch
        '
        Me.PictureClearSearch.AllowDrop = True
        Me.PictureClearSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearch.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearch.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearch.Location = New System.Drawing.Point(752, 3)
        Me.PictureClearSearch.Name = "PictureClearSearch"
        Me.PictureClearSearch.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearch.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearch.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearch.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem1.Text = "Clear Search"
        ToolTipItem1.LeftIndent = 6
        ToolTipItem1.Text = "Click here to clear all search boxes."
        SuperToolTip1.Items.Add(ToolTipTitleItem1)
        SuperToolTip1.Items.Add(ToolTipItem1)
        Me.PictureClearSearch.SuperTip = SuperToolTip1
        Me.PictureClearSearch.TabIndex = 0
        Me.PictureClearSearch.TabStop = True
        '
        'PictureAdvancedSearch
        '
        Me.PictureAdvancedSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearch.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearch.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearch.Location = New System.Drawing.Point(752, 494)
        Me.PictureAdvancedSearch.Margin = New System.Windows.Forms.Padding(3, 6, 3, 7)
        Me.PictureAdvancedSearch.Name = "PictureAdvancedSearch"
        Me.PictureAdvancedSearch.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearch.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearch.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearch.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem2.Text = "Advanced Search"
        ToolTipItem2.LeftIndent = 6
        ToolTipItem2.Text = "Click here to search individual column values."
        SuperToolTip2.Items.Add(ToolTipTitleItem2)
        SuperToolTip2.Items.Add(ToolTipItem2)
        Me.PictureAdvancedSearch.SuperTip = SuperToolTip2
        Me.PictureAdvancedSearch.TabIndex = 7
        Me.PictureAdvancedSearch.TabStop = True
        '
        'ObjectTypeColumn
        '
        Me.ObjectTypeColumn.DataPropertyName = "ObjectType"
        Me.ObjectTypeColumn.HeaderText = "What"
        Me.ObjectTypeColumn.Name = "ObjectTypeColumn"
        Me.ObjectTypeColumn.ReadOnly = True
        Me.ObjectTypeColumn.Width = 90
        '
        'ObjectNameColumn
        '
        Me.ObjectNameColumn.DataPropertyName = "ObjectName"
        Me.ObjectNameColumn.HeaderText = "Which"
        Me.ObjectNameColumn.Name = "ObjectNameColumn"
        Me.ObjectNameColumn.ReadOnly = True
        Me.ObjectNameColumn.Width = 90
        '
        'UserColumn
        '
        Me.UserColumn.DataPropertyName = "User"
        Me.UserColumn.HeaderText = "Who"
        Me.UserColumn.Name = "UserColumn"
        Me.UserColumn.ReadOnly = True
        Me.UserColumn.Width = 90
        '
        'ActionDateColumn
        '
        Me.ActionDateColumn.DataPropertyName = "ActionDate"
        DataGridViewCellStyle3.NullValue = Nothing
        Me.ActionDateColumn.DefaultCellStyle = DataGridViewCellStyle3
        Me.ActionDateColumn.HeaderText = "When"
        Me.ActionDateColumn.Name = "ActionDateColumn"
        Me.ActionDateColumn.ReadOnly = True
        Me.ActionDateColumn.Width = 130
        '
        'ActionColumn
        '
        Me.ActionColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.ActionColumn.DataPropertyName = "Action"
        Me.ActionColumn.HeaderText = "Action"
        Me.ActionColumn.Name = "ActionColumn"
        Me.ActionColumn.ReadOnly = True
        '
        'FormAuditLog
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.ClientSize = New System.Drawing.Size(797, 543)
        Me.Controls.Add(Me.GroupItems)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Name = "FormAuditLog"
        Me.Text = "Audit Log"
        Me.Controls.SetChildIndex(Me.GroupItems, 0)
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupItems, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupItems.ResumeLayout(False)
        CType(Me.Grid, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents GroupItems As DevExpress.XtraEditors.GroupControl
    Friend WithEvents Grid As System.Windows.Forms.DataGridView
    Friend WithEvents LabelSearch As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditSearch As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearch As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearch As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents ObjectTypeColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents ObjectNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents UserColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents ActionDateColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents ActionColumn As System.Windows.Forms.DataGridViewTextBoxColumn

End Class
