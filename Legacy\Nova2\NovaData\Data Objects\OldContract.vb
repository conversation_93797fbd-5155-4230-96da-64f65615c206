Imports Universal.Entities

Public Class OldContract
    Inherits OldBaseObject

#Region "Fields"

    ' Global variables
    Public ConsumingForm As LiquidShell.BaseForm
    Private ContractWasNotSignedPriorToThisSession As Boolean = False
    Private DataSetSnapShot As New DataSet
    Private InitialRentalValue As Decimal
    Public Locked As Boolean = True
    Private LockedWhenOpened As Nullable(Of Boolean)
    Public InLockPeriodWhenOpened As Nullable(Of Boolean)
    Public ErrorMessage_LockedContractUnsignable As String = "Because of the first week selected, " _
    & "this contract would have been locked by the operations department if it were signed." & vbCrLf _
    & "You will be unable to sign the contract at this time."
    Public ErrorMessage_LockedContractUnchangeable As String = "Because of the first week selected, " _
    & "this contract has been locked by the operations department." & vbCrLf & "Some contract properties " _
    & "cannot be modified at this time."

    ' Custom fields
    Private _BurstBindingSource As BindingSource
    Private _PONumberBindingSource As BindingSource
    Private _ProductionBindingSource As BindingSource
    Private _MiscellaneousChargeBindingSource As BindingSource
    Private _MediaCostBindingSource As BindingSource
    Private _InvoiceNumberBindingSource As BindingSource
    Private _CostEstimateBindingSource As BindingSource
    Private _Errors As New Dictionary(Of String, String)

#End Region

#Region "Events"

    Public Event BurstInfoChanged()
    Public Event ProductionInfoChanged()
    Public Event ErrorInfoChanged()

#End Region

#Region "Database Column Properties"

    Public ReadOnly Property ContractID() As Guid
        ' This object's identifier.
        Get
            Return Row("ContractID")
        End Get
    End Property

    Public ReadOnly Property ContractNumber() As String
        Get
            If String.IsNullOrEmpty(Row("ContractNumber")) Then
                Return "(new contract)"
            Else
                Return Row("ContractNumber")
            End If
        End Get
    End Property

    Public Property AccountManagerID() As Integer
        Get
            If IsDBNull(Row("AccountManagerID")) Then
                Return -1
            Else
                Return Row("AccountManagerID")
            End If
        End Get
        Set(ByVal value As Integer)

            If Row.RowState = DataRowState.Detached Then
                ' This is a new contract being created.
                ' Set the value of AccountManagerID.
                Row("AccountManagerID") = value
                Row("AccountManagerName") = DataSet.Tables("AccountManager").Rows.Find(value).Item("AccountManagerName")
                Row("Code") = DataSet.Tables("AccountManager").Rows.Find(value).Item("Code")
                ' Get a new contract number if one hasn't been created yet.
                If String.Compare("(new contract)", ContractNumber) = 0 Then
                    Row("ContractNumber") = CreateNewContractNumber(Code, ConnectionString)
                End If
            Else
                If Not Row("AccountManagerID") = value Then
                    ' New value is different than existing value. Proceed with update.
                    Row("AccountManagerID") = value
                    AddLog("ContractNumber", "AccountManagerName", Row("AccountManagerName").ToString)
                    IsDirty = True
                End If
            End If

            ' Check for errors.
            If IsDBNull(Row("ClientID")) = False Then
                RefreshAccountManagerClientComboError()
            End If

        End Set
    End Property

    Public ReadOnly Property ClientID() As Integer
        Get
            If IsDBNull(Row("ClientID")) Then
                Return -1
            Else
                Return Row("ClientID")
            End If
        End Get
    End Property

    Public ReadOnly Property ClientName() As String
        Get
            Return CStr(Row("ClientName")).Replace("&", "&&")
        End Get
    End Property
    Public ReadOnly Property ContractProposalHeatName() As String
        Get
            Return CStr(Row("ContractProposalHeatName")).Replace("&", "&&")
        End Get
    End Property

    Public ReadOnly Property Classification() As String
        Get
            If String.IsNullOrEmpty(Row("Classification")) Then
                Return Row("Classification")
            Else
                Return Row("Classification")
            End If
        End Get
    End Property

    Public Property ProjectName() As String
        Get
            If String.IsNullOrEmpty(Row("ProjectName")) Then
                Return "Select..."
            Else
                Return Row("ProjectName")
            End If
        End Get
        Set(ByVal value As String)
            ' Clean the proposed new value.
            value = value.Trim
            If Not String.Compare(Row("ProjectName"), value, False) = 0 Then
                ' New value is different than existing value. Proceed with update.
                Row("ProjectName") = value
                AddLog("ContractNumber", "ProjectName", value.ToString)
                IsDirty = True
            End If
        End Set
    End Property

    Public ReadOnly Property ClientBillingAddress() As String
        Get
            Return Row("ClientBillingAddress")
        End Get
    End Property

    Public Property SpecialConditions() As String
        Get
            If String.IsNullOrEmpty(Row("SpecialConditions")) Then
                Return "(none specified)"
            Else
                Return Row("SpecialConditions")
            End If
        End Get
        Set(ByVal value As String)
            ' Clean the proposed new value.
            value = value.Trim
            If Not String.Compare(Row("SpecialConditions"), value, False) = 0 Then
                ' New value is different than existing value. Proceed with update.
                Row("SpecialConditions") = value
                AddLog("ContractNumber", "SpecialConditions", value.ToString)
                IsDirty = True
            End If
        End Set
    End Property

    Public Property BillingInstructions() As String
        Get
            If String.IsNullOrEmpty(Row("BillingInstructions")) Then
                Return String.Empty
            Else
                Return Row("BillingInstructions")
            End If
        End Get
        Set(ByVal value As String)
            ' Clean the proposed new value.
            value = value.Trim
            If Not String.Compare(Row("BillingInstructions"), value, False) = 0 Then
                ' New value is different than existing value. Proceed with update.
                Row("BillingInstructions") = value
                AddLog("ContractNumber", "BillingInstructions", value.ToString)
                IsDirty = True
            End If
        End Set
    End Property

    Public Property CreatedBy() As String
        Get
            Return Row("CreatedBy")
        End Get
        Set(ByVal value As String)
            Row("CreatedBy") = value
        End Set
    End Property

    Public Property CreationDate() As Date
        Get
            If IsDBNull(Row("CreationDate")) = False Then
                Return Row("CreationDate")
            Else
                Return Settings.GetServerTime(ConnectionString, ConsumingForm)
            End If
        End Get
        Set(ByVal value As Date)
            Row("CreationDate") = value
        End Set
    End Property

    Public Property Signed() As Boolean
        Get
            Return Row("Signed")
        End Get
        Set(ByVal value As Boolean)
            Row("Signed") = value
        End Set
    End Property

    Public ReadOnly Property SignedBy() As String
        Get
            If IsDBNull(Row("SignedBy")) Then
                Return String.Empty
            Else
                Return Row("SignedBy")
            End If
        End Get
    End Property

    Public ReadOnly Property SignDate() As Date
        Get
            If IsDBNull(Row("SignDate")) Then
                Return New Date(2001, 2, 16)
            Else
                Return Row("SignDate")
            End If
        End Get
    End Property

    Public Property Cancelled() As Boolean
        Get
            Return Row("Cancelled")
        End Get
        Set(ByVal value As Boolean)
            Row("Cancelled") = value
            IsDirty = True
        End Set
    End Property

    Public Property CancelledBy() As String
        Get
            If IsDBNull(Row("CancelledBy")) Then
                Return String.Empty
            Else
                Return Row("CancelledBy")
            End If
        End Get
        Set(ByVal value As String)
            Row("CancelledBy") = value
        End Set
    End Property

    Public Property CancelDate() As Date
        Get
            If IsDBNull(Row("CancelDate")) Then
                Return New Date(1900, 1, 1)
            Else
                Return Row("CancelDate")
            End If
        End Get
        Set(ByVal value As Date)
            Row("CancelDate") = value
        End Set
    End Property

    Public Property AgencyID() As Integer
        Get
            Return Row("AgencyID")
        End Get
        Set(ByVal value As Integer)
            If IsDBNull(Row("AgencyID")) Then
                Row("AgencyID") = value
            Else
                If Not Row("AgencyID") = value Then
                    ' New value is different than existing value. Proceed with update.
                    Row("AgencyID") = value
                    AddLog("ContractNumber", "AgencyName", Row("AgencyName").ToString)
                    IsDirty = True
                End If
            End If
        End Set
    End Property

    Public Property AgencyName() As String
        Get
            If String.IsNullOrEmpty(Row("AgencyName")) Then
                Return "Select..."
            Else
                Return Row("AgencyName")
            End If
        End Get
        Set(ByVal value As String)
            Row("AgencyName") = value
        End Set
    End Property

    Public Property AgencyCommPercentage() As Decimal
        Get
            If ApplyAgencyComm Then
                Return Row("AgencyCommPercentage")
            Else
                Return 0
            End If
        End Get
        Set(ByVal value As Decimal)
            If Not Row("AgencyCommPercentage") = value Then
                ' New value is different than existing value. Proceed with update.
                Row("AgencyCommPercentage") = value
                AddLog("ContractNumber", "AgencyCommPercentage", value.ToString)
                IsDirty = True
            End If
        End Set
    End Property

    Public Property ApplyAgencyComm() As Boolean
        Get
            Return Row("ApplyAgencyComm")
        End Get
        Set(ByVal value As Boolean)
            If Not Row("ApplyAgencyComm") = value Then
                ' New value is different than existing value. Proceed with update.
                Row("ApplyAgencyComm") = value
                AddLog("ContractNumber", "ApplyAgencyComm", value.ToString)
                IsDirty = True
                ' Clear the agency name if value is FALSE.
                If value = False Then
                    Row("AgencyName") = String.Empty
                End If
            End If
        End Set
    End Property

    Public Property AgencyCommIsPercentageOfNetRental() As Boolean
        Get
            Return Row("AgencyCommIsPercentageOfNetRental")
        End Get
        Set(ByVal value As Boolean)
            If Not Row("AgencyCommIsPercentageOfNetRental") = value Then
                ' New value is different than existing value. Proceed with update.
                Row("AgencyCommIsPercentageOfNetRental") = value
                AddLog("ContractNumber", "AgencyCommIsPercentageOfNetRental", value.ToString)
                IsDirty = True
            End If
        End Set
    End Property

    Public Property PrintAgencyComm() As Boolean
        Get
            Return Row("PrintAgencyComm")
        End Get
        Set(ByVal value As Boolean)
            If Not Row("PrintAgencyComm") = value Then
                ' New value is different than existing value. Proceed with update.
                Row("PrintAgencyComm") = value
                AddLog("ContractNumber", "PrintAgencyComm", value.ToString)
                IsDirty = True
            End If
        End Set
    End Property

    Public Property ContractNotes() As String
        Get
            If String.IsNullOrEmpty(Row("ContractNotes")) Then
                Return String.Empty
            Else
                Return Row("ContractNotes")
            End If
        End Get
        Set(ByVal value As String)
            ' Clean the proposed new value.
            value = value.Trim
            If Not String.Compare(Row("ContractNotes"), value, False) = 0 Then
                ' New value is different than existing value. Proceed with update.
                Row("ContractNotes") = value
                AddLog("ContractNumber", "ContractNotes", value.ToString)
                IsDirty = True
            End If
        End Set
    End Property

#End Region

#Region "Custom Properties"

    Public WriteOnly Property SelectedClient() As DataRow
        Set(ByVal value As DataRow)

            If IsDBNull(Row("ClientID")) Then
                Row("ClientID") = value("ClientID")
                Row("ClientName") = value("ClientName")
                Row("ClientBillingAddress") = value("ClientAddress")
            Else
                If Not Row("ClientID") = value("ClientID") Then
                    ' New value is different than existing value. Proceed with update.
                    Row("ClientID") = value("ClientID")
                    Row("ClientName") = value("ClientName")
                    Row("ClientBillingAddress") = value("ClientAddress")
                    AddLog("ContractNumber", "ClientName", Row("ClientName").ToString)
                    IsDirty = True
                End If
            End If

            ' Check for errors.
            RefreshAccountManagerClientComboError()

        End Set
    End Property

    Public WriteOnly Property SelectedProposalHeat() As DataRow
        Set(ByVal value As DataRow)

            If IsDBNull(Row("ContractProposalHeatId")) Then
                Row("ContractProposalHeatId") = value("ContractProposalHeatId")
                Row("ContractProposalHeatName") = value("ContractProposalHeatName")

            Else
                If Not Row("ContractProposalHeatId") = value("ContractProposalHeatId") Then
                    ' New value is different than existing value. Proceed with update.
                    Row("ContractProposalHeatId") = value("ContractProposalHeatId")
                    Row("ContractProposalHeatName") = value("ContractProposalHeatName")

                    AddLog("ContractNumber", "ContractProposalHeatName", Row("ContractProposalHeatName").ToString)
                    IsDirty = True
                End If
            End If

            ' Check for errors.
            RefreshAccountManagerClientComboError()

        End Set
    End Property

    Public ReadOnly Property AccountManagerName() As String
        Get
            Return Row("AccountManagerName")
        End Get
    End Property

    Public ReadOnly Property Code() As String
        Get
            Return Row("Code")
        End Get
    End Property

    Public ReadOnly Property ProductionCharges() As Decimal
        Get
            ' Add together all production charges and return the total.
            Dim Total As Decimal = 0
            For Each ProductionItem As DataRow In Row.GetChildRows("Contract_ContractInventoryQty")
                Total += ProductionItem("SellPrice")
            Next
            Return Total
        End Get
    End Property

    Public ReadOnly Property MiscellaneousCharges() As Decimal
        Get
            ' Add together all miscellaneous charges and return the total.
            Dim Total As Decimal = 0
            For Each Charge As DataRow In Row.GetChildRows("Contract_ContractMiscellaneousCharge")
                Total += Charge("MiscellaneousChargeAmount")
            Next
            Return Total
        End Get
    End Property

    Public ReadOnly Property TotalProduction() As Decimal
        Get
            Return ProductionCharges + MiscellaneousCharges
        End Get
    End Property

    Public ReadOnly Property Brands() As String
        Get
            If IsDBNull(Row("Brands")) Then
                Return String.Empty
            Else
                Return Row("Brands")
            End If
        End Get
    End Property

    Public ReadOnly Property Chains() As String
        Get
            If IsDBNull(Row("Chains")) Then
                Return String.Empty
            Else
                Return Row("Chains")
            End If
        End Get
    End Property

    Public ReadOnly Property MediaServices() As String
        Get
            If IsDBNull(Row("MediaServices")) Then
                Return String.Empty
            Else
                Return Row("MediaServices")
            End If
        End Get
    End Property

    Public ReadOnly Property Categories() As String
        Get
            If IsDBNull(Row("Categories")) Then
                Return String.Empty
            Else
                Return Row("Categories").ToString.Replace("&", "&&")
            End If
        End Get
    End Property

    Public ReadOnly Property FirstWeek() As Date
        Get
            If IsDBNull(Row("FirstWeek")) Then
                Return New Date(1000, 1, 1)
            Else
                Return Row("FirstWeek")
            End If
        End Get
    End Property

    Public ReadOnly Property LastWeek() As Date
        Get
            If IsDBNull(Row("LastWeek")) Then
                Return New Date(1900, 1, 1)
            Else
                Return Row("LastWeek")
            End If
        End Get
    End Property

    Public ReadOnly Property TotalWeeks() As Integer
        Get
            If IsDBNull(Row("TotalWeeks")) Then
                Return 0
            Else
                Return Row("TotalWeeks")
            End If
        End Get
    End Property

    Public ReadOnly Property BurstBindingSource() As BindingSource
        Get
            Return _BurstBindingSource
        End Get
    End Property

    Public ReadOnly Property PONumberBindingSource() As BindingSource
        Get
            Return _PONumberBindingSource
        End Get
    End Property

    Public ReadOnly Property MiscellaneousChargeBindingSource() As BindingSource
        Get
            Return _MiscellaneousChargeBindingSource
        End Get
    End Property

    Public ReadOnly Property ProductionBindingSource() As BindingSource
        Get
            Return _ProductionBindingSource
        End Get
    End Property
    Public ReadOnly Property MediaCostBindingSource() As BindingSource
        Get
            Return _MediaCostBindingSource
        End Get
    End Property
    Public ReadOnly Property InvoiceNumberBindingSource() As BindingSource
        Get
            Return _InvoiceNumberBindingSource
        End Get
    End Property
    Public ReadOnly Property CostEstimateBindingSource() As BindingSource
        Get
            Return _CostEstimateBindingSource
        End Get
    End Property

    Public ReadOnly Property isReplacement As Boolean
        Get
            Return Row("isReplacement")
        End Get
    End Property

    Public ReadOnly Property ClonedContractNumber As String
        Get
            Return Row("ClonedContractNumber")
        End Get
    End Property

    Public ReadOnly Property ExistingRow() As Boolean
        Get
            If Row.RowState = DataRowState.Detached Then
                Return False
            Else
                Return True
            End If
        End Get
    End Property

    Public ReadOnly Property SignedByLabel() As String
        Get
            If Signed Then
                Return "Signed By:"
            Else
                Return "Not signed (click here to sign)"
            End If
        End Get
    End Property

    Public ReadOnly Property SignatureStatusIcon() As Image
        Get
            If Not Signed Then
                Return My.Resources.Contract_Unsigned_64
            Else
                If Cancelled Then
                    Return My.Resources.Contract_Cancelled_64
                Else
                    Return My.Resources.Contract_Signed_64
                End If
            End If
        End Get
    End Property

    Public ReadOnly Property Rental() As Decimal            ' Sum of the rental totals of all bursts.
        Get

            ' A variable to hold the contract's total rental.
            Dim ContractTotalRental As Decimal = 0

            For Each Burst As DataRow In Row.GetChildRows("FK_Burst_Contract")

                ' Calculate the base rental amount first.
                Dim BaseRental As Decimal = Burst("RentalRate") * Burst("BillableStoreQty") * Burst("BillableWeeks")

                ' Calculate the sum of all loading fee percentages.
                Dim LoadingFeePercentage As Decimal = 0
                For Each LoadingFee As DataRow In Burst.GetChildRows("FK_BurstLoadingFee_Burst")
                    LoadingFeePercentage += LoadingFee("Percentage")
                Next

                ' Calculate the loading fee amount.
                Dim LoadingFeeAmount As Decimal = BaseRental * LoadingFeePercentage / 100

                ' Claculate the gross rental.
                Dim GrossRental As Decimal = BaseRental + LoadingFeeAmount

                ' Subtract the discount to get the net rental for this burst.
                Dim BurstNetRental As Decimal = GrossRental * (100 - Burst("Discount")) / 100

                ' Add the net rental to the contract total.
                ContractTotalRental += BurstNetRental

            Next

            ' Return the sum of the rental of all bursts.
            Return ContractTotalRental

        End Get
    End Property

    Public ReadOnly Property AgencyCommission() As Decimal
        Get
            Dim CommPercentage As Decimal = -1 * CInt(ApplyAgencyComm) * AgencyCommPercentage

            If AgencyCommIsPercentageOfNetRental Then
                Return CommPercentage / 100 * Rental
            Else
                Return Rental - (Rental / (1 + CommPercentage / 100))
            End If
        End Get
    End Property

    Public ReadOnly Property TotalRental() As Decimal
        Get
            Return Rental
        End Get
    End Property

    Public ReadOnly Property AgencySummaryLabel() As String
        Get
            If ApplyAgencyComm Then
                Return "Agency:"
            Else
                Return "No agency involvement"
            End If
        End Get
    End Property

    Public ReadOnly Property BurstsExist() As Boolean
        ' Do bursts exist for this contract?
        Get
            If Row.GetChildRows("FK_Burst_Contract").Length = 0 Then
                Return False
            Else
                Return True
            End If
        End Get
    End Property

    Public ReadOnly Property ContractErrors() As String
        Get

            ' A string to hold all the errors.
            Dim ErrorList As New System.Text.StringBuilder

            ' Add all errors to the string.
            For Each ErrorString As KeyValuePair(Of String, String) In _Errors
                ' Check if this error exists.
                If Not String.IsNullOrEmpty(ErrorString.Value) Then
                    ' This error exists. Add it to the string builder.
                    If ErrorList.Length > 0 Then
                        ErrorList.AppendLine()
                        ErrorList.AppendLine()
                    End If
                    ErrorList.Append(ErrorString.Value)
                End If
            Next

            ' Return the combination of all the errors.
            Return ErrorList.ToString

        End Get
    End Property

    Public ReadOnly Property ContractDate() As Date
        Get
            If FirstWeek.Year = 1000 Then
                Return CreationDate
            Else
                Return FirstWeek
            End If
        End Get
    End Property

    Public ReadOnly Property InLockPeriod() As Boolean
        Get

            ' Get the first week of the contract.
            Dim FirstWeekDate As Date
            If Year(FirstWeek) = 1 Then
                ' This date will be out of range. Use a fictitious past date.
                FirstWeekDate = FirstWeek.AddYears(2100)
            Else
                FirstWeekDate = FirstWeek
            End If

            ' Get the timing settings for locked contracts from the database.
            Dim LockSettings As DataTable = Settings.GetSettings(ConnectionString, ConsumingForm)
            Dim LockDays As Integer = LockSettings.Select("SettingName = 'ContractLockDaysLocked'")(0).Item("SettingValue")
            Dim StartHourOfDay As Integer = LockSettings.Select("SettingName = 'ContractLockStartHourOfDay'")(0).Item("SettingValue")
            Dim StartMinuteOfHour As Integer = LockSettings.Select("SettingName = 'ContractLockStartMinuteOfHour'")(0).Item("SettingValue")
            Dim DaysPriorToContractFirstWeek As Integer = LockSettings.Select("SettingName = 'ContractLockDaysPriorToContractFirstWeek'")(0).Item("SettingValue")

            ' Get the current time.
            Dim ServerTime As Date = Settings.GetServerTime(ConnectionString, ConsumingForm)

            ' Calculate the number of minutes before the first monday of the contract that the contract will become locked.
            Dim Minutes As Integer = DaysPriorToContractFirstWeek * 24 * 60
            Minutes -= StartHourOfDay * 60
            Minutes -= StartMinuteOfHour

            ' Calculate the exact start date and time of the lock period.
            Dim LockPeriodStart As Date = DateAdd(DateInterval.Minute, -Minutes, FirstWeekDate)
            Dim LockPeriodEnd As Date = DateAdd(DateInterval.DayOfYear, LockDays, LockPeriodStart)

            ' Test if the contract is currently in the lock period or not.
            If ServerTime >= LockPeriodStart AndAlso ServerTime <= LockPeriodEnd Then
                ' The contract is in the lock period.
                If InLockPeriodWhenOpened.HasValue = False Then
                    InLockPeriodWhenOpened = True
                End If
                Return True
            Else
                ' The contract is not in the lock period.
                If InLockPeriodWhenOpened.HasValue = False Then
                    InLockPeriodWhenOpened = False
                End If
                Return False
            End If

        End Get
    End Property

    Public ReadOnly Property SpecialConditionsHyperlinkText As String
        Get
            ' Return a string that has all return characters replaced with spaces, and all double spaces replaced with single spaces.
            Return SpecialConditions.Replace(vbCrLf, " ").Replace("  ", " ")
        End Get
    End Property

#End Region

#Region "Public Shared Methods"

    Public Shared Function CreateNewContractNumber _
    (ByVal Code As String, ByVal ConnectionString As String) _
    As String

        ' Create a string to hold the select statement.
        Dim SelectStatement As String = "SELECT dbo.udfLastUsedContractNumber('" & Code & "')"

        ' A string variable to hold potential errors.
        Dim Errors As String = String.Empty

        ' A variable to hold the last number used as a contract number for the AM with the given code.
        Dim LastNumberUsed As Integer = 0

        ' Run the command on the server.
        Dim LastUsedContractNumber As Object = LiquidShell.LiquidAgent.GetSqlScalarValue(ConnectionString, SelectStatement, Errors)
        If Not IsDBNull(LastUsedContractNumber) Then
            ' The query result was not null. In other words, previous contracts exist for the AM with the given code.
            LastNumberUsed = CInt(LastUsedContractNumber)
        End If

        ' Check if any errors occured.
        If String.IsNullOrEmpty(Errors) = False Then
            Return Errors
        End If

        ' Get a random number between 1 and 5 and add it to the last used contract number.
        Dim Generator As New Random
        LastNumberUsed += Generator.Next(4, 9)

        ' Return the last used number plus the random number, prefixed with the account manager code.
        Dim NewContractNumberString As String = LastNumberUsed.ToString("000000")
        Return Code & NewContractNumberString

    End Function

    Public Shared Sub SearchContracts _
    (ByVal ConString As String,
    ByRef ErrorMessage As String,
    ByVal GridToExclude As DataGridView,
    ByRef TableToFill As DataTable,
    ByVal Cancelled As Nullable(Of Boolean),
    ByVal ContractNumber As String,
    ByVal Project As String,
    ByVal ClientName As String,
    ByVal BrandName As String,
    ByVal MediaServiceName As String,
    ByVal History As Nullable(Of Boolean))

        ' Instantiate a new data set and create a sql connection to use to populate it.
        Dim SqlCon As New SqlClient.SqlConnection(ConString)

        ' Create table adapters to collect list data and related data.
        Dim ListAdapter As New DataSetContractTableAdapters.ContractTableAdapter
        ListAdapter.Connection = SqlCon

        ' Populate tables in the dataset.
        Try
            ListAdapter.Fill(TableToFill, Cancelled, ContractNumber, Project, ClientName, History, BrandName, MediaServiceName)
        Catch ex As Exception
            ErrorMessage += LiquidShell.LiquidAgent.GetErrorMessage(ex)
        Finally
            ListAdapter.Dispose()
            SqlCon.Dispose()
        End Try

    End Sub

    Public Shared Sub Delete(ByVal Grid As DataGridView, ByVal ConnectionString As String)
        ' Delete one or more rows from the database.

        ' Get the form that is consuming this method so that the ShowMessage method can be used.
        Dim Consumingform As LiquidShell.BaseForm = CType(Grid.TopLevelControl, LiquidShell.BaseForm)

        ' Stop if user may not delete any of the selected contracts for any reason.
        For Each GridRow As DataGridViewRow In Grid.SelectedRows

            ' Read-only contracts may not be deleted.
            Dim ContractRow As DataSetContract.ContractRow = CType(GridRow.DataBoundItem, DataRowView).Row
            If OldContract.ContractIsReadOnly(ContractRow, ConnectionString) Then
                Consumingform.ShowMessage _
                ("Read-only contracts may not be deleted." & vbCrLf & vbCrLf _
                & "Please request permission to proceed from the selected account manager.",
                "Delete Not Permitted", MessageBoxIcon.Stop)
                Exit Sub
            End If

            ' Cancelled contracts may not be deleted.
            If CType(GridRow.DataBoundItem, DataRowView).Item("Cancelled") Then
                Consumingform.ShowMessage("Cancelled contracts may not be deleted.",
                "Delete Not Permitted", MessageBoxButtons.OK, MessageBoxIcon.Stop)
                Exit Sub
            End If

            ' Signed contracts may not be deleted.
            If CType(GridRow.DataBoundItem, DataRowView).Item("Signed") Then
                Consumingform.ShowMessage("Signed contracts may not be deleted. Please cancel them instead.",
                "Delete Not Permitted", MessageBoxButtons.OK, MessageBoxIcon.Stop)
                Exit Sub
            End If

        Next

        ' Build a list of data relation names of child rows that must be deleted with the parent row.
        Dim DeletableChildRelationNames As New List(Of String)

        ' Create an audit table to record this action.
        Dim AuditLog As New DataSetAudit.AuditLogDataTable

        ' Add row descriptions to the Tag properties of all selected grid rows (these will be used as the object name
        ' in the audit log entries for rows that are deleted.)
        For Each SelectedGridRow As DataGridViewRow In Grid.SelectedRows
            Dim Row As DataRow = CType(SelectedGridRow.DataBoundItem, DataRowView).Row
            SelectedGridRow.Tag = Row("ContractNumber")
        Next

        ' Delete rows from the dataset.
        LiquidShell.LiquidAgent.DeleteParentRows _
        (Grid, String.Empty, DeletableChildRelationNames, AuditLog, String.Empty)

        ' Get the tables and data set from which rows are being deleted to use as a parameter for the table adapters.
        Dim GridTable As DataTable = CType(CType(Grid.DataSource, BindingSource).List, DataView).Table
        Dim DataSet As DataSet = GridTable.DataSet

        ' If any rows were deleted from the data set, delete them from the database too.
        Using DeletedRows As New DataView(GridTable, "", "", DataViewRowState.Deleted)
            If IsNothing(DeletedRows) = False AndAlso DeletedRows.Count > 0 Then

                ' Create and configure table adapters to connect to the database.
                Dim Adapter As New DataSetContractTableAdapters.ContractTableAdapter
                Dim BurstAdapter As New DataSetContractTableAdapters.BurstTableAdapter
                Dim AuditAdapter As New DataSetAuditTableAdapters.AuditLogTableAdapter

                ' Create a new connection for the table adapters to use.
                Dim SqlCon As New SqlClient.SqlConnection(ConnectionString)
                Adapter.Connection = SqlCon
                BurstAdapter.Connection = SqlCon
                AuditAdapter.Connection = SqlCon

                ' Perform the delete operation.
                Try
                    BurstAdapter.Update(DataSet.Tables("Burst"))
                    Adapter.Update(GridTable)
                    AuditAdapter.Update(AuditLog)
                    ' If this contract contained a burst that was sharing a store pool, we could end up
                    ' with a scenario where the remaining bursts using the store pool uses LESS stores than the store pool's
                    ' capacity, resulting in waste. To prevent this, we need to run a store procedure on the SQL server that
                    ' will clean up any such waste after deleting this contract and updating the contract table.
                    Dim Command As String = "exec spTrimStorePoolCapacities"
                    LiquidShell.LiquidAgent.RunCommand(SqlCon, Command, String.Empty)
                Catch ex As Exception
                    Consumingform.ShowMessage("Aaaagghh!!!  An error occured while trying to delete. The technical description of the error is:" _
                    & vbCrLf & vbCrLf & LiquidShell.LiquidAgent.GetErrorMessage(ex))
                Finally
                    ' Dispose all data adapters.
                    Adapter.Dispose()
                    BurstAdapter.Dispose()
                    AuditAdapter.Dispose()
                    ' Close all connections.
                    SqlCon.Dispose()
                End Try

            End If
        End Using

    End Sub

    Public Shared Function ContractIsReadOnly _
    (ByVal ContractRow As DataSetContract.ContractRow, ByVal ConnectionString As String) _
    As Boolean
        ' Check if the current user has permission to modify this contract.

        ' The SQL statement to execute.
        Dim Statement As String = String.Empty
        If Not ContractRow.RowState = DataRowState.Detached Then
            Statement = "SELECT ContractID FROM Sales.vContractsByPermission_EditMyContracts WHERE (ContractID = '" & ContractRow.ContractID.ToString & "')"
        Else
            Statement = "SELECT AccountManagerID FROM Sales.vAccountManagersByPermission_EditMyContracts WHERE (AccountManagerID = " & ContractRow.AccountManagerID.ToString & ")"
        End If

        ' Get the result of the query.
        Dim Result As Object = LiquidShell.LiquidAgent.GetSqlScalarValue(ConnectionString, Statement, String.Empty)

        ' Check the result.
        If IsNothing(Result) Then
            Return True
        Else
            Return False
        End If

    End Function

#End Region

#Region "Public Methods"

    Public Sub New _
    (ByVal DataSource As BindingSource,
    ByVal AddNew As Boolean,
    ByVal _ConsumingForm As LiquidShell.BaseForm,
    ByVal Constring As String)
        ' This constructor takes a binding source and a boolean indicating whether a new
        ' row must be added or whether this is an existing row being edited.

        If AddNew Then
            ' This is a new row being added.
            DataSource.AddNew()
            LockedWhenOpened = False
        End If

        ' Update variables.
        ConsumingForm = _ConsumingForm
        ConnectionString = Constring
        DataBindingSource = DataSource
        If Row("Signed") = False Then
            ContractWasNotSignedPriorToThisSession = True
            LockedWhenOpened = False
        Else
            InitialRentalValue = TotalRental
            ' Make a copy of the current footprint in the media gap so that later we can compare it to possibly
            ' modified bursts to determine if the footprint has spread to previously unoccupied space.
            DataSetSnapShot = DataSet.Copy()
        End If

        ' Check for errors if this is an existing contract being edited.
        If AddNew = False Then
            RefreshAllErrors()
        End If

    End Sub

    Public Function Save(ByVal ConnectionString As String, ByVal GridsToAudit As List(Of DataGridView)) As Boolean
        ' Save this object to the database.

        ' Add audit log entries.
        ' Check if this is a new row being added.
        If Row.RowState = DataRowState.Detached Then
            ' This is a new row being added. Clear the audit log and create only one entry for the new row.
            AuditLog.Clear()
            AddLog("ContractNumber")
            ' The SQL server will stamp this contract with the username and date/time, but to ensure that the username
            ' and creation time is not blank in the contract list after creation, we'll manually add these two properties
            ' now, even though they won't be saved back to the database.
            Row("CreationDate") = Settings.GetServerTime(ConnectionString, ConsumingForm)
            Row("CreatedBy") = My.User.Name
        Else
            ' This row was edited. Update audit log with child row addition details.
            For Each Grid As DataGridView In GridsToAudit
                ' Check each row in this grid.
                For Each GridRow As DataGridViewRow In Grid.Rows
                    ' Get the datarow feeding this grid row.
                    Dim Row As DataRow = CType(GridRow.DataBoundItem, DataRowView).Row
                    If Row.RowState = DataRowState.Added Then
                        ' This row is newly added. Add an entry for it into the audit log.
                        Dim ActionText As String = BuildChildRowActionText(Grid, GridRow, ChildRowAction.Added)
                        LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, Table.TableName, ContractNumber, ActionText)
                    End If
                    If Row.RowState = DataRowState.Modified Then
                        ' This row has been modified. Add an entry for it into the audit log.
                        Dim ActionText As String = BuildChildRowActionText(Grid, GridRow, ChildRowAction.Modified)
                        LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, Table.TableName, ContractNumber, ActionText)
                    End If
                Next
            Next
        End If

        ' Create and configure table adapters to connect to the database.
        Dim SqlAdapter As New DataSetContractTableAdapters.ContractTableAdapter
        Dim BurstAdapter As New DataSetContractTableAdapters.BurstTableAdapter
        Dim PeerBurstAdapter As New DataSetContractTableAdapters.PeerBurstTableAdapter      ' To update bursts which have had only their StorePoolIDs changed by another burst.
        Dim BurstLoadingFeeAdapter As New DataSetContractTableAdapters.BurstLoadingFeeTableAdapter
        Dim BurstCategoryAdapter As New DataSetContractTableAdapters.BurstCategoryTableAdapter
        Dim BurstInstallationDayAdapter As New DataSetContractTableAdapters.BurstInstallationDayTableAdapter
        Dim BurstPcaStatusAdapter As New DataSetContractTableAdapters.BurstPcaStatusTableAdapter
        Dim ProductionAdapter As New DataSetContractTableAdapters.ContractInventoryQtyTableAdapter
        Dim ContractMiscellaneousChargeAdapter As New DataSetContractTableAdapters.ContractMiscellaneousChargeTableAdapter
        Dim PurchaseOrderNumberAdapter As New DataSetContractTableAdapters.PurchaseOrderNumberTableAdapter
        Dim StorePoolAdapter As New DataSetContractTableAdapters.StorePoolTableAdapter
        Dim StoreListAdapter As New DataSetContractTableAdapters.StoreListTableAdapter
        Dim MediaCostAdapter As New DataSetContractTableAdapters.MediaCostTableAdapter
        Dim InvoiceNumberAdapter As New DataSetContractTableAdapters.ContractInvoicesTableAdapter
        Dim CostEstimateAdapter As New DataSetContractTableAdapters.ContractCostEstimatesTableAdapter
        Dim AuditAdapter As New DataSetAuditTableAdapters.AuditLogTableAdapter

        ' Create a new connection for the table adapters to use.
        Dim SqlCon As New SqlClient.SqlConnection(ConnectionString)
        SqlAdapter.Connection = SqlCon
        BurstAdapter.Connection = SqlCon
        PeerBurstAdapter.Connection = SqlCon
        BurstLoadingFeeAdapter.Connection = SqlCon
        BurstCategoryAdapter.Connection = SqlCon
        BurstInstallationDayAdapter.Connection = SqlCon
        BurstPcaStatusAdapter.Connection = SqlCon
        ProductionAdapter.Connection = SqlCon
        ContractMiscellaneousChargeAdapter.Connection = SqlCon
        PurchaseOrderNumberAdapter.Connection = SqlCon
        StorePoolAdapter.Connection = SqlCon
        StoreListAdapter.Connection = SqlCon
        MediaCostAdapter.Connection = SqlCon
        InvoiceNumberAdapter.Connection = SqlCon
        CostEstimateAdapter.Connection = SqlCon
        AuditAdapter.Connection = SqlCon

        ' The StorePool table is a funny one. Unlike the other child tables we need to save, the StorePool table
        ' is a parent of the Burst table.
        ' Let's first split the table into changed row types.
        Dim StorePoolAdded As DataTable = DataSet.Tables("StorePool").GetChanges(DataRowState.Added)
        Dim StorePoolModified As DataTable = DataSet.Tables("StorePool").GetChanges(DataRowState.Modified)
        Dim StorePoolDeleted As DataTable = DataSet.Tables("StorePool").GetChanges(DataRowState.Deleted)
        ' Also split the BurstCategory table for fragmented saving.
        Dim BurstCategoryAdded As DataTable = DataSet.Tables("StorePool").GetChanges(DataRowState.Added)
        Dim BurstCategoryModified As DataTable = DataSet.Tables("StorePool").GetChanges(DataRowState.Modified)
        Dim BurstCategoryDeleted As DataTable = DataSet.Tables("StorePool").GetChanges(DataRowState.Deleted)

        ' Perform the save operation.
        Try

            ' Remember the current item in the binding source list.
            Dim CurrentItem As Object = DataBindingSource.Current
            ' Commit changes to the dataset.
            DataBindingSource.EndEdit()     ' This line changes the binding source's current item.
            ' Restore the current item in the binding source list.
            DataBindingSource.Position = DataBindingSource.IndexOf(CurrentItem)
            ' Update the database.

            ' First save added or modified parent tables.
            If IsNothing(StorePoolAdded) = False Then
                StorePoolAdapter.Update(StorePoolAdded)
            End If
            If IsNothing(StorePoolModified) = False Then
                StorePoolAdapter.Update(StorePoolModified)
            End If

            ' Next, proceed with saving the row and all child tables.
            SqlAdapter.Update(Row)
            PeerBurstAdapter.Update(DataSet.Tables("PeerBurst"))
            BurstAdapter.Update(DataSet.Tables("Burst"))
            BurstLoadingFeeAdapter.Update(DataSet.Tables("BurstLoadingFee"))
            BurstCategoryAdapter.Update(DataSet.Tables("BurstCategory"))
            BurstInstallationDayAdapter.Update(DataSet.Tables("BurstInstallationDay"))
            BurstPcaStatusAdapter.Update(DataSet.Tables("BurstPcaStatus"))
            ProductionAdapter.Update(DataSet.Tables("ContractInventoryQty"))
            ContractMiscellaneousChargeAdapter.Update(DataSet.Tables("ContractMiscellaneousCharge"))
            PurchaseOrderNumberAdapter.Update(DataSet.Tables("PurchaseOrderNumber"))
            StoreListAdapter.Update(DataSet.Tables("StoreList"))
            MediaCostAdapter.Update(DataSet.Tables("MediaCost"))
            InvoiceNumberAdapter.Update(DataSet.Tables("ContractInvoices"))
            CostEstimateAdapter.Update(DataSet.Tables("ContractCostEstimates"))
            ' Lastly, save deleted parent tables.
            If IsNothing(StorePoolDeleted) = False Then
                Try
                    StorePoolAdapter.Update(StorePoolDeleted)
                Catch ex As Exception
                End Try
            End If

            ' Accept changes to the store pool table.
            DataSet.Tables("StorePool").AcceptChanges()

            IsDirty = False
            AuditAdapter.Update(AuditLog)

        Finally
            ' Dispose all table adapters.
            SqlAdapter.Dispose()
            BurstAdapter.Dispose()
            PeerBurstAdapter.Dispose()
            BurstLoadingFeeAdapter.Dispose()
            BurstCategoryAdapter.Dispose()
            BurstInstallationDayAdapter.Dispose()
            BurstPcaStatusAdapter.Dispose()
            ProductionAdapter.Dispose()
            ContractMiscellaneousChargeAdapter.Dispose()
            PurchaseOrderNumberAdapter.Dispose()
            StorePoolAdapter.Dispose()
            StoreListAdapter.Dispose()
            MediaCostAdapter.Dispose()
            InvoiceNumberAdapter.Dispose()
            CostEstimateAdapter.Dispose()
            AuditAdapter.Dispose()
            ' Dispose temporary tables.
            If IsNothing(StorePoolAdded) = False Then
                StorePoolAdded.Dispose()
            End If
            If IsNothing(StorePoolModified) = False Then
                StorePoolModified.Dispose()
            End If
            If IsNothing(StorePoolDeleted) = False Then
                StorePoolDeleted.Dispose()
            End If
            ' Dispose the connection.
            SqlCon.Close()
        End Try

        UpdateBurstInfoColumns()
        Return True

    End Function

    Public Sub UpdateBurstInfoColumns()

        UpdateBurstInfoColumn("MediaName", "MediaServices")
        UpdateBurstInfoColumn("ChainName", "Chains")
        UpdateBurstInfoColumn("BrandName", "Brands")
        UpdateCategoriesColumn()
        UpdateFirstWeekColumn()
        UpdateLastWeekColumn()
        UpdateTotalWeeksColumn()

        RaiseEvent BurstInfoChanged()

    End Sub

    Public Sub ProductionUpdated()
        IsDirty = True
        RaiseEvent ProductionInfoChanged()
    End Sub

    Public Sub DeleteBursts(ByVal Grid As DataGridView)

        ' Stop if no rows were selected.
        If Grid.SelectedRows.Count < 1 Then
            CType(Grid.TopLevelControl, LiquidShell.BaseForm).ShowMessage _
            ("Please select at least one row before using the 'Delete' button.", "Grated Cheese Paradigm")
            Exit Sub
        End If

        ' Confirm the delete action.
        If LiquidShell.LiquidAgent.ActionConfirmed(Grid, "Delete") = False Then
            Exit Sub
        End If

        ' Delete the datarows corresponding to all the selected grid rows.
        For Each SelectedGridRow As DataGridViewRow In Grid.SelectedRows
            Dim RowToDelete As New Burst(Me, False, ConnectionString)
            ' Log it.
            LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, "Burst", RowToDelete.RowDescription, "Deleted")

            ' Reset the row states of all BurstLoadingFee and BurstCategory child rows so that no attempt is made to update
            ' loading fee or category child rows of this burst.

            ' Loading fee child rows.
            For Each BurstLoadingFee As DataRow In RowToDelete.Row.GetChildRows _
            ("FK_BurstLoadingFee_Burst", DataRowVersion.Default)
                If BurstLoadingFee.RowState = DataRowState.Added Then
                    ' This burst loading fee was added. Reset its rowstate.
                    BurstLoadingFee.AcceptChanges()
                End If
            Next
            Try
                For Each BurstLoadingFee As DataRow In RowToDelete.Row.GetChildRows _
                ("FK_BurstLoadingFee_Burst", DataRowVersion.Original)
                    ' This burst loading fee was modified or deleted. Reset its rowstate.
                    BurstLoadingFee.AcceptChanges()
                Next
            Catch ex As VersionNotFoundException
            End Try

            ' Category child rows.
            For Each BurstCategory As DataRow In RowToDelete.Row.GetChildRows _
            ("Burst_BurstCategory", DataRowVersion.Default)
                If BurstCategory.RowState = DataRowState.Added Then
                    ' This burst loading fee was added. Reset its rowstate.
                    BurstCategory.AcceptChanges()
                End If
            Next
            Try
                For Each BurstCategory As DataRow In RowToDelete.Row.GetChildRows _
                ("Burst_BurstCategory", DataRowVersion.Original)
                    ' This burst loading fee was modified or deleted. Reset its rowstate.
                    BurstCategory.AcceptChanges()
                Next
            Catch ex As VersionNotFoundException
            End Try

            For Each BurstInstallationDay As DataRow In RowToDelete.Row.GetChildRows _
            ("FK_Burst_BurstInstallationDay", DataRowVersion.Default)
                If BurstInstallationDay.RowState = DataRowState.Added Then
                    ' This burst loading fee was added. Reset its rowstate.
                    BurstInstallationDay.AcceptChanges()
                End If
            Next
            Try
                For Each BurstInstallationDay As DataRow In RowToDelete.Row.GetChildRows _
                ("FK_Burst_BurstInstallationDay", DataRowVersion.Original)
                    ' This burst loading fee was modified or deleted. Reset its rowstate.
                    BurstInstallationDay.AcceptChanges()
                Next
            Catch ex As VersionNotFoundException
            End Try

            For Each BurstPcaStatus As DataRow In RowToDelete.Row.GetChildRows _
            ("Burst_BurstPcaStatus", DataRowVersion.Default)
                If BurstPcaStatus.RowState = DataRowState.Added Then
                    ' This burst loading fee was added. Reset its rowstate.
                    BurstPcaStatus.AcceptChanges()
                End If
            Next
            Try
                For Each BurstPcaStatus As DataRow In RowToDelete.Row.GetChildRows _
                ("Burst_BurstPcaStatus", DataRowVersion.Original)
                    ' This burst loading fee was modified or deleted. Reset its rowstate.
                    BurstPcaStatus.AcceptChanges()
                Next
            Catch ex As VersionNotFoundException
            End Try


            ' Delete the row.
            CType(SelectedGridRow.DataBoundItem, DataRowView).Row.Delete()
        Next

        UpdateBurstInfoColumns()
        RefreshAllErrors()

    End Sub

    Public Sub DeleteProduction(ByVal Grid As DataGridView)

        ' Stop if no rows were selected.
        If Grid.SelectedRows.Count < 1 Then
            CType(Grid.TopLevelControl, LiquidShell.BaseForm).ShowMessage _
            ("Please select at least one row before using the 'Delete' button.", "Grated Cheese Paradigm")
            Exit Sub
        End If

        ' Confirm the delete action.
        If LiquidShell.LiquidAgent.ActionConfirmed(Grid, "Delete") = False Then
            Exit Sub
        End If

        ' Delete the datarows corresponding to all the selected grid rows.
        For Each SelectedGridRow As DataGridViewRow In Grid.SelectedRows
            Dim RowToDelete As New Production(Me, False, ConnectionString)
            ' Log it.
            LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, "Contract Production", RowToDelete.RowDescription, "Deleted")
            ' Delete the row.
            CType(SelectedGridRow.DataBoundItem, DataRowView).Row.Delete()
        Next

        ProductionUpdated()

    End Sub

    Public Sub DeleteMiscellaneousCharge(ByVal Grid As DataGridView)

        ' Stop if no rows were selected.
        If Grid.SelectedRows.Count < 1 Then
            CType(Grid.TopLevelControl, LiquidShell.BaseForm).ShowMessage _
            ("Please select at least one row before using the 'Delete' button.", "Grated Cheese Paradigm")
            Exit Sub
        End If

        ' Confirm the delete action.
        If LiquidShell.LiquidAgent.ActionConfirmed(Grid, "Delete") = False Then
            Exit Sub
        End If

        ' Delete the datarows corresponding to all the selected grid rows.
        For Each SelectedGridRow As DataGridViewRow In Grid.SelectedRows
            Dim RowToDelete As New MiscellaneousCharge(Me, False, ConnectionString)
            ' Log it.
            LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, "Contract Miscellaneous Charge", RowToDelete.RowDescription, "Deleted")
            ' Delete the row.
            CType(SelectedGridRow.DataBoundItem, DataRowView).Row.Delete()
        Next

        ProductionUpdated()

    End Sub

    Public Sub DeleteInvoiceNumber(ByVal Grid As DataGridView)

        ' Stop if no rows were selected.
        If Grid.SelectedRows.Count < 1 Then
            CType(Grid.TopLevelControl, LiquidShell.BaseForm).ShowMessage _
            ("Please select at least one row before using the 'Delete' button.", "Grated Cheese Paradigm")
            Exit Sub
        End If

        ' Confirm the delete action.
        If LiquidShell.LiquidAgent.ActionConfirmed(Grid, "Delete") = False Then
            Exit Sub
        End If

        ' Delete the datarows corresponding to all the selected grid rows.
        For Each SelectedGridRow As DataGridViewRow In Grid.SelectedRows

            ' Delete the row.
            CType(SelectedGridRow.DataBoundItem, DataRowView).Row.Delete()
        Next



    End Sub

    Public Sub DeleteCostEstimateNumber(ByVal Grid As DataGridView)

        ' Stop if no rows were selected.
        If Grid.SelectedRows.Count < 1 Then
            CType(Grid.TopLevelControl, LiquidShell.BaseForm).ShowMessage _
            ("Please select at least one row before using the 'Delete' button.", "Grated Cheese Paradigm")
            Exit Sub
        End If

        ' Confirm the delete action.
        If LiquidShell.LiquidAgent.ActionConfirmed(Grid, "Delete") = False Then
            Exit Sub
        End If

        ' Delete the datarows corresponding to all the selected grid rows.
        For Each SelectedGridRow As DataGridViewRow In Grid.SelectedRows

            ' Delete the row.
            CType(SelectedGridRow.DataBoundItem, DataRowView).Row.Delete()
        Next



    End Sub

    Public Function SignContract() As Boolean
        ' As opposed to simply setting the Signed property to TRUE, this method sets the Signed property
        ' to TRUE and also sets the usernam of the user who signed the contract together with the date
        ' and time of signature.

        ' Sign the contract.
        Row("Signed") = True

        ' Confirm that the user wants to sign the contract.
        Dim MessageText As String = "You are about to sign the selected contract. Once a contract is signed the " _
        & "signature cannot be removed, and some " & vbCrLf & "contract properties will become permanent, preventing " _
        & "any changes in the future." & vbCrLf & vbCrLf & "Are you sure you would like to sign this contract?"
        Dim Response As DialogResult = ConsumingForm.ShowMessage _
        (MessageText, "Are You Sure About This?", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question)
        If Not Response = DialogResult.Yes Then
            ' Reset the contract signature status and exit.
            Row("Signed") = False
            Return False
        End If
        'Update Proposal Heat
        'Row("ContractProposalHeatId") = GetProposalHeat("Hot")

        '' Confirm that the user wants to sign the contract.
        'If Row("isReplacement") And Row("ClonedContractNumber") IsNot Nothing Then
        '    Row("Cancelled") = True
        '    Row("CancelDate") = DateTime.Today
        '    Row("CancelledBy") = My.User.CurrentPrincipal.Identity.Name
        'End If
        'We need to update the contract to be approved if the media type is unapproved media.
        ' Update signature details.
        Row("SignedBy") = My.User.Name
        Row("SignDate") = Settings.GetServerTime(ConnectionString, ConsumingForm)

        'I want to see the media services here
        'ok, we can create a list, go through, and only call the approval if needed.
        Dim onlyUnnaproved As Boolean = True
        Dim listOfMediaServices As New List(Of String)
        Dim words As String() = MediaServices.Split(New Char() {","c})
        Dim word As String
        For Each word In words
            If Not word.ToLower().Contains("unapproved") Then
                onlyUnnaproved = False
            End If

        Next
        If onlyUnnaproved And TotalRental = 0 And TotalProduction = 0 And MiscellaneousCharges = 0 Then
            'This wil check whether it is unapproved media, and then approve the contract. It only approves it if all media are unapproved
            Dim Statement As String = "EXEC dbo.spApproveContractIfUnapprovedMedia '" & ContractID.ToString & "'"

            'A String variable to hold potential errors.
            Dim Errors As String = String.Empty

            ' Run the command on the server.
            Using SqlCon As New SqlClient.SqlConnection(ConnectionString)
                SqlCon.Open()
                LiquidShell.LiquidAgent.RunCommand(SqlCon, Statement, Errors)
                SqlCon.Close()
            End Using
        End If



        Return True

    End Function

    Public Function CancelContract() As Boolean

        ' Pending contracts cannot be cancelled.
        If Signed = False Then
            Dim Message As String = "Only signed contracts may be cancelled. Instead of trying to cancel a pending " _
            & "contract, simply delete it."
            ConsumingForm.ShowMessage(Message, "Let's Not, And Say We Did", MessageBoxIcon.Information)
            Return False
        End If

        ' Check if contract is locked.
        UpdateLockedState()
        If Locked Then
            ' Contract is locked. Cancellation not permitted unless user is authorised by Ops (in the required role).
            If Not My.User.IsInRole("ops_contractmodifier") Then
                ConsumingForm.ShowMessage(ErrorMessage_LockedContractUnchangeable, "Contract Locked", MessageBoxIcon.Error)
                Return False
            End If
        Else
            ' Contract is not locked. Cancellation not permitted if user is in the ops_contractmodifier role.
            If InLockPeriod = False AndAlso My.User.IsInRole("ops_contractmodifier") Then
                ConsumingForm.ShowMessage("You are not permitted to Cancel a contract that isn't locked.", "Contract Not Locked", MessageBoxIcon.Error)
                Return False
            End If
        End If

        ' Confirm that the user wants to cancel the contract.
        Dim MessageText As String = "You are about to cancel the selected contract. This action cannot be undone. " _
        & vbCrLf & vbCrLf & "Are you sure you would like to cancel this contract?"
        Dim Response As DialogResult = ConsumingForm.ShowMessage _
        (MessageText, "Are You Sure About This?", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question)
        If Not Response = DialogResult.Yes Then
            Return False
        End If

        ' Set cancellation details.
        Dim ServerTime As Date = Settings.GetServerTime(ConnectionString, ConsumingForm)
        Row("CancelledBy") = My.User.Name
        Row("CancelDate") = ServerTime
        Row("Cancelled") = True
        Return True

    End Function

#End Region

#Region "Private Methods"

    Protected Overrides Sub UpdateProperties()
        ' Update properties to match the current Row.
        UpdateCustomProperties()
    End Sub

    Private Sub UpdateCustomProperties()
        _BurstBindingSource = New BindingSource(DataBindingSource, "FK_Burst_Contract")
        _PONumberBindingSource = New BindingSource(DataBindingSource, "Contract_PurchaseOrderNumber")
        _ProductionBindingSource = New BindingSource(DataBindingSource, "Contract_ContractInventoryQty")
        _MiscellaneousChargeBindingSource = New BindingSource(DataBindingSource, "Contract_ContractMiscellaneousCharge")
        _MediaCostBindingSource = New BindingSource(DataBindingSource, "Contract_MediaCost")
        _InvoiceNumberBindingSource = New BindingSource(DataBindingSource, "Contract_ContractInvoices")
        _CostEstimateBindingSource = New BindingSource(DataBindingSource, "Contract_ContractCostEstimates")
    End Sub

    Private Sub UpdateBurstInfoColumn(ByVal BurstColumnName As String, ByVal ContractColumnToUpdate As String)
        ' Update the custom column of the datarow of this object to display all the burst data.

        ' A string list to hold the resulting values.
        Dim InfoList As New List(Of String)

        ' Add names to the string list.
        For Each Burst As DataRow In Row.GetChildRows("FK_Burst_Contract")
            ' Check if the list already contains this item.
            If InfoList.Contains(Burst(BurstColumnName)) = False Then
                ' This info isn't in the list. Add it.
                InfoList.Add(Burst(BurstColumnName))
            End If
        Next

        ' A string builder to turn the list into a consecutive string.
        Dim Builder As New System.Text.StringBuilder

        ' Add all list items to the string builder, separating values with a comma.
        For Each Item As String In InfoList
            If Builder.Length > 0 Then
                Builder.Append(", ")
            End If
            Builder.Append(Item)
        Next

        ' Update the row column with the updated list.
        Row(ContractColumnToUpdate) = Builder.ToString

    End Sub

    Private Sub UpdateCategoriesColumn()
        ' Update the 'Categories' column of the datarow of this object to display all the selected categories.

        ' A string list to hold the resulting values.
        Dim InfoList As New List(Of String)

        ' Add names to the string list.
        For Each Burst As DataRow In Row.GetChildRows("FK_Burst_Contract")
            For Each Category As DataRow In Burst.GetChildRows("Burst_BurstCategory")
                Dim Priority As Integer = Category("Priority")
                If Priority <= Burst("CrossoverQty") Then
                    ' This category is not a crossover with a priority that causes it to be excluded from the installation schedule
                    ' (i.e. it's priority is less than the crossover qty required), so it must be included.
                    ' NEW EXCEPTION: If this category is a homesite (i.e. with priority of '0') AND the user has selected to
                    ' NOT install ads at the homesite, it must not be included.
                    Dim InstallAtHomesite As Boolean = Burst("InstallAtHomesite")
                    If Not (Priority = 0 And InstallAtHomesite = False) Then
                        ' Check if the list already contains this item.
                        If InfoList.Contains(Category("CategoryName")) = False Then
                            ' This info isn't in the list. Add it.
                            InfoList.Add(Category("CategoryName"))
                        End If
                    End If
                End If
            Next
        Next

        ' A string builder to turn the list into a consecutive string.
        Dim Builder As New System.Text.StringBuilder

        ' Add all list items to the string builder, separating values with a comma.
        For Each Item As String In InfoList
            If Builder.Length > 0 Then
                Builder.Append(", ")
            End If
            Builder.Append(Item)
        Next

        ' Update the row column with the updated list.
        Row("Categories") = Builder.ToString

    End Sub

    Private Sub UpdateFirstWeekColumn()
        ' Update the 'FirstWeek' column of the datarow of this object to display the earliest FirstWeek of all bursts.

        If Row.GetChildRows("FK_Burst_Contract", DataRowVersion.Default).Length = 0 Then
            ' There are no bursts. FirstWeek must be a null value.
            Row("FirstWeek") = DBNull.Value
            Exit Sub
        End If

        ' A variable to hold the result.
        Dim NewFirstWeek As Date = New Date(2100, 1, 1)

        ' Find the earliest first week of all bursts.
        For Each Burst As DataRow In Row.GetChildRows("FK_Burst_Contract")
            If Date.Compare(Burst("FirstWeek"), NewFirstWeek) < 0 Then
                ' This burst's first week is earlier than NewFirstWeek. Save it.
                NewFirstWeek = Burst("FirstWeek")
            End If
        Next

        ' Update the FirstWeek column with the new value.
        Row("FirstWeek") = NewFirstWeek

    End Sub

    Private Sub UpdateLastWeekColumn()
        ' Update the 'LastWeek' column of the datarow of this object to display the latest LastWeek of all bursts.

        If Row.GetChildRows("FK_Burst_Contract", DataRowVersion.Default).Length = 0 Then
            ' There are no bursts. LastWeek must be a null value.
            Row("LastWeek") = DBNull.Value
            Exit Sub
        End If

        ' A variable to hold the result.
        Dim NewLastWeek As Date = New Date(1000, 1, 1)

        ' Find the earliest Last week of all bursts.
        For Each Burst As DataRow In Row.GetChildRows("FK_Burst_Contract")
            If Date.Compare(Burst("LastWeek"), NewLastWeek) > 0 Then
                ' This burst's Last week is later than NewLastWeek. Save it.
                NewLastWeek = Burst("LastWeek")
            End If
        Next

        ' Update the LastWeek column with the new value.
        Row("LastWeek") = NewLastWeek

    End Sub

    Private Sub UpdateTotalWeeksColumn()
        If BurstsExist Then
            Row("TotalWeeks") = DateDiff(DateInterval.WeekOfYear, Row("FirstWeek"), Row("LastWeek")) + 1
        Else
            Row("TotalWeeks") = 0
        End If
    End Sub

#End Region

#Region "Errors"

    Public Sub RefreshAllErrors()

        ' Get some common data needed by some of the error checking methods.
        ' Get a list of burst objects for the bursts in this contract.
        Dim BurstList As New List(Of Burst)
        For i As Integer = 0 To BurstBindingSource.List.Count - 1
            BurstBindingSource.Position = i
            ' Create a Burst object for this burst.
            BurstList.Add(New Burst(Me, False, ConnectionString))
        Next
        ' Get the contract status.
        Dim contractstatus As ContractStatus = GetContractStatus(ContractID)

        ' Refresh the errors.
        If String.IsNullOrEmpty(_Errors("ContractStatusCheckError")) Then
            ' If anything went wrong with the above code which assigns a value to "contractstatus", then it would've created
            ' a string error message in the _Errors list. Since this error message is empty, the "contractstatus" variable
            ' was successfully assigned a value. Error checking may continue.
            RefreshMediaGapConflictError(contractstatus)
            RefreshAccountManagerClientComboError()
            RefreshCompetingBrandInFirstPlaceError()
            RefreshStoreQtyAllowedByMediaServiceExceededError(BurstList)
            UpdateLockedState()
            RefreshRentalChangedError(contractstatus)
            RefreshUsingTakenStoresError(BurstList)
            RefreshMediaLifeCycleExceededError(BurstList)
            RefreshStorePoolTooSmallError(BurstList)
            'we need to force a user to select a day for interactions before signing if they have an interaction burst
        End If

        RaiseEvent ErrorInfoChanged()

    End Sub

    Private Function GetContractStatus(IDofContract As Guid) As ContractStatus

        ' Delete this error string from the dictionary of errors.
        _Errors.Remove("ContractStatusCheckError")

        ' A string to hold the error.
        Dim errormessage As String = String.Empty

        ' Check the contract status.
        Dim contractstatus As ContractStatus = DataService.SalesServices.GetContractStatus(errormessage, IDofContract)

        If String.IsNullOrEmpty(errormessage) = False Then
            errormessage = ("Unable To Check The Contract Status").ToUpper & vbCrLf _
                & "An attempt to check the status of this contract has failed. The message return by the database was: '" _
                & errormessage & "'." & vbCrLf & "Please try again. If this error continues, please contact technical support."
        End If

        ' Add this error to the dictionary of errors.
        _Errors.Add("ContractStatusCheckError", errormessage)

        Return contractstatus

    End Function

    Private Sub RefreshMediaGapConflictError(contractstatus As ContractStatus)

        ' ONLY check for this error if the user is trying to sign the contract.
        If Signed = False Then
            Return
        End If

        ' Delete this error string from the dictionary of errors.
        _Errors.Remove("MediaGapConflictError")

        ' A string to hold the error.
        Dim ThisError As String = String.Empty

        ' Check if the store quantities requested in this contract can be accommodated after all other signed contracts
        ' have taken the stores they need.
        If contractstatus.ClashingWithOtherContracts Then
            ' There aren't enough stores left in the media gap to give this contract its desired quantity.
            ThisError = ("Media Gap Conflict").ToUpper & vbCrLf _
                & "The number of stores specified in this contract exceeds the number of stores available in the media gap." _
                & vbCrLf & "The number of stores used by all signed contracts in the media gap, plus the quantity specified " _
                & "in this contract, may not exceed the total number of stores available for the selected media service." _
                & vbCrLf & "IMPORTANT: Please close this contract to clear this error before making further changes."
        End If

        ' Add this error to the dictionary of errors.
        _Errors.Add("MediaGapConflictError", ThisError)

    End Sub

    Private Sub RefreshMediaLifeCycleExceededError(BurstList As List(Of Burst))

        ' Delete this error string from the dictionary of errors.
        _Errors.Remove("MediaServiceLifeCycleExceededError")

        ' A string to hold the error.
        Dim ThisError As String = String.Empty

        ' Create a string list to hold descriptions of bursts that have violations.
        Dim ViolatingBurstList As New List(Of String)

        ' Check if the media service of any burst falls outside the life cycle of that media service.
        For Each BurstToCheck As Burst In BurstList
            If Not BurstToCheck.MediaLifeCycleValid Then
                ' This burst's dates fall outside those allowed by the selected media service.
                If ViolatingBurstList.Contains(BurstToCheck.RowDescription) = False Then
                    ViolatingBurstList.Add(BurstToCheck.RowDescription)
                End If
            End If
        Next

        If Not ViolatingBurstList.Count = 0 Then

            ' Create a string with all descriptions of problem bursts.
            Dim ViolatingBursts As New System.Text.StringBuilder
            For Each ViolatingBurst As String In ViolatingBurstList
                If Not ViolatingBursts.Length = 0 Then
                    ViolatingBursts.AppendLine()
                End If
                ViolatingBursts.Append(ViolatingBurst)
            Next

            ' Build the error message.
            ThisError = ("MEDIA SERVICE LIFE CYCLE EXCEEDED").ToUpper & vbCrLf _
            & "The dates selected for the following burst(s) extend beyond the dates allowed by the media service selected. Please either " _
            & "change the dates of the burst(s) or select a different media service." & vbCrLf _
            & ViolatingBursts.ToString

        End If

        ' Add this error to the dictionary of errors.
        _Errors.Add("MediaServiceLifeCycleExceededError", ThisError)

    End Sub

    Private Sub UpdateStoreListMembersInBurstStoreTable(CurrentBurst As Burst)

        ' Create a list to record all stores that were originally in the store list but which are not in the store list
        ' anymore because of changes made to the store universe.
        Dim MissingStoreList As New System.Text.StringBuilder

        ' Update the 'StoreListMember' column.
        For Each StoreListStore As DataRow In CurrentBurst.StoreListTable.Rows
            ' Flag the StoreListMember column of this store in the StoreTable table.
            If Not StoreListStore.RowState = DataRowState.Deleted Then
                If CurrentBurst.StoreTable.Rows.Contains(StoreListStore("StoreID")) Then
                    CurrentBurst.StoreTable.Rows.Find(StoreListStore("StoreID")).Item("StoreListMember") = True
                Else
                    ' Add this store to the list.
                    MissingStoreList.Append(StoreListStore("StoreDescription") & vbCrLf)
                    ' Delete the store from the store list table in the dataset.
                    Dim Key() As Object = {StoreListStore("BurstID"), StoreListStore("StoreID")}
                    Dim RowToDelete As DataRow = DataSet.Tables("StoreList").Rows.Find(Key)
                    'I think we need to double check that it doesn't exist anymore.
                    'ADD DOUBLE CHECKING LOGIC HERE
                    RowToDelete.Delete()
                    ' Delete the store from the StoreListTable table in the CurrentBurst object.
                    StoreListStore.Delete()
                End If
            End If
        Next

        ' Check if any missing stores were found and deleted.
        If MissingStoreList.Length > 0 Then

            ' Missing stores were found and deleted. Save the changes (deleted stores) back to the database.
            Using SqlCon As New SqlClient.SqlConnection(ConnectionString)
                Using StoreListAdapter As New DataSetContractTableAdapters.StoreListTableAdapter
                    StoreListAdapter.Connection = SqlCon
                    StoreListAdapter.Update(DataSet.Tables("StoreList"))
                End Using
            End Using

            ' Display the list of stores which are no longer in the store list to the user so that the user can take whatever
            ' necessary action they want to (like replacing with new stores, for example).
            ConsumingForm.ShowMessage _
                ("The following stores which were selected for your contract have been removed because of changes made to the store " &
                 "universe by the store universe manager." & vbCrLf & vbCrLf & MissingStoreList.ToString, "Removed Stores")

        End If

        CurrentBurst.StoreTable.AcceptChanges()

    End Sub

    Private Sub RefreshUsingTakenStoresError(BurstList As List(Of Burst))

        ' Delete this error string from the dictionary of errors.
        _Errors.Remove("UsingTakenStoresError")

        ' A string to hold the error.
        Dim ThisError As String = String.Empty

        ' Create a string list to hold descriptions of bursts that have store list violations.
        Dim ViolatingBurstList As New List(Of String)

        ' Check if the store lists of any bursts are using stores that have been taken by other bursts.
        For Each BurstToCheck As Burst In BurstList
            ' This error should only apply in cases where the store list has been confirmed.
            If BurstToCheck.StoreListConfirmed Then
                ' Refresh the store table for this burst (the store table lists all the stores in the chain selected
                ' and includes columns for which of those stores are members of the burst's store list, are taken
                ' by other bursts, and are allowed by the media selected by the burst, and which are dormant).
                BurstToCheck.UpdateStoreTable()
                ' Update the StoreListMember column in the store table of the burst.
                UpdateStoreListMembersInBurstStoreTable(BurstToCheck)
                ' Check each row in the StoreTable to see if that store is in the burst's store list but also already
                ' taken by a different burst.
                For Each Store As DataSetContract.StoreRow In BurstToCheck.StoreTable.Rows
                    If Store.StoreListMember AndAlso Store.Taken Then
                        ' This store is a member of the burst's store list, but it has already been taken by a different
                        ' burst. This will result in an installation conflict in the stores and may not be permitted.
                        If ViolatingBurstList.Contains(BurstToCheck.RowDescription) = False Then
                            ViolatingBurstList.Add(BurstToCheck.RowDescription)
                        End If
                    End If
                Next
            End If
        Next

        ' Exit with no errors if no bursts are violating this rule.
        If ViolatingBurstList.Count = 0 Then
            _Errors.Add("UsingTakenStoresError", ThisError)
            Exit Sub
        End If

        ' Create a string with all descriptions of problem bursts.
        Dim ViolatingBursts As New System.Text.StringBuilder
        For Each ViolatingBurst As String In ViolatingBurstList
            If Not ViolatingBursts.Length = 0 Then
                ViolatingBursts.AppendLine()
            End If
            ViolatingBursts.Append(ViolatingBurst)
        Next

        ' Build the error message.
        ThisError = ("STORE LIST VIOLATION").ToUpper & vbCrLf _
        & "The following bursts have stores in their store lists which have already been taken by other contracts. To prevent " _
        & "conflicts in the stores, please remove stores already taken from the store lists of these bursts:" & vbCrLf _
        & ViolatingBursts.ToString

        ' Add this error to the dictionary of errors.
        _Errors.Add("UsingTakenStoresError", ThisError)

    End Sub

    Private Sub RefreshRentalChangedError(contractstatus As ContractStatus)

        ' Delete this error string from the dictionary of errors.
        _Errors.Remove("RentalChangedError")

        ' A string to hold the error.
        Dim ThisError As String = String.Empty
        Dim UserIsAuthorisedToModifySignedContract As Boolean = UserMayModifySignedContract(contractstatus)

        ' Check if the user is trying to change the rental of a signed contract.
        If Signed _
            AndAlso ContractWasNotSignedPriorToThisSession = False _
            AndAlso UserIsAuthorisedToModifySignedContract = False Then
            If Not Decimal.Round(TotalRental, 2) = Decimal.Round(InitialRentalValue, 2) Then
                ' The rental value has changed.
                ThisError = ("Rental May Not Change").ToUpper & vbCrLf _
                & "This contract was signed with a rental value of " & InitialRentalValue.ToString("C2") & ". " _
                & "However, the changes that have been made now cause the rental value to be changed to " & TotalRental.ToString("C2") _
                & "." & vbCrLf & "Changing the rental value of a contract after signature is not permitted."
            End If
        End If

        ' Add this error to the dictionary of errors.
        _Errors.Add("RentalChangedError", ThisError)

    End Sub

    Private Function GetHotProposalHeat(HeatType As String)
        'lets get the heat type
        Dim ErrorMessage As String = String.Empty
        Dim GridData As BindingSource = Lookup.GetProposalHeat(ConnectionString, ErrorMessage, Nothing)



    End Function

    Public Function UserMayModifySignedContract(contractid As Guid) As Boolean
        Return UserMayModifySignedContract(GetContractStatus(contractid))
    End Function

    Private Function UserMayModifySignedContract(contractstatus As ContractStatus) As Boolean

        Dim permissiongranted As Boolean = False
        Dim userhasneededrolemembership = My.User.IsInRole("signedcontractmodifier")

        If userhasneededrolemembership AndAlso contractstatus.ApprovedByFinance = False Then
            permissiongranted = True
        End If

        Return permissiongranted

    End Function

    Private Sub RefreshStorePoolTooSmallError(BurstList As List(Of Burst))

        ' Delete this error string from the dictionary of errors.
        _Errors.Remove("StorePoolTooSmallError")

        ' A string to hold the error.
        Dim ThisError As String = String.Empty

        ' Create a string list to hold descriptions of bursts that have store list violations.
        Dim ViolatingBurstList As New List(Of String)

        ' Check if the store pool of each burst is sufficient to cover the requested installation stores in the burst.
        For Each BurstToCheck As Burst In BurstList
            Dim StorePoolSurplus As Integer = BurstToCheck.StorePoolCapacity - BurstToCheck.InstallStoreQty
            If StorePoolSurplus < 0 Then
                ' Problem. This burst's store pool doesn't provide sufficient capacity.
                ViolatingBurstList.Add(BurstToCheck.RowDescription)
            End If
        Next

        ' Exit with no errors if no bursts are violating this rule.
        If ViolatingBurstList.Count = 0 Then
            _Errors.Add("StorePoolTooSmallError", ThisError)
            Exit Sub
        End If

        ' Create a string with all descriptions of problem bursts.
        Dim ViolatingBursts As New System.Text.StringBuilder
        For Each ViolatingBurst As String In ViolatingBurstList
            If Not ViolatingBursts.Length = 0 Then
                ViolatingBursts.AppendLine()
            End If
            ViolatingBursts.Append(ViolatingBurst)
        Next

        ' Build the error message.
        ThisError = ("STORE POOL TOO SMALL").ToUpper & vbCrLf _
        & "The following bursts have more installation stores than what their store pool allows. To reflect accurate store quantities " _
        & "in the media gap, please adjust the store pool capacities of these bursts:" & vbCrLf _
        & ViolatingBursts.ToString

        ' Add this error to the dictionary of errors.
        _Errors.Add("StorePoolTooSmallError", ThisError)

    End Sub

    Private Sub UpdateLockedState()

        ' Delete this error string from the dictionary of errors.
        _Errors.Remove("ContractLocked")

        ' An unsigned contract cannot be locked.
        If Signed = False Then
            Locked = False
            Exit Sub
        End If

        ' Check if we're in the lock period.
        If InLockPeriod = False Then
            ' This contract is not in the lock period.
            Locked = False
            If LockedWhenOpened.HasValue = False Then
                ' LockedWhenOpened doesn't have a value. This means that this method was originally called
                ' from the constructor and this is a new contract object.
                LockedWhenOpened = False
            End If
            Exit Sub
        Else
            ' This contract is signed and is in the lock period.
            If My.User.IsInRole("ops_contractmodifier") Then
                ' A contract must not be locked for users in the ops_contractmodifier role.
                Locked = False
                Exit Sub
            Else
                Locked = True
                If LockedWhenOpened.HasValue = False Then
                    ' LockedWhenOpened doesn't have a value. This means that this method was originally called
                    ' from the constructor and this is a new contract object.
                    LockedWhenOpened = True
                End If
            End If
        End If

        ' A string to hold the error.
        Dim ThisError As String = String.Empty

        ' Add a value to the error string only if the contract NOT locked when it was first
        ' opened, but it IS locked now.
        If LockedWhenOpened.Value = False Then
            ThisError = ("Contract Locked").ToUpper & vbCrLf _
            & "The operations department has locked this contract because its first week occurs too soon - they need " _
            & "sufficient time to prepare before the contract starts. To avoid this error, please " _
            & "modify the bursts so that the contract starts at a later date than what it does now."
        End If

        ' Add this error to the dictionary of errors.
        _Errors.Add("ContractLocked", ThisError)

    End Sub

    Private Sub RefreshAccountManagerClientComboError()

        ' Delete this error string from the dictionary of errors.
        _Errors.Remove("AccountManagerClientComboError")

        ' Stop if no account manager or client has been selected yet.
        If String.Compare(AccountManagerName, "Select...") = 0 _
        Or String.Compare(ClientName, "Select...") = 0 Then
            Exit Sub
        End If

        ' A string to hold the error.
        Dim ThisError As String = String.Empty





        ' Execution stops here. This error check is temporarily suspended because I need to accommodate the scenario
        ' where one client has two account managers. This error check will be re-enabled in future.
        _Errors.Add("AccountManagerClientComboError", ThisError)
        Exit Sub






        '' Get a binding source containing all the clients of the selected account manager.
        'Dim Clients As BindingSource = Lookup.GetClientsByAccountManager _
        '(ConnectionString, String.Empty, Nothing, AccountManagerID, CreationDate)

        '' Filter the list to display only the selected client.
        'Clients.Filter = "ClientID = " & ClientID.ToString

        '' Check the results of the filter.
        'If Not Clients.List.Count = 1 Then
        '    ' The client doesn't appear in the filtered list. Big problem. Build an error message.
        '    Dim ErrorMessage As String = String.Empty
        '    If Row.RowState = DataRowState.Detached Then
        '        ' This is a new contract being created. Build an error relating to the present time.
        '        ErrorMessage = ClientName & " is not one of " & AccountManagerName & "'s allocated accounts."
        '    Else
        '        ' This contract was created in the past. Build an error relating to the past.
        '        ErrorMessage = ClientName & " was not one of " & AccountManagerName & "'s allocated accounts " _
        '        & "at the time that this contract was created."
        '    End If
        '    ' Save the error in the AccountManagerClientComboError variable.
        '    ThisError = ("Account Manager / Client Mismatch").ToUpper & vbCrLf _
        '    & ErrorMessage & "  To correct this error, go to the 'Summary' page, click on the client name and " _
        '    & "select a valid client from the list, or select the correct account manager for " & ClientName & "."
        'End If

        '' Add this error to the dictionary of errors.
        '_Errors.Add("AccountManagerClientComboError", ThisError)

    End Sub

    Private Sub RefreshCompetingBrandInFirstPlaceError()

        ' Delete this error string from the dictionary of errors.
        _Errors.Remove("CompetingBrandInFirstPlaceError")

        ' A string to hold the error.
        Dim ThisError As String = String.Empty

        ' No error if this is only a proposal.
        If Signed = False Then
            _Errors.Add("CompetingBrandInFirstPlaceError", ThisError)
            Exit Sub
        End If

        ' A string to hold the error.
        Dim DataReadError As String = String.Empty

        ' Get a list of BurstIDs of bursts that need to be checked against possible competing provisional
        ' bookings that are in first place in the queue.
        Dim BurstsToTest As List(Of Guid) = BurstsThatHaveMovedIntoPreviouslyUnoccupiedSpace(DataReadError)

        ' A variable for the table that will contain all the first place provisional booking data.
        Dim FirstPlaceProvisionalBookingTable As DataTable = Nothing

        ' Check all bursts in the contract.
        For Each Burst As DataRow In Row.GetChildRows("FK_Burst_Contract")

            ' Must this burst be checked for competing provisinoal bookings in first place?
            If BurstsToTest.Contains(Burst("BurstID")) Then
                ' Yip. This burst needs to be checked.

                ' Get all data needed for the SQL select statement.
                Dim ChainID As String = Burst("ChainID").ToString
                Dim MediaID As String = Burst("MediaID").ToString
                Dim FirstWeek As String = "'" & LiquidShell.LiquidAgent.GetSqlFriendlyDate(Burst("FirstWeek")) & "'"
                Dim LastWeek As String = "'" & LiquidShell.LiquidAgent.GetSqlFriendlyDate(Burst("LastWeek")) & "'"
                Dim BrandID As String = "'" & Burst("BrandID").ToString & "'"
                Dim CategoryIDListBuilder As New System.Text.StringBuilder
                For Each BurstCategory As DataRow In Burst.GetChildRows("Burst_BurstCategory")
                    ' Build a list of category IDs to use in the select statement.
                    If BurstCategory("Priority") < 1 Then
                        ' This category is not a crossover. It might need to be added it to the list.
                        If Not (BurstCategory("Priority") = 0 AndAlso Burst("InstallAtHomesite") = False) Then
                            ' This category is not the homesite, or it is and the user has chosen to install at the homesite. Add it to the list.
                            If CategoryIDListBuilder.Length > 0 Then
                                CategoryIDListBuilder.Append(",")
                            End If
                            CategoryIDListBuilder.Append(BurstCategory("CategoryID").ToString)
                        End If
                    End If
                Next
                Dim CategoryIDList As String = CategoryIDListBuilder.ToString

                ' Exit without errors if there are no categories in the list.
                If String.IsNullOrEmpty(CategoryIDList) Then
                    _Errors.Add("CompetingBrandInFirstPlaceError", ThisError)
                    Exit Sub
                End If

                ' The SELECT statement to fetch the data.
                Dim SelectStatement As String = "SELECT * FROM udfFirstPlaceBookingsAheadOfBurst" _
                & "(" _
                & ChainID _
                & ",'" & CategoryIDList & "'" _
                & "," & MediaID _
                & "," & FirstWeek _
                & "," & LastWeek _
                & "," & BrandID _
                & ")"

                ' Create a table for first place provisional bookings ahead of this burst.
                Dim EnemyProvisionalBookings As DataTable =
                LiquidShell.LiquidAgent.GetSqlDataTable(ConnectionString, SelectStatement, DataReadError)

                ' Merge the table for this burst with the table that will be used to create the binding source
                ' that will be returned.
                If IsNothing(FirstPlaceProvisionalBookingTable) Then
                    FirstPlaceProvisionalBookingTable = EnemyProvisionalBookings
                Else
                    FirstPlaceProvisionalBookingTable.Merge(EnemyProvisionalBookings)
                    FirstPlaceProvisionalBookingTable.AcceptChanges()
                End If

            End If

        Next

        ' Stop if there were errors while retrieving provisional booking data from the database.
        If String.IsNullOrEmpty(DataReadError) = False Then
            ' Add this error to the dictionary of errors.
            _Errors.Add("CompetingBrandInFirstPlaceError", "-- SYSTEM ERROR OCCURED --" & vbCrLf & DataReadError)
            Exit Sub
        End If

        ' Check the table of competing provisional bookings in first place.
        If IsNothing(FirstPlaceProvisionalBookingTable) Then
            ' No competing provisional bookings are in first place. No errors.
            _Errors.Add("CompetingBrandInFirstPlaceError", ThisError)
            Exit Sub
        End If
        If FirstPlaceProvisionalBookingTable.Rows.Count = 0 Then
            ' No competing provisional bookings are in first place. No errors.
            _Errors.Add("CompetingBrandInFirstPlaceError", ThisError)
            Exit Sub
        End If

        ' Create a string builder to generate details of all conflicting provisional bookings.
        Dim ProvisionalBookingsInFirstPlace As New System.Text.StringBuilder

        ' Add all conflicting provisional booking data to the string builder.
        For Each ProvisionalBooking As DataRow In FirstPlaceProvisionalBookingTable.Rows

            ' Get the values needed for the provisional booking description.
            Dim MediaFamilyName As String = ProvisionalBooking("MediaFamilyName")
            Dim BookTime As Date = ProvisionalBooking("BookTime")
            Dim CategoryName As String = ProvisionalBooking("CategoryName")
            Dim ChainName As String = ProvisionalBooking("ChainName")
            Dim ExpiryTime As Date = ProvisionalBooking("ExpiryTime")
            Dim BrandName As String = ProvisionalBooking("BrandName")

            ' Replace BrandName with a friendly string if confidentialty has resulted in an empty string.
            If String.IsNullOrEmpty(BrandName) Then
                BrandName = "a competing brand"
            End If

            ' Construct the booking description.
            Dim ProvisionalBookingDescription As String = MediaFamilyName & " booked at " _
            & BookTime.ToShortTimeString & " on " _
            & BookTime.ToLongDateString & " in " _
            & CategoryName & " for " _
            & BrandName & " in " _
            & ChainName & "."

            ' Add the description to the string builder.
            If ProvisionalBookingsInFirstPlace.Length > 0 Then
                ProvisionalBookingsInFirstPlace.AppendLine()
            End If
            ProvisionalBookingsInFirstPlace.Append(ProvisionalBookingDescription)

        Next

        ' Save the error in the CompetingBrandInFirstPlaceError variable.
        ThisError = ("Competing Brand in First Place").ToUpper & vbCrLf _
        & "The following provisional bookings are first in the queue for at least one of the weeks that are " _
        & "needed for the bursts in this contract:" & vbCrLf & "   --> " _
        & ProvisionalBookingsInFirstPlace.ToString & vbCrLf & "To correct this error, change the bursts in this " _
        & "contract so that they don't conflict with any existing provisional bookings, or simply wait for the " _
        & "conflicting provisional bookings to expire."

        ' Add this error to the dictionary of errors.
        _Errors.Add("CompetingBrandInFirstPlaceError", ThisError)

    End Sub

    Private Function BurstsThatHaveMovedIntoPreviouslyUnoccupiedSpace _
    (ByRef DataReadError As String) As List(Of Guid)
        ' This function will return a list of IDs of bursts that have been reconfigured to use media
        ' gap space that it previously was not using (e.g. a new Additional Category was added, a new burst was
        ' added, weeks were extended, etc.). This, of course, implies that if the contract was not signed
        ' before and the user is trying to sign it now, all burst IDs will be returned because all of them need
        ' to be checked for media gap violations. But if the user is not trying to sign the contract now, no
        ' IDs will be returned.

        ' Create the list that will be returned.
        Dim BurstIDList As New List(Of Guid)

        ' Check if the user is trying to sign an unsigned contract.
        If ContractWasNotSignedPriorToThisSession Then
            ' The contract was not signed before.
            If Signed = False Then
                ' User is not trying to sign it now. It's not necessary to test for media gap violations.
                Return BurstIDList
            Else
                ' This is an unsigned contract that the user is trying to sign, check all bursts if they have more than zero stores.
                For Each Burst As DataSetContract.BurstRow In Row.GetChildRows("FK_Burst_Contract")
                    If Burst.InstallStoreQty > 0 Then
                        ' This burst requires at least one store installation. It must be tested.
                        BurstIDList.Add(Burst.BurstID)
                    End If
                Next
                Return BurstIDList
            End If
        Else
            ' The contract was signed when the user opened it during this session.
            If IsDirty = False Then
                ' The contract is not dirty, so no changes to any bursts could've been saved. No need to check them.
                Return BurstIDList
            End If
        End If


        ' If execution hasn't returned at this point then this is a signed contract whose bursts have possibly
        ' been modified. We'll need to check which bursts have been modified so that they can be added to the
        ' list so that we can check whether their new changes will result in any media gap violations.

        ' Get a copy of the MediaFamilyMember table so that we can compare affected media families later.
        Dim SelectStatement As String = "SELECT MediaID, MediaFamilyID FROM Media.MediaFamilyMember"
        Dim MediaFamilyMemberTable As DataTable = LiquidShell.LiquidAgent.GetSqlDataTable(ConnectionString, SelectStatement, DataReadError)

        ' Stop if errors occured while fetching media family member data.
        If String.IsNullOrEmpty(DataReadError) = False Then
            ' Simply return the empty list. When the calling method sees that there are errors in the DataReadError string, it
            ' will be handled.
            MediaFamilyMemberTable.Dispose()
            Return BurstIDList
        End If

        ' Check each burst against the snapshot of the dataset that was taken when this contract was opened.
        For Each Burst As DataSetContract.BurstRow In Row.GetChildRows("FK_Burst_Contract")
            If Burst.InstallStoreQty > 0 Then
                ' This burst requires at least one store installation. It must be tested.
                CheckBurstForChangesRequiringMediaGapCheck(Burst, BurstIDList, MediaFamilyMemberTable)
            End If
        Next

        MediaFamilyMemberTable.Dispose()
        Return BurstIDList

    End Function

    Private Sub CheckBurstForChangesRequiringMediaGapCheck _
    (ByVal BurstToCheck As DataSetContract.BurstRow,
    ByRef BurstIDList As List(Of Guid),
    ByVal MediaFamilyMemberTable As DataTable)
        ' This method takes a list of burst IDs, one burst (a datarow) and does some checks on the burst. If the burst
        ' is found to occupy space in the media gap that it never did before, then it adds the burst's ID to the list.
        ' The list will be used later to check for first place competing provisional bookings that might prevent this
        ' contract from being saved or signed.

        ' Get the ID of this burst.
        Dim BurstID As Guid = BurstToCheck.BurstID

        ' If this is a new burst that was added, it must be checked.
        If BurstToCheck.RowState = DataRowState.Added Then
            BurstIDList.Add(BurstID)
            Exit Sub
        End If

        ' If this burst has been changed to use a media service belonging to any new media family, it must be checked.
        ' ------------------------------------------------------------------------------------------------------------

        ' Get the original version of the burst from the dataset snapshot.
        Dim OriginalBurst As DataSetContract.BurstRow = DataSetSnapShot.Tables("Burst").Rows.Find(BurstID)

        ' Get the originally selected media service.
        Dim OriginalMediaID As Integer = OriginalBurst.MediaID

        ' Get the new media ID.
        Dim NewMediaID As Integer = BurstToCheck.MediaID

        ' Compare the old and the new media services.
        If Not NewMediaID = OriginalMediaID Then
            ' The media service was changed. Check if any new media families come into play because of this change.

            ' Get the media family member rows for the original media service.
            Dim OldMediaFamilyMemberRows() As DataRow = MediaFamilyMemberTable.Select("MediaID = " & OriginalMediaID.ToString)
            Dim OldMediaFamilyIDList As New List(Of Integer)
            For Each MediaFamilyMember As DataRow In OldMediaFamilyMemberRows
                OldMediaFamilyIDList.Add(MediaFamilyMember("MediaFamilyID"))
            Next

            ' Get the media family member rows for the new media service.
            Dim NewMediaFamilyMemberRows() As DataRow = MediaFamilyMemberTable.Select("MediaID = " & NewMediaID.ToString)

            ' Check each of the new media family member rows to see if the media family also appears in the
            ' media family list of the original media service.
            For Each NewMediaFamilyMember As DataRow In NewMediaFamilyMemberRows
                If Not OldMediaFamilyIDList.Contains(NewMediaFamilyMember("MediaFamilyID")) Then
                    ' This is a new media family and hence a new media gap that is affected. This burst must be tested.
                    BurstIDList.Add(BurstID)
                    Exit Sub
                End If
            Next

        End If

        ' If the categories selected for this burst has changed, it must be checked.
        ' --------------------------------------------------------------------------

        ' Get a list of the original categories used.
        Dim OldCategoryIDList As New List(Of Integer)
        For Each BurstCategory As DataSetContract.BurstCategoryRow In OriginalBurst.GetChildRows("Burst_BurstCategory")
            If BurstCategory.Priority < 1 Then
                ' This category is not a crossover. Add it to the list.
                OldCategoryIDList.Add(BurstCategory.CategoryID)
            End If
        Next

        ' Check if all the current categories were used before.
        For Each BurstCategory As DataSetContract.BurstCategoryRow In BurstToCheck.GetChildRows("Burst_BurstCategory")
            If BurstCategory.Priority < 1 Then
                ' This category is not a crossover. Check if it was used before.
                If Not OldCategoryIDList.Contains(BurstCategory.CategoryID) Then
                    ' This is a new category. This burst must be check for media gap violations.
                    BurstIDList.Add(BurstID)
                    Exit Sub
                End If
            End If
        Next

        ' Check if the burst has been changed to use a different brand than before.
        ' -------------------------------------------------------------------------

        If Not Object.Equals(OriginalBurst.BrandID, BurstToCheck.BrandID) Then
            ' The brand has been changed. This burst must be checked.
            BurstIDList.Add(BurstID)
            Exit Sub
        End If

        ' Check if the burst has been changed to use a different chain than before.
        ' -------------------------------------------------------------------------

        If Not Object.Equals(OriginalBurst.ChainID, BurstToCheck.ChainID) Then
            ' The chain has been changed. This burst must be checked.
            BurstIDList.Add(BurstID)
            Exit Sub
        End If

        ' Check if the burst uses different weeks than originally specified.
        ' ------------------------------------------------------------------

        ' Check the first week.
        If BurstToCheck.FirstWeek < OriginalBurst.FirstWeek Or BurstToCheck.LastWeek > OriginalBurst.LastWeek Then
            ' The first week is sooner than before. Check this burst for violations.
            BurstIDList.Add(BurstID)
            Exit Sub
        End If

    End Sub

    Private Sub RefreshStoreQtyAllowedByMediaServiceExceededError(BurstList As List(Of Burst))

        ' Delete this error string from the dictionary of errors.
        _Errors.Remove("StoreQtyAllowedByMediaServiceExceededError")

        ' A string to hold the error.
        Dim ThisError As String = String.Empty

        ' A string to hold all the problem bursts.
        Dim ErrorBursts As New System.Text.StringBuilder

        ' Check all bursts for store quantities exceeding limit imposed by selected media service.
        For Each TestBurst As Burst In BurstList
            ' Check if the install store quantity is within the limit imposed by the media / chain combination.
            If Signed AndAlso TestBurst.StoresAllowingCurrentMediaService < TestBurst.InstallStoreQty Then
                ' The limit has been exceeded. List this burst as an error.
                If ErrorBursts.Length > 0 Then
                    ErrorBursts.AppendLine()
                End If
                ErrorBursts.Append(TestBurst.RowDescription)
            End If
        Next

        ' Check if any bursts had errors.
        If ErrorBursts.Length > 0 Then
            ' At least one burst had an error.
            ThisError = ("Store quantity allowed by media service exceeded").ToUpper & vbCrLf _
            & "At least one burst in your contract uses too many stores. " _
            & "Please edit the following problem bursts to correct this error: " & vbCrLf _
            & ErrorBursts.ToString
        End If

        ' Add this error to the dictionary of errors.
        _Errors.Add("StoreQtyAllowedByMediaServiceExceededError", ThisError)

    End Sub

#End Region

End Class
