﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformWelcome
    Inherits Subform

    'UserControl overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformWelcome))
        Me.LabelControlHeadingNews = New DevExpress.XtraEditors.LabelControl
        Me.LabelControlNewsContent = New DevExpress.XtraEditors.LabelControl
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl
        Me.PanelControl1 = New DevExpress.XtraEditors.PanelControl
        Me.PictureBoxIcon = New System.Windows.Forms.PictureBox
        CType(Me.PanelControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelControl1.SuspendLayout()
        CType(Me.PictureBoxIcon, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 12.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Appearance.Options.UseFont = True
        Me.LabelTitle.Appearance.Options.UseForeColor = True
        Me.LabelTitle.Size = New System.Drawing.Size(464, 19)
        Me.LabelTitle.Text = "WELCOME TO THE PRIMEDIA INSTORE NOVA DATABASE SYSTEM"
        '
        'LabelControlHeadingNews
        '
        Me.LabelControlHeadingNews.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControlHeadingNews.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControlHeadingNews.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelControlHeadingNews.Appearance.Options.UseFont = True
        Me.LabelControlHeadingNews.Appearance.Options.UseForeColor = True
        Me.LabelControlHeadingNews.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControlHeadingNews.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelControlHeadingNews.LineVisible = True
        Me.LabelControlHeadingNews.Location = New System.Drawing.Point(12, 49)
        Me.LabelControlHeadingNews.Margin = New System.Windows.Forms.Padding(3, 15, 3, 12)
        Me.LabelControlHeadingNews.Name = "LabelControlHeadingNews"
        Me.LabelControlHeadingNews.Size = New System.Drawing.Size(663, 18)
        Me.LabelControlHeadingNews.TabIndex = 0
        Me.LabelControlHeadingNews.Text = "News && Announcements"
        '
        'LabelControlNewsContent
        '
        Me.LabelControlNewsContent.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControlNewsContent.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControlNewsContent.Appearance.Options.UseFont = True
        Me.LabelControlNewsContent.Appearance.Options.UseForeColor = True
        Me.LabelControlNewsContent.Appearance.Options.UseTextOptions = True
        Me.LabelControlNewsContent.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top
        Me.LabelControlNewsContent.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap
        Me.LabelControlNewsContent.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControlNewsContent.Dock = System.Windows.Forms.DockStyle.Fill
        Me.LabelControlNewsContent.Location = New System.Drawing.Point(0, 0)
        Me.LabelControlNewsContent.Name = "LabelControlNewsContent"
        Me.LabelControlNewsContent.Size = New System.Drawing.Size(663, 449)
        Me.LabelControlNewsContent.TabIndex = 2
        Me.LabelControlNewsContent.Text = resources.GetString("LabelControlNewsContent.Text")
        '
        'LabelControl1
        '
        Me.LabelControl1.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl1.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelControl1.Appearance.Options.UseFont = True
        Me.LabelControl1.Appearance.Options.UseForeColor = True
        Me.LabelControl1.Appearance.Options.UseTextOptions = True
        Me.LabelControl1.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.LabelControl1.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl1.Location = New System.Drawing.Point(440, 51)
        Me.LabelControl1.Margin = New System.Windows.Forms.Padding(3, 15, 3, 3)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(235, 13)
        Me.LabelControl1.TabIndex = 3
        Me.LabelControl1.Text = "Updated 26-03-2010"
        '
        'PanelControl1
        '
        Me.PanelControl1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelControl1.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PanelControl1.Appearance.Options.UseBackColor = True
        Me.PanelControl1.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PanelControl1.Controls.Add(Me.LabelControlNewsContent)
        Me.PanelControl1.Location = New System.Drawing.Point(12, 82)
        Me.PanelControl1.LookAndFeel.SkinName = "Black"
        Me.PanelControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.PanelControl1.Name = "PanelControl1"
        Me.PanelControl1.Size = New System.Drawing.Size(663, 449)
        Me.PanelControl1.TabIndex = 4
        '
        'PictureBoxIcon
        '
        Me.PictureBoxIcon.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureBoxIcon.BackColor = System.Drawing.Color.Transparent
        Me.PictureBoxIcon.Image = Global.Nova2.My.Resources.Resources.news128
        Me.PictureBoxIcon.Location = New System.Drawing.Point(681, 12)
        Me.PictureBoxIcon.Name = "PictureBoxIcon"
        Me.PictureBoxIcon.Size = New System.Drawing.Size(128, 128)
        Me.PictureBoxIcon.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.PictureBoxIcon.TabIndex = 26
        Me.PictureBoxIcon.TabStop = False
        '
        'SubformWelcome
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.Controls.Add(Me.PictureBoxIcon)
        Me.Controls.Add(Me.PanelControl1)
        Me.Controls.Add(Me.LabelControl1)
        Me.Controls.Add(Me.LabelControlHeadingNews)
        Me.Name = "SubformWelcome"
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        Me.Controls.SetChildIndex(Me.LabelControlHeadingNews, 0)
        Me.Controls.SetChildIndex(Me.LabelControl1, 0)
        Me.Controls.SetChildIndex(Me.PanelControl1, 0)
        Me.Controls.SetChildIndex(Me.PictureBoxIcon, 0)
        CType(Me.PanelControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelControl1.ResumeLayout(False)
        CType(Me.PictureBoxIcon, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents LabelControlHeadingNews As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControlNewsContent As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents PanelControl1 As DevExpress.XtraEditors.PanelControl
    Friend WithEvents PictureBoxIcon As System.Windows.Forms.PictureBox

End Class
