﻿Public Class SubformChainGroupManager

    Dim GridBindingSource As BindingSource
    Dim DataSet As DataSet

    Public Sub New()

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        '' Add any initialization after the InitializeComponent() call.
        'GridController = New GridManager _
        '(GridItems,
        ''',
        'Category.GetListData(My.Settings.DBConnection, String.Empty, Nothing),
        'My.Settings.DBConnection)
        ''PictureAdvancedSearch,
        ''PictureClearSearch,
        ''ButtonEdit,
        ''ButtonDelete
        ')

    End Sub

    Private Sub SubformChainGroupManager_Load(sender As Object, e As EventArgs) Handles MyBase.Load

    End Sub
End Class
