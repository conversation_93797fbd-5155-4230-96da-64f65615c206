Imports System.Data.SqlClient
Imports System.ComponentModel

Public Class FormLogin

    Public ConnectionString As String
    Private Builder As SqlConnectionStringBuilder
    Private NewPrincipal As SqlPrincipal
    Private Username As String
    Private Password As String
    Dim ServerAvailable As Boolean = False

    Public Sub New(ByVal AppName As String, ByRef ConString As String)
        ' This call is required by the Windows Form Designer.
        InitializeComponent()
        ' Add any initialization after the InitializeComponent() call.
        ConnectionString = ConString
        Text = AppName & " Login"
        ' Set connection option control values.
        Builder = New SqlConnectionStringBuilder(ConnectionString)
        TextEditServerAddress.EditValue = Builder.DataSource
        TextEditDatabaseName.EditValue = Builder.InitialCatalog
        ToggleFormHeight()
    End Sub

    Private Sub CreateSqlPrincipal()

        ' Check if the server is available.
        ServerAvailable = ServerIsAvailable()

        ' Create a new principal using the supplied username and password.
        Username = Trim(TextBoxUsername.Text)
        Password = Trim(TextBoxPassword.Text)
        NewPrincipal = New SqlPrincipal _
        (Trim(TextEditServerAddress.Text), Trim(TextEditDatabaseName.Text), Username, Password)

    End Sub

    Private Sub ButtonLogin_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonLogin.Click

        ' Attempt user authentication.
        RunInBackground(AddressOf CreateSqlPrincipal)

        ' Check for server availability.
        If CheckEditTestNetwork.Checked Then
            If ServerAvailable = False Then
                ShowMessage("The server cannot be reached.", "Error")
                TextBoxPassword.Text = ""
                TextBoxUsername.Focus()
                TextBoxUsername.SelectAll()
                Exit Sub
            End If
        End If

        ' Check if authentication succeeded.
        If IsNothing(NewPrincipal) = False AndAlso NewPrincipal.Identity.IsAuthenticated Then
            ' Authentication succeeded. Make authenticate principal the current principal.
            My.User.CurrentPrincipal = NewPrincipal
            ' Update the connection string with the authenticated password, username, server and database name.
            Builder.UserID = Username
            Builder.Password = Password
            Builder.ConnectTimeout = 0
            Builder.DataSource = Trim(TextEditServerAddress.Text)
            Builder.InitialCatalog = Trim(TextEditDatabaseName.Text)
            ConnectionString = Builder.ConnectionString
            ' Check if the user needs to change their password and allow them to do so if they want to.
            ChangePasswordIfNecessary()
            ' Let's get outta here.
            DialogResult = Windows.Forms.DialogResult.OK
            Me.Close()
        Else
            ' Authentication failed.
            TextBoxPassword.Focus()
            TextBoxPassword.SelectAll()
            LiquidAgent.ControlValidation _
            (TextBoxPassword, "Authentication failed. Incorrect username / password combination provided.")
        End If

    End Sub

    Private Sub ChangePasswordIfNecessary()
        ' Check if the user's password will be expiring soon. If so, ask if they want to change their password now.

        Dim DaysLeft As Integer = LiquidAgent.GetDaysBeforePasswordExpiration(ConnectionString, Me)

        If DaysLeft < 6 Then
            Dim DaysLeftString As String = String.Empty
            If DaysLeft = 1 Then
                DaysLeftString = DaysLeft.ToString & " day."
            Else
                DaysLeftString = DaysLeft.ToString & " days."
            End If
            Dim ChangePasswordResponse As DialogResult = ShowMessage("Your password will expire in " & DaysLeftString & vbCrLf _
                        & "Do you want to change your password now?", "Password Expiration", MessageBoxButtons.YesNo, MessageBoxIcon.Question)

            If ChangePasswordResponse = Windows.Forms.DialogResult.Yes Then
                ' Change the users password now.
                FormChangePassword.ChangePassword(ConnectionString, Icon, True)
            End If

        End If

    End Sub

    Private Function ServerIsAvailable() As Boolean

        ' Check the connection string.
        If String.IsNullOrEmpty(ConnectionString) Then
            ' No string provided.
            Return True
        End If

        ' Check for a working network connection.
        If Not My.Computer.Network.IsAvailable Then
            ' No network is available.
            Return False
        End If

        ' Check if there's a working network connection to the SQL server.
        Dim Builder As New SqlConnectionStringBuilder(ConnectionString)
        Try
            Return My.Computer.Network.Ping(Builder.DataSource)
        Catch ex As System.Net.NetworkInformation.PingException
            Return False
        End Try

    End Function

    Private Sub TextBoxUsername_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles TextBoxUsername.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If ValidatedControl.Text.Length = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "A username is required in order to login.")
        ElseIf ValidatedControl.Text.IndexOf(" ") > -1 Then
            LiquidAgent.ControlValidation(ValidatedControl, "No spaces allowed.")
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub TextBox_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles TextBoxPassword.EditValueChanged, TextBoxUsername.EditValueChanged

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)
        LiquidAgent.ControlValidation(ValidatedControl, String.Empty)

        ' Reset the password text box if the username text box is modified.
        If ValidatedControl.Name Is "TextBoxUsername" Then
            LiquidAgent.ControlValidation(TextBoxPassword, String.Empty)
        End If

    End Sub

    Private Sub ToggleFormHeight()
        ' Toggle the form height to show or hide the optional controls.

        If Height > 146 Then
            Height = 146
            HyperlinkOptions.Text = "Show connection options"
        Else
            Height = 267
            HyperlinkOptions.Text = "Hide connection options"
        End If

    End Sub

    Private Sub HyperlinkOptions_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles HyperlinkOptions.Click
        ToggleFormHeight()
    End Sub

End Class


