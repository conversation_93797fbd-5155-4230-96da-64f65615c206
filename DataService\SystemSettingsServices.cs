﻿using DataAccess;
using DataService.StoreUniverse;
using System;
using System.Data;

namespace DataService
{
    public static class SystemSettingsServices
    {

        public static DataTable GetTableOfSystemSettings(ref string errormessage)
        {
            string connectionstring = Universal.Settings.CurrentSession.ConnectionString;
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, connectionstring))
            {
                var command = new GetTableOfSystemSettingsCommand(Universal.Settings.CurrentSession.SessionId);
                manager.ExecuteCommand(ref errormessage, command, new GetTableOfSystemSettingsCommandExecutor());
                return command.Table;
            }
        }

        public static void UpdateSystemSetting(ref string errormessage, Guid settingid, string settingvalue)
        {
            string connectionstring = Universal.Settings.CurrentSession.ConnectionString;
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, connectionstring))
            {
                var command = new UpdateSystemSettingCommand(Universal.Settings.CurrentSession.SessionId, settingid, settingvalue);
                manager.ExecuteCommand(ref errormessage, command, new UpdateSystemSettingCommandExecutor());
            }
        }

    }
}
