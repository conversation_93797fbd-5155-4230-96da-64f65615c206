﻿using DataAccess;
using System.Data;

namespace DataService.StoreUniverse
{
    class GetTableOfSystemSettingsCommandExecutor : CommandExecutor<GetTableOfSystemSettingsCommand>
    {
        public override void Execute(GetTableOfSystemSettingsCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.GetSystemSettingsTable))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    command.Table = storedprocedure.OutputTable;
                    command.Table.PrimaryKey = new DataColumn[] { command.Table.Columns[0] };
                }
            }
        }
    }
}
