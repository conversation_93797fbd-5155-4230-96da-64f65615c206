<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DataSetContract" targetNamespace="http://tempuri.org/DataSetContract.xsd" xmlns:mstns="http://tempuri.org/DataSetContract.xsd" xmlns="http://tempuri.org/DataSetContract.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="2" FunctionsComponentName="QueriesTableAdapter" GeneratorFunctionsComponentClassName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" UserFunctionsComponentName="QueriesTableAdapter" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="DBConnection" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Public" Name="DBConnection (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.NovaData.My.MySettings.GlobalReference.Default.DBConnection" Provider="System.Data.SqlClient" />
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="NovaDBConnectionString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="NovaDBConnectionString (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.NovaData.My.MySettings.GlobalReference.Default.NovaDBConnectionString" Provider="System.Data.SqlClient" />
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="NovaDBConnectionString1" IsAppSettingsProperty="true" Modifier="Assembly" Name="NovaDBConnectionString1 (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.NovaData.My.MySettings.GlobalReference.Default.NovaDBConnectionString1" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ContractTableAdapter" GeneratorDataComponentClassName="ContractTableAdapter" Name="Contract" UserDataComponentName="ContractTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      DELETE FROM Sales.Contract
                      WHERE        (ContractID = @Original_ContractID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_ContractID" ColumnName="ContractID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@Original_ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO Sales.Contract
                  (ContractID, AccountManagerID, ClientID, ClientName, ClientBillingAddress, ContractNumber, Signed, SignDate, SignedBy, SpecialConditions, ProjectName, ApplyAgencyComm, AgencyID, AgencyName, AgencyCommPercentage, 
                  Cancelled, CancelDate, CancelledBy, BillingInstructions, ContractNotes, ContractDate, ContractType, RollForward, AddedValue, AgencyCommIsPercentageOfNetRental, PrintAgencyComm, ContractClassificationId, 
                  ContractProposalHeatId, isReplacement)
VALUES (@ContractID,@AccountManagerID,@ClientID,@ClientName,@ClientBillingAddress,@ContractNumber,@Signed,@SignDate,@SignedBy,@SpecialConditions,@ProjectName,@ApplyAgencyComm,@AgencyID,@AgencyName,@AgencyCommPercentage,@Cancelled,@CancelDate,@CancelledBy,@BillingInstructions,@ContractNotes,@ContractDate,@ContractType,@RollForward,@AddedValue,@AgencyCommIsPercentageOfNetRental,@PrintAgencyComm,@ContractClassificationId,@ContractProposalHeatId,@isReplacement)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="AccountManagerID" ColumnName="AccountManagerID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ClientID" ColumnName="ClientID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ClientID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ClientID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ClientName" ColumnName="ClientName" DataSourceName="" DataTypeServer="nvarchar(250)" DbType="String" Direction="Input" ParameterName="@ClientName" Precision="0" ProviderType="NVarChar" Scale="0" Size="250" SourceColumn="ClientName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ClientBillingAddress" ColumnName="ClientBillingAddress" DataSourceName="" DataTypeServer="nvarchar(1000)" DbType="String" Direction="Input" ParameterName="@ClientBillingAddress" Precision="0" ProviderType="NVarChar" Scale="0" Size="1000" SourceColumn="ClientBillingAddress" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractNumber" ColumnName="ContractNumber" DataSourceName="" DataTypeServer="nvarchar(8)" DbType="String" Direction="Input" ParameterName="@ContractNumber" Precision="0" ProviderType="NVarChar" Scale="0" Size="8" SourceColumn="ContractNumber" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Signed" ColumnName="Signed" DataSourceName="" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@Signed" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="Signed" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="SignDate" ColumnName="SignDate" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@SignDate" Precision="0" ProviderType="DateTime" Scale="0" Size="8" SourceColumn="SignDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="SignedBy" ColumnName="SignedBy" DataSourceName="" DataTypeServer="nvarchar(50)" DbType="String" Direction="Input" ParameterName="@SignedBy" Precision="0" ProviderType="NVarChar" Scale="0" Size="50" SourceColumn="SignedBy" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="SpecialConditions" ColumnName="SpecialConditions" DataSourceName="" DataTypeServer="varchar(2000)" DbType="AnsiString" Direction="Input" ParameterName="@SpecialConditions" Precision="0" ProviderType="VarChar" Scale="0" Size="2000" SourceColumn="SpecialConditions" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ProjectName" ColumnName="ProjectName" DataSourceName="" DataTypeServer="nvarchar(250)" DbType="String" Direction="Input" ParameterName="@ProjectName" Precision="0" ProviderType="NVarChar" Scale="0" Size="250" SourceColumn="ProjectName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ApplyAgencyComm" ColumnName="ApplyAgencyComm" DataSourceName="" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@ApplyAgencyComm" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="ApplyAgencyComm" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="AgencyID" ColumnName="AgencyID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@AgencyID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="AgencyID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="AgencyName" ColumnName="AgencyName" DataSourceName="" DataTypeServer="nvarchar(250)" DbType="String" Direction="Input" ParameterName="@AgencyName" Precision="0" ProviderType="NVarChar" Scale="0" Size="250" SourceColumn="AgencyName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="AgencyCommPercentage" ColumnName="AgencyCommPercentage" DataSourceName="" DataTypeServer="decimal(5, 2)" DbType="Decimal" Direction="Input" ParameterName="@AgencyCommPercentage" Precision="5" ProviderType="Decimal" Scale="2" Size="5" SourceColumn="AgencyCommPercentage" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Cancelled" ColumnName="Cancelled" DataSourceName="" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@Cancelled" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="Cancelled" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="CancelDate" ColumnName="CancelDate" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@CancelDate" Precision="0" ProviderType="DateTime" Scale="0" Size="8" SourceColumn="CancelDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="CancelledBy" ColumnName="CancelledBy" DataSourceName="" DataTypeServer="nvarchar(50)" DbType="String" Direction="Input" ParameterName="@CancelledBy" Precision="0" ProviderType="NVarChar" Scale="0" Size="50" SourceColumn="CancelledBy" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="BillingInstructions" ColumnName="BillingInstructions" DataSourceName="" DataTypeServer="varchar(2000)" DbType="AnsiString" Direction="Input" ParameterName="@BillingInstructions" Precision="0" ProviderType="VarChar" Scale="0" Size="2000" SourceColumn="BillingInstructions" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractNotes" ColumnName="ContractNotes" DataSourceName="" DataTypeServer="varchar(2000)" DbType="AnsiString" Direction="Input" ParameterName="@ContractNotes" Precision="0" ProviderType="VarChar" Scale="0" Size="2000" SourceColumn="ContractNotes" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="ContractDate" ColumnName="ContractDate" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@ContractDate" Precision="0" ProviderType="DateTime" Scale="0" Size="8" SourceColumn="ContractDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractType" ColumnName="ContractType" DataSourceName="" DataTypeServer="nvarchar(50)" DbType="String" Direction="Input" ParameterName="@ContractType" Precision="0" ProviderType="NVarChar" Scale="0" Size="50" SourceColumn="ContractType" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="RollForward" ColumnName="RollForward" DataSourceName="" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@RollForward" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="RollForward" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="AddedValue" ColumnName="AddedValue" DataSourceName="" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@AddedValue" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="AddedValue" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="AgencyCommIsPercentageOfNetRental" ColumnName="AgencyCommIsPercentageOfNetRental" DataSourceName="" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@AgencyCommIsPercentageOfNetRental" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="AgencyCommIsPercentageOfNetRental" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="PrintAgencyComm" ColumnName="PrintAgencyComm" DataSourceName="" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@PrintAgencyComm" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="PrintAgencyComm" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="ContractClassificationId" ColumnName="ContractClassificationId" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ContractClassificationId" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ContractClassificationId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="ContractProposalHeatId" ColumnName="ContractProposalHeatId" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ContractProposalHeatId" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ContractProposalHeatId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="isReplacement" ColumnName="isReplacement" DataSourceName="" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@isReplacement" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="isReplacement" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT Sales.Contract.ContractID, Sales.Contract.AccountManagerID, Sales.Contract.ClientID, Sales.Contract.ClientName, Sales.Contract.ClientBillingAddress, Sales.Contract.ContractNumber, Sales.Contract.Signed, Sales.Contract.SignDate, 
                  Sales.Contract.SignedBy, Sales.Contract.SpecialConditions, Sales.Contract.ProjectName, Sales.Contract.Cancelled, Sales.Contract.CancelDate, Sales.Contract.CancelledBy, Sales.Contract.CreatedBy, Sales.Contract.CreationDate, 
                  dtBurstInfo.Brands, dtBurstInfo.Chains, dtBurstInfo.MediaServices, dtBurstInfo.Categories, dtBurstInfo.ResearchCategories, dtContractDates.FirstWeek, dtContractDates.LastWeek, DATEDIFF(wk, dtContractDates.FirstWeek, 
                  dtContractDates.LastWeek) + 1 AS TotalWeeks, Sales.Contract.AgencyID, Sales.Contract.AgencyName, Sales.Contract.AgencyCommPercentage, Sales.Contract.ApplyAgencyComm, Sales.Contract.BillingInstructions, 
                  Sales.Contract.ContractNotes, dtHistoryStatus.History, Sales.Contract.ContractDate, Sales.Contract.ContractType, Sales.AccountManager.FirstName + N' ' + Sales.AccountManager.LastName AS AccountManagerName, 
                  Sales.AccountManager.Code, Sales.Contract.RollForward, Sales.Contract.AddedValue, Sales.Contract.AgencyCommIsPercentageOfNetRental, Sales.Contract.PrintAgencyComm, ISNULL(Sales.ContractApproved.Approved, 0) 
                  AS Approved, dbo.udfGetClientAccountManager(Sales.Contract.ClientID, Sales.Contract.CreationDate) AS ClientAccountManager, ISNULL
                      ((SELECT TOP (1) Classification
                        FROM      Sales.ContractClassification AS cs
                        WHERE   (ContractClassificationId = Sales.Contract.ContractClassificationId)), 'Choose Classification') AS Classification, ISNULL
                      ((SELECT TOP (1) ContractProposalHeatName
                        FROM      Sales.ContractProposalHeat AS cs
                        WHERE   (ContractProposalHeatId = Sales.Contract.ContractProposalHeatId)), 'Choose Proposal Heat') AS ContractProposalHeatName, Sales.Contract.ContractProposalHeatId, ISNULL(Sales.Contract.isReplacement, 0) 
                  AS isReplacement, ISNULL(Sales.Contract.ClonedContractNumber, 'None') AS ClonedContractNumber, Sales.Contract.DemoProvider, Sales.Contract.DemoOwner, Sales.Contract.ContractClassificationId
FROM     Sales.Contract INNER JOIN
                  Sales.vVisibleContractList ON Sales.Contract.ContractID = Sales.vVisibleContractList.ContractID INNER JOIN
                      (SELECT Contract_1.ContractID, CONVERT(bit, 0) AS History
                       FROM      Sales.Contract AS Contract_1 LEFT OUTER JOIN
                                         Sales.vContractDates ON Contract_1.ContractID = Sales.vContractDates.ContractID
                       WHERE   (Sales.vContractDates.ContractID IS NULL)
                       UNION ALL
                       SELECT ContractID, CONVERT(bit, 0) AS History
                       FROM     Sales.vContractDates AS vContractDates_2
                       WHERE  (DATEDIFF(wk, GETDATE(), LastInstallWeek) &gt;= 0)
                       UNION ALL
                       SELECT ContractID, CONVERT(bit, 1) AS History
                       FROM     Sales.vContractDates AS vContractDates_1
                       WHERE  (DATEDIFF(wk, GETDATE(), LastInstallWeek) &lt; 0)) AS dtHistoryStatus ON Sales.Contract.ContractID = dtHistoryStatus.ContractID INNER JOIN
                      (SELECT ContractID, ISNULL(dbo.udfBrandListByContract(ContractID), N'') AS Brands, ISNULL(dbo.udfChainListByContract(ContractID), N'') AS Chains, ISNULL(dbo.udfMediaListByContract(ContractID), N'') AS MediaServices, 
                                         ISNULL(dbo.udfCategoryListByContract(ContractID), N'') AS Categories, ISNULL(dbo.udfResearchCategoryListByContract(ContractID), N'') AS ResearchCategories
                       FROM      Sales.Contract AS Contract_2) AS dtBurstInfo ON Sales.Contract.ContractID = dtBurstInfo.ContractID INNER JOIN
                  Sales.AccountManager ON Sales.Contract.AccountManagerID = Sales.AccountManager.AccountManagerID LEFT OUTER JOIN
                  Sales.ContractApproved ON Sales.Contract.ContractID = Sales.ContractApproved.ContractID LEFT OUTER JOIN
                      (SELECT ContractID, MIN(FirstWeek) AS FirstWeek, MAX(DATEADD(wk, InstallWeeks - 1, FirstWeek)) AS LastWeek
                       FROM      Sales.Burst
                       GROUP BY ContractID
                       UNION ALL
                       SELECT ContractID, MIN(FromDate) AS FirstWeek, MAX(DATEADD(d, - 1, DATEADD(m, Months, FromDate))) AS LastWeek
                       FROM     Sales.ResearchCategory
                       GROUP BY ContractID) AS dtContractDates ON Sales.Contract.ContractID = dtContractDates.ContractID
WHERE  (Sales.Contract.Cancelled = ISNULL(@Cancelled, Sales.Contract.Cancelled)) AND (Sales.Contract.ContractNumber LIKE N'%' + @ContractNumber + N'%') AND (Sales.Contract.ProjectName LIKE N'%' + @Project + N'%') AND 
                  (Sales.Contract.ClientName LIKE N'%' + @ClientName + N'%') AND (dtHistoryStatus.History = ISNULL(@History, dtHistoryStatus.History)) AND (dtBurstInfo.Brands LIKE N'%' + @BrandName + N'%') AND 
                  (dtBurstInfo.MediaServices LIKE N'%' + @MediaServiceName + N'%')
ORDER BY Sales.Contract.ContractNumber</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="Cancelled" ColumnName="Cancelled" DataSourceName="NovaDB.Sales.Contract" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@Cancelled" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="Cancelled" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractNumber" ColumnName="ContractNumber" DataSourceName="NovaDB.Sales.Contract" DataTypeServer="nvarchar(8)" DbType="String" Direction="Input" ParameterName="@ContractNumber" Precision="0" ProviderType="NVarChar" Scale="0" Size="8" SourceColumn="ContractNumber" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Project" ColumnName="ProjectName" DataSourceName="NovaDB.Sales.Contract" DataTypeServer="nvarchar(250)" DbType="String" Direction="Input" ParameterName="@Project" Precision="0" ProviderType="NVarChar" Scale="0" Size="250" SourceColumn="ProjectName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ClientName" ColumnName="ClientName" DataSourceName="NovaDB.Sales.Contract" DataTypeServer="nvarchar(250)" DbType="String" Direction="Input" ParameterName="@ClientName" Precision="0" ProviderType="NVarChar" Scale="0" Size="250" SourceColumn="ClientName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="History" ColumnName="History" DataSourceName="SELECT Contract_1.ContractID, CONVERT(bit, 0) AS History FROM Sales.Contract AS Contract_1 LEFT OUTER JOIN Sales.vContractDates ON Contract_1.ContractID = Sales.vContractDates.ContractID WHERE (Sales.vContractDates.ContractID IS NULL) UNION ALL SELECT ContractID, CONVERT(bit, 0) AS History FROM Sales.vContractDates AS vContractDates_2 WHERE (DATEDIFF(wk, GETDATE(), LastInstallWeek) &gt;= 0) UNION ALL SELECT ContractID, CONVERT(bit, 1) AS History FROM Sales.vContractDates AS vContractDates_1 WHERE (DATEDIFF(wk, GETDATE(), LastInstallWeek) &lt; 0)" DataTypeServer="unknown" DbType="Boolean" Direction="Input" ParameterName="@History" Precision="0" Scale="0" Size="0" SourceColumn="History" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="BrandName" ColumnName="Brands" DataSourceName="SELECT ContractID, ISNULL(dbo.udfBrandListByContract(ContractID), N'') AS Brands, ISNULL(dbo.udfChainListByContract(ContractID), N'') AS Chains, ISNULL(dbo.udfMediaListByContract(ContractID), N'') AS MediaServices, ISNULL(dbo.udfCategoryListByContract(ContractID), N'') AS Categories, ISNULL(dbo.udfResearchCategoryListByContract(ContractID), N'') AS ResearchCategories FROM Sales.Contract AS Contract_2" DataTypeServer="unknown" DbType="String" Direction="Input" ParameterName="@BrandName" Precision="0" Scale="0" Size="1024" SourceColumn="Brands" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaServiceName" ColumnName="MediaServices" DataSourceName="SELECT ContractID, ISNULL(dbo.udfBrandListByContract(ContractID), N'') AS Brands, ISNULL(dbo.udfChainListByContract(ContractID), N'') AS Chains, ISNULL(dbo.udfMediaListByContract(ContractID), N'') AS MediaServices, ISNULL(dbo.udfCategoryListByContract(ContractID), N'') AS Categories, ISNULL(dbo.udfResearchCategoryListByContract(ContractID), N'') AS ResearchCategories FROM Sales.Contract AS Contract_2" DataTypeServer="unknown" DbType="String" Direction="Input" ParameterName="@MediaServiceName" Precision="0" Scale="0" Size="1024" SourceColumn="MediaServices" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE Sales.Contract
SET          ContractID = @ContractID, AccountManagerID = @AccountManagerID, ClientID = @ClientID, ClientName = @ClientName, ClientBillingAddress = @ClientBillingAddress, ContractNumber = @ContractNumber, Signed = @Signed, 
                  SignDate = @SignDate, SignedBy = @SignedBy, SpecialConditions = @SpecialConditions, ProjectName = @ProjectName, ApplyAgencyComm = @ApplyAgencyComm, AgencyID = @AgencyID, AgencyName = @AgencyName, 
                  AgencyCommPercentage = @AgencyCommPercentage, Cancelled = @Cancelled, CancelDate = @CancelDate, CancelledBy = @CancelledBy, BillingInstructions = @BillingInstructions, ContractNotes = @ContractNotes, 
                  ContractDate = @ContractDate, ContractType = @ContractType, RollForward = @RollForward, AddedValue = @AddedValue, AgencyCommIsPercentageOfNetRental = @AgencyCommIsPercentageOfNetRental, 
                  PrintAgencyComm = @PrintAgencyComm, ContractClassificationId =
                      (SELECT TOP (1) ContractClassificationId
                       FROM      Sales.ContractClassification
                       WHERE   (Classification = @ContractClassificationName)), ContractProposalHeatId = @ContractProposalHeatId, DemoProvider = @DemoProvider, DemoOwner = @DemoOwner
WHERE  (ContractID = @Original_ContractID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="AccountManagerID" ColumnName="AccountManagerID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ClientID" ColumnName="ClientID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ClientID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ClientID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ClientName" ColumnName="ClientName" DataSourceName="" DataTypeServer="nvarchar(250)" DbType="String" Direction="Input" ParameterName="@ClientName" Precision="0" ProviderType="NVarChar" Scale="0" Size="250" SourceColumn="ClientName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ClientBillingAddress" ColumnName="ClientBillingAddress" DataSourceName="" DataTypeServer="nvarchar(1000)" DbType="String" Direction="Input" ParameterName="@ClientBillingAddress" Precision="0" ProviderType="NVarChar" Scale="0" Size="1000" SourceColumn="ClientBillingAddress" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractNumber" ColumnName="ContractNumber" DataSourceName="" DataTypeServer="nvarchar(8)" DbType="String" Direction="Input" ParameterName="@ContractNumber" Precision="0" ProviderType="NVarChar" Scale="0" Size="8" SourceColumn="ContractNumber" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Signed" ColumnName="Signed" DataSourceName="" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@Signed" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="Signed" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="SignDate" ColumnName="SignDate" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@SignDate" Precision="0" ProviderType="DateTime" Scale="0" Size="8" SourceColumn="SignDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="SignedBy" ColumnName="SignedBy" DataSourceName="" DataTypeServer="nvarchar(50)" DbType="String" Direction="Input" ParameterName="@SignedBy" Precision="0" ProviderType="NVarChar" Scale="0" Size="50" SourceColumn="SignedBy" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="SpecialConditions" ColumnName="SpecialConditions" DataSourceName="" DataTypeServer="varchar(2000)" DbType="AnsiString" Direction="Input" ParameterName="@SpecialConditions" Precision="0" ProviderType="VarChar" Scale="0" Size="2000" SourceColumn="SpecialConditions" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ProjectName" ColumnName="ProjectName" DataSourceName="" DataTypeServer="nvarchar(250)" DbType="String" Direction="Input" ParameterName="@ProjectName" Precision="0" ProviderType="NVarChar" Scale="0" Size="250" SourceColumn="ProjectName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ApplyAgencyComm" ColumnName="ApplyAgencyComm" DataSourceName="" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@ApplyAgencyComm" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="ApplyAgencyComm" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="AgencyID" ColumnName="AgencyID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@AgencyID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="AgencyID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="AgencyName" ColumnName="AgencyName" DataSourceName="" DataTypeServer="nvarchar(250)" DbType="String" Direction="Input" ParameterName="@AgencyName" Precision="0" ProviderType="NVarChar" Scale="0" Size="250" SourceColumn="AgencyName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="AgencyCommPercentage" ColumnName="AgencyCommPercentage" DataSourceName="" DataTypeServer="decimal(5, 2)" DbType="Decimal" Direction="Input" ParameterName="@AgencyCommPercentage" Precision="5" ProviderType="Decimal" Scale="2" Size="5" SourceColumn="AgencyCommPercentage" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Cancelled" ColumnName="Cancelled" DataSourceName="" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@Cancelled" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="Cancelled" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="CancelDate" ColumnName="CancelDate" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@CancelDate" Precision="0" ProviderType="DateTime" Scale="0" Size="8" SourceColumn="CancelDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="CancelledBy" ColumnName="CancelledBy" DataSourceName="" DataTypeServer="nvarchar(50)" DbType="String" Direction="Input" ParameterName="@CancelledBy" Precision="0" ProviderType="NVarChar" Scale="0" Size="50" SourceColumn="CancelledBy" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="BillingInstructions" ColumnName="BillingInstructions" DataSourceName="" DataTypeServer="varchar(2000)" DbType="AnsiString" Direction="Input" ParameterName="@BillingInstructions" Precision="0" ProviderType="VarChar" Scale="0" Size="2000" SourceColumn="BillingInstructions" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractNotes" ColumnName="ContractNotes" DataSourceName="" DataTypeServer="varchar(2000)" DbType="AnsiString" Direction="Input" ParameterName="@ContractNotes" Precision="0" ProviderType="VarChar" Scale="0" Size="2000" SourceColumn="ContractNotes" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="ContractDate" ColumnName="ContractDate" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@ContractDate" Precision="0" ProviderType="DateTime" Scale="0" Size="8" SourceColumn="ContractDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractType" ColumnName="ContractType" DataSourceName="" DataTypeServer="nvarchar(50)" DbType="String" Direction="Input" ParameterName="@ContractType" Precision="0" ProviderType="NVarChar" Scale="0" Size="50" SourceColumn="ContractType" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="RollForward" ColumnName="RollForward" DataSourceName="" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@RollForward" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="RollForward" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="AddedValue" ColumnName="AddedValue" DataSourceName="" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@AddedValue" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="AddedValue" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="AgencyCommIsPercentageOfNetRental" ColumnName="AgencyCommIsPercentageOfNetRental" DataSourceName="" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@AgencyCommIsPercentageOfNetRental" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="AgencyCommIsPercentageOfNetRental" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="PrintAgencyComm" ColumnName="PrintAgencyComm" DataSourceName="" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@PrintAgencyComm" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="PrintAgencyComm" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="ContractProposalHeatId" ColumnName="ContractProposalHeatId" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ContractProposalHeatId" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ContractProposalHeatId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="DemoProvider" ColumnName="DemoProvider" DataSourceName="" DataTypeServer="varchar(2000)" DbType="AnsiString" Direction="Input" ParameterName="@DemoProvider" Precision="0" ProviderType="VarChar" Scale="0" Size="2000" SourceColumn="DemoProvider" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="DemoOwner" ColumnName="DemoOwner" DataSourceName="" DataTypeServer="varchar(2000)" DbType="AnsiString" Direction="Input" ParameterName="@DemoOwner" Precision="0" ProviderType="VarChar" Scale="0" Size="2000" SourceColumn="DemoOwner" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_ContractID" ColumnName="ContractID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@Original_ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractClassificationName" ColumnName="" DataSourceName="" DataTypeServer="varchar(255)" DbType="AnsiString" Direction="Input" ParameterName="@ContractClassificationName" Precision="0" Scale="0" Size="255" SourceColumn="ContractClassificationName" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ContractID" DataSetColumn="ContractID" />
              <Mapping SourceColumn="AccountManagerID" DataSetColumn="AccountManagerID" />
              <Mapping SourceColumn="ClientID" DataSetColumn="ClientID" />
              <Mapping SourceColumn="ClientName" DataSetColumn="ClientName" />
              <Mapping SourceColumn="ClientBillingAddress" DataSetColumn="ClientBillingAddress" />
              <Mapping SourceColumn="ContractNumber" DataSetColumn="ContractNumber" />
              <Mapping SourceColumn="Signed" DataSetColumn="Signed" />
              <Mapping SourceColumn="SignDate" DataSetColumn="SignDate" />
              <Mapping SourceColumn="SignedBy" DataSetColumn="SignedBy" />
              <Mapping SourceColumn="SpecialConditions" DataSetColumn="SpecialConditions" />
              <Mapping SourceColumn="ProjectName" DataSetColumn="ProjectName" />
              <Mapping SourceColumn="Cancelled" DataSetColumn="Cancelled" />
              <Mapping SourceColumn="CancelDate" DataSetColumn="CancelDate" />
              <Mapping SourceColumn="CancelledBy" DataSetColumn="CancelledBy" />
              <Mapping SourceColumn="CreatedBy" DataSetColumn="CreatedBy" />
              <Mapping SourceColumn="CreationDate" DataSetColumn="CreationDate" />
              <Mapping SourceColumn="Brands" DataSetColumn="Brands" />
              <Mapping SourceColumn="Chains" DataSetColumn="Chains" />
              <Mapping SourceColumn="MediaServices" DataSetColumn="MediaServices" />
              <Mapping SourceColumn="Categories" DataSetColumn="Categories" />
              <Mapping SourceColumn="FirstWeek" DataSetColumn="FirstWeek" />
              <Mapping SourceColumn="LastWeek" DataSetColumn="LastWeek" />
              <Mapping SourceColumn="AgencyID" DataSetColumn="AgencyID" />
              <Mapping SourceColumn="AgencyName" DataSetColumn="AgencyName" />
              <Mapping SourceColumn="AgencyCommPercentage" DataSetColumn="AgencyCommPercentage" />
              <Mapping SourceColumn="ApplyAgencyComm" DataSetColumn="ApplyAgencyComm" />
              <Mapping SourceColumn="BillingInstructions" DataSetColumn="BillingInstructions" />
              <Mapping SourceColumn="ContractNotes" DataSetColumn="ContractNotes" />
              <Mapping SourceColumn="TotalWeeks" DataSetColumn="TotalWeeks" />
              <Mapping SourceColumn="History" DataSetColumn="History" />
              <Mapping SourceColumn="ContractDate" DataSetColumn="ContractDate" />
              <Mapping SourceColumn="ContractType" DataSetColumn="ContractType" />
              <Mapping SourceColumn="AccountManagerName" DataSetColumn="AccountManagerName" />
              <Mapping SourceColumn="Code" DataSetColumn="Code" />
              <Mapping SourceColumn="ResearchCategories" DataSetColumn="ResearchCategories" />
              <Mapping SourceColumn="RollForward" DataSetColumn="RollForward" />
              <Mapping SourceColumn="AddedValue" DataSetColumn="AddedValue" />
              <Mapping SourceColumn="AgencyCommIsPercentageOfNetRental" DataSetColumn="AgencyCommIsPercentageOfNetRental" />
              <Mapping SourceColumn="PrintAgencyComm" DataSetColumn="PrintAgencyComm" />
              <Mapping SourceColumn="Approved" DataSetColumn="Approved" />
              <Mapping SourceColumn="ClientAccountManager" DataSetColumn="ClientAccountManager" />
              <Mapping SourceColumn="Classification" DataSetColumn="ContractClassificationName" />
              <Mapping SourceColumn="ContractProposalHeatName" DataSetColumn="ContractProposalHeatName" />
              <Mapping SourceColumn="ContractProposalHeatId" DataSetColumn="ContractProposalHeatId" />
              <Mapping SourceColumn="isReplacement" DataSetColumn="isReplacement" />
              <Mapping SourceColumn="ClonedContractNumber" DataSetColumn="ClonedContractNumber" />
              <Mapping SourceColumn="DemoProvider" DataSetColumn="DemoProvider" />
              <Mapping SourceColumn="DemoOwner" DataSetColumn="DemoOwner" />
              <Mapping SourceColumn="ContractClassificationId" DataSetColumn="ContractClassificationId" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="BurstTableAdapter" GeneratorDataComponentClassName="BurstTableAdapter" Name="Burst" UserDataComponentName="BurstTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      DELETE FROM Sales.Burst
                      WHERE        (BurstID = @Original_BurstID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_BurstID" ColumnName="BurstID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@Original_BurstID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="BurstID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO Sales.Burst
                  (BurstID, ContractID, ChainID, ChainName, StorePoolID, MediaID, MediaName, BrandID, BrandName, ProductName, FirstWeek, InstallWeeks, InstallStoreQty, BillableStoreQty, RentalRate, BillableWeeks, Discount, CrossoverQty, 
                  InstallAtHomesite, InstallationInstructions, StoreListConfirmed, AdsPerInstallation, AdsPerCrossover, AdsPerShelfTalk, InstallRegardlessOfStock)
VALUES (@BurstID,@ContractID,@ChainID,@ChainName,@StorePoolID,@MediaID,@MediaName,@BrandID,@BrandName,@ProductName,@FirstWeek,@InstallWeeks,@InstallStoreQty,@BillableStoreQty,@RentalRate,@BillableWeeks,@Discount,@CrossoverQty,@InstallAtHomesite,@InstallationInstructions,@StoreListConfirmed,@AdsPerInstallation,@AdsPerCrossover,@AdsPerShelfTalk,@InstallRegardlessOfStock)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="BurstID" ColumnName="BurstID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@BurstID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="BurstID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ChainID" ColumnName="ChainID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ChainID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ChainID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ChainName" ColumnName="ChainName" DataSourceName="" DataTypeServer="nvarchar(200)" DbType="String" Direction="Input" ParameterName="@ChainName" Precision="0" ProviderType="NVarChar" Scale="0" Size="200" SourceColumn="ChainName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="StorePoolID" ColumnName="StorePoolID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@StorePoolID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="StorePoolID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaID" ColumnName="MediaID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaName" ColumnName="MediaName" DataSourceName="" DataTypeServer="nvarchar(200)" DbType="String" Direction="Input" ParameterName="@MediaName" Precision="0" ProviderType="NVarChar" Scale="0" Size="200" SourceColumn="MediaName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="BrandID" ColumnName="BrandID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="BrandID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="BrandName" ColumnName="BrandName" DataSourceName="" DataTypeServer="nvarchar(200)" DbType="String" Direction="Input" ParameterName="@BrandName" Precision="0" ProviderType="NVarChar" Scale="0" Size="200" SourceColumn="BrandName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ProductName" ColumnName="ProductName" DataSourceName="" DataTypeServer="nvarchar(200)" DbType="String" Direction="Input" ParameterName="@ProductName" Precision="0" ProviderType="NVarChar" Scale="0" Size="200" SourceColumn="ProductName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="FirstWeek" ColumnName="FirstWeek" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@FirstWeek" Precision="0" ProviderType="DateTime" Scale="0" Size="8" SourceColumn="FirstWeek" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="InstallWeeks" ColumnName="InstallWeeks" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@InstallWeeks" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="InstallWeeks" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="InstallStoreQty" ColumnName="InstallStoreQty" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@InstallStoreQty" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="InstallStoreQty" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="BillableStoreQty" ColumnName="BillableStoreQty" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@BillableStoreQty" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="BillableStoreQty" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="RentalRate" ColumnName="RentalRate" DataSourceName="" DataTypeServer="decimal(18, 4)" DbType="Decimal" Direction="Input" ParameterName="@RentalRate" Precision="18" ProviderType="Decimal" Scale="4" Size="9" SourceColumn="RentalRate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="BillableWeeks" ColumnName="BillableWeeks" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@BillableWeeks" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="BillableWeeks" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Discount" ColumnName="Discount" DataSourceName="" DataTypeServer="decimal(5, 2)" DbType="Decimal" Direction="Input" ParameterName="@Discount" Precision="5" ProviderType="Decimal" Scale="2" Size="5" SourceColumn="Discount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="CrossoverQty" ColumnName="CrossoverQty" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@CrossoverQty" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="CrossoverQty" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="InstallAtHomesite" ColumnName="InstallAtHomesite" DataSourceName="" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@InstallAtHomesite" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="InstallAtHomesite" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="InstallationInstructions" ColumnName="InstallationInstructions" DataSourceName="" DataTypeServer="varchar(2000)" DbType="AnsiString" Direction="Input" ParameterName="@InstallationInstructions" Precision="0" ProviderType="VarChar" Scale="0" Size="2000" SourceColumn="InstallationInstructions" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="StoreListConfirmed" ColumnName="StoreListConfirmed" DataSourceName="" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@StoreListConfirmed" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="StoreListConfirmed" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="AdsPerInstallation" ColumnName="AdsPerInstallation" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@AdsPerInstallation" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="AdsPerInstallation" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="AdsPerCrossover" ColumnName="AdsPerCrossover" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@AdsPerCrossover" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="AdsPerCrossover" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="AdsPerShelfTalk" ColumnName="AdsPerShelfTalk" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@AdsPerShelfTalk" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="AdsPerShelfTalk" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="InstallRegardlessOfStock" ColumnName="InstallRegardlessOfStock" DataSourceName="" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@InstallRegardlessOfStock" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="InstallRegardlessOfStock" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT Sales.Burst.BurstID, Sales.Burst.ContractID, Sales.Burst.ChainID, Sales.Burst.ChainName, Sales.Burst.StorePoolID, Sales.Burst.MediaID, Sales.Burst.MediaName, Sales.Burst.BrandID, Sales.Burst.BrandName, Sales.Burst.ProductName, 
                  Sales.Burst.FirstWeek, Sales.Burst.InstallWeeks, Sales.Burst.InstallStoreQty, Sales.Burst.BillableStoreQty, Sales.Burst.RentalRate, Sales.Burst.BillableWeeks, Sales.Burst.Discount, Sales.Burst.CrossoverQty, 
                  Sales.Burst.InstallationInstructions, DATEADD(wk, Sales.Burst.InstallWeeks - 1, Sales.Burst.FirstWeek) AS LastWeek, dbo.udfCategoryListByBurst(Sales.Burst.BurstID) AS Categories, 
                  dbo.udfCategoryListByBurstInstallation(Sales.Burst.BurstID) AS InstallCategories, Media.Media.Homesite AS MediaAllowsHomesite, Media.Media.Crossover AS MediaAllowsCrossover, 
                  ISNULL(dbo.udfMediaFamilyIDListByMedia(Sales.Burst.MediaID), N'') AS MediaFamilyIDList, Sales.StorePool.StorePoolQty, Sales.Burst.StoreListConfirmed, Sales.Burst.CreationDate, Sales.Burst.InstallAtHomesite, 
                  Sales.Burst.AdsPerInstallation, Sales.Burst.AdsPerCrossover, ISNULL(dtMediaLifeCycle.Valid, 0) AS MediaLifeCycleValid, Store.Chain.ChainTypeID, ISNULL(Sales.Burst.AdsPerShelfTalk, 0) AS AdsPerShelfTalk, 
                  ISNULL(Sales.Burst.InstallRegardlessOfStock, 0) AS InstallRegardlessOfStock, Sales.Burst.ApplyInstructionsAcrossAllBursts, Media.Media.isPNPPcaStatus
FROM     Sales.Burst INNER JOIN
                  Media.Media ON Sales.Burst.MediaID = Media.Media.MediaID INNER JOIN
                  Sales.StorePool ON Sales.Burst.StorePoolID = Sales.StorePool.StorePoolID INNER JOIN
                  Store.Chain ON Sales.Burst.ChainID = Store.Chain.ChainID LEFT OUTER JOIN
                      (SELECT MediaID, FirstWeek, LastWeek, CONVERT(bit, 1) AS Valid
                       FROM      Media.MediaLifeCycle) AS dtMediaLifeCycle ON Sales.Burst.MediaID = dtMediaLifeCycle.MediaID AND Sales.Burst.FirstWeek &gt;= dtMediaLifeCycle.FirstWeek AND DATEADD(wk, Sales.Burst.InstallWeeks - 1, 
                  Sales.Burst.FirstWeek) &lt;= dtMediaLifeCycle.LastWeek
WHERE  (Sales.Burst.ContractID = @ContractID) AND (Sales.Burst.ChainID IN
                      (SELECT ChainID
                       FROM      dbo.udfChainPermission(@Username) AS udfChainPermission_1))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.Burst" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Username" ColumnName="" DataSourceName="" DataTypeServer="varchar(500)" DbType="AnsiString" Direction="Input" ParameterName="@Username" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE Sales.Burst
SET          BurstID = @BurstID, ContractID = @ContractID, ChainID = @ChainID, ChainName = @ChainName, StorePoolID = @StorePoolID, MediaID = @MediaID, MediaName = @MediaName, BrandID = @BrandID, BrandName = @BrandName, 
                  ProductName = @ProductName, FirstWeek = @FirstWeek, InstallWeeks = @InstallWeeks, InstallStoreQty = @InstallStoreQty, BillableStoreQty = @BillableStoreQty, RentalRate = @RentalRate, BillableWeeks = @BillableWeeks, 
                  Discount = @Discount, CrossoverQty = @CrossoverQty, InstallAtHomesite = @InstallAtHomesite, InstallationInstructions = @InstallationInstructions, StoreListConfirmed = @StoreListConfirmed, AdsPerInstallation = @AdsPerInstallation, 
                  AdsPerCrossover = @AdsPerCrossover, AdsPerShelfTalk = @AdsPerShelfTalk, InstallRegardlessOfStock = @InstallRegardlessOfStocl
WHERE  (BurstID = @Original_BurstID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="BurstID" ColumnName="BurstID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@BurstID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="BurstID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ChainID" ColumnName="ChainID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ChainID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ChainID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ChainName" ColumnName="ChainName" DataSourceName="" DataTypeServer="nvarchar(200)" DbType="String" Direction="Input" ParameterName="@ChainName" Precision="0" ProviderType="NVarChar" Scale="0" Size="200" SourceColumn="ChainName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="StorePoolID" ColumnName="StorePoolID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@StorePoolID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="StorePoolID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaID" ColumnName="MediaID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaName" ColumnName="MediaName" DataSourceName="" DataTypeServer="nvarchar(200)" DbType="String" Direction="Input" ParameterName="@MediaName" Precision="0" ProviderType="NVarChar" Scale="0" Size="200" SourceColumn="MediaName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="BrandID" ColumnName="BrandID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="BrandID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="BrandName" ColumnName="BrandName" DataSourceName="" DataTypeServer="nvarchar(200)" DbType="String" Direction="Input" ParameterName="@BrandName" Precision="0" ProviderType="NVarChar" Scale="0" Size="200" SourceColumn="BrandName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ProductName" ColumnName="ProductName" DataSourceName="" DataTypeServer="nvarchar(200)" DbType="String" Direction="Input" ParameterName="@ProductName" Precision="0" ProviderType="NVarChar" Scale="0" Size="200" SourceColumn="ProductName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="FirstWeek" ColumnName="FirstWeek" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@FirstWeek" Precision="0" ProviderType="DateTime" Scale="0" Size="8" SourceColumn="FirstWeek" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="InstallWeeks" ColumnName="InstallWeeks" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@InstallWeeks" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="InstallWeeks" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="InstallStoreQty" ColumnName="InstallStoreQty" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@InstallStoreQty" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="InstallStoreQty" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="BillableStoreQty" ColumnName="BillableStoreQty" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@BillableStoreQty" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="BillableStoreQty" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="RentalRate" ColumnName="RentalRate" DataSourceName="" DataTypeServer="decimal(18, 4)" DbType="Decimal" Direction="Input" ParameterName="@RentalRate" Precision="18" ProviderType="Decimal" Scale="4" Size="9" SourceColumn="RentalRate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="BillableWeeks" ColumnName="BillableWeeks" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@BillableWeeks" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="BillableWeeks" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Discount" ColumnName="Discount" DataSourceName="" DataTypeServer="decimal(5, 2)" DbType="Decimal" Direction="Input" ParameterName="@Discount" Precision="5" ProviderType="Decimal" Scale="2" Size="5" SourceColumn="Discount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="CrossoverQty" ColumnName="CrossoverQty" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@CrossoverQty" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="CrossoverQty" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="InstallAtHomesite" ColumnName="InstallAtHomesite" DataSourceName="" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@InstallAtHomesite" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="InstallAtHomesite" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="InstallationInstructions" ColumnName="InstallationInstructions" DataSourceName="" DataTypeServer="varchar(2000)" DbType="AnsiString" Direction="Input" ParameterName="@InstallationInstructions" Precision="0" ProviderType="VarChar" Scale="0" Size="2000" SourceColumn="InstallationInstructions" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="StoreListConfirmed" ColumnName="StoreListConfirmed" DataSourceName="" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@StoreListConfirmed" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="StoreListConfirmed" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="AdsPerInstallation" ColumnName="AdsPerInstallation" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@AdsPerInstallation" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="AdsPerInstallation" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="AdsPerCrossover" ColumnName="AdsPerCrossover" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@AdsPerCrossover" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="AdsPerCrossover" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="AdsPerShelfTalk" ColumnName="AdsPerShelfTalk" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@AdsPerShelfTalk" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="AdsPerShelfTalk" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="InstallRegardlessOfStocl" ColumnName="InstallRegardlessOfStock" DataSourceName="" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@InstallRegardlessOfStocl" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="InstallRegardlessOfStock" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_BurstID" ColumnName="BurstID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@Original_BurstID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="BurstID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="BurstID" DataSetColumn="BurstID" />
              <Mapping SourceColumn="ContractID" DataSetColumn="ContractID" />
              <Mapping SourceColumn="ChainID" DataSetColumn="ChainID" />
              <Mapping SourceColumn="ChainName" DataSetColumn="ChainName" />
              <Mapping SourceColumn="StorePoolID" DataSetColumn="StorePoolID" />
              <Mapping SourceColumn="MediaID" DataSetColumn="MediaID" />
              <Mapping SourceColumn="MediaName" DataSetColumn="MediaName" />
              <Mapping SourceColumn="BrandName" DataSetColumn="BrandName" />
              <Mapping SourceColumn="ProductName" DataSetColumn="ProductName" />
              <Mapping SourceColumn="FirstWeek" DataSetColumn="FirstWeek" />
              <Mapping SourceColumn="InstallWeeks" DataSetColumn="InstallWeeks" />
              <Mapping SourceColumn="InstallStoreQty" DataSetColumn="InstallStoreQty" />
              <Mapping SourceColumn="BillableStoreQty" DataSetColumn="BillableStoreQty" />
              <Mapping SourceColumn="RentalRate" DataSetColumn="RentalRate" />
              <Mapping SourceColumn="BillableWeeks" DataSetColumn="BillableWeeks" />
              <Mapping SourceColumn="Discount" DataSetColumn="Discount" />
              <Mapping SourceColumn="InstallationInstructions" DataSetColumn="InstallationInstructions" />
              <Mapping SourceColumn="CrossoverQty" DataSetColumn="CrossoverQty" />
              <Mapping SourceColumn="LastWeek" DataSetColumn="LastWeek" />
              <Mapping SourceColumn="Categories" DataSetColumn="Categories" />
              <Mapping SourceColumn="MediaAllowsHomesite" DataSetColumn="MediaAllowsHomesite" />
              <Mapping SourceColumn="MediaAllowsCrossover" DataSetColumn="MediaAllowsCrossover" />
              <Mapping SourceColumn="BrandID" DataSetColumn="BrandID" />
              <Mapping SourceColumn="MediaFamilyIDList" DataSetColumn="MediaFamilyIDList" />
              <Mapping SourceColumn="StorePoolQty" DataSetColumn="StorePoolQty" />
              <Mapping SourceColumn="StoreListConfirmed" DataSetColumn="StoreListConfirmed" />
              <Mapping SourceColumn="CreationDate" DataSetColumn="CreationDate" />
              <Mapping SourceColumn="InstallAtHomesite" DataSetColumn="InstallAtHomesite" />
              <Mapping SourceColumn="AdsPerInstallation" DataSetColumn="AdsPerInstallation" />
              <Mapping SourceColumn="MediaLifeCycleValid" DataSetColumn="MediaLifeCycleValid" />
              <Mapping SourceColumn="ChainTypeID" DataSetColumn="ChainTypeID" />
              <Mapping SourceColumn="AdsPerCrossover" DataSetColumn="AdsPerCrossover" />
              <Mapping SourceColumn="AdsPerShelfTalk" DataSetColumn="AdsPerShelfTalk" />
              <Mapping SourceColumn="InstallRegardlessOfStock" DataSetColumn="InstallRegardlessOfStock" />
              <Mapping SourceColumn="ApplyInstructionsAcrossAllBursts" DataSetColumn="ApplyInstructionsAcrossAllBursts" />
              <Mapping SourceColumn="isPNPPcaStatus" DataSetColumn="isPNPPcaStatus" />
              <Mapping SourceColumn="InstallCategories" DataSetColumn="InstallCategories" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="AccountManagerTableAdapter" GeneratorDataComponentClassName="AccountManagerTableAdapter" Name="AccountManager" UserDataComponentName="AccountManagerTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Sales.AccountManager" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      SELECT        AccountManagerID, Code, FirstName + N' ' + LastName AS AccountManagerName
                      FROM            Sales.AccountManager
                    </CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="AccountManagerID" DataSetColumn="AccountManagerID" />
              <Mapping SourceColumn="Code" DataSetColumn="Code" />
              <Mapping SourceColumn="AccountManagerName" DataSetColumn="AccountManagerName" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="BurstLoadingFeeTableAdapter" GeneratorDataComponentClassName="BurstLoadingFeeTableAdapter" Name="BurstLoadingFee" UserDataComponentName="BurstLoadingFeeTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>
                      DELETE FROM Sales.BurstLoadingFee
                      WHERE        (BurstID = @Original_BurstID) AND (LoadingFeeID = @Original_LoadingFeeID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_BurstID" ColumnName="BurstID" DataSourceName="NovaDB.Sales.BurstLoadingFee" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@Original_BurstID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="BurstID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_LoadingFeeID" ColumnName="LoadingFeeID" DataSourceName="NovaDB.Sales.BurstLoadingFee" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@Original_LoadingFeeID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="LoadingFeeID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      INSERT INTO [Sales].[BurstLoadingFee] ([BurstID], [LoadingFeeID], [Percentage]) VALUES (@BurstID, @LoadingFeeID, @Percentage);
                      SELECT BurstID, LoadingFeeID, Percentage FROM Sales.BurstLoadingFee WHERE (BurstID = @BurstID) AND (LoadingFeeID = @LoadingFeeID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@BurstID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BurstID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@LoadingFeeID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="LoadingFeeID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Percentage" Precision="5" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="Percentage" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      SELECT        Sales.BurstLoadingFee.BurstID, Sales.BurstLoadingFee.LoadingFeeID, Sales.BurstLoadingFee.Percentage, Sales.LoadingFee.LoadingFeeName,
                      Sales.BurstLoadingFee.Percentage / 100 * Sales.Burst.BillableStoreQty * Sales.Burst.BillableWeeks * Sales.Burst.RentalRate AS LoadingFeeAmount
                      FROM            Sales.BurstLoadingFee INNER JOIN
                      Sales.Burst ON Sales.BurstLoadingFee.BurstID = Sales.Burst.BurstID INNER JOIN
                      Sales.LoadingFee ON Sales.BurstLoadingFee.LoadingFeeID = Sales.LoadingFee.LoadingFeeID
                      WHERE        (Sales.Burst.ContractID = @ContractID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.Burst" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>
                      UPDATE       Sales.BurstLoadingFee
                      SET                BurstID = @BurstID, LoadingFeeID = @LoadingFeeID, Percentage = @Percentage
                      WHERE        (BurstID = @Original_BurstID) AND (LoadingFeeID = @Original_LoadingFeeID);
                      SELECT BurstID, LoadingFeeID, Percentage FROM Sales.BurstLoadingFee WHERE (BurstID = @BurstID) AND (LoadingFeeID = @LoadingFeeID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="BurstID" ColumnName="BurstID" DataSourceName="NovaDB.Sales.BurstLoadingFee" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@BurstID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="BurstID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="LoadingFeeID" ColumnName="LoadingFeeID" DataSourceName="NovaDB.Sales.BurstLoadingFee" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@LoadingFeeID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="LoadingFeeID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Percentage" ColumnName="Percentage" DataSourceName="NovaDB.Sales.BurstLoadingFee" DataTypeServer="decimal(5, 2)" DbType="Decimal" Direction="Input" ParameterName="@Percentage" Precision="5" ProviderType="Decimal" Scale="2" Size="5" SourceColumn="Percentage" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_BurstID" ColumnName="BurstID" DataSourceName="NovaDB.Sales.BurstLoadingFee" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@Original_BurstID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="BurstID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_LoadingFeeID" ColumnName="LoadingFeeID" DataSourceName="NovaDB.Sales.BurstLoadingFee" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@Original_LoadingFeeID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="LoadingFeeID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="BurstID" DataSetColumn="BurstID" />
              <Mapping SourceColumn="LoadingFeeID" DataSetColumn="LoadingFeeID" />
              <Mapping SourceColumn="Percentage" DataSetColumn="Percentage" />
              <Mapping SourceColumn="LoadingFeeName" DataSetColumn="LoadingFeeName" />
              <Mapping SourceColumn="LoadingFeeAmount" DataSetColumn="LoadingFeeAmount" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="BurstCategoryTableAdapter" GeneratorDataComponentClassName="BurstCategoryTableAdapter" Name="BurstCategory" UserDataComponentName="BurstCategoryTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Sales].[BurstCategory] WHERE (([BurstID] = @Original_BurstID) AND ([CategoryID] = @Original_CategoryID) AND ([Priority] = @Original_Priority))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_BurstID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BurstID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_Priority" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Priority" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      INSERT INTO [Sales].[BurstCategory] ([BurstID], [CategoryID], [Priority]) VALUES (@BurstID, @CategoryID, @Priority);
                      SELECT BurstID, CategoryID, Priority FROM Sales.BurstCategory WHERE (BurstID = @BurstID) AND (CategoryID = @CategoryID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@BurstID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BurstID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Priority" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Priority" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      SELECT        Sales.BurstCategory.BurstID, Sales.BurstCategory.CategoryID, Sales.BurstCategory.Priority, Store.Category.CategoryName
                      FROM            Sales.BurstCategory INNER JOIN
                      Store.Category ON Sales.BurstCategory.CategoryID = Store.Category.CategoryID INNER JOIN
                      Sales.Burst ON Sales.BurstCategory.BurstID = Sales.Burst.BurstID
                      WHERE        (Sales.Burst.ContractID = @ContractID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.Burst" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      UPDATE [Sales].[BurstCategory] SET [BurstID] = @BurstID, [CategoryID] = @CategoryID, [Priority] = @Priority WHERE (([BurstID] = @Original_BurstID) AND ([CategoryID] = @Original_CategoryID) AND ([Priority] = @Original_Priority));
                      SELECT BurstID, CategoryID, Priority FROM Sales.BurstCategory WHERE (BurstID = @BurstID) AND (CategoryID = @CategoryID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@BurstID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BurstID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Priority" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Priority" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_BurstID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BurstID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int16" Direction="Input" ParameterName="@Original_Priority" Precision="0" ProviderType="SmallInt" Scale="0" Size="0" SourceColumn="Priority" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="BurstID" DataSetColumn="BurstID" />
              <Mapping SourceColumn="CategoryID" DataSetColumn="CategoryID" />
              <Mapping SourceColumn="Priority" DataSetColumn="Priority" />
              <Mapping SourceColumn="CategoryName" DataSetColumn="CategoryName" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="LoadingFeeTableAdapter" GeneratorDataComponentClassName="LoadingFeeTableAdapter" Name="LoadingFee" UserDataComponentName="LoadingFeeTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Sales.LoadingFee" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>
                      SELECT        LoadingFeeID, LoadingFeeName, DefaultPercentage
                      FROM            Sales.LoadingFee
                    </CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="LoadingFeeID" DataSetColumn="LoadingFeeID" />
              <Mapping SourceColumn="LoadingFeeName" DataSetColumn="LoadingFeeName" />
              <Mapping SourceColumn="DefaultPercentage" DataSetColumn="DefaultPercentage" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="CategoryTableAdapter" GeneratorDataComponentClassName="CategoryTableAdapter" Name="Category" UserDataComponentName="CategoryTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Store.Category" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>
                      SELECT        CategoryID, CategoryName, Dormant
                      FROM            Store.Category
                    </CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="CategoryID" DataSetColumn="CategoryID" />
              <Mapping SourceColumn="CategoryName" DataSetColumn="CategoryName" />
              <Mapping SourceColumn="Dormant" DataSetColumn="Dormant" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="PurchaseOrderNumberTableAdapter" GeneratorDataComponentClassName="PurchaseOrderNumberTableAdapter" Name="PurchaseOrderNumber" UserDataComponentName="PurchaseOrderNumberTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Sales.PurchaseOrderNumber" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Sales].[PurchaseOrderNumber] WHERE (([ContractID] = @Original_ContractID) AND ([PurchaseOrderNumber] = @Original_PurchaseOrderNumber) AND ([PurchaseOrderNumberDescription] = @Original_PurchaseOrderNumberDescription))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_PurchaseOrderNumber" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="PurchaseOrderNumber" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_PurchaseOrderNumberDescription" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="PurchaseOrderNumberDescription" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      INSERT INTO [Sales].[PurchaseOrderNumber] ([ContractID], [PurchaseOrderNumber], [PurchaseOrderNumberDescription]) VALUES (@ContractID, @PurchaseOrderNumber, @PurchaseOrderNumberDescription);
                      SELECT ContractID, PurchaseOrderNumber, PurchaseOrderNumberDescription FROM Sales.PurchaseOrderNumber WHERE (ContractID = @ContractID) AND (PurchaseOrderNumber = @PurchaseOrderNumber)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@PurchaseOrderNumber" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="PurchaseOrderNumber" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@PurchaseOrderNumberDescription" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="PurchaseOrderNumberDescription" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      SELECT        ContractID, PurchaseOrderNumber, PurchaseOrderNumberDescription
                      FROM            Sales.PurchaseOrderNumber
                      WHERE        (ContractID = @ContractID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.PurchaseOrderNumber" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      UPDATE [Sales].[PurchaseOrderNumber] SET [ContractID] = @ContractID, [PurchaseOrderNumber] = @PurchaseOrderNumber, [PurchaseOrderNumberDescription] = @PurchaseOrderNumberDescription WHERE (([ContractID] = @Original_ContractID) AND ([PurchaseOrderNumber] = @Original_PurchaseOrderNumber) AND ([PurchaseOrderNumberDescription] = @Original_PurchaseOrderNumberDescription));
                      SELECT ContractID, PurchaseOrderNumber, PurchaseOrderNumberDescription FROM Sales.PurchaseOrderNumber WHERE (ContractID = @ContractID) AND (PurchaseOrderNumber = @PurchaseOrderNumber)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@PurchaseOrderNumber" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="PurchaseOrderNumber" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@PurchaseOrderNumberDescription" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="PurchaseOrderNumberDescription" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_PurchaseOrderNumber" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="PurchaseOrderNumber" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_PurchaseOrderNumberDescription" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="PurchaseOrderNumberDescription" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ContractID" DataSetColumn="ContractID" />
              <Mapping SourceColumn="PurchaseOrderNumber" DataSetColumn="PurchaseOrderNumber" />
              <Mapping SourceColumn="PurchaseOrderNumberDescription" DataSetColumn="PurchaseOrderNumberDescription" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ContractInventoryQtyTableAdapter" GeneratorDataComponentClassName="ContractInventoryQtyTableAdapter" Name="ContractInventoryQty" UserDataComponentName="ContractInventoryQtyTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Sales].[ContractInventoryQty] WHERE (([ContractItemQtyID] = @Original_ContractItemQtyID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_ContractItemQtyID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="ContractItemQtyID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>
                      INSERT INTO Sales.ContractInventoryQty
                      (ContractItemQtyID, ContractID, ItemQtyID, SellPrice, Notes, BrandID)
                      VALUES        (@ContractItemQtyID,@ContractID,@ItemQtyID,@SellPrice,@Notes,@BrandID);
                      SELECT ContractItemQtyID, ContractID, ItemQtyID, SellPrice, Notes FROM Sales.ContractInventoryQty WHERE (ContractItemQtyID = @ContractItemQtyID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractItemQtyID" ColumnName="ContractItemQtyID" DataSourceName="NovaDB.Sales.ContractInventoryQty" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractItemQtyID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractItemQtyID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.ContractInventoryQty" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ItemQtyID" ColumnName="ItemQtyID" DataSourceName="NovaDB.Sales.ContractInventoryQty" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ItemQtyID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ItemQtyID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="SellPrice" ColumnName="SellPrice" DataSourceName="NovaDB.Sales.ContractInventoryQty" DataTypeServer="money" DbType="Currency" Direction="Input" ParameterName="@SellPrice" Precision="0" ProviderType="Money" Scale="0" Size="8" SourceColumn="SellPrice" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Notes" ColumnName="Notes" DataSourceName="NovaDB.Sales.ContractInventoryQty" DataTypeServer="nvarchar(2000)" DbType="String" Direction="Input" ParameterName="@Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="2000" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="BrandID" ColumnName="BrandID" DataSourceName="NovaDB.Sales.ContractInventoryQty" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="BrandID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      SELECT        Sales.ContractInventoryQty.ContractItemQtyID, Sales.ContractInventoryQty.ContractID, Sales.ContractInventoryQty.ItemQtyID, Sales.ContractInventoryQty.SellPrice,
                      Sales.ContractInventoryQty.Notes, Ops.InventoryQty.ItemQty, Ops.Inventory.ItemName, Ops.InventoryQty.ItemID, Sales.ContractInventoryQty.BrandID,
                      Client.Brand.BrandName
                      FROM            Sales.ContractInventoryQty INNER JOIN
                      Ops.InventoryQty ON Sales.ContractInventoryQty.ItemQtyID = Ops.InventoryQty.ItemQtyID INNER JOIN
                      Ops.Inventory ON Ops.InventoryQty.ItemID = Ops.Inventory.ItemID INNER JOIN
                      Client.Brand ON Sales.ContractInventoryQty.BrandID = Client.Brand.BrandID
                      WHERE        (Sales.ContractInventoryQty.ContractID = @ContractID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.ContractInventoryQty" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>
                      UPDATE       Sales.ContractInventoryQty
                      SET                ContractItemQtyID = @ContractItemQtyID, ContractID = @ContractID, ItemQtyID = @ItemQtyID, SellPrice = @SellPrice, Notes = @Notes, BrandID = @BrandID
                      WHERE        (ContractItemQtyID = @Original_ContractItemQtyID);
                      SELECT ContractItemQtyID, ContractID, ItemQtyID, SellPrice, Notes FROM Sales.ContractInventoryQty WHERE (ContractItemQtyID = @ContractItemQtyID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractItemQtyID" ColumnName="ContractItemQtyID" DataSourceName="NovaDB.Sales.ContractInventoryQty" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractItemQtyID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractItemQtyID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.ContractInventoryQty" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ItemQtyID" ColumnName="ItemQtyID" DataSourceName="NovaDB.Sales.ContractInventoryQty" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ItemQtyID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ItemQtyID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="SellPrice" ColumnName="SellPrice" DataSourceName="NovaDB.Sales.ContractInventoryQty" DataTypeServer="money" DbType="Currency" Direction="Input" ParameterName="@SellPrice" Precision="0" ProviderType="Money" Scale="0" Size="8" SourceColumn="SellPrice" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Notes" ColumnName="Notes" DataSourceName="NovaDB.Sales.ContractInventoryQty" DataTypeServer="nvarchar(2000)" DbType="String" Direction="Input" ParameterName="@Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="2000" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="BrandID" ColumnName="BrandID" DataSourceName="NovaDB.Sales.ContractInventoryQty" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="BrandID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_ContractItemQtyID" ColumnName="ContractItemQtyID" DataSourceName="NovaDB.Sales.ContractInventoryQty" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@Original_ContractItemQtyID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractItemQtyID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ContractItemQtyID" DataSetColumn="ContractItemQtyID" />
              <Mapping SourceColumn="ContractID" DataSetColumn="ContractID" />
              <Mapping SourceColumn="ItemQtyID" DataSetColumn="ItemQtyID" />
              <Mapping SourceColumn="SellPrice" DataSetColumn="SellPrice" />
              <Mapping SourceColumn="Notes" DataSetColumn="Notes" />
              <Mapping SourceColumn="ItemQty" DataSetColumn="ItemQty" />
              <Mapping SourceColumn="ItemName" DataSetColumn="ItemName" />
              <Mapping SourceColumn="ItemID" DataSetColumn="ItemID" />
              <Mapping SourceColumn="BrandID" DataSetColumn="BrandID" />
              <Mapping SourceColumn="BrandName" DataSetColumn="BrandName" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ContractMiscellaneousChargeTableAdapter" GeneratorDataComponentClassName="ContractMiscellaneousChargeTableAdapter" Name="ContractMiscellaneousCharge" UserDataComponentName="ContractMiscellaneousChargeTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Sales].[ContractMiscellaneousCharge] WHERE (([ContractMiscellaneousChargeID] = @Original_ContractMiscellaneousChargeID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_ContractMiscellaneousChargeID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="ContractMiscellaneousChargeID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      INSERT INTO [Sales].[ContractMiscellaneousCharge] ([ContractMiscellaneousChargeID], [ContractID], [MiscellaneousChargeID], [MiscellaneousChargeAmount]) VALUES (@ContractMiscellaneousChargeID, @ContractID, @MiscellaneousChargeID, @MiscellaneousChargeAmount);
                      SELECT ContractMiscellaneousChargeID, ContractID, MiscellaneousChargeID, MiscellaneousChargeAmount FROM Sales.ContractMiscellaneousCharge WHERE (ContractMiscellaneousChargeID = @ContractMiscellaneousChargeID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@ContractMiscellaneousChargeID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="ContractMiscellaneousChargeID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@MiscellaneousChargeID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MiscellaneousChargeID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Currency" Direction="Input" ParameterName="@MiscellaneousChargeAmount" Precision="0" ProviderType="Money" Scale="0" Size="0" SourceColumn="MiscellaneousChargeAmount" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      SELECT        Sales.ContractMiscellaneousCharge.ContractMiscellaneousChargeID, Sales.ContractMiscellaneousCharge.ContractID,
                      Sales.ContractMiscellaneousCharge.MiscellaneousChargeID, Sales.ContractMiscellaneousCharge.MiscellaneousChargeAmount,
                      Sales.MiscellaneousCharge.MiscellaneousChargeName
                      FROM            Sales.ContractMiscellaneousCharge INNER JOIN
                      Sales.MiscellaneousCharge ON Sales.ContractMiscellaneousCharge.MiscellaneousChargeID = Sales.MiscellaneousCharge.MiscellaneousChargeID
                      WHERE        (Sales.ContractMiscellaneousCharge.ContractID = @ContractID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.ContractMiscellaneousCharge" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      UPDATE [Sales].[ContractMiscellaneousCharge] SET [ContractMiscellaneousChargeID] = @ContractMiscellaneousChargeID, [ContractID] = @ContractID, [MiscellaneousChargeID] = @MiscellaneousChargeID, [MiscellaneousChargeAmount] = @MiscellaneousChargeAmount WHERE (([ContractMiscellaneousChargeID] = @Original_ContractMiscellaneousChargeID));
                      SELECT ContractMiscellaneousChargeID, ContractID, MiscellaneousChargeID, MiscellaneousChargeAmount FROM Sales.ContractMiscellaneousCharge WHERE (ContractMiscellaneousChargeID = @ContractMiscellaneousChargeID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@ContractMiscellaneousChargeID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="ContractMiscellaneousChargeID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@MiscellaneousChargeID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MiscellaneousChargeID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Currency" Direction="Input" ParameterName="@MiscellaneousChargeAmount" Precision="0" ProviderType="Money" Scale="0" Size="0" SourceColumn="MiscellaneousChargeAmount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_ContractMiscellaneousChargeID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="ContractMiscellaneousChargeID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ContractMiscellaneousChargeID" DataSetColumn="ContractMiscellaneousChargeID" />
              <Mapping SourceColumn="ContractID" DataSetColumn="ContractID" />
              <Mapping SourceColumn="MiscellaneousChargeID" DataSetColumn="MiscellaneousChargeID" />
              <Mapping SourceColumn="MiscellaneousChargeAmount" DataSetColumn="MiscellaneousChargeAmount" />
              <Mapping SourceColumn="MiscellaneousChargeName" DataSetColumn="MiscellaneousChargeName" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="StorePoolTableAdapter" GeneratorDataComponentClassName="StorePoolTableAdapter" Name="StorePool" UserDataComponentName="StorePoolTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Sales.StorePool" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Sales].[StorePool] WHERE (([StorePoolID] = @Original_StorePoolID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_StorePoolID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="StorePoolID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      INSERT INTO [Sales].[StorePool] ([StorePoolID], [StorePoolQty]) VALUES (@StorePoolID, @StorePoolQty);
                      SELECT StorePoolID, StorePoolQty FROM Sales.StorePool WHERE (StorePoolID = @StorePoolID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@StorePoolID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="StorePoolID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@StorePoolQty" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StorePoolQty" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      SELECT DISTINCT Sales.StorePool.StorePoolID, Sales.StorePool.StorePoolQty
                      FROM            Sales.StorePool INNER JOIN
                      Sales.Burst ON Sales.StorePool.StorePoolID = Sales.Burst.StorePoolID
                      WHERE        (Sales.Burst.ContractID = @ContractID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.Burst" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      UPDATE [Sales].[StorePool] SET [StorePoolID] = @StorePoolID, [StorePoolQty] = @StorePoolQty WHERE (([StorePoolID] = @Original_StorePoolID));
                      SELECT StorePoolID, StorePoolQty FROM Sales.StorePool WHERE (StorePoolID = @StorePoolID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@StorePoolID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="StorePoolID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@StorePoolQty" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StorePoolQty" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_StorePoolID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="StorePoolID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="StorePoolID" DataSetColumn="StorePoolID" />
              <Mapping SourceColumn="StorePoolQty" DataSetColumn="StorePoolQty" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="BrandFamilyMemberTableAdapter" GeneratorDataComponentClassName="BrandFamilyMemberTableAdapter" Name="BrandFamilyMember" UserDataComponentName="BrandFamilyMemberTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Client.BrandFamilyMember" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      SELECT DISTINCT NonCompetingBrand.BrandID AS NonCompetingBrandID
                      FROM            Client.BrandFamilyMember INNER JOIN
                      Client.BrandFamilyMember AS NonCompetingBrand ON Client.BrandFamilyMember.BrandFamilyID = NonCompetingBrand.BrandFamilyID
                      WHERE        (Client.BrandFamilyMember.BrandID = @BrandID)
                      UNION
                      SELECT        @BrandID AS NonCompetingBrandID
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="BrandID" ColumnName="BrandID" DataSourceName="NovaDB.Client.BrandFamilyMember" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="NonCompetingBrandID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="NonCompetingBrandID" DataSetColumn="NonCompetingBrandID" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="PeerBurstTableAdapter" GeneratorDataComponentClassName="PeerBurstTableAdapter" Name="PeerBurst" UserDataComponentName="PeerBurstTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.dbo.udfPeerBurst" DbObjectType="Function" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      SELECT        BurstID, StorePoolID, StorePoolQty, ContractNumber, Signed, BrandName, InstallStoreQty, FirstWeek, LastWeek, InstallStoreQty AS Stores, MediaName
                      FROM            dbo.udfPeerBurst(@BurstID, @FirstWeek, @LastWeek, @ChainID, @BrandID, @CategoryIDList, @MediaFamilyIDList) AS udfPeerBurst
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="BurstID" ColumnName="" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@BurstID" Precision="0" Scale="0" Size="0" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="FirstWeek" ColumnName="" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@FirstWeek" Precision="0" Scale="0" Size="0" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="LastWeek" ColumnName="" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@LastWeek" Precision="0" Scale="0" Size="0" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ChainID" ColumnName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ChainID" Precision="0" Scale="0" Size="0" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="BrandID" ColumnName="" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@BrandID" Precision="0" Scale="0" Size="0" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="CategoryIDList" ColumnName="" DataSourceName="" DataTypeServer="nvarchar(6000)" DbType="String" Direction="Input" ParameterName="@CategoryIDList" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaFamilyIDList" ColumnName="" DataSourceName="" DataTypeServer="nvarchar(6000)" DbType="String" Direction="Input" ParameterName="@MediaFamilyIDList" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Sales].[Burst] SET [BurstID] = @BurstID, [StorePoolID] = @StorePoolID WHERE (([BurstID] = @Original_BurstID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@BurstID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BurstID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@StorePoolID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="StorePoolID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_BurstID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BurstID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="BurstID" DataSetColumn="BurstID" />
              <Mapping SourceColumn="StorePoolID" DataSetColumn="StorePoolID" />
              <Mapping SourceColumn="StorePoolQty" DataSetColumn="StorePoolQty" />
              <Mapping SourceColumn="ContractNumber" DataSetColumn="ContractNumber" />
              <Mapping SourceColumn="BrandName" DataSetColumn="BrandName" />
              <Mapping SourceColumn="InstallStoreQty" DataSetColumn="InstallStoreQty" />
              <Mapping SourceColumn="FirstWeek" DataSetColumn="FirstWeek" />
              <Mapping SourceColumn="LastWeek" DataSetColumn="LastWeek" />
              <Mapping SourceColumn="Stores" DataSetColumn="Stores" />
              <Mapping SourceColumn="MediaName" DataSetColumn="MediaName" />
              <Mapping SourceColumn="Signed" DataSetColumn="Signed" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="CompetingBurstTableAdapter" GeneratorDataComponentClassName="CompetingBurstTableAdapter" Name="CompetingBurst" UserDataComponentName="CompetingBurstTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.dbo.udfCompetingBurst" DbObjectType="Function" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      SELECT        BurstID, ContractNumber, StorePoolID, BrandName, FirstWeek, LastWeek, InstallWeeks, InstallStoreQty, StorePoolQty, CreationDate, SignDate,
                      StoreListConfirmed
                      FROM            dbo.udfCompetingBurst(@FirstWeek, @LastWeek, @ChainID, @BrandID, @CategoryIDList, @MediaFamilyIDList, @BurstID) AS udfCompetingBurst
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="FirstWeek" ColumnName="" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@FirstWeek" Precision="0" Scale="0" Size="0" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="LastWeek" ColumnName="" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@LastWeek" Precision="0" Scale="0" Size="0" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ChainID" ColumnName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ChainID" Precision="0" Scale="0" Size="0" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="BrandID" ColumnName="" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@BrandID" Precision="0" Scale="0" Size="0" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="CategoryIDList" ColumnName="" DataSourceName="" DataTypeServer="nvarchar(6000)" DbType="String" Direction="Input" ParameterName="@CategoryIDList" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaFamilyIDList" ColumnName="" DataSourceName="" DataTypeServer="nvarchar(6000)" DbType="String" Direction="Input" ParameterName="@MediaFamilyIDList" Precision="0" Scale="0" Size="1024" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="BurstID" ColumnName="" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@BurstID" Precision="0" Scale="0" Size="0" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="BurstID" DataSetColumn="BurstID" />
              <Mapping SourceColumn="ContractNumber" DataSetColumn="ContractNumber" />
              <Mapping SourceColumn="StorePoolID" DataSetColumn="StorePoolID" />
              <Mapping SourceColumn="BrandName" DataSetColumn="BrandName" />
              <Mapping SourceColumn="FirstWeek" DataSetColumn="FirstWeek" />
              <Mapping SourceColumn="LastWeek" DataSetColumn="LastWeek" />
              <Mapping SourceColumn="InstallWeeks" DataSetColumn="InstallWeeks" />
              <Mapping SourceColumn="InstallStoreQty" DataSetColumn="InstallStoreQty" />
              <Mapping SourceColumn="StorePoolQty" DataSetColumn="StorePoolQty" />
              <Mapping SourceColumn="CreationDate" DataSetColumn="CreationDate" />
              <Mapping SourceColumn="SignDate" DataSetColumn="SignDate" />
              <Mapping SourceColumn="StoreListConfirmed" DataSetColumn="StoreListConfirmed" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="StoreListTableAdapter" GeneratorDataComponentClassName="StoreListTableAdapter" Name="StoreList" UserDataComponentName="StoreListTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Sales.StoreList" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Sales].[StoreList] WHERE (([BurstID] = @Original_BurstID) AND ([StoreID] = @Original_StoreID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_BurstID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BurstID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_StoreID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StoreID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      INSERT INTO [Sales].[StoreList] ([BurstID], [StoreID]) VALUES (@BurstID, @StoreID);
                      SELECT BurstID, StoreID FROM Sales.StoreList WHERE (BurstID = @BurstID) AND (StoreID = @StoreID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@BurstID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BurstID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@StoreID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StoreID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      SELECT        Sales.StoreList.BurstID, Sales.StoreList.StoreID, Store.Store.StoreNumber + N' ' + Sales.Burst.ChainName + N' ' + Store.Store.StoreName AS StoreDescription
                      FROM            Sales.StoreList INNER JOIN
                      Sales.Burst ON Sales.StoreList.BurstID = Sales.Burst.BurstID INNER JOIN
                      Store.Store ON Sales.StoreList.StoreID = Store.Store.StoreID
                      WHERE        (Sales.Burst.ContractID = @ContractID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.Burst" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      UPDATE [Sales].[StoreList] SET [BurstID] = @BurstID, [StoreID] = @StoreID WHERE (([BurstID] = @Original_BurstID) AND ([StoreID] = @Original_StoreID));
                      SELECT BurstID, StoreID FROM Sales.StoreList WHERE (BurstID = @BurstID) AND (StoreID = @StoreID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@BurstID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BurstID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@StoreID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StoreID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_BurstID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BurstID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_StoreID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StoreID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="BurstID" DataSetColumn="BurstID" />
              <Mapping SourceColumn="StoreID" DataSetColumn="StoreID" />
              <Mapping SourceColumn="StoreDescription" DataSetColumn="StoreDescription" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Sales.StoreList" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillByBurst" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetDataByBurst" GeneratorSourceName="FillByBurst" GetMethodModifier="Public" GetMethodName="GetDataByBurst" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataByBurst" UserSourceName="FillByBurst">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>
                      SELECT        Sales.StoreList.BurstID, Sales.StoreList.StoreID, Store.Store.StoreNumber + N' ' + Sales.Burst.ChainName + N' ' + Store.Store.StoreName AS StoreDecription
                      FROM            Sales.StoreList INNER JOIN
                      Sales.Burst ON Sales.StoreList.BurstID = Sales.Burst.BurstID INNER JOIN
                      Store.Store ON Sales.StoreList.StoreID = Store.Store.StoreID
                      WHERE        (Sales.StoreList.BurstID = @BurstID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="BurstID" ColumnName="BurstID" DataSourceName="NovaDB.Sales.StoreList" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@BurstID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="BurstID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="StoreTableAdapter" GeneratorDataComponentClassName="StoreTableAdapter" Name="Store" UserDataComponentName="StoreTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      SELECT DISTINCT
                      Store.Store.StoreID, Store.Store.StoreNumber, Store.Store.StoreName, City.CityName, Store.Region.RegionName, ISNULL(dtMediaAllowed.MediaAllowed, 0) AS MediaAllowed, CONVERT(bit, 0) AS StoreListMember,
                      ISNULL(dtStorePoolMember.StorePoolMember, 0) AS StorePoolMember, Store.Store.Dormant, ISNULL(dtStoreTakenByBurst.Taken, 0) AS Taken, CONVERT(bit, 0) AS IndependentStoreListMember, CONVERT(bit, 1 - CONVERT(int,
                      ISNULL(dtMediaAllowed.MediaAllowed, 0))) AS MediaNotAllowed, dbo.udfGetStoreGroup(Store.Store.StoreID) AS StoreGroup, ISNULL
                      ((SELECT        TOP (1) CASE WHEN ISNULL(ID, 0) &gt; 0 THEN CAST(1 AS BIT) ELSE CAST(0 AS BIT) END AS Expr1
                      FROM            Store.StoreMediaCategoryPermission AS SC
                      WHERE        (StoreID = Store.Store.StoreID) AND (MediaID = @MediaID) AND (CategoryID IN
                      (SELECT        CategoryID
                      FROM            Media.MediaCategory AS MC
                      WHERE        (MediaID = @MediaID)))), 0) AS MediaAllowedInCategoryForStore
                      FROM            Store.Store INNER JOIN
                      Store.Region ON Store.Store.RegionID = Store.Region.RegionID INNER JOIN
                      City ON Store.Store.PhysicalAddressCityID = City.CityID LEFT OUTER JOIN
                      (SELECT        TOP (100) PERCENT StoreList_1.StoreID, CONVERT(bit, 1) AS Taken
                      FROM            Sales.vEffectiveBurst AS MyBurst INNER JOIN
                      Sales.vEffectiveBurst AS OverlappingBurst ON MyBurst.BurstID &lt;&gt; OverlappingBurst.BurstID AND MyBurst.ChainID = OverlappingBurst.ChainID AND MyBurst.CategoryID = OverlappingBurst.CategoryID AND
                      MyBurst.Homesite = OverlappingBurst.Homesite AND MyBurst.StorePoolID &lt;&gt; OverlappingBurst.StorePoolID AND MyBurst.MediaFamilyID = OverlappingBurst.MediaFamilyID AND
                      MyBurst.FirstWeek &lt;= OverlappingBurst.LastInstallWeek AND MyBurst.LastInstallWeek &gt;= OverlappingBurst.FirstWeek INNER JOIN
                      Sales.StoreList AS StoreList_1 ON OverlappingBurst.BurstID = StoreList_1.BurstID
                      WHERE        (OverlappingBurst.Signed = 1) AND (OverlappingBurst.Cancelled = 0) AND (MyBurst.Homesite = 1) AND (MyBurst.BurstID = @BurstID) AND (OverlappingBurst.Homesite = 1)
                      ORDER BY MyBurst.BurstID) AS dtStoreTakenByBurst ON Store.Store.StoreID = dtStoreTakenByBurst.StoreID LEFT OUTER JOIN
                      (SELECT        Sales.StoreList.StoreID, CONVERT(bit, 1) AS StorePoolMember
                      FROM            Sales.Burst INNER JOIN
                      Sales.StoreList ON Sales.Burst.BurstID = Sales.StoreList.BurstID INNER JOIN
                      Sales.Contract ON Sales.Burst.ContractID = Sales.Contract.ContractID
                      WHERE        (Sales.Burst.ContractID &lt;&gt; @ContractID) AND (Sales.Burst.StorePoolID = @StorePoolID) AND (Sales.Contract.Cancelled = 0)) AS dtStorePoolMember ON
                      Store.Store.StoreID = dtStorePoolMember.StoreID LEFT OUTER JOIN
                      (SELECT        StoreID, CONVERT(bit, 1) AS MediaAllowed, MediaID
                      FROM            Store.StoreMedia
                      WHERE        (MediaID = @MediaID)) AS dtMediaAllowed ON Store.Store.StoreID = dtMediaAllowed.StoreID
                      WHERE  (Store.Region.ChainID IN
                      (SELECT ChainId
                      FROM      dbo.udfGetChainsByGroup(@ChainID) AS udfGetChainsByGroup_1))
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ChainID" ColumnName="ChainID" DataSourceName="NovaDB.Store.Region" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ChainID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ChainID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaID" ColumnName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaID" Precision="0" Scale="0" Size="4" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="BurstID" ColumnName="" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@BurstID" Precision="0" Scale="0" Size="16" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" Scale="0" Size="16" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="StorePoolID" ColumnName="" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@StorePoolID" Precision="0" Scale="0" Size="16" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="StoreID" DataSetColumn="StoreID" />
              <Mapping SourceColumn="StoreNumber" DataSetColumn="StoreNumber" />
              <Mapping SourceColumn="StoreName" DataSetColumn="StoreName" />
              <Mapping SourceColumn="CityName" DataSetColumn="CityName" />
              <Mapping SourceColumn="RegionName" DataSetColumn="RegionName" />
              <Mapping SourceColumn="MediaAllowed" DataSetColumn="MediaAllowed" />
              <Mapping SourceColumn="StoreListMember" DataSetColumn="StoreListMember" />
              <Mapping SourceColumn="StorePoolMember" DataSetColumn="StorePoolMember" />
              <Mapping SourceColumn="Dormant" DataSetColumn="Dormant" />
              <Mapping SourceColumn="Taken" DataSetColumn="Taken" />
              <Mapping SourceColumn="IndependentStoreListMember" DataSetColumn="IndependentStoreListMember" />
              <Mapping SourceColumn="MediaNotAllowed" DataSetColumn="MediaNotAllowed" />
              <Mapping SourceColumn="StoreGroup" DataSetColumn="StoreGroup" />
              <Mapping SourceColumn="MediaAllowedInCategoryForStore" DataSetColumn="MediaAllowedInCategoryForStore" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="IndependentStoreListMemberTableAdapter" GeneratorDataComponentClassName="IndependentStoreListMemberTableAdapter" Name="IndependentStoreListMember" UserDataComponentName="IndependentStoreListMemberTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Sales.IndependentStoreListMember" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Sales].[IndependentStoreListMember] WHERE (([IndependentStoreListID] = @Original_IndependentStoreListID) AND ([StoreID] = @Original_StoreID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_IndependentStoreListID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="IndependentStoreListID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_StoreID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StoreID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      INSERT INTO [Sales].[IndependentStoreListMember] ([IndependentStoreListID], [StoreID]) VALUES (@IndependentStoreListID, @StoreID);
                      SELECT IndependentStoreListID, StoreID FROM Sales.IndependentStoreListMember WHERE (IndependentStoreListID = @IndependentStoreListID) AND (StoreID = @StoreID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@IndependentStoreListID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="IndependentStoreListID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@StoreID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StoreID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      SELECT        IndependentStoreListID, StoreID
                      FROM            Sales.IndependentStoreListMember
                      WHERE        (IndependentStoreListID = @IndependentStoreListID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="IndependentStoreListID" ColumnName="IndependentStoreListID" DataSourceName="NovaDB.Sales.IndependentStoreListMember" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@IndependentStoreListID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="IndependentStoreListID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      UPDATE [Sales].[IndependentStoreListMember] SET [IndependentStoreListID] = @IndependentStoreListID, [StoreID] = @StoreID WHERE (([IndependentStoreListID] = @Original_IndependentStoreListID) AND ([StoreID] = @Original_StoreID));
                      SELECT IndependentStoreListID, StoreID FROM Sales.IndependentStoreListMember WHERE (IndependentStoreListID = @IndependentStoreListID) AND (StoreID = @StoreID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@IndependentStoreListID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="IndependentStoreListID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@StoreID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StoreID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_IndependentStoreListID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="IndependentStoreListID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_StoreID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StoreID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="IndependentStoreListID" DataSetColumn="IndependentStoreListID" />
              <Mapping SourceColumn="StoreID" DataSetColumn="StoreID" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="IndependentStoreListTableAdapter" GeneratorDataComponentClassName="IndependentStoreListTableAdapter" Name="IndependentStoreList" UserDataComponentName="IndependentStoreListTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Sales.IndependentStoreList" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Sales].[IndependentStoreList] WHERE (([IndependentStoreListID] = @Original_IndependentStoreListID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_IndependentStoreListID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="IndependentStoreListID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      INSERT INTO Sales.IndependentStoreList
                      (IndependentStoreListID, IndependentStoreListName)
                      VALUES        (@IndependentStoreListID,@IndependentStoreListName);
                      SELECT IndependentStoreListID, IndependentStoreListName, principal_id FROM Sales.IndependentStoreList WHERE (IndependentStoreListID = @IndependentStoreListID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="IndependentStoreListID" ColumnName="IndependentStoreListID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@IndependentStoreListID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="IndependentStoreListID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="IndependentStoreListName" ColumnName="IndependentStoreListName" DataSourceName="" DataTypeServer="nvarchar(250)" DbType="String" Direction="Input" ParameterName="@IndependentStoreListName" Precision="0" ProviderType="NVarChar" Scale="0" Size="250" SourceColumn="IndependentStoreListName" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      SELECT        IndependentStoreListID, IndependentStoreListName
                      FROM            Sales.IndependentStoreList
                    </CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      UPDATE       Sales.IndependentStoreList
                      SET                IndependentStoreListID = @IndependentStoreListID, IndependentStoreListName = @IndependentStoreListName
                      WHERE        (IndependentStoreListID = @Original_IndependentStoreListID);
                      SELECT IndependentStoreListID, IndependentStoreListName, principal_id FROM Sales.IndependentStoreList WHERE (IndependentStoreListID = @IndependentStoreListID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="IndependentStoreListID" ColumnName="IndependentStoreListID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@IndependentStoreListID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="IndependentStoreListID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="IndependentStoreListName" ColumnName="IndependentStoreListName" DataSourceName="" DataTypeServer="nvarchar(250)" DbType="String" Direction="Input" ParameterName="@IndependentStoreListName" Precision="0" ProviderType="NVarChar" Scale="0" Size="250" SourceColumn="IndependentStoreListName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_IndependentStoreListID" ColumnName="IndependentStoreListID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@Original_IndependentStoreListID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="IndependentStoreListID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="IndependentStoreListID" DataSetColumn="IndependentStoreListID" />
              <Mapping SourceColumn="IndependentStoreListName" DataSetColumn="IndependentStoreListName" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ValidMediaTableAdapter" GeneratorDataComponentClassName="ValidMediaTableAdapter" Name="ValidMedia" UserDataComponentName="ValidMediaTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Media.Media" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      SELECT        Media.Media.MediaID, ISNULL(dtValidMedia.Valid, 0) AS Valid
                      FROM            (SELECT        MediaID, CONVERT(bit, 1) AS Valid
                      FROM            Media.MediaLifeCycle
                      WHERE        (FirstWeek &lt;= @StartDate) AND (LastWeek &gt;= @EndDate)) AS dtValidMedia RIGHT OUTER JOIN
                      Media.Media ON dtValidMedia.MediaID = Media.Media.MediaID
                      WHERE        (Media.Media.MediaID = @MediaID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaID" ColumnName="MediaID" DataSourceName="NovaDB.Media.Media" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="StartDate" ColumnName="" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@StartDate" Precision="0" Scale="0" Size="8" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="EndDate" ColumnName="" DataSourceName="" DataTypeServer="datetime" DbType="DateTime" Direction="Input" ParameterName="@EndDate" Precision="0" Scale="0" Size="8" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MediaID" DataSetColumn="MediaID" />
              <Mapping SourceColumn="Valid" DataSetColumn="Valid" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ResearchCategoryTableAdapter" GeneratorDataComponentClassName="ResearchCategoryTableAdapter" Name="ResearchCategory" UserDataComponentName="ResearchCategoryTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Sales].[ResearchCategory] WHERE (([ResearchCategoryID] = @Original_ResearchCategoryID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_ResearchCategoryID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="ResearchCategoryID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      INSERT INTO Sales.ResearchCategory
                      (ResearchCategoryID, ContractID, CategoryID, FromDate, Months, Fee, Discount)
                      VALUES        (@ResearchCategoryID,@ContractID,@CategoryID,@FromDate,@Months,@Fee,@Discount);
                      SELECT ResearchCategoryID, ContractID, CategoryID, FromDate, Months, Fee FROM Sales.ResearchCategory WHERE (ResearchCategoryID = @ResearchCategoryID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ResearchCategoryID" ColumnName="ResearchCategoryID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ResearchCategoryID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ResearchCategoryID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="CategoryID" ColumnName="CategoryID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="FromDate" ColumnName="FromDate" DataSourceName="" DataTypeServer="date" DbType="AnsiString" Direction="Input" ParameterName="@FromDate" Precision="0" ProviderType="Date" Scale="0" Size="3" SourceColumn="FromDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Months" ColumnName="Months" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@Months" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="Months" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Fee" ColumnName="Fee" DataSourceName="" DataTypeServer="decimal(18, 4)" DbType="Decimal" Direction="Input" ParameterName="@Fee" Precision="18" ProviderType="Decimal" Scale="4" Size="9" SourceColumn="Fee" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Discount" ColumnName="Discount" DataSourceName="" DataTypeServer="decimal(5, 2)" DbType="Decimal" Direction="Input" ParameterName="@Discount" Precision="5" ProviderType="Decimal" Scale="2" Size="5" SourceColumn="Discount" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      SELECT        Sales.ResearchCategory.ResearchCategoryID, Sales.ResearchCategory.ContractID, Sales.ResearchCategory.CategoryID, Sales.ResearchCategory.FromDate,
                      Sales.ResearchCategory.Months, Sales.ResearchCategory.Fee, Sales.ResearchCategory.Discount,
                      Sales.ResearchCategory.Fee - Sales.ResearchCategory.Discount / 100 * Sales.ResearchCategory.Fee AS NetFee,
                      { fn MONTHNAME(Sales.ResearchCategory.FromDate) } + ' ' + CONVERT(nchar(4), YEAR(Sales.ResearchCategory.FromDate)) AS FirstMonth,
                      { fn MONTHNAME(DATEADD(m, Sales.ResearchCategory.Months - 1, Sales.ResearchCategory.FromDate)) } + ' ' + CONVERT(nchar(4), YEAR(DATEADD(m,
                      Sales.ResearchCategory.Months - 1, Sales.ResearchCategory.FromDate))) AS LastMonth, Store.Category.CategoryName
                      FROM            Sales.ResearchCategory INNER JOIN
                      Store.Category ON Sales.ResearchCategory.CategoryID = Store.Category.CategoryID
                      WHERE        (Sales.ResearchCategory.ContractID = @ContractID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.ResearchCategory" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      UPDATE       Sales.ResearchCategory
                      SET                ResearchCategoryID = @ResearchCategoryID, ContractID = @ContractID, CategoryID = @CategoryID, FromDate = @FromDate, Months = @Months, Fee = @Fee,
                      Discount = @Discount
                      WHERE        (ResearchCategoryID = @Original_ResearchCategoryID);
                      SELECT ResearchCategoryID, ContractID, CategoryID, FromDate, Months, Fee FROM Sales.ResearchCategory WHERE (ResearchCategoryID = @ResearchCategoryID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ResearchCategoryID" ColumnName="ResearchCategoryID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ResearchCategoryID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ResearchCategoryID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="CategoryID" ColumnName="CategoryID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="FromDate" ColumnName="FromDate" DataSourceName="" DataTypeServer="date" DbType="AnsiString" Direction="Input" ParameterName="@FromDate" Precision="0" ProviderType="Date" Scale="0" Size="3" SourceColumn="FromDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Months" ColumnName="Months" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@Months" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="Months" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Fee" ColumnName="Fee" DataSourceName="" DataTypeServer="decimal(18, 4)" DbType="Decimal" Direction="Input" ParameterName="@Fee" Precision="18" ProviderType="Decimal" Scale="4" Size="9" SourceColumn="Fee" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Discount" ColumnName="Discount" DataSourceName="" DataTypeServer="decimal(5, 2)" DbType="Decimal" Direction="Input" ParameterName="@Discount" Precision="5" ProviderType="Decimal" Scale="2" Size="5" SourceColumn="Discount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_ResearchCategoryID" ColumnName="ResearchCategoryID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@Original_ResearchCategoryID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ResearchCategoryID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ResearchCategoryID" DataSetColumn="ResearchCategoryID" />
              <Mapping SourceColumn="ContractID" DataSetColumn="ContractID" />
              <Mapping SourceColumn="CategoryID" DataSetColumn="CategoryID" />
              <Mapping SourceColumn="Months" DataSetColumn="Months" />
              <Mapping SourceColumn="Fee" DataSetColumn="Fee" />
              <Mapping SourceColumn="FromDate" DataSetColumn="FromDate" />
              <Mapping SourceColumn="FirstMonth" DataSetColumn="FirstMonth" />
              <Mapping SourceColumn="LastMonth" DataSetColumn="LastMonth" />
              <Mapping SourceColumn="CategoryName" DataSetColumn="CategoryName" />
              <Mapping SourceColumn="Discount" DataSetColumn="Discount" />
              <Mapping SourceColumn="NetFee" DataSetColumn="NetFee" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="BillingInstructionTableAdapter" GeneratorDataComponentClassName="BillingInstructionTableAdapter" Name="BillingInstruction" UserDataComponentName="BillingInstructionTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Sales].[BillingInstruction] WHERE (([BillingInstructionID] = @Original_BillingInstructionID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_BillingInstructionID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BillingInstructionID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      INSERT INTO [Sales].[BillingInstruction] ([BillingInstructionID], [ContractID], [PeriodID], [Amount], [PONumber]) VALUES (@BillingInstructionID, @ContractID, @PeriodID, @Amount, @PONumber);
                      SELECT BillingInstructionID, ContractID, PeriodID, Amount, PONumber FROM Sales.BillingInstruction WHERE (BillingInstructionID = @BillingInstructionID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@BillingInstructionID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BillingInstructionID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@PeriodID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="PeriodID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Amount" Precision="18" ProviderType="Decimal" Scale="4" Size="0" SourceColumn="Amount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@PONumber" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="PONumber" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      SELECT        Sales.BillingInstruction.BillingInstructionID, Sales.BillingInstruction.ContractID, Sales.BillingInstruction.PeriodID, Sales.BillingInstruction.Amount,
                      Sales.BillingInstruction.PONumber, Finance.Period.PeriodName
                      FROM            Sales.BillingInstruction INNER JOIN
                      Finance.Period ON Sales.BillingInstruction.PeriodID = Finance.Period.PeriodID
                      WHERE        (Sales.BillingInstruction.ContractID = @ContractID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.BillingInstruction" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      UPDATE [Sales].[BillingInstruction] SET [BillingInstructionID] = @BillingInstructionID, [ContractID] = @ContractID, [PeriodID] = @PeriodID, [Amount] = @Amount, [PONumber] = @PONumber WHERE (([BillingInstructionID] = @Original_BillingInstructionID));
                      SELECT BillingInstructionID, ContractID, PeriodID, Amount, PONumber FROM Sales.BillingInstruction WHERE (BillingInstructionID = @BillingInstructionID)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@BillingInstructionID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BillingInstructionID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@PeriodID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="PeriodID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Amount" Precision="18" ProviderType="Decimal" Scale="4" Size="0" SourceColumn="Amount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@PONumber" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="PONumber" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_BillingInstructionID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BillingInstructionID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="BillingInstructionID" DataSetColumn="BillingInstructionID" />
              <Mapping SourceColumn="ContractID" DataSetColumn="ContractID" />
              <Mapping SourceColumn="PeriodID" DataSetColumn="PeriodID" />
              <Mapping SourceColumn="Amount" DataSetColumn="Amount" />
              <Mapping SourceColumn="PONumber" DataSetColumn="PONumber" />
              <Mapping SourceColumn="PeriodName" DataSetColumn="PeriodName" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ContractClassificaitonTableAdapter" GeneratorDataComponentClassName="ContractClassificaitonTableAdapter" Name="ContractClassificaiton" UserDataComponentName="ContractClassificaitonTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Sales.ContractClassificaiton" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Sales].[ContractClassificaiton] WHERE (([ContractClassificationId] = @Original_ContractClassificationId) AND ([Classification] = @Original_Classification))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ContractClassificationId" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ContractClassificationId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Classification" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Classification" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      INSERT INTO [Sales].[ContractClassificaiton] ([Classification]) VALUES (@Classification);
                      SELECT ContractClassificationId, Classification FROM Sales.ContractClassificaiton WHERE (ContractClassificationId = SCOPE_IDENTITY())
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Classification" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Classification" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>
                      SELECT ContractClassificationId, Classification
                      FROM     Sales.ContractClassificaiton
                    </CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>
                      UPDATE [Sales].[ContractClassificaiton] SET [Classification] = @Classification WHERE (([ContractClassificationId] = @Original_ContractClassificationId) AND ([Classification] = @Original_Classification));
                      SELECT ContractClassificationId, Classification FROM Sales.ContractClassificaiton WHERE (ContractClassificationId = @ContractClassificationId)
                    </CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Classification" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Classification" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ContractClassificationId" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ContractClassificationId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Classification" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Classification" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractClassificationId" ColumnName="ContractClassificationId" DataSourceName="NovaDB.Sales.ContractClassificaiton" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ContractClassificationId" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ContractClassificationId" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ContractClassificationId" DataSetColumn="ContractClassificationId" />
              <Mapping SourceColumn="Classification" DataSetColumn="Classification" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="BurstInstallationDayTableAdapter" GeneratorDataComponentClassName="BurstInstallationDayTableAdapter" Name="BurstInstallationDay" UserDataComponentName="BurstInstallationDayTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Sales].[BurstInstallationDay] WHERE (([BurstID] = @Original_BurstID) AND ([InstallationDayID] = @Original_InstallationDayID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_BurstID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BurstID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_InstallationDayID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="InstallationDayID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO Sales.BurstInstallationDay
                  (BurstID, InstallationDayID)
VALUES (@BurstId,@InstallationDayID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="BurstId" ColumnName="BurstID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@BurstId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="BurstID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="InstallationDayID" ColumnName="InstallationDayID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@InstallationDayID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="InstallationDayID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT Sales.BurstInstallationDay.BurstID, Sales.BurstInstallationDay.InstallationDayID, Media.MediaInstallationDay.MediaInstallationDayName AS InstallationDayName
FROM     Sales.BurstInstallationDay INNER JOIN
                  Sales.Burst ON Sales.Burst.BurstID = Sales.BurstInstallationDay.BurstID INNER JOIN
                  Media.MediaInstallationDay ON Media.MediaInstallationDay.InstallationDayID = Sales.BurstInstallationDay.InstallationDayID
WHERE  (Sales.Burst.ContractID = @ContractID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.Burst" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Sales].[BurstInstallationDay] SET [BurstID] = @BurstID, [InstallationDayID] = @InstallationDayID WHERE (([BurstID] = @Original_BurstID) AND ([InstallationDayID] = @Original_InstallationDayID));
SELECT BurstID, InstallationDayID FROM Sales.BurstInstallationDay WHERE (BurstID = @BurstID) AND (InstallationDayID = @InstallationDayID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@BurstID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BurstID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@InstallationDayID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="InstallationDayID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_BurstID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BurstID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_InstallationDayID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="InstallationDayID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="BurstID" DataSetColumn="BurstID" />
              <Mapping SourceColumn="InstallationDayID" DataSetColumn="InstallationDayID" />
              <Mapping SourceColumn="InstallationDayName" DataSetColumn="InstallationDayName" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="BurstPcaStatusTableAdapter" GeneratorDataComponentClassName="BurstPcaStatusTableAdapter" Name="BurstPcaStatus" UserDataComponentName="BurstPcaStatusTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>DELETE FROM Sales.BurstPcaStatus
WHERE  (BurstID = @Original_BurstID) AND (PcaStatusId = @Original_PcaStatusId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_BurstID" ColumnName="BurstID" DataSourceName="NovaDB.Sales.BurstPcaStatus" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@Original_BurstID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="BurstID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_PcaStatusId" ColumnName="PcaStatusId" DataSourceName="NovaDB.Sales.BurstPcaStatus" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@Original_PcaStatusId" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="PcaStatusId" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>INSERT INTO Sales.BurstPcaStatus
                  (BurstID, PcaStatusId)
VALUES (@BurstId,@PcaStatusID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="BurstId" ColumnName="BurstID" DataSourceName="NovaDB.Sales.BurstPcaStatus" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@BurstId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="BurstID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="PcaStatusID" ColumnName="PcaStatusId" DataSourceName="NovaDB.Sales.BurstPcaStatus" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@PcaStatusID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="PcaStatusId" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT Sales.BurstPcaStatus.BurstID, Sales.BurstPcaStatus.PcaStatusId, Media.PcaStatus.PcaStatusName, Sales.BurstPcaStatus.CreationDate
FROM     Sales.BurstPcaStatus INNER JOIN
                  Sales.Burst ON Sales.Burst.BurstID = Sales.BurstPcaStatus.BurstID INNER JOIN
                  Media.PcaStatus ON Sales.BurstPcaStatus.PcaStatusId = Media.PcaStatus.PcaStatusId
WHERE  (Sales.Burst.ContractID = @ContractId)
ORDER BY Sales.BurstPcaStatus.CreationDate DESC</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractId" ColumnName="ContractID" DataSourceName="NovaDB.Sales.Burst" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="BurstID" DataSetColumn="BurstID" />
              <Mapping SourceColumn="PcaStatusId" DataSetColumn="PcaStatusId" />
              <Mapping SourceColumn="PcaStatusName" DataSetColumn="PcaStatusName" />
              <Mapping SourceColumn="CreationDate" DataSetColumn="CreationDate" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="MediaCostTableAdapter" GeneratorDataComponentClassName="MediaCostTableAdapter" Name="MediaCost" UserDataComponentName="MediaCostTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Sales.vMediaCosts" DbObjectType="View" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <InsertCommand>
                  <DbCommand CommandType="StoredProcedure" ModifiedByUser="false">
                    <CommandText>createContractMediaCost</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="ReturnValue" ParameterName="@RETURN_VALUE" Precision="10" ProviderType="Int" Scale="0" Size="4" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="varchar" DbType="AnsiString" Direction="Input" ParameterName="@MediaName" Precision="0" ProviderType="VarChar" Scale="0" Size="255" SourceColumn="MediaName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="" DataSourceName="" DataTypeServer="decimal" DbType="Decimal" Direction="Input" ParameterName="@MediaCost" Precision="18" ProviderType="Decimal" Scale="2" Size="9" SourceColumn="MediaCostInputed" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT ContractID, MediaName, MediaCostSystemCalculated, MediaCostInputed
FROM     Sales.vMediaCosts
WHERE  (ContractID = @ContractId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractId" ColumnName="ContractID" DataSourceName="NovaDB.Sales.vMediaCosts" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE Sales.ContractMediaCost
SET          MediaCost = @MediaCostInputed
FROM     Sales.ContractMediaCost INNER JOIN
                  Media.Media ON Sales.ContractMediaCost.MediaID = Media.Media.MediaID
WHERE  (Sales.ContractMediaCost.MediaID =
                      (SELECT TOP (1) Media.Media.MediaID
                       WHERE   (Media.Media.MediaName = @MediaName))) AND (Sales.ContractMediaCost.ContractID = @Contractid)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaCostInputed" ColumnName="MediaCost" DataSourceName="" DataTypeServer="decimal(18, 4)" DbType="Decimal" Direction="Input" ParameterName="@MediaCostInputed" Precision="18" ProviderType="Decimal" Scale="4" Size="9" SourceColumn="MediaCostInputed" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Contractid" ColumnName="ContractID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@Contractid" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaName" ColumnName="MediaName" DataSourceName="" DataTypeServer="nvarchar(200)" DbType="String" Direction="Input" ParameterName="@MediaName" Precision="0" ProviderType="NVarChar" Scale="0" Size="200" SourceColumn="MediaName" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MediaName" DataSetColumn="MediaName" />
              <Mapping SourceColumn="MediaCostSystemCalculated" DataSetColumn="MediaCostSystemCalculated" />
              <Mapping SourceColumn="MediaCostInputed" DataSetColumn="MediaCostInputed" />
              <Mapping SourceColumn="ContractID" DataSetColumn="ContractID" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ContractInvoicesTableAdapter" GeneratorDataComponentClassName="ContractInvoicesTableAdapter" Name="ContractInvoices" UserDataComponentName="ContractInvoicesTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Sales.ContractInvoices" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Sales].[ContractInvoices] WHERE (([ContractInvoiceNumber] = @Original_ContractInvoiceNumber) AND ([ContractID] = @Original_ContractID) AND ([InvoiceNumber] = @Original_InvoiceNumber) AND ([InvoiceAmount] = @Original_InvoiceAmount))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ContractInvoiceNumber" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ContractInvoiceNumber" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_InvoiceNumber" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="InvoiceNumber" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_InvoiceAmount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="InvoiceAmount" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>INSERT INTO Sales.ContractInvoices
                  (ContractID, InvoiceNumber, InvoiceAmount, MediaId)
VALUES (@ContractID,@InvoiceNumber,@InvoiceAmount,@MediaId); 
SELECT ContractInvoiceNumber, ContractID, InvoiceNumber, InvoiceAmount FROM Sales.ContractInvoices WHERE (ContractInvoiceNumber = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.ContractInvoices" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="InvoiceNumber" ColumnName="InvoiceNumber" DataSourceName="NovaDB.Sales.ContractInvoices" DataTypeServer="varchar(255)" DbType="AnsiString" Direction="Input" ParameterName="@InvoiceNumber" Precision="0" ProviderType="VarChar" Scale="0" Size="255" SourceColumn="InvoiceNumber" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="InvoiceAmount" ColumnName="InvoiceAmount" DataSourceName="NovaDB.Sales.ContractInvoices" DataTypeServer="decimal(18, 2)" DbType="Decimal" Direction="Input" ParameterName="@InvoiceAmount" Precision="18" ProviderType="Decimal" Scale="2" Size="9" SourceColumn="InvoiceAmount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="MediaId" ColumnName="MediaId" DataSourceName="NovaDB.Sales.ContractInvoices" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaId" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaId" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT Sales.ContractInvoices.ContractInvoiceNumber, Sales.ContractInvoices.ContractID, Sales.ContractInvoices.InvoiceNumber, Sales.ContractInvoices.InvoiceAmount, ISNULL(Media.Media.MediaName, 'Select...') AS MediaName
FROM     Sales.ContractInvoices LEFT OUTER JOIN
                  Media.Media ON Media.Media.MediaID = Sales.ContractInvoices.MediaId
WHERE  (Sales.ContractInvoices.ContractID = @ContractID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.ContractInvoices" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>UPDATE Sales.ContractInvoices
SET          ContractID = @ContractID, InvoiceNumber = @InvoiceNumber, InvoiceAmount = @InvoiceAmount, MediaId = @MediaId
WHERE  (ContractInvoiceNumber = @Original_ContractInvoiceNumber) AND (ContractID = @Original_ContractID) AND (InvoiceNumber = @Original_InvoiceNumber) AND (InvoiceAmount = @Original_InvoiceAmount); 
SELECT ContractInvoiceNumber, ContractID, InvoiceNumber, InvoiceAmount FROM Sales.ContractInvoices WHERE (ContractInvoiceNumber = @ContractInvoiceNumber)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.ContractInvoices" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="InvoiceNumber" ColumnName="InvoiceNumber" DataSourceName="NovaDB.Sales.ContractInvoices" DataTypeServer="varchar(255)" DbType="AnsiString" Direction="Input" ParameterName="@InvoiceNumber" Precision="0" ProviderType="VarChar" Scale="0" Size="255" SourceColumn="InvoiceNumber" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="InvoiceAmount" ColumnName="InvoiceAmount" DataSourceName="NovaDB.Sales.ContractInvoices" DataTypeServer="decimal(18, 2)" DbType="Decimal" Direction="Input" ParameterName="@InvoiceAmount" Precision="18" ProviderType="Decimal" Scale="2" Size="9" SourceColumn="InvoiceAmount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="MediaId" ColumnName="MediaId" DataSourceName="NovaDB.Sales.ContractInvoices" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaId" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_ContractInvoiceNumber" ColumnName="ContractInvoiceNumber" DataSourceName="NovaDB.Sales.ContractInvoices" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@Original_ContractInvoiceNumber" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ContractInvoiceNumber" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.ContractInvoices" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@Original_ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_InvoiceNumber" ColumnName="InvoiceNumber" DataSourceName="NovaDB.Sales.ContractInvoices" DataTypeServer="varchar(255)" DbType="AnsiString" Direction="Input" ParameterName="@Original_InvoiceNumber" Precision="0" ProviderType="VarChar" Scale="0" Size="255" SourceColumn="InvoiceNumber" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_InvoiceAmount" ColumnName="InvoiceAmount" DataSourceName="NovaDB.Sales.ContractInvoices" DataTypeServer="decimal(18, 2)" DbType="Decimal" Direction="Input" ParameterName="@Original_InvoiceAmount" Precision="18" ProviderType="Decimal" Scale="2" Size="9" SourceColumn="InvoiceAmount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractInvoiceNumber" ColumnName="ContractInvoiceNumber" DataSourceName="NovaDB.Sales.ContractInvoices" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ContractInvoiceNumber" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ContractInvoiceNumber" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ContractInvoiceNumber" DataSetColumn="ContractInvoiceNumber" />
              <Mapping SourceColumn="ContractID" DataSetColumn="ContractID" />
              <Mapping SourceColumn="InvoiceNumber" DataSetColumn="InvoiceNumber" />
              <Mapping SourceColumn="InvoiceAmount" DataSetColumn="InvoiceAmount" />
              <Mapping SourceColumn="MediaName" DataSetColumn="MediaName" />
              <Mapping SourceColumn="MediaId" DataSetColumn="MediaId" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ContractCostEstimatesTableAdapter" GeneratorDataComponentClassName="ContractCostEstimatesTableAdapter" Name="ContractCostEstimates" UserDataComponentName="ContractCostEstimatesTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Sales.ContractCostEstimates" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Sales].[ContractCostEstimates] WHERE (([CostEstimateID] = @Original_CostEstimateID) AND ([ContractID] = @Original_ContractID) AND ([CostEstimateNumber] = @Original_CostEstimateNumber) AND ([CostEstimateAmount] = @Original_CostEstimateAmount))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_CostEstimateID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CostEstimateID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_CostEstimateNumber" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="CostEstimateNumber" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_CostEstimateAmount" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="CostEstimateAmount" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO Sales.ContractCostEstimates
                  (ContractID, CostEstimateNumber, CostEstimateAmount, MediaId)
VALUES (@ContractID,@CostEstimateNumber,@CostEstimateAmount,@MediaId);   
SELECT CostEstimateID, ContractID, CostEstimateNumber, CostEstimateAmount FROM Sales.ContractCostEstimates WHERE (CostEstimateID = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="CostEstimateNumber" ColumnName="CostEstimateNumber" DataSourceName="" DataTypeServer="varchar(255)" DbType="AnsiString" Direction="Input" ParameterName="@CostEstimateNumber" Precision="0" ProviderType="VarChar" Scale="0" Size="255" SourceColumn="CostEstimateNumber" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="CostEstimateAmount" ColumnName="CostEstimateAmount" DataSourceName="" DataTypeServer="decimal(18, 2)" DbType="Decimal" Direction="Input" ParameterName="@CostEstimateAmount" Precision="18" ProviderType="Decimal" Scale="2" Size="9" SourceColumn="CostEstimateAmount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="MediaId" ColumnName="MediaId" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaId" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaId" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT Sales.ContractCostEstimates.CostEstimateID, Sales.ContractCostEstimates.ContractID, Sales.ContractCostEstimates.CostEstimateNumber, Sales.ContractCostEstimates.CostEstimateAmount, ISNULL(Media.Media.MediaName, 
                  'Select...') AS MediaName
FROM     Sales.ContractCostEstimates LEFT OUTER JOIN
                  Media.Media ON Media.Media.MediaID = Sales.ContractCostEstimates.MediaId
WHERE  (Sales.ContractCostEstimates.ContractID = @ContractID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.ContractCostEstimates" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>UPDATE Sales.ContractCostEstimates
SET          ContractID = @ContractID, CostEstimateNumber = @CostEstimateNumber, CostEstimateAmount = @CostEstimateAmount, MediaId = @MediaId
WHERE  (CostEstimateID = @Original_CostEstimateID) AND (ContractID = @Original_ContractID) AND (CostEstimateNumber = @Original_CostEstimateNumber) AND (CostEstimateAmount = @Original_CostEstimateAmount);  
SELECT CostEstimateID, ContractID, CostEstimateNumber, CostEstimateAmount FROM Sales.ContractCostEstimates WHERE (CostEstimateID = @CostEstimateID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.ContractCostEstimates" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="CostEstimateNumber" ColumnName="CostEstimateNumber" DataSourceName="NovaDB.Sales.ContractCostEstimates" DataTypeServer="varchar(255)" DbType="AnsiString" Direction="Input" ParameterName="@CostEstimateNumber" Precision="0" ProviderType="VarChar" Scale="0" Size="255" SourceColumn="CostEstimateNumber" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="CostEstimateAmount" ColumnName="CostEstimateAmount" DataSourceName="NovaDB.Sales.ContractCostEstimates" DataTypeServer="decimal(18, 2)" DbType="Decimal" Direction="Input" ParameterName="@CostEstimateAmount" Precision="18" ProviderType="Decimal" Scale="2" Size="9" SourceColumn="CostEstimateAmount" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="MediaId" ColumnName="MediaId" DataSourceName="NovaDB.Sales.ContractCostEstimates" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaId" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_CostEstimateID" ColumnName="CostEstimateID" DataSourceName="NovaDB.Sales.ContractCostEstimates" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@Original_CostEstimateID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="CostEstimateID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.ContractCostEstimates" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@Original_ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_CostEstimateNumber" ColumnName="CostEstimateNumber" DataSourceName="NovaDB.Sales.ContractCostEstimates" DataTypeServer="varchar(255)" DbType="AnsiString" Direction="Input" ParameterName="@Original_CostEstimateNumber" Precision="0" ProviderType="VarChar" Scale="0" Size="255" SourceColumn="CostEstimateNumber" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_CostEstimateAmount" ColumnName="CostEstimateAmount" DataSourceName="NovaDB.Sales.ContractCostEstimates" DataTypeServer="decimal(18, 2)" DbType="Decimal" Direction="Input" ParameterName="@Original_CostEstimateAmount" Precision="18" ProviderType="Decimal" Scale="2" Size="9" SourceColumn="CostEstimateAmount" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="CostEstimateID" ColumnName="CostEstimateID" DataSourceName="NovaDB.Sales.ContractCostEstimates" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@CostEstimateID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="CostEstimateID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="CostEstimateID" DataSetColumn="CostEstimateID" />
              <Mapping SourceColumn="ContractID" DataSetColumn="ContractID" />
              <Mapping SourceColumn="CostEstimateNumber" DataSetColumn="CostEstimateNumber" />
              <Mapping SourceColumn="CostEstimateAmount" DataSetColumn="CostEstimateAmount" />
              <Mapping SourceColumn="MediaName" DataSetColumn="MediaName" />
              <Mapping SourceColumn="MediaId" DataSetColumn="MediaId" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ContractBrandAgencyTableAdapter" GeneratorDataComponentClassName="ContractBrandAgencyTableAdapter" Name="ContractBrandAgency" UserDataComponentName="ContractBrandAgencyTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>select distinct cl.ClientID, cl.Agency, br.BrandID, br.BrandName,c.ContractID from sales.Contract c
inner join sales.Burst b on b.ContractID = c.ContractID
inner join client.Brand br on br.BrandID = b.BrandID
inner join client.ClientBrand cb on cb.BrandID = br.BrandID
inner join client.Client cl on cl.ClientID = c.ClientID
where c.contractid = @ContractId</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ContractId" ColumnName="ContractID" DataSourceName="NovaDB.Sales.Contract" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractId" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ClientID" DataSetColumn="ClientID" />
              <Mapping SourceColumn="Agency" DataSetColumn="Agency" />
              <Mapping SourceColumn="BrandID" DataSetColumn="BrandID" />
              <Mapping SourceColumn="BrandName" DataSetColumn="BrandName" />
              <Mapping SourceColumn="ContractID" DataSetColumn="ContractID" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DataSetContract" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_DataSetName="DataSetContract" msprop:Generator_UserDSName="DataSetContract">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="Contract" msprop:Generator_UserTableName="Contract" msprop:Generator_RowEvArgName="ContractRowChangeEvent" msprop:Generator_TableVarName="tableContract" msprop:Generator_TablePropName="Contract" msprop:Generator_RowDeletingName="ContractRowDeleting" msprop:Generator_RowChangingName="ContractRowChanging" msprop:Generator_RowDeletedName="ContractRowDeleted" msprop:Generator_RowEvHandlerName="ContractRowChangeEventHandler" msprop:Generator_TableClassName="ContractDataTable" msprop:Generator_RowChangedName="ContractRowChanged" msprop:Generator_RowClassName="ContractRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ContractID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msdata:Caption="DontAudit" msprop:Generator_ColumnVarNameInTable="columnContractID" msprop:Generator_ColumnPropNameInRow="ContractID" msprop:Generator_ColumnPropNameInTable="ContractIDColumn" msprop:Generator_UserColumnName="ContractID" type="xs:string" />
              <xs:element name="AccountManagerID" msdata:Caption="DontAudit" msprop:Generator_ColumnVarNameInTable="columnAccountManagerID" msprop:Generator_ColumnPropNameInRow="AccountManagerID" msprop:Generator_ColumnPropNameInTable="AccountManagerIDColumn" msprop:Generator_UserColumnName="AccountManagerID" type="xs:int" />
              <xs:element name="ClientID" msdata:Caption="DontAudit" msprop:Generator_ColumnVarNameInTable="columnClientID" msprop:Generator_ColumnPropNameInRow="ClientID" msprop:Generator_ColumnPropNameInTable="ClientIDColumn" msprop:Generator_UserColumnName="ClientID" type="xs:int" />
              <xs:element name="ClientName" msdata:Caption="Client Name" msprop:Generator_ColumnVarNameInTable="columnClientName" msprop:Generator_ColumnPropNameInRow="ClientName" msprop:Generator_ColumnPropNameInTable="ClientNameColumn" msprop:Generator_UserColumnName="ClientName" default="Select...">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ClientBillingAddress" msdata:Caption="Client Billing Address" msprop:Generator_ColumnVarNameInTable="columnClientBillingAddress" msprop:Generator_ColumnPropNameInRow="ClientBillingAddress" msprop:Generator_ColumnPropNameInTable="ClientBillingAddressColumn" msprop:Generator_UserColumnName="ClientBillingAddress" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="1000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ContractNumber" msdata:Caption="DontAudit" msprop:Generator_ColumnVarNameInTable="columnContractNumber" msprop:Generator_ColumnPropNameInRow="ContractNumber" msprop:Generator_ColumnPropNameInTable="ContractNumberColumn" msprop:Generator_UserColumnName="ContractNumber" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Signed" msprop:Generator_ColumnVarNameInTable="columnSigned" msprop:Generator_ColumnPropNameInRow="Signed" msprop:Generator_ColumnPropNameInTable="SignedColumn" msprop:Generator_UserColumnName="Signed" type="xs:boolean" default="false" />
              <xs:element name="SignDate" msdata:Caption="DontAudit" msprop:Generator_ColumnVarNameInTable="columnSignDate" msprop:Generator_ColumnPropNameInRow="SignDate" msprop:Generator_ColumnPropNameInTable="SignDateColumn" msprop:Generator_UserColumnName="SignDate" type="xs:dateTime" minOccurs="0" />
              <xs:element name="SignedBy" msdata:Caption="DontAudit" msprop:Generator_ColumnVarNameInTable="columnSignedBy" msprop:Generator_ColumnPropNameInRow="SignedBy" msprop:Generator_ColumnPropNameInTable="SignedByColumn" msprop:Generator_UserColumnName="SignedBy" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="SpecialConditions" msdata:Caption="Special Conditions" msprop:Generator_ColumnVarNameInTable="columnSpecialConditions" msprop:Generator_ColumnPropNameInRow="SpecialConditions" msprop:Generator_ColumnPropNameInTable="SpecialConditionsColumn" msprop:Generator_UserColumnName="SpecialConditions" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ProjectName" msdata:Caption="Project Name" msprop:Generator_ColumnVarNameInTable="columnProjectName" msprop:Generator_ColumnPropNameInRow="ProjectName" msprop:Generator_ColumnPropNameInTable="ProjectNameColumn" msprop:Generator_UserColumnName="ProjectName" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Cancelled" msprop:Generator_ColumnVarNameInTable="columnCancelled" msprop:Generator_ColumnPropNameInRow="Cancelled" msprop:Generator_ColumnPropNameInTable="CancelledColumn" msprop:Generator_UserColumnName="Cancelled" type="xs:boolean" default="false" />
              <xs:element name="CancelDate" msdata:Caption="DontAudit" msprop:Generator_ColumnVarNameInTable="columnCancelDate" msprop:Generator_ColumnPropNameInRow="CancelDate" msprop:Generator_ColumnPropNameInTable="CancelDateColumn" msprop:Generator_UserColumnName="CancelDate" type="xs:dateTime" minOccurs="0" />
              <xs:element name="CancelledBy" msdata:Caption="DontAudit" msprop:Generator_ColumnVarNameInTable="columnCancelledBy" msprop:Generator_ColumnPropNameInRow="CancelledBy" msprop:Generator_ColumnPropNameInTable="CancelledByColumn" msprop:Generator_UserColumnName="CancelledBy" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CreatedBy" msdata:Caption="DontAudit" msprop:Generator_ColumnVarNameInTable="columnCreatedBy" msprop:Generator_ColumnPropNameInRow="CreatedBy" msprop:Generator_ColumnPropNameInTable="CreatedByColumn" msprop:Generator_UserColumnName="CreatedBy" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CreationDate" msdata:Caption="DontAudit" msprop:Generator_ColumnVarNameInTable="columnCreationDate" msprop:Generator_ColumnPropNameInRow="CreationDate" msprop:Generator_ColumnPropNameInTable="CreationDateColumn" msprop:Generator_UserColumnName="CreationDate" type="xs:dateTime" />
              <xs:element name="Brands" msprop:Generator_ColumnVarNameInTable="columnBrands" msprop:Generator_ColumnPropNameInRow="Brands" msprop:Generator_ColumnPropNameInTable="BrandsColumn" msprop:Generator_UserColumnName="Brands" default="" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="**********" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Chains" msprop:Generator_ColumnVarNameInTable="columnChains" msprop:Generator_ColumnPropNameInRow="Chains" msprop:Generator_ColumnPropNameInTable="ChainsColumn" msprop:Generator_UserColumnName="Chains" default="" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="**********" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="MediaServices" msdata:Caption="Media Services" msprop:Generator_ColumnVarNameInTable="columnMediaServices" msprop:Generator_ColumnPropNameInRow="MediaServices" msprop:Generator_ColumnPropNameInTable="MediaServicesColumn" msprop:Generator_UserColumnName="MediaServices" default="" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="**********" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Categories" msprop:Generator_ColumnVarNameInTable="columnCategories" msprop:Generator_ColumnPropNameInRow="Categories" msprop:Generator_ColumnPropNameInTable="CategoriesColumn" msprop:Generator_UserColumnName="Categories" default="" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="**********" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="FirstWeek" msdata:Caption="First Week" msprop:Generator_ColumnVarNameInTable="columnFirstWeek" msprop:Generator_ColumnPropNameInRow="FirstWeek" msprop:Generator_ColumnPropNameInTable="FirstWeekColumn" msprop:Generator_UserColumnName="FirstWeek" type="xs:dateTime" minOccurs="0" />
              <xs:element name="LastWeek" msdata:Caption="Last Week" msprop:Generator_ColumnVarNameInTable="columnLastWeek" msprop:Generator_ColumnPropNameInRow="LastWeek" msprop:Generator_ColumnPropNameInTable="LastWeekColumn" msprop:Generator_UserColumnName="LastWeek" type="xs:dateTime" minOccurs="0" />
              <xs:element name="AgencyID" msdata:Caption="DontAudit" msprop:Generator_ColumnVarNameInTable="columnAgencyID" msprop:Generator_ColumnPropNameInRow="AgencyID" msprop:Generator_ColumnPropNameInTable="AgencyIDColumn" msprop:Generator_UserColumnName="AgencyID" type="xs:int" minOccurs="0" />
              <xs:element name="AgencyName" msdata:Caption="Agency Name" msprop:Generator_ColumnVarNameInTable="columnAgencyName" msprop:Generator_ColumnPropNameInRow="AgencyName" msprop:Generator_ColumnPropNameInTable="AgencyNameColumn" msprop:Generator_UserColumnName="AgencyName" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="AgencyCommPercentage" msdata:Caption="Agency Comm Percentage" msprop:Generator_ColumnVarNameInTable="columnAgencyCommPercentage" msprop:Generator_ColumnPropNameInRow="AgencyCommPercentage" msprop:Generator_ColumnPropNameInTable="AgencyCommPercentageColumn" msprop:Generator_UserColumnName="AgencyCommPercentage" type="xs:decimal" default="0" />
              <xs:element name="ApplyAgencyComm" msdata:Caption="Apply Agency Comm" msprop:Generator_ColumnVarNameInTable="columnApplyAgencyComm" msprop:Generator_ColumnPropNameInRow="ApplyAgencyComm" msprop:Generator_ColumnPropNameInTable="ApplyAgencyCommColumn" msprop:Generator_UserColumnName="ApplyAgencyComm" type="xs:boolean" default="false" />
              <xs:element name="BillingInstructions" msdata:Caption="Billing Instructions" msprop:Generator_ColumnVarNameInTable="columnBillingInstructions" msprop:Generator_ColumnPropNameInRow="BillingInstructions" msprop:Generator_ColumnPropNameInTable="BillingInstructionsColumn" msprop:Generator_UserColumnName="BillingInstructions" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ContractNotes" msdata:Caption="Notes" msprop:Generator_ColumnVarNameInTable="columnContractNotes" msprop:Generator_ColumnPropNameInRow="ContractNotes" msprop:Generator_ColumnPropNameInTable="ContractNotesColumn" msprop:Generator_UserColumnName="ContractNotes" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="TotalWeeks" msdata:Caption="DontAudit" msprop:Generator_ColumnVarNameInTable="columnTotalWeeks" msprop:Generator_ColumnPropNameInRow="TotalWeeks" msprop:Generator_ColumnPropNameInTable="TotalWeeksColumn" msprop:Generator_UserColumnName="TotalWeeks" type="xs:int" minOccurs="0" />
              <xs:element name="History" msdata:ReadOnly="true" msdata:Caption="DontAudit" msprop:Generator_ColumnVarNameInTable="columnHistory" msprop:Generator_ColumnPropNameInRow="History" msprop:Generator_ColumnPropNameInTable="HistoryColumn" msprop:Generator_UserColumnName="History" type="xs:boolean" minOccurs="0" />
              <xs:element name="ContractDate" msdata:Caption="Contract Date" msprop:Generator_ColumnVarNameInTable="columnContractDate" msprop:Generator_ColumnPropNameInRow="ContractDate" msprop:Generator_ColumnPropNameInTable="ContractDateColumn" msprop:Generator_UserColumnName="ContractDate" type="xs:dateTime" minOccurs="0" />
              <xs:element name="ContractType" msdata:Caption="Contract Type" msprop:Generator_ColumnVarNameInTable="columnContractType" msprop:Generator_ColumnPropNameInRow="ContractType" msprop:Generator_ColumnPropNameInTable="ContractTypeColumn" msprop:Generator_UserColumnName="ContractType">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="AccountManagerName" msdata:Caption="Account Manager Name" msprop:Generator_ColumnVarNameInTable="columnAccountManagerName" msprop:Generator_ColumnPropNameInRow="AccountManagerName" msprop:Generator_ColumnPropNameInTable="AccountManagerNameColumn" msprop:Generator_UserColumnName="AccountManagerName" default="Select..." minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="151" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Code" msprop:Generator_ColumnVarNameInTable="columnCode" msprop:Generator_ColumnPropNameInRow="Code" msprop:Generator_ColumnPropNameInTable="CodeColumn" msprop:Generator_UserColumnName="Code" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ResearchCategories" msdata:Caption="DontAudit" msprop:Generator_ColumnVarNameInTable="columnResearchCategories" msprop:Generator_ColumnPropNameInRow="ResearchCategories" msprop:Generator_ColumnPropNameInTable="ResearchCategoriesColumn" msprop:Generator_UserColumnName="ResearchCategories" default="" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="**********" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="RollForward" msprop:Generator_ColumnVarNameInTable="columnRollForward" msprop:Generator_ColumnPropNameInRow="RollForward" msprop:Generator_ColumnPropNameInTable="RollForwardColumn" msprop:Generator_UserColumnName="RollForward" type="xs:boolean" default="false" />
              <xs:element name="AddedValue" msprop:Generator_ColumnVarNameInTable="columnAddedValue" msprop:Generator_ColumnPropNameInRow="AddedValue" msprop:Generator_ColumnPropNameInTable="AddedValueColumn" msprop:Generator_UserColumnName="AddedValue" type="xs:boolean" default="false" />
              <xs:element name="AgencyCommIsPercentageOfNetRental" msprop:Generator_ColumnVarNameInTable="columnAgencyCommIsPercentageOfNetRental" msprop:Generator_ColumnPropNameInRow="AgencyCommIsPercentageOfNetRental" msprop:Generator_ColumnPropNameInTable="AgencyCommIsPercentageOfNetRentalColumn" msprop:Generator_UserColumnName="AgencyCommIsPercentageOfNetRental" type="xs:boolean" default="false" />
              <xs:element name="PrintAgencyComm" msprop:Generator_ColumnVarNameInTable="columnPrintAgencyComm" msprop:Generator_ColumnPropNameInRow="PrintAgencyComm" msprop:Generator_ColumnPropNameInTable="PrintAgencyCommColumn" msprop:Generator_UserColumnName="PrintAgencyComm" type="xs:boolean" default="false" />
              <xs:element name="Approved" msprop:Generator_ColumnVarNameInTable="columnApproved" msprop:Generator_ColumnPropNameInRow="Approved" msprop:Generator_ColumnPropNameInTable="ApprovedColumn" msprop:Generator_UserColumnName="Approved" type="xs:boolean" default="false" minOccurs="0" />
              <xs:element name="ClientAccountManager" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnClientAccountManager" msprop:Generator_ColumnPropNameInRow="ClientAccountManager" msprop:Generator_ColumnPropNameInTable="ClientAccountManagerColumn" msprop:Generator_UserColumnName="ClientAccountManager" default="" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="**********" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ContractClassificationName" msprop:Generator_ColumnVarNameInTable="columnContractClassificationName" msprop:Generator_ColumnPropNameInRow="ContractClassificationName" msprop:Generator_ColumnPropNameInTable="ContractClassificationNameColumn" msprop:Generator_UserColumnName="ContractClassificationName" default="Choose Classification" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ContractProposalHeatName" msprop:Generator_ColumnVarNameInTable="columnContractProposalHeatName" msprop:Generator_ColumnPropNameInRow="ContractProposalHeatName" msprop:Generator_ColumnPropNameInTable="ContractProposalHeatNameColumn" msprop:Generator_UserColumnName="ContractProposalHeatName" default="Choose Proposal Heat" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ContractProposalHeatId" msprop:Generator_ColumnVarNameInTable="columnContractProposalHeatId" msprop:Generator_ColumnPropNameInRow="ContractProposalHeatId" msprop:Generator_ColumnPropNameInTable="ContractProposalHeatIdColumn" msprop:Generator_UserColumnName="ContractProposalHeatId" type="xs:int" minOccurs="0" />
              <xs:element name="isReplacement" msprop:Generator_ColumnVarNameInTable="columnisReplacement" msprop:Generator_ColumnPropNameInRow="isReplacement" msprop:Generator_ColumnPropNameInTable="isReplacementColumn" msprop:Generator_UserColumnName="isReplacement" type="xs:boolean" default="false" minOccurs="0" />
              <xs:element name="ClonedContractNumber" msprop:nullValue="None" msprop:Generator_ColumnPropNameInRow="ClonedContractNumber" msprop:Generator_ColumnVarNameInTable="columnClonedContractNumber" msprop:Generator_ColumnPropNameInTable="ClonedContractNumberColumn" msprop:Generator_UserColumnName="ClonedContractNumber" type="xs:string" default="None" minOccurs="0" />
              <xs:element name="ContractClassificationId" msprop:nullValue="_null" msprop:Generator_ColumnPropNameInRow="ContractClassificationId" msprop:Generator_ColumnVarNameInTable="columnContractClassificationId" msprop:Generator_ColumnPropNameInTable="ContractClassificationIdColumn" msprop:Generator_UserColumnName="ContractClassificationId" type="xs:string" minOccurs="0" />
              <xs:element name="DemoProvider" msprop:nullValue="_empty" msprop:Generator_ColumnPropNameInRow="DemoProvider" msprop:Generator_ColumnVarNameInTable="columnDemoProvider" msprop:Generator_ColumnPropNameInTable="DemoProviderColumn" msprop:Generator_UserColumnName="DemoProvider" type="xs:string" default="" />
              <xs:element name="DemoOwner" msprop:nullValue="_empty" msprop:Generator_ColumnPropNameInRow="DemoOwner" msprop:Generator_ColumnVarNameInTable="columnDemoOwner" msprop:Generator_ColumnPropNameInTable="DemoOwnerColumn" msprop:Generator_UserColumnName="DemoOwner" type="xs:string" default="" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Burst" msprop:Generator_UserTableName="Burst" msprop:Generator_RowEvArgName="BurstRowChangeEvent" msprop:Generator_TableVarName="tableBurst" msprop:Generator_TablePropName="Burst" msprop:Generator_RowDeletingName="BurstRowDeleting" msprop:Generator_RowChangingName="BurstRowChanging" msprop:Generator_RowDeletedName="BurstRowDeleted" msprop:Generator_RowEvHandlerName="BurstRowChangeEventHandler" msprop:Generator_TableClassName="BurstDataTable" msprop:Generator_RowChangedName="BurstRowChanged" msprop:Generator_RowClassName="BurstRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="BurstID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnBurstID" msprop:Generator_ColumnPropNameInRow="BurstID" msprop:Generator_ColumnPropNameInTable="BurstIDColumn" msprop:Generator_UserColumnName="BurstID" type="xs:string" />
              <xs:element name="ContractID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnContractID" msprop:Generator_ColumnPropNameInRow="ContractID" msprop:Generator_ColumnPropNameInTable="ContractIDColumn" msprop:Generator_UserColumnName="ContractID" type="xs:string" />
              <xs:element name="ChainID" msprop:Generator_ColumnVarNameInTable="columnChainID" msprop:Generator_ColumnPropNameInRow="ChainID" msprop:Generator_ColumnPropNameInTable="ChainIDColumn" msprop:Generator_UserColumnName="ChainID" type="xs:int" />
              <xs:element name="ChainName" msprop:Generator_ColumnVarNameInTable="columnChainName" msprop:Generator_ColumnPropNameInRow="ChainName" msprop:Generator_ColumnPropNameInTable="ChainNameColumn" msprop:Generator_UserColumnName="ChainName" default="Select...">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="StorePoolID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnStorePoolID" msprop:Generator_ColumnPropNameInRow="StorePoolID" msprop:Generator_ColumnPropNameInTable="StorePoolIDColumn" msprop:Generator_UserColumnName="StorePoolID" type="xs:string" />
              <xs:element name="MediaID" msprop:Generator_ColumnVarNameInTable="columnMediaID" msprop:Generator_ColumnPropNameInRow="MediaID" msprop:Generator_ColumnPropNameInTable="MediaIDColumn" msprop:Generator_UserColumnName="MediaID" type="xs:int" />
              <xs:element name="MediaName" msprop:Generator_ColumnVarNameInTable="columnMediaName" msprop:Generator_ColumnPropNameInRow="MediaName" msprop:Generator_ColumnPropNameInTable="MediaNameColumn" msprop:Generator_UserColumnName="MediaName" default="Select...">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="BrandName" msprop:Generator_ColumnVarNameInTable="columnBrandName" msprop:Generator_ColumnPropNameInRow="BrandName" msprop:Generator_ColumnPropNameInTable="BrandNameColumn" msprop:Generator_UserColumnName="BrandName" default="Select...">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ProductName" msprop:Generator_ColumnVarNameInTable="columnProductName" msprop:Generator_ColumnPropNameInRow="ProductName" msprop:Generator_ColumnPropNameInTable="ProductNameColumn" msprop:Generator_UserColumnName="ProductName" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="FirstWeek" msprop:Generator_ColumnVarNameInTable="columnFirstWeek" msprop:Generator_ColumnPropNameInRow="FirstWeek" msprop:Generator_ColumnPropNameInTable="FirstWeekColumn" msprop:Generator_UserColumnName="FirstWeek" type="xs:dateTime" />
              <xs:element name="InstallWeeks" msprop:Generator_ColumnVarNameInTable="columnInstallWeeks" msprop:Generator_ColumnPropNameInRow="InstallWeeks" msprop:Generator_ColumnPropNameInTable="InstallWeeksColumn" msprop:Generator_UserColumnName="InstallWeeks" type="xs:int" default="0" />
              <xs:element name="InstallStoreQty" msprop:Generator_ColumnVarNameInTable="columnInstallStoreQty" msprop:Generator_ColumnPropNameInRow="InstallStoreQty" msprop:Generator_ColumnPropNameInTable="InstallStoreQtyColumn" msprop:Generator_UserColumnName="InstallStoreQty" type="xs:int" default="0" />
              <xs:element name="BillableStoreQty" msprop:Generator_ColumnVarNameInTable="columnBillableStoreQty" msprop:Generator_ColumnPropNameInRow="BillableStoreQty" msprop:Generator_ColumnPropNameInTable="BillableStoreQtyColumn" msprop:Generator_UserColumnName="BillableStoreQty" type="xs:int" default="0" />
              <xs:element name="RentalRate" msprop:Generator_ColumnVarNameInTable="columnRentalRate" msprop:Generator_ColumnPropNameInRow="RentalRate" msprop:Generator_ColumnPropNameInTable="RentalRateColumn" msprop:Generator_UserColumnName="RentalRate" type="xs:decimal" default="0" />
              <xs:element name="BillableWeeks" msprop:Generator_ColumnVarNameInTable="columnBillableWeeks" msprop:Generator_ColumnPropNameInRow="BillableWeeks" msprop:Generator_ColumnPropNameInTable="BillableWeeksColumn" msprop:Generator_UserColumnName="BillableWeeks" type="xs:int" default="0" />
              <xs:element name="Discount" msprop:Generator_ColumnVarNameInTable="columnDiscount" msprop:Generator_ColumnPropNameInRow="Discount" msprop:Generator_ColumnPropNameInTable="DiscountColumn" msprop:Generator_UserColumnName="Discount" type="xs:decimal" default="0" />
              <xs:element name="InstallationInstructions" msprop:Generator_ColumnVarNameInTable="columnInstallationInstructions" msprop:Generator_ColumnPropNameInRow="InstallationInstructions" msprop:Generator_ColumnPropNameInTable="InstallationInstructionsColumn" msprop:Generator_UserColumnName="InstallationInstructions" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CrossoverQty" msprop:Generator_ColumnVarNameInTable="columnCrossoverQty" msprop:Generator_ColumnPropNameInRow="CrossoverQty" msprop:Generator_ColumnPropNameInTable="CrossoverQtyColumn" msprop:Generator_UserColumnName="CrossoverQty" type="xs:int" default="0" />
              <xs:element name="LastWeek" msprop:Generator_ColumnVarNameInTable="columnLastWeek" msprop:Generator_ColumnPropNameInRow="LastWeek" msprop:Generator_ColumnPropNameInTable="LastWeekColumn" msprop:Generator_UserColumnName="LastWeek" type="xs:dateTime" minOccurs="0" />
              <xs:element name="Categories" msprop:Generator_ColumnVarNameInTable="columnCategories" msprop:Generator_ColumnPropNameInRow="Categories" msprop:Generator_ColumnPropNameInTable="CategoriesColumn" msprop:Generator_UserColumnName="Categories" default="" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="**********" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="MediaAllowsHomesite" msprop:Generator_ColumnVarNameInTable="columnMediaAllowsHomesite" msprop:Generator_ColumnPropNameInRow="MediaAllowsHomesite" msprop:Generator_ColumnPropNameInTable="MediaAllowsHomesiteColumn" msprop:Generator_UserColumnName="MediaAllowsHomesite" type="xs:boolean" default="false" />
              <xs:element name="MediaAllowsCrossover" msprop:Generator_ColumnVarNameInTable="columnMediaAllowsCrossover" msprop:Generator_ColumnPropNameInRow="MediaAllowsCrossover" msprop:Generator_ColumnPropNameInTable="MediaAllowsCrossoverColumn" msprop:Generator_UserColumnName="MediaAllowsCrossover" type="xs:boolean" default="false" />
              <xs:element name="BrandID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnBrandID" msprop:Generator_ColumnPropNameInRow="BrandID" msprop:Generator_ColumnPropNameInTable="BrandIDColumn" msprop:Generator_UserColumnName="BrandID" type="xs:string" />
              <xs:element name="MediaFamilyIDList" msprop:Generator_ColumnVarNameInTable="columnMediaFamilyIDList" msprop:Generator_ColumnPropNameInRow="MediaFamilyIDList" msprop:Generator_ColumnPropNameInTable="MediaFamilyIDListColumn" msprop:Generator_UserColumnName="MediaFamilyIDList" default="" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="**********" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="StorePoolQty" msprop:Generator_ColumnVarNameInTable="columnStorePoolQty" msprop:Generator_ColumnPropNameInRow="StorePoolQty" msprop:Generator_ColumnPropNameInTable="StorePoolQtyColumn" msprop:Generator_UserColumnName="StorePoolQty" type="xs:int" default="0" />
              <xs:element name="StoreListConfirmed" msprop:Generator_ColumnVarNameInTable="columnStoreListConfirmed" msprop:Generator_ColumnPropNameInRow="StoreListConfirmed" msprop:Generator_ColumnPropNameInTable="StoreListConfirmedColumn" msprop:Generator_UserColumnName="StoreListConfirmed" type="xs:boolean" default="false" />
              <xs:element name="CreationDate" msprop:Generator_ColumnVarNameInTable="columnCreationDate" msprop:Generator_ColumnPropNameInRow="CreationDate" msprop:Generator_ColumnPropNameInTable="CreationDateColumn" msprop:Generator_UserColumnName="CreationDate" type="xs:dateTime" />
              <xs:element name="InstallAtHomesite" msprop:Generator_ColumnVarNameInTable="columnInstallAtHomesite" msprop:Generator_ColumnPropNameInRow="InstallAtHomesite" msprop:Generator_ColumnPropNameInTable="InstallAtHomesiteColumn" msprop:Generator_UserColumnName="InstallAtHomesite" type="xs:boolean" default="true" />
              <xs:element name="AdsPerInstallation" msprop:Generator_ColumnVarNameInTable="columnAdsPerInstallation" msprop:Generator_ColumnPropNameInRow="AdsPerInstallation" msprop:Generator_ColumnPropNameInTable="AdsPerInstallationColumn" msprop:Generator_UserColumnName="AdsPerInstallation" type="xs:int" default="1" />
              <xs:element name="MediaLifeCycleValid" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnMediaLifeCycleValid" msprop:Generator_ColumnPropNameInRow="MediaLifeCycleValid" msprop:Generator_ColumnPropNameInTable="MediaLifeCycleValidColumn" msprop:Generator_UserColumnName="MediaLifeCycleValid" type="xs:boolean" default="true" minOccurs="0" />
              <xs:element name="ChainTypeID" msprop:Generator_ColumnVarNameInTable="columnChainTypeID" msprop:Generator_ColumnPropNameInRow="ChainTypeID" msprop:Generator_ColumnPropNameInTable="ChainTypeIDColumn" msprop:Generator_UserColumnName="ChainTypeID" type="xs:int" default="0" />
              <xs:element name="AdsPerCrossover" msprop:Generator_ColumnVarNameInTable="columnAdsPerCrossover" msprop:Generator_ColumnPropNameInRow="AdsPerCrossover" msprop:Generator_ColumnPropNameInTable="AdsPerCrossoverColumn" msprop:Generator_UserColumnName="AdsPerCrossover" type="xs:int" default="0" />
              <xs:element name="AdsPerShelfTalk" msprop:Generator_ColumnVarNameInTable="columnAdsPerShelfTalk" msprop:Generator_ColumnPropNameInRow="AdsPerShelfTalk" msprop:Generator_ColumnPropNameInTable="AdsPerShelfTalkColumn" msprop:Generator_UserColumnName="AdsPerShelfTalk" type="xs:int" default="0" minOccurs="0" />
              <xs:element name="InstallRegardlessOfStock" msprop:Generator_ColumnVarNameInTable="columnInstallRegardlessOfStock" msprop:Generator_ColumnPropNameInRow="InstallRegardlessOfStock" msprop:Generator_ColumnPropNameInTable="InstallRegardlessOfStockColumn" msprop:Generator_UserColumnName="InstallRegardlessOfStock" type="xs:boolean" default="false" minOccurs="0" />
              <xs:element name="ApplyInstructionsAcrossAllBursts" msprop:Generator_ColumnVarNameInTable="columnApplyInstructionsAcrossAllBursts" msprop:Generator_ColumnPropNameInRow="ApplyInstructionsAcrossAllBursts" msprop:Generator_ColumnPropNameInTable="ApplyInstructionsAcrossAllBurstsColumn" msprop:Generator_UserColumnName="ApplyInstructionsAcrossAllBursts" type="xs:boolean" default="false" minOccurs="0" />
              <xs:element name="isPNPPcaStatus" msprop:Generator_ColumnVarNameInTable="columnisPNPPcaStatus" msprop:Generator_ColumnPropNameInRow="isPNPPcaStatus" msprop:Generator_ColumnPropNameInTable="isPNPPcaStatusColumn" msprop:Generator_UserColumnName="isPNPPcaStatus" type="xs:boolean" default="false" />
              <xs:element name="InstallCategories" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnInstallCategories" msprop:Generator_ColumnPropNameInRow="InstallCategories" msprop:Generator_ColumnPropNameInTable="InstallCategoriesColumn" msprop:Generator_UserColumnName="InstallCategories" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="**********" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="AccountManager" msprop:Generator_UserTableName="AccountManager" msprop:Generator_RowEvArgName="AccountManagerRowChangeEvent" msprop:Generator_TableVarName="tableAccountManager" msprop:Generator_TablePropName="AccountManager" msprop:Generator_RowDeletingName="AccountManagerRowDeleting" msprop:Generator_RowChangingName="AccountManagerRowChanging" msprop:Generator_RowDeletedName="AccountManagerRowDeleted" msprop:Generator_RowEvHandlerName="AccountManagerRowChangeEventHandler" msprop:Generator_TableClassName="AccountManagerDataTable" msprop:Generator_RowChangedName="AccountManagerRowChanged" msprop:Generator_RowClassName="AccountManagerRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="AccountManagerID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msprop:Generator_ColumnVarNameInTable="columnAccountManagerID" msprop:Generator_ColumnPropNameInRow="AccountManagerID" msprop:Generator_ColumnPropNameInTable="AccountManagerIDColumn" msprop:Generator_UserColumnName="AccountManagerID" type="xs:int" />
              <xs:element name="Code" msprop:Generator_ColumnVarNameInTable="columnCode" msprop:Generator_ColumnPropNameInRow="Code" msprop:Generator_ColumnPropNameInTable="CodeColumn" msprop:Generator_UserColumnName="Code">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="AccountManagerName" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnAccountManagerName" msprop:Generator_ColumnPropNameInRow="AccountManagerName" msprop:Generator_ColumnPropNameInTable="AccountManagerNameColumn" msprop:Generator_UserColumnName="AccountManagerName" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="151" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="BurstLoadingFee" msprop:Generator_UserTableName="BurstLoadingFee" msprop:Generator_RowEvArgName="BurstLoadingFeeRowChangeEvent" msprop:Generator_TableVarName="tableBurstLoadingFee" msprop:Generator_TablePropName="BurstLoadingFee" msprop:Generator_RowDeletingName="BurstLoadingFeeRowDeleting" msprop:Generator_RowChangingName="BurstLoadingFeeRowChanging" msprop:Generator_RowDeletedName="BurstLoadingFeeRowDeleted" msprop:Generator_RowEvHandlerName="BurstLoadingFeeRowChangeEventHandler" msprop:Generator_TableClassName="BurstLoadingFeeDataTable" msprop:Generator_RowChangedName="BurstLoadingFeeRowChanged" msprop:Generator_RowClassName="BurstLoadingFeeRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="BurstID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnBurstID" msprop:Generator_ColumnPropNameInRow="BurstID" msprop:Generator_ColumnPropNameInTable="BurstIDColumn" msprop:Generator_UserColumnName="BurstID" type="xs:string" />
              <xs:element name="LoadingFeeID" msprop:Generator_ColumnVarNameInTable="columnLoadingFeeID" msprop:Generator_ColumnPropNameInRow="LoadingFeeID" msprop:Generator_ColumnPropNameInTable="LoadingFeeIDColumn" msprop:Generator_UserColumnName="LoadingFeeID" type="xs:int" />
              <xs:element name="Percentage" msprop:Generator_ColumnVarNameInTable="columnPercentage" msprop:Generator_ColumnPropNameInRow="Percentage" msprop:Generator_ColumnPropNameInTable="PercentageColumn" msprop:Generator_UserColumnName="Percentage" type="xs:decimal" />
              <xs:element name="LoadingFeeName" msprop:Generator_ColumnVarNameInTable="columnLoadingFeeName" msprop:Generator_ColumnPropNameInRow="LoadingFeeName" msprop:Generator_ColumnPropNameInTable="LoadingFeeNameColumn" msprop:Generator_UserColumnName="LoadingFeeName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="LoadingFeeAmount" msprop:Generator_ColumnVarNameInTable="columnLoadingFeeAmount" msprop:Generator_ColumnPropNameInRow="LoadingFeeAmount" msprop:Generator_ColumnPropNameInTable="LoadingFeeAmountColumn" msprop:Generator_UserColumnName="LoadingFeeAmount" type="xs:decimal" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="BurstCategory" msprop:Generator_UserTableName="BurstCategory" msprop:Generator_RowEvArgName="BurstCategoryRowChangeEvent" msprop:Generator_TableVarName="tableBurstCategory" msprop:Generator_TablePropName="BurstCategory" msprop:Generator_RowDeletingName="BurstCategoryRowDeleting" msprop:Generator_RowChangingName="BurstCategoryRowChanging" msprop:Generator_RowDeletedName="BurstCategoryRowDeleted" msprop:Generator_RowEvHandlerName="BurstCategoryRowChangeEventHandler" msprop:Generator_TableClassName="BurstCategoryDataTable" msprop:Generator_RowChangedName="BurstCategoryRowChanged" msprop:Generator_RowClassName="BurstCategoryRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="BurstID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnBurstID" msprop:Generator_ColumnPropNameInRow="BurstID" msprop:Generator_ColumnPropNameInTable="BurstIDColumn" msprop:Generator_UserColumnName="BurstID" type="xs:string" />
              <xs:element name="CategoryID" msprop:Generator_ColumnVarNameInTable="columnCategoryID" msprop:Generator_ColumnPropNameInRow="CategoryID" msprop:Generator_ColumnPropNameInTable="CategoryIDColumn" msprop:Generator_UserColumnName="CategoryID" type="xs:int" />
              <xs:element name="Priority" msprop:Generator_ColumnVarNameInTable="columnPriority" msprop:Generator_ColumnPropNameInRow="Priority" msprop:Generator_ColumnPropNameInTable="PriorityColumn" msprop:Generator_UserColumnName="Priority" type="xs:short" />
              <xs:element name="CategoryName" msprop:Generator_ColumnVarNameInTable="columnCategoryName" msprop:Generator_ColumnPropNameInRow="CategoryName" msprop:Generator_ColumnPropNameInTable="CategoryNameColumn" msprop:Generator_UserColumnName="CategoryName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="LoadingFee" msprop:Generator_UserTableName="LoadingFee" msprop:Generator_RowEvArgName="LoadingFeeRowChangeEvent" msprop:Generator_TableVarName="tableLoadingFee" msprop:Generator_TablePropName="LoadingFee" msprop:Generator_RowDeletingName="LoadingFeeRowDeleting" msprop:Generator_RowChangingName="LoadingFeeRowChanging" msprop:Generator_RowDeletedName="LoadingFeeRowDeleted" msprop:Generator_RowEvHandlerName="LoadingFeeRowChangeEventHandler" msprop:Generator_TableClassName="LoadingFeeDataTable" msprop:Generator_RowChangedName="LoadingFeeRowChanged" msprop:Generator_RowClassName="LoadingFeeRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="LoadingFeeID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msprop:Generator_ColumnVarNameInTable="columnLoadingFeeID" msprop:Generator_ColumnPropNameInRow="LoadingFeeID" msprop:Generator_ColumnPropNameInTable="LoadingFeeIDColumn" msprop:Generator_UserColumnName="LoadingFeeID" type="xs:int" />
              <xs:element name="LoadingFeeName" msprop:Generator_ColumnVarNameInTable="columnLoadingFeeName" msprop:Generator_ColumnPropNameInRow="LoadingFeeName" msprop:Generator_ColumnPropNameInTable="LoadingFeeNameColumn" msprop:Generator_UserColumnName="LoadingFeeName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="DefaultPercentage" msprop:Generator_ColumnVarNameInTable="columnDefaultPercentage" msprop:Generator_ColumnPropNameInRow="DefaultPercentage" msprop:Generator_ColumnPropNameInTable="DefaultPercentageColumn" msprop:Generator_UserColumnName="DefaultPercentage" type="xs:decimal" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Category" msprop:Generator_UserTableName="Category" msprop:Generator_RowEvArgName="CategoryRowChangeEvent" msprop:Generator_TableVarName="tableCategory" msprop:Generator_TablePropName="Category" msprop:Generator_RowDeletingName="CategoryRowDeleting" msprop:Generator_RowChangingName="CategoryRowChanging" msprop:Generator_RowDeletedName="CategoryRowDeleted" msprop:Generator_RowEvHandlerName="CategoryRowChangeEventHandler" msprop:Generator_TableClassName="CategoryDataTable" msprop:Generator_RowChangedName="CategoryRowChanged" msprop:Generator_RowClassName="CategoryRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="CategoryID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msprop:Generator_ColumnVarNameInTable="columnCategoryID" msprop:Generator_ColumnPropNameInRow="CategoryID" msprop:Generator_ColumnPropNameInTable="CategoryIDColumn" msprop:Generator_UserColumnName="CategoryID" type="xs:int" />
              <xs:element name="CategoryName" msprop:Generator_ColumnVarNameInTable="columnCategoryName" msprop:Generator_ColumnPropNameInRow="CategoryName" msprop:Generator_ColumnPropNameInTable="CategoryNameColumn" msprop:Generator_UserColumnName="CategoryName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Dormant" msprop:Generator_ColumnVarNameInTable="columnDormant" msprop:Generator_ColumnPropNameInRow="Dormant" msprop:Generator_ColumnPropNameInTable="DormantColumn" msprop:Generator_UserColumnName="Dormant" type="xs:boolean" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="PurchaseOrderNumber" msprop:Generator_UserTableName="PurchaseOrderNumber" msprop:Generator_RowEvArgName="PurchaseOrderNumberRowChangeEvent" msprop:Generator_TableVarName="tablePurchaseOrderNumber" msprop:Generator_TablePropName="PurchaseOrderNumber" msprop:Generator_RowDeletingName="PurchaseOrderNumberRowDeleting" msprop:Generator_RowChangingName="PurchaseOrderNumberRowChanging" msprop:Generator_RowDeletedName="PurchaseOrderNumberRowDeleted" msprop:Generator_RowEvHandlerName="PurchaseOrderNumberRowChangeEventHandler" msprop:Generator_TableClassName="PurchaseOrderNumberDataTable" msprop:Generator_RowChangedName="PurchaseOrderNumberRowChanged" msprop:Generator_RowClassName="PurchaseOrderNumberRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ContractID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnContractID" msprop:Generator_ColumnPropNameInRow="ContractID" msprop:Generator_ColumnPropNameInTable="ContractIDColumn" msprop:Generator_UserColumnName="ContractID" type="xs:string" />
              <xs:element name="PurchaseOrderNumber" msprop:Generator_ColumnVarNameInTable="columnPurchaseOrderNumber" msprop:Generator_ColumnPropNameInRow="PurchaseOrderNumber" msprop:Generator_ColumnPropNameInTable="PurchaseOrderNumberColumn" msprop:Generator_UserColumnName="PurchaseOrderNumber" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="PurchaseOrderNumberDescription" msprop:Generator_ColumnVarNameInTable="columnPurchaseOrderNumberDescription" msprop:Generator_ColumnPropNameInRow="PurchaseOrderNumberDescription" msprop:Generator_ColumnPropNameInTable="PurchaseOrderNumberDescriptionColumn" msprop:Generator_UserColumnName="PurchaseOrderNumberDescription" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ContractInventoryQty" msprop:Generator_UserTableName="ContractInventoryQty" msprop:Generator_RowEvArgName="ContractInventoryQtyRowChangeEvent" msprop:Generator_TableVarName="tableContractInventoryQty" msprop:Generator_TablePropName="ContractInventoryQty" msprop:Generator_RowDeletingName="ContractInventoryQtyRowDeleting" msprop:Generator_RowChangingName="ContractInventoryQtyRowChanging" msprop:Generator_RowDeletedName="ContractInventoryQtyRowDeleted" msprop:Generator_RowEvHandlerName="ContractInventoryQtyRowChangeEventHandler" msprop:Generator_TableClassName="ContractInventoryQtyDataTable" msprop:Generator_RowChangedName="ContractInventoryQtyRowChanged" msprop:Generator_RowClassName="ContractInventoryQtyRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ContractItemQtyID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnContractItemQtyID" msprop:Generator_ColumnPropNameInRow="ContractItemQtyID" msprop:Generator_ColumnPropNameInTable="ContractItemQtyIDColumn" msprop:Generator_UserColumnName="ContractItemQtyID" type="xs:string" />
              <xs:element name="ContractID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnContractID" msprop:Generator_ColumnPropNameInRow="ContractID" msprop:Generator_ColumnPropNameInTable="ContractIDColumn" msprop:Generator_UserColumnName="ContractID" type="xs:string" />
              <xs:element name="ItemQtyID" msprop:Generator_ColumnVarNameInTable="columnItemQtyID" msprop:Generator_ColumnPropNameInRow="ItemQtyID" msprop:Generator_ColumnPropNameInTable="ItemQtyIDColumn" msprop:Generator_UserColumnName="ItemQtyID" type="xs:int" />
              <xs:element name="SellPrice" msprop:Generator_ColumnVarNameInTable="columnSellPrice" msprop:Generator_ColumnPropNameInRow="SellPrice" msprop:Generator_ColumnPropNameInTable="SellPriceColumn" msprop:Generator_UserColumnName="SellPrice" type="xs:decimal" default="0" />
              <xs:element name="Notes" msprop:Generator_ColumnVarNameInTable="columnNotes" msprop:Generator_ColumnPropNameInRow="Notes" msprop:Generator_ColumnPropNameInTable="NotesColumn" msprop:Generator_UserColumnName="Notes">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ItemQty" msprop:Generator_ColumnVarNameInTable="columnItemQty" msprop:Generator_ColumnPropNameInRow="ItemQty" msprop:Generator_ColumnPropNameInTable="ItemQtyColumn" msprop:Generator_UserColumnName="ItemQty" type="xs:int" />
              <xs:element name="ItemName" msprop:Generator_ColumnVarNameInTable="columnItemName" msprop:Generator_ColumnPropNameInRow="ItemName" msprop:Generator_ColumnPropNameInTable="ItemNameColumn" msprop:Generator_UserColumnName="ItemName" type="xs:string" />
              <xs:element name="ItemID" msprop:Generator_ColumnVarNameInTable="columnItemID" msprop:Generator_ColumnPropNameInRow="ItemID" msprop:Generator_ColumnPropNameInTable="ItemIDColumn" msprop:Generator_UserColumnName="ItemID" type="xs:int" />
              <xs:element name="BrandID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnBrandID" msprop:Generator_ColumnPropNameInRow="BrandID" msprop:Generator_ColumnPropNameInTable="BrandIDColumn" msprop:Generator_UserColumnName="BrandID" type="xs:string" minOccurs="0" />
              <xs:element name="BrandName" msprop:Generator_ColumnVarNameInTable="columnBrandName" msprop:Generator_ColumnPropNameInRow="BrandName" msprop:Generator_ColumnPropNameInTable="BrandNameColumn" msprop:Generator_UserColumnName="BrandName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ContractMiscellaneousCharge" msprop:Generator_UserTableName="ContractMiscellaneousCharge" msprop:Generator_RowEvArgName="ContractMiscellaneousChargeRowChangeEvent" msprop:Generator_TableVarName="tableContractMiscellaneousCharge" msprop:Generator_TablePropName="ContractMiscellaneousCharge" msprop:Generator_RowDeletingName="ContractMiscellaneousChargeRowDeleting" msprop:Generator_RowChangingName="ContractMiscellaneousChargeRowChanging" msprop:Generator_RowDeletedName="ContractMiscellaneousChargeRowDeleted" msprop:Generator_RowEvHandlerName="ContractMiscellaneousChargeRowChangeEventHandler" msprop:Generator_TableClassName="ContractMiscellaneousChargeDataTable" msprop:Generator_RowChangedName="ContractMiscellaneousChargeRowChanged" msprop:Generator_RowClassName="ContractMiscellaneousChargeRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ContractMiscellaneousChargeID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnContractMiscellaneousChargeID" msprop:Generator_ColumnPropNameInRow="ContractMiscellaneousChargeID" msprop:Generator_ColumnPropNameInTable="ContractMiscellaneousChargeIDColumn" msprop:Generator_UserColumnName="ContractMiscellaneousChargeID" type="xs:string" />
              <xs:element name="ContractID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnContractID" msprop:Generator_ColumnPropNameInRow="ContractID" msprop:Generator_ColumnPropNameInTable="ContractIDColumn" msprop:Generator_UserColumnName="ContractID" type="xs:string" />
              <xs:element name="MiscellaneousChargeID" msprop:Generator_ColumnVarNameInTable="columnMiscellaneousChargeID" msprop:Generator_ColumnPropNameInRow="MiscellaneousChargeID" msprop:Generator_ColumnPropNameInTable="MiscellaneousChargeIDColumn" msprop:Generator_UserColumnName="MiscellaneousChargeID" type="xs:int" />
              <xs:element name="MiscellaneousChargeAmount" msprop:Generator_ColumnVarNameInTable="columnMiscellaneousChargeAmount" msprop:Generator_ColumnPropNameInRow="MiscellaneousChargeAmount" msprop:Generator_ColumnPropNameInTable="MiscellaneousChargeAmountColumn" msprop:Generator_UserColumnName="MiscellaneousChargeAmount" type="xs:decimal" default="0" />
              <xs:element name="MiscellaneousChargeName" msprop:Generator_ColumnVarNameInTable="columnMiscellaneousChargeName" msprop:Generator_ColumnPropNameInRow="MiscellaneousChargeName" msprop:Generator_ColumnPropNameInTable="MiscellaneousChargeNameColumn" msprop:Generator_UserColumnName="MiscellaneousChargeName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="StorePool" msprop:Generator_UserTableName="StorePool" msprop:Generator_RowEvArgName="StorePoolRowChangeEvent" msprop:Generator_TableVarName="tableStorePool" msprop:Generator_TablePropName="StorePool" msprop:Generator_RowDeletingName="StorePoolRowDeleting" msprop:Generator_RowChangingName="StorePoolRowChanging" msprop:Generator_RowDeletedName="StorePoolRowDeleted" msprop:Generator_RowEvHandlerName="StorePoolRowChangeEventHandler" msprop:Generator_TableClassName="StorePoolDataTable" msprop:Generator_RowChangedName="StorePoolRowChanged" msprop:Generator_RowClassName="StorePoolRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="StorePoolID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnStorePoolID" msprop:Generator_ColumnPropNameInRow="StorePoolID" msprop:Generator_ColumnPropNameInTable="StorePoolIDColumn" msprop:Generator_UserColumnName="StorePoolID" type="xs:string" />
              <xs:element name="StorePoolQty" msprop:Generator_ColumnVarNameInTable="columnStorePoolQty" msprop:Generator_ColumnPropNameInRow="StorePoolQty" msprop:Generator_ColumnPropNameInTable="StorePoolQtyColumn" msprop:Generator_UserColumnName="StorePoolQty" type="xs:int" default="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="BrandFamilyMember" msprop:Generator_UserTableName="BrandFamilyMember" msprop:Generator_RowEvArgName="BrandFamilyMemberRowChangeEvent" msprop:Generator_TableVarName="tableBrandFamilyMember" msprop:Generator_TablePropName="BrandFamilyMember" msprop:Generator_RowDeletingName="BrandFamilyMemberRowDeleting" msprop:Generator_RowChangingName="BrandFamilyMemberRowChanging" msprop:Generator_RowDeletedName="BrandFamilyMemberRowDeleted" msprop:Generator_RowEvHandlerName="BrandFamilyMemberRowChangeEventHandler" msprop:Generator_TableClassName="BrandFamilyMemberDataTable" msprop:Generator_RowChangedName="BrandFamilyMemberRowChanged" msprop:Generator_RowClassName="BrandFamilyMemberRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="NonCompetingBrandID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnNonCompetingBrandID" msprop:Generator_ColumnPropNameInRow="NonCompetingBrandID" msprop:Generator_ColumnPropNameInTable="NonCompetingBrandIDColumn" msprop:Generator_UserColumnName="NonCompetingBrandID" type="xs:string" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="PeerBurst" msprop:Generator_UserTableName="PeerBurst" msprop:Generator_RowEvArgName="PeerBurstRowChangeEvent" msprop:Generator_TableVarName="tablePeerBurst" msprop:Generator_TablePropName="PeerBurst" msprop:Generator_RowDeletingName="PeerBurstRowDeleting" msprop:Generator_RowChangingName="PeerBurstRowChanging" msprop:Generator_RowDeletedName="PeerBurstRowDeleted" msprop:Generator_RowEvHandlerName="PeerBurstRowChangeEventHandler" msprop:Generator_TableClassName="PeerBurstDataTable" msprop:Generator_RowChangedName="PeerBurstRowChanged" msprop:Generator_RowClassName="PeerBurstRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="BurstID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnBurstID" msprop:Generator_ColumnPropNameInRow="BurstID" msprop:Generator_ColumnPropNameInTable="BurstIDColumn" msprop:Generator_UserColumnName="BurstID" type="xs:string" />
              <xs:element name="StorePoolID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnStorePoolID" msprop:Generator_ColumnPropNameInRow="StorePoolID" msprop:Generator_ColumnPropNameInTable="StorePoolIDColumn" msprop:Generator_UserColumnName="StorePoolID" type="xs:string" />
              <xs:element name="StorePoolQty" msprop:Generator_ColumnVarNameInTable="columnStorePoolQty" msprop:Generator_ColumnPropNameInRow="StorePoolQty" msprop:Generator_ColumnPropNameInTable="StorePoolQtyColumn" msprop:Generator_UserColumnName="StorePoolQty" type="xs:int" default="0" />
              <xs:element name="ContractNumber" msprop:Generator_ColumnVarNameInTable="columnContractNumber" msprop:Generator_ColumnPropNameInRow="ContractNumber" msprop:Generator_ColumnPropNameInTable="ContractNumberColumn" msprop:Generator_UserColumnName="ContractNumber" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="BrandName" msprop:Generator_ColumnVarNameInTable="columnBrandName" msprop:Generator_ColumnPropNameInRow="BrandName" msprop:Generator_ColumnPropNameInTable="BrandNameColumn" msprop:Generator_UserColumnName="BrandName" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="InstallStoreQty" msprop:Generator_ColumnVarNameInTable="columnInstallStoreQty" msprop:Generator_ColumnPropNameInRow="InstallStoreQty" msprop:Generator_ColumnPropNameInTable="InstallStoreQtyColumn" msprop:Generator_UserColumnName="InstallStoreQty" type="xs:int" default="0" />
              <xs:element name="FirstWeek" msprop:Generator_ColumnVarNameInTable="columnFirstWeek" msprop:Generator_ColumnPropNameInRow="FirstWeek" msprop:Generator_ColumnPropNameInTable="FirstWeekColumn" msprop:Generator_UserColumnName="FirstWeek" type="xs:dateTime" />
              <xs:element name="LastWeek" msprop:Generator_ColumnVarNameInTable="columnLastWeek" msprop:Generator_ColumnPropNameInRow="LastWeek" msprop:Generator_ColumnPropNameInTable="LastWeekColumn" msprop:Generator_UserColumnName="LastWeek" type="xs:dateTime" minOccurs="0" />
              <xs:element name="Stores" msprop:Generator_ColumnVarNameInTable="columnStores" msprop:Generator_ColumnPropNameInRow="Stores" msprop:Generator_ColumnPropNameInTable="StoresColumn" msprop:Generator_UserColumnName="Stores" type="xs:int" default="0" />
              <xs:element name="MediaName" msprop:Generator_ColumnVarNameInTable="columnMediaName" msprop:Generator_ColumnPropNameInRow="MediaName" msprop:Generator_ColumnPropNameInTable="MediaNameColumn" msprop:Generator_UserColumnName="MediaName" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Signed" msprop:Generator_ColumnVarNameInTable="columnSigned" msprop:Generator_ColumnPropNameInRow="Signed" msprop:Generator_ColumnPropNameInTable="SignedColumn" msprop:Generator_UserColumnName="Signed" type="xs:boolean" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="CompetingBurst" msprop:Generator_UserTableName="CompetingBurst" msprop:Generator_RowEvArgName="CompetingBurstRowChangeEvent" msprop:Generator_TableVarName="tableCompetingBurst" msprop:Generator_TablePropName="CompetingBurst" msprop:Generator_RowDeletingName="CompetingBurstRowDeleting" msprop:Generator_RowChangingName="CompetingBurstRowChanging" msprop:Generator_RowDeletedName="CompetingBurstRowDeleted" msprop:Generator_RowEvHandlerName="CompetingBurstRowChangeEventHandler" msprop:Generator_TableClassName="CompetingBurstDataTable" msprop:Generator_RowChangedName="CompetingBurstRowChanged" msprop:Generator_RowClassName="CompetingBurstRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="BurstID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnBurstID" msprop:Generator_ColumnPropNameInRow="BurstID" msprop:Generator_ColumnPropNameInTable="BurstIDColumn" msprop:Generator_UserColumnName="BurstID" type="xs:string" />
              <xs:element name="ContractNumber" msprop:Generator_ColumnVarNameInTable="columnContractNumber" msprop:Generator_ColumnPropNameInRow="ContractNumber" msprop:Generator_ColumnPropNameInTable="ContractNumberColumn" msprop:Generator_UserColumnName="ContractNumber">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="StorePoolID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnStorePoolID" msprop:Generator_ColumnPropNameInRow="StorePoolID" msprop:Generator_ColumnPropNameInTable="StorePoolIDColumn" msprop:Generator_UserColumnName="StorePoolID" type="xs:string" />
              <xs:element name="BrandName" msprop:Generator_ColumnVarNameInTable="columnBrandName" msprop:Generator_ColumnPropNameInRow="BrandName" msprop:Generator_ColumnPropNameInTable="BrandNameColumn" msprop:Generator_UserColumnName="BrandName" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="FirstWeek" msprop:Generator_ColumnVarNameInTable="columnFirstWeek" msprop:Generator_ColumnPropNameInRow="FirstWeek" msprop:Generator_ColumnPropNameInTable="FirstWeekColumn" msprop:Generator_UserColumnName="FirstWeek" type="xs:dateTime" />
              <xs:element name="LastWeek" msprop:Generator_ColumnVarNameInTable="columnLastWeek" msprop:Generator_ColumnPropNameInRow="LastWeek" msprop:Generator_ColumnPropNameInTable="LastWeekColumn" msprop:Generator_UserColumnName="LastWeek" type="xs:dateTime" minOccurs="0" />
              <xs:element name="InstallWeeks" msprop:Generator_ColumnVarNameInTable="columnInstallWeeks" msprop:Generator_ColumnPropNameInRow="InstallWeeks" msprop:Generator_ColumnPropNameInTable="InstallWeeksColumn" msprop:Generator_UserColumnName="InstallWeeks" type="xs:int" />
              <xs:element name="InstallStoreQty" msprop:Generator_ColumnVarNameInTable="columnInstallStoreQty" msprop:Generator_ColumnPropNameInRow="InstallStoreQty" msprop:Generator_ColumnPropNameInTable="InstallStoreQtyColumn" msprop:Generator_UserColumnName="InstallStoreQty" type="xs:int" />
              <xs:element name="StorePoolQty" msprop:Generator_ColumnVarNameInTable="columnStorePoolQty" msprop:Generator_ColumnPropNameInRow="StorePoolQty" msprop:Generator_ColumnPropNameInTable="StorePoolQtyColumn" msprop:Generator_UserColumnName="StorePoolQty" type="xs:int" />
              <xs:element name="CreationDate" msprop:Generator_ColumnVarNameInTable="columnCreationDate" msprop:Generator_ColumnPropNameInRow="CreationDate" msprop:Generator_ColumnPropNameInTable="CreationDateColumn" msprop:Generator_UserColumnName="CreationDate" type="xs:dateTime" />
              <xs:element name="SignDate" msprop:Generator_ColumnVarNameInTable="columnSignDate" msprop:Generator_ColumnPropNameInRow="SignDate" msprop:Generator_ColumnPropNameInTable="SignDateColumn" msprop:Generator_UserColumnName="SignDate" type="xs:dateTime" minOccurs="0" />
              <xs:element name="StoreListConfirmed" msprop:Generator_ColumnVarNameInTable="columnStoreListConfirmed" msprop:Generator_ColumnPropNameInRow="StoreListConfirmed" msprop:Generator_ColumnPropNameInTable="StoreListConfirmedColumn" msprop:Generator_UserColumnName="StoreListConfirmed" type="xs:boolean" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="StoreList" msprop:Generator_UserTableName="StoreList" msprop:Generator_RowEvArgName="StoreListRowChangeEvent" msprop:Generator_TableVarName="tableStoreList" msprop:Generator_TablePropName="StoreList" msprop:Generator_RowDeletingName="StoreListRowDeleting" msprop:Generator_RowChangingName="StoreListRowChanging" msprop:Generator_RowDeletedName="StoreListRowDeleted" msprop:Generator_RowEvHandlerName="StoreListRowChangeEventHandler" msprop:Generator_TableClassName="StoreListDataTable" msprop:Generator_RowChangedName="StoreListRowChanged" msprop:Generator_RowClassName="StoreListRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="BurstID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnBurstID" msprop:Generator_ColumnPropNameInRow="BurstID" msprop:Generator_ColumnPropNameInTable="BurstIDColumn" msprop:Generator_UserColumnName="BurstID" type="xs:string" />
              <xs:element name="StoreID" msprop:Generator_ColumnVarNameInTable="columnStoreID" msprop:Generator_ColumnPropNameInRow="StoreID" msprop:Generator_ColumnPropNameInTable="StoreIDColumn" msprop:Generator_UserColumnName="StoreID" type="xs:int" />
              <xs:element name="StoreDescription" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnStoreDescription" msprop:Generator_ColumnPropNameInRow="StoreDescription" msprop:Generator_ColumnPropNameInTable="StoreDescriptionColumn" msprop:Generator_UserColumnName="StoreDescription" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="552" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Store" msprop:Generator_UserTableName="Store" msprop:Generator_RowEvArgName="StoreRowChangeEvent" msprop:Generator_TableVarName="tableStore" msprop:Generator_TablePropName="Store" msprop:Generator_RowDeletingName="StoreRowDeleting" msprop:Generator_RowChangingName="StoreRowChanging" msprop:Generator_RowDeletedName="StoreRowDeleted" msprop:Generator_RowEvHandlerName="StoreRowChangeEventHandler" msprop:Generator_TableClassName="StoreDataTable" msprop:Generator_RowChangedName="StoreRowChanged" msprop:Generator_RowClassName="StoreRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="StoreID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msprop:Generator_ColumnVarNameInTable="columnStoreID" msprop:Generator_ColumnPropNameInRow="StoreID" msprop:Generator_ColumnPropNameInTable="StoreIDColumn" msprop:Generator_UserColumnName="StoreID" type="xs:int" />
              <xs:element name="StoreNumber" msprop:Generator_ColumnVarNameInTable="columnStoreNumber" msprop:Generator_ColumnPropNameInRow="StoreNumber" msprop:Generator_ColumnPropNameInTable="StoreNumberColumn" msprop:Generator_UserColumnName="StoreNumber">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="StoreName" msprop:Generator_ColumnVarNameInTable="columnStoreName" msprop:Generator_ColumnPropNameInRow="StoreName" msprop:Generator_ColumnPropNameInTable="StoreNameColumn" msprop:Generator_UserColumnName="StoreName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CityName" msprop:Generator_ColumnVarNameInTable="columnCityName" msprop:Generator_ColumnPropNameInRow="CityName" msprop:Generator_ColumnPropNameInTable="CityNameColumn" msprop:Generator_UserColumnName="CityName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="RegionName" msprop:Generator_ColumnVarNameInTable="columnRegionName" msprop:Generator_ColumnPropNameInRow="RegionName" msprop:Generator_ColumnPropNameInTable="RegionNameColumn" msprop:Generator_UserColumnName="RegionName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="MediaAllowed" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnMediaAllowed" msprop:Generator_ColumnPropNameInRow="MediaAllowed" msprop:Generator_ColumnPropNameInTable="MediaAllowedColumn" msprop:Generator_UserColumnName="MediaAllowed" type="xs:boolean" minOccurs="0" />
              <xs:element name="StoreListMember" msprop:Generator_ColumnVarNameInTable="columnStoreListMember" msprop:Generator_ColumnPropNameInRow="StoreListMember" msprop:Generator_ColumnPropNameInTable="StoreListMemberColumn" msprop:Generator_UserColumnName="StoreListMember" type="xs:boolean" minOccurs="0" />
              <xs:element name="StorePoolMember" msprop:Generator_ColumnVarNameInTable="columnStorePoolMember" msprop:Generator_ColumnPropNameInRow="StorePoolMember" msprop:Generator_ColumnPropNameInTable="StorePoolMemberColumn" msprop:Generator_UserColumnName="StorePoolMember" type="xs:boolean" minOccurs="0" />
              <xs:element name="SourceBurstStoreListMember" msprop:Generator_ColumnVarNameInTable="columnSourceBurstStoreListMember" msprop:Generator_ColumnPropNameInRow="SourceBurstStoreListMember" msprop:Generator_ColumnPropNameInTable="SourceBurstStoreListMemberColumn" msprop:Generator_UserColumnName="SourceBurstStoreListMember" type="xs:boolean" default="false" minOccurs="0" />
              <xs:element name="Dormant" msprop:Generator_ColumnVarNameInTable="columnDormant" msprop:Generator_ColumnPropNameInRow="Dormant" msprop:Generator_ColumnPropNameInTable="DormantColumn" msprop:Generator_UserColumnName="Dormant" type="xs:boolean" />
              <xs:element name="Taken" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnTaken" msprop:Generator_ColumnPropNameInRow="Taken" msprop:Generator_ColumnPropNameInTable="TakenColumn" msprop:Generator_UserColumnName="Taken" type="xs:boolean" minOccurs="0" />
              <xs:element name="IndependentStoreListMember" msprop:Generator_ColumnVarNameInTable="columnIndependentStoreListMember" msprop:Generator_ColumnPropNameInRow="IndependentStoreListMember" msprop:Generator_ColumnPropNameInTable="IndependentStoreListMemberColumn" msprop:Generator_UserColumnName="IndependentStoreListMember" type="xs:boolean" minOccurs="0" />
              <xs:element name="MediaNotAllowed" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnMediaNotAllowed" msprop:Generator_ColumnPropNameInRow="MediaNotAllowed" msprop:Generator_ColumnPropNameInTable="MediaNotAllowedColumn" msprop:Generator_UserColumnName="MediaNotAllowed" type="xs:boolean" minOccurs="0" />
              <xs:element name="StoreGroup" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnStoreGroup" msprop:Generator_ColumnPropNameInRow="StoreGroup" msprop:Generator_ColumnPropNameInTable="StoreGroupColumn" msprop:Generator_UserColumnName="StoreGroup" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="**********" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="MediaAllowedInCategoryForStore" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnMediaAllowedInCategoryForStore" msprop:Generator_ColumnPropNameInRow="MediaAllowedInCategoryForStore" msprop:Generator_ColumnPropNameInTable="MediaAllowedInCategoryForStoreColumn" msprop:Generator_UserColumnName="MediaAllowedInCategoryForStore" type="xs:boolean" default="false" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="IndependentStoreListMember" msprop:Generator_TableClassName="IndependentStoreListMemberDataTable" msprop:Generator_TableVarName="tableIndependentStoreListMember" msprop:Generator_RowChangedName="IndependentStoreListMemberRowChanged" msprop:Generator_TablePropName="IndependentStoreListMember" msprop:Generator_RowDeletingName="IndependentStoreListMemberRowDeleting" msprop:Generator_RowChangingName="IndependentStoreListMemberRowChanging" msprop:Generator_RowEvHandlerName="IndependentStoreListMemberRowChangeEventHandler" msprop:Generator_RowDeletedName="IndependentStoreListMemberRowDeleted" msprop:Generator_RowClassName="IndependentStoreListMemberRow" msprop:Generator_UserTableName="IndependentStoreListMember" msprop:Generator_RowEvArgName="IndependentStoreListMemberRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="IndependentStoreListID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnIndependentStoreListID" msprop:Generator_ColumnPropNameInRow="IndependentStoreListID" msprop:Generator_ColumnPropNameInTable="IndependentStoreListIDColumn" msprop:Generator_UserColumnName="IndependentStoreListID" type="xs:string" />
              <xs:element name="StoreID" msprop:Generator_ColumnVarNameInTable="columnStoreID" msprop:Generator_ColumnPropNameInRow="StoreID" msprop:Generator_ColumnPropNameInTable="StoreIDColumn" msprop:Generator_UserColumnName="StoreID" type="xs:int" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="IndependentStoreList" msprop:Generator_TableClassName="IndependentStoreListDataTable" msprop:Generator_TableVarName="tableIndependentStoreList" msprop:Generator_RowChangedName="IndependentStoreListRowChanged" msprop:Generator_TablePropName="IndependentStoreList" msprop:Generator_RowDeletingName="IndependentStoreListRowDeleting" msprop:Generator_RowChangingName="IndependentStoreListRowChanging" msprop:Generator_RowEvHandlerName="IndependentStoreListRowChangeEventHandler" msprop:Generator_RowDeletedName="IndependentStoreListRowDeleted" msprop:Generator_RowClassName="IndependentStoreListRow" msprop:Generator_UserTableName="IndependentStoreList" msprop:Generator_RowEvArgName="IndependentStoreListRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="IndependentStoreListID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnIndependentStoreListID" msprop:Generator_ColumnPropNameInRow="IndependentStoreListID" msprop:Generator_ColumnPropNameInTable="IndependentStoreListIDColumn" msprop:Generator_UserColumnName="IndependentStoreListID" type="xs:string" />
              <xs:element name="IndependentStoreListName" msprop:Generator_ColumnVarNameInTable="columnIndependentStoreListName" msprop:Generator_ColumnPropNameInRow="IndependentStoreListName" msprop:Generator_ColumnPropNameInTable="IndependentStoreListNameColumn" msprop:Generator_UserColumnName="IndependentStoreListName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ValidMedia" msprop:Generator_TableClassName="ValidMediaDataTable" msprop:Generator_TableVarName="tableValidMedia" msprop:Generator_RowChangedName="ValidMediaRowChanged" msprop:Generator_TablePropName="ValidMedia" msprop:Generator_RowDeletingName="ValidMediaRowDeleting" msprop:Generator_RowChangingName="ValidMediaRowChanging" msprop:Generator_RowEvHandlerName="ValidMediaRowChangeEventHandler" msprop:Generator_RowDeletedName="ValidMediaRowDeleted" msprop:Generator_RowClassName="ValidMediaRow" msprop:Generator_UserTableName="ValidMedia" msprop:Generator_RowEvArgName="ValidMediaRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MediaID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnMediaID" msprop:Generator_ColumnPropNameInRow="MediaID" msprop:Generator_ColumnPropNameInTable="MediaIDColumn" msprop:Generator_UserColumnName="MediaID" type="xs:int" />
              <xs:element name="Valid" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnValid" msprop:Generator_ColumnPropNameInRow="Valid" msprop:Generator_ColumnPropNameInTable="ValidColumn" msprop:Generator_UserColumnName="Valid" type="xs:boolean" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ResearchCategory" msprop:Generator_TableClassName="ResearchCategoryDataTable" msprop:Generator_TableVarName="tableResearchCategory" msprop:Generator_RowChangedName="ResearchCategoryRowChanged" msprop:Generator_TablePropName="ResearchCategory" msprop:Generator_RowDeletingName="ResearchCategoryRowDeleting" msprop:Generator_RowChangingName="ResearchCategoryRowChanging" msprop:Generator_RowEvHandlerName="ResearchCategoryRowChangeEventHandler" msprop:Generator_RowDeletedName="ResearchCategoryRowDeleted" msprop:Generator_RowClassName="ResearchCategoryRow" msprop:Generator_UserTableName="ResearchCategory" msprop:Generator_RowEvArgName="ResearchCategoryRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ResearchCategoryID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msdata:Caption="DontAudit" msprop:Generator_ColumnVarNameInTable="columnResearchCategoryID" msprop:Generator_ColumnPropNameInRow="ResearchCategoryID" msprop:Generator_ColumnPropNameInTable="ResearchCategoryIDColumn" msprop:Generator_UserColumnName="ResearchCategoryID" type="xs:string" />
              <xs:element name="ContractID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msdata:Caption="DontAudit" msprop:Generator_ColumnVarNameInTable="columnContractID" msprop:Generator_ColumnPropNameInRow="ContractID" msprop:Generator_ColumnPropNameInTable="ContractIDColumn" msprop:Generator_UserColumnName="ContractID" type="xs:string" />
              <xs:element name="CategoryID" msdata:Caption="DontAudit" msprop:Generator_ColumnVarNameInTable="columnCategoryID" msprop:Generator_ColumnPropNameInRow="CategoryID" msprop:Generator_ColumnPropNameInTable="CategoryIDColumn" msprop:Generator_UserColumnName="CategoryID" type="xs:int" />
              <xs:element name="Months" msdata:Caption="DontAudit" msprop:Generator_ColumnVarNameInTable="columnMonths" msprop:Generator_ColumnPropNameInRow="Months" msprop:Generator_ColumnPropNameInTable="MonthsColumn" msprop:Generator_UserColumnName="Months" type="xs:int" default="1" />
              <xs:element name="Fee" msprop:Generator_ColumnVarNameInTable="columnFee" msprop:Generator_ColumnPropNameInRow="Fee" msprop:Generator_ColumnPropNameInTable="FeeColumn" msprop:Generator_UserColumnName="Fee" type="xs:decimal" default="0" />
              <xs:element name="FromDate" msdata:Caption="DontAudit" msprop:Generator_ColumnVarNameInTable="columnFromDate" msprop:Generator_ColumnPropNameInRow="FromDate" msprop:Generator_ColumnPropNameInTable="FromDateColumn" msprop:Generator_UserColumnName="FromDate" type="xs:dateTime" />
              <xs:element name="FirstMonth" msdata:Caption="First Month" msprop:Generator_ColumnVarNameInTable="columnFirstMonth" msprop:Generator_ColumnPropNameInRow="FirstMonth" msprop:Generator_ColumnPropNameInTable="FirstMonthColumn" msprop:Generator_UserColumnName="FirstMonth" default="" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="35" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="LastMonth" msdata:Caption="Last Month" msprop:Generator_ColumnVarNameInTable="columnLastMonth" msprop:Generator_ColumnPropNameInRow="LastMonth" msprop:Generator_ColumnPropNameInTable="LastMonthColumn" msprop:Generator_UserColumnName="LastMonth" default="" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="35" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CategoryName" msdata:Caption="Category Name" msprop:Generator_ColumnVarNameInTable="columnCategoryName" msprop:Generator_ColumnPropNameInRow="CategoryName" msprop:Generator_ColumnPropNameInTable="CategoryNameColumn" msprop:Generator_UserColumnName="CategoryName" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Discount" msprop:Generator_ColumnVarNameInTable="columnDiscount" msprop:Generator_ColumnPropNameInRow="Discount" msprop:Generator_ColumnPropNameInTable="DiscountColumn" msprop:Generator_UserColumnName="Discount" type="xs:decimal" default="0" />
              <xs:element name="NetFee" msprop:Generator_ColumnVarNameInTable="columnNetFee" msprop:Generator_ColumnPropNameInRow="NetFee" msprop:Generator_ColumnPropNameInTable="NetFeeColumn" msprop:Generator_UserColumnName="NetFee" type="xs:decimal" default="0" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="BillingInstruction" msprop:Generator_TableClassName="BillingInstructionDataTable" msprop:Generator_TableVarName="tableBillingInstruction" msprop:Generator_RowChangedName="BillingInstructionRowChanged" msprop:Generator_TablePropName="BillingInstruction" msprop:Generator_RowDeletingName="BillingInstructionRowDeleting" msprop:Generator_RowChangingName="BillingInstructionRowChanging" msprop:Generator_RowEvHandlerName="BillingInstructionRowChangeEventHandler" msprop:Generator_RowDeletedName="BillingInstructionRowDeleted" msprop:Generator_RowClassName="BillingInstructionRow" msprop:Generator_UserTableName="BillingInstruction" msprop:Generator_RowEvArgName="BillingInstructionRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="BillingInstructionID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnBillingInstructionID" msprop:Generator_ColumnPropNameInRow="BillingInstructionID" msprop:Generator_ColumnPropNameInTable="BillingInstructionIDColumn" msprop:Generator_UserColumnName="BillingInstructionID" type="xs:string" />
              <xs:element name="ContractID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnContractID" msprop:Generator_ColumnPropNameInRow="ContractID" msprop:Generator_ColumnPropNameInTable="ContractIDColumn" msprop:Generator_UserColumnName="ContractID" type="xs:string" />
              <xs:element name="PeriodID" msprop:Generator_ColumnVarNameInTable="columnPeriodID" msprop:Generator_ColumnPropNameInRow="PeriodID" msprop:Generator_ColumnPropNameInTable="PeriodIDColumn" msprop:Generator_UserColumnName="PeriodID" type="xs:int" />
              <xs:element name="Amount" msprop:Generator_ColumnVarNameInTable="columnAmount" msprop:Generator_ColumnPropNameInRow="Amount" msprop:Generator_ColumnPropNameInTable="AmountColumn" msprop:Generator_UserColumnName="Amount" type="xs:decimal" />
              <xs:element name="PONumber" msprop:Generator_ColumnVarNameInTable="columnPONumber" msprop:Generator_ColumnPropNameInRow="PONumber" msprop:Generator_ColumnPropNameInTable="PONumberColumn" msprop:Generator_UserColumnName="PONumber">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="PeriodName" msprop:Generator_ColumnVarNameInTable="columnPeriodName" msprop:Generator_ColumnPropNameInRow="PeriodName" msprop:Generator_ColumnPropNameInTable="PeriodNameColumn" msprop:Generator_UserColumnName="PeriodName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ContractClassificaiton" msprop:Generator_TableClassName="ContractClassificaitonDataTable" msprop:Generator_TableVarName="tableContractClassificaiton" msprop:Generator_RowChangedName="ContractClassificaitonRowChanged" msprop:Generator_TablePropName="ContractClassificaiton" msprop:Generator_RowDeletingName="ContractClassificaitonRowDeleting" msprop:Generator_RowChangingName="ContractClassificaitonRowChanging" msprop:Generator_RowEvHandlerName="ContractClassificaitonRowChangeEventHandler" msprop:Generator_RowDeletedName="ContractClassificaitonRowDeleted" msprop:Generator_RowClassName="ContractClassificaitonRow" msprop:Generator_UserTableName="ContractClassificaiton" msprop:Generator_RowEvArgName="ContractClassificaitonRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ContractClassificationId" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnContractClassificationId" msprop:Generator_ColumnPropNameInRow="ContractClassificationId" msprop:Generator_ColumnPropNameInTable="ContractClassificationIdColumn" msprop:Generator_UserColumnName="ContractClassificationId" type="xs:int" />
              <xs:element name="Classification" msprop:Generator_ColumnVarNameInTable="columnClassification" msprop:Generator_ColumnPropNameInRow="Classification" msprop:Generator_ColumnPropNameInTable="ClassificationColumn" msprop:Generator_UserColumnName="Classification">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="BurstInstallationDay" msprop:Generator_TableClassName="BurstInstallationDayDataTable" msprop:Generator_TableVarName="tableBurstInstallationDay" msprop:Generator_TablePropName="BurstInstallationDay" msprop:Generator_RowDeletingName="BurstInstallationDayRowDeleting" msprop:Generator_RowChangingName="BurstInstallationDayRowChanging" msprop:Generator_RowEvHandlerName="BurstInstallationDayRowChangeEventHandler" msprop:Generator_RowDeletedName="BurstInstallationDayRowDeleted" msprop:Generator_UserTableName="BurstInstallationDay" msprop:Generator_RowChangedName="BurstInstallationDayRowChanged" msprop:Generator_RowEvArgName="BurstInstallationDayRowChangeEvent" msprop:Generator_RowClassName="BurstInstallationDayRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="BurstID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnBurstID" msprop:Generator_ColumnPropNameInRow="BurstID" msprop:Generator_ColumnPropNameInTable="BurstIDColumn" msprop:Generator_UserColumnName="BurstID" type="xs:string" />
              <xs:element name="InstallationDayID" msprop:Generator_ColumnVarNameInTable="columnInstallationDayID" msprop:Generator_ColumnPropNameInRow="InstallationDayID" msprop:Generator_ColumnPropNameInTable="InstallationDayIDColumn" msprop:Generator_UserColumnName="InstallationDayID" type="xs:int" />
              <xs:element name="InstallationDayName" msprop:Generator_ColumnVarNameInTable="columnInstallationDayName" msprop:Generator_ColumnPropNameInRow="InstallationDayName" msprop:Generator_ColumnPropNameInTable="InstallationDayNameColumn" msprop:Generator_UserColumnName="InstallationDayName" type="xs:string" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="BurstPcaStatus" msprop:Generator_TableClassName="BurstPcaStatusDataTable" msprop:Generator_TableVarName="tableBurstPcaStatus" msprop:Generator_TablePropName="BurstPcaStatus" msprop:Generator_RowDeletingName="BurstPcaStatusRowDeleting" msprop:Generator_RowChangingName="BurstPcaStatusRowChanging" msprop:Generator_RowEvHandlerName="BurstPcaStatusRowChangeEventHandler" msprop:Generator_RowDeletedName="BurstPcaStatusRowDeleted" msprop:Generator_UserTableName="BurstPcaStatus" msprop:Generator_RowChangedName="BurstPcaStatusRowChanged" msprop:Generator_RowEvArgName="BurstPcaStatusRowChangeEvent" msprop:Generator_RowClassName="BurstPcaStatusRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="BurstID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnBurstID" msprop:Generator_ColumnPropNameInRow="BurstID" msprop:Generator_ColumnPropNameInTable="BurstIDColumn" msprop:Generator_UserColumnName="BurstID" type="xs:string" />
              <xs:element name="PcaStatusId" msprop:Generator_ColumnVarNameInTable="columnPcaStatusId" msprop:Generator_ColumnPropNameInRow="PcaStatusId" msprop:Generator_ColumnPropNameInTable="PcaStatusIdColumn" msprop:Generator_UserColumnName="PcaStatusId" type="xs:int" />
              <xs:element name="PcaStatusName" msprop:Generator_ColumnVarNameInTable="columnPcaStatusName" msprop:Generator_ColumnPropNameInRow="PcaStatusName" msprop:Generator_ColumnPropNameInTable="PcaStatusNameColumn" msprop:Generator_UserColumnName="PcaStatusName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CreationDate" msprop:Generator_ColumnVarNameInTable="columnCreationDate" msprop:Generator_ColumnPropNameInRow="CreationDate" msprop:Generator_ColumnPropNameInTable="CreationDateColumn" msprop:Generator_UserColumnName="CreationDate" type="xs:dateTime" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="MediaCost" msprop:Generator_TableClassName="MediaCostDataTable" msprop:Generator_TableVarName="tableMediaCost" msprop:Generator_RowChangedName="MediaCostRowChanged" msprop:Generator_TablePropName="MediaCost" msprop:Generator_RowDeletingName="MediaCostRowDeleting" msprop:Generator_RowChangingName="MediaCostRowChanging" msprop:Generator_RowEvHandlerName="MediaCostRowChangeEventHandler" msprop:Generator_RowDeletedName="MediaCostRowDeleted" msprop:Generator_RowClassName="MediaCostRow" msprop:Generator_UserTableName="MediaCost" msprop:Generator_RowEvArgName="MediaCostRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MediaName" msprop:Generator_ColumnVarNameInTable="columnMediaName" msprop:Generator_ColumnPropNameInRow="MediaName" msprop:Generator_ColumnPropNameInTable="MediaNameColumn" msprop:Generator_UserColumnName="MediaName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="MediaCostSystemCalculated" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnMediaCostSystemCalculated" msprop:Generator_ColumnPropNameInRow="MediaCostSystemCalculated" msprop:Generator_ColumnPropNameInTable="MediaCostSystemCalculatedColumn" msprop:Generator_UserColumnName="MediaCostSystemCalculated" type="xs:decimal" minOccurs="0" />
              <xs:element name="MediaCostInputed" msprop:Generator_ColumnVarNameInTable="columnMediaCostInputed" msprop:Generator_ColumnPropNameInRow="MediaCostInputed" msprop:Generator_ColumnPropNameInTable="MediaCostInputedColumn" msprop:Generator_UserColumnName="MediaCostInputed" type="xs:decimal" minOccurs="0" />
              <xs:element name="ContractID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnContractID" msprop:Generator_ColumnPropNameInRow="ContractID" msprop:Generator_ColumnPropNameInTable="ContractIDColumn" msprop:Generator_UserColumnName="ContractID" type="xs:string" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ContractInvoices" msprop:Generator_TableClassName="ContractInvoicesDataTable" msprop:Generator_TableVarName="tableContractInvoices" msprop:Generator_RowChangedName="ContractInvoicesRowChanged" msprop:Generator_TablePropName="ContractInvoices" msprop:Generator_RowDeletingName="ContractInvoicesRowDeleting" msprop:Generator_RowChangingName="ContractInvoicesRowChanging" msprop:Generator_RowEvHandlerName="ContractInvoicesRowChangeEventHandler" msprop:Generator_RowDeletedName="ContractInvoicesRowDeleted" msprop:Generator_RowClassName="ContractInvoicesRow" msprop:Generator_UserTableName="ContractInvoices" msprop:Generator_RowEvArgName="ContractInvoicesRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ContractInvoiceNumber" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnContractInvoiceNumber" msprop:Generator_ColumnPropNameInRow="ContractInvoiceNumber" msprop:Generator_ColumnPropNameInTable="ContractInvoiceNumberColumn" msprop:Generator_UserColumnName="ContractInvoiceNumber" type="xs:int" />
              <xs:element name="ContractID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnContractID" msprop:Generator_ColumnPropNameInRow="ContractID" msprop:Generator_ColumnPropNameInTable="ContractIDColumn" msprop:Generator_UserColumnName="ContractID" type="xs:string" />
              <xs:element name="InvoiceNumber" msprop:Generator_ColumnVarNameInTable="columnInvoiceNumber" msprop:Generator_ColumnPropNameInRow="InvoiceNumber" msprop:Generator_ColumnPropNameInTable="InvoiceNumberColumn" msprop:Generator_UserColumnName="InvoiceNumber">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="InvoiceAmount" msprop:Generator_ColumnVarNameInTable="columnInvoiceAmount" msprop:Generator_ColumnPropNameInRow="InvoiceAmount" msprop:Generator_ColumnPropNameInTable="InvoiceAmountColumn" msprop:Generator_UserColumnName="InvoiceAmount" type="xs:decimal" default="0.00" />
              <xs:element name="MediaName" msprop:Generator_ColumnVarNameInTable="columnMediaName" msprop:Generator_ColumnPropNameInRow="MediaName" msprop:Generator_ColumnPropNameInTable="MediaNameColumn" msprop:Generator_UserColumnName="MediaName" type="xs:string" default="Select..." minOccurs="0" />
              <xs:element name="MediaId" msprop:Generator_ColumnVarNameInTable="columnMediaId" msprop:Generator_ColumnPropNameInRow="MediaId" msprop:Generator_ColumnPropNameInTable="MediaIdColumn" msprop:Generator_UserColumnName="MediaId" type="xs:string" default="0" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ContractCostEstimates" msprop:Generator_TableClassName="ContractCostEstimatesDataTable" msprop:Generator_TableVarName="tableContractCostEstimates" msprop:Generator_RowChangedName="ContractCostEstimatesRowChanged" msprop:Generator_TablePropName="ContractCostEstimates" msprop:Generator_RowDeletingName="ContractCostEstimatesRowDeleting" msprop:Generator_RowChangingName="ContractCostEstimatesRowChanging" msprop:Generator_RowEvHandlerName="ContractCostEstimatesRowChangeEventHandler" msprop:Generator_RowDeletedName="ContractCostEstimatesRowDeleted" msprop:Generator_RowClassName="ContractCostEstimatesRow" msprop:Generator_UserTableName="ContractCostEstimates" msprop:Generator_RowEvArgName="ContractCostEstimatesRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="CostEstimateID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnCostEstimateID" msprop:Generator_ColumnPropNameInRow="CostEstimateID" msprop:Generator_ColumnPropNameInTable="CostEstimateIDColumn" msprop:Generator_UserColumnName="CostEstimateID" type="xs:int" />
              <xs:element name="ContractID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnContractID" msprop:Generator_ColumnPropNameInRow="ContractID" msprop:Generator_ColumnPropNameInTable="ContractIDColumn" msprop:Generator_UserColumnName="ContractID" type="xs:string" />
              <xs:element name="CostEstimateNumber" msprop:Generator_ColumnVarNameInTable="columnCostEstimateNumber" msprop:Generator_ColumnPropNameInRow="CostEstimateNumber" msprop:Generator_ColumnPropNameInTable="CostEstimateNumberColumn" msprop:Generator_UserColumnName="CostEstimateNumber">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CostEstimateAmount" msprop:Generator_ColumnVarNameInTable="columnCostEstimateAmount" msprop:Generator_ColumnPropNameInRow="CostEstimateAmount" msprop:Generator_ColumnPropNameInTable="CostEstimateAmountColumn" msprop:Generator_UserColumnName="CostEstimateAmount" type="xs:decimal" default="0.00" />
              <xs:element name="MediaName" msprop:Generator_ColumnVarNameInTable="columnMediaName" msprop:Generator_ColumnPropNameInRow="MediaName" msprop:Generator_ColumnPropNameInTable="MediaNameColumn" msprop:Generator_UserColumnName="MediaName" default="Select..." minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="MediaId" msprop:Generator_ColumnVarNameInTable="columnMediaId" msprop:Generator_ColumnPropNameInRow="MediaId" msprop:Generator_ColumnPropNameInTable="MediaIdColumn" msprop:Generator_UserColumnName="MediaId" type="xs:string" default="0" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ContractBrandAgency" msprop:Generator_TableClassName="ContractBrandAgencyDataTable" msprop:Generator_TableVarName="tableContractBrandAgency" msprop:Generator_RowChangedName="ContractBrandAgencyRowChanged" msprop:Generator_TablePropName="ContractBrandAgency" msprop:Generator_RowDeletingName="ContractBrandAgencyRowDeleting" msprop:Generator_RowChangingName="ContractBrandAgencyRowChanging" msprop:Generator_RowEvHandlerName="ContractBrandAgencyRowChangeEventHandler" msprop:Generator_RowDeletedName="ContractBrandAgencyRowDeleted" msprop:Generator_RowClassName="ContractBrandAgencyRow" msprop:Generator_UserTableName="ContractBrandAgency" msprop:Generator_RowEvArgName="ContractBrandAgencyRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ClientID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnClientID" msprop:Generator_ColumnPropNameInRow="ClientID" msprop:Generator_ColumnPropNameInTable="ClientIDColumn" msprop:Generator_UserColumnName="ClientID" type="xs:int" />
              <xs:element name="Agency" msprop:Generator_ColumnVarNameInTable="columnAgency" msprop:Generator_ColumnPropNameInRow="Agency" msprop:Generator_ColumnPropNameInTable="AgencyColumn" msprop:Generator_UserColumnName="Agency" type="xs:boolean" />
              <xs:element name="BrandID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnBrandID" msprop:Generator_ColumnPropNameInRow="BrandID" msprop:Generator_ColumnPropNameInTable="BrandIDColumn" msprop:Generator_UserColumnName="BrandID" type="xs:string" />
              <xs:element name="BrandName" msprop:Generator_ColumnVarNameInTable="columnBrandName" msprop:Generator_ColumnPropNameInRow="BrandName" msprop:Generator_ColumnPropNameInTable="BrandNameColumn" msprop:Generator_UserColumnName="BrandName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ContractID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnContractID" msprop:Generator_ColumnPropNameInRow="ContractID" msprop:Generator_ColumnPropNameInTable="ContractIDColumn" msprop:Generator_UserColumnName="ContractID" type="xs:string" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Contract" />
      <xs:field xpath="mstns:ContractID" />
    </xs:unique>
    <xs:unique name="Burst_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Burst" />
      <xs:field xpath="mstns:BurstID" />
    </xs:unique>
    <xs:unique name="AccountManager_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:AccountManager" />
      <xs:field xpath="mstns:AccountManagerID" />
    </xs:unique>
    <xs:unique name="BurstLoadingFee_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:BurstLoadingFee" />
      <xs:field xpath="mstns:BurstID" />
      <xs:field xpath="mstns:LoadingFeeID" />
    </xs:unique>
    <xs:unique name="BurstCategory_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:BurstCategory" />
      <xs:field xpath="mstns:BurstID" />
      <xs:field xpath="mstns:CategoryID" />
    </xs:unique>
    <xs:unique name="LoadingFee_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:LoadingFee" />
      <xs:field xpath="mstns:LoadingFeeID" />
    </xs:unique>
    <xs:unique name="Category_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Category" />
      <xs:field xpath="mstns:CategoryID" />
    </xs:unique>
    <xs:unique name="PurchaseOrderNumber_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:PurchaseOrderNumber" />
      <xs:field xpath="mstns:ContractID" />
      <xs:field xpath="mstns:PurchaseOrderNumber" />
    </xs:unique>
    <xs:unique name="ContractInventoryQty_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ContractInventoryQty" />
      <xs:field xpath="mstns:ContractItemQtyID" />
    </xs:unique>
    <xs:unique name="ContractMiscellaneousCharge_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ContractMiscellaneousCharge" />
      <xs:field xpath="mstns:ContractMiscellaneousChargeID" />
    </xs:unique>
    <xs:unique name="StorePool_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:StorePool" />
      <xs:field xpath="mstns:StorePoolID" />
    </xs:unique>
    <xs:unique name="BrandFamilyMember_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:BrandFamilyMember" />
      <xs:field xpath="mstns:NonCompetingBrandID" />
    </xs:unique>
    <xs:unique name="PeerBurst_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:PeerBurst" />
      <xs:field xpath="mstns:BurstID" />
    </xs:unique>
    <xs:unique name="CompetingBurst_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:CompetingBurst" />
      <xs:field xpath="mstns:BurstID" />
    </xs:unique>
    <xs:unique name="StoreList_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:StoreList" />
      <xs:field xpath="mstns:BurstID" />
      <xs:field xpath="mstns:StoreID" />
    </xs:unique>
    <xs:unique name="Store_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Store" />
      <xs:field xpath="mstns:StoreID" />
    </xs:unique>
    <xs:unique name="IndependentStoreListMember_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:IndependentStoreListMember" />
      <xs:field xpath="mstns:IndependentStoreListID" />
      <xs:field xpath="mstns:StoreID" />
    </xs:unique>
    <xs:unique name="IndependentStoreList_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:IndependentStoreList" />
      <xs:field xpath="mstns:IndependentStoreListID" />
    </xs:unique>
    <xs:unique name="ValidMedia_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ValidMedia" />
      <xs:field xpath="mstns:MediaID" />
    </xs:unique>
    <xs:unique name="ResearchCategory_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ResearchCategory" />
      <xs:field xpath="mstns:ResearchCategoryID" />
    </xs:unique>
    <xs:unique name="BillingInstruction_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:BillingInstruction" />
      <xs:field xpath="mstns:BillingInstructionID" />
    </xs:unique>
    <xs:unique name="ContractClassificaiton_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ContractClassificaiton" />
      <xs:field xpath="mstns:ContractClassificationId" />
    </xs:unique>
    <xs:unique name="BurstInstallationDay_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:BurstInstallationDay" />
      <xs:field xpath="mstns:BurstID" />
      <xs:field xpath="mstns:InstallationDayID" />
    </xs:unique>
    <xs:unique name="ContractInvoices_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ContractInvoices" />
      <xs:field xpath="mstns:ContractInvoiceNumber" />
    </xs:unique>
    <xs:unique name="ContractCostEstimates_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ContractCostEstimates" />
      <xs:field xpath="mstns:CostEstimateID" />
    </xs:unique>
    <xs:unique name="ContractBrandAgency_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ContractBrandAgency" />
      <xs:field xpath="mstns:ClientID" />
      <xs:field xpath="mstns:BrandID" />
      <xs:field xpath="mstns:ContractID" />
    </xs:unique>
  </xs:element>
  <xs:annotation>
    <xs:appinfo>
      <msdata:Relationship name="FK_Burst_Contract" msdata:parent="Contract" msdata:child="Burst" msdata:parentkey="ContractID" msdata:childkey="ContractID" msprop:Generator_UserChildTable="Burst" msprop:Generator_ChildPropName="GetBurstRows" msprop:Generator_UserRelationName="FK_Burst_Contract" msprop:Generator_RelationVarName="relationFK_Burst_Contract" msprop:Generator_UserParentTable="Contract" msprop:Generator_ParentPropName="ContractRow" />
      <msdata:Relationship name="FK_Contract_AccountManager" msdata:parent="AccountManager" msdata:child="Contract" msdata:parentkey="AccountManagerID" msdata:childkey="AccountManagerID" msprop:Generator_UserChildTable="Contract" msprop:Generator_ChildPropName="GetContractRows" msprop:Generator_UserRelationName="FK_Contract_AccountManager" msprop:Generator_RelationVarName="relationFK_Contract_AccountManager" msprop:Generator_UserParentTable="AccountManager" msprop:Generator_ParentPropName="AccountManagerRow" />
      <msdata:Relationship name="FK_BurstLoadingFee_Burst" msdata:parent="Burst" msdata:child="BurstLoadingFee" msdata:parentkey="BurstID" msdata:childkey="BurstID" msprop:Generator_UserChildTable="BurstLoadingFee" msprop:Generator_ChildPropName="GetBurstLoadingFeeRows" msprop:Generator_UserRelationName="FK_BurstLoadingFee_Burst" msprop:Generator_RelationVarName="relationFK_BurstLoadingFee_Burst" msprop:Generator_UserParentTable="Burst" msprop:Generator_ParentPropName="BurstRow" />
      <msdata:Relationship name="Burst_BurstCategory" msdata:parent="Burst" msdata:child="BurstCategory" msdata:parentkey="BurstID" msdata:childkey="BurstID" msprop:Generator_UserChildTable="BurstCategory" msprop:Generator_ChildPropName="GetBurstCategoryRows" msprop:Generator_UserRelationName="Burst_BurstCategory" msprop:Generator_RelationVarName="relationBurst_BurstCategory" msprop:Generator_UserParentTable="Burst" msprop:Generator_ParentPropName="BurstRow" />
      <msdata:Relationship name="FK_BurstLoadingFee_LoadingFee" msdata:parent="LoadingFee" msdata:child="BurstLoadingFee" msdata:parentkey="LoadingFeeID" msdata:childkey="LoadingFeeID" msprop:Generator_UserChildTable="BurstLoadingFee" msprop:Generator_ChildPropName="GetBurstLoadingFeeRows" msprop:Generator_UserRelationName="FK_BurstLoadingFee_LoadingFee" msprop:Generator_RelationVarName="relationFK_BurstLoadingFee_LoadingFee" msprop:Generator_UserParentTable="LoadingFee" msprop:Generator_ParentPropName="LoadingFeeRow" />
      <msdata:Relationship name="FK_BurstCategory_Category" msdata:parent="Category" msdata:child="BurstCategory" msdata:parentkey="CategoryID" msdata:childkey="CategoryID" msprop:Generator_UserChildTable="BurstCategory" msprop:Generator_ChildPropName="GetBurstCategoryRows" msprop:Generator_UserRelationName="FK_BurstCategory_Category" msprop:Generator_RelationVarName="relationFK_BurstCategory_Category" msprop:Generator_UserParentTable="Category" msprop:Generator_ParentPropName="CategoryRow" />
      <msdata:Relationship name="Contract_PurchaseOrderNumber" msdata:parent="Contract" msdata:child="PurchaseOrderNumber" msdata:parentkey="ContractID" msdata:childkey="ContractID" msprop:Generator_UserChildTable="PurchaseOrderNumber" msprop:Generator_ChildPropName="GetPurchaseOrderNumberRows" msprop:Generator_UserRelationName="Contract_PurchaseOrderNumber" msprop:Generator_RelationVarName="relationContract_PurchaseOrderNumber" msprop:Generator_UserParentTable="Contract" msprop:Generator_ParentPropName="ContractRow" />
      <msdata:Relationship name="Contract_ContractInventoryQty" msdata:parent="Contract" msdata:child="ContractInventoryQty" msdata:parentkey="ContractID" msdata:childkey="ContractID" msprop:Generator_UserChildTable="ContractInventoryQty" msprop:Generator_ChildPropName="GetContractInventoryQtyRows" msprop:Generator_UserRelationName="Contract_ContractInventoryQty" msprop:Generator_RelationVarName="relationContract_ContractInventoryQty" msprop:Generator_UserParentTable="Contract" msprop:Generator_ParentPropName="ContractRow" />
      <msdata:Relationship name="Contract_ContractMiscellaneousCharge" msdata:parent="Contract" msdata:child="ContractMiscellaneousCharge" msdata:parentkey="ContractID" msdata:childkey="ContractID" msprop:Generator_UserChildTable="ContractMiscellaneousCharge" msprop:Generator_ChildPropName="GetContractMiscellaneousChargeRows" msprop:Generator_UserRelationName="Contract_ContractMiscellaneousCharge" msprop:Generator_RelationVarName="relationContract_ContractMiscellaneousCharge" msprop:Generator_UserParentTable="Contract" msprop:Generator_ParentPropName="ContractRow" />
      <msdata:Relationship name="StorePool_Burst" msdata:parent="StorePool" msdata:child="Burst" msdata:parentkey="StorePoolID" msdata:childkey="StorePoolID" msprop:Generator_UserChildTable="Burst" msprop:Generator_ChildPropName="GetBurstRows" msprop:Generator_UserRelationName="StorePool_Burst" msprop:Generator_RelationVarName="relationStorePool_Burst" msprop:Generator_UserParentTable="StorePool" msprop:Generator_ParentPropName="StorePoolRow" />
      <msdata:Relationship name="Burst_StoreList" msdata:parent="Burst" msdata:child="StoreList" msdata:parentkey="BurstID" msdata:childkey="BurstID" msprop:Generator_UserChildTable="StoreList" msprop:Generator_ChildPropName="GetStoreListRows" msprop:Generator_UserRelationName="Burst_StoreList" msprop:Generator_RelationVarName="relationBurst_StoreList" msprop:Generator_UserParentTable="Burst" msprop:Generator_ParentPropName="BurstRow" />
      <msdata:Relationship name="FK_IndependentStoreListMember_IndependentStoreList" msdata:parent="IndependentStoreList" msdata:child="IndependentStoreListMember" msdata:parentkey="IndependentStoreListID" msdata:childkey="IndependentStoreListID" msprop:Generator_UserChildTable="IndependentStoreListMember" msprop:Generator_ChildPropName="GetIndependentStoreListMemberRows" msprop:Generator_UserRelationName="FK_IndependentStoreListMember_IndependentStoreList" msprop:Generator_ParentPropName="IndependentStoreListRow" msprop:Generator_RelationVarName="relationFK_IndependentStoreListMember_IndependentStoreList" msprop:Generator_UserParentTable="IndependentStoreList" />
      <msdata:Relationship name="FK_ResearchCategory_Category" msdata:parent="Category" msdata:child="ResearchCategory" msdata:parentkey="CategoryID" msdata:childkey="CategoryID" msprop:Generator_UserChildTable="ResearchCategory" msprop:Generator_ChildPropName="GetResearchCategoryRows" msprop:Generator_UserRelationName="FK_ResearchCategory_Category" msprop:Generator_ParentPropName="CategoryRow" msprop:Generator_RelationVarName="relationFK_ResearchCategory_Category" msprop:Generator_UserParentTable="Category" />
      <msdata:Relationship name="Contract_ResearchCategory" msdata:parent="Contract" msdata:child="ResearchCategory" msdata:parentkey="ContractID" msdata:childkey="ContractID" msprop:Generator_UserChildTable="ResearchCategory" msprop:Generator_ChildPropName="GetResearchCategoryRows" msprop:Generator_UserRelationName="Contract_ResearchCategory" msprop:Generator_ParentPropName="ContractRow" msprop:Generator_RelationVarName="relationContract_ResearchCategory" msprop:Generator_UserParentTable="Contract" />
      <msdata:Relationship name="Contract_BillingInstruction" msdata:parent="Contract" msdata:child="BillingInstruction" msdata:parentkey="ContractID" msdata:childkey="ContractID" msprop:Generator_UserChildTable="BillingInstruction" msprop:Generator_ChildPropName="GetBillingInstructionRows" msprop:Generator_UserRelationName="Contract_BillingInstruction" msprop:Generator_ParentPropName="ContractRow" msprop:Generator_RelationVarName="relationContract_BillingInstruction" msprop:Generator_UserParentTable="Contract" />
      <msdata:Relationship name="FK_Burst_BurstInstallationDay" msdata:parent="Burst" msdata:child="BurstInstallationDay" msdata:parentkey="BurstID" msdata:childkey="BurstID" msprop:Generator_UserChildTable="BurstInstallationDay" msprop:Generator_ChildPropName="GetBurstInstallationDayRows" msprop:Generator_UserRelationName="FK_Burst_BurstInstallationDay" msprop:Generator_ParentPropName="BurstRow" msprop:Generator_RelationVarName="relationFK_Burst_BurstInstallationDay" msprop:Generator_UserParentTable="Burst" />
      <msdata:Relationship name="Burst_BurstPcaStatus" msdata:parent="Burst" msdata:child="BurstPcaStatus" msdata:parentkey="BurstID" msdata:childkey="BurstID" msprop:Generator_UserChildTable="BurstPcaStatus" msprop:Generator_ChildPropName="GetBurstPcaStatusRows" msprop:Generator_UserRelationName="Burst_BurstPcaStatus" msprop:Generator_ParentPropName="BurstRow" msprop:Generator_RelationVarName="relationBurst_BurstPcaStatus" msprop:Generator_UserParentTable="Burst" />
      <msdata:Relationship name="Contract_MediaCost" msdata:parent="Contract" msdata:child="MediaCost" msdata:parentkey="ContractID" msdata:childkey="ContractID" msprop:Generator_UserChildTable="MediaCost" msprop:Generator_ChildPropName="GetMediaCostRows" msprop:Generator_UserRelationName="Contract_MediaCost" msprop:Generator_ParentPropName="ContractRow" msprop:Generator_RelationVarName="relationContract_MediaCost" msprop:Generator_UserParentTable="Contract" />
      <msdata:Relationship name="Contract_ContractInvoices" msdata:parent="Contract" msdata:child="ContractInvoices" msdata:parentkey="ContractID" msdata:childkey="ContractID" msprop:Generator_UserChildTable="ContractInvoices" msprop:Generator_ChildPropName="GetContractInvoicesRows" msprop:Generator_UserRelationName="Contract_ContractInvoices" msprop:Generator_RelationVarName="relationContract_ContractInvoices" msprop:Generator_UserParentTable="Contract" msprop:Generator_ParentPropName="ContractRow" />
      <msdata:Relationship name="Contract_ContractCostEstimates" msdata:parent="Contract" msdata:child="ContractCostEstimates" msdata:parentkey="ContractID" msdata:childkey="ContractID" msprop:Generator_UserChildTable="ContractCostEstimates" msprop:Generator_ChildPropName="GetContractCostEstimatesRows" msprop:Generator_UserRelationName="Contract_ContractCostEstimates" msprop:Generator_RelationVarName="relationContract_ContractCostEstimates" msprop:Generator_UserParentTable="Contract" msprop:Generator_ParentPropName="ContractRow" />
      <msdata:Relationship name="Contract_ContractBrandAgency" msdata:parent="Contract" msdata:child="ContractBrandAgency" msdata:parentkey="ContractID" msdata:childkey="ContractID" msprop:Generator_UserChildTable="ContractBrandAgency" msprop:Generator_ChildPropName="GetContractBrandAgencyRows" msprop:Generator_UserRelationName="Contract_ContractBrandAgency" msprop:Generator_RelationVarName="relationContract_ContractBrandAgency" msprop:Generator_UserParentTable="Contract" msprop:Generator_ParentPropName="ContractRow" />
    </xs:appinfo>
  </xs:annotation>
</xs:schema>