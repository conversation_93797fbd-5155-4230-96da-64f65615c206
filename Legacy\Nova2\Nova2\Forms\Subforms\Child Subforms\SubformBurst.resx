<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="ImageList24x24.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>153, 17</value>
  </metadata>
  <data name="ImageList24x24.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj00LjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAAD+
        FgAAAk1TRnQBSQFMAgEBAwEAAVgBCAFYAQgBGAEAARgBAAT/AREBAAj/AUIBTQE2BwABNgMAASgDAAFg
        AwABGAMAAQEBAAEQBgABEhgAAf8BfwGdAXMBewFvAZwBcwH/AX8QAAH/AX8BnAFzAXsBbwGdAXMB/wF/
        CgAB/wF/AZwBcwF7AW8BewFvAXsBbwF7AW8BewFvAXsBbwF7AW8BewFvAXsBbwF7AW8BewFvAXsBbwF7
        AW8BewFvAXsBbwF7AW8BnAFzAf8BfxYAAf8BfwH/AX8B/wF/TAAB/wF/AXsBbwH/AXsB/wF7Af8BewGc
        AXMB/wF/DAAB/wF/AZwBcwH/AXsB/wF7Af8CewFvAf8BfwYAAf8BfwF7AW8B/gF/Af4BfwH+AX8B/gF/
        Af4BfwH+AX8B/gF/Af4BfwH+AX8B/gF/Af4BfwH+AX8B/gF/Af4BfwH+AX8B/gF/Af4BfwH+AX8BewFv
        Ad4BexQAAb0BdwF7AW8BnAFvAd4Be0gAAf8BfwF7AW8B/wF7ARgBawHGAUAB7gFVAf8BewGcAXMB/wF/
        CAAB/wF/AZwBbwH/AXsB7gFVAcYBQAEYAWsB/wJ7AW8B/wF/AgAB/wF/AXsBbwH+AX8BuQFCAdUBGQH2
        ARkB9gEZAfYBGQH2ARkB9gEZAfYBGQH2ARkB9gEZAdUBGQHVARkB1QEZAdUBGQHVARkB1QEZAZQBFQFX
        ATYB3gF/AZwBdwH/AX8OAAH/AX8BewFvAb8BewHfAX8B3wF/AZwBcwH/AX9EAAH/AX8BewFvAf8BewHW
        AWYBAAE0AQABNAEAATQBagFNAf8BewGdAXMB/wF/BAAB/wF/AZ0BcwH/AXsBagFNAQABNAEAATQBAAE0
        AdYBZgH/AnsBbwH/AX8BvQF3Af4BfwGYAToB0gEAAVMBAQEzAQEBMwEBATMBAQEzAQEBMwEBATMBAQEz
        AQEBMwEBARMBAQESAQEBEgEBARIBAQHyAQAB8QEAARIBAQGPAQAB9QEhAf4BfwGcAXMMAAH/AX8BewFv
        Ad8BfwF4AWcBaAE6Aa4BSgHfAnsBbwH/AX9CAAG9AXcB/wF7AfcBagEAATgBQQE8AWMBQAFjAUABAAE4
        AYwBUQH/AX8BnAFzAf8BfwH/AX8BnAFzAf8BfwGMAVEBAAE4AWMBQAFjAUABQQE8AQABOAH3AWoB/wF7
        Ab0BdwGcAXMB/gF/AXQBAQF0AQEB/wF/AZgBPgE0AQEBdAEBAXQBAQF0AQEBdAEBAXQBAQF0AQEBdAEB
        AXQBAQFTAQEBUwEBARIBAQEWASYB/wF/AZQBEQHQAQABvQF3AXsBbwoAAf8BfwF7AW8B3wF/AVYBYwEB
        ASYBAQEmAeABHQHwAU4B3wF/AZwBc0IAAXwBbwH/AXsBYwFAASABPAGDAUABYwFAAWMBQAFiAUABAAE4
        AYwBVQH/AXsBfAFvAXwBbwH/AX8BrAFVAQABOAFiAUABYwFAAWMBQAGDAUABIAE8AWMBQAH/AXsBnAFv
        AZwBcwH+AX8BlQEFAVQBAQG6AT4B9gERAXQBAQGVAQEBlQEBAZUBAQGVAQEBdQEBAXQBAQF0AQEBdAEB
        AXQBAQFUAQEBUwEBAZQBCQG5AUIBUwEBARIBAQG9AXcBewFvCAAB/wF/AXsBbwHfAX8BeQFrASEBKgFD
        AS4BRAEyAUQBLgEiASoBnAFzAb4BdwH/AX9AAAGcAXMB/wF7AeYBTAEAAUABgwFEAWMBRAFjAUQBYwFE
        AWIBRAEAATwBawFVAf8BfwH/AX8BawFVAQABPAFiAUQBYwFEAWMBRAFjAUQBgwFEAQABQAHmAUwB/wF7
        AZwBcwGcAXMB/gF/AbYBCQF1AQEBVAEBAXUBAQGVAQEBlQEBAZUBAQGVAQEBlQEBAZUBAQGVAQEBdQEB
        AXQBAQF0AQEBdAEBAXQBAQFTAQEBEgEBARIBAQEyAQEBvQF3AXsBbwYAAf8BfwF7AW8B3wF/AXgBZwFC
        AS4BQwEuAWQBMgFkATIBZAEyAUEBKgHLAUYB/wF/AXsBb0AAAf8BfwH/AXsBvQF3AWIBRAEgAUQBgwFI
        AWMBSAFjAUgBYwFIAWMBSAEAAUAB7wFdAe8BXQEAAUABYgFIAWMBSAFjAUgBYwFIAYMBSAEgAUQBYgFE
        Ab0BdwH/AXsB/wF/AZwBcwH+AX8B1gEJAXUBAQG2AQEBtgEBAbYBAQG2AQEBlgEBAZYBAQGWAQEBlQEB
        AZUBAQGVAQEBlQEBAXUBAQF0AQEBdAEBAXQBAQFTAQEBMwEBATMBAQHeAnsBbwQAAf8BfwF7AW8B3wF/
        AXkBawFBAS4BYwEyAYQBNgGEATYBhAE2AYQBNgFkATYBQAEqAXcBZwHfAX8B3gF7QAAB3gF7Af8BewGc
        AXcBgwFIASABRAGDAUgBgwFIAYMBSAGDAUgBYwFIASABRAEgAUQBYwFIAYMBSAGDAUgBgwFIAYMBSAEg
        AUQBgwFIAZwBdwH/AXsB3gF7AgABnAFzAf4BfwHXAQkBlgEBAbYBAQG2AQEBtgEBAbYBAQG2AQEBtgEB
        AbYBAQG2AQEBlgEBAZUBAQGVAQEBlQEBAXUBAQF0AQEBdAEBAXQBAQFTAQEBMwEBAd4CewFzAgAB/wF/
        AZwBcwHfAX8BmgFvAYMBMgGDATIBhAE2AYQBNgGEATYBhAE2AYQBNgGEATYBgwEyAaUBOgHfAXsBfAFz
        Af8Bf0AAAd4BewH/AX8B3gF3AYMBTAEgAUgBgwFMAYMBTAGDAUwBgwFMAYMBTAGDAUwBgwFMAYMBTAGD
        AUwBgwFMASABSAGDAUwB3gF3Af8BfwHeAXsEAAGcAXMB/gF/AdcBCQGWAQEBtwEBAbcBAQG3AQEBtwEB
        AbcBAQG2AQEBtgEBAbYBAQG2AQEBtgEBAZUBAQGVAQEBlQEBAXUBAQF0AQEBdAEBAVMBAQFTAQEB3gF7
        AZsBcwIAAZwBcwH/AX8BmgFvAYIBMgGDATYBpAE6AaQBOgGkAToBpAE6AaQBOgGkAToBpAE6AaQBOgGA
        AS4BLwFTAf8BfwGcAXNCAAHeAXsB/wF/Ab0BdwFiAUwBIAFIAYMBUAGDAVABgwFQAYMBUAGDAVABgwFQ
        AYMBUAGDAVABIAFIAWIBTAG9AXcB/wF/Ad4BewYAAZwBcwH+AX8B9wEJAbYBAQHXAQEB1wEBAdcBAQHX
        AQEB1wEBAdcBAQG3AQEBtgEBAbYBAQG2AQEBtgEBAZYBAQGVAQEBlQEBAXUBAQF0AQEBVAEBAVMBAQHe
        AXsBmwFzAd4BewHfAX8BuwFzAaIBNgGiATYBxAE6AcQBOgHEAToBxAE6AaIBNgGiATYBxAE6AcQBOgHE
        AToBpAE6AaIBNgG8AXcB3wF/Af8Bf0IAAd4BewH/AXsB3gF7AeYBVAFBAVABgwFQAYMBUAGDAVABgwFQ
        AYMBUAGDAVABQQFQAeYBVAHeAXsB/wF7Ad4BewgAAZwBcwH/AX8B+AEJAbcBAQHXAQEB1wEBAdcBAQHX
        AQEB1wEBAdcBAQHXAQEB1wEBAbcBAQG2AQEBtgEBAbYBAQGWAQEBlQEBAZUBAQF1AQEBVAEBAVMBAQHe
        AXsBnAFzAZwBcwHfAX8B5gE+AcIBNgHEAT4BxAE+AcQBPgHEAT4BoAEyAVABVwFQAVsBoQE2AcQBPgHE
        AT4BxAE+AcIBNgHoAUYB/wF/AXwBbwH/AX9AAAH/AX8BvQFzAf8BfwHOAWEBIAFQAYMBVAGDAVQBgwFU
        AYMBVAGDAVQBgwFUASABUAHOAWEB/wF/Ab0BcwH/AX8IAAGcAXMB/wF/AfgBCQG3AQEB+AEBAdgBAQHY
        AQEB2AEBAdcBAQG3AQEBtwEBAbcBAQG3AQEBlgEBAZYBAQGWAQEBdQEBAXUBAQGVAQEBlQEBAXQBAQFU
        AQEB3gF/AZwBcwGcAXMB/wF/AQYBQwHBATYB5QE+AeQBPgHkAT4BwAE2ASwBUwH/AX8B/wF/AeQBPgHj
        AToB5AE+AeQBPgHkAT4BwAE2AXEBXwH/AX8BnAFzPgAB/wF/Ab0BcwH/AX8BrQFhAQABUAFjAVQBgwFU
        AYMBVAGDAVQBgwFUAYMBVAGDAVQBYwFUAQABUAGtAWEB/wF/Ab0BcwH/AX8GAAGcAXMB/wF/ARgBCgHY
        AQEB+AEBAdgBAQEZAQoBOQESATkBDgE5AQ4BGAEOARgBDgEYAQ4BGAEOAfgBDQH3AQ0B9wERAdYBCQFV
        AQEBdQEBAXQBAQFUAQEB/gF/AZwBcwH/AX8B/wF/Ad0BdwHkAT4B4gE6AQQBQwHAATYBKwFTAf8BfwG+
        AXcB3wF/AbgBbwHgATYB5AFCAQQBQwEEAUMB5AE+AeEBOgG6AXMB/wF/Ab0BdzoAAf8BfwHeAXcB/wF/
        Ac0BZQEAAVABgwFYAYMBWAGDAVgBgwFYAYMBWAGDAVgBgwFYAYMBWAGDAVgBgwFYAQABUAHNAWUB/wF/
        Ab4BdwH/AX8EAAGcAXMB/wF/ARkBCgHYAQEB2AEBAdsBOgH9AX8B3QF/Ad0BfwHdAX8B3QF/Ad0BfwHd
        AX8B/QF/Af4BfwH/AX8B/wF/Af4BfwGZATYBVAEBAXQBAQF0AQEB/gF/AZwBcwIAAd4BewH/AX8B3QF3
        AeMBQgHAATYBKgFTAf8BfwHeAXsB/wF/Ab0BdwH/AX8BTQFXAeABOgEEAUMBBAFDAQQBQwHiAT4BBQFH
        Af8BfwHfAX8B/wF/NgAB/wF/Ab0BcwH/AX8BzgFpAQABVAGDAVgBgwFcAYMBXAGDAVwBgwFcAWIBWAFi
        AVgBgwFcAYMBXAGDAVwBgwFcAYMBWAEAAVQBzgFpAf8BfwG9AXMB/wF/AgABnAFzAf8BfwEZAQoB2AEB
        AfkBAQG9AXcB3QF/Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Af4BfwG5AUYBlAEdATYBLgH+AX8BnQFz
        AVQBAQF0AQEBdAEBAf8BfwGcAXMEAAHeAXsB/wF/Ad0BewGWAWsB/wF/Ad8BfwH/AX8CAAH/AX8B3gF7
        Af8BfwEDAUMBAgFDAQQBRwEEAUcBBAFHAeABPgEqAVMB/wF/Ab0BdwH/AX8yAAH/AX8BvQF3Af8BfwGs
        AWkBAAFYAYMBXAGDAVwBgwFcAYMBXAGDAVwBIAFcAcYBYAHmAWABIAFcAYMBXAGDAVwBgwFcAYMBXAGD
        AVwBAAFYAawBaQH/AX8BvQF3Af8BfwGcAXMB/wF/ATkBCgH5AQEB+QEBAb0BdwHeAX8BvQF3Ab0BdwG9
        AXcBvQF3Ab0BdwG9AXcB/wF/AfYBGQHRAQABMgEBAf4BfwGdAXMBdQEBAXUBAQF0AQEB/wF/AZwBcwYA
        Ad4BewG+AXsB/wF/Ab0BdwH/AX8GAAHeAXsB/wF/AbgBbwHgAT4BAwFHAQMBRwEDAUcBAwFHAeABPgFM
        AVcB/wF/Ab0BdwH/AX8wAAGcAXMB/wF/AQ8BbgEAAVgBgwFgAYMBYAGDAWABgwFgAYMBYAEgAVwBgwFg
        Af8BfwH/AX8BYgFgASABXAGDAWABgwFgAYMBYAGDAWABgwFgAQABWAEPAW4B/wF/AZwBcwGcAXMB/wF/
        ATkBCgH5AQEB+QEBAb4BdwHeAX8B3gF7Ad4BewHeAXsB3gF7Ad4BewHeAXsB/wF/AVgBIgFUAQEBlQEB
        Af8BfwG9AXMBdQEBAXUBAQF0AQEB/wF/AZwBcxgAAb0BdwH/AX8BbgFfAeABPgEDAUcBAwFHAQMBRwED
        AUcB4AE+AW8BXwH/AX8BnAFzMAABnAFzAf8BfwFBAWABQQFgAYMBYAGDAWABgwFgAYMBYAEgAWABgwFg
        Ab0BewH/AX8B/wF/Ab0BewGDAWABIAFgAYMBYAGDAWABgwFgAYMBYAFBAWABQQFgAf8BfwGcAXMBnAFz
        Af8BfwE6AQoB+QEBARoBAgHeAXcB/gF/Ad4BewHeAXsB3gF7Ad4BewHeAXsB3gF7Af8BfwF6ASIBlwEB
        AdcBAQH/AX8BvgFzAXUBAQF1AQEBdAEBAf8BfwGcAXMaAAG9AXcB/wF/AUgBUwEAAUMBAwFLAQMBSwED
        AUsBAAFDAQABRwH/AX8BnAFzMAABvQF3Af8BfwHvAW0BAAFgAYMBZAGDAWQBgwFkASABYAGDAWQB3gF/
        Af8BfwHeAXsB3gF7Af8BfwHeAX8BgwFkASABYAGDAWQBgwFkAYMBZAEAAWABDwFuAf8BfwG9AXcBnAFz
        Af8BfwE6AQYB+QEBARoBAgHeAXcB/wF/Ad4BewHeAXsB3gF7Ad4BewHeAXsB3gF7Af8BfwGbARoBuAEB
        AbcBAQH/AX8BvgF3AXUBAQF1AQEBdAEBAf8BfwGcAXMaAAH/AX8B/wF/Af8BfwEiAUsBAAFHASIBSwEA
        AUcBAAFHAbYBbwH/AX8B3gF7MAAB/wF/Ad4BewH/AX8BjAFtAQABZAFiAWQBIAFkAWIBZAHeAX8B/wF/
        Ad4BewQAAd4BewH/AX8BvQF/AWEBZAEgAWQBYgFkAQABZAGMAW0B/wF/Ad4BewH/AX8BvQF3Af8BfwF6
        ARoB2QEBARoBAgHfAXsB/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwEdAUMBOgEOAXoBJgH/
        AX8BvgFzAXUBAQE0AQEBlQEBAf8BfwGcAXMcAAHeAXsB/wF/AfwBewEiAU8BAAFDAUgBVwH9AXsB/wF/
        Ab0BdzQAAf8BfwHeAXsB/wF/Ac4BcQEAAWQBhAFoAb0BfwH/AX8B3gF7CAAB3gF7Af8BfwG9AX8BpAFo
        AQABZAHOAXEB/wF/Ad4BewH/AX8CAAH/AX8B/wF/Ad8BdwE6AQYB2QEBAd8BdwH/AX8B/wF/Af8BfwH/
        AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwG+AXMBFAEBAXQBAQF9AWcB/wF/Ad4Bex4A
        Ad4BewH/AX8B/wF/AdsBdwH/AX8B/wF/Ad4BezgAAf8BfwHeAXsB/wF/Ab0BfwH/AX8B/wF/Ad4BewwA
        Ad4BewH/AX8B/wF/Ab0BfwH/AX8B3gF7Af8BfwYAAd4BewH/AX8B/wF/Ab8BbwH/AX8B/wF/Af8BfwH/
        AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Ab4BbwH/AX8B/wF/Ab0BdyIA
        Ad4BewHeAXsB/wF/Ab0Bd0AAAb0BdwG9AXcBvQF3Ad4BexAAAd4BewG9AXcBvQF3Ab0BdwwAAf8BfwG9
        AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9
        AXcBvQF3Ab0BdwHeAXsmAAH/AX8B/wF/Af8BfzgAAUIBTQE+BwABPgMAASgDAAFgAwABGAMAAQEBAAEB
        BQABIAEBFgAD/wEAAeAB/wEHAcABAAEDAf8BjwH/AwABwAF+AQMBgAEAAQEB/wGHAf8DAAGAATwBAQMA
        Af4BAwH/BAABGAQAAfwBAQH/CQAB+AEBAf8JAAHwAQAB/wkAAeABAAH/CQABwAEAAX8DAAGAAQABAQMA
        AYABAAE/AwABwAEAAQMDAAGAAQABPwMAAeABAAEHBQABHwMAAfABAAEPBQABDwMAAfABAAEPBQABDwMA
        AeABAAEHBQABBwMAAcABAAEDAwABgAEAAQMDAAGAAQABAQMAAcABQAEBCQAC4AoAAf8B8AoAAf8B+AoA
        Af8B+AUAARgEAAH/AfwBAQMAAYABPAEBAwAB/wH+AQMDAAHAAX4BAwGAAQABAQL/AQ8DAAHwAf8BDwHA
        AQABAwL/AY8DAAs=
</value>
  </data>
  <metadata name="ImageList16x16.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>19, 17</value>
  </metadata>
  <data name="ImageList16x16.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj00LjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAAAo
        IQAAAk1TRnQBSQFMAgEBCwEAAfwBCwH8AQsBEAEAARABAAT/AREBAAj/AUIBTQE2BwABNgMAASgDAAFA
        AwABMAMAAQEBAAEQBgABGB4AAf8BfwF7AW8BewFvAf8BfxAAAf8BfwH/AX8B3gF7Ad4BewHeAXsB3gF7
        Ad4BewHeAXsB3gF7Ad4BewH/AX8B/wF/TgAB/wF/AbwBdwHZAU4B2QFKAbwBewH/AX8OAAHeAXsBfAFv
        AXwBawF8AWsBfAFrAXwBawF8AWsBfAFrAXwBawF8AWsBfAFrAZwBcwH/AX8MAAHeAXsBnQFzAb0BdwQA
        Ab0BdwGdAXMB3gF7LgAB/wF/AbwBdwH6AVIBsAEAAbABAAHZAUoBvAF3Af8BfwoAAd4BewG+AXMBVAF3
        ATEBdwEyAXcBMgF3ATIBdwEyAXcBMgF3ATIBdwEyAXcBMgF3AZsBdwF7AW8B/wF/CAAB/wF/Ab4BdwF7
        AW8BvgF3AZwBcwGcAXMBvgF3AZsBbwGdAXcB/wF/KgAB/wF/AZwBcwE7AVsB8QEAATIBAQEyAQEB8QEA
        ARoBVwGcAXMB/wF/CAABnAFvATMBdwGDAXoBpQF6AaUBegGlAXoBpQF6AaUBegGlAXoBpQF6AaUBegGE
        AXoByAF6Ab0BdwH/AX8IAAF8AXMB8gFWAcABGQFnATYB3wF7Ad8BewFGATIBwAEZARMBWwGcAXMoAAH/
        AX8BmwFzAVwBZwEzAQEBMwEBAVMBAQFTAQEBMwEBARIBAQFbAWMBnAFzAf8BfwQAAf8BfwG+AXMBMgF7
        AaYBegHJAXoByQF6AckBegHJAXoByQF6AckBegHJAXoByQF6AckBegGmAXoBmQF3Ab0BdwgAAZwBcwHv
        AU4B4AEdAWQBMgHfAXsBvwF7AUQBMgEAAR4BEQFXAZwBcyYAAf8BfwGcAXMBfAFnATMBAQEzAQEBdAEB
        AXQBAQF0AQEBdAEBATMBAQETAQEBXAFjAZsBcwH/AX8CAAGcAXMBWQFvATMBdwHIAXoB6gF6AeoBegHq
        AXoB6gF6AeoBegHqAXoB6gF6AeoBegHqAXoBxwF6AXgBewGdAXMGAAH/AX8BfAFzAd8BfwF2AWcBvAF3
        Ad8BewHfAXsBvAFzAXcBZwHfAX8BnQFzAf8BfyQAAf8BfwH+AX8BeAEuAbYBEQG2AQUBdQEBAZUBAQGV
        AQEBdQEBAbYBBQG2AREBOAEmAf4BfwH/AX8CAAGcAW8BjwFqATQBdwHpAXoB6wF6AesBegHrAXoB6wF6
        AesBegHrAXoB6wF6AesBegHrAXoByAF6AXUBewGcAXMGAAGcAXMBmQFvAYIBMgGkATYBowE2AYIBMgGC
        ATIBgwE2AaMBNgFgAS4BdgFnAZwBcyYAAd4BewG9AXcB/wF/AdoBQgFVAQEBlgEBAZYBAQFUAQEBGwFT
        Af8BfwHdAXsBvQF3Af8BfwIAAZwBcwGPAWoBNAF3AQsBfwEMAXsBDAF7AQwBewEMAXsBDAF7AQwBewEM
        AXsBDAF7AQwBewHpAXoBUwF7AZwBbwYAAZ0BdwEuAVMBoAEuAeYBQgEJAUcB6QFGAekBRgHpAUYB6QFG
        AecBQgG7AXMBnAFzKgABvQF3ARsBTwF1AQEBtgEBAbYBAQF1AQEBXAFfAb0BdwgAAZwBcwGOAWoBNAFz
        ASwBfwEMAXsBDQF7AQ0BewENAXsBDQF7AQ0BewENAXsBDQF7AQ0BewELAXsBUgF7AZwBbwQAAf8BfwH/
        AX8BBgFDAeIBOgG4AW8BTwFbAU8BVwFPAVcBTwFXAU8BVwFNAVcBcwFjAb4BdwH/AX8oAAHeAXsBngFv
        AZYBAQHXAQEB1wEBAZYBAQF9AWMBvQF3CAABnAFzAY4BagEUAXMBcQF/AQsBewEMAXsBDAF7AQwBewEM
        AXsBDAF7AQwBewEMAXsBDAF7AQoBewFzAXsBnAFzBAAB3gF7Ad0BdwHgAToBKAFPAXABXwHAATIB4AE6
        AeABOgHgAToB4AE6AcABNgHiAT4B/wF/Af8BfyoAAf4BfwFZARoBtwEBAfgBAQGXAQEBXQFXAb0BdwgA
        AZwBcwGvAW4BKAFmAd0BewGWAX8BlQF/AZUBfwGVAX8BlQF/AZUBfwGVAX8BlQF/AZQBfwF0AXsB3gF/
        Ab0BdwH/AX8BvQF3AZwBcwG2AWsBwAE2AXABXwH/AX8BkgFjAZMBZwGTAWcBkwFnAZMBZwGSAWMBtgFr
        Af8BfwG9AXcqAAG9AXcBvgFvAbgBAQH4AQEBuAEBARwBQwGcAXMIAAGcAXMBNAFzAQQBZgJqAc8BbgGv
        AW4BrgFuAa4BbgGtAW4BrQFuAa0BbgGtAW4BjAFuAf8BewG9AXcCAAG9AXcB3QF7AZMBawEoAVMB4AE+
        AZEBYwHgAT4B4AE+AeABPgHgAT4B4AE+AeABPgHgAT4BwAE2AUkBUwG9AXcsAAG9AXcBXQFXAdgBAQHY
        AQEBegEaAd4BewH/AX8GAAHeAXsB/wF/AcoBcgGpAXIBqQFyAYgBcgGqAXIB7QFyAc0BcgHNAXIBzQFy
        AcwBcgF2AXsB3gF7BAABvQF3AWsBWwHAATYB4AE+AQABRwH/AX8BbwFjAWsBWwFsAV8BbAFfAWwBXwFs
        AV8BbAFfAUoBWwG4AXMBnAFzLAAB/wF/Ad4BewGeAWMBOgEKAbkBAQE9AUsB3gF7Ad4BewYAAd4BewGX
        AX8BLwF7AVABewEwAXsB3AF/Af8BfwHeAXsB3gF7Ad4BewHeAXsBvQF3Af8BfwQAAb0BdwH+AX8BswFr
        AbMBawHZAXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Af8BfzAAAb0BdwH/
        AX8BXgFTAXsBEgF+AVcBnAFzAf8BfwQAAf8BfwG9AXcB3gF7Ad4BewHeAXsBvQF3FAAB3gF7Ab0BdwG9
        AXcBvQF3SAAB/wF/Ab0BdwG9AXcBvQF3Af8BfwgAAf8BfwH/AX8B/wF/Af8BfwH/AX9eAAHeAXsBfAFv
        AXwBbwHeAXteAAH/AX8BvQF3AXsBbwF7AW8B/wF/DgABvQF3Ad8BfwETAVsBEwFbAd8BfwG9AXcaAAHe
        AXsBnAFzAb0BdxQAAb0BdwGcAXMBvQF3IAABvQF3AZ0BdwGbAW8BrgFKATYBYwGcAXMB/wF/CgABvQF3
        Ad8BfwGLAUIB4AEhAeABIQGLAUIB3wF/Ab0BdxYAAf8BfwGdAXMBNgFjAb0BdwGcAXMQAAG9AXcBvgF7
        ATYBXwG9AXcB/wF/GgAB/wF/AXwBbwG+AXcBrAFGAcABHQGLAUIBvQF3Ab0BdwoAAb0BdwHfAX8BqgFC
        ASEBJgFEATIBRAEyASEBJgGqAUIB3wF/AZwBcwoAAf8BfwH/AX8B/wF/Af8BfwH/AX8B3gF7AZsBbwHA
        AR0BZwE2Ad8BewGcAXMMAAG9AXcB3wF/AYoBPgHAARkBWAFnAb0BdwH/AX8B/wF/Af8BfwH/AX8B/wF/
        Af8Bfw4AAXwBbwGbAW8BJAEyAeABIQFmATYB/wF/Af8BfwoAAZwBcwHfAX8BqgFCAUEBKgFkATYBZAE2
        AWQBNgFkATYBQQEqAaoBQgHfAX8BnAFzBAAB/wF/AXwBbwG+AXcBvgF3Ab4BdwG+AXcBvgF3Ad4BewGc
        AXMBQgEqASEBKgGJAT4B3wF/AZwBcwgAAb0BdwHfAX8BqwFCASEBJgEhASoBeQFrAd4BewG+AXcBvgF3
        Ab4BdwG+AXcBvgF3AXwBcwH/AX8KAAG9AXcBvQF3AUMBNgEhAS4BIAEuAfABUgGcAXMKAAHeAXsB3wF7
        AckBRgFhAS4BhAE2AYQBNgGEATYBhAE2AYQBNgGEATYBYQEuAckBQgHfAXsBvQF3AgABnAFzAVYBYwGm
        AToBqAFCAagBQgGoAUIBqAFCAakBQgGoAT4BhAEyAYQBNgFBAS4ByQFCAd8BfwGdAXMEAAG9AXcB3wF/
        AesBRgFAASoBhAE2AWMBMgGoAT4BqQFCAagBQgGoAUIBqAFCAagBQgGGAToBMwFfAXwBcwoAAb0BdwHt
        AU4BQAEuAWMBOgFAAS4BVQFjAb0BdwoAAZwBcwFSAVsBYAEiAYEBMgGkAToBpAE6AaQBOgGkAToBpAE6
        AaQBOgGBATIBQAEmAVEBWwGdAXMCAAGcAXMBDQFPAWABLgGDATYBgwEyAYMBMgGDATIBgwE2AaMBNgGk
        AToBpAE6AaQBOgGBAS4B6QFGAd8BfwHeAXsB/wF/Ad8BfwEMAUsBgAEuAaQBOgGkAToBpAE6AaMBNgGD
        ATIBgwEyAYMBMgGDATIBgwE2AWABLgHqAUYBnAFzCAAB3gF7Ab0BdwFhATYBYgE6AYMBOgFAATIBeAFr
        Ab0BdwoAAd4BewHdAXcBdQFjAXYBZwHFAT4BxAE6AcQBPgHEAT4BxAE6AcUBPgF2AWcBdAFjAbwBdwG9
        AXcCAAGcAXMBLgFTAaEBNgHFAT4BxAE+AcQBPgHEAT4BxAE+AcQBPgHEAT4BxAE+AcQBPgHEAT4BoAEu
        AVABVwGcAXMBnAJzAV8BoAEuAcQBOgHEAT4BxAE+AcQBPgHEAT4BxAE+AcQBPgHEAT4BxAE+AcUBPgGi
        ATYBCwFPAZwBcwgAAb0BdwF2AWcBYAE2AaMBPgGiAT4BYAE2AZgBbwG9AXcMAAH/AX8BvQF3Af8BfwEF
        AUMB4wE+AeUBPgHlAT4B4wE+AQUBQwH/AX8BvQF3Ad4BewQAAZwBcwFOAVcB4QE6AeUBPgHkAT4B5AE+
        AeQBPgHkAT4B5AE+AeQBPgHkAT4B5AE+AeUBPgHAATYBLAFPAZwBcwGcAXMBTwFXAcABNgHkAT4B5AE+
        AeQBPgHkAT4B5AE+AeQBPgHkAT4B5AE+AeQBPgHlAT4B4gE6ASwBTwGcAXMEAAHeAXsBfAFvAb0BdwFT
        AWMBgAE6AaIBQgGiAUIBgAE6AZcBawG9AXcBfAFvAd4BewH/AX8IAAH/AX8B/wF/AQQBQwHjAT4BBAFD
        AQQBQwHjAT4BBAFDAf8BfwH/AX8GAAGcAXMBTQFXAeABOgEEAUMBBAFDAQQBQwEEAUMBBAFDAQQBQwEE
        AUMBBAFDAQQBQwHiAT4B4gE+Ad0BewG9AXcB3gF7Af8BfwEFAUMB4QE+AQQBQwEEAUMBBAFDAQQBQwEE
        AUMBBAFDAQQBQwEEAUMBBAFDAeEBOgEqAVMBnAFzAgAB/wF/Ad4BewFyAWcBTwFfAQgBUwGgAUIBwgFG
        AcIBRgGgAUIBCQFTAU8BXwFRAWMB3wF7Ad4BewgAAf8BfwH/AX8BBAFHAQMBQwEEAUMBBAFDAQMBQwEE
        AUMB/wF/Af8BfwYAAZwBcwFxAV8B4AE6AQEBQwEBAUMBAQFDAQEBQwEBAUMBAgFDAQMBRwEDAUcB4QFC
        AQEBQwHcAXcBvQF3BAABvQF3Af4BewEDAUcB4QE+AQMBRwEDAUcBAgFDAQEBQwEBAUMBAQFDAQEBQwEB
        AUMB4AE6AU0BWwGcAXMCAAH/AX8BvQF3AXMBZwGAAToBwAFCAeIBSgHiAUYB4gFGAeIBSgHAAUIBgAE6
        AXIBZwG9AXcB/wF/CAAB/wF/Af8BfwEDAUcBAgFDAQMBRwEDAUcBAgFDAQMBRwH/AX8B/wF/BgAB/wF/
        Af8BfwHdAXsB/gF7Af4BewH+AXsB/gF7Af8BfwHaAXMBAQFHAQEBQwEBAUcB3AF7Ab0BdwgAAb0BdwH+
        AX8BAwFHAQABQwEAAUMBtwFvAf8BfwH+AXsB/gF7Af4BewH+AXsB/QF7Af8BfwHeAXsGAAHeAXsBlAFr
        AcABQgHhAUoB4QFKAeEBSgHhAUoBwAFCAZIBZwHeAXsB/wF/CgAB/wF/Af8BfwEDAUsBAgFHAQMBSwED
        AUsBAgFHAQMBSwH/AX8B/wF/CAAB/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8BvQF3AdwBewHgAT4BAAFH
        AdsBdwG9AXcMAAG9AXcB/gF/AQEBSwHgAToB2QFzAb0BdwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwgA
        Af8BfwHeAXsBkQFnAeABRgEAAU8BAAFPAeABRgFwAWcB/wF/Af8BfwwAAf8BfwH/AX8BAAFHAQABRwEA
        AUcBAAFHAQABRwEAAUcB/wF/Af8BfxQAAf8BfwH/AX8BawFfAdoBdwG9AXcQAAG9AXcB/QF7AWsBXwH/
        AX8B/wF/FgAB/wF/Af8BfwGOAWcBAAFLAQABSwFsAWMB/wF/Af8Bfw4AAf8BfwH/AX8BbAFfAUUBUwFH
        AVcBRwFXAUUBUwFsAV8B/wF/GAAB3gF7Ab0BdwG9AXcUAAG9AXcBvQF3Ab0BdxoAAf8BfwH/AX8BjAFn
        AYsBYwH/AX8B/wF/EgAB3gF7Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ad4Be1QAAf8BfwG9AXcBvQF3
        Af8BfxYAATkBZwEZAWMBOgFnAToBZwE5AWcBGAFjCgABWgFrARgBYwE5AWcBGAFjARgBYxoAARgBYwFa
        AWcBGAFjATkBZwgAATkBZwEYAWMBWgFnARgBYwwAAf8BfwF8AW8BfAFvAXwBbwF8AW8BfAFvAXwBbwH/
        AX8QAAFaAWsBWwFvATYBYwHQAU4B0AFOATUBXwGcAXMBOQFnCAABGAFjAXsBbwG1AVYBGAFjAd4BewEY
        AWMBGAFjFAAB9wFeAf8BewEQAVYB1gFmAZwBbwE5AWcEAAE5AWcBnAFvAdYBZgEQAVYB/wF7AfcBXggA
        Af8BfwGcAXMBNQFfAdABTgHxAVIB8QFSAdABTgE1AV8BnQFzEAABOQFnAb4BdwEjASoB4AEhAQABIgEB
        ASYBnAFzARgBYwgAARgBYwGcAXMB7gE9AgABCAEhAXoBcwHeAXcBOgFjARgBYw4AARgBXwH/AXsBawFN
        AQABMAEAATQB1gFmAZwBbwE5AWcBOQFnAZwBbwHWAWYBAAE0AQABMAFrAU0B/wF7ARgBXwYAAf8BfwG+
        AXsBIgEqAeABIQEBASYBAQEmAeABIQEjASoBvgF7Af8Bfw4AATkBZwG+AXcBRAEyAUQBMgFEATIBQwEu
        AbwBcwE5AWcIAAFaAWsBOQFnARgBYwHnARwBkAFeAVQBfwE1AXsBlwF3AX0BawE5AWcMAAF7AWsBzgFZ
        AQABNAFjAUABQQFAAQABPAH3AWoBewFvAXsBbwH3AWoBAAE8AUEBQAFjAUABAAE0Ac4BWQF7AWsGAAH/
        AX8B3wF7AWUBMgFEATIBZQEyAWUBMgFEATIBZQEyAd8BewH/AX8IAAFaAWsBGAFjARgBYwH3AV4B3gF7
        AWQBNgFjATIBZAEyAWMBMgG8AXcB9wFeARgBYwEYAWMBWgFrBAABOQFnAb0BcwF3AXsBugF/AVgBfwEG
        AX8B4AF+AZgBdwE6AWcMAAF7AWsBMQFiAQABPAFiAUQBYwFEAUEBRAEAAUABOQFvATkBbwEAAUABQQFE
        AWMBRAFiAUQBAAE8ATEBYgF7AWsGAAH/AX8B3wF7AYUBNgFkATIBZAE2AWQBNgFkATIBhQE2Ad8BewH/
        AX8GAAE5AWcBWwFrAd8BewHfAXsB3wF/Ad8BfwGlAToBgwE2AYQBNgGDATYBvgF7Ad8BfwHfAXsB3wF7
        AXwBbwEYAWMCAAFaAWsB3gF3AbwBfwGZAX8BSAF/AQABfwEAAX8BAAF/Ab0BdwEYAWMKAAE5AWcB/wF7
        ARABYgEAAUABYgFIAYMBSAFCAUgBYgFIAWIBSAFCAUgBgwFIAWIBSAEAAUABEAFiAf8BewE5AWcGAAH/
        AX8B3wF7AYUBNgGDATYBhAE2AYQBNgGDATYBhQE6Ad8BewH/AX8GAAE5AWcBdgFnAaUBOgHGAT4BxwE+
        AccBQgGkAToBpAE6AaQBOgGkATYBxwE+AccBPgHGAT4BpAE6AVQBXwFaAWsEAAGcAW8BugF/AY8BfwFq
        AX8BJAF/AQABfwHgAX4BIwF7Ad8BdwH4AV4KAAE5AWcB/wF/ATABYgEAAUQBYgFMAYMBTAFiAUwBYgFM
        AYMBTAFiAUwBAAFEATABYgH/AX8BOQFnCAAB/wF/Ad8BfwGlAToBowE2AaQBOgGkAToBowE2AcUBOgHf
        AX8B/wF/BgABewFvAS0BUwGgATIBwwE6AcMBOgHDAToBxAE+AcQBPgHEAT4BxAE+AcMBOgHDAToBwwE6
        AaABMgEKAUsBewFvBAABWgFnAdwBewGPAX8BjwF/AWsBfwEjAXsBAAF/AeABfgFKAXsB3wF3ARgBYwoA
        ATkBZwG9AXcBUgFmAUEBTAFjAVABgwFQAYMBUAFjAVABQQFMAVIBZgHeAXsBOQFnCAAB/wF/Ad4BewH/
        AX8BxQE+AcMBOgHEAT4BxAE+AcMBOgHFAT4B/wF/Ad4BewH/AX8EAAF7AW8BTQFTAcEBNgHkAT4B5AE+
        AeQBPgHkAT4B5AE+AeQBPgHkAT4B5AE+AeQBPgHkAT4BwgE6ASsBTwF7AW8GAAF8AW8B2gF7Aa8BfwGP
        AX8BSgF/ASMBewEAAX8B4AF+AY8BfwG9AXMBOQFnCAABOQFnAXsBbwFaAXcBQQFQAWMBVAGDAVQBgwFU
        AWMBVAFBAVABWgF3AXsBbwE5AWcGAAHeAXsB3wF7Ad4BewHfAXsBBQFDAeQBPgHlAT4B5AE+AeQBPgEF
        AUMB3wF7Ad4BewHeAXsB3gF7AgABewFvAXEBXwHAATYB4QE6AeEBOgHiAT4B4wFCAQQBQwEEAUMB4wFC
        AeIBPgHhAToB4QE6AcABNgFOAVcBewFvBgABWgFrAd4BdwHYAX8BjwF/AY8BfwFJAX8BIAF/AQABfwHt
        AWIBfQFvAXsBbwYAATkBZwGcAXMBOAFzASABVAFiAVQBgwFYAYMBWAGDAVgBgwFYAWIBVAEgAVQBOAFz
        AZwBcwE5AWcEAAGcAXMBcgFjAcABNgHjAT4B4wE+AQQBQwEEAUMBBAFDAQQBQwHjAT4B4wE+AcABNgGT
        AWMBnQF3AgABOQFnAf8BfwHbAXcB2wF3AdwBdwHbAXcBBAFDAQMBQwEDAUMBAgFDAboBcwHcAXcB2wF3
        AbsBdwH/AX8BOQFnCAABOgFnAf8BewG1AX8BjwF/AY0BfwFpAX8BVgFnAY4BUQFgAUwBvQF7ATkBYwE5
        AWcBOQFnAZwBcwE4AXcBIAFYAUEBWAGDAVwBYgFcASABWAEgAVgBYgFcAYMBXAFBAVgBIAFYATgBdwGc
        AXMBOQFnAgABvQF3AdsBdwHhAT4B4QE+AQQBQwEEAUMBBAFDAQQBQwEEAUMBBAFDAeEBPgHhAT4B2wF3
        Ab0BdwQAAVoBawFaAWsBWgFrATkBZwH/AX8BAwFHAQIBRwEDAUcBAgFDAf8BfwE5AWcBWgFrAVoBawFa
        AWsMAAE6AWcB/wF/AbIBfwG0AXsBWwFnAaoBXQGgAVgBQAFMAWoBWQGcAXMB9wFeATkBZwEYAXcBAAFc
        AUEBXAGDAWABYgFgAQABXAFzAXIBcwFyAQABXAFiAWABgwFgAUEBXAEAAVwBGAF3ATkBZwQAAb0BdwHd
        AXsBAgFDAQEBQwEDAUcBAwFHAQMBRwEDAUcBAQFDAQIBRwHdAXsBvQF3DAABOQFnAf8BfwEDAUsBAgFH
        AQIBRwEBAUcB/gF7ARgBYxQAAVoBawH/AX8BFgFrAYQBZQEgAWUBIgFdAUABTAEPAWYBnAFzARgBYwGc
        AXMBiwFpAQABXAGDAWQBYwFgAQABYAExAXIB3gF7Ad4BewExAXIBAAFgAYMBYAGDAWQBAAFcAYsBbQF7
        AW8GAAG9AXcB/QF7AQIBRwEAAUcBAwFLAQMBSwEAAUcBAgFHAf0BewG9AXcOAAE5AWcB/wF/AQABRwEA
        AUcBAAFHAQABQwH+AXsBOQFnFgABnAFzATUBewEgAW0BIAFlAcABWAFPAWoB/wF/AVoBawIAATkBZwHe
        AX8BxQFoAQABZAEAAWQBMAFyAf8BfwE5AWcBOQFnAf8BfwEwAXIBAAFkAQABZAHFAWgB3gF/ATkBZwgA
        Ab0BdwH9AXsBAQFLAQABQwEAAUMBIQFLAf4BewG9AXcQAAFaAWsB/wF/AY4BYwFFAVMBRQFTAWsBXwH/
        AX8BOQFnFgABWgFrAd4BewHzAXYBhQFpARYBdwHeAXsBWgFrBgABWgFrAf8BfwEIAW0BMAF2Af8BfwE5
        AWcEAAE5AWcB/wF/ATEBdgEIAW0B/wF/AVoBawwAAb0BdwH+AX8BSAFbAUgBWwH+AX8BvQF3FAABOQFn
        AZwBcwG9AXcBvQF3Ab0BdwE5AWcaAAE5AWcBnAFzAb0BdwF7AW8BWgFrCgABWgFrAb0BdwGcAXMBOQFn
        CAABOQFnAZwBcwG9AXcBWgFrEAABvQF3Ab0BdwG9AXcB3gF7DAABQgFNAT4HAAE+AwABKAMAAUADAAEw
        AwABAQEAAQEFAAGAAQEWAAP/AQAB/AE/AcABAwL/AgAB+AEfAcABAQH4AccCAAHwAQ8BgAEAAfABAwIA
        AeABBwGAAQAB8AEDAgABwAEDAgAB8AEDAgABgAEBAgAB4AEBAgABgAEBAgAB4AEBAgABwAEBAgAB4AEB
        AgAB8AEPAgABwAMAAfABDwIAAcADAAH4AQ8GAAH4AQ8BAAEBBAAB/AEHAQABAwQAAfwBAwGAAQMEAAH/
        AQEBgQH/AYcB/wIAAf8BgwHBA/8CAAH8AT8F/wGDAfgBHwH/AR8B+AL/AQEB8AEPAf4BDwHwAX8B/AED
        AeABBwHAAQcB4AEBAfwBBwHAAQMBAAEDAcABAAH4AQ8BgAEBAQABAQGAAQAB+AEPAYABAQQAAfABDwGA
        AQEEAAHwAQ8BwAEDBAABwAEBAeABBwQAAYABAQHgAQcBAAEBAYABAAGAAQEB4AEHAQABAwHAAQAB4AED
        AeABBwGAAQcB4AEBAeABBwHgAQcB/gEPAfABfwHwAQ8B4AEPAf8BHwH4Af8B+AEfAfABDwT/AfwBPwH4
        AR8BBwH/AsMB8AEPAfABDwEBAf8CgQHgAQ8B8AEPAQABfwIAAeABBwHwAQ8BAAE/AgAB4AEHAYABAQGA
        AT8CAAHgAQcCAAGAAR8CAAHgAQcCAAHAAQ8BgAEBAeABBwIAAcABBwHAAQMBwAEDAgAB4AEDAcABAwGA
        AQECAAHgAQMBgAEBAYABAQIAAfADAAGAAQEBgAEBAfgDAAHAAQMB8AEPAfwDAAHgAQcB8AEPAf4BAQIA
        AfABDwHwAQ8B/gEDAoEB+AEfAfgBHwH/AQcCwwH8AT8L
</value>
  </data>
  <data name="LabelLoadingFeesInfo.Text" xml:space="preserve">
    <value>Use the 'Add' button to add loading fees to the basic rental of this burst. The default percentage for each fee can be changed directly in the grid below.
TO MODIFY A LOADING FEE: Simply double-click the appropriate row and type a new value.</value>
  </data>
  <metadata name="LoadingFeeNameColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="LoadingFeePercentageColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="LoadingFeeAmountColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="CrossoverCategoryNameColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="CrossoverPriorityColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="AdditionalCategoryNameColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="RelativeContractNumberColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="RelativeBrandNameColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="RelativeFirstWeekColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="RelativeLastWeekColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="RelativeInstallStoreQty.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="LabelControl1.Text" xml:space="preserve">
    <value>The store pool capacity tells the system how many stores to reserve in the media gap for all the bursts which share the pool. Normally this can be left untouched, but if you've added some bursts into the list below, you might need to change it.

For example, if you've specified 100 stores for this burst and you have one burst in the list for another 100 stores, then the store pool capacity will tell the system whether you want the same 100 stores for both bursts or not. If you make the store pool capacity 100, then the same 100 stores will be reserved for both bursts (i.e. they'll share the exact same store list). However, if you make the store pool capacity 140, then each burst can have 40 unique stores, and 60 stores which will be common to both bursts.</value>
  </data>
  <metadata name="InstallationDayNameColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
</root>