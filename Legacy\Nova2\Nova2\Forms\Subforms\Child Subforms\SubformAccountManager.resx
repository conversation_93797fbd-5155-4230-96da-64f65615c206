<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="ImageList16x16.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="ImageList16x16.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj00LjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAAAE
        CgAAAk1TRnQBSQFMAgEBAwEAAbwBAwG8AQMBEAEAARABAAT/AREBAAj/AUIBTQE2BwABNgMAASgDAAFA
        AwABEAMAAQEBAAEQBgABCBwAATkBZwEZAWMBOgFnAToBZwE5AWcBGAFjCgABWgFrARgBYwE5AWcBGAFj
        ARgBYxoAARgBYwFaAWcBGAFjATkBZwgAATkBZwEYAWMBWgFnARgBYywAAVoBawFbAW8BNgFjAdABTgHQ
        AU4BNQFfAZwBcwE5AWcIAAEYAWMBewFvAbUBVgEYAWMB3gF7ARgBYwEYAWMUAAH3AV4B/wF7ARABVgHW
        AWYBnAFvATkBZwQAATkBZwGcAW8B1gFmARABVgH/AXsB9wFeKgABOQFnAb4BdwEjASoB4AEhAQABIgEB
        ASYBnAFzARgBYwgAARgBYwGcAXMB7gE9AgABCAEhAXoBcwHeAXcBOgFjARgBYw4AARgBXwH/AXsBawFN
        AQABMAEAATQB1gFmAZwBbwE5AWcBOQFnAZwBbwHWAWYBAAE0AQABMAFrAU0B/wF7ARgBXygAATkBZwG+
        AXcBRAEyAUQBMgFEATIBQwEuAbwBcwE5AWcIAAFaAWsBOQFnARgBYwHnARwBkAFeAVQBfwE1AXsBlwF3
        AX0BawE5AWcMAAF7AWsBzgFZAQABNAFjAUABQQFAAQABPAH3AWoBewFvAXsBbwH3AWoBAAE8AUEBQAFj
        AUABAAE0Ac4BWQF7AWsiAAFaAWsBGAFjARgBYwH3AV4B3gF7AWQBNgFjATIBZAEyAWMBMgG8AXcB9wFe
        ARgBYwEYAWMBWgFrBAABOQFnAb0BcwF3AXsBugF/AVgBfwEGAX8B4AF+AZgBdwE6AWcMAAF7AWsBMQFi
        AQABPAFiAUQBYwFEAUEBRAEAAUABOQFvATkBbwEAAUABQQFEAWMBRAFiAUQBAAE8ATEBYgF7AWsgAAE5
        AWcBWwFrAd8BewHfAXsB3wF/Ad8BfwGlAToBgwE2AYQBNgGDATYBvgF7Ad8BfwHfAXsB3wF7AXwBbwEY
        AWMCAAFaAWsB3gF3AbwBfwGZAX8BSAF/AQABfwEAAX8BAAF/Ab0BdwEYAWMKAAE5AWcB/wF7ARABYgEA
        AUABYgFIAYMBSAFCAUgBYgFIAWIBSAFCAUgBgwFIAWIBSAEAAUABEAFiAf8BewE5AWcgAAE5AWcBdgFn
        AaUBOgHGAT4BxwE+AccBQgGkAToBpAE6AaQBOgGkATYBxwE+AccBPgHGAT4BpAE6AVQBXwFaAWsEAAGc
        AW8BugF/AY8BfwFqAX8BJAF/AQABfwHgAX4BIwF7Ad8BdwH4AV4KAAE5AWcB/wF/ATABYgEAAUQBYgFM
        AYMBTAFiAUwBYgFMAYMBTAFiAUwBAAFEATABYgH/AX8BOQFnIgABewFvAS0BUwGgATIBwwE6AcMBOgHD
        AToBxAE+AcQBPgHEAT4BxAE+AcMBOgHDAToBwwE6AaABMgEKAUsBewFvBAABWgFnAdwBewGPAX8BjwF/
        AWsBfwEjAXsBAAF/AeABfgFKAXsB3wF3ARgBYwoAATkBZwG9AXcBUgFmAUEBTAFjAVABgwFQAYMBUAFj
        AVABQQFMAVIBZgHeAXsBOQFnJAABewFvAU0BUwHBATYB5AE+AeQBPgHkAT4B5AE+AeQBPgHkAT4B5AE+
        AeQBPgHkAT4B5AE+AcIBOgErAU8BewFvBgABfAFvAdoBewGvAX8BjwF/AUoBfwEjAXsBAAF/AeABfgGP
        AX8BvQFzATkBZwgAATkBZwF7AW8BWgF3AUEBUAFjAVQBgwFUAYMBVAFjAVQBQQFQAVoBdwF7AW8BOQFn
        JAABewFvAXEBXwHAATYB4QE6AeEBOgHiAT4B4wFCAQQBQwEEAUMB4wFCAeIBPgHhAToB4QE6AcABNgFO
        AVcBewFvBgABWgFrAd4BdwHYAX8BjwF/AY8BfwFJAX8BIAF/AQABfwHtAWIBfQFvAXsBbwYAATkBZwGc
        AXMBOAFzASABVAFiAVQBgwFYAYMBWAGDAVgBgwFYAWIBVAEgAVQBOAFzAZwBcwE5AWciAAE5AWcB/wF/
        AdsBdwHbAXcB3AF3AdsBdwEEAUMBAwFDAQMBQwECAUMBugFzAdwBdwHbAXcBuwF3Af8BfwE5AWcIAAE6
        AWcB/wF7AbUBfwGPAX8BjQF/AWkBfwFWAWcBjgFRAWABTAG9AXsBOQFjATkBZwE5AWcBnAFzATgBdwEg
        AVgBQQFYAYMBXAFiAVwBIAFYASABWAFiAVwBgwFcAUEBWAEgAVgBOAF3AZwBcwE5AWciAAFaAWsBWgFr
        AVoBawE5AWcB/wF/AQMBRwECAUcBAwFHAQIBQwH/AX8BOQFnAVoBawFaAWsBWgFrDAABOgFnAf8BfwGy
        AX8BtAF7AVsBZwGqAV0BoAFYAUABTAFqAVkBnAFzAfcBXgE5AWcBGAF3AQABXAFBAVwBgwFgAWIBYAEA
        AVwBcwFyAXMBcgEAAVwBYgFgAYMBYAFBAVwBAAFcARgBdwE5AWcoAAE5AWcB/wF/AQMBSwECAUcBAgFH
        AQEBRwH+AXsBGAFjFAABWgFrAf8BfwEWAWsBhAFlASABZQEiAV0BQAFMAQ8BZgGcAXMBGAFjAZwBcwGL
        AWkBAAFcAYMBZAFjAWABAAFgATEBcgHeAXsB3gF7ATEBcgEAAWABgwFgAYMBZAEAAVwBiwFtAXsBbygA
        ATkBZwH/AX8BAAFHAQABRwEAAUcBAAFDAf4BewE5AWcWAAGcAXMBNQF7ASABbQEgAWUBwAFYAU8BagH/
        AX8BWgFrAgABOQFnAd4BfwHFAWgBAAFkAQABZAEwAXIB/wF/ATkBZwE5AWcB/wF/ATABcgEAAWQBAAFk
        AcUBaAHeAX8BOQFnKAABWgFrAf8BfwGOAWMBRQFTAUUBUwFrAV8B/wF/ATkBZxYAAVoBawHeAXsB8wF2
        AYUBaQEWAXcB3gF7AVoBawYAAVoBawH/AX8BCAFtATABdgH/AX8BOQFnBAABOQFnAf8BfwExAXYBCAFt
        Af8BfwFaAWssAAE5AWcBnAFzAb0BdwG9AXcBvQF3ATkBZxoAATkBZwGcAXMBvQF3AXsBbwFaAWsKAAFa
        AWsBvQF3AZwBcwE5AWcIAAE5AWcBnAFzAb0BdwFaAWskAAFCAU0BPgcAAT4DAAEoAwABQAMAARADAAEB
        AQABAQUAAYAXAAP/AQAB+AEfAQcB/wLDAgAB8AEPAQEB/wKBAgAB8AEPAQABfwQAAfABDwEAAT8EAAGA
        AQEBgAE/BgABgAEfBgABwAEPAYABAQQAAcABBwHAAQMEAAHgAQMBwAEDBAAB4AEDAYABAQQAAfAFAAGA
        AQEB+AUAAfABDwH8BQAB8AEPAf4BAQQAAfABDwH+AQMCgQIAAfgBHwH/AQcCwwIACw==
</value>
  </data>
  <metadata name="ImageList24x24.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>153, 17</value>
  </metadata>
  <data name="ImageList24x24.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj00LjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAAD4
        HQAAAk1TRnQBSQFMAgEBBAEAAVgBAAFYAQABGAEAARgBAAT/AREBAAj/AUIBTQE2BwABNgMAASgDAAFg
        AwABMAMAAQEBAAEQBgABJP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/ACoAAf8BfwGd
        AXMBewFvAZwBcwH/AX8QAAH/AX8BnAFzAXsBbwGdAXMB/wF/CgAB/wF/AZwBcwF7AW8BewFvAXsBbwF7
        AW8BewFvAXsBbwF7AW8BewFvAXsBbwF7AW8BewFvAXsBbwF7AW8BewFvAXsBbwF7AW8BnAFzAf8BfxYA
        Af8BfwH/AX8B/wF/HAAB/wF/AZwBcwF7AW8BfAFvAXwBbwF8AW8BfAFvAXwBbwF8AW8BfAFvAXwBbwF7
        AW8BnAFzAd4BexQAAf8BfwF7AW8B/wF7Af8BewH/AXsBnAFzAf8BfwwAAf8BfwGcAXMB/wF7Af8BewH/
        AnsBbwH/AX8GAAH/AX8BewFvAf4BfwH+AX8B/gF/Af4BfwH+AX8B/gF/Af4BfwH+AX8B/gF/Af4BfwH+
        AX8B/gF/Af4BfwH+AX8B/gF/Af4BfwH+AX8B/gF/AXsBbwHeAXsUAAG9AXcBewFvAZwBbwHeAXsYAAH/
        AX8BewFvAd8BcwHfAXMB3wFzAd8BcwHfAXMB3wFzAd8BcwHfAXMB3wFzAd8BcwHfAXMB3wFzAZ0BcwG9
        AXcQAAH/AX8BewFvAf8BewEYAWsBxgFAAe4BVQH/AXsBnAFzAf8BfwgAAf8BfwGcAW8B/wF7Ae4BVQHG
        AUABGAFrAf8CewFvAf8BfwIAAf8BfwF7AW8B/gF/AbkBQgHVARkB9gEZAfYBGQH2ARkB9gEZAfYBGQH2
        ARkB9gEZAfYBGQHVARkB1QEZAdUBGQHVARkB1QEZAdUBGQGUARUBVwE2Ad4BfwGcAXcB/wF/DgAB/wF/
        AXsBbwG/AXsB3wF/Ad8BfwGcAXMB/wF/FAAB/wF/AXwBbwG/AXMB8gFyAWsBcgFrAXIBawFyAWwBcgFs
        AXIBbAFyAWwBcgFrAXIBawFyAWsBcgGvAXIBvQFzAb0BcwH/AX8MAAH/AX8BewFvAf8BewHWAWYBAAE0
        AQABNAEAATQBagFNAf8BewGdAXMB/wF/BAAB/wF/AZ0BcwH/AXsBagFNAQABNAEAATQBAAE0AdYBZgH/
        AnsBbwH/AX8BvQF3Af4BfwGYAToB0gEAAVMBAQEzAQEBMwEBATMBAQEzAQEBMwEBATMBAQEzAQEBMwEB
        ARMBAQESAQEBEgEBARIBAQHyAQAB8QEAARIBAQGPAQAB9QEhAf4BfwGcAXMMAAH/AX8BewFvAd8BfwF4
        AWcBaAE6Aa4BSgHfAnsBbwH/AX8SAAG9AXcB3wFzAfEBdgGiAXUBwQF1AcEBdQHBAXUBwQF1AcEBdQHB
        AXUBwQF1AcEBdQHBAXUBwQF1AaIBdQFpAXYB3wF3AZwBcwwAAb0BdwH/AXsB9wFqAQABOAFBATwBYwFA
        AWMBQAEAATgBjAFRAf8BfwGcAXMB/wF/Af8BfwGcAXMB/wF/AYwBUQEAATgBYwFAAWMBQAFBATwBAAE4
        AfcBagH/AXsBvQF3AZwBcwH+AX8BdAEBAXQBAQH/AX8BmAE+ATQBAQF0AQEBdAEBAXQBAQF0AQEBdAEB
        AXQBAQF0AQEBdAEBAVMBAQFTAQEBEgEBARYBJgH/AX8BlAERAdABAAG9AXcBewFvCgAB/wF/AXsBbwHf
        AX8BVgFjAQEBJgEBASYB4AEdAfABTgHfAX8BnAFzEgABnAFzAd8BdwEmAXYBwAF1AeIBdQHiAXUB4gF1
        AeEBdQHhAXUB4gF1AeEBdQHiAXUB4gF1AeIBdQHhAXUB4QF1Ab0BdwF8AW8MAAF8AW8B/wF7AWMBQAEg
        ATwBgwFAAWMBQAFjAUABYgFAAQABOAGMAVUB/wF7AXwBbwF8AW8B/wF/AawBVQEAATgBYgFAAWMBQAFj
        AUABgwFAASABPAFjAUAB/wF7AZwBbwGcAXMB/gF/AZUBBQFUAQEBugE+AfYBEQF0AQEBlQEBAZUBAQGV
        AQEBlQEBAXUBAQF0AQEBdAEBAXQBAQF0AQEBVAEBAVMBAQGUAQkBuQFCAVMBAQESAQEBvQF3AXsBbwgA
        Af8BfwF7AW8B3wF/AXkBawEhASoBQwEuAUQBMgFEAS4BIgEqAZwBcwG+AXcB/wF/EAABnAFzAd8BdwEk
        AXYB4AF1AQIBdgECAXYBAgF2AeABdQE1AXcBeAF3AQMBdgEBAXYBAgF2AQIBdgECAXYB4AF1AZwBdwGc
        AW8MAAGcAXMB/wF7AeYBTAEAAUABgwFEAWMBRAFjAUQBYwFEAWIBRAEAATwBawFVAf8BfwH/AX8BawFV
        AQABPAFiAUQBYwFEAWMBRAFjAUQBgwFEAQABQAHmAUwB/wF7AZwBcwGcAXMB/gF/AbYBCQF1AQEBVAEB
        AXUBAQGVAQEBlQEBAZUBAQGVAQEBlQEBAZUBAQGVAQEBdQEBAXQBAQF0AQEBdAEBAXQBAQFTAQEBEgEB
        ARIBAQEyAQEBvQF3AXsBbwYAAf8BfwF7AW8B3wF/AXgBZwFCAS4BQwEuAWQBMgFkATIBZAEyAUEBKgHL
        AUYB/wF/AXsBbxAAAZwBcwHfAXcBJAF2AQABdgECAXYBAgF2AQIBdgHgAXUBeQF3Ad8BdwEBAXYBAQF2
        AQIBdgECAXYBAQF2AeABdQGcAXcBnAFvDAAB/wF/Af8BewG9AXcBYgFEASABRAGDAUgBYwFIAWMBSAFj
        AUgBYwFIAQABQAHvAV0B7wFdAQABQAFiAUgBYwFIAWMBSAFjAUgBgwFIASABRAFiAUQBvQF3Af8BewH/
        AX8BnAFzAf4BfwHWAQkBdQEBAbYBAQG2AQEBtgEBAbYBAQGWAQEBlgEBAZYBAQGVAQEBlQEBAZUBAQGV
        AQEBdQEBAXQBAQF0AQEBdAEBAVMBAQEzAQEBMwEBAd4CewFvBAAB/wF/AXsBbwHfAX8BeQFrAUEBLgFj
        ATIBhAE2AYQBNgGEATYBhAE2AWQBNgFAASoBdwFnAd8BfwHeAXsOAAGcAXMB/wF3AUQBdgEAAXYBIgF2
        ASIBdgEiAXYB4AF1ATUBdwG9AXcB4AF1ASEBdgEiAXYBIgF2ASEBdgEAAXYBvAF3AZwBbw4AAd4BewH/
        AXsBnAF3AYMBSAEgAUQBgwFIAYMBSAGDAUgBgwFIAWMBSAEgAUQBIAFEAWMBSAGDAUgBgwFIAYMBSAGD
        AUgBIAFEAYMBSAGcAXcB/wF7Ad4BewIAAZwBcwH+AX8B1wEJAZYBAQG2AQEBtgEBAbYBAQG2AQEBtgEB
        AbYBAQG2AQEBtgEBAZYBAQGVAQEBlQEBAZUBAQF1AQEBdAEBAXQBAQF0AQEBUwEBATMBAQHeAnsBcwIA
        Af8BfwGcAXMB3wF/AZoBbwGDATIBgwEyAYQBNgGEATYBhAE2AYQBNgGEATYBhAE2AYMBMgGlAToB3wF7
        AXwBcwH/AX8MAAGcAXMB/wF3AUQBdgEgAXYBIgF2ASIBdgEhAXYBAAF2AZoBdwHeAXcBQwF2ASABdgEi
        AXYBIgF2ASEBdgEAAXYBvAF3AZwBcxAAAd4BewH/AX8B3gF3AYMBTAEgAUgBgwFMAYMBTAGDAUwBgwFM
        AYMBTAGDAUwBgwFMAYMBTAGDAUwBgwFMASABSAGDAUwB3gF3Af8BfwHeAXsEAAGcAXMB/gF/AdcBCQGW
        AQEBtwEBAbcBAQG3AQEBtwEBAbcBAQG2AQEBtgEBAbYBAQG2AQEBtgEBAZUBAQGVAQEBlQEBAXUBAQF0
        AQEBdAEBAVMBAQFTAQEB3gF7AZsBcwIAAZwBcwH/AX8BmgFvAYIBMgGDATYBpAE6AaQBOgGkAToBpAE6
        AaQBOgGkAToBpAE6AaQBOgGAAS4BLwFTAf8BfwGcAXMMAAGcAXMB/wF3AWMBdgEgAXYBQQF2AUEBdgFB
        AXYBIAF2AXgBdwG7AXcBQwF2ASABdgFBAXYBQQF2AUEBdgEgAXYBvAF3AZ0BcwQAAf8BfwwAAd4BewH/
        AX8BvQF3AWIBTAEgAUgBgwFQAYMBUAGDAVABgwFQAYMBUAGDAVABgwFQAYMBUAEgAUgBYgFMAb0BdwH/
        AX8B3gF7BgABnAFzAf4BfwH3AQkBtgEBAdcBAQHXAQEB1wEBAdcBAQHXAQEB1wEBAbcBAQG2AQEBtgEB
        AbYBAQG2AQEBlgEBAZUBAQGVAQEBdQEBAXQBAQFUAQEBUwEBAd4BewGbAXMB3gF7Ad8BfwG7AXMBogE2
        AaIBNgHEAToBxAE6AcQBOgHEAToBogE2AaIBNgHEAToBxAE6AcQBOgGkAToBogE2AbwBdwHfAX8B/wF/
        CgABnAFzAf8BewFjAXYBQAF2AUEBdgFBAXYBQQF2AUABdgGoAXYBywF2AUEBdgFBAXYBQQF2AUEBdgFB
        AXYBIAF2Ab0BewGdAXMCAAHeAXsBvQF3Ab0BdwH/AX8KAAHeAXsB/wF7Ad4BewHmAVQBQQFQAYMBUAGD
        AVABgwFQAYMBUAGDAVABgwFQAUEBUAHmAVQB3gF7Af8BewHeAXsIAAGcAXMB/wF/AfgBCQG3AQEB1wEB
        AdcBAQHXAQEB1wEBAdcBAQHXAQEB1wEBAdcBAQG3AQEBtgEBAbYBAQG2AQEBlgEBAZUBAQGVAQEBdQEB
        AVQBAQFTAQEB3gF7AZwBcwGcAXMB3wF/AeYBPgHCATYBxAE+AcQBPgHEAT4BxAE+AaABMgFQAVcBUAFb
        AaEBNgHEAT4BxAE+AcQBPgHCATYB6AFGAf8BfwF8AW8B/wF/CAABvQF3Af8BewHrAXoBIAF2AUABdgFA
        AXYBQAF2AUABdgFAAXYBIAF2AUABdgFAAXYBYQF2AWEBdgFAAXYBhQF2Ad4BewGcAXMB3gF7AZ0BcwGd
        AXMBnQFzAb0BcwH/AX8IAAH/AX8BvQFzAf8BfwHOAWEBIAFQAYMBVAGDAVQBgwFUAYMBVAGDAVQBgwFU
        ASABUAHOAWEB/wF/Ab0BcwH/AX8IAAGcAXMB/wF/AfgBCQG3AQEB+AEBAdgBAQHYAQEB2AEBAdcBAQG3
        AQEBtwEBAbcBAQG3AQEBlgEBAZYBAQGWAQEBdQEBAXUBAQGVAQEBlQEBAXQBAQFUAQEB3gF/AZwBcwGc
        AXMB/wF/AQYBQwHBATYB5QE+AeQBPgHkAT4BwAE2ASwBUwH/AX8B/wF/AeQBPgHjAToB5AE+AeQBPgHk
        AT4BwAE2AXEBXwH/AX8BnAFzCAAB/wF/Ad4BdwGZAXsBygF6AYYBdgGmAXYBpgF2AaYBdgGmAXYBpgF2
        AacBdgGmAXYBYAF2AWABdgGoAXYBdAF7Ad8BdwHeAXsBnAFzAd4BewExAXsBUwF7Af8BewG9AXcGAAH/
        AX8BvQFzAf8BfwGtAWEBAAFQAWMBVAGDAVQBgwFUAYMBVAGDAVQBgwFUAYMBVAFjAVQBAAFQAa0BYQH/
        AX8BvQFzAf8BfwYAAZwBcwH/AX8BGAEKAdgBAQH4AQEB2AEBARkBCgE5ARIBOQEOATkBDgEYAQ4BGAEO
        ARgBDgEYAQ4B+AENAfcBDQH3AREB1gEJAVUBAQF1AQEBdAEBAVQBAQH+AX8BnAFzAf8BfwH/AX8B3QF3
        AeQBPgHiAToBBAFDAcABNgErAVMB/wF/Ab4BdwHfAX8BuAFvAeABNgHkAUIBBAFDAQQBQwHkAT4B4QE6
        AboBcwH/AX8BvQF3CAABvgF3Ad4BdwG6AXsBdQF7AXUBewF2AXsBdgF7AXYBewF2AXsBlgF7AXQBewFg
        AXYBYAJ2AXsB/wF3Ab4BdwH/AX8BvQFzAZkBewFAAXYBgwF6Af8BewGcAXMEAAH/AX8B3gF3Af8BfwHN
        AWUBAAFQAYMBWAGDAVgBgwFYAYMBWAGDAVgBgwFYAYMBWAGDAVgBgwFYAYMBWAEAAVABzQFlAf8BfwG+
        AXcB/wF/BAABnAFzAf8BfwEZAQoB2AEBAdgBAQHbAToB/QF/Ad0BfwHdAX8B3QF/Ad0BfwHdAX8B3QF/
        Af0BfwH+AX8B/wF/Af8BfwH+AX8BmQE2AVQBAQF0AQEBdAEBAf4BfwGcAXMCAAHeAXsB/wF/Ad0BdwHj
        AUIBwAE2ASoBUwH/AX8B3gF7Af8BfwG9AXcB/wF/AU0BVwHgAToBBAFDAQQBQwEEAUMB4gE+AQUBRwH/
        AX8B3wF/Af8BfwgAAd4BewHeAXsB3gF7Ad4BewHeAXsB3gF7Ad4BewHeAXsB3gF3Ad0BewFgAXoBYAF6
        AbwBewHeAXcB/wF/Af8BfwHeAXcBdQF7AUABdgGAAXYB3wF7AZwBcwIAAf8BfwG9AXMB/wF/Ac4BaQEA
        AVQBgwFYAYMBXAGDAVwBgwFcAYMBXAFiAVgBYgFYAYMBXAGDAVwBgwFcAYMBXAGDAVgBAAFUAc4BaQH/
        AX8BvQFzAf8BfwIAAZwBcwH/AX8BGQEKAdgBAQH5AQEBvQF3Ad0BfwG9AXcBvQF3Ab0BdwG9AXcBvQF3
        Ab0BdwH+AX8BuQFGAZQBHQE2AS4B/gF/AZ0BcwFUAQEBdAEBAXQBAQH/AX8BnAFzBAAB3gF7Af8BfwHd
        AXsBlgFrAf8BfwHfAX8B/wF/AgAB/wF/Ad4BewH/AX8BAwFDAQIBQwEEAUcBBAFHAQQBRwHgAT4BKgFT
        Af8BfwG9AXcB/wF/CgAB/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8BvQFzAbsBewGAAXoBYAF6AbsBewG9
        AXMCAAH/AX8B3gF3AZYBewFgAXoBgAF6Af8BewGcAXMB/wF/Ab0BdwH/AX8BrAFpAQABWAGDAVwBgwFc
        AYMBXAGDAVwBgwFcASABXAHGAWAB5gFgASABXAGDAVwBgwFcAYMBXAGDAVwBgwFcAQABWAGsAWkB/wF/
        Ab0BdwH/AX8BnAFzAf8BfwE5AQoB+QEBAfkBAQG9AXcB3gF/Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3
        Af8BfwH2ARkB0QEAATIBAQH+AX8BnQFzAXUBAQF1AQEBdAEBAf8BfwGcAXMGAAHeAXsBvgF7Af8BfwG9
        AXcB/wF/BgAB3gF7Af8BfwG4AW8B4AE+AQMBRwEDAUcBAwFHAQMBRwHgAT4BTAFXAf8BfwG9AXcB/wF/
        FAABvQFzAdsBewGAAXoBgAF6AboBewG+AXcB/wF/Af8BfwHeAXsBlgF7AYABegGgAXoB/wF/AZwBcwGc
        AXMB/wF/AQ8BbgEAAVgBgwFgAYMBYAGDAWABgwFgAYMBYAEgAVwBgwFgAf8BfwH/AX8BYgFgASABXAGD
        AWABgwFgAYMBYAGDAWABgwFgAQABWAEPAW4B/wF/AZwBcwGcAXMB/wF/ATkBCgH5AQEB+QEBAb4BdwHe
        AX8B3gF7Ad4BewHeAXsB3gF7Ad4BewHeAXsB/wF/AVgBIgFUAQEBlQEBAf8BfwG9AXMBdQEBAXUBAQF0
        AQEB/wF/AZwBcxgAAb0BdwH/AX8BbgFfAeABPgEDAUcBAwFHAQMBRwEDAUcB4AE+AW8BXwH/AX8BnAFz
        FAABnQFzAd0BfwGgAXoBgAF6AZYBewHeAXsB3gF7Ab0BdwH/AX8BcQF7AYABegGgAXoB/wF/AZwBcwGc
        AXMB/wF/AUEBYAFBAWABgwFgAYMBYAGDAWABgwFgASABYAGDAWABvQF7Af8BfwH/AX8BvQF7AYMBYAEg
        AWABgwFgAYMBYAGDAWABgwFgAUEBYAFBAWAB/wF/AZwBcwGcAXMB/wF/AToBCgH5AQEBGgECAd4BdwH+
        AX8B3gF7Ad4BewHeAXsB3gF7Ad4BewHeAXsB/wF/AXoBIgGXAQEB1wEBAf8BfwG+AXMBdQEBAXUBAQF0
        AQEB/wF/AZwBcxoAAb0BdwH/AX8BSAFTAQABQwEDAUsBAwFLAQMBSwEAAUMBAAFHAf8BfwGcAXMUAAG9
        AXcB/wF/AeMBegGgAXoBCAF7AdsBfwHdAXsB3QF7AdoBfwHkAXoBoAF6AQYBewH/AX8BvQF3Ab0BdwH/
        AX8B7wFtAQABYAGDAWQBgwFkAYMBZAEgAWABgwFkAd4BfwH/AX8B3gF7Ad4BewH/AX8B3gF/AYMBZAEg
        AWABgwFkAYMBZAGDAWQBAAFgAQ8BbgH/AX8BvQF3AZwBcwH/AX8BOgEGAfkBAQEaAQIB3gF3Af8BfwHe
        AXsB3gF7Ad4BewHeAXsB3gF7Ad4BewH/AX8BmwEaAbgBAQG3AQEB/wF/Ab4BdwF1AQEBdQEBAXQBAQH/
        AX8BnAFzGgAB/wF/Af8BfwH/AX8BIgFLAQABRwEiAUsBAAFHAQABRwG2AW8B/wF/Ad4BexQAAd4BewH/
        AX8BcQF/AcABegGgAXoBBwF7AZYBfwGVAX8BBQF7AaABegHBAXoBlQF/Af8BewH/AX8B/wF/Ad4BewH/
        AX8BjAFtAQABZAFiAWQBIAFkAWIBZAHeAX8B/wF/Ad4BewQAAd4BewH/AX8BvQF/AWEBZAEgAWQBYgFk
        AQABZAGMAW0B/wF/Ad4BewH/AX8BvQF3Af8BfwF6ARoB2QEBARoBAgHfAXsB/wF/Af8BfwH/AX8B/wF/
        Af8BfwH/AX8B/wF/Af8BfwEdAUMBOgEOAXoBJgH/AX8BvgFzAXUBAQE0AQEBlQEBAf8BfwGcAXMcAAHe
        AXsB/wF/AfwBewEiAU8BAAFDAUgBVwH9AXsB/wF/Ab0BdxgAAd4BewH/AX8BJwF7AcEBegHAAXoBwAF6
        AcABegHAAXoBwgF6AUkBfwH/AX8BvQF3BAAB/wF/Ad4BewH/AX8BzgFxAQABZAGEAWgBvQF/Af8BfwHe
        AXsIAAHeAXsB/wF/Ab0BfwGkAWgBAAFkAc4BcQH/AX8B3gF7Af8BfwIAAf8BfwH/AX8B3wF3AToBBgHZ
        AQEB3wF3Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Ab4BcwEU
        AQEBdAEBAX0BZwH/AX8B3gF7HgAB3gF7Af8BfwH/AX8B2wF3Af8BfwH/AX8B3gF7GgAB/wF/Af8BfwH/
        AX8BbwF/AQIBewHiAXoB4gF6AQMBewGSAX8B/wF/Ad4BewH/AX8GAAH/AX8B3gF7Af8BfwG9AX8B/wF/
        Af8BfwHeAXsMAAHeAXsB/wF/Af8BfwG9AX8B/wF/Ad4BewH/AX8GAAHeAXsB/wF/Af8BfwG/AW8B/wF/
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwG+AW8B/wF/
        Af8BfwG9AXciAAHeAXsB3gF7Af8BfwG9AXcgAAH/AX8B3gF7Af8BfwH/AX8B3AF/AdwBfwH/AX8B/wF/
        Ad4BdwH/AX8MAAG9AXcBvQF3Ab0BdwHeAXsQAAHeAXsBvQF3Ab0BdwG9AXcMAAH/AX8BvQF3Ab0BdwG9
        AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9
        AXcB3gF7JgAB/wF/Af8BfwH/AX8kAAHeAXsBvQF3Ab0BdwG9AXcBvQF3Ad4BewgAAUIBTQE+BwABPgMA
        ASgDAAFgAwABMAMAAQEBAAEBBQABQAECFgAD//8AIgAB4AH/AQcBwAEAAQMB/wGPAf8BwAEAAf8BwAF+
        AQMBgAEAAQEB/wGHAf8BgAEAAX8BgAE8AQEDAAH+AQMB/wIAAT8BAAEYBAAB/AEBAf8CAAE/BgAB+AEB
        Af8CAAE/BgAB8AEAAf8CAAE/BgAB4AEAAf8CAAE/BgABwAEAAX8CAAE/AYABAAEBAwABgAEAAT8CAAE/
        AcABAAEDAwABgAEAAT8CAAE3AeABAAEHBQABHwIAASEB8AEAAQ8FAAEPAwAB8AEAAQ8FAAEPAwAB4AEA
        AQcFAAEHAYACAAHAAQABAwMAAYABAAEDAcACAAGAAQABAQMAAcABQAEBAfABAAGABgAC4AEAAf8BwAcA
        Af8B8AEAAf8BwAcAAf8B+AEAAf8BwAcAAf8B+AEAAf8BwAIAARgEAAH/AfwBAQH/AeABAQGAATwBAQMA
        Af8B/gEDAf8B4AEBAcABfgEDAYABAAEBAv8BDwH/AfABAwHwAf8BDwHAAQABAwL/AY8B/wH8AQ8L
</value>
  </data>
  <metadata name="ClientNameColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="FromColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="ToColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="CareTakerColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="LabelClientManagementInfo.Text" xml:space="preserve">
    <value>Here is where you assign clients to this account manager. The account manager will only be able to manage a client if the client has been added to this list, and if the current date falls between the 'From' and 'To' dates specified for the client. The 'To' date is calculated based on the next account manager that will be taking over the client. Bear in mind that you might be changing commission allocation and previous reports by modifying this list.</value>
  </data>
  <metadata name="ContractNumberColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="LabelAccountManagerDetailsInfo.Text" xml:space="preserve">
    <value>This section allows you to configure various properties of this account manager. The 'System User' selection indicates which physical user corresponds to this account manager. Ticking the 'Dormant' box will prevent any contracts or provisional bookings from being created for this account manager.</value>
  </data>
  <metadata name="FiscalNameColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="BudgetColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="BrandNameColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn2.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn3.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewCheckBoxColumn1.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
</root>