﻿using DataAccess;

namespace DataService.Security
{
    class UpdateUserCommandExecutor : CommandExecutor<UpdateUserCommand>
    {

        public override void Execute(UpdateUserCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.UpdateUser))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.AddInputParameter("userid", command.UserId);
                storedprocedure.AddInputParameter("firstname", command.FirstName);
                storedprocedure.AddInputParameter("lastname", command.LastName);
                storedprocedure.AddInputParameter("email", command.EmailAddress);
                storedprocedure.AddInputParameter("mobilephone", command.MobilePhone);
                storedprocedure.AddInputParameter("gender", command.Gender);
                storedprocedure.AddInputParameter("notes", command.Notes);
                storedprocedure.AddInputParameter("deleted", command.Deleted);
                storedprocedure.AddInputParameter("username", command.Username);
                storedprocedure.AddInputParameter("enabled", command.Enabled);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
            }
        }

    }
}
