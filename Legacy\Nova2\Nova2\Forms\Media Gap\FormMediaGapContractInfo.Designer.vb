<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class FormMediaGapContractInfo
    Inherits LiquidShell.EditorForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Dim SuperToolTip1 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip
        Dim ToolTipTitleItem1 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem
        Dim ToolTipItem1 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem
        Dim SuperToolTip2 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip
        Dim ToolTipTitleItem2 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem
        Dim ToolTipItem2 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle
        Dim DataGridViewCellStyle5 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle
        Dim DataGridViewCellStyle6 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle
        Dim DataGridViewCellStyle4 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(FormMediaGapContractInfo))
        Me.GroupControlAvailableItems = New DevExpress.XtraEditors.GroupControl
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl
        Me.TextEditSearch = New DevExpress.XtraEditors.TextEdit
        Me.PictureClearSearch = New DevExpress.XtraEditors.PictureEdit
        Me.PictureAdvancedSearch = New DevExpress.XtraEditors.PictureEdit
        Me.GridItems = New System.Windows.Forms.DataGridView
        Me.MediaNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn
        Me.ChainNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn
        Me.DataGridViewTextBoxColumn1 = New System.Windows.Forms.DataGridViewTextBoxColumn
        Me.InstallStoreQtyColumn = New System.Windows.Forms.DataGridViewTextBoxColumn
        Me.FirstWeekColumn = New System.Windows.Forms.DataGridViewTextBoxColumn
        Me.LastWeekColumn = New System.Windows.Forms.DataGridViewTextBoxColumn
        Me.InstallWeeksColumn = New System.Windows.Forms.DataGridViewTextBoxColumn
        Me.BrandNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn
        Me.ProductNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn
        Me.HomesiteColumn = New System.Windows.Forms.DataGridViewCheckBoxColumn
        Me.LabelProjectName = New DevExpress.XtraEditors.LabelControl
        Me.LabelClientName = New DevExpress.XtraEditors.LabelControl
        Me.LabelClientNameValue = New DevExpress.XtraEditors.LabelControl
        Me.LabelProjectNameValue = New DevExpress.XtraEditors.LabelControl
        Me.LabelSignedBy = New DevExpress.XtraEditors.LabelControl
        Me.LabelSignedByValue = New DevExpress.XtraEditors.LabelControl
        Me.LabelSignDate = New DevExpress.XtraEditors.LabelControl
        Me.LabelSignDateValue = New DevExpress.XtraEditors.LabelControl
        Me.LabelCreatedBy = New DevExpress.XtraEditors.LabelControl
        Me.LabelCreatedByValue = New DevExpress.XtraEditors.LabelControl
        Me.LabelCreationDate = New DevExpress.XtraEditors.LabelControl
        Me.LabelCreationDateValue = New DevExpress.XtraEditors.LabelControl
        Me.LabelContractNumberValue = New DevExpress.XtraEditors.LabelControl
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonOK = New DevExpress.XtraEditors.SimpleButton
        Me.LabelAccountManager = New DevExpress.XtraEditors.LabelControl
        Me.LabelAccountManagerValue = New DevExpress.XtraEditors.LabelControl
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelButtonBar.SuspendLayout()
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControlAvailableItems, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlAvailableItems.SuspendLayout()
        CType(Me.TextEditSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridItems, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'PanelButtonBar
        '
        Me.PanelButtonBar.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelButtonBar.Appearance.BackColor2 = System.Drawing.Color.LightSteelBlue
        Me.PanelButtonBar.Appearance.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical
        Me.PanelButtonBar.Appearance.Options.UseBackColor = True
        Me.PanelButtonBar.Controls.Add(Me.ButtonOK)
        Me.PanelButtonBar.Location = New System.Drawing.Point(0, 415)
        Me.PanelButtonBar.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Office2003
        Me.PanelButtonBar.LookAndFeel.UseDefaultLookAndFeel = False
        Me.PanelButtonBar.Size = New System.Drawing.Size(829, 52)
        Me.PanelButtonBar.TabIndex = 14
        '
        'GroupControlAvailableItems
        '
        Me.GroupControlAvailableItems.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlAvailableItems.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlAvailableItems.Appearance.Options.UseFont = True
        Me.GroupControlAvailableItems.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlAvailableItems.AppearanceCaption.Options.UseFont = True
        Me.GroupControlAvailableItems.Controls.Add(Me.LabelControl1)
        Me.GroupControlAvailableItems.Controls.Add(Me.TextEditSearch)
        Me.GroupControlAvailableItems.Controls.Add(Me.PictureClearSearch)
        Me.GroupControlAvailableItems.Controls.Add(Me.PictureAdvancedSearch)
        Me.GroupControlAvailableItems.Controls.Add(Me.GridItems)
        Me.GroupControlAvailableItems.Location = New System.Drawing.Point(12, 154)
        Me.GroupControlAvailableItems.LookAndFeel.SkinName = "Black"
        Me.GroupControlAvailableItems.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlAvailableItems.Margin = New System.Windows.Forms.Padding(3, 12, 3, 0)
        Me.GroupControlAvailableItems.Name = "GroupControlAvailableItems"
        Me.GroupControlAvailableItems.Size = New System.Drawing.Size(805, 261)
        Me.GroupControlAvailableItems.TabIndex = 13
        Me.GroupControlAvailableItems.Tag = ""
        Me.GroupControlAvailableItems.Text = "Burst List"
        '
        'LabelControl1
        '
        Me.LabelControl1.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl1.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl1.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl1.Location = New System.Drawing.Point(647, 237)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(45, 13)
        Me.LabelControl1.TabIndex = 2
        Me.LabelControl1.Text = "Search:"
        '
        'TextEditSearch
        '
        Me.TextEditSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearch.EditValue = ""
        Me.TextEditSearch.Location = New System.Drawing.Point(698, 234)
        Me.TextEditSearch.Margin = New System.Windows.Forms.Padding(3, 4, 3, 5)
        Me.TextEditSearch.Name = "TextEditSearch"
        Me.TextEditSearch.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearch.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearch.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearch.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearch.Size = New System.Drawing.Size(80, 20)
        Me.TextEditSearch.TabIndex = 3
        '
        'PictureClearSearch
        '
        Me.PictureClearSearch.AllowDrop = True
        Me.PictureClearSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearch.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearch.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearch.Location = New System.Drawing.Point(784, 3)
        Me.PictureClearSearch.Name = "PictureClearSearch"
        Me.PictureClearSearch.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearch.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearch.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearch.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem1.Text = "Clear Search"
        ToolTipItem1.LeftIndent = 6
        ToolTipItem1.Text = "Click here to clear all search boxes."
        SuperToolTip1.Items.Add(ToolTipTitleItem1)
        SuperToolTip1.Items.Add(ToolTipItem1)
        Me.PictureClearSearch.SuperTip = SuperToolTip1
        Me.PictureClearSearch.TabIndex = 0
        Me.PictureClearSearch.TabStop = True
        '
        'PictureAdvancedSearch
        '
        Me.PictureAdvancedSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearch.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearch.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearch.Location = New System.Drawing.Point(784, 236)
        Me.PictureAdvancedSearch.Margin = New System.Windows.Forms.Padding(3, 6, 3, 7)
        Me.PictureAdvancedSearch.Name = "PictureAdvancedSearch"
        Me.PictureAdvancedSearch.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearch.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearch.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearch.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem2.Text = "Advanced Search"
        ToolTipItem2.LeftIndent = 6
        ToolTipItem2.Text = "Click here to search individual column values."
        SuperToolTip2.Items.Add(ToolTipTitleItem2)
        SuperToolTip2.Items.Add(ToolTipItem2)
        Me.PictureAdvancedSearch.SuperTip = SuperToolTip2
        Me.PictureAdvancedSearch.TabIndex = 4
        Me.PictureAdvancedSearch.TabStop = True
        '
        'GridItems
        '
        Me.GridItems.AllowUserToAddRows = False
        Me.GridItems.AllowUserToDeleteRows = False
        Me.GridItems.AllowUserToOrderColumns = True
        Me.GridItems.AllowUserToResizeRows = False
        DataGridViewCellStyle1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridItems.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle1
        Me.GridItems.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridItems.BackgroundColor = System.Drawing.Color.White
        Me.GridItems.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridItems.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridItems.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle2.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle2.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridItems.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle2
        Me.GridItems.ColumnHeadersHeight = 22
        Me.GridItems.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridItems.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.MediaNameColumn, Me.ChainNameColumn, Me.DataGridViewTextBoxColumn1, Me.InstallStoreQtyColumn, Me.FirstWeekColumn, Me.LastWeekColumn, Me.InstallWeeksColumn, Me.BrandNameColumn, Me.ProductNameColumn, Me.HomesiteColumn})
        DataGridViewCellStyle5.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle5.BackColor = System.Drawing.SystemColors.Window
        DataGridViewCellStyle5.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle5.ForeColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(31, Byte), Integer), CType(CType(53, Byte), Integer))
        DataGridViewCellStyle5.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle5.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle5.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.GridItems.DefaultCellStyle = DataGridViewCellStyle5
        Me.GridItems.EnableHeadersVisualStyles = False
        Me.GridItems.GridColor = System.Drawing.Color.White
        Me.GridItems.Location = New System.Drawing.Point(2, 22)
        Me.GridItems.Name = "GridItems"
        Me.GridItems.ReadOnly = True
        Me.GridItems.RowHeadersVisible = False
        DataGridViewCellStyle6.ForeColor = System.Drawing.Color.DimGray
        Me.GridItems.RowsDefaultCellStyle = DataGridViewCellStyle6
        Me.GridItems.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridItems.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridItems.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridItems.RowTemplate.Height = 19
        Me.GridItems.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridItems.ShowCellToolTips = False
        Me.GridItems.Size = New System.Drawing.Size(801, 205)
        Me.GridItems.StandardTab = True
        Me.GridItems.TabIndex = 1
        '
        'MediaNameColumn
        '
        Me.MediaNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.MediaNameColumn.DataPropertyName = "MediaName"
        Me.MediaNameColumn.HeaderText = "Media Service"
        Me.MediaNameColumn.Name = "MediaNameColumn"
        Me.MediaNameColumn.ReadOnly = True
        Me.MediaNameColumn.Width = 111
        '
        'ChainNameColumn
        '
        Me.ChainNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.ChainNameColumn.DataPropertyName = "ChainName"
        Me.ChainNameColumn.HeaderText = "Chain"
        Me.ChainNameColumn.Name = "ChainNameColumn"
        Me.ChainNameColumn.ReadOnly = True
        Me.ChainNameColumn.Width = 64
        '
        'DataGridViewTextBoxColumn1
        '
        Me.DataGridViewTextBoxColumn1.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.DataGridViewTextBoxColumn1.DataPropertyName = "CategoryName"
        Me.DataGridViewTextBoxColumn1.HeaderText = "Category"
        Me.DataGridViewTextBoxColumn1.Name = "DataGridViewTextBoxColumn1"
        Me.DataGridViewTextBoxColumn1.ReadOnly = True
        Me.DataGridViewTextBoxColumn1.Width = 84
        '
        'InstallStoreQtyColumn
        '
        Me.InstallStoreQtyColumn.DataPropertyName = "InstallStoreQty"
        DataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        Me.InstallStoreQtyColumn.DefaultCellStyle = DataGridViewCellStyle3
        Me.InstallStoreQtyColumn.HeaderText = "Stores"
        Me.InstallStoreQtyColumn.Name = "InstallStoreQtyColumn"
        Me.InstallStoreQtyColumn.ReadOnly = True
        Me.InstallStoreQtyColumn.Width = 50
        '
        'FirstWeekColumn
        '
        Me.FirstWeekColumn.DataPropertyName = "FirstWeek"
        Me.FirstWeekColumn.HeaderText = "First Week"
        Me.FirstWeekColumn.Name = "FirstWeekColumn"
        Me.FirstWeekColumn.ReadOnly = True
        Me.FirstWeekColumn.Width = 80
        '
        'LastWeekColumn
        '
        Me.LastWeekColumn.DataPropertyName = "LastInstallWeek"
        Me.LastWeekColumn.HeaderText = "Last Week"
        Me.LastWeekColumn.Name = "LastWeekColumn"
        Me.LastWeekColumn.ReadOnly = True
        Me.LastWeekColumn.Width = 80
        '
        'InstallWeeksColumn
        '
        Me.InstallWeeksColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.InstallWeeksColumn.DataPropertyName = "InstallWeeks"
        DataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        Me.InstallWeeksColumn.DefaultCellStyle = DataGridViewCellStyle4
        Me.InstallWeeksColumn.HeaderText = "Weeks"
        Me.InstallWeeksColumn.Name = "InstallWeeksColumn"
        Me.InstallWeeksColumn.ReadOnly = True
        Me.InstallWeeksColumn.Width = 69
        '
        'BrandNameColumn
        '
        Me.BrandNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.BrandNameColumn.DataPropertyName = "BrandName"
        Me.BrandNameColumn.HeaderText = "Brand"
        Me.BrandNameColumn.Name = "BrandNameColumn"
        Me.BrandNameColumn.ReadOnly = True
        Me.BrandNameColumn.Width = 65
        '
        'ProductNameColumn
        '
        Me.ProductNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.ProductNameColumn.DataPropertyName = "ProductName"
        Me.ProductNameColumn.HeaderText = "Product"
        Me.ProductNameColumn.Name = "ProductNameColumn"
        Me.ProductNameColumn.ReadOnly = True
        Me.ProductNameColumn.Width = 74
        '
        'HomesiteColumn
        '
        Me.HomesiteColumn.DataPropertyName = "Homesite"
        Me.HomesiteColumn.HeaderText = "Homesite"
        Me.HomesiteColumn.Name = "HomesiteColumn"
        Me.HomesiteColumn.ReadOnly = True
        Me.HomesiteColumn.Width = 70
        '
        'LabelProjectName
        '
        Me.LabelProjectName.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelProjectName.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelProjectName.Location = New System.Drawing.Point(12, 31)
        Me.LabelProjectName.Name = "LabelProjectName"
        Me.LabelProjectName.Size = New System.Drawing.Size(45, 13)
        Me.LabelProjectName.TabIndex = 2
        Me.LabelProjectName.Text = "Project:"
        '
        'LabelClientName
        '
        Me.LabelClientName.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelClientName.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelClientName.Location = New System.Drawing.Point(12, 12)
        Me.LabelClientName.Name = "LabelClientName"
        Me.LabelClientName.Size = New System.Drawing.Size(75, 13)
        Me.LabelClientName.TabIndex = 0
        Me.LabelClientName.Text = "Client Name:"
        '
        'LabelClientNameValue
        '
        Me.LabelClientNameValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelClientNameValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelClientNameValue.Location = New System.Drawing.Point(133, 12)
        Me.LabelClientNameValue.Name = "LabelClientNameValue"
        Me.LabelClientNameValue.Size = New System.Drawing.Size(98, 13)
        Me.LabelClientNameValue.TabIndex = 1
        Me.LabelClientNameValue.Text = "ClientNameValue"
        '
        'LabelProjectNameValue
        '
        Me.LabelProjectNameValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelProjectNameValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelProjectNameValue.Location = New System.Drawing.Point(133, 31)
        Me.LabelProjectNameValue.Name = "LabelProjectNameValue"
        Me.LabelProjectNameValue.Size = New System.Drawing.Size(105, 13)
        Me.LabelProjectNameValue.TabIndex = 3
        Me.LabelProjectNameValue.Text = "ProjectNameValue"
        '
        'LabelSignedBy
        '
        Me.LabelSignedBy.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSignedBy.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelSignedBy.Location = New System.Drawing.Point(12, 107)
        Me.LabelSignedBy.Name = "LabelSignedBy"
        Me.LabelSignedBy.Size = New System.Drawing.Size(63, 13)
        Me.LabelSignedBy.TabIndex = 4
        Me.LabelSignedBy.Text = "Signed By:"
        '
        'LabelSignedByValue
        '
        Me.LabelSignedByValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSignedByValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelSignedByValue.Location = New System.Drawing.Point(133, 107)
        Me.LabelSignedByValue.Name = "LabelSignedByValue"
        Me.LabelSignedByValue.Size = New System.Drawing.Size(86, 13)
        Me.LabelSignedByValue.TabIndex = 5
        Me.LabelSignedByValue.Text = "SignedByValue"
        '
        'LabelSignDate
        '
        Me.LabelSignDate.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSignDate.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelSignDate.Location = New System.Drawing.Point(12, 126)
        Me.LabelSignDate.Name = "LabelSignDate"
        Me.LabelSignDate.Size = New System.Drawing.Size(91, 13)
        Me.LabelSignDate.TabIndex = 6
        Me.LabelSignDate.Text = "Signature Date:"
        '
        'LabelSignDateValue
        '
        Me.LabelSignDateValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSignDateValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelSignDateValue.Location = New System.Drawing.Point(133, 126)
        Me.LabelSignDateValue.Name = "LabelSignDateValue"
        Me.LabelSignDateValue.Size = New System.Drawing.Size(84, 13)
        Me.LabelSignDateValue.TabIndex = 7
        Me.LabelSignDateValue.Text = "SignDateValue"
        '
        'LabelCreatedBy
        '
        Me.LabelCreatedBy.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCreatedBy.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCreatedBy.Location = New System.Drawing.Point(12, 69)
        Me.LabelCreatedBy.Name = "LabelCreatedBy"
        Me.LabelCreatedBy.Size = New System.Drawing.Size(70, 13)
        Me.LabelCreatedBy.TabIndex = 8
        Me.LabelCreatedBy.Text = "Created By:"
        '
        'LabelCreatedByValue
        '
        Me.LabelCreatedByValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCreatedByValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCreatedByValue.Location = New System.Drawing.Point(133, 69)
        Me.LabelCreatedByValue.Name = "LabelCreatedByValue"
        Me.LabelCreatedByValue.Size = New System.Drawing.Size(93, 13)
        Me.LabelCreatedByValue.TabIndex = 9
        Me.LabelCreatedByValue.Text = "CreatedByValue"
        '
        'LabelCreationDate
        '
        Me.LabelCreationDate.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCreationDate.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCreationDate.Location = New System.Drawing.Point(12, 88)
        Me.LabelCreationDate.Name = "LabelCreationDate"
        Me.LabelCreationDate.Size = New System.Drawing.Size(85, 13)
        Me.LabelCreationDate.TabIndex = 10
        Me.LabelCreationDate.Text = "Creation Date:"
        '
        'LabelCreationDateValue
        '
        Me.LabelCreationDateValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCreationDateValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCreationDateValue.Location = New System.Drawing.Point(133, 88)
        Me.LabelCreationDateValue.Name = "LabelCreationDateValue"
        Me.LabelCreationDateValue.Size = New System.Drawing.Size(108, 13)
        Me.LabelCreationDateValue.TabIndex = 11
        Me.LabelCreationDateValue.Text = "CreationDateValue"
        '
        'LabelContractNumberValue
        '
        Me.LabelContractNumberValue.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelContractNumberValue.Appearance.Font = New System.Drawing.Font("Century Gothic", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelContractNumberValue.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelContractNumberValue.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.LabelContractNumberValue.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelContractNumberValue.Location = New System.Drawing.Point(600, 12)
        Me.LabelContractNumberValue.Name = "LabelContractNumberValue"
        Me.LabelContractNumberValue.Size = New System.Drawing.Size(217, 44)
        Me.LabelContractNumberValue.TabIndex = 12
        Me.LabelContractNumberValue.Text = "AV902212"
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        Me.ImageList24x24.Images.SetKeyName(3, "back.png")
        '
        'ButtonOK
        '
        Me.ButtonOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonOK.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonOK.Appearance.Options.UseFont = True
        Me.ButtonOK.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.ButtonOK.ImageIndex = 2
        Me.ButtonOK.ImageList = Me.ImageList24x24
        Me.ButtonOK.Location = New System.Drawing.Point(717, 12)
        Me.ButtonOK.LookAndFeel.SkinName = "Black"
        Me.ButtonOK.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonOK.Name = "ButtonOK"
        Me.ButtonOK.Size = New System.Drawing.Size(100, 28)
        Me.ButtonOK.TabIndex = 0
        Me.ButtonOK.Text = "OK"
        '
        'LabelAccountManager
        '
        Me.LabelAccountManager.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelAccountManager.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelAccountManager.Location = New System.Drawing.Point(12, 50)
        Me.LabelAccountManager.Name = "LabelAccountManager"
        Me.LabelAccountManager.Size = New System.Drawing.Size(103, 13)
        Me.LabelAccountManager.TabIndex = 2
        Me.LabelAccountManager.Text = "Account Manager:"
        '
        'LabelAccountManagerValue
        '
        Me.LabelAccountManagerValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelAccountManagerValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelAccountManagerValue.Location = New System.Drawing.Point(133, 50)
        Me.LabelAccountManagerValue.Name = "LabelAccountManagerValue"
        Me.LabelAccountManagerValue.Size = New System.Drawing.Size(94, 13)
        Me.LabelAccountManagerValue.TabIndex = 3
        Me.LabelAccountManagerValue.Text = "AccountManager"
        '
        'FormMediaGapContractInfo
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.CancelButton = Me.ButtonOK
        Me.ClientSize = New System.Drawing.Size(829, 467)
        Me.Controls.Add(Me.GroupControlAvailableItems)
        Me.Controls.Add(Me.LabelCreationDateValue)
        Me.Controls.Add(Me.LabelSignDateValue)
        Me.Controls.Add(Me.LabelAccountManagerValue)
        Me.Controls.Add(Me.LabelProjectNameValue)
        Me.Controls.Add(Me.LabelCreationDate)
        Me.Controls.Add(Me.LabelAccountManager)
        Me.Controls.Add(Me.LabelSignDate)
        Me.Controls.Add(Me.LabelProjectName)
        Me.Controls.Add(Me.LabelCreatedByValue)
        Me.Controls.Add(Me.LabelSignedByValue)
        Me.Controls.Add(Me.LabelContractNumberValue)
        Me.Controls.Add(Me.LabelClientNameValue)
        Me.Controls.Add(Me.LabelCreatedBy)
        Me.Controls.Add(Me.LabelSignedBy)
        Me.Controls.Add(Me.LabelClientName)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.MinimumSize = New System.Drawing.Size(386, 288)
        Me.Name = "FormMediaGapContractInfo"
        Me.Tag = "848, 474"
        Me.Text = "Contract Information - AV902212"
        Me.Controls.SetChildIndex(Me.LabelClientName, 0)
        Me.Controls.SetChildIndex(Me.LabelSignedBy, 0)
        Me.Controls.SetChildIndex(Me.LabelCreatedBy, 0)
        Me.Controls.SetChildIndex(Me.LabelClientNameValue, 0)
        Me.Controls.SetChildIndex(Me.LabelContractNumberValue, 0)
        Me.Controls.SetChildIndex(Me.LabelSignedByValue, 0)
        Me.Controls.SetChildIndex(Me.LabelCreatedByValue, 0)
        Me.Controls.SetChildIndex(Me.LabelProjectName, 0)
        Me.Controls.SetChildIndex(Me.LabelSignDate, 0)
        Me.Controls.SetChildIndex(Me.LabelAccountManager, 0)
        Me.Controls.SetChildIndex(Me.LabelCreationDate, 0)
        Me.Controls.SetChildIndex(Me.LabelProjectNameValue, 0)
        Me.Controls.SetChildIndex(Me.LabelAccountManagerValue, 0)
        Me.Controls.SetChildIndex(Me.LabelSignDateValue, 0)
        Me.Controls.SetChildIndex(Me.LabelCreationDateValue, 0)
        Me.Controls.SetChildIndex(Me.PanelButtonBar, 0)
        Me.Controls.SetChildIndex(Me.GroupControlAvailableItems, 0)
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelButtonBar.ResumeLayout(False)
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControlAvailableItems, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlAvailableItems.ResumeLayout(False)
        CType(Me.TextEditSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridItems, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents GroupControlAvailableItems As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditSearch As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearch As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearch As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents GridItems As System.Windows.Forms.DataGridView
    Friend WithEvents LabelProjectName As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelClientName As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelClientNameValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelProjectNameValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelSignedBy As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelSignedByValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelSignDate As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelSignDateValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelCreatedBy As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelCreatedByValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelCreationDate As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelCreationDateValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelContractNumberValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents ButtonOK As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelAccountManager As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelAccountManagerValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents MediaNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents ChainNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn1 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents InstallStoreQtyColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents FirstWeekColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents LastWeekColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents InstallWeeksColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents BrandNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents ProductNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents HomesiteColumn As System.Windows.Forms.DataGridViewCheckBoxColumn

End Class
