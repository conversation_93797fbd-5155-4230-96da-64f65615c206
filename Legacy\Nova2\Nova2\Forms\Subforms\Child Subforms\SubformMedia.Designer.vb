<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformMedia
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformMedia))
        Dim SuperToolTip1 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem1 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem1 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip2 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem2 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem2 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle5 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle4 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim SuperToolTip3 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem3 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem3 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip4 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem4 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem4 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle6 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle7 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle8 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim SuperToolTip5 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem5 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem5 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip6 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem6 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem6 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle9 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle10 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle11 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim SuperToolTip7 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem7 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem7 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip8 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem8 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem8 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle12 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle13 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle14 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim SuperToolTip9 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem9 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem9 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip10 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem10 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem10 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle15 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle16 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle17 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim SuperToolTip11 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem11 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem11 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip12 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem12 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem12 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle18 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle19 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle22 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle20 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle21 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Me.ImageList16x16 = New System.Windows.Forms.ImageList(Me.components)
        Me.LabelControlNewsContent = New DevExpress.XtraEditors.LabelControl()
        Me.GroupControlMediaLifeCycle = New DevExpress.XtraEditors.GroupControl()
        Me.LabelControl7 = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearchMediaLifeCycle = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearchMediaLifeCycle = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearchMediaLifeCycle = New DevExpress.XtraEditors.PictureEdit()
        Me.ButtonDeleteMediaLifeCycle = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAddMediaLifeCycle = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonEditMediaLifeCycle = New DevExpress.XtraEditors.SimpleButton()
        Me.GridMediaLifeCycle = New System.Windows.Forms.DataGridView()
        Me.FirstWeekColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.LastWeekColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.LabelHeadingLifeCycles = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl5 = New DevExpress.XtraEditors.LabelControl()
        Me.PanelDetails = New System.Windows.Forms.Panel()
        Me.CheckEditMediaCostOverridable = New DevExpress.XtraEditors.CheckEdit()
        Me.CheckEditMediaCost = New DevExpress.XtraEditors.CheckEdit()
        Me.CheckEditPNPPcaStatus = New DevExpress.XtraEditors.CheckEdit()
        Me.MemoEditNotes = New DevExpress.XtraEditors.MemoEdit()
        Me.CheckEditCrossover = New DevExpress.XtraEditors.CheckEdit()
        Me.CheckEditHomesite = New DevExpress.XtraEditors.CheckEdit()
        Me.LabelNotes = New DevExpress.XtraEditors.LabelControl()
        Me.LabelMediaName = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditMediaName = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl6 = New DevExpress.XtraEditors.LabelControl()
        Me.TableMediaFamilyMembership = New System.Windows.Forms.TableLayoutPanel()
        Me.LabelControl3 = New DevExpress.XtraEditors.LabelControl()
        Me.GroupControlCompetingMediaServices = New DevExpress.XtraEditors.GroupControl()
        Me.LabelControl12 = New DevExpress.XtraEditors.LabelControl()
        Me.PictureClearSearchCompetingMedia = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearchCompetingMedia = New DevExpress.XtraEditors.PictureEdit()
        Me.TextEditSearchCompetingMediaServices = New DevExpress.XtraEditors.TextEdit()
        Me.GridCompetingMediaServices = New System.Windows.Forms.DataGridView()
        Me.CompetingMediaNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.GroupControlMediaFamilyMembership = New DevExpress.XtraEditors.GroupControl()
        Me.LabelControl11 = New DevExpress.XtraEditors.LabelControl()
        Me.ButtonRemoveMediaFamilyMembership = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAddMediaFamilyMembership = New DevExpress.XtraEditors.SimpleButton()
        Me.TextEditSearchMediaFamilyMembership = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearchMediaFamilyMembership = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearchMediaFamilyMembership = New DevExpress.XtraEditors.PictureEdit()
        Me.GridMediaFamilyMembership = New System.Windows.Forms.DataGridView()
        Me.MediaFamilyNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.LabelControl4 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl9 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl10 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.GroupControlMediaCategory = New DevExpress.XtraEditors.GroupControl()
        Me.LabelControl14 = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearchMediaCategory = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearchMediaCategory = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearchMediaCategory = New DevExpress.XtraEditors.PictureEdit()
        Me.ButtonRemoveMediaCategory = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAddMediaCategory = New DevExpress.XtraEditors.SimpleButton()
        Me.GridMediaCategory = New System.Windows.Forms.DataGridView()
        Me.CategoryNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl()
        Me.ButtonSave = New DevExpress.XtraEditors.SimpleButton()
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.XtraTabControl1 = New DevExpress.XtraTab.XtraTabControl()
        Me.TabPageDetails = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.TableLayoutPanelDetails = New System.Windows.Forms.TableLayoutPanel()
        Me.TabPageFamilies = New DevExpress.XtraTab.XtraTabPage()
        Me.PanelFamilies = New System.Windows.Forms.Panel()
        Me.TabPageCategories = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.TabPageLifeCycles = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel4 = New System.Windows.Forms.Panel()
        Me.TableLayoutPanel2 = New System.Windows.Forms.TableLayoutPanel()
        Me.XtraTabPage1 = New DevExpress.XtraTab.XtraTabPage()
        Me.GroupControlItems = New DevExpress.XtraEditors.GroupControl()
        Me.LabelControl8 = New DevExpress.XtraEditors.LabelControl()
        Me.TextMediaRulesSearch = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearchMediaRule = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearchMediaRule = New DevExpress.XtraEditors.PictureEdit()
        Me.GridMediaRule = New System.Windows.Forms.DataGridView()
        Me.DataGridViewTextBoxColumn1 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.ApplicableColumn = New System.Windows.Forms.DataGridViewCheckBoxColumn()
        Me.MediaRuleID = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.TabPageMediaCosts = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel3 = New System.Windows.Forms.Panel()
        Me.TableLayoutPanel3 = New System.Windows.Forms.TableLayoutPanel()
        Me.GroupControlMediaCosts = New DevExpress.XtraEditors.GroupControl()
        Me.LabelControl13 = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearchMediaCost = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearchMediaCost = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearchMediaCost = New DevExpress.XtraEditors.PictureEdit()
        Me.ButtonDeleteMediaCost = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAddMediaCost = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonEditMediaCost = New DevExpress.XtraEditors.SimpleButton()
        Me.GridMediaCosts = New System.Windows.Forms.DataGridView()
        Me.IsPercentageColumn = New System.Windows.Forms.DataGridViewCheckBoxColumn()
        Me.CostPercentageColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.CostPriceColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.EffectiveDateColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.LabelControlMediaCostConetent = New DevExpress.XtraEditors.LabelControl()
        Me.LabelHeadingMediaCosts = New DevExpress.XtraEditors.LabelControl()
        CType(Me.GroupControlMediaLifeCycle, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlMediaLifeCycle.SuspendLayout()
        CType(Me.TextEditSearchMediaLifeCycle.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearchMediaLifeCycle.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearchMediaLifeCycle.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridMediaLifeCycle, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelDetails.SuspendLayout()
        CType(Me.CheckEditMediaCostOverridable.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditMediaCost.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditPNPPcaStatus.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.MemoEditNotes.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditCrossover.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditHomesite.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditMediaName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TableMediaFamilyMembership.SuspendLayout()
        CType(Me.GroupControlCompetingMediaServices, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlCompetingMediaServices.SuspendLayout()
        CType(Me.PictureClearSearchCompetingMedia.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearchCompetingMedia.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditSearchCompetingMediaServices.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridCompetingMediaServices, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControlMediaFamilyMembership, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlMediaFamilyMembership.SuspendLayout()
        CType(Me.TextEditSearchMediaFamilyMembership.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearchMediaFamilyMembership.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearchMediaFamilyMembership.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridMediaFamilyMembership, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControlMediaCategory, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlMediaCategory.SuspendLayout()
        CType(Me.TextEditSearchMediaCategory.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearchMediaCategory.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearchMediaCategory.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridMediaCategory, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabControl1.SuspendLayout()
        Me.TabPageDetails.SuspendLayout()
        Me.Panel1.SuspendLayout()
        Me.TableLayoutPanelDetails.SuspendLayout()
        Me.TabPageFamilies.SuspendLayout()
        Me.PanelFamilies.SuspendLayout()
        Me.TabPageCategories.SuspendLayout()
        Me.Panel2.SuspendLayout()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.TabPageLifeCycles.SuspendLayout()
        Me.Panel4.SuspendLayout()
        Me.TableLayoutPanel2.SuspendLayout()
        Me.XtraTabPage1.SuspendLayout()
        CType(Me.GroupControlItems, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlItems.SuspendLayout()
        CType(Me.TextMediaRulesSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearchMediaRule.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearchMediaRule.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridMediaRule, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabPageMediaCosts.SuspendLayout()
        Me.Panel3.SuspendLayout()
        Me.TableLayoutPanel3.SuspendLayout()
        CType(Me.GroupControlMediaCosts, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlMediaCosts.SuspendLayout()
        CType(Me.TextEditSearchMediaCost.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearchMediaCost.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearchMediaCost.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridMediaCosts, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Margin = New System.Windows.Forms.Padding(5, 5, 5, 21)
        Me.LabelTitle.Size = New System.Drawing.Size(186, 37)
        Me.LabelTitle.TabIndex = 0
        Me.LabelTitle.Text = "(new media)"
        '
        'ImageList16x16
        '
        Me.ImageList16x16.ImageStream = CType(resources.GetObject("ImageList16x16.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList16x16.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList16x16.Images.SetKeyName(0, "add.png")
        Me.ImageList16x16.Images.SetKeyName(1, "pencil.png")
        Me.ImageList16x16.Images.SetKeyName(2, "delete.png")
        '
        'LabelControlNewsContent
        '
        Me.LabelControlNewsContent.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControlNewsContent.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControlNewsContent.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControlNewsContent.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top
        Me.LabelControlNewsContent.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap
        Me.LabelControlNewsContent.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelControlNewsContent.Location = New System.Drawing.Point(4, 48)
        Me.LabelControlNewsContent.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelControlNewsContent.Name = "LabelControlNewsContent"
        Me.LabelControlNewsContent.Size = New System.Drawing.Size(972, 51)
        Me.LabelControlNewsContent.TabIndex = 0
        Me.LabelControlNewsContent.Text = resources.GetString("LabelControlNewsContent.Text")
        '
        'GroupControlMediaLifeCycle
        '
        Me.GroupControlMediaLifeCycle.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlMediaLifeCycle.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlMediaLifeCycle.Appearance.Options.UseFont = True
        Me.GroupControlMediaLifeCycle.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlMediaLifeCycle.AppearanceCaption.Options.UseFont = True
        Me.GroupControlMediaLifeCycle.Controls.Add(Me.LabelControl7)
        Me.GroupControlMediaLifeCycle.Controls.Add(Me.TextEditSearchMediaLifeCycle)
        Me.GroupControlMediaLifeCycle.Controls.Add(Me.PictureClearSearchMediaLifeCycle)
        Me.GroupControlMediaLifeCycle.Controls.Add(Me.PictureAdvancedSearchMediaLifeCycle)
        Me.GroupControlMediaLifeCycle.Controls.Add(Me.ButtonDeleteMediaLifeCycle)
        Me.GroupControlMediaLifeCycle.Controls.Add(Me.ButtonAddMediaLifeCycle)
        Me.GroupControlMediaLifeCycle.Controls.Add(Me.ButtonEditMediaLifeCycle)
        Me.GroupControlMediaLifeCycle.Controls.Add(Me.GridMediaLifeCycle)
        Me.GroupControlMediaLifeCycle.Location = New System.Drawing.Point(4, 119)
        Me.GroupControlMediaLifeCycle.LookAndFeel.SkinName = "Black"
        Me.GroupControlMediaLifeCycle.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlMediaLifeCycle.Margin = New System.Windows.Forms.Padding(4)
        Me.GroupControlMediaLifeCycle.Name = "GroupControlMediaLifeCycle"
        Me.GroupControlMediaLifeCycle.Size = New System.Drawing.Size(972, 367)
        Me.GroupControlMediaLifeCycle.TabIndex = 1
        Me.GroupControlMediaLifeCycle.Text = "Life Cycle List"
        '
        'LabelControl7
        '
        Me.LabelControl7.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl7.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl7.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl7.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl7.Location = New System.Drawing.Point(769, 336)
        Me.LabelControl7.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelControl7.Name = "LabelControl7"
        Me.LabelControl7.Size = New System.Drawing.Size(58, 17)
        Me.LabelControl7.TabIndex = 5
        Me.LabelControl7.Text = "Search:"
        '
        'TextEditSearchMediaLifeCycle
        '
        Me.TextEditSearchMediaLifeCycle.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchMediaLifeCycle.EditValue = ""
        Me.TextEditSearchMediaLifeCycle.Location = New System.Drawing.Point(834, 332)
        Me.TextEditSearchMediaLifeCycle.Margin = New System.Windows.Forms.Padding(4, 5, 4, 7)
        Me.TextEditSearchMediaLifeCycle.Name = "TextEditSearchMediaLifeCycle"
        Me.TextEditSearchMediaLifeCycle.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchMediaLifeCycle.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchMediaLifeCycle.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchMediaLifeCycle.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchMediaLifeCycle.Size = New System.Drawing.Size(103, 24)
        Me.TextEditSearchMediaLifeCycle.TabIndex = 6
        '
        'PictureClearSearchMediaLifeCycle
        '
        Me.PictureClearSearchMediaLifeCycle.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchMediaLifeCycle.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchMediaLifeCycle.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchMediaLifeCycle.Location = New System.Drawing.Point(945, 4)
        Me.PictureClearSearchMediaLifeCycle.Margin = New System.Windows.Forms.Padding(4)
        Me.PictureClearSearchMediaLifeCycle.Name = "PictureClearSearchMediaLifeCycle"
        Me.PictureClearSearchMediaLifeCycle.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchMediaLifeCycle.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchMediaLifeCycle.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchMediaLifeCycle.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem1.Text = "Clear Search"
        ToolTipItem1.LeftIndent = 6
        ToolTipItem1.Text = "Click here to clear all search boxes."
        SuperToolTip1.Items.Add(ToolTipTitleItem1)
        SuperToolTip1.Items.Add(ToolTipItem1)
        Me.PictureClearSearchMediaLifeCycle.SuperTip = SuperToolTip1
        Me.PictureClearSearchMediaLifeCycle.TabIndex = 0
        Me.PictureClearSearchMediaLifeCycle.TabStop = True
        '
        'PictureAdvancedSearchMediaLifeCycle
        '
        Me.PictureAdvancedSearchMediaLifeCycle.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearchMediaLifeCycle.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearchMediaLifeCycle.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearchMediaLifeCycle.Location = New System.Drawing.Point(945, 334)
        Me.PictureAdvancedSearchMediaLifeCycle.Margin = New System.Windows.Forms.Padding(4, 8, 4, 9)
        Me.PictureAdvancedSearchMediaLifeCycle.Name = "PictureAdvancedSearchMediaLifeCycle"
        Me.PictureAdvancedSearchMediaLifeCycle.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearchMediaLifeCycle.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearchMediaLifeCycle.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearchMediaLifeCycle.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem2.Text = "Advanced Search"
        ToolTipItem2.LeftIndent = 6
        ToolTipItem2.Text = "Click here to search individual column values."
        SuperToolTip2.Items.Add(ToolTipTitleItem2)
        SuperToolTip2.Items.Add(ToolTipItem2)
        Me.PictureAdvancedSearchMediaLifeCycle.SuperTip = SuperToolTip2
        Me.PictureAdvancedSearchMediaLifeCycle.TabIndex = 7
        Me.PictureAdvancedSearchMediaLifeCycle.TabStop = True
        '
        'ButtonDeleteMediaLifeCycle
        '
        Me.ButtonDeleteMediaLifeCycle.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonDeleteMediaLifeCycle.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonDeleteMediaLifeCycle.Appearance.Options.UseFont = True
        Me.ButtonDeleteMediaLifeCycle.ImageIndex = 2
        Me.ButtonDeleteMediaLifeCycle.ImageList = Me.ImageList16x16
        Me.ButtonDeleteMediaLifeCycle.Location = New System.Drawing.Point(215, 330)
        Me.ButtonDeleteMediaLifeCycle.LookAndFeel.SkinName = "Black"
        Me.ButtonDeleteMediaLifeCycle.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonDeleteMediaLifeCycle.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonDeleteMediaLifeCycle.Name = "ButtonDeleteMediaLifeCycle"
        Me.ButtonDeleteMediaLifeCycle.Size = New System.Drawing.Size(96, 30)
        Me.ButtonDeleteMediaLifeCycle.TabIndex = 4
        Me.ButtonDeleteMediaLifeCycle.Text = "Delete"
        '
        'ButtonAddMediaLifeCycle
        '
        Me.ButtonAddMediaLifeCycle.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAddMediaLifeCycle.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAddMediaLifeCycle.Appearance.Options.UseFont = True
        Me.ButtonAddMediaLifeCycle.ImageIndex = 0
        Me.ButtonAddMediaLifeCycle.ImageList = Me.ImageList16x16
        Me.ButtonAddMediaLifeCycle.Location = New System.Drawing.Point(6, 330)
        Me.ButtonAddMediaLifeCycle.LookAndFeel.SkinName = "Black"
        Me.ButtonAddMediaLifeCycle.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAddMediaLifeCycle.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonAddMediaLifeCycle.Name = "ButtonAddMediaLifeCycle"
        Me.ButtonAddMediaLifeCycle.Size = New System.Drawing.Size(96, 30)
        Me.ButtonAddMediaLifeCycle.TabIndex = 2
        Me.ButtonAddMediaLifeCycle.Text = "Add"
        '
        'ButtonEditMediaLifeCycle
        '
        Me.ButtonEditMediaLifeCycle.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonEditMediaLifeCycle.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonEditMediaLifeCycle.Appearance.Options.UseFont = True
        Me.ButtonEditMediaLifeCycle.ImageIndex = 1
        Me.ButtonEditMediaLifeCycle.ImageList = Me.ImageList16x16
        Me.ButtonEditMediaLifeCycle.Location = New System.Drawing.Point(111, 330)
        Me.ButtonEditMediaLifeCycle.LookAndFeel.SkinName = "Black"
        Me.ButtonEditMediaLifeCycle.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonEditMediaLifeCycle.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonEditMediaLifeCycle.Name = "ButtonEditMediaLifeCycle"
        Me.ButtonEditMediaLifeCycle.Size = New System.Drawing.Size(96, 30)
        Me.ButtonEditMediaLifeCycle.TabIndex = 3
        Me.ButtonEditMediaLifeCycle.Text = "Edit"
        '
        'GridMediaLifeCycle
        '
        Me.GridMediaLifeCycle.AllowUserToAddRows = False
        Me.GridMediaLifeCycle.AllowUserToDeleteRows = False
        Me.GridMediaLifeCycle.AllowUserToOrderColumns = True
        Me.GridMediaLifeCycle.AllowUserToResizeRows = False
        DataGridViewCellStyle1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridMediaLifeCycle.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle1
        Me.GridMediaLifeCycle.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridMediaLifeCycle.BackgroundColor = System.Drawing.Color.White
        Me.GridMediaLifeCycle.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridMediaLifeCycle.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridMediaLifeCycle.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle2.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle2.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridMediaLifeCycle.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle2
        Me.GridMediaLifeCycle.ColumnHeadersHeight = 22
        Me.GridMediaLifeCycle.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridMediaLifeCycle.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.FirstWeekColumn, Me.LastWeekColumn})
        Me.GridMediaLifeCycle.EnableHeadersVisualStyles = False
        Me.GridMediaLifeCycle.GridColor = System.Drawing.Color.White
        Me.GridMediaLifeCycle.Location = New System.Drawing.Point(3, 29)
        Me.GridMediaLifeCycle.Margin = New System.Windows.Forms.Padding(4)
        Me.GridMediaLifeCycle.Name = "GridMediaLifeCycle"
        Me.GridMediaLifeCycle.ReadOnly = True
        Me.GridMediaLifeCycle.RowHeadersVisible = False
        Me.GridMediaLifeCycle.RowHeadersWidth = 51
        DataGridViewCellStyle5.ForeColor = System.Drawing.Color.DimGray
        Me.GridMediaLifeCycle.RowsDefaultCellStyle = DataGridViewCellStyle5
        Me.GridMediaLifeCycle.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridMediaLifeCycle.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridMediaLifeCycle.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridMediaLifeCycle.RowTemplate.Height = 19
        Me.GridMediaLifeCycle.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridMediaLifeCycle.ShowCellToolTips = False
        Me.GridMediaLifeCycle.Size = New System.Drawing.Size(967, 294)
        Me.GridMediaLifeCycle.StandardTab = True
        Me.GridMediaLifeCycle.TabIndex = 1
        '
        'FirstWeekColumn
        '
        Me.FirstWeekColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.FirstWeekColumn.DataPropertyName = "FirstWeek"
        DataGridViewCellStyle3.Format = "D"
        DataGridViewCellStyle3.NullValue = Nothing
        Me.FirstWeekColumn.DefaultCellStyle = DataGridViewCellStyle3
        Me.FirstWeekColumn.HeaderText = "Start"
        Me.FirstWeekColumn.MinimumWidth = 6
        Me.FirstWeekColumn.Name = "FirstWeekColumn"
        Me.FirstWeekColumn.ReadOnly = True
        '
        'LastWeekColumn
        '
        Me.LastWeekColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.LastWeekColumn.DataPropertyName = "LastWeek"
        DataGridViewCellStyle4.Format = "D"
        DataGridViewCellStyle4.NullValue = Nothing
        Me.LastWeekColumn.DefaultCellStyle = DataGridViewCellStyle4
        Me.LastWeekColumn.HeaderText = "Finish"
        Me.LastWeekColumn.MinimumWidth = 6
        Me.LastWeekColumn.Name = "LastWeekColumn"
        Me.LastWeekColumn.ReadOnly = True
        Me.LastWeekColumn.Resizable = System.Windows.Forms.DataGridViewTriState.[True]
        '
        'LabelHeadingLifeCycles
        '
        Me.LabelHeadingLifeCycles.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelHeadingLifeCycles.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelHeadingLifeCycles.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelHeadingLifeCycles.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelHeadingLifeCycles.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelHeadingLifeCycles.LineVisible = True
        Me.LabelHeadingLifeCycles.Location = New System.Drawing.Point(4, 4)
        Me.LabelHeadingLifeCycles.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelHeadingLifeCycles.Name = "LabelHeadingLifeCycles"
        Me.LabelHeadingLifeCycles.Size = New System.Drawing.Size(972, 24)
        Me.LabelHeadingLifeCycles.TabIndex = 0
        Me.LabelHeadingLifeCycles.Text = "Media Service Life Cycle Management"
        '
        'LabelControl5
        '
        Me.LabelControl5.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl5.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl5.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl5.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top
        Me.LabelControl5.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap
        Me.LabelControl5.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelControl5.Location = New System.Drawing.Point(4, 48)
        Me.LabelControl5.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelControl5.Name = "LabelControl5"
        Me.LabelControl5.Size = New System.Drawing.Size(972, 51)
        Me.LabelControl5.TabIndex = 0
        Me.LabelControl5.Text = resources.GetString("LabelControl5.Text")
        '
        'PanelDetails
        '
        Me.PanelDetails.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelDetails.Controls.Add(Me.CheckEditMediaCostOverridable)
        Me.PanelDetails.Controls.Add(Me.CheckEditMediaCost)
        Me.PanelDetails.Controls.Add(Me.CheckEditPNPPcaStatus)
        Me.PanelDetails.Controls.Add(Me.MemoEditNotes)
        Me.PanelDetails.Controls.Add(Me.CheckEditCrossover)
        Me.PanelDetails.Controls.Add(Me.CheckEditHomesite)
        Me.PanelDetails.Controls.Add(Me.LabelNotes)
        Me.PanelDetails.Controls.Add(Me.LabelMediaName)
        Me.PanelDetails.Controls.Add(Me.TextEditMediaName)
        Me.PanelDetails.Location = New System.Drawing.Point(0, 115)
        Me.PanelDetails.Margin = New System.Windows.Forms.Padding(0)
        Me.PanelDetails.Name = "PanelDetails"
        Me.PanelDetails.Size = New System.Drawing.Size(980, 375)
        Me.PanelDetails.TabIndex = 1
        '
        'CheckEditMediaCostOverridable
        '
        Me.CheckEditMediaCostOverridable.Location = New System.Drawing.Point(185, 288)
        Me.CheckEditMediaCostOverridable.Margin = New System.Windows.Forms.Padding(4)
        Me.CheckEditMediaCostOverridable.Name = "CheckEditMediaCostOverridable"
        Me.CheckEditMediaCostOverridable.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditMediaCostOverridable.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditMediaCostOverridable.Properties.Appearance.Options.UseFont = True
        Me.CheckEditMediaCostOverridable.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditMediaCostOverridable.Properties.AutoWidth = True
        Me.CheckEditMediaCostOverridable.Properties.Caption = "Overridable Media Cost"
        Me.CheckEditMediaCostOverridable.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditMediaCostOverridable.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditMediaCostOverridable.Size = New System.Drawing.Size(183, 21)
        Me.CheckEditMediaCostOverridable.TabIndex = 8
        '
        'CheckEditMediaCost
        '
        Me.CheckEditMediaCost.Location = New System.Drawing.Point(185, 259)
        Me.CheckEditMediaCost.Margin = New System.Windows.Forms.Padding(4)
        Me.CheckEditMediaCost.Name = "CheckEditMediaCost"
        Me.CheckEditMediaCost.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditMediaCost.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditMediaCost.Properties.Appearance.Options.UseFont = True
        Me.CheckEditMediaCost.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditMediaCost.Properties.AutoWidth = True
        Me.CheckEditMediaCost.Properties.Caption = "Has Media Cost"
        Me.CheckEditMediaCost.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditMediaCost.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditMediaCost.Size = New System.Drawing.Size(130, 21)
        Me.CheckEditMediaCost.TabIndex = 7
        '
        'CheckEditPNPPcaStatus
        '
        Me.CheckEditPNPPcaStatus.Location = New System.Drawing.Point(185, 230)
        Me.CheckEditPNPPcaStatus.Margin = New System.Windows.Forms.Padding(4)
        Me.CheckEditPNPPcaStatus.Name = "CheckEditPNPPcaStatus"
        Me.CheckEditPNPPcaStatus.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditPNPPcaStatus.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditPNPPcaStatus.Properties.Appearance.Options.UseFont = True
        Me.CheckEditPNPPcaStatus.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditPNPPcaStatus.Properties.AutoWidth = True
        Me.CheckEditPNPPcaStatus.Properties.Caption = "Artwork Status"
        Me.CheckEditPNPPcaStatus.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditPNPPcaStatus.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditPNPPcaStatus.Size = New System.Drawing.Size(130, 21)
        Me.CheckEditPNPPcaStatus.TabIndex = 6
        '
        'MemoEditNotes
        '
        Me.MemoEditNotes.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.MemoEditNotes.Location = New System.Drawing.Point(188, 38)
        Me.MemoEditNotes.Margin = New System.Windows.Forms.Padding(4)
        Me.MemoEditNotes.Name = "MemoEditNotes"
        Me.MemoEditNotes.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.MemoEditNotes.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.MemoEditNotes.Properties.Appearance.Options.UseFont = True
        Me.MemoEditNotes.Properties.Appearance.Options.UseForeColor = True
        Me.MemoEditNotes.Properties.LookAndFeel.SkinName = "Black"
        Me.MemoEditNotes.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.MemoEditNotes.Properties.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.MemoEditNotes.Size = New System.Drawing.Size(792, 124)
        Me.MemoEditNotes.TabIndex = 3
        '
        'CheckEditCrossover
        '
        Me.CheckEditCrossover.Location = New System.Drawing.Point(185, 201)
        Me.CheckEditCrossover.Margin = New System.Windows.Forms.Padding(4)
        Me.CheckEditCrossover.Name = "CheckEditCrossover"
        Me.CheckEditCrossover.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditCrossover.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditCrossover.Properties.Appearance.Options.UseFont = True
        Me.CheckEditCrossover.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditCrossover.Properties.AutoWidth = True
        Me.CheckEditCrossover.Properties.Caption = "Crossover installations permitted"
        Me.CheckEditCrossover.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditCrossover.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditCrossover.Size = New System.Drawing.Size(255, 21)
        Me.CheckEditCrossover.TabIndex = 5
        '
        'CheckEditHomesite
        '
        Me.CheckEditHomesite.Location = New System.Drawing.Point(185, 170)
        Me.CheckEditHomesite.Margin = New System.Windows.Forms.Padding(4)
        Me.CheckEditHomesite.Name = "CheckEditHomesite"
        Me.CheckEditHomesite.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditHomesite.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditHomesite.Properties.Appearance.Options.UseFont = True
        Me.CheckEditHomesite.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditHomesite.Properties.AutoWidth = True
        Me.CheckEditHomesite.Properties.Caption = "Homesite installations permitted"
        Me.CheckEditHomesite.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditHomesite.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditHomesite.Size = New System.Drawing.Size(248, 21)
        Me.CheckEditHomesite.TabIndex = 4
        '
        'LabelNotes
        '
        Me.LabelNotes.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelNotes.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelNotes.Location = New System.Drawing.Point(0, 42)
        Me.LabelNotes.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelNotes.Name = "LabelNotes"
        Me.LabelNotes.Size = New System.Drawing.Size(47, 17)
        Me.LabelNotes.TabIndex = 2
        Me.LabelNotes.Text = "Notes:"
        '
        'LabelMediaName
        '
        Me.LabelMediaName.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelMediaName.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelMediaName.Location = New System.Drawing.Point(0, 8)
        Me.LabelMediaName.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelMediaName.Name = "LabelMediaName"
        Me.LabelMediaName.Size = New System.Drawing.Size(89, 17)
        Me.LabelMediaName.TabIndex = 0
        Me.LabelMediaName.Text = "Media Name:"
        '
        'TextEditMediaName
        '
        Me.TextEditMediaName.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditMediaName.Location = New System.Drawing.Point(188, 4)
        Me.TextEditMediaName.Margin = New System.Windows.Forms.Padding(4)
        Me.TextEditMediaName.Name = "TextEditMediaName"
        Me.TextEditMediaName.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditMediaName.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditMediaName.Properties.Appearance.Options.UseFont = True
        Me.TextEditMediaName.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditMediaName.Properties.MaxLength = 200
        Me.TextEditMediaName.Size = New System.Drawing.Size(537, 24)
        Me.TextEditMediaName.TabIndex = 1
        '
        'LabelControl6
        '
        Me.LabelControl6.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl6.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl6.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelControl6.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl6.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelControl6.LineVisible = True
        Me.LabelControl6.Location = New System.Drawing.Point(4, 4)
        Me.LabelControl6.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelControl6.Name = "LabelControl6"
        Me.LabelControl6.Size = New System.Drawing.Size(972, 24)
        Me.LabelControl6.TabIndex = 0
        Me.LabelControl6.Text = "Media Service Details"
        '
        'TableMediaFamilyMembership
        '
        Me.TableMediaFamilyMembership.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableMediaFamilyMembership.BackColor = System.Drawing.Color.Transparent
        Me.TableMediaFamilyMembership.ColumnCount = 2
        Me.TableMediaFamilyMembership.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50.0!))
        Me.TableMediaFamilyMembership.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50.0!))
        Me.TableMediaFamilyMembership.Controls.Add(Me.LabelControl3, 0, 1)
        Me.TableMediaFamilyMembership.Controls.Add(Me.GroupControlCompetingMediaServices, 1, 2)
        Me.TableMediaFamilyMembership.Controls.Add(Me.GroupControlMediaFamilyMembership, 0, 2)
        Me.TableMediaFamilyMembership.Controls.Add(Me.LabelControl4, 0, 0)
        Me.TableMediaFamilyMembership.Controls.Add(Me.LabelControl9, 1, 0)
        Me.TableMediaFamilyMembership.Controls.Add(Me.LabelControl10, 1, 1)
        Me.TableMediaFamilyMembership.Location = New System.Drawing.Point(15, 16)
        Me.TableMediaFamilyMembership.Margin = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.TableMediaFamilyMembership.Name = "TableMediaFamilyMembership"
        Me.TableMediaFamilyMembership.RowCount = 3
        Me.TableMediaFamilyMembership.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableMediaFamilyMembership.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableMediaFamilyMembership.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableMediaFamilyMembership.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 26.0!))
        Me.TableMediaFamilyMembership.Size = New System.Drawing.Size(980, 490)
        Me.TableMediaFamilyMembership.TabIndex = 0
        '
        'LabelControl3
        '
        Me.LabelControl3.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl3.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl3.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl3.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top
        Me.LabelControl3.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap
        Me.LabelControl3.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelControl3.Location = New System.Drawing.Point(4, 48)
        Me.LabelControl3.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelControl3.Name = "LabelControl3"
        Me.LabelControl3.Size = New System.Drawing.Size(482, 85)
        Me.LabelControl3.TabIndex = 1
        Me.LabelControl3.Text = resources.GetString("LabelControl3.Text")
        '
        'GroupControlCompetingMediaServices
        '
        Me.GroupControlCompetingMediaServices.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlCompetingMediaServices.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlCompetingMediaServices.Appearance.Options.UseFont = True
        Me.GroupControlCompetingMediaServices.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlCompetingMediaServices.AppearanceCaption.Options.UseFont = True
        Me.GroupControlCompetingMediaServices.Controls.Add(Me.LabelControl12)
        Me.GroupControlCompetingMediaServices.Controls.Add(Me.PictureClearSearchCompetingMedia)
        Me.GroupControlCompetingMediaServices.Controls.Add(Me.PictureAdvancedSearchCompetingMedia)
        Me.GroupControlCompetingMediaServices.Controls.Add(Me.TextEditSearchCompetingMediaServices)
        Me.GroupControlCompetingMediaServices.Controls.Add(Me.GridCompetingMediaServices)
        Me.GroupControlCompetingMediaServices.Location = New System.Drawing.Point(505, 153)
        Me.GroupControlCompetingMediaServices.LookAndFeel.SkinName = "Black"
        Me.GroupControlCompetingMediaServices.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlCompetingMediaServices.Margin = New System.Windows.Forms.Padding(15, 4, 4, 4)
        Me.GroupControlCompetingMediaServices.Name = "GroupControlCompetingMediaServices"
        Me.GroupControlCompetingMediaServices.Size = New System.Drawing.Size(471, 333)
        Me.GroupControlCompetingMediaServices.TabIndex = 5
        Me.GroupControlCompetingMediaServices.Text = "Competing Media Service List"
        '
        'LabelControl12
        '
        Me.LabelControl12.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl12.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl12.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl12.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl12.Location = New System.Drawing.Point(267, 302)
        Me.LabelControl12.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelControl12.Name = "LabelControl12"
        Me.LabelControl12.Size = New System.Drawing.Size(58, 17)
        Me.LabelControl12.TabIndex = 2
        Me.LabelControl12.Text = "Search:"
        '
        'PictureClearSearchCompetingMedia
        '
        Me.PictureClearSearchCompetingMedia.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchCompetingMedia.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchCompetingMedia.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchCompetingMedia.Location = New System.Drawing.Point(444, 4)
        Me.PictureClearSearchCompetingMedia.Margin = New System.Windows.Forms.Padding(4)
        Me.PictureClearSearchCompetingMedia.Name = "PictureClearSearchCompetingMedia"
        Me.PictureClearSearchCompetingMedia.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchCompetingMedia.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchCompetingMedia.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchCompetingMedia.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem3.Text = "Clear Search"
        ToolTipItem3.LeftIndent = 6
        ToolTipItem3.Text = "Click here to clear all search boxes."
        SuperToolTip3.Items.Add(ToolTipTitleItem3)
        SuperToolTip3.Items.Add(ToolTipItem3)
        Me.PictureClearSearchCompetingMedia.SuperTip = SuperToolTip3
        Me.PictureClearSearchCompetingMedia.TabIndex = 0
        Me.PictureClearSearchCompetingMedia.TabStop = True
        '
        'PictureAdvancedSearchCompetingMedia
        '
        Me.PictureAdvancedSearchCompetingMedia.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearchCompetingMedia.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearchCompetingMedia.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearchCompetingMedia.Location = New System.Drawing.Point(444, 301)
        Me.PictureAdvancedSearchCompetingMedia.Margin = New System.Windows.Forms.Padding(4, 8, 4, 9)
        Me.PictureAdvancedSearchCompetingMedia.Name = "PictureAdvancedSearchCompetingMedia"
        Me.PictureAdvancedSearchCompetingMedia.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearchCompetingMedia.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearchCompetingMedia.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearchCompetingMedia.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem4.Text = "Advanced Search"
        ToolTipItem4.LeftIndent = 6
        ToolTipItem4.Text = "Click here to search individual column values."
        SuperToolTip4.Items.Add(ToolTipTitleItem4)
        SuperToolTip4.Items.Add(ToolTipItem4)
        Me.PictureAdvancedSearchCompetingMedia.SuperTip = SuperToolTip4
        Me.PictureAdvancedSearchCompetingMedia.TabIndex = 4
        Me.PictureAdvancedSearchCompetingMedia.TabStop = True
        '
        'TextEditSearchCompetingMediaServices
        '
        Me.TextEditSearchCompetingMediaServices.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchCompetingMediaServices.EditValue = ""
        Me.TextEditSearchCompetingMediaServices.Location = New System.Drawing.Point(333, 298)
        Me.TextEditSearchCompetingMediaServices.Margin = New System.Windows.Forms.Padding(4, 5, 4, 7)
        Me.TextEditSearchCompetingMediaServices.Name = "TextEditSearchCompetingMediaServices"
        Me.TextEditSearchCompetingMediaServices.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchCompetingMediaServices.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchCompetingMediaServices.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchCompetingMediaServices.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchCompetingMediaServices.Size = New System.Drawing.Size(103, 24)
        Me.TextEditSearchCompetingMediaServices.TabIndex = 3
        '
        'GridCompetingMediaServices
        '
        Me.GridCompetingMediaServices.AllowUserToAddRows = False
        Me.GridCompetingMediaServices.AllowUserToDeleteRows = False
        Me.GridCompetingMediaServices.AllowUserToOrderColumns = True
        Me.GridCompetingMediaServices.AllowUserToResizeRows = False
        DataGridViewCellStyle6.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridCompetingMediaServices.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle6
        Me.GridCompetingMediaServices.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridCompetingMediaServices.BackgroundColor = System.Drawing.Color.White
        Me.GridCompetingMediaServices.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridCompetingMediaServices.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridCompetingMediaServices.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle7.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle7.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle7.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle7.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle7.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle7.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle7.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridCompetingMediaServices.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle7
        Me.GridCompetingMediaServices.ColumnHeadersHeight = 22
        Me.GridCompetingMediaServices.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridCompetingMediaServices.ColumnHeadersVisible = False
        Me.GridCompetingMediaServices.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.CompetingMediaNameColumn})
        Me.GridCompetingMediaServices.EnableHeadersVisualStyles = False
        Me.GridCompetingMediaServices.GridColor = System.Drawing.Color.White
        Me.GridCompetingMediaServices.Location = New System.Drawing.Point(3, 29)
        Me.GridCompetingMediaServices.Margin = New System.Windows.Forms.Padding(4)
        Me.GridCompetingMediaServices.Name = "GridCompetingMediaServices"
        Me.GridCompetingMediaServices.ReadOnly = True
        Me.GridCompetingMediaServices.RowHeadersVisible = False
        Me.GridCompetingMediaServices.RowHeadersWidth = 51
        DataGridViewCellStyle8.ForeColor = System.Drawing.Color.DimGray
        Me.GridCompetingMediaServices.RowsDefaultCellStyle = DataGridViewCellStyle8
        Me.GridCompetingMediaServices.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridCompetingMediaServices.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridCompetingMediaServices.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridCompetingMediaServices.RowTemplate.Height = 19
        Me.GridCompetingMediaServices.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridCompetingMediaServices.ShowCellToolTips = False
        Me.GridCompetingMediaServices.Size = New System.Drawing.Size(465, 260)
        Me.GridCompetingMediaServices.StandardTab = True
        Me.GridCompetingMediaServices.TabIndex = 1
        '
        'CompetingMediaNameColumn
        '
        Me.CompetingMediaNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.CompetingMediaNameColumn.DataPropertyName = "CompetingMediaName"
        Me.CompetingMediaNameColumn.HeaderText = "Media Service"
        Me.CompetingMediaNameColumn.MinimumWidth = 6
        Me.CompetingMediaNameColumn.Name = "CompetingMediaNameColumn"
        Me.CompetingMediaNameColumn.ReadOnly = True
        '
        'GroupControlMediaFamilyMembership
        '
        Me.GroupControlMediaFamilyMembership.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlMediaFamilyMembership.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlMediaFamilyMembership.Appearance.Options.UseFont = True
        Me.GroupControlMediaFamilyMembership.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlMediaFamilyMembership.AppearanceCaption.Options.UseFont = True
        Me.GroupControlMediaFamilyMembership.Controls.Add(Me.LabelControl11)
        Me.GroupControlMediaFamilyMembership.Controls.Add(Me.ButtonRemoveMediaFamilyMembership)
        Me.GroupControlMediaFamilyMembership.Controls.Add(Me.ButtonAddMediaFamilyMembership)
        Me.GroupControlMediaFamilyMembership.Controls.Add(Me.TextEditSearchMediaFamilyMembership)
        Me.GroupControlMediaFamilyMembership.Controls.Add(Me.PictureClearSearchMediaFamilyMembership)
        Me.GroupControlMediaFamilyMembership.Controls.Add(Me.PictureAdvancedSearchMediaFamilyMembership)
        Me.GroupControlMediaFamilyMembership.Controls.Add(Me.GridMediaFamilyMembership)
        Me.GroupControlMediaFamilyMembership.Location = New System.Drawing.Point(4, 153)
        Me.GroupControlMediaFamilyMembership.LookAndFeel.SkinName = "Black"
        Me.GroupControlMediaFamilyMembership.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlMediaFamilyMembership.Margin = New System.Windows.Forms.Padding(4)
        Me.GroupControlMediaFamilyMembership.Name = "GroupControlMediaFamilyMembership"
        Me.GroupControlMediaFamilyMembership.Size = New System.Drawing.Size(482, 333)
        Me.GroupControlMediaFamilyMembership.TabIndex = 2
        Me.GroupControlMediaFamilyMembership.Text = "Media Family Membership List"
        '
        'LabelControl11
        '
        Me.LabelControl11.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl11.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl11.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl11.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl11.Location = New System.Drawing.Point(279, 302)
        Me.LabelControl11.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelControl11.Name = "LabelControl11"
        Me.LabelControl11.Size = New System.Drawing.Size(58, 17)
        Me.LabelControl11.TabIndex = 4
        Me.LabelControl11.Text = "Search:"
        '
        'ButtonRemoveMediaFamilyMembership
        '
        Me.ButtonRemoveMediaFamilyMembership.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonRemoveMediaFamilyMembership.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonRemoveMediaFamilyMembership.Appearance.Options.UseFont = True
        Me.ButtonRemoveMediaFamilyMembership.ImageIndex = 2
        Me.ButtonRemoveMediaFamilyMembership.ImageList = Me.ImageList16x16
        Me.ButtonRemoveMediaFamilyMembership.Location = New System.Drawing.Point(111, 297)
        Me.ButtonRemoveMediaFamilyMembership.LookAndFeel.SkinName = "Black"
        Me.ButtonRemoveMediaFamilyMembership.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonRemoveMediaFamilyMembership.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonRemoveMediaFamilyMembership.Name = "ButtonRemoveMediaFamilyMembership"
        Me.ButtonRemoveMediaFamilyMembership.Size = New System.Drawing.Size(96, 30)
        Me.ButtonRemoveMediaFamilyMembership.TabIndex = 3
        Me.ButtonRemoveMediaFamilyMembership.Text = "Remove"
        '
        'ButtonAddMediaFamilyMembership
        '
        Me.ButtonAddMediaFamilyMembership.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAddMediaFamilyMembership.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAddMediaFamilyMembership.Appearance.Options.UseFont = True
        Me.ButtonAddMediaFamilyMembership.ImageIndex = 0
        Me.ButtonAddMediaFamilyMembership.ImageList = Me.ImageList16x16
        Me.ButtonAddMediaFamilyMembership.Location = New System.Drawing.Point(6, 297)
        Me.ButtonAddMediaFamilyMembership.LookAndFeel.SkinName = "Black"
        Me.ButtonAddMediaFamilyMembership.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAddMediaFamilyMembership.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonAddMediaFamilyMembership.Name = "ButtonAddMediaFamilyMembership"
        Me.ButtonAddMediaFamilyMembership.Size = New System.Drawing.Size(96, 30)
        Me.ButtonAddMediaFamilyMembership.TabIndex = 2
        Me.ButtonAddMediaFamilyMembership.Text = "Add"
        '
        'TextEditSearchMediaFamilyMembership
        '
        Me.TextEditSearchMediaFamilyMembership.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchMediaFamilyMembership.EditValue = ""
        Me.TextEditSearchMediaFamilyMembership.Location = New System.Drawing.Point(345, 298)
        Me.TextEditSearchMediaFamilyMembership.Margin = New System.Windows.Forms.Padding(4, 5, 4, 7)
        Me.TextEditSearchMediaFamilyMembership.Name = "TextEditSearchMediaFamilyMembership"
        Me.TextEditSearchMediaFamilyMembership.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchMediaFamilyMembership.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchMediaFamilyMembership.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchMediaFamilyMembership.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchMediaFamilyMembership.Size = New System.Drawing.Size(103, 24)
        Me.TextEditSearchMediaFamilyMembership.TabIndex = 5
        '
        'PictureClearSearchMediaFamilyMembership
        '
        Me.PictureClearSearchMediaFamilyMembership.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchMediaFamilyMembership.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchMediaFamilyMembership.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchMediaFamilyMembership.Location = New System.Drawing.Point(455, 4)
        Me.PictureClearSearchMediaFamilyMembership.Margin = New System.Windows.Forms.Padding(4)
        Me.PictureClearSearchMediaFamilyMembership.Name = "PictureClearSearchMediaFamilyMembership"
        Me.PictureClearSearchMediaFamilyMembership.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchMediaFamilyMembership.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchMediaFamilyMembership.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchMediaFamilyMembership.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem5.Text = "Clear Search"
        ToolTipItem5.LeftIndent = 6
        ToolTipItem5.Text = "Click here to clear all search boxes."
        SuperToolTip5.Items.Add(ToolTipTitleItem5)
        SuperToolTip5.Items.Add(ToolTipItem5)
        Me.PictureClearSearchMediaFamilyMembership.SuperTip = SuperToolTip5
        Me.PictureClearSearchMediaFamilyMembership.TabIndex = 0
        Me.PictureClearSearchMediaFamilyMembership.TabStop = True
        '
        'PictureAdvancedSearchMediaFamilyMembership
        '
        Me.PictureAdvancedSearchMediaFamilyMembership.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearchMediaFamilyMembership.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearchMediaFamilyMembership.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearchMediaFamilyMembership.Location = New System.Drawing.Point(455, 301)
        Me.PictureAdvancedSearchMediaFamilyMembership.Margin = New System.Windows.Forms.Padding(4, 8, 4, 9)
        Me.PictureAdvancedSearchMediaFamilyMembership.Name = "PictureAdvancedSearchMediaFamilyMembership"
        Me.PictureAdvancedSearchMediaFamilyMembership.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearchMediaFamilyMembership.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearchMediaFamilyMembership.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearchMediaFamilyMembership.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem6.Text = "Advanced Search"
        ToolTipItem6.LeftIndent = 6
        ToolTipItem6.Text = "Click here to search individual column values."
        SuperToolTip6.Items.Add(ToolTipTitleItem6)
        SuperToolTip6.Items.Add(ToolTipItem6)
        Me.PictureAdvancedSearchMediaFamilyMembership.SuperTip = SuperToolTip6
        Me.PictureAdvancedSearchMediaFamilyMembership.TabIndex = 6
        Me.PictureAdvancedSearchMediaFamilyMembership.TabStop = True
        '
        'GridMediaFamilyMembership
        '
        Me.GridMediaFamilyMembership.AllowUserToAddRows = False
        Me.GridMediaFamilyMembership.AllowUserToDeleteRows = False
        Me.GridMediaFamilyMembership.AllowUserToOrderColumns = True
        Me.GridMediaFamilyMembership.AllowUserToResizeRows = False
        DataGridViewCellStyle9.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridMediaFamilyMembership.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle9
        Me.GridMediaFamilyMembership.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridMediaFamilyMembership.BackgroundColor = System.Drawing.Color.White
        Me.GridMediaFamilyMembership.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridMediaFamilyMembership.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridMediaFamilyMembership.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle10.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle10.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle10.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle10.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle10.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle10.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle10.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridMediaFamilyMembership.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle10
        Me.GridMediaFamilyMembership.ColumnHeadersHeight = 22
        Me.GridMediaFamilyMembership.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridMediaFamilyMembership.ColumnHeadersVisible = False
        Me.GridMediaFamilyMembership.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.MediaFamilyNameColumn})
        Me.GridMediaFamilyMembership.EnableHeadersVisualStyles = False
        Me.GridMediaFamilyMembership.GridColor = System.Drawing.Color.White
        Me.GridMediaFamilyMembership.Location = New System.Drawing.Point(3, 29)
        Me.GridMediaFamilyMembership.Margin = New System.Windows.Forms.Padding(4)
        Me.GridMediaFamilyMembership.Name = "GridMediaFamilyMembership"
        Me.GridMediaFamilyMembership.ReadOnly = True
        Me.GridMediaFamilyMembership.RowHeadersVisible = False
        Me.GridMediaFamilyMembership.RowHeadersWidth = 51
        DataGridViewCellStyle11.ForeColor = System.Drawing.Color.DimGray
        Me.GridMediaFamilyMembership.RowsDefaultCellStyle = DataGridViewCellStyle11
        Me.GridMediaFamilyMembership.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridMediaFamilyMembership.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridMediaFamilyMembership.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridMediaFamilyMembership.RowTemplate.Height = 19
        Me.GridMediaFamilyMembership.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridMediaFamilyMembership.ShowCellToolTips = False
        Me.GridMediaFamilyMembership.Size = New System.Drawing.Size(477, 260)
        Me.GridMediaFamilyMembership.StandardTab = True
        Me.GridMediaFamilyMembership.TabIndex = 1
        '
        'MediaFamilyNameColumn
        '
        Me.MediaFamilyNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.MediaFamilyNameColumn.DataPropertyName = "MediaFamilyName"
        Me.MediaFamilyNameColumn.HeaderText = "Media Family"
        Me.MediaFamilyNameColumn.MinimumWidth = 6
        Me.MediaFamilyNameColumn.Name = "MediaFamilyNameColumn"
        Me.MediaFamilyNameColumn.ReadOnly = True
        '
        'LabelControl4
        '
        Me.LabelControl4.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl4.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl4.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelControl4.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl4.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelControl4.LineVisible = True
        Me.LabelControl4.Location = New System.Drawing.Point(4, 4)
        Me.LabelControl4.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelControl4.Name = "LabelControl4"
        Me.LabelControl4.Size = New System.Drawing.Size(482, 24)
        Me.LabelControl4.TabIndex = 0
        Me.LabelControl4.Text = "Media Family Memberships"
        '
        'LabelControl9
        '
        Me.LabelControl9.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl9.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl9.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelControl9.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl9.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelControl9.LineVisible = True
        Me.LabelControl9.Location = New System.Drawing.Point(505, 4)
        Me.LabelControl9.Margin = New System.Windows.Forms.Padding(15, 4, 4, 16)
        Me.LabelControl9.Name = "LabelControl9"
        Me.LabelControl9.Size = New System.Drawing.Size(471, 24)
        Me.LabelControl9.TabIndex = 3
        Me.LabelControl9.Text = "Competing Media Services"
        '
        'LabelControl10
        '
        Me.LabelControl10.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl10.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl10.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl10.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top
        Me.LabelControl10.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap
        Me.LabelControl10.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelControl10.Location = New System.Drawing.Point(505, 48)
        Me.LabelControl10.Margin = New System.Windows.Forms.Padding(15, 4, 4, 16)
        Me.LabelControl10.Name = "LabelControl10"
        Me.LabelControl10.Size = New System.Drawing.Size(471, 51)
        Me.LabelControl10.TabIndex = 4
        Me.LabelControl10.Text = "This list displays all media services which compete with this one. It automatical" &
    "ly refreshes based on the media family membership list on this page."
        '
        'LabelControl1
        '
        Me.LabelControl1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl1.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl1.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top
        Me.LabelControl1.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap
        Me.LabelControl1.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelControl1.Location = New System.Drawing.Point(4, 48)
        Me.LabelControl1.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(972, 51)
        Me.LabelControl1.TabIndex = 0
        Me.LabelControl1.Text = resources.GetString("LabelControl1.Text")
        '
        'GroupControlMediaCategory
        '
        Me.GroupControlMediaCategory.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlMediaCategory.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlMediaCategory.Appearance.Options.UseFont = True
        Me.GroupControlMediaCategory.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlMediaCategory.AppearanceCaption.Options.UseFont = True
        Me.GroupControlMediaCategory.Controls.Add(Me.LabelControl14)
        Me.GroupControlMediaCategory.Controls.Add(Me.TextEditSearchMediaCategory)
        Me.GroupControlMediaCategory.Controls.Add(Me.PictureClearSearchMediaCategory)
        Me.GroupControlMediaCategory.Controls.Add(Me.PictureAdvancedSearchMediaCategory)
        Me.GroupControlMediaCategory.Controls.Add(Me.ButtonRemoveMediaCategory)
        Me.GroupControlMediaCategory.Controls.Add(Me.ButtonAddMediaCategory)
        Me.GroupControlMediaCategory.Controls.Add(Me.GridMediaCategory)
        Me.GroupControlMediaCategory.Location = New System.Drawing.Point(4, 119)
        Me.GroupControlMediaCategory.LookAndFeel.SkinName = "Black"
        Me.GroupControlMediaCategory.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlMediaCategory.Margin = New System.Windows.Forms.Padding(4)
        Me.GroupControlMediaCategory.Name = "GroupControlMediaCategory"
        Me.GroupControlMediaCategory.Size = New System.Drawing.Size(972, 367)
        Me.GroupControlMediaCategory.TabIndex = 1
        Me.GroupControlMediaCategory.Text = "Categories Permitted"
        '
        'LabelControl14
        '
        Me.LabelControl14.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl14.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl14.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl14.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl14.Location = New System.Drawing.Point(769, 335)
        Me.LabelControl14.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelControl14.Name = "LabelControl14"
        Me.LabelControl14.Size = New System.Drawing.Size(58, 17)
        Me.LabelControl14.TabIndex = 4
        Me.LabelControl14.Text = "Search:"
        '
        'TextEditSearchMediaCategory
        '
        Me.TextEditSearchMediaCategory.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchMediaCategory.EditValue = ""
        Me.TextEditSearchMediaCategory.Location = New System.Drawing.Point(834, 332)
        Me.TextEditSearchMediaCategory.Margin = New System.Windows.Forms.Padding(4, 5, 4, 7)
        Me.TextEditSearchMediaCategory.Name = "TextEditSearchMediaCategory"
        Me.TextEditSearchMediaCategory.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchMediaCategory.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchMediaCategory.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchMediaCategory.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchMediaCategory.Size = New System.Drawing.Size(103, 24)
        Me.TextEditSearchMediaCategory.TabIndex = 5
        '
        'PictureClearSearchMediaCategory
        '
        Me.PictureClearSearchMediaCategory.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchMediaCategory.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchMediaCategory.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchMediaCategory.Location = New System.Drawing.Point(945, 4)
        Me.PictureClearSearchMediaCategory.Margin = New System.Windows.Forms.Padding(4)
        Me.PictureClearSearchMediaCategory.Name = "PictureClearSearchMediaCategory"
        Me.PictureClearSearchMediaCategory.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchMediaCategory.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchMediaCategory.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchMediaCategory.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem7.Text = "Clear Search"
        ToolTipItem7.LeftIndent = 6
        ToolTipItem7.Text = "Click here to clear all search boxes."
        SuperToolTip7.Items.Add(ToolTipTitleItem7)
        SuperToolTip7.Items.Add(ToolTipItem7)
        Me.PictureClearSearchMediaCategory.SuperTip = SuperToolTip7
        Me.PictureClearSearchMediaCategory.TabIndex = 0
        Me.PictureClearSearchMediaCategory.TabStop = True
        '
        'PictureAdvancedSearchMediaCategory
        '
        Me.PictureAdvancedSearchMediaCategory.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearchMediaCategory.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearchMediaCategory.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearchMediaCategory.Location = New System.Drawing.Point(945, 334)
        Me.PictureAdvancedSearchMediaCategory.Margin = New System.Windows.Forms.Padding(4, 8, 4, 9)
        Me.PictureAdvancedSearchMediaCategory.Name = "PictureAdvancedSearchMediaCategory"
        Me.PictureAdvancedSearchMediaCategory.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearchMediaCategory.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearchMediaCategory.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearchMediaCategory.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem8.Text = "Advanced Search"
        ToolTipItem8.LeftIndent = 6
        ToolTipItem8.Text = "Click here to search individual column values."
        SuperToolTip8.Items.Add(ToolTipTitleItem8)
        SuperToolTip8.Items.Add(ToolTipItem8)
        Me.PictureAdvancedSearchMediaCategory.SuperTip = SuperToolTip8
        Me.PictureAdvancedSearchMediaCategory.TabIndex = 6
        Me.PictureAdvancedSearchMediaCategory.TabStop = True
        '
        'ButtonRemoveMediaCategory
        '
        Me.ButtonRemoveMediaCategory.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonRemoveMediaCategory.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonRemoveMediaCategory.Appearance.Options.UseFont = True
        Me.ButtonRemoveMediaCategory.ImageIndex = 2
        Me.ButtonRemoveMediaCategory.ImageList = Me.ImageList16x16
        Me.ButtonRemoveMediaCategory.Location = New System.Drawing.Point(111, 330)
        Me.ButtonRemoveMediaCategory.LookAndFeel.SkinName = "Black"
        Me.ButtonRemoveMediaCategory.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonRemoveMediaCategory.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonRemoveMediaCategory.Name = "ButtonRemoveMediaCategory"
        Me.ButtonRemoveMediaCategory.Size = New System.Drawing.Size(96, 30)
        Me.ButtonRemoveMediaCategory.TabIndex = 3
        Me.ButtonRemoveMediaCategory.Text = "Remove"
        '
        'ButtonAddMediaCategory
        '
        Me.ButtonAddMediaCategory.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAddMediaCategory.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAddMediaCategory.Appearance.Options.UseFont = True
        Me.ButtonAddMediaCategory.ImageIndex = 0
        Me.ButtonAddMediaCategory.ImageList = Me.ImageList16x16
        Me.ButtonAddMediaCategory.Location = New System.Drawing.Point(6, 330)
        Me.ButtonAddMediaCategory.LookAndFeel.SkinName = "Black"
        Me.ButtonAddMediaCategory.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAddMediaCategory.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonAddMediaCategory.Name = "ButtonAddMediaCategory"
        Me.ButtonAddMediaCategory.Size = New System.Drawing.Size(96, 30)
        Me.ButtonAddMediaCategory.TabIndex = 2
        Me.ButtonAddMediaCategory.Text = "Add"
        '
        'GridMediaCategory
        '
        Me.GridMediaCategory.AllowUserToAddRows = False
        Me.GridMediaCategory.AllowUserToDeleteRows = False
        Me.GridMediaCategory.AllowUserToOrderColumns = True
        Me.GridMediaCategory.AllowUserToResizeRows = False
        DataGridViewCellStyle12.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridMediaCategory.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle12
        Me.GridMediaCategory.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridMediaCategory.BackgroundColor = System.Drawing.Color.White
        Me.GridMediaCategory.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridMediaCategory.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridMediaCategory.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle13.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle13.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle13.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle13.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle13.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle13.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle13.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridMediaCategory.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle13
        Me.GridMediaCategory.ColumnHeadersHeight = 22
        Me.GridMediaCategory.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridMediaCategory.ColumnHeadersVisible = False
        Me.GridMediaCategory.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.CategoryNameColumn})
        Me.GridMediaCategory.EnableHeadersVisualStyles = False
        Me.GridMediaCategory.GridColor = System.Drawing.Color.White
        Me.GridMediaCategory.Location = New System.Drawing.Point(3, 29)
        Me.GridMediaCategory.Margin = New System.Windows.Forms.Padding(4)
        Me.GridMediaCategory.Name = "GridMediaCategory"
        Me.GridMediaCategory.ReadOnly = True
        Me.GridMediaCategory.RowHeadersVisible = False
        Me.GridMediaCategory.RowHeadersWidth = 51
        DataGridViewCellStyle14.ForeColor = System.Drawing.Color.DimGray
        Me.GridMediaCategory.RowsDefaultCellStyle = DataGridViewCellStyle14
        Me.GridMediaCategory.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridMediaCategory.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridMediaCategory.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridMediaCategory.RowTemplate.Height = 19
        Me.GridMediaCategory.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridMediaCategory.ShowCellToolTips = False
        Me.GridMediaCategory.Size = New System.Drawing.Size(967, 294)
        Me.GridMediaCategory.StandardTab = True
        Me.GridMediaCategory.TabIndex = 1
        '
        'CategoryNameColumn
        '
        Me.CategoryNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.CategoryNameColumn.DataPropertyName = "CategoryName"
        Me.CategoryNameColumn.HeaderText = "Category"
        Me.CategoryNameColumn.MinimumWidth = 6
        Me.CategoryNameColumn.Name = "CategoryNameColumn"
        Me.CategoryNameColumn.ReadOnly = True
        '
        'LabelControl2
        '
        Me.LabelControl2.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl2.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl2.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelControl2.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl2.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelControl2.LineVisible = True
        Me.LabelControl2.Location = New System.Drawing.Point(4, 4)
        Me.LabelControl2.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.Size = New System.Drawing.Size(972, 24)
        Me.LabelControl2.TabIndex = 0
        Me.LabelControl2.Text = "Media Service Category Management"
        '
        'ButtonSave
        '
        Me.ButtonSave.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonSave.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonSave.Appearance.Options.UseFont = True
        Me.ButtonSave.ImageIndex = 1
        Me.ButtonSave.ImageList = Me.ImageList24x24
        Me.ButtonSave.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonSave.Location = New System.Drawing.Point(775, 658)
        Me.ButtonSave.LookAndFeel.SkinName = "Black"
        Me.ButtonSave.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonSave.Margin = New System.Windows.Forms.Padding(4, 16, 4, 4)
        Me.ButtonSave.Name = "ButtonSave"
        Me.ButtonSave.Size = New System.Drawing.Size(129, 37)
        Me.ButtonSave.TabIndex = 2
        Me.ButtonSave.Text = "Save"
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(912, 658)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Margin = New System.Windows.Forms.Padding(4, 16, 4, 4)
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(129, 37)
        Me.ButtonCancel.TabIndex = 3
        Me.ButtonCancel.Text = "Cancel"
        '
        'XtraTabControl1
        '
        Me.XtraTabControl1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.XtraTabControl1.AppearancePage.Header.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.XtraTabControl1.AppearancePage.Header.Options.UseFont = True
        Me.XtraTabControl1.Location = New System.Drawing.Point(15, 75)
        Me.XtraTabControl1.LookAndFeel.SkinName = "Black"
        Me.XtraTabControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.XtraTabControl1.Margin = New System.Windows.Forms.Padding(4)
        Me.XtraTabControl1.Name = "XtraTabControl1"
        Me.XtraTabControl1.SelectedTabPage = Me.TabPageDetails
        Me.XtraTabControl1.Size = New System.Drawing.Size(1025, 564)
        Me.XtraTabControl1.TabIndex = 1
        Me.XtraTabControl1.TabPages.AddRange(New DevExpress.XtraTab.XtraTabPage() {Me.TabPageDetails, Me.TabPageFamilies, Me.TabPageCategories, Me.TabPageLifeCycles, Me.XtraTabPage1, Me.TabPageMediaCosts})
        '
        'TabPageDetails
        '
        Me.TabPageDetails.Controls.Add(Me.Panel1)
        Me.TabPageDetails.Margin = New System.Windows.Forms.Padding(4)
        Me.TabPageDetails.Name = "TabPageDetails"
        Me.TabPageDetails.Size = New System.Drawing.Size(1019, 532)
        Me.TabPageDetails.Text = "Details"
        '
        'Panel1
        '
        Me.Panel1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Panel1.Controls.Add(Me.TableLayoutPanelDetails)
        Me.Panel1.Location = New System.Drawing.Point(4, 4)
        Me.Panel1.Margin = New System.Windows.Forms.Padding(4)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(1011, 522)
        Me.Panel1.TabIndex = 3
        '
        'TableLayoutPanelDetails
        '
        Me.TableLayoutPanelDetails.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanelDetails.BackColor = System.Drawing.Color.Transparent
        Me.TableLayoutPanelDetails.ColumnCount = 1
        Me.TableLayoutPanelDetails.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanelDetails.Controls.Add(Me.PanelDetails, 0, 2)
        Me.TableLayoutPanelDetails.Controls.Add(Me.LabelControl5, 0, 1)
        Me.TableLayoutPanelDetails.Controls.Add(Me.LabelControl6, 0, 0)
        Me.TableLayoutPanelDetails.Location = New System.Drawing.Point(15, 16)
        Me.TableLayoutPanelDetails.Margin = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.TableLayoutPanelDetails.Name = "TableLayoutPanelDetails"
        Me.TableLayoutPanelDetails.RowCount = 3
        Me.TableLayoutPanelDetails.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanelDetails.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanelDetails.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanelDetails.Size = New System.Drawing.Size(980, 490)
        Me.TableLayoutPanelDetails.TabIndex = 3
        '
        'TabPageFamilies
        '
        Me.TabPageFamilies.Controls.Add(Me.PanelFamilies)
        Me.TabPageFamilies.Margin = New System.Windows.Forms.Padding(4)
        Me.TabPageFamilies.Name = "TabPageFamilies"
        Me.TabPageFamilies.Size = New System.Drawing.Size(1019, 532)
        Me.TabPageFamilies.Text = "Families"
        '
        'PanelFamilies
        '
        Me.PanelFamilies.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelFamilies.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelFamilies.Controls.Add(Me.TableMediaFamilyMembership)
        Me.PanelFamilies.Location = New System.Drawing.Point(4, 4)
        Me.PanelFamilies.Margin = New System.Windows.Forms.Padding(4)
        Me.PanelFamilies.Name = "PanelFamilies"
        Me.PanelFamilies.Size = New System.Drawing.Size(1011, 522)
        Me.PanelFamilies.TabIndex = 2
        '
        'TabPageCategories
        '
        Me.TabPageCategories.Controls.Add(Me.Panel2)
        Me.TabPageCategories.Margin = New System.Windows.Forms.Padding(4)
        Me.TabPageCategories.Name = "TabPageCategories"
        Me.TabPageCategories.Size = New System.Drawing.Size(1019, 532)
        Me.TabPageCategories.Text = "Categories"
        '
        'Panel2
        '
        Me.Panel2.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel2.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Panel2.Controls.Add(Me.TableLayoutPanel1)
        Me.Panel2.Location = New System.Drawing.Point(4, 4)
        Me.Panel2.Margin = New System.Windows.Forms.Padding(4)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(1011, 522)
        Me.Panel2.TabIndex = 4
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanel1.BackColor = System.Drawing.Color.Transparent
        Me.TableLayoutPanel1.ColumnCount = 1
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.GroupControlMediaCategory, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.LabelControl1, 0, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.LabelControl2, 0, 0)
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(15, 16)
        Me.TableLayoutPanel1.Margin = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 3
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(980, 490)
        Me.TableLayoutPanel1.TabIndex = 3
        '
        'TabPageLifeCycles
        '
        Me.TabPageLifeCycles.Controls.Add(Me.Panel4)
        Me.TabPageLifeCycles.Margin = New System.Windows.Forms.Padding(4)
        Me.TabPageLifeCycles.Name = "TabPageLifeCycles"
        Me.TabPageLifeCycles.Size = New System.Drawing.Size(1019, 532)
        Me.TabPageLifeCycles.Text = "Life Cycles"
        '
        'Panel4
        '
        Me.Panel4.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel4.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Panel4.Controls.Add(Me.TableLayoutPanel2)
        Me.Panel4.Location = New System.Drawing.Point(4, 4)
        Me.Panel4.Margin = New System.Windows.Forms.Padding(4)
        Me.Panel4.Name = "Panel4"
        Me.Panel4.Size = New System.Drawing.Size(1011, 522)
        Me.Panel4.TabIndex = 4
        '
        'TableLayoutPanel2
        '
        Me.TableLayoutPanel2.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanel2.BackColor = System.Drawing.Color.Transparent
        Me.TableLayoutPanel2.ColumnCount = 1
        Me.TableLayoutPanel2.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel2.Controls.Add(Me.GroupControlMediaLifeCycle, 0, 2)
        Me.TableLayoutPanel2.Controls.Add(Me.LabelControlNewsContent, 0, 1)
        Me.TableLayoutPanel2.Controls.Add(Me.LabelHeadingLifeCycles, 0, 0)
        Me.TableLayoutPanel2.Location = New System.Drawing.Point(15, 16)
        Me.TableLayoutPanel2.Margin = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.TableLayoutPanel2.Name = "TableLayoutPanel2"
        Me.TableLayoutPanel2.RowCount = 3
        Me.TableLayoutPanel2.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanel2.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanel2.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel2.Size = New System.Drawing.Size(980, 490)
        Me.TableLayoutPanel2.TabIndex = 3
        '
        'XtraTabPage1
        '
        Me.XtraTabPage1.Controls.Add(Me.GroupControlItems)
        Me.XtraTabPage1.Margin = New System.Windows.Forms.Padding(4)
        Me.XtraTabPage1.Name = "XtraTabPage1"
        Me.XtraTabPage1.Size = New System.Drawing.Size(1019, 532)
        Me.XtraTabPage1.Text = "Applicable Media Rules"
        '
        'GroupControlItems
        '
        Me.GroupControlItems.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlItems.Appearance.Options.UseFont = True
        Me.GroupControlItems.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlItems.AppearanceCaption.Options.UseFont = True
        Me.GroupControlItems.AutoSize = True
        Me.GroupControlItems.Controls.Add(Me.LabelControl8)
        Me.GroupControlItems.Controls.Add(Me.TextMediaRulesSearch)
        Me.GroupControlItems.Controls.Add(Me.PictureClearSearchMediaRule)
        Me.GroupControlItems.Controls.Add(Me.PictureAdvancedSearchMediaRule)
        Me.GroupControlItems.Controls.Add(Me.GridMediaRule)
        Me.GroupControlItems.Dock = System.Windows.Forms.DockStyle.Fill
        Me.GroupControlItems.Location = New System.Drawing.Point(0, 0)
        Me.GroupControlItems.LookAndFeel.SkinName = "Black"
        Me.GroupControlItems.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlItems.Margin = New System.Windows.Forms.Padding(4)
        Me.GroupControlItems.Name = "GroupControlItems"
        Me.GroupControlItems.Size = New System.Drawing.Size(1019, 532)
        Me.GroupControlItems.TabIndex = 27
        Me.GroupControlItems.Text = "List of Media Rules"
        '
        'LabelControl8
        '
        Me.LabelControl8.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl8.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl8.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl8.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl8.Location = New System.Drawing.Point(816, 501)
        Me.LabelControl8.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelControl8.Name = "LabelControl8"
        Me.LabelControl8.Size = New System.Drawing.Size(58, 17)
        Me.LabelControl8.TabIndex = 5
        Me.LabelControl8.Text = "Search:"
        '
        'TextMediaRulesSearch
        '
        Me.TextMediaRulesSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextMediaRulesSearch.EditValue = ""
        Me.TextMediaRulesSearch.Location = New System.Drawing.Point(881, 497)
        Me.TextMediaRulesSearch.Margin = New System.Windows.Forms.Padding(4, 5, 4, 7)
        Me.TextMediaRulesSearch.Name = "TextMediaRulesSearch"
        Me.TextMediaRulesSearch.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextMediaRulesSearch.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextMediaRulesSearch.Properties.Appearance.Options.UseFont = True
        Me.TextMediaRulesSearch.Properties.Appearance.Options.UseForeColor = True
        Me.TextMediaRulesSearch.Size = New System.Drawing.Size(103, 24)
        Me.TextMediaRulesSearch.TabIndex = 6
        '
        'PictureClearSearchMediaRule
        '
        Me.PictureClearSearchMediaRule.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchMediaRule.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchMediaRule.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchMediaRule.Location = New System.Drawing.Point(992, 4)
        Me.PictureClearSearchMediaRule.Margin = New System.Windows.Forms.Padding(4)
        Me.PictureClearSearchMediaRule.Name = "PictureClearSearchMediaRule"
        Me.PictureClearSearchMediaRule.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchMediaRule.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchMediaRule.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchMediaRule.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem9.Text = "Clear Search"
        ToolTipItem9.LeftIndent = 6
        ToolTipItem9.Text = "Click here to clear all search boxes."
        SuperToolTip9.Items.Add(ToolTipTitleItem9)
        SuperToolTip9.Items.Add(ToolTipItem9)
        Me.PictureClearSearchMediaRule.SuperTip = SuperToolTip9
        Me.PictureClearSearchMediaRule.TabIndex = 0
        Me.PictureClearSearchMediaRule.TabStop = True
        '
        'PictureAdvancedSearchMediaRule
        '
        Me.PictureAdvancedSearchMediaRule.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearchMediaRule.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearchMediaRule.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearchMediaRule.Location = New System.Drawing.Point(992, 499)
        Me.PictureAdvancedSearchMediaRule.Margin = New System.Windows.Forms.Padding(4, 8, 4, 9)
        Me.PictureAdvancedSearchMediaRule.Name = "PictureAdvancedSearchMediaRule"
        Me.PictureAdvancedSearchMediaRule.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearchMediaRule.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearchMediaRule.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearchMediaRule.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem10.Text = "Advanced Search"
        ToolTipItem10.LeftIndent = 6
        ToolTipItem10.Text = "Click here to search individual column values."
        SuperToolTip10.Items.Add(ToolTipTitleItem10)
        SuperToolTip10.Items.Add(ToolTipItem10)
        Me.PictureAdvancedSearchMediaRule.SuperTip = SuperToolTip10
        Me.PictureAdvancedSearchMediaRule.TabIndex = 7
        Me.PictureAdvancedSearchMediaRule.TabStop = True
        '
        'GridMediaRule
        '
        Me.GridMediaRule.AllowUserToAddRows = False
        Me.GridMediaRule.AllowUserToDeleteRows = False
        Me.GridMediaRule.AllowUserToResizeRows = False
        DataGridViewCellStyle15.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridMediaRule.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle15
        Me.GridMediaRule.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridMediaRule.BackgroundColor = System.Drawing.Color.White
        Me.GridMediaRule.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridMediaRule.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridMediaRule.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle16.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle16.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle16.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle16.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle16.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle16.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle16.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridMediaRule.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle16
        Me.GridMediaRule.ColumnHeadersHeight = 22
        Me.GridMediaRule.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridMediaRule.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.DataGridViewTextBoxColumn1, Me.ApplicableColumn, Me.MediaRuleID})
        Me.GridMediaRule.EnableHeadersVisualStyles = False
        Me.GridMediaRule.GridColor = System.Drawing.Color.White
        Me.GridMediaRule.Location = New System.Drawing.Point(3, 29)
        Me.GridMediaRule.Margin = New System.Windows.Forms.Padding(4)
        Me.GridMediaRule.Name = "GridMediaRule"
        Me.GridMediaRule.RowHeadersVisible = False
        Me.GridMediaRule.RowHeadersWidth = 51
        DataGridViewCellStyle17.ForeColor = System.Drawing.Color.DimGray
        Me.GridMediaRule.RowsDefaultCellStyle = DataGridViewCellStyle17
        Me.GridMediaRule.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridMediaRule.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridMediaRule.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridMediaRule.RowTemplate.Height = 19
        Me.GridMediaRule.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridMediaRule.ShowCellToolTips = False
        Me.GridMediaRule.Size = New System.Drawing.Size(1014, 459)
        Me.GridMediaRule.StandardTab = True
        Me.GridMediaRule.TabIndex = 1
        '
        'DataGridViewTextBoxColumn1
        '
        Me.DataGridViewTextBoxColumn1.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.DataGridViewTextBoxColumn1.DataPropertyName = "Rule"
        Me.DataGridViewTextBoxColumn1.HeaderText = "Media Rule"
        Me.DataGridViewTextBoxColumn1.MinimumWidth = 6
        Me.DataGridViewTextBoxColumn1.Name = "DataGridViewTextBoxColumn1"
        Me.DataGridViewTextBoxColumn1.ReadOnly = True
        '
        'ApplicableColumn
        '
        Me.ApplicableColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None
        Me.ApplicableColumn.DataPropertyName = "Selected"
        Me.ApplicableColumn.HeaderText = "Applicable"
        Me.ApplicableColumn.MinimumWidth = 6
        Me.ApplicableColumn.Name = "ApplicableColumn"
        Me.ApplicableColumn.Resizable = System.Windows.Forms.DataGridViewTriState.[True]
        Me.ApplicableColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Automatic
        Me.ApplicableColumn.Width = 125
        '
        'MediaRuleID
        '
        Me.MediaRuleID.DataPropertyName = "MediaRuleID"
        Me.MediaRuleID.HeaderText = "MediaRuleID"
        Me.MediaRuleID.MinimumWidth = 6
        Me.MediaRuleID.Name = "MediaRuleID"
        Me.MediaRuleID.ReadOnly = True
        Me.MediaRuleID.Visible = False
        Me.MediaRuleID.Width = 125
        '
        'TabPageMediaCosts
        '
        Me.TabPageMediaCosts.Controls.Add(Me.Panel3)
        Me.TabPageMediaCosts.Name = "TabPageMediaCosts"
        Me.TabPageMediaCosts.Size = New System.Drawing.Size(1019, 532)
        Me.TabPageMediaCosts.Text = "Media Costs"
        '
        'Panel3
        '
        Me.Panel3.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel3.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Panel3.Controls.Add(Me.TableLayoutPanel3)
        Me.Panel3.Location = New System.Drawing.Point(4, 5)
        Me.Panel3.Margin = New System.Windows.Forms.Padding(4)
        Me.Panel3.Name = "Panel3"
        Me.Panel3.Size = New System.Drawing.Size(1011, 522)
        Me.Panel3.TabIndex = 5
        '
        'TableLayoutPanel3
        '
        Me.TableLayoutPanel3.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanel3.BackColor = System.Drawing.Color.Transparent
        Me.TableLayoutPanel3.ColumnCount = 1
        Me.TableLayoutPanel3.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel3.Controls.Add(Me.GroupControlMediaCosts, 0, 2)
        Me.TableLayoutPanel3.Controls.Add(Me.LabelControlMediaCostConetent, 0, 1)
        Me.TableLayoutPanel3.Controls.Add(Me.LabelHeadingMediaCosts, 0, 0)
        Me.TableLayoutPanel3.Location = New System.Drawing.Point(15, 16)
        Me.TableLayoutPanel3.Margin = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.TableLayoutPanel3.Name = "TableLayoutPanel3"
        Me.TableLayoutPanel3.RowCount = 3
        Me.TableLayoutPanel3.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanel3.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanel3.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel3.Size = New System.Drawing.Size(980, 490)
        Me.TableLayoutPanel3.TabIndex = 3
        '
        'GroupControlMediaCosts
        '
        Me.GroupControlMediaCosts.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlMediaCosts.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlMediaCosts.Appearance.Options.UseFont = True
        Me.GroupControlMediaCosts.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlMediaCosts.AppearanceCaption.Options.UseFont = True
        Me.GroupControlMediaCosts.Controls.Add(Me.LabelControl13)
        Me.GroupControlMediaCosts.Controls.Add(Me.TextEditSearchMediaCost)
        Me.GroupControlMediaCosts.Controls.Add(Me.PictureClearSearchMediaCost)
        Me.GroupControlMediaCosts.Controls.Add(Me.PictureAdvancedSearchMediaCost)
        Me.GroupControlMediaCosts.Controls.Add(Me.ButtonDeleteMediaCost)
        Me.GroupControlMediaCosts.Controls.Add(Me.ButtonAddMediaCost)
        Me.GroupControlMediaCosts.Controls.Add(Me.ButtonEditMediaCost)
        Me.GroupControlMediaCosts.Controls.Add(Me.GridMediaCosts)
        Me.GroupControlMediaCosts.Location = New System.Drawing.Point(4, 102)
        Me.GroupControlMediaCosts.LookAndFeel.SkinName = "Black"
        Me.GroupControlMediaCosts.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlMediaCosts.Margin = New System.Windows.Forms.Padding(4)
        Me.GroupControlMediaCosts.Name = "GroupControlMediaCosts"
        Me.GroupControlMediaCosts.Size = New System.Drawing.Size(972, 384)
        Me.GroupControlMediaCosts.TabIndex = 1
        Me.GroupControlMediaCosts.Text = "Media Cost List"
        '
        'LabelControl13
        '
        Me.LabelControl13.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl13.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl13.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl13.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl13.Location = New System.Drawing.Point(769, 353)
        Me.LabelControl13.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelControl13.Name = "LabelControl13"
        Me.LabelControl13.Size = New System.Drawing.Size(58, 17)
        Me.LabelControl13.TabIndex = 5
        Me.LabelControl13.Text = "Search:"
        '
        'TextEditSearchMediaCost
        '
        Me.TextEditSearchMediaCost.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchMediaCost.EditValue = ""
        Me.TextEditSearchMediaCost.Location = New System.Drawing.Point(834, 349)
        Me.TextEditSearchMediaCost.Margin = New System.Windows.Forms.Padding(4, 5, 4, 7)
        Me.TextEditSearchMediaCost.Name = "TextEditSearchMediaCost"
        Me.TextEditSearchMediaCost.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchMediaCost.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchMediaCost.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchMediaCost.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchMediaCost.Size = New System.Drawing.Size(103, 24)
        Me.TextEditSearchMediaCost.TabIndex = 6
        '
        'PictureClearSearchMediaCost
        '
        Me.PictureClearSearchMediaCost.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchMediaCost.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchMediaCost.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchMediaCost.Location = New System.Drawing.Point(945, 4)
        Me.PictureClearSearchMediaCost.Margin = New System.Windows.Forms.Padding(4)
        Me.PictureClearSearchMediaCost.Name = "PictureClearSearchMediaCost"
        Me.PictureClearSearchMediaCost.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchMediaCost.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchMediaCost.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchMediaCost.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem11.Text = "Clear Search"
        ToolTipItem11.LeftIndent = 6
        ToolTipItem11.Text = "Click here to clear all search boxes."
        SuperToolTip11.Items.Add(ToolTipTitleItem11)
        SuperToolTip11.Items.Add(ToolTipItem11)
        Me.PictureClearSearchMediaCost.SuperTip = SuperToolTip11
        Me.PictureClearSearchMediaCost.TabIndex = 0
        Me.PictureClearSearchMediaCost.TabStop = True
        '
        'PictureAdvancedSearchMediaCost
        '
        Me.PictureAdvancedSearchMediaCost.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearchMediaCost.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearchMediaCost.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearchMediaCost.Location = New System.Drawing.Point(945, 351)
        Me.PictureAdvancedSearchMediaCost.Margin = New System.Windows.Forms.Padding(4, 8, 4, 9)
        Me.PictureAdvancedSearchMediaCost.Name = "PictureAdvancedSearchMediaCost"
        Me.PictureAdvancedSearchMediaCost.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearchMediaCost.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearchMediaCost.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearchMediaCost.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem12.Text = "Advanced Search"
        ToolTipItem12.LeftIndent = 6
        ToolTipItem12.Text = "Click here to search individual column values."
        SuperToolTip12.Items.Add(ToolTipTitleItem12)
        SuperToolTip12.Items.Add(ToolTipItem12)
        Me.PictureAdvancedSearchMediaCost.SuperTip = SuperToolTip12
        Me.PictureAdvancedSearchMediaCost.TabIndex = 7
        Me.PictureAdvancedSearchMediaCost.TabStop = True
        '
        'ButtonDeleteMediaCost
        '
        Me.ButtonDeleteMediaCost.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonDeleteMediaCost.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonDeleteMediaCost.Appearance.Options.UseFont = True
        Me.ButtonDeleteMediaCost.ImageIndex = 2
        Me.ButtonDeleteMediaCost.ImageList = Me.ImageList16x16
        Me.ButtonDeleteMediaCost.Location = New System.Drawing.Point(215, 347)
        Me.ButtonDeleteMediaCost.LookAndFeel.SkinName = "Black"
        Me.ButtonDeleteMediaCost.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonDeleteMediaCost.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonDeleteMediaCost.Name = "ButtonDeleteMediaCost"
        Me.ButtonDeleteMediaCost.Size = New System.Drawing.Size(96, 30)
        Me.ButtonDeleteMediaCost.TabIndex = 4
        Me.ButtonDeleteMediaCost.Text = "Delete"
        '
        'ButtonAddMediaCost
        '
        Me.ButtonAddMediaCost.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAddMediaCost.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAddMediaCost.Appearance.Options.UseFont = True
        Me.ButtonAddMediaCost.ImageIndex = 0
        Me.ButtonAddMediaCost.ImageList = Me.ImageList16x16
        Me.ButtonAddMediaCost.Location = New System.Drawing.Point(6, 347)
        Me.ButtonAddMediaCost.LookAndFeel.SkinName = "Black"
        Me.ButtonAddMediaCost.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAddMediaCost.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonAddMediaCost.Name = "ButtonAddMediaCost"
        Me.ButtonAddMediaCost.Size = New System.Drawing.Size(96, 30)
        Me.ButtonAddMediaCost.TabIndex = 2
        Me.ButtonAddMediaCost.Text = "Add"
        '
        'ButtonEditMediaCost
        '
        Me.ButtonEditMediaCost.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonEditMediaCost.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonEditMediaCost.Appearance.Options.UseFont = True
        Me.ButtonEditMediaCost.ImageIndex = 1
        Me.ButtonEditMediaCost.ImageList = Me.ImageList16x16
        Me.ButtonEditMediaCost.Location = New System.Drawing.Point(111, 347)
        Me.ButtonEditMediaCost.LookAndFeel.SkinName = "Black"
        Me.ButtonEditMediaCost.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonEditMediaCost.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonEditMediaCost.Name = "ButtonEditMediaCost"
        Me.ButtonEditMediaCost.Size = New System.Drawing.Size(96, 30)
        Me.ButtonEditMediaCost.TabIndex = 3
        Me.ButtonEditMediaCost.Text = "Edit"
        '
        'GridMediaCosts
        '
        Me.GridMediaCosts.AllowUserToAddRows = False
        Me.GridMediaCosts.AllowUserToDeleteRows = False
        Me.GridMediaCosts.AllowUserToOrderColumns = True
        Me.GridMediaCosts.AllowUserToResizeRows = False
        DataGridViewCellStyle18.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridMediaCosts.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle18
        Me.GridMediaCosts.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridMediaCosts.BackgroundColor = System.Drawing.Color.White
        Me.GridMediaCosts.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridMediaCosts.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridMediaCosts.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle19.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle19.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle19.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle19.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle19.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle19.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle19.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridMediaCosts.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle19
        Me.GridMediaCosts.ColumnHeadersHeight = 22
        Me.GridMediaCosts.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridMediaCosts.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.IsPercentageColumn, Me.CostPercentageColumn, Me.CostPriceColumn, Me.EffectiveDateColumn})
        Me.GridMediaCosts.EnableHeadersVisualStyles = False
        Me.GridMediaCosts.GridColor = System.Drawing.Color.White
        Me.GridMediaCosts.Location = New System.Drawing.Point(3, 29)
        Me.GridMediaCosts.Margin = New System.Windows.Forms.Padding(4)
        Me.GridMediaCosts.Name = "GridMediaCosts"
        Me.GridMediaCosts.ReadOnly = True
        Me.GridMediaCosts.RowHeadersVisible = False
        Me.GridMediaCosts.RowHeadersWidth = 51
        DataGridViewCellStyle22.ForeColor = System.Drawing.Color.DimGray
        Me.GridMediaCosts.RowsDefaultCellStyle = DataGridViewCellStyle22
        Me.GridMediaCosts.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridMediaCosts.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridMediaCosts.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridMediaCosts.RowTemplate.Height = 19
        Me.GridMediaCosts.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridMediaCosts.ShowCellToolTips = False
        Me.GridMediaCosts.Size = New System.Drawing.Size(967, 311)
        Me.GridMediaCosts.StandardTab = True
        Me.GridMediaCosts.TabIndex = 1
        '
        'IsPercentageColumn
        '
        Me.IsPercentageColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.IsPercentageColumn.DataPropertyName = "isPercentage"
        Me.IsPercentageColumn.HeaderText = "Is Percentage"
        Me.IsPercentageColumn.Name = "IsPercentageColumn"
        Me.IsPercentageColumn.ReadOnly = True
        '
        'CostPercentageColumn
        '
        Me.CostPercentageColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.CostPercentageColumn.DataPropertyName = "CostPercentage"
        DataGridViewCellStyle20.Format = "N2"
        DataGridViewCellStyle20.NullValue = Nothing
        Me.CostPercentageColumn.DefaultCellStyle = DataGridViewCellStyle20
        Me.CostPercentageColumn.HeaderText = "Percentage Cost"
        Me.CostPercentageColumn.MinimumWidth = 6
        Me.CostPercentageColumn.Name = "CostPercentageColumn"
        Me.CostPercentageColumn.ReadOnly = True
        '
        'CostPriceColumn
        '
        Me.CostPriceColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.CostPriceColumn.DataPropertyName = "CostPrice"
        DataGridViewCellStyle21.Format = "C2"
        DataGridViewCellStyle21.NullValue = Nothing
        Me.CostPriceColumn.DefaultCellStyle = DataGridViewCellStyle21
        Me.CostPriceColumn.HeaderText = "Cost in Rand"
        Me.CostPriceColumn.MinimumWidth = 6
        Me.CostPriceColumn.Name = "CostPriceColumn"
        Me.CostPriceColumn.ReadOnly = True
        Me.CostPriceColumn.Resizable = System.Windows.Forms.DataGridViewTriState.[True]
        '
        'EffectiveDateColumn
        '
        Me.EffectiveDateColumn.DataPropertyName = "EffectiveDate"
        Me.EffectiveDateColumn.HeaderText = "Effective Date"
        Me.EffectiveDateColumn.Name = "EffectiveDateColumn"
        Me.EffectiveDateColumn.ReadOnly = True
        '
        'LabelControlMediaCostConetent
        '
        Me.LabelControlMediaCostConetent.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControlMediaCostConetent.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControlMediaCostConetent.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControlMediaCostConetent.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top
        Me.LabelControlMediaCostConetent.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap
        Me.LabelControlMediaCostConetent.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelControlMediaCostConetent.Location = New System.Drawing.Point(4, 48)
        Me.LabelControlMediaCostConetent.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelControlMediaCostConetent.Name = "LabelControlMediaCostConetent"
        Me.LabelControlMediaCostConetent.Size = New System.Drawing.Size(972, 34)
        Me.LabelControlMediaCostConetent.TabIndex = 0
        Me.LabelControlMediaCostConetent.Text = resources.GetString("LabelControlMediaCostConetent.Text")
        '
        'LabelHeadingMediaCosts
        '
        Me.LabelHeadingMediaCosts.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelHeadingMediaCosts.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelHeadingMediaCosts.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelHeadingMediaCosts.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelHeadingMediaCosts.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelHeadingMediaCosts.LineVisible = True
        Me.LabelHeadingMediaCosts.Location = New System.Drawing.Point(4, 4)
        Me.LabelHeadingMediaCosts.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelHeadingMediaCosts.Name = "LabelHeadingMediaCosts"
        Me.LabelHeadingMediaCosts.Size = New System.Drawing.Size(972, 24)
        Me.LabelHeadingMediaCosts.TabIndex = 0
        Me.LabelHeadingMediaCosts.Text = "Media Cost Management"
        '
        'SubformMedia
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(9.0!, 17.0!)
        Me.Controls.Add(Me.XtraTabControl1)
        Me.Controls.Add(Me.ButtonSave)
        Me.Controls.Add(Me.ButtonCancel)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(5)
        Me.Name = "SubformMedia"
        Me.Padding = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.Tag = "821, 543"
        Me.Controls.SetChildIndex(Me.ButtonCancel, 0)
        Me.Controls.SetChildIndex(Me.ButtonSave, 0)
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        Me.Controls.SetChildIndex(Me.XtraTabControl1, 0)
        CType(Me.GroupControlMediaLifeCycle, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlMediaLifeCycle.ResumeLayout(False)
        CType(Me.TextEditSearchMediaLifeCycle.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearchMediaLifeCycle.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearchMediaLifeCycle.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridMediaLifeCycle, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelDetails.ResumeLayout(False)
        Me.PanelDetails.PerformLayout()
        CType(Me.CheckEditMediaCostOverridable.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditMediaCost.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditPNPPcaStatus.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.MemoEditNotes.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditCrossover.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditHomesite.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditMediaName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TableMediaFamilyMembership.ResumeLayout(False)
        CType(Me.GroupControlCompetingMediaServices, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlCompetingMediaServices.ResumeLayout(False)
        CType(Me.PictureClearSearchCompetingMedia.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearchCompetingMedia.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditSearchCompetingMediaServices.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridCompetingMediaServices, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControlMediaFamilyMembership, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlMediaFamilyMembership.ResumeLayout(False)
        CType(Me.TextEditSearchMediaFamilyMembership.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearchMediaFamilyMembership.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearchMediaFamilyMembership.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridMediaFamilyMembership, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControlMediaCategory, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlMediaCategory.ResumeLayout(False)
        CType(Me.TextEditSearchMediaCategory.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearchMediaCategory.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearchMediaCategory.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridMediaCategory, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabControl1.ResumeLayout(False)
        Me.TabPageDetails.ResumeLayout(False)
        Me.Panel1.ResumeLayout(False)
        Me.TableLayoutPanelDetails.ResumeLayout(False)
        Me.TabPageFamilies.ResumeLayout(False)
        Me.PanelFamilies.ResumeLayout(False)
        Me.TabPageCategories.ResumeLayout(False)
        Me.Panel2.ResumeLayout(False)
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.TabPageLifeCycles.ResumeLayout(False)
        Me.Panel4.ResumeLayout(False)
        Me.TableLayoutPanel2.ResumeLayout(False)
        Me.XtraTabPage1.ResumeLayout(False)
        Me.XtraTabPage1.PerformLayout()
        CType(Me.GroupControlItems, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlItems.ResumeLayout(False)
        CType(Me.TextMediaRulesSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearchMediaRule.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearchMediaRule.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridMediaRule, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabPageMediaCosts.ResumeLayout(False)
        Me.Panel3.ResumeLayout(False)
        Me.TableLayoutPanel3.ResumeLayout(False)
        CType(Me.GroupControlMediaCosts, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlMediaCosts.ResumeLayout(False)
        CType(Me.TextEditSearchMediaCost.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearchMediaCost.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearchMediaCost.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridMediaCosts, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents ImageList16x16 As System.Windows.Forms.ImageList
    Friend WithEvents LabelControlNewsContent As DevExpress.XtraEditors.LabelControl
    Friend WithEvents GroupControlMediaLifeCycle As DevExpress.XtraEditors.GroupControl
    Friend WithEvents GridMediaLifeCycle As System.Windows.Forms.DataGridView
    Friend WithEvents LabelHeadingLifeCycles As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ButtonSave As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl5 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl6 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TableMediaFamilyMembership As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents LabelControl3 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents GroupControlMediaFamilyMembership As DevExpress.XtraEditors.GroupControl
    Friend WithEvents PictureClearSearchMediaFamilyMembership As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearchMediaFamilyMembership As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents GridMediaFamilyMembership As System.Windows.Forms.DataGridView
    Friend WithEvents LabelControl4 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents GroupControlMediaCategory As DevExpress.XtraEditors.GroupControl
    Friend WithEvents GridMediaCategory As System.Windows.Forms.DataGridView
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents PanelDetails As System.Windows.Forms.Panel
    Friend WithEvents LabelMediaName As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditMediaName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents MemoEditNotes As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents LabelNotes As DevExpress.XtraEditors.LabelControl
    Friend WithEvents CheckEditCrossover As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents CheckEditHomesite As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ButtonDeleteMediaLifeCycle As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonAddMediaLifeCycle As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonEditMediaLifeCycle As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonRemoveMediaFamilyMembership As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonAddMediaFamilyMembership As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonRemoveMediaCategory As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonAddMediaCategory As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents MediaFamilyNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents CategoryNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents FirstWeekColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents LastWeekColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents TextEditSearchMediaFamilyMembership As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl9 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl10 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents GroupControlCompetingMediaServices As DevExpress.XtraEditors.GroupControl
    Friend WithEvents PictureClearSearchCompetingMedia As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearchCompetingMedia As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents GridCompetingMediaServices As System.Windows.Forms.DataGridView
    Friend WithEvents TextEditSearchCompetingMediaServices As DevExpress.XtraEditors.TextEdit
    Friend WithEvents CompetingMediaNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents LabelControl11 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl12 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl14 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditSearchMediaCategory As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearchMediaCategory As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearchMediaCategory As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents LabelControl7 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditSearchMediaLifeCycle As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearchMediaLifeCycle As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearchMediaLifeCycle As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents XtraTabControl1 As DevExpress.XtraTab.XtraTabControl
    Friend WithEvents TabPageDetails As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents TableLayoutPanelDetails As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents TabPageFamilies As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents PanelFamilies As System.Windows.Forms.Panel
    Friend WithEvents TabPageCategories As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel2 As System.Windows.Forms.Panel
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents TabPageLifeCycles As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel4 As System.Windows.Forms.Panel
    Friend WithEvents TableLayoutPanel2 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents XtraTabPage1 As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents GroupControlItems As GroupControl
    Friend WithEvents LabelControl8 As LabelControl
    Friend WithEvents TextMediaRulesSearch As TextEdit
    Friend WithEvents PictureClearSearchMediaRule As PictureEdit
    Friend WithEvents PictureAdvancedSearchMediaRule As PictureEdit
    Friend WithEvents GridMediaRule As DataGridView
    Friend WithEvents DataGridViewTextBoxColumn1 As DataGridViewTextBoxColumn
    Friend WithEvents ApplicableColumn As DataGridViewCheckBoxColumn
    Friend WithEvents MediaRuleID As DataGridViewTextBoxColumn
    Friend WithEvents CheckEditPNPPcaStatus As CheckEdit
    Friend WithEvents CheckEditMediaCostOverridable As CheckEdit
    Friend WithEvents CheckEditMediaCost As CheckEdit
    Friend WithEvents TabPageMediaCosts As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel3 As Panel
    Friend WithEvents TableLayoutPanel3 As TableLayoutPanel
    Friend WithEvents GroupControlMediaCosts As GroupControl
    Friend WithEvents LabelControl13 As LabelControl
    Friend WithEvents TextEditSearchMediaCost As TextEdit
    Friend WithEvents PictureClearSearchMediaCost As PictureEdit
    Friend WithEvents PictureAdvancedSearchMediaCost As PictureEdit
    Friend WithEvents ButtonDeleteMediaCost As SimpleButton
    Friend WithEvents ButtonAddMediaCost As SimpleButton
    Friend WithEvents ButtonEditMediaCost As SimpleButton
    Friend WithEvents GridMediaCosts As DataGridView
    Friend WithEvents LabelControlMediaCostConetent As LabelControl
    Friend WithEvents LabelHeadingMediaCosts As LabelControl
    Friend WithEvents IsPercentageColumn As DataGridViewCheckBoxColumn
    Friend WithEvents CostPercentageColumn As DataGridViewTextBoxColumn
    Friend WithEvents CostPriceColumn As DataGridViewTextBoxColumn
    Friend WithEvents EffectiveDateColumn As DataGridViewTextBoxColumn
End Class
