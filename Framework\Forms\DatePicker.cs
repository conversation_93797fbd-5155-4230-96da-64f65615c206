﻿using System;

namespace Framework.Forms
{
    public partial class DatePicker : DataForm, IPicker
    {
        
        #region Properties

        private string _ErrorMessage = string.Empty;
        public string ErrorMessage
        {
            get { return _ErrorMessage; }
            set
            {
                _ErrorMessage = value;
            }
        }

        #endregion


        public DatePicker()
        {
            InitializeComponent();
            monthCalendar1.SetDate(DateTime.Now.Date);
            monthCalendar1.DateSelected += MonthCalendar1_DateSelected;
        }

        private void MonthCalendar1_DateSelected(object sender, System.Windows.Forms.DateRangeEventArgs e)
        {
            SelectedDate = e.Start;
            DialogResult = System.Windows.Forms.DialogResult.OK;
            Close();
        }

        public object Pick()
        {
            DateTime? newselecteddate = null;
            ShowDialog();
            if (DialogResult == System.Windows.Forms.DialogResult.OK)
            {
                newselecteddate = SelectedDate;
            }
            return newselecteddate;
        }

        private DateTime _SelectedDate = DateTime.Now.Date;
        public DateTime SelectedDate
        {
            get { return _SelectedDate; }
            set
            {
                _SelectedDate = value;
                monthCalendar1.SetDate(value);
            }
        }
    }
}
