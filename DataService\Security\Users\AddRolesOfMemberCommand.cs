﻿using DataAccess;
using System;
using System.Collections.Generic;
using System.Data;

namespace DataService.Security
{
    class AddRolesOfMemberCommand : Command
    {
        public Guid SessionId { get; set; }
        public Guid UserId { get; set; }
        public DataTable NewRoles { get; set; }
        public string Username { get; set; }

        public AddRolesOfMemberCommand(Guid sessionid, Guid userid, List<DataRow> newroleslist)
        {
            SessionId = sessionid;
            UserId = userid;
            Username = string.Empty;

            // Create a new table.
            NewRoles = new DataTable();
            NewRoles.Columns.Add("id", typeof(Guid));

            // Convert the rows in the specified list to rows that match the schema of the table and then add them to the table.
            if (newroleslist != null && newroleslist.Count > 0)
            {
                for (int i = 0; i < newroleslist.Count; i++)
                {
                    DataRow newrow = NewRoles.NewRow();
                    newrow["id"] = newroleslist[i]["roleid"];
                    NewRoles.Rows.Add(newrow);
                }
            }
        }
    }
}
