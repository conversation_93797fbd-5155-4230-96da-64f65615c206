C:\Users\<USER>\source\repos\PrimediaInstore\NovaLegacy\Nova\DataAccess\bin\Debug\DataAccess.dll
C:\Users\<USER>\source\repos\PrimediaInstore\NovaLegacy\Nova\DataAccess\bin\Debug\DataAccess.pdb
C:\Users\<USER>\source\repos\PrimediaInstore\NovaLegacy\Nova\DataAccess\bin\Debug\RestSharp.dll
C:\Users\<USER>\source\repos\PrimediaInstore\NovaLegacy\Nova\DataAccess\bin\Debug\Universal.dll
C:\Users\<USER>\source\repos\PrimediaInstore\NovaLegacy\Nova\DataAccess\bin\Debug\Newtonsoft.Json.dll
C:\Users\<USER>\source\repos\PrimediaInstore\NovaLegacy\Nova\DataAccess\bin\Debug\Universal.pdb
C:\Users\<USER>\source\repos\PrimediaInstore\NovaLegacy\Nova\DataAccess\bin\Debug\RestSharp.xml
C:\Users\<USER>\source\repos\PrimediaInstore\NovaLegacy\Nova\DataAccess\bin\Debug\Newtonsoft.Json.xml
C:\Users\<USER>\source\repos\PrimediaInstore\NovaLegacy\Nova\DataAccess\obj\Debug\DataAccess.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\PrimediaInstore\NovaLegacy\Nova\DataAccess\obj\Debug\DataAccess.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\PrimediaInstore\NovaLegacy\Nova\DataAccess\obj\Debug\DataAccess.csproj.CopyComplete
C:\Users\<USER>\source\repos\PrimediaInstore\NovaLegacy\Nova\DataAccess\obj\Debug\DataAccess.dll
C:\Users\<USER>\source\repos\PrimediaInstore\NovaLegacy\Nova\DataAccess\obj\Debug\DataAccess.pdb
C:\Users\<USER>\source\repos\PrimediaInstore\Nova\DataAccess\bin\Debug\DataAccess.dll
C:\Users\<USER>\source\repos\PrimediaInstore\Nova\DataAccess\bin\Debug\DataAccess.pdb
C:\Users\<USER>\source\repos\PrimediaInstore\Nova\DataAccess\bin\Debug\RestSharp.dll
C:\Users\<USER>\source\repos\PrimediaInstore\Nova\DataAccess\bin\Debug\Universal.dll
C:\Users\<USER>\source\repos\PrimediaInstore\Nova\DataAccess\bin\Debug\Newtonsoft.Json.dll
C:\Users\<USER>\source\repos\PrimediaInstore\Nova\DataAccess\bin\Debug\Universal.pdb
C:\Users\<USER>\source\repos\PrimediaInstore\Nova\DataAccess\bin\Debug\RestSharp.xml
C:\Users\<USER>\source\repos\PrimediaInstore\Nova\DataAccess\bin\Debug\Newtonsoft.Json.xml
C:\Users\<USER>\source\repos\PrimediaInstore\Nova\DataAccess\obj\Debug\DataAccess.csproj.AssemblyReference.cache
C:\Users\<USER>\source\repos\PrimediaInstore\Nova\DataAccess\obj\Debug\DataAccess.csproj.CoreCompileInputs.cache
C:\Users\<USER>\source\repos\PrimediaInstore\Nova\DataAccess\obj\Debug\DataAccess.dll
C:\Users\<USER>\source\repos\PrimediaInstore\Nova\DataAccess\obj\Debug\DataAccess.pdb
C:\Users\<USER>\source\repos\PrimediaInstore\Nova\DataAccess\obj\Debug\DataAccess.csproj.Up2Date
C:\NovaNelli\Nova\DataAccess\bin\Debug\DataAccess.dll
C:\NovaNelli\Nova\DataAccess\bin\Debug\DataAccess.pdb
C:\NovaNelli\Nova\DataAccess\bin\Debug\RestSharp.dll
C:\NovaNelli\Nova\DataAccess\bin\Debug\Universal.dll
C:\NovaNelli\Nova\DataAccess\bin\Debug\Newtonsoft.Json.dll
C:\NovaNelli\Nova\DataAccess\bin\Debug\Universal.pdb
C:\NovaNelli\Nova\DataAccess\bin\Debug\RestSharp.xml
C:\NovaNelli\Nova\DataAccess\bin\Debug\Newtonsoft.Json.xml
C:\NovaNelli\Nova\DataAccess\obj\Debug\DataAccess.csproj.AssemblyReference.cache
C:\NovaNelli\Nova\DataAccess\obj\Debug\DataAccess.csproj.CoreCompileInputs.cache
C:\NovaNelli\Nova\DataAccess\obj\Debug\DataAccess.dll
C:\NovaNelli\Nova\DataAccess\obj\Debug\DataAccess.pdb
C:\NovaNelli\Nova\DataAccess\obj\Debug\DataAccess.csproj.CopyComplete
