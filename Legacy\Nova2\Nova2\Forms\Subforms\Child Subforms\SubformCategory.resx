<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="ImageList16x16.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="ImageList16x16.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj00LjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAAAE
        CgAAAk1TRnQBSQFMAgEBAwEAASQBAgEkAQIBEAEAARABAAT/AREBAAj/AUIBTQE2BwABNgMAASgDAAFA
        AwABEAMAAQEBAAEQBgABCBwAATkBZwEZAWMBOgFnAToBZwE5AWcBGAFjCgABWgFrARgBYwE5AWcBGAFj
        ARgBYxoAARgBYwFaAWcBGAFjATkBZwgAATkBZwEYAWMBWgFnARgBYywAAVoBawFbAW8BNgFjAdABTgHQ
        AU4BNQFfAZwBcwE5AWcIAAEYAWMBewFvAbUBVgEYAWMB3gF7ARgBYwEYAWMUAAH3AV4B/wF7ARABVgHW
        AWYBnAFvATkBZwQAATkBZwGcAW8B1gFmARABVgH/AXsB9wFeKgABOQFnAb4BdwEjASoB4AEhAQABIgEB
        ASYBnAFzARgBYwgAARgBYwGcAXMB7gE9AgABCAEhAXoBcwHeAXcBOgFjARgBYw4AARgBXwH/AXsBawFN
        AQABMAEAATQB1gFmAZwBbwE5AWcBOQFnAZwBbwHWAWYBAAE0AQABMAFrAU0B/wF7ARgBXygAATkBZwG+
        AXcBRAEyAUQBMgFEATIBQwEuAbwBcwE5AWcIAAFaAWsBOQFnARgBYwHnARwBkAFeAVQBfwE1AXsBlwF3
        AX0BawE5AWcMAAF7AWsBzgFZAQABNAFjAUABQQFAAQABPAH3AWoBewFvAXsBbwH3AWoBAAE8AUEBQAFj
        AUABAAE0Ac4BWQF7AWsiAAFaAWsBGAFjARgBYwH3AV4B3gF7AWQBNgFjATIBZAEyAWMBMgG8AXcB9wFe
        ARgBYwEYAWMBWgFrBAABOQFnAb0BcwF3AXsBugF/AVgBfwEGAX8B4AF+AZgBdwE6AWcMAAF7AWsBMQFi
        AQABPAFiAUQBYwFEAUEBRAEAAUABOQFvATkBbwEAAUABQQFEAWMBRAFiAUQBAAE8ATEBYgF7AWsgAAE5
        AWcBWwFrAd8BewHfAXsB3wF/Ad8BfwGlAToBgwE2AYQBNgGDATYBvgF7Ad8BfwHfAXsB3wF7AXwBbwEY
        AWMCAAFaAWsB3gF3AbwBfwGZAX8BSAF/AQABfwEAAX8BAAF/Ab0BdwEYAWMKAAE5AWcB/wF7ARABYgEA
        AUABYgFIAYMBSAFCAUgBYgFIAWIBSAFCAUgBgwFIAWIBSAEAAUABEAFiAf8BewE5AWcgAAE5AWcBdgFn
        AaUBOgHGAT4BxwE+AccBQgGkAToBpAE6AaQBOgGkATYBxwE+AccBPgHGAT4BpAE6AVQBXwFaAWsEAAGc
        AW8BugF/AY8BfwFqAX8BJAF/AQABfwHgAX4BIwF7Ad8BdwH4AV4KAAE5AWcB/wF/ATABYgEAAUQBYgFM
        AYMBTAFiAUwBYgFMAYMBTAFiAUwBAAFEATABYgH/AX8BOQFnIgABewFvAS0BUwGgATIBwwE6AcMBOgHD
        AToBxAE+AcQBPgHEAT4BxAE+AcMBOgHDAToBwwE6AaABMgEKAUsBewFvBAABWgFnAdwBewGPAX8BjwF/
        AWsBfwEjAXsBAAF/AeABfgFKAXsB3wF3ARgBYwoAATkBZwG9AXcBUgFmAUEBTAFjAVABgwFQAYMBUAFj
        AVABQQFMAVIBZgHeAXsBOQFnJAABewFvAU0BUwHBATYB5AE+AeQBPgHkAT4B5AE+AeQBPgHkAT4B5AE+
        AeQBPgHkAT4B5AE+AcIBOgErAU8BewFvBgABfAFvAdoBewGvAX8BjwF/AUoBfwEjAXsBAAF/AeABfgGP
        AX8BvQFzATkBZwgAATkBZwF7AW8BWgF3AUEBUAFjAVQBgwFUAYMBVAFjAVQBQQFQAVoBdwF7AW8BOQFn
        JAABewFvAXEBXwHAATYB4QE6AeEBOgHiAT4B4wFCAQQBQwEEAUMB4wFCAeIBPgHhAToB4QE6AcABNgFO
        AVcBewFvBgABWgFrAd4BdwHYAX8BjwF/AY8BfwFJAX8BIAF/AQABfwHtAWIBfQFvAXsBbwYAATkBZwGc
        AXMBOAFzASABVAFiAVQBgwFYAYMBWAGDAVgBgwFYAWIBVAEgAVQBOAFzAZwBcwE5AWciAAE5AWcB/wF/
        AdsBdwHbAXcB3AF3AdsBdwEEAUMBAwFDAQMBQwECAUMBugFzAdwBdwHbAXcBuwF3Af8BfwE5AWcIAAE6
        AWcB/wF7AbUBfwGPAX8BjQF/AWkBfwFWAWcBjgFRAWABTAG9AXsBOQFjATkBZwE5AWcBnAFzATgBdwEg
        AVgBQQFYAYMBXAFiAVwBIAFYASABWAFiAVwBgwFcAUEBWAEgAVgBOAF3AZwBcwE5AWciAAFaAWsBWgFr
        AVoBawE5AWcB/wF/AQMBRwECAUcBAwFHAQIBQwH/AX8BOQFnAVoBawFaAWsBWgFrDAABOgFnAf8BfwGy
        AX8BtAF7AVsBZwGqAV0BoAFYAUABTAFqAVkBnAFzAfcBXgE5AWcBGAF3AQABXAFBAVwBgwFgAWIBYAEA
        AVwBcwFyAXMBcgEAAVwBYgFgAYMBYAFBAVwBAAFcARgBdwE5AWcoAAE5AWcB/wF/AQMBSwECAUcBAgFH
        AQEBRwH+AXsBGAFjFAABWgFrAf8BfwEWAWsBhAFlASABZQEiAV0BQAFMAQ8BZgGcAXMBGAFjAZwBcwGL
        AWkBAAFcAYMBZAFjAWABAAFgATEBcgHeAXsB3gF7ATEBcgEAAWABgwFgAYMBZAEAAVwBiwFtAXsBbygA
        ATkBZwH/AX8BAAFHAQABRwEAAUcBAAFDAf4BewE5AWcWAAGcAXMBNQF7ASABbQEgAWUBwAFYAU8BagH/
        AX8BWgFrAgABOQFnAd4BfwHFAWgBAAFkAQABZAEwAXIB/wF/ATkBZwE5AWcB/wF/ATABcgEAAWQBAAFk
        AcUBaAHeAX8BOQFnKAABWgFrAf8BfwGOAWMBRQFTAUUBUwFrAV8B/wF/ATkBZxYAAVoBawHeAXsB8wF2
        AYUBaQEWAXcB3gF7AVoBawYAAVoBawH/AX8BCAFtATABdgH/AX8BOQFnBAABOQFnAf8BfwExAXYBCAFt
        Af8BfwFaAWssAAE5AWcBnAFzAb0BdwG9AXcBvQF3ATkBZxoAATkBZwGcAXMBvQF3AXsBbwFaAWsKAAFa
        AWsBvQF3AZwBcwE5AWcIAAE5AWcBnAFzAb0BdwFaAWskAAFCAU0BPgcAAT4DAAEoAwABQAMAARADAAEB
        AQABAQUAAYAXAAP/AQAB+AEfAQcB/wLDAgAB8AEPAQEB/wKBAgAB8AEPAQABfwQAAfABDwEAAT8EAAGA
        AQEBgAE/BgABgAEfBgABwAEPAYABAQQAAcABBwHAAQMEAAHgAQMBwAEDBAAB4AEDAYABAQQAAfAFAAGA
        AQEB+AUAAfABDwH8BQAB8AEPAf4BAQQAAfABDwH+AQMCgQIAAfgBHwH/AQcCwwIACw==
</value>
  </data>
  <metadata name="ImageList24x24.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>153, 17</value>
  </metadata>
  <data name="ImageList24x24.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj00LjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAAD8
        FgAAAk1TRnQBSQFMAgEBAwIAAQEBAAEBARgBAAEYAQAE/wERAQAI/wFCAU0BNgcAATYDAAEoAwABYAMA
        ARgDAAEBAQABEAYAARIYAAH/AX8BnQFzAXsBbwGcAXMB/wF/EAAB/wF/AZwBcwF7AW8BnQFzAf8BfwoA
        Af8BfwGcAXMBewFvAXsBbwF7AW8BewFvAXsBbwF7AW8BewFvAXsBbwF7AW8BewFvAXsBbwF7AW8BewFv
        AXsBbwF7AW8BewFvAZwBcwH/AX8WAAH/AX8B/wF/Af8Bf0wAAf8BfwF7AW8B/wF7Af8BewH/AXsBnAFz
        Af8BfwwAAf8BfwGcAXMB/wF7Af8BewH/AnsBbwH/AX8GAAH/AX8BewFvAf4BfwH+AX8B/gF/Af4BfwH+
        AX8B/gF/Af4BfwH+AX8B/gF/Af4BfwH+AX8B/gF/Af4BfwH+AX8B/gF/Af4BfwH+AX8B/gF/AXsBbwHe
        AXsUAAG9AXcBewFvAZwBbwHeAXtIAAH/AX8BewFvAf8BewEYAWsBxgFAAe4BVQH/AXsBnAFzAf8BfwgA
        Af8BfwGcAW8B/wF7Ae4BVQHGAUABGAFrAf8CewFvAf8BfwIAAf8BfwF7AW8B/gF/AbkBQgHVARkB9gEZ
        AfYBGQH2ARkB9gEZAfYBGQH2ARkB9gEZAfYBGQHVARkB1QEZAdUBGQHVARkB1QEZAdUBGQGUARUBVwE2
        Ad4BfwGcAXcB/wF/DgAB/wF/AXsBbwG/AXsB3wF/Ad8BfwGcAXMB/wF/RAAB/wF/AXsBbwH/AXsB1gFm
        AQABNAEAATQBAAE0AWoBTQH/AXsBnQFzAf8BfwQAAf8BfwGdAXMB/wF7AWoBTQEAATQBAAE0AQABNAHW
        AWYB/wJ7AW8B/wF/Ab0BdwH+AX8BmAE6AdIBAAFTAQEBMwEBATMBAQEzAQEBMwEBATMBAQEzAQEBMwEB
        ATMBAQETAQEBEgEBARIBAQESAQEB8gEAAfEBAAESAQEBjwEAAfUBIQH+AX8BnAFzDAAB/wF/AXsBbwHf
        AX8BeAFnAWgBOgGuAUoB3wJ7AW8B/wF/QgABvQF3Af8BewH3AWoBAAE4AUEBPAFjAUABYwFAAQABOAGM
        AVEB/wF/AZwBcwH/AX8B/wF/AZwBcwH/AX8BjAFRAQABOAFjAUABYwFAAUEBPAEAATgB9wFqAf8BewG9
        AXcBnAFzAf4BfwF0AQEBdAEBAf8BfwGYAT4BNAEBAXQBAQF0AQEBdAEBAXQBAQF0AQEBdAEBAXQBAQF0
        AQEBUwEBAVMBAQESAQEBFgEmAf8BfwGUAREB0AEAAb0BdwF7AW8KAAH/AX8BewFvAd8BfwFWAWMBAQEm
        AQEBJgHgAR0B8AFOAd8BfwGcAXNCAAF8AW8B/wF7AWMBQAEgATwBgwFAAWMBQAFjAUABYgFAAQABOAGM
        AVUB/wF7AXwBbwF8AW8B/wF/AawBVQEAATgBYgFAAWMBQAFjAUABgwFAASABPAFjAUAB/wF7AZwBbwGc
        AXMB/gF/AZUBBQFUAQEBugE+AfYBEQF0AQEBlQEBAZUBAQGVAQEBlQEBAXUBAQF0AQEBdAEBAXQBAQF0
        AQEBVAEBAVMBAQGUAQkBuQFCAVMBAQESAQEBvQF3AXsBbwgAAf8BfwF7AW8B3wF/AXkBawEhASoBQwEu
        AUQBMgFEAS4BIgEqAZwBcwG+AXcB/wF/QAABnAFzAf8BewHmAUwBAAFAAYMBRAFjAUQBYwFEAWMBRAFi
        AUQBAAE8AWsBVQH/AX8B/wF/AWsBVQEAATwBYgFEAWMBRAFjAUQBYwFEAYMBRAEAAUAB5gFMAf8BewGc
        AXMBnAFzAf4BfwG2AQkBdQEBAVQBAQF1AQEBlQEBAZUBAQGVAQEBlQEBAZUBAQGVAQEBlQEBAXUBAQF0
        AQEBdAEBAXQBAQF0AQEBUwEBARIBAQESAQEBMgEBAb0BdwF7AW8GAAH/AX8BewFvAd8BfwF4AWcBQgEu
        AUMBLgFkATIBZAEyAWQBMgFBASoBywFGAf8BfwF7AW9AAAH/AX8B/wF7Ab0BdwFiAUQBIAFEAYMBSAFj
        AUgBYwFIAWMBSAFjAUgBAAFAAe8BXQHvAV0BAAFAAWIBSAFjAUgBYwFIAWMBSAGDAUgBIAFEAWIBRAG9
        AXcB/wF7Af8BfwGcAXMB/gF/AdYBCQF1AQEBtgEBAbYBAQG2AQEBtgEBAZYBAQGWAQEBlgEBAZUBAQGV
        AQEBlQEBAZUBAQF1AQEBdAEBAXQBAQF0AQEBUwEBATMBAQEzAQEB3gJ7AW8EAAH/AX8BewFvAd8BfwF5
        AWsBQQEuAWMBMgGEATYBhAE2AYQBNgGEATYBZAE2AUABKgF3AWcB3wF/Ad4Be0AAAd4BewH/AXsBnAF3
        AYMBSAEgAUQBgwFIAYMBSAGDAUgBgwFIAWMBSAEgAUQBIAFEAWMBSAGDAUgBgwFIAYMBSAGDAUgBIAFE
        AYMBSAGcAXcB/wF7Ad4BewIAAZwBcwH+AX8B1wEJAZYBAQG2AQEBtgEBAbYBAQG2AQEBtgEBAbYBAQG2
        AQEBtgEBAZYBAQGVAQEBlQEBAZUBAQF1AQEBdAEBAXQBAQF0AQEBUwEBATMBAQHeAnsBcwIAAf8BfwGc
        AXMB3wF/AZoBbwGDATIBgwEyAYQBNgGEATYBhAE2AYQBNgGEATYBhAE2AYMBMgGlAToB3wF7AXwBcwH/
        AX9AAAHeAXsB/wF/Ad4BdwGDAUwBIAFIAYMBTAGDAUwBgwFMAYMBTAGDAUwBgwFMAYMBTAGDAUwBgwFM
        AYMBTAEgAUgBgwFMAd4BdwH/AX8B3gF7BAABnAFzAf4BfwHXAQkBlgEBAbcBAQG3AQEBtwEBAbcBAQG3
        AQEBtgEBAbYBAQG2AQEBtgEBAbYBAQGVAQEBlQEBAZUBAQF1AQEBdAEBAXQBAQFTAQEBUwEBAd4BewGb
        AXMCAAGcAXMB/wF/AZoBbwGCATIBgwE2AaQBOgGkAToBpAE6AaQBOgGkAToBpAE6AaQBOgGkAToBgAEu
        AS8BUwH/AX8BnAFzQgAB3gF7Af8BfwG9AXcBYgFMASABSAGDAVABgwFQAYMBUAGDAVABgwFQAYMBUAGD
        AVABgwFQASABSAFiAUwBvQF3Af8BfwHeAXsGAAGcAXMB/gF/AfcBCQG2AQEB1wEBAdcBAQHXAQEB1wEB
        AdcBAQHXAQEBtwEBAbYBAQG2AQEBtgEBAbYBAQGWAQEBlQEBAZUBAQF1AQEBdAEBAVQBAQFTAQEB3gF7
        AZsBcwHeAXsB3wF/AbsBcwGiATYBogE2AcQBOgHEAToBxAE6AcQBOgGiATYBogE2AcQBOgHEAToBxAE6
        AaQBOgGiATYBvAF3Ad8BfwH/AX9CAAHeAXsB/wF7Ad4BewHmAVQBQQFQAYMBUAGDAVABgwFQAYMBUAGD
        AVABgwFQAUEBUAHmAVQB3gF7Af8BewHeAXsIAAGcAXMB/wF/AfgBCQG3AQEB1wEBAdcBAQHXAQEB1wEB
        AdcBAQHXAQEB1wEBAdcBAQG3AQEBtgEBAbYBAQG2AQEBlgEBAZUBAQGVAQEBdQEBAVQBAQFTAQEB3gF7
        AZwBcwGcAXMB3wF/AeYBPgHCATYBxAE+AcQBPgHEAT4BxAE+AaABMgFQAVcBUAFbAaEBNgHEAT4BxAE+
        AcQBPgHCATYB6AFGAf8BfwF8AW8B/wF/QAAB/wF/Ab0BcwH/AX8BzgFhASABUAGDAVQBgwFUAYMBVAGD
        AVQBgwFUAYMBVAEgAVABzgFhAf8BfwG9AXMB/wF/CAABnAFzAf8BfwH4AQkBtwEBAfgBAQHYAQEB2AEB
        AdgBAQHXAQEBtwEBAbcBAQG3AQEBtwEBAZYBAQGWAQEBlgEBAXUBAQF1AQEBlQEBAZUBAQF0AQEBVAEB
        Ad4BfwGcAXMBnAFzAf8BfwEGAUMBwQE2AeUBPgHkAT4B5AE+AcABNgEsAVMB/wF/Af8BfwHkAT4B4wE6
        AeQBPgHkAT4B5AE+AcABNgFxAV8B/wF/AZwBcz4AAf8BfwG9AXMB/wF/Aa0BYQEAAVABYwFUAYMBVAGD
        AVQBgwFUAYMBVAGDAVQBgwFUAWMBVAEAAVABrQFhAf8BfwG9AXMB/wF/BgABnAFzAf8BfwEYAQoB2AEB
        AfgBAQHYAQEBGQEKATkBEgE5AQ4BOQEOARgBDgEYAQ4BGAEOARgBDgH4AQ0B9wENAfcBEQHWAQkBVQEB
        AXUBAQF0AQEBVAEBAf4BfwGcAXMB/wF/Af8BfwHdAXcB5AE+AeIBOgEEAUMBwAE2ASsBUwH/AX8BvgF3
        Ad8BfwG4AW8B4AE2AeQBQgEEAUMBBAFDAeQBPgHhAToBugFzAf8BfwG9AXc6AAH/AX8B3gF3Af8BfwHN
        AWUBAAFQAYMBWAGDAVgBgwFYAYMBWAGDAVgBgwFYAYMBWAGDAVgBgwFYAYMBWAEAAVABzQFlAf8BfwG+
        AXcB/wF/BAABnAFzAf8BfwEZAQoB2AEBAdgBAQHbAToB/QF/Ad0BfwHdAX8B3QF/Ad0BfwHdAX8B3QF/
        Af0BfwH+AX8B/wF/Af8BfwH+AX8BmQE2AVQBAQF0AQEBdAEBAf4BfwGcAXMCAAHeAXsB/wF/Ad0BdwHj
        AUIBwAE2ASoBUwH/AX8B3gF7Af8BfwG9AXcB/wF/AU0BVwHgAToBBAFDAQQBQwEEAUMB4gE+AQUBRwH/
        AX8B3wF/Af8BfzYAAf8BfwG9AXMB/wF/Ac4BaQEAAVQBgwFYAYMBXAGDAVwBgwFcAYMBXAFiAVgBYgFY
        AYMBXAGDAVwBgwFcAYMBXAGDAVgBAAFUAc4BaQH/AX8BvQFzAf8BfwIAAZwBcwH/AX8BGQEKAdgBAQH5
        AQEBvQF3Ad0BfwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwH+AX8BuQFGAZQBHQE2AS4B/gF/AZ0BcwFU
        AQEBdAEBAXQBAQH/AX8BnAFzBAAB3gF7Af8BfwHdAXsBlgFrAf8BfwHfAX8B/wF/AgAB/wF/Ad4BewH/
        AX8BAwFDAQIBQwEEAUcBBAFHAQQBRwHgAT4BKgFTAf8BfwG9AXcB/wF/MgAB/wF/Ab0BdwH/AX8BrAFp
        AQABWAGDAVwBgwFcAYMBXAGDAVwBgwFcASABXAHGAWAB5gFgASABXAGDAVwBgwFcAYMBXAGDAVwBgwFc
        AQABWAGsAWkB/wF/Ab0BdwH/AX8BnAFzAf8BfwE5AQoB+QEBAfkBAQG9AXcB3gF/Ab0BdwG9AXcBvQF3
        Ab0BdwG9AXcBvQF3Af8BfwH2ARkB0QEAATIBAQH+AX8BnQFzAXUBAQF1AQEBdAEBAf8BfwGcAXMGAAHe
        AXsBvgF7Af8BfwG9AXcB/wF/BgAB3gF7Af8BfwG4AW8B4AE+AQMBRwEDAUcBAwFHAQMBRwHgAT4BTAFX
        Af8BfwG9AXcB/wF/MAABnAFzAf8BfwEPAW4BAAFYAYMBYAGDAWABgwFgAYMBYAGDAWABIAFcAYMBYAH/
        AX8B/wF/AWIBYAEgAVwBgwFgAYMBYAGDAWABgwFgAYMBYAEAAVgBDwFuAf8BfwGcAXMBnAFzAf8BfwE5
        AQoB+QEBAfkBAQG+AXcB3gF/Ad4BewHeAXsB3gF7Ad4BewHeAXsB3gF7Af8BfwFYASIBVAEBAZUBAQH/
        AX8BvQFzAXUBAQF1AQEBdAEBAf8BfwGcAXMYAAG9AXcB/wF/AW4BXwHgAT4BAwFHAQMBRwEDAUcBAwFH
        AeABPgFvAV8B/wF/AZwBczAAAZwBcwH/AX8BQQFgAUEBYAGDAWABgwFgAYMBYAGDAWABIAFgAYMBYAG9
        AXsB/wF/Af8BfwG9AXsBgwFgASABYAGDAWABgwFgAYMBYAGDAWABQQFgAUEBYAH/AX8BnAFzAZwBcwH/
        AX8BOgEKAfkBAQEaAQIB3gF3Af4BfwHeAXsB3gF7Ad4BewHeAXsB3gF7Ad4BewH/AX8BegEiAZcBAQHX
        AQEB/wF/Ab4BcwF1AQEBdQEBAXQBAQH/AX8BnAFzGgABvQF3Af8BfwFIAVMBAAFDAQMBSwEDAUsBAwFL
        AQABQwEAAUcB/wF/AZwBczAAAb0BdwH/AX8B7wFtAQABYAGDAWQBgwFkAYMBZAEgAWABgwFkAd4BfwH/
        AX8B3gF7Ad4BewH/AX8B3gF/AYMBZAEgAWABgwFkAYMBZAGDAWQBAAFgAQ8BbgH/AX8BvQF3AZwBcwH/
        AX8BOgEGAfkBAQEaAQIB3gF3Af8BfwHeAXsB3gF7Ad4BewHeAXsB3gF7Ad4BewH/AX8BmwEaAbgBAQG3
        AQEB/wF/Ab4BdwF1AQEBdQEBAXQBAQH/AX8BnAFzGgAB/wF/Af8BfwH/AX8BIgFLAQABRwEiAUsBAAFH
        AQABRwG2AW8B/wF/Ad4BezAAAf8BfwHeAXsB/wF/AYwBbQEAAWQBYgFkASABZAFiAWQB3gF/Af8BfwHe
        AXsEAAHeAXsB/wF/Ab0BfwFhAWQBIAFkAWIBZAEAAWQBjAFtAf8BfwHeAXsB/wF/Ab0BdwH/AX8BegEa
        AdkBAQEaAQIB3wF7Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8BHQFDAToBDgF6ASYB/wF/
        Ab4BcwF1AQEBNAEBAZUBAQH/AX8BnAFzHAAB3gF7Af8BfwH8AXsBIgFPAQABQwFIAVcB/QF7Af8BfwG9
        AXc0AAH/AX8B3gF7Af8BfwHOAXEBAAFkAYQBaAG9AX8B/wF/Ad4BewgAAd4BewH/AX8BvQF/AaQBaAEA
        AWQBzgFxAf8BfwHeAXsB/wF/AgAB/wF/Af8BfwHfAXcBOgEGAdkBAQHfAXcB/wF/Af8BfwH/AX8B/wF/
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8BvgFzARQBAQF0AQEBfQFnAf8BfwHeAXseAAHe
        AXsB/wF/Af8BfwHbAXcB/wF/Af8BfwHeAXs4AAH/AX8B3gF7Af8BfwG9AX8B/wF/Af8BfwHeAXsMAAHe
        AXsB/wF/Af8BfwG9AX8B/wF/Ad4BewH/AX8GAAHeAXsB/wF/Af8BfwG/AW8B/wF/Af8BfwH/AX8B/wF/
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwG+AW8B/wF/Af8BfwG9AXciAAHe
        AXsB3gF7Af8BfwG9AXdAAAG9AXcBvQF3Ab0BdwHeAXsQAAHeAXsBvQF3Ab0BdwG9AXcMAAH/AX8BvQF3
        Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3
        Ab0BdwG9AXcB3gF7JgAB/wF/Af8BfwH/AX84AAFCAU0BPgcAAT4DAAEoAwABYAMAARgDAAEBAQABAQUA
        ASABARYAA/8BAAHgAf8BBwHAAQABAwH/AY8B/wMAAcABfgEDAYABAAEBAf8BhwH/AwABgAE8AQEDAAH+
        AQMB/wQAARgEAAH8AQEB/wkAAfgBAQH/CQAB8AEAAf8JAAHgAQAB/wkAAcABAAF/AwABgAEAAQEDAAGA
        AQABPwMAAcABAAEDAwABgAEAAT8DAAHgAQABBwUAAR8DAAHwAQABDwUAAQ8DAAHwAQABDwUAAQ8DAAHg
        AQABBwUAAQcDAAHAAQABAwMAAYABAAEDAwABgAEAAQEDAAHAAUABAQkAAuAKAAH/AfAKAAH/AfgKAAH/
        AfgFAAEYBAAB/wH8AQEDAAGAATwBAQMAAf8B/gEDAwABwAF+AQMBgAEAAQEC/wEPAwAB8AH/AQ8BwAEA
        AQMC/wGPAwAL
</value>
  </data>
  <metadata name="MediaNameColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="MediaID.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="LabelControl3.Text" xml:space="preserve">
    <value>This section allows you to configure various properties of this category. If the category is made dormant by ticking the box below, it will be hidden when creating new provisional bookings and contracts.</value>
  </data>
  <data name="LabelControl10.Text" xml:space="preserve">
    <value>This section allows you to configure applicable media services. If this category permits the installation of a particular media service, add the media service to the list. If it is not, remove the media service from the list. An empty list means that this category will be unusable because it won't permit any media installations.</value>
  </data>
  <metadata name="StoreID.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="StoreName.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="StoreNumber.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="RegionName.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="isProhibited.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>202</value>
  </metadata>
</root>