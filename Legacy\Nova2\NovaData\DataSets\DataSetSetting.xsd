<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DataSetSetting" targetNamespace="http://tempuri.org/DataSetSetting.xsd" xmlns:mstns="http://tempuri.org/DataSetSetting.xsd" xmlns="http://tempuri.org/DataSetSetting.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" GeneratorFunctionsComponentClassName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" UserFunctionsComponentName="QueriesTableAdapter" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="DBConnection" ConnectionStringObject="" IsAppSettingsProperty="True" Modifier="Assembly" Name="DBConnection (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.NovaData.My.MySettings.GlobalReference.Default.DBConnection" Provider="System.Data.SqlClient">
          </Connection>
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="SettingGroupTableAdapter" GeneratorDataComponentClassName="SettingGroupTableAdapter" Name="SettingGroup" UserDataComponentName="SettingGroupTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.dbo.SettingGroup" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="False" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT        SettingGroupID, SettingGroupName
FROM            SettingGroup</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="SettingGroupID" DataSetColumn="SettingGroupID" />
              <Mapping SourceColumn="SettingGroupName" DataSetColumn="SettingGroupName" />
            </Mappings>
            <Sources>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="SettingTableAdapter" GeneratorDataComponentClassName="SettingTableAdapter" Name="Setting" UserDataComponentName="SettingTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.dbo.Setting" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="True" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="True" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="True">
                    <CommandText>SELECT        SettingID, SettingGroupID, SettingName, SettingValue
FROM            Setting</CommandText>
                    <Parameters>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>UPDATE [Setting] SET [SettingGroupID] = @SettingGroupID, [SettingName] = @SettingName, [SettingValue] = @SettingValue WHERE (([SettingID] = @Original_SettingID) AND ([SettingGroupID] = @Original_SettingGroupID) AND ([SettingName] = @Original_SettingName) AND ([SettingValue] = @Original_SettingValue));
SELECT SettingID, SettingGroupID, SettingName, SettingValue FROM Setting WHERE (SettingID = @SettingID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@SettingGroupID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="SettingGroupID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@SettingName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="SettingName" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@SettingValue" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="SettingValue" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_SettingID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="SettingID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_SettingGroupID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="SettingGroupID" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_SettingName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="SettingName" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_SettingValue" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="SettingValue" SourceColumnNullMapping="False" SourceVersion="Original">
                      </Parameter>
                      <Parameter AllowDbNull="False" AutogeneratedName="SettingID" ColumnName="SettingID" DataSourceName="NovaDB.dbo.Setting" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@SettingID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="SettingID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="SettingID" DataSetColumn="SettingID" />
              <Mapping SourceColumn="SettingGroupID" DataSetColumn="SettingGroupID" />
              <Mapping SourceColumn="SettingName" DataSetColumn="SettingName" />
              <Mapping SourceColumn="SettingValue" DataSetColumn="SettingValue" />
            </Mappings>
            <Sources>
            </Sources>
          </TableAdapter>
        </Tables>
        <Sources>
        </Sources>
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DataSetSetting" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="DataSetSetting" msprop:Generator_DataSetName="DataSetSetting">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="SettingGroup" msprop:Generator_UserTableName="SettingGroup" msprop:Generator_RowDeletedName="SettingGroupRowDeleted" msprop:Generator_RowChangedName="SettingGroupRowChanged" msprop:Generator_RowClassName="SettingGroupRow" msprop:Generator_RowChangingName="SettingGroupRowChanging" msprop:Generator_RowEvArgName="SettingGroupRowChangeEvent" msprop:Generator_RowEvHandlerName="SettingGroupRowChangeEventHandler" msprop:Generator_TableClassName="SettingGroupDataTable" msprop:Generator_TableVarName="tableSettingGroup" msprop:Generator_RowDeletingName="SettingGroupRowDeleting" msprop:Generator_TablePropName="SettingGroup">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="SettingGroupID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msprop:Generator_UserColumnName="SettingGroupID" msprop:Generator_ColumnPropNameInRow="SettingGroupID" msprop:Generator_ColumnVarNameInTable="columnSettingGroupID" msprop:Generator_ColumnPropNameInTable="SettingGroupIDColumn" type="xs:int" />
              <xs:element name="SettingGroupName" msdata:ReadOnly="true" msprop:Generator_UserColumnName="SettingGroupName" msprop:Generator_ColumnPropNameInRow="SettingGroupName" msprop:Generator_ColumnVarNameInTable="columnSettingGroupName" msprop:Generator_ColumnPropNameInTable="SettingGroupNameColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Setting" msprop:Generator_UserTableName="Setting" msprop:Generator_RowDeletedName="SettingRowDeleted" msprop:Generator_RowChangedName="SettingRowChanged" msprop:Generator_RowClassName="SettingRow" msprop:Generator_RowChangingName="SettingRowChanging" msprop:Generator_RowEvArgName="SettingRowChangeEvent" msprop:Generator_RowEvHandlerName="SettingRowChangeEventHandler" msprop:Generator_TableClassName="SettingDataTable" msprop:Generator_TableVarName="tableSetting" msprop:Generator_RowDeletingName="SettingRowDeleting" msprop:Generator_TablePropName="Setting">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="SettingID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msprop:Generator_UserColumnName="SettingID" msprop:Generator_ColumnPropNameInRow="SettingID" msprop:Generator_ColumnVarNameInTable="columnSettingID" msprop:Generator_ColumnPropNameInTable="SettingIDColumn" type="xs:int" />
              <xs:element name="SettingGroupID" msdata:ReadOnly="true" msprop:Generator_UserColumnName="SettingGroupID" msprop:Generator_ColumnPropNameInRow="SettingGroupID" msprop:Generator_ColumnVarNameInTable="columnSettingGroupID" msprop:Generator_ColumnPropNameInTable="SettingGroupIDColumn" type="xs:int" />
              <xs:element name="SettingName" msdata:ReadOnly="true" msprop:Generator_UserColumnName="SettingName" msprop:Generator_ColumnPropNameInRow="SettingName" msprop:Generator_ColumnVarNameInTable="columnSettingName" msprop:Generator_ColumnPropNameInTable="SettingNameColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="SettingValue" msprop:Generator_UserColumnName="SettingValue" msprop:Generator_ColumnPropNameInRow="SettingValue" msprop:Generator_ColumnVarNameInTable="columnSettingValue" msprop:Generator_ColumnPropNameInTable="SettingValueColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="SettingGroupName" msdata:ReadOnly="true" msdata:Expression="Parent(FK_Setting_SettingGroup).SettingGroupName" msprop:Generator_UserColumnName="SettingGroupName" msprop:Generator_ColumnVarNameInTable="columnSettingGroupName" msprop:Generator_ColumnPropNameInRow="SettingGroupName" msprop:Generator_ColumnPropNameInTable="SettingGroupNameColumn" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:SettingGroup" />
      <xs:field xpath="mstns:SettingGroupID" />
    </xs:unique>
    <xs:unique name="Setting_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Setting" />
      <xs:field xpath="mstns:SettingID" />
    </xs:unique>
  </xs:element>
  <xs:annotation>
    <xs:appinfo>
      <msdata:Relationship name="FK_Setting_SettingGroup" msdata:parent="SettingGroup" msdata:child="Setting" msdata:parentkey="SettingGroupID" msdata:childkey="SettingGroupID" msprop:Generator_UserRelationName="FK_Setting_SettingGroup" msprop:Generator_RelationVarName="relationFK_Setting_SettingGroup" msprop:Generator_UserChildTable="Setting" msprop:Generator_UserParentTable="SettingGroup" msprop:Generator_ParentPropName="SettingGroupRow" msprop:Generator_ChildPropName="GetSettingRows" />
    </xs:appinfo>
  </xs:annotation>
</xs:schema>