﻿Public Class SubformStoreGroupManager
    Dim GridBindingSource As BindingSource
    Dim DataSet As DataSet


    Public Sub New()

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        GridController = New GridManager _
        (GridItems,
        TextEditSearch,
        StoreGroups.GetListData(My.Settings.DBConnection, String.Empty, Nothing),
        My.Settings.DBConnection,
        PictureAdvancedSearch,
        PictureClearSearch,
        ButtonEdit,
        ButtonDelete)

    End Sub
#Region "Event Handlers"
    Private Sub Subform_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        ' Get the datasource of the grid.
        GridBindingSource = GridItems.DataSource

        ' Get the dataset being used by the grid.
        DataSet = CType(GridBindingSource.List, DataView).Table.DataSet

    End Sub
    Private Sub ButtonAdd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonAdd.Click
        OpenDetailSubform(True)
    End Sub
    Private Sub ButtonEdit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonEdit.Click
        OpenDetailSubform(False)
    End Sub
    Private Sub GridItems_CellMouseDoubleClick _
    (ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) _
    Handles GridItems.CellMouseDoubleClick
        If e.RowIndex > -1 Then
            ' The row double-clicked is NOT the column header row. Handle the event.
            ButtonEdit_Click(sender, e)
        End If
    End Sub
#End Region

#Region "Private Methods"

    Private Sub OpenDetailSubform(ByVal NewItem As Boolean)

        If NewItem = False Then
            ' User clicked the Edit button.
            Dim ParentForm As BaseForm = CType(TopLevelControl, BaseForm)
            If GridItems.SelectedRows.Count < 1 Then
                ' Stop because no rows are selected.
                ParentForm.ShowMessage("Please select the row to edit before using the 'Edit' button.")
                Exit Sub
            ElseIf GridItems.SelectedRows.Count > 1 Then
                ' Stop because more than one row is selected.
                ParentForm.ShowMessage("Please select only one row to edit before using the 'Edit' button.")
                Exit Sub
            End If
        End If

        ' Create table adapters to populate related tables.
        Dim GroupChainAdapter As New DataSetStoreGroupsTableAdapters.GroupChainTableAdapter
        Dim ChainGroupStoresAdapter As New DataSetStoreGroupsTableAdapters.ChainGroupStoreTableAdapter

        ' Set the sql connection of the new adapters.
        Dim SqlCon As New SqlClient.SqlConnection(My.Settings.DBConnection)
        GroupChainAdapter.Connection = SqlCon
        ChainGroupStoresAdapter.Connection = SqlCon

        ' Populate tables needed for adding new rows and editing existing rows.
        Try
            'GroupChainAdapter.Fill(DataSet.Tables("GroupChain"))
        Catch ex As Exception
            CType(TopLevelControl, BaseForm).ShowMessage _
            ("Rats!  Some data failed to load correctly." & vbCrLf & vbCrLf _
            & LiquidShell.LiquidAgent.GetErrorMessage(ex), "Data Load Error", MessageBoxIcon.Error)
        Finally
            GroupChainAdapter.Dispose()
        End Try

        ' Populate tables needed for editing existing rows.
        If NewItem = False Then
            Dim ParentID As Integer = CType(GridBindingSource.Current, DataRowView).Item("GroupChainID")
            Try
                ChainGroupStoresAdapter.Fill(DataSet.Tables("ChainGroupStore"), ParentID)
            Catch ex As Exception
                CType(TopLevelControl, BaseForm).ShowMessage _
                ("Rats!  Some data failed to load correctly." & vbCrLf & vbCrLf _
                & LiquidShell.LiquidAgent.GetErrorMessage(ex), "Data Load Error", MessageBoxIcon.Error)
            Finally
                ChainGroupStoresAdapter.Dispose()
            End Try
        End If

        ' Dispose the connection.
        SqlCon.Dispose()

        ' Open the form to edit the item.
        AddChild(New SubformStoreGroups(New StoreGroups(GridBindingSource, NewItem)))

    End Sub

#End Region
   
End Class
