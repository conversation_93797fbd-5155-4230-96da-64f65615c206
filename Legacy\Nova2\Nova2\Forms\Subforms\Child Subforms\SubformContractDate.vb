﻿Public Class SubformContractDate

    Private ConnectionString As String = String.Empty
    Private ContractID As Guid
    Private ContractDateDataSet As New DataSetContractDate
    Private ContractDateAdapter As New DataSetContractDateTableAdapters.ContractDateTableAdapter
    Private ContractActivityAdapter As New DataSetContractDateTableAdapters.ContractActivityDateTableAdapter

#Region "Fields"
    Private _InstallationInstructionsDate As Date?
    Private _PONumberDate As Date?
    Private _StoreListDate As Date?
    Private _ArtworkDate As Date?
#End Region

#Region "Properties"

    ReadOnly Property InstallationInstructionsDate As Date?
        Get
            Return _InstallationInstructionsDate
        End Get
    End Property

    ReadOnly Property PONumberDate As Date?
        Get
            Return _PONumberDate
        End Get
    End Property

    ReadOnly Property StoreListDate As Date?
        Get
            Return _StoreListDate
        End Get
    End Property

    ReadOnly Property ArtworkDate As Date?
        Get
            Return _ArtworkDate
        End Get
    End Property

    ReadOnly Property InstallationInstructionsEnabled As Boolean
        Get
            Dim ReturnValue As Boolean = False
            Dim Activity As DataSetContractDate.ContractActivityDateRow = ContractDateDataSet.ContractActivityDate.Rows(0)
            If Activity.InstallationInstructionsCaptured Then
                ReturnValue = True
            End If
            Return ReturnValue
        End Get
    End Property

    ReadOnly Property PONumberEnabled As Boolean
        Get
            Dim ReturnValue As Boolean = False
            Dim Activity As DataSetContractDate.ContractActivityDateRow = ContractDateDataSet.ContractActivityDate.Rows(0)
            If Activity.PONumberCaptured Then
                ReturnValue = True
            End If
            Return ReturnValue
        End Get
    End Property

    ReadOnly Property StoreListEnabled As Boolean
        Get
            Dim ReturnValue As Boolean = False
            Dim Activity As DataSetContractDate.ContractActivityDateRow = ContractDateDataSet.ContractActivityDate.Rows(0)
            If Activity.StoreListConfirmed Then
                ReturnValue = True
            End If
            Return ReturnValue
        End Get
    End Property

#End Region

#Region "Constructors"

    Sub New(ConString As String, IDofContract As Guid)

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        ConnectionString = ConString
        ContractID = IDofContract

    End Sub

#End Region

#Region "Event Handlers"

    Private Sub SubformContractDate_Load(sender As Object, e As EventArgs) Handles Me.Load
        LoadData()
        UpdateFormTitle()
        InitialiseControlValues()
        AddDataBindings()
    End Sub

    Private Sub HyperlinkHyperlinkInstallationInstructions_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles HyperlinkInstallationInstructions.Click

        Dim Hyperlink As LabelControl = sender
        Dim NewDate As Nullable(Of Date) = DateSelector.GetDate(False, Now)
        If NewDate.HasValue Then
            _InstallationInstructionsDate = NewDate.Value
            Hyperlink.DataBindings("Text").ReadValue()
        End If
        Save()

    End Sub

    Private Sub HyperlinkPONumber_Click(sender As Object, e As EventArgs) Handles HyperlinkPONumber.Click

        Dim Hyperlink As LabelControl = sender
        Dim NewDate As Nullable(Of Date) = DateSelector.GetDate(False, Now)
        If NewDate.HasValue Then
            _PONumberDate = NewDate.Value
            Hyperlink.DataBindings("Text").ReadValue()
        End If
        Save()

    End Sub

    Private Sub HyperlinkStoreList_Click(sender As Object, e As EventArgs) Handles HyperlinkStoreList.Click

        Dim Hyperlink As LabelControl = sender
        Dim NewDate As Nullable(Of Date) = DateSelector.GetDate(False, Now)
        If NewDate.HasValue Then
            _StoreListDate = NewDate.Value
            Hyperlink.DataBindings("Text").ReadValue()
        End If
        Save()

    End Sub

    Private Sub HyperlinkArtwork_Click(sender As Object, e As EventArgs) Handles HyperlinkArtwork.Click

        Dim Hyperlink As LabelControl = sender
        Dim NewDate As Nullable(Of Date) = DateSelector.GetDate(False, Now)
        If NewDate.HasValue Then
            _ArtworkDate = NewDate.Value
            Hyperlink.DataBindings("Text").ReadValue()
        End If
        Save()

    End Sub

    Private Sub ButtonBack_Click(sender As Object, e As EventArgs) Handles ButtonBack.Click
        RevertToParentSubform()
    End Sub

#End Region

#Region "Internal Methods"

    Private Sub LoadData()

        Using SqlCon As New SqlClient.SqlConnection(ConnectionString)
            ContractDateAdapter.Connection = SqlCon
            ContractActivityAdapter.Connection = SqlCon
            ContractDateAdapter.Fill(ContractDateDataSet.ContractDate, ContractID)
            ContractActivityAdapter.Fill(ContractDateDataSet.ContractActivityDate, ContractID)
        End Using

    End Sub

    Private Sub AddDataBindings()

        HyperlinkInstallationInstructions.DataBindings.Add("Text", Me, "InstallationInstructionsDate", True, DataSourceUpdateMode.Never, 0, "d MMMM yyyy")
        HyperlinkPONumber.DataBindings.Add("Text", Me, "PONumberDate", True, DataSourceUpdateMode.Never, 0, "d MMMM yyyy")
        HyperlinkStoreList.DataBindings.Add("Text", Me, "StoreListDate", True, DataSourceUpdateMode.Never, 0, "d MMMM yyyy")
        HyperlinkArtwork.DataBindings.Add("Text", Me, "ArtworkDate", True, DataSourceUpdateMode.Never, 0, "d MMMM yyyy")

        HyperlinkInstallationInstructions.DataBindings.Add("Visible", Me, "InstallationInstructionsEnabled")
        HyperlinkPONumber.DataBindings.Add("Visible", Me, "PONumberEnabled")
        HyperlinkStoreList.DataBindings.Add("Visible", Me, "StoreListEnabled")

    End Sub

    Private Sub InitialiseControlValues()

        Dim Table As DataSetContractDate.ContractDateDataTable = ContractDateDataSet.ContractDate
        If Table.Rows.Count > 0 Then
            Dim ContractDate As DataSetContractDate.ContractDateRow = Table.Rows(0)
            If IsDBNull(ContractDate("InstallationInstructions")) = False Then
                _InstallationInstructionsDate = ContractDate.InstallationInstructions
            End If
            If IsDBNull(ContractDate("PONumber")) = False Then
                _PONumberDate = ContractDate.PONumber
            End If
            If IsDBNull(ContractDate("StoreList")) = False Then
                _StoreListDate = ContractDate.StoreList
            End If
            If IsDBNull(ContractDate("Artwork")) = False Then
                _ArtworkDate = ContractDate.Artwork
            End If
        End If

    End Sub

    Private Sub UpdateFormTitle()

        Dim ContractRow As DataSetContractDate.ContractActivityDateRow = ContractDateDataSet.ContractActivityDate.Rows(0)
        LabelTitle.Text = "Contract " & ContractRow.ContractNumber & " Data Entry Dates"

    End Sub

    Protected Overrides Function Save() As Boolean

        ' Get the row for the current contract.
        Dim ContractDate As DataSetContractDate.ContractDateRow
        Dim Table As DataSetContractDate.ContractDateDataTable = ContractDateDataSet.ContractDate
        If Table.Rows.Count > 0 Then
            ContractDate = Table.Rows(0)
        Else
            ' No row exists for this contract. Create one.
            ContractDate = Table.NewRow
            ContractDate.ContractID = ContractID
            Table.Rows.Add(ContractDate)
        End If

        ' Update the dataset.
        If InstallationInstructionsDate.HasValue Then
            ContractDate.InstallationInstructions = InstallationInstructionsDate
        End If
        If StoreListDate.HasValue Then
            ContractDate.StoreList = StoreListDate
        End If
        If PONumberDate.HasValue Then
            ContractDate.PONumber = PONumberDate
        End If
        If ArtworkDate.HasValue Then
            ContractDate.Artwork = ArtworkDate
        End If

        ' Update the database.
        Dim SqlCon As New SqlClient.SqlConnection(ConnectionString)
        Try
            ContractDateAdapter.Connection = SqlCon
            ContractActivityAdapter.Connection = SqlCon
            ContractDateAdapter.Update(Table)
        Catch ex As Exception
        Finally
            SqlCon.Dispose()
        End Try

    End Function

#End Region

End Class
