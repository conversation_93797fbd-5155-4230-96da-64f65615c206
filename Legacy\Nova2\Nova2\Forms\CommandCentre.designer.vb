<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()>
Partial Class CommandCentre
    Inherits LiquidShell.BaseForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Dim SuperToolTip1 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem1 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem1 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Me.PanelTitle = New DevExpress.XtraEditors.PanelControl()
        Me.LabelControlSubTitle = New DevExpress.XtraEditors.LabelControl()
        Me.PanelTitleColor = New DevExpress.XtraEditors.PanelControl()
        Me.PictureBoxLogo = New System.Windows.Forms.PictureBox()
        Me.PanelFooter = New DevExpress.XtraEditors.PanelControl()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.HyperlinkSettings = New DevExpress.XtraEditors.LabelControl()
        Me.PictureBoxAccount = New System.Windows.Forms.PictureBox()
        Me.LabelLoginInfo = New DevExpress.XtraEditors.LabelControl()
        Me.ContentSwitcherMaster = New LiquidShell.ContentSwitcher()
        Me.PanelFooterBorder = New DevExpress.XtraEditors.PanelControl()
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PanelTitle, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelTitle.SuspendLayout()
        CType(Me.PanelTitleColor, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBoxLogo, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PanelFooter, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelFooter.SuspendLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureBoxAccount, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PanelFooterBorder, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'PanelTitle
        '
        Me.PanelTitle.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelTitle.Appearance.BackColor2 = System.Drawing.Color.Silver
        Me.PanelTitle.Appearance.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical
        Me.PanelTitle.Appearance.Options.UseBackColor = True
        Me.PanelTitle.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PanelTitle.Controls.Add(Me.LabelControlSubTitle)
        Me.PanelTitle.Controls.Add(Me.PanelTitleColor)
        Me.PanelTitle.Controls.Add(Me.PictureBoxLogo)
        Me.PanelTitle.Dock = System.Windows.Forms.DockStyle.Top
        Me.PanelTitle.Location = New System.Drawing.Point(0, 0)
        Me.PanelTitle.Name = "PanelTitle"
        Me.PanelTitle.Size = New System.Drawing.Size(984, 82)
        Me.PanelTitle.TabIndex = 5
        '
        'LabelControlSubTitle
        '
        Me.LabelControlSubTitle.Appearance.Font = New System.Drawing.Font("Verdana", 6.0!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControlSubTitle.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControlSubTitle.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelControlSubTitle.Location = New System.Drawing.Point(15, 58)
        Me.LabelControlSubTitle.Margin = New System.Windows.Forms.Padding(3, 0, 3, 3)
        Me.LabelControlSubTitle.Name = "LabelControlSubTitle"
        Me.LabelControlSubTitle.Size = New System.Drawing.Size(550, 10)
        Me.LabelControlSubTitle.TabIndex = 9
        Me.LabelControlSubTitle.Text = "THE PRIMEDIA INSTORE SPINAL COLUMN OF INNOVATION, INSPIRATION AND OTHER POSITIVE " &
    "THINGS LIKE THAT"
        '
        'PanelTitleColor
        '
        Me.PanelTitleColor.Appearance.BackColor = System.Drawing.Color.SteelBlue
        Me.PanelTitleColor.Appearance.Options.UseBackColor = True
        Me.PanelTitleColor.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PanelTitleColor.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.PanelTitleColor.Location = New System.Drawing.Point(0, 78)
        Me.PanelTitleColor.Margin = New System.Windows.Forms.Padding(3, 0, 3, 3)
        Me.PanelTitleColor.Name = "PanelTitleColor"
        Me.PanelTitleColor.Size = New System.Drawing.Size(984, 4)
        Me.PanelTitleColor.TabIndex = 7
        '
        'PictureBoxLogo
        '
        Me.PictureBoxLogo.BackColor = System.Drawing.Color.Transparent
        Me.PictureBoxLogo.Image = Global.Nova2.My.Resources.Resources.Logo_PNG_159x39
        Me.PictureBoxLogo.Location = New System.Drawing.Point(15, 16)
        Me.PictureBoxLogo.Margin = New System.Windows.Forms.Padding(9, 9, 3, 3)
        Me.PictureBoxLogo.Name = "PictureBoxLogo"
        Me.PictureBoxLogo.Size = New System.Drawing.Size(159, 39)
        Me.PictureBoxLogo.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.PictureBoxLogo.TabIndex = 5
        Me.PictureBoxLogo.TabStop = False
        '
        'PanelFooter
        '
        Me.PanelFooter.Appearance.BackColor = System.Drawing.Color.Gainsboro
        Me.PanelFooter.Appearance.BackColor2 = System.Drawing.Color.Silver
        Me.PanelFooter.Appearance.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical
        Me.PanelFooter.Appearance.Options.UseBackColor = True
        Me.PanelFooter.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PanelFooter.Controls.Add(Me.PictureBox1)
        Me.PanelFooter.Controls.Add(Me.HyperlinkSettings)
        Me.PanelFooter.Controls.Add(Me.PictureBoxAccount)
        Me.PanelFooter.Controls.Add(Me.LabelLoginInfo)
        Me.PanelFooter.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.PanelFooter.Location = New System.Drawing.Point(0, 633)
        Me.PanelFooter.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Flat
        Me.PanelFooter.LookAndFeel.UseDefaultLookAndFeel = False
        Me.PanelFooter.Name = "PanelFooter"
        Me.PanelFooter.Size = New System.Drawing.Size(984, 41)
        Me.PanelFooter.TabIndex = 6
        '
        'PictureBox1
        '
        Me.PictureBox1.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureBox1.BackColor = System.Drawing.Color.Transparent
        Me.PictureBox1.Image = Global.Nova2.My.Resources.Resources.tools32
        Me.PictureBox1.Location = New System.Drawing.Point(891, 4)
        Me.PictureBox1.Margin = New System.Windows.Forms.Padding(3, 1, 3, 3)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(32, 32)
        Me.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.PictureBox1.TabIndex = 5
        Me.PictureBox1.TabStop = False
        '
        'HyperlinkSettings
        '
        Me.HyperlinkSettings.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.HyperlinkSettings.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkSettings.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkSettings.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkSettings.Location = New System.Drawing.Point(929, 14)
        Me.HyperlinkSettings.Margin = New System.Windows.Forms.Padding(3, 3, 0, 3)
        Me.HyperlinkSettings.Name = "HyperlinkSettings"
        Me.HyperlinkSettings.Size = New System.Drawing.Size(46, 13)
        ToolTipTitleItem1.Text = "Nova Settings"
        ToolTipItem1.LeftIndent = 6
        ToolTipItem1.Text = "Here you can change various system settings. Please DO NOT click the 'Self Destru" &
    "ct' button."
        SuperToolTip1.Items.Add(ToolTipTitleItem1)
        SuperToolTip1.Items.Add(ToolTipItem1)
        Me.HyperlinkSettings.SuperTip = SuperToolTip1
        Me.HyperlinkSettings.TabIndex = 7
        Me.HyperlinkSettings.Tag = "883, 14"
        Me.HyperlinkSettings.Text = "Settings"
        '
        'PictureBoxAccount
        '
        Me.PictureBoxAccount.BackColor = System.Drawing.Color.Transparent
        Me.PictureBoxAccount.Image = Global.Nova2.My.Resources.Resources.user32
        Me.PictureBoxAccount.Location = New System.Drawing.Point(3, 4)
        Me.PictureBoxAccount.Margin = New System.Windows.Forms.Padding(3, 1, 3, 3)
        Me.PictureBoxAccount.Name = "PictureBoxAccount"
        Me.PictureBoxAccount.Size = New System.Drawing.Size(32, 32)
        Me.PictureBoxAccount.SizeMode = System.Windows.Forms.PictureBoxSizeMode.AutoSize
        Me.PictureBoxAccount.TabIndex = 5
        Me.PictureBoxAccount.TabStop = False
        '
        'LabelLoginInfo
        '
        Me.LabelLoginInfo.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelLoginInfo.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelLoginInfo.Location = New System.Drawing.Point(41, 14)
        Me.LabelLoginInfo.Margin = New System.Windows.Forms.Padding(3, 3, 0, 3)
        Me.LabelLoginInfo.Name = "LabelLoginInfo"
        Me.LabelLoginInfo.Size = New System.Drawing.Size(81, 13)
        Me.LabelLoginInfo.TabIndex = 5
        Me.LabelLoginInfo.Text = "Logged in as: "
        '
        'ContentSwitcherMaster
        '
        Me.ContentSwitcherMaster.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.ContentSwitcherMaster.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ContentSwitcherMaster.Appearance.Options.UseBackColor = True
        Me.ContentSwitcherMaster.Appearance.Options.UseFont = True
        Me.ContentSwitcherMaster.ButtonHeight = 50
        Me.ContentSwitcherMaster.Dock = System.Windows.Forms.DockStyle.Fill
        Me.ContentSwitcherMaster.Location = New System.Drawing.Point(0, 82)
        Me.ContentSwitcherMaster.Name = "ContentSwitcherMaster"
        Me.ContentSwitcherMaster.Size = New System.Drawing.Size(984, 549)
        Me.ContentSwitcherMaster.TabIndex = 8
        '
        'PanelFooterBorder
        '
        Me.PanelFooterBorder.Appearance.BackColor = System.Drawing.Color.DarkGray
        Me.PanelFooterBorder.Appearance.BackColor2 = System.Drawing.Color.LightGray
        Me.PanelFooterBorder.Appearance.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical
        Me.PanelFooterBorder.Appearance.Options.UseBackColor = True
        Me.PanelFooterBorder.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PanelFooterBorder.Dock = System.Windows.Forms.DockStyle.Bottom
        Me.PanelFooterBorder.Location = New System.Drawing.Point(0, 631)
        Me.PanelFooterBorder.Name = "PanelFooterBorder"
        Me.PanelFooterBorder.Size = New System.Drawing.Size(984, 2)
        Me.PanelFooterBorder.TabIndex = 9
        '
        'CommandCentre
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.ClientSize = New System.Drawing.Size(984, 674)
        Me.Controls.Add(Me.ContentSwitcherMaster)
        Me.Controls.Add(Me.PanelFooterBorder)
        Me.Controls.Add(Me.PanelFooter)
        Me.Controls.Add(Me.PanelTitle)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.MinimumSize = New System.Drawing.Size(1000, 712)
        Me.Name = "CommandCentre"
        Me.Text = "Nova"
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PanelTitle, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelTitle.ResumeLayout(False)
        Me.PanelTitle.PerformLayout()
        CType(Me.PanelTitleColor, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBoxLogo, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PanelFooter, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelFooter.ResumeLayout(False)
        Me.PanelFooter.PerformLayout()
        CType(Me.PictureBox1, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureBoxAccount, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PanelFooterBorder, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents PanelTitle As DevExpress.XtraEditors.PanelControl
    Friend WithEvents PictureBoxLogo As System.Windows.Forms.PictureBox
    Friend WithEvents PanelFooter As DevExpress.XtraEditors.PanelControl
    Friend WithEvents PictureBoxAccount As System.Windows.Forms.PictureBox
    Friend WithEvents LabelLoginInfo As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ContentSwitcherMaster As LiquidShell.ContentSwitcher
    Friend WithEvents PanelTitleColor As DevExpress.XtraEditors.PanelControl
    Friend WithEvents PanelFooterBorder As DevExpress.XtraEditors.PanelControl
    Friend WithEvents LabelControlSubTitle As DevExpress.XtraEditors.LabelControl
    Friend WithEvents PictureBox1 As System.Windows.Forms.PictureBox
    Friend WithEvents HyperlinkSettings As DevExpress.XtraEditors.LabelControl
End Class
