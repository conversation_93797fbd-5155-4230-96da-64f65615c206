<?xml version="1.0" encoding="utf-8"?>
<Report xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner" xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition">
  <DataSources>
    <DataSource Name="DBConnection">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString>Data Source=sqlserv_3;Initial Catalog=NovaDB;User ID=alan</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>e78db224-9858-4187-ab39-8432d1757933</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="ResearchCategoryDataSet">
      <Fields>
        <Field Name="ResearchCategoryID">
          <DataField>ResearchCategoryID</DataField>
          <rd:TypeName>System.Guid</rd:TypeName>
        </Field>
        <Field Name="ContractID">
          <DataField>ContractID</DataField>
          <rd:TypeName>System.Guid</rd:TypeName>
        </Field>
        <Field Name="CategoryName">
          <DataField>CategoryName</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="FirstMonth">
          <DataField>FirstMonth</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="LastMonth">
          <DataField>LastMonth</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Months">
          <DataField>Months</DataField>
          <rd:TypeName>System.Int32</rd:TypeName>
        </Field>
        <Field Name="Fee">
          <DataField>Fee</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="Discount">
          <DataField>Discount</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="DiscountAmount">
          <DataField>DiscountAmount</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
      </Fields>
      <Query>
        <DataSourceName>DBConnection</DataSourceName>
        <CommandText>/* Local Query */</CommandText>
      </Query>
      <rd:DataSetInfo>
        <rd:DataSetName>DataSetContractReport</rd:DataSetName>
        <rd:SchemaPath>C:\Users\<USER>\Documents\Projects\Visual Studio\Nova2\NovaReports\DataSetContractReport.xsd</rd:SchemaPath>
        <rd:TableName>ResearchCategory</rd:TableName>
        <rd:TableAdapterFillMethod>Fill</rd:TableAdapterFillMethod>
        <rd:TableAdapterGetDataMethod>GetData</rd:TableAdapterGetDataMethod>
        <rd:TableAdapterName>ResearchCategoryTableAdapter</rd:TableAdapterName>
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <Body>
    <ReportItems>
      <Tablix Name="Table_ResearchCategories">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>3.11024in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.9685in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.9685in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.7874in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.98425in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>0.98425in</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.02362in</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.31496in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Label_Category">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Bottom End Research Category</Value>
                              <Style>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>7pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Left</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <ZIndex>67</ZIndex>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                        </Border>
                        <TopBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>0.5pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>0.5pt</Width>
                        </BottomBorder>
                        <BackgroundColor>LightGrey</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>1pt</PaddingTop>
                        <PaddingBottom>1pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Label_FirstMonth">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>First Month</Value>
                              <Style>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>7pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <ZIndex>66</ZIndex>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                        </Border>
                        <TopBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>0.5pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>0.5pt</Width>
                        </BottomBorder>
                        <BackgroundColor>LightGrey</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>1pt</PaddingTop>
                        <PaddingBottom>1pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Label_LastMonth">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Last Month</Value>
                              <Style>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>7pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <ZIndex>65</ZIndex>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                        </Border>
                        <TopBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>0.5pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>0.5pt</Width>
                        </BottomBorder>
                        <BackgroundColor>LightGrey</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>1pt</PaddingTop>
                        <PaddingBottom>1pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Label_Months">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Months</Value>
                              <Style>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>7pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <ZIndex>62</ZIndex>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                        </Border>
                        <TopBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>0.5pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>0.5pt</Width>
                        </BottomBorder>
                        <BackgroundColor>LightGrey</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>1pt</PaddingTop>
                        <PaddingBottom>1pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox5">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Gross Fee</Value>
                              <Style>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>7pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox5</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                          <Style>None</Style>
                        </Border>
                        <TopBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>0.5pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>0.5pt</Width>
                        </BottomBorder>
                        <BackgroundColor>LightGrey</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>1pt</PaddingTop>
                        <PaddingBottom>1pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox1">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Discount</Value>
                              <Style>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>7pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox1</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                          <Style>None</Style>
                        </Border>
                        <TopBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>0.5pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>0.5pt</Width>
                        </BottomBorder>
                        <BackgroundColor>LightGrey</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>1pt</PaddingTop>
                        <PaddingBottom>1pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Label_Fee">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>Net Fee</Value>
                              <Style>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>7pt</FontSize>
                                <FontWeight>Bold</FontWeight>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <ZIndex>54</ZIndex>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                          <Style>None</Style>
                        </Border>
                        <TopBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>0.5pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>0.5pt</Width>
                        </BottomBorder>
                        <BackgroundColor>LightGrey</BackgroundColor>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>1pt</PaddingTop>
                        <PaddingBottom>1pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.15748in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="CategoryName">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!CategoryName.Value</Value>
                              <Style>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>7pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>CategoryName</rd:DefaultName>
                      <ZIndex>13</ZIndex>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                        </Border>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>1pt</PaddingTop>
                        <PaddingBottom>1pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="FirstMonth">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!FirstMonth.Value</Value>
                              <Style>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>dd-MM-yyyy</Format>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <ZIndex>12</ZIndex>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                        </Border>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>1pt</PaddingTop>
                        <PaddingBottom>1pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="LastMonth">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!LastMonth.Value</Value>
                              <Style>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>dd-MM-yyyy</Format>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <ZIndex>11</ZIndex>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                        </Border>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>1pt</PaddingTop>
                        <PaddingBottom>1pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Months">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Months.Value</Value>
                              <Style>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>7pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <ZIndex>8</ZIndex>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                        </Border>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>1pt</PaddingTop>
                        <PaddingBottom>1pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Fee1">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Fee.Value</Value>
                              <Style>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>R # ### ##0.00</Format>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Fee1</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                        </Border>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingTop>1pt</PaddingTop>
                        <PaddingBottom>1pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Discount">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Discount.Value / 100</Value>
                              <Style>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>0.00 %</Format>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Discount</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                        </Border>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingTop>1pt</PaddingTop>
                        <PaddingBottom>1pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Fee">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!Fee.Value-Fields!DiscountAmount.Value</Value>
                              <Style>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>R # ### ##0.00</Format>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                        </Border>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingTop>1pt</PaddingTop>
                        <PaddingBottom>1pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
            <TablixRow>
              <Height>0.19685in</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="textbox4">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>7pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Left</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>textbox4</rd:DefaultName>
                      <ZIndex>41</ZIndex>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                        </Border>
                        <TopBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>0.5pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Gray</Color>
                          <Style>None</Style>
                          <Width>0.5pt</Width>
                        </BottomBorder>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>1pt</PaddingTop>
                        <PaddingBottom>1pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Footer_CommencementDate">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>7pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <ZIndex>40</ZIndex>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                        </Border>
                        <TopBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>0.5pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Gray</Color>
                          <Style>None</Style>
                          <Width>0.5pt</Width>
                        </BottomBorder>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>1pt</PaddingTop>
                        <PaddingBottom>1pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Footer_TerminationDate">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>7pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Center</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <ZIndex>39</ZIndex>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                        </Border>
                        <TopBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>0.5pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Gray</Color>
                          <Style>None</Style>
                          <Width>0.5pt</Width>
                        </BottomBorder>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>1pt</PaddingTop>
                        <PaddingBottom>1pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="textbox17">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value />
                              <Style />
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>textbox17</rd:DefaultName>
                      <ZIndex>36</ZIndex>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                        </Border>
                        <TopBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>0.5pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Gray</Color>
                          <Width>0.5pt</Width>
                        </BottomBorder>
                        <PaddingLeft>2pt</PaddingLeft>
                        <PaddingRight>2pt</PaddingRight>
                        <PaddingTop>2pt</PaddingTop>
                        <PaddingBottom>2pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Textbox7">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Sum(Fields!Fee.Value)</Value>
                              <Style>
                                <FontStyle>Normal</FontStyle>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>7pt</FontSize>
                                <FontWeight>Normal</FontWeight>
                                <Format>R # ### ##0.00</Format>
                                <TextDecoration>None</TextDecoration>
                                <Color>#000000</Color>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Textbox7</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                        </Border>
                        <TopBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>0.5pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>0.5pt</Width>
                        </BottomBorder>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingTop>1pt</PaddingTop>
                        <PaddingBottom>1pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="DiscountAmount">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Sum(Fields!DiscountAmount.Value)</Value>
                              <Style>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>R # ### ##0.00</Format>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>DiscountAmount</rd:DefaultName>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                        </Border>
                        <TopBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>0.5pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>0.5pt</Width>
                        </BottomBorder>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingTop>1pt</PaddingTop>
                        <PaddingBottom>1pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Footer_FeeTotal">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Sum(Fields!Fee.Value-Fields!DiscountAmount.Value)</Value>
                              <Style>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>7pt</FontSize>
                                <Format>R # ### ##0.00</Format>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style />
                        </Paragraph>
                      </Paragraphs>
                      <ZIndex>28</ZIndex>
                      <Style>
                        <Border>
                          <Color>Gray</Color>
                        </Border>
                        <TopBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>0.5pt</Width>
                        </TopBorder>
                        <BottomBorder>
                          <Color>Gray</Color>
                          <Style>Solid</Style>
                          <Width>0.5pt</Width>
                        </BottomBorder>
                        <VerticalAlign>Middle</VerticalAlign>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingTop>1pt</PaddingTop>
                        <PaddingBottom>1pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <KeepTogether>true</KeepTogether>
            </TablixMember>
            <TablixMember>
              <TablixMembers>
                <TablixMember>
                  <TablixMembers>
                    <TablixMember>
                      <Group Name="Table_Bursts_Details_Group">
                        <DataElementName>Detail</DataElementName>
                      </Group>
                      <TablixMembers>
                        <TablixMember />
                      </TablixMembers>
                      <DataElementName>Detail_Collection</DataElementName>
                      <DataElementOutput>Output</DataElementOutput>
                      <KeepTogether>true</KeepTogether>
                    </TablixMember>
                  </TablixMembers>
                </TablixMember>
              </TablixMembers>
            </TablixMember>
            <TablixMember>
              <KeepTogether>true</KeepTogether>
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <DataSetName>ResearchCategoryDataSet</DataSetName>
        <Height>1.7cm</Height>
        <Width>27.49997cm</Width>
        <Style>
          <FontFamily>Verdana</FontFamily>
          <FontSize>8pt</FontSize>
        </Style>
      </Tablix>
    </ReportItems>
    <Height>1.7cm</Height>
    <Style>
      <RightBorder>
        <Width>0.5pt</Width>
      </RightBorder>
    </Style>
  </Body>
  <ReportParameters>
    <ReportParameter Name="ContractID">
      <DataType>String</DataType>
      <Prompt>ContractID</Prompt>
    </ReportParameter>
  </ReportParameters>
  <Width>27.5cm</Width>
  <Page>
    <PageHeight>21cm</PageHeight>
    <PageWidth>29.5cm</PageWidth>
    <LeftMargin>0.5cm</LeftMargin>
    <RightMargin>0.5cm</RightMargin>
    <TopMargin>0.5cm</TopMargin>
    <BottomMargin>0.5cm</BottomMargin>
    <ColumnSpacing>1cm</ColumnSpacing>
    <Style />
  </Page>
  <Language>en-ZA</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportID>996bdd0f-c4a4-45b4-b5c7-4e4d9744ac5b</rd:ReportID>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
</Report>