Public Class Settings

    Public Shared Function GetSettings(ByVal ConnectionString As String, ByVal ConsumingForm As LiquidShell.BaseForm) As DataTable

        ' Return a table of all settings.
        Dim SettingAdapter As New DataSetSettingTableAdapters.SettingTableAdapter
        Dim SettingGroupAdapter As New DataSetSettingTableAdapters.SettingGroupTableAdapter
        Dim SqlConnection As New SqlClient.SqlConnection(ConnectionString)
        SettingAdapter.Connection = SqlConnection
        SettingGroupAdapter.Connection = SqlConnection
        Dim SettingData As New DataSetSetting

        Try
            SettingGroupAdapter.Fill(SettingData.Tables("SettingGroup"))
            SettingAdapter.Fill(SettingData.Tables("Setting"))
        Catch ex As Exception
            ConsumingForm.ShowMessage("Bummer!  Something went wrong while loading data from the database." _
            & vbCrLf & vbCrLf & LiquidShell.LiquidAgent.GetErrorMessage(ex), "Data Load Error")
            Return Nothing
        Finally
            SettingAdapter.Dispose()
            SettingGroupAdapter.Dispose()
        End Try

        Return SettingData.Tables("Setting")

    End Function

    Public Shared Sub SetSettings(ByVal ConnectionString As String, ByVal Changes As DataTable)

        ' Save changed settings back to the database.
        Dim SettingAdapter As New DataSetSettingTableAdapters.SettingTableAdapter
        Dim AuditAdapter As New DataSetAuditTableAdapters.AuditLogTableAdapter
        Dim SqlConnection As New SqlClient.SqlConnection(ConnectionString)
        SettingAdapter.Connection = SqlConnection
        AuditAdapter.Connection = SqlConnection

        ' Update the audit log.
        Dim AuditLog As New DataSetAudit.AuditLogDataTable
        For Each ChangedRow As DataRow In Changes.Rows
            If ChangedRow.RowState = DataRowState.Modified Then
                LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, "Setting", ChangedRow("SettingName"), "Changed value to " & ChangedRow("SettingValue"))
            End If
        Next

        Try
            SettingAdapter.Update(Changes)
            AuditAdapter.Update(AuditLog)
        Finally
            SettingAdapter.Dispose()
            AuditAdapter.Dispose()
            AuditLog.Dispose()
        End Try

    End Sub

    Public Shared Function GetServerTime(ByVal ConnectionString As String, ByVal ConsumingForm As LiquidShell.BaseForm) As Date

        ' Create a string to hold possible errors.
        Dim Errors As String = String.Empty

        ' Execute the command to fetch the data.
        Dim ServerTime As Date = CDate(LiquidShell.LiquidAgent.GetSqlScalarValue(ConnectionString, "SELECT GETDATE()", Errors))

        ' Display error if something went wrong.
        If String.IsNullOrEmpty(Errors) = False Then
            ConsumingForm.ShowMessage("Rats!  Something went wrong while trying to read the time from the database server." _
            & vbCrLf & vbCrLf & Errors, "Data Load Error")
            Return Nothing
        Else
            Return ServerTime
        End If

    End Function

    Public Shared Function GetServerTime(ByVal Connection As SqlClient.SqlConnection, ByVal ConsumingForm As LiquidShell.BaseForm) As Date

        ' Create a string to hold possible errors.
        Dim Errors As String = String.Empty

        ' Execute the command to fetch the data.
        Dim ServerTime As Date = CDate(LiquidShell.LiquidAgent.GetSqlScalarValue(Connection, "SELECT GETDATE()", Errors))

        ' Display error if something went wrong.
        If String.IsNullOrEmpty(Errors) = False Then
            ConsumingForm.ShowMessage("Rats!  Something went wrong while trying to read the time from the database server." _
            & vbCrLf & vbCrLf & Errors, "Data Load Error")
            Return Nothing
        Else
            Return ServerTime
        End If

    End Function

End Class
