﻿using DataAccess;

namespace DataService.Security
{
    internal class GetRoleHistoryCommandExecutor : CommandExecutor<GetRoleHistoryCommand>
    {
        public override void Execute(GetRoleHistoryCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.GetRoleHistory))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.AddInputParameter("roleid", command.RoleId);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    command.Table = storedprocedure.OutputTable;
                }
            }
        }
    }
}
