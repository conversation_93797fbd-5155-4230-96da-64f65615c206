<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DataSetCategory" targetNamespace="http://tempuri.org/DataSetCategory.xsd" xmlns:mstns="http://tempuri.org/DataSetCategory.xsd" xmlns="http://tempuri.org/DataSetCategory.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="1" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="DBConnection" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Public" Name="DBConnection (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.NovaData.My.MySettings.GlobalReference.Default.DBConnection" Provider="System.Data.SqlClient" />
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="NovaDBConnectionString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Public" Name="NovaDBConnectionString (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.NovaData.My.MySettings.GlobalReference.Default.NovaDBConnectionString" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="BurstCountTableAdapter" GeneratorDataComponentClassName="BurstCountTableAdapter" Name="BurstCount" UserDataComponentName="BurstCountTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="NovaDBConnectionString (MySettings)" DbObjectName="NovaDB.Sales.vEffectiveBurst" DbObjectType="View" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT        CategoryID, COUNT(BurstID) AS BurstCount
FROM            Sales.vEffectiveBurst
GROUP BY CategoryID</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="CategoryID" DataSetColumn="CategoryID" />
              <Mapping SourceColumn="BurstCount" DataSetColumn="BurstCount" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="MediaCategoryTableAdapter" GeneratorDataComponentClassName="MediaCategoryTableAdapter" Name="MediaCategory" UserDataComponentName="MediaCategoryTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="NovaDBConnectionString (MySettings)" DbObjectName="NovaDB.Media.MediaCategory" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>DELETE FROM [Media].[MediaCategory] WHERE (([MediaID] = @Original_MediaID) AND ([CategoryID] = @Original_CategoryID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_MediaID" ColumnName="MediaID" DataSourceName="NovaDB.Media.MediaCategory" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@Original_MediaID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_CategoryID" ColumnName="CategoryID" DataSourceName="NovaDB.Media.MediaCategory" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@Original_CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>INSERT INTO [Media].[MediaCategory] ([MediaID], [CategoryID]) VALUES (@MediaID, @CategoryID);</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaID" ColumnName="MediaID" DataSourceName="NovaDB.Media.MediaCategory" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="CategoryID" ColumnName="CategoryID" DataSourceName="NovaDB.Media.MediaCategory" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT Media.MediaCategory.MediaID, Media.MediaCategory.CategoryID, Media.MediaCategory.MediaLimit
FROM     Media.MediaCategory INNER JOIN
                  Media.Media ON Media.MediaCategory.MediaID = Media.Media.MediaID
WHERE  (Media.MediaCategory.CategoryID = @CategoryID)
ORDER BY Media.Media.MediaName</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="CategoryID" ColumnName="CategoryID" DataSourceName="NovaDB.Media.MediaCategory" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>UPDATE Media.MediaCategory
SET          MediaID = @MediaID, CategoryID = @CategoryID, MediaLimit = @MediaLimit
WHERE  (MediaID = @Original_MediaID) AND (CategoryID = @Original_CategoryID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaID" ColumnName="MediaID" DataSourceName="NovaDB.Media.MediaCategory" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="CategoryID" ColumnName="CategoryID" DataSourceName="NovaDB.Media.MediaCategory" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="MediaLimit" ColumnName="MediaLimit" DataSourceName="NovaDB.Media.MediaCategory" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaLimit" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaLimit" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_MediaID" ColumnName="MediaID" DataSourceName="NovaDB.Media.MediaCategory" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@Original_MediaID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_CategoryID" ColumnName="CategoryID" DataSourceName="NovaDB.Media.MediaCategory" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@Original_CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MediaID" DataSetColumn="MediaID" />
              <Mapping SourceColumn="CategoryID" DataSetColumn="CategoryID" />
              <Mapping SourceColumn="MediaLimit" DataSetColumn="MediaLimit" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="MediaTableAdapter" GeneratorDataComponentClassName="MediaTableAdapter" Name="Media" UserDataComponentName="MediaTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="NovaDBConnectionString (MySettings)" DbObjectName="NovaDB.Media.Media" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT     MediaID, MediaName
FROM         Media.Media</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MediaID" DataSetColumn="MediaID" />
              <Mapping SourceColumn="MediaName" DataSetColumn="MediaName" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="CategoryTableAdapter" GeneratorDataComponentClassName="CategoryTableAdapter" Name="Category" UserDataComponentName="CategoryTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="NovaDBConnectionString (MySettings)" DbObjectName="NovaDB.Store.Category" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Store].[Category] WHERE (([CategoryID] = @Original_CategoryID) AND ([CategoryName] = @Original_CategoryName) AND ([Dormant] = @Original_Dormant) AND ([CrossoverAllowed] = @Original_CrossoverAllowed))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_CategoryName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="CategoryName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Dormant" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_CrossoverAllowed" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="CrossoverAllowed" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Store].[Category] ([CategoryName], [Dormant], [CrossoverAllowed]) VALUES (@CategoryName, @Dormant, @CrossoverAllowed);
SELECT CategoryID, CategoryName, Dormant, CrossoverAllowed FROM Store.Category WHERE (CategoryID = SCOPE_IDENTITY()) ORDER BY CategoryName</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@CategoryName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="CategoryName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Dormant" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@CrossoverAllowed" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="CrossoverAllowed" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT CategoryID, CategoryName, Dormant, CrossoverAllowed
FROM     Store.Category
ORDER BY CategoryName</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Store].[Category] SET [CategoryName] = @CategoryName, [Dormant] = @Dormant, [CrossoverAllowed] = @CrossoverAllowed WHERE (([CategoryID] = @Original_CategoryID) AND ([CategoryName] = @Original_CategoryName) AND ([Dormant] = @Original_Dormant) AND ([CrossoverAllowed] = @Original_CrossoverAllowed));
SELECT CategoryID, CategoryName, Dormant, CrossoverAllowed FROM Store.Category WHERE (CategoryID = @CategoryID) ORDER BY CategoryName</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@CategoryName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="CategoryName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Dormant" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@CrossoverAllowed" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="CrossoverAllowed" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_CategoryName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="CategoryName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Dormant" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_CrossoverAllowed" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="CrossoverAllowed" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="CategoryID" ColumnName="CategoryID" DataSourceName="NovaDB.Store.Category" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="CategoryID" DataSetColumn="CategoryID" />
              <Mapping SourceColumn="CategoryName" DataSetColumn="CategoryName" />
              <Mapping SourceColumn="Dormant" DataSetColumn="Dormant" />
              <Mapping SourceColumn="CrossoverAllowed" DataSetColumn="CrossoverAllowed" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DataSetCategory" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_DataSetName="DataSetCategory" msprop:Generator_UserDSName="DataSetCategory">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="BurstCount" msprop:Generator_UserTableName="BurstCount" msprop:Generator_RowEvArgName="BurstCountRowChangeEvent" msprop:Generator_TableVarName="tableBurstCount" msprop:Generator_TablePropName="BurstCount" msprop:Generator_RowDeletingName="BurstCountRowDeleting" msprop:Generator_RowChangingName="BurstCountRowChanging" msprop:Generator_RowDeletedName="BurstCountRowDeleted" msprop:Generator_RowEvHandlerName="BurstCountRowChangeEventHandler" msprop:Generator_TableClassName="BurstCountDataTable" msprop:Generator_RowChangedName="BurstCountRowChanged" msprop:Generator_RowClassName="BurstCountRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="CategoryID" msprop:Generator_ColumnVarNameInTable="columnCategoryID" msprop:Generator_ColumnPropNameInRow="CategoryID" msprop:Generator_ColumnPropNameInTable="CategoryIDColumn" msprop:Generator_UserColumnName="CategoryID" type="xs:int" />
              <xs:element name="BurstCount" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnBurstCount" msprop:Generator_ColumnPropNameInRow="BurstCount" msprop:Generator_ColumnPropNameInTable="BurstCountColumn" msprop:Generator_UserColumnName="BurstCount" type="xs:int" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="MediaCategory" msprop:Generator_UserTableName="MediaCategory" msprop:Generator_RowEvArgName="MediaCategoryRowChangeEvent" msprop:Generator_TableVarName="tableMediaCategory" msprop:Generator_TablePropName="MediaCategory" msprop:Generator_RowDeletingName="MediaCategoryRowDeleting" msprop:Generator_RowChangingName="MediaCategoryRowChanging" msprop:Generator_RowDeletedName="MediaCategoryRowDeleted" msprop:Generator_RowEvHandlerName="MediaCategoryRowChangeEventHandler" msprop:Generator_TableClassName="MediaCategoryDataTable" msprop:Generator_RowChangedName="MediaCategoryRowChanged" msprop:Generator_RowClassName="MediaCategoryRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MediaID" msprop:Generator_ColumnVarNameInTable="columnMediaID" msprop:Generator_ColumnPropNameInRow="MediaID" msprop:Generator_ColumnPropNameInTable="MediaIDColumn" msprop:Generator_UserColumnName="MediaID" type="xs:int" />
              <xs:element name="CategoryID" msprop:Generator_ColumnVarNameInTable="columnCategoryID" msprop:Generator_ColumnPropNameInRow="CategoryID" msprop:Generator_ColumnPropNameInTable="CategoryIDColumn" msprop:Generator_UserColumnName="CategoryID" type="xs:int" />
              <xs:element name="MediaName" msdata:ReadOnly="true" msdata:Expression="Parent(FK_MediaCategory_Media).MediaName" msprop:Generator_ColumnVarNameInTable="columnMediaName" msprop:Generator_ColumnPropNameInRow="MediaName" msprop:Generator_ColumnPropNameInTable="MediaNameColumn" msprop:Generator_UserColumnName="MediaName" type="xs:string" minOccurs="0" />
              <xs:element name="MediaLimit" msprop:Generator_ColumnVarNameInTable="columnMediaLimit" msprop:Generator_ColumnPropNameInRow="MediaLimit" msprop:Generator_ColumnPropNameInTable="MediaLimitColumn" msprop:Generator_UserColumnName="MediaLimit" type="xs:int" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Media" msprop:Generator_UserTableName="Media" msprop:Generator_RowEvArgName="MediaRowChangeEvent" msprop:Generator_TableVarName="tableMedia" msprop:Generator_TablePropName="Media" msprop:Generator_RowDeletingName="MediaRowDeleting" msprop:Generator_RowChangingName="MediaRowChanging" msprop:Generator_RowDeletedName="MediaRowDeleted" msprop:Generator_RowEvHandlerName="MediaRowChangeEventHandler" msprop:Generator_TableClassName="MediaDataTable" msprop:Generator_RowChangedName="MediaRowChanged" msprop:Generator_RowClassName="MediaRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MediaID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msprop:Generator_ColumnVarNameInTable="columnMediaID" msprop:Generator_ColumnPropNameInRow="MediaID" msprop:Generator_ColumnPropNameInTable="MediaIDColumn" msprop:Generator_UserColumnName="MediaID" type="xs:int" />
              <xs:element name="MediaName" msprop:Generator_ColumnVarNameInTable="columnMediaName" msprop:Generator_ColumnPropNameInRow="MediaName" msprop:Generator_ColumnPropNameInTable="MediaNameColumn" msprop:Generator_UserColumnName="MediaName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Category" msprop:Generator_UserTableName="Category" msprop:Generator_RowEvArgName="CategoryRowChangeEvent" msprop:Generator_TableVarName="tableCategory" msprop:Generator_TablePropName="Category" msprop:Generator_RowDeletingName="CategoryRowDeleting" msprop:Generator_RowChangingName="CategoryRowChanging" msprop:Generator_RowDeletedName="CategoryRowDeleted" msprop:Generator_RowEvHandlerName="CategoryRowChangeEventHandler" msprop:Generator_TableClassName="CategoryDataTable" msprop:Generator_RowChangedName="CategoryRowChanged" msprop:Generator_RowClassName="CategoryRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="CategoryID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msprop:Generator_ColumnVarNameInTable="columnCategoryID" msprop:Generator_ColumnPropNameInRow="CategoryID" msprop:Generator_ColumnPropNameInTable="CategoryIDColumn" msprop:Generator_UserColumnName="CategoryID" type="xs:int" />
              <xs:element name="CategoryName" msprop:Generator_ColumnVarNameInTable="columnCategoryName" msprop:Generator_ColumnPropNameInRow="CategoryName" msprop:Generator_ColumnPropNameInTable="CategoryNameColumn" msprop:Generator_UserColumnName="CategoryName" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Dormant" msprop:Generator_ColumnVarNameInTable="columnDormant" msprop:Generator_ColumnPropNameInRow="Dormant" msprop:Generator_ColumnPropNameInTable="DormantColumn" msprop:Generator_UserColumnName="Dormant" type="xs:boolean" default="false" />
              <xs:element name="CrossoverAllowed" msprop:Generator_ColumnVarNameInTable="columnCrossoverAllowed" msprop:Generator_ColumnPropNameInRow="CrossoverAllowed" msprop:Generator_ColumnPropNameInTable="CrossoverAllowedColumn" msprop:Generator_UserColumnName="CrossoverAllowed" type="xs:boolean" default="false" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:BurstCount" />
      <xs:field xpath="mstns:CategoryID" />
    </xs:unique>
    <xs:unique name="MediaCategory_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:MediaCategory" />
      <xs:field xpath="mstns:MediaID" />
      <xs:field xpath="mstns:CategoryID" />
    </xs:unique>
    <xs:unique name="Media_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Media" />
      <xs:field xpath="mstns:MediaID" />
    </xs:unique>
    <xs:unique name="Category_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Category" />
      <xs:field xpath="mstns:CategoryID" />
    </xs:unique>
  </xs:element>
  <xs:annotation>
    <xs:appinfo>
      <msdata:Relationship name="FK_MediaCategory_Media" msdata:parent="Media" msdata:child="MediaCategory" msdata:parentkey="MediaID" msdata:childkey="MediaID" msprop:Generator_UserChildTable="MediaCategory" msprop:Generator_ChildPropName="GetMediaCategoryRows" msprop:Generator_UserRelationName="FK_MediaCategory_Media" msprop:Generator_ParentPropName="MediaRow" msprop:Generator_RelationVarName="relationFK_MediaCategory_Media" msprop:Generator_UserParentTable="Media" />
      <msdata:Relationship name="FK_MediaCategory_Category" msdata:parent="Category" msdata:child="MediaCategory" msdata:parentkey="CategoryID" msdata:childkey="CategoryID" msprop:Generator_UserChildTable="MediaCategory" msprop:Generator_ChildPropName="GetMediaCategoryRows" msprop:Generator_UserRelationName="FK_MediaCategory_Category" msprop:Generator_ParentPropName="CategoryRow" msprop:Generator_RelationVarName="relationFK_MediaCategory_Category" msprop:Generator_UserParentTable="Category" />
      <msdata:Relationship name="Category_BurstCount" msdata:parent="Category" msdata:child="BurstCount" msdata:parentkey="CategoryID" msdata:childkey="CategoryID" msprop:Generator_UserChildTable="BurstCount" msprop:Generator_ChildPropName="GetBurstCountRows" msprop:Generator_UserRelationName="Category_BurstCount" msprop:Generator_ParentPropName="CategoryRow" msprop:Generator_RelationVarName="relationCategory_BurstCount" msprop:Generator_UserParentTable="Category" />
    </xs:appinfo>
  </xs:annotation>
</xs:schema>