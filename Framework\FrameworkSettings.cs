﻿using System;
using System.Drawing;

namespace FrameworkSettings
{

    public static class Icons
    {
        public static Icon DataFormIcon = Framework.Properties.Resources.appicon;
    }

    internal static class Integers
    {
        public const int BUTTONWIDTH = 180;
        public const int BREADCRUMBBUTTONWIDTH = 180;
    }


    public static class Strings
    {
        public const string CREATEBUTTONTEXT = "New";
        public const string SAVEBUTTONTEXT = "Save";
        public const string UNDOBUTTONTEXT = "Discard Changes";
        public const string GRIDCOLUMNNAMEFORAPPROVEDROWS = "approved";
        public const string GRIDCOLUMNNAMEFOREDITSAWAITINGAPPROVAL = "pendingedits";
        public const string COMPANYNAME = "Primedia Instore (Pty) Ltd";
    }


    public static class Colors
    {

        #region Theme color

        private static Color _ThemeColor = Color.SteelBlue;
        public static Color ThemeColor
        {
            get { return _ThemeColor; }
            set
            {
                if (_ThemeColor != value)
                {
                    _ThemeColor = value;
                    OnThemeColorChanged();
                }
            }
        }

        public static event Action ThemeColorChanged;
        private static void OnThemeColorChanged()
        {
            ThemeColorChanged?.Invoke();
        }

        #endregion

        public static Color FORMFORECOLOR = Color.FromArgb(60, 60, 60);
        public static Color FORMBACKCOLOR = Color.WhiteSmoke;
        public static Color TABFORECOLOR = Color.White;
        public static Color TABMOUSEOVERBACKCOLOR = Color.Black;
        public static Color ERRORFORECOLOR = Color.Firebrick;
        public static Color BUTTONDISABLEDBACKCOLOR = Color.Gainsboro;

        public static Color GRIDCELLFORECOLOR = FORMFORECOLOR;
        public static Color GRIDCELLBACKCOLOR = Color.WhiteSmoke;
        public static Color GRIDCELLSELECTIONFORECOLOR = Color.Black;
        public static Color GRIDCELLSELECTIONBACKCOLOR = Color.Silver;

        public static Color GRIDCELLDISABLEDFORECOLOR = Color.Silver;
        public static Color GRIDCELLDISABLEDBACKCOLOR = GRIDCELLBACKCOLOR;
        public static Color GRIDCELLDISABLEDSELECTIONFORECOLOR = Color.Gray;
        public static Color GRIDCELLDISABLEDSELECTIONBACKCOLOR = GRIDCELLSELECTIONBACKCOLOR;

        public static Color GRIDLINECOLOR = Color.Silver;
        public static Color GRIDBACKCOLOR = FORMBACKCOLOR;
        public static Color GRIDHEADERFORECOLOR = TABFORECOLOR;
        public static Color GRIDHEADERBACKCOLOR = GRIDCELLFORECOLOR;
    }


    internal static class Fonts
    {
        public static Font FORMFONT = new Font("Verdana", 8);
        public static Font ERRORFONT = new Font(FORMFONT.FontFamily, 5.25F, FontStyle.Bold);
        public static Font TABFONT = new Font("Verdana", 8, FontStyle.Bold);
    }

}
