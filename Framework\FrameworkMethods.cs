﻿using FrameworkSettings;
using System.Drawing;
using System.Windows.Forms;

namespace FrameworkMethods
{

    public static class Methods
    {
        public static Color GetBestForeColor(Color backcolor)
        {
            // Adjust the forecolor to ensure good contrast and readability.
            if (backcolor.GetBrightness() > 0.5)
            {
                return Colors.FORMFORECOLOR;
            }
            else
            {
                return Color.White;
            }
        }
    }

    public static class EventHandlers
    {

        public static void Control_BackColorChanged(object sender, System.EventArgs e)
        {
            Control sendercontrol = (Control)sender;
            sendercontrol.ForeColor = Methods.GetBestForeColor(sendercontrol.BackColor);
        }

    }
}
