Public Class SubformMiscellaneousCharge

    Private DataObject As MiscellaneousCharge
    Private ParentContract As OldContract

#Region "Event Handlers"

    Private Sub Subform_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        PerformDataBinding()
    End Sub

    Private Sub ButtonOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonOK.Click
        ' Save and close.
        Save(True)
    End Sub

    Private Sub ButtonCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonCancel.Click
        DataObject.RejectChanges()
        RevertToParentSubform()
    End Sub

    Private Sub HyperlinkMiscellanouesCharge_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles HyperlinkMiscellanouesCharge.Click

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupMiscellaneousCharge.SelectRows(My.Settings.DBConnection, False, Nothing)

        ' Update the data object and the label with the selection.
        If SelectedItems.Count > 0 Then
            DataObject.SelectedMiscellaneousCharge = SelectedItems(0)
            CurrentControl.DataBindings("Text").ReadValue()
            LabelTitle.DataBindings("Text").ReadValue()
            ' Reset the error text of the control to an empty string.
            LiquidAgent.ControlValidation(CurrentControl, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkMiscellanouesCharge_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles HyperlinkMiscellanouesCharge.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        If String.Compare(ValidatedControl.Text, "Select...") = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "A miscellaneous charge must be selected.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

#End Region

#Region "Public Methods"

    Public Sub New(ByVal AddNew As Boolean, ByVal Parent As OldContract)

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        DataObject = New MiscellaneousCharge(Parent, AddNew, My.Settings.DBConnection)
        ParentContract = Parent

    End Sub

#End Region

#Region "Private Methods"

    Private Sub PerformDataBinding()

        LabelTitle.DataBindings.Add("Text", DataObject, "RowTitle")
        HyperlinkMiscellanouesCharge.DataBindings.Add("Text", DataObject, "MiscellaneousChargeName", False, DataSourceUpdateMode.Never)
        TextEditAmount.DataBindings.Add("EditValue", DataObject, "MiscellaneousChargeAmount", False, DataSourceUpdateMode.OnPropertyChanged)

    End Sub

    Protected Overrides Function Save() As Boolean
        DataObject.SaveToDataSet()
        Return True
    End Function

#End Region

End Class
