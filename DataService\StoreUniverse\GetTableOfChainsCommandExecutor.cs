﻿using DataAccess;
using System.Data;

namespace DataService.StoreUniverse
{
    class GetTableOfChainsCommandExecutor : CommandExecutor<GetTableOfChainsCommand>
    {
        public override void Execute(GetTableOfChainsCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.GetChainTable))
            {
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    command.Table = storedprocedure.OutputTable;
                    command.Table.PrimaryKey = new DataColumn[] { command.Table.Columns[0] };
                }
            }
        }
    }
}
