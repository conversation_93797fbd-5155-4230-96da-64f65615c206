Public Class SubformMediaCost

    Private _DataBindingSource As BindingSource
    Private _GridOfCosts As DataGridView

#Region "Properties"

    Private Property DataBindingSource() As BindingSource
        Get
            Return _DataBindingSource
        End Get
        Set(ByVal value As BindingSource)
            _DataBindingSource = value
        End Set
    End Property

    Private ReadOnly Property Row() As DataRow
        Get
            Return CType(DataBindingSource.Current, DataRowView).Row
        End Get
    End Property

#End Region

#Region "Event Handlers"

    Private Sub ButtonCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonCancel.Click
        DataBindingSource.CancelEdit()
        RevertToParentSubform()
    End Sub

    Private Sub ButtonOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonOK.Click
        Save(True)
    End Sub

    Private Sub Hyperlink_TextChanged(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles HyperlinkEffectiveDate.TextChanged

        ' Create an object to represent the control.
        Dim Hyperlink As LabelControl = CType(sender, LabelControl)

        ' Display 'Select...' if this is a new row being added.
        If String.IsNullOrEmpty(Hyperlink.Text) Then
            Hyperlink.Text = "Select..."
        End If

        ' Format the displayed text in the hyper link.
        If Date.TryParse(Hyperlink.Text, Nothing) Then
            Dim HyperlinkValue As Date = Date.Parse(Hyperlink.Text)
            Hyperlink.Text = HyperlinkValue.ToLongDateString
        End If

    End Sub

    Private Sub HyperlinkEffectiveDate_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles HyperlinkEffectiveDate.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Validate the control.
        LiquidAgent.ControlValidation(ValidatedControl, GetDateValidationErrors(ValidatedControl))

    End Sub

    Private Sub HyperlinkWeek_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles HyperlinkEffectiveDate.Click

        ' Create an object to represent the control.
        Dim ClickedHyperlink As LabelControl = CType(sender, LabelControl)

        ' Get the current date of the first selected row (to pass into the date selector later on).
        Dim CurrentDate As Nullable(Of Date)
        If Date.TryParse(ClickedHyperlink.Text, Nothing) Then
            CurrentDate = CDate(ClickedHyperlink.Text)
        End If

        ' Obtain a date to apply to selected rows.
        Dim SelectedDate As Nullable(Of Date)
        If CurrentDate.HasValue Then
            SelectedDate = DateSelector.GetDate(True, CurrentDate)
        Else
            SelectedDate = DateSelector.GetDate(True)
        End If

        If SelectedDate.HasValue Then
            ' Reset the error text of both date controls to an empty string.
            LiquidAgent.ControlValidation(HyperlinkEffectiveDate, String.Empty)

            ' Update the data object and the label with the selection.
            Dim DataBoundColumnName As String = ClickedHyperlink.DataBindings("Text").BindingMemberInfo.BindingMember
            Row(DataBoundColumnName) = SelectedDate.Value
            ClickedHyperlink.Text = SelectedDate.Value.ToLongDateString
        End If

    End Sub
    Private Sub CheckEditMediaCostPercentage_CheckedChanged(sender As Object, e As EventArgs) Handles CheckEditMediaCostPercentage.CheckedChanged
        If CheckEditMediaCostPercentage.Checked Then
            TextEditAmount.Enabled = False
            TextEditPercentage.Enabled = True
            TextEditAmount.Text = "0.00"
        Else
            TextEditPercentage.Enabled = False
            TextEditAmount.Enabled = True
            TextEditPercentage.Text = "0.00"
        End If
    End Sub

#End Region

#Region "Public Methods"

    Public Sub New(ByVal Grid As DataGridView, ByVal NewItem As Boolean)

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.

        ' Get the binding source of the supplied grid.
        DataBindingSource = CType(Grid.DataSource, BindingSource)

        ' Add a new row to the binding source list if required.
        If NewItem Then
            DataBindingSource.AddNew()
        End If
        _GridOfCosts = Grid
        ' Do data binding.
        HyperlinkEffectiveDate.DataBindings.Add("Text", DataBindingSource, "EffectiveDate")
        CheckEditMediaCostPercentage.Checked = Row("isPercentage")
        CheckEditMediaCostPercentage.DataBindings.Add("EditValue", DataBindingSource, "isPercentage", False, DataSourceUpdateMode.OnPropertyChanged)
        TextEditAmount.DataBindings.Add("EditValue", DataBindingSource, "CostPrice", False, DataSourceUpdateMode.OnPropertyChanged)
        TextEditPercentage.DataBindings.Add("EditValue", DataBindingSource, "CostPercentage", False, DataSourceUpdateMode.OnPropertyChanged)



    End Sub

#End Region

#Region "Protected Methods"

    Protected Overrides Function Save() As Boolean
        DataBindingSource.EndEdit()
        Return True
    End Function

#End Region

#Region "Private Methods"

    Private Function GetDateValidationErrors(ByVal ValidatedControl As Control) As String
        ' Check the given control for errors and return a string containing the errors or an
        ' empty string if no errors are found.

        ' Create a string builder to build the error list.
        Dim Errors As New System.Text.StringBuilder

        ' Get the date in the control.
        Dim SelectedDate As Date

        ' Check the first week date picker for errors.
        If String.Compare(ValidatedControl.Name, "HyperlinkEffectiveDate") = 0 Then

            ' Must have a value.
            If Date.TryParse(ValidatedControl.Text, SelectedDate) = False Then
                Errors.Append("The first week date must be selected.")
            Else
                ' May not be after the last week.
                'we also need to check that it doesn't exist where another effective date exists?
                For Each GridRow As DataGridViewRow In _GridOfCosts.Rows
                    If GridRow.Cells.Item("EffectiveDateColumn").Value = Convert.ToDateTime(HyperlinkEffectiveDate.Text) And GridRow.Index <> (_GridOfCosts.Rows.Count - 1) Then
                        Errors.Append("The first week date must be unique, the current date has already been selected.")
                    End If

                Next
            End If

        End If

        ' Check the last week date picker for errors.


        Return Errors.ToString

    End Function



#End Region

End Class
