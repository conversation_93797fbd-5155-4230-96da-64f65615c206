<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DataSetMedia" targetNamespace="http://tempuri.org/DataSetMedia.xsd" xmlns:mstns="http://tempuri.org/DataSetMedia.xsd" xmlns="http://tempuri.org/DataSetMedia.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="1" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="DBConnection" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Public" Name="DBConnection (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.NovaData.My.MySettings.GlobalReference.Default.DBConnection" Provider="System.Data.SqlClient" />
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="NovaDBConnectionString" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Assembly" Name="NovaDBConnectionString (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.NovaData.My.MySettings.GlobalReference.Default.DBConnection" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="MediaTableAdapter" GeneratorDataComponentClassName="MediaTableAdapter" Name="Media" UserDataComponentName="MediaTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Media.Media" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Media].[Media] WHERE (([MediaID] = @Original_MediaID) AND ([MediaName] = @Original_MediaName) AND ([Stock] = @Original_Stock) AND ([Clutter] = @Original_Clutter) AND ([Notes] = @Original_Notes) AND ([Homesite] = @Original_Homesite) AND ([Crossover] = @Original_Crossover) AND ([isPNPPcaStatus] = @Original_isPNPPcaStatus) AND ([hasMediaCost] = @Original_hasMediaCost) AND ([isCostOverridable] = @Original_isCostOverridable))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_MediaName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="MediaName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Stock" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Stock" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_Clutter" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Clutter" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Notes" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_Homesite" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Homesite" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_Crossover" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Crossover" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_isPNPPcaStatus" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="isPNPPcaStatus" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_hasMediaCost" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="hasMediaCost" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_isCostOverridable" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="isCostOverridable" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Media].[Media] ([MediaName], [Stock], [Clutter], [Notes], [Homesite], [Crossover], [isPNPPcaStatus], [hasMediaCost], [isCostOverridable]) VALUES (@MediaName, @Stock, @Clutter, @Notes, @Homesite, @Crossover, @isPNPPcaStatus, @hasMediaCost, @isCostOverridable);
SELECT MediaID, MediaName, Stock, Clutter, Notes, Homesite, Crossover, isPNPPcaStatus, hasMediaCost, isCostOverridable FROM Media.Media WHERE (MediaID = SCOPE_IDENTITY()) ORDER BY MediaName</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@MediaName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="MediaName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Stock" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Stock" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Clutter" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Clutter" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Notes" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Homesite" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Homesite" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Crossover" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Crossover" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@isPNPPcaStatus" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="isPNPPcaStatus" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@hasMediaCost" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="hasMediaCost" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@isCostOverridable" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="isCostOverridable" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT MediaID, MediaName, Stock, Clutter, Notes, Homesite, Crossover, isPNPPcaStatus, hasMediaCost, isCostOverridable
FROM     Media.Media
ORDER BY MediaName</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Media].[Media] SET [MediaName] = @MediaName, [Stock] = @Stock, [Clutter] = @Clutter, [Notes] = @Notes, [Homesite] = @Homesite, [Crossover] = @Crossover, [isPNPPcaStatus] = @isPNPPcaStatus, [hasMediaCost] = @hasMediaCost, [isCostOverridable] = @isCostOverridable WHERE (([MediaID] = @Original_MediaID) AND ([MediaName] = @Original_MediaName) AND ([Stock] = @Original_Stock) AND ([Clutter] = @Original_Clutter) AND ([Notes] = @Original_Notes) AND ([Homesite] = @Original_Homesite) AND ([Crossover] = @Original_Crossover) AND ([isPNPPcaStatus] = @Original_isPNPPcaStatus) AND ([hasMediaCost] = @Original_hasMediaCost) AND ([isCostOverridable] = @Original_isCostOverridable));
SELECT MediaID, MediaName, Stock, Clutter, Notes, Homesite, Crossover, isPNPPcaStatus, hasMediaCost, isCostOverridable FROM Media.Media WHERE (MediaID = @MediaID) ORDER BY MediaName</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@MediaName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="MediaName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Stock" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Stock" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Clutter" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Clutter" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Notes" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Homesite" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Homesite" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Crossover" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Crossover" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@isPNPPcaStatus" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="isPNPPcaStatus" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@hasMediaCost" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="hasMediaCost" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@isCostOverridable" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="isCostOverridable" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_MediaName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="MediaName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_Stock" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="Stock" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_Clutter" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Clutter" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_Notes" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_Homesite" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Homesite" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_Crossover" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Crossover" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_isPNPPcaStatus" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="isPNPPcaStatus" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_hasMediaCost" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="hasMediaCost" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_isCostOverridable" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="isCostOverridable" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaID" ColumnName="MediaID" DataSourceName="NovaDB.Media.Media" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MediaID" DataSetColumn="MediaID" />
              <Mapping SourceColumn="MediaName" DataSetColumn="MediaName" />
              <Mapping SourceColumn="Stock" DataSetColumn="Stock" />
              <Mapping SourceColumn="Clutter" DataSetColumn="Clutter" />
              <Mapping SourceColumn="Notes" DataSetColumn="Notes" />
              <Mapping SourceColumn="Homesite" DataSetColumn="Homesite" />
              <Mapping SourceColumn="Crossover" DataSetColumn="Crossover" />
              <Mapping SourceColumn="isPNPPcaStatus" DataSetColumn="isPNPPcaStatus" />
              <Mapping SourceColumn="hasMediaCost" DataSetColumn="hasMediaCost" />
              <Mapping SourceColumn="isCostOverridable" DataSetColumn="isCostOverridable" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="MediaCategoryTableAdapter" GeneratorDataComponentClassName="MediaCategoryTableAdapter" Name="MediaCategory" UserDataComponentName="MediaCategoryTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Media.MediaCategory" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Media].[MediaCategory] WHERE (([MediaID] = @Original_MediaID) AND ([CategoryID] = @Original_CategoryID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Media].[MediaCategory] ([MediaID], [CategoryID]) VALUES (@MediaID, @CategoryID);
SELECT MediaID, CategoryID FROM Media.MediaCategory WHERE (CategoryID = @CategoryID) AND (MediaID = @MediaID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@MediaID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT        Media.MediaCategory.MediaID, Media.MediaCategory.CategoryID
FROM            Media.MediaCategory INNER JOIN
                         Store.Category ON Media.MediaCategory.CategoryID = Store.Category.CategoryID
WHERE        (Media.MediaCategory.MediaID = @MediaID)
ORDER BY Store.Category.CategoryName</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaID" ColumnName="MediaID" DataSourceName="NovaDB.Media.MediaCategory" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Media].[MediaCategory] SET [MediaID] = @MediaID, [CategoryID] = @CategoryID WHERE (([MediaID] = @Original_MediaID) AND ([CategoryID] = @Original_CategoryID));
SELECT MediaID, CategoryID FROM Media.MediaCategory WHERE (CategoryID = @CategoryID) AND (MediaID = @MediaID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@MediaID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MediaID" DataSetColumn="MediaID" />
              <Mapping SourceColumn="CategoryID" DataSetColumn="CategoryID" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="CategoryTableAdapter" GeneratorDataComponentClassName="CategoryTableAdapter" Name="Category" UserDataComponentName="CategoryTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Store.Category" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Store].[Category] WHERE (([CategoryID] = @Original_CategoryID) AND ([CategoryName] = @Original_CategoryName) AND ([Dormant] = @Original_Dormant))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_CategoryName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="CategoryName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Dormant" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Store].[Category] ([CategoryName], [Dormant]) VALUES (@CategoryName, @Dormant);
SELECT CategoryID, CategoryName, Dormant FROM Store.Category WHERE (CategoryID = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@CategoryName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="CategoryName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Dormant" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT        CategoryID, CategoryName, Dormant
FROM            Store.Category</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Store].[Category] SET [CategoryName] = @CategoryName, [Dormant] = @Dormant WHERE (([CategoryID] = @Original_CategoryID) AND ([CategoryName] = @Original_CategoryName) AND ([Dormant] = @Original_Dormant));
SELECT CategoryID, CategoryName, Dormant FROM Store.Category WHERE (CategoryID = @CategoryID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@CategoryName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="CategoryName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Dormant" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_CategoryName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="CategoryName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Dormant" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="CategoryID" ColumnName="CategoryID" DataSourceName="NovaDB.Store.Category" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="CategoryID" DataSetColumn="CategoryID" />
              <Mapping SourceColumn="CategoryName" DataSetColumn="CategoryName" />
              <Mapping SourceColumn="Dormant" DataSetColumn="Dormant" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="MediaFamilyMemberTableAdapter" GeneratorDataComponentClassName="MediaFamilyMemberTableAdapter" Name="MediaFamilyMember" UserDataComponentName="MediaFamilyMemberTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Media.MediaFamilyMember" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Media].[MediaFamilyMember] WHERE (([MediaID] = @Original_MediaID) AND ([MediaFamilyID] = @Original_MediaFamilyID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaFamilyID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaFamilyID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Media].[MediaFamilyMember] ([MediaID], [MediaFamilyID]) VALUES (@MediaID, @MediaFamilyID);
SELECT MediaID, MediaFamilyID FROM Media.MediaFamilyMember WHERE (MediaFamilyID = @MediaFamilyID) AND (MediaID = @MediaID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@MediaID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@MediaFamilyID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaFamilyID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT        Media.MediaFamilyMember.MediaID, Media.MediaFamilyMember.MediaFamilyID
FROM            Media.MediaFamilyMember INNER JOIN
                         Media.MediaFamily ON Media.MediaFamilyMember.MediaFamilyID = Media.MediaFamily.MediaFamilyID
ORDER BY Media.MediaFamily.MediaFamilyName</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Media].[MediaFamilyMember] SET [MediaID] = @MediaID, [MediaFamilyID] = @MediaFamilyID WHERE (([MediaID] = @Original_MediaID) AND ([MediaFamilyID] = @Original_MediaFamilyID));
SELECT MediaID, MediaFamilyID FROM Media.MediaFamilyMember WHERE (MediaFamilyID = @MediaFamilyID) AND (MediaID = @MediaID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@MediaID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@MediaFamilyID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaFamilyID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaFamilyID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaFamilyID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MediaID" DataSetColumn="MediaID" />
              <Mapping SourceColumn="MediaFamilyID" DataSetColumn="MediaFamilyID" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="MediaFamilyTableAdapter" GeneratorDataComponentClassName="MediaFamilyTableAdapter" Name="MediaFamily" UserDataComponentName="MediaFamilyTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Media.MediaFamily" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Media].[MediaFamily] WHERE (([MediaFamilyID] = @Original_MediaFamilyID) AND ([MediaFamilyName] = @Original_MediaFamilyName))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaFamilyID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaFamilyID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_MediaFamilyName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="MediaFamilyName" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Media].[MediaFamily] ([MediaFamilyName]) VALUES (@MediaFamilyName);
SELECT MediaFamilyID, MediaFamilyName FROM Media.MediaFamily WHERE (MediaFamilyID = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@MediaFamilyName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="MediaFamilyName" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT     MediaFamilyID, MediaFamilyName
FROM         Media.MediaFamily</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Media].[MediaFamily] SET [MediaFamilyName] = @MediaFamilyName WHERE (([MediaFamilyID] = @Original_MediaFamilyID) AND ([MediaFamilyName] = @Original_MediaFamilyName));
SELECT MediaFamilyID, MediaFamilyName FROM Media.MediaFamily WHERE (MediaFamilyID = @MediaFamilyID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@MediaFamilyName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="MediaFamilyName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaFamilyID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaFamilyID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_MediaFamilyName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="MediaFamilyName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaFamilyID" ColumnName="MediaFamilyID" DataSourceName="NovaDB.Media.MediaFamily" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaFamilyID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaFamilyID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MediaFamilyID" DataSetColumn="MediaFamilyID" />
              <Mapping SourceColumn="MediaFamilyName" DataSetColumn="MediaFamilyName" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="CompetingMediaTableAdapter" GeneratorDataComponentClassName="CompetingMediaTableAdapter" Name="CompetingMedia" UserDataComponentName="CompetingMediaTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT DISTINCT Media.MediaFamilyMember.MediaID, Media.Media.MediaName AS CompetingMediaName
FROM            Media.Media INNER JOIN
                         Media.MediaFamilyMember AS MediaFamilyMember_1 ON Media.Media.MediaID = MediaFamilyMember_1.MediaID INNER JOIN
                         Media.MediaFamilyMember ON MediaFamilyMember_1.MediaFamilyID = Media.MediaFamilyMember.MediaFamilyID
WHERE        (Media.MediaFamilyMember.MediaID = @MediaID)
ORDER BY CompetingMediaName</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaID" ColumnName="MediaID" DataSourceName="NovaDB.Media.MediaFamilyMember" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MediaID" DataSetColumn="MediaID" />
              <Mapping SourceColumn="CompetingMediaName" DataSetColumn="CompetingMediaName" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="MediaLifeCycleTableAdapter" GeneratorDataComponentClassName="MediaLifeCycleTableAdapter" Name="MediaLifeCycle" UserDataComponentName="MediaLifeCycleTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Media.MediaLifeCycle" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Media].[MediaLifeCycle] WHERE (([MediaID] = @Original_MediaID) AND ([FirstWeek] = @Original_FirstWeek) AND ([LastWeek] = @Original_LastWeek))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_FirstWeek" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="FirstWeek" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastWeek" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastWeek" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Media].[MediaLifeCycle] ([MediaID], [FirstWeek], [LastWeek]) VALUES (@MediaID, @FirstWeek, @LastWeek);
SELECT MediaID, FirstWeek, LastWeek FROM Media.MediaLifeCycle WHERE (FirstWeek = @FirstWeek) AND (LastWeek = @LastWeek) AND (MediaID = @MediaID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@MediaID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@FirstWeek" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="FirstWeek" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastWeek" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastWeek" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT     MediaID, FirstWeek, LastWeek
FROM         Media.MediaLifeCycle
WHERE     (MediaID = @MediaID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaID" ColumnName="MediaID" DataSourceName="NovaDB.Media.MediaLifeCycle" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Media].[MediaLifeCycle] SET [MediaID] = @MediaID, [FirstWeek] = @FirstWeek, [LastWeek] = @LastWeek WHERE (([MediaID] = @Original_MediaID) AND ([FirstWeek] = @Original_FirstWeek) AND ([LastWeek] = @Original_LastWeek));
SELECT MediaID, FirstWeek, LastWeek FROM Media.MediaLifeCycle WHERE (FirstWeek = @FirstWeek) AND (LastWeek = @LastWeek) AND (MediaID = @MediaID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@MediaID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@FirstWeek" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="FirstWeek" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@LastWeek" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastWeek" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_FirstWeek" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="FirstWeek" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_LastWeek" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="LastWeek" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MediaID" DataSetColumn="MediaID" />
              <Mapping SourceColumn="FirstWeek" DataSetColumn="FirstWeek" />
              <Mapping SourceColumn="LastWeek" DataSetColumn="LastWeek" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="BurstCountTableAdapter" GeneratorDataComponentClassName="BurstCountTableAdapter" Name="BurstCount" UserDataComponentName="BurstCountTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Sales.Burst" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT        MediaID, COUNT(BurstID) AS BurstCount
FROM            Sales.Burst
GROUP BY MediaID</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MediaID" DataSetColumn="MediaID" />
              <Mapping SourceColumn="BurstCount" DataSetColumn="BurstCount" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="StoreMediaCategoryPermissionsTableAdapter" GeneratorDataComponentClassName="StoreMediaCategoryPermissionsTableAdapter" Name="StoreMediaCategoryPermissions" UserDataComponentName="StoreMediaCategoryPermissionsTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="NovaDBConnectionString (MySettings)" DbObjectName="NovaDB.dbo.udfGetStoreMediaCategoryPermissions" DbObjectType="Function" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM Store.StoreMediaCategoryPermission
WHERE        (StoreID = @StoreID) AND (MediaID = @MediaID) AND (CategoryID = @CategoryID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="StoreID" ColumnName="StoreID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@StoreID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="StoreID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaID" ColumnName="MediaID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="CategoryID" ColumnName="CategoryID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO Store.StoreMediaCategoryPermission
                         (CategoryID, StoreID, MediaID)
VALUES        (@CategoryID,@StoreID,@MediaID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="CategoryID" ColumnName="CategoryID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="StoreID" ColumnName="StoreID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@StoreID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="StoreID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaID" ColumnName="MediaID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT StoreID, StoreName, RegionName FROM dbo.udfGetStoreMediaCategoryPermissions(@CategoryID, @MediaID) AS udfGetStoreMediaCategoryPermissions_1</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="CategoryID" ColumnName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@CategoryID" Precision="0" Scale="0" Size="0" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaID" ColumnName="" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaID" Precision="0" Scale="0" Size="0" SourceColumn="" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="StoreName" DataSetColumn="StoreName" />
              <Mapping SourceColumn="StoreID" DataSetColumn="StoreID" />
              <Mapping SourceColumn="RegionName" DataSetColumn="RegionName" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="StoreMediaCategoryPermissionTableAdapter" GeneratorDataComponentClassName="StoreMediaCategoryPermissionTableAdapter" Name="StoreMediaCategoryPermission" UserDataComponentName="StoreMediaCategoryPermissionTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="NovaDBConnectionString (MySettings)" DbObjectName="NovaDB.Store.StoreMediaCategoryPermission" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Store].[StoreMediaCategoryPermission] WHERE (([ID] = @Original_ID) AND ([CategoryID] = @Original_CategoryID) AND ([StoreID] = @Original_StoreID) AND ([MediaID] = @Original_MediaID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_StoreID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StoreID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Store].[StoreMediaCategoryPermission] ([CategoryID], [StoreID], [MediaID]) VALUES (@CategoryID, @StoreID, @MediaID);
SELECT ID, CategoryID, StoreID, MediaID FROM Store.StoreMediaCategoryPermission WHERE (ID = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@StoreID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StoreID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@MediaID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>Select * from Store.StoreMediaCategoryPermission</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Store].[StoreMediaCategoryPermission] SET [CategoryID] = @CategoryID, [StoreID] = @StoreID, [MediaID] = @MediaID WHERE (([ID] = @Original_ID) AND ([CategoryID] = @Original_CategoryID) AND ([StoreID] = @Original_StoreID) AND ([MediaID] = @Original_MediaID));
SELECT ID, CategoryID, StoreID, MediaID FROM Store.StoreMediaCategoryPermission WHERE (ID = @ID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@StoreID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StoreID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@MediaID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_StoreID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="StoreID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ID" ColumnName="ID" DataSourceName="NovaDB.Store.StoreMediaCategoryPermission" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ID" DataSetColumn="ID" />
              <Mapping SourceColumn="CategoryID" DataSetColumn="CategoryID" />
              <Mapping SourceColumn="StoreID" DataSetColumn="StoreID" />
              <Mapping SourceColumn="MediaID" DataSetColumn="MediaID" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="NovaDBConnectionString (MySettings)" DbObjectName="NovaDB.Store.StoreMediaCategoryPermission" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillBy" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetDataBy" GeneratorSourceName="FillBy" GetMethodModifier="Public" GetMethodName="GetDataBy" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataBy" UserSourceName="FillBy">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT        TOP (1) ID, CategoryID, StoreID, MediaID
FROM            Store.StoreMediaCategoryPermission
WHERE        (CategoryID = @CategoryID) AND (StoreID = @StoreID) AND (MediaID = @MediaID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="CategoryID" ColumnName="CategoryID" DataSourceName="NovaDB.Store.StoreMediaCategoryPermission" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@CategoryID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="CategoryID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="StoreID" ColumnName="StoreID" DataSourceName="NovaDB.Store.StoreMediaCategoryPermission" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@StoreID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="StoreID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaID" ColumnName="MediaID" DataSourceName="NovaDB.Store.StoreMediaCategoryPermission" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="MediaGroupTableAdapter" GeneratorDataComponentClassName="MediaGroupTableAdapter" Name="MediaGroup" UserDataComponentName="MediaGroupTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Media.MediaGroup" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Media].[MediaGroup] WHERE (([MediaGroupID] = @Original_MediaGroupID) AND ([MediaGroupName] = @Original_MediaGroupName))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaGroupID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaGroupID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_MediaGroupName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="MediaGroupName" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>INSERT INTO Media.MediaGroup
                         (MediaGroupName, Dormant)
VALUES        (@MediaGroupName,@Dormant); 
SELECT MediaGroupID, MediaGroupName FROM Media.MediaGroup WHERE (MediaGroupID = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaGroupName" ColumnName="MediaGroupName" DataSourceName="NovaDB.Media.MediaGroup" DataTypeServer="nvarchar(250)" DbType="String" Direction="Input" ParameterName="@MediaGroupName" Precision="0" ProviderType="NVarChar" Scale="0" Size="250" SourceColumn="MediaGroupName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="Dormant" ColumnName="Dormant" DataSourceName="NovaDB.Media.MediaGroup" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="Dormant" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT MediaGroupID, MediaGroupName, Dormant FROM Media.MediaGroup</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>UPDATE       Media.MediaGroup
SET                MediaGroupName = @MediaGroupName, Dormant = @Dormant
WHERE        (MediaGroupID = @MediaGroupID); 
SELECT MediaGroupID, MediaGroupName FROM Media.MediaGroup WHERE (MediaGroupID = @MediaGroupID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaGroupName" ColumnName="MediaGroupName" DataSourceName="NovaDB.Media.MediaGroup" DataTypeServer="nvarchar(250)" DbType="String" Direction="Input" ParameterName="@MediaGroupName" Precision="0" ProviderType="NVarChar" Scale="0" Size="250" SourceColumn="MediaGroupName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="true" AutogeneratedName="Dormant" ColumnName="Dormant" DataSourceName="NovaDB.Media.MediaGroup" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="Dormant" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaGroupID" ColumnName="MediaGroupID" DataSourceName="NovaDB.Media.MediaGroup" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaGroupID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaGroupID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MediaGroupID" DataSetColumn="MediaGroupID" />
              <Mapping SourceColumn="MediaGroupName" DataSetColumn="MediaGroupName" />
              <Mapping SourceColumn="Dormant" DataSetColumn="Dormant" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="MediaGroupMemberTableAdapter" GeneratorDataComponentClassName="MediaGroupMemberTableAdapter" Name="MediaGroupMember" UserDataComponentName="MediaGroupMemberTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Media].[MediaGroupMember] WHERE (([MediaGroupID] = @Original_MediaGroupID) AND ([MediaID] = @Original_MediaID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaGroupID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaGroupID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Media].[MediaGroupMember] ([MediaGroupID], [MediaID]) VALUES (@MediaGroupID, @MediaID);
SELECT MediaGroupID, MediaID FROM Media.MediaGroupMember WHERE (MediaGroupID = @MediaGroupID) AND (MediaID = @MediaID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@MediaGroupID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaGroupID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@MediaID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT        GM.MediaGroupID, GM.MediaID, M.MediaName
FROM            Media.MediaGroupMember AS GM INNER JOIN
                         Media.Media AS M ON GM.MediaID = M.MediaID</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Media].[MediaGroupMember] SET [MediaGroupID] = @MediaGroupID, [MediaID] = @MediaID WHERE (([MediaGroupID] = @Original_MediaGroupID) AND ([MediaID] = @Original_MediaID));
SELECT MediaGroupID, MediaID FROM Media.MediaGroupMember WHERE (MediaGroupID = @MediaGroupID) AND (MediaID = @MediaID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@MediaGroupID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaGroupID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@MediaID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaGroupID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaGroupID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MediaGroupID" DataSetColumn="MediaGroupID" />
              <Mapping SourceColumn="MediaID" DataSetColumn="MediaID" />
              <Mapping SourceColumn="MediaName" DataSetColumn="MediaName" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="FillByMediaGroupID" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetDataByMediaGroupID" GeneratorSourceName="FillByMediaGroupID" GetMethodModifier="Public" GetMethodName="GetDataByMediaGroupID" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataByMediaGroupID" UserSourceName="FillByMediaGroupID">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT        GM.MediaGroupID, GM.MediaID, M.MediaName
FROM            Media.MediaGroupMember AS GM INNER JOIN
                         Media.Media AS M ON GM.MediaID = M.MediaID
where (GM.MediaGroupID = @MediaGroupID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaGroupID" ColumnName="MediaGroupID" DataSourceName="NovaDB.Media.MediaGroupMember" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaGroupID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaGroupID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="MediaChannelTableAdapter" GeneratorDataComponentClassName="MediaChannelTableAdapter" Name="MediaChannel" UserDataComponentName="MediaChannelTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Media.MediaChannel" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Media].[MediaChannel] WHERE (([MediaChannelID] = @Original_MediaChannelID) AND ([MediaChannelName] = @Original_MediaChannelName) AND ([Dormant] = @Original_Dormant))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaChannelID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaChannelID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_MediaChannelName" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="MediaChannelName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Dormant" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Media].[MediaChannel] ([MediaChannelName], [Dormant]) VALUES (@MediaChannelName, @Dormant);
SELECT MediaChannelID, MediaChannelName, Dormant FROM Media.MediaChannel WHERE (MediaChannelID = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@MediaChannelName" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="MediaChannelName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Dormant" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT MediaChannelID, MediaChannelName, Dormant FROM Media.MediaChannel</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Media].[MediaChannel] SET [MediaChannelName] = @MediaChannelName, [Dormant] = @Dormant WHERE (([MediaChannelID] = @Original_MediaChannelID) AND ([MediaChannelName] = @Original_MediaChannelName) AND ([Dormant] = @Original_Dormant));
SELECT MediaChannelID, MediaChannelName, Dormant FROM Media.MediaChannel WHERE (MediaChannelID = @MediaChannelID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@MediaChannelName" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="MediaChannelName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Dormant" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaChannelID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaChannelID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_MediaChannelName" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="MediaChannelName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Dormant" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaChannelID" ColumnName="MediaChannelID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaChannelID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaChannelID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MediaChannelID" DataSetColumn="MediaChannelID" />
              <Mapping SourceColumn="MediaChannelName" DataSetColumn="MediaChannelName" />
              <Mapping SourceColumn="Dormant" DataSetColumn="Dormant" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="MediaChannelGroupMemberTableAdapter" GeneratorDataComponentClassName="MediaChannelGroupMemberTableAdapter" Name="MediaChannelGroupMember" UserDataComponentName="MediaChannelGroupMemberTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Media].[MediaChannelGroupMember] WHERE (([MediaChannelGroupMemberID] = @Original_MediaChannelGroupMemberID) AND ([MediaChannelID] = @Original_MediaChannelID) AND ([MediaGroupID] = @Original_MediaGroupID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaChannelGroupMemberID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaChannelGroupMemberID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaChannelID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaChannelID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaGroupID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaGroupID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Media].[MediaChannelGroupMember] ([MediaChannelID], [MediaGroupID]) VALUES (@MediaChannelID, @MediaGroupID);
SELECT MediaChannelGroupMemberID, MediaChannelID, MediaGroupID FROM Media.MediaChannelGroupMember WHERE (MediaChannelGroupMemberID = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@MediaChannelID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaChannelID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@MediaGroupID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaGroupID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT        GM.MediaChannelGroupMemberID, MC.MediaChannelID, GM.MediaGroupID, MC.MediaChannelName, MG.MediaGroupName
FROM            Media.MediaChannelGroupMember AS GM INNER JOIN
                         Media.MediaChannel AS MC ON GM.MediaChannelID = MC.MediaChannelID INNER JOIN
                         Media.MediaGroup AS MG ON GM.MediaGroupID = MG.MediaGroupID</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Media].[MediaChannelGroupMember] SET [MediaChannelID] = @MediaChannelID, [MediaGroupID] = @MediaGroupID WHERE (([MediaChannelGroupMemberID] = @Original_MediaChannelGroupMemberID) AND ([MediaChannelID] = @Original_MediaChannelID) AND ([MediaGroupID] = @Original_MediaGroupID));
SELECT MediaChannelGroupMemberID, MediaChannelID, MediaGroupID FROM Media.MediaChannelGroupMember WHERE (MediaChannelGroupMemberID = @MediaChannelGroupMemberID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@MediaChannelID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaChannelID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@MediaGroupID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaGroupID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaChannelGroupMemberID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaChannelGroupMemberID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaChannelID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaChannelID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaGroupID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaGroupID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaChannelGroupMemberID" ColumnName="MediaChannelGroupMemberID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaChannelGroupMemberID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaChannelGroupMemberID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MediaChannelGroupMemberID" DataSetColumn="MediaChannelGroupMemberID" />
              <Mapping SourceColumn="MediaChannelID" DataSetColumn="MediaChannelID" />
              <Mapping SourceColumn="MediaGroupID" DataSetColumn="MediaGroupID" />
              <Mapping SourceColumn="MediaChannelName" DataSetColumn="MediaChannelName" />
              <Mapping SourceColumn="MediaGroupName" DataSetColumn="MediaGroupName" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="MediaCostTableAdapter" GeneratorDataComponentClassName="MediaCostTableAdapter" Name="MediaCost" UserDataComponentName="MediaCostTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Media.MediaCost" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Media].[MediaCost] WHERE (([MediaCostId] = @Original_MediaCostId) AND ([MediaId] = @Original_MediaId) AND ([EffectiveDate] = @Original_EffectiveDate) AND ([isPercentage] = @Original_isPercentage) AND ([CostPercentage] = @Original_CostPercentage) AND ([CostPrice] = @Original_CostPrice))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaCostId" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaCostId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaId" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_EffectiveDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="EffectiveDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_isPercentage" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="isPercentage" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_CostPercentage" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="CostPercentage" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Currency" Direction="Input" ParameterName="@Original_CostPrice" Precision="0" ProviderType="Money" Scale="0" Size="0" SourceColumn="CostPrice" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Media].[MediaCost] ([MediaId], [EffectiveDate], [isPercentage], [CostPercentage], [CostPrice]) VALUES (@MediaId, @EffectiveDate, @isPercentage, @CostPercentage, @CostPrice);
SELECT MediaCostId, MediaId, EffectiveDate, isPercentage, CostPercentage, CostPrice FROM Media.MediaCost WHERE (MediaCostId = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@MediaId" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@EffectiveDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="EffectiveDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@isPercentage" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="isPercentage" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@CostPercentage" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="CostPercentage" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Currency" Direction="Input" ParameterName="@CostPrice" Precision="0" ProviderType="Money" Scale="0" Size="0" SourceColumn="CostPrice" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT MediaCostId, MediaId, EffectiveDate, isPercentage, CostPercentage, CostPrice
FROM     Media.MediaCost
WHERE  (MediaId = @MediaId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaId" ColumnName="MediaId" DataSourceName="NovaDB.Media.MediaCost" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaId" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaId" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Media].[MediaCost] SET [MediaId] = @MediaId, [EffectiveDate] = @EffectiveDate, [isPercentage] = @isPercentage, [CostPercentage] = @CostPercentage, [CostPrice] = @CostPrice WHERE (([MediaCostId] = @Original_MediaCostId) AND ([MediaId] = @Original_MediaId) AND ([EffectiveDate] = @Original_EffectiveDate) AND ([isPercentage] = @Original_isPercentage) AND ([CostPercentage] = @Original_CostPercentage) AND ([CostPrice] = @Original_CostPrice));
SELECT MediaCostId, MediaId, EffectiveDate, isPercentage, CostPercentage, CostPrice FROM Media.MediaCost WHERE (MediaCostId = @MediaCostId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@MediaId" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaId" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@EffectiveDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="EffectiveDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@isPercentage" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="isPercentage" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@CostPercentage" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="CostPercentage" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Currency" Direction="Input" ParameterName="@CostPrice" Precision="0" ProviderType="Money" Scale="0" Size="0" SourceColumn="CostPrice" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaCostId" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaCostId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_MediaId" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="MediaId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_EffectiveDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="EffectiveDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_isPercentage" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="isPercentage" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Decimal" Direction="Input" ParameterName="@Original_CostPercentage" Precision="18" ProviderType="Decimal" Scale="2" Size="0" SourceColumn="CostPercentage" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Currency" Direction="Input" ParameterName="@Original_CostPrice" Precision="0" ProviderType="Money" Scale="0" Size="0" SourceColumn="CostPrice" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="MediaCostId" ColumnName="MediaCostId" DataSourceName="NovaDB.Media.MediaCost" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@MediaCostId" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="MediaCostId" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="MediaCostId" DataSetColumn="MediaCostId" />
              <Mapping SourceColumn="MediaId" DataSetColumn="MediaId" />
              <Mapping SourceColumn="EffectiveDate" DataSetColumn="EffectiveDate" />
              <Mapping SourceColumn="isPercentage" DataSetColumn="isPercentage" />
              <Mapping SourceColumn="CostPercentage" DataSetColumn="CostPercentage" />
              <Mapping SourceColumn="CostPrice" DataSetColumn="CostPrice" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DataSetMedia" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_DataSetName="DataSetMedia" msprop:Generator_UserDSName="DataSetMedia">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="Media" msprop:Generator_UserTableName="Media" msprop:Generator_RowEvArgName="MediaRowChangeEvent" msprop:Generator_TableVarName="tableMedia" msprop:Generator_TablePropName="Media" msprop:Generator_RowDeletingName="MediaRowDeleting" msprop:Generator_RowChangingName="MediaRowChanging" msprop:Generator_RowDeletedName="MediaRowDeleted" msprop:Generator_RowEvHandlerName="MediaRowChangeEventHandler" msprop:Generator_TableClassName="MediaDataTable" msprop:Generator_RowChangedName="MediaRowChanged" msprop:Generator_RowClassName="MediaRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MediaID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msprop:Generator_ColumnVarNameInTable="columnMediaID" msprop:Generator_ColumnPropNameInRow="MediaID" msprop:Generator_ColumnPropNameInTable="MediaIDColumn" msprop:Generator_UserColumnName="MediaID" type="xs:int" />
              <xs:element name="MediaName" msprop:Generator_ColumnVarNameInTable="columnMediaName" msprop:Generator_ColumnPropNameInRow="MediaName" msprop:Generator_ColumnPropNameInTable="MediaNameColumn" msprop:Generator_UserColumnName="MediaName" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Stock" msprop:Generator_ColumnVarNameInTable="columnStock" msprop:Generator_ColumnPropNameInRow="Stock" msprop:Generator_ColumnPropNameInTable="StockColumn" msprop:Generator_UserColumnName="Stock" type="xs:int" default="0" />
              <xs:element name="Clutter" msprop:Generator_ColumnVarNameInTable="columnClutter" msprop:Generator_ColumnPropNameInRow="Clutter" msprop:Generator_ColumnPropNameInTable="ClutterColumn" msprop:Generator_UserColumnName="Clutter" type="xs:boolean" default="false" />
              <xs:element name="Notes" msprop:Generator_ColumnVarNameInTable="columnNotes" msprop:Generator_ColumnPropNameInRow="Notes" msprop:Generator_ColumnPropNameInTable="NotesColumn" msprop:Generator_UserColumnName="Notes" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Homesite" msprop:Generator_ColumnVarNameInTable="columnHomesite" msprop:Generator_ColumnPropNameInRow="Homesite" msprop:Generator_ColumnPropNameInTable="HomesiteColumn" msprop:Generator_UserColumnName="Homesite" type="xs:boolean" default="true" />
              <xs:element name="Crossover" msprop:Generator_ColumnVarNameInTable="columnCrossover" msprop:Generator_ColumnPropNameInRow="Crossover" msprop:Generator_ColumnPropNameInTable="CrossoverColumn" msprop:Generator_UserColumnName="Crossover" type="xs:boolean" default="true" />
              <xs:element name="isPNPPcaStatus" msprop:Generator_ColumnVarNameInTable="columnisPNPPcaStatus" msprop:Generator_ColumnPropNameInRow="isPNPPcaStatus" msprop:Generator_ColumnPropNameInTable="isPNPPcaStatusColumn" msprop:Generator_UserColumnName="isPNPPcaStatus" type="xs:boolean" default="false" />
              <xs:element name="hasMediaCost" msprop:Generator_ColumnVarNameInTable="columnhasMediaCost" msprop:Generator_ColumnPropNameInRow="hasMediaCost" msprop:Generator_ColumnPropNameInTable="hasMediaCostColumn" msprop:Generator_UserColumnName="hasMediaCost" type="xs:boolean" default="false" />
              <xs:element name="isCostOverridable" msprop:Generator_ColumnVarNameInTable="columnisCostOverridable" msprop:Generator_ColumnPropNameInRow="isCostOverridable" msprop:Generator_ColumnPropNameInTable="isCostOverridableColumn" msprop:Generator_UserColumnName="isCostOverridable" type="xs:boolean" default="false" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="MediaCategory" msprop:Generator_UserTableName="MediaCategory" msprop:Generator_RowEvArgName="MediaCategoryRowChangeEvent" msprop:Generator_TableVarName="tableMediaCategory" msprop:Generator_TablePropName="MediaCategory" msprop:Generator_RowDeletingName="MediaCategoryRowDeleting" msprop:Generator_RowChangingName="MediaCategoryRowChanging" msprop:Generator_RowDeletedName="MediaCategoryRowDeleted" msprop:Generator_RowEvHandlerName="MediaCategoryRowChangeEventHandler" msprop:Generator_TableClassName="MediaCategoryDataTable" msprop:Generator_RowChangedName="MediaCategoryRowChanged" msprop:Generator_RowClassName="MediaCategoryRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MediaID" msprop:Generator_ColumnVarNameInTable="columnMediaID" msprop:Generator_ColumnPropNameInRow="MediaID" msprop:Generator_ColumnPropNameInTable="MediaIDColumn" msprop:Generator_UserColumnName="MediaID" type="xs:int" />
              <xs:element name="CategoryID" msprop:Generator_ColumnVarNameInTable="columnCategoryID" msprop:Generator_ColumnPropNameInRow="CategoryID" msprop:Generator_ColumnPropNameInTable="CategoryIDColumn" msprop:Generator_UserColumnName="CategoryID" type="xs:int" />
              <xs:element name="CategoryName" msdata:ReadOnly="true" msdata:Expression="Parent(FK_MediaCategory_Category).CategoryName" msprop:Generator_ColumnVarNameInTable="columnCategoryName" msprop:Generator_ColumnPropNameInRow="CategoryName" msprop:Generator_ColumnPropNameInTable="CategoryNameColumn" msprop:Generator_UserColumnName="CategoryName" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Category" msprop:Generator_UserTableName="Category" msprop:Generator_RowEvArgName="CategoryRowChangeEvent" msprop:Generator_TableVarName="tableCategory" msprop:Generator_TablePropName="Category" msprop:Generator_RowDeletingName="CategoryRowDeleting" msprop:Generator_RowChangingName="CategoryRowChanging" msprop:Generator_RowDeletedName="CategoryRowDeleted" msprop:Generator_RowEvHandlerName="CategoryRowChangeEventHandler" msprop:Generator_TableClassName="CategoryDataTable" msprop:Generator_RowChangedName="CategoryRowChanged" msprop:Generator_RowClassName="CategoryRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="CategoryID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msprop:Generator_ColumnVarNameInTable="columnCategoryID" msprop:Generator_ColumnPropNameInRow="CategoryID" msprop:Generator_ColumnPropNameInTable="CategoryIDColumn" msprop:Generator_UserColumnName="CategoryID" type="xs:int" />
              <xs:element name="CategoryName" msprop:Generator_ColumnVarNameInTable="columnCategoryName" msprop:Generator_ColumnPropNameInRow="CategoryName" msprop:Generator_ColumnPropNameInTable="CategoryNameColumn" msprop:Generator_UserColumnName="CategoryName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Dormant" msprop:Generator_ColumnVarNameInTable="columnDormant" msprop:Generator_ColumnPropNameInRow="Dormant" msprop:Generator_ColumnPropNameInTable="DormantColumn" msprop:Generator_UserColumnName="Dormant" type="xs:boolean" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="MediaFamilyMember" msprop:Generator_UserTableName="MediaFamilyMember" msprop:Generator_RowEvArgName="MediaFamilyMemberRowChangeEvent" msprop:Generator_TableVarName="tableMediaFamilyMember" msprop:Generator_TablePropName="MediaFamilyMember" msprop:Generator_RowDeletingName="MediaFamilyMemberRowDeleting" msprop:Generator_RowChangingName="MediaFamilyMemberRowChanging" msprop:Generator_RowDeletedName="MediaFamilyMemberRowDeleted" msprop:Generator_RowEvHandlerName="MediaFamilyMemberRowChangeEventHandler" msprop:Generator_TableClassName="MediaFamilyMemberDataTable" msprop:Generator_RowChangedName="MediaFamilyMemberRowChanged" msprop:Generator_RowClassName="MediaFamilyMemberRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MediaID" msprop:Generator_ColumnVarNameInTable="columnMediaID" msprop:Generator_ColumnPropNameInRow="MediaID" msprop:Generator_ColumnPropNameInTable="MediaIDColumn" msprop:Generator_UserColumnName="MediaID" type="xs:int" />
              <xs:element name="MediaFamilyID" msprop:Generator_ColumnVarNameInTable="columnMediaFamilyID" msprop:Generator_ColumnPropNameInRow="MediaFamilyID" msprop:Generator_ColumnPropNameInTable="MediaFamilyIDColumn" msprop:Generator_UserColumnName="MediaFamilyID" type="xs:int" />
              <xs:element name="MediaFamilyName" msdata:ReadOnly="true" msdata:Expression="Parent(FK_MediaFamilyMember_MediaFamily).MediaFamilyName" msprop:Generator_ColumnVarNameInTable="columnMediaFamilyName" msprop:Generator_ColumnPropNameInRow="MediaFamilyName" msprop:Generator_ColumnPropNameInTable="MediaFamilyNameColumn" msprop:Generator_UserColumnName="MediaFamilyName" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="MediaFamily" msprop:Generator_UserTableName="MediaFamily" msprop:Generator_RowEvArgName="MediaFamilyRowChangeEvent" msprop:Generator_TableVarName="tableMediaFamily" msprop:Generator_TablePropName="MediaFamily" msprop:Generator_RowDeletingName="MediaFamilyRowDeleting" msprop:Generator_RowChangingName="MediaFamilyRowChanging" msprop:Generator_RowDeletedName="MediaFamilyRowDeleted" msprop:Generator_RowEvHandlerName="MediaFamilyRowChangeEventHandler" msprop:Generator_TableClassName="MediaFamilyDataTable" msprop:Generator_RowChangedName="MediaFamilyRowChanged" msprop:Generator_RowClassName="MediaFamilyRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MediaFamilyID" msprop:Generator_ColumnVarNameInTable="columnMediaFamilyID" msprop:Generator_ColumnPropNameInRow="MediaFamilyID" msprop:Generator_ColumnPropNameInTable="MediaFamilyIDColumn" msprop:Generator_UserColumnName="MediaFamilyID" type="xs:int" />
              <xs:element name="MediaFamilyName" msprop:Generator_ColumnVarNameInTable="columnMediaFamilyName" msprop:Generator_ColumnPropNameInRow="MediaFamilyName" msprop:Generator_ColumnPropNameInTable="MediaFamilyNameColumn" msprop:Generator_UserColumnName="MediaFamilyName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="CompetingMedia" msprop:Generator_UserTableName="CompetingMedia" msprop:Generator_RowEvArgName="CompetingMediaRowChangeEvent" msprop:Generator_TableVarName="tableCompetingMedia" msprop:Generator_TablePropName="CompetingMedia" msprop:Generator_RowDeletingName="CompetingMediaRowDeleting" msprop:Generator_RowChangingName="CompetingMediaRowChanging" msprop:Generator_RowDeletedName="CompetingMediaRowDeleted" msprop:Generator_RowEvHandlerName="CompetingMediaRowChangeEventHandler" msprop:Generator_TableClassName="CompetingMediaDataTable" msprop:Generator_RowChangedName="CompetingMediaRowChanged" msprop:Generator_RowClassName="CompetingMediaRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MediaID" msprop:Generator_ColumnVarNameInTable="columnMediaID" msprop:Generator_ColumnPropNameInRow="MediaID" msprop:Generator_ColumnPropNameInTable="MediaIDColumn" msprop:Generator_UserColumnName="MediaID" type="xs:int" />
              <xs:element name="CompetingMediaName" msprop:Generator_ColumnVarNameInTable="columnCompetingMediaName" msprop:Generator_ColumnPropNameInRow="CompetingMediaName" msprop:Generator_ColumnPropNameInTable="CompetingMediaNameColumn" msprop:Generator_UserColumnName="CompetingMediaName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="MediaLifeCycle" msprop:Generator_UserTableName="MediaLifeCycle" msprop:Generator_RowEvArgName="MediaLifeCycleRowChangeEvent" msprop:Generator_TableVarName="tableMediaLifeCycle" msprop:Generator_TablePropName="MediaLifeCycle" msprop:Generator_RowDeletingName="MediaLifeCycleRowDeleting" msprop:Generator_RowChangingName="MediaLifeCycleRowChanging" msprop:Generator_RowDeletedName="MediaLifeCycleRowDeleted" msprop:Generator_RowEvHandlerName="MediaLifeCycleRowChangeEventHandler" msprop:Generator_TableClassName="MediaLifeCycleDataTable" msprop:Generator_RowChangedName="MediaLifeCycleRowChanged" msprop:Generator_RowClassName="MediaLifeCycleRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MediaID" msprop:Generator_ColumnVarNameInTable="columnMediaID" msprop:Generator_ColumnPropNameInRow="MediaID" msprop:Generator_ColumnPropNameInTable="MediaIDColumn" msprop:Generator_UserColumnName="MediaID" type="xs:int" />
              <xs:element name="FirstWeek" msprop:Generator_ColumnVarNameInTable="columnFirstWeek" msprop:Generator_ColumnPropNameInRow="FirstWeek" msprop:Generator_ColumnPropNameInTable="FirstWeekColumn" msprop:Generator_UserColumnName="FirstWeek" type="xs:dateTime" />
              <xs:element name="LastWeek" msprop:Generator_ColumnVarNameInTable="columnLastWeek" msprop:Generator_ColumnPropNameInRow="LastWeek" msprop:Generator_ColumnPropNameInTable="LastWeekColumn" msprop:Generator_UserColumnName="LastWeek" type="xs:dateTime" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="BurstCount" msprop:Generator_UserTableName="BurstCount" msprop:Generator_RowEvArgName="BurstCountRowChangeEvent" msprop:Generator_TableVarName="tableBurstCount" msprop:Generator_TablePropName="BurstCount" msprop:Generator_RowDeletingName="BurstCountRowDeleting" msprop:Generator_RowChangingName="BurstCountRowChanging" msprop:Generator_RowDeletedName="BurstCountRowDeleted" msprop:Generator_RowEvHandlerName="BurstCountRowChangeEventHandler" msprop:Generator_TableClassName="BurstCountDataTable" msprop:Generator_RowChangedName="BurstCountRowChanged" msprop:Generator_RowClassName="BurstCountRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MediaID" msprop:Generator_ColumnVarNameInTable="columnMediaID" msprop:Generator_ColumnPropNameInRow="MediaID" msprop:Generator_ColumnPropNameInTable="MediaIDColumn" msprop:Generator_UserColumnName="MediaID" type="xs:int" />
              <xs:element name="BurstCount" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnBurstCount" msprop:Generator_ColumnPropNameInRow="BurstCount" msprop:Generator_ColumnPropNameInTable="BurstCountColumn" msprop:Generator_UserColumnName="BurstCount" type="xs:int" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="StoreMediaCategoryPermissions" msprop:Generator_TableClassName="StoreMediaCategoryPermissionsDataTable" msprop:Generator_TableVarName="tableStoreMediaCategoryPermissions" msprop:Generator_RowChangedName="StoreMediaCategoryPermissionsRowChanged" msprop:Generator_TablePropName="StoreMediaCategoryPermissions" msprop:Generator_RowDeletingName="StoreMediaCategoryPermissionsRowDeleting" msprop:Generator_RowChangingName="StoreMediaCategoryPermissionsRowChanging" msprop:Generator_RowEvHandlerName="StoreMediaCategoryPermissionsRowChangeEventHandler" msprop:Generator_RowDeletedName="StoreMediaCategoryPermissionsRowDeleted" msprop:Generator_RowClassName="StoreMediaCategoryPermissionsRow" msprop:Generator_UserTableName="StoreMediaCategoryPermissions" msprop:Generator_RowEvArgName="StoreMediaCategoryPermissionsRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="StoreName" msprop:Generator_ColumnVarNameInTable="columnStoreName" msprop:Generator_ColumnPropNameInRow="StoreName" msprop:Generator_ColumnPropNameInTable="StoreNameColumn" msprop:Generator_UserColumnName="StoreName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="isProhibited" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnisProhibited" msprop:Generator_ColumnPropNameInRow="isProhibited" msprop:Generator_ColumnPropNameInTable="isProhibitedColumn" msprop:Generator_UserColumnName="isProhibited" type="xs:int" minOccurs="0" />
              <xs:element name="StoreID" msprop:Generator_ColumnVarNameInTable="columnStoreID" msprop:Generator_ColumnPropNameInRow="StoreID" msprop:Generator_ColumnPropNameInTable="StoreIDColumn" msprop:Generator_UserColumnName="StoreID" type="xs:int" />
              <xs:element name="RegionName" msprop:Generator_ColumnVarNameInTable="columnRegionName" msprop:Generator_ColumnPropNameInRow="RegionName" msprop:Generator_ColumnPropNameInTable="RegionNameColumn" msprop:Generator_UserColumnName="RegionName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="StoreMediaCategoryPermission" msprop:Generator_TableClassName="StoreMediaCategoryPermissionDataTable" msprop:Generator_TableVarName="tableStoreMediaCategoryPermission" msprop:Generator_TablePropName="StoreMediaCategoryPermission" msprop:Generator_RowDeletingName="StoreMediaCategoryPermissionRowDeleting" msprop:Generator_RowChangingName="StoreMediaCategoryPermissionRowChanging" msprop:Generator_RowEvHandlerName="StoreMediaCategoryPermissionRowChangeEventHandler" msprop:Generator_RowDeletedName="StoreMediaCategoryPermissionRowDeleted" msprop:Generator_UserTableName="StoreMediaCategoryPermission" msprop:Generator_RowChangedName="StoreMediaCategoryPermissionRowChanged" msprop:Generator_RowEvArgName="StoreMediaCategoryPermissionRowChangeEvent" msprop:Generator_RowClassName="StoreMediaCategoryPermissionRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnID" msprop:Generator_ColumnPropNameInRow="ID" msprop:Generator_ColumnPropNameInTable="IDColumn" msprop:Generator_UserColumnName="ID" type="xs:int" />
              <xs:element name="CategoryID" msprop:Generator_ColumnVarNameInTable="columnCategoryID" msprop:Generator_ColumnPropNameInRow="CategoryID" msprop:Generator_ColumnPropNameInTable="CategoryIDColumn" msprop:Generator_UserColumnName="CategoryID" type="xs:int" />
              <xs:element name="StoreID" msprop:Generator_ColumnVarNameInTable="columnStoreID" msprop:Generator_ColumnPropNameInRow="StoreID" msprop:Generator_ColumnPropNameInTable="StoreIDColumn" msprop:Generator_UserColumnName="StoreID" type="xs:int" />
              <xs:element name="MediaID" msprop:Generator_ColumnVarNameInTable="columnMediaID" msprop:Generator_ColumnPropNameInRow="MediaID" msprop:Generator_ColumnPropNameInTable="MediaIDColumn" msprop:Generator_UserColumnName="MediaID" type="xs:int" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="MediaGroup" msprop:Generator_TableClassName="MediaGroupDataTable" msprop:Generator_TableVarName="tableMediaGroup" msprop:Generator_TablePropName="MediaGroup" msprop:Generator_RowDeletingName="MediaGroupRowDeleting" msprop:Generator_RowChangingName="MediaGroupRowChanging" msprop:Generator_RowEvHandlerName="MediaGroupRowChangeEventHandler" msprop:Generator_RowDeletedName="MediaGroupRowDeleted" msprop:Generator_UserTableName="MediaGroup" msprop:Generator_RowChangedName="MediaGroupRowChanged" msprop:Generator_RowEvArgName="MediaGroupRowChangeEvent" msprop:Generator_RowClassName="MediaGroupRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MediaGroupID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnMediaGroupID" msprop:Generator_ColumnPropNameInRow="MediaGroupID" msprop:Generator_ColumnPropNameInTable="MediaGroupIDColumn" msprop:Generator_UserColumnName="MediaGroupID" type="xs:int" />
              <xs:element name="MediaGroupName" msprop:nullValue="_empty" msprop:Generator_ColumnPropNameInRow="MediaGroupName" msprop:Generator_ColumnVarNameInTable="columnMediaGroupName" msprop:Generator_ColumnPropNameInTable="MediaGroupNameColumn" msprop:Generator_UserColumnName="MediaGroupName" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Dormant" msprop:Generator_ColumnVarNameInTable="columnDormant" msprop:Generator_ColumnPropNameInRow="Dormant" msprop:Generator_ColumnPropNameInTable="DormantColumn" msprop:Generator_UserColumnName="Dormant" type="xs:boolean" default="false" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="MediaGroupMember" msprop:Generator_TableClassName="MediaGroupMemberDataTable" msprop:Generator_TableVarName="tableMediaGroupMember" msprop:Generator_TablePropName="MediaGroupMember" msprop:Generator_RowDeletingName="MediaGroupMemberRowDeleting" msprop:Generator_RowChangingName="MediaGroupMemberRowChanging" msprop:Generator_RowEvHandlerName="MediaGroupMemberRowChangeEventHandler" msprop:Generator_RowDeletedName="MediaGroupMemberRowDeleted" msprop:Generator_UserTableName="MediaGroupMember" msprop:Generator_RowChangedName="MediaGroupMemberRowChanged" msprop:Generator_RowEvArgName="MediaGroupMemberRowChangeEvent" msprop:Generator_RowClassName="MediaGroupMemberRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MediaGroupID" msprop:Generator_ColumnVarNameInTable="columnMediaGroupID" msprop:Generator_ColumnPropNameInRow="MediaGroupID" msprop:Generator_ColumnPropNameInTable="MediaGroupIDColumn" msprop:Generator_UserColumnName="MediaGroupID" type="xs:int" />
              <xs:element name="MediaID" msprop:Generator_ColumnVarNameInTable="columnMediaID" msprop:Generator_ColumnPropNameInRow="MediaID" msprop:Generator_ColumnPropNameInTable="MediaIDColumn" msprop:Generator_UserColumnName="MediaID" type="xs:int" />
              <xs:element name="MediaName" msprop:nullValue="_empty" msprop:Generator_ColumnPropNameInRow="MediaName" msprop:Generator_ColumnVarNameInTable="columnMediaName" msprop:Generator_ColumnPropNameInTable="MediaNameColumn" msprop:Generator_UserColumnName="MediaName" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="MediaChannel" msprop:Generator_TableClassName="MediaChannelDataTable" msprop:Generator_TableVarName="tableMediaChannel" msprop:Generator_RowChangedName="MediaChannelRowChanged" msprop:Generator_TablePropName="MediaChannel" msprop:Generator_RowDeletingName="MediaChannelRowDeleting" msprop:Generator_RowChangingName="MediaChannelRowChanging" msprop:Generator_RowEvHandlerName="MediaChannelRowChangeEventHandler" msprop:Generator_RowDeletedName="MediaChannelRowDeleted" msprop:Generator_RowClassName="MediaChannelRow" msprop:Generator_UserTableName="MediaChannel" msprop:Generator_RowEvArgName="MediaChannelRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MediaChannelID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnMediaChannelID" msprop:Generator_ColumnPropNameInRow="MediaChannelID" msprop:Generator_ColumnPropNameInTable="MediaChannelIDColumn" msprop:Generator_UserColumnName="MediaChannelID" type="xs:int" />
              <xs:element name="MediaChannelName" msprop:Generator_ColumnVarNameInTable="columnMediaChannelName" msprop:Generator_ColumnPropNameInRow="MediaChannelName" msprop:Generator_ColumnPropNameInTable="MediaChannelNameColumn" msprop:Generator_UserColumnName="MediaChannelName" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Dormant" msprop:Generator_ColumnVarNameInTable="columnDormant" msprop:Generator_ColumnPropNameInRow="Dormant" msprop:Generator_ColumnPropNameInTable="DormantColumn" msprop:Generator_UserColumnName="Dormant" type="xs:boolean" default="false" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="MediaChannelGroupMember" msprop:Generator_TableClassName="MediaChannelGroupMemberDataTable" msprop:Generator_TableVarName="tableMediaChannelGroupMember" msprop:Generator_RowChangedName="MediaChannelGroupMemberRowChanged" msprop:Generator_TablePropName="MediaChannelGroupMember" msprop:Generator_RowDeletingName="MediaChannelGroupMemberRowDeleting" msprop:Generator_RowChangingName="MediaChannelGroupMemberRowChanging" msprop:Generator_RowEvHandlerName="MediaChannelGroupMemberRowChangeEventHandler" msprop:Generator_RowDeletedName="MediaChannelGroupMemberRowDeleted" msprop:Generator_RowClassName="MediaChannelGroupMemberRow" msprop:Generator_UserTableName="MediaChannelGroupMember" msprop:Generator_RowEvArgName="MediaChannelGroupMemberRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MediaChannelGroupMemberID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnMediaChannelGroupMemberID" msprop:Generator_ColumnPropNameInRow="MediaChannelGroupMemberID" msprop:Generator_ColumnPropNameInTable="MediaChannelGroupMemberIDColumn" msprop:Generator_UserColumnName="MediaChannelGroupMemberID" type="xs:int" />
              <xs:element name="MediaChannelID" msprop:Generator_ColumnVarNameInTable="columnMediaChannelID" msprop:Generator_ColumnPropNameInRow="MediaChannelID" msprop:Generator_ColumnPropNameInTable="MediaChannelIDColumn" msprop:Generator_UserColumnName="MediaChannelID" type="xs:int" />
              <xs:element name="MediaGroupID" msprop:Generator_ColumnVarNameInTable="columnMediaGroupID" msprop:Generator_ColumnPropNameInRow="MediaGroupID" msprop:Generator_ColumnPropNameInTable="MediaGroupIDColumn" msprop:Generator_UserColumnName="MediaGroupID" type="xs:int" />
              <xs:element name="MediaChannelName" msprop:Generator_ColumnVarNameInTable="columnMediaChannelName" msprop:Generator_ColumnPropNameInRow="MediaChannelName" msprop:Generator_ColumnPropNameInTable="MediaChannelNameColumn" msprop:Generator_UserColumnName="MediaChannelName" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="MediaGroupName" msprop:Generator_ColumnVarNameInTable="columnMediaGroupName" msprop:Generator_ColumnPropNameInRow="MediaGroupName" msprop:Generator_ColumnPropNameInTable="MediaGroupNameColumn" msprop:Generator_UserColumnName="MediaGroupName" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="MediaCost" msprop:Generator_TableClassName="MediaCostDataTable" msprop:Generator_TableVarName="tableMediaCost" msprop:Generator_RowChangedName="MediaCostRowChanged" msprop:Generator_TablePropName="MediaCost" msprop:Generator_RowDeletingName="MediaCostRowDeleting" msprop:Generator_RowChangingName="MediaCostRowChanging" msprop:Generator_RowEvHandlerName="MediaCostRowChangeEventHandler" msprop:Generator_RowDeletedName="MediaCostRowDeleted" msprop:Generator_RowClassName="MediaCostRow" msprop:Generator_UserTableName="MediaCost" msprop:Generator_RowEvArgName="MediaCostRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="MediaCostId" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnMediaCostId" msprop:Generator_ColumnPropNameInRow="MediaCostId" msprop:Generator_ColumnPropNameInTable="MediaCostIdColumn" msprop:Generator_UserColumnName="MediaCostId" type="xs:int" />
              <xs:element name="MediaId" msprop:Generator_ColumnVarNameInTable="columnMediaId" msprop:Generator_ColumnPropNameInRow="MediaId" msprop:Generator_ColumnPropNameInTable="MediaIdColumn" msprop:Generator_UserColumnName="MediaId" type="xs:int" />
              <xs:element name="EffectiveDate" msprop:Generator_ColumnVarNameInTable="columnEffectiveDate" msprop:Generator_ColumnPropNameInRow="EffectiveDate" msprop:Generator_ColumnPropNameInTable="EffectiveDateColumn" msprop:Generator_UserColumnName="EffectiveDate" type="xs:dateTime" />
              <xs:element name="isPercentage" msprop:Generator_ColumnVarNameInTable="columnisPercentage" msprop:Generator_ColumnPropNameInRow="isPercentage" msprop:Generator_ColumnPropNameInTable="isPercentageColumn" msprop:Generator_UserColumnName="isPercentage" type="xs:boolean" default="false" />
              <xs:element name="CostPercentage" msprop:Generator_ColumnVarNameInTable="columnCostPercentage" msprop:Generator_ColumnPropNameInRow="CostPercentage" msprop:Generator_ColumnPropNameInTable="CostPercentageColumn" msprop:Generator_UserColumnName="CostPercentage" type="xs:decimal" default="0.00" />
              <xs:element name="CostPrice" msprop:Generator_ColumnVarNameInTable="columnCostPrice" msprop:Generator_ColumnPropNameInRow="CostPrice" msprop:Generator_ColumnPropNameInTable="CostPriceColumn" msprop:Generator_UserColumnName="CostPrice" type="xs:decimal" default="0.00" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Media" />
      <xs:field xpath="mstns:MediaID" />
    </xs:unique>
    <xs:unique name="MediaCategory_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:MediaCategory" />
      <xs:field xpath="mstns:MediaID" />
      <xs:field xpath="mstns:CategoryID" />
    </xs:unique>
    <xs:unique name="Category_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Category" />
      <xs:field xpath="mstns:CategoryID" />
    </xs:unique>
    <xs:unique name="MediaFamilyMember_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:MediaFamilyMember" />
      <xs:field xpath="mstns:MediaID" />
      <xs:field xpath="mstns:MediaFamilyID" />
    </xs:unique>
    <xs:unique name="MediaFamily_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:MediaFamily" />
      <xs:field xpath="mstns:MediaFamilyID" />
    </xs:unique>
    <xs:unique name="CompetingMedia_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:CompetingMedia" />
      <xs:field xpath="mstns:MediaID" />
      <xs:field xpath="mstns:CompetingMediaName" />
    </xs:unique>
    <xs:unique name="MediaLifeCycle_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:MediaLifeCycle" />
      <xs:field xpath="mstns:MediaID" />
      <xs:field xpath="mstns:FirstWeek" />
      <xs:field xpath="mstns:LastWeek" />
    </xs:unique>
    <xs:unique name="StoreMediaCategoryPermissions_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:StoreMediaCategoryPermissions" />
      <xs:field xpath="mstns:StoreID" />
    </xs:unique>
    <xs:unique name="StoreMediaCategoryPermission_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:StoreMediaCategoryPermission" />
      <xs:field xpath="mstns:ID" />
    </xs:unique>
    <xs:unique name="MediaGroup_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:MediaGroup" />
      <xs:field xpath="mstns:MediaGroupID" />
    </xs:unique>
    <xs:unique name="MediaGroupMember_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:MediaGroupMember" />
      <xs:field xpath="mstns:MediaGroupID" />
      <xs:field xpath="mstns:MediaID" />
    </xs:unique>
    <xs:unique name="MediaChannel_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:MediaChannel" />
      <xs:field xpath="mstns:MediaChannelID" />
    </xs:unique>
    <xs:unique name="MediaChannelGroupMember_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:MediaChannelGroupMember" />
      <xs:field xpath="mstns:MediaChannelGroupMemberID" />
    </xs:unique>
    <xs:unique name="MediaCost_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:MediaCost" />
      <xs:field xpath="mstns:MediaCostId" />
    </xs:unique>
  </xs:element>
  <xs:annotation>
    <xs:appinfo>
      <msdata:Relationship name="FK_MediaCategory_Media" msdata:parent="Media" msdata:child="MediaCategory" msdata:parentkey="MediaID" msdata:childkey="MediaID" msprop:Generator_UserChildTable="MediaCategory" msprop:Generator_ChildPropName="GetMediaCategoryRows" msprop:Generator_UserRelationName="FK_MediaCategory_Media" msprop:Generator_ParentPropName="MediaRow" msprop:Generator_RelationVarName="relationFK_MediaCategory_Media" msprop:Generator_UserParentTable="Media" />
      <msdata:Relationship name="FK_MediaCategory_Category" msdata:parent="Category" msdata:child="MediaCategory" msdata:parentkey="CategoryID" msdata:childkey="CategoryID" msprop:Generator_UserChildTable="MediaCategory" msprop:Generator_ChildPropName="GetMediaCategoryRows" msprop:Generator_UserRelationName="FK_MediaCategory_Category" msprop:Generator_ParentPropName="CategoryRow" msprop:Generator_RelationVarName="relationFK_MediaCategory_Category" msprop:Generator_UserParentTable="Category" />
      <msdata:Relationship name="FK_MediaFamilyMember_Media" msdata:parent="Media" msdata:child="MediaFamilyMember" msdata:parentkey="MediaID" msdata:childkey="MediaID" msprop:Generator_UserChildTable="MediaFamilyMember" msprop:Generator_ChildPropName="GetMediaFamilyMemberRows" msprop:Generator_UserRelationName="FK_MediaFamilyMember_Media" msprop:Generator_ParentPropName="MediaRow" msprop:Generator_RelationVarName="relationFK_MediaFamilyMember_Media" msprop:Generator_UserParentTable="Media" />
      <msdata:Relationship name="FK_MediaFamilyMember_MediaFamily" msdata:parent="MediaFamily" msdata:child="MediaFamilyMember" msdata:parentkey="MediaFamilyID" msdata:childkey="MediaFamilyID" msprop:Generator_UserChildTable="MediaFamilyMember" msprop:Generator_ChildPropName="GetMediaFamilyMemberRows" msprop:Generator_UserRelationName="FK_MediaFamilyMember_MediaFamily" msprop:Generator_ParentPropName="MediaFamilyRow" msprop:Generator_RelationVarName="relationFK_MediaFamilyMember_MediaFamily" msprop:Generator_UserParentTable="MediaFamily" />
      <msdata:Relationship name="Media_CompetingMedia" msdata:parent="Media" msdata:child="CompetingMedia" msdata:parentkey="MediaID" msdata:childkey="MediaID" msprop:Generator_UserChildTable="CompetingMedia" msprop:Generator_ChildPropName="GetCompetingMediaRows" msprop:Generator_UserRelationName="Media_CompetingMedia" msprop:Generator_ParentPropName="MediaRow" msprop:Generator_RelationVarName="relationMedia_CompetingMedia" msprop:Generator_UserParentTable="Media" />
      <msdata:Relationship name="FK_MediaLifeCycle_Media" msdata:parent="Media" msdata:child="MediaLifeCycle" msdata:parentkey="MediaID" msdata:childkey="MediaID" msprop:Generator_UserChildTable="MediaLifeCycle" msprop:Generator_ChildPropName="GetMediaLifeCycleRows" msprop:Generator_UserRelationName="FK_MediaLifeCycle_Media" msprop:Generator_ParentPropName="MediaRow" msprop:Generator_RelationVarName="relationFK_MediaLifeCycle_Media" msprop:Generator_UserParentTable="Media" />
      <msdata:Relationship name="Media_BurstCount" msdata:parent="Media" msdata:child="BurstCount" msdata:parentkey="MediaID" msdata:childkey="MediaID" msprop:Generator_UserChildTable="BurstCount" msprop:Generator_ChildPropName="GetBurstCountRows" msprop:Generator_UserRelationName="Media_BurstCount" msprop:Generator_ParentPropName="MediaRow" msprop:Generator_RelationVarName="relationMedia_BurstCount" msprop:Generator_UserParentTable="Media" />
      <msdata:Relationship name="fk_StoreMediaCategoryPermission_CategoryID" msdata:parent="Category" msdata:child="StoreMediaCategoryPermission" msdata:parentkey="CategoryID" msdata:childkey="CategoryID" msprop:Generator_UserChildTable="StoreMediaCategoryPermission" msprop:Generator_ChildPropName="GetStoreMediaCategoryPermissionRows" msprop:Generator_UserRelationName="fk_StoreMediaCategoryPermission_CategoryID" msprop:Generator_ParentPropName="CategoryRow" msprop:Generator_RelationVarName="relationfk_StoreMediaCategoryPermission_CategoryID" msprop:Generator_UserParentTable="Category" />
      <msdata:Relationship name="fk_StoreMediaCategoryPermission_MediaID" msdata:parent="Media" msdata:child="StoreMediaCategoryPermission" msdata:parentkey="MediaID" msdata:childkey="MediaID" msprop:Generator_UserChildTable="StoreMediaCategoryPermission" msprop:Generator_ChildPropName="GetStoreMediaCategoryPermissionRows" msprop:Generator_UserRelationName="fk_StoreMediaCategoryPermission_MediaID" msprop:Generator_ParentPropName="MediaRow" msprop:Generator_RelationVarName="relationfk_StoreMediaCategoryPermission_MediaID" msprop:Generator_UserParentTable="Media" />
      <msdata:Relationship name="FK_MediaGroupMember_MediaGroup" msdata:parent="MediaGroup" msdata:child="MediaGroupMember" msdata:parentkey="MediaGroupID" msdata:childkey="MediaGroupID" msprop:Generator_UserChildTable="MediaGroupMember" msprop:Generator_ChildPropName="GetMediaGroupMemberRows" msprop:Generator_UserRelationName="FK_MediaGroupMember_MediaGroup" msprop:Generator_ParentPropName="MediaGroupRow" msprop:Generator_RelationVarName="relationFK_MediaGroupMember_MediaGroup" msprop:Generator_UserParentTable="MediaGroup" />
      <msdata:Relationship name="FK_MediaGroupMember_Media" msdata:parent="Media" msdata:child="MediaGroupMember" msdata:parentkey="MediaID" msdata:childkey="MediaID" msprop:Generator_UserChildTable="MediaGroupMember" msprop:Generator_ChildPropName="GetMediaGroupMemberRows" msprop:Generator_UserRelationName="FK_MediaGroupMember_Media" msprop:Generator_ParentPropName="MediaRow" msprop:Generator_RelationVarName="relationFK_MediaGroupMember_Media" msprop:Generator_UserParentTable="Media" />
      <msdata:Relationship name="fk_MediaChannelGroupMember_MediaChannelID" msdata:parent="MediaChannel" msdata:child="MediaChannelGroupMember" msdata:parentkey="MediaChannelID" msdata:childkey="MediaChannelID" msprop:Generator_UserChildTable="MediaChannelGroupMember" msprop:Generator_ChildPropName="GetMediaChannelGroupMemberRows" msprop:Generator_UserRelationName="fk_MediaChannelGroupMember_MediaChannelID" msprop:Generator_RelationVarName="relationfk_MediaChannelGroupMember_MediaChannelID" msprop:Generator_UserParentTable="MediaChannel" msprop:Generator_ParentPropName="MediaChannelRow" />
      <msdata:Relationship name="fk_MediaChannelGroupMember_MediaGroupID" msdata:parent="MediaGroup" msdata:child="MediaChannelGroupMember" msdata:parentkey="MediaGroupID" msdata:childkey="MediaGroupID" msprop:Generator_UserChildTable="MediaChannelGroupMember" msprop:Generator_ChildPropName="GetMediaChannelGroupMemberRows" msprop:Generator_UserRelationName="fk_MediaChannelGroupMember_MediaGroupID" msprop:Generator_RelationVarName="relationfk_MediaChannelGroupMember_MediaGroupID" msprop:Generator_UserParentTable="MediaGroup" msprop:Generator_ParentPropName="MediaGroupRow" />
      <msdata:Relationship name="FK_MediaCost_Media" msdata:parent="Media" msdata:child="MediaCost" msdata:parentkey="MediaID" msdata:childkey="MediaId" msprop:Generator_UserChildTable="MediaCost" msprop:Generator_ChildPropName="GetMediaCostRows" msprop:Generator_UserRelationName="FK_MediaCost_Media" msprop:Generator_RelationVarName="relationFK_MediaCost_Media" msprop:Generator_UserParentTable="Media" msprop:Generator_ParentPropName="MediaRow" />
    </xs:appinfo>
  </xs:annotation>
</xs:schema>