<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="ImageList24x24.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>280, 17</value>
  </metadata>
  <data name="ImageList24x24.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        ********************************************************************************
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAAAi
        HQAAAk1TRnQBSQFMAgEBBAEAAVgBAAFYAQABGAEAARgBAAT/AREBAAj/AUIBTQE2BwABNgMAASgDAAFg
        AwABMAMAAQEBAAEQBgABJP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/ACoAAf8BfwGd
        AXMBewFvAZwBcwH/AX8QAAH/AX8BnAFzAXsBbwGdAXMB/wF/CgAB/wF/AZwBcwF7AW8BewFvAXsBbwF7
        AW8BewFvAXsBbwF7AW8BewFvAXsBbwF7AW8BewFvAXsBbwF7AW8BewFvAXsBbwF7AW8BnAFzAf8BfxYA
        Af8BfwH/AX8B/wF/TAAB/wF/AXsBbwH/AXsB/wF7Af8BewGcAXMB/wF/DAAB/wF/AZwBcwH/AXsB/wF7
        Af8CewFvAf8BfwYAAf8BfwF7AW8B/gF/Af4BfwH+AX8B/gF/Af4BfwH+AX8B/gF/Af4BfwH+AX8B/gF/
        Af4BfwH+AX8B/gF/Af4BfwH+AX8B/gF/Af4BfwH+AX8BewFvAd4BexQAAb0BdwF7AW8BnAFvAd4BeyYA
        Af8BfwH/AX8B/wF/HAAB/wF/AXsBbwH/AXsBGAFrAcYBQAHuAVUB/wF7AZwBcwH/AX8IAAH/AX8BnAFv
        Af8BewHuAVUBxgFAARgBawH/AnsBbwH/AX8CAAH/AX8BewFvAf4BfwG5AUIB1QEZAfYBGQH2ARkB9gEZ
        AfYBGQH2ARkB9gEZAfYBGQH2ARkB1QEZAdUBGQHVARkB1QEZAdUBGQHVARkBlAEVAVcBNgHeAX8BnAF3
        Af8Bfw4AAf8BfwF7AW8BvwF7Ad8BfwHfAX8BnAFzAf8BfyIAAf8BfwGcAXMBWwFvAXsBbwH/AX8YAAH/
        AX8BewFvAf8BewHWAWYBAAE0AQABNAEAATQBagFNAf8BewGdAXMB/wF/BAAB/wF/AZ0BcwH/AXsBagFN
        AQABNAEAATQBAAE0AdYBZgH/AnsBbwH/AX8BvQF3Af4BfwGYAToB0gEAAVMBAQEzAQEBMwEBATMBAQEz
        AQEBMwEBATMBAQEzAQEBMwEBARMBAQESAQEBEgEBARIBAQHyAQAB8QEAARIBAQGPAQAB9QEhAf4BfwGc
        AXMMAAH/AX8BewFvAd8BfwF4AWcBaAE6Aa4BSgHfAnsBbwH/AX8eAAH/AX8BWwFrAd8BewG9AXcBvgF3
        AZwBcwH/AX8WAAG9AXcB/wF7AfcBagEAATgBQQE8AWMBQAFjAUABAAE4AYwBUQH/AX8BnAFzAf8BfwH/
        AX8BnAFzAf8BfwGMAVEBAAE4AWMBQAFjAUABQQE8AQABOAH3AWoB/wF7Ab0BdwGcAXMB/gF/AXQBAQF0
        AQEB/wF/AZgBPgE0AQEBdAEBAXQBAQF0AQEBdAEBAXQBAQF0AQEBdAEBAXQBAQFTAQEBUwEBARIBAQEW
        ASYB/wF/AZQBEQHQAQABvQF3AXsBbwoAAf8BfwF7AW8B3wF/AVYBYwEBASYBAQEmAeABHQHwAU4B3wF/
        AZwBcxwAAf8BfwF7AW8B3wF/ARMBWwHgASEBiwFCAd8BfwGcAXMWAAF8AW8B/wF7AWMBQAEgATwBgwFA
        AWMBQAFjAUABYgFAAQABOAGMAVUB/wF7AXwBbwF8AW8B/wF/AawBVQEAATgBYgFAAWMBQAFjAUABgwFA
        ASABPAFjAUAB/wF7AZwBbwGcAXMB/gF/AZUBBQFUAQEBugE+AfYBEQF0AQEBlQEBAZUBAQGVAQEBlQEB
        AXUBAQF0AQEBdAEBAXQBAQF0AQEBVAEBAVMBAQGUAQkBuQFCAVMBAQESAQEBvQF3AXsBbwgAAf8BfwF7
        AW8B3wF/AXkBawEhASoBQwEuAUQBMgFEAS4BIgEqAZwBcwG+AXcB/wF/GAAB/wF/AXsBbwHfAX8BNQFf
        AQABJgECASYBRQEyAd8CewFvAf8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/BAABnAFz
        Af8BewHmAUwBAAFAAYMBRAFjAUQBYwFEAWMBRAFiAUQBAAE8AWsBVQH/AX8B/wF/AWsBVQEAATwBYgFE
        AWMBRAFjAUQBYwFEAYMBRAEAAUAB5gFMAf8BewGcAXMBnAFzAf4BfwG2AQkBdQEBAVQBAQF1AQEBlQEB
        AZUBAQGVAQEBlQEBAZUBAQGVAQEBlQEBAXUBAQF0AQEBdAEBAXQBAQF0AQEBUwEBARIBAQESAQEBMgEB
        Ab0BdwF7AW8GAAH/AX8BewFvAd8BfwF4AWcBQgEuAUMBLgFkATIBZAEyAWQBMgFBASoBywFGAf8BfwF7
        AW8WAAH/AX8BewFvAd8BfwE0AV8BIQEmAUQBLgFDAS4BZwE2Ad8BfwG+AXcBnAFzAZ0BcwGdAXMBnQFz
        AZ0BcwGdAXMBnQFzAZ0BcwF7AW8BvQF3AgAB/wF/Af8BewG9AXcBYgFEASABRAGDAUgBYwFIAWMBSAFj
        AUgBYwFIAQABQAHvAV0B7wFdAQABQAFiAUgBYwFIAWMBSAFjAUgBgwFIASABRAFiAUQBvQF3Af8BewH/
        AX8BnAFzAf4BfwHWAQkBdQEBAbYBAQG2AQEBtgEBAbYBAQGWAQEBlgEBAZYBAQGVAQEBlQEBAZUBAQGV
        AQEBdQEBAXQBAQF0AQEBdAEBAVMBAQEzAQEBMwEBAd4CewFvBAAB/wF/AXsBbwHfAX8BeQFrAUEBLgFj
        ATIBhAE2AYQBNgGEATYBhAE2AWQBNgFAASoBdwFnAd8BfwHeAXsSAAH/AX8BewFvAf8BfwFWAWMBQAEq
        AWMBMgFkATYBYwEyAYcBOgGcAXMBmgFvAZoBbwGaAW8BmgFvAZoBbwGaAW8BmgFvAZoBbwGaAW8BvgF3
        Ab4BewHeAXsCAAHeAXsB/wF7AZwBdwGDAUgBIAFEAYMBSAGDAUgBgwFIAYMBSAFjAUgBIAFEASABRAFj
        AUgBgwFIAYMBSAGDAUgBgwFIASABRAGDAUgBnAF3Af8BewHeAXsCAAGcAXMB/gF/AdcBCQGWAQEBtgEB
        AbYBAQG2AQEBtgEBAbYBAQG2AQEBtgEBAbYBAQGWAQEBlQEBAZUBAQGVAQEBdQEBAXQBAQF0AQEBdAEB
        AVMBAQEzAQEB3gJ7AXMCAAH/AX8BnAFzAd8BfwGaAW8BgwEyAYMBMgGEATYBhAE2AYQBNgGEATYBhAE2
        AYQBNgGDATIBpQE6Ad8BewF8AXMB/wF/DgAB/wF/AXsBbwH/AX8BdgFnAWEBLgGDATIBhAE2AYQBNgGE
        ATYBhAE2AWIBLgFhAS4BYQEuAWEBLgFhAS4BYQEuAWEBLgFhAS4BYQEuAUABKgGnAT4B3wF7AXwBbwQA
        Ad4BewH/AX8B3gF3AYMBTAEgAUgBgwFMAYMBTAGDAUwBgwFMAYMBTAGDAUwBgwFMAYMBTAGDAUwBgwFM
        ASABSAGDAUwB3gF3Af8BfwHeAXsEAAGcAXMB/gF/AdcBCQGWAQEBtwEBAbcBAQG3AQEBtwEBAbcBAQG2
        AQEBtgEBAbYBAQG2AQEBtgEBAZUBAQGVAQEBlQEBAXUBAQF0AQEBdAEBAVMBAQFTAQEB3gF7AZsBcwIA
        AZwBcwH/AX8BmgFvAYIBMgGDATYBpAE6AaQBOgGkAToBpAE6AaQBOgGkAToBpAE6AaQBOgGAAS4BLwFT
        Af8BfwGcAXMOAAGcAXMB3wF/AXYBZwFhAS4BgwE2AaQBOgGkAToBpAE6AaQBOgGkAToBpAE6AaQBOgGk
        AToBpAE6AaQBOgGkAToBpAE6AaQBOgGkAToBhAE2AYMBMgG+AXcBfAFvBgAB3gF7Af8BfwG9AXcBYgFM
        ASABSAGDAVABgwFQAYMBUAGDAVABgwFQAYMBUAGDAVABgwFQASABSAFiAUwBvQF3Af8BfwHeAXsGAAGc
        AXMB/gF/AfcBCQG2AQEB1wEBAdcBAQHXAQEB1wEBAdcBAQHXAQEBtwEBAbYBAQG2AQEBtgEBAbYBAQGW
        AQEBlQEBAZUBAQF1AQEBdAEBAVQBAQFTAQEB3gF7AZsBcwHeAXsB3wF/AbsBcwGiATYBogE2AcQBOgHE
        AToBxAE6AcQBOgGiATYBogE2AcQBOgHEAToBxAE6AaQBOgGiATYBvAF3Ad8BfwH/AX8KAAH/AX8B3wF/
        AbsBcwGBATIBowE2AcQBOgHEAToBxAE6AcQBOgHEAToBxAE6AcQBOgHEAToBxAE6AcQBOgHEAToBxAE6
        AcQBOgHEAToBxAE6AaQBOgGkAToB3gF7AXwBbwgAAd4BewH/AXsB3gF7AeYBVAFBAVABgwFQAYMBUAGD
        AVABgwFQAYMBUAGDAVABQQFQAeYBVAHeAXsB/wF7Ad4BewgAAZwBcwH/AX8B+AEJAbcBAQHXAQEB1wEB
        AdcBAQHXAQEB1wEBAdcBAQHXAQEB1wEBAbcBAQG2AQEBtgEBAbYBAQGWAQEBlQEBAZUBAQF1AQEBVAEB
        AVMBAQHeAXsBnAFzAZwBcwHfAX8B5gE+AcIBNgHEAT4BxAE+AcQBPgHEAT4BoAEyAVABVwFQAVsBoQE2
        AcQBPgHEAT4BxAE+AcIBNgHoAUYB/wF/AXwBbwH/AX8IAAGcAXMB/wF/AQkBSwGhATYBxAE+AcQBPgHE
        AT4BxAE+AcQBPgHEAT4BxAE+AcQBPgHEAT4BxAE+AcQBPgHEAT4BxAE+AcQBPgHEAT4BxAE+AcQBOgHE
        AToB3gF7AXwBcwgAAf8BfwG9AXMB/wF/Ac4BYQEgAVABgwFUAYMBVAGDAVQBgwFUAYMBVAGDAVQBIAFQ
        Ac4BYQH/AX8BvQFzAf8BfwgAAZwBcwH/AX8B+AEJAbcBAQH4AQEB2AEBAdgBAQHYAQEB1wEBAbcBAQG3
        AQEBtwEBAbcBAQGWAQEBlgEBAZYBAQF1AQEBdQEBAZUBAQGVAQEBdAEBAVQBAQHeAX8BnAFzAZwBcwH/
        AX8BBgFDAcEBNgHlAT4B5AE+AeQBPgHAATYBLAFTAf8BfwH/AX8B5AE+AeMBOgHkAT4B5AE+AeQBPgHA
        ATYBcQFfAf8BfwGcAXMIAAGcAXMB/wF/AeUBQgHiAToB5QE+AeQBPgHkAT4B5AE+AeQBPgHkAT4B5AE+
        AeQBPgHkAT4B5AE+AeQBPgHkAT4B5AE+AeQBPgHkAT4B5QE+AeQBPgHkAT4B3wF7AZwBcwYAAf8BfwG9
        AXMB/wF/Aa0BYQEAAVABYwFUAYMBVAGDAVQBgwFUAYMBVAGDAVQBgwFUAWMBVAEAAVABrQFhAf8BfwG9
        AXMB/wF/BgABnAFzAf8BfwEYAQoB2AEBAfgBAQHYAQEBGQEKATkBEgE5AQ4BOQEOARgBDgEYAQ4BGAEO
        ARgBDgH4AQ0B9wENAfcBEQHWAQkBVQEBAXUBAQF0AQEBVAEBAf4BfwGcAXMB/wF/Af8BfwHdAXcB5AE+
        AeIBOgEEAUMBwAE2ASsBUwH/AX8BvgF3Ad8BfwG4AW8B4AE2AeQBQgEEAUMBBAFDAeQBPgHhAToBugFz
        Af8BfwG9AXcGAAHeAXsB/wF/AXIBYwHAATYBBAFDAQQBQwEEAUMBBAFDAQQBQwEEAUMBBAFDAQQBQwEE
        AUMBBAFDAQQBQwEEAUMBBAFDAQQBQwEEAUMBBAFDAeMBPgHkAT4B3wF/AZwBcwQAAf8BfwHeAXcB/wF/
        Ac0BZQEAAVABgwFYAYMBWAGDAVgBgwFYAYMBWAGDAVgBgwFYAYMBWAGDAVgBgwFYAQABUAHNAWUB/wF/
        Ab4BdwH/AX8EAAGcAXMB/wF/ARkBCgHYAQEB2AEBAdsBOgH9AX8B3QF/Ad0BfwHdAX8B3QF/Ad0BfwHd
        AX8B/QF/Af4BfwH/AX8B/wF/Af4BfwGZATYBVAEBAXQBAQF0AQEB/gF/AZwBcwIAAd4BewH/AX8B3QF3
        AeMBQgHAATYBKgFTAf8BfwHeAXsB/wF/Ab0BdwH/AX8BTQFXAeABOgEEAUMBBAFDAQQBQwHiAT4BBQFH
        Af8BfwHfAX8B/wF/BgABvQF3Af8BfwEqAU8B4AE6AQQBQwEEAUMBBAFDAQQBQwEEAUMBBAFDAQQBQwEE
        AUMBBAFDAQQBQwEEAUMBBAFDAQQBQwEEAUMBBAFDAeMBQgHjAUIB3wF/AZwBcwIAAf8BfwG9AXMB/wF/
        Ac4BaQEAAVQBgwFYAYMBXAGDAVwBgwFcAYMBXAFiAVgBYgFYAYMBXAGDAVwBgwFcAYMBXAGDAVgBAAFU
        Ac4BaQH/AX8BvQFzAf8BfwIAAZwBcwH/AX8BGQEKAdgBAQH5AQEBvQF3Ad0BfwG9AXcBvQF3Ab0BdwG9
        AXcBvQF3Ab0BdwH+AX8BuQFGAZQBHQE2AS4B/gF/AZ0BcwFUAQEBdAEBAXQBAQH/AX8BnAFzBAAB3gF7
        Af8BfwHdAXsBlgFrAf8BfwHfAX8B/wF/AgAB/wF/Ad4BewH/AX8BAwFDAQIBQwEEAUcBBAFHAQQBRwHg
        AT4BKgFTAf8BfwG9AXcB/wF/BAAB/wF/Ad8BewH/AX8BKQFTAeABOgEDAUMBAwFHAQMBRwEEAUMBAwFD
        AeEBPgHhAT4B4QE+AeEBPgHhAT4B4QE+AeEBPgHhAT4B4QE+AeABPgHhAT4B/wF/AZwBcwH/AX8BvQF3
        Af8BfwGsAWkBAAFYAYMBXAGDAVwBgwFcAYMBXAGDAVwBIAFcAcYBYAHmAWABIAFcAYMBXAGDAVwBgwFc
        AYMBXAGDAVwBAAFYAawBaQH/AX8BvQF3Af8BfwGcAXMB/wF/ATkBCgH5AQEB+QEBAb0BdwHeAX8BvQF3
        Ab0BdwG9AXcBvQF3Ab0BdwG9AXcB/wF/AfYBGQHRAQABMgEBAf4BfwGdAXMBdQEBAXUBAQF0AQEB/wF/
        AZwBcwYAAd4BewG+AXsB/wF/Ab0BdwH/AX8GAAHeAXsB/wF/AbgBbwHgAT4BAwFHAQMBRwEDAUcBAwFH
        AeABPgFMAVcB/wF/Ab0BdwH/AX8EAAH/AX8B/wF/Af8BfwEpAVMB4AE+AQMBRwEDAUcBAgFHAQMBRwFM
        AVsBSwFXAUsBVwFLAVcBSwFXAUsBVwFLAVcBSwFXAUsBVwFKAVMBlQFrAf8BfwG9AXcBnAFzAf8BfwEP
        AW4BAAFYAYMBYAGDAWABgwFgAYMBYAGDAWABIAFcAYMBYAH/AX8B/wF/AWIBYAEgAVwBgwFgAYMBYAGD
        AWABgwFgAYMBYAEAAVgBDwFuAf8BfwGcAXMBnAFzAf8BfwE5AQoB+QEBAfkBAQG+AXcB3gF/Ad4BewHe
        AXsB3gF7Ad4BewHeAXsB3gF7Af8BfwFYASIBVAEBAZUBAQH/AX8BvQFzAXUBAQF1AQEBdAEBAf8BfwGc
        AXMYAAG9AXcB/wF/AW4BXwHgAT4BAwFHAQMBRwEDAUcBAwFHAeABPgFvAV8B/wF/AZwBcwYAAf8BfwH/
        AX8B/wF/AScBUwHgAUIBAwFLAQEBRwEmAU8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/
        AX8B/wF/Af8BfwG9AXcCAAGcAXMB/wF/AUEBYAFBAWABgwFgAYMBYAGDAWABgwFgASABYAGDAWABvQF7
        Af8BfwH/AX8BvQF7AYMBYAEgAWABgwFgAYMBYAGDAWABgwFgAUEBYAFBAWAB/wF/AZwBcwGcAXMB/wF/
        AToBCgH5AQEBGgECAd4BdwH+AX8B3gF7Ad4BewHeAXsB3gF7Ad4BewHeAXsB/wF/AXoBIgGXAQEB1wEB
        Af8BfwG+AXMBdQEBAXUBAQF0AQEB/wF/AZwBcxoAAb0BdwH/AX8BSAFTAQABQwEDAUsBAwFLAQMBSwEA
        AUMBAAFHAf8BfwGcAXMIAAH/AX8B/wF/Af8BfwFHAVMBAAFDAQEBRwEkAU8B/wF/AZwBcwH/AX8B3gF7
        Ad4BewHeAXsB3gF7Ad4BewHeAXsB3gF7Af8BfwQAAb0BdwH/AX8B7wFtAQABYAGDAWQBgwFkAYMBZAEg
        AWABgwFkAd4BfwH/AX8B3gF7Ad4BewH/AX8B3gF/AYMBZAEgAWABgwFkAYMBZAGDAWQBAAFgAQ8BbgH/
        AX8BvQF3AZwBcwH/AX8BOgEGAfkBAQEaAQIB3gF3Af8BfwHeAXsB3gF7Ad4BewHeAXsB3gF7Ad4BewH/
        AX8BmwEaAbgBAQG3AQEB/wF/Ab4BdwF1AQEBdQEBAXQBAQH/AX8BnAFzGgAB/wF/Af8BfwH/AX8BIgFL
        AQABRwEiAUsBAAFHAQABRwG2AW8B/wF/Ad4BewoAAf8BfwH/AX8B/wF/AUYBUwHgAT4BJQFTAf8BfwG9
        AXcWAAH/AX8B3gF7Af8BfwGMAW0BAAFkAWIBZAEgAWQBYgFkAd4BfwH/AX8B3gF7BAAB3gF7Af8BfwG9
        AX8BYQFkASABZAFiAWQBAAFkAYwBbQH/AX8B3gF7Af8BfwG9AXcB/wF/AXoBGgHZAQEBGgECAd8BewH/
        AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/AR0BQwE6AQ4BegEmAf8BfwG+AXMBdQEBATQBAQGV
        AQEB/wF/AZwBcxwAAd4BewH/AX8B/AF7ASIBTwEAAUMBSAFXAf0BewH/AX8BvQF3DgAB/wF/Af8BfwH/
        AX8BkQFnAdoBdwH/AX8B/wF/GAAB/wF/Ad4BewH/AX8BzgFxAQABZAGEAWgBvQF/Af8BfwHeAXsIAAHe
        AXsB/wF/Ab0BfwGkAWgBAAFkAc4BcQH/AX8B3gF7Af8BfwIAAf8BfwH/AX8B3wF3AToBBgHZAQEB3wF3
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Ab4BcwEUAQEBdAEB
        AX0BZwH/AX8B3gF7HgAB3gF7Af8BfwH/AX8B2wF3Af8BfwH/AX8B3gF7EgAB/wF/Ad4BewH/AX8B/wF/
        Ad4BexwAAf8BfwHeAXsB/wF/Ab0BfwH/AX8B/wF/Ad4BewwAAd4BewH/AX8B/wF/Ab0BfwH/AX8B3gF7
        Af8BfwYAAd4BewH/AX8B/wF/Ab8BbwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/
        AX8B/wF/Af8BfwH/AX8B/wF/Ab4BbwH/AX8B/wF/Ab0BdyIAAd4BewHeAXsB/wF/Ab0BdxgAAd4BewHe
        AXsB3gF7IgABvQF3Ab0BdwG9AXcB3gF7EAAB3gF7Ab0BdwG9AXcBvQF3DAAB/wF/Ab0BdwG9AXcBvQF3
        Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3
        Ad4BeyYAAf8BfwH/AX8B/wF/OAABQgFNAT4HAAE+AwABKAMAAWADAAEwAwABAQEAAQEFAAFAAQIWAAP/
        /wAiAAHgAf8BBwHAAQABAwH/AY8E/wHAAX4BAwGAAQABAQH/AYcC/wEfAf8BgAE8AQEDAAH+AQMB/wH+
        AQ8B/wEAARgEAAH8AQEB/wH8AQcB/wYAAfgBAQH/AfgBBwH/BgAB8AEAAf8B8AEAAQMGAAHgAQAB/wHg
        AQABAQYAAcABAAF/AcACAAGAAQABAQMAAYABAAE/AYACAAHAAQABAwMAAYABAAE/AYACAAHgAQABBwUA
        AR8DAAHwAQABDwUAAQ8DAAHwAQABDwUAAQ8DAAHgAQABBwUAAQcDAAHAAQABAwMAAYABAAEDAYACAAGA
        AQABAQMAAcABQAEBAYAIAALgAQABwAgAAf8B8AEAAeABAAEBBgAB/wH4AQAB8AEAAQMGAAH/AfgBAAH4
        AQcB/wEAARgEAAH/AfwBAQH8AQcB/wGAATwBAQMAAf8B/gEDAf4BDwH/AcABfgEDAYABAAEBAv8BDwH/
        AR8B/wHwAf8BDwHAAQABAwL/AY8D/ws=
</value>
  </data>
  <metadata name="ErrorManager.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="ImageList16x16.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>144, 17</value>
  </metadata>
  <data name="ImageList16x16.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        ********************************************************************************
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAAAI
        BgAAAk1TRnQBSQFMAgEBAgEAAVQBAgFUAQIBEAEAARABAAT/AREBAAj/AUIBTQE2BwABNgMAASgDAAFA
        AwABEAMAAQEBAAEQBgABCKIAAd4BewGcAXMBvQF3FAABvQF3AZwBcwG9AXdeAAH/AX8BnQFzATYBYwG9
        AXcBnAFzEAABvQF3Ab4BewE2AV8BvQF3Af8Bf1IAAf8BfwH/AX8B/wF/Af8BfwH/AX8B3gF7AZsBbwHA
        AR0BZwE2Ad8BewGcAXMMAAG9AXcB3wF/AYoBPgHAARkBWAFnAb0BdwH/AX8B/wF/Af8BfwH/AX8B/wF/
        Af8Bf0IAAf8BfwF8AW8BvgF3Ab4BdwG+AXcBvgF3Ab4BdwHeAXsBnAFzAUIBKgEhASoBiQE+Ad8BfwGc
        AXMIAAG9AXcB3wF/AasBQgEhASYBIQEqAXkBawHeAXsBvgF3Ab4BdwG+AXcBvgF3Ab4BdwF8AXMB/wF/
        QAABnAFzAVYBYwGmAToBqAFCAagBQgGoAUIBqAFCAakBQgGoAT4BhAEyAYQBNgFBAS4ByQFCAd8BfwGd
        AXMEAAG9AXcB3wF/AesBRgFAASoBhAE2AWMBMgGoAT4BqQFCAagBQgGoAUIBqAFCAagBQgGGAToBMwFf
        AXwBc0AAAZwBcwENAU8BYAEuAYMBNgGDATIBgwEyAYMBMgGDATYBowE2AaQBOgGkAToBpAE6AYEBLgHp
        AUYB3wF/Ad4BewH/AX8B3wF/AQwBSwGAAS4BpAE6AaQBOgGkAToBowE2AYMBMgGDATIBgwEyAYMBMgGD
        ATYBYAEuAeoBRgGcAXNAAAGcAXMBLgFTAaEBNgHFAT4BxAE+AcQBPgHEAT4BxAE+AcQBPgHEAT4BxAE+
        AcQBPgHEAT4BoAEuAVABVwGcAXMBnAJzAV8BoAEuAcQBOgHEAT4BxAE+AcQBPgHEAT4BxAE+AcQBPgHE
        AT4BxAE+AcUBPgGiATYBCwFPAZwBc0AAAZwBcwFOAVcB4QE6AeUBPgHkAT4B5AE+AeQBPgHkAT4B5AE+
        AeQBPgHkAT4B5AE+AeUBPgHAATYBLAFPAZwBcwGcAXMBTwFXAcABNgHkAT4B5AE+AeQBPgHkAT4B5AE+
        AeQBPgHkAT4B5AE+AeQBPgHlAT4B4gE6ASwBTwGcAXNAAAGcAXMBTQFXAeABOgEEAUMBBAFDAQQBQwEE
        AUMBBAFDAQQBQwEEAUMBBAFDAQQBQwHiAT4B4gE+Ad0BewG9AXcB3gF7Af8BfwEFAUMB4QE+AQQBQwEE
        AUMBBAFDAQQBQwEEAUMBBAFDAQQBQwEEAUMBBAFDAeEBOgEqAVMBnAFzQAABnAFzAXEBXwHgAToBAQFD
        AQEBQwEBAUMBAQFDAQEBQwECAUMBAwFHAQMBRwHhAUIBAQFDAdwBdwG9AXcEAAG9AXcB/gF7AQMBRwHh
        AT4BAwFHAQMBRwECAUMBAQFDAQEBQwEBAUMBAQFDAQEBQwHgAToBTQFbAZwBc0AAAf8BfwH/AX8B3QF7
        Af4BewH+AXsB/gF7Af4BewH/AX8B2gFzAQEBRwEBAUMBAQFHAdwBewG9AXcIAAG9AXcB/gF/AQMBRwEA
        AUMBAAFDAbcBbwH/AX8B/gF7Af4BewH+AXsB/gF7Af0BewH/AX8B3gF7QgAB/wF/Af8BfwH/AX8B/wF/
        Af8BfwH/AX8BvQF3AdwBewHgAT4BAAFHAdsBdwG9AXcMAAG9AXcB/gF/AQEBSwHgAToB2QFzAb0BdwH/
        AX8B/wF/Af8BfwH/AX8B/wF/Af8Bf1AAAf8BfwH/AX8BawFfAdoBdwG9AXcQAAG9AXcB/QF7AWsBXwH/
        AX8B/wF/XgAB3gF7Ab0BdwG9AXcUAAG9AXcBvQF3Ab0Bd9AAAUIBTQE+BwABPgMAASgDAAFAAwABEAMA
        AQEBAAEBBQABgBcAA/8BAAT/BAAB/wEfAfgB/wQAAf4BDwHwAX8EAAHAAQcB4AEBBQABAwHABgABAQGA
        JgABAQGABgABAwHABQABgAEHAeABAQQAAf4BDwHwAX8EAAH/AR8B+AH/BAAE/wQACw==
</value>
  </data>
  <metadata name="ChainNameColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <data name="LabelControl5.Text" xml:space="preserve">
    <value>All available items are displayed in the left list. Currently selected items are displayed in the right list. To select one or more available items, highlight them in the left list (multiple items can be highlighted using the SHIFT or CTRL keys) then click the 'Add' button.</value>
  </data>
  <metadata name="DataGridViewTextBoxColumn1.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
</root>