﻿Imports System.Security.Principal

Public Class SubformContractTasks

    Private WithEvents DataObject As Contract
    Private FormLoadComplete = False

#Region "Properties"

    Public ReadOnly Property ContractTitleText As String
        Get
            Return String.Concat("Contract " & DataObject.ContractNumber, If(DataObject.isReplacement, String.Format(" (Replaces {0})", DataObject.ClonedContractNumber), ""))
        End Get
    End Property

    Public ReadOnly Property ContractTitleColor As Color
        Get
            If Not DataObject.Signed Then
                ' This is un unsigned contract.
                Return Color.Goldenrod
            ElseIf DataObject.Cancelled Then
                ' This is a cancelled contract.
                Return Color.IndianRed
            ElseIf DataObject.LastWeek < Date.Now Then
                ' This is a history contract.
                Return Color.Silver
            Else
                ' This is an executory contract.
                Return Color.SteelBlue
            End If
        End Get
    End Property

    Public ReadOnly Property RentalContract As Boolean
        Get
            If IsNothing(DataObject) Then
                Return False
            Else
                If DataObject.Type = Contract.ContractType.Rental Or
                    DataObject.Type = Contract.ContractType.Distribution Or
                    DataObject.Type = Contract.ContractType.InstallationOnly Then
                    Return True
                Else
                    Return False
                End If
            End If
        End Get
    End Property

    Public ReadOnly Property LabelInfo1Text As String
        Get
            If DataObject.Type = Contract.ContractType.Rental Or
                DataObject.Type = Contract.ContractType.Distribution Or
                    DataObject.Type = Contract.ContractType.InstallationOnly Then
                Return "Brands:"
            ElseIf DataObject.Type = Contract.ContractType.Research Then
                Return "Categories:"
            Else
                Return String.Empty
            End If
        End Get
    End Property

    Public ReadOnly Property HyperlinkInfo1ValueText As String
        Get
            If DataObject.Type = Contract.ContractType.Rental Or
                DataObject.Type = Contract.ContractType.Distribution Or
                DataObject.Type = Contract.ContractType.InstallationOnly Then
                Return DataObject.Brands
            ElseIf DataObject.Type = Contract.ContractType.Research Then
                Return DataObject.ResearchCategories
            Else
                Return String.Empty
            End If
        End Get
    End Property

    Public ReadOnly Property BillingInstructionWarning As String
        Get
            If DataObject.BillingInstructionCount > 0 AndAlso DataObject.BillingInstructionDifference <> 0 Then
                Return "ATTENTION: The sum of all billing instructions is different to the total contract value!"
            Else
                Return String.Empty
            End If
        End Get
    End Property

    Public ReadOnly Property BillingInstructionIncomplete As Boolean
        Get
            If LabelBillingInstructionWarning.Text.Length > 0 Then
                Return True
            Else : Return False
            End If
        End Get
    End Property

#End Region

#Region "Public Methods"

    Public Sub New(ByVal ContractObject As Contract)

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        DataObject = ContractObject

    End Sub

    Public Sub New(ByVal ContractRow As DataSetContract.ContractRow)

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        DataObject = New Contract(ContractRow, My.Settings.DBConnection)

    End Sub

    Public Sub RefreshControls(Container As Control)

        For Each ControlObject As Control In Container.Controls
            ' Check if this is a panel containing other controls.
            If TypeOf ControlObject Is Panel Then
                ' This is a panel. Refresh its child controls.
                RefreshControls(ControlObject)
                ' Check if this control is data bound.
            ElseIf ControlObject.DataBindings.Count > 0 Then
                ' This control has data bound to at least one of its properties.
                For Each DataBinding As Binding In ControlObject.DataBindings
                    DataBinding.ReadValue()
                Next
            End If
        Next

    End Sub

    Public Sub RefreshWarnings()
        LabelBillingInstructionWarning.DataBindings("Text").ReadValue()
        LabelBillingInstructionWarning.DataBindings("Visible").ReadValue()
    End Sub

#End Region

#Region "Event Handlers"

    Private Sub SubformContractTasks_Load(sender As Object, e As System.EventArgs) Handles Me.Load

        ' Get the parent form.
        Dim ParentForm As BaseForm = CType(TopLevelControl, BaseForm)

        ' Change the cursor to a wait cursor.
        Dim OriginalCursor As Cursor = ParentForm.Cursor
        ParentForm.Cursor = Cursors.WaitCursor

        ' Sometimes the LabelInfo1Value label will need to be a hyperlink, and sometimes it won't.
        If DataObject.Type = Contract.ContractType.Rental Or
        DataObject.Type = Contract.ContractType.InstallationOnly Then
            ' The current contract is a Research contract. Enable the hyperlink.
            LiquidShell.LiquidAgent.EnableHyperlink(HyperlinkInfo1Value, False)
        End If

        AddDataBindings()

        ' Initialize controls.
        CheckEditRollForward.Checked = DataObject.RollForwardContract
        CheckEditAddedValue.Checked = DataObject.AddedValueContract

        ' Change the cursor back to what it was before.
        ParentForm.Cursor = OriginalCursor

        ' Setup the "Cancel Contract" hyperlink as a "revive contract" link if the contract is cancelled.
        InitializeReviveHyperlink()

        ' Update the appearance of the "Approve contract" hyperlink.
        UpdateApprovedHyperlinkAppearance()

        'hide cloning,deleting and cancelling if the user is ops_modifier
        If My.User.IsInRole("ops_contractmodifier") Then
            HyperlinkClone.Visible = False
            HyperlinkCancel.Visible = False
            HyperlinkDelete.Visible = False
        End If

        ' Indicate that the form has finished loading.
        FormLoadComplete = True

    End Sub

    Private Sub ButtonBack_Click(sender As System.Object, e As System.EventArgs) Handles ButtonBack.Click

        ' Get the parent form.
        Dim ParentForm As BaseForm = CType(TopLevelControl, BaseForm)

        ' Change the cursor to a wait cursor.
        Dim OriginalCursor As Cursor = ParentForm.Cursor
        ParentForm.Cursor = Cursors.WaitCursor

        ' Return to the Start form.
        RevertToParentSubform()

        ' Change the cursor back to what it was before.
        ParentForm.Cursor = OriginalCursor

    End Sub

    Private Sub HyperlinkPrint_Click(sender As System.Object, e As System.EventArgs) Handles HyperlinkPrint.Click

        ' Get the current date and time to check how old this contract is.
        Dim CurrentTime As Date = Settings.GetServerTime(My.Settings.DBConnection, CType(TopLevelControl, BaseForm))

        ' Check the age of this contract.
        If DataObject.FirstWeek > CurrentTime Then
            ' This is not a history contract, or one that is already running.
            If DataObject.Value > 0 Then
                ' This contract has a value greater than zero, it needs billing instructions.
                ' Check if the billing instructons have been done before printing.
                If DataObject.BillingInstructionCount = 0 Or DataObject.BillingInstructionDifference <> 0 Then
                    CType(TopLevelControl, BaseForm).ShowMessage("Billing instructions need to be completed before printing.", "Billing Instructions Not Complete")
                    Exit Sub
                End If
            End If
        End If

        ' Preview the contract.
        FormContractPrintPrompt.Print _
        (DataObject,
        "Johannesburg - Tel: ************, Fax: ************  |  " _
        & "Cape Town - Tel: ************ |  " _
        & "Durban - Tel: ************, Fax: ************")

    End Sub

    Private Sub HyperlinkDelete_Click(sender As System.Object, e As System.EventArgs) Handles HyperlinkDelete.Click

        ' Before we begin, remove all bindings from all controls.
        RemoveDataBindings(Me)

        ' Try to delete this row.
        If DataObject.Delete(CType(TopLevelControl, BaseForm), My.Settings.DBConnection) Then
            ' Rowdeletion was successful.
            RevertToParentSubform()
        Else
            ' Row deletion was unsuccessful. Restore data bindings.
            AddDataBindings()
        End If

    End Sub

    Private Sub HyperlinkModifyContract_Click(sender As System.Object, e As System.EventArgs) Handles HyperlinkModifyContract.Click

        ' Create table adapters to populate related tables.
        Dim AccountManagerAdapter As New DataSetContractTableAdapters.AccountManagerTableAdapter
        Dim BurstAdapter As New DataSetContractTableAdapters.BurstTableAdapter
        Dim BurstLoadingFeeAdapter As New DataSetContractTableAdapters.BurstLoadingFeeTableAdapter
        Dim BurstCategoryAdapter As New DataSetContractTableAdapters.BurstCategoryTableAdapter
        Dim BurstInstallationDayAdapter As New DataSetContractTableAdapters.BurstInstallationDayTableAdapter
        Dim BurstPcaStatusTableAdapter As New DataSetContractTableAdapters.BurstPcaStatusTableAdapter
        Dim PurchaseOrderNumberAdapter As New DataSetContractTableAdapters.PurchaseOrderNumberTableAdapter
        Dim ProductionAdapter As New DataSetContractTableAdapters.ContractInventoryQtyTableAdapter
        Dim ContractMiscellaneousChargeAdapter As New DataSetContractTableAdapters.ContractMiscellaneousChargeTableAdapter
        Dim MediaCostAdapter As New DataSetContractTableAdapters.MediaCostTableAdapter
        Dim StorePoolAdapter As New DataSetContractTableAdapters.StorePoolTableAdapter
        Dim StoreListAdapter As New DataSetContractTableAdapters.StoreListTableAdapter
        Dim ContractInvoiceAdapter As New DataSetContractTableAdapters.ContractInvoicesTableAdapter
        Dim ContractCostEstimateAdapter As New DataSetContractTableAdapters.ContractCostEstimatesTableAdapter

        ' Set the sql connection of the new adapters.
        Dim SqlCon As New SqlClient.SqlConnection(My.Settings.DBConnection)
        AccountManagerAdapter.Connection = SqlCon
        BurstAdapter.Connection = SqlCon ' New SqlClient.SqlConnection("Data Source=primenovasql\Live;Initial Catalog=NovaDB;User ID=NovaApp;Password=******************;Connect Timeout=0")
        BurstLoadingFeeAdapter.Connection = SqlCon
        BurstCategoryAdapter.Connection = SqlCon
        BurstInstallationDayAdapter.Connection = SqlCon
        BurstPcaStatusTableAdapter.Connection = SqlCon
        PurchaseOrderNumberAdapter.Connection = SqlCon
        ProductionAdapter.Connection = SqlCon
        ContractMiscellaneousChargeAdapter.Connection = SqlCon
        MediaCostAdapter.Connection = SqlCon
        StorePoolAdapter.Connection = SqlCon
        StoreListAdapter.Connection = SqlCon
        ContractCostEstimateAdapter.Connection = SqlCon
        ContractInvoiceAdapter.Connection = SqlCon

        ' A string to hold possible errors.
        Dim ErrorMessage As String = String.Empty

        ' Get the current data set.
        Dim ContractDataSet As DataSet = DataObject.Row.Table.DataSet

        ' Populate tables needed for adding new rows and editing existing rows (parent tables).
        Try
            AccountManagerAdapter.Fill(ContractDataSet.Tables("AccountManager"))
        Catch ex As Exception
            ' Remember the error for later.
            ErrorMessage = LiquidShell.LiquidAgent.GetErrorMessage(ex)
        Finally
            AccountManagerAdapter.Dispose()
        End Try

        ' Populate tables needed for editing existing rows (child tables).
        Try
            BurstAdapter.Fill(ContractDataSet.Tables("Burst"), DataObject.ContractID, My.User.CurrentPrincipal.Identity.Name)
            BurstLoadingFeeAdapter.Fill(ContractDataSet.Tables("BurstLoadingFee"), DataObject.ContractID)
            BurstCategoryAdapter.Fill(ContractDataSet.Tables("BurstCategory"), DataObject.ContractID)
            BurstInstallationDayAdapter.Fill(ContractDataSet.Tables("BurstInstallationDay"), DataObject.ContractID)
            BurstPcaStatusTableAdapter.Fill(ContractDataSet.Tables("BurstPcaStatus"), DataObject.ContractID)
            PurchaseOrderNumberAdapter.Fill(ContractDataSet.Tables("PurchaseOrderNumber"), DataObject.ContractID)
            ProductionAdapter.Fill(ContractDataSet.Tables("ContractInventoryQty"), DataObject.ContractID)
            ContractMiscellaneousChargeAdapter.Fill(ContractDataSet.Tables("ContractMiscellaneousCharge"), DataObject.ContractID)
            MediaCostAdapter.Fill(ContractDataSet.Tables("MediaCost"), DataObject.ContractID)
            StorePoolAdapter.Fill(ContractDataSet.Tables("StorePool"), DataObject.ContractID)
            StoreListAdapter.Fill(ContractDataSet.Tables("StoreList"), DataObject.ContractID)
            ContractInvoiceAdapter.Fill(ContractDataSet.Tables("ContractInvoices"), DataObject.ContractID)
            ContractCostEstimateAdapter.Fill(ContractDataSet.Tables("ContractCostEstimates"), DataObject.ContractID)
        Catch ex As Exception
            ' Remember the error for later.
            ErrorMessage = LiquidShell.LiquidAgent.GetErrorMessage(ex)
        Finally
            BurstAdapter.Dispose()
            BurstLoadingFeeAdapter.Dispose()
            BurstCategoryAdapter.Dispose()
            BurstInstallationDayAdapter.Dispose()
            BurstPcaStatusTableAdapter.Dispose()
            PurchaseOrderNumberAdapter.Dispose()
            ProductionAdapter.Dispose()
            ContractMiscellaneousChargeAdapter.Dispose()
            MediaCostAdapter.Dispose()
            StorePoolAdapter.Dispose()
            StoreListAdapter.Dispose()
            ContractInvoiceAdapter.Dispose()
            ContractCostEstimateAdapter.Dispose()
            SqlCon.Dispose()
        End Try

        ' If errors occured while fetching data, display error and stop.
        If String.IsNullOrEmpty(ErrorMessage) = False Then
            CType(TopLevelControl, BaseForm).ShowMessage _
            ("Rats!  Some data failed to load correctly." & vbCrLf & vbCrLf _
            & ErrorMessage, "Data Load Error", MessageBoxIcon.Error)
            Exit Sub
        End If

        ' Create a binding source to be used with the contract subform.
        Dim ContractBindingSource As New BindingSource(ContractDataSet, "Contract")

        ' Open the form to edit the item.
        AddChild _
        (New SubformContract(New OldContract(ContractBindingSource, False, CType(TopLevelControl, BaseForm), My.Settings.DBConnection)))

    End Sub

    Private Sub HyperlinkProjectName_Click(sender As System.Object, e As System.EventArgs) Handles HyperlinkProjectName.Click

        ' Prevent changes to the contract if it has already been signed.
        If DataObject.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get the current value of the control's data bound property.
        Dim CurrentValue As String = CurrentControl.Text.Replace("&&", "&")
        If String.Compare(CurrentValue, "(none specified)") = 0 Then
            CurrentValue = String.Empty
        End If

        ' Get input from the user by displaying an input box.
        Dim Input As String = LiquidAgent.GetInput _
        ("Project Name", CurrentValue, CType(TopLevelControl, BaseForm).Icon)

        ' Update the data object and the label with the selection.
        If IsNothing(Input) = False Then
            If Not String.Compare(Input.Trim, DataObject.ProjectName) = 0 Then
                ' The user's input is different to the current value. Proceed with the update.
                DataObject.ProjectName = Input.Trim
                CurrentControl.DataBindings("Text").ReadValue()
                ' Save the data object.
                DataObject.Save(My.Settings.DBConnection, CType(TopLevelControl, BaseForm))
            End If
        End If

    End Sub


    Private Sub HyperlinkContractClassificationValue_Click(sender As System.Object, e As System.EventArgs) Handles HyperlinkContractClassificationValue.Click

        ' Prevent changes to the contract if it has already been signed.
        If DataObject.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get the current value of the control's data bound property.
        Dim CurrentValue As String = CurrentControl.Text.Replace("&&", "&")
        If String.Compare(CurrentValue, "(none specified)") = 0 Then
            CurrentValue = String.Empty
        End If

        Dim SelectedItems As List(Of DataRow) = LookupContractClassification.SelectRows(My.Settings.DBConnection, False, Nothing)

        ' Update the data object and the label with the selection.
        If SelectedItems.Count > 0 Then
            DataObject.ContractClassificationName = SelectedItems(0)("Classification").ToString()
            CurrentControl.DataBindings("Text").ReadValue()
            ' Reset the error text of the control to an empty string.
            DataObject.Save(My.Settings.DBConnection, CType(TopLevelControl, BaseForm))
            LiquidAgent.ControlValidation(CurrentControl, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkModifySpecialConditions_Click(sender As System.Object, e As System.EventArgs) Handles HyperlinkModifySpecialConditions.Click

        ' Prevent changes to the contract if it has already been signed.
        If DataObject.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If

        ' Get input from the user by displaying an input box.
        Dim Input As String = LiquidAgent.GetInputLarge _
        ("Special Conditions", DataObject.SpecialConditions, CType(TopLevelControl, BaseForm).Icon)

        ' Update the data object and the label with the selection.
        If IsNothing(Input) = False Then
            If Not String.Compare(Input.Trim, DataObject.SpecialConditions) = 0 Then
                ' The user's input is different to the current value. Proceed with the update.
                DataObject.SpecialConditions = Input.Trim
                ' Save the data object.
                DataObject.Save(My.Settings.DBConnection, CType(TopLevelControl, BaseForm))
            End If
        End If

    End Sub

    Private Sub HyperlinkModifyNotes_Click(sender As System.Object, e As System.EventArgs) Handles HyperlinkModifyNotes.Click

        ' Get input from the user by displaying an input box.
        Dim Input As String = LiquidAgent.GetInputLarge _
        ("Contract Notes", DataObject.Notes, CType(TopLevelControl, BaseForm).Icon)

        ' Update the data object and the label with the selection.
        If IsNothing(Input) = False Then
            If Not String.Compare(Input.Trim, DataObject.Notes) = 0 Then
                ' The user's input is different to the current value. Proceed with the update.
                DataObject.Notes = Input.Trim
                ' Save the data object.
                DataObject.Save(My.Settings.DBConnection, CType(TopLevelControl, BaseForm))
            End If
        End If

    End Sub

    Private Sub HyperlinkCancel_Click(sender As System.Object, e As System.EventArgs) Handles HyperlinkCancel.Click

        ' Revive the contract if it is currently cancelled.
        If DataObject.Cancelled Then
            If DataObject.Revive(CType(TopLevelControl, BaseForm), My.Settings.DBConnection) Then
                ButtonBack_Click(sender, e)
            End If
            Exit Sub
        End If

        ' Try to cancel this contract.
        If DataObject.Cancel(CType(TopLevelControl, BaseForm), My.Settings.DBConnection) Then
            ' Cancellation was successful. Refresh controls.
            RefreshControls(Me)
            InitializeReviveHyperlink()
        End If

    End Sub

    Private Sub HyperlinkInfo1Value_Click(sender As System.Object, e As System.EventArgs) Handles HyperlinkInfo1Value.Click

        ' Prevent changes to the contract if it has already been signed.
        If DataObject.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If

        ' The type of contract will determine how this event is handled.
        If DataObject.Type = Contract.ContractType.Research Then
            ' Show the research category manager for this contract.
            DataObject.UpdateConnectionString(My.Settings.DBConnection)
            AddChild(New SubformResearchCategoryList(DataObject))
        End If

    End Sub

    Private Sub HyperlinkAuditLog_Click(sender As System.Object, e As System.EventArgs) Handles HyperlinkAuditLog.Click
        ' Display the audit log for this contract.
        FormAuditLog.ShowLogByContract(DataObject.ContractNumber)
    End Sub

    Private Sub DataObject_Saved() Handles DataObject.Saved
        LabelSaveInfo.DataBindings("Text").ReadValue()
    End Sub

    Private Sub HyperlinkContractDateValue_Click(sender As System.Object, e As System.EventArgs) Handles HyperlinkContractDateValue.Click

        ' Prevent changes to the contract if it has already been signed.
        If DataObject.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get the default date to use in the date selector.
        Dim DefaultDate As Date = DataObject.ContractDate
        If DefaultDate.Year = 1000 Then
            ' The year is 1000, a dummy year, meaning there actually is no contract date for this contract. Use the current date as the default.
            DefaultDate = Now.Date
        End If

        ' Get a date selection from the user.
        Dim SelectedDate As Nullable(Of Date) = DateSelector.GetDate(False, DefaultDate)

        ' Update the data object and the label with the selection.
        If SelectedDate.HasValue Then
            DataObject.ContractDate = SelectedDate.Value
            CurrentControl.DataBindings("Text").ReadValue()
            ' Save the data object.
            DataObject.Save(My.Settings.DBConnection, CType(TopLevelControl, BaseForm))
        End If

        ' Reset the error text of the related control to an empty string.
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)

    End Sub

    Private Sub HyperlinkBilling_Click(sender As System.Object, e As System.EventArgs) Handles HyperlinkBilling.Click

        ' Open the form to edit the item.
        AddChild(New SubformBillingInstructions(DataObject))

    End Sub

    Private Sub HyperlinkDates_Click(sender As Object, e As EventArgs) Handles HyperlinkDates.Click



        ' Open the form to edit the item.
        AddChild(New SubformContractDate(My.Settings.DBConnection, DataObject.ContractID))

    End Sub

    Private Sub CheckEditRollForward_CheckedChanged(sender As Object, e As EventArgs) Handles CheckEditRollForward.CheckedChanged
        If FormLoadComplete Then
            DataObject.RollForwardContract = CheckEditRollForward.Checked
            DataObject.Save(My.Settings.DBConnection, CType(TopLevelControl, BaseForm))
        End If
    End Sub

    Private Sub CheckEditAddedValue_CheckedChanged(sender As Object, e As EventArgs) Handles CheckEditAddedValue.CheckedChanged
        If FormLoadComplete Then
            DataObject.AddedValueContract = CheckEditAddedValue.Checked
            DataObject.Save(My.Settings.DBConnection, CType(TopLevelControl, BaseForm))
        End If
    End Sub


    Private Sub HyperlinkCloneNewContract_Click(sender As Object, e As EventArgs) Handles HyperlinkCloneNewContract.Click

        ' Get a new contract number for the clone. 
        'Lukas changed here to get the new AM code of the person logged in
        'we will have to change both the am code, and the account manager

        Dim AMCode As String = DataObject.ContractNumber.Substring(0, 2)
        Dim NewContractNumber As String = Contract.CreateNewContractNumber(AMCode, My.Settings.DBConnection)

        ' Use the new contract number to clone the current contract.
        Dim Errors As String = DataObject.Clone(NewContractNumber, False)

        ' Stop if there were any errors.
        If String.IsNullOrEmpty(Errors) = False Then
            CType(TopLevelControl, BaseForm).ShowMessage(Errors, "Clone Failed", MessageBoxIcon.Error)
        Else
            CType(ParentSubform, SubformContractStart).OpenContract(NewContractNumber)
        End If
    End Sub

    Private Sub HyperlinkClone_Click(sender As Object, e As EventArgs) Handles HyperlinkClone.Click

        Dim errorMessage As String = String.Empty
        Dim NewContractNumber As String = String.Empty
        Dim Execute As Boolean = False

        SubformReplacementPrompt.AskUserToSelectReasonsForReplacementOfContract(DataObject, errorMessage, NewContractNumber, Execute)

        ' Stop if there were any errors.
        If String.IsNullOrEmpty(errorMessage) = False Then
            CType(TopLevelControl, BaseForm).ShowMessage(errorMessage, "Clone Failed", MessageBoxIcon.Error)
        Else
            If Execute = True Then
                CType(ParentSubform, SubformContractStart).OpenContract(NewContractNumber)
            End If
        End If

    End Sub




    Private Sub HyperlinkApproved_Click(sender As Object, e As EventArgs) Handles HyperlinkApproved.Click

        ' Exit if the contract is not signed.
        If My.User.IsInRole("contract_approver") = False Then
            MessageBox.Show("You do not have permission to change the approval status of contracts.", "Not Permitted")
            Exit Sub
        End If

        If Not (DataObject.FirstWeek.Year > DateTime.Today.Year) Then
            If (DataObject.Signed = True And DataObject.Approved = False And
                ((DataObject.FirstWeek.Month < DateTime.Today.Month) And (DataObject.FirstWeek.Year = DateTime.Today.Year)) Or (DataObject.FirstWeek.Month < DateTime.Today.Month And (DataObject.FirstWeek.Year = DateTime.Today.Year Or DataObject.FirstWeek.Year < DateTime.Today.Year))) Then
                MessageBox.Show("A contract cannot be approved after the month of the start date has elapsed or the start date of the earliest burst has elapsed, as this will effect revenue recognition.", "Not Permitted - Finance Department")
                Exit Sub
            End If
        End If

        ' Exit if the contract is not signed.
        If DataObject.Signed = False Then
            MessageBox.Show("Only signed contracts can have their approval status changed.", "Contract Must First Be Signed")
            Exit Sub
        End If

        ' Toggle the APPROVED status of the contract object, and catch any errors.
        Dim Errors As String = DataObject.ToggleApproveStatus()

        ' Stop if there were errors.
        If String.IsNullOrEmpty(Errors) = False Then
            MessageBox.Show(Errors, "Error While Changing Contract Approval Status")
            Exit Sub
        End If

        ' Update the appearance of the hyperlink.
        UpdateApprovedHyperlinkAppearance()

    End Sub

    Private Sub HyperlinDemoProvider_Click(sender As Object, e As EventArgs) Handles HyperlinDemoProvider.Click
        ' Prevent changes to the contract if it has already been signed.
        If DataObject.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get the current value of the control's data bound property.
        Dim CurrentValue As String = CurrentControl.Text.Replace("&&", "&")
        If String.Compare(CurrentValue, "(none specified)") = 0 Then
            CurrentValue = String.Empty
        End If

        ' Get input from the user by displaying an input box.
        Dim Input As String = LiquidAgent.GetInput _
        ("Demonstration Provider", CurrentValue, CType(TopLevelControl, BaseForm).Icon)

        ' Update the data object and the label with the selection.
        If IsNothing(Input) = False Then
            If Not String.Compare(Input.Trim, DataObject.DemoProvider) = 0 Then
                ' The user's input is different to the current value. Proceed with the update.
                DataObject.DemoProvider = Input.Trim
                CurrentControl.DataBindings("Text").ReadValue()
                ' Save the data object.
                DataObject.Save(My.Settings.DBConnection, CType(TopLevelControl, BaseForm))
            End If
        End If
    End Sub

    Private Sub HyperlinkDemoOwner_Click(sender As Object, e As EventArgs) Handles HyperlinkDemoOwner.Click
        If DataObject.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get the current value of the control's data bound property.
        Dim CurrentValue As String = CurrentControl.Text.Replace("&&", "&")
        If String.Compare(CurrentValue, "(none specified)") = 0 Then
            CurrentValue = String.Empty
        End If

        ' Get input from the user by displaying an input box.
        Dim Input As String = LiquidAgent.GetInput _
        ("Demonstration Owner", CurrentValue, CType(TopLevelControl, BaseForm).Icon)

        ' Update the data object and the label with the selection.
        If IsNothing(Input) = False Then
            If Not String.Compare(Input.Trim, DataObject.DemoOwner) = 0 Then
                ' The user's input is different to the current value. Proceed with the update.
                DataObject.DemoOwner = Input.Trim
                CurrentControl.DataBindings("Text").ReadValue()
                ' Save the data object.
                DataObject.Save(My.Settings.DBConnection, CType(TopLevelControl, BaseForm))
            End If
        End If
    End Sub

#End Region

#Region "Private Methods"

    Private Sub AddDataBindings()

        ' For some weird reason, this hyperlink remains visible while the subform is loading. It
        ' looks ugly. So hide it until we're done adding data bindings.
        HyperlinkInfo1Value.Visible = False

        ' Form elements
        LabelTitle.DataBindings.Add("Text", Me, "ContractTitleText")
        LabelTitle.DataBindings.Add("ForeColor", Me, "ContractTitleColor")
        LabelSignedBy.DataBindings.Add("Visible", DataObject, "Signed")
        LabelSignatureDate.DataBindings.Add("Visible", DataObject, "Signed")
        LabelCancelledBy.DataBindings.Add("Visible", DataObject, "Cancelled")
        LabelCancelDate.DataBindings.Add("Visible", DataObject, "Cancelled")
        LabelCancelledByValue.DataBindings.Add("Visible", DataObject, "Cancelled")
        LabelCancelDateValue.DataBindings.Add("Visible", DataObject, "Cancelled")

        LabelFirstWeek.DataBindings.Add("Visible", Me, "RentalContract")
        LabelMediaServices.DataBindings.Add("Visible", Me, "RentalContract")
        LabelLastWeek.DataBindings.Add("Visible", Me, "RentalContract")
        HyperlinkModifyContract.DataBindings.Add("Visible", Me, "RentalContract")

        LabelInfo1.DataBindings.Add("Text", Me, "LabelInfo1Text")
        HyperlinkInfo1Value.DataBindings.Add("Text", Me, "HyperlinkInfo1ValueText")
        LabelSignedByValue.DataBindings.Add("Visible", DataObject, "Signed")
        LabelSignDateValue.DataBindings.Add("Visible", DataObject, "Signed")
        LabelBillingInstructionWarning.DataBindings.Add("Text", Me, "BillingInstructionWarning")
        LabelBillingInstructionWarning.DataBindings.Add("Visible", Me, "BillingInstructionIncomplete")

        ' Contract data
        LabelContractTypeValue.DataBindings.Add("Text", DataObject, "Type")
        LabelClientNameValue.DataBindings.Add("Text", DataObject, "ClientName")
        HyperlinkProjectName.DataBindings.Add("Text", DataObject, "ProjectName")
        HyperlinDemoProvider.DataBindings.Add("Text", DataObject, "DemoProvider")
        HyperlinkDemoOwner.DataBindings.Add("Text", DataObject, "DemoOwner")
        LabelMediaServicesValue.DataBindings.Add("Text", DataObject, "MediaServices")
        LabelFirstWeekValue.DataBindings.Add("Text", DataObject, "FirstWeekString")
        LabelLastWeekValue.DataBindings.Add("Text", DataObject, "EndDateString")
        HyperlinkContractDateValue.DataBindings.Add("Text", DataObject, "ContractDateString")
        LabelCreatedByValue.DataBindings.Add("Text", DataObject, "CreatedBy")
        LabelCreationDateValue.DataBindings.Add("Text", DataObject, "CreationDateString")
        LabelSignedByValue.DataBindings.Add("Text", DataObject, "SignedBy")
        LabelSignDateValue.DataBindings.Add("Text", DataObject, "SignDateTimeString")
        LabelCancelledByValue.DataBindings.Add("Text", DataObject, "CancelledBy")

        LabelCancelDateValue.DataBindings.Add("Text", DataObject, "CancelDateString")
        LabelSaveInfo.DataBindings.Add("Text", DataObject, "SaveInfo")
        LabelClientAccountManagerValue.DataBindings.Add("Text", DataObject, "ClientAccountManager")
        HyperlinkContractClassificationValue.DataBindings.Add("Text", DataObject, "ContractClassificationName")

        ' Restore the visible property of the problem hyperlink that was hidden before we started
        ' with data binding above.
        HyperlinkInfo1Value.Visible = True

    End Sub

    Private Sub InitializeReviveHyperlink()
        ' If this contract is cancelled, the "Cancel Contract" hyperlink will be changed to a "Revive Contract" hyperlink.
        ' If the user clicks it, the contract will change from being a cancelled contract to being a signed contract but
        ' with its store lists unconfirmed.

        If DataObject.Cancelled Then
            HyperlinkCancel.Text = "Revive the contract"
        Else
            HyperlinkCancel.Text = "Cancel the contract"
        End If

    End Sub

    Private Sub RemoveDataBindings(Container As Control)

        ' Remove all data bindings from labels to prevent errors when attempting to delete the contract.
        For Each ControlObject As Control In Container.Controls
            If TypeOf ControlObject Is Panel Then
                RemoveDataBindings(ControlObject)
            ElseIf TypeOf ControlObject Is LabelControl Then
                ControlObject.DataBindings.Clear()
            End If
        Next

    End Sub

    Private Sub UpdateApprovedHyperlinkAppearance()
        If DataObject.Approved = False Then
            HyperlinkApproved.Text = "Not approved by Finance"
            HyperlinkApproved.ForeColor = Color.Firebrick
        Else
            HyperlinkApproved.Text = "Approved by Finance"
            HyperlinkApproved.ForeColor = Color.SeaGreen
        End If
    End Sub

    Private Sub GetCurrentLoggedInAm()
        'this method will return the current logged in account manager
        'My.User.Name -- we have the username, now get the am code and id

    End Sub






#End Region

End Class