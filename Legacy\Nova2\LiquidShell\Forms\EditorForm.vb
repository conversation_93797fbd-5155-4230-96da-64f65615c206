﻿Public Class EditorForm

    Protected _IsDirty As Boolean = False
    Protected _RowDescription As String = String.Empty

    Protected ReadOnly Property RowDescription As String
        Get
            ' Get the description of the row being changed.
            Return _RowDescription
        End Get
    End Property

    Protected Sub LogRowModifications( _
                                     ByRef AuditLogTable As DataTable, _
                                     ModifiedRow As DataRow, _
                                     AuditedRowDescription As String _
                                     )

        ' This method will log user activity for a main application object, not a child row object.
        LogRowModifications(AuditLogTable, Nothing, ModifiedRow, String.Empty, ModifiedRow.Table.TableName, AuditedRowDescription)

    End Sub

    Protected Sub LogRowModifications( _
                                     ByRef AuditLogTable As DataTable, _
                                     OriginalRow As DataRow, _
                                     ModifiedRow As DataRow, _
                                     AuditedRowDescription As String _
                                     )

        ' This method will log user activity for a main application object, not a child row object.
        LogRowModifications(AuditLogTable, OriginalRow, ModifiedRow, String.Empty, OriginalRow.Table.TableName, AuditedRowDescription)

    End Sub

    Protected Sub LogRowModifications( _
                                     ByRef AuditLogTable As DataTable, _
                                     OriginalRow As DataRow, _
                                     ModifiedRow As DataRow, _
                                     ChildRowDescriptionPrefix As String, _
                                     ObjectType As String, _
                                     AuditedRowDescription As String _
                                     )
        ' Record any activity of the user into the audit table.

        ' Check if this is a newly created row or an existing row being modified.
        If ModifiedRow.RowState = DataRowState.Detached Then

            ' This is a new row. Create a single log entry.
            LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLogTable, ObjectType, AuditedRowDescription, "Created")

        Else

            ' This is not a newly created row. Check each row property to see what has been changed. If a changed occured, log it.
            For Each Column As DataColumn In OriginalRow.Table.Columns
                If Not Object.Equals(ModifiedRow(Column.ColumnName), OriginalRow(Column.ColumnName)) Then
                    ' This value has changed. Check if its column needs to be audited.
                    If String.IsNullOrEmpty(Column.Caption) = False Then
                        ' This column needs to be audited. Proceed.
                        If Column.DataType = GetType(Decimal) Then
                            LogRowModification( _
                                ObjectType, _
                                AuditedRowDescription, _
                                Column.Caption, _
                                CDec(OriginalRow(Column.ColumnName)).ToString("C"), _
                                CDec(ModifiedRow(Column.ColumnName)).ToString("C"), _
                                AuditLogTable, _
                                ChildRowDescriptionPrefix)
                        Else
                            LogRowModification( _
                                ObjectType, _
                                AuditedRowDescription, _
                                Column.Caption, _
                                OriginalRow(Column.ColumnName), _
                                ModifiedRow(Column.ColumnName), _
                                AuditLogTable, _
                                ChildRowDescriptionPrefix)
                        End If
                    End If
                End If
            Next

        End If

    End Sub

    Private Sub LogRowModification( _
                                  ByVal AuditedRow As DataRow, _
                                  ByVal RowDescription As String, _
                                  ByVal ChangedPropertyName As String, _
                                  ByVal ChangedPropertyOldValue As String, _
                                  ByVal ChangedPropertyNewValue As String, _
                                  ByVal AuditLog As DataTable, _
                                  ByVal ChildRowDescriptionPrefix As String _
                                  )
        LogRowModification(AuditedRow.Table.TableName, RowDescription, ChangedPropertyName, ChangedPropertyOldValue, ChangedPropertyNewValue, AuditLog, ChildRowDescriptionPrefix)
    End Sub

    Private Sub LogRowModification( _
                                  ByVal ObectType As String, _
                                  ByVal AuditedRowDescription As String, _
                                  ByVal ChangedPropertyName As String, _
                                  ByVal ChangedPropertyOldValue As String, _
                                  ByVal ChangedPropertyNewValue As String, _
                                  ByVal AuditLog As DataTable, _
                                  ByVal ChildRowDescriptionPrefix As String _
                                  )
        ' Add an entry into the audit log table for a modified row.

        ' Create a string builder to build the log description.
        Dim ActionBuilder As New System.Text.StringBuilder()

        ' Check if this audit log entry is for a child row.
        If String.IsNullOrEmpty(ChildRowDescriptionPrefix) Then
            ' This audit log entry is NOT for a child row. Simply append an upper case letter to start the description of the change.
            ActionBuilder.Append("C")
        Else
            ' This audit log entry IS for a child row. Append the child row prefix and a lower case letter to start the description of the change.
            ActionBuilder.Append(ChildRowDescriptionPrefix & ", c")
        End If

        ' Now append the remainder of the description of the change.
        ActionBuilder.Append("hanged the value of " & ChangedPropertyName.ToUpper & " from ")

        ' Add the old value to the string builder.
        If String.IsNullOrEmpty(ChangedPropertyOldValue) Then
            ActionBuilder.Append("an empty string")
        Else
            ActionBuilder.Append("'" & ChangedPropertyOldValue & "'")
        End If
        ActionBuilder.Append(" to ")

        ' Add the new value to the string builder.
        If String.IsNullOrEmpty(ChangedPropertyNewValue) Then
            ActionBuilder.Append("an empty string")
        Else
            ActionBuilder.Append("'" & ChangedPropertyNewValue & "'")
        End If

        ' Log the entry.
        LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, ObectType, AuditedRowDescription, ActionBuilder.ToString)

    End Sub

#Region "Interface 'IDataRowEditor' methods"

    Protected Overridable Sub LogChildRowModifications()
    End Sub

#End Region

End Class


Public Interface IDataRowEditor

    Sub LogChildRowModifications()

End Interface