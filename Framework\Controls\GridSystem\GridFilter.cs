﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace Framework.Controls.GridSystem
{
    public partial class GridFilter : UserControl
    {

        #region Startup

        public GridFilter()
        {
            InitializeComponent();
            Font = FrameworkSettings.Fonts.FORMFONT;
            ForeColor = FrameworkSettings.Colors.FORMFORECOLOR;
            textBox1.TextChanged += TextBox1_TextChanged;
            textBox1.KeyPress += TextBox1_KeyPress;
            InitializeInvalidCharacterList();
            ApplyTheme();
            FrameworkSettings.Colors.ThemeColorChanged += Settings_ThemeColorChanged;
        }

        private void InitializeInvalidCharacterList()
        {
            InvalidCharacterList.Add('%');
            InvalidCharacterList.Add('*');
            InvalidCharacterList.Add('[');
            InvalidCharacterList.Add(']');
        }

        #endregion


        #region Filtering

        private List<char> _InvalidCharacterList = new List<char>();
        public List<char> InvalidCharacterList
        {
            get { return _InvalidCharacterList; }
        }

        private void TextBox1_KeyPress(object sender, KeyPressEventArgs e)
        {
            bool cancelkeypress = false;
            if (InvalidCharacterList.Contains(e.KeyChar))
            {
                cancelkeypress = true;
            }
            e.Handled = cancelkeypress;
        }

        private void TextBox1_TextChanged(object sender, EventArgs e)
        {
            OnTextChanged(e);
        }

        public string FilterResultsText
        {
            get { return label1.Text; }
            set
            {
                if (label1.Text != value)
                {
                    label1.Text = value;
                }
            }
        }

        [Browsable(true)]
        [EditorBrowsable(EditorBrowsableState.Always)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        [Bindable(true)]
        public override string Text
        {
            get { return textBox1.Text; }
            set
            {
                if (textBox1.Text != value)
                {
                    textBox1.Text = value;
                }
            }
        }

        public new event EventHandler TextChanged;
        protected override void OnTextChanged(EventArgs e)
        {
            base.OnTextChanged(e);
            TextChanged?.Invoke(this, e);
        }

        public int SelectionStart
        {
            get { return textBox1.SelectionStart; }
            set { textBox1.SelectionStart = value; }
        }

        #endregion


        #region Appearance

        private void ApplyTheme()
        {
            Color themecolor = FrameworkSettings.Colors.ThemeColor;
            BackColor = themecolor;
            tableLayoutPanel1.BackColor = themecolor;
            label1.BackColor = themecolor;
        }

        private void Settings_ThemeColorChanged()
        {
            ApplyTheme();
        }

        #endregion

    }
}
