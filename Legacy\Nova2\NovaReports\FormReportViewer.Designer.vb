﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class FormReportViewer
    Inherits DevExpress.XtraEditors.XtraForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.ResearchContractBindingSource = New System.Windows.Forms.BindingSource()
        Me.DataSetContractReport = New NovaReports.DataSetContractReport()
        Me.ContractBindingSource = New System.Windows.Forms.BindingSource()
        Me.PONumberBindingSource = New System.Windows.Forms.BindingSource()
        Me.BurstBindingSource = New System.Windows.Forms.BindingSource()
        Me.ProductionBindingSource = New System.Windows.Forms.BindingSource()
        Me.MiscellaneousChargeBindingSource = New System.Windows.Forms.BindingSource()
        Me.ContractTableAdapter = New NovaReports.DataSetContractReportTableAdapters.ContractTableAdapter()
        Me.ResearchContractTableAdapter = New NovaReports.DataSetContractReportTableAdapters.ResearchContractTableAdapter()
        Me.Viewer = New Microsoft.Reporting.WinForms.ReportViewer()
        CType(Me.ResearchContractBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.DataSetContractReport, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ContractBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PONumberBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.BurstBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.ProductionBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.MiscellaneousChargeBindingSource, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'ResearchContractBindingSource
        '
        Me.ResearchContractBindingSource.DataMember = "ResearchContract"
        Me.ResearchContractBindingSource.DataSource = Me.DataSetContractReport
        '
        'DataSetContractReport
        '
        Me.DataSetContractReport.DataSetName = "DataSetContractReport"
        Me.DataSetContractReport.SchemaSerializationMode = System.Data.SchemaSerializationMode.IncludeSchema
        '
        'ContractBindingSource
        '
        Me.ContractBindingSource.DataMember = "Contract"
        Me.ContractBindingSource.DataSource = Me.DataSetContractReport
        '
        'ContractTableAdapter
        '
        Me.ContractTableAdapter.ClearBeforeFill = True
        '
        'ResearchContractTableAdapter
        '
        Me.ResearchContractTableAdapter.ClearBeforeFill = True
        '
        'Viewer
        '
        Me.Viewer.Dock = System.Windows.Forms.DockStyle.Fill
        Me.Viewer.Location = New System.Drawing.Point(0, 0)
        Me.Viewer.Name = "Viewer"
        Me.Viewer.ShowBackButton = False
        Me.Viewer.ShowContextMenu = False
        Me.Viewer.ShowCredentialPrompts = False
        Me.Viewer.ShowDocumentMapButton = False
        Me.Viewer.ShowFindControls = False
        Me.Viewer.ShowPageNavigationControls = False
        Me.Viewer.ShowParameterPrompts = False
        Me.Viewer.ShowPromptAreaButton = False
        Me.Viewer.ShowRefreshButton = False
        Me.Viewer.ShowStopButton = False
        Me.Viewer.Size = New System.Drawing.Size(984, 662)
        Me.Viewer.TabIndex = 1
        '
        'FormReportViewer
        '
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.ClientSize = New System.Drawing.Size(984, 662)
        Me.Controls.Add(Me.Viewer)
        Me.LookAndFeel.SkinName = "Office 2010 Black"
        Me.Name = "FormReportViewer"
        Me.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen
        Me.Text = "Report Viewer"
        Me.WindowState = System.Windows.Forms.FormWindowState.Maximized
        CType(Me.ResearchContractBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.DataSetContractReport, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ContractBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PONumberBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.BurstBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.ProductionBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.MiscellaneousChargeBindingSource, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents ContractBindingSource As System.Windows.Forms.BindingSource
    Friend WithEvents DataSetContractReport As NovaReports.DataSetContractReport
    Friend WithEvents PONumberBindingSource As System.Windows.Forms.BindingSource
    Friend WithEvents BurstBindingSource As System.Windows.Forms.BindingSource
    Friend WithEvents ProductionBindingSource As System.Windows.Forms.BindingSource
    Friend WithEvents MiscellaneousChargeBindingSource As System.Windows.Forms.BindingSource
    Friend WithEvents ContractTableAdapter As NovaReports.DataSetContractReportTableAdapters.ContractTableAdapter
    Friend WithEvents ResearchContractBindingSource As System.Windows.Forms.BindingSource
    Friend WithEvents ResearchContractTableAdapter As NovaReports.DataSetContractReportTableAdapters.ResearchContractTableAdapter
    Friend WithEvents Viewer As Microsoft.Reporting.WinForms.ReportViewer
End Class
