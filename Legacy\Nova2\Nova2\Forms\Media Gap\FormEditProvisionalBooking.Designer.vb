<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class FormEditProvisionalBooking
    Inherits LiquidShell.EditorForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(FormEditProvisionalBooking))
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonSave = New DevExpress.XtraEditors.SimpleButton
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton
        Me.LabelWeeksInfo = New DevExpress.XtraEditors.LabelControl
        Me.HyperlinkLastWeek = New DevExpress.XtraEditors.LabelControl
        Me.HyperlinkFirstWeek = New DevExpress.XtraEditors.LabelControl
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel
        Me.Panel1 = New System.Windows.Forms.Panel
        Me.CheckEditFirstWeek = New DevExpress.XtraEditors.CheckEdit
        Me.CheckEditWeeks = New DevExpress.XtraEditors.CheckEdit
        Me.CheckEditLastWeek = New DevExpress.XtraEditors.CheckEdit
        Me.TextEditWeeks = New DevExpress.XtraEditors.TextEdit
        Me.LabelControl5 = New DevExpress.XtraEditors.LabelControl
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelButtonBar.SuspendLayout()
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.Panel1.SuspendLayout()
        CType(Me.CheckEditFirstWeek.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditWeeks.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditLastWeek.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditWeeks.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'PanelButtonBar
        '
        Me.PanelButtonBar.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelButtonBar.Appearance.BackColor2 = System.Drawing.Color.LightSteelBlue
        Me.PanelButtonBar.Appearance.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical
        Me.PanelButtonBar.Appearance.Options.UseBackColor = True
        Me.PanelButtonBar.Controls.Add(Me.ButtonSave)
        Me.PanelButtonBar.Controls.Add(Me.ButtonCancel)
        Me.PanelButtonBar.Location = New System.Drawing.Point(0, 188)
        Me.PanelButtonBar.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Office2003
        Me.PanelButtonBar.LookAndFeel.UseDefaultLookAndFeel = False
        Me.PanelButtonBar.Size = New System.Drawing.Size(515, 52)
        Me.PanelButtonBar.TabIndex = 1
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        '
        'ButtonSave
        '
        Me.ButtonSave.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonSave.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonSave.Appearance.Options.UseFont = True
        Me.ButtonSave.ImageIndex = 1
        Me.ButtonSave.ImageList = Me.ImageList24x24
        Me.ButtonSave.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonSave.Location = New System.Drawing.Point(297, 12)
        Me.ButtonSave.LookAndFeel.SkinName = "Black"
        Me.ButtonSave.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonSave.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonSave.Name = "ButtonSave"
        Me.ButtonSave.Size = New System.Drawing.Size(100, 28)
        Me.ButtonSave.TabIndex = 0
        Me.ButtonSave.Text = "Save"
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(403, 12)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(100, 28)
        Me.ButtonCancel.TabIndex = 1
        Me.ButtonCancel.Text = "Cancel"
        '
        'LabelWeeksInfo
        '
        Me.LabelWeeksInfo.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelWeeksInfo.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelWeeksInfo.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelWeeksInfo.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap
        Me.LabelWeeksInfo.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelWeeksInfo.Location = New System.Drawing.Point(2, 81)
        Me.LabelWeeksInfo.Margin = New System.Windows.Forms.Padding(0, 3, 0, 3)
        Me.LabelWeeksInfo.Name = "LabelWeeksInfo"
        Me.LabelWeeksInfo.Size = New System.Drawing.Size(483, 13)
        Me.LabelWeeksInfo.TabIndex = 6
        Me.LabelWeeksInfo.Text = "(Changing the number of weeks will cause the last week to adjust automatically.)"
        '
        'HyperlinkLastWeek
        '
        Me.HyperlinkLastWeek.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkLastWeek.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkLastWeek.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkLastWeek.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkLastWeek.Location = New System.Drawing.Point(202, 29)
        Me.HyperlinkLastWeek.Margin = New System.Windows.Forms.Padding(3, 3, 3, 6)
        Me.HyperlinkLastWeek.Name = "HyperlinkLastWeek"
        Me.HyperlinkLastWeek.Size = New System.Drawing.Size(47, 13)
        Me.HyperlinkLastWeek.TabIndex = 3
        Me.HyperlinkLastWeek.Text = "Select..."
        '
        'HyperlinkFirstWeek
        '
        Me.HyperlinkFirstWeek.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkFirstWeek.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkFirstWeek.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkFirstWeek.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkFirstWeek.Location = New System.Drawing.Point(202, 3)
        Me.HyperlinkFirstWeek.Margin = New System.Windows.Forms.Padding(3, 3, 3, 6)
        Me.HyperlinkFirstWeek.Name = "HyperlinkFirstWeek"
        Me.HyperlinkFirstWeek.Size = New System.Drawing.Size(47, 13)
        Me.HyperlinkFirstWeek.TabIndex = 1
        Me.HyperlinkFirstWeek.Text = "Select..."
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanel1.ColumnCount = 1
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 20.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.Panel1, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.LabelControl5, 0, 0)
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(12, 12)
        Me.TableLayoutPanel1.Margin = New System.Windows.Forms.Padding(3, 3, 3, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 3
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle)
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 15.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(491, 176)
        Me.TableLayoutPanel1.TabIndex = 0
        '
        'Panel1
        '
        Me.Panel1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel1.Controls.Add(Me.CheckEditFirstWeek)
        Me.Panel1.Controls.Add(Me.CheckEditWeeks)
        Me.Panel1.Controls.Add(Me.CheckEditLastWeek)
        Me.Panel1.Controls.Add(Me.HyperlinkFirstWeek)
        Me.Panel1.Controls.Add(Me.LabelWeeksInfo)
        Me.Panel1.Controls.Add(Me.TextEditWeeks)
        Me.Panel1.Controls.Add(Me.HyperlinkLastWeek)
        Me.Panel1.Location = New System.Drawing.Point(3, 50)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(485, 123)
        Me.Panel1.TabIndex = 19
        '
        'CheckEditFirstWeek
        '
        Me.CheckEditFirstWeek.EditValue = True
        Me.CheckEditFirstWeek.Location = New System.Drawing.Point(0, 0)
        Me.CheckEditFirstWeek.Margin = New System.Windows.Forms.Padding(0, 3, 3, 3)
        Me.CheckEditFirstWeek.Name = "CheckEditFirstWeek"
        Me.CheckEditFirstWeek.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditFirstWeek.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditFirstWeek.Properties.Appearance.Options.UseFont = True
        Me.CheckEditFirstWeek.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditFirstWeek.Properties.AutoWidth = True
        Me.CheckEditFirstWeek.Properties.Caption = "Change the first week to:"
        Me.CheckEditFirstWeek.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditFirstWeek.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditFirstWeek.Size = New System.Drawing.Size(168, 19)
        Me.CheckEditFirstWeek.TabIndex = 0
        '
        'CheckEditWeeks
        '
        Me.CheckEditWeeks.Location = New System.Drawing.Point(0, 52)
        Me.CheckEditWeeks.Margin = New System.Windows.Forms.Padding(0, 3, 3, 3)
        Me.CheckEditWeeks.Name = "CheckEditWeeks"
        Me.CheckEditWeeks.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditWeeks.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditWeeks.Properties.Appearance.Options.UseFont = True
        Me.CheckEditWeeks.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditWeeks.Properties.AutoWidth = True
        Me.CheckEditWeeks.Properties.Caption = "Change the number of weeks:"
        Me.CheckEditWeeks.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditWeeks.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditWeeks.Size = New System.Drawing.Size(196, 19)
        Me.CheckEditWeeks.TabIndex = 4
        '
        'CheckEditLastWeek
        '
        Me.CheckEditLastWeek.EditValue = True
        Me.CheckEditLastWeek.Location = New System.Drawing.Point(0, 26)
        Me.CheckEditLastWeek.Margin = New System.Windows.Forms.Padding(0, 3, 3, 3)
        Me.CheckEditLastWeek.Name = "CheckEditLastWeek"
        Me.CheckEditLastWeek.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditLastWeek.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditLastWeek.Properties.Appearance.Options.UseFont = True
        Me.CheckEditLastWeek.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditLastWeek.Properties.AutoWidth = True
        Me.CheckEditLastWeek.Properties.Caption = "Change the last week to:"
        Me.CheckEditLastWeek.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditLastWeek.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditLastWeek.Size = New System.Drawing.Size(166, 19)
        Me.CheckEditLastWeek.TabIndex = 2
        '
        'TextEditWeeks
        '
        Me.TextEditWeeks.EditValue = 1
        Me.TextEditWeeks.Enabled = False
        Me.TextEditWeeks.Location = New System.Drawing.Point(202, 52)
        Me.TextEditWeeks.Margin = New System.Windows.Forms.Padding(3, 3, 0, 3)
        Me.TextEditWeeks.Name = "TextEditWeeks"
        Me.TextEditWeeks.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditWeeks.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditWeeks.Properties.Appearance.Options.UseFont = True
        Me.TextEditWeeks.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditWeeks.Properties.Mask.EditMask = "n0"
        Me.TextEditWeeks.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditWeeks.Size = New System.Drawing.Size(41, 20)
        Me.TextEditWeeks.TabIndex = 5
        '
        'LabelControl5
        '
        Me.LabelControl5.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl5.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl5.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl5.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top
        Me.LabelControl5.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap
        Me.LabelControl5.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelControl5.Location = New System.Drawing.Point(3, 3)
        Me.LabelControl5.Name = "LabelControl5"
        Me.LabelControl5.Size = New System.Drawing.Size(485, 26)
        Me.LabelControl5.TabIndex = 0
        Me.LabelControl5.Text = "Change the dates of all selected provisional bookings by using the controls below" & _
            ". No provisional booking may be extended into weeks that were not originally bo" & _
            "oked."
        '
        'FormEditProvisionalBooking
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.CancelButton = Me.ButtonCancel
        Me.ClientSize = New System.Drawing.Size(515, 240)
        Me.ControlBox = False
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.MaximizeBox = False
        Me.Name = "FormEditProvisionalBooking"
        Me.Text = "Edit Provisional Booking"
        Me.Controls.SetChildIndex(Me.PanelButtonBar, 0)
        Me.Controls.SetChildIndex(Me.TableLayoutPanel1, 0)
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelButtonBar.ResumeLayout(False)
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.Panel1.ResumeLayout(False)
        Me.Panel1.PerformLayout()
        CType(Me.CheckEditFirstWeek.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditWeeks.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditLastWeek.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditWeeks.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents ButtonSave As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelWeeksInfo As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkLastWeek As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkFirstWeek As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents LabelControl5 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents CheckEditFirstWeek As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents CheckEditLastWeek As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents TextEditWeeks As DevExpress.XtraEditors.TextEdit
    Friend WithEvents CheckEditWeeks As DevExpress.XtraEditors.CheckEdit

End Class
