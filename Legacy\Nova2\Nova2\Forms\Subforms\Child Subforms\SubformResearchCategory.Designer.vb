﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformResearchCategory
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformResearchCategory))
        Me.HyperlinkFirstMonth = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkLastMonth = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkCategory = New DevExpress.XtraEditors.LabelControl()
        Me.LabelFee = New DevExpress.XtraEditors.LabelControl()
        Me.LabelLastMonth = New DevExpress.XtraEditors.LabelControl()
        Me.LabelCategory = New DevExpress.XtraEditors.LabelControl()
        Me.LabelFirstMonth = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditMonths = New DevExpress.XtraEditors.TextEdit()
        Me.LabelMonths = New DevExpress.XtraEditors.LabelControl()
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonOK = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.TextEditFee = New DevExpress.XtraEditors.TextEdit()
        Me.LabelDiscount = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditDiscount = New DevExpress.XtraEditors.TextEdit()
        CType(Me.TextEditMonths.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditFee.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditDiscount.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!)
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Size = New System.Drawing.Size(420, 30)
        Me.LabelTitle.TabIndex = 0
        Me.LabelTitle.Text = "AV105263 - Add Research Category"
        '
        'HyperlinkFirstMonth
        '
        Me.HyperlinkFirstMonth.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkFirstMonth.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkFirstMonth.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkFirstMonth.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkFirstMonth.AutoEllipsis = True
        Me.HyperlinkFirstMonth.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkFirstMonth.Location = New System.Drawing.Point(141, 86)
        Me.HyperlinkFirstMonth.Margin = New System.Windows.Forms.Padding(3, 3, 9, 10)
        Me.HyperlinkFirstMonth.Name = "HyperlinkFirstMonth"
        Me.HyperlinkFirstMonth.Size = New System.Drawing.Size(47, 13)
        Me.HyperlinkFirstMonth.TabIndex = 4
        Me.HyperlinkFirstMonth.Text = "Select..."
        '
        'HyperlinkLastMonth
        '
        Me.HyperlinkLastMonth.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkLastMonth.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkLastMonth.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkLastMonth.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkLastMonth.AutoEllipsis = True
        Me.HyperlinkLastMonth.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkLastMonth.Location = New System.Drawing.Point(141, 112)
        Me.HyperlinkLastMonth.Margin = New System.Windows.Forms.Padding(3, 3, 9, 10)
        Me.HyperlinkLastMonth.Name = "HyperlinkLastMonth"
        Me.HyperlinkLastMonth.Size = New System.Drawing.Size(47, 13)
        Me.HyperlinkLastMonth.TabIndex = 6
        Me.HyperlinkLastMonth.Text = "Select..."
        '
        'HyperlinkCategory
        '
        Me.HyperlinkCategory.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkCategory.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkCategory.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkCategory.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkCategory.AutoEllipsis = True
        Me.HyperlinkCategory.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkCategory.Location = New System.Drawing.Point(141, 60)
        Me.HyperlinkCategory.Margin = New System.Windows.Forms.Padding(3, 3, 9, 10)
        Me.HyperlinkCategory.Name = "HyperlinkCategory"
        Me.HyperlinkCategory.Size = New System.Drawing.Size(47, 13)
        Me.HyperlinkCategory.TabIndex = 2
        Me.HyperlinkCategory.Text = "Select..."
        '
        'LabelFee
        '
        Me.LabelFee.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelFee.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelFee.Location = New System.Drawing.Point(12, 164)
        Me.LabelFee.Margin = New System.Windows.Forms.Padding(3, 3, 9, 3)
        Me.LabelFee.Name = "LabelFee"
        Me.LabelFee.Size = New System.Drawing.Size(117, 13)
        Me.LabelFee.TabIndex = 9
        Me.LabelFee.Text = "Fee (excl. discount):"
        '
        'LabelLastMonth
        '
        Me.LabelLastMonth.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelLastMonth.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelLastMonth.Location = New System.Drawing.Point(12, 112)
        Me.LabelLastMonth.Margin = New System.Windows.Forms.Padding(3, 3, 9, 3)
        Me.LabelLastMonth.Name = "LabelLastMonth"
        Me.LabelLastMonth.Size = New System.Drawing.Size(66, 13)
        Me.LabelLastMonth.TabIndex = 5
        Me.LabelLastMonth.Text = "Last Month:"
        '
        'LabelCategory
        '
        Me.LabelCategory.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCategory.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCategory.Location = New System.Drawing.Point(12, 60)
        Me.LabelCategory.Margin = New System.Windows.Forms.Padding(3, 3, 15, 3)
        Me.LabelCategory.Name = "LabelCategory"
        Me.LabelCategory.Size = New System.Drawing.Size(58, 13)
        Me.LabelCategory.TabIndex = 1
        Me.LabelCategory.Text = "Category:"
        '
        'LabelFirstMonth
        '
        Me.LabelFirstMonth.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelFirstMonth.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelFirstMonth.Location = New System.Drawing.Point(12, 86)
        Me.LabelFirstMonth.Margin = New System.Windows.Forms.Padding(3, 3, 9, 3)
        Me.LabelFirstMonth.Name = "LabelFirstMonth"
        Me.LabelFirstMonth.Size = New System.Drawing.Size(67, 13)
        Me.LabelFirstMonth.TabIndex = 3
        Me.LabelFirstMonth.Text = "First Month:"
        '
        'TextEditMonths
        '
        Me.TextEditMonths.EditValue = ""
        Me.TextEditMonths.Location = New System.Drawing.Point(141, 135)
        Me.TextEditMonths.Margin = New System.Windows.Forms.Padding(3, 3, 15, 3)
        Me.TextEditMonths.Name = "TextEditMonths"
        Me.TextEditMonths.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditMonths.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditMonths.Properties.Appearance.Options.UseFont = True
        Me.TextEditMonths.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditMonths.Properties.Mask.EditMask = "n0"
        Me.TextEditMonths.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditMonths.Properties.MaxLength = 3
        Me.TextEditMonths.Size = New System.Drawing.Size(124, 20)
        Me.TextEditMonths.TabIndex = 8
        '
        'LabelMonths
        '
        Me.LabelMonths.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelMonths.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelMonths.Location = New System.Drawing.Point(12, 138)
        Me.LabelMonths.Margin = New System.Windows.Forms.Padding(3, 3, 9, 3)
        Me.LabelMonths.Name = "LabelMonths"
        Me.LabelMonths.Size = New System.Drawing.Size(45, 13)
        Me.LabelMonths.TabIndex = 7
        Me.LabelMonths.Text = "Months:"
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        '
        'ButtonOK
        '
        Me.ButtonOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonOK.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonOK.Appearance.Options.UseFont = True
        Me.ButtonOK.ImageIndex = 2
        Me.ButtonOK.ImageList = Me.ImageList24x24
        Me.ButtonOK.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonOK.Location = New System.Drawing.Point(605, 509)
        Me.ButtonOK.LookAndFeel.SkinName = "Black"
        Me.ButtonOK.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonOK.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonOK.Name = "ButtonOK"
        Me.ButtonOK.Size = New System.Drawing.Size(100, 28)
        Me.ButtonOK.TabIndex = 11
        Me.ButtonOK.Text = "OK"
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(711, 509)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(100, 28)
        Me.ButtonCancel.TabIndex = 12
        Me.ButtonCancel.Text = "Cancel"
        '
        'TextEditFee
        '
        Me.TextEditFee.EditValue = "R 0.00"
        Me.TextEditFee.Location = New System.Drawing.Point(141, 161)
        Me.TextEditFee.Margin = New System.Windows.Forms.Padding(3, 3, 0, 3)
        Me.TextEditFee.Name = "TextEditFee"
        Me.TextEditFee.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditFee.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditFee.Properties.Appearance.Options.UseFont = True
        Me.TextEditFee.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditFee.Properties.AppearanceReadOnly.BackColor = System.Drawing.Color.Gainsboro
        Me.TextEditFee.Properties.AppearanceReadOnly.Options.UseBackColor = True
        Me.TextEditFee.Properties.Mask.EditMask = "c2"
        Me.TextEditFee.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditFee.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TextEditFee.Size = New System.Drawing.Size(124, 20)
        Me.TextEditFee.TabIndex = 10
        '
        'LabelDiscount
        '
        Me.LabelDiscount.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelDiscount.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelDiscount.Location = New System.Drawing.Point(12, 190)
        Me.LabelDiscount.Margin = New System.Windows.Forms.Padding(3, 3, 9, 3)
        Me.LabelDiscount.Name = "LabelDiscount"
        Me.LabelDiscount.Size = New System.Drawing.Size(54, 13)
        Me.LabelDiscount.TabIndex = 11
        Me.LabelDiscount.Text = "Discount:"
        '
        'TextEditDiscount
        '
        Me.TextEditDiscount.EditValue = ""
        Me.TextEditDiscount.Location = New System.Drawing.Point(141, 187)
        Me.TextEditDiscount.Margin = New System.Windows.Forms.Padding(3, 3, 0, 3)
        Me.TextEditDiscount.Name = "TextEditDiscount"
        Me.TextEditDiscount.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditDiscount.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditDiscount.Properties.Appearance.Options.UseFont = True
        Me.TextEditDiscount.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditDiscount.Properties.AppearanceReadOnly.BackColor = System.Drawing.Color.Gainsboro
        Me.TextEditDiscount.Properties.AppearanceReadOnly.Options.UseBackColor = True
        Me.TextEditDiscount.Properties.Mask.EditMask = "P"
        Me.TextEditDiscount.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditDiscount.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TextEditDiscount.Size = New System.Drawing.Size(124, 20)
        Me.TextEditDiscount.TabIndex = 12
        '
        'SubformResearchCategory
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.Controls.Add(Me.TextEditDiscount)
        Me.Controls.Add(Me.TextEditFee)
        Me.Controls.Add(Me.ButtonOK)
        Me.Controls.Add(Me.ButtonCancel)
        Me.Controls.Add(Me.HyperlinkFirstMonth)
        Me.Controls.Add(Me.HyperlinkLastMonth)
        Me.Controls.Add(Me.LabelDiscount)
        Me.Controls.Add(Me.HyperlinkCategory)
        Me.Controls.Add(Me.LabelFee)
        Me.Controls.Add(Me.LabelMonths)
        Me.Controls.Add(Me.LabelLastMonth)
        Me.Controls.Add(Me.LabelCategory)
        Me.Controls.Add(Me.LabelFirstMonth)
        Me.Controls.Add(Me.TextEditMonths)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Name = "SubformResearchCategory"
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        Me.Controls.SetChildIndex(Me.TextEditMonths, 0)
        Me.Controls.SetChildIndex(Me.LabelFirstMonth, 0)
        Me.Controls.SetChildIndex(Me.LabelCategory, 0)
        Me.Controls.SetChildIndex(Me.LabelLastMonth, 0)
        Me.Controls.SetChildIndex(Me.LabelMonths, 0)
        Me.Controls.SetChildIndex(Me.LabelFee, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkCategory, 0)
        Me.Controls.SetChildIndex(Me.LabelDiscount, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkLastMonth, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkFirstMonth, 0)
        Me.Controls.SetChildIndex(Me.ButtonCancel, 0)
        Me.Controls.SetChildIndex(Me.ButtonOK, 0)
        Me.Controls.SetChildIndex(Me.TextEditFee, 0)
        Me.Controls.SetChildIndex(Me.TextEditDiscount, 0)
        CType(Me.TextEditMonths.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditFee.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditDiscount.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents HyperlinkFirstMonth As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkLastMonth As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkCategory As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelFee As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelLastMonth As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelCategory As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelFirstMonth As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditMonths As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelMonths As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents ButtonOK As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents TextEditFee As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelDiscount As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditDiscount As DevExpress.XtraEditors.TextEdit

End Class
