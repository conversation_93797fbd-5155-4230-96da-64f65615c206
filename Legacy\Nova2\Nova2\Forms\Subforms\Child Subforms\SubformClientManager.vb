Public Class SubformClientManager

    Private GridBindingSource3 As BindingSource
    Private FormLoaded As Boolean = False

#Region "Fields"
    Private _ThisTable As DataSetClient.ClientDataTable
    Private _ThisDataSet As DataSetClient
#End Region

#Region "Properties"

    Private ReadOnly Property ThisTable As DataSetClient.ClientDataTable
        Get
            Return _ThisTable
        End Get
    End Property

    Private Property ThisDataSet As DataSetClient
        Get
            Return _ThisDataSet
        End Get
        Set(value As DataSetClient)
            _ThisDataSet = value
            _ThisTable = value.Client
        End Set
    End Property

#End Region

#Region "Public Methods"

    Public Sub New()

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        ThisDataSet = New DataSetClient
        Dim ClientBindingSource As New BindingSource(ThisDataSet, "Client")

        GridController = New GridManager _
        (GridItems, _
        TextEditSearch, _
        ClientBindingSource, _
        My.Settings.DBConnection, _
        PictureAdvancedSearch, _
        PictureClearSearch, _
        ButtonEdit, _
        ButtonDelete)

    End Sub

#End Region

#Region "Event Handlers"

    Private Sub Subform_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        LoadData()
        FormLoaded = True
    End Sub

    Private Sub ButtonAdd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonAdd.Click
        OpenDetailSubform(True)
    End Sub

    Private Sub ButtonEdit_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonEdit.Click
        OpenDetailSubform(False)
    End Sub

    Private Sub ButtonDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonDelete.Click

        ' Authenticate user.
        If Not (My.User.IsInRole("client_manager")) Then
            Dim ParentForm As BaseForm = CType(TopLevelControl, BaseForm)
            ' Stop because user does not have the right permissions
            ParentForm.ShowMessage("You do not have permission to edit client information. Only someone with 'Client Manager' permissions can do so.")
            Exit Sub
        End If

        ' Delete the selected clients, unless they have contract history.

        ' Check if at least one row is selected.
        If GridItems.SelectedRows.Count = 0 Then
            CType(TopLevelControl, BaseForm).ShowMessage("Please select the row to delete before using the 'Delete' button.")
            Exit Sub
        End If

        ' Create a list to hold the clients which cannot be deleted.
        Dim ClientList As New List(Of String)

        ' Cycle throught the selectd clients and test to see if each one has any linked contracts.
        For Each SelectedClientRow As DataGridViewRow In GridItems.SelectedRows
            Dim SelectedClient As DataSetClient.ClientRow = CType(SelectedClientRow.DataBoundItem, DataRowView).Row
            If SelectedClient.LinkedContracts Then
                ClientList.Add(SelectedClient.ClientName)
            End If
        Next

        ' Check if any of the selected clients have contracts linked and then inform the user.
        If ClientList.Count > 0 Then
            Dim MessageBuilder As New System.Text.StringBuilder
            MessageBuilder.Append("Some of the selected clients have contracts linked to them and therefore cannot be deleted. The " _
                               & vbCrLf & "clients with contracts are:" & vbCrLf & vbCrLf)
            For i As Integer = 0 To ClientList.Count - 1
                MessageBuilder.Append(ClientList(i))
                MessageBuilder.AppendLine()
            Next
            CType(TopLevelControl, BaseForm).ShowMessage(MessageBuilder.ToString, "Clients Contain History")
            Exit Sub
        End If

        ' Confirm that the user wants to delete.
        If LiquidAgent.ActionConfirmed(CType(TopLevelControl, BaseForm), "Delete") = False Then
            Exit Sub
        End If

        ' Delete the selected clients.
        For Each SelectedClientRow As DataGridViewRow In GridItems.SelectedRows
            Dim SelectedClient As DataSetClient.ClientRow = CType(SelectedClientRow.DataBoundItem, DataRowView).Row
            SelectedClient.Delete()
        Next

        ' Save the changes to the database.
        Dim ClientAdapter As New DataSetClientTableAdapters.ClientTableAdapter
        Using SqlCon As New SqlClient.SqlConnection(My.Settings.DBConnection)
            ClientAdapter.Connection = SqlCon
            ClientAdapter.Update(ThisTable)
        End Using

    End Sub

    Private Sub GridItems_CellMouseDoubleClick _
    (ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) _
    Handles GridItems.CellMouseDoubleClick
        If e.RowIndex > -1 Then
            ' The row double-clicked is NOT the column header row. Handle the event.
            ButtonEdit_Click(sender, e)
        End If
    End Sub

    Private Sub ButtonBack_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonBack.Click
        RevertToParentSubform()
    End Sub

    Private Sub GridItems_RowsAdded(sender As Object, e As DataGridViewRowsAddedEventArgs) Handles GridItems.RowsAdded
        ' Select the newly added row.
        If FormLoaded Then
            GridItems.CurrentCell = GridItems(0, e.RowIndex)
        End If
    End Sub

#End Region

#Region "Private Methods"

    Private Sub LoadData()
        ' Load data from the database into the dataset of this form.

        ' Create the adapters.
        Dim ClientAdapter As New DataSetClientTableAdapters.ClientTableAdapter

        Using SqlCon As New SqlClient.SqlConnection(My.Settings.DBConnection)
            ClientAdapter.Connection = SqlCon
            ClientAdapter.Fill(ThisTable)
        End Using

        ' Kill the adapter.
        ClientAdapter.Dispose()

    End Sub

    Private Sub OpenDetailSubform(ByVal NewItem As Boolean)

        ' Authenticate user.
        If Not (My.User.IsInRole("client_manager")) Then
            Dim ParentForm As BaseForm = CType(TopLevelControl, BaseForm)
            ' Stop because user does not have the right permissions
            ParentForm.ShowMessage("You do not have permission to edit client information. Only someone with 'Client Manager' permissions can do so.")
            Exit Sub
        End If

        If NewItem Then
            ' User clicked the New button.
            SubformClient.CreateNew(ThisTable, Me)
        Else
            ' User clicked the Edit button.
            ' Check if user selected a row before clicking.
            Dim ParentForm As BaseForm = CType(TopLevelControl, BaseForm)
            If GridItems.SelectedRows.Count < 1 Then
                ' Stop because no rows are selected.
                ParentForm.ShowMessage("Please select the row to edit before using the 'Edit' button.")
                Exit Sub
            ElseIf GridItems.SelectedRows.Count > 1 Then
                ' Stop because more than one row is selected.
                ParentForm.ShowMessage("Please select only one row to edit before using the 'Edit' button.")
                Exit Sub
            End If
            ' Only one row selected. Proceed to edit the row.
            Dim SelectedRow As DataSetClient.ClientRow = CType(GridItems.SelectedRows(0).DataBoundItem, DataRowView).Row
            SubformClient.EditExisting(SelectedRow, Me)
        End If

    End Sub

#End Region

End Class
