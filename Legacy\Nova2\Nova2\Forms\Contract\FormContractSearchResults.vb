﻿Public Class FormContractSearchResults

    Public SelectedContractRow As DataSetContract.ContractRow = Nothing

    Public Sub New(ContractTable As DataSetContract.ContractDataTable)

        ' This call is required by the designer.
        InitializeComponent()

        ' Create a datasource for the grid.
        Dim ContractDataSet As DataSetContract = ContractTable.DataSet
        Dim GridBindingSource As New BindingSource(ContractDataSet, "Contract")

        ' Setup the grid manager.
        Dim GridController As New GridManager _
        (GridItems, _
        TextEditSearch, _
        GridBindingSource, _
        My.Settings.DBConnection, _
        PictureAdvancedSearch, _
        PictureClearSearch, _
        Nothing, _
        Nothing)

    End Sub

    Public Shared Function SelectedContract(ContractTable As DataSetContract.ContractDataTable) As DataSetContract.ContractRow

        ' Instantiate this form.
        Dim ContractSelector As New FormContractSearchResults(ContractTable)
        ContractSelector.ShowDialog()

        ' Save the selected contract.
        Dim SelectedContractRow As DataSetContract.ContractRow = ContractSelector.SelectedContractRow

        ' Dispose the form.
        ContractSelector.Dispose()

        ' Return the results.
        Return SelectedContractRow

    End Function

    Private Sub ButtonSelect_Click(sender As System.Object, e As System.EventArgs) Handles ButtonSelect.Click
        ' Assign the value of the selected row to the global variable.
        If GridItems.SelectedRows.Count < 1 Then
            ShowMessage("Please select a contract.", "Captain, ice berg ahead!")
        Else
            SelectedContractRow = CType(GridItems.SelectedRows(0).DataBoundItem, DataRowView).Row
            Close()
        End If
    End Sub

    Private Sub GridItems_CellDoubleClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles GridItems.CellDoubleClick
        ' Click the "Select" button.
        ButtonSelect_Click(sender, e)
    End Sub

    Private Sub GridItems_CellFormatting(sender As Object, e As System.Windows.Forms.DataGridViewCellFormattingEventArgs) Handles GridItems.CellFormatting

        If GridItems("SignedColumn", e.RowIndex).Value = False Then
            ' Contract not signed.
            GridItems.Rows(e.RowIndex).DefaultCellStyle.ForeColor = Color.Goldenrod
            GridItems.Rows(e.RowIndex).DefaultCellStyle.SelectionBackColor = Color.Khaki
        Else
            ' Contract is signed.
            If GridItems("CancelledColumn", e.RowIndex).Value = True Then
                ' Contract is cancelled.
                GridItems.Rows(e.RowIndex).DefaultCellStyle.ForeColor = Color.IndianRed
                GridItems.Rows(e.RowIndex).DefaultCellStyle.SelectionBackColor = Color.LightCoral
            Else
                ' Contract is not cancelled.
                If Not IsDBNull(GridItems("LastWeekColumn", e.RowIndex).Value) Then
                    If GridItems("LastWeekColumn", e.RowIndex).Value < DateAdd(DateInterval.DayOfYear, -7, Date.Now) Then
                        ' Contract is completed.
                        GridItems.Rows(e.RowIndex).DefaultCellStyle.SelectionBackColor = Color.Silver
                    Else
                        ' Contract is executory.
                        GridItems.Rows(e.RowIndex).DefaultCellStyle.ForeColor = Color.RoyalBlue
                    End If
                End If
            End If
        End If

    End Sub

End Class
