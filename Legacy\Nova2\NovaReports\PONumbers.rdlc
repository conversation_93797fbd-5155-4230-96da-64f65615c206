<?xml version="1.0" encoding="utf-8"?>
<Report xmlns="http://schemas.microsoft.com/sqlserver/reporting/2008/01/reportdefinition" xmlns:rd="http://schemas.microsoft.com/SQLServer/reporting/reportdesigner">
  <Body>
    <ReportItems>
      <Tablix Name="Table_PONumbers">
        <TablixBody>
          <TablixColumns>
            <TablixColumn>
              <Width>1.4cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>1.8cm</Width>
            </TablixColumn>
            <TablixColumn>
              <Width>2.49999cm</Width>
            </TablixColumn>
          </TablixColumns>
          <TablixRows>
            <TablixRow>
              <Height>0.3cm</Height>
              <TablixCells>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Description">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Fields!PeriodName.Value &amp; ":"</Value>
                              <Style>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>6pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Left</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Description</rd:DefaultName>
                      <Style>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>1pt</PaddingTop>
                        <PaddingBottom>1pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="Amount">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=Left
(
FormatCurrency(Fields!Amount.Value,2).Replace("R","R ").Replace("R  ","R "),Len(FormatCurrency(Fields!Amount.Value,2).Replace("R","R ").Replace("R  ","R "))-3
)
+
Right
(
FormatCurrency(Fields!Amount.Value,2).Replace("R","R ").Replace("R  ","R "),3
)
.Replace(",",".")</Value>
                              <Style>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>6pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>Amount</rd:DefaultName>
                      <Style>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>1pt</PaddingTop>
                        <PaddingBottom>1pt</PaddingBottom>
                      </Style>
                    </Textbox>
                    <rd:Selected>true</rd:Selected>
                  </CellContents>
                </TablixCell>
                <TablixCell>
                  <CellContents>
                    <Textbox Name="PONumber">
                      <CanGrow>true</CanGrow>
                      <KeepTogether>true</KeepTogether>
                      <Paragraphs>
                        <Paragraph>
                          <TextRuns>
                            <TextRun>
                              <Value>=IIf(Len(Fields!PONumber.Value)&gt;0,"PO#: " &amp; Fields!PONumber.Value,"")</Value>
                              <Style>
                                <FontFamily>Verdana</FontFamily>
                                <FontSize>6pt</FontSize>
                              </Style>
                            </TextRun>
                          </TextRuns>
                          <Style>
                            <TextAlign>Right</TextAlign>
                          </Style>
                        </Paragraph>
                      </Paragraphs>
                      <rd:DefaultName>PONumber</rd:DefaultName>
                      <Style>
                        <PaddingLeft>1pt</PaddingLeft>
                        <PaddingRight>1pt</PaddingRight>
                        <PaddingTop>1pt</PaddingTop>
                        <PaddingBottom>1pt</PaddingBottom>
                      </Style>
                    </Textbox>
                  </CellContents>
                </TablixCell>
              </TablixCells>
            </TablixRow>
          </TablixRows>
        </TablixBody>
        <TablixColumnHierarchy>
          <TablixMembers>
            <TablixMember />
            <TablixMember />
            <TablixMember />
          </TablixMembers>
        </TablixColumnHierarchy>
        <TablixRowHierarchy>
          <TablixMembers>
            <TablixMember>
              <Group Name="Table_PONumbers_Details_Group">
                <DataElementName>Detail</DataElementName>
              </Group>
              <TablixMembers>
                <TablixMember />
              </TablixMembers>
              <DataElementName>Detail_Collection</DataElementName>
              <DataElementOutput>Output</DataElementOutput>
              <KeepTogether>true</KeepTogether>
            </TablixMember>
          </TablixMembers>
        </TablixRowHierarchy>
        <DataSetName>DataSetContractReport_BillingInstruction</DataSetName>
        <Height>0.3cm</Height>
        <Width>5.69999cm</Width>
        <Style>
          <Border>
            <Color>Gray</Color>
            <Style>Solid</Style>
            <Width>0.5pt</Width>
          </Border>
          <TopBorder>
            <Style>None</Style>
          </TopBorder>
          <BottomBorder>
            <Style>Solid</Style>
          </BottomBorder>
          <LeftBorder>
            <Style>None</Style>
          </LeftBorder>
          <RightBorder>
            <Style>None</Style>
          </RightBorder>
        </Style>
      </Tablix>
    </ReportItems>
    <Height>0.3cm</Height>
    <Style />
  </Body>
  <Width>5.7cm</Width>
  <Page>
    <PageHeight>29.7cm</PageHeight>
    <PageWidth>21cm</PageWidth>
    <LeftMargin>2.5cm</LeftMargin>
    <RightMargin>2.5cm</RightMargin>
    <TopMargin>2.5cm</TopMargin>
    <BottomMargin>2.5cm</BottomMargin>
    <ColumnSpacing>1cm</ColumnSpacing>
    <Style />
  </Page>
  <AutoRefresh>0</AutoRefresh>
  <DataSources>
    <DataSource Name="DBConnection">
      <ConnectionProperties>
        <DataProvider>SQL</DataProvider>
        <ConnectString>Data Source=sqlserv_3;Initial Catalog=NovaDB;User ID=alan</ConnectString>
      </ConnectionProperties>
      <rd:DataSourceID>8790fa1e-e9df-46af-820a-2eca596f348c</rd:DataSourceID>
    </DataSource>
  </DataSources>
  <DataSets>
    <DataSet Name="DataSetContractReport_BillingInstruction">
      <Query>
        <DataSourceName>DBConnection</DataSourceName>
        <CommandText>SELECT        ContractID, PurchaseOrderNumber, PurchaseOrderNumberDescription
FROM            Sales.PurchaseOrderNumber
WHERE        (ContractID = @ContractID)</CommandText>
        <rd:UseGenericDesigner>true</rd:UseGenericDesigner>
      </Query>
      <Fields>
        <Field Name="ContractID">
          <DataField>ContractID</DataField>
          <rd:TypeName>System.Guid</rd:TypeName>
        </Field>
        <Field Name="PeriodName">
          <DataField>PeriodName</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
        <Field Name="Amount">
          <DataField>Amount</DataField>
          <rd:TypeName>System.Decimal</rd:TypeName>
        </Field>
        <Field Name="PONumber">
          <DataField>PONumber</DataField>
          <rd:TypeName>System.String</rd:TypeName>
        </Field>
      </Fields>
      <rd:DataSetInfo>
        <rd:DataSetName>DataSetContractReport</rd:DataSetName>
        <rd:SchemaPath>C:\Users\<USER>\Documents\Projects\Visual Studio\Nova2\NovaReports\DataSetContractReport.xsd</rd:SchemaPath>
        <rd:TableName>BillingInstruction</rd:TableName>
        <rd:TableAdapterFillMethod>Fill</rd:TableAdapterFillMethod>
        <rd:TableAdapterGetDataMethod>GetData</rd:TableAdapterGetDataMethod>
        <rd:TableAdapterName>BillingInstructionTableAdapter</rd:TableAdapterName>
      </rd:DataSetInfo>
    </DataSet>
  </DataSets>
  <Code>    Public Function DisplayAsCurrency(Number As Object) As String
        ' This function displays a numeric value as currency
        ' in this format: R 1 000 000.00

        ' First convert the passed object into a decimal.
        Dim NumberValue As Decimal = 0
        If Decimal.TryParse(Number.ToString, NumberValue) = False Then
            Return "--error--"
        End If

        ' Format the decimal the way we want.
        NumberValue = Math.Round(NumberValue, 2)
        Dim NumberString As String = NumberValue.ToString
        Dim PointPosition As Integer = NumberString.IndexOf(".")
        Dim DecimalPart As String = NumberString.Substring(PointPosition + 1, 2)

        ' Drop the fractions from NumberString.
        NumberString = NumberString.Substring(0, PointPosition)

        ' Loop through the number string's characters inserting a comma at every third position.
        Dim InsertPosition As Integer = PointPosition - 3
        While InsertPosition &gt; 0
            NumberString = NumberString.Insert(InsertPosition, " ")
            InsertPosition -= 3
        End While

        Return "R " &amp; NumberString &amp; "." &amp; DecimalPart

    End Function</Code>
  <Language>en-ZA</Language>
  <ConsumeContainerWhitespace>true</ConsumeContainerWhitespace>
  <rd:ReportUnitType>Cm</rd:ReportUnitType>
  <rd:ReportID>57b11519-3de4-47f0-9eaf-af7aeee84ea4</rd:ReportID>
</Report>