Public Class AccountManager
    Inherits OldBaseObject

#Region "Fields"

    ' Custom fields
    Private _ClientAccountMangerBindingSource As BindingSource
    Private _BrandAccountMangerBindingSource As BindingSource
    Private _LinkedContractsByClientPeriodBindingSource As BindingSource
    Private _BudgetBindingSource As BindingSource
    Private _AccountManagerPermissionUserBindingSource As BindingSource

#End Region

#Region "Database Column Properties"

    Public ReadOnly Property AccountManagerID() As Integer
        ' This object's identifier.
        Get
            Return Row("AccountManagerID")
        End Get
    End Property

    Public Property principal_id() As Integer
        ' First name of the Account Manager.
        Get
            Return Row("principal_id")
        End Get
        Set(ByVal value As Integer)
            If Not Row("principal_id") = value Then
                ' New value is different than existing value. Proceed with update.
                Row("principal_id") = value
                IsDirty = True
            End If
        End Set
    End Property

    Public Property FirstName() As String
        ' First name of the Account Manager.
        Get
            If IsDBNull(Row("FirstName")) Then
                Return String.Empty
            Else
                Return Row("FirstName")
            End If
        End Get
        Set(ByVal value As String)
            If Not String.Compare(Row("FirstName"), value, False) = 0 Then
                ' New value is different than existing value. Proceed with update.
                AddLog("FullName", "FirstName", value.ToString)
                Row("FirstName") = value
                IsDirty = True
            End If
        End Set
    End Property

    Public Property LastName() As String
        ' First name of the Account Manager.
        Get
            If IsDBNull(Row("LastName")) Then
                Return String.Empty
            Else
                Return Row("LastName")
            End If
        End Get
        Set(ByVal value As String)
            If Not String.Compare(Row("LastName"), value, False) = 0 Then
                ' New value is different than existing value. Proceed with update.
                AddLog("FullName", "LastName", value.ToString)
                Row("LastName") = value
                IsDirty = True
            End If
        End Set
    End Property

    Public Property Email() As String
        ' First name of the Account Manager.
        Get
            Return Row("Email")
        End Get
        Set(ByVal value As String)
            If Not String.Compare(Row("Email"), value, False) = 0 Then
                ' New value is different than existing value. Proceed with update.
                AddLog("FullName", "Email", value.ToString)
                Row("Email") = value
                IsDirty = True
            End If
        End Set
    End Property

    Public Property Code() As String
        ' First name of the Account Manager.
        Get
            Return Row("Code")
        End Get
        Set(ByVal value As String)
            If Not String.Compare(Row("Code"), value, False) = 0 Then
                ' New value is different than existing value. Proceed with update.
                AddLog("FullName", "Code", value.ToString)
                Row("Code") = value
                IsDirty = True
            End If
        End Set
    End Property

    Public Property Dormant() As Boolean
        ' Dormant media is inactive and may not be sold anymore.
        Get
            Return Row("Dormant")
        End Get
        Set(ByVal value As Boolean)
            If Not Row("Dormant") = value Then
                ' New value is different than existing value. Proceed with update.
                AddLog("FullName", "Dormant", value.ToString)
                Row("Dormant") = value
                IsDirty = True
            End If
        End Set
    End Property

    Public Property Username() As String
        Get
            If Not IsDBNull(Row("Username")) Then
                Return Row("Username")
            Else
                Return "(unknown)"
            End If
        End Get
        Set(ByVal value As String)
            ' The principal_id referred to a non-existent SQL login.
            If IsDBNull(Row("Username")) Then
                AddLog("FullName", "User", value.ToString)
                Row("Username") = value
                IsDirty = True
            Else
                If Not String.Compare(Row("Username"), value, False) = 0 Then
                    ' New value is different than existing value. Proceed with update.
                    AddLog("FullName", "User", value.ToString)
                    Row("Username") = value
                    IsDirty = True
                End If
            End If
        End Set
    End Property

#End Region

#Region "Custom Properties"

    Public ReadOnly Property FullName() As String
        Get
            Return FirstName & " " & LastName
        End Get
    End Property

    Public ReadOnly Property ClientAccountMangerBindingSource() As BindingSource
        Get
            Return _ClientAccountMangerBindingSource
        End Get
    End Property

    Public ReadOnly Property BrandAccountMangerBindingSource() As BindingSource
        Get
            Return _BrandAccountMangerBindingSource
        End Get
    End Property

    Public ReadOnly Property LinkedContractsByClientPeriodBindingSource() As BindingSource
        Get
            Return _LinkedContractsByClientPeriodBindingSource
        End Get
    End Property

    Public ReadOnly Property BudgetBindingSource() As BindingSource
        Get
            Return _BudgetBindingSource
        End Get
    End Property

    Public ReadOnly Property AccountManagerPermissionUserBindingSource() As BindingSource
        Get
            Return _AccountManagerPermissionUserBindingSource
        End Get
    End Property

#End Region

#Region "Public Shared Methods"

    Public Shared Function GetListData _
    (ByVal ConString As String, _
    ByRef ErrorMessage As String, _
    ByVal GridToExclude As DataGridView) _
    As System.Windows.Forms.BindingSource

        Return GetListData(ConString, ErrorMessage, GridToExclude, Nothing)

    End Function

    Public Shared Function GetListData _
    (ByVal ConString As String, _
    ByRef ErrorMessage As String, _
    ByVal GridToExclude As DataGridView, _
    ByVal AccountManagerUsername As String) _
    As System.Windows.Forms.BindingSource
        ' This method returns a binding source containing a single row - the row that corresponds to the
        ' supplied AccountManagerUsername argument.

        ' Instantiate a new data set and create a sql connection to use to populate it.
        Dim ListDataSet As New DataSetAccountManager
        Dim SqlCon As New SqlClient.SqlConnection(ConString)

        ' Create table adapters to collect list data and related data.
        Dim ListAdapter As New DataSetAccountManagerTableAdapters.AccountManagerTableAdapter
        ListAdapter.Connection = SqlCon

        ' Populate tables in the dataset.
        Try
            If String.IsNullOrEmpty(AccountManagerUsername) = False Then
                ListAdapter.FillByAccountManager(ListDataSet.Tables("AccountManager"), AccountManagerUsername)
                If ListDataSet.Tables("AccountManager").Rows.Count = 0 Then
                    ErrorMessage = "No account managers exist with the supplied username."
                End If
            Else
                ListAdapter.Fill(ListDataSet.Tables("AccountManager"))
            End If
        Catch ex As Exception
            ErrorMessage += LiquidShell.LiquidAgent.GetErrorMessage(ex)
            Return Nothing
        Finally
            ListAdapter.Dispose()
            SqlCon.Dispose()
        End Try

        ' Return a binding source linked to the retrieved data.
        Dim ReturnBindingSource As BindingSource = New BindingSource(ListDataSet, "AccountManager")
        ReturnBindingSource.Sort = "FirstName, LastName"
        Return ReturnBindingSource

    End Function

    Public Shared Sub Delete(ByVal Grid As DataGridView, ByVal ConnectionString As String)
        ' Delete one or more rows from the database.

        ' Build a list of data relation names of child rows that must be deleted with the parent row.
        Dim DeletableChildRelationNames As New List(Of String)
        DeletableChildRelationNames.Add("AccountManager_ClientAccountManager")
        DeletableChildRelationNames.Add("AccountManager_AccountManagerPermissionUser")

        ' Create an audit table to record this action.
        Dim AuditLog As New DataSetAudit.AuditLogDataTable

        ' Create a list of names of the columns that will be used to describe each row being deleted.
        Dim ColumnNames As New List(Of String)
        ColumnNames.Add("FullName")

        ' Add row descriptions to the Tag properties of all selected grid rows (these will be used as the object name
        ' in the audit log entries for rows that are deleted.)
        For Each SelectedGridRow As DataGridViewRow In Grid.SelectedRows
            Dim Row As DataRow = CType(SelectedGridRow.DataBoundItem, DataRowView).Row
            SelectedGridRow.Tag = Row("FullName")
        Next

        ' Delete rows from the dataset.
        LiquidShell.LiquidAgent.DeleteParentRows _
        (Grid, "AccountManager_ContractCount", DeletableChildRelationNames, AuditLog, "Contract")

        ' Get the tables and data set from which rows are being deleted to use as a parameter for the table adapters.
        Dim GridTable As DataTable = CType(CType(Grid.DataSource, BindingSource).List, DataView).Table
        Dim DataSet As DataSet = GridTable.DataSet

        ' If any rows were deleted from the data set, delete them from the database too.
        Using DeletedRows As New DataView(GridTable, "", "", DataViewRowState.Deleted)
            If IsNothing(DeletedRows) = False AndAlso DeletedRows.Count > 0 Then

                ' Create and configure table adapters to connect to the database.
                Dim Adapter As New DataSetAccountManagerTableAdapters.AccountManagerTableAdapter
                Dim ClientAccountManagerAdapter As New DataSetAccountManagerTableAdapters.ClientAccountManagerTableAdapter
                Dim BrandAccountManagerAdapter As New DataSetAccountManagerTableAdapters.BrandAccountManagerTableAdapter
                Dim AuditAdapter As New DataSetAuditTableAdapters.AuditLogTableAdapter

                ' Create a new connection for the table adapters to use.
                Dim SqlCon As New SqlClient.SqlConnection(ConnectionString)
                Adapter.Connection = SqlCon
                ClientAccountManagerAdapter.Connection = SqlCon
                AuditAdapter.Connection = SqlCon

                ' Get the form that is consuming this method so that the ShowMessage method can be used.
                Dim Consumingform As LiquidShell.BaseForm = CType(Grid.TopLevelControl, LiquidShell.BaseForm)

                ' Perform the delete operation.
                Try
                    ClientAccountManagerAdapter.Update(DataSet.Tables("ClientAccountManager"))
                    BrandAccountManagerAdapter.Update(DataSet.Tables("BrandAccountManager"))
                    Adapter.Update(GridTable)
                    AuditAdapter.Update(AuditLog)
                Catch ex As Exception
                    Consumingform.ShowMessage("Aaaagghh!!!  An error occured while trying to delete. The technical description of the error is:" _
                    & vbCrLf & vbCrLf & LiquidShell.LiquidAgent.GetErrorMessage(ex))
                Finally
                    ' Dispose all data adapters.
                    Adapter.Dispose()
                    ClientAccountManagerAdapter.Dispose()
                    BrandAccountManagerAdapter.Dispose()
                    AuditAdapter.Dispose()
                    ' Close all connections.
                    SqlCon.Dispose()
                End Try

            End If
        End Using

    End Sub

    Public Shared Sub UpdateExistingClientAccountManagers _
    (ByVal ConsumingForm As LiquidShell.BaseForm,
    ByVal ConnectionString As String,
    ByVal ClientID As Integer,
    ByRef TableToFill As DataTable)
        ' Fill the given table with all account managers and their effective dates for the client with the given id.

        ' Create a connection to use for data retrieval.
        Dim SqlCon As New SqlClient.SqlConnection(ConnectionString)

        ' Create and configure a table adapter to retrieve data.
        Dim ExistingAccountManagerAdapter As New DataSetAccountManagerTableAdapters.ExistingAccountManagersTableAdapter
        ExistingAccountManagerAdapter.Connection = SqlCon
        ExistingAccountManagerAdapter.ClearBeforeFill = False

        ' Configure the schema of the table if necessary.
        If IsNothing(TableToFill) Then
            TableToFill = New DataSetAccountManager.ExistingAccountManagersDataTable
        End If

        ' Retrieve and return the data.
        Try
            ExistingAccountManagerAdapter.Fill(TableToFill, ClientID)
        Catch ex As Exception
            ConsumingForm.ShowMessage("Oh bother!  I tripped and fell while trying to collect some data." _
            & vbCrLf & vbCrLf & LiquidShell.LiquidAgent.GetErrorMessage(ex), "Data Load Error", MessageBoxIcon.Error)
        Finally
            ExistingAccountManagerAdapter.Dispose()
        End Try

    End Sub

    Public Shared Sub UpdateExistingBrandAccountManagers _
    (ByVal ConsumingForm As LiquidShell.BaseForm,
    ByVal ConnectionString As String,
    ByVal BrandID As Guid,
    ByRef TableToFill As DataTable)
        ' Fill the given table with all account managers and their effective dates for the client with the given id.

        ' Create a connection to use for data retrieval.
        Dim SqlCon As New SqlClient.SqlConnection(ConnectionString)

        ' Create and configure a table adapter to retrieve data.
        Dim ExistingAccountManagerAdapter As New DataSetAccountManagerTableAdapters.ExistingBrandManagerTableAdapter
        ExistingAccountManagerAdapter.Connection = SqlCon
        ExistingAccountManagerAdapter.ClearBeforeFill = False

        ' Configure the schema of the table if necessary.
        If IsNothing(TableToFill) Then
            TableToFill = New DataSetAccountManager.ExistingBrandManagerDataTable
        End If

        ' Retrieve and return the data.
        Try
            ExistingAccountManagerAdapter.Fill(TableToFill, BrandID)
        Catch ex As Exception
            ConsumingForm.ShowMessage("Oh bother!  I tripped and fell while trying to collect some data." _
            & vbCrLf & vbCrLf & LiquidShell.LiquidAgent.GetErrorMessage(ex), "Data Load Error", MessageBoxIcon.Error)
        Finally
            ExistingAccountManagerAdapter.Dispose()
        End Try

    End Sub


    Public Shared Function GetAccountManagerIDofCurrentUser() As Integer

    End Function

#End Region

#Region "Public Methods"

    Public Sub New(ByVal DataSource As BindingSource, ByVal AddNew As Boolean, ByVal ConString As String)
        ' This constructor takes a binding source and a boolean indicating whether a new
        ' row must be added or whether this is an existing row being edited.

        ' Update the connection string variable.
        ConnectionString = ConString

        If AddNew Then
            ' This is a new row being added.
            DataSource.AddNew()
        End If

        ' Update the base class binding source variable.
        DataBindingSource = DataSource

    End Sub

    Public Sub Save(ByVal ConnectionString As String, ByVal GridsToAudit As List(Of DataGridView))
        ' Save this object to the database.

        ' Port possible changes to the permissions grid to the permissions table.
        SaveAccountManagerPermissionUserTable()

        ' Create and configure table adapters to connect to the database.
        Dim SqlAdapter As New DataSetAccountManagerTableAdapters.AccountManagerTableAdapter
        Dim ClientAccountManagerAdapter As New DataSetAccountManagerTableAdapters.ClientAccountManagerTableAdapter
        Dim BrandAccountManagerAdapter As New DataSetAccountManagerTableAdapters.BrandAccountManagerTableAdapter
        Dim AccountManagerPermissionUserAdapter As New DataSetAccountManagerTableAdapters.AccountManagerPermissionUserTableAdapter
        Dim AccountManagerBudgetAdapter As New DataSetAccountManagerTableAdapters.AccountManagerBudgetsTableAdapter
        Dim AuditAdapter As New DataSetAuditTableAdapters.AuditLogTableAdapter

        ' Create a new connection for the table adapters to use.
        Dim SqlCon As New SqlClient.SqlConnection(ConnectionString)
        SqlAdapter.Connection = SqlCon
        ClientAccountManagerAdapter.Connection = SqlCon
        BrandAccountManagerAdapter.Connection = SqlCon
        AccountManagerPermissionUserAdapter.Connection = SqlCon
        AccountManagerBudgetAdapter.Connection = SqlCon
        AuditAdapter.Connection = SqlCon

        ' Check if this is a new row being added.
        If Row.RowState = DataRowState.Detached Then
            ' This is a new row being added. Clear the audit log and create only one entry for the new row.
            AuditLog.Clear()
            AddLog("FullName")
        Else
            ' This row was edited. Update audit log with child row addition details.
            For Each Grid As DataGridView In GridsToAudit
                ' Check each row in this grid.
                For Each GridRow As DataGridViewRow In Grid.Rows
                    ' Get the datarow feeding this grid row.
                    Dim Row As DataRow = CType(GridRow.DataBoundItem, DataRowView).Row
                    If Row.RowState = DataRowState.Added Then
                        ' This row is newly added. Add an entry for it into the audit log.
                        Dim ActionText As String = BuildChildRowActionText(Grid, GridRow, ChildRowAction.Added)
                        LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, Table.TableName, FullName, ActionText)
                    End If
                    If Row.RowState = DataRowState.Modified Then
                        ' This row has been modified. Add an entry for it into the audit log.
                        Dim ActionText As String = BuildChildRowActionText(Grid, GridRow, ChildRowAction.Modified)
                        LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, Table.TableName, FullName, ActionText)
                    End If
                Next
            Next
        End If

        ' Perform the save operation.
        Try
            ' Remember the current item in the binding source list.
            Dim CurrentItem As Object = DataBindingSource.Current
            ' Commit changes to the dataset.
            DataBindingSource.EndEdit()     ' This line changes the binding source's current item.
            ' Restore the current item in the binding source list.
            DataBindingSource.Position = DataBindingSource.IndexOf(CurrentItem)
            ' Update the database.
            SqlAdapter.Update(Row)
            ClientAccountManagerAdapter.Update(DataSet.Tables("ClientAccountManager"))
            BrandAccountManagerAdapter.Update(DataSet.Tables("BrandAccountManager"))
            AccountManagerPermissionUserAdapter.Update(DataSet.Tables("AccountManagerPermissionUser"))
            AccountManagerBudgetAdapter.Update(DataSet.Tables("AccountManagerBudgets"))
            IsDirty = False
            AuditAdapter.Update(AuditLog)
        Finally
            ' Dispose all data adapters.
            SqlAdapter.Dispose()
            ClientAccountManagerAdapter.Dispose()
            BrandAccountManagerAdapter.Dispose()
            AccountManagerPermissionUserAdapter.Dispose()
            AccountManagerBudgetAdapter.Dispose()
            AuditAdapter.Dispose()
            ' Dispose the connection.
            SqlCon.Close()
        End Try

    End Sub

    Public Sub AddClientAccountManager(ByVal NewClientAccounts As List(Of DataRow))

        ' Check for legal selection by user.
        If IsNothing(NewClientAccounts) Then
            Exit Sub
        Else
            If NewClientAccounts.Count = 0 Then
                Exit Sub
            End If
        End If

        ' Add a new row to the table for each item that was selected.
        For Each SelectedItem As DataRow In NewClientAccounts

            ' Create a new row for the table.
            Dim NewRow As DataRow = DataSet.Tables("ClientAccountManager").NewRow
            NewRow("AccountManagerID") = Row("AccountManagerID")
            NewRow("ClientID") = SelectedItem("ClientID")
            NewRow("EffectiveDate") = SelectedItem("EffectiveDate")
            NewRow("CareTaker") = SelectedItem("CareTaker")
            NewRow("CareTakerWeeks") = SelectedItem("CareTakerWeeks")
            DataSet.Tables("ClientAccountManager").Rows.Add(NewRow)

            ' Flag the object as dirty.
            IsDirty = True

        Next

    End Sub

    Public Sub AddBrandAccountManager(ByVal NewBrandAccounts As List(Of DataRow))

        ' Check for legal selection by user.
        If IsNothing(NewBrandAccounts) Then
            Exit Sub
        Else
            If NewBrandAccounts.Count = 0 Then
                Exit Sub
            End If
        End If

        ' Add a new row to the table for each item that was selected.
        For Each SelectedItem As DataRow In NewBrandAccounts

            ' Create a new row for the table.
            Dim NewRow As DataRow = DataSet.Tables("BrandAccountManager").NewRow
            NewRow("AccountManagerID") = Row("AccountManagerID")
            NewRow("BrandID") = SelectedItem("BrandID")
            NewRow("EffectiveDate") = SelectedItem("EffectiveDate")
            NewRow("CareTaker") = SelectedItem("CareTaker")
            NewRow("CareTakerWeeks") = SelectedItem("CareTakerWeeks")
            DataSet.Tables("BrandAccountManager").Rows.Add(NewRow)

            ' Flag the object as dirty.
            IsDirty = True

        Next

    End Sub

#End Region

#Region "Private Methods"

    Protected Overrides Sub UpdateProperties()

        ' Update properties to match the current Row.
        UpdateCustomFields()

    End Sub

    Private Sub UpdateCustomFields()

        _ClientAccountMangerBindingSource = New BindingSource(DataBindingSource, "AccountManager_ClientAccountManager")
        _ClientAccountMangerBindingSource.Sort = "ClientName"


        _BrandAccountMangerBindingSource = New BindingSource(DataBindingSource, "AccountManager_BrandAccountManager")
        _BrandAccountMangerBindingSource.Sort = "BrandName"

        _LinkedContractsByClientPeriodBindingSource = New BindingSource _
        (_ClientAccountMangerBindingSource, "ClientAccountManager_vClientAccountManagerPeriodsWithActivity")

        _BudgetBindingSource = New BindingSource(DataBindingSource, "AccountManager_AccountManagerBudgets")
        _BudgetBindingSource.Sort = "FiscalName"

        _AccountManagerPermissionUserBindingSource = NewAccountManagerPermissionUserBindingSource()

    End Sub

    Private Sub SaveAccountManagerPermissionUserTable()

        ' Check if changes have been made to the permissions grid so that these can be ported to
        ' the permissions table.
        Dim UserTable As DataTable = LiquidShell.LiquidAgent.GetBindingSourceTable(_AccountManagerPermissionUserBindingSource)
        Dim PermissionTable As DataTable = DataSet.Tables("AccountManagerPermissionUser")

        For Each UserRow As DataRow In UserTable.Rows
            If UserRow.RowState = DataRowState.Modified Then
                ' This user's permissions have been modified.

                For Each PermissionColumn As DataColumn In UserTable.Columns

                    If Not String.Compare(PermissionColumn.ColumnName, "principal_id") = 0 _
                    AndAlso Not String.Compare(PermissionColumn.ColumnName, "name") = 0 Then
                        ' This is not the ID or name columns - it is a column corresponding to one of the AccountManagerPermissions.
                        ' It's safe to proceed.

                        ' Build a key parameter to find the corresponding row in the permissions table.
                        Dim Keys(2) As Object

                        ' Get the first key value - the AccountManagerPermissionID corresponding to the current column.
                        Dim Filter As String = "AccountManagerPermissionName = '" & PermissionColumn.ColumnName & "'"
                        Dim PermissionLookupRow() As DataRow = DataSet.Tables("AccountManagerPermission").Select(Filter)
                        Keys(0) = PermissionLookupRow(0).Item("AccountManagerPermissionID")

                        ' The second key value is the account manager ID.
                        Keys(1) = AccountManagerID

                        ' The third key value is the principal_id of the user.
                        Keys(2) = UserRow("principal_id")

                        ' Get the row from the permissions table.
                        Dim PermissionRow As DataRow = PermissionTable.Rows.Find(Keys)

                        ' If the user row (from the grid) has a TRUE value for this column, the corresponding permission row
                        ' must exist. If the user row has a FALSE value for this column, then permission row must not exist.
                        If IsNothing(PermissionRow) = False Then
                            If UserRow(PermissionColumn.ColumnName) = False Then
                                ' The permission row exists but the box in the grid for this permission was unticked so
                                ' the permission row must be deleted.
                                PermissionRow.Delete()
                            End If
                        Else
                            If UserRow(PermissionColumn.ColumnName) = True Then
                                ' The permission row doesn not exist but the box in the grid for this permission was ticked so
                                ' the permission row must be created.
                                Dim NewPermissionRow As DataRow = PermissionTable.NewRow
                                With NewPermissionRow
                                    .Item("AccountManagerPermissionID") = Keys(0)
                                    .Item("AccountManagerID") = Keys(1)
                                    .Item("principal_id") = Keys(2)
                                End With
                                PermissionTable.Rows.Add(NewPermissionRow)
                            End If
                        End If

                    End If

                Next PermissionColumn

            End If
        Next UserRow

    End Sub

    Private Function NewAccountManagerPermissionUserBindingSource() As BindingSource
        ' Create a binding source that connects to a table that can be used to easily modify user
        ' permissions for this account manager.

        ' Return nothing if the dataset doesn't have a value.
        If IsNothing(DataSet) Then
            Return Nothing
        End If

        ' Create adapters to collect permission data from the database.
        Dim AccountManagerPermissionUserAdapter As New DataSetAccountManagerTableAdapters.AccountManagerPermissionUserTableAdapter
        Dim AccountManagerPermissionAdapter As New DataSetAccountManagerTableAdapters.AccountManagerPermissionTableAdapter
        Dim sql_loginsAdapter As New DataSetAccountManagerTableAdapters.sql_loginsTableAdapter

        Dim SqlCon As New SqlClient.SqlConnection(ConnectionString)
        AccountManagerPermissionUserAdapter.Connection = SqlCon
        AccountManagerPermissionAdapter.Connection = SqlCon
        sql_loginsAdapter.Connection = SqlCon

        ' Fetch data from the server.
        Dim RawDataTable As DataTable = DataSet.Tables("AccountManagerPermissionUser")
        Dim StandardPermissions As DataTable = DataSet.Tables("AccountManagerPermission")
        Dim SqlUsers As DataTable = DataSet.Tables("sql_logins")
        sql_loginsAdapter.Fill(SqlUsers)
        AccountManagerPermissionAdapter.Fill(StandardPermissions)
        AccountManagerPermissionUserAdapter.Fill(RawDataTable, Row("AccountManagerID"))

        ' Create columns for a table to hold the data.
        Dim PermissionColumns(2 + StandardPermissions.Rows.Count - 1) As DataColumn
        PermissionColumns(0) = New DataColumn("principal_id", GetType(Integer))
        PermissionColumns(1) = New DataColumn("name", GetType(String))
        For i As Integer = 0 To StandardPermissions.Rows.Count - 1
            Dim ColumnName As String = StandardPermissions.Rows(i).Item("AccountManagerPermissionName")
            PermissionColumns(i + 2) = New DataColumn(ColumnName, GetType(Boolean))
            PermissionColumns(i + 2).DefaultValue = False
        Next

        ' Create a table for the binding source that will be returned.
        Dim PermissionsTable As New DataTable
        PermissionsTable.TableName = "PermissionsTable"
        PermissionsTable.Columns.AddRange(PermissionColumns)
        Dim Key() As DataColumn = {PermissionColumns(0)}
        PermissionsTable.PrimaryKey = Key
        Dim PermissionsDataSet As New DataSet
        PermissionsDataSet.Tables.Add(PermissionsTable)

        ' Create a new row in the permissions table for each sql user.
        For Each SqlUserRow As DataRow In SqlUsers.Rows
            If String.Compare(SqlUserRow("name"), "sa") <> 0 _
            AndAlso String.Compare(SqlUserRow("name"), "sp_executor") <> 0 _
            AndAlso String.Compare(SqlUserRow("name"), "systemdesigner") <> 0 _
            AndAlso String.Compare(SqlUserRow("name"), Username) <> 0 Then
                ' Remove system logins.
                If Not CStr(SqlUserRow("name")).Contains("Policy") Then
                    Dim NewRow As DataRow = PermissionsTable.NewRow
                    With NewRow
                        .Item("principal_id") = SqlUserRow("principal_id")
                        .Item("name") = SqlUserRow("name")
                    End With
                    PermissionsTable.Rows.Add(NewRow)
                End If
            End If
        Next

        ' Using the standard table from the dataset, populate our new table with data.
        For Each RawDataRow As DataRow In RawDataTable.Rows
            Dim PermissionRow As DataRow = PermissionsTable.Rows.Find(RawDataRow("principal_id"))
            If IsNothing(PermissionRow) = False Then
                Dim PermissionName As String = RawDataRow("AccountManagerPermissionName")
                PermissionRow(PermissionName) = True
            End If
        Next

        ' Accept new table additions.
        PermissionsTable.AcceptChanges()

        ' Create and configure a binding source then return it.
        Dim ReturnBindingSource As New BindingSource(PermissionsDataSet, PermissionsTable.TableName)
        ReturnBindingSource.Sort = "name"
        Return ReturnBindingSource

    End Function

#End Region

End Class
