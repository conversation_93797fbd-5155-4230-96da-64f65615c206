<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DataSetContractInfo" targetNamespace="http://tempuri.org/DataSetContractInfo.xsd" xmlns:mstns="http://tempuri.org/DataSetContractInfo.xsd" xmlns="http://tempuri.org/DataSetContractInfo.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="DBConnection" ConnectionStringObject="" IsAppSettingsProperty="True" Modifier="Public" Name="DBConnection (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.NovaData.My.MySettings.GlobalReference.Default.DBConnection" Provider="System.Data.SqlClient">
          </Connection>
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ContractInfoTableAdapter" GeneratorDataComponentClassName="ContractInfoTableAdapter" Name="ContractInfo" UserDataComponentName="ContractInfoTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Sales.vViewableContractInfo" DbObjectType="View" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="False" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>SELECT        ContractID, ContractNumber, ClientName, ProjectName, SignedBy, SignDate, CreatedBy, CreationDate, AccountManager
FROM            Sales.vViewableContractInfo
WHERE        (ContractID = @ContractID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.vViewableContractInfo" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ContractID" DataSetColumn="ContractID" />
              <Mapping SourceColumn="ContractNumber" DataSetColumn="ContractNumber" />
              <Mapping SourceColumn="ClientName" DataSetColumn="ClientName" />
              <Mapping SourceColumn="ProjectName" DataSetColumn="ProjectName" />
              <Mapping SourceColumn="SignedBy" DataSetColumn="SignedBy" />
              <Mapping SourceColumn="SignDate" DataSetColumn="SignDate" />
              <Mapping SourceColumn="CreatedBy" DataSetColumn="CreatedBy" />
              <Mapping SourceColumn="CreationDate" DataSetColumn="CreationDate" />
              <Mapping SourceColumn="AccountManager" DataSetColumn="AccountManager" />
            </Mappings>
            <Sources>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="BurstInfoTableAdapter" GeneratorDataComponentClassName="BurstInfoTableAdapter" Name="BurstInfo" UserDataComponentName="BurstInfoTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="False" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="False" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="False">
                    <CommandText>SELECT DISTINCT 
                         Sales.vEffectiveBurst.BurstID, Sales.vEffectiveBurst.ContractID, Sales.vEffectiveBurst.MediaName, Sales.vEffectiveBurst.ChainName, 
                         Store.Category.CategoryName, Sales.vEffectiveBurst.Homesite, Sales.vEffectiveBurst.BrandName, Sales.vEffectiveBurst.ProductName, 
                         Sales.vEffectiveBurst.FirstWeek, Sales.vEffectiveBurst.LastInstallWeek, Sales.vEffectiveBurst.InstallStoreQty, Sales.vEffectiveBurst.InstallWeeks
FROM            Sales.vEffectiveBurst INNER JOIN
                         Store.Category ON Sales.vEffectiveBurst.CategoryID = Store.Category.CategoryID
WHERE        (Sales.vEffectiveBurst.ContractID = @ContractID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="False" AutogeneratedName="ContractID" ColumnName="ContractID" DataSourceName="NovaDB.Sales.vEffectiveBurst" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@ContractID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="ContractID" SourceColumnNullMapping="False" SourceVersion="Current">
                      </Parameter>
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="BurstID" DataSetColumn="BurstID" />
              <Mapping SourceColumn="ContractID" DataSetColumn="ContractID" />
              <Mapping SourceColumn="MediaName" DataSetColumn="MediaName" />
              <Mapping SourceColumn="ChainName" DataSetColumn="ChainName" />
              <Mapping SourceColumn="CategoryName" DataSetColumn="CategoryName" />
              <Mapping SourceColumn="Homesite" DataSetColumn="Homesite" />
              <Mapping SourceColumn="BrandName" DataSetColumn="BrandName" />
              <Mapping SourceColumn="ProductName" DataSetColumn="ProductName" />
              <Mapping SourceColumn="FirstWeek" DataSetColumn="FirstWeek" />
              <Mapping SourceColumn="LastInstallWeek" DataSetColumn="LastInstallWeek" />
              <Mapping SourceColumn="InstallStoreQty" DataSetColumn="InstallStoreQty" />
              <Mapping SourceColumn="InstallWeeks" DataSetColumn="InstallWeeks" />
            </Mappings>
            <Sources>
            </Sources>
          </TableAdapter>
        </Tables>
        <Sources>
        </Sources>
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DataSetContractInfo" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_UserDSName="DataSetContractInfo" msprop:Generator_DataSetName="DataSetContractInfo">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="ContractInfo" msprop:Generator_UserTableName="ContractInfo" msprop:Generator_RowDeletedName="ContractInfoRowDeleted" msprop:Generator_RowChangedName="ContractInfoRowChanged" msprop:Generator_RowClassName="ContractInfoRow" msprop:Generator_RowChangingName="ContractInfoRowChanging" msprop:Generator_RowEvArgName="ContractInfoRowChangeEvent" msprop:Generator_RowEvHandlerName="ContractInfoRowChangeEventHandler" msprop:Generator_TableClassName="ContractInfoDataTable" msprop:Generator_TableVarName="tableContractInfo" msprop:Generator_RowDeletingName="ContractInfoRowDeleting" msprop:Generator_TablePropName="ContractInfo">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ContractID" msdata:DataType="System.Guid, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_UserColumnName="ContractID" msprop:Generator_ColumnVarNameInTable="columnContractID" msprop:Generator_ColumnPropNameInRow="ContractID" msprop:Generator_ColumnPropNameInTable="ContractIDColumn" type="xs:string" />
              <xs:element name="ContractNumber" msprop:Generator_UserColumnName="ContractNumber" msprop:Generator_ColumnVarNameInTable="columnContractNumber" msprop:Generator_ColumnPropNameInRow="ContractNumber" msprop:Generator_ColumnPropNameInTable="ContractNumberColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ClientName" msprop:Generator_UserColumnName="ClientName" msprop:Generator_ColumnVarNameInTable="columnClientName" msprop:Generator_ColumnPropNameInRow="ClientName" msprop:Generator_ColumnPropNameInTable="ClientNameColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ProjectName" msprop:Generator_UserColumnName="ProjectName" msprop:Generator_ColumnVarNameInTable="columnProjectName" msprop:Generator_ColumnPropNameInRow="ProjectName" msprop:Generator_ColumnPropNameInTable="ProjectNameColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="SignedBy" msprop:Generator_UserColumnName="SignedBy" msprop:Generator_ColumnVarNameInTable="columnSignedBy" msprop:Generator_ColumnPropNameInRow="SignedBy" msprop:Generator_ColumnPropNameInTable="SignedByColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="SignDate" msprop:Generator_UserColumnName="SignDate" msprop:Generator_ColumnVarNameInTable="columnSignDate" msprop:Generator_ColumnPropNameInRow="SignDate" msprop:Generator_ColumnPropNameInTable="SignDateColumn" type="xs:dateTime" minOccurs="0" />
              <xs:element name="CreatedBy" msprop:Generator_UserColumnName="CreatedBy" msprop:Generator_ColumnVarNameInTable="columnCreatedBy" msprop:Generator_ColumnPropNameInRow="CreatedBy" msprop:Generator_ColumnPropNameInTable="CreatedByColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CreationDate" msprop:Generator_UserColumnName="CreationDate" msprop:Generator_ColumnVarNameInTable="columnCreationDate" msprop:Generator_ColumnPropNameInRow="CreationDate" msprop:Generator_ColumnPropNameInTable="CreationDateColumn" type="xs:dateTime" />
              <xs:element name="AccountManager" msprop:Generator_UserColumnName="AccountManager" msprop:Generator_ColumnPropNameInRow="AccountManager" msprop:Generator_ColumnVarNameInTable="columnAccountManager" msprop:Generator_ColumnPropNameInTable="AccountManagerColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="151" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="BurstInfo" msprop:Generator_UserTableName="BurstInfo" msprop:Generator_RowDeletedName="BurstInfoRowDeleted" msprop:Generator_RowChangedName="BurstInfoRowChanged" msprop:Generator_RowClassName="BurstInfoRow" msprop:Generator_RowChangingName="BurstInfoRowChanging" msprop:Generator_RowEvArgName="BurstInfoRowChangeEvent" msprop:Generator_RowEvHandlerName="BurstInfoRowChangeEventHandler" msprop:Generator_TableClassName="BurstInfoDataTable" msprop:Generator_TableVarName="tableBurstInfo" msprop:Generator_RowDeletingName="BurstInfoRowDeleting" msprop:Generator_TablePropName="BurstInfo">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="BurstID" msdata:DataType="System.Guid, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_UserColumnName="BurstID" msprop:Generator_ColumnVarNameInTable="columnBurstID" msprop:Generator_ColumnPropNameInRow="BurstID" msprop:Generator_ColumnPropNameInTable="BurstIDColumn" type="xs:string" />
              <xs:element name="ContractID" msdata:DataType="System.Guid, mscorlib, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_UserColumnName="ContractID" msprop:Generator_ColumnVarNameInTable="columnContractID" msprop:Generator_ColumnPropNameInRow="ContractID" msprop:Generator_ColumnPropNameInTable="ContractIDColumn" type="xs:string" />
              <xs:element name="MediaName" msprop:Generator_UserColumnName="MediaName" msprop:Generator_ColumnVarNameInTable="columnMediaName" msprop:Generator_ColumnPropNameInRow="MediaName" msprop:Generator_ColumnPropNameInTable="MediaNameColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ChainName" msprop:Generator_UserColumnName="ChainName" msprop:Generator_ColumnVarNameInTable="columnChainName" msprop:Generator_ColumnPropNameInRow="ChainName" msprop:Generator_ColumnPropNameInTable="ChainNameColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CategoryName" msprop:Generator_UserColumnName="CategoryName" msprop:Generator_ColumnVarNameInTable="columnCategoryName" msprop:Generator_ColumnPropNameInRow="CategoryName" msprop:Generator_ColumnPropNameInTable="CategoryNameColumn">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Homesite" msdata:ReadOnly="true" msprop:Generator_UserColumnName="Homesite" msprop:Generator_ColumnVarNameInTable="columnHomesite" msprop:Generator_ColumnPropNameInRow="Homesite" msprop:Generator_ColumnPropNameInTable="HomesiteColumn" type="xs:boolean" minOccurs="0" />
              <xs:element name="BrandName" msprop:Generator_UserColumnName="BrandName" msprop:Generator_ColumnVarNameInTable="columnBrandName" msprop:Generator_ColumnPropNameInRow="BrandName" msprop:Generator_ColumnPropNameInTable="BrandNameColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ProductName" msprop:Generator_UserColumnName="ProductName" msprop:Generator_ColumnVarNameInTable="columnProductName" msprop:Generator_ColumnPropNameInRow="ProductName" msprop:Generator_ColumnPropNameInTable="ProductNameColumn" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="FirstWeek" msprop:Generator_UserColumnName="FirstWeek" msprop:Generator_ColumnVarNameInTable="columnFirstWeek" msprop:Generator_ColumnPropNameInRow="FirstWeek" msprop:Generator_ColumnPropNameInTable="FirstWeekColumn" type="xs:dateTime" />
              <xs:element name="LastInstallWeek" msdata:ReadOnly="true" msprop:Generator_UserColumnName="LastInstallWeek" msprop:Generator_ColumnVarNameInTable="columnLastInstallWeek" msprop:Generator_ColumnPropNameInRow="LastInstallWeek" msprop:Generator_ColumnPropNameInTable="LastInstallWeekColumn" type="xs:dateTime" minOccurs="0" />
              <xs:element name="InstallStoreQty" msprop:Generator_UserColumnName="InstallStoreQty" msprop:Generator_ColumnVarNameInTable="columnInstallStoreQty" msprop:Generator_ColumnPropNameInRow="InstallStoreQty" msprop:Generator_ColumnPropNameInTable="InstallStoreQtyColumn" type="xs:int" />
              <xs:element name="InstallWeeks" msprop:Generator_UserColumnName="InstallWeeks" msprop:Generator_ColumnPropNameInRow="InstallWeeks" msprop:Generator_ColumnVarNameInTable="columnInstallWeeks" msprop:Generator_ColumnPropNameInTable="InstallWeeksColumn" type="xs:int" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ContractInfo" />
      <xs:field xpath="mstns:ContractID" />
    </xs:unique>
  </xs:element>
  <xs:annotation>
    <xs:appinfo>
      <msdata:Relationship name="ContractInfo_BurstInfo" msdata:parent="ContractInfo" msdata:child="BurstInfo" msdata:parentkey="ContractID" msdata:childkey="ContractID" msprop:Generator_UserRelationName="ContractInfo_BurstInfo" msprop:Generator_RelationVarName="relationContractInfo_BurstInfo" msprop:Generator_UserChildTable="BurstInfo" msprop:Generator_UserParentTable="ContractInfo" msprop:Generator_ParentPropName="vViewableContractInfoRow" msprop:Generator_ChildPropName="GetBurstInfoRows" />
    </xs:appinfo>
  </xs:annotation>
</xs:schema>