﻿Public Class FormAccountOptions

    Private Sub ButtonPassword_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonPassword.Click
        LiquidAgent.ChangeUserPassword(My.Settings.Item("DBConnection"), Icon, False)
    End Sub

    Private Sub ButtonClose_Click(ByVal sender As System.Object, ByVal e As System.EventArgs)
        Me.Close()
    End Sub

    Private Sub FormAccountOptions_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        SetupControls()
    End Sub

    Private Sub SetupControls()

        ' Labels
        LabelPassword.Text = "Change the password for account '" & My.User.Name & "'."

        ' Display controls relevant to account managers.
        If My.User.IsInRole("accountmanager") Then
            ButtonDetails.Visible = True
            ButtonPermissions.Visible = True
            LabelDetails.Visible = True
            LabelPermissions.Visible = True
        End If

    End Sub

    Private Sub ButtonPermissions_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonPermissions.Click

        ' Get a bindingsource with only this one account manager in its list to pass into the constructor 
        ' of the permissions form.
        Dim Errors As String = String.Empty
        Dim AccountManagerBindingSource As BindingSource = AccountManager.GetListData _
        (My.Settings.DBConnection, Errors, Nothing, My.User.Name)

        ' Create and display a new permissions form.
        If Errors.Length > 0 Then
            ' Data didn't load correctly.
            ShowMessage("Errors occured while loading account manager permission data." & vbCrLf & vbCrLf _
            & Errors, "Data Load Error", MessageBoxIcon.Error)
        Else
            ' Data loaded correctly. Display the form.
            Dim PermissionsForm As New FormAccountManagerUserPermissions _
            (New AccountManager(AccountManagerBindingSource, False, My.Settings.DBConnection))
            PermissionsForm.ShowDialog()
        End If

    End Sub

End Class