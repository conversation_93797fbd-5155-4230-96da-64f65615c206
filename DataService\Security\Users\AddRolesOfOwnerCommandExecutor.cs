﻿using DataAccess;

namespace DataService.Security
{
    class AddRolesOfOwnerCommandExecutor : CommandExecutor<AddRolesOfOwnerCommand>
    {

        public override void Execute(AddRolesOfOwnerCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.AddRolesOfOwner))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.AddInputParameter("userid", command.UserId);
                storedprocedure.AddInputParameter("newroles", command.NewRoles);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
            }
        }

    }
}
