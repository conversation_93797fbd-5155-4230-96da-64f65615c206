﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="accept16" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAIGNIUk0AAHolAACAgwAA+f8AAIDpAAB1
        MAAA6mAAADqYAAAXb5JfxUYAAAAJcEhZcwAACxIAAAsSAdLdfvwAAALASURBVDhPfZJtSJNRFMdPpUUW
        hC9FIkaKWh8Ksg/RywcNiyCigqBPUSKm+EZi6kwjSSMRUpaIBJG9mWWONNvUnBN1lVZCtTS1pFqKuqdt
        j2667Xk29+8+g0aaeuHPvZd7zu8c/ufSjM1ODrtAgiCQywkS2W63OwgAud3ueZqYmEjlOE4+NjYWCDd7
        n3MTLQVwOASS5HLNkRSs0+nOMyhElwU8z3+ura0NWhYgCKIXoNVqY6Xkmg87UNJFeDdWKl3RrGqOXrKD
        vwBZrmyDFKzoP45ilix/7Y+8NsLL4VTo9Xq1FyAKTvrSP7ifiFbaPZ6ITE6yWCw/e/Ry5LcTyt+EoFQb
        iCsdhB8mLfre91V5AHer721yiS5hnB8EXIBa3bHLYXPS4MBgyfjUAPLUhOvdIR7lsupDkxowIz+yYmsp
        Onq3HzNjrmX4KhKfEx7rksBMQ6em+wScwI1X0SjQBOBaVwQyW1ag/VsZrFYrx5LXOdjESD+ib2obliP5
        BSFfE4Y0lQ+e6rI9JrV9lSNd5YvCju3IavXHzZ6jkKBxcYdCbdL4JcDI0PfWJl0Jzj0hyFRRyFNFIrVh
        Pe70JiJXGY4cZRiylVuR0RAEo9mA+rqGDOuMjbyA2NiDW+AAHvbm4sx9H2QqIpH1LAopdZtxQRGOi+x8
        9sFqNH+qxOTopE4yeYYBpM8miCLR1NQ0xccn7IQAVHdm4/QtX6TXRCHj0TaPEqo3Qla/F3M2IDQ0NNhk
        4ulfeQA8P015skt7pE4qW9JwstwXSbejmCJwSu6H/pFeaDvfVvDmKTIvkBcgUXOyZQfcrFJZYxKOFK/C
        4SI2+8ZkCBbBylpfM2ngaKHmAYxGni4XFMaIs2x8ilSkVO4DZzChsqLqmIH7TYvpPwDHmaioqDhm/Jdp
        1GyYnVEqW0uNnHnRZAm43Apgj8GS68sF/QHNBYwpNKUmawAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="ErrorAlert" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAIAEBAAAAEACABoBQAAJgAAABAQAAABACAAaAQAAPYKAAAoAAAAEAAAACAAAAABAAgAAAAAAAAB
        AAAAAAAAAAAAAAABAAAAAQAAAAAAABx10gAge9UAIJ7gADK97wBju+kASMDvAETe/wBI3/8ATd//AGPO
        7wBtzu8ATOb/AE7o/wBT4P8AVef/AFrh/wBQ6f8AX+z/AGLj/wBq5P8AZOv/AGrr/wBz5f8Ae+b/AH3v
        /wB28P8AhOf/AIPu/wCN6f8AjO//AJXq/wCH8v8AjvH/AJDz/wCW8v8AnPH/AJn1/wCf9P8Anfn/AKPy
        /wCk8/8Aofb/AKb1/wCp9f8As/z/ALz//wDD//8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAA////AAAAAAAAAAAAAAAAAAAAAAAABQMDAwMDAwMDAwMDAwUABQYUExAO
        CQkHBwcHBwcEBQMYFxQTEA4JBwcHBwcHBwMDHiAXFBMQAQEJBwcHEQwDAAMhGBcUEwEBCQcHBw0DAAAD
        IyMYFxQTEA4JCQ0MAwAAAAMjGxgXAQIQDgkRAwAAAAADJCQbGAEBExASDgMAAAAAAAMkHhsBARQTFQMA
        AAAAAAADKSkeAQEXGhYDAAAAAAAAAAMpHwEBGBkDAAAAAAAAAAADLCwfHiMbAwAAAAAAAAAAAAMtKSQl
        AwAAAAAAAAAAAAAFCy8uCgUAAAAAAAAAAAAAAAUDAwUAAAAAAAD//wAAgAEAAAAAAAAAAAAAAAAAAIAB
        AACAAQAAwAMAAMADAADgBwAA4AcAAPAPAADwDwAA+B8AAPgfAAD8PwAAKAAAABAAAAAgAAAAAQAIAAAA
        AAAAAQAAAAAAAAAAAAAAAQAAAAEAAAAAAAAcddIAIHvVACCe4AAyve8AY7vpAEjA7wBE3v8ASN//AE3f
        /wBjzu8Abc7vAEzm/wBO6P8AU+D/AFXn/wBa4f8AUOn/AF/s/wBi4/8AauT/AGTr/wBq6/8Ac+X/AHvm
        /wB97/8AdvD/AITn/wCD7v8Ajen/AIzv/wCV6v8Ah/L/AI7x/wCQ8/8AlvL/AJzx/wCZ9f8An/T/AJ35
        /wCj8v8ApPP/AKH2/wCm9f8AqfX/ALP8/wC8//8Aw///AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAP///wAAAAAAAAAAAAAAAAAAAAAAAAUDAwMDAwMDAwMDAwMFAAUG
        FBMQDgkJBwcHBwcHBAUDGBcUExAOCQcHBwcHBwcDAx4gFxQTEAEBCQcHBxEMAwADIRgXFBMBAQkHBwcN
        AwAAAyMjGBcUExAOCQkNDAMAAAADIxsYFwECEA4JEQMAAAAAAyQkGxgBARMQEg4DAAAAAAADJB4bAQEU
        ExUDAAAAAAAAAykpHgEBFxoWAwAAAAAAAAADKR8BARgZAwAAAAAAAAAAAywsHx4jGwMAAAAAAAAAAAAD
        LSkkJQMAAAAAAAAAAAAABQsvLgoFAAAAAAAAAAAAAAAFAwMFAAAAAAAA//8AAIABAAAAAAAAAAAAAAAA
        AACAAQAAgAEAAMADAADAAwAA4AcAAOAHAADwDwAA8A8AAPgfAAD4HwAA/D8AACgAAAAQAAAAIAAAAAEA
        IAAAAAAAQAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACCe4LIgnuD/IJ7g/yCe4P8gnuD/IJ7g/yCe
        4P8gnuD/IJ7g/yCe4P8gnuD/IJ7g/yCe4P8gnuCyAAAAACCe4LJIwO//auT//2Lj//9a4f//U+D//03f
        //9I3///RN7//0Te//9E3v//RN7//0Te//9E3v//Mr3v/yCe4LIgnuD/e+b//3Pl//9q5P//YuP//1rh
        //9T4P//Td///0jf//9E3v//RN7//0Te//9E3v//RN7//0Te//8gnuD/IJ7g/4zv//+H8v//c+X//2rk
        //9i4///WuH//xx10v8cddL/SN///0Te//9E3v//RN7//1Dq//9M5v//IJ7g/wAAAAAgnuD/jvH//3vm
        //9z5f//auT//2Lj//8cddL/HHXS/03f//9I3///RN7//0Te//9O6P//IJ7g/wAAAAAAAAAAIJ7g/5Xx
        //+Q8///e+b//3Pl//9q5P//YuP//1rh//9T4P//Td///0jf//9Q6v//TOb//yCe4P8AAAAAAAAAAAAA
        AAAgnuD/l/P//4Tn//975v//c+X//xx10v8ge9X/WuH//1Pg//9N3///Uun//yCe4P8AAAAAAAAAAAAA
        AAAAAAAAIJ7g/53y//+Z9f//hOf//3vm//8cddL/HHXS/2Lj//9a4f//X+z//1Xn//8gnuD/AAAAAAAA
        AAAAAAAAAAAAAAAAAAAgnuD/n/T//43p//+E5///HHXS/xx10v9q5P//YuP//2Tr//8gnuD/AAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAIJ7g/6Tz//+h9v//jen//xx10v8cddL/c+X//3bw//9q6///IJ7g/wAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgnuD/pvX//5Xq//8cddL/HHXS/3vm//997///IJ7g/wAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIJ7g/6v0//+o9///ler//43p//+Q8///g+7//yCe
        4P8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgnuD/s/z//6Py//+c8f//nfn//yCe
        4P8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIJ7gsm3O7//D////vP///2PO
        7/8gnuCyAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgnuCyIJ7g/yCe
        4P8gnuCyAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA//8AAIABAAAAAAAAAAAAAAAAAACAAQAAgAEAAMAD
        AADAAwAA4AcAAOAHAADwDwAA8A8AAPgfAAD4HwAA/D8AACgAAAAQAAAAIAAAAAEAIAAAAAAAQAQAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAACCe4LIgnuD/IJ7g/yCe4P8gnuD/IJ7g/yCe4P8gnuD/IJ7g/yCe
        4P8gnuD/IJ7g/yCe4P8gnuCyAAAAACCe4LJIwO//auT//2Lj//9a4f//U+D//03f//9I3///RN7//0Te
        //9E3v//RN7//0Te//9E3v//Mr3v/yCe4LIgnuD/e+b//3Pl//9q5P//YuP//1rh//9T4P//Td///0jf
        //9E3v//RN7//0Te//9E3v//RN7//0Te//8gnuD/IJ7g/4zv//+H8v//c+X//2rk//9i4///WuH//xx1
        0v8cddL/SN///0Te//9E3v//RN7//1Dq//9M5v//IJ7g/wAAAAAgnuD/jvH//3vm//9z5f//auT//2Lj
        //8cddL/HHXS/03f//9I3///RN7//0Te//9O6P//IJ7g/wAAAAAAAAAAIJ7g/5Xx//+Q8///e+b//3Pl
        //9q5P//YuP//1rh//9T4P//Td///0jf//9Q6v//TOb//yCe4P8AAAAAAAAAAAAAAAAgnuD/l/P//4Tn
        //975v//c+X//xx10v8ge9X/WuH//1Pg//9N3///Uun//yCe4P8AAAAAAAAAAAAAAAAAAAAAIJ7g/53y
        //+Z9f//hOf//3vm//8cddL/HHXS/2Lj//9a4f//X+z//1Xn//8gnuD/AAAAAAAAAAAAAAAAAAAAAAAA
        AAAgnuD/n/T//43p//+E5///HHXS/xx10v9q5P//YuP//2Tr//8gnuD/AAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAIJ7g/6Tz//+h9v//jen//xx10v8cddL/c+X//3bw//9q6///IJ7g/wAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAgnuD/pvX//5Xq//8cddL/HHXS/3vm//997///IJ7g/wAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAIJ7g/6v0//+o9///ler//43p//+Q8///g+7//yCe4P8AAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgnuD/s/z//6Py//+c8f//nfn//yCe4P8AAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIJ7gsm3O7//D////vP///2PO7/8gnuCyAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgnuCyIJ7g/yCe4P8gnuCyAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAA//8AAIABAAAAAAAAAAAAAAAAAACAAQAAgAEAAMADAADAAwAA4AcAAOAH
        AADwDwAA8A8AAPgfAAD4HwAA/D8AAA==
</value>
  </data>
</root>