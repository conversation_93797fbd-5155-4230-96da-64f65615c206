﻿Imports System.Text

Public Class LookupForm

    Private _GridController As GridManager
    Private WithEvents Grid As DataGridView

#Region "Properties"

    Public WriteOnly Property GridController() As GridManager
        Set(ByVal value As GridManager)
            _GridController = value
            Grid = value.Grid
        End Set
    End Property

#End Region

#Region "Public Shared Methods"

    Public Shared Function SelectRows(ByVal Selector As LookupForm, ByVal ErrorMessage As String) As List(Of DataRow)

        ' Create an empty list of columns to hide to pass into the function.
        Dim EmptyList As New List(Of String)

        ' Return the result of the default function.
        Return SelectRows(Selector, ErrorMessage, EmptyList)

    End Function

    Public Shared Function SelectRows _
    (ByVal Selector As LookupForm, ByVal ErrorMessage As String, ByVal ColumnsToHide As List(Of String)) As List(Of DataRow)

        ' The list that this function will return.
        Dim SelectedDataRows As New List(Of DataRow)

        ' Return an empty list if any errors occured while collecting data needed for the lookup form.
        If String.IsNullOrEmpty(ErrorMessage) = False Then

            Selector.ShowMessage(ErrorMessage, "Ooops!", MessageBoxIcon.Error)
            Return SelectedDataRows

        Else

            ' Hide the colums specified in the given ColumnsToHide list.
            For i As Integer = 0 To ColumnsToHide.Count - 1
                Selector.Grid.Columns(ColumnsToHide(i)).Visible = False
            Next

            ' Display the form.
            Selector.ShowDialog()

            ' Add all selected rows to the list if user clicked the OK button.
            If Selector.DialogResult = Windows.Forms.DialogResult.OK Then
                For Each SelectedGridRow As DataGridViewRow In Selector.Grid.SelectedRows
                    Dim SelectedRowView As DataRowView = SelectedGridRow.DataBoundItem
                    If IsNothing(SelectedRowView) = False Then
                        SelectedDataRows.Add(SelectedRowView.Row)
                    End If
                Next
            End If

            ' Return the list of selected rows.
            Return SelectedDataRows

        End If

    End Function

#End Region

#Region "Event Handlers"

    Private Sub LookupForm_Load(sender As Object, e As System.EventArgs) Handles Me.Load

        ' Find the SearchBox on this form.
        Dim SearchBox As TextEdit = Nothing
        FindSearchBox(Me, SearchBox)

        ' If a search box was found, give it focus.
        If Not IsNothing(SearchBox) Then
            SearchBox.TabIndex = 0
        End If

    End Sub

    Private Sub ButtonOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonOK.Click
        ' Check that the user selected at least one row.
        If Grid.SelectedRows.Count = 0 Then
            ShowMessage("Please select at least one row before clicking the OK button.", "Selection Required")
            Exit Sub
        End If
        ' Set the form's dialog result to OK and close the form.
        Me.DialogResult = Windows.Forms.DialogResult.OK
        Me.Close()
    End Sub

    Private Sub ButtonCancel_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles ButtonCancel.Click
        Me.DialogResult = Windows.Forms.DialogResult.Cancel
        Me.Close()
    End Sub

    Private Sub Grid_CellDoubleClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellEventArgs) Handles Grid.CellDoubleClick
        ' Click the OK button.
        ButtonOK_Click(sender, e)
    End Sub

#End Region

#Region "Private Methods"

    Private Sub FindSearchBox(Container As Control, ByRef SearchBox As TextEdit)
        ' Find the TextEdit with the name of "TextEditSearch" and assign it as the value of the SearchBox argument.

        For Each ChildControl As Control In Container.Controls
            If String.Compare(ChildControl.Name, "TextEditSearch") = 0 Then
                ' Search box has been found. Assign it to the given argument reference.
                If TypeOf ChildControl Is TextEdit Then
                    SearchBox = ChildControl
                    Exit For
                End If
            ElseIf ChildControl.Controls.Count > 0 Then
                ' This control contains other controls (possibly including the SearchBox that we're looking for.
                ' Check all child controls of this control for the SearchBox.
                FindSearchBox(ChildControl, SearchBox)
            End If
        Next

    End Sub

#End Region

End Class