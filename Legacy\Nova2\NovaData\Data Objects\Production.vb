Public Class Production
    Inherits OldBaseObject

    Private ConsumingForm As LiquidShell.BaseForm
    Private ParentContract As OldContract

#Region "Fields"

    Private _ItemQtyID As Integer
    Private _BrandID As Guid
    Private _CostPrice As Decimal = 0
    Private _SellPrice As Decimal = 0
    Private _Notes As String = String.Empty

    Private _ItemName As String = "Select..."
    Private _ItemQty As Integer = 0
    Private _ItemID As Integer
    Private _BrandName As String = "Select..."

#End Region

#Region "Database Column Properties"

    Public ReadOnly Property ItemQtyID() As Integer
        Get
            Return _ItemQtyID
        End Get
    End Property

    Public ReadOnly Property BrandID() As Guid
        Get
            Return _BrandID
        End Get
    End Property

    Public ReadOnly Property BrandName() As String
        Get
            Return _BrandName
        End Get
    End Property

    Public ReadOnly Property CostPrice() As Decimal
        Get
            Return _CostPrice
        End Get
    End Property

    Public Property SellPrice() As Decimal
        Get
            Return _SellPrice
        End Get
        Set(ByVal value As Decimal)
            _SellPrice = value
            IsDirty = True
        End Set
    End Property

    Public Property Notes() As String
        Get
            Return _Notes
        End Get
        Set(ByVal value As String)
            ' Clean the proposed new value.
            value = value.Trim
            _Notes = value
            IsDirty = True
        End Set
    End Property

#End Region

#Region "Custom Properties"

    Public WriteOnly Property SelectedInventory() As DataRow
        Set(ByVal value As DataRow)
            _ItemID = value("ItemID")
            _ItemName = value("ItemName")
        End Set
    End Property

    Public WriteOnly Property SelectedQty() As DataRow
        Set(ByVal value As DataRow)
            _ItemQtyID = value("ItemQtyID")
            _ItemQty = value("ItemQty")
            _CostPrice = value("CostPrice")
            _SellPrice = value("SellPrice")
            IsDirty = True
        End Set
    End Property

    Public WriteOnly Property SelectedBrand() As DataRow
        Set(ByVal value As DataRow)
            ' Update relatd variables.
            _BrandID = value("BrandID")
            _BrandName = value("BrandName")
            ' Flag the object as dirty.
            IsDirty = True
        End Set
    End Property

    Public ReadOnly Property ItemID() As Integer
        Get
            Return _ItemID
        End Get
    End Property

    Public ReadOnly Property ItemName() As String
        Get
            Return _ItemName
        End Get
    End Property

    Public ReadOnly Property ItemQty() As String
        Get
            If _ItemQty = 0 Then
                Return "Select..."
            Else
                Return _ItemQty.ToString
            End If
        End Get
    End Property

    Public ReadOnly Property ProductionTitle() As String
        Get
            Dim Separator As String = " / "
            Dim ReturnString As New System.Text.StringBuilder(ParentContract.ContractNumber & Separator)
            If String.Compare(_ItemName, "Select...") = 0 Then
                ReturnString.Append("(new production)")
            Else
                ReturnString.Append(_ItemName & " Production")
            End If
            Return ReturnString.ToString
        End Get
    End Property

#End Region

#Region "Public Methods"

    Public Shared Function GetRowDescription(ByVal Row As DataRow, ByVal ParentContractNumber As String) As String
        If Row.RowState = DataRowState.Detached AndAlso IsDBNull(Row("ItemQtyID")) Then
            Return "New production for " & ParentContractNumber
        Else
            Return CInt(Row("ItemQty")).ToString & " of " & Row("ItemName") & " for " & Row("BrandName") & " in " & ParentContractNumber
        End If
    End Function

    Public Sub New _
    (ByVal Parent As OldContract, _
    ByVal AddNew As Boolean, _
    ByVal Constring As String)

        ' Create a new row if this is not an existing row that the user is modifying.
        If AddNew Then
            Parent.ProductionBindingSource.AddNew()
        End If

        ' Update variables.
        ParentContract = Parent
        AuditLog = ParentContract.AuditLog
        ConsumingForm = ParentContract.ConsumingForm
        ConnectionString = Constring
        DataBindingSource = ParentContract.ProductionBindingSource

        ' Create a description for this burst to use in the audit log.
        _RowDescription = GetRowDescription(Row, ParentContract.ContractNumber)

        InitializeFields()

    End Sub

    Public Sub SaveToDataSet()

        ' Update all columns that do not require an audit log entry.
        Row("ItemQtyID") = _ItemQtyID
        Row("ItemID") = _ItemID
        Row("BrandID") = _BrandID

        ' Update all columns that require an audit log entry.
        If Not Object.Equals(Row("ItemName"), _ItemName) Then
            AddLog(Row, RowDescription, "ItemName", Row("ItemName").ToString, _ItemName.ToString, AuditLog)
            Row("ItemName") = _ItemName
        End If
        If Not Object.Equals(Row("BrandName"), _BrandName) Then
            AddLog(Row, RowDescription, "BrandName", Row("BrandName").ToString, _BrandName.ToString, AuditLog)
            Row("BrandName") = _BrandName
        End If
        If Not Object.Equals(Row("ItemQty"), _ItemQty) Then
            AddLog(Row, RowDescription, "ItemQty", Row("ItemQty").ToString, _ItemQty.ToString, AuditLog)
            Row("ItemQty") = _ItemQty
        End If
        If Not Object.Equals(Row("Notes"), _Notes) Then
            AddLog(Row, RowDescription, "Notes", Row("Notes").ToString, _Notes.ToString, AuditLog)
            Row("Notes") = _Notes
        End If
        If Not Object.Equals(Row("SellPrice"), _SellPrice) Then
            AddLog(Row, RowDescription, "SellPrice", CDec(Row("SellPrice")).ToString("c"), _SellPrice.ToString("c"), AuditLog)
            Row("SellPrice") = _SellPrice
        End If

        ' If this is a new row being created, add only one entry for the creation of the object (as opposed to
        ' adding entries for every modified property of this object).
        If Row.RowState = DataRowState.Detached Then
            _RowDescription = GetRowDescription(Row, ParentContract.ContractNumber)
            LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, "Contract Production", RowDescription, "Created")
        End If

        ' Remember the current item in the binding source list.
        Dim CurrentItem As Object = DataBindingSource.Current
        ' Commit changes to the dataset.
        DataBindingSource.EndEdit()     ' This line changes the binding source's current item.
        ' Restore the current item in the binding source list.
        DataBindingSource.Position = DataBindingSource.IndexOf(CurrentItem)

        ' Update production on the parent contract.
        ParentContract.ProductionUpdated()

    End Sub

    Public Sub RejectChanges()

        ' Remember the current item in the binding source list.
        Dim CurrentItem As Object = DataBindingSource.Current
        ' Commit changes to the dataset.
        DataBindingSource.CancelEdit()    ' This line changes the binding source's current item.
        ' Restore the current item in the binding source list.
        DataBindingSource.Position = DataBindingSource.IndexOf(CurrentItem)

    End Sub

#End Region

#Region "Private Methods"

    Private Sub InitializeFields()

        ' Stop if this is a new row being added.
        If Row.RowState = DataRowState.Detached Then
            Row("ContractItemQtyID") = Guid.NewGuid
            Exit Sub
        End If

        ' Initialize field values to row values.
        _ItemQtyID = Row("ItemQtyID")
        _ItemID = Row("ItemID")
        _ItemName = Row("ItemName")
        _ItemQty = Row("ItemQty")
        _BrandID = Row("BrandID")
        _BrandName = Row("BrandName")
        _Notes = Row("Notes")
        _SellPrice = Row("SellPrice")

        ' Gather some info needed to fetch the cost price of the item quantity.
        Dim ContractDate As String = LiquidShell.LiquidAgent.GetSqlFriendlyDate(ParentContract.ContractDate)
        Dim SQLStatement As String = "SELECT ISNULL(CostPrice, 0) AS CostPrice " _
                                     & "FROM Ops.vInventoryQtyPriceDates AS vInventoryQtyPriceDates_1 " _
                                     & "WHERE ([From] <= '" & ContractDate & "') " _
                                     & "AND ([To] >= '" & ContractDate & "' OR [To] IS NULL) " _
                                     & "AND (ItemQtyID = " & _ItemQtyID.ToString & ")"
        Dim Errors As String = String.Empty

        ' Fetch the cost price of the item quantity.
        _CostPrice = CDec(LiquidShell.LiquidAgent.GetSqlScalarValue(ConnectionString, SQLStatement, Errors))

    End Sub

    Private Shared Shadows Sub AddLog _
    (ByVal AuditRow As DataRow, _
    ByVal RowDescription As String, _
    ByVal ChangedPropertyName As String, _
    ByVal ChangedPropertyOldValue As String, _
    ByVal ChangedPropertyNewValue As String, _
    ByVal AuditLog As DataTable)
        ' Add an entry into the audit log table for a modified row.

        ' Exit if the row being audited is a new detached row.
        If AuditRow.RowState = DataRowState.Detached Then
            Exit Sub
        End If

        ' Create a string builder to build the log description.
        Dim ActionBuilder As New System.Text.StringBuilder("Changed the value of " & ChangedPropertyName.ToUpper & " from ")

        ' Add the old value to the string builder.
        If String.IsNullOrEmpty(ChangedPropertyOldValue) Then
            ActionBuilder.Append("an empty string")
        Else
            ActionBuilder.Append("'" & ChangedPropertyOldValue & "'")
        End If
        ActionBuilder.Append(" to ")

        ' Add the new value to the string builder.
        If String.IsNullOrEmpty(ChangedPropertyNewValue) Then
            ActionBuilder.Append("an empty string")
        Else
            ActionBuilder.Append("'" & ChangedPropertyNewValue & "'")
        End If

        ' Log the entry.
        LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, "Contract Production", RowDescription, ActionBuilder.ToString)

    End Sub

#End Region

End Class
