﻿using Framework.Controls.GridSystem;
using System;
using System.Collections.Generic;
using System.Data;
using System.Windows.Forms;

namespace Framework.Surfaces
{
    public partial class GridSurface : Surface
    {
        private IGridFiller GridFiller;
        public bool AutoFillTheGridOnLoad = false;
        private ExcelExporter Exporter;
        private string RowTitleColumnName = string.Empty;
        private List<ITabContentSurfaceFactory> ListOfContentFactoriesForTabsInTheSelectedRow;
        protected DataRow ParentRow;
        protected object ParentRowId;
        private object DeleteRowsMethod;
        private ContextMenuStrip EditMenu;


        #region Startup

        public GridSurface()
        {
            // This constructor should not be used in code. It exists only to service the designer of derived classes.
            InitializeComponent();
        }

        public GridSurface
            (
            IGridFiller gridfiller,
            ExcelExporter exporter
            )
        {
            InitializeComponent();
            Construct(gridfiller, string.Empty, null, exporter, null, null, null, null);
        }

        public GridSurface
            (
            IGridFiller gridfiller,
            ExcelExporter exporter,
            Func<List<DataRow>, string> deleterowsmethod
            )
        {
            InitializeComponent();
            Construct(gridfiller, string.Empty, null, exporter, deleterowsmethod, null, null, null);
        }

        public GridSurface
            (
            IGridFiller gridfiller,
            ExcelExporter exporter,
            Func<List<DataRow>, object, string> deleterowsmethod,
            object parentrowid
            )
        {
            InitializeComponent();
            Construct(gridfiller, string.Empty, null, exporter, deleterowsmethod, null, parentrowid, null);
        }

        public GridSurface
            (
            IGridFiller gridfiller,
            ExcelExporter exporter,
            Func<List<DataRow>, object, string> deleterowsmethod,
            DataRow parentrow,
            object parentrowid
            )
        {
            InitializeComponent();
            Construct(gridfiller, string.Empty, null, exporter, deleterowsmethod, parentrow, parentrowid, null);
        }

        public GridSurface
            (
            IGridFiller gridfiller,
            ExcelExporter exporter,
            Func<List<DataRow>, string> deleterowsmethod,
            string rowtitlecolumnname,
            List<ITabContentSurfaceFactory> listofcontentfactoriesfortabsintheselectedrow
            )
        {
            InitializeComponent();
            Construct(gridfiller, rowtitlecolumnname, listofcontentfactoriesfortabsintheselectedrow, exporter, deleterowsmethod, null, null, null);
        }

        public GridSurface
            (
            IGridFiller gridfiller,
            ExcelExporter exporter,
            Func<List<DataRow>, string> deleterowsmethod,
            string rowtitlecolumnname,
            List<ITabContentSurfaceFactory> listofcontentfactoriesfortabsintheselectedrow,
            ContextMenuStrip editmenu
            )
        {
            InitializeComponent();
            Construct(gridfiller, rowtitlecolumnname, listofcontentfactoriesfortabsintheselectedrow, exporter, deleterowsmethod, null, null, editmenu);
        }

        protected void Construct
            (
            IGridFiller gridfiller,
            string rowtitlecolumnname,
            List<ITabContentSurfaceFactory> listofcontentfactoriesfortabsintheselectedrow,
            ExcelExporter exporter,
            object deleterowsmethod,
            DataRow parentrow,
            object parentrowid,
            ContextMenuStrip editmenu
            )
        {
            GridFiller = gridfiller;
            gridfiller.Grid = thegrid;
            flatButtonDelete.Text = DeleteButtonText;
            RowTitleColumnName = rowtitlecolumnname;
            ListOfContentFactoriesForTabsInTheSelectedRow = listofcontentfactoriesfortabsintheselectedrow;
            Exporter = exporter;
            DeleteRowsMethod = deleterowsmethod;
            ParentRow = parentrow;
            ParentRowId = parentrowid;
            EditMenu = editmenu;
            SubscribeToEvents();
            InitializeEditMenu();
        }

        private void SubscribeToEvents()
        {
            thegrid.VisibleChanged += Thegrid_VisibleChanged;
            thegrid.GridFilterChanged += Thegrid_GridFilterChanged;
            thegrid.RowDoubleClick += Thegrid_RowDoubleClick;
            GridFiller.GridFillAttemptStarted += GridFiller_GridFillAttemptStarted;
            GridFiller.GridFillAttemptCompleted += GridFiller_GridFillAttemptCompleted;
            flatButtonSearch.Click += FlatButtonSearch_Click;
            flatButtonExport.Click += FlatButtonExport_Click;
            flatButtonEdit.Click += FlatButtonEdit_Click;
            flatButtonNew.Click += FlatButtonNew_Click;
            flatButtonDelete.Click += FlatButtonDelete_Click;
            Load += GridSurface_Load;
            thegrid.DataSourceChanged += Thegrid_DataSourceChanged;
            thegrid.SelectionChanged += Thegrid_SelectionChanged;
            if (EditMenu != null)
            {
                EditMenu.SizeChanged += EditMenu_SizeChanged;
            }
        }

        private void InitializeEditMenu()
        {
            if (EditMenu != null)
            {
                EditMenu.ShowImageMargin = false;
                for (int i = 0; i < EditMenu.Items.Count; i++)
                {
                    EditMenu.Items[i].Tag = thegrid;
                }
            }
        }

        private void Thegrid_SelectionChanged(object sender, EventArgs e)
        {
            UpdateEditButtonText();
        }

        private void Thegrid_DataSourceChanged(object sender, EventArgs e)
        {
            if (thegrid.DataSource != null)
            {
                thegrid.DataSource.RowChanged += DataSource_RowChanged;
            }
        }

        private void DataSource_RowChanged(object sender, DataRowChangeEventArgs e)
        {
            if ((e.Action == DataRowAction.Add && thegrid.Rows.Count == 1) || e.Action == DataRowAction.Commit && thegrid.Rows.Count == 0)
            {
                UpdateGridVisibility();
            }
        }

        private void GridSurface_Load(object sender, EventArgs e)
        {
            if (AutoFillTheGridOnLoad)
            {
                FillTheGrid();
            }
            UpdateNewButtonVisibility();
            UpdateAddButtonVisibility();
            UpdateEditButtonVisibility();
            UpdateDeleteButtonVisibility();
        }

        private void FlatButtonSearch_Click(object sender, EventArgs e)
        {
            FillTheGrid();
        }

        #endregion


        #region Properties

        private string _DeleteButtonText = "Delete";
        public string DeleteButtonText
        {
            get { return _DeleteButtonText; }
            set
            {
                if (_DeleteButtonText != value)
                {
                    _DeleteButtonText = value;
                    flatButtonDelete.Text = DeleteButtonText;
                }
            }
        }

        #endregion


        #region Fill the grid with data

        private void FillTheGrid()
        {
            GridFiller.Fill();
            UpdateGridVisibility();
        }

        private void GridFiller_GridFillAttemptStarted(object sender, EventArgs e)
        {
            flatButtonSearch.StartAnimation();
        }

        private void GridFiller_GridFillAttemptCompleted(object sender, EventArgs e)
        {
            flatButtonSearch.StopAnimation();
        }

        #endregion


        #region Appearance

        private void Thegrid_VisibleChanged(object sender, EventArgs e)
        {
            UpdateSearchButtonText();
            UpdateExportButtonVisibility();
            UpdateEditButtonVisibility();
            UpdateDeleteButtonVisibility();
        }

        private void Thegrid_GridFilterChanged(object sender, EventArgs e)
        {
            UpdateExportButtonVisibility();
            UpdateEditButtonVisibility();
            UpdateDeleteButtonVisibility();
        }

        private bool _ShowNewButton = true;
        public bool ShowNewButton
        {
            get { return _ShowNewButton; }
            set
            {
                _ShowNewButton = value;
            }
        }

        private bool _ShowAddButton = false;
        public bool ShowAddButton
        {
            get { return _ShowAddButton; }
            set
            {
                _ShowAddButton = value;
            }
        }

        private bool _ShowEditButton = true;
        public bool ShowEditButton
        {
            get { return _ShowEditButton; }
            set
            {
                _ShowEditButton = value;
            }
        }

        private bool _ShowDeleteButton = true;
        public bool ShowDeleteButton
        {
            get { return _ShowDeleteButton; }
            set
            {
                _ShowDeleteButton = value;
            }
        }

        private bool _ShowTrashButton = true;
        public bool ShowTrashButton
        {
            get { return _ShowTrashButton; }
            set
            {
                _ShowTrashButton = value;
            }
        }

        private void UpdateGridVisibility()
        {
            thegrid.Visible = thegrid.DataSource != null && thegrid.DataSource.Rows.Count > 0;
        }

        private void UpdateNewButtonVisibility()
        {
            flatButtonNew.Visible = ShowNewButton;
        }

        private void UpdateSearchButtonText()
        {
            flatButtonSearch.Text = thegrid.Visible ? "Refresh" : "Search";
        }

        private void UpdateExportButtonVisibility()
        {
            flatButtonExport.Visible = (thegrid.Visible && thegrid.Rows.Count > 0);
        }

        private void UpdateAddButtonVisibility()
        {
            flatButtonAdd.Visible = ShowAddButton;
            UpdateBottomButtonPanelHeight();
            UpdateAddButtonPanelWidth();
        }

        private void UpdateEditButtonVisibility()
        {
            flatButtonEdit.Visible = ShowEditButton && thegrid.Visible && thegrid.SelectedRows.Count > 0;
            UpdateBottomButtonPanelHeight();
            UpdateEditButtonPanelWidth();
        }

        private void UpdateDeleteButtonVisibility()
        {
            flatButtonDelete.Visible = ShowDeleteButton && thegrid.Visible && thegrid.SelectedRows.Count > 0;
            UpdateBottomButtonPanelHeight();
        }

        private void UpdateTrashButtonVisibility()
        {
            flatButtonTrash.Visible = ShowTrashButton && thegrid.Visible;
            UpdateBottomButtonPanelHeight();
        }

        private void UpdateAddButtonPanelWidth()
        {
            if (flatButtonAdd.Visible)
            {
                panelAddButton.Width = 200;
            }
            else
            {
                panelAddButton.Width = 0;
            }
        }

        private void UpdateEditButtonPanelWidth()
        {
            if (flatButtonEdit.Visible)
            {
                panelEditButton.Width = 200;
            }
            else
            {
                panelEditButton.Width = 0;
            }
        }

        private void UpdateBottomButtonPanelHeight()
        {
            if (flatButtonEdit.Visible || flatButtonDelete.Visible || flatButtonAdd.Visible)
            {
                tableLayoutPanelBottomButtons.Height = 80;
            }
            else
            {
                tableLayoutPanelBottomButtons.Height = 0;
            }
        }

        private void UpdateEditButtonText()
        {
            int selectedrows = thegrid.SelectedRows.Count;
            flatButtonEdit.Text = selectedrows > 1 ? selectedrows.ToString() + " rows selected" : "Edit";
        }

        #endregion


        #region Excel export

        private void FlatButtonExport_Click(object sender, EventArgs e)
        {
            Exporter.Export(thegrid);
        }

        #endregion

        #region Edit rows

        private void FlatButtonEdit_Click(object sender, EventArgs e)
        {
            EditRow();
        }

        private void Thegrid_RowDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (flatButtonEdit.Visible)
            {
                EditRow();
            }
        }

        private void EditRow()
        {
            if (thegrid.SelectedRows.Count == 1)
            {
                var rowsurface = new RowSurface(thegrid.BindingSource, RowTitleColumnName, ListOfContentFactoriesForTabsInTheSelectedRow);
                ParentTab.DisplayContent(rowsurface);
            }
            else
            {
                if (thegrid.SelectedRows.Count > 1)
                {
                    DisplayEditMenu();
                }
            }
        }

        private void EditMenu_SizeChanged(object sender, EventArgs e)
        {
            DisplayEditMenu();
        }

        private void DisplayEditMenu()
        {
            // This method is called in the EditRow method AND the EditMenu_SizeChanged eventhandler because the first
            // time the edit menu is displayed, the size is incorrect, meaning the position is incorrect.
            EditMenu?.Show(flatButtonEdit, new System.Drawing.Point(0, -EditMenu.Height));
        }

        #endregion


        #region Create row

        private void FlatButtonNew_Click(object sender, EventArgs e)
        {
            CreateRow();
        }

        private void CreateRow()
        {
            thegrid.BindingSource.AddNew();
            var rowsurface = new RowSurface(thegrid.BindingSource, RowTitleColumnName, ListOfContentFactoriesForTabsInTheSelectedRow);
            ParentTab.DisplayContent(rowsurface);
        }

        #endregion


        #region Add rows

        protected void AddSelectedRowsToTheGrid(List<DataRow> selectedrows)
        {
            if (selectedrows != null)
            {
                DataTable table = thegrid.DataSource;
                foreach (DataRow selectedrow in selectedrows)
                {
                    DataRow newrow = table.NewRow();
                    foreach (DataColumn column in table.Columns)
                    {
                        newrow[column.ColumnName] = selectedrow[column.ColumnName];
                    }
                    table.Rows.Add(newrow);
                }
            }
        }

        #endregion


        #region Delete rows

        private void FlatButtonDelete_Click(object sender, EventArgs e)
        {
            DeleteRows();
        }

        private void DeleteRows()
        {
            if (DeleteRowsMethod != null)
            {
                var rowstodelete = thegrid.SelectedDataRows;

                if (rowstodelete.Count > 0)
                {
                    string deleteconfirmationprompt = "Are you sure you want to " + DeleteButtonText.ToLower() + " the selected rows?";
                    DialogResult deleteconfirmed = MessageForm.Show
                        (deleteconfirmationprompt, DeleteButtonText + " Confirmation", MessageForm.MessageFormButtons.YesNo);
                    if (deleteconfirmed == DialogResult.Yes)
                    {
                        string errormessage = "I haven't been taught how to delete rows of this type. Please report this problem.";
                        if (DeleteRowsMethod is Func<List<DataRow>, string>)
                        {
                            errormessage = ((Func<List<DataRow>, string>)DeleteRowsMethod).Invoke(rowstodelete);
                        }
                        else
                        {
                            if (DeleteRowsMethod is Func<List<DataRow>, object, string> && ParentRowId != null)
                            {
                                errormessage = ((Func<List<DataRow>, object, string>)DeleteRowsMethod).Invoke(rowstodelete, ParentRowId);
                            }
                        }

                        if (string.IsNullOrEmpty(errormessage) == false)
                        {
                            MessageForm.Show(errormessage, "Row Delete Failed");
                        }
                        else
                        {
                            foreach (DataRow rowtodelete in rowstodelete)
                            {
                                rowtodelete.Delete();
                            }
                            thegrid.DataSource.AcceptChanges();
                            UpdateGridVisibility();
                        }
                    }
                }
            }
        }

        #endregion

    }
}
