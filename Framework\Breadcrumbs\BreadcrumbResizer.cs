﻿using System.Drawing;
using System.Windows.Forms;

namespace Framework.Breadcrumbs
{
    public class BreadcrumbResizer
    {
        public void Resize(Panel breadcrumbbuttoncontainer, int defaultbreadcrumbbuttonwidth)
        {
            // Check if all buttons can fit into the panel with the default button width.
            int numberofbuttons = breadcrumbbuttoncontainer.Controls.Count;
            int spaceavailable = breadcrumbbuttoncontainer.Width;
            int spaceneededfordefaultwidthbuttons = numberofbuttons * defaultbreadcrumbbuttonwidth;

            int buttonwidth = 0;
            if (spaceneededfordefaultwidthbuttons <= spaceavailable)
            {
                // There is enough space for the buttons at the default width.
                buttonwidth = defaultbreadcrumbbuttonwidth;
            }
            else
            {
                // There is insufficient space for all the buttons. The width of the buttons needs to be reduced.
                buttonwidth = spaceavailable / numberofbuttons;
            }

            // The required width of the buttons has been determined. Set the width now.
            for (int i = 0; i < numberofbuttons; i++)
            {
                breadcrumbbuttoncontainer.Controls[i].Width = buttonwidth;
            }

            // All the buttons are now the correct size. Now they must be placed correctly.
            for (int i = 0; i < numberofbuttons; i++)
            {
                int x = buttonwidth * i;
                int y = breadcrumbbuttoncontainer.Controls[i].Location.Y;
                breadcrumbbuttoncontainer.Controls[i].Location = new Point(x, y);
            }
        }
    }
}
