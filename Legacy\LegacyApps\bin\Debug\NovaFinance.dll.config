<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="NovaFinance.My.MySettings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false"/>
        </sectionGroup>
    </configSections>
    <connectionStrings>
        <add name="NovaFinance.My.MySettings.NovaDBConnectionString" connectionString="Data Source=primenovasql\Live;Initial Catalog=NovaDB;User ID=NovaApp;Connect Timeout=300" providerName="System.Data.SqlClient"/>
        <add name="NovaFinance.My.MySettings.NovaDBConnectionString1" connectionString="Data Source=primenovasql\Live;Initial Catalog=NovaDB;User ID=NovaApp;Connect Timeout=120" providerName="System.Data.SqlClient"/>
    </connectionStrings>
    <startup>
        <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.1"/>
    </startup>
    <userSettings>
        <NovaFinance.My.MySettings>
            <setting name="NovaFinanceConnectionString" serializeAs="String">
                <value>Data Source=primenovasql\Live;Initial Catalog=NovaDB;User ID=NovaApp;Connect Timeout=120</value>
            </setting>
        </NovaFinance.My.MySettings>
    </userSettings>
</configuration>
