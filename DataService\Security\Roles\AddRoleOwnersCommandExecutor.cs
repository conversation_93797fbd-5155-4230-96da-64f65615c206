﻿using DataAccess;

namespace DataService.Security
{
    class AddRoleOwnersCommandExecutor : CommandExecutor<AddRoleOwnersCommand>
    {

        public override void Execute(AddRoleOwnersCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.AddRoleOwners))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.AddInputParameter("roleid", command.RoleId);
                storedprocedure.AddInputParameter("newowners", command.NewOwners);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
            }
        }

    }
}
