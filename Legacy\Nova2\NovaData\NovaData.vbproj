﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="12.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.50727</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{E31612D1-25F0-4999-9793-109A9EC3CBD8}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>NovaData</RootNamespace>
    <AssemblyName>NovaData</AssemblyName>
    <MyType>Windows</MyType>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <OldToolsVersion>2.0</OldToolsVersion>
    <UpgradeBackupLocation />
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>NovaData.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>NovaData.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevExpress.Utils.v12.2, Version=12.2.18.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\..\..\..\..\..\..\..\..\Devexpress\Framework\DevExpress.Utils.v12.2.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Windows.Forms">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Drawing" />
    <Import Include="System.Windows.Forms" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BaseObject.vb" />
    <Compile Include="Data Objects\BrandCategory.vb" />
    <Compile Include="Data Objects\MediaChannelGroup.vb" />
    <Compile Include="Data Objects\MediaGroup.vb" />
    <Compile Include="Data Objects\StoreGroups.vb" />
    <Compile Include="DataSets\DataSetBrandCategory.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DataSetBrandCategory.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\DataSetBrandFamily.vb">
      <DependentUpon>DataSetBrandFamily.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\DataSetBudgets.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DataSetBudgets.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\DataSetBudgets.vb">
      <DependentUpon>DataSetBudgets.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\DataSetCategory.vb">
      <DependentUpon>DataSetCategory.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\DataSetClient.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DataSetClient.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\DataSetClient.vb">
      <DependentUpon>DataSetClient.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\DataSetContractDate.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DataSetContractDate.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\DataSetContractDate.vb">
      <DependentUpon>DataSetContractDate.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\DataSetContractInfo.vb">
      <DependentUpon>DataSetContractInfo.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\DataSetStoreGroups.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DataSetStoreGroups.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\DataSetStoreGroups.vb">
      <DependentUpon>DataSetStoreGroups.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\DataSetMediaGap.vb">
      <DependentUpon>DataSetMediaGap.xsd</DependentUpon>
    </Compile>
    <Compile Include="OldBaseObject.vb" />
    <Compile Include="Data Objects\AccountManager.vb" />
    <Compile Include="Data Objects\BrandFamily.vb" />
    <Compile Include="Data Objects\Burst.vb" />
    <Compile Include="Data Objects\Category.vb" />
    <Compile Include="Data Objects\Brand.vb" />
    <Compile Include="Data Objects\Contract.vb" />
    <Compile Include="Data Objects\OldContract.vb" />
    <Compile Include="Data Objects\MediaGap.vb" />
    <Compile Include="Data Objects\MiscellaneousCharge.vb" />
    <Compile Include="Data Objects\Production.vb" />
    <Compile Include="DataSets\DataSetAudit.vb">
      <DependentUpon>DataSetAudit.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\DataSetBrand.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DataSetBrand.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\DataSetBrand.vb">
      <DependentUpon>DataSetBrand.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\DataSetBrandFamily.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DataSetBrandFamily.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\DataSetContract.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DataSetContract.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\DataSetContract.vb">
      <DependentUpon>DataSetContract.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\DataSetContractInfo.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DataSetContractInfo.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\DataSetMediaGap.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DataSetMediaGap.xsd</DependentUpon>
    </Compile>
    <Compile Include="Settings.vb" />
    <Compile Include="DataSets\DataSetAccountManager.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DataSetAccountManager.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\DataSetAccountManager.vb">
      <DependentUpon>DataSetAccountManager.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\DataSetAudit.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DataSetAudit.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\DataSetCategory.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DataSetCategory.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\DataSetMedia.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DataSetMedia.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\DataSetMedia.vb">
      <DependentUpon>DataSetMedia.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\DataSetSetting.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DataSetSetting.xsd</DependentUpon>
    </Compile>
    <Compile Include="DataSets\DataSetSetting.vb">
      <DependentUpon>DataSetSetting.xsd</DependentUpon>
    </Compile>
    <Compile Include="Lookup.vb" />
    <Compile Include="Data Objects\Media.vb" />
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="My Project\licenses.licx" />
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="DataSets\DataSetAccountManager.xsc">
      <DependentUpon>DataSetAccountManager.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetAccountManager.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DataSetAccountManager.Designer.vb</LastGenOutput>
    </None>
    <None Include="DataSets\DataSetAccountManager.xss">
      <DependentUpon>DataSetAccountManager.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetAudit.xsc">
      <DependentUpon>DataSetAudit.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetAudit.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DataSetAudit.Designer.vb</LastGenOutput>
    </None>
    <None Include="DataSets\DataSetAudit.xss">
      <DependentUpon>DataSetAudit.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetBrand.xsc">
      <DependentUpon>DataSetBrand.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetBrand.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DataSetBrand.Designer.vb</LastGenOutput>
    </None>
    <None Include="DataSets\DataSetBrand.xss">
      <DependentUpon>DataSetBrand.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetBrandCategory.xsc">
      <DependentUpon>DataSetBrandCategory.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetBrandCategory.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DataSetBrandCategory.Designer.vb</LastGenOutput>
    </None>
    <None Include="DataSets\DataSetBrandCategory.xss">
      <DependentUpon>DataSetBrandCategory.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetBrandFamily.xsc">
      <DependentUpon>DataSetBrandFamily.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetBrandFamily.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DataSetBrandFamily.Designer.vb</LastGenOutput>
    </None>
    <None Include="DataSets\DataSetBrandFamily.xss">
      <DependentUpon>DataSetBrandFamily.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetBudgets.xsc">
      <DependentUpon>DataSetBudgets.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetBudgets.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DataSetBudgets.Designer.vb</LastGenOutput>
    </None>
    <None Include="DataSets\DataSetBudgets.xss">
      <DependentUpon>DataSetBudgets.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetCategory.xsc">
      <DependentUpon>DataSetCategory.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetCategory.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DataSetCategory.Designer.vb</LastGenOutput>
    </None>
    <None Include="DataSets\DataSetCategory.xss">
      <DependentUpon>DataSetCategory.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetClient.xsc">
      <DependentUpon>DataSetClient.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetClient.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DataSetClient.Designer.vb</LastGenOutput>
    </None>
    <None Include="DataSets\DataSetClient.xss">
      <DependentUpon>DataSetClient.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetContract.xsc">
      <DependentUpon>DataSetContract.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetContract.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DataSetContract.Designer.vb</LastGenOutput>
    </None>
    <None Include="DataSets\DataSetContract.xss">
      <DependentUpon>DataSetContract.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetContractDate.xsc">
      <DependentUpon>DataSetContractDate.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetContractDate.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DataSetContractDate.Designer.vb</LastGenOutput>
    </None>
    <None Include="DataSets\DataSetContractDate.xss">
      <DependentUpon>DataSetContractDate.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetContractInfo.xsc">
      <DependentUpon>DataSetContractInfo.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetContractInfo.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DataSetContractInfo.Designer.vb</LastGenOutput>
    </None>
    <None Include="DataSets\DataSetContractInfo.xss">
      <DependentUpon>DataSetContractInfo.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetStoreGroups.xsc">
      <DependentUpon>DataSetStoreGroups.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetStoreGroups.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DataSetStoreGroups.Designer.vb</LastGenOutput>
    </None>
    <None Include="DataSets\DataSetStoreGroups.xss">
      <DependentUpon>DataSetStoreGroups.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetMedia.xsc">
      <DependentUpon>DataSetMedia.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetMedia.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DataSetMedia.Designer.vb</LastGenOutput>
    </None>
    <None Include="DataSets\DataSetMedia.xss">
      <DependentUpon>DataSetMedia.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetMediaGap.xsc">
      <DependentUpon>DataSetMediaGap.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetMediaGap.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DataSetMediaGap.Designer.vb</LastGenOutput>
    </None>
    <None Include="DataSets\DataSetMediaGap.xss">
      <DependentUpon>DataSetMediaGap.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetSetting.xsc">
      <DependentUpon>DataSetSetting.xsd</DependentUpon>
    </None>
    <None Include="DataSets\DataSetSetting.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DataSetSetting.Designer.vb</LastGenOutput>
    </None>
    <None Include="DataSets\DataSetSetting.xss">
      <DependentUpon>DataSetSetting.xsd</DependentUpon>
    </None>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Resources\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\..\DataService\DataService.csproj">
      <Project>{8dde8153-da42-403b-8ce0-85ae455e6ea5}</Project>
      <Name>DataService</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\..\Universal\Universal.csproj">
      <Project>{b6b01178-1f34-4c5d-8601-e666a10f675b}</Project>
      <Name>Universal</Name>
    </ProjectReference>
    <ProjectReference Include="..\LiquidShell\LiquidShell.vbproj">
      <Project>{3e5c068d-c18e-4b6d-a7d4-50c6c2365f3f}</Project>
      <Name>LiquidShell</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>