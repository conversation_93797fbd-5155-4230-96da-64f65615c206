﻿using DataAccess;

namespace DataService.Security
{
    class SaveAcceptanceOfTermsOfUseCommandExecutor : CommandExecutor<SaveAcceptanceOfTermsOfUseCommand>
    {

        public override void Execute(SaveAcceptanceOfTermsOfUseCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.SaveAcceptanceOfTermsOfUse))
            {
                storedprocedure.AddInputParameter("userid", command.UserId);
                storedprocedure.AddInputParameter("termsofuseid", command.TermsOfUseId);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
            }
        }

    }
}
