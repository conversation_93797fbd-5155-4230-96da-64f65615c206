﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class FormChangePassword
    Inherits EditorForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(FormChangePassword))
        Me.LabelControlHeadingTasks = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditNewPassword = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditNewPasswordAgain = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl()
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonOK = New DevExpress.XtraEditors.SimpleButton()
        Me.TextEditCurrentPassword = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl3 = New DevExpress.XtraEditors.LabelControl()
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelButtonBar.SuspendLayout()
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditNewPassword.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditNewPasswordAgain.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditCurrentPassword.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'PanelButtonBar
        '
        Me.PanelButtonBar.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelButtonBar.Appearance.BackColor2 = System.Drawing.Color.LightSteelBlue
        Me.PanelButtonBar.Appearance.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical
        Me.PanelButtonBar.Appearance.Options.UseBackColor = True
        Me.PanelButtonBar.Controls.Add(Me.ButtonCancel)
        Me.PanelButtonBar.Controls.Add(Me.ButtonOK)
        Me.PanelButtonBar.Location = New System.Drawing.Point(0, 151)
        Me.PanelButtonBar.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Office2003
        Me.PanelButtonBar.LookAndFeel.UseDefaultLookAndFeel = False
        Me.PanelButtonBar.Size = New System.Drawing.Size(370, 52)
        Me.PanelButtonBar.TabIndex = 7
        '
        'LabelControlHeadingTasks
        '
        Me.LabelControlHeadingTasks.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControlHeadingTasks.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControlHeadingTasks.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelControlHeadingTasks.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControlHeadingTasks.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelControlHeadingTasks.LineVisible = True
        Me.LabelControlHeadingTasks.Location = New System.Drawing.Point(12, 12)
        Me.LabelControlHeadingTasks.Margin = New System.Windows.Forms.Padding(3, 3, 3, 12)
        Me.LabelControlHeadingTasks.Name = "LabelControlHeadingTasks"
        Me.LabelControlHeadingTasks.Size = New System.Drawing.Size(346, 18)
        Me.LabelControlHeadingTasks.TabIndex = 0
        Me.LabelControlHeadingTasks.Text = "Please enter a new password for your account"
        '
        'TextEditNewPassword
        '
        Me.TextEditNewPassword.EditValue = ""
        Me.TextEditNewPassword.Location = New System.Drawing.Point(190, 71)
        Me.TextEditNewPassword.Margin = New System.Windows.Forms.Padding(24, 3, 3, 3)
        Me.TextEditNewPassword.Name = "TextEditNewPassword"
        Me.TextEditNewPassword.Properties.Appearance.BackColor = System.Drawing.Color.White
        Me.TextEditNewPassword.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditNewPassword.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditNewPassword.Properties.Appearance.Options.UseBackColor = True
        Me.TextEditNewPassword.Properties.Appearance.Options.UseFont = True
        Me.TextEditNewPassword.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditNewPassword.Properties.PasswordChar = Global.Microsoft.VisualBasic.ChrW(9679)
        Me.TextEditNewPassword.Size = New System.Drawing.Size(168, 20)
        Me.TextEditNewPassword.TabIndex = 4
        '
        'LabelControl1
        '
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl1.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl1.Location = New System.Drawing.Point(11, 74)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(87, 13)
        Me.LabelControl1.TabIndex = 3
        Me.LabelControl1.Text = "New password:"
        '
        'TextEditNewPasswordAgain
        '
        Me.TextEditNewPasswordAgain.Location = New System.Drawing.Point(190, 97)
        Me.TextEditNewPasswordAgain.Margin = New System.Windows.Forms.Padding(24, 3, 3, 3)
        Me.TextEditNewPasswordAgain.Name = "TextEditNewPasswordAgain"
        Me.TextEditNewPasswordAgain.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditNewPasswordAgain.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditNewPasswordAgain.Properties.Appearance.Options.UseFont = True
        Me.TextEditNewPasswordAgain.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditNewPasswordAgain.Properties.PasswordChar = Global.Microsoft.VisualBasic.ChrW(9679)
        Me.TextEditNewPasswordAgain.Size = New System.Drawing.Size(168, 20)
        Me.TextEditNewPasswordAgain.TabIndex = 6
        '
        'LabelControl2
        '
        Me.LabelControl2.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl2.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl2.Location = New System.Drawing.Point(11, 100)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.Size = New System.Drawing.Size(135, 13)
        Me.LabelControl2.TabIndex = 5
        Me.LabelControl2.Text = "Re-type new password:"
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "accept.png")
        Me.ImageList24x24.Images.SetKeyName(1, "delete.png")
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.ButtonCancel.ImageIndex = 1
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(258, 12)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(100, 28)
        Me.ButtonCancel.TabIndex = 1
        Me.ButtonCancel.Text = "Cancel"
        '
        'ButtonOK
        '
        Me.ButtonOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonOK.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonOK.Appearance.Options.UseFont = True
        Me.ButtonOK.ImageIndex = 0
        Me.ButtonOK.ImageList = Me.ImageList24x24
        Me.ButtonOK.Location = New System.Drawing.Point(152, 12)
        Me.ButtonOK.LookAndFeel.SkinName = "Black"
        Me.ButtonOK.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonOK.Name = "ButtonOK"
        Me.ButtonOK.Size = New System.Drawing.Size(100, 28)
        Me.ButtonOK.TabIndex = 0
        Me.ButtonOK.Text = "OK"
        '
        'TextEditCurrentPassword
        '
        Me.TextEditCurrentPassword.EditValue = ""
        Me.TextEditCurrentPassword.Location = New System.Drawing.Point(190, 45)
        Me.TextEditCurrentPassword.Margin = New System.Windows.Forms.Padding(24, 3, 3, 3)
        Me.TextEditCurrentPassword.Name = "TextEditCurrentPassword"
        Me.TextEditCurrentPassword.Properties.Appearance.BackColor = System.Drawing.Color.White
        Me.TextEditCurrentPassword.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditCurrentPassword.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditCurrentPassword.Properties.Appearance.Options.UseBackColor = True
        Me.TextEditCurrentPassword.Properties.Appearance.Options.UseFont = True
        Me.TextEditCurrentPassword.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditCurrentPassword.Properties.PasswordChar = Global.Microsoft.VisualBasic.ChrW(9679)
        Me.TextEditCurrentPassword.Size = New System.Drawing.Size(168, 20)
        Me.TextEditCurrentPassword.TabIndex = 2
        '
        'LabelControl3
        '
        Me.LabelControl3.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl3.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl3.Location = New System.Drawing.Point(11, 48)
        Me.LabelControl3.Name = "LabelControl3"
        Me.LabelControl3.Size = New System.Drawing.Size(107, 13)
        Me.LabelControl3.TabIndex = 1
        Me.LabelControl3.Text = "Current password:"
        '
        'FormChangePassword
        '
        Me.AcceptButton = Me.ButtonOK
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.CancelButton = Me.ButtonCancel
        Me.ClientSize = New System.Drawing.Size(370, 203)
        Me.Controls.Add(Me.LabelControl2)
        Me.Controls.Add(Me.LabelControl3)
        Me.Controls.Add(Me.LabelControl1)
        Me.Controls.Add(Me.TextEditNewPasswordAgain)
        Me.Controls.Add(Me.TextEditCurrentPassword)
        Me.Controls.Add(Me.TextEditNewPassword)
        Me.Controls.Add(Me.LabelControlHeadingTasks)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "FormChangePassword"
        Me.Tag = "376, 231"
        Me.Text = "Change Password"
        Me.Controls.SetChildIndex(Me.PanelButtonBar, 0)
        Me.Controls.SetChildIndex(Me.LabelControlHeadingTasks, 0)
        Me.Controls.SetChildIndex(Me.TextEditNewPassword, 0)
        Me.Controls.SetChildIndex(Me.TextEditCurrentPassword, 0)
        Me.Controls.SetChildIndex(Me.TextEditNewPasswordAgain, 0)
        Me.Controls.SetChildIndex(Me.LabelControl1, 0)
        Me.Controls.SetChildIndex(Me.LabelControl3, 0)
        Me.Controls.SetChildIndex(Me.LabelControl2, 0)
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelButtonBar.ResumeLayout(False)
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditNewPassword.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditNewPasswordAgain.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditCurrentPassword.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents LabelControlHeadingTasks As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditNewPassword As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditNewPasswordAgain As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonOK As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents TextEditCurrentPassword As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelControl3 As DevExpress.XtraEditors.LabelControl
End Class
