﻿using Framework.Controls;
using System.Drawing;
using System;
using System.Windows.Forms;

namespace Framework.Forms
{
    public partial class ColorPicker : DataForm
    {
        internal Color Color;


        #region Startup

        public ColorPicker()
        {
            InitializeComponent();
            Load += ColorPicker_Load;
        }

        private void ColorPicker_Load(object sender, EventArgs e)
        {
            InitializeButtons();
        }

        private void InitializeButtons()
        {
            for (int i = 0; i < Controls.Count; i++)
            {
                if (Controls[i] is FlatButton)
                {
                    FlatButton button = (FlatButton)Controls[i];
                    button.Click += Button_Click;
                    button.FlatAppearance.MouseOverBackColor = ToolBox.GetLighterShadeOfColor(button.BackColor, 30);
                    button.Text = ToolBox.SplitStringByCapitalLetters(button.BackColor.Name);
                    button.TextAlign = ContentAlignment.MiddleLeft;
                    button.Padding = new Padding(10, 0, 0, 0);
                }
            }
        }

        #endregion


        private void Button_Click(object sender, System.EventArgs e)
        {
            Color = ((FlatButton)sender).BackColor;
            DialogResult = DialogResult.OK;
            Close();
        }

        public static Color Pick()
        {
            Color selectedcolor = FrameworkSettings.Colors.ThemeColor;
            using (ColorPicker picker = new ColorPicker())
            {
                picker.ShowDialog();
                if (picker.DialogResult == DialogResult.OK)
                {
                    selectedcolor = picker.Color;
                }
            }
            return selectedcolor;
        }
    }
}
