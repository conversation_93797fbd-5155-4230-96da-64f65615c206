<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformBrandFamily
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformBrandFamily))
        Dim SuperToolTip1 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip
        Dim ToolTipTitleItem1 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem
        Dim ToolTipItem1 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem
        Dim SuperToolTip2 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip
        Dim ToolTipTitleItem2 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem
        Dim ToolTipItem2 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle
        Me.ImageList16x16 = New System.Windows.Forms.ImageList(Me.components)
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.TextEditBrandFamilyName = New DevExpress.XtraEditors.TextEdit
        Me.LabelBrandFamilyName = New DevExpress.XtraEditors.LabelControl
        Me.GroupControlBrandFamilyMember = New DevExpress.XtraEditors.GroupControl
        Me.LabelControl14 = New DevExpress.XtraEditors.LabelControl
        Me.TextEditSearch = New DevExpress.XtraEditors.TextEdit
        Me.PictureClearSearchClientBrand = New DevExpress.XtraEditors.PictureEdit
        Me.PictureAdvancedSearch = New DevExpress.XtraEditors.PictureEdit
        Me.ButtonRemove = New DevExpress.XtraEditors.SimpleButton
        Me.ButtonAdd = New DevExpress.XtraEditors.SimpleButton
        Me.GridBrandFamilyMember = New System.Windows.Forms.DataGridView
        Me.BrandNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn
        Me.ButtonSave = New DevExpress.XtraEditors.SimpleButton
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl
        CType(Me.TextEditBrandFamilyName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControlBrandFamilyMember, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlBrandFamilyMember.SuspendLayout()
        CType(Me.TextEditSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearchClientBrand.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridBrandFamilyMember, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!)
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Size = New System.Drawing.Size(225, 30)
        Me.LabelTitle.TabIndex = 0
        Me.LabelTitle.Text = "(new brand family)"
        '
        'ImageList16x16
        '
        Me.ImageList16x16.ImageStream = CType(resources.GetObject("ImageList16x16.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList16x16.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList16x16.Images.SetKeyName(0, "add.png")
        Me.ImageList16x16.Images.SetKeyName(1, "pencil.png")
        Me.ImageList16x16.Images.SetKeyName(2, "delete.png")
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        '
        'TextEditBrandFamilyName
        '
        Me.TextEditBrandFamilyName.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditBrandFamilyName.Location = New System.Drawing.Point(171, 90)
        Me.TextEditBrandFamilyName.Margin = New System.Windows.Forms.Padding(3, 3, 3, 15)
        Me.TextEditBrandFamilyName.Name = "TextEditBrandFamilyName"
        Me.TextEditBrandFamilyName.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditBrandFamilyName.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditBrandFamilyName.Properties.Appearance.Options.UseFont = True
        Me.TextEditBrandFamilyName.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditBrandFamilyName.Properties.MaxLength = 200
        Me.TextEditBrandFamilyName.Size = New System.Drawing.Size(295, 20)
        Me.TextEditBrandFamilyName.TabIndex = 3
        '
        'LabelBrandFamilyName
        '
        Me.LabelBrandFamilyName.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelBrandFamilyName.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelBrandFamilyName.Location = New System.Drawing.Point(12, 93)
        Me.LabelBrandFamilyName.Name = "LabelBrandFamilyName"
        Me.LabelBrandFamilyName.Size = New System.Drawing.Size(117, 13)
        Me.LabelBrandFamilyName.TabIndex = 2
        Me.LabelBrandFamilyName.Text = "Brand Family Name:"
        '
        'GroupControlBrandFamilyMember
        '
        Me.GroupControlBrandFamilyMember.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlBrandFamilyMember.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlBrandFamilyMember.Appearance.Options.UseFont = True
        Me.GroupControlBrandFamilyMember.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlBrandFamilyMember.AppearanceCaption.Options.UseFont = True
        Me.GroupControlBrandFamilyMember.Controls.Add(Me.LabelControl14)
        Me.GroupControlBrandFamilyMember.Controls.Add(Me.TextEditSearch)
        Me.GroupControlBrandFamilyMember.Controls.Add(Me.PictureClearSearchClientBrand)
        Me.GroupControlBrandFamilyMember.Controls.Add(Me.PictureAdvancedSearch)
        Me.GroupControlBrandFamilyMember.Controls.Add(Me.ButtonRemove)
        Me.GroupControlBrandFamilyMember.Controls.Add(Me.ButtonAdd)
        Me.GroupControlBrandFamilyMember.Controls.Add(Me.GridBrandFamilyMember)
        Me.GroupControlBrandFamilyMember.Location = New System.Drawing.Point(12, 161)
        Me.GroupControlBrandFamilyMember.LookAndFeel.SkinName = "Black"
        Me.GroupControlBrandFamilyMember.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlBrandFamilyMember.Name = "GroupControlBrandFamilyMember"
        Me.GroupControlBrandFamilyMember.Size = New System.Drawing.Size(799, 333)
        Me.GroupControlBrandFamilyMember.TabIndex = 5
        Me.GroupControlBrandFamilyMember.Text = "Brands in this Brand Family"
        '
        'LabelControl14
        '
        Me.LabelControl14.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl14.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl14.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl14.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl14.Location = New System.Drawing.Point(641, 309)
        Me.LabelControl14.Name = "LabelControl14"
        Me.LabelControl14.Size = New System.Drawing.Size(45, 13)
        Me.LabelControl14.TabIndex = 4
        Me.LabelControl14.Text = "Search:"
        '
        'TextEditSearch
        '
        Me.TextEditSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearch.EditValue = ""
        Me.TextEditSearch.Location = New System.Drawing.Point(692, 306)
        Me.TextEditSearch.Margin = New System.Windows.Forms.Padding(3, 4, 3, 5)
        Me.TextEditSearch.Name = "TextEditSearch"
        Me.TextEditSearch.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearch.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearch.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearch.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearch.Size = New System.Drawing.Size(80, 20)
        Me.TextEditSearch.TabIndex = 5
        '
        'PictureClearSearchClientBrand
        '
        Me.PictureClearSearchClientBrand.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchClientBrand.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchClientBrand.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchClientBrand.Location = New System.Drawing.Point(778, 3)
        Me.PictureClearSearchClientBrand.Name = "PictureClearSearchClientBrand"
        Me.PictureClearSearchClientBrand.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchClientBrand.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchClientBrand.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchClientBrand.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem1.Text = "Clear Search"
        ToolTipItem1.LeftIndent = 6
        ToolTipItem1.Text = "Click here to clear all search boxes."
        SuperToolTip1.Items.Add(ToolTipTitleItem1)
        SuperToolTip1.Items.Add(ToolTipItem1)
        Me.PictureClearSearchClientBrand.SuperTip = SuperToolTip1
        Me.PictureClearSearchClientBrand.TabIndex = 0
        Me.PictureClearSearchClientBrand.TabStop = True
        '
        'PictureAdvancedSearch
        '
        Me.PictureAdvancedSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearch.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearch.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearch.Location = New System.Drawing.Point(778, 308)
        Me.PictureAdvancedSearch.Margin = New System.Windows.Forms.Padding(3, 6, 3, 7)
        Me.PictureAdvancedSearch.Name = "PictureAdvancedSearch"
        Me.PictureAdvancedSearch.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearch.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearch.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearch.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem2.Text = "Advanced Search"
        ToolTipItem2.LeftIndent = 6
        ToolTipItem2.Text = "Click here to search individual column values."
        SuperToolTip2.Items.Add(ToolTipTitleItem2)
        SuperToolTip2.Items.Add(ToolTipItem2)
        Me.PictureAdvancedSearch.SuperTip = SuperToolTip2
        Me.PictureAdvancedSearch.TabIndex = 6
        Me.PictureAdvancedSearch.TabStop = True
        '
        'ButtonRemove
        '
        Me.ButtonRemove.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonRemove.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonRemove.Appearance.Options.UseFont = True
        Me.ButtonRemove.ImageIndex = 2
        Me.ButtonRemove.ImageList = Me.ImageList16x16
        Me.ButtonRemove.Location = New System.Drawing.Point(86, 305)
        Me.ButtonRemove.LookAndFeel.SkinName = "Black"
        Me.ButtonRemove.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonRemove.Name = "ButtonRemove"
        Me.ButtonRemove.Size = New System.Drawing.Size(75, 23)
        Me.ButtonRemove.TabIndex = 3
        Me.ButtonRemove.Text = "Remove"
        '
        'ButtonAdd
        '
        Me.ButtonAdd.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAdd.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAdd.Appearance.Options.UseFont = True
        Me.ButtonAdd.ImageIndex = 0
        Me.ButtonAdd.ImageList = Me.ImageList16x16
        Me.ButtonAdd.Location = New System.Drawing.Point(5, 305)
        Me.ButtonAdd.LookAndFeel.SkinName = "Black"
        Me.ButtonAdd.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAdd.Name = "ButtonAdd"
        Me.ButtonAdd.Size = New System.Drawing.Size(75, 23)
        Me.ButtonAdd.TabIndex = 2
        Me.ButtonAdd.Text = "Add"
        '
        'GridBrandFamilyMember
        '
        Me.GridBrandFamilyMember.AllowUserToAddRows = False
        Me.GridBrandFamilyMember.AllowUserToDeleteRows = False
        Me.GridBrandFamilyMember.AllowUserToOrderColumns = True
        Me.GridBrandFamilyMember.AllowUserToResizeRows = False
        DataGridViewCellStyle1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridBrandFamilyMember.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle1
        Me.GridBrandFamilyMember.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridBrandFamilyMember.BackgroundColor = System.Drawing.Color.White
        Me.GridBrandFamilyMember.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridBrandFamilyMember.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridBrandFamilyMember.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle2.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle2.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridBrandFamilyMember.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle2
        Me.GridBrandFamilyMember.ColumnHeadersHeight = 22
        Me.GridBrandFamilyMember.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridBrandFamilyMember.ColumnHeadersVisible = False
        Me.GridBrandFamilyMember.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.BrandNameColumn})
        Me.GridBrandFamilyMember.EnableHeadersVisualStyles = False
        Me.GridBrandFamilyMember.GridColor = System.Drawing.Color.White
        Me.GridBrandFamilyMember.Location = New System.Drawing.Point(2, 22)
        Me.GridBrandFamilyMember.Name = "GridBrandFamilyMember"
        Me.GridBrandFamilyMember.ReadOnly = True
        Me.GridBrandFamilyMember.RowHeadersVisible = False
        DataGridViewCellStyle3.ForeColor = System.Drawing.Color.DimGray
        Me.GridBrandFamilyMember.RowsDefaultCellStyle = DataGridViewCellStyle3
        Me.GridBrandFamilyMember.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridBrandFamilyMember.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridBrandFamilyMember.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridBrandFamilyMember.RowTemplate.Height = 19
        Me.GridBrandFamilyMember.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridBrandFamilyMember.ShowCellToolTips = False
        Me.GridBrandFamilyMember.Size = New System.Drawing.Size(795, 277)
        Me.GridBrandFamilyMember.StandardTab = True
        Me.GridBrandFamilyMember.TabIndex = 1
        '
        'BrandNameColumn
        '
        Me.BrandNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.BrandNameColumn.DataPropertyName = "BrandName"
        Me.BrandNameColumn.HeaderText = "Brand"
        Me.BrandNameColumn.Name = "BrandNameColumn"
        Me.BrandNameColumn.ReadOnly = True
        '
        'ButtonSave
        '
        Me.ButtonSave.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonSave.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonSave.Appearance.Options.UseFont = True
        Me.ButtonSave.ImageIndex = 1
        Me.ButtonSave.ImageList = Me.ImageList24x24
        Me.ButtonSave.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonSave.Location = New System.Drawing.Point(605, 509)
        Me.ButtonSave.LookAndFeel.SkinName = "Black"
        Me.ButtonSave.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonSave.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonSave.Name = "ButtonSave"
        Me.ButtonSave.Size = New System.Drawing.Size(100, 28)
        Me.ButtonSave.TabIndex = 6
        Me.ButtonSave.Text = "Save"
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(711, 509)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(100, 28)
        Me.ButtonCancel.TabIndex = 7
        Me.ButtonCancel.Text = "Cancel"
        '
        'LabelControl1
        '
        Me.LabelControl1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl1.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelControl1.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl1.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelControl1.LineVisible = True
        Me.LabelControl1.Location = New System.Drawing.Point(12, 57)
        Me.LabelControl1.Margin = New System.Windows.Forms.Padding(3, 3, 3, 12)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(799, 18)
        Me.LabelControl1.TabIndex = 1
        Me.LabelControl1.Text = "Brand Family Details"
        '
        'LabelControl2
        '
        Me.LabelControl2.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl2.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl2.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelControl2.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl2.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelControl2.LineVisible = True
        Me.LabelControl2.Location = New System.Drawing.Point(12, 128)
        Me.LabelControl2.Margin = New System.Windows.Forms.Padding(3, 3, 3, 12)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.Size = New System.Drawing.Size(799, 18)
        Me.LabelControl2.TabIndex = 4
        Me.LabelControl2.Text = "Brand Family Memberships"
        '
        'SubformBrandFamily
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.Controls.Add(Me.GroupControlBrandFamilyMember)
        Me.Controls.Add(Me.TextEditBrandFamilyName)
        Me.Controls.Add(Me.LabelControl2)
        Me.Controls.Add(Me.LabelControl1)
        Me.Controls.Add(Me.LabelBrandFamilyName)
        Me.Controls.Add(Me.ButtonSave)
        Me.Controls.Add(Me.ButtonCancel)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Name = "SubformBrandFamily"
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        Me.Controls.SetChildIndex(Me.ButtonCancel, 0)
        Me.Controls.SetChildIndex(Me.ButtonSave, 0)
        Me.Controls.SetChildIndex(Me.LabelBrandFamilyName, 0)
        Me.Controls.SetChildIndex(Me.LabelControl1, 0)
        Me.Controls.SetChildIndex(Me.LabelControl2, 0)
        Me.Controls.SetChildIndex(Me.TextEditBrandFamilyName, 0)
        Me.Controls.SetChildIndex(Me.GroupControlBrandFamilyMember, 0)
        CType(Me.TextEditBrandFamilyName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControlBrandFamilyMember, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlBrandFamilyMember.ResumeLayout(False)
        CType(Me.TextEditSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearchClientBrand.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridBrandFamilyMember, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents ImageList16x16 As System.Windows.Forms.ImageList
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents TextEditBrandFamilyName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelBrandFamilyName As DevExpress.XtraEditors.LabelControl
    Friend WithEvents GroupControlBrandFamilyMember As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelControl14 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditSearch As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearchClientBrand As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearch As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents ButtonRemove As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonAdd As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GridBrandFamilyMember As System.Windows.Forms.DataGridView
    Friend WithEvents BrandNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents ButtonSave As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl

End Class
