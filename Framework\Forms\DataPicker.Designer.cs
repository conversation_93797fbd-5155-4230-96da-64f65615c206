﻿namespace Framework.Forms
{
    partial class DataPicker
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.buttonPanel = new System.Windows.Forms.Panel();
            this.flatButtonOK = new Framework.Controls.FlatButton();
            this.grid = new Framework.Controls.GridSystem.Grid();
            this.buttonPanel.SuspendLayout();
            this.SuspendLayout();
            // 
            // buttonPanel
            // 
            this.buttonPanel.Controls.Add(this.flatButtonOK);
            this.buttonPanel.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.buttonPanel.Location = new System.Drawing.Point(0, 306);
            this.buttonPanel.Margin = new System.Windows.Forms.Padding(0);
            this.buttonPanel.Name = "buttonPanel";
            this.buttonPanel.Size = new System.Drawing.Size(367, 100);
            this.buttonPanel.TabIndex = 1;
            // 
            // flatButtonOK
            // 
            this.flatButtonOK.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.flatButtonOK.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            this.flatButtonOK.Cursor = System.Windows.Forms.Cursors.Hand;
            this.flatButtonOK.ErrorMessage = "";
            this.flatButtonOK.FlatAppearance.BorderSize = 0;
            this.flatButtonOK.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(30)))), ((int)(((byte)(158)))), ((int)(((byte)(158)))));
            this.flatButtonOK.FlatAppearance.MouseOverBackColor = System.Drawing.Color.Teal;
            this.flatButtonOK.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.flatButtonOK.ForeColor = System.Drawing.Color.White;
            this.flatButtonOK.Location = new System.Drawing.Point(20, 20);
            this.flatButtonOK.Margin = new System.Windows.Forms.Padding(20, 0, 20, 20);
            this.flatButtonOK.Name = "flatButtonOK";
            this.flatButtonOK.Size = new System.Drawing.Size(180, 60);
            this.flatButtonOK.TabIndex = 0;
            this.flatButtonOK.Text = "OK";
            this.flatButtonOK.UseVisualStyleBackColor = false;
            // 
            // grid
            // 
            this.grid.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.grid.ColumnHeadersVisible = true;
            this.grid.CurrentRow = null;
            this.grid.DataSource = null;
            this.grid.FilterText = "";
            this.grid.FirstDisplayedScrollingRowIndex = -1;
            this.grid.Font = new System.Drawing.Font("Verdana", 8F);
            this.grid.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(60)))), ((int)(((byte)(60)))), ((int)(((byte)(60)))));
            this.grid.Location = new System.Drawing.Point(20, 20);
            this.grid.Margin = new System.Windows.Forms.Padding(11, 11, 11, 0);
            this.grid.MultiSelect = true;
            this.grid.Name = "grid";
            this.grid.Size = new System.Drawing.Size(327, 286);
            this.grid.TabIndex = 0;
            // 
            // DataPicker
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 13F);
            this.ClientSize = new System.Drawing.Size(367, 406);
            this.Controls.Add(this.grid);
            this.Controls.Add(this.buttonPanel);
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.MinimumSize = new System.Drawing.Size(236, 272);
            this.Name = "DataPicker";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Tag = "383, 444";
            this.Text = "Data Picker";
            this.buttonPanel.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel buttonPanel;
        private Controls.FlatButton flatButtonOK;
        protected Controls.GridSystem.Grid grid;
    }
}
