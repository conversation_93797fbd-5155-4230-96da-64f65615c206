﻿using DataAccess;

namespace DataService.Security
{
    class AddLegacyRolesOfMemberCommandExecutor : CommandExecutor<AddLegacyRolesOfMemberCommand>
    {

        public override void Execute(AddLegacyRolesOfMemberCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.AddLegacyRolesOfMember))
            {
                storedprocedure.AddInputParameter("login", command.Login);
                storedprocedure.AddInputParameter("newroles", command.NewRoles);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
            }
        }

    }
}
