﻿'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated by a tool.
'     Runtime Version:4.0.30319.42000
'
'     Changes to this file may cause incorrect behavior and will be lost if
'     the code is regenerated.
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict Off
Option Explicit On



'''<summary>
'''Represents a strongly typed in-memory cache of data.
'''</summary>
<Global.System.Serializable(),  _
 Global.System.ComponentModel.DesignerCategoryAttribute("code"),  _
 Global.System.ComponentModel.ToolboxItem(true),  _
 Global.System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedDataSetSchema"),  _
 Global.System.Xml.Serialization.XmlRootAttribute("DataSetContractInfo"),  _
 Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.DataSet")>  _
Partial Public Class DataSetContractInfo
    Inherits Global.System.Data.DataSet
    
    Private tableContractInfo As ContractInfoDataTable
    
    Private tableBurstInfo As BurstInfoDataTable
    
    Private relationContractInfo_BurstInfo As Global.System.Data.DataRelation
    
    Private _schemaSerializationMode As Global.System.Data.SchemaSerializationMode = Global.System.Data.SchemaSerializationMode.IncludeSchema
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Sub New()
        MyBase.New
        Me.BeginInit
        Me.InitClass
        Dim schemaChangedHandler As Global.System.ComponentModel.CollectionChangeEventHandler = AddressOf Me.SchemaChanged
        AddHandler MyBase.Tables.CollectionChanged, schemaChangedHandler
        AddHandler MyBase.Relations.CollectionChanged, schemaChangedHandler
        Me.EndInit
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Protected Sub New(ByVal info As Global.System.Runtime.Serialization.SerializationInfo, ByVal context As Global.System.Runtime.Serialization.StreamingContext)
        MyBase.New(info, context, false)
        If (Me.IsBinarySerialized(info, context) = true) Then
            Me.InitVars(false)
            Dim schemaChangedHandler1 As Global.System.ComponentModel.CollectionChangeEventHandler = AddressOf Me.SchemaChanged
            AddHandler Me.Tables.CollectionChanged, schemaChangedHandler1
            AddHandler Me.Relations.CollectionChanged, schemaChangedHandler1
            Return
        End If
        Dim strSchema As String = CType(info.GetValue("XmlSchema", GetType(String)),String)
        If (Me.DetermineSchemaSerializationMode(info, context) = Global.System.Data.SchemaSerializationMode.IncludeSchema) Then
            Dim ds As Global.System.Data.DataSet = New Global.System.Data.DataSet()
            ds.ReadXmlSchema(New Global.System.Xml.XmlTextReader(New Global.System.IO.StringReader(strSchema)))
            If (Not (ds.Tables("ContractInfo")) Is Nothing) Then
                MyBase.Tables.Add(New ContractInfoDataTable(ds.Tables("ContractInfo")))
            End If
            If (Not (ds.Tables("BurstInfo")) Is Nothing) Then
                MyBase.Tables.Add(New BurstInfoDataTable(ds.Tables("BurstInfo")))
            End If
            Me.DataSetName = ds.DataSetName
            Me.Prefix = ds.Prefix
            Me.Namespace = ds.Namespace
            Me.Locale = ds.Locale
            Me.CaseSensitive = ds.CaseSensitive
            Me.EnforceConstraints = ds.EnforceConstraints
            Me.Merge(ds, false, Global.System.Data.MissingSchemaAction.Add)
            Me.InitVars
        Else
            Me.ReadXmlSchema(New Global.System.Xml.XmlTextReader(New Global.System.IO.StringReader(strSchema)))
        End If
        Me.GetSerializationData(info, context)
        Dim schemaChangedHandler As Global.System.ComponentModel.CollectionChangeEventHandler = AddressOf Me.SchemaChanged
        AddHandler MyBase.Tables.CollectionChanged, schemaChangedHandler
        AddHandler Me.Relations.CollectionChanged, schemaChangedHandler
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
     Global.System.ComponentModel.Browsable(false),  _
     Global.System.ComponentModel.DesignerSerializationVisibility(Global.System.ComponentModel.DesignerSerializationVisibility.Content)>  _
    Public ReadOnly Property ContractInfo() As ContractInfoDataTable
        Get
            Return Me.tableContractInfo
        End Get
    End Property
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
     Global.System.ComponentModel.Browsable(false),  _
     Global.System.ComponentModel.DesignerSerializationVisibility(Global.System.ComponentModel.DesignerSerializationVisibility.Content)>  _
    Public ReadOnly Property BurstInfo() As BurstInfoDataTable
        Get
            Return Me.tableBurstInfo
        End Get
    End Property
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
     Global.System.ComponentModel.BrowsableAttribute(true),  _
     Global.System.ComponentModel.DesignerSerializationVisibilityAttribute(Global.System.ComponentModel.DesignerSerializationVisibility.Visible)>  _
    Public Overrides Property SchemaSerializationMode() As Global.System.Data.SchemaSerializationMode
        Get
            Return Me._schemaSerializationMode
        End Get
        Set
            Me._schemaSerializationMode = value
        End Set
    End Property
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
     Global.System.ComponentModel.DesignerSerializationVisibilityAttribute(Global.System.ComponentModel.DesignerSerializationVisibility.Hidden)>  _
    Public Shadows ReadOnly Property Tables() As Global.System.Data.DataTableCollection
        Get
            Return MyBase.Tables
        End Get
    End Property
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
     Global.System.ComponentModel.DesignerSerializationVisibilityAttribute(Global.System.ComponentModel.DesignerSerializationVisibility.Hidden)>  _
    Public Shadows ReadOnly Property Relations() As Global.System.Data.DataRelationCollection
        Get
            Return MyBase.Relations
        End Get
    End Property
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Protected Overrides Sub InitializeDerivedDataSet()
        Me.BeginInit
        Me.InitClass
        Me.EndInit
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Overrides Function Clone() As Global.System.Data.DataSet
        Dim cln As DataSetContractInfo = CType(MyBase.Clone,DataSetContractInfo)
        cln.InitVars
        cln.SchemaSerializationMode = Me.SchemaSerializationMode
        Return cln
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Protected Overrides Function ShouldSerializeTables() As Boolean
        Return false
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Protected Overrides Function ShouldSerializeRelations() As Boolean
        Return false
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Protected Overrides Sub ReadXmlSerializable(ByVal reader As Global.System.Xml.XmlReader)
        If (Me.DetermineSchemaSerializationMode(reader) = Global.System.Data.SchemaSerializationMode.IncludeSchema) Then
            Me.Reset
            Dim ds As Global.System.Data.DataSet = New Global.System.Data.DataSet()
            ds.ReadXml(reader)
            If (Not (ds.Tables("ContractInfo")) Is Nothing) Then
                MyBase.Tables.Add(New ContractInfoDataTable(ds.Tables("ContractInfo")))
            End If
            If (Not (ds.Tables("BurstInfo")) Is Nothing) Then
                MyBase.Tables.Add(New BurstInfoDataTable(ds.Tables("BurstInfo")))
            End If
            Me.DataSetName = ds.DataSetName
            Me.Prefix = ds.Prefix
            Me.Namespace = ds.Namespace
            Me.Locale = ds.Locale
            Me.CaseSensitive = ds.CaseSensitive
            Me.EnforceConstraints = ds.EnforceConstraints
            Me.Merge(ds, false, Global.System.Data.MissingSchemaAction.Add)
            Me.InitVars
        Else
            Me.ReadXml(reader)
            Me.InitVars
        End If
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Protected Overrides Function GetSchemaSerializable() As Global.System.Xml.Schema.XmlSchema
        Dim stream As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
        Me.WriteXmlSchema(New Global.System.Xml.XmlTextWriter(stream, Nothing))
        stream.Position = 0
        Return Global.System.Xml.Schema.XmlSchema.Read(New Global.System.Xml.XmlTextReader(stream), Nothing)
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Friend Overloads Sub InitVars()
        Me.InitVars(true)
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Friend Overloads Sub InitVars(ByVal initTable As Boolean)
        Me.tableContractInfo = CType(MyBase.Tables("ContractInfo"),ContractInfoDataTable)
        If (initTable = true) Then
            If (Not (Me.tableContractInfo) Is Nothing) Then
                Me.tableContractInfo.InitVars
            End If
        End If
        Me.tableBurstInfo = CType(MyBase.Tables("BurstInfo"),BurstInfoDataTable)
        If (initTable = true) Then
            If (Not (Me.tableBurstInfo) Is Nothing) Then
                Me.tableBurstInfo.InitVars
            End If
        End If
        Me.relationContractInfo_BurstInfo = Me.Relations("ContractInfo_BurstInfo")
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Private Sub InitClass()
        Me.DataSetName = "DataSetContractInfo"
        Me.Prefix = ""
        Me.Namespace = "http://tempuri.org/DataSetContractInfo.xsd"
        Me.EnforceConstraints = true
        Me.SchemaSerializationMode = Global.System.Data.SchemaSerializationMode.IncludeSchema
        Me.tableContractInfo = New ContractInfoDataTable()
        MyBase.Tables.Add(Me.tableContractInfo)
        Me.tableBurstInfo = New BurstInfoDataTable()
        MyBase.Tables.Add(Me.tableBurstInfo)
        Me.relationContractInfo_BurstInfo = New Global.System.Data.DataRelation("ContractInfo_BurstInfo", New Global.System.Data.DataColumn() {Me.tableContractInfo.ContractIDColumn}, New Global.System.Data.DataColumn() {Me.tableBurstInfo.ContractIDColumn}, false)
        Me.Relations.Add(Me.relationContractInfo_BurstInfo)
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Private Function ShouldSerializeContractInfo() As Boolean
        Return false
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Private Function ShouldSerializeBurstInfo() As Boolean
        Return false
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Private Sub SchemaChanged(ByVal sender As Object, ByVal e As Global.System.ComponentModel.CollectionChangeEventArgs)
        If (e.Action = Global.System.ComponentModel.CollectionChangeAction.Remove) Then
            Me.InitVars
        End If
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Shared Function GetTypedDataSetSchema(ByVal xs As Global.System.Xml.Schema.XmlSchemaSet) As Global.System.Xml.Schema.XmlSchemaComplexType
        Dim ds As DataSetContractInfo = New DataSetContractInfo()
        Dim type As Global.System.Xml.Schema.XmlSchemaComplexType = New Global.System.Xml.Schema.XmlSchemaComplexType()
        Dim sequence As Global.System.Xml.Schema.XmlSchemaSequence = New Global.System.Xml.Schema.XmlSchemaSequence()
        Dim any As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
        any.Namespace = ds.Namespace
        sequence.Items.Add(any)
        type.Particle = sequence
        Dim dsSchema As Global.System.Xml.Schema.XmlSchema = ds.GetSchemaSerializable
        If xs.Contains(dsSchema.TargetNamespace) Then
            Dim s1 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
            Dim s2 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
            Try 
                Dim schema As Global.System.Xml.Schema.XmlSchema = Nothing
                dsSchema.Write(s1)
                Dim schemas As Global.System.Collections.IEnumerator = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator
                Do While schemas.MoveNext
                    schema = CType(schemas.Current,Global.System.Xml.Schema.XmlSchema)
                    s2.SetLength(0)
                    schema.Write(s2)
                    If (s1.Length = s2.Length) Then
                        s1.Position = 0
                        s2.Position = 0
                        
                        Do While ((s1.Position <> s1.Length)  _
                                    AndAlso (s1.ReadByte = s2.ReadByte))
                            
                            
                        Loop
                        If (s1.Position = s1.Length) Then
                            Return type
                        End If
                    End If
                    
                Loop
            Finally
                If (Not (s1) Is Nothing) Then
                    s1.Close
                End If
                If (Not (s2) Is Nothing) Then
                    s2.Close
                End If
            End Try
        End If
        xs.Add(dsSchema)
        Return type
    End Function
    
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Delegate Sub ContractInfoRowChangeEventHandler(ByVal sender As Object, ByVal e As ContractInfoRowChangeEvent)
    
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Delegate Sub BurstInfoRowChangeEventHandler(ByVal sender As Object, ByVal e As BurstInfoRowChangeEvent)
    
    '''<summary>
    '''Represents the strongly named DataTable class.
    '''</summary>
    <Global.System.Serializable(),  _
     Global.System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")>  _
    Partial Public Class ContractInfoDataTable
        Inherits Global.System.Data.TypedTableBase(Of ContractInfoRow)
        
        Private columnContractID As Global.System.Data.DataColumn
        
        Private columnContractNumber As Global.System.Data.DataColumn
        
        Private columnClientName As Global.System.Data.DataColumn
        
        Private columnProjectName As Global.System.Data.DataColumn
        
        Private columnSignedBy As Global.System.Data.DataColumn
        
        Private columnSignDate As Global.System.Data.DataColumn
        
        Private columnCreatedBy As Global.System.Data.DataColumn
        
        Private columnCreationDate As Global.System.Data.DataColumn
        
        Private columnAccountManager As Global.System.Data.DataColumn
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New()
            MyBase.New
            Me.TableName = "ContractInfo"
            Me.BeginInit
            Me.InitClass
            Me.EndInit
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub New(ByVal table As Global.System.Data.DataTable)
            MyBase.New
            Me.TableName = table.TableName
            If (table.CaseSensitive <> table.DataSet.CaseSensitive) Then
                Me.CaseSensitive = table.CaseSensitive
            End If
            If (table.Locale.ToString <> table.DataSet.Locale.ToString) Then
                Me.Locale = table.Locale
            End If
            If (table.Namespace <> table.DataSet.Namespace) Then
                Me.Namespace = table.Namespace
            End If
            Me.Prefix = table.Prefix
            Me.MinimumCapacity = table.MinimumCapacity
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Sub New(ByVal info As Global.System.Runtime.Serialization.SerializationInfo, ByVal context As Global.System.Runtime.Serialization.StreamingContext)
            MyBase.New(info, context)
            Me.InitVars
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ContractIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnContractID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ContractNumberColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnContractNumber
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ClientNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnClientName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ProjectNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnProjectName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property SignedByColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnSignedBy
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property SignDateColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnSignDate
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property CreatedByColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnCreatedBy
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property CreationDateColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnCreationDate
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property AccountManagerColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnAccountManager
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Browsable(false)>  _
        Public ReadOnly Property Count() As Integer
            Get
                Return Me.Rows.Count
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Default ReadOnly Property Item(ByVal index As Integer) As ContractInfoRow
            Get
                Return CType(Me.Rows(index),ContractInfoRow)
            End Get
        End Property
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ContractInfoRowChanging As ContractInfoRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ContractInfoRowChanged As ContractInfoRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ContractInfoRowDeleting As ContractInfoRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ContractInfoRowDeleted As ContractInfoRowChangeEventHandler
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overloads Sub AddContractInfoRow(ByVal row As ContractInfoRow)
            Me.Rows.Add(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overloads Function AddContractInfoRow(ByVal ContractID As System.Guid, ByVal ContractNumber As String, ByVal ClientName As String, ByVal ProjectName As String, ByVal SignedBy As String, ByVal SignDate As Date, ByVal CreatedBy As String, ByVal CreationDate As Date, ByVal AccountManager As String) As ContractInfoRow
            Dim rowContractInfoRow As ContractInfoRow = CType(Me.NewRow,ContractInfoRow)
            Dim columnValuesArray() As Object = New Object() {ContractID, ContractNumber, ClientName, ProjectName, SignedBy, SignDate, CreatedBy, CreationDate, AccountManager}
            rowContractInfoRow.ItemArray = columnValuesArray
            Me.Rows.Add(rowContractInfoRow)
            Return rowContractInfoRow
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function FindByContractID(ByVal ContractID As System.Guid) As ContractInfoRow
            Return CType(Me.Rows.Find(New Object() {ContractID}),ContractInfoRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overrides Function Clone() As Global.System.Data.DataTable
            Dim cln As ContractInfoDataTable = CType(MyBase.Clone,ContractInfoDataTable)
            cln.InitVars
            Return cln
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function CreateInstance() As Global.System.Data.DataTable
            Return New ContractInfoDataTable()
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub InitVars()
            Me.columnContractID = MyBase.Columns("ContractID")
            Me.columnContractNumber = MyBase.Columns("ContractNumber")
            Me.columnClientName = MyBase.Columns("ClientName")
            Me.columnProjectName = MyBase.Columns("ProjectName")
            Me.columnSignedBy = MyBase.Columns("SignedBy")
            Me.columnSignDate = MyBase.Columns("SignDate")
            Me.columnCreatedBy = MyBase.Columns("CreatedBy")
            Me.columnCreationDate = MyBase.Columns("CreationDate")
            Me.columnAccountManager = MyBase.Columns("AccountManager")
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitClass()
            Me.columnContractID = New Global.System.Data.DataColumn("ContractID", GetType(Global.System.Guid), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnContractID)
            Me.columnContractNumber = New Global.System.Data.DataColumn("ContractNumber", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnContractNumber)
            Me.columnClientName = New Global.System.Data.DataColumn("ClientName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnClientName)
            Me.columnProjectName = New Global.System.Data.DataColumn("ProjectName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnProjectName)
            Me.columnSignedBy = New Global.System.Data.DataColumn("SignedBy", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnSignedBy)
            Me.columnSignDate = New Global.System.Data.DataColumn("SignDate", GetType(Date), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnSignDate)
            Me.columnCreatedBy = New Global.System.Data.DataColumn("CreatedBy", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnCreatedBy)
            Me.columnCreationDate = New Global.System.Data.DataColumn("CreationDate", GetType(Date), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnCreationDate)
            Me.columnAccountManager = New Global.System.Data.DataColumn("AccountManager", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnAccountManager)
            Me.Constraints.Add(New Global.System.Data.UniqueConstraint("Constraint1", New Global.System.Data.DataColumn() {Me.columnContractID}, true))
            Me.columnContractID.AllowDBNull = false
            Me.columnContractID.Unique = true
            Me.columnContractNumber.AllowDBNull = false
            Me.columnContractNumber.MaxLength = 8
            Me.columnClientName.AllowDBNull = false
            Me.columnClientName.MaxLength = 250
            Me.columnProjectName.AllowDBNull = false
            Me.columnProjectName.MaxLength = 250
            Me.columnSignedBy.MaxLength = 50
            Me.columnCreatedBy.AllowDBNull = false
            Me.columnCreatedBy.MaxLength = 50
            Me.columnCreationDate.AllowDBNull = false
            Me.columnAccountManager.AllowDBNull = false
            Me.columnAccountManager.MaxLength = 151
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function NewContractInfoRow() As ContractInfoRow
            Return CType(Me.NewRow,ContractInfoRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function NewRowFromBuilder(ByVal builder As Global.System.Data.DataRowBuilder) As Global.System.Data.DataRow
            Return New ContractInfoRow(builder)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function GetRowType() As Global.System.Type
            Return GetType(ContractInfoRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowChanged(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanged(e)
            If (Not (Me.ContractInfoRowChangedEvent) Is Nothing) Then
                RaiseEvent ContractInfoRowChanged(Me, New ContractInfoRowChangeEvent(CType(e.Row,ContractInfoRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowChanging(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanging(e)
            If (Not (Me.ContractInfoRowChangingEvent) Is Nothing) Then
                RaiseEvent ContractInfoRowChanging(Me, New ContractInfoRowChangeEvent(CType(e.Row,ContractInfoRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowDeleted(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleted(e)
            If (Not (Me.ContractInfoRowDeletedEvent) Is Nothing) Then
                RaiseEvent ContractInfoRowDeleted(Me, New ContractInfoRowChangeEvent(CType(e.Row,ContractInfoRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowDeleting(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleting(e)
            If (Not (Me.ContractInfoRowDeletingEvent) Is Nothing) Then
                RaiseEvent ContractInfoRowDeleting(Me, New ContractInfoRowChangeEvent(CType(e.Row,ContractInfoRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub RemoveContractInfoRow(ByVal row As ContractInfoRow)
            Me.Rows.Remove(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Shared Function GetTypedTableSchema(ByVal xs As Global.System.Xml.Schema.XmlSchemaSet) As Global.System.Xml.Schema.XmlSchemaComplexType
            Dim type As Global.System.Xml.Schema.XmlSchemaComplexType = New Global.System.Xml.Schema.XmlSchemaComplexType()
            Dim sequence As Global.System.Xml.Schema.XmlSchemaSequence = New Global.System.Xml.Schema.XmlSchemaSequence()
            Dim ds As DataSetContractInfo = New DataSetContractInfo()
            Dim any1 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any1.Namespace = "http://www.w3.org/2001/XMLSchema"
            any1.MinOccurs = New Decimal(0)
            any1.MaxOccurs = Decimal.MaxValue
            any1.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any1)
            Dim any2 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1"
            any2.MinOccurs = New Decimal(1)
            any2.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any2)
            Dim attribute1 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute1.Name = "namespace"
            attribute1.FixedValue = ds.Namespace
            type.Attributes.Add(attribute1)
            Dim attribute2 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute2.Name = "tableTypeName"
            attribute2.FixedValue = "ContractInfoDataTable"
            type.Attributes.Add(attribute2)
            type.Particle = sequence
            Dim dsSchema As Global.System.Xml.Schema.XmlSchema = ds.GetSchemaSerializable
            If xs.Contains(dsSchema.TargetNamespace) Then
                Dim s1 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Dim s2 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Try 
                    Dim schema As Global.System.Xml.Schema.XmlSchema = Nothing
                    dsSchema.Write(s1)
                    Dim schemas As Global.System.Collections.IEnumerator = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator
                    Do While schemas.MoveNext
                        schema = CType(schemas.Current,Global.System.Xml.Schema.XmlSchema)
                        s2.SetLength(0)
                        schema.Write(s2)
                        If (s1.Length = s2.Length) Then
                            s1.Position = 0
                            s2.Position = 0
                            
                            Do While ((s1.Position <> s1.Length)  _
                                        AndAlso (s1.ReadByte = s2.ReadByte))
                                
                                
                            Loop
                            If (s1.Position = s1.Length) Then
                                Return type
                            End If
                        End If
                        
                    Loop
                Finally
                    If (Not (s1) Is Nothing) Then
                        s1.Close
                    End If
                    If (Not (s2) Is Nothing) Then
                        s2.Close
                    End If
                End Try
            End If
            xs.Add(dsSchema)
            Return type
        End Function
    End Class
    
    '''<summary>
    '''Represents the strongly named DataTable class.
    '''</summary>
    <Global.System.Serializable(),  _
     Global.System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")>  _
    Partial Public Class BurstInfoDataTable
        Inherits Global.System.Data.TypedTableBase(Of BurstInfoRow)
        
        Private columnBurstID As Global.System.Data.DataColumn
        
        Private columnContractID As Global.System.Data.DataColumn
        
        Private columnMediaName As Global.System.Data.DataColumn
        
        Private columnChainName As Global.System.Data.DataColumn
        
        Private columnCategoryName As Global.System.Data.DataColumn
        
        Private columnHomesite As Global.System.Data.DataColumn
        
        Private columnBrandName As Global.System.Data.DataColumn
        
        Private columnProductName As Global.System.Data.DataColumn
        
        Private columnFirstWeek As Global.System.Data.DataColumn
        
        Private columnLastInstallWeek As Global.System.Data.DataColumn
        
        Private columnInstallStoreQty As Global.System.Data.DataColumn
        
        Private columnInstallWeeks As Global.System.Data.DataColumn
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New()
            MyBase.New
            Me.TableName = "BurstInfo"
            Me.BeginInit
            Me.InitClass
            Me.EndInit
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub New(ByVal table As Global.System.Data.DataTable)
            MyBase.New
            Me.TableName = table.TableName
            If (table.CaseSensitive <> table.DataSet.CaseSensitive) Then
                Me.CaseSensitive = table.CaseSensitive
            End If
            If (table.Locale.ToString <> table.DataSet.Locale.ToString) Then
                Me.Locale = table.Locale
            End If
            If (table.Namespace <> table.DataSet.Namespace) Then
                Me.Namespace = table.Namespace
            End If
            Me.Prefix = table.Prefix
            Me.MinimumCapacity = table.MinimumCapacity
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Sub New(ByVal info As Global.System.Runtime.Serialization.SerializationInfo, ByVal context As Global.System.Runtime.Serialization.StreamingContext)
            MyBase.New(info, context)
            Me.InitVars
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property BurstIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnBurstID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ContractIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnContractID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property MediaNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnMediaName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ChainNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnChainName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property CategoryNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnCategoryName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property HomesiteColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnHomesite
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property BrandNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnBrandName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ProductNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnProductName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property FirstWeekColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnFirstWeek
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property LastInstallWeekColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnLastInstallWeek
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property InstallStoreQtyColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnInstallStoreQty
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property InstallWeeksColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnInstallWeeks
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Browsable(false)>  _
        Public ReadOnly Property Count() As Integer
            Get
                Return Me.Rows.Count
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Default ReadOnly Property Item(ByVal index As Integer) As BurstInfoRow
            Get
                Return CType(Me.Rows(index),BurstInfoRow)
            End Get
        End Property
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event BurstInfoRowChanging As BurstInfoRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event BurstInfoRowChanged As BurstInfoRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event BurstInfoRowDeleting As BurstInfoRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event BurstInfoRowDeleted As BurstInfoRowChangeEventHandler
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overloads Sub AddBurstInfoRow(ByVal row As BurstInfoRow)
            Me.Rows.Add(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overloads Function AddBurstInfoRow(ByVal BurstID As System.Guid, ByVal parentContractInfoRowByContractInfo_BurstInfo As ContractInfoRow, ByVal MediaName As String, ByVal ChainName As String, ByVal CategoryName As String, ByVal Homesite As Boolean, ByVal BrandName As String, ByVal ProductName As String, ByVal FirstWeek As Date, ByVal LastInstallWeek As Date, ByVal InstallStoreQty As Integer, ByVal InstallWeeks As Integer) As BurstInfoRow
            Dim rowBurstInfoRow As BurstInfoRow = CType(Me.NewRow,BurstInfoRow)
            Dim columnValuesArray() As Object = New Object() {BurstID, Nothing, MediaName, ChainName, CategoryName, Homesite, BrandName, ProductName, FirstWeek, LastInstallWeek, InstallStoreQty, InstallWeeks}
            If (Not (parentContractInfoRowByContractInfo_BurstInfo) Is Nothing) Then
                columnValuesArray(1) = parentContractInfoRowByContractInfo_BurstInfo(0)
            End If
            rowBurstInfoRow.ItemArray = columnValuesArray
            Me.Rows.Add(rowBurstInfoRow)
            Return rowBurstInfoRow
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overrides Function Clone() As Global.System.Data.DataTable
            Dim cln As BurstInfoDataTable = CType(MyBase.Clone,BurstInfoDataTable)
            cln.InitVars
            Return cln
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function CreateInstance() As Global.System.Data.DataTable
            Return New BurstInfoDataTable()
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub InitVars()
            Me.columnBurstID = MyBase.Columns("BurstID")
            Me.columnContractID = MyBase.Columns("ContractID")
            Me.columnMediaName = MyBase.Columns("MediaName")
            Me.columnChainName = MyBase.Columns("ChainName")
            Me.columnCategoryName = MyBase.Columns("CategoryName")
            Me.columnHomesite = MyBase.Columns("Homesite")
            Me.columnBrandName = MyBase.Columns("BrandName")
            Me.columnProductName = MyBase.Columns("ProductName")
            Me.columnFirstWeek = MyBase.Columns("FirstWeek")
            Me.columnLastInstallWeek = MyBase.Columns("LastInstallWeek")
            Me.columnInstallStoreQty = MyBase.Columns("InstallStoreQty")
            Me.columnInstallWeeks = MyBase.Columns("InstallWeeks")
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitClass()
            Me.columnBurstID = New Global.System.Data.DataColumn("BurstID", GetType(Global.System.Guid), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnBurstID)
            Me.columnContractID = New Global.System.Data.DataColumn("ContractID", GetType(Global.System.Guid), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnContractID)
            Me.columnMediaName = New Global.System.Data.DataColumn("MediaName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnMediaName)
            Me.columnChainName = New Global.System.Data.DataColumn("ChainName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnChainName)
            Me.columnCategoryName = New Global.System.Data.DataColumn("CategoryName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnCategoryName)
            Me.columnHomesite = New Global.System.Data.DataColumn("Homesite", GetType(Boolean), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnHomesite)
            Me.columnBrandName = New Global.System.Data.DataColumn("BrandName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnBrandName)
            Me.columnProductName = New Global.System.Data.DataColumn("ProductName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnProductName)
            Me.columnFirstWeek = New Global.System.Data.DataColumn("FirstWeek", GetType(Date), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnFirstWeek)
            Me.columnLastInstallWeek = New Global.System.Data.DataColumn("LastInstallWeek", GetType(Date), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnLastInstallWeek)
            Me.columnInstallStoreQty = New Global.System.Data.DataColumn("InstallStoreQty", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnInstallStoreQty)
            Me.columnInstallWeeks = New Global.System.Data.DataColumn("InstallWeeks", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnInstallWeeks)
            Me.columnBurstID.AllowDBNull = false
            Me.columnContractID.AllowDBNull = false
            Me.columnMediaName.AllowDBNull = false
            Me.columnMediaName.MaxLength = 200
            Me.columnChainName.AllowDBNull = false
            Me.columnChainName.MaxLength = 200
            Me.columnCategoryName.AllowDBNull = false
            Me.columnCategoryName.MaxLength = 200
            Me.columnHomesite.ReadOnly = true
            Me.columnBrandName.MaxLength = 50
            Me.columnProductName.MaxLength = 200
            Me.columnFirstWeek.AllowDBNull = false
            Me.columnLastInstallWeek.ReadOnly = true
            Me.columnInstallStoreQty.AllowDBNull = false
            Me.columnInstallWeeks.AllowDBNull = false
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function NewBurstInfoRow() As BurstInfoRow
            Return CType(Me.NewRow,BurstInfoRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function NewRowFromBuilder(ByVal builder As Global.System.Data.DataRowBuilder) As Global.System.Data.DataRow
            Return New BurstInfoRow(builder)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function GetRowType() As Global.System.Type
            Return GetType(BurstInfoRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowChanged(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanged(e)
            If (Not (Me.BurstInfoRowChangedEvent) Is Nothing) Then
                RaiseEvent BurstInfoRowChanged(Me, New BurstInfoRowChangeEvent(CType(e.Row,BurstInfoRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowChanging(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanging(e)
            If (Not (Me.BurstInfoRowChangingEvent) Is Nothing) Then
                RaiseEvent BurstInfoRowChanging(Me, New BurstInfoRowChangeEvent(CType(e.Row,BurstInfoRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowDeleted(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleted(e)
            If (Not (Me.BurstInfoRowDeletedEvent) Is Nothing) Then
                RaiseEvent BurstInfoRowDeleted(Me, New BurstInfoRowChangeEvent(CType(e.Row,BurstInfoRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowDeleting(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleting(e)
            If (Not (Me.BurstInfoRowDeletingEvent) Is Nothing) Then
                RaiseEvent BurstInfoRowDeleting(Me, New BurstInfoRowChangeEvent(CType(e.Row,BurstInfoRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub RemoveBurstInfoRow(ByVal row As BurstInfoRow)
            Me.Rows.Remove(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Shared Function GetTypedTableSchema(ByVal xs As Global.System.Xml.Schema.XmlSchemaSet) As Global.System.Xml.Schema.XmlSchemaComplexType
            Dim type As Global.System.Xml.Schema.XmlSchemaComplexType = New Global.System.Xml.Schema.XmlSchemaComplexType()
            Dim sequence As Global.System.Xml.Schema.XmlSchemaSequence = New Global.System.Xml.Schema.XmlSchemaSequence()
            Dim ds As DataSetContractInfo = New DataSetContractInfo()
            Dim any1 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any1.Namespace = "http://www.w3.org/2001/XMLSchema"
            any1.MinOccurs = New Decimal(0)
            any1.MaxOccurs = Decimal.MaxValue
            any1.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any1)
            Dim any2 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1"
            any2.MinOccurs = New Decimal(1)
            any2.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any2)
            Dim attribute1 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute1.Name = "namespace"
            attribute1.FixedValue = ds.Namespace
            type.Attributes.Add(attribute1)
            Dim attribute2 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute2.Name = "tableTypeName"
            attribute2.FixedValue = "BurstInfoDataTable"
            type.Attributes.Add(attribute2)
            type.Particle = sequence
            Dim dsSchema As Global.System.Xml.Schema.XmlSchema = ds.GetSchemaSerializable
            If xs.Contains(dsSchema.TargetNamespace) Then
                Dim s1 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Dim s2 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Try 
                    Dim schema As Global.System.Xml.Schema.XmlSchema = Nothing
                    dsSchema.Write(s1)
                    Dim schemas As Global.System.Collections.IEnumerator = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator
                    Do While schemas.MoveNext
                        schema = CType(schemas.Current,Global.System.Xml.Schema.XmlSchema)
                        s2.SetLength(0)
                        schema.Write(s2)
                        If (s1.Length = s2.Length) Then
                            s1.Position = 0
                            s2.Position = 0
                            
                            Do While ((s1.Position <> s1.Length)  _
                                        AndAlso (s1.ReadByte = s2.ReadByte))
                                
                                
                            Loop
                            If (s1.Position = s1.Length) Then
                                Return type
                            End If
                        End If
                        
                    Loop
                Finally
                    If (Not (s1) Is Nothing) Then
                        s1.Close
                    End If
                    If (Not (s2) Is Nothing) Then
                        s2.Close
                    End If
                End Try
            End If
            xs.Add(dsSchema)
            Return type
        End Function
    End Class
    
    '''<summary>
    '''Represents strongly named DataRow class.
    '''</summary>
    Partial Public Class ContractInfoRow
        Inherits Global.System.Data.DataRow
        
        Private tableContractInfo As ContractInfoDataTable
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub New(ByVal rb As Global.System.Data.DataRowBuilder)
            MyBase.New(rb)
            Me.tableContractInfo = CType(Me.Table,ContractInfoDataTable)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ContractID() As System.Guid
            Get
                Return CType(Me(Me.tableContractInfo.ContractIDColumn),Global.System.Guid)
            End Get
            Set
                Me(Me.tableContractInfo.ContractIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ContractNumber() As String
            Get
                Return CType(Me(Me.tableContractInfo.ContractNumberColumn),String)
            End Get
            Set
                Me(Me.tableContractInfo.ContractNumberColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ClientName() As String
            Get
                Return CType(Me(Me.tableContractInfo.ClientNameColumn),String)
            End Get
            Set
                Me(Me.tableContractInfo.ClientNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ProjectName() As String
            Get
                Return CType(Me(Me.tableContractInfo.ProjectNameColumn),String)
            End Get
            Set
                Me(Me.tableContractInfo.ProjectNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property SignedBy() As String
            Get
                Try 
                    Return CType(Me(Me.tableContractInfo.SignedByColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'SignedBy' in table 'ContractInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableContractInfo.SignedByColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property SignDate() As Date
            Get
                Try 
                    Return CType(Me(Me.tableContractInfo.SignDateColumn),Date)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'SignDate' in table 'ContractInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableContractInfo.SignDateColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property CreatedBy() As String
            Get
                Return CType(Me(Me.tableContractInfo.CreatedByColumn),String)
            End Get
            Set
                Me(Me.tableContractInfo.CreatedByColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property CreationDate() As Date
            Get
                Return CType(Me(Me.tableContractInfo.CreationDateColumn),Date)
            End Get
            Set
                Me(Me.tableContractInfo.CreationDateColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property AccountManager() As String
            Get
                Return CType(Me(Me.tableContractInfo.AccountManagerColumn),String)
            End Get
            Set
                Me(Me.tableContractInfo.AccountManagerColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsSignedByNull() As Boolean
            Return Me.IsNull(Me.tableContractInfo.SignedByColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetSignedByNull()
            Me(Me.tableContractInfo.SignedByColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsSignDateNull() As Boolean
            Return Me.IsNull(Me.tableContractInfo.SignDateColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetSignDateNull()
            Me(Me.tableContractInfo.SignDateColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function GetBurstInfoRows() As BurstInfoRow()
            If (Me.Table.ChildRelations("ContractInfo_BurstInfo") Is Nothing) Then
                Return New BurstInfoRow(-1) {}
            Else
                Return CType(MyBase.GetChildRows(Me.Table.ChildRelations("ContractInfo_BurstInfo")),BurstInfoRow())
            End If
        End Function
    End Class
    
    '''<summary>
    '''Represents strongly named DataRow class.
    '''</summary>
    Partial Public Class BurstInfoRow
        Inherits Global.System.Data.DataRow
        
        Private tableBurstInfo As BurstInfoDataTable
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub New(ByVal rb As Global.System.Data.DataRowBuilder)
            MyBase.New(rb)
            Me.tableBurstInfo = CType(Me.Table,BurstInfoDataTable)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property BurstID() As System.Guid
            Get
                Return CType(Me(Me.tableBurstInfo.BurstIDColumn),Global.System.Guid)
            End Get
            Set
                Me(Me.tableBurstInfo.BurstIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ContractID() As System.Guid
            Get
                Return CType(Me(Me.tableBurstInfo.ContractIDColumn),Global.System.Guid)
            End Get
            Set
                Me(Me.tableBurstInfo.ContractIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property MediaName() As String
            Get
                Return CType(Me(Me.tableBurstInfo.MediaNameColumn),String)
            End Get
            Set
                Me(Me.tableBurstInfo.MediaNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ChainName() As String
            Get
                Return CType(Me(Me.tableBurstInfo.ChainNameColumn),String)
            End Get
            Set
                Me(Me.tableBurstInfo.ChainNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property CategoryName() As String
            Get
                Return CType(Me(Me.tableBurstInfo.CategoryNameColumn),String)
            End Get
            Set
                Me(Me.tableBurstInfo.CategoryNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property Homesite() As Boolean
            Get
                Try 
                    Return CType(Me(Me.tableBurstInfo.HomesiteColumn),Boolean)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'Homesite' in table 'BurstInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableBurstInfo.HomesiteColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property BrandName() As String
            Get
                Try 
                    Return CType(Me(Me.tableBurstInfo.BrandNameColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'BrandName' in table 'BurstInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableBurstInfo.BrandNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ProductName() As String
            Get
                Try 
                    Return CType(Me(Me.tableBurstInfo.ProductNameColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'ProductName' in table 'BurstInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableBurstInfo.ProductNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property FirstWeek() As Date
            Get
                Return CType(Me(Me.tableBurstInfo.FirstWeekColumn),Date)
            End Get
            Set
                Me(Me.tableBurstInfo.FirstWeekColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property LastInstallWeek() As Date
            Get
                Try 
                    Return CType(Me(Me.tableBurstInfo.LastInstallWeekColumn),Date)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'LastInstallWeek' in table 'BurstInfo' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableBurstInfo.LastInstallWeekColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property InstallStoreQty() As Integer
            Get
                Return CType(Me(Me.tableBurstInfo.InstallStoreQtyColumn),Integer)
            End Get
            Set
                Me(Me.tableBurstInfo.InstallStoreQtyColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property InstallWeeks() As Integer
            Get
                Return CType(Me(Me.tableBurstInfo.InstallWeeksColumn),Integer)
            End Get
            Set
                Me(Me.tableBurstInfo.InstallWeeksColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property vViewableContractInfoRow() As ContractInfoRow
            Get
                Return CType(Me.GetParentRow(Me.Table.ParentRelations("ContractInfo_BurstInfo")),ContractInfoRow)
            End Get
            Set
                Me.SetParentRow(value, Me.Table.ParentRelations("ContractInfo_BurstInfo"))
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsHomesiteNull() As Boolean
            Return Me.IsNull(Me.tableBurstInfo.HomesiteColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetHomesiteNull()
            Me(Me.tableBurstInfo.HomesiteColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsBrandNameNull() As Boolean
            Return Me.IsNull(Me.tableBurstInfo.BrandNameColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetBrandNameNull()
            Me(Me.tableBurstInfo.BrandNameColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsProductNameNull() As Boolean
            Return Me.IsNull(Me.tableBurstInfo.ProductNameColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetProductNameNull()
            Me(Me.tableBurstInfo.ProductNameColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsLastInstallWeekNull() As Boolean
            Return Me.IsNull(Me.tableBurstInfo.LastInstallWeekColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetLastInstallWeekNull()
            Me(Me.tableBurstInfo.LastInstallWeekColumn) = Global.System.Convert.DBNull
        End Sub
    End Class
    
    '''<summary>
    '''Row event argument class
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Class ContractInfoRowChangeEvent
        Inherits Global.System.EventArgs
        
        Private eventRow As ContractInfoRow
        
        Private eventAction As Global.System.Data.DataRowAction
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New(ByVal row As ContractInfoRow, ByVal action As Global.System.Data.DataRowAction)
            MyBase.New
            Me.eventRow = row
            Me.eventAction = action
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property Row() As ContractInfoRow
            Get
                Return Me.eventRow
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property Action() As Global.System.Data.DataRowAction
            Get
                Return Me.eventAction
            End Get
        End Property
    End Class
    
    '''<summary>
    '''Row event argument class
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Class BurstInfoRowChangeEvent
        Inherits Global.System.EventArgs
        
        Private eventRow As BurstInfoRow
        
        Private eventAction As Global.System.Data.DataRowAction
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New(ByVal row As BurstInfoRow, ByVal action As Global.System.Data.DataRowAction)
            MyBase.New
            Me.eventRow = row
            Me.eventAction = action
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property Row() As BurstInfoRow
            Get
                Return Me.eventRow
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property Action() As Global.System.Data.DataRowAction
            Get
                Return Me.eventAction
            End Get
        End Property
    End Class
End Class

Namespace DataSetContractInfoTableAdapters
    
    '''<summary>
    '''Represents the connection and commands used to retrieve and save data.
    '''</summary>
    <Global.System.ComponentModel.DesignerCategoryAttribute("code"),  _
     Global.System.ComponentModel.ToolboxItem(true),  _
     Global.System.ComponentModel.DataObjectAttribute(true),  _
     Global.System.ComponentModel.DesignerAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterDesigner, Microsoft.VSDesigner"& _ 
        ", Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"),  _
     Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
    Partial Public Class ContractInfoTableAdapter
        Inherits Global.System.ComponentModel.Component
        
        Private WithEvents _adapter As Global.System.Data.SqlClient.SqlDataAdapter
        
        Private _connection As Global.System.Data.SqlClient.SqlConnection
        
        Private _commandCollection() As Global.System.Data.SqlClient.SqlCommand
        
        Private _clearBeforeFill As Boolean
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New()
            MyBase.New
            Me.ClearBeforeFill = true
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private ReadOnly Property Adapter() As Global.System.Data.SqlClient.SqlDataAdapter
            Get
                If (Me._adapter Is Nothing) Then
                    Me.InitAdapter
                End If
                Return Me._adapter
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property Connection() As Global.System.Data.SqlClient.SqlConnection
            Get
                If (Me._connection Is Nothing) Then
                    Me.InitConnection
                End If
                Return Me._connection
            End Get
            Set
                Me._connection = value
                If (Not (Me.Adapter.InsertCommand) Is Nothing) Then
                    Me.Adapter.InsertCommand.Connection = value
                End If
                If (Not (Me.Adapter.DeleteCommand) Is Nothing) Then
                    Me.Adapter.DeleteCommand.Connection = value
                End If
                If (Not (Me.Adapter.UpdateCommand) Is Nothing) Then
                    Me.Adapter.UpdateCommand.Connection = value
                End If
                Dim i As Integer = 0
                Do While (i < Me.CommandCollection.Length)
                    If (Not (Me.CommandCollection(i)) Is Nothing) Then
                        CType(Me.CommandCollection(i),Global.System.Data.SqlClient.SqlCommand).Connection = value
                    End If
                    i = (i + 1)
                Loop
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected ReadOnly Property CommandCollection() As Global.System.Data.SqlClient.SqlCommand()
            Get
                If (Me._commandCollection Is Nothing) Then
                    Me.InitCommandCollection
                End If
                Return Me._commandCollection
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ClearBeforeFill() As Boolean
            Get
                Return Me._clearBeforeFill
            End Get
            Set
                Me._clearBeforeFill = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitAdapter()
            Me._adapter = New Global.System.Data.SqlClient.SqlDataAdapter()
            Dim tableMapping As Global.System.Data.Common.DataTableMapping = New Global.System.Data.Common.DataTableMapping()
            tableMapping.SourceTable = "Table"
            tableMapping.DataSetTable = "ContractInfo"
            tableMapping.ColumnMappings.Add("ContractID", "ContractID")
            tableMapping.ColumnMappings.Add("ContractNumber", "ContractNumber")
            tableMapping.ColumnMappings.Add("ClientName", "ClientName")
            tableMapping.ColumnMappings.Add("ProjectName", "ProjectName")
            tableMapping.ColumnMappings.Add("SignedBy", "SignedBy")
            tableMapping.ColumnMappings.Add("SignDate", "SignDate")
            tableMapping.ColumnMappings.Add("CreatedBy", "CreatedBy")
            tableMapping.ColumnMappings.Add("CreationDate", "CreationDate")
            tableMapping.ColumnMappings.Add("AccountManager", "AccountManager")
            Me._adapter.TableMappings.Add(tableMapping)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitConnection()
            Me._connection = New Global.System.Data.SqlClient.SqlConnection()
            Me._connection.ConnectionString = Global.NovaData.My.MySettings.Default.DBConnection
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitCommandCollection()
            Me._commandCollection = New Global.System.Data.SqlClient.SqlCommand(0) {}
            Me._commandCollection(0) = New Global.System.Data.SqlClient.SqlCommand()
            Me._commandCollection(0).Connection = Me.Connection
            Me._commandCollection(0).CommandText = "SELECT        ContractID, ContractNumber, ClientName, ProjectName, SignedBy, Sign"& _ 
                "Date, CreatedBy, CreationDate, AccountManager"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"FROM            Sales.vViewableCo"& _ 
                "ntractInfo"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"WHERE        (ContractID = @ContractID)"
            Me._commandCollection(0).CommandType = Global.System.Data.CommandType.Text
            Me._commandCollection(0).Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@ContractID", Global.System.Data.SqlDbType.UniqueIdentifier, 16, Global.System.Data.ParameterDirection.Input, 0, 0, "ContractID", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.Fill, true)>  _
        Public Overloads Overridable Function Fill(ByVal dataTable As DataSetContractInfo.ContractInfoDataTable, ByVal ContractID As System.Guid) As Integer
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            Me.Adapter.SelectCommand.Parameters(0).Value = CType(ContractID,System.Guid)
            If (Me.ClearBeforeFill = true) Then
                dataTable.Clear
            End If
            Dim returnValue As Integer = Me.Adapter.Fill(dataTable)
            Return returnValue
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.[Select], true)>  _
        Public Overloads Overridable Function GetData(ByVal ContractID As System.Guid) As DataSetContractInfo.ContractInfoDataTable
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            Me.Adapter.SelectCommand.Parameters(0).Value = CType(ContractID,System.Guid)
            Dim dataTable As DataSetContractInfo.ContractInfoDataTable = New DataSetContractInfo.ContractInfoDataTable()
            Me.Adapter.Fill(dataTable)
            Return dataTable
        End Function
    End Class
    
    '''<summary>
    '''Represents the connection and commands used to retrieve and save data.
    '''</summary>
    <Global.System.ComponentModel.DesignerCategoryAttribute("code"),  _
     Global.System.ComponentModel.ToolboxItem(true),  _
     Global.System.ComponentModel.DataObjectAttribute(true),  _
     Global.System.ComponentModel.DesignerAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterDesigner, Microsoft.VSDesigner"& _ 
        ", Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"),  _
     Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
    Partial Public Class BurstInfoTableAdapter
        Inherits Global.System.ComponentModel.Component
        
        Private WithEvents _adapter As Global.System.Data.SqlClient.SqlDataAdapter
        
        Private _connection As Global.System.Data.SqlClient.SqlConnection
        
        Private _commandCollection() As Global.System.Data.SqlClient.SqlCommand
        
        Private _clearBeforeFill As Boolean
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New()
            MyBase.New
            Me.ClearBeforeFill = true
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private ReadOnly Property Adapter() As Global.System.Data.SqlClient.SqlDataAdapter
            Get
                If (Me._adapter Is Nothing) Then
                    Me.InitAdapter
                End If
                Return Me._adapter
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property Connection() As Global.System.Data.SqlClient.SqlConnection
            Get
                If (Me._connection Is Nothing) Then
                    Me.InitConnection
                End If
                Return Me._connection
            End Get
            Set
                Me._connection = value
                If (Not (Me.Adapter.InsertCommand) Is Nothing) Then
                    Me.Adapter.InsertCommand.Connection = value
                End If
                If (Not (Me.Adapter.DeleteCommand) Is Nothing) Then
                    Me.Adapter.DeleteCommand.Connection = value
                End If
                If (Not (Me.Adapter.UpdateCommand) Is Nothing) Then
                    Me.Adapter.UpdateCommand.Connection = value
                End If
                Dim i As Integer = 0
                Do While (i < Me.CommandCollection.Length)
                    If (Not (Me.CommandCollection(i)) Is Nothing) Then
                        CType(Me.CommandCollection(i),Global.System.Data.SqlClient.SqlCommand).Connection = value
                    End If
                    i = (i + 1)
                Loop
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected ReadOnly Property CommandCollection() As Global.System.Data.SqlClient.SqlCommand()
            Get
                If (Me._commandCollection Is Nothing) Then
                    Me.InitCommandCollection
                End If
                Return Me._commandCollection
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ClearBeforeFill() As Boolean
            Get
                Return Me._clearBeforeFill
            End Get
            Set
                Me._clearBeforeFill = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitAdapter()
            Me._adapter = New Global.System.Data.SqlClient.SqlDataAdapter()
            Dim tableMapping As Global.System.Data.Common.DataTableMapping = New Global.System.Data.Common.DataTableMapping()
            tableMapping.SourceTable = "Table"
            tableMapping.DataSetTable = "BurstInfo"
            tableMapping.ColumnMappings.Add("BurstID", "BurstID")
            tableMapping.ColumnMappings.Add("ContractID", "ContractID")
            tableMapping.ColumnMappings.Add("MediaName", "MediaName")
            tableMapping.ColumnMappings.Add("ChainName", "ChainName")
            tableMapping.ColumnMappings.Add("CategoryName", "CategoryName")
            tableMapping.ColumnMappings.Add("Homesite", "Homesite")
            tableMapping.ColumnMappings.Add("BrandName", "BrandName")
            tableMapping.ColumnMappings.Add("ProductName", "ProductName")
            tableMapping.ColumnMappings.Add("FirstWeek", "FirstWeek")
            tableMapping.ColumnMappings.Add("LastInstallWeek", "LastInstallWeek")
            tableMapping.ColumnMappings.Add("InstallStoreQty", "InstallStoreQty")
            tableMapping.ColumnMappings.Add("InstallWeeks", "InstallWeeks")
            Me._adapter.TableMappings.Add(tableMapping)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitConnection()
            Me._connection = New Global.System.Data.SqlClient.SqlConnection()
            Me._connection.ConnectionString = Global.NovaData.My.MySettings.Default.DBConnection
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitCommandCollection()
            Me._commandCollection = New Global.System.Data.SqlClient.SqlCommand(0) {}
            Me._commandCollection(0) = New Global.System.Data.SqlClient.SqlCommand()
            Me._commandCollection(0).Connection = Me.Connection
            Me._commandCollection(0).CommandText = "SELECT DISTINCT "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         Sales.vEffectiveBurst.BurstID, Sales.v"& _ 
                "EffectiveBurst.ContractID, Sales.vEffectiveBurst.MediaName, Sales.vEffectiveBurs"& _ 
                "t.ChainName, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         Store.Category.CategoryName, Sales.vEffe"& _ 
                "ctiveBurst.Homesite, Sales.vEffectiveBurst.BrandName, Sales.vEffectiveBurst.Prod"& _ 
                "uctName, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         Sales.vEffectiveBurst.FirstWeek, Sales.vEffe"& _ 
                "ctiveBurst.LastInstallWeek, Sales.vEffectiveBurst.InstallStoreQty, Sales.vEffect"& _ 
                "iveBurst.InstallWeeks"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"FROM            Sales.vEffectiveBurst INNER JOIN"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"       "& _ 
                "                  Store.Category ON Sales.vEffectiveBurst.CategoryID = Store.Cat"& _ 
                "egory.CategoryID"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"WHERE        (Sales.vEffectiveBurst.ContractID = @ContractID)"
            Me._commandCollection(0).CommandType = Global.System.Data.CommandType.Text
            Me._commandCollection(0).Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@ContractID", Global.System.Data.SqlDbType.UniqueIdentifier, 16, Global.System.Data.ParameterDirection.Input, 0, 0, "ContractID", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.Fill, true)>  _
        Public Overloads Overridable Function Fill(ByVal dataTable As DataSetContractInfo.BurstInfoDataTable, ByVal ContractID As System.Guid) As Integer
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            Me.Adapter.SelectCommand.Parameters(0).Value = CType(ContractID,System.Guid)
            If (Me.ClearBeforeFill = true) Then
                dataTable.Clear
            End If
            Dim returnValue As Integer = Me.Adapter.Fill(dataTable)
            Return returnValue
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.[Select], true)>  _
        Public Overloads Overridable Function GetData(ByVal ContractID As System.Guid) As DataSetContractInfo.BurstInfoDataTable
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            Me.Adapter.SelectCommand.Parameters(0).Value = CType(ContractID,System.Guid)
            Dim dataTable As DataSetContractInfo.BurstInfoDataTable = New DataSetContractInfo.BurstInfoDataTable()
            Me.Adapter.Fill(dataTable)
            Return dataTable
        End Function
    End Class
End Namespace
