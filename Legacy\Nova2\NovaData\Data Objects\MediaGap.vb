Imports System.Text

Public Class MediaGap

#Region "Global Variables"

    Private MediaGapDataSet As New DataSetMediaGap
    Public ProvisionalBookingData As DataTable = MediaGapDataSet.Tables("ProvisionalBooking")
    Private BurstData As DataTable = MediaGapDataSet.Tables("BurstData")
    Private ProvisionalBookingBindingSource As New BindingSource(MediaGapDataSet, ProvisionalBookingData.TableName)
    Private ProvisionalBookingBySelectionBindingSource As New BindingSource
    Public ContractListBindingSource As BindingSource
    Public FirstWeekToView As Date = Today.Date
    Private WeeksToView As Integer = 104
    Private _ChainsToView As DataTable
    Private _MediaFamiliesToView As DataTable
    Private _CategoriesToView As DataTable
    Private GridDataTable As New DataTable
    Private GridBindingSource As BindingSource
    Public WithEvents Grid As DataGridView
    Public WithEvents ProvisionalBookingGrid As DataGridView
    Private ConsumingForm As LiquidShell.BaseForm
    Public ConnectionString As String = String.Empty
    Public CellInfoCollection As New Dictionary(Of String, CellInfo)

#End Region

#Region "Properties"

    Public Property ChainsToView() As DataTable
        Get
            If IsNothing(_ChainsToView) Then
                ' The data table doesn't exist. Create a new one so that a null value isn't returned.
                Dim Columns(1) As DataColumn
                Columns(0) = New DataColumn("ChainID", GetType(Integer))
                Columns(1) = New DataColumn("ChainName", GetType(String))
                _ChainsToView = New DataTable
                _ChainsToView.Columns.AddRange(Columns)
            End If
            Return _ChainsToView
        End Get
        Set(ByVal value As DataTable)
            _ChainsToView = value
        End Set
    End Property

    Public Property MediaFamiliesToView() As DataTable
        Get
            If IsNothing(_MediaFamiliesToView) Then
                ' The data table doesn't exist. Create a new one so that a null value isn't returned.
                Dim Columns(1) As DataColumn
                Columns(0) = New DataColumn("MediaFamilyID", GetType(Integer))
                Columns(1) = New DataColumn("MediaFamilyName", GetType(String))
                _MediaFamiliesToView = New DataTable
                _MediaFamiliesToView.Columns.AddRange(Columns)
            End If
            Return _MediaFamiliesToView
        End Get
        Set(ByVal value As DataTable)
            _MediaFamiliesToView = value
        End Set
    End Property

    Public Property CategoriesToView() As DataTable
        Get
            If IsNothing(_CategoriesToView) Then
                ' The data table doesn't exist. Create a new one so that a null value isn't returned.
                Dim Columns(1) As DataColumn
                Columns(0) = New DataColumn("CategoryID", GetType(Integer))
                Columns(1) = New DataColumn("CategoryName", GetType(String))
                _CategoriesToView = New DataTable
                _CategoriesToView.Columns.AddRange(Columns)
            End If
            Return _CategoriesToView
        End Get
        Set(ByVal value As DataTable)
            _CategoriesToView = value
        End Set
    End Property

#End Region

#Region "Event Handlers"

    Private Sub Grid_CellFormatting _
    (ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellFormattingEventArgs) _
    Handles Grid.CellFormatting

        ' Exit if the cell is a the label for the row (first column).
        If e.ColumnIndex = 0 Then
            Exit Sub
        End If

        ' Shade the cell with a colour if it contains provisional booking data.
        Dim Cell As DataGridViewCell = Grid(e.ColumnIndex, e.RowIndex)
        Dim Key As String = e.ColumnIndex.ToString("000") & e.RowIndex.ToString("000")
        Cell.Style.BackColor = CellInfoCollection(Key).BackColor
        Cell.Style.SelectionBackColor = CellInfoCollection(Key).SelectionBackColor

    End Sub

    Private Sub Grid_SelectionChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles Grid.SelectionChanged

        ' Update the list of contracts linked to the selected cells.
        PopulateContractListTable()

        ' Create a dictionary to hold the list of provisional bookings linked to each selected cell.
        Dim ProvisionalBookingList As Dictionary(Of Guid, Dictionary(Of String, Object))

        ' Create a list to hold the IDs of the provisional bookings linked to each selected cell.
        Dim FilterList As New List(Of Guid)

        ' Populate the above list of GUIDs with the IDs of all provisional bookings in the selected area of the grid.
        For Each SelectedCell As DataGridViewCell In Grid.SelectedCells
            If SelectedCell.ColumnIndex > 0 Then
                ' The selected cell doesn't appear in the chain name column. It's safe to proceed.
                ' Get the list of provisional bookings that appear in this cell.
                Dim Key As String = SelectedCell.ColumnIndex.ToString("000") & SelectedCell.RowIndex.ToString("000")
                ProvisionalBookingList = CellInfoCollection(Key).ProvisionalBookingList
                For Each ProvisionalBookingID As Guid In ProvisionalBookingList.Keys
                    If FilterList.Contains(ProvisionalBookingID) = False Then

                        ' Stop if the list of IDs to filter by is getting too long.
                        If FilterList.Count > 100 Then
                            ' The list is getting too big. A StackOverFlowException might occur if we leave it to continue.
                            ConsumingForm.ShowMessage("Too much data has been selected. I'm starting to see purple elephants." _
                            & vbCrLf & vbCrLf & "Please reduce the displayed media gap data by selecting fewer categories or" _
                            & vbCrLf & "media families.", "Brain Overload", MessageBoxIcon.Error)
                            Exit Sub
                        End If

                        FilterList.Add(ProvisionalBookingID)
                    End If
                Next
            End If
        Next

        ' Build a filter using the list of GUIDs.
        Dim Filter As New StringBuilder
        If FilterList.Count = 0 Then
            ' The filter list contains no provisional booking IDs.
            Filter.Append("ProvisionalBookingID = ''")
        Else
            ' The filter list contains at least one provisional booking ID. Add it to the filter string builder.
            For Each ID As Guid In FilterList
                If Filter.Length > 0 Then
                    Filter.Append(" OR ")
                End If
                Filter.Append("ProvisionalBookingID = '" & ID.ToString & "'")
            Next
        End If

        ' Apply the filter to the binding source of the provisional booking data table (not the list in the grid).
        ProvisionalBookingBindingSource.Filter = Filter.ToString

        ' Update the ProvisionalBookingBySelectionTable table with the current list of the ProvisionalBookingBindingSource.
        Dim SelectedProvisionalBookings As DataTable = CType(ProvisionalBookingBindingSource.List, DataView).ToTable
        SelectedProvisionalBookings.AcceptChanges()
        ProvisionalBookingBySelectionBindingSource.DataSource = SelectedProvisionalBookings

    End Sub

#End Region

#Region "Public Shared Methods"

    Public Shared Function GetRowDescription(ByVal Row As DataRow) As String

        ' Create a string builder to contruct the description.
        Dim Description As New System.Text.StringBuilder

        ' Add descriptive elements to the string builder.
        If Row.RowState = DataRowState.Detached Then
            Description.Append("New provisional booking")
        Else
            ' Add the media family.
            Description.Append(Row("MediaFamilyName"))
            ' Add the book time.
            Description.Append(" booked at " & CDate(Row("BookTime")).ToString("HH:mm"))
            ' Add the book date.
            Description.Append(" on " & CDate(Row("BookTime")).Date.ToString("d MMMM yyyy"))
            ' Add the category.
            Description.Append(" in " & Row("CategoryName"))
            ' Add the brand name.
            Description.Append(" for " & Row("BrandName"))
            ' Add the duration.
            Description.Append(" running for " & Row("Duration").ToString & " week(s)")
            ' Add the chain.
            Description.Append(" in " & Row("ChainName"))
            ' Add the first week date.
            Description.Append(" from " & CDate(Row("FirstWeek")).ToString("d MMMM yyyy"))
            ' Add the name.
            Description.Append(" (" & Row("ProvisionalBookingName") & ")")
        End If

        Return Description.ToString

    End Function

    Public Shared Function CreateProvisionalBookings _
    (ByVal ConnectionString As String, _
    ByVal ProvisionalBookingName As String, _
    ByVal FirstWeek As Date, _
    ByVal Duration As Integer, _
    ByVal MediaFamilyTable As DataTable, _
    ByVal CategoryTable As DataTable, _
    ByVal ChainTable As DataTable, _
    ByVal BrandID As Guid, _
    ByVal BookTime As Date, _
    ByVal ExpiryTime As Date)

        ' Create provisional booking table and table adapter.
        Dim BookingTable As New DataSetMediaGap.ProvisionalBookingDataTable
        Dim BookingAdapter As New DataSetMediaGapTableAdapters.ProvisionalBookingTableAdapter
        BookingAdapter.Connection = New SqlClient.SqlConnection(ConnectionString)

        ' Create provisional booking rows in the table.
        For Each MediaFamily As DataRow In MediaFamilyTable.Rows
            For Each Category As DataRow In CategoryTable.Rows
                For Each Chain As DataRow In ChainTable.Rows

                    Dim NewProvisionalBooking As DataRow = BookingTable.NewRow
                    With NewProvisionalBooking
                        .Item("ProvisionalBookingID") = Guid.NewGuid
                        .Item("ProvisionalBookingName") = ProvisionalBookingName
                        .Item("ChainID") = Chain("ChainID")
                        .Item("CategoryID") = Category("CategoryID")
                        .Item("MediaFamilyID") = MediaFamily("MediaFamilyID")
                        .Item("BrandID") = BrandID
                        .Item("FirstWeek") = FirstWeek
                        .Item("Duration") = Duration
                        .Item("BookTime") = BookTime
                        .Item("ExpiryTime") = ExpiryTime
                    End With
                    BookingTable.Rows.Add(NewProvisionalBooking)

                Next
            Next
        Next

        ' Update the database.
        Try
            BookingAdapter.Update(BookingTable)
        Catch ex As Exception
            MessageBox.Show(LiquidShell.LiquidAgent.GetErrorMessage(ex))
            Return False
        End Try

        ' Destroy unneeded objects.
        BookingTable.Dispose()
        BookingAdapter.Dispose()

        Return True

    End Function

    Public Shared Sub Delete(ByVal Grid As DataGridView, ByVal ConnectionString As String, ByVal Gap As MediaGap)
        ' Delete one or more rows from the database.

        ' Get the form that is consuming this method so that the ShowMessage method can be used.
        Dim Consumingform As LiquidShell.BaseForm = CType(Grid.TopLevelControl, LiquidShell.BaseForm)

        ' Stop if any selected rows have an empty brand name (i.e. the user doesn't have permission to modify the
        ' selected provisional bookings).
        For Each SelectedGridRow As DataGridViewRow In Grid.SelectedRows
            Dim Row As DataRowView = CType(SelectedGridRow.DataBoundItem, DataRowView)

            If Not String.Compare(CStr(Row("BookedBy")), My.User.Name) = 0 Then
                ' The current user is not the user that created this booking.
                If String.IsNullOrEmpty(Row("BrandName")) Then
                    Dim MessageText As String = "You do not have permission to delete at least one of the selected provisional " _
                    & "bookings."
                    Consumingform.ShowMessage(MessageText, "Sadly, You Don't Have the Required Permissions", MessageBoxIcon.Error)
                    Exit Sub
                End If
            End If

        Next

        ' Create an audit table to record this action.
        Dim AuditLog As New DataSetAudit.AuditLogDataTable

        ' Add row descriptions to the Tag properties of all selected grid rows (these will be used as the object name
        ' in the audit log entries for rows that are deleted.)
        For Each SelectedGridRow As DataGridViewRow In Grid.SelectedRows
            Dim Row As DataRow = CType(SelectedGridRow.DataBoundItem, DataRowView).Row
            SelectedGridRow.Tag = GetRowDescription(Row)
        Next

        ' Delete rows from the dataset.
        LiquidShell.LiquidAgent.DeleteParentRows _
        (Grid, String.Empty, Nothing, AuditLog, String.Empty)

        ' Get the tables and data set from which rows are being deleted to use as a parameter for the table adapters.
        Dim GridTable As DataTable = LiquidShell.LiquidAgent.GetBindingSourceTable(CType(Grid.DataSource, BindingSource))

        ' If the grid's table isn't of type ProvisionalBooking, then the rows were actually deleted from a copy of
        ' the ProvisionalBooking table. And the table adapter can't update the database using a copy. So lets delete
        ' the equivalent rows in the ProvisionalBooking table.
        If Not TypeOf GridTable Is DataSetMediaGap.ProvisionalBookingDataTable Then
            Using DeletedRows As New DataView(GridTable, "", "", DataViewRowState.Deleted)
                For Each DeletedRow As DataRowView In DeletedRows
                    Dim ProvisionalBookingRowToDelete As DataRow = Gap.ProvisionalBookingData.Rows.Find(DeletedRow("ProvisionalBookingID"))
                    If IsNothing(ProvisionalBookingRowToDelete) = False Then
                        ProvisionalBookingRowToDelete.Delete()
                    End If
                Next
            End Using
            GridTable = Gap.ProvisionalBookingData
        End If

        ' If any rows were deleted from the data set, delete them from the database too.
        Using DeletedRows As New DataView(GridTable, "", "", DataViewRowState.Deleted)
            If IsNothing(DeletedRows) = False AndAlso DeletedRows.Count > 0 Then

                ' Create and configure table adapters to connect to the database.
                Dim Adapter As New DataSetMediaGapTableAdapters.ProvisionalBookingTableAdapter
                Dim AuditAdapter As New DataSetAuditTableAdapters.AuditLogTableAdapter

                ' Create a new connection for the table adapters to use.
                Dim SqlCon As New SqlClient.SqlConnection(ConnectionString)
                Adapter.Connection = SqlCon
                AuditAdapter.Connection = SqlCon

                ' Perform the delete operation.
                Try
                    Adapter.Update(GridTable)
                    AuditAdapter.Update(AuditLog)
                Catch ex As Exception
                    Consumingform.ShowMessage("Aaaagghh!!!  An error occured while trying to delete. The technical description of the error is:" _
                    & vbCrLf & vbCrLf & LiquidShell.LiquidAgent.GetErrorMessage(ex))
                Finally
                    ' Dispose all data adapters.
                    Adapter.Dispose()
                    AuditAdapter.Dispose()
                    ' Close all connections.
                    SqlCon.Dispose()
                End Try

            End If
        End Using

        ' Refresh the grid if it is visible.
        Dim CurrentBindingSource As BindingSource = CType(Gap.ProvisionalBookingGrid.DataSource, BindingSource)
        If Object.ReferenceEquals(CurrentBindingSource, Gap.ProvisionalBookingBySelectionBindingSource) Then
            Gap.RefreshInterface()
        End If

    End Sub

    Public Shared Sub ExpireProvisionalBookings _
    (ByVal Grid As DataGridView, ByVal ConnectionString As String, ByVal Gap As MediaGap)
        ' Force all provisional booking rows in the given grid to expire.

        ' Get the form that is consuming this method so that the ShowMessage method can be used.
        Dim Consumingform As LiquidShell.BaseForm = CType(Grid.TopLevelControl, LiquidShell.BaseForm)

        ' Confirm that the user wants to expire the selected provisional bookings.
        If LiquidShell.LiquidAgent.ActionConfirmed(Grid, "Expire") = False Then
            Exit Sub
        End If

        ' Stop if any selected rows have an empty brand name (i.e. the user doesn't have permission to modify the
        ' selected provisional bookings).
        For Each SelectedGridRow As DataGridViewRow In Grid.SelectedRows
            Dim Row As DataRowView = CType(SelectedGridRow.DataBoundItem, DataRowView)
            If String.IsNullOrEmpty(Row("BrandName")) Then
                Dim MessageText As String = "You do not have permission to modify at least one of the selected provisional " _
                & "bookings."
                Consumingform.ShowMessage(MessageText, "Sadly, You Don't Have the Required Permissions", MessageBoxIcon.Error)
                Exit Sub
            End If
        Next

        ' Remember all the cells that are currently selected.
        Dim SelectedCellColumns As New List(Of Integer)
        Dim SelectedCellRows As New List(Of Integer)
        For Each SelectedCell As DataGridViewCell In Gap.Grid.SelectedCells
            SelectedCellColumns.Add(SelectedCell.ColumnIndex)
            SelectedCellRows.Add(SelectedCell.RowIndex)
        Next

        ' Create an audit table to record this action.
        Dim AuditLog As New DataSetAudit.AuditLogDataTable

        ' Get the tables and data set from which rows are being deleted to use as a parameter for the table adapters.
        Dim GridTable As DataTable = LiquidShell.LiquidAgent.GetBindingSourceTable(CType(Grid.DataSource, BindingSource))

        ' Set the Expiry Time of all selected provisional bookings to the current server time.
        Dim ServerTime As Date = Settings.GetServerTime(ConnectionString, Consumingform)
        For Each SelectedGridRow As DataGridViewRow In Grid.SelectedRows

            ' Change the Expiry Time.
            Dim ProvisionalBookingRow As DataRow = CType(SelectedGridRow.DataBoundItem, DataRowView).Row
            ProvisionalBookingRow("ExpiryTime") = ServerTime

            ' Add an audit log entry for this change.
            AddLog(GetRowDescription(ProvisionalBookingRow), "ExpiryTime", ServerTime.ToString("d MMM yyyy HH:mm"), AuditLog)

        Next

        ' If the grid's table isn't of type ProvisionalBooking, then the modified rows were actually from a copy of
        ' the ProvisionalBooking table. And the table adapter can't update the database using a copy. So lets modify
        ' the equivalent rows in the ProvisionalBooking table.
        If Not TypeOf GridTable Is DataSetMediaGap.ProvisionalBookingDataTable Then
            Using ModifiedRows As New DataView(GridTable, "", "", DataViewRowState.ModifiedCurrent)
                For Each ModifiedRow As DataRowView In ModifiedRows
                    Dim ProvisionalBookingRowModify As DataRow = Gap.ProvisionalBookingData.Rows.Find(ModifiedRow("ProvisionalBookingID"))
                    If IsNothing(ProvisionalBookingRowModify) = False Then
                        ProvisionalBookingRowModify("ExpiryTime") = ServerTime
                    End If
                Next
            End Using
            GridTable = Gap.ProvisionalBookingData
        End If

        ' If any rows in the data set were modified, change them in the database too.
        Using ModifiedRows As New DataView(GridTable, "", "", DataViewRowState.ModifiedCurrent)
            If IsNothing(ModifiedRows) = False AndAlso ModifiedRows.Count > 0 Then

                ' Create and configure table adapters to connect to the database.
                Dim Adapter As New DataSetMediaGapTableAdapters.ProvisionalBookingTableAdapter
                Dim AuditAdapter As New DataSetAuditTableAdapters.AuditLogTableAdapter

                ' Create a new connection for the table adapters to use.
                Dim SqlCon As New SqlClient.SqlConnection(ConnectionString)
                Adapter.Connection = SqlCon
                AuditAdapter.Connection = SqlCon

                ' Perform the delete operation.
                Try
                    Adapter.Update(GridTable)
                    AuditAdapter.Update(AuditLog)
                Catch ex As Exception
                    Consumingform.ShowMessage("Aaaagghh!!!  An error occured while trying to delete. The technical description of the error is:" _
                    & vbCrLf & vbCrLf & LiquidShell.LiquidAgent.GetErrorMessage(ex))
                Finally
                    ' Dispose all data adapters.
                    Adapter.Dispose()
                    AuditAdapter.Dispose()
                    ' Close all connections.
                    SqlCon.Dispose()
                End Try

            End If
        End Using

        ' Refresh the grid if it is visible.
        Dim CurrentBindingSource As BindingSource = CType(Gap.ProvisionalBookingGrid.DataSource, BindingSource)
        If Object.ReferenceEquals(CurrentBindingSource, Gap.ProvisionalBookingBySelectionBindingSource) Then
            Gap.RefreshData(ConnectionString)
        End If

        ' Reselect cells that user selected to create these provisional bookings, if any.
        For i As Integer = 0 To SelectedCellColumns.Count - 1
            Gap.Grid.Rows(SelectedCellRows(i)).Cells(SelectedCellColumns(i)).Selected = True
        Next

    End Sub

    Public Shared Function CurrentProvisionalBookingLifeSpan _
    (ByVal ConnectionString As String, ByVal ConsumingForm As LiquidShell.BaseForm) _
    As Integer

        ' Get all settings from the database.
        Dim SettingsTable As DataTable = Settings.GetSettings(ConnectionString, ConsumingForm)
        Dim SettingRow() As DataRow = SettingsTable.Select("SettingName = 'ProvisionalBookingLifeSpan'")
        Return CInt(SettingRow(0).Item("SettingValue"))
        SettingsTable.Dispose()

    End Function

    Public Shared Sub ChangeProvisionalBookings(ByVal ConnectionString As String, _
    ByVal Grid As DataGridView, _
    ByVal FirstWeek As Nullable(Of Date), _
    ByVal LastWeek As Nullable(Of Date), _
    ByVal ProvisionalBookingTable As DataSetMediaGap.ProvisionalBookingDataTable, _
    ByVal Gap As MediaGap)

        ' Remember which cells in the provisional booking grid are selected.
        Dim SelectedCellColumns As New List(Of Integer)
        Dim SelectedCellRows As New List(Of Integer)
        For Each SelectedCell As DataGridViewCell In Gap.Grid.SelectedCells
            SelectedCellColumns.Add(SelectedCell.ColumnIndex)
            SelectedCellRows.Add(SelectedCell.RowIndex)
        Next

        ' Create an audit table to record the changes.
        Dim AuditLog As New DataSetAudit.AuditLogDataTable

        ' Update the selected rows with new dates.
        For Each GridRow As DataGridViewRow In Grid.SelectedRows
            Dim ProvisionalBookingID As Guid = CType(GridRow.DataBoundItem, DataRowView).Item("ProvisionalBookingID")
            Dim ProvisionalBooking As DataRow = ProvisionalBookingTable.Rows.Find(ProvisionalBookingID)
            Dim Rowdescription As String = GetRowDescription(ProvisionalBooking)
            Dim LastWeekBooked As Date = DateAdd _
            (DateInterval.WeekOfYear, ProvisionalBooking("Duration") - 1, ProvisionalBooking("FirstWeek"))

            ' Change the dates and add audit log entries for each change.
            If FirstWeek.HasValue Then
                If ProvisionalBooking("FirstWeek") <> FirstWeek.Value Then
                    ' The selected FirstWeek is different to the provisional booking's current FirstWeek. Make the change.
                    ProvisionalBooking("FirstWeek") = FirstWeek.Value
                    AddLog(Rowdescription, "FirstWeek", FirstWeek.Value.ToString("d MMM yyyy"), AuditLog)
                End If
            End If
            If LastWeek.HasValue Then
                Dim Duration As Integer = CInt(DateDiff(DateInterval.WeekOfYear, ProvisionalBooking("FirstWeek"), LastWeek.Value)) + 1
                If ProvisionalBooking("Duration") <> Duration Then
                    ' The modified Duration is different to the provisional booking's current Duration. Make the change.
                    ProvisionalBooking("Duration") = Duration
                    AddLog(Rowdescription, "Duration", Duration.ToString, AuditLog)
                End If
            End If

        Next

        ' Create table adapters.
        Dim BookingAdapter As New DataSetMediaGapTableAdapters.ProvisionalBookingTableAdapter
        Dim AuditAdapter As New DataSetAuditTableAdapters.AuditLogTableAdapter

        Dim SqlCon As New SqlClient.SqlConnection(ConnectionString)
        BookingAdapter.Connection = SqlCon
        AuditAdapter.Connection = SqlCon

        ' Update the database.
        BookingAdapter.Update(ProvisionalBookingTable)
        AuditAdapter.Update(AuditLog)

        ' Destroy unneeded objects.
        BookingAdapter.Dispose()
        AuditLog.Dispose()
        AuditAdapter.Dispose()

        ' Refresh the grid if it is visible.
        Dim CurrentBindingSource As BindingSource = CType(Gap.ProvisionalBookingGrid.DataSource, BindingSource)
        If Object.ReferenceEquals(CurrentBindingSource, Gap.ProvisionalBookingBySelectionBindingSource) Then
            Gap.RefreshData(Gap.ConnectionString)
        End If

        ' Reselect cells that user selected to change these provisional bookings, if any.
        For i As Integer = 0 To SelectedCellColumns.Count - 1
            Gap.Grid.Rows(SelectedCellRows(i)).Cells(SelectedCellColumns(i)).Selected = True
        Next

    End Sub

    Private Shared Sub AddLog _
    (ByVal RowDescription As String, _
    ByVal ChangedPropertyName As String, _
    ByVal ChangedPropertyNewValue As String, _
    ByVal AuditLog As DataTable)
        ' Add an entry into the audit log table for a modified row.

        ' Create a string variable to hold the log description.
        Dim Action As String = String.Empty

        ' Create a log description.
        If String.IsNullOrEmpty(ChangedPropertyNewValue) Then
            Action = "Changed the value of " & ChangedPropertyName.ToUpper & " to an EMPTY STRING"
        Else
            Action = "Changed the value of " & ChangedPropertyName.ToUpper & " to '" & ChangedPropertyNewValue & "'"
        End If

        ' Log the entry.
        LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, "ProvisionalBooking", RowDescription, Action)

    End Sub

    Public Shared Function BrandIsAlreadyBooked _
    (ByVal BrandID As Guid, _
    ByVal FirstWeek As Date, _
    ByVal LastWeek As Date, _
    ByVal MediaFamilyTable As DataTable, _
    ByVal CategoryTable As DataTable, _
    ByVal ChainTable As DataTable, _
    ByVal ConnectionString As String, _
    ByRef Errors As String) _
    As Boolean

        ' Get a string list of ChainIDs.
        Dim ChainIDList As New StringBuilder
        For Each Chain As DataRow In ChainTable.Rows
            If ChainIDList.Length > 0 Then
                ChainIDList.Append(",")
            End If
            ChainIDList.Append(Chain("ChainID").ToString)
        Next

        ' Get a string list of CategoryIDs.
        Dim CategoryIDList As New StringBuilder
        For Each Category As DataRow In CategoryTable.Rows
            If CategoryIDList.Length > 0 Then
                CategoryIDList.Append(",")
            End If
            CategoryIDList.Append(Category("CategoryID").ToString)
        Next

        ' Get a string list of MediaFamilyIDs.
        Dim MediaFamilyIDList As New StringBuilder
        For Each MediaFamily As DataRow In MediaFamilyTable.Rows
            If MediaFamilyIDList.Length > 0 Then
                MediaFamilyIDList.Append(",")
            End If
            MediaFamilyIDList.Append(MediaFamily("MediaFamilyID").ToString)
        Next

        Dim Command As String = "SELECT RowsExist FROM dbo.udfBrandIsAlreadyBooked('" _
        & BrandID.ToString & "', '" _
        & LiquidShell.LiquidAgent.GetSqlFriendlyDate(FirstWeek) & "', '" _
        & LiquidShell.LiquidAgent.GetSqlFriendlyDate(LastWeek) & "', '" _
        & ChainIDList.ToString & "', '" _
        & CategoryIDList.ToString & "', '" _
        & MediaFamilyIDList.ToString & "')"

        ' Execute the command to fetch the data.
        Dim Result As Boolean = CBool(LiquidShell.LiquidAgent.GetSqlScalarValue(ConnectionString, Command, Errors))
        Return Result

    End Function

#End Region

#Region "Public Methods"

    Public Sub RefreshData(ByVal ConString As String)

        ' Check if user selected search criteria.
        If ChainsToView.Rows.Count = 0 _
        Or MediaFamiliesToView.Rows.Count = 0 _
        Or CategoriesToView.Rows.Count = 0 Then
            Dim MessageText As String = "No media gap data can be displayed if any of the view options have not been " _
            & "selected." & vbCrLf & vbCrLf & "Please select at least one chain, at least one media family, and at " _
            & "least one category."
            'If ConsumingForm Is Nothing Then
            '    ConsumingForm = 
            'End If
            ConsumingForm.ShowMessage(MessageText, "No Filters Selected")
            Exit Sub
        End If

        ConnectionString = ConString

        RefreshData()
        RefreshInterface()

    End Sub

    Public Sub RefreshInterface()

        ' Rebuild the grid and data table.
        UpdateGridColumns()
        RebuildGridDataTable()
        PopulateGridDataTable()
        UpdateCellInfo()

        ' Clear all provisional booking filters and searches.
        ProvisionalBookingBindingSource.Filter = String.Empty
        CType(ProvisionalBookingGrid.Tag, LiquidShell.GridManager).ClearSearch()

    End Sub

    Public Sub New _
    (ByVal MediaGapGrid As DataGridView, ByVal GridOfProvisionalBookings As DataGridView, ByVal MyBaseForm As LiquidShell.BaseForm)

        ' Set the default FirstWeekToView equal to the next Monday.
        While Not FirstWeekToView.DayOfWeek = DayOfWeek.Monday
            FirstWeekToView = FirstWeekToView.AddDays(1)
        End While

        ' Setup the grid data set and binding source.
        Dim GridDataSet As New DataSet
        GridDataSet.Tables.Add(GridDataTable)
        GridBindingSource = New BindingSource(GridDataSet, GridDataTable.TableName)
        Grid = MediaGapGrid
        Grid.DataSource = GridBindingSource
        Grid.AutoGenerateColumns = False
        AddGridColumns()

        ' Setup the contract list data set and binding source.
        ContractListBindingSource = New BindingSource(MediaGapDataSet, "ContractList")
        ContractListBindingSource.Sort = "ContractNumber"

        ' Sort the unfiltered provisional bookings.
        ProvisionalBookingBindingSource.Sort = "BookTime"

        ' Get the grid continaing the list of provisional bookings.
        ProvisionalBookingGrid = GridOfProvisionalBookings
        ProvisionalBookingGrid.AutoGenerateColumns = False
        ProvisionalBookingGrid.DataSource = ProvisionalBookingBySelectionBindingSource

        ' Get the base form that is creating this object.
        ConsumingForm = MyBaseForm

    End Sub

    Public Sub ToggleProvisionalBookingList()
        ' Toggle the data source of the provisional booking grid between the complete binding source
        ' and the filtered binding source.

        ' Clear any search criteria currently in place.
        CType(ProvisionalBookingGrid.Tag, LiquidShell.GridManager).ClearSearch()

        ' Get the grid manager for the provisional booking grid.
        Dim Manager As LiquidShell.GridManager = CType(ProvisionalBookingGrid.Tag, LiquidShell.GridManager)

        ' Get the current binding source of the provisional booking grid.
        Dim CurrentBindingSource As BindingSource = CType(ProvisionalBookingGrid.DataSource, BindingSource)

        ' Set the grid's binding source depending on whether it is visible or not.
        If Object.ReferenceEquals(CurrentBindingSource, ProvisionalBookingBySelectionBindingSource) Then
            ProvisionalBookingGrid.DataSource = ProvisionalBookingBindingSource
            ProvisionalBookingBindingSource.Filter = String.Empty
            Manager.GridBindingSource = ProvisionalBookingBindingSource
        Else
            ProvisionalBookingGrid.DataSource = ProvisionalBookingBySelectionBindingSource
            Manager.GridBindingSource = ProvisionalBookingBySelectionBindingSource
        End If

    End Sub

#End Region

#Region "Private Methods"

    Private Sub PopulateContractListTable()

        ' Empty the contract list table.
        MediaGapDataSet.Tables("ContractList").Rows.Clear()

        ' Repopulate the contract list table with current selection data.
        For Each SelectedCell As DataGridViewCell In Grid.SelectedCells
            If Not SelectedCell.ColumnIndex = 0 Then
                ' The current cell is not in the first column, which contains the chain names. It's safe to proceed.
                Dim Key As String = SelectedCell.ColumnIndex.ToString("000") & SelectedCell.RowIndex.ToString("000")
                For Each Contract As KeyValuePair(Of Guid, String) In CellInfoCollection(Key).ContractList
                    ' Check if the current contract is already listed in the table.
                    Dim ExistingContract As DataRow = MediaGapDataSet.Tables("ContractList").Rows.Find(Contract.Key)
                    If IsNothing(ExistingContract) Then
                        ' The current contract doesn't exist in the table. It's safe to proceed.
                        Dim NewContractRow As DataRow = MediaGapDataSet.Tables("ContractList").NewRow
                        With NewContractRow
                            .Item("ContractID") = Contract.Key
                            .Item("ContractNumber") = Contract.Value
                        End With
                        MediaGapDataSet.Tables("ContractList").Rows.Add(NewContractRow)
                    End If
                Next
            End If
        Next

    End Sub

    Private Sub RefreshData()

        ' Retrieve data from the database.
        Dim ErrorMessage As String = String.Empty
        FetchCurrentData(ConnectionString, ErrorMessage)

        ' Stop if errors occured.
        If String.IsNullOrEmpty(ErrorMessage) = False Then
            If ErrorMessage.Length > 0 Then
                ConsumingForm.ShowMessage _
                ("Media gap data load error:" & vbCrLf & vbCrLf & ErrorMessage, "Something Went Wrong")
                Exit Sub
            End If
        End If

    End Sub

    Private Sub FetchCurrentData(ByVal ConnectionString As String, ByRef ErrorMessage As String)

        ' Get date parameter values required to fetch data.
        Dim ToDate As Date = DateAdd(DateInterval.WeekOfYear, WeeksToView - 1, FirstWeekToView)

        ' Get chain parameter values required to fetch data.
        Dim SelectedChainIDs As New StringBuilder
        For Each Row As DataRow In _ChainsToView.Rows
            If SelectedChainIDs.Length > 0 Then
                SelectedChainIDs.Append(",")
            End If
            SelectedChainIDs.Append(Row("ChainID"))
        Next

        ' Get category parameter values required to fetch data.
        Dim SelectedCategoryIDs As New StringBuilder
        For Each Row As DataRow In _CategoriesToView.Rows
            If SelectedCategoryIDs.Length > 0 Then
                SelectedCategoryIDs.Append(",")
            End If
            SelectedCategoryIDs.Append(Row("CategoryID"))
        Next

        ' Get media family parameter values required to fetch data.
        Dim SelectedMediaFamilyIDs As New StringBuilder
        For Each Row As DataRow In _MediaFamiliesToView.Rows
            If SelectedMediaFamilyIDs.Length > 0 Then
                SelectedMediaFamilyIDs.Append(",")
            End If
            SelectedMediaFamilyIDs.Append(Row("MediaFamilyID"))
        Next

        ' Get the current server time.
        Dim ServerTime As Date = Settings.GetServerTime(ConnectionString, ConsumingForm)

        ' Try and collect the data.
        Dim BurstAdapter As New DataSetMediaGapTableAdapters.BurstDataTableAdapter
        Dim ProvisionalBookingAdapter As New DataSetMediaGapTableAdapters.ProvisionalBookingTableAdapter
        Dim SqlCon As New SqlClient.SqlConnection(ConnectionString)
        BurstAdapter.Connection = SqlCon
        ProvisionalBookingAdapter.Connection = SqlCon
        Try
            BurstAdapter.Fill _
            (BurstData, FirstWeekToView, ToDate, SelectedChainIDs.ToString, SelectedCategoryIDs.ToString, SelectedMediaFamilyIDs.ToString)
            ProvisionalBookingAdapter.Fill _
            (ProvisionalBookingData, FirstWeekToView, ToDate, SelectedChainIDs.ToString, SelectedCategoryIDs.ToString, SelectedMediaFamilyIDs.ToString, ServerTime)
        Catch ex As Exception
            ErrorMessage = LiquidShell.LiquidAgent.GetErrorMessage(ex)
        Finally
            BurstAdapter.Dispose()
            ProvisionalBookingAdapter.Dispose()
        End Try

    End Sub

    Private Sub AddGridColumns()

        ' Create an array to hold the columns.
        Dim GridColumns(WeeksToView) As DataGridViewColumn

        ' Add the Chain column.
        GridColumns(0) = New DataGridViewTextBoxColumn
        With GridColumns(0)
            .HeaderText = "Year:" & vbCrLf & "Month:" & vbCrLf & "Day:"
            .Name = "ChainColumn"
            .DataPropertyName = "ChainName"
            .AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells
            .DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleLeft
            .HeaderCell.Style.Alignment = DataGridViewContentAlignment.MiddleLeft
            .Frozen = True
            .SortMode = DataGridViewColumnSortMode.NotSortable
        End With

        ' Add the week columns.
        For i As Integer = 1 To WeeksToView
            GridColumns(i) = New DataGridViewTextBoxColumn
            With GridColumns(i)
                .Width = 46
                .DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter
                .SortMode = DataGridViewColumnSortMode.NotSortable
            End With
        Next

        ' Add all columns to the grid.
        Grid.Columns.AddRange(GridColumns)

    End Sub

    Private Sub PopulateGridDataTable()

        ' Turn off sorting.
        GridBindingSource.Sort = String.Empty

        ' Add burst data.
        For Each Chain As DataRow In ChainsToView.Rows
            Dim ChainName As String = Chain("ChainName")
            Dim FilterFriendlyChainName As String = ChainName.Replace("'", "''")
            Dim NewGridDataRow As DataRow = GridDataTable.NewRow
            NewGridDataRow("ChainName") = ChainName

            For i As Integer = 0 To WeeksToView - 1

                ' Get the current week.
                Dim Week As Date = DateAdd(DateInterval.WeekOfYear, CDbl(i), FirstWeekToView)

                ' Build a filter to extract data for this week in this chain.
                Dim Filter As String = "ChainName = '" & FilterFriendlyChainName _
                & "' AND FirstWeek <= '" & Week.ToString _
                & "' AND LastWeek >= '" & Week.ToString _
                & "'"

                ' Create dictionary collections to record the size of all the affected store pools.
                Dim HomesiteStorePoolQty As New Dictionary(Of Guid, Integer)
                Dim CrossoverStorePoolQty As New Dictionary(Of Guid, Integer)

                ' Extract the data from the table using the filter.
                Dim Bursts As DataRow() = BurstData.Select(Filter)

                ' Count the store quantities of each store pool.
                For Each Burst As DataRow In Bursts
                    If Burst("Homesite") Then
                        ' This is a homesite burst.
                        If HomesiteStorePoolQty.ContainsKey(Burst("StorePoolID")) = False Then
                            ' No quantities for this store pool have been recorded. Add it to the dictionary.
                            HomesiteStorePoolQty.Add(Burst("StorePoolID"), Burst("StorePoolCapacity"))
                        End If
                    Else
                        ' This is a crossover burst.
                        If CrossoverStorePoolQty.ContainsKey(Burst("StorePoolID")) = False Then
                            ' No quantities for this store pool have been recorded. Add it to the dictionary.
                            CrossoverStorePoolQty.Add(Burst("StorePoolID"), Burst("StorePoolCapacity"))
                        End If
                    End If
                Next

                ' Create variables to count store quantities.
                Dim HomesiteStoreQty As Integer = 0
                Dim CrossoverStoreQty As Integer = 0

                ' Count store quantities from each dictionary.
                For Each StorePoolQty As KeyValuePair(Of Guid, Integer) In HomesiteStorePoolQty
                    HomesiteStoreQty += StorePoolQty.Value
                Next
                For Each StorePoolQty As KeyValuePair(Of Guid, Integer) In CrossoverStorePoolQty
                    CrossoverStoreQty += StorePoolQty.Value
                Next

                ' Get the string representation of the store quantity totals.
                Dim HomesiteStoreQtyString As String = String.Empty
                Dim CrossoverStoreQtyString As String = String.Empty
                If HomesiteStoreQty > 0 Then
                    HomesiteStoreQtyString = HomesiteStoreQty.ToString
                End If
                If CrossoverStoreQty > 0 Then
                    CrossoverStoreQtyString = CrossoverStoreQty.ToString
                End If

                ' Fill this cell with the calculated quantity data.
                NewGridDataRow(i + 1) = HomesiteStoreQtyString & vbCrLf & CrossoverStoreQtyString

            Next

            ' Add the new row to the grid table.
            GridDataTable.Rows.Add(NewGridDataRow)

        Next

        ' Turn on sorting.
        GridBindingSource.Sort = "ChainName"

    End Sub

    Private Sub UpdateGridColumns()

        ' Update the header text's and data property name's of all week columns.
        For i As Integer = 1 To WeeksToView
            Dim GridColumnWeek As Date = DateAdd(DateInterval.WeekOfYear, i - 1, FirstWeekToView)
            Dim YearString As String = GridColumnWeek.Year.ToString("####")
            Dim MonthString As String = GridColumnWeek.Month.ToString("0#")
            Dim DayString As String = GridColumnWeek.Day.ToString("0#")
            Grid.Columns(i).HeaderText = YearString & vbCrLf & MonthString & vbCrLf & DayString
            Grid.Columns(i).DataPropertyName = YearString & MonthString & DayString
        Next

    End Sub

    Private Sub RebuildGridDataTable()

        ' Clear the table of all data.
        GridDataTable.Rows.Clear()
        GridDataTable.Columns.Clear()

        ' Create an array to hold all the columns.
        Dim Columns(WeeksToView) As DataColumn

        ' Create the chain columns.
        Columns(0) = New DataColumn("ChainName", GetType(String))

        ' Create the week columns.
        For i As Integer = 1 To WeeksToView
            Dim ColumnWeek As Date = DateAdd(DateInterval.WeekOfYear, i - 1, FirstWeekToView)
            Dim YearString As String = ColumnWeek.Year.ToString("####")
            Dim MonthString As String = ColumnWeek.Month.ToString("0#")
            Dim DayString As String = ColumnWeek.Day.ToString("0#")
            Dim ColumnName As String = YearString & MonthString & DayString
            Columns(i) = New DataColumn(ColumnName, GetType(String))
        Next

        ' Add the columns to the table.
        GridDataTable.Columns.AddRange(Columns)

    End Sub

    Private Sub UpdateCellInfo()

        ' Clear the current list of cell info objects.
        CellInfoCollection.Clear()

        ' Setup variables that we'll need to add contracts to the list.
        Dim Filter As String = String.Empty
        Dim Bursts() As DataRow = Nothing
        Dim ProvisionalBookings() As DataRow = Nothing
        Dim ColumnDateString As String = String.Empty
        Dim Year As Integer = Nothing
        Dim Month As Integer = Nothing
        Dim Day As Integer = Nothing
        Dim Cell As DataGridViewCell
        Dim CellInfoObject As CellInfo
        Dim ProvisionalBookingTableColumnName As String = String.Empty

        ' Check each selected cell.
        For RowIndex As Integer = 0 To Grid.Rows.Count - 1
            For ColumnIndex As Integer = 1 To Grid.Columns.Count - 1

                ' Get the current cell and create a CellInfo object for it.
                Cell = Grid(ColumnIndex, RowIndex)
                CellInfoObject = New CellInfo
                Dim Key As String = ColumnIndex.ToString("000") & RowIndex.ToString("000")
                CellInfoCollection.Add(Key, CellInfoObject)

                ' Set the 'Week' value of the cell info object.
                ColumnDateString = Grid.Columns(Cell.ColumnIndex).DataPropertyName
                Year = CInt(ColumnDateString.Substring(0, 4))
                Month = CInt(ColumnDateString.Substring(4, 2))
                Day = CInt(ColumnDateString.Substring(6, 2))
                CellInfoObject.Week = New Date(Year, Month, Day)

                ' Get the chain name.
                CellInfoObject.ChainName = CType(Grid.Rows(Cell.RowIndex).DataBoundItem, DataRowView).Item("ChainName")

                ' Use the chain name and week to build a filter.
                Filter = "ChainName = '" & CellInfoObject.FilterFriendlyChainName _
                & "' AND FirstWeek <= '" & CellInfoObject.Week.ToString _
                & "' AND LastWeek >= '" & CellInfoObject.Week.ToString _
                & "'"


                ' UPDATE CELL INFO CONTRACT DATA
                ' ==============================

                ' Extract the burst data rows using the filter.
                Bursts = BurstData.Select(Filter)

                ' If any contracts that appear in the burst selection are not in the contract list, add them.
                For Each Burst As DataRow In Bursts
                    If CellInfoObject.ContractList.ContainsKey(Burst("ContractID")) = False Then
                        ' The contract of this burst isn't in this cell's list. Add it.
                        CellInfoObject.ContractList.Add(Burst("ContractID"), Burst("ContractNumber"))
                    End If
                Next


                ' UPDATE CELL INFO PROVISIONAL BOOKING DATA
                ' =========================================

                ' Extract the Provisional Booking data rows using the filter.
                ProvisionalBookings = ProvisionalBookingData.Select(Filter)

                ' If any provisional bookings that appear in the selection are not in the provisional booking list, add them.
                For Each ProvisionalBooking As DataRow In ProvisionalBookings
                    If CellInfoObject.ProvisionalBookingList.ContainsKey(ProvisionalBooking("ProvisionalBookingID")) = False Then
                        ' This provisional booking isn't in this cell's list. Add it.
                        ' Create a dictionary to contain the values of all columns in the provisional booking row.
                        Dim Values As New Dictionary(Of String, Object)
                        For i As Integer = 1 To ProvisionalBookingData.Columns.Count - 1
                            ProvisionalBookingTableColumnName = ProvisionalBookingData.Columns(i).ColumnName
                            Values.Add(ProvisionalBookingTableColumnName, ProvisionalBooking(ProvisionalBookingTableColumnName))
                        Next
                        CellInfoObject.ProvisionalBookingList.Add(ProvisionalBooking("ProvisionalBookingID"), Values)
                    End If
                Next

            Next
        Next

    End Sub

#End Region

End Class


Public Class CellInfo

    Public ContractList As New Dictionary(Of Guid, String)
    Public ProvisionalBookingList As New Dictionary(Of Guid, Dictionary(Of String, Object))
    Public Week As Date
    Private _FilterFriendlyChainName As String
    Private _ChainName As String

    Public Property ChainName() As String
        Get
            Return _ChainName
        End Get
        Set(ByVal value As String)
            _ChainName = value
            _FilterFriendlyChainName = value.Replace("'", "''")
        End Set
    End Property

    Public ReadOnly Property FilterFriendlyChainName() As String
        Get
            Return _FilterFriendlyChainName
        End Get
    End Property

    Public ReadOnly Property BackColor() As Color
        Get
            If ProvisionalBookingList.Count = 0 Then
                Return Color.Empty
            Else
                Return Color.Khaki
            End If
        End Get
    End Property

    Public ReadOnly Property SelectionBackColor() As Color
        Get
            If ProvisionalBookingList.Count = 0 Then
                Return Color.Empty
            Else
                Return Color.Goldenrod
            End If
        End Get
    End Property

End Class