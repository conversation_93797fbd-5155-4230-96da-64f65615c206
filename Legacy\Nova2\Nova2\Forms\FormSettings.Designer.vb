<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class FormSettings
    Inherits LiquidShell.EditorForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(FormSettings))
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim SuperToolTip1 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem1 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem1 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip2 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem2 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem2 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Me.ImageList16x16 = New System.Windows.Forms.ImageList(Me.components)
        Me.TabSet = New DevExpress.XtraTab.XtraTabControl()
        Me.TabPageCommission = New DevExpress.XtraTab.XtraTabPage()
        Me.PanelDetails = New System.Windows.Forms.Panel()
        Me.PanelAccountManagerDetails = New System.Windows.Forms.Panel()
        Me.LabelQuarter3Ratio = New DevExpress.XtraEditors.LabelControl()
        Me.LabelQuarter1Ratio = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditQuarter3Ratio = New DevExpress.XtraEditors.TextEdit()
        Me.TextEditQuarter1Ratio = New DevExpress.XtraEditors.TextEdit()
        Me.LabelQuarterRatiosTotal = New DevExpress.XtraEditors.LabelControl()
        Me.LabelQuarter4Ratio = New DevExpress.XtraEditors.LabelControl()
        Me.LabelQuarter2Ratio = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditQuarterRatiosTotal = New DevExpress.XtraEditors.TextEdit()
        Me.TextEditQuarter4Ratio = New DevExpress.XtraEditors.TextEdit()
        Me.TextEditQuarter2Ratio = New DevExpress.XtraEditors.TextEdit()
        Me.LabelWriteContract = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditWriteContract = New DevExpress.XtraEditors.TextEdit()
        Me.LabelQuarterCommissionRatios = New DevExpress.XtraEditors.LabelControl()
        Me.LabelBAccountDefinition = New DevExpress.XtraEditors.LabelControl()
        Me.LabelEarningRates = New DevExpress.XtraEditors.LabelControl()
        Me.LabelCareTakeContract = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditCareTakeContract = New DevExpress.XtraEditors.TextEdit()
        Me.LabelBAccountInactivityPeriod = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditBAccountInactivityPeriod = New DevExpress.XtraEditors.TextEdit()
        Me.TabPageContractReport = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.Panel3 = New System.Windows.Forms.Panel()
        Me.ButtonContractPreview = New DevExpress.XtraEditors.SimpleButton()
        Me.GroupTelephoneNumbers = New DevExpress.XtraEditors.GroupControl()
        Me.MemoTelephoneNumbers = New DevExpress.XtraEditors.MemoEdit()
        Me.GroupCompanyAddress = New DevExpress.XtraEditors.GroupControl()
        Me.MemoCompanyAddress = New DevExpress.XtraEditors.MemoEdit()
        Me.LabelVAT = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditVAT = New DevExpress.XtraEditors.TextEdit()
        Me.LableHardCopyOptionsTitle = New DevExpress.XtraEditors.LabelControl()
        Me.TabPageBudgets = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel4 = New System.Windows.Forms.Panel()
        Me.Panel5 = New System.Windows.Forms.Panel()
        Me.TabPageNoSettings = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.PictureBox1 = New System.Windows.Forms.PictureBox()
        Me.ButtonSave = New DevExpress.XtraEditors.SimpleButton()
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.GroupWholesalePurchases = New DevExpress.XtraEditors.GroupControl()
        Me.ButtonDeleteBudget = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAddBudget = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonEditBudget = New DevExpress.XtraEditors.SimpleButton()
        Me.GridBudget = New System.Windows.Forms.DataGridView()
        Me.PurchaseVanColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.LabelSearchBudget = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearchBudget = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearchBudget = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearchBudget = New DevExpress.XtraEditors.PictureEdit()
        CType(Me.PanelButtonBar,System.ComponentModel.ISupportInitialize).BeginInit
        Me.PanelButtonBar.SuspendLayout
        CType(Me.ErrorManager,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.TabSet,System.ComponentModel.ISupportInitialize).BeginInit
        Me.TabSet.SuspendLayout
        Me.TabPageCommission.SuspendLayout
        Me.PanelDetails.SuspendLayout
        Me.PanelAccountManagerDetails.SuspendLayout
        CType(Me.TextEditQuarter3Ratio.Properties,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.TextEditQuarter1Ratio.Properties,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.TextEditQuarterRatiosTotal.Properties,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.TextEditQuarter4Ratio.Properties,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.TextEditQuarter2Ratio.Properties,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.TextEditWriteContract.Properties,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.TextEditCareTakeContract.Properties,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.TextEditBAccountInactivityPeriod.Properties,System.ComponentModel.ISupportInitialize).BeginInit
        Me.TabPageContractReport.SuspendLayout
        Me.Panel2.SuspendLayout
        Me.Panel3.SuspendLayout
        CType(Me.GroupTelephoneNumbers,System.ComponentModel.ISupportInitialize).BeginInit
        Me.GroupTelephoneNumbers.SuspendLayout
        CType(Me.MemoTelephoneNumbers.Properties,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.GroupCompanyAddress,System.ComponentModel.ISupportInitialize).BeginInit
        Me.GroupCompanyAddress.SuspendLayout
        CType(Me.MemoCompanyAddress.Properties,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.TextEditVAT.Properties,System.ComponentModel.ISupportInitialize).BeginInit
        Me.TabPageBudgets.SuspendLayout
        Me.Panel4.SuspendLayout
        Me.Panel5.SuspendLayout
        Me.TabPageNoSettings.SuspendLayout
        Me.Panel1.SuspendLayout
        Me.TableLayoutPanel1.SuspendLayout
        CType(Me.PictureBox1,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.GroupWholesalePurchases,System.ComponentModel.ISupportInitialize).BeginInit
        Me.GroupWholesalePurchases.SuspendLayout
        CType(Me.GridBudget,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.TextEditSearchBudget.Properties,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.PictureClearSearchBudget.Properties,System.ComponentModel.ISupportInitialize).BeginInit
        CType(Me.PictureAdvancedSearchBudget.Properties,System.ComponentModel.ISupportInitialize).BeginInit
        Me.SuspendLayout
        '
        'PanelButtonBar
        '
        Me.PanelButtonBar.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelButtonBar.Appearance.BackColor2 = System.Drawing.Color.LightSteelBlue
        Me.PanelButtonBar.Appearance.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical
        Me.PanelButtonBar.Appearance.Options.UseBackColor = true
        Me.PanelButtonBar.Controls.Add(Me.ButtonSave)
        Me.PanelButtonBar.Controls.Add(Me.ButtonCancel)
        Me.PanelButtonBar.Location = New System.Drawing.Point(0, 453)
        Me.PanelButtonBar.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Office2003
        Me.PanelButtonBar.LookAndFeel.UseDefaultLookAndFeel = false
        Me.PanelButtonBar.Size = New System.Drawing.Size(545, 52)
        Me.PanelButtonBar.TabIndex = 1
        '
        'ImageList16x16
        '
        Me.ImageList16x16.ImageStream = CType(resources.GetObject("ImageList16x16.ImageStream"),System.Windows.Forms.ImageListStreamer)
        Me.ImageList16x16.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList16x16.Images.SetKeyName(0, "add.png")
        Me.ImageList16x16.Images.SetKeyName(1, "pencil.png")
        Me.ImageList16x16.Images.SetKeyName(2, "delete.png")
        Me.ImageList16x16.Images.SetKeyName(3, "print.png")
        '
        'TabSet
        '
        Me.TabSet.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.TabSet.AppearancePage.Header.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.TabSet.AppearancePage.Header.Options.UseFont = true
        Me.TabSet.Location = New System.Drawing.Point(12, 12)
        Me.TabSet.LookAndFeel.SkinName = "Black"
        Me.TabSet.LookAndFeel.UseDefaultLookAndFeel = false
        Me.TabSet.Margin = New System.Windows.Forms.Padding(3, 3, 3, 0)
        Me.TabSet.Name = "TabSet"
        Me.TabSet.SelectedTabPage = Me.TabPageCommission
        Me.TabSet.Size = New System.Drawing.Size(521, 441)
        Me.TabSet.TabIndex = 0
        Me.TabSet.TabPages.AddRange(New DevExpress.XtraTab.XtraTabPage() {Me.TabPageContractReport, Me.TabPageBudgets, Me.TabPageCommission, Me.TabPageNoSettings})
        '
        'TabPageCommission
        '
        Me.TabPageCommission.Controls.Add(Me.PanelDetails)
        Me.TabPageCommission.Name = "TabPageCommission"
        Me.TabPageCommission.PageVisible = false
        Me.TabPageCommission.Size = New System.Drawing.Size(515, 413)
        Me.TabPageCommission.Text = "Commission"
        '
        'PanelDetails
        '
        Me.PanelDetails.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.PanelDetails.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelDetails.Controls.Add(Me.PanelAccountManagerDetails)
        Me.PanelDetails.Location = New System.Drawing.Point(3, 3)
        Me.PanelDetails.Name = "PanelDetails"
        Me.PanelDetails.Size = New System.Drawing.Size(510, 409)
        Me.PanelDetails.TabIndex = 0
        '
        'PanelAccountManagerDetails
        '
        Me.PanelAccountManagerDetails.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.PanelAccountManagerDetails.Controls.Add(Me.LabelQuarter3Ratio)
        Me.PanelAccountManagerDetails.Controls.Add(Me.LabelQuarter1Ratio)
        Me.PanelAccountManagerDetails.Controls.Add(Me.TextEditQuarter3Ratio)
        Me.PanelAccountManagerDetails.Controls.Add(Me.TextEditQuarter1Ratio)
        Me.PanelAccountManagerDetails.Controls.Add(Me.LabelQuarterRatiosTotal)
        Me.PanelAccountManagerDetails.Controls.Add(Me.LabelQuarter4Ratio)
        Me.PanelAccountManagerDetails.Controls.Add(Me.LabelQuarter2Ratio)
        Me.PanelAccountManagerDetails.Controls.Add(Me.TextEditQuarterRatiosTotal)
        Me.PanelAccountManagerDetails.Controls.Add(Me.TextEditQuarter4Ratio)
        Me.PanelAccountManagerDetails.Controls.Add(Me.TextEditQuarter2Ratio)
        Me.PanelAccountManagerDetails.Controls.Add(Me.LabelWriteContract)
        Me.PanelAccountManagerDetails.Controls.Add(Me.TextEditWriteContract)
        Me.PanelAccountManagerDetails.Controls.Add(Me.LabelQuarterCommissionRatios)
        Me.PanelAccountManagerDetails.Controls.Add(Me.LabelBAccountDefinition)
        Me.PanelAccountManagerDetails.Controls.Add(Me.LabelEarningRates)
        Me.PanelAccountManagerDetails.Controls.Add(Me.LabelCareTakeContract)
        Me.PanelAccountManagerDetails.Controls.Add(Me.TextEditCareTakeContract)
        Me.PanelAccountManagerDetails.Controls.Add(Me.LabelBAccountInactivityPeriod)
        Me.PanelAccountManagerDetails.Controls.Add(Me.TextEditBAccountInactivityPeriod)
        Me.PanelAccountManagerDetails.Location = New System.Drawing.Point(12, 12)
        Me.PanelAccountManagerDetails.Margin = New System.Windows.Forms.Padding(12)
        Me.PanelAccountManagerDetails.Name = "PanelAccountManagerDetails"
        Me.PanelAccountManagerDetails.Size = New System.Drawing.Size(486, 385)
        Me.PanelAccountManagerDetails.TabIndex = 1
        '
        'LabelQuarter3Ratio
        '
        Me.LabelQuarter3Ratio.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.LabelQuarter3Ratio.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelQuarter3Ratio.Location = New System.Drawing.Point(3, 259)
        Me.LabelQuarter3Ratio.Name = "LabelQuarter3Ratio"
        Me.LabelQuarter3Ratio.Size = New System.Drawing.Size(86, 13)
        Me.LabelQuarter3Ratio.TabIndex = 13
        Me.LabelQuarter3Ratio.Text = "Quarter 3 (%):"
        '
        'LabelQuarter1Ratio
        '
        Me.LabelQuarter1Ratio.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.LabelQuarter1Ratio.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelQuarter1Ratio.Location = New System.Drawing.Point(3, 207)
        Me.LabelQuarter1Ratio.Name = "LabelQuarter1Ratio"
        Me.LabelQuarter1Ratio.Size = New System.Drawing.Size(86, 13)
        Me.LabelQuarter1Ratio.TabIndex = 9
        Me.LabelQuarter1Ratio.Text = "Quarter 1 (%):"
        '
        'TextEditQuarter3Ratio
        '
        Me.TextEditQuarter3Ratio.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.TextEditQuarter3Ratio.Location = New System.Drawing.Point(376, 256)
        Me.TextEditQuarter3Ratio.Name = "TextEditQuarter3Ratio"
        Me.TextEditQuarter3Ratio.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.TextEditQuarter3Ratio.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditQuarter3Ratio.Properties.Appearance.Options.UseFont = true
        Me.TextEditQuarter3Ratio.Properties.Appearance.Options.UseForeColor = true
        Me.TextEditQuarter3Ratio.Properties.Appearance.Options.UseTextOptions = true
        Me.TextEditQuarter3Ratio.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.TextEditQuarter3Ratio.Properties.DisplayFormat.FormatString = "{0:n}%"
        Me.TextEditQuarter3Ratio.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.TextEditQuarter3Ratio.Properties.Mask.EditMask = "P2"
        Me.TextEditQuarter3Ratio.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditQuarter3Ratio.Properties.MaxLength = 3
        Me.TextEditQuarter3Ratio.Size = New System.Drawing.Size(107, 20)
        Me.TextEditQuarter3Ratio.TabIndex = 14
        Me.TextEditQuarter3Ratio.Tag = "Quarter3CommissionRatio"
        '
        'TextEditQuarter1Ratio
        '
        Me.TextEditQuarter1Ratio.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.TextEditQuarter1Ratio.Location = New System.Drawing.Point(376, 204)
        Me.TextEditQuarter1Ratio.Name = "TextEditQuarter1Ratio"
        Me.TextEditQuarter1Ratio.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.TextEditQuarter1Ratio.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditQuarter1Ratio.Properties.Appearance.Options.UseFont = true
        Me.TextEditQuarter1Ratio.Properties.Appearance.Options.UseForeColor = true
        Me.TextEditQuarter1Ratio.Properties.Appearance.Options.UseTextOptions = true
        Me.TextEditQuarter1Ratio.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.TextEditQuarter1Ratio.Properties.DisplayFormat.FormatString = "{0:n}%"
        Me.TextEditQuarter1Ratio.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.TextEditQuarter1Ratio.Properties.Mask.EditMask = "P2"
        Me.TextEditQuarter1Ratio.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditQuarter1Ratio.Properties.MaxLength = 3
        Me.TextEditQuarter1Ratio.Size = New System.Drawing.Size(107, 20)
        Me.TextEditQuarter1Ratio.TabIndex = 10
        Me.TextEditQuarter1Ratio.Tag = "Quarter1CommissionRatio"
        '
        'LabelQuarterRatiosTotal
        '
        Me.LabelQuarterRatiosTotal.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.LabelQuarterRatiosTotal.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelQuarterRatiosTotal.Location = New System.Drawing.Point(3, 311)
        Me.LabelQuarterRatiosTotal.Name = "LabelQuarterRatiosTotal"
        Me.LabelQuarterRatiosTotal.Size = New System.Drawing.Size(33, 13)
        Me.LabelQuarterRatiosTotal.TabIndex = 17
        Me.LabelQuarterRatiosTotal.Text = "Total:"
        '
        'LabelQuarter4Ratio
        '
        Me.LabelQuarter4Ratio.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.LabelQuarter4Ratio.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelQuarter4Ratio.Location = New System.Drawing.Point(3, 285)
        Me.LabelQuarter4Ratio.Name = "LabelQuarter4Ratio"
        Me.LabelQuarter4Ratio.Size = New System.Drawing.Size(86, 13)
        Me.LabelQuarter4Ratio.TabIndex = 15
        Me.LabelQuarter4Ratio.Text = "Quarter 4 (%):"
        '
        'LabelQuarter2Ratio
        '
        Me.LabelQuarter2Ratio.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.LabelQuarter2Ratio.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelQuarter2Ratio.Location = New System.Drawing.Point(3, 233)
        Me.LabelQuarter2Ratio.Name = "LabelQuarter2Ratio"
        Me.LabelQuarter2Ratio.Size = New System.Drawing.Size(86, 13)
        Me.LabelQuarter2Ratio.TabIndex = 11
        Me.LabelQuarter2Ratio.Text = "Quarter 2 (%):"
        '
        'TextEditQuarterRatiosTotal
        '
        Me.TextEditQuarterRatiosTotal.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.TextEditQuarterRatiosTotal.Location = New System.Drawing.Point(376, 308)
        Me.TextEditQuarterRatiosTotal.Name = "TextEditQuarterRatiosTotal"
        Me.TextEditQuarterRatiosTotal.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.TextEditQuarterRatiosTotal.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditQuarterRatiosTotal.Properties.Appearance.Options.UseFont = true
        Me.TextEditQuarterRatiosTotal.Properties.Appearance.Options.UseForeColor = true
        Me.TextEditQuarterRatiosTotal.Properties.Appearance.Options.UseTextOptions = true
        Me.TextEditQuarterRatiosTotal.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.TextEditQuarterRatiosTotal.Properties.AppearanceReadOnly.BackColor = System.Drawing.Color.Gainsboro
        Me.TextEditQuarterRatiosTotal.Properties.AppearanceReadOnly.Options.UseBackColor = true
        Me.TextEditQuarterRatiosTotal.Properties.DisplayFormat.FormatString = "{0:n}%"
        Me.TextEditQuarterRatiosTotal.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.TextEditQuarterRatiosTotal.Properties.Mask.EditMask = "P2"
        Me.TextEditQuarterRatiosTotal.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditQuarterRatiosTotal.Properties.MaxLength = 3
        Me.TextEditQuarterRatiosTotal.Properties.ReadOnly = true
        Me.TextEditQuarterRatiosTotal.Size = New System.Drawing.Size(107, 20)
        Me.TextEditQuarterRatiosTotal.TabIndex = 16
        Me.TextEditQuarterRatiosTotal.Tag = "Quarter4CommissionRatio"
        '
        'TextEditQuarter4Ratio
        '
        Me.TextEditQuarter4Ratio.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.TextEditQuarter4Ratio.Location = New System.Drawing.Point(376, 282)
        Me.TextEditQuarter4Ratio.Name = "TextEditQuarter4Ratio"
        Me.TextEditQuarter4Ratio.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.TextEditQuarter4Ratio.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditQuarter4Ratio.Properties.Appearance.Options.UseFont = true
        Me.TextEditQuarter4Ratio.Properties.Appearance.Options.UseForeColor = true
        Me.TextEditQuarter4Ratio.Properties.Appearance.Options.UseTextOptions = true
        Me.TextEditQuarter4Ratio.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.TextEditQuarter4Ratio.Properties.DisplayFormat.FormatString = "{0:n}%"
        Me.TextEditQuarter4Ratio.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.TextEditQuarter4Ratio.Properties.Mask.EditMask = "P2"
        Me.TextEditQuarter4Ratio.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditQuarter4Ratio.Properties.MaxLength = 3
        Me.TextEditQuarter4Ratio.Size = New System.Drawing.Size(107, 20)
        Me.TextEditQuarter4Ratio.TabIndex = 16
        Me.TextEditQuarter4Ratio.Tag = "Quarter4CommissionRatio"
        '
        'TextEditQuarter2Ratio
        '
        Me.TextEditQuarter2Ratio.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.TextEditQuarter2Ratio.Location = New System.Drawing.Point(376, 230)
        Me.TextEditQuarter2Ratio.Name = "TextEditQuarter2Ratio"
        Me.TextEditQuarter2Ratio.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.TextEditQuarter2Ratio.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditQuarter2Ratio.Properties.Appearance.Options.UseFont = true
        Me.TextEditQuarter2Ratio.Properties.Appearance.Options.UseForeColor = true
        Me.TextEditQuarter2Ratio.Properties.Appearance.Options.UseTextOptions = true
        Me.TextEditQuarter2Ratio.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.TextEditQuarter2Ratio.Properties.DisplayFormat.FormatString = "{0:n}%"
        Me.TextEditQuarter2Ratio.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.TextEditQuarter2Ratio.Properties.Mask.EditMask = "P2"
        Me.TextEditQuarter2Ratio.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditQuarter2Ratio.Properties.MaxLength = 3
        Me.TextEditQuarter2Ratio.Size = New System.Drawing.Size(107, 20)
        Me.TextEditQuarter2Ratio.TabIndex = 12
        Me.TextEditQuarter2Ratio.Tag = "Quarter2CommissionRatio"
        '
        'LabelWriteContract
        '
        Me.LabelWriteContract.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.LabelWriteContract.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelWriteContract.Location = New System.Drawing.Point(3, 39)
        Me.LabelWriteContract.Name = "LabelWriteContract"
        Me.LabelWriteContract.Size = New System.Drawing.Size(262, 13)
        Me.LabelWriteContract.TabIndex = 1
        Me.LabelWriteContract.Text = "Percentage earned for writing a contract (%):"
        '
        'TextEditWriteContract
        '
        Me.TextEditWriteContract.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.TextEditWriteContract.Location = New System.Drawing.Point(376, 36)
        Me.TextEditWriteContract.Name = "TextEditWriteContract"
        Me.TextEditWriteContract.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.TextEditWriteContract.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditWriteContract.Properties.Appearance.Options.UseFont = true
        Me.TextEditWriteContract.Properties.Appearance.Options.UseForeColor = true
        Me.TextEditWriteContract.Properties.Appearance.Options.UseTextOptions = true
        Me.TextEditWriteContract.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.TextEditWriteContract.Properties.DisplayFormat.FormatString = "{0:n}%"
        Me.TextEditWriteContract.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.TextEditWriteContract.Properties.Mask.EditMask = "P2"
        Me.TextEditWriteContract.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditWriteContract.Properties.MaxLength = 3
        Me.TextEditWriteContract.Size = New System.Drawing.Size(107, 20)
        Me.TextEditWriteContract.TabIndex = 2
        '
        'LabelQuarterCommissionRatios
        '
        Me.LabelQuarterCommissionRatios.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.LabelQuarterCommissionRatios.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.LabelQuarterCommissionRatios.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelQuarterCommissionRatios.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelQuarterCommissionRatios.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelQuarterCommissionRatios.LineVisible = true
        Me.LabelQuarterCommissionRatios.Location = New System.Drawing.Point(3, 171)
        Me.LabelQuarterCommissionRatios.Margin = New System.Windows.Forms.Padding(3, 3, 3, 12)
        Me.LabelQuarterCommissionRatios.Name = "LabelQuarterCommissionRatios"
        Me.LabelQuarterCommissionRatios.Size = New System.Drawing.Size(480, 18)
        Me.LabelQuarterCommissionRatios.TabIndex = 8
        Me.LabelQuarterCommissionRatios.Text = "Quarter Commission Ratios"
        '
        'LabelBAccountDefinition
        '
        Me.LabelBAccountDefinition.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.LabelBAccountDefinition.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.LabelBAccountDefinition.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelBAccountDefinition.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelBAccountDefinition.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelBAccountDefinition.LineVisible = true
        Me.LabelBAccountDefinition.Location = New System.Drawing.Point(3, 100)
        Me.LabelBAccountDefinition.Margin = New System.Windows.Forms.Padding(3, 3, 3, 12)
        Me.LabelBAccountDefinition.Name = "LabelBAccountDefinition"
        Me.LabelBAccountDefinition.Size = New System.Drawing.Size(480, 18)
        Me.LabelBAccountDefinition.TabIndex = 5
        Me.LabelBAccountDefinition.Text = "B Account Definition"
        '
        'LabelEarningRates
        '
        Me.LabelEarningRates.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.LabelEarningRates.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.LabelEarningRates.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelEarningRates.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelEarningRates.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelEarningRates.LineVisible = true
        Me.LabelEarningRates.Location = New System.Drawing.Point(3, 3)
        Me.LabelEarningRates.Margin = New System.Windows.Forms.Padding(3, 3, 3, 12)
        Me.LabelEarningRates.Name = "LabelEarningRates"
        Me.LabelEarningRates.Size = New System.Drawing.Size(480, 18)
        Me.LabelEarningRates.TabIndex = 0
        Me.LabelEarningRates.Text = "Commission Earning Rates"
        '
        'LabelCareTakeContract
        '
        Me.LabelCareTakeContract.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.LabelCareTakeContract.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCareTakeContract.Location = New System.Drawing.Point(3, 65)
        Me.LabelCareTakeContract.Name = "LabelCareTakeContract"
        Me.LabelCareTakeContract.Size = New System.Drawing.Size(227, 13)
        Me.LabelCareTakeContract.TabIndex = 3
        Me.LabelCareTakeContract.Text = "Percentage earned for care taking (%):"
        '
        'TextEditCareTakeContract
        '
        Me.TextEditCareTakeContract.AllowDrop = true
        Me.TextEditCareTakeContract.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.TextEditCareTakeContract.Location = New System.Drawing.Point(376, 62)
        Me.TextEditCareTakeContract.Margin = New System.Windows.Forms.Padding(3, 3, 3, 15)
        Me.TextEditCareTakeContract.Name = "TextEditCareTakeContract"
        Me.TextEditCareTakeContract.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.TextEditCareTakeContract.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditCareTakeContract.Properties.Appearance.Options.UseFont = true
        Me.TextEditCareTakeContract.Properties.Appearance.Options.UseForeColor = true
        Me.TextEditCareTakeContract.Properties.Appearance.Options.UseTextOptions = true
        Me.TextEditCareTakeContract.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.TextEditCareTakeContract.Properties.DisplayFormat.FormatString = "{0:n}%"
        Me.TextEditCareTakeContract.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.TextEditCareTakeContract.Properties.Mask.EditMask = "P2"
        Me.TextEditCareTakeContract.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditCareTakeContract.Properties.MaxLength = 3
        Me.TextEditCareTakeContract.Size = New System.Drawing.Size(107, 20)
        Me.TextEditCareTakeContract.TabIndex = 4
        '
        'LabelBAccountInactivityPeriod
        '
        Me.LabelBAccountInactivityPeriod.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.LabelBAccountInactivityPeriod.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelBAccountInactivityPeriod.Location = New System.Drawing.Point(3, 136)
        Me.LabelBAccountInactivityPeriod.Name = "LabelBAccountInactivityPeriod"
        Me.LabelBAccountInactivityPeriod.Size = New System.Drawing.Size(346, 13)
        Me.LabelBAccountInactivityPeriod.TabIndex = 6
        Me.LabelBAccountInactivityPeriod.Text = "Weeks of inactivity before an account becomes a B account:"
        '
        'TextEditBAccountInactivityPeriod
        '
        Me.TextEditBAccountInactivityPeriod.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.TextEditBAccountInactivityPeriod.Location = New System.Drawing.Point(376, 133)
        Me.TextEditBAccountInactivityPeriod.Margin = New System.Windows.Forms.Padding(3, 3, 3, 15)
        Me.TextEditBAccountInactivityPeriod.Name = "TextEditBAccountInactivityPeriod"
        Me.TextEditBAccountInactivityPeriod.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.TextEditBAccountInactivityPeriod.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditBAccountInactivityPeriod.Properties.Appearance.Options.UseFont = true
        Me.TextEditBAccountInactivityPeriod.Properties.Appearance.Options.UseForeColor = true
        Me.TextEditBAccountInactivityPeriod.Properties.Appearance.Options.UseTextOptions = true
        Me.TextEditBAccountInactivityPeriod.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.TextEditBAccountInactivityPeriod.Properties.DisplayFormat.FormatString = "#,###"
        Me.TextEditBAccountInactivityPeriod.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.TextEditBAccountInactivityPeriod.Properties.Mask.EditMask = "d"
        Me.TextEditBAccountInactivityPeriod.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditBAccountInactivityPeriod.Properties.MaxLength = 3
        Me.TextEditBAccountInactivityPeriod.Size = New System.Drawing.Size(107, 20)
        Me.TextEditBAccountInactivityPeriod.TabIndex = 7
        '
        'TabPageContractReport
        '
        Me.TabPageContractReport.Controls.Add(Me.Panel2)
        Me.TabPageContractReport.Name = "TabPageContractReport"
        Me.TabPageContractReport.PageVisible = false
        Me.TabPageContractReport.Size = New System.Drawing.Size(515, 413)
        Me.TabPageContractReport.Text = "Contract Report"
        '
        'Panel2
        '
        Me.Panel2.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Panel2.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Panel2.Controls.Add(Me.Panel3)
        Me.Panel2.Location = New System.Drawing.Point(3, 3)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(510, 409)
        Me.Panel2.TabIndex = 1
        '
        'Panel3
        '
        Me.Panel3.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Panel3.Controls.Add(Me.ButtonContractPreview)
        Me.Panel3.Controls.Add(Me.GroupTelephoneNumbers)
        Me.Panel3.Controls.Add(Me.GroupCompanyAddress)
        Me.Panel3.Controls.Add(Me.LabelVAT)
        Me.Panel3.Controls.Add(Me.TextEditVAT)
        Me.Panel3.Controls.Add(Me.LableHardCopyOptionsTitle)
        Me.Panel3.Location = New System.Drawing.Point(12, 12)
        Me.Panel3.Margin = New System.Windows.Forms.Padding(12)
        Me.Panel3.Name = "Panel3"
        Me.Panel3.Size = New System.Drawing.Size(486, 385)
        Me.Panel3.TabIndex = 1
        '
        'ButtonContractPreview
        '
        Me.ButtonContractPreview.AccessibleRole = System.Windows.Forms.AccessibleRole.None
        Me.ButtonContractPreview.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.ButtonContractPreview.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.ButtonContractPreview.Appearance.Options.UseFont = true
        Me.ButtonContractPreview.ImageIndex = 3
        Me.ButtonContractPreview.ImageList = Me.ImageList16x16
        Me.ButtonContractPreview.Location = New System.Drawing.Point(408, 359)
        Me.ButtonContractPreview.LookAndFeel.SkinName = "Black"
        Me.ButtonContractPreview.LookAndFeel.UseDefaultLookAndFeel = false
        Me.ButtonContractPreview.Name = "ButtonContractPreview"
        Me.ButtonContractPreview.Size = New System.Drawing.Size(75, 23)
        Me.ButtonContractPreview.TabIndex = 5
        Me.ButtonContractPreview.Text = "Preview"
        '
        'GroupTelephoneNumbers
        '
        Me.GroupTelephoneNumbers.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.GroupTelephoneNumbers.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.GroupTelephoneNumbers.Appearance.Options.UseFont = true
        Me.GroupTelephoneNumbers.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.GroupTelephoneNumbers.AppearanceCaption.Options.UseFont = true
        Me.GroupTelephoneNumbers.Controls.Add(Me.MemoTelephoneNumbers)
        Me.GroupTelephoneNumbers.Location = New System.Drawing.Point(3, 190)
        Me.GroupTelephoneNumbers.LookAndFeel.SkinName = "Black"
        Me.GroupTelephoneNumbers.LookAndFeel.UseDefaultLookAndFeel = false
        Me.GroupTelephoneNumbers.Name = "GroupTelephoneNumbers"
        Me.GroupTelephoneNumbers.Size = New System.Drawing.Size(480, 104)
        Me.GroupTelephoneNumbers.TabIndex = 4
        Me.GroupTelephoneNumbers.Tag = ""
        Me.GroupTelephoneNumbers.Text = "Branch Telephone Numbers"
        '
        'MemoTelephoneNumbers
        '
        Me.MemoTelephoneNumbers.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MemoTelephoneNumbers.EditValue = ""
        Me.MemoTelephoneNumbers.Location = New System.Drawing.Point(2, 21)
        Me.MemoTelephoneNumbers.Margin = New System.Windows.Forms.Padding(3, 3, 3, 0)
        Me.MemoTelephoneNumbers.Name = "MemoTelephoneNumbers"
        Me.MemoTelephoneNumbers.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.MemoTelephoneNumbers.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.MemoTelephoneNumbers.Properties.Appearance.Options.UseFont = true
        Me.MemoTelephoneNumbers.Properties.Appearance.Options.UseForeColor = true
        Me.MemoTelephoneNumbers.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.MemoTelephoneNumbers.Properties.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.MemoTelephoneNumbers.Size = New System.Drawing.Size(476, 81)
        Me.MemoTelephoneNumbers.TabIndex = 0
        '
        'GroupCompanyAddress
        '
        Me.GroupCompanyAddress.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.GroupCompanyAddress.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.GroupCompanyAddress.Appearance.Options.UseFont = true
        Me.GroupCompanyAddress.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.GroupCompanyAddress.AppearanceCaption.Options.UseFont = true
        Me.GroupCompanyAddress.Controls.Add(Me.MemoCompanyAddress)
        Me.GroupCompanyAddress.Location = New System.Drawing.Point(3, 71)
        Me.GroupCompanyAddress.LookAndFeel.SkinName = "Black"
        Me.GroupCompanyAddress.LookAndFeel.UseDefaultLookAndFeel = false
        Me.GroupCompanyAddress.Margin = New System.Windows.Forms.Padding(3, 3, 3, 12)
        Me.GroupCompanyAddress.Name = "GroupCompanyAddress"
        Me.GroupCompanyAddress.Size = New System.Drawing.Size(480, 104)
        Me.GroupCompanyAddress.TabIndex = 3
        Me.GroupCompanyAddress.Tag = ""
        Me.GroupCompanyAddress.Text = "Company Address"
        '
        'MemoCompanyAddress
        '
        Me.MemoCompanyAddress.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MemoCompanyAddress.EditValue = ""
        Me.MemoCompanyAddress.Location = New System.Drawing.Point(2, 21)
        Me.MemoCompanyAddress.Margin = New System.Windows.Forms.Padding(3, 3, 3, 0)
        Me.MemoCompanyAddress.Name = "MemoCompanyAddress"
        Me.MemoCompanyAddress.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.MemoCompanyAddress.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.MemoCompanyAddress.Properties.Appearance.Options.UseFont = true
        Me.MemoCompanyAddress.Properties.Appearance.Options.UseForeColor = true
        Me.MemoCompanyAddress.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.MemoCompanyAddress.Properties.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.MemoCompanyAddress.Size = New System.Drawing.Size(476, 81)
        Me.MemoCompanyAddress.TabIndex = 0
        '
        'LabelVAT
        '
        Me.LabelVAT.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.LabelVAT.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelVAT.Location = New System.Drawing.Point(3, 39)
        Me.LabelVAT.Name = "LabelVAT"
        Me.LabelVAT.Size = New System.Drawing.Size(122, 13)
        Me.LabelVAT.TabIndex = 1
        Me.LabelVAT.Text = "VAT percentage (%):"
        '
        'TextEditVAT
        '
        Me.TextEditVAT.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.TextEditVAT.Location = New System.Drawing.Point(142, 36)
        Me.TextEditVAT.Margin = New System.Windows.Forms.Padding(3, 3, 3, 12)
        Me.TextEditVAT.Name = "TextEditVAT"
        Me.TextEditVAT.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.TextEditVAT.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditVAT.Properties.Appearance.Options.UseFont = true
        Me.TextEditVAT.Properties.Appearance.Options.UseForeColor = true
        Me.TextEditVAT.Properties.Appearance.Options.UseTextOptions = true
        Me.TextEditVAT.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.TextEditVAT.Properties.DisplayFormat.FormatString = "{0:n}%"
        Me.TextEditVAT.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.TextEditVAT.Properties.Mask.EditMask = "P2"
        Me.TextEditVAT.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditVAT.Properties.MaxLength = 3
        Me.TextEditVAT.Size = New System.Drawing.Size(107, 20)
        Me.TextEditVAT.TabIndex = 2
        '
        'LableHardCopyOptionsTitle
        '
        Me.LableHardCopyOptionsTitle.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.LableHardCopyOptionsTitle.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.LableHardCopyOptionsTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LableHardCopyOptionsTitle.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LableHardCopyOptionsTitle.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LableHardCopyOptionsTitle.LineVisible = true
        Me.LableHardCopyOptionsTitle.Location = New System.Drawing.Point(3, 3)
        Me.LableHardCopyOptionsTitle.Margin = New System.Windows.Forms.Padding(3, 3, 3, 12)
        Me.LableHardCopyOptionsTitle.Name = "LableHardCopyOptionsTitle"
        Me.LableHardCopyOptionsTitle.Size = New System.Drawing.Size(480, 18)
        Me.LableHardCopyOptionsTitle.TabIndex = 0
        Me.LableHardCopyOptionsTitle.Text = "Contract Hard Copy Options"
        '
        'TabPageBudgets
        '
        Me.TabPageBudgets.Controls.Add(Me.Panel4)
        Me.TabPageBudgets.Name = "TabPageBudgets"
        Me.TabPageBudgets.Size = New System.Drawing.Size(515, 413)
        Me.TabPageBudgets.Text = "Budgets"
        '
        'Panel4
        '
        Me.Panel4.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Panel4.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Panel4.Controls.Add(Me.Panel5)
        Me.Panel4.Location = New System.Drawing.Point(3, 3)
        Me.Panel4.Name = "Panel4"
        Me.Panel4.Size = New System.Drawing.Size(510, 409)
        Me.Panel4.TabIndex = 1
        '
        'Panel5
        '
        Me.Panel5.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Panel5.Controls.Add(Me.GroupWholesalePurchases)
        Me.Panel5.Location = New System.Drawing.Point(12, 12)
        Me.Panel5.Margin = New System.Windows.Forms.Padding(12)
        Me.Panel5.Name = "Panel5"
        Me.Panel5.Size = New System.Drawing.Size(486, 385)
        Me.Panel5.TabIndex = 1
        '
        'TabPageNoSettings
        '
        Me.TabPageNoSettings.Controls.Add(Me.Panel1)
        Me.TabPageNoSettings.Name = "TabPageNoSettings"
        Me.TabPageNoSettings.PageVisible = false
        Me.TabPageNoSettings.Size = New System.Drawing.Size(515, 413)
        Me.TabPageNoSettings.Text = "No Settings Available"
        '
        'Panel1
        '
        Me.Panel1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.Panel1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Panel1.Controls.Add(Me.TableLayoutPanel1)
        Me.Panel1.Location = New System.Drawing.Point(3, 3)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(510, 409)
        Me.Panel1.TabIndex = 1
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanel1.BackColor = System.Drawing.Color.Transparent
        Me.TableLayoutPanel1.ColumnCount = 1
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100!))
        Me.TableLayoutPanel1.Controls.Add(Me.LabelControl1, 0, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.PictureBox1, 0, 1)
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(12, 12)
        Me.TableLayoutPanel1.Margin = New System.Windows.Forms.Padding(12)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 2
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(486, 385)
        Me.TableLayoutPanel1.TabIndex = 0
        '
        'LabelControl1
        '
        Me.LabelControl1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.LabelControl1.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl1.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top
        Me.LabelControl1.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap
        Me.LabelControl1.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelControl1.Location = New System.Drawing.Point(3, 3)
        Me.LabelControl1.Margin = New System.Windows.Forms.Padding(3, 3, 3, 15)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(480, 26)
        Me.LabelControl1.TabIndex = 0
        Me.LabelControl1.Text = "You do not have permission to modify any system settings. Can I offer you a cup "& _ 
    "of tea instead?"
        '
        'PictureBox1
        '
        Me.PictureBox1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.PictureBox1.Image = Global.Nova2.My.Resources.Resources.coffee256
        Me.PictureBox1.Location = New System.Drawing.Point(3, 47)
        Me.PictureBox1.Name = "PictureBox1"
        Me.PictureBox1.Size = New System.Drawing.Size(480, 335)
        Me.PictureBox1.SizeMode = System.Windows.Forms.PictureBoxSizeMode.CenterImage
        Me.PictureBox1.TabIndex = 1
        Me.PictureBox1.TabStop = false
        '
        'ButtonSave
        '
        Me.ButtonSave.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.ButtonSave.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.ButtonSave.Appearance.Options.UseFont = true
        Me.ButtonSave.ImageIndex = 1
        Me.ButtonSave.ImageList = Me.ImageList24x24
        Me.ButtonSave.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonSave.Location = New System.Drawing.Point(327, 12)
        Me.ButtonSave.LookAndFeel.SkinName = "Black"
        Me.ButtonSave.LookAndFeel.UseDefaultLookAndFeel = false
        Me.ButtonSave.Name = "ButtonSave"
        Me.ButtonSave.Size = New System.Drawing.Size(100, 28)
        Me.ButtonSave.TabIndex = 0
        Me.ButtonSave.Text = "Save"
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"),System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = true
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(433, 12)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = false
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(100, 28)
        Me.ButtonCancel.TabIndex = 1
        Me.ButtonCancel.Text = "Cancel"
        '
        'GroupWholesalePurchases
        '
        Me.GroupWholesalePurchases.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.GroupWholesalePurchases.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.GroupWholesalePurchases.Appearance.Options.UseFont = true
        Me.GroupWholesalePurchases.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.GroupWholesalePurchases.AppearanceCaption.ForeColor = System.Drawing.Color.DimGray
        Me.GroupWholesalePurchases.AppearanceCaption.Options.UseFont = true
        Me.GroupWholesalePurchases.AppearanceCaption.Options.UseForeColor = true
        Me.GroupWholesalePurchases.Controls.Add(Me.ButtonDeleteBudget)
        Me.GroupWholesalePurchases.Controls.Add(Me.ButtonAddBudget)
        Me.GroupWholesalePurchases.Controls.Add(Me.ButtonEditBudget)
        Me.GroupWholesalePurchases.Controls.Add(Me.GridBudget)
        Me.GroupWholesalePurchases.Controls.Add(Me.LabelSearchBudget)
        Me.GroupWholesalePurchases.Controls.Add(Me.TextEditSearchBudget)
        Me.GroupWholesalePurchases.Controls.Add(Me.PictureClearSearchBudget)
        Me.GroupWholesalePurchases.Controls.Add(Me.PictureAdvancedSearchBudget)
        Me.GroupWholesalePurchases.Location = New System.Drawing.Point(3, 3)
        Me.GroupWholesalePurchases.LookAndFeel.SkinName = "Black"
        Me.GroupWholesalePurchases.LookAndFeel.UseDefaultLookAndFeel = false
        Me.GroupWholesalePurchases.Name = "GroupWholesalePurchases"
        Me.GroupWholesalePurchases.Size = New System.Drawing.Size(429, 163)
        Me.GroupWholesalePurchases.TabIndex = 10
        Me.GroupWholesalePurchases.Text = "Budgets"
        '
        'ButtonDeleteBudget
        '
        Me.ButtonDeleteBudget.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left),System.Windows.Forms.AnchorStyles)
        Me.ButtonDeleteBudget.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.ButtonDeleteBudget.Appearance.Options.UseFont = true
        Me.ButtonDeleteBudget.ImageIndex = 2
        Me.ButtonDeleteBudget.ImageList = Me.ImageList16x16
        Me.ButtonDeleteBudget.Location = New System.Drawing.Point(167, 135)
        Me.ButtonDeleteBudget.LookAndFeel.SkinName = "Black"
        Me.ButtonDeleteBudget.LookAndFeel.UseDefaultLookAndFeel = false
        Me.ButtonDeleteBudget.Margin = New System.Windows.Forms.Padding(3, 3, 15, 3)
        Me.ButtonDeleteBudget.Name = "ButtonDeleteBudget"
        Me.ButtonDeleteBudget.Size = New System.Drawing.Size(75, 23)
        Me.ButtonDeleteBudget.TabIndex = 4
        Me.ButtonDeleteBudget.Text = "Delete"
        '
        'ButtonAddBudget
        '
        Me.ButtonAddBudget.AccessibleRole = System.Windows.Forms.AccessibleRole.None
        Me.ButtonAddBudget.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left),System.Windows.Forms.AnchorStyles)
        Me.ButtonAddBudget.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.ButtonAddBudget.Appearance.Options.UseFont = true
        Me.ButtonAddBudget.ImageIndex = 0
        Me.ButtonAddBudget.ImageList = Me.ImageList16x16
        Me.ButtonAddBudget.Location = New System.Drawing.Point(5, 135)
        Me.ButtonAddBudget.LookAndFeel.SkinName = "Black"
        Me.ButtonAddBudget.LookAndFeel.UseDefaultLookAndFeel = false
        Me.ButtonAddBudget.Name = "ButtonAddBudget"
        Me.ButtonAddBudget.Size = New System.Drawing.Size(75, 23)
        Me.ButtonAddBudget.TabIndex = 2
        Me.ButtonAddBudget.Text = "Add"
        '
        'ButtonEditBudget
        '
        Me.ButtonEditBudget.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left),System.Windows.Forms.AnchorStyles)
        Me.ButtonEditBudget.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.ButtonEditBudget.Appearance.Options.UseFont = true
        Me.ButtonEditBudget.ImageIndex = 1
        Me.ButtonEditBudget.ImageList = Me.ImageList16x16
        Me.ButtonEditBudget.Location = New System.Drawing.Point(86, 135)
        Me.ButtonEditBudget.LookAndFeel.SkinName = "Black"
        Me.ButtonEditBudget.LookAndFeel.UseDefaultLookAndFeel = false
        Me.ButtonEditBudget.Name = "ButtonEditBudget"
        Me.ButtonEditBudget.Size = New System.Drawing.Size(75, 23)
        Me.ButtonEditBudget.TabIndex = 3
        Me.ButtonEditBudget.Text = "Edit"
        '
        'GridBudget
        '
        Me.GridBudget.AllowUserToAddRows = false
        Me.GridBudget.AllowUserToDeleteRows = false
        Me.GridBudget.AllowUserToOrderColumns = true
        Me.GridBudget.AllowUserToResizeRows = false
        DataGridViewCellStyle1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridBudget.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle1
        Me.GridBudget.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom)  _
            Or System.Windows.Forms.AnchorStyles.Left)  _
            Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.GridBudget.BackgroundColor = System.Drawing.Color.White
        Me.GridBudget.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridBudget.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridBudget.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle2.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        DataGridViewCellStyle2.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridBudget.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle2
        Me.GridBudget.ColumnHeadersHeight = 22
        Me.GridBudget.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridBudget.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.PurchaseVanColumn})
        Me.GridBudget.EnableHeadersVisualStyles = false
        Me.GridBudget.GridColor = System.Drawing.Color.White
        Me.GridBudget.Location = New System.Drawing.Point(2, 22)
        Me.GridBudget.Margin = New System.Windows.Forms.Padding(0, 0, 0, 3)
        Me.GridBudget.Name = "GridBudget"
        Me.GridBudget.ReadOnly = true
        Me.GridBudget.RowHeadersVisible = false
        DataGridViewCellStyle3.ForeColor = System.Drawing.Color.DimGray
        Me.GridBudget.RowsDefaultCellStyle = DataGridViewCellStyle3
        Me.GridBudget.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridBudget.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridBudget.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridBudget.RowTemplate.Height = 19
        Me.GridBudget.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridBudget.ShowCellToolTips = false
        Me.GridBudget.Size = New System.Drawing.Size(425, 107)
        Me.GridBudget.StandardTab = true
        Me.GridBudget.TabIndex = 1
        '
        'PurchaseVanColumn
        '
        Me.PurchaseVanColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.PurchaseVanColumn.DataPropertyName = "VanName"
        Me.PurchaseVanColumn.FillWeight = 40!
        Me.PurchaseVanColumn.HeaderText = "Van"
        Me.PurchaseVanColumn.Name = "PurchaseVanColumn"
        Me.PurchaseVanColumn.ReadOnly = true
        '
        'LabelSearchBudget
        '
        Me.LabelSearchBudget.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.LabelSearchBudget.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.LabelSearchBudget.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelSearchBudget.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelSearchBudget.Location = New System.Drawing.Point(271, 139)
        Me.LabelSearchBudget.Name = "LabelSearchBudget"
        Me.LabelSearchBudget.Size = New System.Drawing.Size(45, 13)
        Me.LabelSearchBudget.TabIndex = 5
        Me.LabelSearchBudget.Text = "Search:"
        '
        'TextEditSearchBudget
        '
        Me.TextEditSearchBudget.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchBudget.EditValue = ""
        Me.TextEditSearchBudget.Location = New System.Drawing.Point(322, 136)
        Me.TextEditSearchBudget.Margin = New System.Windows.Forms.Padding(3, 4, 3, 5)
        Me.TextEditSearchBudget.Name = "TextEditSearchBudget"
        Me.TextEditSearchBudget.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.TextEditSearchBudget.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchBudget.Properties.Appearance.Options.UseFont = true
        Me.TextEditSearchBudget.Properties.Appearance.Options.UseForeColor = true
        Me.TextEditSearchBudget.Size = New System.Drawing.Size(80, 20)
        Me.TextEditSearchBudget.TabIndex = 6
        '
        'PictureClearSearchBudget
        '
        Me.PictureClearSearchBudget.AllowDrop = true
        Me.PictureClearSearchBudget.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchBudget.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchBudget.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchBudget.Location = New System.Drawing.Point(408, 3)
        Me.PictureClearSearchBudget.Name = "PictureClearSearchBudget"
        Me.PictureClearSearchBudget.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchBudget.Properties.Appearance.Options.UseBackColor = true
        Me.PictureClearSearchBudget.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchBudget.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem1.Text = "Clear Search"
        ToolTipItem1.LeftIndent = 6
        ToolTipItem1.Text = "Click here to clear all search boxes."
        SuperToolTip1.Items.Add(ToolTipTitleItem1)
        SuperToolTip1.Items.Add(ToolTipItem1)
        Me.PictureClearSearchBudget.SuperTip = SuperToolTip1
        Me.PictureClearSearchBudget.TabIndex = 0
        Me.PictureClearSearchBudget.TabStop = true
        '
        'PictureAdvancedSearchBudget
        '
        Me.PictureAdvancedSearchBudget.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right),System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearchBudget.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearchBudget.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearchBudget.Location = New System.Drawing.Point(408, 138)
        Me.PictureAdvancedSearchBudget.Margin = New System.Windows.Forms.Padding(3, 6, 3, 7)
        Me.PictureAdvancedSearchBudget.Name = "PictureAdvancedSearchBudget"
        Me.PictureAdvancedSearchBudget.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearchBudget.Properties.Appearance.Options.UseBackColor = true
        Me.PictureAdvancedSearchBudget.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearchBudget.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem2.Text = "Advanced Search"
        ToolTipItem2.LeftIndent = 6
        ToolTipItem2.Text = "Click here to search individual column values."
        SuperToolTip2.Items.Add(ToolTipTitleItem2)
        SuperToolTip2.Items.Add(ToolTipItem2)
        Me.PictureAdvancedSearchBudget.SuperTip = SuperToolTip2
        Me.PictureAdvancedSearchBudget.TabIndex = 7
        Me.PictureAdvancedSearchBudget.TabStop = true
        '
        'FormSettings
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0,Byte))
        Me.Appearance.Options.UseBackColor = true
        Me.Appearance.Options.UseFont = true
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7!, 13!)
        Me.ClientSize = New System.Drawing.Size(545, 505)
        Me.Controls.Add(Me.TabSet)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = false
        Me.MinimumSize = New System.Drawing.Size(561, 543)
        Me.Name = "FormSettings"
        Me.Text = "Settings"
        Me.Controls.SetChildIndex(Me.PanelButtonBar, 0)
        Me.Controls.SetChildIndex(Me.TabSet, 0)
        CType(Me.PanelButtonBar,System.ComponentModel.ISupportInitialize).EndInit
        Me.PanelButtonBar.ResumeLayout(false)
        CType(Me.ErrorManager,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.TabSet,System.ComponentModel.ISupportInitialize).EndInit
        Me.TabSet.ResumeLayout(false)
        Me.TabPageCommission.ResumeLayout(false)
        Me.PanelDetails.ResumeLayout(false)
        Me.PanelAccountManagerDetails.ResumeLayout(false)
        Me.PanelAccountManagerDetails.PerformLayout
        CType(Me.TextEditQuarter3Ratio.Properties,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.TextEditQuarter1Ratio.Properties,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.TextEditQuarterRatiosTotal.Properties,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.TextEditQuarter4Ratio.Properties,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.TextEditQuarter2Ratio.Properties,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.TextEditWriteContract.Properties,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.TextEditCareTakeContract.Properties,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.TextEditBAccountInactivityPeriod.Properties,System.ComponentModel.ISupportInitialize).EndInit
        Me.TabPageContractReport.ResumeLayout(false)
        Me.Panel2.ResumeLayout(false)
        Me.Panel3.ResumeLayout(false)
        Me.Panel3.PerformLayout
        CType(Me.GroupTelephoneNumbers,System.ComponentModel.ISupportInitialize).EndInit
        Me.GroupTelephoneNumbers.ResumeLayout(false)
        CType(Me.MemoTelephoneNumbers.Properties,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.GroupCompanyAddress,System.ComponentModel.ISupportInitialize).EndInit
        Me.GroupCompanyAddress.ResumeLayout(false)
        CType(Me.MemoCompanyAddress.Properties,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.TextEditVAT.Properties,System.ComponentModel.ISupportInitialize).EndInit
        Me.TabPageBudgets.ResumeLayout(false)
        Me.Panel4.ResumeLayout(false)
        Me.Panel5.ResumeLayout(false)
        Me.TabPageNoSettings.ResumeLayout(false)
        Me.Panel1.ResumeLayout(false)
        Me.TableLayoutPanel1.ResumeLayout(false)
        CType(Me.PictureBox1,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.GroupWholesalePurchases,System.ComponentModel.ISupportInitialize).EndInit
        Me.GroupWholesalePurchases.ResumeLayout(false)
        CType(Me.GridBudget,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.TextEditSearchBudget.Properties,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.PictureClearSearchBudget.Properties,System.ComponentModel.ISupportInitialize).EndInit
        CType(Me.PictureAdvancedSearchBudget.Properties,System.ComponentModel.ISupportInitialize).EndInit
        Me.ResumeLayout(false)

End Sub
    Friend WithEvents ImageList16x16 As System.Windows.Forms.ImageList
    Friend WithEvents TabSet As DevExpress.XtraTab.XtraTabControl
    Friend WithEvents TabPageCommission As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents PanelDetails As System.Windows.Forms.Panel
    Friend WithEvents PanelAccountManagerDetails As System.Windows.Forms.Panel
    Friend WithEvents LabelWriteContract As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditWriteContract As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelCareTakeContract As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditCareTakeContract As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelBAccountInactivityPeriod As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditBAccountInactivityPeriod As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ButtonSave As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelBAccountDefinition As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelEarningRates As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelQuarterCommissionRatios As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelQuarter1Ratio As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditQuarter1Ratio As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelQuarter2Ratio As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditQuarter2Ratio As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelQuarter3Ratio As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditQuarter3Ratio As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelQuarterRatiosTotal As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelQuarter4Ratio As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditQuarter4Ratio As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TabPageNoSettings As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents PictureBox1 As System.Windows.Forms.PictureBox
    Friend WithEvents TextEditQuarterRatiosTotal As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents TabPageContractReport As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel2 As System.Windows.Forms.Panel
    Friend WithEvents Panel3 As System.Windows.Forms.Panel
    Friend WithEvents LabelVAT As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditVAT As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LableHardCopyOptionsTitle As DevExpress.XtraEditors.LabelControl
    Friend WithEvents GroupCompanyAddress As DevExpress.XtraEditors.GroupControl
    Friend WithEvents MemoCompanyAddress As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents GroupTelephoneNumbers As DevExpress.XtraEditors.GroupControl
    Friend WithEvents MemoTelephoneNumbers As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents ButtonContractPreview As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents TabPageBudgets As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel4 As System.Windows.Forms.Panel
    Friend WithEvents Panel5 As System.Windows.Forms.Panel
    Friend WithEvents GroupWholesalePurchases As DevExpress.XtraEditors.GroupControl
    Friend WithEvents ButtonDeleteBudget As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonAddBudget As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonEditBudget As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GridBudget As System.Windows.Forms.DataGridView
    Friend WithEvents PurchaseVanColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents LabelSearchBudget As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditSearchBudget As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearchBudget As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearchBudget As DevExpress.XtraEditors.PictureEdit

End Class
