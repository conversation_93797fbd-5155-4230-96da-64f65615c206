Public Class LookupBrand

    Public Sub New(ByVal GridData As BindingSource, ByVal AllowMultiSelect As Boolean)

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        GridController = New GridManager _
        (GridItems, _
        TextEditSearch, _
        GridData, _
        My.Settings.DBConnection, _
        PictureAdvancedSearch, _
        PictureClearSearch, _
        Nothing, _
        Nothing)

        ' Apply multi-select restrictions if necessary.
        GridItems.MultiSelect = AllowMultiSelect

    End Sub

    Public Overloads Shared Function SelectRows _
    (ByVal ConnectionString As String, _
    ByVal AllowMultiSelect As Boolean, _
    ByVal GridToExclude As DataGridView) _
    As List(Of DataRow)

        ' A string variable to hold any potential errors during data retrieval.
        Dim ErrorMessage As String = String.Empty

        ' Create a data source for the list.
        Dim GridData As BindingSource = Lookup.GetBrands(ConnectionString, ErrorMessage, GridToExclude)

        ' Create an instance of this lookup list form and return the user-selected rows.
        Using Selector As New LookupBrand(GridData, AllowMultiSelect)
            Return SelectRows(Selector, ErrorMessage)
        End Using

    End Function

    Public Shared Function SelectRowsByPermission_EditMyProvisionalBookings _
    (ByVal ConnectionString As String, _
    ByVal AllowMultiSelect As Boolean, _
    ByVal GridToExclude As DataGridView) _
    As List(Of DataRow)

        ' A string variable to hold any potential errors during data retrieval.
        Dim ErrorMessage As String = String.Empty

        ' Create a data source for the list.
        Dim GridData As BindingSource = Lookup.GetBrandsByPermission_EditMyProvisionalBookings _
        (ConnectionString, ErrorMessage, GridToExclude)

        ' Create an instance of this lookup list form and return the user-selected rows.
        Using Selector As New LookupBrand(GridData, AllowMultiSelect)
            Return SelectRows(Selector, ErrorMessage)
        End Using

    End Function

    Public Overloads Shared Function SelectRowsByClient _
    (ByVal ConnectionString As String, _
    ByVal AllowMultiSelect As Boolean, _
    ByVal GridToExclude As DataGridView, _
    ByVal ClientID As Integer) _
    As List(Of DataRow)

        ' A string variable to hold any potential errors during data retrieval.
        Dim ErrorMessage As String = String.Empty

        ' Create a data source for the list.
        Dim GridData As BindingSource = Lookup.GetBrandsByClient(ConnectionString, ErrorMessage, GridToExclude, ClientID)

        ' Create an instance of this lookup list form and return the user-selected rows.
        Using Selector As New LookupBrand(GridData, AllowMultiSelect)
            Return SelectRows(Selector, ErrorMessage)
        End Using

    End Function

End Class
