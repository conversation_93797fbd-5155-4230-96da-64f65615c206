<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformClientsAndBrands
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformClientsAndBrands))
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonClients = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl6 = New DevExpress.XtraEditors.LabelControl()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.Panel2 = New System.Windows.Forms.Panel()
        Me.ButtonBrandCategories = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.ButtonBrandFamilies = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl5 = New DevExpress.XtraEditors.LabelControl()
        Me.ButtonBrands = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelPassword = New DevExpress.XtraEditors.LabelControl()
        Me.Panel1.SuspendLayout()
        Me.Panel2.SuspendLayout()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!)
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Margin = New System.Windows.Forms.Padding(5, 5, 5, 21)
        Me.LabelTitle.Size = New System.Drawing.Size(232, 37)
        Me.LabelTitle.TabIndex = 0
        Me.LabelTitle.Text = "Clients && Brands"
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "tag.png")
        Me.ImageList24x24.Images.SetKeyName(1, "users.png")
        Me.ImageList24x24.Images.SetKeyName(2, "wired.png")
        '
        'ButtonClients
        '
        Me.ButtonClients.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonClients.Appearance.Options.UseFont = True
        Me.ButtonClients.ImageIndex = 1
        Me.ButtonClients.ImageList = Me.ImageList24x24
        Me.ButtonClients.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonClients.Location = New System.Drawing.Point(22, 9)
        Me.ButtonClients.LookAndFeel.SkinName = "Black"
        Me.ButtonClients.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonClients.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.ButtonClients.Name = "ButtonClients"
        Me.ButtonClients.Size = New System.Drawing.Size(207, 42)
        Me.ButtonClients.TabIndex = 0
        Me.ButtonClients.Text = "Clients"
        '
        'LabelControl6
        '
        Me.LabelControl6.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl6.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl6.Location = New System.Drawing.Point(248, 22)
        Me.LabelControl6.Margin = New System.Windows.Forms.Padding(15, 4, 4, 4)
        Me.LabelControl6.Name = "LabelControl6"
        Me.LabelControl6.Size = New System.Drawing.Size(230, 17)
        Me.LabelControl6.TabIndex = 1
        Me.LabelControl6.Text = "View or modify the list of clients."
        '
        'Panel1
        '
        Me.Panel1.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.Panel1.Controls.Add(Me.ButtonClients)
        Me.Panel1.Controls.Add(Me.LabelControl6)
        Me.Panel1.Location = New System.Drawing.Point(152, 177)
        Me.Panel1.Margin = New System.Windows.Forms.Padding(4)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(739, 59)
        Me.Panel1.TabIndex = 1
        '
        'Panel2
        '
        Me.Panel2.Anchor = System.Windows.Forms.AnchorStyles.None
        Me.Panel2.Controls.Add(Me.ButtonBrandCategories)
        Me.Panel2.Controls.Add(Me.LabelControl1)
        Me.Panel2.Controls.Add(Me.ButtonBrandFamilies)
        Me.Panel2.Controls.Add(Me.LabelControl5)
        Me.Panel2.Controls.Add(Me.ButtonBrands)
        Me.Panel2.Controls.Add(Me.LabelPassword)
        Me.Panel2.Location = New System.Drawing.Point(152, 244)
        Me.Panel2.Margin = New System.Windows.Forms.Padding(4)
        Me.Panel2.Name = "Panel2"
        Me.Panel2.Size = New System.Drawing.Size(739, 181)
        Me.Panel2.TabIndex = 8
        '
        'ButtonBrandCategories
        '
        Me.ButtonBrandCategories.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonBrandCategories.Appearance.Options.UseFont = True
        Me.ButtonBrandCategories.ImageIndex = 2
        Me.ButtonBrandCategories.ImageList = Me.ImageList24x24
        Me.ButtonBrandCategories.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonBrandCategories.Location = New System.Drawing.Point(22, 120)
        Me.ButtonBrandCategories.LookAndFeel.SkinName = "Black"
        Me.ButtonBrandCategories.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonBrandCategories.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonBrandCategories.Name = "ButtonBrandCategories"
        Me.ButtonBrandCategories.Size = New System.Drawing.Size(207, 42)
        Me.ButtonBrandCategories.TabIndex = 12
        Me.ButtonBrandCategories.Text = "Brand Groups"
        '
        'LabelControl1
        '
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl1.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl1.Location = New System.Drawing.Point(248, 133)
        Me.LabelControl1.Margin = New System.Windows.Forms.Padding(15, 4, 4, 4)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(357, 17)
        Me.LabelControl1.TabIndex = 13
        Me.LabelControl1.Text = "Add brands to Categories for Reporting purposes."
        '
        'ButtonBrandFamilies
        '
        Me.ButtonBrandFamilies.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonBrandFamilies.Appearance.Options.UseFont = True
        Me.ButtonBrandFamilies.ImageIndex = 2
        Me.ButtonBrandFamilies.ImageList = Me.ImageList24x24
        Me.ButtonBrandFamilies.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonBrandFamilies.Location = New System.Drawing.Point(22, 61)
        Me.ButtonBrandFamilies.LookAndFeel.SkinName = "Black"
        Me.ButtonBrandFamilies.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonBrandFamilies.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonBrandFamilies.Name = "ButtonBrandFamilies"
        Me.ButtonBrandFamilies.Size = New System.Drawing.Size(207, 42)
        Me.ButtonBrandFamilies.TabIndex = 10
        Me.ButtonBrandFamilies.Text = "Brand Families"
        '
        'LabelControl5
        '
        Me.LabelControl5.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl5.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl5.Location = New System.Drawing.Point(248, 18)
        Me.LabelControl5.Margin = New System.Windows.Forms.Padding(15, 4, 4, 4)
        Me.LabelControl5.Name = "LabelControl5"
        Me.LabelControl5.Size = New System.Drawing.Size(235, 17)
        Me.LabelControl5.TabIndex = 9
        Me.LabelControl5.Text = "View or modify the list of brands."
        '
        'ButtonBrands
        '
        Me.ButtonBrands.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonBrands.Appearance.Options.UseFont = True
        Me.ButtonBrands.ImageIndex = 0
        Me.ButtonBrands.ImageList = Me.ImageList24x24
        Me.ButtonBrands.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonBrands.Location = New System.Drawing.Point(22, 5)
        Me.ButtonBrands.LookAndFeel.SkinName = "Black"
        Me.ButtonBrands.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonBrands.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.ButtonBrands.Name = "ButtonBrands"
        Me.ButtonBrands.Size = New System.Drawing.Size(207, 42)
        Me.ButtonBrands.TabIndex = 8
        Me.ButtonBrands.Text = "Brands"
        '
        'LabelPassword
        '
        Me.LabelPassword.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelPassword.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelPassword.Location = New System.Drawing.Point(248, 74)
        Me.LabelPassword.Margin = New System.Windows.Forms.Padding(15, 4, 4, 4)
        Me.LabelPassword.Name = "LabelPassword"
        Me.LabelPassword.Size = New System.Drawing.Size(468, 17)
        Me.LabelPassword.TabIndex = 11
        Me.LabelPassword.Text = "Configure families of brands that don't compete with one another."
        '
        'SubformClientsAndBrands
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(9.0!, 17.0!)
        Me.Controls.Add(Me.Panel2)
        Me.Controls.Add(Me.Panel1)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(5)
        Me.Name = "SubformClientsAndBrands"
        Me.Padding = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        Me.Controls.SetChildIndex(Me.Panel1, 0)
        Me.Controls.SetChildIndex(Me.Panel2, 0)
        Me.Panel1.ResumeLayout(False)
        Me.Panel1.PerformLayout()
        Me.Panel2.ResumeLayout(False)
        Me.Panel2.PerformLayout()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents ButtonClients As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl6 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents Panel2 As Panel
    Friend WithEvents ButtonBrandCategories As SimpleButton
    Friend WithEvents LabelControl1 As LabelControl
    Friend WithEvents ButtonBrandFamilies As SimpleButton
    Friend WithEvents LabelControl5 As LabelControl
    Friend WithEvents ButtonBrands As SimpleButton
    Friend WithEvents LabelPassword As LabelControl
End Class
