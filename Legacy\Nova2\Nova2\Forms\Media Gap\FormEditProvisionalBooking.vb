Public Class FormEditProvisionalBooking

    Private _FirstWeek As Date
    Private Grid As DataGridView
    Private ProvisionalBookingTable As DataSetMediaGap.ProvisionalBookingDataTable
    Private Gap As MediaGap

#Region "Properties"

    Private Property FirstWeek() As Date
        Get
            ' Keep adding a day to the First Week date until it becomes a Monday.
            While Not _FirstWeek.DayOfWeek = DayOfWeek.Monday
                _FirstWeek = _FirstWeek.AddDays(1)
            End While
            Return _FirstWeek
        End Get
        Set(ByVal value As Date)
            ' Update the displayed date in the hyperlink.
            HyperlinkFirstWeek.Text = value.ToLongDateString
            ' Update the field's value.
            _FirstWeek = value
            ' Update the LastWeek value.
            LastWeek = DateAdd(DateInterval.WeekOfYear, CInt(TextEditWeeks.EditValue) - 1, value)
        End Set
    End Property

    Private Property LastWeek() As Date
        Get
            Return DateAdd(DateInterval.WeekOfYear, CInt(TextEditWeeks.EditValue) - 1, FirstWeek)
        End Get
        Set(ByVal value As Date)
            ' Update the text in the hyper link.
            HyperlinkLastWeek.Text = value.ToLongDateString
            ' Update the value in TextEditWeeks if necessary.
            Dim DateDifference As Integer = CInt(DateDiff(DateInterval.WeekOfYear, FirstWeek, value)) + 1
            Dim Weeks As Integer = CInt(TextEditWeeks.EditValue)
            If Not DateDifference = Weeks Then
                TextEditWeeks.EditValue = DateDifference
            End If
        End Set
    End Property

#End Region

#Region "Public Shared Methods"

    Public Shared Sub ChangeDate _
    (ByVal Grid As DataGridView, _
    ByVal ConnectionString As String, _
    ByVal ProvisionalBookingTable As DataSetMediaGap.ProvisionalBookingDataTable, _
    ByVal MediaGapManager As MediaGap)

        Using EditForm As New FormEditProvisionalBooking(Grid, ProvisionalBookingTable, MediaGapManager)
            EditForm.ShowDialog()
        End Using

    End Sub

#End Region

#Region "Public Methods"

    Public Sub New _
    (ByVal ProvisionalBookingGrid As DataGridView, _
    ByVal TableOfProvisionalBookings As DataSetMediaGap.ProvisionalBookingDataTable, _
    ByVal MediaGapManager As MediaGap)

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        Grid = ProvisionalBookingGrid
        InitializeDates()
        ProvisionalBookingTable = TableOfProvisionalBookings
        Gap = MediaGapManager
        CheckEditLastWeek.Checked = False

    End Sub

#End Region

#Region "Event Handlers"

    Private Sub ButtonSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonSave.Click

        ' Check if any changes were requested.
        If CheckEditFirstWeek.Checked = False _
        And CheckEditLastWeek.Checked = False _
        And CheckEditWeeks.Checked = False Then
            ShowMessage("You clicked the 'Save' button, but you haven't ticked any boxes, meaning you don't want to " _
            & "change anything, and" & vbCrLf & "if you don't want to change anything, then it's pointless trying to " _
            & "save, don't you agree?", "But Nothing Has Changed...", MessageBoxIcon.Exclamation)
            Exit Sub
        End If

        ' Stop user is trying to extend booking into new space.
        For Each GridRow As DataGridViewRow In Grid.SelectedRows
            ' User has not yet been prompted regarding possible book time resetting. Go ahead and check if it might happen.
            Dim ProvisionalBookingID As Guid = CType(GridRow.DataBoundItem, DataRowView).Item("ProvisionalBookingID")
            Dim ProvisionalBooking As DataRow = ProvisionalBookingTable.Rows.Find(ProvisionalBookingID)
            Dim LastWeekBooked As Date = DateAdd _
            (DateInterval.WeekOfYear, ProvisionalBooking("Duration") - 1, ProvisionalBooking("FirstWeek"))
            Dim MoreSpaceNeeded As Boolean = False

            ' Check if the booking needs weeks not previously used.
            If ProposedFirstWeek(ProvisionalBooking) < ProvisionalBooking("FirstWeek") Then
                ' User is extending the booking into new space.
                MoreSpaceNeeded = True
            End If
            If ProposedLastWeek(ProvisionalBooking) > LastWeekBooked Then
                ' User is extending the booking into new space.
                MoreSpaceNeeded = True
            End If
            If MoreSpaceNeeded Then
                ShowMessage _
                ("You are attempting to extend at least one of the selected provisional bookings into weeks that " & vbCrLf _
                & "were originally not booked. To book additional weeks, please create a new provisional booking " & vbCrLf _
                & "instead of modifying an existing one.", "Extension Into New Weeks Not Permitted")
                Exit Sub
            End If
        Next

        ' Save
        SaveAndClose()

    End Sub

    Private Sub CheckEditFirstWeek_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckEditFirstWeek.CheckedChanged
        Dim CheckedControl As CheckEdit = CType(sender, CheckEdit)
        ' Enable or disable the related hyperlink when the check edit is checked or unchecked.
        LiquidAgent.EnableHyperlink(HyperlinkFirstWeek, CheckedControl.Checked)
    End Sub

    Private Sub CheckEditLastWeek_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckEditLastWeek.CheckedChanged
        Dim CheckedControl As CheckEdit = CType(sender, CheckEdit)
        ' Enable or disable the related hyperlink when the check edit is checked or unchecked.
        LiquidAgent.EnableHyperlink(HyperlinkLastWeek, CheckedControl.Checked)
        If CheckedControl.Checked Then
            ' Uncheck the CheckEditWeeks box when the check edit is checked.
            CheckEditWeeks.Checked = Not CheckedControl.Checked
        End If
    End Sub

    Private Sub CheckEditWeeks_CheckedChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles CheckEditWeeks.CheckedChanged
        Dim CheckedControl As CheckEdit = CType(sender, CheckEdit)
        ' Enable or disable the related hyperlink when the check edit is checked or unchecked.
        TextEditWeeks.Enabled = CheckedControl.Checked
        If CheckedControl.Checked Then
            ' Uncheck the CheckEditLastWeek box when the check edit is checked.
            CheckEditLastWeek.Checked = Not CheckedControl.Checked
        End If
    End Sub

    Private Sub TextEditWeeks_EditValueChanged(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles TextEditWeeks.EditValueChanged
        ' Update the text of HyperlinkLastWeek if necessary.
        Dim Weeks As Integer = CInt(TextEditWeeks.EditValue)
        HyperlinkLastWeek.Text = DateAdd(DateInterval.WeekOfYear, CDbl(Weeks - 1), FirstWeek).ToLongDateString
    End Sub

    Private Sub HyperlinkFirstWeek_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles HyperlinkFirstWeek.Click
        Dim NewDate As Nullable(Of Date) = DateSelector.GetDate(True, FirstWeek)
        If NewDate.HasValue Then
            FirstWeek = NewDate.Value
        End If
    End Sub

    Private Sub HyperlinkLastWeek_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles HyperlinkLastWeek.Click
        Dim NewDate As Nullable(Of Date) = DateSelector.GetDate(True, LastWeek)
        If NewDate.HasValue Then
            LastWeek = NewDate.Value
        End If
    End Sub

    Private Sub TextEditWeeks_KeyPress(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles TextEditWeeks.KeyPress
        ' Prevent user from entering a number with more than three digits.
        Dim KeyPressControl As TextEdit = CType(sender, TextEdit)
        If KeyPressControl.EditValue.ToString.Length = 3 AndAlso Char.IsDigit(e.KeyChar) Then
            e.Handled = True
        End If
    End Sub

#End Region

#Region "Private Methods"

    Private Function ProposedFirstWeek(ByVal ProvisionalBooking As DataRow) As Date

        ' Get the proposed new date.
        Dim NewFirstWeek As Date = ProvisionalBooking("FirstWeek")
        If CheckEditFirstWeek.Checked Then
            NewFirstWeek = FirstWeek
        End If
        Return NewFirstWeek

    End Function

    Private Function ProposedLastWeek(ByVal ProvisionalBooking As DataRow) As Date

        ' Get the proposed new date.
        Dim NewLastWeek As Date = DateAdd(DateInterval.WeekOfYear, ProvisionalBooking("Duration") - 1, ProposedFirstWeek(ProvisionalBooking))
        If CheckEditLastWeek.Checked Or CheckEditWeeks.Checked Then
            NewLastWeek = LastWeek
        End If
        Return NewLastWeek

    End Function

    Private Sub InitializeDates()
        ' Set the initial dates for the hyperlinks.

        ' Create variables to remember the earliest FirstWeek and latest LastWeek of all selected provisional bookings.
        Dim EarliestFirstWeek As Date = New Date(2300, 1, 1)
        Dim LatestLastWeek As Date = New Date(2000, 1, 1)

        ' Cycle through the provisional bookings and find the earliest FirstWeek and latest LastWeek.
        For Each GridRow As DataGridViewRow In Grid.SelectedRows
            Dim ProvisionalBooking As DataRow = CType(GridRow.DataBoundItem, DataRowView).Row
            ' Check if this provisional booking has an earlier FirstWeek.
            If EarliestFirstWeek > ProvisionalBooking("FirstWeek") Then
                EarliestFirstWeek = ProvisionalBooking("FirstWeek")
            End If
            ' Check if this provisional booking has a later LastWeek.
            Dim ProvisionalBookingLastWeek As Date = DateAdd _
            (DateInterval.WeekOfYear, ProvisionalBooking("Duration") - 1, ProvisionalBooking("FirstWeek"))
            If LatestLastWeek < ProvisionalBookingLastWeek Then
                LatestLastWeek = ProvisionalBookingLastWeek
            End If
        Next

        ' Set the values of the dates.
        FirstWeek = EarliestFirstWeek
        LastWeek = LatestLastWeek

    End Sub

#End Region

#Region "Protected Methods"

    Protected Overrides Sub Save()

        ' Get the new date values.
        Dim NewFirstWeek As Nullable(Of Date) = Nothing
        Dim NewLastWeek As Nullable(Of Date) = Nothing
        If CheckEditFirstWeek.Checked Then
            NewFirstWeek = FirstWeek
        End If
        If CheckEditLastWeek.Checked Or CheckEditWeeks.Checked Then
            NewLastWeek = LastWeek
        End If

        ' Save the changes to the database.
        MediaGap.ChangeProvisionalBookings _
        (My.Settings.DBConnection, Grid, NewFirstWeek, NewLastWeek, ProvisionalBookingTable, Gap)

    End Sub

#End Region

End Class
