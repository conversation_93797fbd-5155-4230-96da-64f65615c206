﻿Imports System.ComponentModel

Public Class ContentSwitcher

    ' This class houses the NavButton and Subform controls.
    ' To add buttons to the Content Switcher, use the AddButton method below.

    Private _ButtonHeight As Integer = 50

#Region "Properties"

    <Browsable(True)> _
    Public Property ButtonHeight() As Integer
        Get
            Return _ButtonHeight
        End Get
        Set(ByVal value As Integer)
            ' Update the field.
            _ButtonHeight = value
            ' Update the heights of all the existing buttons.
            For Each Item As Control In PanelButtons.Controls
                If TypeOf Item Is NavButton Then
                    Item.Size = New Size(Item.Width, value)
                End If
            Next
            ' Reset the locations of all buttons.
            Dim NewYLocation As Integer = 0
            For Each Item As Control In PanelButtons.Controls
                If TypeOf Item Is NavButton Then
                    Item.Location = New Point(0, NewYLocation)
                    NewYLocation += value
                End If
            Next
        End Set
    End Property

#End Region

#Region "Methods"

    Public Sub AddButton(ByVal text As String, ByVal subform As Subform)

        ' Create a new button and add it to the ContentSwitcher control.
        Dim <PERSON>on As New NavButton
        With NewButton
            .Location = New Point(0, PanelButtons.Controls.Count * _ButtonHeight)
            .Text = text
            If IsNothing(subform) = False Then
                .Subform = subform
                .Subform.Dock = DockStyle.Fill
            End If
            .Margin = New Padding(0)
            .Size = New Size(PanelButtons.Width, _ButtonHeight)
            .Anchor = AnchorStyles.Left Or AnchorStyles.Right Or AnchorStyles.Top
            .SelectedBackColor = Color.WhiteSmoke
        End With

        PanelButtons.Controls.Add(NewButton)

        ' If this is the first button added, display its subform.
        If PanelButtons.Controls.Count = 1 Then
            NewButton.DisplayContent()
            NewButton.ApplyStyle(NavButton.EventStyle.Clicked)
            NewButton.Selected = True
        End If

    End Sub

#End Region

End Class
