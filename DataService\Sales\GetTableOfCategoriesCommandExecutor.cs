﻿using DataAccess;

namespace DataService.Sales
{
    class GetTableOfCategoriesCommandExecutor : CommandExecutor<GetTableOfCategoriesCommand>
    {
        public override void Execute(GetTableOfCategoriesCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.GetCategoryTable))
            {
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    command.Table = storedprocedure.OutputTable;
                }
            }
        }
    }
}
