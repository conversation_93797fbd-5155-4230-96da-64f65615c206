<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class FormAccountManagerUserPermissions
    Inherits LiquidShell.EditorForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(FormAccountManagerUserPermissions))
        Dim SuperToolTip1 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip
        Dim ToolTipTitleItem1 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem
        Dim ToolTipItem1 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem
        Dim SuperToolTip2 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip
        Dim ToolTipTitleItem2 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem
        Dim ToolTipItem2 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle
        Dim DataGridViewCellStyle4 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle
        Me.ButtonSave = New DevExpress.XtraEditors.SimpleButton
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton
        Me.GroupControlPermissions = New DevExpress.XtraEditors.GroupControl
        Me.LabelControl14 = New DevExpress.XtraEditors.LabelControl
        Me.TextEditSearch = New DevExpress.XtraEditors.TextEdit
        Me.PictureClearSearch = New DevExpress.XtraEditors.PictureEdit
        Me.PictureAdvancedSearch = New DevExpress.XtraEditors.PictureEdit
        Me.Grid = New System.Windows.Forms.DataGridView
        Me.UserColumn = New System.Windows.Forms.DataGridViewTextBoxColumn
        Me.ViewContractsColumn = New System.Windows.Forms.DataGridViewCheckBoxColumn
        Me.ModifyContractsColumn = New System.Windows.Forms.DataGridViewCheckBoxColumn
        Me.ModifyBookingsColumn = New System.Windows.Forms.DataGridViewCheckBoxColumn
        Me.ModifyClientsColumn = New System.Windows.Forms.DataGridViewCheckBoxColumn
        Me.LabelTitle = New DevExpress.XtraEditors.LabelControl
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelButtonBar.SuspendLayout()
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControlPermissions, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlPermissions.SuspendLayout()
        CType(Me.TextEditSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Grid, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'PanelButtonBar
        '
        Me.PanelButtonBar.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelButtonBar.Appearance.BackColor2 = System.Drawing.Color.LightSteelBlue
        Me.PanelButtonBar.Appearance.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical
        Me.PanelButtonBar.Appearance.Options.UseBackColor = True
        Me.PanelButtonBar.Controls.Add(Me.ButtonSave)
        Me.PanelButtonBar.Controls.Add(Me.ButtonCancel)
        Me.PanelButtonBar.Location = New System.Drawing.Point(0, 520)
        Me.PanelButtonBar.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Office2003
        Me.PanelButtonBar.LookAndFeel.UseDefaultLookAndFeel = False
        Me.PanelButtonBar.Size = New System.Drawing.Size(874, 52)
        '
        'ButtonSave
        '
        Me.ButtonSave.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonSave.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonSave.Appearance.Options.UseFont = True
        Me.ButtonSave.ImageIndex = 1
        Me.ButtonSave.ImageList = Me.ImageList24x24
        Me.ButtonSave.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonSave.Location = New System.Drawing.Point(656, 12)
        Me.ButtonSave.LookAndFeel.SkinName = "Black"
        Me.ButtonSave.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonSave.Name = "ButtonSave"
        Me.ButtonSave.Size = New System.Drawing.Size(100, 28)
        Me.ButtonSave.TabIndex = 2
        Me.ButtonSave.Text = "Save"
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(762, 12)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(100, 28)
        Me.ButtonCancel.TabIndex = 3
        Me.ButtonCancel.Text = "Cancel"
        '
        'GroupControlPermissions
        '
        Me.GroupControlPermissions.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlPermissions.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlPermissions.Appearance.Options.UseFont = True
        Me.GroupControlPermissions.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlPermissions.AppearanceCaption.Options.UseFont = True
        Me.GroupControlPermissions.Controls.Add(Me.LabelControl14)
        Me.GroupControlPermissions.Controls.Add(Me.TextEditSearch)
        Me.GroupControlPermissions.Controls.Add(Me.PictureClearSearch)
        Me.GroupControlPermissions.Controls.Add(Me.PictureAdvancedSearch)
        Me.GroupControlPermissions.Controls.Add(Me.Grid)
        Me.GroupControlPermissions.Location = New System.Drawing.Point(12, 57)
        Me.GroupControlPermissions.LookAndFeel.SkinName = "Black"
        Me.GroupControlPermissions.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlPermissions.Margin = New System.Windows.Forms.Padding(3, 3, 3, 0)
        Me.GroupControlPermissions.Name = "GroupControlPermissions"
        Me.GroupControlPermissions.Size = New System.Drawing.Size(850, 463)
        Me.GroupControlPermissions.TabIndex = 5
        Me.GroupControlPermissions.Text = "User Permission List"
        '
        'LabelControl14
        '
        Me.LabelControl14.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl14.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl14.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl14.Location = New System.Drawing.Point(692, 439)
        Me.LabelControl14.Name = "LabelControl14"
        Me.LabelControl14.Size = New System.Drawing.Size(45, 13)
        Me.LabelControl14.TabIndex = 4
        Me.LabelControl14.Text = "Search:"
        '
        'TextEditSearch
        '
        Me.TextEditSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearch.EditValue = ""
        Me.TextEditSearch.Location = New System.Drawing.Point(743, 436)
        Me.TextEditSearch.Margin = New System.Windows.Forms.Padding(3, 4, 3, 5)
        Me.TextEditSearch.Name = "TextEditSearch"
        Me.TextEditSearch.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearch.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearch.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearch.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearch.Size = New System.Drawing.Size(80, 20)
        Me.TextEditSearch.TabIndex = 5
        '
        'PictureClearSearch
        '
        Me.PictureClearSearch.AllowDrop = True
        Me.PictureClearSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearch.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearch.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearch.Location = New System.Drawing.Point(829, 3)
        Me.PictureClearSearch.Name = "PictureClearSearch"
        Me.PictureClearSearch.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearch.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearch.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearch.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem1.Text = "Clear Search"
        ToolTipItem1.LeftIndent = 6
        ToolTipItem1.Text = "Click here to clear all search boxes."
        SuperToolTip1.Items.Add(ToolTipTitleItem1)
        SuperToolTip1.Items.Add(ToolTipItem1)
        Me.PictureClearSearch.SuperTip = SuperToolTip1
        Me.PictureClearSearch.TabIndex = 0
        Me.PictureClearSearch.TabStop = True
        '
        'PictureAdvancedSearch
        '
        Me.PictureAdvancedSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearch.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearch.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearch.Location = New System.Drawing.Point(829, 438)
        Me.PictureAdvancedSearch.Margin = New System.Windows.Forms.Padding(3, 6, 3, 7)
        Me.PictureAdvancedSearch.Name = "PictureAdvancedSearch"
        Me.PictureAdvancedSearch.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearch.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearch.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearch.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem2.Text = "Advanced Search"
        ToolTipItem2.LeftIndent = 6
        ToolTipItem2.Text = "Click here to search individual column values."
        SuperToolTip2.Items.Add(ToolTipTitleItem2)
        SuperToolTip2.Items.Add(ToolTipItem2)
        Me.PictureAdvancedSearch.SuperTip = SuperToolTip2
        Me.PictureAdvancedSearch.TabIndex = 6
        Me.PictureAdvancedSearch.TabStop = True
        '
        'Grid
        '
        Me.Grid.AllowUserToAddRows = False
        Me.Grid.AllowUserToDeleteRows = False
        Me.Grid.AllowUserToOrderColumns = True
        Me.Grid.AllowUserToResizeRows = False
        DataGridViewCellStyle1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Grid.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle1
        Me.Grid.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
                    Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Grid.BackgroundColor = System.Drawing.Color.White
        Me.Grid.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.Grid.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.Grid.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle2.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle2.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.Grid.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle2
        Me.Grid.ColumnHeadersHeight = 22
        Me.Grid.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.Grid.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.UserColumn, Me.ViewContractsColumn, Me.ModifyContractsColumn, Me.ModifyBookingsColumn, Me.ModifyClientsColumn})
        DataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle3.BackColor = System.Drawing.SystemColors.Window
        DataGridViewCellStyle3.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle3.ForeColor = System.Drawing.Color.FromArgb(CType(CType(32, Byte), Integer), CType(CType(31, Byte), Integer), CType(CType(53, Byte), Integer))
        DataGridViewCellStyle3.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle3.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.[False]
        Me.Grid.DefaultCellStyle = DataGridViewCellStyle3
        Me.Grid.EnableHeadersVisualStyles = False
        Me.Grid.GridColor = System.Drawing.Color.White
        Me.Grid.Location = New System.Drawing.Point(2, 22)
        Me.Grid.Name = "Grid"
        Me.Grid.RowHeadersVisible = False
        DataGridViewCellStyle4.ForeColor = System.Drawing.Color.DimGray
        Me.Grid.RowsDefaultCellStyle = DataGridViewCellStyle4
        Me.Grid.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.Grid.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.Grid.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.Grid.RowTemplate.Height = 19
        Me.Grid.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.Grid.ShowCellToolTips = False
        Me.Grid.Size = New System.Drawing.Size(846, 407)
        Me.Grid.StandardTab = True
        Me.Grid.TabIndex = 1
        '
        'UserColumn
        '
        Me.UserColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.UserColumn.DataPropertyName = "name"
        Me.UserColumn.HeaderText = "User"
        Me.UserColumn.Name = "UserColumn"
        Me.UserColumn.ReadOnly = True
        '
        'ViewContractsColumn
        '
        Me.ViewContractsColumn.DataPropertyName = "ViewMyContracts"
        Me.ViewContractsColumn.HeaderText = "View Contracts"
        Me.ViewContractsColumn.Name = "ViewContractsColumn"
        Me.ViewContractsColumn.Resizable = System.Windows.Forms.DataGridViewTriState.[True]
        Me.ViewContractsColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Automatic
        Me.ViewContractsColumn.Width = 180
        '
        'ModifyContractsColumn
        '
        Me.ModifyContractsColumn.DataPropertyName = "EditMyContracts"
        Me.ModifyContractsColumn.HeaderText = "Create & Modify Contracts"
        Me.ModifyContractsColumn.Name = "ModifyContractsColumn"
        Me.ModifyContractsColumn.Width = 180
        '
        'ModifyBookingsColumn
        '
        Me.ModifyBookingsColumn.DataPropertyName = "EditMyProvisionalBookings"
        Me.ModifyBookingsColumn.HeaderText = "Create & Modify PBs"
        Me.ModifyBookingsColumn.Name = "ModifyBookingsColumn"
        Me.ModifyBookingsColumn.Width = 180
        '
        'ModifyClientsColumn
        '
        Me.ModifyClientsColumn.DataPropertyName = "EditMyClients"
        Me.ModifyClientsColumn.HeaderText = "Create & Modify Clients"
        Me.ModifyClientsColumn.Name = "ModifyClientsColumn"
        Me.ModifyClientsColumn.Width = 180
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!)
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Location = New System.Drawing.Point(12, 12)
        Me.LabelTitle.Margin = New System.Windows.Forms.Padding(3, 3, 3, 12)
        Me.LabelTitle.Name = "LabelTitle"
        Me.LabelTitle.Size = New System.Drawing.Size(314, 30)
        Me.LabelTitle.TabIndex = 26
        Me.LabelTitle.Tag = "12, 12"
        Me.LabelTitle.Text = "(account manager name)"
        '
        'FormAccountManagerUserPermissions
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.ClientSize = New System.Drawing.Size(874, 572)
        Me.Controls.Add(Me.LabelTitle)
        Me.Controls.Add(Me.GroupControlPermissions)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.MinimumSize = New System.Drawing.Size(890, 610)
        Me.Name = "FormAccountManagerUserPermissions"
        Me.Text = "Account Manager User Permissions"
        Me.Controls.SetChildIndex(Me.PanelButtonBar, 0)
        Me.Controls.SetChildIndex(Me.GroupControlPermissions, 0)
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelButtonBar.ResumeLayout(False)
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControlPermissions, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlPermissions.ResumeLayout(False)
        Me.GroupControlPermissions.PerformLayout()
        CType(Me.TextEditSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Grid, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents ButtonSave As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GroupControlPermissions As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelControl14 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditSearch As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearch As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearch As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents Grid As System.Windows.Forms.DataGridView
    Protected WithEvents LabelTitle As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents UserColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents ViewContractsColumn As System.Windows.Forms.DataGridViewCheckBoxColumn
    Friend WithEvents ModifyContractsColumn As System.Windows.Forms.DataGridViewCheckBoxColumn
    Friend WithEvents ModifyBookingsColumn As System.Windows.Forms.DataGridViewCheckBoxColumn
    Friend WithEvents ModifyClientsColumn As System.Windows.Forms.DataGridViewCheckBoxColumn

End Class
