Public Class SubformContractMediaCost

    Private _DataBindingSource As BindingSource
    Private ParentContract As OldContract
    Private _GridOfCosts As DataGridView
#Region "Properties"
    Private Property DataBindingSource() As BindingSource
        Get
            Return _DataBindingSource
        End Get
        Set(ByVal value As BindingSource)
            _DataBindingSource = value
        End Set
    End Property
    Private ReadOnly Property Row() As DataRow
        Get
            Return CType(DataBindingSource.Current, DataRowView).Row
        End Get
    End Property
#End Region
#Region "Event Handlers"
    Private Sub ButtonCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonCancel.Click
        DataBindingSource.CancelEdit()
        RevertToParentSubform()
    End Sub
    Private Sub ButtonOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonOK.Click
        Save(True)
    End Sub
#End Region
#Region "Public Methods"
    Public Sub New(ByVal Grid As DataGridView, ByVal NewItem As Boolean)


        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.

        ' Get the binding source of the supplied grid.
        DataBindingSource = CType(Grid.DataSource, BindingSource)
        'we need to check if it is exisint or new item, if new, lets create a new item.


        ' Add a new row to the binding source list if required.
        If NewItem Then
            DataBindingSource.AddNew()
        End If
        _GridOfCosts = Grid
        ' Do data binding.
        TextEditSellPrice.DataBindings.Add("EditValue", DataBindingSource, "MediaCostInputed", False, DataSourceUpdateMode.OnPropertyChanged)

        If TextEditSellPrice.Text = "R0.00" Then
            'it should be a new item, lets create it. 
            'we have to check if it exists
            Row.SetAdded()

        End If

    End Sub
#End Region
#Region "Protected Methods"
    Protected Overrides Function Save() As Boolean
        DataBindingSource.EndEdit()
        Return True
    End Function
#End Region

#Region "Private Methods"
#End Region

End Class