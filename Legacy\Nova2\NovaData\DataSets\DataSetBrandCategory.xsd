﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DataSetBrandCategory" targetNamespace="http://tempuri.org/DataSetBrandCategory.xsd" xmlns:mstns="http://tempuri.org/DataSetBrandCategory.xsd" xmlns="http://tempuri.org/DataSetBrandCategory.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="DBConnection" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Public" Name="DBConnection (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.NovaData.My.MySettings.GlobalReference.Default.DBConnection" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="BrandCategoryTableAdapter" GeneratorDataComponentClassName="BrandCategoryTableAdapter" Name="BrandCategory" UserDataComponentName="BrandCategoryTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Client.BrandCategory" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Client].[BrandCategory] WHERE (([BrandCategoryId] = @Original_BrandCategoryId) AND ([BrandCategoryName] = @Original_BrandCategoryName))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_BrandCategoryId" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="BrandCategoryId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_BrandCategoryName" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="BrandCategoryName" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Client].[BrandCategory] ([BrandCategoryName]) VALUES (@BrandCategoryName);
SELECT BrandCategoryId, BrandCategoryName, ISNULL(dbo.udfBrandListByBrandCategory(BrandCategoryId), '') AS Brands FROM Client.BrandCategory WHERE (BrandCategoryId = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@BrandCategoryName" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="BrandCategoryName" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT BrandCategoryId, BrandCategoryName, ISNULL(dbo.udfBrandListByBrandCategory(BrandCategoryId), '') AS Brands
FROM     Client.BrandCategory</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Client].[BrandCategory] SET [BrandCategoryName] = @BrandCategoryName WHERE (([BrandCategoryId] = @Original_BrandCategoryId) AND ([BrandCategoryName] = @Original_BrandCategoryName));
SELECT BrandCategoryId, BrandCategoryName, ISNULL(dbo.udfBrandListByBrandCategory(BrandCategoryId), '') AS Brands FROM Client.BrandCategory WHERE (BrandCategoryId = @BrandCategoryId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@BrandCategoryName" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="BrandCategoryName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_BrandCategoryId" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="BrandCategoryId" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="AnsiString" Direction="Input" ParameterName="@Original_BrandCategoryName" Precision="0" ProviderType="VarChar" Scale="0" Size="0" SourceColumn="BrandCategoryName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="BrandCategoryId" ColumnName="BrandCategoryId" DataSourceName="NovaDB.Client.BrandCategory" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@BrandCategoryId" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="BrandCategoryId" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="BrandCategoryId" DataSetColumn="BrandCategoryId" />
              <Mapping SourceColumn="BrandCategoryName" DataSetColumn="BrandCategoryName" />
              <Mapping SourceColumn="Brands" DataSetColumn="Brands" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="BrandCategoryMemberTableAdapter" GeneratorDataComponentClassName="BrandCategoryMemberTableAdapter" Name="BrandCategoryMember" UserDataComponentName="BrandCategoryMemberTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Client.BrandCategoryMember" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Client].[BrandCategoryMember] WHERE (([BrandID] = @Original_BrandID) AND ([BrandCategoryID] = @Original_BrandCategoryID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BrandID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_BrandCategoryID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="BrandCategoryID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Client].[BrandCategoryMember] ([BrandID], [BrandCategoryID]) VALUES (@BrandID, @BrandCategoryID);
SELECT BrandID, BrandCategoryID FROM Client.BrandCategoryMember WHERE (BrandCategoryID = @BrandCategoryID) AND (BrandID = @BrandID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BrandID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@BrandCategoryID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="BrandCategoryID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT BrandID, BrandCategoryID
FROM     Client.BrandCategoryMember
WHERE  (BrandCategoryID = @brandCategoryId)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="brandCategoryId" ColumnName="BrandCategoryID" DataSourceName="NovaDB.Client.BrandCategoryMember" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@brandCategoryId" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="BrandCategoryID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Client].[BrandCategoryMember] SET [BrandID] = @BrandID, [BrandCategoryID] = @BrandCategoryID WHERE (([BrandID] = @Original_BrandID) AND ([BrandCategoryID] = @Original_BrandCategoryID));
SELECT BrandID, BrandCategoryID FROM Client.BrandCategoryMember WHERE (BrandCategoryID = @BrandCategoryID) AND (BrandID = @BrandID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BrandID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@BrandCategoryID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="BrandCategoryID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BrandID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_BrandCategoryID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="BrandCategoryID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="BrandID" DataSetColumn="BrandID" />
              <Mapping SourceColumn="BrandCategoryID" DataSetColumn="BrandCategoryID" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="BrandTableAdapter" GeneratorDataComponentClassName="BrandTableAdapter" Name="Brand" UserDataComponentName="BrandTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Client.Brand" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT BrandID, BrandName
FROM     Client.Brand</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Client].[Brand] SET [BrandID] = @BrandID, [BrandName] = @BrandName WHERE (([BrandID] = @Original_BrandID) AND ([BrandName] = @Original_BrandName));
SELECT BrandID, BrandName FROM Client.Brand WHERE (BrandID = @BrandID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BrandID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@BrandName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="BrandName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BrandID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_BrandName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="BrandName" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="BrandID" DataSetColumn="BrandID" />
              <Mapping SourceColumn="BrandName" DataSetColumn="BrandName" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DataSetBrandCategory" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="False" msprop:Generator_DataSetName="DataSetBrandCategory" msprop:Generator_UserDSName="DataSetBrandCategory">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="BrandCategory" msprop:Generator_TableClassName="BrandCategoryDataTable" msprop:Generator_TableVarName="tableBrandCategory" msprop:Generator_TablePropName="BrandCategory" msprop:Generator_RowDeletingName="BrandCategoryRowDeleting" msprop:Generator_RowChangingName="BrandCategoryRowChanging" msprop:Generator_RowEvHandlerName="BrandCategoryRowChangeEventHandler" msprop:Generator_RowDeletedName="BrandCategoryRowDeleted" msprop:Generator_UserTableName="BrandCategory" msprop:Generator_RowChangedName="BrandCategoryRowChanged" msprop:Generator_RowEvArgName="BrandCategoryRowChangeEvent" msprop:Generator_RowClassName="BrandCategoryRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="BrandCategoryId" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnBrandCategoryId" msprop:Generator_ColumnPropNameInRow="BrandCategoryId" msprop:Generator_ColumnPropNameInTable="BrandCategoryIdColumn" msprop:Generator_UserColumnName="BrandCategoryId" type="xs:int" />
              <xs:element name="BrandCategoryName" msprop:Generator_ColumnVarNameInTable="columnBrandCategoryName" msprop:Generator_ColumnPropNameInRow="BrandCategoryName" msprop:Generator_ColumnPropNameInTable="BrandCategoryNameColumn" msprop:Generator_UserColumnName="BrandCategoryName" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="255" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Brands" msprop:Generator_ColumnVarNameInTable="columnBrands" msprop:Generator_ColumnPropNameInRow="Brands" msprop:Generator_ColumnPropNameInTable="BrandsColumn" msprop:Generator_UserColumnName="Brands" default="" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2147483647" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="BrandCategoryMember" msprop:Generator_TableClassName="BrandCategoryMemberDataTable" msprop:Generator_TableVarName="tableBrandCategoryMember" msprop:Generator_TablePropName="BrandCategoryMember" msprop:Generator_RowDeletingName="BrandCategoryMemberRowDeleting" msprop:Generator_RowChangingName="BrandCategoryMemberRowChanging" msprop:Generator_RowEvHandlerName="BrandCategoryMemberRowChangeEventHandler" msprop:Generator_RowDeletedName="BrandCategoryMemberRowDeleted" msprop:Generator_UserTableName="BrandCategoryMember" msprop:Generator_RowChangedName="BrandCategoryMemberRowChanged" msprop:Generator_RowEvArgName="BrandCategoryMemberRowChangeEvent" msprop:Generator_RowClassName="BrandCategoryMemberRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="BrandID" msdata:DataType="System.Guid, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnBrandID" msprop:Generator_ColumnPropNameInRow="BrandID" msprop:Generator_ColumnPropNameInTable="BrandIDColumn" msprop:Generator_UserColumnName="BrandID" type="xs:string" />
              <xs:element name="BrandCategoryID" msprop:Generator_ColumnVarNameInTable="columnBrandCategoryID" msprop:Generator_ColumnPropNameInRow="BrandCategoryID" msprop:Generator_ColumnPropNameInTable="BrandCategoryIDColumn" msprop:Generator_UserColumnName="BrandCategoryID" type="xs:int" />
              <xs:element name="BrandName" msdata:ReadOnly="true" msdata:Expression="Parent(FK_BrandCategoryMember_Brand).BrandName" msprop:Generator_ColumnVarNameInTable="columnBrandName" msprop:Generator_ColumnPropNameInRow="BrandName" msprop:Generator_ColumnPropNameInTable="BrandNameColumn" msprop:Generator_UserColumnName="BrandName" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Brand" msprop:Generator_TableClassName="BrandDataTable" msprop:Generator_TableVarName="tableBrand" msprop:Generator_TablePropName="Brand" msprop:Generator_RowDeletingName="BrandRowDeleting" msprop:Generator_RowChangingName="BrandRowChanging" msprop:Generator_RowEvHandlerName="BrandRowChangeEventHandler" msprop:Generator_RowDeletedName="BrandRowDeleted" msprop:Generator_UserTableName="Brand" msprop:Generator_RowChangedName="BrandRowChanged" msprop:Generator_RowEvArgName="BrandRowChangeEvent" msprop:Generator_RowClassName="BrandRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="BrandID" msdata:DataType="System.Guid, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnBrandID" msprop:Generator_ColumnPropNameInRow="BrandID" msprop:Generator_ColumnPropNameInTable="BrandIDColumn" msprop:Generator_UserColumnName="BrandID" type="xs:string" />
              <xs:element name="BrandName" msprop:Generator_ColumnVarNameInTable="columnBrandName" msprop:Generator_ColumnPropNameInRow="BrandName" msprop:Generator_ColumnPropNameInTable="BrandNameColumn" msprop:Generator_UserColumnName="BrandName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:BrandCategory" />
      <xs:field xpath="mstns:BrandCategoryId" />
    </xs:unique>
    <xs:unique name="BrandCategoryMember_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:BrandCategoryMember" />
      <xs:field xpath="mstns:BrandID" />
      <xs:field xpath="mstns:BrandCategoryID" />
    </xs:unique>
    <xs:unique name="Brand_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Brand" />
      <xs:field xpath="mstns:BrandID" />
    </xs:unique>
  </xs:element>
  <xs:annotation>
    <xs:appinfo>
      <msdata:Relationship name="FK_BrandCategoryMember_BrandCategory" msdata:parent="BrandCategory" msdata:child="BrandCategoryMember" msdata:parentkey="BrandCategoryId" msdata:childkey="BrandCategoryID" msprop:Generator_UserChildTable="BrandCategoryMember" msprop:Generator_ChildPropName="GetBrandCategoryMemberRows" msprop:Generator_UserRelationName="FK_BrandCategoryMember_BrandCategory" msprop:Generator_ParentPropName="BrandCategoryRow" msprop:Generator_RelationVarName="relationFK_BrandCategoryMember_BrandCategory" msprop:Generator_UserParentTable="BrandCategory" />
      <msdata:Relationship name="FK_BrandCategoryMember_Brand" msdata:parent="Brand" msdata:child="BrandCategoryMember" msdata:parentkey="BrandID" msdata:childkey="BrandID" msprop:Generator_UserChildTable="BrandCategoryMember" msprop:Generator_ChildPropName="GetBrandCategoryMemberRows" msprop:Generator_UserRelationName="FK_BrandCategoryMember_Brand" msprop:Generator_RelationVarName="relationFK_BrandCategoryMember_Brand" msprop:Generator_UserParentTable="Brand" msprop:Generator_ParentPropName="BrandRow" />
    </xs:appinfo>
  </xs:annotation>
</xs:schema>