<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformAccountManager
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformAccountManager))
        Dim SuperToolTip1 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem1 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem1 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip2 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem2 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem2 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle5 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle4 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim SuperToolTip3 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem3 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem3 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip4 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem4 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem4 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle6 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle7 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle8 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim SuperToolTip5 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem5 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem5 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip6 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem6 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem6 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle9 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle10 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle13 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle11 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle12 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim SuperToolTip7 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem7 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem7 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip8 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem8 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem8 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle14 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle15 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle18 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle16 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle17 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Me.ImageList16x16 = New System.Windows.Forms.ImageList(Me.components)
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonSave = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelAccountManagerDetailsTitle = New DevExpress.XtraEditors.LabelControl()
        Me.LabelClientManagement = New DevExpress.XtraEditors.LabelControl()
        Me.XtraTabControl1 = New DevExpress.XtraTab.XtraTabControl()
        Me.TabPageDetails = New DevExpress.XtraTab.XtraTabPage()
        Me.PanelDetails = New System.Windows.Forms.Panel()
        Me.TableLayoutPanelDetails = New System.Windows.Forms.TableLayoutPanel()
        Me.LabelAccountManagerDetailsInfo = New DevExpress.XtraEditors.LabelControl()
        Me.PanelAccountManagerDetails = New System.Windows.Forms.Panel()
        Me.LabelUser = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkUser = New DevExpress.XtraEditors.LabelControl()
        Me.LabelFirstName = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditFirstName = New DevExpress.XtraEditors.TextEdit()
        Me.LabelLastName = New DevExpress.XtraEditors.LabelControl()
        Me.CheckEditDormant = New DevExpress.XtraEditors.CheckEdit()
        Me.TextEditLastName = New DevExpress.XtraEditors.TextEdit()
        Me.LabelEmail = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditEmail = New DevExpress.XtraEditors.TextEdit()
        Me.LabelCode = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditCode = New DevExpress.XtraEditors.TextEdit()
        Me.TabPageClients = New DevExpress.XtraTab.XtraTabPage()
        Me.PanelClients = New System.Windows.Forms.Panel()
        Me.TableClientAccountManager = New System.Windows.Forms.TableLayoutPanel()
        Me.GroupControlClientAccountManager = New DevExpress.XtraEditors.GroupControl()
        Me.LabelControl14 = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearchClientAccountManager = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearchClientAccountManager = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearchClientAccountManager = New DevExpress.XtraEditors.PictureEdit()
        Me.ButtonRemoveClientAccountManager = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAddClientAccountManager = New DevExpress.XtraEditors.SimpleButton()
        Me.GridClientAccountManager = New System.Windows.Forms.DataGridView()
        Me.ClientNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.FromColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.ToColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.CareTakerColumn = New System.Windows.Forms.DataGridViewCheckBoxColumn()
        Me.LabelClientManagementInfo = New DevExpress.XtraEditors.LabelControl()
        Me.GroupControlContracts = New DevExpress.XtraEditors.GroupControl()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearchLinkedContracts = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearchLinkedContracts = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearchLinkedContracts = New DevExpress.XtraEditors.PictureEdit()
        Me.GridLinkedContracts = New System.Windows.Forms.DataGridView()
        Me.ContractNumberColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.TabPageBudgets = New DevExpress.XtraTab.XtraTabPage()
        Me.PanelBudgets = New System.Windows.Forms.Panel()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.GroupControlBudget = New DevExpress.XtraEditors.GroupControl()
        Me.ButtonEditBudget = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearchBudget = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearchBudget = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearchBudget = New DevExpress.XtraEditors.PictureEdit()
        Me.ButtonDeleteBudget = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAddBudget = New DevExpress.XtraEditors.SimpleButton()
        Me.GridBudget = New System.Windows.Forms.DataGridView()
        Me.FiscalNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.BudgetColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.LabelControl9 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl10 = New DevExpress.XtraEditors.LabelControl()
        Me.TabPageBrands = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.TableLayoutPanel2 = New System.Windows.Forms.TableLayoutPanel()
        Me.GroupControl1 = New DevExpress.XtraEditors.GroupControl()
        Me.LabelControl3 = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearchBrandAccountManager = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearchBrandAccountManager = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearchBrandAccountManager = New DevExpress.XtraEditors.PictureEdit()
        Me.ButtonRemoveBrandAccountManager = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAddBrandAccountManager = New DevExpress.XtraEditors.SimpleButton()
        Me.GridBrandAccountManager = New System.Windows.Forms.DataGridView()
        Me.LabelControl4 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl5 = New DevExpress.XtraEditors.LabelControl()
        Me.ButtonPermissions = New DevExpress.XtraEditors.SimpleButton()
        Me.BrandNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn2 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewTextBoxColumn3 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DataGridViewCheckBoxColumn1 = New System.Windows.Forms.DataGridViewCheckBoxColumn()
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabControl1.SuspendLayout()
        Me.TabPageDetails.SuspendLayout()
        Me.PanelDetails.SuspendLayout()
        Me.TableLayoutPanelDetails.SuspendLayout()
        Me.PanelAccountManagerDetails.SuspendLayout()
        CType(Me.TextEditFirstName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditDormant.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditLastName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditEmail.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditCode.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabPageClients.SuspendLayout()
        Me.PanelClients.SuspendLayout()
        Me.TableClientAccountManager.SuspendLayout()
        CType(Me.GroupControlClientAccountManager, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlClientAccountManager.SuspendLayout()
        CType(Me.TextEditSearchClientAccountManager.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearchClientAccountManager.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearchClientAccountManager.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridClientAccountManager, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControlContracts, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlContracts.SuspendLayout()
        CType(Me.TextEditSearchLinkedContracts.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearchLinkedContracts.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearchLinkedContracts.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridLinkedContracts, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabPageBudgets.SuspendLayout()
        Me.PanelBudgets.SuspendLayout()
        Me.TableLayoutPanel1.SuspendLayout()
        CType(Me.GroupControlBudget, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlBudget.SuspendLayout()
        CType(Me.TextEditSearchBudget.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearchBudget.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearchBudget.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridBudget, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabPageBrands.SuspendLayout()
        Me.Panel1.SuspendLayout()
        Me.TableLayoutPanel2.SuspendLayout()
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControl1.SuspendLayout()
        CType(Me.TextEditSearchBrandAccountManager.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearchBrandAccountManager.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearchBrandAccountManager.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridBrandAccountManager, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Margin = New System.Windows.Forms.Padding(5, 5, 5, 21)
        Me.LabelTitle.Size = New System.Drawing.Size(357, 37)
        Me.LabelTitle.TabIndex = 0
        Me.LabelTitle.Text = "(new account manager)"
        '
        'ImageList16x16
        '
        Me.ImageList16x16.ImageStream = CType(resources.GetObject("ImageList16x16.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList16x16.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList16x16.Images.SetKeyName(0, "add.png")
        Me.ImageList16x16.Images.SetKeyName(1, "pencil.png")
        Me.ImageList16x16.Images.SetKeyName(2, "delete.png")
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        Me.ImageList24x24.Images.SetKeyName(3, "unlock.png")
        '
        'ButtonSave
        '
        Me.ButtonSave.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonSave.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonSave.Appearance.Options.UseFont = True
        Me.ButtonSave.ImageIndex = 1
        Me.ButtonSave.ImageList = Me.ImageList24x24
        Me.ButtonSave.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonSave.Location = New System.Drawing.Point(815, 790)
        Me.ButtonSave.LookAndFeel.SkinName = "Black"
        Me.ButtonSave.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonSave.Margin = New System.Windows.Forms.Padding(4, 16, 4, 4)
        Me.ButtonSave.Name = "ButtonSave"
        Me.ButtonSave.Size = New System.Drawing.Size(129, 37)
        Me.ButtonSave.TabIndex = 3
        Me.ButtonSave.Text = "Save"
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(951, 790)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Margin = New System.Windows.Forms.Padding(4, 16, 4, 4)
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(129, 37)
        Me.ButtonCancel.TabIndex = 4
        Me.ButtonCancel.Text = "Cancel"
        '
        'LabelAccountManagerDetailsTitle
        '
        Me.LabelAccountManagerDetailsTitle.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelAccountManagerDetailsTitle.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelAccountManagerDetailsTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelAccountManagerDetailsTitle.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelAccountManagerDetailsTitle.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelAccountManagerDetailsTitle.LineVisible = True
        Me.LabelAccountManagerDetailsTitle.Location = New System.Drawing.Point(4, 4)
        Me.LabelAccountManagerDetailsTitle.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelAccountManagerDetailsTitle.Name = "LabelAccountManagerDetailsTitle"
        Me.LabelAccountManagerDetailsTitle.Size = New System.Drawing.Size(1012, 24)
        Me.LabelAccountManagerDetailsTitle.TabIndex = 0
        Me.LabelAccountManagerDetailsTitle.Text = "Account Manager Details"
        '
        'LabelClientManagement
        '
        Me.LabelClientManagement.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelClientManagement.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelClientManagement.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelClientManagement.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.TableClientAccountManager.SetColumnSpan(Me.LabelClientManagement, 2)
        Me.LabelClientManagement.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelClientManagement.LineVisible = True
        Me.LabelClientManagement.Location = New System.Drawing.Point(4, 4)
        Me.LabelClientManagement.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelClientManagement.Name = "LabelClientManagement"
        Me.LabelClientManagement.Size = New System.Drawing.Size(1012, 24)
        Me.LabelClientManagement.TabIndex = 0
        Me.LabelClientManagement.Text = "Account Manager Client Management"
        '
        'XtraTabControl1
        '
        Me.XtraTabControl1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.XtraTabControl1.AppearancePage.Header.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.XtraTabControl1.AppearancePage.Header.Options.UseFont = True
        Me.XtraTabControl1.Location = New System.Drawing.Point(15, 75)
        Me.XtraTabControl1.LookAndFeel.SkinName = "Black"
        Me.XtraTabControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.XtraTabControl1.Margin = New System.Windows.Forms.Padding(4)
        Me.XtraTabControl1.Name = "XtraTabControl1"
        Me.XtraTabControl1.SelectedTabPage = Me.TabPageDetails
        Me.XtraTabControl1.Size = New System.Drawing.Size(1065, 696)
        Me.XtraTabControl1.TabIndex = 1
        Me.XtraTabControl1.TabPages.AddRange(New DevExpress.XtraTab.XtraTabPage() {Me.TabPageDetails, Me.TabPageClients, Me.TabPageBudgets, Me.TabPageBrands})
        '
        'TabPageDetails
        '
        Me.TabPageDetails.Controls.Add(Me.PanelDetails)
        Me.TabPageDetails.Margin = New System.Windows.Forms.Padding(4)
        Me.TabPageDetails.Name = "TabPageDetails"
        Me.TabPageDetails.Size = New System.Drawing.Size(1059, 664)
        Me.TabPageDetails.Text = "Details"
        '
        'PanelDetails
        '
        Me.PanelDetails.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelDetails.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelDetails.Controls.Add(Me.TableLayoutPanelDetails)
        Me.PanelDetails.Location = New System.Drawing.Point(4, 4)
        Me.PanelDetails.Margin = New System.Windows.Forms.Padding(4)
        Me.PanelDetails.Name = "PanelDetails"
        Me.PanelDetails.Size = New System.Drawing.Size(1050, 654)
        Me.PanelDetails.TabIndex = 3
        '
        'TableLayoutPanelDetails
        '
        Me.TableLayoutPanelDetails.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanelDetails.BackColor = System.Drawing.Color.Transparent
        Me.TableLayoutPanelDetails.ColumnCount = 1
        Me.TableLayoutPanelDetails.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanelDetails.Controls.Add(Me.LabelAccountManagerDetailsInfo, 0, 1)
        Me.TableLayoutPanelDetails.Controls.Add(Me.PanelAccountManagerDetails, 0, 2)
        Me.TableLayoutPanelDetails.Controls.Add(Me.LabelAccountManagerDetailsTitle, 0, 0)
        Me.TableLayoutPanelDetails.Location = New System.Drawing.Point(15, 16)
        Me.TableLayoutPanelDetails.Margin = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.TableLayoutPanelDetails.Name = "TableLayoutPanelDetails"
        Me.TableLayoutPanelDetails.RowCount = 3
        Me.TableLayoutPanelDetails.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanelDetails.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanelDetails.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanelDetails.Size = New System.Drawing.Size(1020, 622)
        Me.TableLayoutPanelDetails.TabIndex = 3
        '
        'LabelAccountManagerDetailsInfo
        '
        Me.LabelAccountManagerDetailsInfo.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelAccountManagerDetailsInfo.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelAccountManagerDetailsInfo.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelAccountManagerDetailsInfo.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top
        Me.LabelAccountManagerDetailsInfo.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap
        Me.LabelAccountManagerDetailsInfo.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelAccountManagerDetailsInfo.Location = New System.Drawing.Point(4, 48)
        Me.LabelAccountManagerDetailsInfo.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelAccountManagerDetailsInfo.Name = "LabelAccountManagerDetailsInfo"
        Me.LabelAccountManagerDetailsInfo.Size = New System.Drawing.Size(1012, 51)
        Me.LabelAccountManagerDetailsInfo.TabIndex = 0
        Me.LabelAccountManagerDetailsInfo.Text = resources.GetString("LabelAccountManagerDetailsInfo.Text")
        '
        'PanelAccountManagerDetails
        '
        Me.PanelAccountManagerDetails.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelAccountManagerDetails.Controls.Add(Me.LabelUser)
        Me.PanelAccountManagerDetails.Controls.Add(Me.HyperlinkUser)
        Me.PanelAccountManagerDetails.Controls.Add(Me.LabelFirstName)
        Me.PanelAccountManagerDetails.Controls.Add(Me.TextEditFirstName)
        Me.PanelAccountManagerDetails.Controls.Add(Me.LabelLastName)
        Me.PanelAccountManagerDetails.Controls.Add(Me.CheckEditDormant)
        Me.PanelAccountManagerDetails.Controls.Add(Me.TextEditLastName)
        Me.PanelAccountManagerDetails.Controls.Add(Me.LabelEmail)
        Me.PanelAccountManagerDetails.Controls.Add(Me.TextEditEmail)
        Me.PanelAccountManagerDetails.Controls.Add(Me.LabelCode)
        Me.PanelAccountManagerDetails.Controls.Add(Me.TextEditCode)
        Me.PanelAccountManagerDetails.Location = New System.Drawing.Point(0, 115)
        Me.PanelAccountManagerDetails.Margin = New System.Windows.Forms.Padding(0)
        Me.PanelAccountManagerDetails.Name = "PanelAccountManagerDetails"
        Me.PanelAccountManagerDetails.Size = New System.Drawing.Size(1020, 507)
        Me.PanelAccountManagerDetails.TabIndex = 1
        '
        'LabelUser
        '
        Me.LabelUser.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelUser.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelUser.Location = New System.Drawing.Point(4, 8)
        Me.LabelUser.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelUser.Name = "LabelUser"
        Me.LabelUser.Size = New System.Drawing.Size(96, 17)
        Me.LabelUser.TabIndex = 0
        Me.LabelUser.Text = "System User:"
        '
        'HyperlinkUser
        '
        Me.HyperlinkUser.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkUser.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkUser.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkUser.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkUser.Location = New System.Drawing.Point(168, 8)
        Me.HyperlinkUser.Margin = New System.Windows.Forms.Padding(4)
        Me.HyperlinkUser.Name = "HyperlinkUser"
        Me.HyperlinkUser.Size = New System.Drawing.Size(57, 17)
        Me.HyperlinkUser.TabIndex = 1
        Me.HyperlinkUser.Text = "Select..."
        '
        'LabelFirstName
        '
        Me.LabelFirstName.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelFirstName.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelFirstName.Location = New System.Drawing.Point(4, 42)
        Me.LabelFirstName.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelFirstName.Name = "LabelFirstName"
        Me.LabelFirstName.Size = New System.Drawing.Size(81, 17)
        Me.LabelFirstName.TabIndex = 2
        Me.LabelFirstName.Text = "First Name:"
        '
        'TextEditFirstName
        '
        Me.TextEditFirstName.Location = New System.Drawing.Point(168, 38)
        Me.TextEditFirstName.Margin = New System.Windows.Forms.Padding(4)
        Me.TextEditFirstName.Name = "TextEditFirstName"
        Me.TextEditFirstName.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditFirstName.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditFirstName.Properties.Appearance.Options.UseFont = True
        Me.TextEditFirstName.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditFirstName.Properties.MaxLength = 200
        Me.TextEditFirstName.Size = New System.Drawing.Size(368, 24)
        Me.TextEditFirstName.TabIndex = 3
        '
        'LabelLastName
        '
        Me.LabelLastName.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelLastName.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelLastName.Location = New System.Drawing.Point(4, 76)
        Me.LabelLastName.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelLastName.Name = "LabelLastName"
        Me.LabelLastName.Size = New System.Drawing.Size(80, 17)
        Me.LabelLastName.TabIndex = 4
        Me.LabelLastName.Text = "Last Name:"
        '
        'CheckEditDormant
        '
        Me.CheckEditDormant.Location = New System.Drawing.Point(166, 174)
        Me.CheckEditDormant.Margin = New System.Windows.Forms.Padding(4)
        Me.CheckEditDormant.Name = "CheckEditDormant"
        Me.CheckEditDormant.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditDormant.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditDormant.Properties.Appearance.Options.UseFont = True
        Me.CheckEditDormant.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditDormant.Properties.AutoWidth = True
        Me.CheckEditDormant.Properties.Caption = "Dormant"
        Me.CheckEditDormant.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditDormant.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditDormant.Size = New System.Drawing.Size(84, 21)
        Me.CheckEditDormant.TabIndex = 10
        '
        'TextEditLastName
        '
        Me.TextEditLastName.Location = New System.Drawing.Point(168, 72)
        Me.TextEditLastName.Margin = New System.Windows.Forms.Padding(4)
        Me.TextEditLastName.Name = "TextEditLastName"
        Me.TextEditLastName.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditLastName.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditLastName.Properties.Appearance.Options.UseFont = True
        Me.TextEditLastName.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditLastName.Properties.MaxLength = 200
        Me.TextEditLastName.Size = New System.Drawing.Size(368, 24)
        Me.TextEditLastName.TabIndex = 5
        '
        'LabelEmail
        '
        Me.LabelEmail.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelEmail.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelEmail.Location = New System.Drawing.Point(4, 110)
        Me.LabelEmail.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelEmail.Name = "LabelEmail"
        Me.LabelEmail.Size = New System.Drawing.Size(105, 17)
        Me.LabelEmail.TabIndex = 6
        Me.LabelEmail.Text = "Email Address:"
        '
        'TextEditEmail
        '
        Me.TextEditEmail.Location = New System.Drawing.Point(168, 106)
        Me.TextEditEmail.Margin = New System.Windows.Forms.Padding(4)
        Me.TextEditEmail.Name = "TextEditEmail"
        Me.TextEditEmail.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditEmail.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditEmail.Properties.Appearance.Options.UseFont = True
        Me.TextEditEmail.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditEmail.Properties.MaxLength = 200
        Me.TextEditEmail.Size = New System.Drawing.Size(368, 24)
        Me.TextEditEmail.TabIndex = 7
        '
        'LabelCode
        '
        Me.LabelCode.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCode.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCode.Location = New System.Drawing.Point(4, 144)
        Me.LabelCode.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelCode.Name = "LabelCode"
        Me.LabelCode.Size = New System.Drawing.Size(42, 17)
        Me.LabelCode.TabIndex = 8
        Me.LabelCode.Text = "Code:"
        '
        'TextEditCode
        '
        Me.TextEditCode.Location = New System.Drawing.Point(168, 140)
        Me.TextEditCode.Margin = New System.Windows.Forms.Padding(4)
        Me.TextEditCode.Name = "TextEditCode"
        Me.TextEditCode.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditCode.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditCode.Properties.Appearance.Options.UseFont = True
        Me.TextEditCode.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditCode.Properties.CharacterCasing = System.Windows.Forms.CharacterCasing.Upper
        Me.TextEditCode.Properties.MaxLength = 2
        Me.TextEditCode.Size = New System.Drawing.Size(368, 24)
        Me.TextEditCode.TabIndex = 9
        '
        'TabPageClients
        '
        Me.TabPageClients.Controls.Add(Me.PanelClients)
        Me.TabPageClients.Margin = New System.Windows.Forms.Padding(4)
        Me.TabPageClients.Name = "TabPageClients"
        Me.TabPageClients.Size = New System.Drawing.Size(1059, 664)
        Me.TabPageClients.Text = "Clients"
        '
        'PanelClients
        '
        Me.PanelClients.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelClients.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelClients.Controls.Add(Me.TableClientAccountManager)
        Me.PanelClients.Location = New System.Drawing.Point(4, 4)
        Me.PanelClients.Margin = New System.Windows.Forms.Padding(4)
        Me.PanelClients.Name = "PanelClients"
        Me.PanelClients.Size = New System.Drawing.Size(1050, 654)
        Me.PanelClients.TabIndex = 2
        '
        'TableClientAccountManager
        '
        Me.TableClientAccountManager.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableClientAccountManager.BackColor = System.Drawing.Color.Transparent
        Me.TableClientAccountManager.ColumnCount = 2
        Me.TableClientAccountManager.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableClientAccountManager.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 257.0!))
        Me.TableClientAccountManager.Controls.Add(Me.GroupControlClientAccountManager, 0, 2)
        Me.TableClientAccountManager.Controls.Add(Me.LabelClientManagement, 0, 0)
        Me.TableClientAccountManager.Controls.Add(Me.LabelClientManagementInfo, 0, 1)
        Me.TableClientAccountManager.Controls.Add(Me.GroupControlContracts, 1, 2)
        Me.TableClientAccountManager.Location = New System.Drawing.Point(15, 16)
        Me.TableClientAccountManager.Margin = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.TableClientAccountManager.Name = "TableClientAccountManager"
        Me.TableClientAccountManager.RowCount = 3
        Me.TableClientAccountManager.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableClientAccountManager.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableClientAccountManager.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableClientAccountManager.Size = New System.Drawing.Size(1020, 622)
        Me.TableClientAccountManager.TabIndex = 2
        '
        'GroupControlClientAccountManager
        '
        Me.GroupControlClientAccountManager.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlClientAccountManager.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlClientAccountManager.Appearance.Options.UseFont = True
        Me.GroupControlClientAccountManager.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlClientAccountManager.AppearanceCaption.Options.UseFont = True
        Me.GroupControlClientAccountManager.Controls.Add(Me.LabelControl14)
        Me.GroupControlClientAccountManager.Controls.Add(Me.TextEditSearchClientAccountManager)
        Me.GroupControlClientAccountManager.Controls.Add(Me.PictureClearSearchClientAccountManager)
        Me.GroupControlClientAccountManager.Controls.Add(Me.PictureAdvancedSearchClientAccountManager)
        Me.GroupControlClientAccountManager.Controls.Add(Me.ButtonRemoveClientAccountManager)
        Me.GroupControlClientAccountManager.Controls.Add(Me.ButtonAddClientAccountManager)
        Me.GroupControlClientAccountManager.Controls.Add(Me.GridClientAccountManager)
        Me.GroupControlClientAccountManager.Location = New System.Drawing.Point(4, 136)
        Me.GroupControlClientAccountManager.LookAndFeel.SkinName = "Black"
        Me.GroupControlClientAccountManager.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlClientAccountManager.Margin = New System.Windows.Forms.Padding(4)
        Me.GroupControlClientAccountManager.Name = "GroupControlClientAccountManager"
        Me.GroupControlClientAccountManager.Size = New System.Drawing.Size(755, 482)
        Me.GroupControlClientAccountManager.TabIndex = 0
        Me.GroupControlClientAccountManager.Text = "Clients Managed"
        '
        'LabelControl14
        '
        Me.LabelControl14.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl14.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl14.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl14.Location = New System.Drawing.Point(436, 450)
        Me.LabelControl14.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelControl14.Name = "LabelControl14"
        Me.LabelControl14.Size = New System.Drawing.Size(54, 17)
        Me.LabelControl14.TabIndex = 4
        Me.LabelControl14.Text = "Search:"
        '
        'TextEditSearchClientAccountManager
        '
        Me.TextEditSearchClientAccountManager.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchClientAccountManager.EditValue = ""
        Me.TextEditSearchClientAccountManager.Location = New System.Drawing.Point(617, 446)
        Me.TextEditSearchClientAccountManager.Margin = New System.Windows.Forms.Padding(4, 5, 4, 7)
        Me.TextEditSearchClientAccountManager.Name = "TextEditSearchClientAccountManager"
        Me.TextEditSearchClientAccountManager.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchClientAccountManager.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchClientAccountManager.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchClientAccountManager.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchClientAccountManager.Size = New System.Drawing.Size(103, 24)
        Me.TextEditSearchClientAccountManager.TabIndex = 5
        '
        'PictureClearSearchClientAccountManager
        '
        Me.PictureClearSearchClientAccountManager.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchClientAccountManager.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchClientAccountManager.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchClientAccountManager.Location = New System.Drawing.Point(728, 4)
        Me.PictureClearSearchClientAccountManager.Margin = New System.Windows.Forms.Padding(4)
        Me.PictureClearSearchClientAccountManager.Name = "PictureClearSearchClientAccountManager"
        Me.PictureClearSearchClientAccountManager.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchClientAccountManager.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchClientAccountManager.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchClientAccountManager.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem1.Text = "Clear Search"
        ToolTipItem1.LeftIndent = 6
        ToolTipItem1.Text = "Click here to clear all search boxes."
        SuperToolTip1.Items.Add(ToolTipTitleItem1)
        SuperToolTip1.Items.Add(ToolTipItem1)
        Me.PictureClearSearchClientAccountManager.SuperTip = SuperToolTip1
        Me.PictureClearSearchClientAccountManager.TabIndex = 0
        Me.PictureClearSearchClientAccountManager.TabStop = True
        '
        'PictureAdvancedSearchClientAccountManager
        '
        Me.PictureAdvancedSearchClientAccountManager.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearchClientAccountManager.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearchClientAccountManager.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearchClientAccountManager.Location = New System.Drawing.Point(728, 449)
        Me.PictureAdvancedSearchClientAccountManager.Margin = New System.Windows.Forms.Padding(4, 8, 4, 9)
        Me.PictureAdvancedSearchClientAccountManager.Name = "PictureAdvancedSearchClientAccountManager"
        Me.PictureAdvancedSearchClientAccountManager.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearchClientAccountManager.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearchClientAccountManager.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearchClientAccountManager.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem2.Text = "Advanced Search"
        ToolTipItem2.LeftIndent = 6
        ToolTipItem2.Text = "Click here to search individual column values."
        SuperToolTip2.Items.Add(ToolTipTitleItem2)
        SuperToolTip2.Items.Add(ToolTipItem2)
        Me.PictureAdvancedSearchClientAccountManager.SuperTip = SuperToolTip2
        Me.PictureAdvancedSearchClientAccountManager.TabIndex = 6
        Me.PictureAdvancedSearchClientAccountManager.TabStop = True
        '
        'ButtonRemoveClientAccountManager
        '
        Me.ButtonRemoveClientAccountManager.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonRemoveClientAccountManager.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonRemoveClientAccountManager.Appearance.Options.UseFont = True
        Me.ButtonRemoveClientAccountManager.ImageIndex = 2
        Me.ButtonRemoveClientAccountManager.ImageList = Me.ImageList16x16
        Me.ButtonRemoveClientAccountManager.Location = New System.Drawing.Point(111, 445)
        Me.ButtonRemoveClientAccountManager.LookAndFeel.SkinName = "Black"
        Me.ButtonRemoveClientAccountManager.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonRemoveClientAccountManager.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonRemoveClientAccountManager.Name = "ButtonRemoveClientAccountManager"
        Me.ButtonRemoveClientAccountManager.Size = New System.Drawing.Size(96, 30)
        Me.ButtonRemoveClientAccountManager.TabIndex = 3
        Me.ButtonRemoveClientAccountManager.Text = "Remove"
        '
        'ButtonAddClientAccountManager
        '
        Me.ButtonAddClientAccountManager.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAddClientAccountManager.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAddClientAccountManager.Appearance.Options.UseFont = True
        Me.ButtonAddClientAccountManager.ImageIndex = 0
        Me.ButtonAddClientAccountManager.ImageList = Me.ImageList16x16
        Me.ButtonAddClientAccountManager.Location = New System.Drawing.Point(6, 445)
        Me.ButtonAddClientAccountManager.LookAndFeel.SkinName = "Black"
        Me.ButtonAddClientAccountManager.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAddClientAccountManager.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonAddClientAccountManager.Name = "ButtonAddClientAccountManager"
        Me.ButtonAddClientAccountManager.Size = New System.Drawing.Size(96, 30)
        Me.ButtonAddClientAccountManager.TabIndex = 2
        Me.ButtonAddClientAccountManager.Text = "Add"
        '
        'GridClientAccountManager
        '
        Me.GridClientAccountManager.AllowUserToAddRows = False
        Me.GridClientAccountManager.AllowUserToDeleteRows = False
        Me.GridClientAccountManager.AllowUserToOrderColumns = True
        Me.GridClientAccountManager.AllowUserToResizeRows = False
        DataGridViewCellStyle1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridClientAccountManager.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle1
        Me.GridClientAccountManager.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridClientAccountManager.BackgroundColor = System.Drawing.Color.White
        Me.GridClientAccountManager.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridClientAccountManager.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridClientAccountManager.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle2.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle2.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridClientAccountManager.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle2
        Me.GridClientAccountManager.ColumnHeadersHeight = 22
        Me.GridClientAccountManager.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridClientAccountManager.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.ClientNameColumn, Me.FromColumn, Me.ToColumn, Me.CareTakerColumn})
        Me.GridClientAccountManager.EnableHeadersVisualStyles = False
        Me.GridClientAccountManager.GridColor = System.Drawing.Color.White
        Me.GridClientAccountManager.Location = New System.Drawing.Point(3, 29)
        Me.GridClientAccountManager.Margin = New System.Windows.Forms.Padding(4)
        Me.GridClientAccountManager.Name = "GridClientAccountManager"
        Me.GridClientAccountManager.ReadOnly = True
        Me.GridClientAccountManager.RowHeadersVisible = False
        DataGridViewCellStyle5.ForeColor = System.Drawing.Color.DimGray
        Me.GridClientAccountManager.RowsDefaultCellStyle = DataGridViewCellStyle5
        Me.GridClientAccountManager.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridClientAccountManager.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridClientAccountManager.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridClientAccountManager.RowTemplate.Height = 19
        Me.GridClientAccountManager.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridClientAccountManager.ShowCellToolTips = False
        Me.GridClientAccountManager.Size = New System.Drawing.Size(750, 408)
        Me.GridClientAccountManager.StandardTab = True
        Me.GridClientAccountManager.TabIndex = 1
        '
        'ClientNameColumn
        '
        Me.ClientNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.ClientNameColumn.DataPropertyName = "ClientName"
        Me.ClientNameColumn.HeaderText = "Client"
        Me.ClientNameColumn.Name = "ClientNameColumn"
        Me.ClientNameColumn.ReadOnly = True
        '
        'FromColumn
        '
        Me.FromColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None
        Me.FromColumn.DataPropertyName = "EffectiveDate"
        DataGridViewCellStyle3.Format = "d"
        Me.FromColumn.DefaultCellStyle = DataGridViewCellStyle3
        Me.FromColumn.HeaderText = "From"
        Me.FromColumn.Name = "FromColumn"
        Me.FromColumn.ReadOnly = True
        '
        'ToColumn
        '
        Me.ToColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None
        Me.ToColumn.DataPropertyName = "EndDate"
        DataGridViewCellStyle4.Format = "d"
        DataGridViewCellStyle4.NullValue = Nothing
        Me.ToColumn.DefaultCellStyle = DataGridViewCellStyle4
        Me.ToColumn.HeaderText = "To"
        Me.ToColumn.Name = "ToColumn"
        Me.ToColumn.ReadOnly = True
        '
        'CareTakerColumn
        '
        Me.CareTakerColumn.DataPropertyName = "CareTaker"
        Me.CareTakerColumn.HeaderText = "Care Taker"
        Me.CareTakerColumn.Name = "CareTakerColumn"
        Me.CareTakerColumn.ReadOnly = True
        Me.CareTakerColumn.Resizable = System.Windows.Forms.DataGridViewTriState.[True]
        Me.CareTakerColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Automatic
        '
        'LabelClientManagementInfo
        '
        Me.LabelClientManagementInfo.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelClientManagementInfo.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelClientManagementInfo.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelClientManagementInfo.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top
        Me.LabelClientManagementInfo.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap
        Me.LabelClientManagementInfo.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.TableClientAccountManager.SetColumnSpan(Me.LabelClientManagementInfo, 2)
        Me.LabelClientManagementInfo.Location = New System.Drawing.Point(4, 48)
        Me.LabelClientManagementInfo.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelClientManagementInfo.Name = "LabelClientManagementInfo"
        Me.LabelClientManagementInfo.Size = New System.Drawing.Size(1012, 68)
        Me.LabelClientManagementInfo.TabIndex = 0
        Me.LabelClientManagementInfo.Text = resources.GetString("LabelClientManagementInfo.Text")
        '
        'GroupControlContracts
        '
        Me.GroupControlContracts.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlContracts.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlContracts.Appearance.Options.UseFont = True
        Me.GroupControlContracts.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlContracts.AppearanceCaption.Options.UseFont = True
        Me.GroupControlContracts.Controls.Add(Me.LabelControl1)
        Me.GroupControlContracts.Controls.Add(Me.TextEditSearchLinkedContracts)
        Me.GroupControlContracts.Controls.Add(Me.PictureClearSearchLinkedContracts)
        Me.GroupControlContracts.Controls.Add(Me.PictureAdvancedSearchLinkedContracts)
        Me.GroupControlContracts.Controls.Add(Me.GridLinkedContracts)
        Me.GroupControlContracts.Location = New System.Drawing.Point(778, 136)
        Me.GroupControlContracts.LookAndFeel.SkinName = "Black"
        Me.GroupControlContracts.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlContracts.Margin = New System.Windows.Forms.Padding(15, 4, 4, 4)
        Me.GroupControlContracts.Name = "GroupControlContracts"
        Me.GroupControlContracts.Size = New System.Drawing.Size(238, 482)
        Me.GroupControlContracts.TabIndex = 1
        Me.GroupControlContracts.Text = "Contracts"
        '
        'LabelControl1
        '
        Me.LabelControl1.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl1.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl1.Location = New System.Drawing.Point(-81, 450)
        Me.LabelControl1.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(54, 17)
        Me.LabelControl1.TabIndex = 2
        Me.LabelControl1.Text = "Search:"
        '
        'TextEditSearchLinkedContracts
        '
        Me.TextEditSearchLinkedContracts.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchLinkedContracts.EditValue = ""
        Me.TextEditSearchLinkedContracts.Location = New System.Drawing.Point(100, 446)
        Me.TextEditSearchLinkedContracts.Margin = New System.Windows.Forms.Padding(4, 5, 4, 7)
        Me.TextEditSearchLinkedContracts.Name = "TextEditSearchLinkedContracts"
        Me.TextEditSearchLinkedContracts.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchLinkedContracts.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchLinkedContracts.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchLinkedContracts.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchLinkedContracts.Size = New System.Drawing.Size(103, 24)
        Me.TextEditSearchLinkedContracts.TabIndex = 3
        '
        'PictureClearSearchLinkedContracts
        '
        Me.PictureClearSearchLinkedContracts.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchLinkedContracts.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchLinkedContracts.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchLinkedContracts.Location = New System.Drawing.Point(211, 4)
        Me.PictureClearSearchLinkedContracts.Margin = New System.Windows.Forms.Padding(4)
        Me.PictureClearSearchLinkedContracts.Name = "PictureClearSearchLinkedContracts"
        Me.PictureClearSearchLinkedContracts.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchLinkedContracts.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchLinkedContracts.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchLinkedContracts.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem3.Text = "Clear Search"
        ToolTipItem3.LeftIndent = 6
        ToolTipItem3.Text = "Click here to clear all search boxes."
        SuperToolTip3.Items.Add(ToolTipTitleItem3)
        SuperToolTip3.Items.Add(ToolTipItem3)
        Me.PictureClearSearchLinkedContracts.SuperTip = SuperToolTip3
        Me.PictureClearSearchLinkedContracts.TabIndex = 0
        Me.PictureClearSearchLinkedContracts.TabStop = True
        '
        'PictureAdvancedSearchLinkedContracts
        '
        Me.PictureAdvancedSearchLinkedContracts.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearchLinkedContracts.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearchLinkedContracts.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearchLinkedContracts.Location = New System.Drawing.Point(211, 449)
        Me.PictureAdvancedSearchLinkedContracts.Margin = New System.Windows.Forms.Padding(4, 8, 4, 9)
        Me.PictureAdvancedSearchLinkedContracts.Name = "PictureAdvancedSearchLinkedContracts"
        Me.PictureAdvancedSearchLinkedContracts.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearchLinkedContracts.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearchLinkedContracts.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearchLinkedContracts.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem4.Text = "Advanced Search"
        ToolTipItem4.LeftIndent = 6
        ToolTipItem4.Text = "Click here to search individual column values."
        SuperToolTip4.Items.Add(ToolTipTitleItem4)
        SuperToolTip4.Items.Add(ToolTipItem4)
        Me.PictureAdvancedSearchLinkedContracts.SuperTip = SuperToolTip4
        Me.PictureAdvancedSearchLinkedContracts.TabIndex = 4
        Me.PictureAdvancedSearchLinkedContracts.TabStop = True
        '
        'GridLinkedContracts
        '
        Me.GridLinkedContracts.AllowUserToAddRows = False
        Me.GridLinkedContracts.AllowUserToDeleteRows = False
        Me.GridLinkedContracts.AllowUserToOrderColumns = True
        Me.GridLinkedContracts.AllowUserToResizeRows = False
        DataGridViewCellStyle6.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridLinkedContracts.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle6
        Me.GridLinkedContracts.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridLinkedContracts.BackgroundColor = System.Drawing.Color.White
        Me.GridLinkedContracts.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridLinkedContracts.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridLinkedContracts.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle7.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle7.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle7.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle7.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle7.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle7.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle7.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridLinkedContracts.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle7
        Me.GridLinkedContracts.ColumnHeadersHeight = 22
        Me.GridLinkedContracts.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridLinkedContracts.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.ContractNumberColumn})
        Me.GridLinkedContracts.EnableHeadersVisualStyles = False
        Me.GridLinkedContracts.GridColor = System.Drawing.Color.White
        Me.GridLinkedContracts.Location = New System.Drawing.Point(3, 29)
        Me.GridLinkedContracts.Margin = New System.Windows.Forms.Padding(4)
        Me.GridLinkedContracts.Name = "GridLinkedContracts"
        Me.GridLinkedContracts.ReadOnly = True
        Me.GridLinkedContracts.RowHeadersVisible = False
        DataGridViewCellStyle8.ForeColor = System.Drawing.Color.DimGray
        Me.GridLinkedContracts.RowsDefaultCellStyle = DataGridViewCellStyle8
        Me.GridLinkedContracts.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridLinkedContracts.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridLinkedContracts.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridLinkedContracts.RowTemplate.Height = 19
        Me.GridLinkedContracts.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridLinkedContracts.ShowCellToolTips = False
        Me.GridLinkedContracts.Size = New System.Drawing.Size(233, 408)
        Me.GridLinkedContracts.StandardTab = True
        Me.GridLinkedContracts.TabIndex = 1
        '
        'ContractNumberColumn
        '
        Me.ContractNumberColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.ContractNumberColumn.DataPropertyName = "ContractNumber"
        Me.ContractNumberColumn.HeaderText = "Contract Number"
        Me.ContractNumberColumn.Name = "ContractNumberColumn"
        Me.ContractNumberColumn.ReadOnly = True
        '
        'TabPageBudgets
        '
        Me.TabPageBudgets.Controls.Add(Me.PanelBudgets)
        Me.TabPageBudgets.Margin = New System.Windows.Forms.Padding(4)
        Me.TabPageBudgets.Name = "TabPageBudgets"
        Me.TabPageBudgets.Size = New System.Drawing.Size(1059, 664)
        Me.TabPageBudgets.Text = "Budgets"
        '
        'PanelBudgets
        '
        Me.PanelBudgets.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelBudgets.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelBudgets.Controls.Add(Me.TableLayoutPanel1)
        Me.PanelBudgets.Location = New System.Drawing.Point(4, 4)
        Me.PanelBudgets.Margin = New System.Windows.Forms.Padding(4)
        Me.PanelBudgets.Name = "PanelBudgets"
        Me.PanelBudgets.Size = New System.Drawing.Size(1050, 654)
        Me.PanelBudgets.TabIndex = 5
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanel1.BackColor = System.Drawing.Color.Transparent
        Me.TableLayoutPanel1.ColumnCount = 1
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.GroupControlBudget, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.LabelControl9, 0, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.LabelControl10, 0, 1)
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(15, 16)
        Me.TableLayoutPanel1.Margin = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 3
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(1020, 622)
        Me.TableLayoutPanel1.TabIndex = 3
        '
        'GroupControlBudget
        '
        Me.GroupControlBudget.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlBudget.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlBudget.Appearance.Options.UseFont = True
        Me.GroupControlBudget.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlBudget.AppearanceCaption.Options.UseFont = True
        Me.GroupControlBudget.Controls.Add(Me.ButtonEditBudget)
        Me.GroupControlBudget.Controls.Add(Me.LabelControl2)
        Me.GroupControlBudget.Controls.Add(Me.TextEditSearchBudget)
        Me.GroupControlBudget.Controls.Add(Me.PictureClearSearchBudget)
        Me.GroupControlBudget.Controls.Add(Me.PictureAdvancedSearchBudget)
        Me.GroupControlBudget.Controls.Add(Me.ButtonDeleteBudget)
        Me.GroupControlBudget.Controls.Add(Me.ButtonAddBudget)
        Me.GroupControlBudget.Controls.Add(Me.GridBudget)
        Me.GroupControlBudget.Location = New System.Drawing.Point(4, 85)
        Me.GroupControlBudget.LookAndFeel.SkinName = "Black"
        Me.GroupControlBudget.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlBudget.Margin = New System.Windows.Forms.Padding(4)
        Me.GroupControlBudget.Name = "GroupControlBudget"
        Me.GroupControlBudget.Size = New System.Drawing.Size(1012, 533)
        Me.GroupControlBudget.TabIndex = 5
        Me.GroupControlBudget.Text = "Budget List"
        '
        'ButtonEditBudget
        '
        Me.ButtonEditBudget.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonEditBudget.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonEditBudget.Appearance.Options.UseFont = True
        Me.ButtonEditBudget.ImageIndex = 1
        Me.ButtonEditBudget.ImageList = Me.ImageList16x16
        Me.ButtonEditBudget.Location = New System.Drawing.Point(111, 496)
        Me.ButtonEditBudget.LookAndFeel.SkinName = "Black"
        Me.ButtonEditBudget.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonEditBudget.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonEditBudget.Name = "ButtonEditBudget"
        Me.ButtonEditBudget.Size = New System.Drawing.Size(96, 30)
        Me.ButtonEditBudget.TabIndex = 7
        Me.ButtonEditBudget.Text = "Edit"
        '
        'LabelControl2
        '
        Me.LabelControl2.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl2.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl2.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl2.Location = New System.Drawing.Point(693, 501)
        Me.LabelControl2.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.Size = New System.Drawing.Size(54, 17)
        Me.LabelControl2.TabIndex = 4
        Me.LabelControl2.Text = "Search:"
        '
        'TextEditSearchBudget
        '
        Me.TextEditSearchBudget.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchBudget.EditValue = ""
        Me.TextEditSearchBudget.Location = New System.Drawing.Point(874, 497)
        Me.TextEditSearchBudget.Margin = New System.Windows.Forms.Padding(4, 5, 4, 7)
        Me.TextEditSearchBudget.Name = "TextEditSearchBudget"
        Me.TextEditSearchBudget.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchBudget.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchBudget.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchBudget.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchBudget.Size = New System.Drawing.Size(103, 24)
        Me.TextEditSearchBudget.TabIndex = 5
        '
        'PictureClearSearchBudget
        '
        Me.PictureClearSearchBudget.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchBudget.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchBudget.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchBudget.Location = New System.Drawing.Point(985, 4)
        Me.PictureClearSearchBudget.Margin = New System.Windows.Forms.Padding(4)
        Me.PictureClearSearchBudget.Name = "PictureClearSearchBudget"
        Me.PictureClearSearchBudget.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchBudget.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchBudget.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchBudget.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem5.Text = "Clear Search"
        ToolTipItem5.LeftIndent = 6
        ToolTipItem5.Text = "Click here to clear all search boxes."
        SuperToolTip5.Items.Add(ToolTipTitleItem5)
        SuperToolTip5.Items.Add(ToolTipItem5)
        Me.PictureClearSearchBudget.SuperTip = SuperToolTip5
        Me.PictureClearSearchBudget.TabIndex = 0
        Me.PictureClearSearchBudget.TabStop = True
        '
        'PictureAdvancedSearchBudget
        '
        Me.PictureAdvancedSearchBudget.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearchBudget.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearchBudget.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearchBudget.Location = New System.Drawing.Point(985, 500)
        Me.PictureAdvancedSearchBudget.Margin = New System.Windows.Forms.Padding(4, 8, 4, 9)
        Me.PictureAdvancedSearchBudget.Name = "PictureAdvancedSearchBudget"
        Me.PictureAdvancedSearchBudget.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearchBudget.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearchBudget.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearchBudget.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem6.Text = "Advanced Search"
        ToolTipItem6.LeftIndent = 6
        ToolTipItem6.Text = "Click here to search individual column values."
        SuperToolTip6.Items.Add(ToolTipTitleItem6)
        SuperToolTip6.Items.Add(ToolTipItem6)
        Me.PictureAdvancedSearchBudget.SuperTip = SuperToolTip6
        Me.PictureAdvancedSearchBudget.TabIndex = 6
        Me.PictureAdvancedSearchBudget.TabStop = True
        '
        'ButtonDeleteBudget
        '
        Me.ButtonDeleteBudget.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonDeleteBudget.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonDeleteBudget.Appearance.Options.UseFont = True
        Me.ButtonDeleteBudget.ImageIndex = 2
        Me.ButtonDeleteBudget.ImageList = Me.ImageList16x16
        Me.ButtonDeleteBudget.Location = New System.Drawing.Point(215, 496)
        Me.ButtonDeleteBudget.LookAndFeel.SkinName = "Black"
        Me.ButtonDeleteBudget.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonDeleteBudget.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonDeleteBudget.Name = "ButtonDeleteBudget"
        Me.ButtonDeleteBudget.Size = New System.Drawing.Size(96, 30)
        Me.ButtonDeleteBudget.TabIndex = 3
        Me.ButtonDeleteBudget.Text = "Delete"
        '
        'ButtonAddBudget
        '
        Me.ButtonAddBudget.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAddBudget.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAddBudget.Appearance.Options.UseFont = True
        Me.ButtonAddBudget.ImageIndex = 0
        Me.ButtonAddBudget.ImageList = Me.ImageList16x16
        Me.ButtonAddBudget.Location = New System.Drawing.Point(6, 496)
        Me.ButtonAddBudget.LookAndFeel.SkinName = "Black"
        Me.ButtonAddBudget.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAddBudget.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonAddBudget.Name = "ButtonAddBudget"
        Me.ButtonAddBudget.Size = New System.Drawing.Size(96, 30)
        Me.ButtonAddBudget.TabIndex = 2
        Me.ButtonAddBudget.Text = "Add"
        '
        'GridBudget
        '
        Me.GridBudget.AllowUserToAddRows = False
        Me.GridBudget.AllowUserToDeleteRows = False
        Me.GridBudget.AllowUserToOrderColumns = True
        Me.GridBudget.AllowUserToResizeRows = False
        DataGridViewCellStyle9.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridBudget.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle9
        Me.GridBudget.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridBudget.BackgroundColor = System.Drawing.Color.White
        Me.GridBudget.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridBudget.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridBudget.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle10.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle10.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle10.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle10.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle10.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle10.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle10.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridBudget.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle10
        Me.GridBudget.ColumnHeadersHeight = 22
        Me.GridBudget.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridBudget.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.FiscalNameColumn, Me.BudgetColumn})
        Me.GridBudget.EnableHeadersVisualStyles = False
        Me.GridBudget.GridColor = System.Drawing.Color.White
        Me.GridBudget.Location = New System.Drawing.Point(3, 29)
        Me.GridBudget.Margin = New System.Windows.Forms.Padding(4)
        Me.GridBudget.Name = "GridBudget"
        Me.GridBudget.ReadOnly = True
        Me.GridBudget.RowHeadersVisible = False
        DataGridViewCellStyle13.ForeColor = System.Drawing.Color.DimGray
        Me.GridBudget.RowsDefaultCellStyle = DataGridViewCellStyle13
        Me.GridBudget.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridBudget.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridBudget.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridBudget.RowTemplate.Height = 19
        Me.GridBudget.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridBudget.ShowCellToolTips = False
        Me.GridBudget.Size = New System.Drawing.Size(1007, 459)
        Me.GridBudget.StandardTab = True
        Me.GridBudget.TabIndex = 1
        '
        'FiscalNameColumn
        '
        Me.FiscalNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.FiscalNameColumn.DataPropertyName = "FiscalName"
        DataGridViewCellStyle11.NullValue = Nothing
        Me.FiscalNameColumn.DefaultCellStyle = DataGridViewCellStyle11
        Me.FiscalNameColumn.FillWeight = 50.0!
        Me.FiscalNameColumn.HeaderText = "Financial Year"
        Me.FiscalNameColumn.Name = "FiscalNameColumn"
        Me.FiscalNameColumn.ReadOnly = True
        '
        'BudgetColumn
        '
        Me.BudgetColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.BudgetColumn.DataPropertyName = "Budget"
        DataGridViewCellStyle12.Format = "C2"
        DataGridViewCellStyle12.NullValue = Nothing
        Me.BudgetColumn.DefaultCellStyle = DataGridViewCellStyle12
        Me.BudgetColumn.FillWeight = 50.0!
        Me.BudgetColumn.HeaderText = "Budget"
        Me.BudgetColumn.Name = "BudgetColumn"
        Me.BudgetColumn.ReadOnly = True
        '
        'LabelControl9
        '
        Me.LabelControl9.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl9.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl9.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelControl9.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl9.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelControl9.LineVisible = True
        Me.LabelControl9.Location = New System.Drawing.Point(4, 4)
        Me.LabelControl9.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelControl9.Name = "LabelControl9"
        Me.LabelControl9.Size = New System.Drawing.Size(1012, 24)
        Me.LabelControl9.TabIndex = 3
        Me.LabelControl9.Text = "Account Manager Budgets"
        '
        'LabelControl10
        '
        Me.LabelControl10.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl10.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl10.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl10.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top
        Me.LabelControl10.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap
        Me.LabelControl10.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelControl10.Location = New System.Drawing.Point(4, 48)
        Me.LabelControl10.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelControl10.Name = "LabelControl10"
        Me.LabelControl10.Size = New System.Drawing.Size(1012, 17)
        Me.LabelControl10.TabIndex = 4
        Me.LabelControl10.Text = "Use this section to specify budgets or targets for each financial year for the ac" &
    "count manager."
        '
        'TabPageBrands
        '
        Me.TabPageBrands.Controls.Add(Me.Panel1)
        Me.TabPageBrands.Name = "TabPageBrands"
        Me.TabPageBrands.Size = New System.Drawing.Size(1059, 664)
        Me.TabPageBrands.Text = "Brands"
        '
        'Panel1
        '
        Me.Panel1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Panel1.Controls.Add(Me.TableLayoutPanel2)
        Me.Panel1.Location = New System.Drawing.Point(4, 5)
        Me.Panel1.Margin = New System.Windows.Forms.Padding(4)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(1050, 654)
        Me.Panel1.TabIndex = 6
        '
        'TableLayoutPanel2
        '
        Me.TableLayoutPanel2.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanel2.BackColor = System.Drawing.Color.Transparent
        Me.TableLayoutPanel2.ColumnCount = 1
        Me.TableLayoutPanel2.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel2.Controls.Add(Me.GroupControl1, 0, 2)
        Me.TableLayoutPanel2.Controls.Add(Me.LabelControl4, 0, 0)
        Me.TableLayoutPanel2.Controls.Add(Me.LabelControl5, 0, 1)
        Me.TableLayoutPanel2.Location = New System.Drawing.Point(15, 16)
        Me.TableLayoutPanel2.Margin = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.TableLayoutPanel2.Name = "TableLayoutPanel2"
        Me.TableLayoutPanel2.RowCount = 3
        Me.TableLayoutPanel2.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanel2.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanel2.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel2.Size = New System.Drawing.Size(1020, 622)
        Me.TableLayoutPanel2.TabIndex = 3
        '
        'GroupControl1
        '
        Me.GroupControl1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControl1.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControl1.Appearance.Options.UseFont = True
        Me.GroupControl1.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControl1.AppearanceCaption.Options.UseFont = True
        Me.GroupControl1.Controls.Add(Me.LabelControl3)
        Me.GroupControl1.Controls.Add(Me.TextEditSearchBrandAccountManager)
        Me.GroupControl1.Controls.Add(Me.PictureClearSearchBrandAccountManager)
        Me.GroupControl1.Controls.Add(Me.PictureAdvancedSearchBrandAccountManager)
        Me.GroupControl1.Controls.Add(Me.ButtonRemoveBrandAccountManager)
        Me.GroupControl1.Controls.Add(Me.ButtonAddBrandAccountManager)
        Me.GroupControl1.Controls.Add(Me.GridBrandAccountManager)
        Me.GroupControl1.Location = New System.Drawing.Point(4, 85)
        Me.GroupControl1.LookAndFeel.SkinName = "Black"
        Me.GroupControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControl1.Margin = New System.Windows.Forms.Padding(4)
        Me.GroupControl1.Name = "GroupControl1"
        Me.GroupControl1.Size = New System.Drawing.Size(1012, 533)
        Me.GroupControl1.TabIndex = 5
        Me.GroupControl1.Text = "Brands Managed"
        '
        'LabelControl3
        '
        Me.LabelControl3.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl3.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl3.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl3.Location = New System.Drawing.Point(693, 501)
        Me.LabelControl3.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelControl3.Name = "LabelControl3"
        Me.LabelControl3.Size = New System.Drawing.Size(54, 17)
        Me.LabelControl3.TabIndex = 4
        Me.LabelControl3.Text = "Search:"
        '
        'TextEditSearchBrandAccountManager
        '
        Me.TextEditSearchBrandAccountManager.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchBrandAccountManager.EditValue = ""
        Me.TextEditSearchBrandAccountManager.Location = New System.Drawing.Point(874, 497)
        Me.TextEditSearchBrandAccountManager.Margin = New System.Windows.Forms.Padding(4, 5, 4, 7)
        Me.TextEditSearchBrandAccountManager.Name = "TextEditSearchBrandAccountManager"
        Me.TextEditSearchBrandAccountManager.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchBrandAccountManager.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchBrandAccountManager.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchBrandAccountManager.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchBrandAccountManager.Size = New System.Drawing.Size(103, 24)
        Me.TextEditSearchBrandAccountManager.TabIndex = 5
        '
        'PictureClearSearchBrandAccountManager
        '
        Me.PictureClearSearchBrandAccountManager.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchBrandAccountManager.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchBrandAccountManager.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchBrandAccountManager.Location = New System.Drawing.Point(985, 4)
        Me.PictureClearSearchBrandAccountManager.Margin = New System.Windows.Forms.Padding(4)
        Me.PictureClearSearchBrandAccountManager.Name = "PictureClearSearchBrandAccountManager"
        Me.PictureClearSearchBrandAccountManager.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchBrandAccountManager.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchBrandAccountManager.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchBrandAccountManager.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem7.Text = "Clear Search"
        ToolTipItem7.LeftIndent = 6
        ToolTipItem7.Text = "Click here to clear all search boxes."
        SuperToolTip7.Items.Add(ToolTipTitleItem7)
        SuperToolTip7.Items.Add(ToolTipItem7)
        Me.PictureClearSearchBrandAccountManager.SuperTip = SuperToolTip7
        Me.PictureClearSearchBrandAccountManager.TabIndex = 0
        Me.PictureClearSearchBrandAccountManager.TabStop = True
        '
        'PictureAdvancedSearchBrandAccountManager
        '
        Me.PictureAdvancedSearchBrandAccountManager.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearchBrandAccountManager.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearchBrandAccountManager.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearchBrandAccountManager.Location = New System.Drawing.Point(985, 500)
        Me.PictureAdvancedSearchBrandAccountManager.Margin = New System.Windows.Forms.Padding(4, 8, 4, 9)
        Me.PictureAdvancedSearchBrandAccountManager.Name = "PictureAdvancedSearchBrandAccountManager"
        Me.PictureAdvancedSearchBrandAccountManager.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearchBrandAccountManager.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearchBrandAccountManager.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearchBrandAccountManager.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem8.Text = "Advanced Search"
        ToolTipItem8.LeftIndent = 6
        ToolTipItem8.Text = "Click here to search individual column values."
        SuperToolTip8.Items.Add(ToolTipTitleItem8)
        SuperToolTip8.Items.Add(ToolTipItem8)
        Me.PictureAdvancedSearchBrandAccountManager.SuperTip = SuperToolTip8
        Me.PictureAdvancedSearchBrandAccountManager.TabIndex = 6
        Me.PictureAdvancedSearchBrandAccountManager.TabStop = True
        '
        'ButtonRemoveBrandAccountManager
        '
        Me.ButtonRemoveBrandAccountManager.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonRemoveBrandAccountManager.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonRemoveBrandAccountManager.Appearance.Options.UseFont = True
        Me.ButtonRemoveBrandAccountManager.ImageIndex = 2
        Me.ButtonRemoveBrandAccountManager.ImageList = Me.ImageList16x16
        Me.ButtonRemoveBrandAccountManager.Location = New System.Drawing.Point(111, 496)
        Me.ButtonRemoveBrandAccountManager.LookAndFeel.SkinName = "Black"
        Me.ButtonRemoveBrandAccountManager.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonRemoveBrandAccountManager.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonRemoveBrandAccountManager.Name = "ButtonRemoveBrandAccountManager"
        Me.ButtonRemoveBrandAccountManager.Size = New System.Drawing.Size(96, 30)
        Me.ButtonRemoveBrandAccountManager.TabIndex = 3
        Me.ButtonRemoveBrandAccountManager.Text = "Remove"
        '
        'ButtonAddBrandAccountManager
        '
        Me.ButtonAddBrandAccountManager.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAddBrandAccountManager.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAddBrandAccountManager.Appearance.Options.UseFont = True
        Me.ButtonAddBrandAccountManager.ImageIndex = 0
        Me.ButtonAddBrandAccountManager.ImageList = Me.ImageList16x16
        Me.ButtonAddBrandAccountManager.Location = New System.Drawing.Point(6, 496)
        Me.ButtonAddBrandAccountManager.LookAndFeel.SkinName = "Black"
        Me.ButtonAddBrandAccountManager.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAddBrandAccountManager.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonAddBrandAccountManager.Name = "ButtonAddBrandAccountManager"
        Me.ButtonAddBrandAccountManager.Size = New System.Drawing.Size(96, 30)
        Me.ButtonAddBrandAccountManager.TabIndex = 2
        Me.ButtonAddBrandAccountManager.Text = "Add"
        '
        'GridBrandAccountManager
        '
        Me.GridBrandAccountManager.AllowUserToAddRows = False
        Me.GridBrandAccountManager.AllowUserToDeleteRows = False
        Me.GridBrandAccountManager.AllowUserToOrderColumns = True
        Me.GridBrandAccountManager.AllowUserToResizeRows = False
        DataGridViewCellStyle14.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridBrandAccountManager.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle14
        Me.GridBrandAccountManager.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridBrandAccountManager.BackgroundColor = System.Drawing.Color.White
        Me.GridBrandAccountManager.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridBrandAccountManager.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridBrandAccountManager.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle15.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle15.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle15.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle15.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle15.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle15.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle15.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridBrandAccountManager.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle15
        Me.GridBrandAccountManager.ColumnHeadersHeight = 22
        Me.GridBrandAccountManager.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridBrandAccountManager.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.BrandNameColumn, Me.DataGridViewTextBoxColumn2, Me.DataGridViewTextBoxColumn3, Me.DataGridViewCheckBoxColumn1})
        Me.GridBrandAccountManager.EnableHeadersVisualStyles = False
        Me.GridBrandAccountManager.GridColor = System.Drawing.Color.White
        Me.GridBrandAccountManager.Location = New System.Drawing.Point(3, 29)
        Me.GridBrandAccountManager.Margin = New System.Windows.Forms.Padding(4)
        Me.GridBrandAccountManager.Name = "GridBrandAccountManager"
        Me.GridBrandAccountManager.ReadOnly = True
        Me.GridBrandAccountManager.RowHeadersVisible = False
        DataGridViewCellStyle18.ForeColor = System.Drawing.Color.DimGray
        Me.GridBrandAccountManager.RowsDefaultCellStyle = DataGridViewCellStyle18
        Me.GridBrandAccountManager.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridBrandAccountManager.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridBrandAccountManager.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridBrandAccountManager.RowTemplate.Height = 19
        Me.GridBrandAccountManager.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridBrandAccountManager.ShowCellToolTips = False
        Me.GridBrandAccountManager.Size = New System.Drawing.Size(1007, 459)
        Me.GridBrandAccountManager.StandardTab = True
        Me.GridBrandAccountManager.TabIndex = 1
        '
        'LabelControl4
        '
        Me.LabelControl4.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl4.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl4.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelControl4.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl4.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelControl4.LineVisible = True
        Me.LabelControl4.Location = New System.Drawing.Point(4, 4)
        Me.LabelControl4.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelControl4.Name = "LabelControl4"
        Me.LabelControl4.Size = New System.Drawing.Size(1012, 24)
        Me.LabelControl4.TabIndex = 3
        Me.LabelControl4.Text = "Account Manager Brands"
        '
        'LabelControl5
        '
        Me.LabelControl5.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl5.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl5.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl5.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top
        Me.LabelControl5.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap
        Me.LabelControl5.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelControl5.Location = New System.Drawing.Point(4, 48)
        Me.LabelControl5.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelControl5.Name = "LabelControl5"
        Me.LabelControl5.Size = New System.Drawing.Size(1012, 17)
        Me.LabelControl5.TabIndex = 4
        Me.LabelControl5.Text = "Use this section to specify brands for this account manager."
        '
        'ButtonPermissions
        '
        Me.ButtonPermissions.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonPermissions.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonPermissions.Appearance.Options.UseFont = True
        Me.ButtonPermissions.ImageIndex = 3
        Me.ButtonPermissions.ImageList = Me.ImageList24x24
        Me.ButtonPermissions.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonPermissions.Location = New System.Drawing.Point(15, 790)
        Me.ButtonPermissions.LookAndFeel.SkinName = "Black"
        Me.ButtonPermissions.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonPermissions.Margin = New System.Windows.Forms.Padding(4, 16, 4, 4)
        Me.ButtonPermissions.Name = "ButtonPermissions"
        Me.ButtonPermissions.Size = New System.Drawing.Size(135, 37)
        Me.ButtonPermissions.TabIndex = 2
        Me.ButtonPermissions.Text = "Permissions"
        '
        'BrandNameColumn
        '
        Me.BrandNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.BrandNameColumn.DataPropertyName = "BrandName"
        Me.BrandNameColumn.HeaderText = "Brand"
        Me.BrandNameColumn.Name = "BrandNameColumn"
        Me.BrandNameColumn.ReadOnly = True
        '
        'DataGridViewTextBoxColumn2
        '
        Me.DataGridViewTextBoxColumn2.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None
        Me.DataGridViewTextBoxColumn2.DataPropertyName = "EffectiveDate"
        DataGridViewCellStyle16.Format = "d"
        Me.DataGridViewTextBoxColumn2.DefaultCellStyle = DataGridViewCellStyle16
        Me.DataGridViewTextBoxColumn2.HeaderText = "From"
        Me.DataGridViewTextBoxColumn2.Name = "DataGridViewTextBoxColumn2"
        Me.DataGridViewTextBoxColumn2.ReadOnly = True
        '
        'DataGridViewTextBoxColumn3
        '
        Me.DataGridViewTextBoxColumn3.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None
        Me.DataGridViewTextBoxColumn3.DataPropertyName = "EndDate"
        DataGridViewCellStyle17.Format = "d"
        DataGridViewCellStyle17.NullValue = Nothing
        Me.DataGridViewTextBoxColumn3.DefaultCellStyle = DataGridViewCellStyle17
        Me.DataGridViewTextBoxColumn3.HeaderText = "To"
        Me.DataGridViewTextBoxColumn3.Name = "DataGridViewTextBoxColumn3"
        Me.DataGridViewTextBoxColumn3.ReadOnly = True
        '
        'DataGridViewCheckBoxColumn1
        '
        Me.DataGridViewCheckBoxColumn1.DataPropertyName = "CareTaker"
        Me.DataGridViewCheckBoxColumn1.HeaderText = "Care Taker"
        Me.DataGridViewCheckBoxColumn1.Name = "DataGridViewCheckBoxColumn1"
        Me.DataGridViewCheckBoxColumn1.ReadOnly = True
        Me.DataGridViewCheckBoxColumn1.Resizable = System.Windows.Forms.DataGridViewTriState.[True]
        Me.DataGridViewCheckBoxColumn1.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Automatic
        '
        'SubformAccountManager
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(9.0!, 17.0!)
        Me.Controls.Add(Me.ButtonPermissions)
        Me.Controls.Add(Me.ButtonSave)
        Me.Controls.Add(Me.XtraTabControl1)
        Me.Controls.Add(Me.ButtonCancel)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(5)
        Me.Name = "SubformAccountManager"
        Me.Padding = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.Size = New System.Drawing.Size(1095, 842)
        Me.Tag = ""
        Me.Controls.SetChildIndex(Me.ButtonCancel, 0)
        Me.Controls.SetChildIndex(Me.XtraTabControl1, 0)
        Me.Controls.SetChildIndex(Me.ButtonSave, 0)
        Me.Controls.SetChildIndex(Me.ButtonPermissions, 0)
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabControl1.ResumeLayout(False)
        Me.TabPageDetails.ResumeLayout(False)
        Me.PanelDetails.ResumeLayout(False)
        Me.TableLayoutPanelDetails.ResumeLayout(False)
        Me.PanelAccountManagerDetails.ResumeLayout(False)
        Me.PanelAccountManagerDetails.PerformLayout()
        CType(Me.TextEditFirstName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditDormant.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditLastName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditEmail.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditCode.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabPageClients.ResumeLayout(False)
        Me.PanelClients.ResumeLayout(False)
        Me.TableClientAccountManager.ResumeLayout(False)
        CType(Me.GroupControlClientAccountManager, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlClientAccountManager.ResumeLayout(False)
        Me.GroupControlClientAccountManager.PerformLayout()
        CType(Me.TextEditSearchClientAccountManager.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearchClientAccountManager.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearchClientAccountManager.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridClientAccountManager, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControlContracts, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlContracts.ResumeLayout(False)
        Me.GroupControlContracts.PerformLayout()
        CType(Me.TextEditSearchLinkedContracts.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearchLinkedContracts.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearchLinkedContracts.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridLinkedContracts, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabPageBudgets.ResumeLayout(False)
        Me.PanelBudgets.ResumeLayout(False)
        Me.TableLayoutPanel1.ResumeLayout(False)
        CType(Me.GroupControlBudget, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlBudget.ResumeLayout(False)
        Me.GroupControlBudget.PerformLayout()
        CType(Me.TextEditSearchBudget.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearchBudget.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearchBudget.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridBudget, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabPageBrands.ResumeLayout(False)
        Me.Panel1.ResumeLayout(False)
        Me.TableLayoutPanel2.ResumeLayout(False)
        CType(Me.GroupControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControl1.ResumeLayout(False)
        Me.GroupControl1.PerformLayout()
        CType(Me.TextEditSearchBrandAccountManager.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearchBrandAccountManager.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearchBrandAccountManager.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridBrandAccountManager, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents ImageList16x16 As System.Windows.Forms.ImageList
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents ButtonSave As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelAccountManagerDetailsTitle As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelClientManagement As DevExpress.XtraEditors.LabelControl
    Friend WithEvents XtraTabControl1 As DevExpress.XtraTab.XtraTabControl
    Friend WithEvents TabPageDetails As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents TabPageClients As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents PanelDetails As System.Windows.Forms.Panel
    Friend WithEvents TableLayoutPanelDetails As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents LabelAccountManagerDetailsInfo As DevExpress.XtraEditors.LabelControl
    Friend WithEvents PanelAccountManagerDetails As System.Windows.Forms.Panel
    Friend WithEvents LabelUser As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkUser As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelFirstName As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditFirstName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelLastName As DevExpress.XtraEditors.LabelControl
    Friend WithEvents CheckEditDormant As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents TextEditLastName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelEmail As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditEmail As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelCode As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditCode As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PanelClients As System.Windows.Forms.Panel
    Friend WithEvents TableClientAccountManager As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents LabelClientManagementInfo As DevExpress.XtraEditors.LabelControl
    Friend WithEvents GroupControlContracts As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditSearchLinkedContracts As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearchLinkedContracts As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearchLinkedContracts As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents GridLinkedContracts As System.Windows.Forms.DataGridView
    Friend WithEvents ContractNumberColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents GroupControlClientAccountManager As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelControl14 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditSearchClientAccountManager As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearchClientAccountManager As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearchClientAccountManager As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents ButtonRemoveClientAccountManager As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonAddClientAccountManager As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GridClientAccountManager As System.Windows.Forms.DataGridView
    Friend WithEvents ClientNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents FromColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents ToColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents CareTakerColumn As System.Windows.Forms.DataGridViewCheckBoxColumn
    Friend WithEvents TabPageBudgets As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents PanelBudgets As System.Windows.Forms.Panel
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents GroupControlBudget As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditSearchBudget As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearchBudget As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearchBudget As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents ButtonDeleteBudget As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonAddBudget As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GridBudget As System.Windows.Forms.DataGridView
    Friend WithEvents LabelControl9 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl10 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents FiscalNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents BudgetColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents ButtonEditBudget As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonPermissions As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents TabPageBrands As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel1 As Panel
    Friend WithEvents TableLayoutPanel2 As TableLayoutPanel
    Friend WithEvents LabelControl4 As LabelControl
    Friend WithEvents LabelControl5 As LabelControl
    Friend WithEvents GroupControl1 As GroupControl
    Friend WithEvents LabelControl3 As LabelControl
    Friend WithEvents TextEditSearchBrandAccountManager As TextEdit
    Friend WithEvents PictureClearSearchBrandAccountManager As PictureEdit
    Friend WithEvents PictureAdvancedSearchBrandAccountManager As PictureEdit
    Friend WithEvents ButtonRemoveBrandAccountManager As SimpleButton
    Friend WithEvents ButtonAddBrandAccountManager As SimpleButton
    Friend WithEvents GridBrandAccountManager As DataGridView
    Friend WithEvents BrandNameColumn As DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn2 As DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewTextBoxColumn3 As DataGridViewTextBoxColumn
    Friend WithEvents DataGridViewCheckBoxColumn1 As DataGridViewCheckBoxColumn
End Class
