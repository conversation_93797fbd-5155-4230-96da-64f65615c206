﻿using System;
using System.ComponentModel;

namespace Framework.Controls.Data
{
    public partial class CheckDataBox : DataBox
    {

        #region Startup

        public CheckDataBox()
        {
            InitializeComponent();
            ErrorLabel = errorLabel1;
            GetErrorMessageMethod = GetErrorMessage;
        }

        private string GetErrorMessage()
        {
            // No errors exist for this control.
            return string.Empty;
        }

        private void CheckDataBox_Load(object sender, EventArgs e)
        {
            checkBox1.CheckedChanged += CheckBox1_CheckedChanged;
        }

        #endregion


        #region Value

        public bool Checked
        {
            get { return checkBox1.Checked; }
            set
            {
                if (checkBox1.Checked != value)
                {
                    checkBox1.Checked = value;
                }
            }
        }

        private void CheckBox1_CheckedChanged(object sender, EventArgs e)
        {
            Value = checkBox1.Checked;
        }

        protected override void OnValueChanged()
        {
            base.OnValueChanged();
            if (Value is bool)
            {
                Checked = (bool)Value;
            }
        }

        [Browsable(true)]
        [EditorBrowsable(EditorBrowsableState.Always)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        [Bindable(true)]
        public override string Text
        {
            get { return checkBox1.Text; }
            set
            {
                if (checkBox1.Text != value)
                {
                    checkBox1.Text = value;
                }
            }
        }

        #endregion

    }
}
