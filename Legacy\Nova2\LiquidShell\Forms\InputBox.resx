<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="ImageList24x24.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>144, 17</value>
  </metadata>
  <data name="ImageList24x24.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj0yLjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAACg
        CgAAAk1TRnQBSQFMAgEBAgEAATwBAAE8AQABFAEAARQBAAT/AREBAAj/AUIBTQE2BwABNgMAASgDAAFQ
        AwABFAMAAQEBAAEQBQABgAEMIAABWgFrATkBZwE5AWcBWgFrFAABWgFrAVoBawFaAWsBWgFrAVoBawFa
        AWsBWgFrCAABWgFrAVoBawFaAWsBewFrAVoBawFaAWsBWgFrXgABWgFrAVoBawF8AW8BfAFvAVoBawE5
        AWcSAAE5AWcBWgFnAZwBcwH3AWoBOAFvAZwBcwE5AWcBWgFrBAABWgFrATkBZwF7AW8BWQFvAbUBZgGc
        AXMBWgFrATkBZwFaAWtaAAFaAWsBOQFnAZwBcwE1AV8BFAFbAb4BdwE5AWcBWgFrDgABWgFrATkBZwF7
        AW8BEAFaAWMBPAHFAUABtQFmAb0BcwE5AWcBWgFrAgABOQFnAZwBbwH3AWoBCAFJAUIBOAGtAVEBewFv
        AVoBawFaAWtYAAFaAWsBGAFjAZwBdwHxAVIBJAEuAQIBKgETAVsBnQF3ATkBZw4AATkBZwG9AXcBMQFe
        AUIBPAFCATwBQgE8AaQBQAHWAWoBvQFzATkBZwE5AWcBnAFzATkBbwHnAUgBQgE8AUIBPAFBATwBjAFV
        AZwBcwFaAWtWAAFaAWsBGAFjAd8BfwE0AV8BIgEqAUQBLgFEAS4BIgEqAZwBcwF8AW8BOQFnDAABOQFn
        AVoBcwFBAUABQQFAAWMBQAFjAUABQQFAAYQBRAH3AW4BnAFvAXsBawGbAXcBBwFJASEBPAFiAUABYwFA
        AWIBQAEhATwBlAFmAXsBa1QAAVoBawE5AWcBvQF3ATMBWwFCAS4BZAEyAWQBMgFkATIBQgEuAe0BSgGc
        AXMBOgFnAVoBawoAATkBZwF7AXcB5gFMAUEBRAFiAUQBYwFEAWIBRAFBAUQBpQFIARgBcwGcAXsB5wFM
        ASEBQAFiAUQBYwFEAWMBRAFCAUQBpAFIAfcBbgF7AWtSAAFaAWsBOgFnAZwBcwEzAVsBYgEuAYQBMgGE
        ATYBhAE2AYQBNgGEATYBhAE2AXYBZwF7AW8BOQFnCgABWgFrAb0BdwH3AW4BxQFMAUEBSAFjAUgBYwFI
        AWMBSAFCAUQB5wFQASkBVQEhAUQBYgFIAWMBSAFjAUgBQQFIAYMBSAGUAWYB3gF3AXsBb1IAATkBZwGc
        AXMBUgFbAaUBOgGkATYBpAE6AaQBOgGkAToBpAE6AaQBOgGDATYB6gFGAZoBbwE6AWsBWgFrCgABWgFr
        Ab0BdwEYAW8BxQFQAUEBSAGDAUwBgwFMAWMBTAFiAUgBQQFIAWMBTAGDAUwBgwFMAUEBSAFBAUgB1gFq
        Ab0BdwFaAWsBWgFrUAABGAFjAb4BdwF0AWMBxAE6AaQBOgHEAToBxAE6AaQBOgGjATYBpAE6AcQBOgHE
        AToBpAE6AVMBXwG+AXcBGAFjCgABWgFrAVoBawHeAXsBWgFzAUEBTAFBAUwBgwFQAYMBUAGDAVABgwFQ
        AYMBUAGDAVABQQFMAUEBTAEYAW8B3gJ7AW8BWgFrUgABOQFnAboBcwHEAToBwwE6AcQBPgHEAT4BwwE6
        AeYBQgFQAVsBxAE6AcQBPgHEAT4BwwE6AeUBPgG7AXMBWwFrATkBZwwAAVoBawHeAXcB3gF7AcUBVAFB
        AVABgwFQAYMBUAGDAVABgwFQAWIBUAGkAVQBWQF3Af8BewE5AWdWAAE6AWsBuwF3AQcBRwHjAToB5AE+
        AeMBPgHlAUIBlgFrAf8BfwFNAVMB4gE6AeQBPgHkAT4B4gE6ASsBTwH+AX8BOQFnAVoBawgAAVoBawE5
        AWcBnAFzAZwBewEpAV0BQQFUAYMBVAGDAVQBgwFUAYMBVAFiAVQB5wFYARgBdwG9AXcBWgFnAVoBa1QA
        AVoBawG9AXcBtwFrAQcBRwHiAToBBAFDAZYBawG+AXsBnAFzAbsBdwEFAUMB4wE+AQQBQwEEAUMB4gE6
        AXEBXwHeAXsBWgFrAVoBawQAAVoBawE6AWcBvQFzAVkBdwEIAV0BQQFUAWMBWAGDAVgBgwFYAYMBWAGD
        AVgBYwFYAUIBVAHGAVgBGAF3Ab0BdwFaAWsBWgFrVAABWgFrAb0BdwG3AW8BTgFXAbcBbwHeAnsBbwFa
        AWsB3gF7AbgBbwHiAT4BAwFDAQQBQwEDAUMB4gE+AZUBawG9AXcBOgFnAVoBawIAATkBZwG9AXMBOQF3
        AQgBYQFBAVQBgwFcAYMBXAGDAVwBYgFYAWIBWAGDAVwBgwFcAYMBXAFCAVgBpQFcAfcBdgG9AXcBWgFr
        AVoBa1IAAVoBawFaAWsBnAFzAd0BewGdAXMBWwFrAVoBawIAAVoBawH/AX8BcAFfAeEBQgEDAUcBAwFH
        AQIBQwEEAUcBtgFrAb0BdwE5AWcBOQFnAb0BcwE5AXcBCAFlAWIBXAGDAVwBgwFcAYMBXAFBAVwBhAFg
        AcUBYAFBAVwBgwFcAYMBXAGDAVwBYgFcAaUBYAHWAXYBvQF3AVoBa2QAAXsBbwH8AXsBKAFTAQIBQwED
        AUcBAwFHAQIBRwEmAUsB2QFzAVoBawEYAWMBnAF/ASkBZQFBAVwBgwFgAYMBYAGDAWABQQFgAWEBYAFa
        AXsB/wF/AUEBYAFBAWABgwFgAYMBYAGDAWABYgFgAcYBYAE5AnsBb2YAAb0BdwHYAXMBJQFPAQIBRwEC
        AUsBAQFHAQIBRwG1AWsBewFvAVoBawFaAXsBYgFkAWIBYAGDAWQBgwFkAWIBYAGDAWQB1QF2Ad4BewHe
        AXsBWgF7AcUBZAFBAWABggFgAYMBZAFiAWABYgFkAbQBdgGcAW9mAAFaAWsB3gF7AbQBawEjAU8BAQFH
        ASUBTwGRAWcB3AF3AXsBbwFaAWsBvQF/Ae8BcQFjAWQBYgFkAUEBZAGDAWQBtQF2Ad4BewFaAWsBWgFr
        Ad4BewEYAXsBxQFoAUEBZAFiAWQBYgFkAYsBbQGbAnsBb2YAAVoBawF7AW8B3QF7AbYBbwGQAWMB2AFz
        Ad0CewFvAVoBawFaAWsBnAFzAZwBewHOAXEBQgFoAUIBaAG1AXYB3gJ7AW8BWgFrAgABewFvAb0BewEX
        AXsBxQFsASABZAFrAXEBWgF7Ab0BdwFaAWtoAAFaAWsBewFvAb0BdwHeAXsBnAFzAXsBbwFaAWsEAAFa
        AWsBnAFzAb0BfwEYAXsBWgF7Ad4BfwF7AW8BWgFrBAABWgFrAXsBbwHeAnsBfwH3AXoBnAF/Ab0BdwFa
        AWsBWgFrbAABOQFnATkBZwFaAWsMAAF7AW8BvQF3AZwBcwFaAWsMAAFaAWsBnAFzAb0BdwF7AW9WAAFC
        AU0BPgcAAT4DAAEoAwABUAMAARQDAAEBAQABAQUAAfAXAAP/AQAB/gEfAfgBDwEBBwAB/AEPAfgBBggA
        AfgBBwHwAQIIAAHwAQcB8AkAAeABAwHwCQABwAEBAfAJAAGAAQEB8AkAAYABAAH4CwAB+AEAAQEJAAF+
        AQABBwkAATwBAAEDCQABGAEAAQEHAAGAAQABCAkAAoAKAAH/AcAKAAH/AeAKAAH/AeAKAAH/AeABAAEC
        CAAB/wHwARgBBggAAf8B/AF+AR8BhwcACw==
</value>
  </data>
  <metadata name="ErrorManager.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
</root>