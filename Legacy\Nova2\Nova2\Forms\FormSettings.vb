Public Class FormSettings

    Private SettingTable As DataTable

#Region "Event Handlers"

    Private Sub FormSettings_Load(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles MyBase.Load
        FetchAndBindData()
        InitialiseBudgets()
        UnhideTabs()
    End Sub

    Private Sub ButtonSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonSave.Click
        SaveAndClose()
    End Sub

    Private Sub ButtonCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonCancel.Click
        Close()
    End Sub

    Private Sub TextEditWriteContract_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles TextEditWriteContract.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Update the data object.
        SettingTable.Select("SettingName = 'WriteContract-Percent'")(0).Item("SettingValue") = CDec(ValidatedControl.EditValue)

    End Sub

    Private Sub TextEditCareTakeContract_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles TextEditCareTakeContract.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Update the data object.
        SettingTable.Select("SettingName = 'CareTakeContract-Percent'")(0).Item("SettingValue") = CDec(ValidatedControl.EditValue)

    End Sub

    Private Sub TextEditBAccountInactivityPeriod_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles TextEditBAccountInactivityPeriod.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Update the data object.
        SettingTable.Select("SettingName = 'BAccountInactivityPeriod-Weeks'")(0).Item("SettingValue") = CInt(ValidatedControl.EditValue)

    End Sub

    Private Sub TextEditWriteContract_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles TextEditWriteContract.EditValueChanged
        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)
        ' Clear any possible errors.
        ResetError(ValidatedControl)
    End Sub

    Private Sub TextEditCareTakeContractPerWeek_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles TextEditCareTakeContract.EditValueChanged
        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)
        ' Clear any possible errors.
        ResetError(ValidatedControl)
    End Sub

    Private Sub TextEditBAccountInactivityPeriod_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles TextEditBAccountInactivityPeriod.EditValueChanged
        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)
        ' Clear any possible errors.
        ResetError(ValidatedControl)
    End Sub

    Private Sub TextEditQuarterRatio_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles TextEditQuarter1Ratio.EditValueChanged, _
    TextEditQuarter2Ratio.EditValueChanged, _
    TextEditQuarter3Ratio.EditValueChanged, _
    TextEditQuarter4Ratio.EditValueChanged, _
    TextEditQuarterRatiosTotal.EditValueChanged

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)
        ' Clear any possible errors.
        ResetError(ValidatedControl)
        ' Update the Total label.
        If Not String.Compare(ValidatedControl.Name, "TextEditQuarterRatiosTotal") = 0 Then
            ' This control is not the total box. Update the total box.
            UpdateQuarterRatioTotal()
        End If

    End Sub

    Private Sub TextEditQuarterRatio_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles TextEditQuarter1Ratio.Validated, _
    TextEditQuarter2Ratio.Validated, _
    TextEditQuarter3Ratio.Validated, _
    TextEditQuarter4Ratio.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Update the data object.
        SettingTable.Select("SettingName = '" & ValidatedControl.Tag.ToString & "'")(0).Item("SettingValue") = CDec(ValidatedControl.EditValue)

    End Sub

    Private Sub TextEditQuarterRatiosTotal_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles TextEditQuarterRatiosTotal.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If CDec(ValidatedControl.EditValue) <> 100 Then
            LiquidAgent.ControlValidation(ValidatedControl, _
            "Quarterly commission ratios must add up to 100.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

#End Region

#Region "Private Methods"

    Private Sub UpdateQuarterRatioTotal()
        ' Add all ratio's for each quarter and display the total in the label.

        ' Create variables to hold the input values.
        Dim Q1 As Decimal
        Dim Q2 As Decimal
        Dim Q3 As Decimal
        Dim Q4 As Decimal

        ' Conver the user input to decimal values.
        Decimal.TryParse(TextEditQuarter1Ratio.EditValue, Q1)
        Decimal.TryParse(TextEditQuarter2Ratio.EditValue, Q2)
        Decimal.TryParse(TextEditQuarter3Ratio.EditValue, Q3)
        Decimal.TryParse(TextEditQuarter4Ratio.EditValue, Q4)

        ' Update the label.
        TextEditQuarterRatiosTotal.EditValue = Q1 + Q2 + Q3 + Q4

    End Sub

    Private Sub FetchAndBindData()

        ' Get the setting data from the database.
        SettingTable = Settings.GetSettings(My.Settings.DBConnection, Me)

        ' Bind the data to the controls.
        If Not IsNothing(SettingTable) Then
            TextEditWriteContract.EditValue = CDec(SettingTable.Select("SettingName = 'WriteContract-Percent'")(0).Item("SettingValue"))
            TextEditCareTakeContract.EditValue = CDec(SettingTable.Select("SettingName = 'CareTakeContract-Percent'")(0).Item("SettingValue"))
            TextEditBAccountInactivityPeriod.EditValue = CInt(SettingTable.Select("SettingName = 'BAccountInactivityPeriod-Weeks'")(0).Item("SettingValue"))
            TextEditQuarter1Ratio.EditValue = CDec(SettingTable.Select("SettingName = 'Quarter1CommissionRatio'")(0).Item("SettingValue"))
            TextEditQuarter2Ratio.EditValue = CDec(SettingTable.Select("SettingName = 'Quarter2CommissionRatio'")(0).Item("SettingValue"))
            TextEditQuarter3Ratio.EditValue = CDec(SettingTable.Select("SettingName = 'Quarter3CommissionRatio'")(0).Item("SettingValue"))
            TextEditQuarter4Ratio.EditValue = CDec(SettingTable.Select("SettingName = 'Quarter4CommissionRatio'")(0).Item("SettingValue"))
        End If

    End Sub

    Private Sub InitialiseBudgets()

        ' Create a new data set for budgets.
        Dim BudgetsDataSet As New DataSetBudgets

        '' Create a new binding source for the grid.
        'Dim BudgetsGridBindingSource As New BindingSource(BudgetsDataSet, BudgetsDataSet.Purchase.TableName)
        'GridBudget.AutoGenerateColumns = False
        'GridBudget.DataSource = BudgetsGridBindingSource

        '' Setup grid manager.
        'Dim BudgetsGridManager As New GridManager _
        '(GridBudget, _
        'TextEditSearchBudget, _
        'BudgetsGridBindingSource, _
        'String.Empty, _
        'PictureAdvancedSearchBudget, _
        'PictureClearSearchBudget, _
        'Nothing, _
        'Nothing)

    End Sub

    Private Sub UnhideTabs()

        ' Display tabs which the current user has permission to view.
        If My.User.IsInRole("sales_manager") Then
            TabPageCommission.PageVisible = True
        End If

        ' If no tabs are visible after all of the above, show a dummy placeholder tab.
        Dim AllTabsHidden As Boolean = True
        For Each TabPage As DevExpress.XtraTab.XtraTabPage In TabSet.TabPages
            If TabPage.PageVisible Then
                AllTabsHidden = False
                Exit For
            End If
        Next
        If AllTabsHidden Then
            TabPageNoSettings.PageVisible = True
            ButtonSave.Visible = False
        End If

    End Sub

    Private Sub ResetError(ByVal ControlToReset As Control)
        LiquidAgent.ControlValidation(ControlToReset, String.Empty)
    End Sub

#End Region

#Region "Protected Methods"

    Protected Overrides Sub Save()
        ' Save.
        Settings.SetSettings(My.Settings.DBConnection, SettingTable)
    End Sub

#End Region

End Class
