﻿Imports System.Security.Principal

Public Class Contract
    Inherits BaseDataObject
    Private _Row As DataSetContract.ContractRow = Nothing
    Private _Binder As BindingSource = Nothing
    Private _PONumberBindingSource As BindingSource
    Private _MiscellaneousChargeBindingSource As BindingSource
    Private _MediaCostBindingSource As BindingSource
    Private _InvioiceNumberBindingSource As BindingSource
    Private _ResearchCategoryBindingSource As BindingSource
    Private _SaveInfo As String = String.Empty
    Private _Periods As DataTable
    Private _BillingInstruction As DataSetContract.BillingInstructionDataTable
    Public ConsumingForm As LiquidShell.BaseForm

    Public Event Saved()

    Public Enum ContractType
        None
        Rental
        Research
        Distribution
        InstallationOnly
    End Enum

#Region "Properties"

    Public Shadows ReadOnly Property ConnectionString As String
        Get
            Return MyBase.ConnectionString
        End Get
    End Property

    Public ReadOnly Property Type As ContractType
        Get
            If String.Compare(Row.ContractType, "Rental") = 0 Then
                Return ContractType.Rental
            ElseIf String.Compare(Row.ContractType, "Research") = 0 Then
                Return ContractType.Research
            ElseIf String.Compare(Row.ContractType, "Distribution") = 0 Then
                Return ContractType.Distribution
            ElseIf String.Compare(Row.ContractType, "InstallationOnly") = 0 Then
                Return ContractType.InstallationOnly
            Else
                Return ContractType.None
            End If
        End Get
    End Property

    Public ReadOnly Property Row As DataSetContract.ContractRow
        Get
            Return _Row
        End Get
    End Property

    Public ReadOnly Property Binder As BindingSource
        Get
            Return _Binder
        End Get
    End Property

    Public ReadOnly Property ContractID As Guid
        Get
            If Row.RowState = DataRowState.Deleted Then
                Return Row("ContractID", DataRowVersion.Original)
            Else
                Return _Row.ContractID
            End If
        End Get
    End Property

    Public ReadOnly Property AccountManagerID As Integer
        Get
            If Row.RowState = DataRowState.Deleted Then
                Return Row("AccountManagerID", DataRowVersion.Original)
            Else
                Return _Row.AccountManagerID
            End If
        End Get
    End Property

    Public ReadOnly Property ContractNumber As String
        Get
            If Row.RowState = DataRowState.Deleted Then
                ' When deleting a contract, this property needs to be read so that an audit log entry can be created. But
                ' if the row is deleted then we can't access its data, so we'll use this method to access the contract number
                ' from the original version of the row.
                Return Row("ContractNumber", DataRowVersion.Original).ToString
            Else
                Return Row.ContractNumber
            End If
        End Get
    End Property

    Public Property ProjectName As String
        Get
            If String.IsNullOrEmpty(_Row.ProjectName) Then
                Return "(none specified)"
            Else
                Return _Row.ProjectName.Replace("&", "&&")
            End If
        End Get
        Set(value As String)
            _Row.ProjectName = value
        End Set
    End Property
    Public Property DemoProvider As String
        Get
            If String.IsNullOrEmpty(_Row.DemoProvider) Then
                Return "(none specified)"
            Else
                Return _Row.DemoProvider.Replace("&", "&&")
            End If
        End Get
        Set(value As String)
            _Row.DemoProvider = value
        End Set
    End Property
    Public Property DemoOwner As String
        Get
            If String.IsNullOrEmpty(_Row.DemoOwner) Then
                Return "(none specified)"
            Else
                Return _Row.DemoOwner.Replace("&", "&&")
            End If
        End Get
        Set(value As String)
            _Row.DemoOwner = value
        End Set
    End Property

    Public Property SpecialConditions As String
        Get
            If String.IsNullOrEmpty(_Row.SpecialConditions) Then
                Return String.Empty
            Else
                Return _Row.SpecialConditions
            End If
        End Get
        Set(value As String)
            _Row.SpecialConditions = value
        End Set
    End Property

    Public Property Notes As String
        Get
            If String.IsNullOrEmpty(_Row.ContractNotes) Then
                Return String.Empty
            Else
                Return _Row.ContractNotes
            End If
        End Get
        Set(value As String)
            _Row.ContractNotes = value
        End Set
    End Property

    Public ReadOnly Property ClientName As String
        Get
            Return _Row.ClientName.Replace("&", "&&")
        End Get
    End Property

    Public ReadOnly Property Brands As String
        Get
            Return _Row.Brands.Replace("&", "&&")
        End Get
    End Property

    Public ReadOnly Property ResearchCategories As String
        Get
            If String.IsNullOrEmpty(_Row.ResearchCategories) Then
                Return "(none specified)"
            Else
                Return _Row.ResearchCategories.Replace("&", "&&")
            End If
        End Get
    End Property

    Public ReadOnly Property MediaServices As String
        Get
            Return _Row.MediaServices.Replace("&", "&&")
        End Get
    End Property

    Public ReadOnly Property FirstWeek As Date
        Get
            Try
                ' If this is a research contract then the FirstWeek might not be a Monday. Return the first Monday after FirstWeek.
                Dim StartDate As Date = _Row.FirstWeek
                While Not StartDate.DayOfWeek = DayOfWeek.Monday
                    StartDate = StartDate.AddDays(1)
                End While
                Return StartDate
            Catch ex As StrongTypingException
                Return New Date(1000, 1, 1)
            End Try
        End Get
    End Property

    Public ReadOnly Property FirstWeekString As String
        Get
            If Type = ContractType.Research Then
                Return String.Empty
            Else
                If FirstWeek.Year = 1000 Then
                    ' There is no first week, meaning the contract isn't cancelled.
                    Return String.Empty
                Else
                    Return FirstWeek.ToString("d MMMM yyyy")
                End If
            End If
        End Get
    End Property

    Public ReadOnly Property LastWeek As Date
        Get
            Try
                ' If this is a research contract then the LastWeek might not be a Monday. Return the first Monday after LastWeek.
                Dim EndDate As Date = _Row.LastWeek

                ' If this is a research contract we need to find the last Monday of the months covered by the contract.
                If Type = ContractType.Research And EndDate.Day = 1 Then
                    ' The LastWeek value will be the first day of the last month selected for the contract. So to find the actual
                    ' Monday of the last week of the last month of the contract, we can add one month to the last week value, subtract
                    ' one day to get the very last day of the month, then start subtracting one day at a time until we find the last
                    ' Monday.
                    EndDate = EndDate.AddMonths(1)
                    EndDate = EndDate.AddDays(-1)
                    ' Now we can look for the last Monday.
                    While Not EndDate.DayOfWeek = DayOfWeek.Monday
                        EndDate = EndDate.AddDays(-1)
                    End While
                End If

                Return EndDate
            Catch ex As StrongTypingException
                Return New Date(1000, 1, 1)
            End Try
        End Get
    End Property

    Public ReadOnly Property LastWeekString As String
        Get
            If Type = ContractType.Research Then
                Return String.Empty
            Else
                If LastWeek.Year = 1000 Then
                    ' There is no last week, meaning the contract isn't cancelled.
                    Return String.Empty
                Else
                    Return LastWeek.ToString("d MMMM yyyy")
                End If
            End If
        End Get
    End Property

    Public ReadOnly Property EndDateString As String
        Get
            If Type = ContractType.Research Then
                Return String.Empty
            Else
                If LastWeek.Year = 1000 Then
                    ' There is no last week, meaning the contract isn't cancelled.
                    Return String.Empty
                Else
                    ' The End Date is the week after the last week (i.e. the week hat the ads come down.)
                    Dim EndDate As Date = DateAdd(DateInterval.WeekOfYear, 1, LastWeek)
                    Return EndDate.ToString("d MMMM yyyy")
                End If
            End If
        End Get
    End Property

    Public ReadOnly Property CreatedBy As String
        Get
            Return _Row.CreatedBy
        End Get
    End Property

    Public ReadOnly Property CreationDate As Date
        Get
            Try
                Return _Row.CreationDate
            Catch ex As StrongTypingException
                Return New Date(1000, 1, 1)
            Catch ex As InvalidCastException
                Return New Date(1000, 1, 1)
            End Try
        End Get
    End Property

    Public ReadOnly Property CreationDateString As String
        Get
            If CreationDate.Year = 1000 Then
                ' There is no creation date, meaning the contract hasn't been created yet.
                Return String.Empty
            Else
                Return CreationDate.ToString("d MMMM yyyy 'at' HH:mm")
            End If
        End Get
    End Property

    Public Property ContractDate As Date
        Get
            Try
                ' First try and return the specific contract date selected by the user.
                Return _Row.ContractDate
            Catch ex As StrongTypingException
                ' User selected date failed.
                If Not (Type = ContractType.Rental Or Type = ContractType.Research Or Type = ContractType.InstallationOnly) Then
                    ' This is not a rental or research contract, return a bogus date to show that no date exists.
                    Return New Date(1000, 1, 1)
                Else
                    ' This is a rental or research contract, check for signature.
                    If Signed Then
                        ' The contract is signed. Return the signature date.
                        Return SignDate
                    Else
                        ' The contract is not signed. Return the commencement date of the contract.
                        Return FirstWeek
                    End If
                End If
            End Try
        End Get
        Set(value As Date)
            _Row.ContractDate = value
        End Set
    End Property

    Public ReadOnly Property ContractDateString As String
        Get
            If ContractDate.Year = 1000 Then
                Return "Select..."
            Else
                Return ContractDate.ToString("d MMMM yyyy")
            End If
        End Get
    End Property

    Public ReadOnly Property Signed As Boolean
        Get
            If Not _Row.RowState = DataRowState.Deleted Then
                Return _Row.Signed
            Else
                Return False
            End If
        End Get
    End Property

    Public ReadOnly Property SignedBy As String
        Get
            Return _Row.SignedBy
        End Get
    End Property

    Public ReadOnly Property isReplacement As Boolean
        Get
            Return _Row.isReplacement
        End Get
    End Property

    Public ReadOnly Property ClonedContractNumber As String
        Get
            Return If(_Row.ClonedContractNumber, "None")
        End Get
    End Property

    Public ReadOnly Property SignDate As Date
        Get
            Try
                Return _Row.SignDate
            Catch ex As StrongTypingException
                Return New Date(1000, 1, 1)
            End Try
        End Get
    End Property

    Public ReadOnly Property SignDateString As String
        Get
            If SignDate.Year = 1000 Then
                ' There is no sign date, meaning the contract isn't signed yet.
                Return String.Empty
            Else
                Return SignDate.ToString("d MMMM yyyy")
            End If
        End Get
    End Property

    Public ReadOnly Property SignDateTimeString As String
        Get
            If SignDate.Year = 1000 Then
                ' There is no sign date, meaning the contract isn't signed yet.
                Return String.Empty
            Else
                Return SignDate.ToString("d MMMM yyyy 'at' HH:mm")
            End If
        End Get
    End Property

    Public ReadOnly Property Cancelled As Boolean
        Get
            Return _Row.Cancelled
        End Get
    End Property

    Public ReadOnly Property CancelledBy As String
        Get
            Try
                Return _Row.CancelledBy
            Catch ex As StrongTypingException
                Return String.Empty
            End Try
        End Get
    End Property


    Public ReadOnly Property CancelDate As Date
        Get
            Try
                Return _Row.CancelDate
            Catch ex As StrongTypingException
                Return New Date(1000, 1, 1)
            End Try
        End Get
    End Property

    Public ReadOnly Property CancelDateString As String
        Get
            If CancelDate.Year = 1000 Then
                ' There is no cancel date, meaning the contract isn't cancelled.
                Return String.Empty
            Else
                Return CancelDate.ToString("d MMMM yyyy 'at' HH:mm")
            End If
        End Get
    End Property
    Public ReadOnly Property ClientAccountManager As String
        Get
            Return _Row.ClientAccountManager
        End Get
    End Property


    Public Property ContractClassificationId As Integer
        Get
            Return _Row.ContractClassificationId
        End Get
        Set(value As Integer)
            _Row.ContractClassificationId = value
        End Set
    End Property
    Public Property ContractProposalHeatName As String
        Get
            Return _Row.ContractProposalHeatName
        End Get
        Set(value As String)
            _Row.ContractProposalHeatName = value
        End Set
    End Property
    Public Property ContractClassificationName As String
        Get
            Return _Row.ContractClassificationName
        End Get
        Set(value As String)
            _Row.ContractClassificationName = value
        End Set
    End Property

    Public ReadOnly Property PONumberBindingSource() As BindingSource
        Get
            Return _PONumberBindingSource
        End Get
    End Property

    Public ReadOnly Property MiscellaneousChargeBindingSource() As BindingSource
        Get
            Return _MiscellaneousChargeBindingSource
        End Get
    End Property
    Public ReadOnly Property MediaCostBindingSource() As BindingSource
        Get
            Return _MediaCostBindingSource
        End Get
    End Property
    Public ReadOnly Property InvoiceNumberBindingSource() As BindingSource
        Get
            Return _InvioiceNumberBindingSource
        End Get
    End Property

    Public ReadOnly Property InLockPeriod() As Boolean
        Get

            ' Get the first week of the contract.
            Dim FirstWeekDate As Date
            If Year(FirstWeek) = 1 Then
                ' This date will be out of range. Use a fictitious past date.
                FirstWeekDate = FirstWeek.AddYears(2100)
            Else
                FirstWeekDate = FirstWeek
            End If

            ' Get the timing settings for locked contracts from the database.
            Dim LockSettings As DataTable = Settings.GetSettings(ConnectionString, ConsumingForm)
            Dim LockDays As Integer = LockSettings.Select("SettingName = 'ContractLockDaysLocked'")(0).Item("SettingValue")
            Dim StartHourOfDay As Integer = LockSettings.Select("SettingName = 'ContractLockStartHourOfDay'")(0).Item("SettingValue")
            Dim StartMinuteOfHour As Integer = LockSettings.Select("SettingName = 'ContractLockStartMinuteOfHour'")(0).Item("SettingValue")
            Dim DaysPriorToContractFirstWeek As Integer = LockSettings.Select("SettingName = 'ContractLockDaysPriorToContractFirstWeek'")(0).Item("SettingValue")

            ' Get the current time.
            Dim ServerTime As Date = Settings.GetServerTime(ConnectionString, ConsumingForm)

            ' Calculate the number of minutes before the first monday of the contract that the contract will become locked.
            Dim Minutes As Integer = DaysPriorToContractFirstWeek * 24 * 60
            Minutes -= StartHourOfDay * 60
            Minutes -= StartMinuteOfHour

            ' Calculate the exact start date and time of the lock period.
            Dim LockPeriodStart As Date = DateAdd(DateInterval.Minute, -Minutes, FirstWeekDate)
            Dim LockPeriodEnd As Date = DateAdd(DateInterval.DayOfYear, LockDays, LockPeriodStart)

            ' Test if the contract is currently in the lock period or not.
            If ServerTime >= LockPeriodStart AndAlso ServerTime <= LockPeriodEnd Then
                ' The contract is in the lock period.
                Return True
            Else
                ' The contract is not in the lock period.
                Return False
            End If

        End Get
    End Property

    Public ReadOnly Property PermissionToModify As Boolean
        Get

            If UserMayModifySignedContract(ContractID) Then
                Return True
            End If

            ' The SQL statement to execute.
            Dim Statement As String = String.Empty
            If Not Row.RowState = DataRowState.Added Then
                Statement = "SELECT ContractID FROM Sales.vContractsByPermission_EditMyContracts WHERE (ContractID = '" & ContractID.ToString & "')"
            Else
                Statement = "SELECT AccountManagerID FROM Sales.vAccountManagersByPermission_EditMyContracts WHERE (AccountManagerID = " & AccountManagerID.ToString & ")"
            End If

            ' Get the result of the query and make a variable indicating whether or not the current user has permission to edit this contract.
            Dim Result As Object = LiquidShell.LiquidAgent.GetSqlScalarValue(ConnectionString, Statement, String.Empty)
            If IsNothing(Result) = False Then
                Return True
            Else
                Return False
            End If

        End Get
    End Property

    Public Function UserMayModifySignedContract(IdOfContract As Guid) As Boolean

        Dim permissiongranted As Boolean = False
        Dim userhasneededrolemembership = My.User.IsInRole("signedcontractmodifier")
        Dim errormessage As String = String.Empty
        Dim contractstatus As Universal.Entities.ContractStatus = DataService.SalesServices.GetContractStatus(errormessage, IdOfContract)

        If String.IsNullOrEmpty(errormessage) = False Then
            MessageBox.Show(errormessage, "Error While Checking Contract Status")
        Else
            If userhasneededrolemembership AndAlso contractstatus.ApprovedByFinance = False Then
                permissiongranted = True
            End If
        End If

        Return permissiongranted

    End Function

    Public ReadOnly Property ReadOnlyContract As Boolean
        Get

            ' Between the role membership, lock period status and permission granted, decide if the contract is read-only or not.
            If Signed AndAlso InLockPeriod Then
                If My.User.IsInRole("ops_contractmodifier") Then
                    ' The contract is locked but the current user is in ops with the correct permissions.
                    Return False
                Else
                    ' The contract is locked and the user has no special permission to modify locked contracts.
                    Return True
                End If
            Else
                Return Not PermissionToModify
            End If

        End Get
    End Property

    Public ReadOnly Property ResearchCategoryBindingSource As BindingSource
        Get

            ' Create a SQL connection to connect to the database.
            Using SqlCon As New SqlClient.SqlConnection(ConnectionString)

                ' Create a table adapter to do our bidding.
                Dim ResearchCategoryAdapter As New DataSetContractTableAdapters.ResearchCategoryTableAdapter
                ResearchCategoryAdapter.Connection = SqlCon

                ' Refresh the data in the research category table.
                Dim ResearchCategoryTable As DataSetContract.ResearchCategoryDataTable = CType(Row.Table.DataSet, DataSetContract).ResearchCategory
                ResearchCategoryAdapter.Fill(ResearchCategoryTable, ContractID)

                ' Instantiate the binding source if it doesn't exist yet.
                If IsNothing(_ResearchCategoryBindingSource) Then
                    ' The research category binding source doesn't exist. Create it.
                    _ResearchCategoryBindingSource = New BindingSource(Binder, "Contract_ResearchCategory")
                End If

                ' Return the binding source.
                Return _ResearchCategoryBindingSource

                ' Destroy the table adapter. We don't need it anymore.
                ResearchCategoryAdapter.Dispose()

            End Using

        End Get
    End Property

    Public Property SaveInfo As String
        Get
            Return _SaveInfo
        End Get
        Set(value As String)
            _SaveInfo = value
            ' Raise the Saved event.
            If Not Row.RowState = DataRowState.Detached Then
                RaiseEvent Saved()
            End If
        End Set
    End Property

    Public ReadOnly Property RentalValue As Decimal
        Get

            ' Refresh child rows if necessary.
            Dim Children() As DataSetContract.BurstRow = Row.GetBurstRows
            Dim TotalLoadingFees As Decimal = LoadingFeeAmount

            If Children.Length = 0 Then
                ' Create a connection to retrieve the data.
                Using SqlCon As New SqlClient.SqlConnection("Data Source=primenovasql\Live;Initial Catalog=NovaDB;User ID=NovaApp;Password=******************;Connect Timeout=0")
                    ' Create a table adapter to do our bidding.
                    Dim Adapter As New DataSetContractTableAdapters.BurstTableAdapter
                    Adapter.Connection = SqlCon
                    ' Get the data from the database.
                    Dim ContractDataSet As DataSetContract = Row.Table.DataSet
                    Try
                        Adapter.Fill(ContractDataSet.Burst, ContractID, My.User.CurrentPrincipal.Identity.Name)
                    Catch ex As Exception
                        MessageBox.Show("Burst data retrieval failed.")
                        Return 0
                    Finally
                        Adapter.Dispose()
                    End Try
                End Using

                ' Add the rental of all the bursts in this contract.
                Dim TotalRental As Decimal = 0
                For Each BurstRow As DataSetContract.BurstRow In Children

                    ' Calculate the base rental amount first.
                    Dim BaseRental As Decimal = BurstRow.RentalRate * BurstRow.BillableStoreQty * BurstRow.BillableWeeks

                    ' Claculate the gross rental.
                    Dim GrossRental As Decimal = BaseRental + LoadingFeeAmount

                    ' Subtract the discount to get the net rental for this burst.
                    Dim BurstNetRental As Decimal = GrossRental * (100 - BurstRow.Discount) / 100

                    ' Add the net rental to the contract total.
                    TotalRental += BurstNetRental

                Next

                ' Return the sum of the rental of all bursts.
                Return TotalRental

            Else

                ' Add the rental of all the bursts in this contract.
                Dim TotalRental As Decimal = 0
                For Each BurstRow As DataSetContract.BurstRow In Children

                    ' Calculate the base rental amount first.
                    Dim BaseRental As Decimal = BurstRow.RentalRate * BurstRow.BillableStoreQty * BurstRow.BillableWeeks

                    ' Add loading fees to the rental if there are any.
                    Dim GrossRental As Decimal = BaseRental
                    If TotalLoadingFees > 0 Then
                        Dim LoadingFee As Decimal = 0
                        ' Add up all loading fees for this burst and add it to the rental.
                        Dim LoadingFeeRows() As DataSetContract.BurstLoadingFeeRow = BurstRow.GetBurstLoadingFeeRows
                        For Each LoadingFeeRow As DataSetContract.BurstLoadingFeeRow In LoadingFeeRows
                            LoadingFee += LoadingFeeRow.LoadingFeeAmount
                        Next
                        GrossRental += LoadingFee
                    End If

                    ' Subtract the discount to get the net rental for this burst.
                    Dim BurstNetRental As Decimal = GrossRental * (100 - BurstRow.Discount) / 100

                    ' Add the net rental to the contract total.
                    TotalRental += BurstNetRental

                Next

                ' Return the sum of the rental of all bursts.
                Return TotalRental

            End If

        End Get
    End Property

    Public ReadOnly Property LoadingFeeAmount As Decimal
        Get

            ' Get the loading fee table.
            Dim LoadingFeeTable As DataSetContract.BurstLoadingFeeDataTable = Row.Table.DataSet.Tables("BurstLoadingFee")

            ' Fill the table if necessary.
            If LoadingFeeTable.Rows.Count = 0 Then
                ' Create a connection to retrieve the data.
                Using SqlCon As New SqlClient.SqlConnection(ConnectionString)
                    ' Create a table adapter to do our bidding.
                    Dim Adapter As New DataSetContractTableAdapters.BurstLoadingFeeTableAdapter
                    Adapter.Connection = SqlCon
                    ' Get the data from the database.
                    Dim ContractDataSet As DataSetContract = Row.Table.DataSet
                    Try
                        Adapter.Fill(ContractDataSet.BurstLoadingFee, ContractID)
                    Catch ex As Exception
                        MessageBox.Show("Burst loading fee data retrieval failed." & vbCrLf & vbCrLf & LiquidShell.LiquidAgent.GetErrorMessage(ex))
                        Return 0
                    Finally
                        Adapter.Dispose()
                    End Try
                End Using
            End If

            ' Add together all loading fee amounts.
            Dim LoadingFeeTotal As Decimal = 0
            For Each Fee As DataSetContract.BurstLoadingFeeRow In LoadingFeeTable.Rows
                LoadingFeeTotal += Fee.LoadingFeeAmount
            Next

            ' Return the result.
            Return LoadingFeeTotal

        End Get
    End Property

    Public ReadOnly Property ResearchValue As Decimal
        Get

            ' Refresh child rows if necessary.
            Dim Children() As DataSetContract.ResearchCategoryRow = Row.GetResearchCategoryRows
            If Children.Length = 0 Then
                ' Create a connection to retrieve the data.
                Using SqlCon As New SqlClient.SqlConnection(ConnectionString)
                    ' Create a table adapter to do our bidding.
                    Dim Adapter As New DataSetContractTableAdapters.ResearchCategoryTableAdapter
                    Adapter.Connection = SqlCon
                    ' Get the data from the database.
                    Dim ContractDataSet As DataSetContract = Row.Table.DataSet
                    Try
                        Adapter.Fill(ContractDataSet.ResearchCategory, ContractID)
                    Catch ex As Exception
                        MessageBox.Show("Research category data retrieval failed.")
                        Return 0
                    Finally
                        Adapter.Dispose()
                    End Try
                End Using
            End If

            ' Add together all production charges and return the total.
            Dim Total As Decimal = 0
            For Each ResearchCategory As DataSetContract.ResearchCategoryRow In Children
                Total += ResearchCategory.Fee
            Next
            Return Total

        End Get
    End Property

    Public ReadOnly Property MiscellaneousChargesValue As Decimal
        Get

            ' Refresh child rows if necessary.
            Dim Children() As DataSetContract.ContractMiscellaneousChargeRow = Row.GetContractMiscellaneousChargeRows
            If Children.Length = 0 Then
                ' Create a connection to retrieve the data.
                Using SqlCon As New SqlClient.SqlConnection(ConnectionString)
                    ' Create a table adapter to do our bidding.
                    Dim Adapter As New DataSetContractTableAdapters.ContractMiscellaneousChargeTableAdapter
                    Adapter.Connection = SqlCon
                    ' Get the data from the database.
                    Dim ContractDataSet As DataSetContract = Row.Table.DataSet
                    Try
                        Adapter.Fill(ContractDataSet.ContractMiscellaneousCharge, ContractID)
                    Catch ex As Exception
                        MessageBox.Show("Miscellaneous charge data retrieval failed.")
                        Return 0
                    Finally
                        Adapter.Dispose()
                    End Try
                End Using
            End If

            ' Add together all production charges and return the total.
            Dim Total As Decimal = 0
            For Each Charge As DataSetContract.ContractMiscellaneousChargeRow In Children
                Total += Charge.MiscellaneousChargeAmount
            Next
            Return Total

        End Get
    End Property

    Public ReadOnly Property InventoryValue As Decimal
        Get

            ' Refresh child rows if necessary.
            Dim Children() As DataSetContract.ContractInventoryQtyRow = Row.GetContractInventoryQtyRows
            If Children.Length = 0 Then
                ' Create a connection to retrieve the data.
                Using SqlCon As New SqlClient.SqlConnection(ConnectionString)
                    ' Create a table adapter to do our bidding.
                    Dim Adapter As New DataSetContractTableAdapters.ContractInventoryQtyTableAdapter
                    Adapter.Connection = SqlCon
                    ' Get the data from the database.
                    Dim ContractDataSet As DataSetContract = Row.Table.DataSet
                    Try
                        Adapter.Fill(ContractDataSet.ContractInventoryQty, ContractID)
                    Catch ex As Exception
                        MessageBox.Show("Inventory data retrieval failed.")
                        Return 0
                    Finally
                        Adapter.Dispose()
                    End Try
                End Using
            End If

            ' Add together all production charges and return the total.
            Dim Total As Decimal = 0
            For Each ProductionItem As DataSetContract.ContractInventoryQtyRow In Row.GetContractInventoryQtyRows
                Total += ProductionItem.SellPrice
            Next
            Return Total

        End Get
    End Property

    Public ReadOnly Property Value As Decimal
        Get
            Return Math.Round(RentalValue + ResearchValue + InventoryValue + MiscellaneousChargesValue, 2)
        End Get
    End Property

    ReadOnly Property ValueExcludingAgencyComm As Decimal
        Get
            Dim CommPercentage As Decimal = -1 * CInt(Row.ApplyAgencyComm) * Row.AgencyCommPercentage
            Dim AmountOnWhichCommIsPayable As Decimal = RentalValue + ResearchValue
            Dim AmountOnWhichCommIsExcempt As Decimal = InventoryValue + MiscellaneousChargesValue
            Dim CommAmount As Decimal = 0

            If Row.AgencyCommIsPercentageOfNetRental Then
                CommAmount = CommPercentage / 100 * AmountOnWhichCommIsPayable
            Else
                CommAmount = AmountOnWhichCommIsPayable - (AmountOnWhichCommIsPayable / (1 + CommPercentage / 100))
            End If

            Return Math.Round(AmountOnWhichCommIsPayable - CommAmount + AmountOnWhichCommIsExcempt, 2)
        End Get
    End Property

    Public ReadOnly Property Periods As DataTable
        ' This property returns periods covered by this contract, based on the contract dates.
        Get

            ' Check if the _Periods field has been instantiated.
            If IsNothing(_Periods) Then
                ' The field doesn't exist yet. Create an instance for it.
                ' Define the needed columns for the table.
                Dim TheOnlyColumn As New DataColumn("PeriodID", GetType(Integer))
                Dim Key() As DataColumn = {TheOnlyColumn}
                _Periods = New DataTable
                _Periods.Columns.Add(TheOnlyColumn)
                _Periods.PrimaryKey = Key
            Else
                ' The field already exists. Clear the table's rows.
                _Periods.Clear()
            End If

            ' Populate the _Periods table.
            Dim CurrentWeek As Date = FirstWeek
            While CurrentWeek <= LastWeek
                ' Build the primary key value for this row (year + month)
                Dim PeriodID As Integer = CInt(Year(CurrentWeek).ToString & Month(CurrentWeek).ToString("0#"))
                ' Check if a row with this primary key value already exists in the table.
                If Not _Periods.Rows.Contains(PeriodID) Then
                    ' The table doesn't contain a row for the financial period represented by the current week's year and month values.
                    ' Build a row for this period.
                    Dim NewRow As DataRow = _Periods.NewRow
                    NewRow("PeriodID") = PeriodID
                    ' Add the new row to the grid table.
                    _Periods.Rows.Add(NewRow)
                End If
                ' Process the next week.
                CurrentWeek = CurrentWeek.AddDays(7)
            End While

            ' If there are only bursts of zero weeks length in this contract then no periods will have been added to the periods table
            ' above. In this case we'll need to add the period in which the first week occurs so that the periods table has at least
            ' one row in it.
            If _Periods.Rows.Count = 0 Then
                Dim NewRow As DataRow = _Periods.NewRow
                NewRow("PeriodID") = FirstWeek.Year * 100 + FirstWeek.Month
                _Periods.Rows.Add(NewRow)
            End If

            ' Sort the table.
            _Periods.DefaultView.Sort = "PeriodID"

            ' Return the _Periods table.
            Return _Periods

        End Get
    End Property

    Public ReadOnly Property LastPeriod As Integer
        ' This property returns the last period during which a contract runs (in the format YYYYMM).
        Get
            Dim PeriodCount As Integer = Periods.Rows.Count
            ' Return the last period ID in the format YYYYMM.
            Return Periods.Rows(PeriodCount - 1).Item("PeriodID")
        End Get
    End Property

    Public ReadOnly Property BillingInstructionCount As Integer
        ' This property returns the number of billing instructions with an amount greater than zero.
        Get

            ' Create a SQL connection to connect to the database.
            Using SqlCon As New SqlClient.SqlConnection(ConnectionString)

                ' Create a table adapter to do our bidding.
                Dim BillingInstructionAdapter As New DataSetContractTableAdapters.BillingInstructionTableAdapter
                BillingInstructionAdapter.Connection = SqlCon

                ' Refresh the data in the table.
                _BillingInstruction = BillingInstructionAdapter.GetData(ContractID)

                ' A variable t hold the counted rows with amounts.
                Dim Count As Integer = 0

                ' Count all rows with amounts.
                For Each Instruction As DataSetContract.BillingInstructionRow In _BillingInstruction.Rows
                    If Instruction.Amount > 0 Then
                        Count += 1
                    End If
                Next

                ' Return the binding source.
                Return Count

                ' Destroy the table adapter. We don't need it anymore.
                BillingInstructionAdapter.Dispose()

            End Using

        End Get
    End Property

    Public ReadOnly Property BillingInstructionDifference As Decimal
        ' This property returns the difference between the total contract value and the total sum of all billing instructions for this contract.
        Get

            ' A variable to hold the total of all billing instruction amounts.
            Dim BillingTotal As Decimal = 0

            ' Add up all billing amounts.
            If Not IsNothing(_BillingInstruction) Then
                For Each Instruction As DataSetContract.BillingInstructionRow In _BillingInstruction.Rows
                    BillingTotal += Instruction.Amount
                Next
            End If

            ' Get the contract value to use, taking agency comm into account.
            Dim ContractValue As Decimal = Value
            If PrintAgencyComm Then
                ContractValue = ValueExcludingAgencyComm
            End If

            Return Math.Round(CDec(ContractValue), 2) - Math.Round(CDec(BillingTotal), 2)

        End Get
    End Property

    Public Property RollForwardContract As Boolean
        Get
            Return Row.RollForward
        End Get
        Set(value As Boolean)
            Row.RollForward = value
        End Set
    End Property

    Public Property AddedValueContract As Boolean
        Get
            Return Row.AddedValue
        End Get
        Set(value As Boolean)
            Row.AddedValue = value
        End Set
    End Property

    Public ReadOnly Property PrintAgencyComm As Boolean
        Get
            Return Row.PrintAgencyComm
        End Get
    End Property

    Public Property Approved As Boolean
        Get
            Return Row.Approved
        End Get
        Set(value As Boolean)
            Row.Approved = value
        End Set
    End Property

#End Region

#Region "Public Methods"

    Public Sub New(ContractRow As DataSetContract.ContractRow, ConString As String)

        ' Set the value of the row variable.
        _Row = ContractRow
        MyBase.ConnectionString = ConString

        ' Make a clone of the dataset to enable us to keep track of anything that changes (for auditing purposes).
        OriginalTable = ContractRow.Table.Copy

        ' Initialize binding sources for this row and child rows.
        InitializeBindingSources()

    End Sub

    Public Function Cancel(ByVal Requestingform As LiquidShell.BaseForm, ByVal ConString As String) As Boolean
        ' Cancel this contract.

        ' Update the connection string.
        MyBase.ConnectionString = ConString

        ' Unsigned contracts may not be cancelled.
        If Not Signed Then
            Requestingform.ShowMessage("It's not necessary to cancel this contract because it has not yet been signed. " _
                                      & "If it is no longer needed, simply delete it.", "Cancellation Not Necessary",
                                      MessageBoxButtons.OK, MessageBoxIcon.Stop)
            Return False
        End If

        ' It's stupid trying to cancel a contract that is already cancelled.
        If Cancelled Then
            Requestingform.ShowMessage("This contract is already cancelled. Trying to cancel it again will change nothing.",
                                      "Cancel A Cancelled Contract? I Don't Understand.",
                                      MessageBoxButtons.OK, MessageBoxIcon.Stop)
            Return False
        End If

        ' read-only contracts may not be cancelled.
        ConsumingForm = Requestingform
        If ReadOnlyContract Then
            Requestingform.ShowMessage _
            ("This contract is read-only. No changes can be made at this time.", "Action Not Permitted", MessageBoxIcon.Stop)
            Return False
        End If

        'cannot cancel signed and approved contracts once the month has elapsed - recognised revenue stuff at work here.
        If ((FirstWeek.Month < DateTime.Today.Month) And
            (FirstWeek.Year = DateTime.Today.Year) And Signed And Approved) Or
            (FirstWeek < DateTime.Today And Signed And Approved) Then

            Requestingform.ShowMessage("You cannot cancel a signed contract once the month of the start date of the contract has elapsed (Or the first week start date of the contract has passed) and finance has approved it.", "Talk To Finance!",
                                     MessageBoxButtons.OK, MessageBoxIcon.Stop)
            Return False
        End If

        ' Confirm action.
        If Not LiquidShell.LiquidAgent.ActionConfirmed(Requestingform, "Cancel") Then
            Return False
        End If

        ' Cancel the contract.
        Row.Cancelled = True
        Row.CancelDate = Settings.GetServerTime(ConnectionString, Requestingform)
        Row.CancelledBy = My.User.Name

        ' Save.
        Return Save(ConnectionString, Requestingform)

    End Function

    Public Function Delete(ByVal Consumingform As LiquidShell.BaseForm, ByVal ConString As String) As Boolean
        ' Delete this contract's row from database.

        ' Update the connection string.
        MyBase.ConnectionString = ConString

        ' Cancelled contracts may not be deleted.
        If Cancelled Then
            Consumingform.ShowMessage("Cancelled contracts may not be deleted.",
            "Delete Not Permitted", MessageBoxButtons.OK, MessageBoxIcon.Stop)
            Return False
        End If

        ' Signed contracts may not be deleted.
        If Signed Then
            Consumingform.ShowMessage("Signed contracts may not be deleted. Please cancel them instead.",
            "Delete Not Permitted", MessageBoxButtons.OK, MessageBoxIcon.Stop)
            Return False
        End If

        ' Confirm action.
        If Not LiquidShell.LiquidAgent.ActionConfirmed(Consumingform, "Delete") Then
            Return False
        End If

        ' Delete rows from the dataset.
        Row.Delete()

        ' Save.
        Return Save(ConnectionString, Consumingform)

    End Function

    Public Shared Function CreateNewContractNumber _
    (ByVal Code As String, ByVal ConString As String) _
    As String
        ' This method was originally designed to generate a new contract number for the contract. It still does this, but
        ' I've added a call to a stored procedure to fix an unidentified bug which causes the media gap to inaccurately
        ' display the quantity of stores used by a signed contract.

        ' Create a string to hold the select statement.
        Dim SelectStatement As String = "SELECT dbo.udfLastUsedContractNumber('" & Code & "')"
        Dim Fix As String = "EXEC spFixStorePoolCapacities"

        ' A string variable to hold potential errors.
        Dim Errors As String = String.Empty

        ' A variable to hold the last number used as a contract number for the AM with the given code.
        Dim LastNumberUsed As Integer = 0

        ' Run the command on the server.
        Using SqlCon As New SqlClient.SqlConnection(ConString)
            SqlCon.Open()
            Dim LastUsedContractNumber As Object = LiquidShell.LiquidAgent.GetSqlScalarValue(SqlCon, SelectStatement, Errors)
            LiquidShell.LiquidAgent.RunCommand(SqlCon, Fix, Errors)
            SqlCon.Close()
            If Not IsDBNull(LastUsedContractNumber) Then
                ' The query result was not null. In other words, previous contracts exist for the AM with the given code.
                LastNumberUsed = CInt(LastUsedContractNumber)
            End If
        End Using

        ' Check if any errors occured.
        If String.IsNullOrEmpty(Errors) = False Then
            Return Errors
        End If

        ' Get a random number between 1 and 5 and add it to the last used contract number.
        Dim Generator As New Random
        LastNumberUsed += Generator.Next(4, 9)

        ' Return the last used number plus the random number, prefixed with the account manager code.
        Dim NewContractNumberString As String = LastNumberUsed.ToString("000000")
        Return Code & NewContractNumberString

    End Function

    Public Function Clone(ByVal NewContractNumber As String, ByVal Optional isReplacement As Boolean = False, ByVal Optional Reason As String = "Normal Clone", ByVal Optional ProposalHeat As String = "Cold") As String

        Dim Statement As String = String.Format("EXEC dbo.spCloneContract '{0}','{1}',{2},'{3}','{4}','{5}'", ContractID.ToString, NewContractNumber, If(isReplacement, 1, 0).ToString, Reason, My.User.CurrentPrincipal.Identity.Name, ProposalHeat)
        Dim Errors As String = String.Empty

        Using SqlCon As SqlClient.SqlConnection = New SqlClient.SqlConnection("Data Source=primenovasql\Live;Initial Catalog=NovaDB;User ID=NovaApp;Password=******************;Connect Timeout=0")
            SqlCon.Open()
            LiquidShell.LiquidAgent.RunCommand(SqlCon, Statement, Errors)
            SqlCon.Close()
        End Using

        If String.IsNullOrEmpty(Errors) = False Then
            Return Errors
        Else
            Return String.Empty
        End If
    End Function

    Public Function ToggleApproveStatus() As String
        ' This function approves the contract if it is not approved, or unapproves it if it
        ' is approved.

        ' Toggle the APPROVED status of the contract object.
        Approved = Not Approved

        ' Create a string to hold the select statement.
        Dim ApprovedInteger = 0
        If Approved Then
            ApprovedInteger = 1
        End If
        Dim Statement As String = "EXEC dbo.spApproveContract '" & ContractID.ToString & "', " & ApprovedInteger.ToString

        ' A string variable to hold potential errors.
        Dim Errors As String = String.Empty

        ' Run the command on the server.
        Using SqlCon As New SqlClient.SqlConnection(ConnectionString)
            SqlCon.Open()
            LiquidShell.LiquidAgent.RunCommand(SqlCon, Statement, Errors)
            SqlCon.Close()
        End Using

        ' Check if any errors occured.
        If String.IsNullOrEmpty(Errors) = False Then
            ' Something went wrong at the server. Undo the change.
            Approved = Not Approved
            Return Errors
        Else
            Return String.Empty
        End If

    End Function

    Public Function Save(ConString As String, ConsumingForm As LiquidShell.BaseForm) As Boolean

        ' Update the connection string.
        MyBase.ConnectionString = ConString

        ' Stop if this contract is read-only.
        If ReadOnlyContract Then
            ConsumingForm.ShowMessage _
            ("This contract is read-only. No changes can be made at this time.", "Action Not Permitted", MessageBoxIcon.Stop)
            Row.Table.DataSet.RejectChanges()
            Return False
        End If

        ' Create a SQL connection for the save operation.
        Dim SqlCon As New SqlClient.SqlConnection(ConnectionString)

        ' Create table adapters to do the job.
        Dim ContractAdapter As New DataSetContractTableAdapters.ContractTableAdapter
        Dim AuditAdapter As New DataSetAuditTableAdapters.AuditLogTableAdapter

        ' Set the connection of the adapter.
        ContractAdapter.Connection = SqlCon
        AuditAdapter.Connection = SqlCon

        ' Create an audit log table.
        Dim AuditTable As New DataSetAudit.AuditLogDataTable

        ' Add an entry into the audit log.
        If Row.RowState = DataRowState.Added Then
            ' This is a new row being created. Add a single audit log entry for it.
            LiquidShell.LiquidAgent.AddAuditLogEntry(AuditTable, "Contract", ContractNumber, "Created")
        ElseIf Row.RowState = DataRowState.Deleted Then
            ' This row is being deleted.
            LiquidShell.LiquidAgent.AddAuditLogEntry(AuditTable, "Contract", ContractNumber, "Deleted")
        Else
            ' This is an existing new row being modified. Scan for changes to individual items.
            AuditChanges(AuditTable, Row, "ContractID", "Contract", ContractNumber)
        End If

        ' Save.
        Try
            ' Update the database.
            ContractAdapter.Update(Row)
            AuditAdapter.Update(AuditTable)
            ' Replace the copy of the original row with a copy of the modified row.
            OriginalTable = Row.Table.Copy
            ' Save the date and time of this save operation.
            SaveInfo = "Contract saved on " & Now.ToString("d MMMM yyyy") & " at " & Now.ToString("HH:mm:ss")
            ' Return true to indicate save operation success.
            Return True
        Catch ex As Exception
            ConsumingForm.ShowMessage("Um... This wasn't meant to happen." & vbCrLf & vbCrLf & LiquidShell.LiquidAgent.GetErrorMessage(ex),
                                      "Something Went Wrong", MessageBoxIcon.Error)
            Return False
        Finally
            AuditTable.Dispose()
            ContractAdapter.Dispose()
            AuditAdapter.Dispose()
            SqlCon.Dispose()
        End Try

    End Function

    Public Sub UpdateConnectionString(ConString As String)
        MyBase.ConnectionString = ConString
    End Sub

    Public Function Sign(ConString As String, ConsumingForm As LiquidShell.BaseForm) As Boolean
        ' Sign the contract.

        ' Stop if the contract is already signed.
        If Signed Then
            ConsumingForm.ShowMessage("This contract is already signed. Trying to sign it again will change nothing.",
                                      "Sign A Signed Contract? I Don't Understand.",
                                      MessageBoxButtons.OK, MessageBoxIcon.Stop)
            Return False
        End If

        ' Stop if no billing instructions have been specified.
        ' Define a variable to hold possible errors while checking for billing instructions.
        Dim Errors As String = String.Empty
        ' Define the select statement we need to check for billing instructions.
        Dim SelectStatement As String = "SELECT BillingInstructionID FROM Sales.BillingInstruction WHERE (ContractID = '" & ContractID.ToString & "')"
        ' Get a table containing billing instructions.
        Dim BillingInstruction As DataTable = LiquidShell.LiquidAgent.GetSqlDataTable(ConString, SelectStatement.ToString, Errors)
        ' Check if any errors occured during the command execution.
        If Errors.Length > 0 Then
            ' Errors occured.
            ConsumingForm.ShowMessage("Something went wrong while checking for billing instructions. The error returned was:" & vbCrLf & vbCrLf _
                        & Errors, "Stepped in a Puddle")
            Exit Function
        End If
        ' Check if any results were returned.
        If BillingInstruction.Rows.Count = 0 AndAlso (RentalValue + ResearchValue + MiscellaneousChargesValue + InventoryValue > 0) Then
            ' We have no billing instructions. Inform the user and exit.
            ConsumingForm.ShowMessage("Please specify billing instructions before signing the contract.", "Delete Not Permitted")
            Exit Function
        End If

        ' Confirm that this action cannot be undone.
        Dim Response As DialogResult = ConsumingForm.ShowMessage("You are about to sign this contract. This action " _
            & "is permanent and cannot be undone. Are you sure you would like to sign this contract now?", "Permanently " _
            & "Sign the Contract", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question)
        If Not Response = DialogResult.Yes Then
            ' The user has not confirmed that they want to sign.
            Return False
        End If

        ' Sign the contract.
        Dim ServerTime As Date = Settings.GetServerTime(ConnectionString, ConsumingForm)
        Row.Signed = True
        Row.SignedBy = My.User.Name
        Row.SignDate = ServerTime
        Row.ContractDate = ServerTime
        Return Save(ConString, ConsumingForm)

    End Function

    Public Function Revive(ByVal Requestingform As LiquidShell.BaseForm, ByVal ConString As String)

        ' read-only contracts may not be revived.
        ConsumingForm = Requestingform
        If ReadOnlyContract Then
            Requestingform.ShowMessage _
            ("This contract is read-only. No changes can be made at this time.", "Action Not Permitted", MessageBoxIcon.Stop)
            Return False
        End If

        ' Confirm action.
        If Not LiquidShell.LiquidAgent.ActionConfirmed(Requestingform, "Revive") Then
            Return False
        End If

        ' UNcancel the contract.
        Row.Cancelled = False

        ' Unconfirm all store lists.
        Dim errorMessage As String = String.Empty
        Dim reviveReplacement As Boolean = ReviveReplacementContract(ConString, errorMessage)

        If reviveReplacement = False Then
            If String.IsNullOrEmpty(errorMessage) = False Then
                Requestingform.ShowMessage _
            (errorMessage, "An Error Occured", MessageBoxIcon.Stop)
                Return False
            End If
            Requestingform.ShowMessage _
            ("The contract that was being replaced by the one you are reviving no longer exists. Therefore you cannot revive this one. Sorry.", "Unable To Continue", MessageBoxIcon.Stop)
            Return False
        End If

        ' Unconfirm all store lists.
        Dim storelistsdisabled As Boolean = UnconfirmStoreLists(ConString)
        If storelistsdisabled = False Then
            Requestingform.ShowMessage _
            ("Failed To disable store lists. Contract cannot be revived.", "Unable To Continue", MessageBoxIcon.Stop)
            Return False
        End If

        ' Save.
        Return Save(ConnectionString, Requestingform)

    End Function

    Private Function UnconfirmStoreLists(ByVal ConString As String) As Boolean

        Dim success As Boolean = False

        Using connection As New SqlClient.SqlConnection(ConString)
            Dim commandtext As String = "dbo.spUnconfirmStoreLists"
            Using command As New SqlClient.SqlCommand(commandtext, connection)
                command.CommandType = CommandType.StoredProcedure
                command.Parameters.AddWithValue("contractid", Row.ContractID)
                Try
                    connection.Open()
                    command.ExecuteNonQuery()
                    success = True
                Catch ex As Exception
                    Dim errormessage As String = ex.Message
                End Try
            End Using
        End Using

        Return success

    End Function


    Private Function ReviveReplacementContract(ByVal ConString As String, ByRef errorMessage As String) As Boolean

        Dim Revive As Boolean = False
        errorMessage = String.Empty

        Using connection As New SqlClient.SqlConnection(ConString)
            Dim commandtext As String = "dbo.spPreventReplacementContractFromBeingRevived"
            Using command As New SqlClient.SqlCommand(commandtext, connection)
                command.CommandType = CommandType.StoredProcedure
                command.Parameters.AddWithValue("contractid", Row.ContractID)
                Try
                    connection.Open()
                    Dim result As Boolean = CBool(command.ExecuteScalar())
                    Revive = result
                Catch ex As Exception
                    errorMessage = ex.Message
                End Try
            End Using
        End Using

        Return Revive
    End Function

    Public Shared Sub ModifyingAfterSignatureNotPermitted(Subform As LiquidShell.Subform)
        CType(Subform.TopLevelControl, LiquidShell.BaseForm).ShowMessage("Changes To a signed contract are Not permitted.", "Contract Is Signed")
    End Sub

#End Region

#Region "Private Methods"

    Private Sub InitializeBindingSources()

        ' Create the primary parent binding source.
        _Binder = New BindingSource(Row.Table.DataSet, "Contract")

        ' Create the child binding sources.
        _PONumberBindingSource = New BindingSource(Binder, "Contract_PurchaseOrderNumber")
        _MiscellaneousChargeBindingSource = New BindingSource(Binder, "Contract_ContractMiscellaneousCharge")
        _MediaCostBindingSource = New BindingSource(Binder, "Contract_MediaCost")
        _InvioiceNumberBindingSource = New BindingSource(Binder, "Contract_ContractInvoices")

    End Sub

    Public Sub UpdateResearchCategories()
        ' Update the 'ResearchCategories' column of the datarow of this object to display all the selected categories, and also update
        ' the FirstWeek as LastWeek properties of the row for this contract.

        ' A string list to hold the resulting category list.
        Dim InfoList As New List(Of String)

        ' Date variables to hold the first and last weeks.
        Dim FirstWeekDate As Date = New Date(1000, 1, 1)
        Dim LastWeekDate As Date = New Date(1000, 1, 1)

        ' Add names to the string list.
        For Each ResearchCategory As DataSetContract.ResearchCategoryRow In Row.GetChildRows("Contract_ResearchCategory")

            ' Check if the list already contains this item.
            If InfoList.Contains(ResearchCategory.CategoryName) = False Then
                ' This info isn't in the list. Add it.
                InfoList.Add(ResearchCategory.CategoryName)
            End If

            ' Check if this category has an earlier start date than what we already have.
            If FirstWeekDate.Year = 1000 Then
                ' We have not yet recorded our first date. This must be the first category that we're looping through.
                FirstWeekDate = ResearchCategory.FromDate
            ElseIf ResearchCategory.FromDate < FirstWeekDate Then
                FirstWeekDate = ResearchCategory.FromDate
            End If

            ' Get the last week of this category.
            Dim CategoryLastWeek As Date = DateAdd(DateInterval.Month, ResearchCategory.Months - 1, ResearchCategory.FromDate)

            ' Check if this category has a later end date than what we already have.
            If LastWeekDate.Year = 1000 Then
                ' We have not yet recorded our first date. This must be the first category that we're looping through.
                LastWeekDate = CategoryLastWeek
            ElseIf CategoryLastWeek > LastWeekDate Then
                LastWeekDate = CategoryLastWeek
            End If

        Next

        ' A string builder to turn the list into a consecutive string.
        Dim Builder As New System.Text.StringBuilder

        ' Add all list items to the string builder, separating values with a comma.
        For Each Item As String In InfoList
            If Builder.Length > 0 Then
                Builder.Append(", ")
            End If
            Builder.Append(Item)
        Next

        ' Update the row column with the updated list and dates.
        Row.ResearchCategories = Builder.ToString
        Row.FirstWeek = FirstWeekDate
        Row.LastWeek = LastWeekDate

    End Sub

#End Region

End Class



Public Class ResearchCategory
    Inherits BaseDataObject

    Private _ParentContract As Contract = Nothing
    Private _Row As DataSetContract.ResearchCategoryRow = Nothing

#Region "Properties"

    Public ReadOnly Property Row As DataSetContract.ResearchCategoryRow
        Get
            Return _Row
        End Get
    End Property

    Public ReadOnly Property ResearchCategoryID As Guid
        Get
            If Row.RowState = DataRowState.Deleted Then
                Return Row("ResearchCategoryID", DataRowVersion.Original)
            Else
                Return _Row.ResearchCategoryID
            End If
        End Get
    End Property

    Public ReadOnly Property ContractID As Guid
        Get
            If Row.RowState = DataRowState.Deleted Then
                Return Row("ContractID", DataRowVersion.Original)
            Else
                Return _Row.ContractID
            End If
        End Get
    End Property

    Public ReadOnly Property CategoryID As Integer
        Get
            If Row.RowState = DataRowState.Deleted Then
                Return Row("CategoryID", DataRowVersion.Original)
            Else
                Return _Row.CategoryID
            End If
        End Get
    End Property

    Public ReadOnly Property CategoryName As String
        Get
            If Row.RowState = DataRowState.Deleted Then
                Return Row("CategoryName", DataRowVersion.Original)
            Else
                If String.IsNullOrEmpty(_Row.CategoryName) Then
                    Return "Select..."
                Else
                    Return _Row.CategoryName
                End If
            End If
        End Get
    End Property

    Public ReadOnly Property FirstMonth As String
        Get
            If Row.RowState = DataRowState.Deleted Then
                Return Row("FirstMonth", DataRowVersion.Original).ToString
            Else
                If String.IsNullOrEmpty(Row.FirstMonth) Then
                    Return "Select..."
                Else
                    Return Row.FirstMonth
                End If
            End If
        End Get
    End Property

    Public ReadOnly Property LastMonth As String
        Get
            If Row.RowState = DataRowState.Deleted Then
                Return Row("LastMonth", DataRowVersion.Original).ToString
            Else
                If String.IsNullOrEmpty(Row.FirstMonth) Then
                    Return "Select..."
                Else
                    Return Row.LastMonth
                End If
            End If
        End Get
    End Property

    Public Property Months As Integer
        Get
            If Row.RowState = DataRowState.Deleted Then
                Return Row("Months", DataRowVersion.Original)
            Else
                Return _Row.Months
            End If
        End Get
        Set(value As Integer)
            _Row.Months = value
            Dim ToDate As Date = DateAdd(DateInterval.Month, _Row.Months - 1, _Row.FromDate)
            _Row.LastMonth = ToDate.ToString("MMMM") & " " & ToDate.ToString("yyyy")
        End Set
    End Property

    Public Property Fee() As Decimal
        Get
            If Row.RowState = DataRowState.Deleted Then
                Return Row("Fee", DataRowVersion.Original)
            Else
                Return _Row.Fee
            End If
        End Get
        Set(ByVal value As Decimal)
            _Row.Fee = value
        End Set
    End Property

    Public Property Discount() As Decimal
        Get
            If Row.RowState = DataRowState.Deleted Then
                Return Row("Discount", DataRowVersion.Original)
            Else
                Return _Row.Discount
            End If
        End Get
        Set(ByVal value As Decimal)
            _Row.Discount = value
        End Set
    End Property

    Public WriteOnly Property SelectedCategory As DataRow
        Set(value As DataRow)
            _Row.CategoryID = value("CategoryID")
            _Row.CategoryName = value("CategoryName")
        End Set
    End Property

    Public WriteOnly Property SelectedFirstMonth As Date
        Set(value As Date)
            _Row.FromDate = New Date(value.Year, value.Month, 1)
            _Row.FirstMonth = value.ToString("MMMM") & " " & value.ToString("yyyy")
            Dim ToDate As Date = DateAdd(DateInterval.Month, _Row.Months - 1, _Row.FromDate)
            _Row.LastMonth = ToDate.ToString("MMMM") & " " & ToDate.ToString("yyyy")
        End Set
    End Property

    Public WriteOnly Property SelectedLastMonth As Date
        Set(value As Date)
            Dim SelectedDate As Date = New Date(Year(value), Month(value), 1)
            _Row.Months = DateDiff(DateInterval.Month, _Row.FromDate, SelectedDate) + 1
            _Row.LastMonth = SelectedDate.ToString("MMMM") & " " & SelectedDate.ToString("yyyy")
        End Set
    End Property

    Public ReadOnly Property ParentContract As Contract
        Get
            Return _ParentContract
        End Get
    End Property

#End Region

#Region "Public Methods"

    Public Sub New(ResearchCategoryRow As DataSetContract.ResearchCategoryRow, ConString As String, Parent As Contract)

        ' Set the value of the row variable.
        _Row = ResearchCategoryRow
        ConnectionString = ConString
        _ParentContract = Parent

        ' Make a clone of the dataset to enable us to keep track of anything that changes (for auditing purposes).
        OriginalTable = ResearchCategoryRow.Table.Copy

    End Sub

    Public Function Save(ConString As String, ConsumingForm As LiquidShell.BaseForm) As Boolean

        ' Update the connection string.
        ConnectionString = ConString
        ParentContract.UpdateConnectionString(ConString)

        ' Stop if this contract is read-only.
        If ParentContract.ReadOnlyContract Then
            ConsumingForm.ShowMessage _
            ("This contract Is read-only. No changes can be made at this time.", "Action Not Permitted", MessageBoxIcon.Stop)
            Row.Table.DataSet.RejectChanges()
            Return False
        End If

        ' Save this row to the dataset if it is a new row.
        If Row.RowState = DataRowState.Detached Then
            Dim ResearchCategoryTable As DataSetContract.ResearchCategoryDataTable = ParentContract.Row.Table.DataSet.Tables("ResearchCategory")
            ResearchCategoryTable.Rows.Add(Row)
        End If

        ' Create a SQL connection for the save operation.
        Dim SqlCon As New SqlClient.SqlConnection(ConnectionString)

        ' Create table adapters to do the job.
        Dim ResearchCategoryAdapter As New DataSetContractTableAdapters.ResearchCategoryTableAdapter
        Dim AuditAdapter As New DataSetAuditTableAdapters.AuditLogTableAdapter

        ' Set the connection of the adapter.
        ResearchCategoryAdapter.Connection = SqlCon
        AuditAdapter.Connection = SqlCon

        ' Create an audit log table.
        Dim AuditTable As New DataSetAudit.AuditLogDataTable

        ' Build a description for this row.
        Dim RowDescription As String = String.Empty
        If Row.RowState = DataRowState.Modified Then
            Dim OriginalCategoryName As String = Row("CategoryName", DataRowVersion.Original)
            Dim OriginalFirstMonth As String = Row("FirstMonth", DataRowVersion.Original)
            Dim OriginalLastMonth As String = Row("LastMonth", DataRowVersion.Original)
            Dim OriginalFee As Decimal = Row("Fee", DataRowVersion.Original)
            RowDescription = "research category '" & OriginalCategoryName & "' from " & OriginalFirstMonth & " to " & OriginalLastMonth & " for a fee of R" & OriginalFee.ToString("#,##0.00")
            Else
            RowDescription = "research category '" & CategoryName & "' from " & FirstMonth & " to " & LastMonth & " for a fee of R" & Fee.ToString("#,##0.00")
        End If

        ' Add an entry into the audit log.
        If Row.RowState = DataRowState.Added Then
            ' This is a new row being created. Add a single audit log entry for it.
            LiquidShell.LiquidAgent.AddAuditLogEntry(AuditTable, "Contract", ParentContract.ContractNumber, "Added " & RowDescription)
        ElseIf Row.RowState = DataRowState.Deleted Then
            ' This row is being deleted.
            LiquidShell.LiquidAgent.AddAuditLogEntry(AuditTable, "Contract", ParentContract.ContractNumber, "Removed " & RowDescription)
        Else
            ' This is an existing new row being modified. Scan for changes to individual items.
            AuditChanges(AuditTable, Row, "ResearchCategoryID", "Contract", ParentContract.ContractNumber, "In " & RowDescription & ", ")
        End If

        ' Save.
        Try
            ' Update the database.
            ResearchCategoryAdapter.Update(Row)
            AuditAdapter.Update(AuditTable)
            ' Replace the copy of the original row with a copy of the modified row.
            OriginalTable = Row.Table.Copy
            ' Update the ResearchCategories column of the parent contract row.
            ParentContract.UpdateResearchCategories()
            ' Update the net fee column of this category.
            Row.NetFee = Row.Fee - (Row.Discount / 100 * Row.Fee)
            ' Raise the Saved event.
            ParentContract.SaveInfo = "Contract saved on " & Now.ToString("d MMMM yyyy") & " at " & Now.ToString("HH:mm:ss")
            ' Return true to indicate save operation success.
            Return True
        Catch ex As Exception
            ConsumingForm.ShowMessage("Um... This wasn't meant to happen." & vbCrLf & vbCrLf & LiquidShell.LiquidAgent.GetErrorMessage(ex),
                                      "Something Went Wrong", MessageBoxIcon.Error)
            Return False
        Finally
            AuditTable.Dispose()
            ResearchCategoryAdapter.Dispose()
            AuditAdapter.Dispose()
            SqlCon.Dispose()
        End Try

    End Function

    Public Function Delete(ByVal Consumingform As LiquidShell.BaseForm, ByVal ConString As String) As Boolean
        ' Delete this object's row from database.

        ' Update the connection string.
        ConnectionString = ConString

        ' Delete rows from the dataset.
        Row.Delete()

        ' Save.
        Return Save(ConnectionString, Consumingform)

    End Function

    Public Shared Sub Delete(ByVal Grid As DataGridView, ByVal ConString As String, Parent As Contract)

        ' Stop if no rows were selected.
        If Grid.SelectedRows.Count < 1 Then
            CType(Grid.TopLevelControl, LiquidShell.BaseForm).ShowMessage _
            ("Please select at least one row before using the 'Delete' button.", "Grated Cheese Paradigm")
            Exit Sub
        End If

        ' Get a collection of the deletable rows.
        Dim RowsToDelete As New List(Of DataSetContract.ResearchCategoryRow)
        For Each SelectedGridRow As DataGridViewRow In Grid.SelectedRows
            RowsToDelete.Add(CType(SelectedGridRow.DataBoundItem, DataRowView).Row)
        Next

        ' Check if any row has a fee other than R0.00 - if so, deleting the row will change the contract value
        ' which is not permitted if the contract is signed.
        If Parent.Signed Then
            For Each RowToDelete As DataSetContract.ResearchCategoryRow In RowsToDelete
                If RowToDelete.Fee > 0 Then
                    ' Deleting this row will change the contract value. Cannot continue.
                    CType(Grid.TopLevelControl, LiquidShell.BaseForm).ShowMessage _
                    ("Only research categories with a fee of R 0.00 may be deleted, otherwise the contract value" & vbCrLf _
                     & "will change, which is not allowed because the contract has already been signed.", "Cannot Change Contract Value")
                    Exit Sub
                End If
            Next
        End If

        ' Confirm the delete action.
        If LiquidShell.LiquidAgent.ActionConfirmed(Grid, "Delete") = False Then
            Exit Sub
        End If

        ' Delete all the rows in the collection.
        For Each RowToDelete As DataSetContract.ResearchCategoryRow In RowsToDelete
            Dim ResearchCategoryObject As New ResearchCategory(RowToDelete, ConString, Parent)
            ResearchCategoryObject.Delete(CType(Grid.TopLevelControl, LiquidShell.BaseForm), ConString)
        Next

    End Sub

#End Region

End Class



Public Class BillingInstructionSet
    Inherits BaseDataObject

    Private _ParentContract As Contract = Nothing
    Private _BillingInstructionTable As DataSetContract.BillingInstructionDataTable
    Private _FirstBillingMonth As Date = New Date(1000, 1, 1)
    Private _BillingMonths As Integer = 1
    Private BillingInstructionAdapter As New DataSetContractTableAdapters.BillingInstructionTableAdapter
    Private MonthClosedDay As Integer = 0
    Private ConsumingForm As LiquidShell.BaseForm
    Private ServerDate As Date

#Region "Properties"

    Public ReadOnly Property ParentContract As Contract
        Get
            Return _ParentContract
        End Get
    End Property

    Public ReadOnly Property BillingInstructionTable As DataSetContract.BillingInstructionDataTable
        Get
            Return _BillingInstructionTable
        End Get
    End Property

    Public ReadOnly Property FirstBillingMonthString As String
        Get
            If _FirstBillingMonth.Year = 1000 Then
                Return "Select..."
            Else
                Return _FirstBillingMonth.ToString("MMMM yyyy")
            End If
        End Get
    End Property

    Public Property FirstBillingMonth As Date
        Get
            Return _FirstBillingMonth
        End Get
        Set(value As Date)
            _FirstBillingMonth = New Date(value.Year, value.Month, 1)
        End Set
    End Property

    Public Property BillingMonths As Integer
        Get
            Return _BillingMonths
        End Get
        Set(value As Integer)
            _BillingMonths = value
        End Set
    End Property

#End Region

#Region "Public Methods"

    Public Sub New(ParentContractObject As Contract, MyForm As LiquidShell.BaseForm)

        ' Assign values to variables.
        _ParentContract = ParentContractObject
        ConnectionString = ParentContract.ConnectionString
        ServerDate = Settings.GetServerTime(ConnectionString, ConsumingForm).Date
        ConsumingForm = MyForm

        ' Now get a table containing existing billing instructions, if any.
        _BillingInstructionTable = ParentContract.Row.Table.DataSet.Tables("BillingInstruction")
        Using SqlCon As New SqlClient.SqlConnection(ParentContract.ConnectionString)
            BillingInstructionAdapter.Connection = SqlCon
            BillingInstructionAdapter.Fill(BillingInstructionTable, ParentContract.ContractID)
        End Using

        ' Get the day on which a month closes for finance from the settings table in the database.
        Dim SettingsTable As DataTable = Settings.GetSettings(ConnectionString, MyForm)
        Dim MonthClosedRow As DataRow() = SettingsTable.Select("SettingName = 'DayOnWhichMonthCloses'")
        MonthClosedDay = CInt(MonthClosedRow(0).Item("SettingValue"))

        ' Save the original table as a backup.
        OriginalTable = BillingInstructionTable.Copy

    End Sub

    Public Function Save(ConString As String, ConsumingForm As LiquidShell.BaseForm, GridTable As DataTable) As Boolean

        ' Update the connection string.
        ConnectionString = ConString
        ParentContract.UpdateConnectionString(ConString)

        ' Stop if this contract is read-only.
        If ParentContract.PermissionToModify = False Then
            ConsumingForm.ShowMessage _
            ("This contract is read-only. No changes can be made at this time.", "Action Not Permitted", MessageBoxIcon.Stop)
            Return False
        End If

        ' Go through each row in the BillingInstruction table.
        For i As Integer = 0 To BillingInstructionTable.Rows.Count - 1
            ' Try and find a matching row in the grid table.
            Dim Instruction As DataSetContract.BillingInstructionRow = BillingInstructionTable.Rows(i)
            If Not Instruction.RowState = DataRowState.Deleted Then
                Dim MatchingGridRow As DataRow = GridTable.Rows.Find(Instruction.PeriodID)
                ' If there is no matching row in the grid table with an amount greater than zero, delete this row from the BillingInstruction table.
                If IsNothing(MatchingGridRow) Then
                    Instruction.Delete()
                Else
                    If MatchingGridRow("Amount") = 0 Then
                        Instruction.Delete()
                    End If
                End If
            End If
        Next

        ' Go through each row in the grid table.
        For i As Integer = 0 To GridTable.Rows.Count - 1
            ' Try and find a matching row in the billing instruction table if the amount for this grid row is greater than zero.
            Dim GridRow As DataRow = GridTable.Rows(i)

            If GridRow("Amount") > 0 Then

                ' Search for rows in the billing instruction table with the same period ID as this grid row.
                Dim MatchingInstruction As DataSetContract.BillingInstructionRow = Nothing
                Dim MatchingRows() As DataRow = BillingInstructionTable.Select("PeriodID = " & GridRow("PeriodID").ToString)
                If MatchingRows.Length > 0 Then
                    MatchingInstruction = MatchingRows(0)
                End If

                ' If there is no matching row in the BillingInstruction table, create a new row in the BillingInstruction table.
                If IsNothing(MatchingInstruction) Then
                    Dim NewInstruction As DataSetContract.BillingInstructionRow = BillingInstructionTable.NewRow
                    With NewInstruction
                        .ContractID = ParentContract.ContractID
                        .PeriodID = GridRow("PeriodID")
                        .Amount = GridRow("Amount")
                        .PONumber = GridRow("PONumber")
                        .PeriodName = GridRow("PeriodName")
                    End With
                    BillingInstructionTable.Rows.Add(NewInstruction)
                Else
                    ' If there is a matching row in the BillingInstruction table with different values to this grid table row, update the BillingInstruction row.
                    If Not Object.Equals(MatchingInstruction.Amount, GridRow("Amount")) Then
                        MatchingInstruction.Amount = GridRow("Amount")
                    End If
                    If Not Object.Equals(MatchingInstruction.PONumber, GridRow("PONumber")) Then
                        MatchingInstruction.PONumber = GridRow("PONumber")
                    End If
                End If

            End If

        Next

        ' Update the database.

        ' Create a SQL connection for the save operation.
        Dim SqlCon As New SqlClient.SqlConnection(ConnectionString)

        ' Create table adapters to do the job.
        Dim BillingInstructionAdapter As New DataSetContractTableAdapters.BillingInstructionTableAdapter
        Dim AuditAdapter As New DataSetAuditTableAdapters.AuditLogTableAdapter

        ' Set the connection of the adapter.
        BillingInstructionAdapter.Connection = SqlCon
        AuditAdapter.Connection = SqlCon

        ' Create an audit log table.
        Dim AuditTable As New DataSetAudit.AuditLogDataTable

        For Each InstructionRow As DataRow In BillingInstructionTable.Rows
            ' Build a description for this row.
            Dim AmountString As String = String.Empty
            Dim PeriodName As String = String.Empty
            If InstructionRow.RowState = DataRowState.Added Then
                AmountString = CDec(InstructionRow("Amount")).ToString("C2")
                PeriodName = InstructionRow("PeriodName")
            Else
                AmountString = CDec(InstructionRow("Amount", DataRowVersion.Original)).ToString("C2")
                PeriodName = InstructionRow("PeriodName", DataRowVersion.Original)
            End If

            Dim RowDescription As String = "billing instruction of " & AmountString & " for " & PeriodName
            ' Add an entry into the audit log.
            If InstructionRow.RowState = DataRowState.Added Then
                ' This is a new row being created. Add a single audit log entry for it.
                LiquidShell.LiquidAgent.AddAuditLogEntry(AuditTable, "Contract", ParentContract.ContractNumber, "Added " & RowDescription)
            ElseIf InstructionRow.RowState = DataRowState.Deleted Then
                ' This row is being deleted.
                LiquidShell.LiquidAgent.AddAuditLogEntry(AuditTable, "Contract", ParentContract.ContractNumber, "Removed " & RowDescription)
            Else
                ' This is an existing new row being modified. Scan for changes to individual items.
                AuditChanges(AuditTable, InstructionRow, "BillingInstructionID", "Contract", ParentContract.ContractNumber, "In " & RowDescription & ", ")
            End If
        Next

        ' Save.
        Try
            ' Update the database.
            BillingInstructionAdapter.Update(BillingInstructionTable)
            AuditAdapter.Update(AuditTable)
            ' Replace the copy of the original table with a copy of the modified table.
            OriginalTable = BillingInstructionTable.Copy
            ' Raise the Saved event.
            ParentContract.SaveInfo = "Contract saved on " & Now.ToString("d MMMM yyyy") & " at " & Now.ToString("HH:mm:ss")
            ' Return true to indicate save operation success.
            Return True
        Catch ex As Exception
            ConsumingForm.ShowMessage("Um... This wasn't meant to happen." & vbCrLf & vbCrLf & LiquidShell.LiquidAgent.GetErrorMessage(ex),
                                      "Something Went Wrong", MessageBoxIcon.Error)
            Return False
        Finally
            AuditTable.Dispose()
            BillingInstructionAdapter.Dispose()
            AuditAdapter.Dispose()
            SqlCon.Dispose()
        End Try

    End Function

    Public Function PeriodClosed(PeriodID As Integer) As Boolean
        ' Check whether the billing period for the given billing instruction has been closed. If so, the user
        ' should not be permitted to change the amount for this period.

        ' If the current user is a member of the "finance_billinginstructionmodifier" role then no periods will be closed.
        If My.User.IsInRole("finance_billinginstructionmodifier") Then
            Return False
        End If

        ' Get the month closure date for the given instruction row by using the MonthClosedBy setting as the day
        ' and the period ID as the month and year.
        Dim CloseYear As Integer = CInt(Left(PeriodID.ToString, 4))
        Dim CloseMonth As Integer = CInt(Right(PeriodID.ToString, 2))
        Dim ClosedDate As New Date(CloseYear, CloseMonth, MonthClosedDay)

        ' Now check if the month closure date is in the past or the future.
        If ServerDate < ClosedDate Then
            Return False
        Else
            Return True
        End If

    End Function

#End Region

End Class