<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="ImageList24x24.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>163, 17</value>
  </metadata>
  <data name="ImageList24x24.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj00LjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAAAi
        HQAAAk1TRnQBSQFMAgEBBAEAAWgBAQFoAQEBGAEAARgBAAT/AREBAAj/AUIBTQE2BwABNgMAASgDAAFg
        AwABMAMAAQEBAAEQBgABJP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/AP8A/wD/ACoAAf8BfwGd
        AXMBewFvAZwBcwH/AX8QAAH/AX8BnAFzAXsBbwGdAXMB/wF/CgAB/wF/AZwBcwF7AW8BewFvAXsBbwF7
        AW8BewFvAXsBbwF7AW8BewFvAXsBbwF7AW8BewFvAXsBbwF7AW8BewFvAXsBbwF7AW8BnAFzAf8BfxYA
        Af8BfwH/AX8B/wF/TAAB/wF/AXsBbwH/AXsB/wF7Af8BewGcAXMB/wF/DAAB/wF/AZwBcwH/AXsB/wF7
        Af8CewFvAf8BfwYAAf8BfwF7AW8B/gF/Af4BfwH+AX8B/gF/Af4BfwH+AX8B/gF/Af4BfwH+AX8B/gF/
        Af4BfwH+AX8B/gF/Af4BfwH+AX8B/gF/Af4BfwH+AX8BewFvAd4BexQAAb0BdwF7AW8BnAFvAd4BeyYA
        Af8BfwH/AX8B/wF/HAAB/wF/AXsBbwH/AXsBGAFrAcYBQAHuAVUB/wF7AZwBcwH/AX8IAAH/AX8BnAFv
        Af8BewHuAVUBxgFAARgBawH/AnsBbwH/AX8CAAH/AX8BewFvAf4BfwG5AUIB1QEZAfYBGQH2ARkB9gEZ
        AfYBGQH2ARkB9gEZAfYBGQH2ARkB1QEZAdUBGQHVARkB1QEZAdUBGQHVARkBlAEVAVcBNgHeAX8BnAF3
        Af8Bfw4AAf8BfwF7AW8BvwF7Ad8BfwHfAX8BnAFzAf8BfyIAAf8BfwGcAXMBWwFvAXsBbwH/AX8YAAH/
        AX8BewFvAf8BewHWAWYBAAE0AQABNAEAATQBagFNAf8BewGdAXMB/wF/BAAB/wF/AZ0BcwH/AXsBagFN
        AQABNAEAATQBAAE0AdYBZgH/AnsBbwH/AX8BvQF3Af4BfwGYAToB0gEAAVMBAQEzAQEBMwEBATMBAQEz
        AQEBMwEBATMBAQEzAQEBMwEBARMBAQESAQEBEgEBARIBAQHyAQAB8QEAARIBAQGPAQAB9QEhAf4BfwGc
        AXMMAAH/AX8BewFvAd8BfwF4AWcBaAE6Aa4BSgHfAnsBbwH/AX8eAAH/AX8BWwFrAd8BewG9AXcBvgF3
        AZwBcwH/AX8WAAG9AXcB/wF7AfcBagEAATgBQQE8AWMBQAFjAUABAAE4AYwBUQH/AX8BnAFzAf8BfwH/
        AX8BnAFzAf8BfwGMAVEBAAE4AWMBQAFjAUABQQE8AQABOAH3AWoB/wF7Ab0BdwGcAXMB/gF/AXQBAQF0
        AQEB/wF/AZgBPgE0AQEBdAEBAXQBAQF0AQEBdAEBAXQBAQF0AQEBdAEBAXQBAQFTAQEBUwEBARIBAQEW
        ASYB/wF/AZQBEQHQAQABvQF3AXsBbwoAAf8BfwF7AW8B3wF/AVYBYwEBASYBAQEmAeABHQHwAU4B3wF/
        AZwBcxwAAf8BfwF7AW8B3wF/ARMBWwHgASEBiwFCAd8BfwGcAXMWAAF8AW8B/wF7AWMBQAEgATwBgwFA
        AWMBQAFjAUABYgFAAQABOAGMAVUB/wF7AXwBbwF8AW8B/wF/AawBVQEAATgBYgFAAWMBQAFjAUABgwFA
        ASABPAFjAUAB/wF7AZwBbwGcAXMB/gF/AZUBBQFUAQEBugE+AfYBEQF0AQEBlQEBAZUBAQGVAQEBlQEB
        AXUBAQF0AQEBdAEBAXQBAQF0AQEBVAEBAVMBAQGUAQkBuQFCAVMBAQESAQEBvQF3AXsBbwgAAf8BfwF7
        AW8B3wF/AXkBawEhASoBQwEuAUQBMgFEAS4BIgEqAZwBcwG+AXcB/wF/GAAB/wF/AXsBbwHfAX8BNQFf
        AQABJgECASYBRQEyAd8CewFvAf8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/BAABnAFz
        Af8BewHmAUwBAAFAAYMBRAFjAUQBYwFEAWMBRAFiAUQBAAE8AWsBVQH/AX8B/wF/AWsBVQEAATwBYgFE
        AWMBRAFjAUQBYwFEAYMBRAEAAUAB5gFMAf8BewGcAXMBnAFzAf4BfwG2AQkBdQEBAVQBAQF1AQEBlQEB
        AZUBAQGVAQEBlQEBAZUBAQGVAQEBlQEBAXUBAQF0AQEBdAEBAXQBAQF0AQEBUwEBARIBAQESAQEBMgEB
        Ab0BdwF7AW8GAAH/AX8BewFvAd8BfwF4AWcBQgEuAUMBLgFkATIBZAEyAWQBMgFBASoBywFGAf8BfwF7
        AW8WAAH/AX8BewFvAd8BfwE0AV8BIQEmAUQBLgFDAS4BZwE2Ad8BfwG+AXcBnAFzAZ0BcwGdAXMBnQFz
        AZ0BcwGdAXMBnQFzAZ0BcwF7AW8BvQF3AgAB/wF/Af8BewG9AXcBYgFEASABRAGDAUgBYwFIAWMBSAFj
        AUgBYwFIAQABQAHvAV0B7wFdAQABQAFiAUgBYwFIAWMBSAFjAUgBgwFIASABRAFiAUQBvQF3Af8BewH/
        AX8BnAFzAf4BfwHWAQkBdQEBAbYBAQG2AQEBtgEBAbYBAQGWAQEBlgEBAZYBAQGVAQEBlQEBAZUBAQGV
        AQEBdQEBAXQBAQF0AQEBdAEBAVMBAQEzAQEBMwEBAd4CewFvBAAB/wF/AXsBbwHfAX8BeQFrAUEBLgFj
        ATIBhAE2AYQBNgGEATYBhAE2AWQBNgFAASoBdwFnAd8BfwHeAXsSAAH/AX8BewFvAf8BfwFWAWMBQAEq
        AWMBMgFkATYBYwEyAYcBOgGcAXMBmgFvAZoBbwGaAW8BmgFvAZoBbwGaAW8BmgFvAZoBbwGaAW8BvgF3
        Ab4BewHeAXsCAAHeAXsB/wF7AZwBdwGDAUgBIAFEAYMBSAGDAUgBgwFIAYMBSAFjAUgBIAFEASABRAFj
        AUgBgwFIAYMBSAGDAUgBgwFIASABRAGDAUgBnAF3Af8BewHeAXsCAAGcAXMB/gF/AdcBCQGWAQEBtgEB
        AbYBAQG2AQEBtgEBAbYBAQG2AQEBtgEBAbYBAQGWAQEBlQEBAZUBAQGVAQEBdQEBAXQBAQF0AQEBdAEB
        AVMBAQEzAQEB3gJ7AXMCAAH/AX8BnAFzAd8BfwGaAW8BgwEyAYMBMgGEATYBhAE2AYQBNgGEATYBhAE2
        AYQBNgGDATIBpQE6Ad8BewF8AXMB/wF/DgAB/wF/AXsBbwH/AX8BdgFnAWEBLgGDATIBhAE2AYQBNgGE
        ATYBhAE2AWIBLgFhAS4BYQEuAWEBLgFhAS4BYQEuAWEBLgFhAS4BYQEuAUABKgGnAT4B3wF7AXwBbwQA
        Ad4BewH/AX8B3gF3AYMBTAEgAUgBgwFMAYMBTAGDAUwBgwFMAYMBTAGDAUwBgwFMAYMBTAGDAUwBgwFM
        ASABSAGDAUwB3gF3Af8BfwHeAXsEAAGcAXMB/gF/AdcBCQGWAQEBtwEBAbcBAQG3AQEBtwEBAbcBAQG2
        AQEBtgEBAbYBAQG2AQEBtgEBAZUBAQGVAQEBlQEBAXUBAQF0AQEBdAEBAVMBAQFTAQEB3gF7AZsBcwIA
        AZwBcwH/AX8BmgFvAYIBMgGDATYBpAE6AaQBOgGkAToBpAE6AaQBOgGkAToBpAE6AaQBOgGAAS4BLwFT
        Af8BfwGcAXMOAAGcAXMB3wF/AXYBZwFhAS4BgwE2AaQBOgGkAToBpAE6AaQBOgGkAToBpAE6AaQBOgGk
        AToBpAE6AaQBOgGkAToBpAE6AaQBOgGkAToBhAE2AYMBMgG+AXcBfAFvBgAB3gF7Af8BfwG9AXcBYgFM
        ASABSAGDAVABgwFQAYMBUAGDAVABgwFQAYMBUAGDAVABgwFQASABSAFiAUwBvQF3Af8BfwHeAXsGAAGc
        AXMB/gF/AfcBCQG2AQEB1wEBAdcBAQHXAQEB1wEBAdcBAQHXAQEBtwEBAbYBAQG2AQEBtgEBAbYBAQGW
        AQEBlQEBAZUBAQF1AQEBdAEBAVQBAQFTAQEB3gF7AZsBcwHeAXsB3wF/AbsBcwGiATYBogE2AcQBOgHE
        AToBxAE6AcQBOgGiATYBogE2AcQBOgHEAToBxAE6AaQBOgGiATYBvAF3Ad8BfwH/AX8KAAH/AX8B3wF/
        AbsBcwGBATIBowE2AcQBOgHEAToBxAE6AcQBOgHEAToBxAE6AcQBOgHEAToBxAE6AcQBOgHEAToBxAE6
        AcQBOgHEAToBxAE6AaQBOgGkAToB3gF7AXwBbwgAAd4BewH/AXsB3gF7AeYBVAFBAVABgwFQAYMBUAGD
        AVABgwFQAYMBUAGDAVABQQFQAeYBVAHeAXsB/wF7Ad4BewgAAZwBcwH/AX8B+AEJAbcBAQHXAQEB1wEB
        AdcBAQHXAQEB1wEBAdcBAQHXAQEB1wEBAbcBAQG2AQEBtgEBAbYBAQGWAQEBlQEBAZUBAQF1AQEBVAEB
        AVMBAQHeAXsBnAFzAZwBcwHfAX8B5gE+AcIBNgHEAT4BxAE+AcQBPgHEAT4BoAEyAVABVwFQAVsBoQE2
        AcQBPgHEAT4BxAE+AcIBNgHoAUYB/wF/AXwBbwH/AX8IAAGcAXMB/wF/AQkBSwGhATYBxAE+AcQBPgHE
        AT4BxAE+AcQBPgHEAT4BxAE+AcQBPgHEAT4BxAE+AcQBPgHEAT4BxAE+AcQBPgHEAT4BxAE+AcQBOgHE
        AToB3gF7AXwBcwgAAf8BfwG9AXMB/wF/Ac4BYQEgAVABgwFUAYMBVAGDAVQBgwFUAYMBVAGDAVQBIAFQ
        Ac4BYQH/AX8BvQFzAf8BfwgAAZwBcwH/AX8B+AEJAbcBAQH4AQEB2AEBAdgBAQHYAQEB1wEBAbcBAQG3
        AQEBtwEBAbcBAQGWAQEBlgEBAZYBAQF1AQEBdQEBAZUBAQGVAQEBdAEBAVQBAQHeAX8BnAFzAZwBcwH/
        AX8BBgFDAcEBNgHlAT4B5AE+AeQBPgHAATYBLAFTAf8BfwH/AX8B5AE+AeMBOgHkAT4B5AE+AeQBPgHA
        ATYBcQFfAf8BfwGcAXMIAAGcAXMB/wF/AeUBQgHiAToB5QE+AeQBPgHkAT4B5AE+AeQBPgHkAT4B5AE+
        AeQBPgHkAT4B5AE+AeQBPgHkAT4B5AE+AeQBPgHkAT4B5QE+AeQBPgHkAT4B3wF7AZwBcwYAAf8BfwG9
        AXMB/wF/Aa0BYQEAAVABYwFUAYMBVAGDAVQBgwFUAYMBVAGDAVQBgwFUAWMBVAEAAVABrQFhAf8BfwG9
        AXMB/wF/BgABnAFzAf8BfwEYAQoB2AEBAfgBAQHYAQEBGQEKATkBEgE5AQ4BOQEOARgBDgEYAQ4BGAEO
        ARgBDgH4AQ0B9wENAfcBEQHWAQkBVQEBAXUBAQF0AQEBVAEBAf4BfwGcAXMB/wF/Af8BfwHdAXcB5AE+
        AeIBOgEEAUMBwAE2ASsBUwH/AX8BvgF3Ad8BfwG4AW8B4AE2AeQBQgEEAUMBBAFDAeQBPgHhAToBugFz
        Af8BfwG9AXcGAAHeAXsB/wF/AXIBYwHAATYBBAFDAQQBQwEEAUMBBAFDAQQBQwEEAUMBBAFDAQQBQwEE
        AUMBBAFDAQQBQwEEAUMBBAFDAQQBQwEEAUMBBAFDAeMBPgHkAT4B3wF/AZwBcwQAAf8BfwHeAXcB/wF/
        Ac0BZQEAAVABgwFYAYMBWAGDAVgBgwFYAYMBWAGDAVgBgwFYAYMBWAGDAVgBgwFYAQABUAHNAWUB/wF/
        Ab4BdwH/AX8EAAGcAXMB/wF/ARkBCgHYAQEB2AEBAdsBOgH9AX8B3QF/Ad0BfwHdAX8B3QF/Ad0BfwHd
        AX8B/QF/Af4BfwH/AX8B/wF/Af4BfwGZATYBVAEBAXQBAQF0AQEB/gF/AZwBcwIAAd4BewH/AX8B3QF3
        AeMBQgHAATYBKgFTAf8BfwHeAXsB/wF/Ab0BdwH/AX8BTQFXAeABOgEEAUMBBAFDAQQBQwHiAT4BBQFH
        Af8BfwHfAX8B/wF/BgABvQF3Af8BfwEqAU8B4AE6AQQBQwEEAUMBBAFDAQQBQwEEAUMBBAFDAQQBQwEE
        AUMBBAFDAQQBQwEEAUMBBAFDAQQBQwEEAUMBBAFDAeMBQgHjAUIB3wF/AZwBcwIAAf8BfwG9AXMB/wF/
        Ac4BaQEAAVQBgwFYAYMBXAGDAVwBgwFcAYMBXAFiAVgBYgFYAYMBXAGDAVwBgwFcAYMBXAGDAVgBAAFU
        Ac4BaQH/AX8BvQFzAf8BfwIAAZwBcwH/AX8BGQEKAdgBAQH5AQEBvQF3Ad0BfwG9AXcBvQF3Ab0BdwG9
        AXcBvQF3Ab0BdwH+AX8BuQFGAZQBHQE2AS4B/gF/AZ0BcwFUAQEBdAEBAXQBAQH/AX8BnAFzBAAB3gF7
        Af8BfwHdAXsBlgFrAf8BfwHfAX8B/wF/AgAB/wF/Ad4BewH/AX8BAwFDAQIBQwEEAUcBBAFHAQQBRwHg
        AT4BKgFTAf8BfwG9AXcB/wF/BAAB/wF/Ad8BewH/AX8BKQFTAeABOgEDAUMBAwFHAQMBRwEEAUMBAwFD
        AeEBPgHhAT4B4QE+AeEBPgHhAT4B4QE+AeEBPgHhAT4B4QE+AeABPgHhAT4B/wF/AZwBcwH/AX8BvQF3
        Af8BfwGsAWkBAAFYAYMBXAGDAVwBgwFcAYMBXAGDAVwBIAFcAcYBYAHmAWABIAFcAYMBXAGDAVwBgwFc
        AYMBXAGDAVwBAAFYAawBaQH/AX8BvQF3Af8BfwGcAXMB/wF/ATkBCgH5AQEB+QEBAb0BdwHeAX8BvQF3
        Ab0BdwG9AXcBvQF3Ab0BdwG9AXcB/wF/AfYBGQHRAQABMgEBAf4BfwGdAXMBdQEBAXUBAQF0AQEB/wF/
        AZwBcwYAAd4BewG+AXsB/wF/Ab0BdwH/AX8GAAHeAXsB/wF/AbgBbwHgAT4BAwFHAQMBRwEDAUcBAwFH
        AeABPgFMAVcB/wF/Ab0BdwH/AX8EAAH/AX8B/wF/Af8BfwEpAVMB4AE+AQMBRwEDAUcBAgFHAQMBRwFM
        AVsBSwFXAUsBVwFLAVcBSwFXAUsBVwFLAVcBSwFXAUsBVwFKAVMBlQFrAf8BfwG9AXcBnAFzAf8BfwEP
        AW4BAAFYAYMBYAGDAWABgwFgAYMBYAGDAWABIAFcAYMBYAH/AX8B/wF/AWIBYAEgAVwBgwFgAYMBYAGD
        AWABgwFgAYMBYAEAAVgBDwFuAf8BfwGcAXMBnAFzAf8BfwE5AQoB+QEBAfkBAQG+AXcB3gF/Ad4BewHe
        AXsB3gF7Ad4BewHeAXsB3gF7Af8BfwFYASIBVAEBAZUBAQH/AX8BvQFzAXUBAQF1AQEBdAEBAf8BfwGc
        AXMYAAG9AXcB/wF/AW4BXwHgAT4BAwFHAQMBRwEDAUcBAwFHAeABPgFvAV8B/wF/AZwBcwYAAf8BfwH/
        AX8B/wF/AScBUwHgAUIBAwFLAQEBRwEmAU8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/
        AX8B/wF/Af8BfwG9AXcCAAGcAXMB/wF/AUEBYAFBAWABgwFgAYMBYAGDAWABgwFgASABYAGDAWABvQF7
        Af8BfwH/AX8BvQF7AYMBYAEgAWABgwFgAYMBYAGDAWABgwFgAUEBYAFBAWAB/wF/AZwBcwGcAXMB/wF/
        AToBCgH5AQEBGgECAd4BdwH+AX8B3gF7Ad4BewHeAXsB3gF7Ad4BewHeAXsB/wF/AXoBIgGXAQEB1wEB
        Af8BfwG+AXMBdQEBAXUBAQF0AQEB/wF/AZwBcxoAAb0BdwH/AX8BSAFTAQABQwEDAUsBAwFLAQMBSwEA
        AUMBAAFHAf8BfwGcAXMIAAH/AX8B/wF/Af8BfwFHAVMBAAFDAQEBRwEkAU8B/wF/AZwBcwH/AX8B3gF7
        Ad4BewHeAXsB3gF7Ad4BewHeAXsB3gF7Af8BfwQAAb0BdwH/AX8B7wFtAQABYAGDAWQBgwFkAYMBZAEg
        AWABgwFkAd4BfwH/AX8B3gF7Ad4BewH/AX8B3gF/AYMBZAEgAWABgwFkAYMBZAGDAWQBAAFgAQ8BbgH/
        AX8BvQF3AZwBcwH/AX8BOgEGAfkBAQEaAQIB3gF3Af8BfwHeAXsB3gF7Ad4BewHeAXsB3gF7Ad4BewH/
        AX8BmwEaAbgBAQG3AQEB/wF/Ab4BdwF1AQEBdQEBAXQBAQH/AX8BnAFzGgAB/wF/Af8BfwH/AX8BIgFL
        AQABRwEiAUsBAAFHAQABRwG2AW8B/wF/Ad4BewoAAf8BfwH/AX8B/wF/AUYBUwHgAT4BJQFTAf8BfwG9
        AXcWAAH/AX8B3gF7Af8BfwGMAW0BAAFkAWIBZAEgAWQBYgFkAd4BfwH/AX8B3gF7BAAB3gF7Af8BfwG9
        AX8BYQFkASABZAFiAWQBAAFkAYwBbQH/AX8B3gF7Af8BfwG9AXcB/wF/AXoBGgHZAQEBGgECAd8BewH/
        AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/AR0BQwE6AQ4BegEmAf8BfwG+AXMBdQEBATQBAQGV
        AQEB/wF/AZwBcxwAAd4BewH/AX8B/AF7ASIBTwEAAUMBSAFXAf0BewH/AX8BvQF3DgAB/wF/Af8BfwH/
        AX8BkQFnAdoBdwH/AX8B/wF/GAAB/wF/Ad4BewH/AX8BzgFxAQABZAGEAWgBvQF/Af8BfwHeAXsIAAHe
        AXsB/wF/Ab0BfwGkAWgBAAFkAc4BcQH/AX8B3gF7Af8BfwIAAf8BfwH/AX8B3wF3AToBBgHZAQEB3wF3
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Ab4BcwEUAQEBdAEB
        AX0BZwH/AX8B3gF7HgAB3gF7Af8BfwH/AX8B2wF3Af8BfwH/AX8B3gF7EgAB/wF/Ad4BewH/AX8B/wF/
        Ad4BexwAAf8BfwHeAXsB/wF/Ab0BfwH/AX8B/wF/Ad4BewwAAd4BewH/AX8B/wF/Ab0BfwH/AX8B3gF7
        Af8BfwYAAd4BewH/AX8B/wF/Ab8BbwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/
        AX8B/wF/Af8BfwH/AX8B/wF/Ab4BbwH/AX8B/wF/Ab0BdyIAAd4BewHeAXsB/wF/Ab0BdxgAAd4BewHe
        AXsB3gF7IgABvQF3Ab0BdwG9AXcB3gF7EAAB3gF7Ab0BdwG9AXcBvQF3DAAB/wF/Ab0BdwG9AXcBvQF3
        Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3
        Ad4BeyYAAf8BfwH/AX8B/wF/OAABQgFNAT4HAAE+AwABKAMAAWADAAEwAwABAQEAAQEFAAFAAQIWAAP/
        /wAiAAHgAf8BBwHAAQABAwH/AY8E/wHAAX4BAwGAAQABAQH/AYcC/wEfAf8BgAE8AQEDAAH+AQMB/wH+
        AQ8B/wEAARgEAAH8AQEB/wH8AQcB/wYAAfgBAQH/AfgBBwH/BgAB8AEAAf8B8AEAAQMGAAHgAQAB/wHg
        AQABAQYAAcABAAF/AcACAAGAAQABAQMAAYABAAE/AYACAAHAAQABAwMAAYABAAE/AYACAAHgAQABBwUA
        AR8DAAHwAQABDwUAAQ8DAAHwAQABDwUAAQ8DAAHgAQABBwUAAQcDAAHAAQABAwMAAYABAAEDAYACAAGA
        AQABAQMAAcABQAEBAYAIAALgAQABwAgAAf8B8AEAAeABAAEBBgAB/wH4AQAB8AEAAQMGAAH/AfgBAAH4
        AQcB/wEAARgEAAH/AfwBAQH8AQcB/wGAATwBAQMAAf8B/gEDAf4BDwH/AcABfgEDAYABAAEBAv8BDwH/
        AR8B/wHwAf8BDwHAAQABAwL/AY8D/ws=
</value>
  </data>
  <metadata name="ErrorManager.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="LiquidErrorManager1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>325, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="LiquidErrorManager1.Icon" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        AAABAAIAEBAAAAEACABoBQAAJgAAABAQAAABACAAaAQAAPYKAAAoAAAAEAAAACAAAAABAAgAAAAAAAAB
        AAAAAAAAAAAAAAABAAAAAQAAAAAAABx10gAge9UAIJ7gADK97wBju+kASMDvAETe/wBI3/8ATd//AGPO
        7wBtzu8ATOb/AE7o/wBT4P8AVef/AFrh/wBQ6f8AX+z/AGLj/wBq5P8AZOv/AGrr/wBz5f8Ae+b/AH3v
        /wB28P8AhOf/AIPu/wCN6f8AjO//AJXq/wCH8v8AjvH/AJDz/wCW8v8AnPH/AJn1/wCf9P8Anfn/AKPy
        /wCk8/8Aofb/AKb1/wCp9f8As/z/ALz//wDD//8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAA////AAAAAAAAAAAAAAAAAAAAAAAABQMDAwMDAwMDAwMDAwUABQYUExAO
        CQkHBwcHBwcEBQMYFxQTEA4JBwcHBwcHBwMDHiAXFBMQAQEJBwcHEQwDAAMhGBcUEwEBCQcHBw0DAAAD
        IyMYFxQTEA4JCQ0MAwAAAAMjGxgXAQIQDgkRAwAAAAADJCQbGAEBExASDgMAAAAAAAMkHhsBARQTFQMA
        AAAAAAADKSkeAQEXGhYDAAAAAAAAAAMpHwEBGBkDAAAAAAAAAAADLCwfHiMbAwAAAAAAAAAAAAMtKSQl
        AwAAAAAAAAAAAAAFCy8uCgUAAAAAAAAAAAAAAAUDAwUAAAAAAAD//wAAgAEAAAAAAAAAAAAAAAAAAIAB
        AACAAQAAwAMAAMADAADgBwAA4AcAAPAPAADwDwAA+B8AAPgfAAD8PwAAKAAAABAAAAAgAAAAAQAIAAAA
        AAAAAQAAAAAAAAAAAAAAAQAAAAEAAAAAAAAcddIAIHvVACCe4AAyve8AY7vpAEjA7wBE3v8ASN//AE3f
        /wBjzu8Abc7vAEzm/wBO6P8AU+D/AFXn/wBa4f8AUOn/AF/s/wBi4/8AauT/AGTr/wBq6/8Ac+X/AHvm
        /wB97/8AdvD/AITn/wCD7v8Ajen/AIzv/wCV6v8Ah/L/AI7x/wCQ8/8AlvL/AJzx/wCZ9f8An/T/AJ35
        /wCj8v8ApPP/AKH2/wCm9f8AqfX/ALP8/wC8//8Aw///AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAP///wAAAAAAAAAAAAAAAAAAAAAAAAUDAwMDAwMDAwMDAwMFAAUG
        FBMQDgkJBwcHBwcHBAUDGBcUExAOCQcHBwcHBwcDAx4gFxQTEAEBCQcHBxEMAwADIRgXFBMBAQkHBwcN
        AwAAAyMjGBcUExAOCQkNDAMAAAADIxsYFwECEA4JEQMAAAAAAyQkGxgBARMQEg4DAAAAAAADJB4bAQEU
        ExUDAAAAAAAAAykpHgEBFxoWAwAAAAAAAAADKR8BARgZAwAAAAAAAAAAAywsHx4jGwMAAAAAAAAAAAAD
        LSkkJQMAAAAAAAAAAAAABQsvLgoFAAAAAAAAAAAAAAAFAwMFAAAAAAAA//8AAIABAAAAAAAAAAAAAAAA
        AACAAQAAgAEAAMADAADAAwAA4AcAAOAHAADwDwAA8A8AAPgfAAD4HwAA/D8AACgAAAAQAAAAIAAAAAEA
        IAAAAAAAQAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACCe4LIgnuD/IJ7g/yCe4P8gnuD/IJ7g/yCe
        4P8gnuD/IJ7g/yCe4P8gnuD/IJ7g/yCe4P8gnuCyAAAAACCe4LJIwO//auT//2Lj//9a4f//U+D//03f
        //9I3///RN7//0Te//9E3v//RN7//0Te//9E3v//Mr3v/yCe4LIgnuD/e+b//3Pl//9q5P//YuP//1rh
        //9T4P//Td///0jf//9E3v//RN7//0Te//9E3v//RN7//0Te//8gnuD/IJ7g/4zv//+H8v//c+X//2rk
        //9i4///WuH//xx10v8cddL/SN///0Te//9E3v//RN7//1Dq//9M5v//IJ7g/wAAAAAgnuD/jvH//3vm
        //9z5f//auT//2Lj//8cddL/HHXS/03f//9I3///RN7//0Te//9O6P//IJ7g/wAAAAAAAAAAIJ7g/5Xx
        //+Q8///e+b//3Pl//9q5P//YuP//1rh//9T4P//Td///0jf//9Q6v//TOb//yCe4P8AAAAAAAAAAAAA
        AAAgnuD/l/P//4Tn//975v//c+X//xx10v8ge9X/WuH//1Pg//9N3///Uun//yCe4P8AAAAAAAAAAAAA
        AAAAAAAAIJ7g/53y//+Z9f//hOf//3vm//8cddL/HHXS/2Lj//9a4f//X+z//1Xn//8gnuD/AAAAAAAA
        AAAAAAAAAAAAAAAAAAAgnuD/n/T//43p//+E5///HHXS/xx10v9q5P//YuP//2Tr//8gnuD/AAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAIJ7g/6Tz//+h9v//jen//xx10v8cddL/c+X//3bw//9q6///IJ7g/wAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgnuD/pvX//5Xq//8cddL/HHXS/3vm//997///IJ7g/wAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIJ7g/6v0//+o9///ler//43p//+Q8///g+7//yCe
        4P8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgnuD/s/z//6Py//+c8f//nfn//yCe
        4P8AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIJ7gsm3O7//D////vP///2PO
        7/8gnuCyAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgnuCyIJ7g/yCe
        4P8gnuCyAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA//8AAIABAAAAAAAAAAAAAAAAAACAAQAAgAEAAMAD
        AADAAwAA4AcAAOAHAADwDwAA8A8AAPgfAAD4HwAA/D8AACgAAAAQAAAAIAAAAAEAIAAAAAAAQAQAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAACCe4LIgnuD/IJ7g/yCe4P8gnuD/IJ7g/yCe4P8gnuD/IJ7g/yCe
        4P8gnuD/IJ7g/yCe4P8gnuCyAAAAACCe4LJIwO//auT//2Lj//9a4f//U+D//03f//9I3///RN7//0Te
        //9E3v//RN7//0Te//9E3v//Mr3v/yCe4LIgnuD/e+b//3Pl//9q5P//YuP//1rh//9T4P//Td///0jf
        //9E3v//RN7//0Te//9E3v//RN7//0Te//8gnuD/IJ7g/4zv//+H8v//c+X//2rk//9i4///WuH//xx1
        0v8cddL/SN///0Te//9E3v//RN7//1Dq//9M5v//IJ7g/wAAAAAgnuD/jvH//3vm//9z5f//auT//2Lj
        //8cddL/HHXS/03f//9I3///RN7//0Te//9O6P//IJ7g/wAAAAAAAAAAIJ7g/5Xx//+Q8///e+b//3Pl
        //9q5P//YuP//1rh//9T4P//Td///0jf//9Q6v//TOb//yCe4P8AAAAAAAAAAAAAAAAgnuD/l/P//4Tn
        //975v//c+X//xx10v8ge9X/WuH//1Pg//9N3///Uun//yCe4P8AAAAAAAAAAAAAAAAAAAAAIJ7g/53y
        //+Z9f//hOf//3vm//8cddL/HHXS/2Lj//9a4f//X+z//1Xn//8gnuD/AAAAAAAAAAAAAAAAAAAAAAAA
        AAAgnuD/n/T//43p//+E5///HHXS/xx10v9q5P//YuP//2Tr//8gnuD/AAAAAAAAAAAAAAAAAAAAAAAA
        AAAAAAAAIJ7g/6Tz//+h9v//jen//xx10v8cddL/c+X//3bw//9q6///IJ7g/wAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAgnuD/pvX//5Xq//8cddL/HHXS/3vm//997///IJ7g/wAAAAAAAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAIJ7g/6v0//+o9///ler//43p//+Q8///g+7//yCe4P8AAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgnuD/s/z//6Py//+c8f//nfn//yCe4P8AAAAAAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAIJ7gsm3O7//D////vP///2PO7/8gnuCyAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAgnuCyIJ7g/yCe4P8gnuCyAAAAAAAA
        AAAAAAAAAAAAAAAAAAAAAAAA//8AAIABAAAAAAAAAAAAAAAAAACAAQAAgAEAAMADAADAAwAA4AcAAOAH
        AADwDwAA8A8AAPgfAAD4HwAA/D8AAA==
</value>
  </data>
</root>