Imports System.Data.SqlClient

Public Class SqlIdentity
    Implements System.Security.Principal.IIdentity

    Private _Name As String
    Private _IsAuthenticated As Boolean
    Private _Roles As New List(Of String)

    Public ReadOnly Property AuthenticationType() As String Implements System.Security.Principal.IIdentity.AuthenticationType
        Get
            Return "SqlDatabase"
        End Get
    End Property

    Public ReadOnly Property IsAuthenticated() As Boolean Implements System.Security.Principal.IIdentity.IsAuthenticated
        Get
            Return _IsAuthenticated
        End Get
    End Property

    Public ReadOnly Property Name() As String Implements System.Security.Principal.IIdentity.Name
        Get
            Return _Name
        End Get
    End Property

    Public ReadOnly Property Roles() As List(Of String)
        Get
            Return _Roles
        End Get
    End Property

    Public Sub New _
    (ByVal Server As String, ByVal Database As String, ByVal Name As String, ByVal Password As String)

        If IsValidNameAndPassword(Server, Database, Name, Password) Then
            _Name = Name
            _IsAuthenticated = True
        Else
            _Name = ""
            _IsAuthenticated = False
        End If

    End Sub

    Private Function IsValidNameAndPassword _
    (ByVal Server As String, ByVal Database As String, ByVal UserName As String, ByVal Password As String) _
    As Boolean

        ' Build a connection string to test the supplied credentials.
        Dim ConStringBuilder As New SqlConnectionStringBuilder()
        ConStringBuilder.DataSource = Server
        ConStringBuilder.ConnectTimeout = 0
        ConStringBuilder.InitialCatalog = Database
        ConStringBuilder.UserID = UserName
        ConStringBuilder.Password = Password

        ' Declare a variable to remember if this user is authenticated and valid.
        Dim ValidUser As Boolean = False

        ' Setup the SQL command to execute to collect db roles.
        Dim DataReader As SqlDataReader = Nothing
        'Dim SqlText As String = "Use " & Database & vbCrLf & "Go" & vbCrLf & "sp_helprolemember"
        Dim SqlText As String = "sp_helprolemember"
        Using Connection As New SqlConnection(ConStringBuilder.ToString)
            Dim Command As New SqlCommand(SqlText, Connection)
            ' Try and get the database role information from the sql server.
            Try
                Connection.Open()
                DataReader = Command.ExecuteReader

                ' If the command executed successfully, add the roles to the list.
                If IsNothing(DataReader) = False Then
                    ValidUser = True
                    ' Get a datatable from the data reader.
                    Dim DBRolesTable As DataTable = LiquidAgent.ReaderToTable(DataReader)
                    ' Get only the rows pertaining to the current user.
                    Dim DBRoles() As DataRow = DBRolesTable.Select("MemberName = '" & UserName & "'")
                    ' Add the user's roles to the list.
                    For Each DBRole As DataRow In DBRoles
                        _Roles.Add(DBRole("DbRole"))
                    Next
                End If

            Catch ex As Exception
                ' Couldn't get the db role info from the server. User cannot be validated.
            End Try
        End Using

        ' Manually add the "accountmanager" role if this user is an account manager.
        AddAccountManagerRole(ConStringBuilder.ToString, UserName)

        Return ValidUser

    End Function

    Private Sub AddAccountManagerRole(ByVal ConnectionString As String, ByVal UserName As String)
        ' Manually add the "accountmanager" role if this user is an account manager.

        ' Define the sql statement to check for account manager membership.
        Dim SqlText As String = "SELECT dtLogins.principal_id FROM (SELECT principal_id, name FROM sys.sql_logins) " _
        & "AS dtLogins INNER JOIN Sales.AccountManager ON dtLogins.principal_id = Sales.AccountManager.principal_id " _
        & "WHERE (Sales.AccountManager.Dormant = 0) AND (dtLogins.name = N'" & UserName & "')"

        ' Query the database for a result.
        Dim QueryResult As Object = Nothing
        Using Connection As New SqlConnection(ConnectionString)
            Dim Command As New SqlCommand(SqlText, Connection)
            ' Try and get the database role information from the sql server.
            Try
                Command.CommandTimeout = 0
                Connection.Open()
                QueryResult = Command.ExecuteScalar
            Catch ex As Exception
                ' Couldn't get the db role info from the server. User cannot be validated.
            End Try
        End Using

        ' If the command executed successfully, add the accountmanager role to the list.
        If IsNothing(QueryResult) = False Then
            If IsDBNull(QueryResult) = False Then
                ' This user is an account manager. Add the role to the list.
                _Roles.Add("accountmanager")
            End If
        End If

    End Sub

End Class
