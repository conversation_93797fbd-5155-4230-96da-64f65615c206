Imports System.ComponentModel
Imports System.Data.SqlClient
Imports System.Reflection

Public Class SubformMedia

    Private DataObject As Media
    Dim checkState As System.Collections.Generic.Dictionary(Of Integer, Boolean)
    Private DirtyDoesIt As Boolean = False

#Region "Event Handlers"

    Private Sub Subform_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        ' Load values for all controls.
        TextEditMediaName.Text = DataObject.MediaName
        MemoEditNotes.Text = DataObject.Notes
        CheckEditHomesite.Checked = DataObject.Homesite
        CheckEditCrossover.Checked = DataObject.Crossover
        CheckEditPNPPcaStatus.Checked = DataObject.isPNPPcaStatus
        CheckEditMediaCost.Checked = DataObject.hasMediaCost
        CheckEditMediaCostOverridable.Checked = DataObject.isCostOverridable

        ' The check box column will be virtual.
        ' GridMediaRule.VirtualMode = True
        ' Initialize the dictionary that contains the boolean check state.
        checkState = New Dictionary(Of Integer, Boolean)
        GridMediaRule.VirtualMode = True

        ' Load grid data.
        GridMediaFamilyMembership.AutoGenerateColumns = False
        GridMediaFamilyMembership.DataSource = DataObject.MediaFamilyMemberBindingSource
        GridCompetingMediaServices.AutoGenerateColumns = False
        GridCompetingMediaServices.DataSource = DataObject.CompetingMediaBindingSource
        GridMediaCategory.AutoGenerateColumns = False
        GridMediaCategory.DataSource = DataObject.MediaCategoryBindingSource
        GridMediaLifeCycle.AutoGenerateColumns = False
        GridMediaLifeCycle.DataSource = DataObject.MediaLifeCycleBindingSource
        GridMediaRule.AutoGenerateColumns = False
        GridMediaRule.DataSource = DataObject.MediaRuleBindingSource
        GridMediaCosts.AutoGenerateColumns = False
        GridMediaCosts.DataSource = DataObject.MediaCostBindingSource

        ' Configure grid managers.
        Dim GridManagerMediaFamilyMembership As New GridManager(GridMediaFamilyMembership, TextEditSearchMediaFamilyMembership, Nothing, Nothing,
        PictureAdvancedSearchMediaFamilyMembership, PictureClearSearchMediaFamilyMembership, Nothing, ButtonRemoveMediaFamilyMembership)

        Dim GridManagerCompetingMedia As New GridManager(GridCompetingMediaServices, TextEditSearchCompetingMediaServices, Nothing, Nothing,
        PictureAdvancedSearchCompetingMedia, PictureClearSearchCompetingMedia, Nothing, Nothing)

        Dim GridManagerMediaCategory As New GridManager(GridMediaCategory, TextEditSearchMediaCategory, Nothing, Nothing,
        PictureAdvancedSearchMediaCategory, PictureClearSearchMediaCategory, Nothing, ButtonRemoveMediaCategory)

        Dim GridManagerMediaLifeCycle As New GridManager(GridMediaLifeCycle, TextEditSearchMediaLifeCycle, Nothing, Nothing,
        PictureAdvancedSearchMediaLifeCycle, PictureClearSearchMediaLifeCycle, ButtonEditMediaLifeCycle, ButtonDeleteMediaLifeCycle)

        Dim GridManagerMediaRule As New GridManager(GridMediaRule, TextMediaRulesSearch, Nothing, Nothing,
        PictureAdvancedSearchMediaRule, PictureClearSearchMediaRule, Nothing, Nothing)

        Dim GridManagerMediaCost As New GridManager(GridMediaCosts, TextEditSearchMediaCost, Nothing, Nothing,
       PictureAdvancedSearchMediaCost, PictureClearSearchMediaCost, ButtonEditMediaCost, ButtonDeleteMediaCost)


    End Sub

    Public Sub New(ByVal MediaObject As Media)
        ' This call is required by the Windows Form Designer.
        InitializeComponent()
        ' Add any initialization after the InitializeComponent() call.
        DataObject = MediaObject
    End Sub

    Private Sub ButtonSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonSave.Click
        Save(True)
    End Sub

    Private Sub ButtonCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonCancel.Click

        If DirtyDoesIt Then
            ' Verify that user wants to discard changes.
            Dim Message As New System.Text.StringBuilder
            Message.Append("You have made some modifications to Media Rules." + vbCrLf + vbCrLf + "Are you sure you would like ")
            Message.Append("to close it and discard all your changes?")
            Dim ParentForm As LiquidShell.BaseForm = CType(TopLevelControl, LiquidShell.BaseForm)
            Dim UserResponse As DialogResult = ParentForm.ShowMessage(Message.ToString, MessageBoxButtons.YesNoCancel)
            If Not UserResponse = DialogResult.Yes Then
                ' User doesn't want to discard changes.
                Exit Sub
            End If
        End If

        DataObject.Close(Me)
    End Sub

    Private Sub TextEditMediaName_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles TextEditMediaName.EditValueChanged

        ' Create an object to represent the control.
        Dim CurrentControl As TextEdit = CType(sender, TextEdit)

        ' Update the form title as the user types a name.
        If Not String.IsNullOrEmpty(CurrentControl.Text) Then
            LabelTitle.Text = CurrentControl.Text
        End If

        ' Reset the error text of the control to an empty string.
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)

    End Sub

    Private Sub TextEditMediaName_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles TextEditMediaName.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If ValidatedControl.Text.Length = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "The name of the media service may not be blank.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

        ' Update the data object.
        DataObject.MediaName = ValidatedControl.Text

    End Sub

    Private Sub MemoEditNotes_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles MemoEditNotes.Validated
        ' Update the data object.
        DataObject.Notes = CType(sender, TextEdit).Text
    End Sub

    Private Sub CheckEditHomesite_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles CheckEditHomesite.Validated
        ' Update the data object.
        DataObject.Homesite = CType(sender, CheckEdit).Checked
    End Sub

    Private Sub CheckEditCrossover_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles CheckEditCrossover.Validated
        ' Update the data object.
        DataObject.Crossover = CType(sender, CheckEdit).Checked
    End Sub
    Private Sub CheckEditPNPPcaStatus_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles CheckEditPNPPcaStatus.Validated
        ' Update the data object.
        DataObject.isPNPPcaStatus = CType(sender, CheckEdit).Checked
    End Sub
    Private Sub CheckEditMediaCost_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles CheckEditMediaCost.Validated
        ' Update the data object.
        DataObject.hasMediaCost = CType(sender, CheckEdit).Checked
    End Sub
    Private Sub CheckEditMediaCostOverridable_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles CheckEditMediaCostOverridable.Validated
        ' Update the data object.
        DataObject.isCostOverridable = CType(sender, CheckEdit).Checked
    End Sub

    Private Sub ButtonAddMediaFamilyMembership_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonAddMediaFamilyMembership.Click

        ' Save first if required.
        If LiquidShell.LiquidAgent.SaveAndProceed(DataObject.Row, Me, My.Settings.DBConnection, False) = False Then
            Exit Sub
        End If

        ' Get a selection of data rows from the user and add the selected items.
        Dim SelectedItems As List(Of DataRow) = LookupMediaFamily.SelectRows(My.Settings.DBConnection, True, GridMediaFamilyMembership)
        DataObject.AddMediaFamilyMembership(Me, My.Settings.DBConnection, SelectedItems)

    End Sub

    Private Sub ButtonDeleteMediaFamilyMembership_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles ButtonRemoveMediaFamilyMembership.Click
        DataObject.DeleteChildRow(GridMediaFamilyMembership, "MediaName")
    End Sub

    Private Sub ButtonAddMediaCategory_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonAddMediaCategory.Click

        ' Save first if required.
        If LiquidShell.LiquidAgent.SaveAndProceed(DataObject.Row, Me, My.Settings.DBConnection, False) = False Then
            Exit Sub
        End If

        ' Get a selection of data rows from the user and add the selected items.
        Dim SelectedItems As List(Of DataRow) = LookupCategory.SelectRows(My.Settings.DBConnection, True, GridMediaCategory)
        DataObject.AddMediaCategory(Me, My.Settings.DBConnection, SelectedItems)

    End Sub

    Private Sub ButtonDeleteMediaCategory_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonRemoveMediaCategory.Click
        DataObject.DeleteChildRow(GridMediaCategory, "MediaName")
    End Sub

    Private Sub ButtonAddMediaLifeCycle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonAddMediaLifeCycle.Click
        OpenLifeCycleDetailSubform(True)
    End Sub

    Private Sub ButtonEditMediaLifeCycle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonEditMediaLifeCycle.Click
        OpenLifeCycleDetailSubform(False)
    End Sub

    Private Sub ButtonDeleteMediaLifeCycle_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonDeleteMediaLifeCycle.Click
        DataObject.DeleteChildRow(GridMediaLifeCycle, "MediaName")
    End Sub

    Private Sub GridMediaLifeCycle_CellMouseDoubleClick _
    (ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) _
    Handles GridMediaLifeCycle.CellMouseDoubleClick
        If e.RowIndex > -1 Then
            ' The row double-clicked is NOT the column header row. Handle the event.
            ButtonEditMediaLifeCycle_Click(sender, e)
        End If
    End Sub
    Private Sub ButtonAddMediaCost_Click(sender As Object, e As EventArgs) Handles ButtonAddMediaCost.Click
        OpenMediaCostDetailSubform(True)
    End Sub
    Private Sub ButtonEditMediaCost_Click(sender As Object, e As EventArgs) Handles ButtonEditMediaCost.Click
        OpenMediaCostDetailSubform(False)
    End Sub


#End Region

#Region "Methods"

    Protected Overrides Function Save() As Boolean
        ' Save the current object.
        If DataObject.IsDirty Then
            Dim GridsToAudit As New List(Of DataGridView)
            GridsToAudit.Add(GridMediaFamilyMembership)
            GridsToAudit.Add(GridMediaCategory)
            GridsToAudit.Add(GridMediaLifeCycle)
            GridsToAudit.Add(GridMediaCosts)
            DataObject.Save(My.Settings.DBConnection, GridsToAudit)
        End If

        If DirtyDoesIt Then
            If (checkState.Count > 0) Then
                Dim rulesChanged As List(Of RuleSelection) = New List(Of RuleSelection)()
                For Each item As KeyValuePair(Of Integer, Boolean) In checkState
                    Dim v1 As Integer = item.Key
                    Dim v2 As Boolean = item.Value
                    Dim rule As RuleSelection = New RuleSelection(v1, v2)
                    rulesChanged.Add(rule)
                Next
                Save_MediaRule_Selection(rulesChanged, DataObject.MediaID, My.Settings.DBConnection)
            End If
        End If

        'GridMediaLifeCycle
        If DataObject.IsDirty = False Then
            Dim GridsToAudit As New List(Of DataGridView)
            GridsToAudit.Add(GridMediaLifeCycle)
            GridsToAudit.Add(GridMediaCosts)
            DataObject.Save(My.Settings.DBConnection, GridsToAudit)
        End If

        Return True
    End Function

    Private Sub OpenLifeCycleDetailSubform(ByVal NewItem As Boolean)

        If NewItem = False Then
            ' User clicked the Edit button.
            Dim ParentForm As BaseForm = CType(TopLevelControl, BaseForm)
            If GridMediaLifeCycle.SelectedRows.Count < 1 Then
                ' Stop because no rows are selected.
                ParentForm.ShowMessage("Please select the row to edit before using the 'Edit' button.")
                Exit Sub
            ElseIf GridMediaLifeCycle.SelectedRows.Count > 1 Then
                ' Stop because more than one row is selected.
                ParentForm.ShowMessage("Please select only one row to edit before using the 'Edit' button.")
                Exit Sub
            End If
        End If
        ' Save first if required.
        If LiquidShell.LiquidAgent.SaveAndProceed(DataObject.Row, Me, My.Settings.DBConnection, False) = False Then
            Exit Sub
        End If

        ' Open the form to edit the item.
        AddChild(New SubformMediaLifeCycle(GridMediaLifeCycle, NewItem))

    End Sub
    Private Sub OpenMediaCostDetailSubform(ByVal NewItem As Boolean)

        If NewItem = False Then
            ' User clicked the Edit button.
            Dim ParentForm As BaseForm = CType(TopLevelControl, BaseForm)
            If GridMediaLifeCycle.SelectedRows.Count < 1 Then
                ' Stop because no rows are selected.
                ParentForm.ShowMessage("Please select the row to edit before using the 'Edit' button.")
                Exit Sub
            ElseIf GridMediaLifeCycle.SelectedRows.Count > 1 Then
                ' Stop because more than one row is selected.
                ParentForm.ShowMessage("Please select only one row to edit before using the 'Edit' button.")
                Exit Sub
            End If
        End If
        ' Save first if required.
        If LiquidShell.LiquidAgent.SaveAndProceed(DataObject.Row, Me, My.Settings.DBConnection, False) = False Then
            Exit Sub
        End If

        ' Open the form to edit the item.
        AddChild(New SubformMediaCost(GridMediaCosts, NewItem))

    End Sub

#End Region


#Region "Media Rules Section"


    Private Sub GridMediaRule_CellValueChanged(ByVal sender As Object, ByVal e As DataGridViewCellEventArgs) Handles GridMediaRule.CellContentClick
        ' Update the status bar when the cell value changes.
        If ((e.ColumnIndex = 1) AndAlso (e.RowIndex <> -1)) Then

            Dim MediaRuleID = GridMediaRule(2, e.RowIndex).Value
            Dim Selected = GridMediaRule(e.ColumnIndex, e.RowIndex).Value

            If checkState.ContainsKey(MediaRuleID) Then
                checkState(MediaRuleID) = Selected
            Else
                checkState.Add(MediaRuleID, Selected)
            End If

        End If

    End Sub

    Public Shared Function ToDataTable(Of T)(data As IList(Of T)) As DataTable
        Dim properties As PropertyDescriptorCollection = TypeDescriptor.GetProperties(GetType(T))
        Dim dt As New DataTable()
        For i As Integer = 0 To properties.Count - 1
            Dim [property] As PropertyDescriptor = properties(i)
            dt.Columns.Add([property].Name, [property].PropertyType)
        Next
        Dim values As Object() = New Object(properties.Count - 1) {}
        For Each item As T In data
            For i As Integer = 0 To values.Length - 1
                values(i) = properties(i).GetValue(item)
            Next
            dt.Rows.Add(values)
        Next
        Return dt
    End Function

    Public Sub Save_MediaRule_Selection(ByVal SelectionItems As List(Of RuleSelection), ByVal MediaID As Integer, ByVal ConnectionString As String)
        Dim errorMessage As String = String.Empty
        Dim cnn As SqlConnection = New SqlConnection(ConnectionString)
        Dim cmd As SqlCommand = New SqlCommand("Media.proc_ins_MediaRuleSelection", cnn)

        Dim retMsg As String = String.Empty
        Try
            cnn.Open()

            cmd.CommandType = CommandType.StoredProcedure
            Dim param() As SqlParameter = New SqlParameter((3) - 1) {}
            Dim selectedItemsList As DataTable = ToDataTable(SelectionItems)

            Dim IdParameter = New SqlParameter("@SelectionItems", selectedItemsList)
            IdParameter.TypeName = "dbo.ListOfTrueOrFalse"
            param(0) = IdParameter

            Dim SelectedMediaID = New SqlParameter("@MediaID", MediaID)
            param(1) = SelectedMediaID

            Dim retMsgOutput = New SqlParameter("@RetMsg", SqlDbType.VarChar, 255)
            retMsgOutput.Value = retMsg
            retMsgOutput.Direction = ParameterDirection.Output

            'the output parameter
            param(2) = retMsgOutput

            Dim i As Integer = 0
            Do While (param.Length > i)
                cmd.Parameters.Add(param(i))
                i = (i + 1)
            Loop

            'executes the stored procedure - select statement follows the insert statement
            cmd.ExecuteNonQuery()

        Catch ex As OperationCanceledException

        Catch ex As Exception
            errorMessage = ex.Message
        Finally
            If (Not (cnn) Is Nothing) Then
                cnn.Close()
            End If

            If (Not (cmd) Is Nothing) Then
                cmd.Dispose()
            End If

        End Try
    End Sub

    Private Sub GridMediaRule_CurrentCellDirtyStateChanged(sender As Object, e As EventArgs) Handles GridMediaRule.CurrentCellDirtyStateChanged
        If GridMediaRule.IsCurrentCellDirty Then
            DirtyDoesIt = True
        End If
        GridMediaRule.CommitEdit(DataGridViewDataErrorContexts.Commit)
    End Sub





#End Region

End Class

Public Class RuleSelection
    Public Sub New(ByVal ID As Integer, ByVal Selected As Boolean)
        Me.ID = ID
        Me.BooleanValue = Selected
    End Sub

    Public Property ID As Integer
    Public Property BooleanValue As Boolean
End Class