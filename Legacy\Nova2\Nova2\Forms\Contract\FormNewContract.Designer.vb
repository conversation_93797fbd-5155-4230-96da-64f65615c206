﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class FormNewContract
    Inherits LiquidShell.EditorForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(FormNewContract))
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonOK = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelContractType = New DevExpress.XtraEditors.LabelControl()
        Me.CheckEditRental = New DevExpress.XtraEditors.CheckEdit()
        Me.CheckEditResearch = New DevExpress.XtraEditors.CheckEdit()
        Me.LabelRequiredProperties = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkAccountManager = New DevExpress.XtraEditors.LabelControl()
        Me.LabelProject = New DevExpress.XtraEditors.LabelControl()
        Me.LabelAccountManager = New DevExpress.XtraEditors.LabelControl()
        Me.LabelClient = New DevExpress.XtraEditors.LabelControl()
        Me.LabelSpecialConditions = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkClient = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditProject = New DevExpress.XtraEditors.TextEdit()
        Me.LabelOptionalProperties = New DevExpress.XtraEditors.LabelControl()
        Me.LabelOptionalPropertiesInfo = New DevExpress.XtraEditors.LabelControl()
        Me.MemoEditSpecialConditions = New DevExpress.XtraEditors.MemoEdit()
        Me.CheckEditDuplicate = New DevExpress.XtraEditors.CheckEdit()
        Me.CheckEditInstallationOnlyContract = New DevExpress.XtraEditors.CheckEdit()
        Me.LabelProposalHeat = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkProposalHeat = New DevExpress.XtraEditors.LabelControl()
        Me.LabelClassification = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkClassification = New DevExpress.XtraEditors.LabelControl()
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelButtonBar.SuspendLayout()
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditRental.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditResearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditProject.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.MemoEditSpecialConditions.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditDuplicate.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditInstallationOnlyContract.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'PanelButtonBar
        '
        Me.PanelButtonBar.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelButtonBar.Appearance.BackColor2 = System.Drawing.Color.LightSteelBlue
        Me.PanelButtonBar.Appearance.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical
        Me.PanelButtonBar.Appearance.Options.UseBackColor = True
        Me.PanelButtonBar.Controls.Add(Me.ButtonOK)
        Me.PanelButtonBar.Controls.Add(Me.ButtonCancel)
        Me.PanelButtonBar.Location = New System.Drawing.Point(0, 374)
        Me.PanelButtonBar.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Office2003
        Me.PanelButtonBar.LookAndFeel.UseDefaultLookAndFeel = False
        Me.PanelButtonBar.Margin = New System.Windows.Forms.Padding(4, 0, 4, 4)
        Me.PanelButtonBar.Padding = New System.Windows.Forms.Padding(0, 12, 0, 0)
        Me.PanelButtonBar.Size = New System.Drawing.Size(685, 52)
        Me.PanelButtonBar.TabIndex = 15
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        '
        'ButtonOK
        '
        Me.ButtonOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonOK.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonOK.Appearance.Options.UseFont = True
        Me.ButtonOK.ImageIndex = 2
        Me.ButtonOK.ImageList = Me.ImageList24x24
        Me.ButtonOK.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonOK.Location = New System.Drawing.Point(467, 12)
        Me.ButtonOK.LookAndFeel.SkinName = "Black"
        Me.ButtonOK.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonOK.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonOK.Name = "ButtonOK"
        Me.ButtonOK.Size = New System.Drawing.Size(100, 28)
        Me.ButtonOK.TabIndex = 0
        Me.ButtonOK.Text = "OK"
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(573, 12)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(100, 28)
        Me.ButtonCancel.TabIndex = 1
        Me.ButtonCancel.Text = "Cancel"
        '
        'LabelContractType
        '
        Me.LabelContractType.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelContractType.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelContractType.Location = New System.Drawing.Point(12, 48)
        Me.LabelContractType.Margin = New System.Windows.Forms.Padding(3, 3, 9, 3)
        Me.LabelContractType.Name = "LabelContractType"
        Me.LabelContractType.Size = New System.Drawing.Size(86, 13)
        Me.LabelContractType.TabIndex = 1
        Me.LabelContractType.Text = "Contract Type:"
        '
        'CheckEditRental
        '
        Me.CheckEditRental.EditValue = True
        Me.CheckEditRental.Location = New System.Drawing.Point(148, 45)
        Me.CheckEditRental.Margin = New System.Windows.Forms.Padding(3, 3, 3, 4)
        Me.CheckEditRental.Name = "CheckEditRental"
        Me.CheckEditRental.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditRental.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditRental.Properties.Appearance.Options.UseFont = True
        Me.CheckEditRental.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditRental.Properties.AutoWidth = True
        Me.CheckEditRental.Properties.Caption = "Rental contract with or without production"
        Me.CheckEditRental.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Radio
        Me.CheckEditRental.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditRental.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditRental.Properties.RadioGroupIndex = 1
        Me.CheckEditRental.Size = New System.Drawing.Size(260, 19)
        Me.CheckEditRental.TabIndex = 2
        '
        'CheckEditResearch
        '
        Me.CheckEditResearch.Location = New System.Drawing.Point(467, 45)
        Me.CheckEditResearch.Margin = New System.Windows.Forms.Padding(3, 3, 3, 4)
        Me.CheckEditResearch.Name = "CheckEditResearch"
        Me.CheckEditResearch.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditResearch.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditResearch.Properties.Appearance.Options.UseFont = True
        Me.CheckEditResearch.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditResearch.Properties.AutoWidth = True
        Me.CheckEditResearch.Properties.Caption = "Research contract"
        Me.CheckEditResearch.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Radio
        Me.CheckEditResearch.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditResearch.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditResearch.Properties.RadioGroupIndex = 1
        Me.CheckEditResearch.Size = New System.Drawing.Size(125, 19)
        Me.CheckEditResearch.TabIndex = 3
        Me.CheckEditResearch.TabStop = False
        Me.CheckEditResearch.Visible = False
        '
        'LabelRequiredProperties
        '
        Me.LabelRequiredProperties.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelRequiredProperties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelRequiredProperties.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelRequiredProperties.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelRequiredProperties.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelRequiredProperties.LineVisible = True
        Me.LabelRequiredProperties.Location = New System.Drawing.Point(12, 12)
        Me.LabelRequiredProperties.Margin = New System.Windows.Forms.Padding(3, 3, 3, 12)
        Me.LabelRequiredProperties.Name = "LabelRequiredProperties"
        Me.LabelRequiredProperties.Size = New System.Drawing.Size(661, 18)
        Me.LabelRequiredProperties.TabIndex = 0
        Me.LabelRequiredProperties.Text = "Please enter the required details for this new contract"
        '
        'HyperlinkAccountManager
        '
        Me.HyperlinkAccountManager.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkAccountManager.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkAccountManager.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkAccountManager.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkAccountManager.AutoEllipsis = True
        Me.HyperlinkAccountManager.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkAccountManager.Location = New System.Drawing.Point(150, 77)
        Me.HyperlinkAccountManager.Margin = New System.Windows.Forms.Padding(3, 3, 9, 10)
        Me.HyperlinkAccountManager.Name = "HyperlinkAccountManager"
        Me.HyperlinkAccountManager.Size = New System.Drawing.Size(47, 13)
        Me.HyperlinkAccountManager.TabIndex = 6
        Me.HyperlinkAccountManager.Text = "Select..."
        '
        'LabelProject
        '
        Me.LabelProject.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelProject.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelProject.Location = New System.Drawing.Point(12, 231)
        Me.LabelProject.Margin = New System.Windows.Forms.Padding(3, 3, 9, 3)
        Me.LabelProject.Name = "LabelProject"
        Me.LabelProject.Size = New System.Drawing.Size(45, 13)
        Me.LabelProject.TabIndex = 11
        Me.LabelProject.Text = "Project:"
        '
        'LabelAccountManager
        '
        Me.LabelAccountManager.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelAccountManager.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelAccountManager.Location = New System.Drawing.Point(12, 77)
        Me.LabelAccountManager.Margin = New System.Windows.Forms.Padding(3, 3, 9, 3)
        Me.LabelAccountManager.Name = "LabelAccountManager"
        Me.LabelAccountManager.Size = New System.Drawing.Size(103, 13)
        Me.LabelAccountManager.TabIndex = 5
        Me.LabelAccountManager.Text = "Account Manager:"
        '
        'LabelClient
        '
        Me.LabelClient.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelClient.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelClient.Location = New System.Drawing.Point(12, 107)
        Me.LabelClient.Margin = New System.Windows.Forms.Padding(3, 3, 9, 30)
        Me.LabelClient.Name = "LabelClient"
        Me.LabelClient.Size = New System.Drawing.Size(38, 13)
        Me.LabelClient.TabIndex = 7
        Me.LabelClient.Text = "Client:"
        '
        'LabelSpecialConditions
        '
        Me.LabelSpecialConditions.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSpecialConditions.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelSpecialConditions.Location = New System.Drawing.Point(12, 257)
        Me.LabelSpecialConditions.Margin = New System.Windows.Forms.Padding(3, 3, 9, 3)
        Me.LabelSpecialConditions.Name = "LabelSpecialConditions"
        Me.LabelSpecialConditions.Size = New System.Drawing.Size(110, 13)
        Me.LabelSpecialConditions.TabIndex = 13
        Me.LabelSpecialConditions.Text = "Special Conditions:"
        '
        'HyperlinkClient
        '
        Me.HyperlinkClient.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkClient.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkClient.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkClient.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkClient.AutoEllipsis = True
        Me.HyperlinkClient.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkClient.Location = New System.Drawing.Point(150, 107)
        Me.HyperlinkClient.Margin = New System.Windows.Forms.Padding(3, 3, 9, 30)
        Me.HyperlinkClient.Name = "HyperlinkClient"
        Me.HyperlinkClient.Size = New System.Drawing.Size(47, 13)
        Me.HyperlinkClient.TabIndex = 8
        Me.HyperlinkClient.Text = "Select..."
        '
        'TextEditProject
        '
        Me.TextEditProject.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditProject.EditValue = ""
        Me.TextEditProject.Location = New System.Drawing.Point(150, 228)
        Me.TextEditProject.Name = "TextEditProject"
        Me.TextEditProject.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditProject.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditProject.Properties.Appearance.Options.UseFont = True
        Me.TextEditProject.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditProject.Size = New System.Drawing.Size(523, 20)
        Me.TextEditProject.TabIndex = 12
        '
        'LabelOptionalProperties
        '
        Me.LabelOptionalProperties.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelOptionalProperties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelOptionalProperties.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelOptionalProperties.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelOptionalProperties.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelOptionalProperties.LineVisible = True
        Me.LabelOptionalProperties.Location = New System.Drawing.Point(12, 190)
        Me.LabelOptionalProperties.Margin = New System.Windows.Forms.Padding(3, 3, 3, 12)
        Me.LabelOptionalProperties.Name = "LabelOptionalProperties"
        Me.LabelOptionalProperties.Size = New System.Drawing.Size(661, 18)
        Me.LabelOptionalProperties.TabIndex = 9
        Me.LabelOptionalProperties.Text = "Would you like to fluff out your contract with additional information?"
        '
        'LabelOptionalPropertiesInfo
        '
        Me.LabelOptionalPropertiesInfo.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelOptionalPropertiesInfo.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelOptionalPropertiesInfo.Location = New System.Drawing.Point(12, 209)
        Me.LabelOptionalPropertiesInfo.Margin = New System.Windows.Forms.Padding(3, 3, 9, 3)
        Me.LabelOptionalPropertiesInfo.Name = "LabelOptionalPropertiesInfo"
        Me.LabelOptionalPropertiesInfo.Size = New System.Drawing.Size(471, 13)
        Me.LabelOptionalPropertiesInfo.TabIndex = 10
        Me.LabelOptionalPropertiesInfo.Text = "These details are entirely optional. They can be added or modified later if desir" &
    "ed."
        '
        'MemoEditSpecialConditions
        '
        Me.MemoEditSpecialConditions.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.MemoEditSpecialConditions.EditValue = ""
        Me.MemoEditSpecialConditions.Location = New System.Drawing.Point(150, 254)
        Me.MemoEditSpecialConditions.Name = "MemoEditSpecialConditions"
        Me.MemoEditSpecialConditions.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.MemoEditSpecialConditions.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.MemoEditSpecialConditions.Properties.Appearance.Options.UseFont = True
        Me.MemoEditSpecialConditions.Properties.Appearance.Options.UseForeColor = True
        Me.MemoEditSpecialConditions.Size = New System.Drawing.Size(523, 117)
        Me.MemoEditSpecialConditions.TabIndex = 14
        '
        'CheckEditDuplicate
        '
        Me.CheckEditDuplicate.Location = New System.Drawing.Point(467, 71)
        Me.CheckEditDuplicate.Margin = New System.Windows.Forms.Padding(3, 3, 3, 4)
        Me.CheckEditDuplicate.Name = "CheckEditDuplicate"
        Me.CheckEditDuplicate.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditDuplicate.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditDuplicate.Properties.Appearance.Options.UseFont = True
        Me.CheckEditDuplicate.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditDuplicate.Properties.AutoWidth = True
        Me.CheckEditDuplicate.Properties.Caption = "Duplicate"
        Me.CheckEditDuplicate.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Radio
        Me.CheckEditDuplicate.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditDuplicate.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditDuplicate.Properties.RadioGroupIndex = 1
        Me.CheckEditDuplicate.Size = New System.Drawing.Size(75, 19)
        Me.CheckEditDuplicate.TabIndex = 4
        Me.CheckEditDuplicate.TabStop = False
        Me.CheckEditDuplicate.Visible = False
        '
        'CheckEditInstallationOnlyContract
        '
        Me.CheckEditInstallationOnlyContract.Enabled = False
        Me.CheckEditInstallationOnlyContract.Location = New System.Drawing.Point(467, 97)
        Me.CheckEditInstallationOnlyContract.Margin = New System.Windows.Forms.Padding(3, 3, 3, 4)
        Me.CheckEditInstallationOnlyContract.Name = "CheckEditInstallationOnlyContract"
        Me.CheckEditInstallationOnlyContract.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditInstallationOnlyContract.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditInstallationOnlyContract.Properties.Appearance.Options.UseFont = True
        Me.CheckEditInstallationOnlyContract.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditInstallationOnlyContract.Properties.AutoWidth = True
        Me.CheckEditInstallationOnlyContract.Properties.Caption = "Installation-Only Contract"
        Me.CheckEditInstallationOnlyContract.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Radio
        Me.CheckEditInstallationOnlyContract.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditInstallationOnlyContract.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditInstallationOnlyContract.Properties.RadioGroupIndex = 1
        Me.CheckEditInstallationOnlyContract.Size = New System.Drawing.Size(169, 19)
        Me.CheckEditInstallationOnlyContract.TabIndex = 16
        Me.CheckEditInstallationOnlyContract.TabStop = False
        Me.CheckEditInstallationOnlyContract.Visible = False
        '
        'LabelProposalHeat
        '
        Me.LabelProposalHeat.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelProposalHeat.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelProposalHeat.Location = New System.Drawing.Point(12, 137)
        Me.LabelProposalHeat.Margin = New System.Windows.Forms.Padding(3, 3, 9, 3)
        Me.LabelProposalHeat.Name = "LabelProposalHeat"
        Me.LabelProposalHeat.Size = New System.Drawing.Size(107, 13)
        Me.LabelProposalHeat.TabIndex = 1
        Me.LabelProposalHeat.Text = "Proposal Strenght:"
        '
        'HyperlinkProposalHeat
        '
        Me.HyperlinkProposalHeat.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkProposalHeat.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkProposalHeat.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkProposalHeat.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkProposalHeat.AutoEllipsis = True
        Me.HyperlinkProposalHeat.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkProposalHeat.Location = New System.Drawing.Point(150, 137)
        Me.HyperlinkProposalHeat.Margin = New System.Windows.Forms.Padding(3, 3, 9, 10)
        Me.HyperlinkProposalHeat.Name = "HyperlinkProposalHeat"
        Me.HyperlinkProposalHeat.Size = New System.Drawing.Size(47, 13)
        Me.HyperlinkProposalHeat.TabIndex = 3
        Me.HyperlinkProposalHeat.Text = "Select..."
        '
        'LabelClassification
        '
        Me.LabelClassification.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelClassification.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelClassification.Location = New System.Drawing.Point(12, 161)
        Me.LabelClassification.Margin = New System.Windows.Forms.Padding(3, 3, 9, 3)
        Me.LabelClassification.Name = "LabelClassification"
        Me.LabelClassification.Size = New System.Drawing.Size(84, 13)
        Me.LabelClassification.TabIndex = 17
        Me.LabelClassification.Text = "Classification :"
        '
        'HyperlinkClassification
        '
        Me.HyperlinkClassification.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkClassification.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkClassification.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkClassification.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkClassification.AutoEllipsis = True
        Me.HyperlinkClassification.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkClassification.Location = New System.Drawing.Point(150, 161)
        Me.HyperlinkClassification.Margin = New System.Windows.Forms.Padding(3, 3, 9, 10)
        Me.HyperlinkClassification.Name = "HyperlinkClassification"
        Me.HyperlinkClassification.Size = New System.Drawing.Size(47, 13)
        Me.HyperlinkClassification.TabIndex = 18
        Me.HyperlinkClassification.Text = "Select..."
        '
        'FormNewContract
        '
        Me.AcceptButton = Me.ButtonOK
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.CancelButton = Me.ButtonCancel
        Me.ClientSize = New System.Drawing.Size(685, 426)
        Me.Controls.Add(Me.HyperlinkClassification)
        Me.Controls.Add(Me.LabelClassification)
        Me.Controls.Add(Me.CheckEditInstallationOnlyContract)
        Me.Controls.Add(Me.CheckEditDuplicate)
        Me.Controls.Add(Me.LabelOptionalProperties)
        Me.Controls.Add(Me.HyperlinkProposalHeat)
        Me.Controls.Add(Me.TextEditProject)
        Me.Controls.Add(Me.HyperlinkClient)
        Me.Controls.Add(Me.LabelSpecialConditions)
        Me.Controls.Add(Me.HyperlinkAccountManager)
        Me.Controls.Add(Me.LabelClient)
        Me.Controls.Add(Me.LabelOptionalPropertiesInfo)
        Me.Controls.Add(Me.LabelProject)
        Me.Controls.Add(Me.LabelAccountManager)
        Me.Controls.Add(Me.LabelRequiredProperties)
        Me.Controls.Add(Me.CheckEditResearch)
        Me.Controls.Add(Me.CheckEditRental)
        Me.Controls.Add(Me.LabelContractType)
        Me.Controls.Add(Me.MemoEditSpecialConditions)
        Me.Controls.Add(Me.LabelProposalHeat)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(5, 5, 5, 5)
        Me.Name = "FormNewContract"
        Me.Text = "Create New Contract s"
        Me.Controls.SetChildIndex(Me.LabelProposalHeat, 0)
        Me.Controls.SetChildIndex(Me.MemoEditSpecialConditions, 0)
        Me.Controls.SetChildIndex(Me.PanelButtonBar, 0)
        Me.Controls.SetChildIndex(Me.LabelContractType, 0)
        Me.Controls.SetChildIndex(Me.CheckEditRental, 0)
        Me.Controls.SetChildIndex(Me.CheckEditResearch, 0)
        Me.Controls.SetChildIndex(Me.LabelRequiredProperties, 0)
        Me.Controls.SetChildIndex(Me.LabelAccountManager, 0)
        Me.Controls.SetChildIndex(Me.LabelProject, 0)
        Me.Controls.SetChildIndex(Me.LabelOptionalPropertiesInfo, 0)
        Me.Controls.SetChildIndex(Me.LabelClient, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkAccountManager, 0)
        Me.Controls.SetChildIndex(Me.LabelSpecialConditions, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkClient, 0)
        Me.Controls.SetChildIndex(Me.TextEditProject, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkProposalHeat, 0)
        Me.Controls.SetChildIndex(Me.LabelOptionalProperties, 0)
        Me.Controls.SetChildIndex(Me.CheckEditDuplicate, 0)
        Me.Controls.SetChildIndex(Me.CheckEditInstallationOnlyContract, 0)
        Me.Controls.SetChildIndex(Me.LabelClassification, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkClassification, 0)
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelButtonBar.ResumeLayout(False)
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditRental.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditResearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditProject.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.MemoEditSpecialConditions.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditDuplicate.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditInstallationOnlyContract.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents ButtonOK As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelContractType As DevExpress.XtraEditors.LabelControl
    Friend WithEvents CheckEditRental As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents CheckEditResearch As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents LabelRequiredProperties As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkAccountManager As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelProject As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelAccountManager As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelClient As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelSpecialConditions As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkClient As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditProject As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelOptionalProperties As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelOptionalPropertiesInfo As DevExpress.XtraEditors.LabelControl
    Friend WithEvents MemoEditSpecialConditions As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents CheckEditDuplicate As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents CheckEditInstallationOnlyContract As CheckEdit
    Friend WithEvents LabelProposalHeat As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkProposalHeat As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelClassification As LabelControl
    Friend WithEvents HyperlinkClassification As LabelControl
End Class
