Public Class SubformCategory

    Private DataObject As Category

    ' Grid cell highlight color for dormant stores.
    Private isProhibitedBackColor As Color = Color.LightCoral
    Private isProhibitedSelectionBackColor As Color = Color.IndianRed
    Private isProhibitedForeColor As Color = Color.Black
    Private isProhibitedSelectionForeColor As Color = Color.White

#Region "Event Handlers"

    Private Sub SubformCategory_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        Dim ErrorMessage As String = String.Empty

        ' Load values for all controls.
        TextEditCategoryName.Text = DataObject.CategoryName
        CheckEditDormant.EditValue = DataObject.Dormant
        CheckEditAllowsCrossover.EditValue = DataObject.CrossoverAllowed
        ' Load grid data.
        GridMediaCategory.AutoGenerateColumns = False
        GridMediaCategory.DataSource = DataObject.MediaCategoryBindingSource

        GridStoreMediaCategoryPermissions.AutoGenerateColumns = False

        If GridMediaCategory.Rows.Count > 0 Then
            GridMediaCategory.Rows(0).Selected = True
            Dim SelectedMediaID As Integer = Convert.ToInt32(GridMediaCategory.SelectedCells(1).Value.ToString())

            If SelectedMediaID > 0 Then
                Dim isLoaded As Boolean = DataObject.GetStoresForSelectedMedia(DataObject.CategoryID, SelectedMediaID, My.Settings.DBConnection, ErrorMessage)

                If String.IsNullOrEmpty(ErrorMessage) And isLoaded Then
                    GridStoreMediaCategoryPermissions.DataSource = DataObject.StoreMediaCategoryPermissionBindingSource
                    Dim GridManagerStoreMediaCategoryPermissions As New GridManager(GridStoreMediaCategoryPermissions, TextEditSearchStoreMediaCategoryPermissions, Nothing, Nothing,
                    PictureAdvancedSearchStoreMediaCategoryPermissions, PictureClearStoreMediaCategoryPermissions, Nothing, Nothing)
                Else
                    CType(TopLevelControl, BaseForm).ShowMessage _
             ("Rats! Could not load store list for selected media. Something went wrong." & vbCrLf & vbCrLf _
             & ErrorMessage, "Permissions change Error", MessageBoxIcon.Error)
                    Exit Sub
                End If
            End If

        End If


    End Sub

    Public Sub New(ByVal CategoryObject As Category)
        ' This call is required by the Windows Form Designer.
        InitializeComponent()
        ' Add any initialization after the InitializeComponent() call.
        DataObject = CategoryObject
    End Sub

    Private Sub ButtonSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonSave.Click
        Save(True)
    End Sub

    Private Sub ButtonCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonCancel.Click
        DataObject.Close(Me)
    End Sub

    Private Sub TextEditCategoryName_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles TextEditCategoryName.EditValueChanged

        ' Create an object to represent the control.
        Dim CurrentControl As TextEdit = CType(sender, TextEdit)

        ' Update the form title as the user types a name.
        If Not String.IsNullOrEmpty(CurrentControl.Text) Then
            LabelTitle.Text = CurrentControl.Text
        End If

        ' Reset the error text of the control to an empty string.
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)

    End Sub

    Private Sub TextEditCategoryName_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles TextEditCategoryName.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If ValidatedControl.Text.Length = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "The name of the category may not be blank.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

        ' Update the data object.
        DataObject.CategoryName = CType(sender, TextEdit).Text

    End Sub

    Private Sub CheckEditDormant_EditValueChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles CheckEditDormant.EditValueChanged
        ' Update the data object.
        DataObject.Dormant = CType(sender, CheckEdit).EditValue
    End Sub

    Private Sub CheckEditAllowsCrossover_EditValueChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles CheckEditAllowsCrossover.EditValueChanged
        ' Update the data object.
        DataObject.CrossoverAllowed = CType(sender, CheckEdit).EditValue
    End Sub

    Private Sub ButtonAddMediaCategory_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonAddMediaCategory.Click

        ' Save first if required.
        If LiquidShell.LiquidAgent.SaveAndProceed(DataObject.Row, Me, My.Settings.DBConnection, False) = False Then
            Exit Sub
        End If

        ' Get a selection of data rows from the user and add the selected items.
        Dim SelectedItems As List(Of DataRow) = LookupMedia.SelectRows(My.Settings.DBConnection, True, GridMediaCategory)
        DataObject.AddMediaCategory(Me, My.Settings.DBConnection, SelectedItems)

    End Sub

    Private Sub ButtonDeleteMediaCategory_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonRemoveMediaCategory.Click
        DataObject.DeleteChildRow(GridMediaCategory, "CategoryName")
    End Sub

#End Region

#Region "Methods"

    Protected Overrides Function Save() As Boolean
        ' Save the current object.
        Dim GridsToAudit As New List(Of DataGridView)
        GridsToAudit.Add(GridMediaCategory)
        DataObject.Save(My.Settings.DBConnection, GridsToAudit)
        Return True
    End Function


#Region "Store Category Permissions"
    Private Sub GridStoreMediaCategoryPermissions_CellContentClick(sender As Object, e As DataGridViewCellEventArgs) Handles GridStoreMediaCategoryPermissions.CellContentClick

        Dim Successful As Boolean = False
        Dim ErrorMessage As String = String.Empty
        Dim isProhibited As Boolean = False
        Dim StoreID As Integer = 0
        Dim MediaID As Integer = 0

        ' Update the status bar when the cell value changes.
        If ((e.ColumnIndex = 3) AndAlso (e.RowIndex <> -1)) Then

            If GridStoreMediaCategoryPermissions.Columns(e.ColumnIndex).Name = "isProhibited" AndAlso TypeOf GridStoreMediaCategoryPermissions.CurrentCell Is DataGridViewCheckBoxCell Then

                'First thing's first: check if the media row on the grid is selected.
                If (GridMediaCategory.SelectedRows.Count <> 0) Then
                    StoreID = Convert.ToInt32(GridStoreMediaCategoryPermissions(0, e.RowIndex).Value)
                    isProhibited = GridStoreMediaCategoryPermissions(e.ColumnIndex, e.RowIndex).Value
                    Dim row As DataGridViewRow = Me.GridMediaCategory.SelectedRows(0)
                    MediaID = Convert.ToInt32(row.Cells("MediaID").Value)
                End If

                If StoreID > 0 And MediaID > 0 Then
                    Successful = DataObject.StoreMediaCategoryPermissionExecution_TrueForInsertFalseForDelete(isProhibited, DataObject.CategoryID, StoreID, MediaID, My.Settings.DBConnection, ErrorMessage)
                    If ((Successful = False) _
                    OrElse Not String.IsNullOrEmpty(ErrorMessage)) Then
                        GridStoreMediaCategoryPermissions.Rows(e.RowIndex).Cells("isProhibited").Value = If(isProhibited, False, True)
                        CType(TopLevelControl, BaseForm).ShowMessage _
                ("Rats! could not effect this change that you asking for." & vbCrLf & vbCrLf _
                & ErrorMessage, "Permissions change Error", MessageBoxIcon.Error)
                        Exit Sub
                    End If
                End If
            End If
        End If
    End Sub


    Private Sub GridStoreMediaCategoryPermissions_CurrentCellDirtyStateChanged(ByVal sender As Object, ByVal e As EventArgs) Handles GridStoreMediaCategoryPermissions.CurrentCellDirtyStateChanged
        If TypeOf GridStoreMediaCategoryPermissions.CurrentCell Is DataGridViewCheckBoxCell Then
            GridStoreMediaCategoryPermissions.CommitEdit(DataGridViewDataErrorContexts.Commit)
        End If
    End Sub

    Private Sub GridMediaCategory_CellClick(sender As Object, e As DataGridViewCellEventArgs) Handles GridMediaCategory.CellClick
        Dim ErrorMessage As String = String.Empty
        If e.RowIndex >= 0 Then
            Dim row As DataGridViewRow = Me.GridMediaCategory.Rows(e.RowIndex)

            Dim SelectedMediaID As Integer = Convert.ToInt32(row.Cells(1).Value.ToString())

            Dim isLoaded As Boolean = DataObject.GetStoresForSelectedMedia(DataObject.CategoryID, SelectedMediaID, My.Settings.DBConnection, ErrorMessage)

            If String.IsNullOrEmpty(ErrorMessage) And isLoaded Then
                GridStoreMediaCategoryPermissions.DataSource = DataObject.StoreMediaCategoryPermissionBindingSource
                Dim GridManagerStoreMediaCategoryPermissions As New GridManager(GridStoreMediaCategoryPermissions, TextEditSearchStoreMediaCategoryPermissions, Nothing, Nothing,
                PictureAdvancedSearchStoreMediaCategoryPermissions, PictureClearStoreMediaCategoryPermissions, Nothing, Nothing)
            Else
                CType(TopLevelControl, BaseForm).ShowMessage _
         ("Rats! Could not load store list for select media. Something went wrong." & vbCrLf & vbCrLf _
         & ErrorMessage, "Permissions change Error", MessageBoxIcon.Error)
                Exit Sub
            End If
        End If
    End Sub

    Private Sub GridStoreMediaCategoryPermissions_CellFormatting(sender As Object, e As DataGridViewCellFormattingEventArgs) Handles GridStoreMediaCategoryPermissions.CellFormatting

        ' Get the grid that raised the event.
        Dim Grid As DataGridView = CType(sender, DataGridView)

        ' Get the datarow containing the cell being formatted.
        Dim GridRow As DataGridViewRow = Grid.Rows(e.RowIndex)

        ' Get the datarow.
        Dim Row As DataRow = CType(GridRow.DataBoundItem, DataRowView).Row


        ' Check row properties.
        If Row("isProhibited") Then
            ' This store is dormant. Highlight it.
            GridRow.DefaultCellStyle.BackColor = isProhibitedBackColor
            GridRow.DefaultCellStyle.SelectionBackColor = isProhibitedSelectionBackColor
            GridRow.DefaultCellStyle.ForeColor = isProhibitedForeColor
            GridRow.DefaultCellStyle.SelectionForeColor = isProhibitedSelectionForeColor
        End If

    End Sub

#End Region



#End Region

End Class
