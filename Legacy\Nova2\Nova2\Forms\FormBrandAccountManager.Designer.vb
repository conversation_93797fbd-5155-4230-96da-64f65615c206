<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class FormBrandAccountManager
    Inherits LiquidShell.LookupForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(FormBrandAccountManager))
        Dim SuperToolTip2 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem2 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem2 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip3 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem3 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem3 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle9 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle10 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle12 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle11 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Me.ImageList16x16 = New System.Windows.Forms.ImageList(Me.components)
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.TextEditCareTakerWeeks = New DevExpress.XtraEditors.TextEdit()
        Me.LabelCareTakerWeeks = New DevExpress.XtraEditors.LabelControl()
        Me.CheckEditCareTaker = New DevExpress.XtraEditors.CheckEdit()
        Me.GroupControlBrands = New DevExpress.XtraEditors.GroupControl()
        Me.ButtonDate = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelControl14 = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearch = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearch = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearch = New DevExpress.XtraEditors.PictureEdit()
        Me.Grid = New System.Windows.Forms.DataGridView()
        Me.BrandNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.EffectiveDateColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.CurrentAccountManagerColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelButtonBar.SuspendLayout()
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditCareTakerWeeks.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditCareTaker.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControlBrands, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlBrands.SuspendLayout()
        CType(Me.TextEditSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.Grid, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'ButtonOK
        '
        Me.ButtonOK.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonOK.Appearance.Options.UseFont = True
        Me.ButtonOK.Location = New System.Drawing.Point(551, 0)
        Me.ButtonOK.LookAndFeel.SkinName = "Black"
        Me.ButtonOK.LookAndFeel.UseDefaultLookAndFeel = False
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.Location = New System.Drawing.Point(688, 0)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        '
        'PanelButtonBar
        '
        Me.PanelButtonBar.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelButtonBar.Appearance.BackColor2 = System.Drawing.Color.LightSteelBlue
        Me.PanelButtonBar.Appearance.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical
        Me.PanelButtonBar.Appearance.Options.UseBackColor = True
        Me.PanelButtonBar.Location = New System.Drawing.Point(0, 509)
        Me.PanelButtonBar.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Office2003
        Me.PanelButtonBar.LookAndFeel.UseDefaultLookAndFeel = False
        Me.PanelButtonBar.Margin = New System.Windows.Forms.Padding(6, 0, 6, 7)
        Me.PanelButtonBar.Padding = New System.Windows.Forms.Padding(0, 21, 0, 0)
        Me.PanelButtonBar.Size = New System.Drawing.Size(868, 68)
        '
        'ImageList16x16
        '
        Me.ImageList16x16.ImageStream = CType(resources.GetObject("ImageList16x16.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList16x16.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList16x16.Images.SetKeyName(0, "calendar.png")
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "accept.png")
        Me.ImageList24x24.Images.SetKeyName(1, "delete.png")
        '
        'TextEditCareTakerWeeks
        '
        Me.TextEditCareTakerWeeks.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.TextEditCareTakerWeeks.Location = New System.Drawing.Point(219, 479)
        Me.TextEditCareTakerWeeks.Margin = New System.Windows.Forms.Padding(4)
        Me.TextEditCareTakerWeeks.Name = "TextEditCareTakerWeeks"
        Me.TextEditCareTakerWeeks.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditCareTakerWeeks.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditCareTakerWeeks.Properties.Appearance.Options.UseFont = True
        Me.TextEditCareTakerWeeks.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditCareTakerWeeks.Properties.MaxLength = 200
        Me.TextEditCareTakerWeeks.Size = New System.Drawing.Size(55, 24)
        Me.TextEditCareTakerWeeks.TabIndex = 3
        '
        'LabelCareTakerWeeks
        '
        Me.LabelCareTakerWeeks.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.LabelCareTakerWeeks.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCareTakerWeeks.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCareTakerWeeks.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelCareTakerWeeks.Location = New System.Drawing.Point(15, 483)
        Me.LabelCareTakerWeeks.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelCareTakerWeeks.Name = "LabelCareTakerWeeks"
        Me.LabelCareTakerWeeks.Size = New System.Drawing.Size(162, 17)
        Me.LabelCareTakerWeeks.TabIndex = 2
        Me.LabelCareTakerWeeks.Text = "Weeks of care taking:"
        '
        'CheckEditCareTaker
        '
        Me.CheckEditCareTaker.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.CheckEditCareTaker.Location = New System.Drawing.Point(13, 447)
        Me.CheckEditCareTaker.Margin = New System.Windows.Forms.Padding(4)
        Me.CheckEditCareTaker.Name = "CheckEditCareTaker"
        Me.CheckEditCareTaker.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditCareTaker.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditCareTaker.Properties.Appearance.Options.UseFont = True
        Me.CheckEditCareTaker.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditCareTaker.Properties.AutoWidth = True
        Me.CheckEditCareTaker.Properties.Caption = " will only be the care taker of the selected clients, and not the owner of their " &
    "accounts."
        Me.CheckEditCareTaker.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditCareTaker.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditCareTaker.Size = New System.Drawing.Size(634, 21)
        Me.CheckEditCareTaker.TabIndex = 1
        '
        'GroupControlBrands
        '
        Me.GroupControlBrands.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlBrands.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlBrands.Appearance.Options.UseFont = True
        Me.GroupControlBrands.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlBrands.AppearanceCaption.Options.UseFont = True
        Me.GroupControlBrands.Controls.Add(Me.ButtonDate)
        Me.GroupControlBrands.Controls.Add(Me.LabelControl14)
        Me.GroupControlBrands.Controls.Add(Me.TextEditSearch)
        Me.GroupControlBrands.Controls.Add(Me.PictureClearSearch)
        Me.GroupControlBrands.Controls.Add(Me.PictureAdvancedSearch)
        Me.GroupControlBrands.Controls.Add(Me.Grid)
        Me.GroupControlBrands.Location = New System.Drawing.Point(15, 16)
        Me.GroupControlBrands.LookAndFeel.SkinName = "Black"
        Me.GroupControlBrands.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlBrands.Margin = New System.Windows.Forms.Padding(4, 4, 4, 20)
        Me.GroupControlBrands.Name = "GroupControlBrands"
        Me.GroupControlBrands.Size = New System.Drawing.Size(837, 408)
        Me.GroupControlBrands.TabIndex = 0
        Me.GroupControlBrands.Text = "Brand List"
        '
        'ButtonDate
        '
        Me.ButtonDate.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonDate.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonDate.Appearance.Options.UseFont = True
        Me.ButtonDate.ImageIndex = 0
        Me.ButtonDate.ImageList = Me.ImageList16x16
        Me.ButtonDate.Location = New System.Drawing.Point(6, 371)
        Me.ButtonDate.LookAndFeel.SkinName = "Black"
        Me.ButtonDate.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonDate.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonDate.Name = "ButtonDate"
        Me.ButtonDate.Size = New System.Drawing.Size(96, 30)
        Me.ButtonDate.TabIndex = 2
        Me.ButtonDate.Text = "Date"
        '
        'LabelControl14
        '
        Me.LabelControl14.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl14.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl14.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl14.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl14.Location = New System.Drawing.Point(634, 377)
        Me.LabelControl14.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelControl14.Name = "LabelControl14"
        Me.LabelControl14.Size = New System.Drawing.Size(58, 17)
        Me.LabelControl14.TabIndex = 3
        Me.LabelControl14.Text = "Search:"
        '
        'TextEditSearch
        '
        Me.TextEditSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearch.EditValue = ""
        Me.TextEditSearch.Location = New System.Drawing.Point(699, 373)
        Me.TextEditSearch.Margin = New System.Windows.Forms.Padding(4, 5, 4, 7)
        Me.TextEditSearch.Name = "TextEditSearch"
        Me.TextEditSearch.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearch.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearch.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearch.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearch.Size = New System.Drawing.Size(103, 24)
        Me.TextEditSearch.TabIndex = 4
        '
        'PictureClearSearch
        '
        Me.PictureClearSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearch.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearch.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearch.Location = New System.Drawing.Point(810, 4)
        Me.PictureClearSearch.Margin = New System.Windows.Forms.Padding(4)
        Me.PictureClearSearch.Name = "PictureClearSearch"
        Me.PictureClearSearch.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearch.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearch.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearch.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem2.Text = "Clear Search"
        ToolTipItem2.LeftIndent = 6
        ToolTipItem2.Text = "Click here to clear all search boxes."
        SuperToolTip2.Items.Add(ToolTipTitleItem2)
        SuperToolTip2.Items.Add(ToolTipItem2)
        Me.PictureClearSearch.SuperTip = SuperToolTip2
        Me.PictureClearSearch.TabIndex = 0
        Me.PictureClearSearch.TabStop = True
        '
        'PictureAdvancedSearch
        '
        Me.PictureAdvancedSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearch.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearch.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearch.Location = New System.Drawing.Point(810, 375)
        Me.PictureAdvancedSearch.Margin = New System.Windows.Forms.Padding(4, 8, 4, 9)
        Me.PictureAdvancedSearch.Name = "PictureAdvancedSearch"
        Me.PictureAdvancedSearch.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearch.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearch.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearch.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem3.Text = "Advanced Search"
        ToolTipItem3.LeftIndent = 6
        ToolTipItem3.Text = "Click here to search individual column values."
        SuperToolTip3.Items.Add(ToolTipTitleItem3)
        SuperToolTip3.Items.Add(ToolTipItem3)
        Me.PictureAdvancedSearch.SuperTip = SuperToolTip3
        Me.PictureAdvancedSearch.TabIndex = 5
        Me.PictureAdvancedSearch.TabStop = True
        '
        'Grid
        '
        Me.Grid.AllowUserToAddRows = False
        Me.Grid.AllowUserToDeleteRows = False
        Me.Grid.AllowUserToOrderColumns = True
        Me.Grid.AllowUserToResizeRows = False
        DataGridViewCellStyle9.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Grid.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle9
        Me.Grid.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Grid.BackgroundColor = System.Drawing.Color.White
        Me.Grid.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.Grid.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.Grid.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle10.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle10.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle10.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle10.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle10.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle10.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle10.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.Grid.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle10
        Me.Grid.ColumnHeadersHeight = 22
        Me.Grid.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.Grid.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.BrandNameColumn, Me.EffectiveDateColumn, Me.CurrentAccountManagerColumn})
        Me.Grid.EnableHeadersVisualStyles = False
        Me.Grid.GridColor = System.Drawing.Color.White
        Me.Grid.Location = New System.Drawing.Point(3, 29)
        Me.Grid.Margin = New System.Windows.Forms.Padding(4)
        Me.Grid.Name = "Grid"
        Me.Grid.ReadOnly = True
        Me.Grid.RowHeadersVisible = False
        DataGridViewCellStyle12.ForeColor = System.Drawing.Color.DimGray
        Me.Grid.RowsDefaultCellStyle = DataGridViewCellStyle12
        Me.Grid.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.Grid.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.Grid.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.Grid.RowTemplate.Height = 19
        Me.Grid.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.Grid.ShowCellToolTips = False
        Me.Grid.Size = New System.Drawing.Size(832, 335)
        Me.Grid.StandardTab = True
        Me.Grid.TabIndex = 1
        '
        'BrandNameColumn
        '
        Me.BrandNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.BrandNameColumn.DataPropertyName = "BrandName"
        Me.BrandNameColumn.HeaderText = "Brand"
        Me.BrandNameColumn.Name = "BrandNameColumn"
        Me.BrandNameColumn.ReadOnly = True
        '
        'EffectiveDateColumn
        '
        Me.EffectiveDateColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None
        Me.EffectiveDateColumn.DataPropertyName = "EffectiveDate"
        DataGridViewCellStyle11.Format = "d"
        DataGridViewCellStyle11.NullValue = Nothing
        Me.EffectiveDateColumn.DefaultCellStyle = DataGridViewCellStyle11
        Me.EffectiveDateColumn.HeaderText = "Effective Date"
        Me.EffectiveDateColumn.Name = "EffectiveDateColumn"
        Me.EffectiveDateColumn.ReadOnly = True
        '
        'CurrentAccountManagerColumn
        '
        Me.CurrentAccountManagerColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.None
        Me.CurrentAccountManagerColumn.DataPropertyName = "CurrentAccountManager"
        Me.CurrentAccountManagerColumn.HeaderText = "Current Account Manager"
        Me.CurrentAccountManagerColumn.Name = "CurrentAccountManagerColumn"
        Me.CurrentAccountManagerColumn.ReadOnly = True
        Me.CurrentAccountManagerColumn.Width = 200
        '
        'FormBrandAccountManager
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(9.0!, 17.0!)
        Me.ClientSize = New System.Drawing.Size(868, 577)
        Me.Controls.Add(Me.GroupControlBrands)
        Me.Controls.Add(Me.TextEditCareTakerWeeks)
        Me.Controls.Add(Me.CheckEditCareTaker)
        Me.Controls.Add(Me.LabelCareTakerWeeks)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(8, 9, 8, 9)
        Me.Name = "FormBrandAccountManager"
        Me.Text = "Add Brands to an Account Manager"
        Me.Controls.SetChildIndex(Me.LabelCareTakerWeeks, 0)
        Me.Controls.SetChildIndex(Me.CheckEditCareTaker, 0)
        Me.Controls.SetChildIndex(Me.TextEditCareTakerWeeks, 0)
        Me.Controls.SetChildIndex(Me.GroupControlBrands, 0)
        Me.Controls.SetChildIndex(Me.PanelButtonBar, 0)
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelButtonBar.ResumeLayout(False)
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditCareTakerWeeks.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditCareTaker.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControlBrands, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlBrands.ResumeLayout(False)
        CType(Me.TextEditSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.Grid, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents ImageList16x16 As System.Windows.Forms.ImageList
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents CheckEditCareTaker As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents GroupControlBrands As DevExpress.XtraEditors.GroupControl
    Friend WithEvents ButtonDate As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl14 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditSearch As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearch As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearch As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents Grid As System.Windows.Forms.DataGridView
    Friend WithEvents LabelCareTakerWeeks As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditCareTakerWeeks As DevExpress.XtraEditors.TextEdit
    Friend WithEvents BrandNameColumn As DataGridViewTextBoxColumn
    Friend WithEvents EffectiveDateColumn As DataGridViewTextBoxColumn
    Friend WithEvents CurrentAccountManagerColumn As DataGridViewTextBoxColumn
End Class
