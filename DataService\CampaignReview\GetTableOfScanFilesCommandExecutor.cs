﻿using DataAccess;
using System.Data;

namespace DataService.CampaignReview
{
    class GetTableOfScanFilesCommandExecutor : CommandExecutor<GetTableOfScanFilesCommand>
    {
        public override void Execute(GetTableOfScanFilesCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.GetScanFileTable))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    command.Table = storedprocedure.OutputTable;

                    command.Table.PrimaryKey = new DataColumn[] { command.Table.Columns[0] };
                    for (int i = 0; i < command.Table.Columns.Count; i++)
                    {
                        command.Table.Columns[i].ReadOnly = false;
                    }
                }
            }
        }
    }
}
