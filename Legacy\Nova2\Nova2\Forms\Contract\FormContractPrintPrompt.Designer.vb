<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class FormContractPrintPrompt
    Inherits LiquidShell.EditorForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(FormContractPrintPrompt))
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonOK = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelTitle = New DevExpress.XtraEditors.LabelControl()
        Me.CheckEditContract = New DevExpress.XtraEditors.CheckEdit()
        Me.CheckEditQuotation = New DevExpress.XtraEditors.CheckEdit()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelTotalWeeks = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditVAT = New DevExpress.XtraEditors.TextEdit()
        Me.chPickNPay = New System.Windows.Forms.CheckBox()
        Me.LiquidErrorManager1 = New LiquidShell.LiquidErrorManager()
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelButtonBar.SuspendLayout()
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditContract.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditQuotation.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditVAT.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.LiquidErrorManager1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'PanelButtonBar
        '
        Me.PanelButtonBar.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelButtonBar.Appearance.BackColor2 = System.Drawing.Color.LightSteelBlue
        Me.PanelButtonBar.Appearance.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical
        Me.PanelButtonBar.Appearance.Options.UseBackColor = True
        Me.PanelButtonBar.Controls.Add(Me.ButtonOK)
        Me.PanelButtonBar.Controls.Add(Me.ButtonCancel)
        Me.PanelButtonBar.Location = New System.Drawing.Point(0, 191)
        Me.PanelButtonBar.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Office2003
        Me.PanelButtonBar.LookAndFeel.UseDefaultLookAndFeel = False
        Me.PanelButtonBar.Size = New System.Drawing.Size(280, 52)
        Me.PanelButtonBar.TabIndex = 6
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        Me.ImageList24x24.Images.SetKeyName(3, "back.png")
        '
        'ButtonOK
        '
        Me.ButtonOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonOK.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonOK.Appearance.Options.UseFont = True
        Me.ButtonOK.DialogResult = System.Windows.Forms.DialogResult.OK
        Me.ButtonOK.ImageIndex = 2
        Me.ButtonOK.ImageList = Me.ImageList24x24
        Me.ButtonOK.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonOK.Location = New System.Drawing.Point(62, 12)
        Me.ButtonOK.LookAndFeel.SkinName = "Black"
        Me.ButtonOK.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonOK.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonOK.Name = "ButtonOK"
        Me.ButtonOK.Size = New System.Drawing.Size(100, 28)
        Me.ButtonOK.TabIndex = 0
        Me.ButtonOK.Text = "OK"
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(168, 12)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(100, 28)
        Me.ButtonCancel.TabIndex = 1
        Me.ButtonCancel.Text = "Cancel"
        '
        'LabelTitle
        '
        Me.LabelTitle.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelTitle.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelTitle.LineVisible = True
        Me.LabelTitle.Location = New System.Drawing.Point(12, 12)
        Me.LabelTitle.Margin = New System.Windows.Forms.Padding(3, 3, 3, 15)
        Me.LabelTitle.Name = "LabelTitle"
        Me.LabelTitle.Size = New System.Drawing.Size(256, 18)
        Me.LabelTitle.TabIndex = 0
        Me.LabelTitle.Text = "What would you like to print?"
        '
        'CheckEditContract
        '
        Me.CheckEditContract.EditValue = True
        Me.CheckEditContract.Location = New System.Drawing.Point(12, 54)
        Me.CheckEditContract.Name = "CheckEditContract"
        Me.CheckEditContract.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditContract.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditContract.Properties.Appearance.Options.UseFont = True
        Me.CheckEditContract.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditContract.Properties.Caption = "Contract"
        Me.CheckEditContract.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Radio
        Me.CheckEditContract.Properties.RadioGroupIndex = 1
        Me.CheckEditContract.Size = New System.Drawing.Size(85, 19)
        Me.CheckEditContract.TabIndex = 1
        '
        'CheckEditQuotation
        '
        Me.CheckEditQuotation.Location = New System.Drawing.Point(12, 79)
        Me.CheckEditQuotation.Margin = New System.Windows.Forms.Padding(3, 3, 3, 20)
        Me.CheckEditQuotation.Name = "CheckEditQuotation"
        Me.CheckEditQuotation.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditQuotation.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditQuotation.Properties.Appearance.Options.UseFont = True
        Me.CheckEditQuotation.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditQuotation.Properties.Caption = "Quotation"
        Me.CheckEditQuotation.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Radio
        Me.CheckEditQuotation.Properties.RadioGroupIndex = 1
        Me.CheckEditQuotation.Size = New System.Drawing.Size(85, 19)
        Me.CheckEditQuotation.TabIndex = 2
        Me.CheckEditQuotation.TabStop = False
        '
        'LabelControl1
        '
        Me.LabelControl1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl1.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelControl1.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl1.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelControl1.LineVisible = True
        Me.LabelControl1.Location = New System.Drawing.Point(12, 115)
        Me.LabelControl1.Margin = New System.Windows.Forms.Padding(3, 3, 3, 15)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(256, 18)
        Me.LabelControl1.TabIndex = 3
        Me.LabelControl1.Text = "What VAT percentage should be used?"
        '
        'LabelTotalWeeks
        '
        Me.LabelTotalWeeks.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelTotalWeeks.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelTotalWeeks.Location = New System.Drawing.Point(12, 151)
        Me.LabelTotalWeeks.Name = "LabelTotalWeeks"
        Me.LabelTotalWeeks.Size = New System.Drawing.Size(96, 13)
        Me.LabelTotalWeeks.TabIndex = 4
        Me.LabelTotalWeeks.Text = "VAT Percentage:"
        '
        'TextEditVAT
        '
        Me.TextEditVAT.EditValue = "15"
        Me.TextEditVAT.Location = New System.Drawing.Point(133, 148)
        Me.TextEditVAT.Name = "TextEditVAT"
        Me.TextEditVAT.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditVAT.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditVAT.Properties.Appearance.Options.UseFont = True
        Me.TextEditVAT.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditVAT.Properties.Mask.EditMask = "P2"
        Me.TextEditVAT.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditVAT.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TextEditVAT.Size = New System.Drawing.Size(60, 20)
        Me.TextEditVAT.TabIndex = 5
        '
        'chPickNPay
        '
        Me.chPickNPay.AutoSize = True
        Me.chPickNPay.BackColor = System.Drawing.Color.Gainsboro
        Me.chPickNPay.Location = New System.Drawing.Point(100, 39)
        Me.chPickNPay.Name = "chPickNPay"
        Me.chPickNPay.Size = New System.Drawing.Size(86, 17)
        Me.chPickNPay.TabIndex = 7
        Me.chPickNPay.Text = "Pick N Pay"
        Me.chPickNPay.UseVisualStyleBackColor = False
        '
        'LiquidErrorManager1
        '
        Me.LiquidErrorManager1.BlinkStyle = System.Windows.Forms.ErrorBlinkStyle.NeverBlink
        Me.LiquidErrorManager1.ContainerControl = Me
        Me.LiquidErrorManager1.Icon = CType(resources.GetObject("LiquidErrorManager1.Icon"), System.Drawing.Icon)
        '
        'FormContractPrintPrompt
        '
        Me.AcceptButton = Me.ButtonOK
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.CancelButton = Me.ButtonCancel
        Me.ClientSize = New System.Drawing.Size(280, 243)
        Me.Controls.Add(Me.chPickNPay)
        Me.Controls.Add(Me.TextEditVAT)
        Me.Controls.Add(Me.LabelTotalWeeks)
        Me.Controls.Add(Me.CheckEditQuotation)
        Me.Controls.Add(Me.CheckEditContract)
        Me.Controls.Add(Me.LabelControl1)
        Me.Controls.Add(Me.LabelTitle)
        Me.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.MaximizeBox = False
        Me.MinimizeBox = False
        Me.Name = "FormContractPrintPrompt"
        Me.Text = "Print Contract"
        Me.Controls.SetChildIndex(Me.PanelButtonBar, 0)
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        Me.Controls.SetChildIndex(Me.LabelControl1, 0)
        Me.Controls.SetChildIndex(Me.CheckEditContract, 0)
        Me.Controls.SetChildIndex(Me.CheckEditQuotation, 0)
        Me.Controls.SetChildIndex(Me.LabelTotalWeeks, 0)
        Me.Controls.SetChildIndex(Me.TextEditVAT, 0)
        Me.Controls.SetChildIndex(Me.chPickNPay, 0)
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelButtonBar.ResumeLayout(False)
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditContract.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditQuotation.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditVAT.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.LiquidErrorManager1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents ButtonOK As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelTitle As DevExpress.XtraEditors.LabelControl
    Friend WithEvents CheckEditContract As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents CheckEditQuotation As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelTotalWeeks As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditVAT As DevExpress.XtraEditors.TextEdit
    Friend WithEvents chPickNPay As CheckBox
    Friend WithEvents LiquidErrorManager1 As LiquidErrorManager
End Class
