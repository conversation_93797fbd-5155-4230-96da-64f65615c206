<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="ImageList24x24.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="ImageList24x24.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj00LjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAACo
        FAAAAk1TRnQBSQFMAgEBAwEAAXABAAFwAQABGAEAARgBAAT/AREBAAj/AUIBTQE2BwABNgMAASgDAAFg
        AwABGAMAAQEBAAEQBgABEkwAAf8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8Bfx4AAf8BfwGcAXMBewFv
        AZwBcwGcAXMBnAFzAXsBbwGcAXMBvQF3VAAB/wF/Af8BfwH/AX8kAAH/AX8BnAFzAVoBawF6AW8BewFv
        AXsBcwF7AW8BegFvAVoBawGcAXMB/wF/GAAB/wF/AXsBbwH+AXsB/wF/Af8BfwH/AX8B/wF/Af8BfwH/
        AX8BvQF3Ab0Bd1AAAf8BfwF7AW8BewFvAXsBbwHeAXseAAH/AX8BewFvAd0BewH+AX8B3QF7AZwBbwFb
        AWcBXAFnAXwBawGcAXMB3gF/Af4BfwGcAXMBvQF3FgABnAFzAd4BewF2AVIBkgE1AdMBOQHTATkB0wE5
        AbIBOQGyATkBfAFzAXsBbwH/AX9MAAH/AX8BfAFvAf8BewG9AXMB/wF7Ad8BdwGcAXMcAAGcAXMB3gF/
        AXwBawH2ASUBUwEBARIBAQHyAQAB8QEAAfEBAAERAQEBUwEFATYBMgG9AXsB3QF7Ad4BexQAAZwBcwH+
        AXsB7gEgAYwBGAHNARwBrQEcAa0BHAGtARwBSwEQAbgBVgHdAXsB3gF7Af8BfwH/AX8B/wF/RAAB/wF/
        AXsBbwH/AXsBrQFRAQABNAHFAUABnAFzAf8CewFvAf8BfxYAAf8BfwHeAX8BfAFrARIBAQESAQEBUwEB
        AVMBAQFTAQEBUwEBAVMBAQEzAQEBEgEBAdABAAFzAQkB3QF7AZsBcwH/AX8SAAGcAXMB/gF/AVEBLQEP
        ASkBUAEtATABLQEwAS0BUAEtAe4BIAEaAWMB/wF/Af8BfwH/AX8B3gF3AXsBbwH/AX9AAAH/AX8BewFv
        Af8BfwFSAV4BAAE0AUIBPAEAATgBIQE8ARgBawH/AX8BewFvAf8BfxQAAb0BdwH+AX8BFgEeARMBAQF0
        AQEBdAEBAXQBAQF0AQEBdAEBAVQBAQFTAQEBUwEBAVMBAQHRAQABmAFCAf4BfwH/AX8SAAGcAXMB/gF/
        AZIBNQFQAS0BcQExAXEBMQFxATEBcQExATABLQEzAUYBtAFWAZMBUgFzAU4BOQFnAf8BfwF7AW9AAAGc
        AXMB/wF7AfcBagEAATwBYgFAAYMBQAFjAUABQQE8AQABPAGUAWIB/wF/AZwBbwH/AX8IAAHeAXsBnAFz
        Af8BfwQAAZwBcwH+AX8BtQEFAVQBAQF1AQEBdQEBAXUBAQF0AQEBdAEBAXQBAQF0AQEBdAEBAVMBAQHy
        AQABFgEmAf8BfwF7AW8BnAFzAZwBcwHeAXsB/wF/CgABnAFzAf4BewGzATkBkQE1AZIBNQGSATUBkgE1
        AZIBNQFyATEBMwFGAZMBUgFzAU4BcwFOAUoBKQH3AV4B/wF7Ad4BewH/AX86AAHeAXsB/wJ7AXMBIAFA
        AUEBQAFjAUQBYwFEAWMBRAFjAUQBQgFEAQABPAHOAVkB/wF/Ad4BdwHeAXsEAAHeAXsB3gF3Ad4BdwF7
        AW8EAAGcAXMB/gF/AbYBCQF1AQEBlQEBAZUBAQGVAQEBlQEBAZUBAQGVAQEBdAEBAXQBAQF0AQEBEgEB
        AVcBMgH/AX8B/wF/Af8BfwH/AX8B/wF/Ad0CewFvAf8BfwYAAZwBcwH+AXsB1AE9AbMBOQHTAT0B0wE9
        AdMBPQHTAT0BkgE1ATsBZwH/AX8B/wF/Af8BfwFZAWsBDwFCAf8BfwH/AX8B3QF3AZwBczYAAf8BfwHe
        AXcB/wF7AYMBSAEgAUQBgwFIAWMBSAFjAUgBYwFIAWMBSAFjAUgBYgFIAQABQAFKAVUB/wF7Af8BewG9
        AXcB/wF/AZwBbwF7AXMBGAFvAd4BewGcAXMB/wF/Ab0BdwH+AX8BOAEiAVUBAQG2AQEBtgEBAZYBAQGW
        AQEBlQEBAZUBAQGVAQEBdQEBAXQBAQFTAQEB9gEdATcBKgG1ARUBtAEVAdUBGQE2ATIBOwFfAf8BfwF7
        AW8B/wF/BAABnAFzAf4BfwH0AUEBswE5AdMBPQHTAT0B0wE9AdMBPQGSATUBOgFnAf4BfwG3AVYB2QFa
        AVQBSgGPATUB2QFeAZcBVgFbAWsB/gF7Ab0BdzQAAZwBcwH/AX8BawFZAQABRAGDAUwBgwFMAYMBTAGD
        AUwBgwFMAYMBTAGDAUwBgwFMAWMBSAEAAUQBpQFMAb0BdwH/AnsBbwHeAXcBtQFqAXsBdwH/AXsBvQF3
        AZwBcwH/AX8B/gF/ATsBVwFVAQEBtgEBAbYBAQG2AQEBtgEBAbYBAQGWAQEBlQEBAZUBAQGVAQEBdAEB
        ATMBAQESAQEBEgEBARIBAQESAQEB8QEAAbABAAHZAUoB/wF/Ab0BdwQAAb0BdwH+AXsBOwFrAbgBWgHZ
        AVoB2QFaAdkBWgHZAVoB2QFaAf8BfwGXAVYBKgEMAYwBGAGtARgBrgEcAYwBGAFLARABMAEtAf8BfwGc
        AXM0AAGcAXMB/wF7AYMBTAEgAUgBgwFMAYMBTAGDAUwBgwFMAYMBTAGDAUwBgwFMAYMBTAGDAUwBgwFM
        ASABSAFBAUwBWgFzAf8BfwH/AXsBkwFqAf8BewHWAWoBvQF3AZwBcwIAAZwBcwH/AX8BGAESAXYBAQHX
        AQEBtwEBAbcBAQG2AQEBtgEBAbYBAQGVAQEBlQEBAXUBAQF0AQEBdAEBAXQBAQF0AQEBdAEBAVQBAQES
        AQEBMwEBAf8BfwGcAXMGAAGcAXMB/gF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwGXAVYBzQEc
        ATABKQEwASkBMAEpATABKQEPASUBkgE1Af8BfwGcAXM0AAG9AXcB/wF/AXIBagEAAUgBYgFQAYMBUAGD
        AVABgwFQAYMBUAGDAVABgwFQAYMBUAGDAVABgwFQAYMBUAFBAUwBAAFMAbUBagH/AX8BtQFqAVoBcwE5
        AXMB/wF7Af8BfwIAAf8BfwH+AX8B3gF/ARgBDgGWAQEB1wEBAdcBAQHXAQEBtwEBAZYBAQF1AQEBlQEB
        AXUBAQGVAQEBlQEBAZUBAQF1AQEBdAEBAXQBAQFTAQEBMgEBAd4BewGcAXMIAAH/AX8B/wF/Af8BfwH/
        AX8B/wF/AgAB/wF/Af8BfwG4AVoBDwElAVEBMQFRATEBUQExAVEBMQEwASkBswE5Af8BfwG9AXM2AAGc
        AXMB/wF/AZQBbgEAAVABQQFQAYMBVAGDAVQBgwFUAYMBVAGDAVQBgwFUAYMBVAGDAVQBgwFUAYMBVAFi
        AVABAAFMAdYBbgF6AXcBcwFqAZwBdwGcAXMGAAHeAXsB/gF/Af8BfwH7AUYB2AEBAdgBAQHXAQEBtwEB
        AdcBCQF9AWcB+wFKAVQBAQGWAQEBlgEBAZUBAQGVAQEBlQEBAXUBAQFUAQEBdAEBAf8BfwGcAXMIAAH/
        AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/AdkBXgEwASkBkgE1AZIBNQGSATUBkgE1AXEBMQHz
        AT0B/wF/Ab0BdzgAAZwBcwH/AX8BWgF3AWIBVAEgAVQBgwFUAYMBVAGDAVQBgwFUAYMBVAGDAVQBgwFU
        AYMBVAGDAVQBgwFUAUEBVAGDAVQBnAF3ARABagG9AXsBnAFzCAABnAFzAf8BfwFdAVcB2AEBAfgBAQH4
        AQEB1wEBAdcBBQHeAXsB/gF/AXUBAQG2AQEBtgEBAbYBAQG2AQEBlgEBAZUBAQETAQEBeQE2Af8BfwHe
        AXsGAAHeAnsBbwGcAXMBnAFzAZwBcwGcAXMBmwFzAXsBbwH/AX8B+QFeAXEBMQGzATkBswE5AbMBOQGz
        ATkBkgE1ARUBRgH/AX8BvQF3OgABvQF3Af8BfwHeAXsBpAFcAQABVAGDAVgBgwFYAYMBWAGDAVgBgwFY
        AYMBWAGDAVgBgwFYAYMBWAGDAVgBAAFUAbUBbgExAWoBnAF7AZwBcwYAAf8BfwHeAXsBvgFvAdgBAQH5
        AQEB+QEBAfgBAQH4AQEBtwEBAdcBAQH/AX8B+wFCAVUBAQG3AQEB1wEBAbcBAQG2AQEBVAEBAbYBBQH/
        AX8B3QF7Af8BfwQAAd4BewH/AX8B/wF/Ab0BdwG+AXcBvgF3Ab4BdwG9AXcBvgF3Af8BfwH5AWIBkgE1
        AdMBPQH0AT0B9AFBAdMBPQGyATkBFQFGAf8BfwG9AXc8AAHeAXsB/wF/Af8BfwFJAWUBAAFYAYMBXAGD
        AVwBgwFcAYMBXAGDAVwBgwFcAYMBXAGDAVwBgwFcASABWAGsAWUBUgFuAXsBdwG9AXMGAAH/AX8B/wF/
        AfwBPgHZAQEBGQECARkBAgH5AQEB+AEBAdgBAQF2AQEBfQFjAf8BfwE8AVMB+AEJAdcBAQHXAQEBtgEB
        AXkBLgH/AX8B/wF/Af8BfwYAAZwBcwH/AX8BUQExAWsBFAGtARgBrAEYAawBGAGMARgBawEUAVwBawG+
        AXcBNQFGAVYBSgE1AUoBFAFCAVYBTgEVAUYB2QFeAf8BfwG9AXc+AAH/AX8B/wF/Af8BfwHOAWkBAAFc
        AWIBYAGDAWABgwFgAYMBYAGDAWABgwFgAYMBYAFBAVwBIAFcAcYBYAG0AXIB1gF2Ad4BewH/AX8EAAH/
        AX8B/wF/AdwBMgH5AQEBGgECARkBAgEZAQIB+AEBAfgBAQF2AQEBPAFXAf8BfwH/AX8BOQESAdgBAQHY
        AQEBtwEBAX0BXwH/AX8BnAFzCAABnAFzAf8BfwEvASkB7gEkAQ8BKQEPASkBDwEpAQ8BKQGtARgB+QFe
        Af8BfwH/AX8B/wF/AXoBbwEPAUIB/wF/Af8BfwH/AX8BvQF3Af8Bf0AAAf8BfwHeAXcB/wF/AZQBcgEA
        AWABQQFgAYMBYAGDAWABgwFgAYMBYAFBAWABagFpAc4BbQEAAWAB5gFkATkBdwH/AX8B/wF/BAAB/wF/
        Af8BfwF+AVcB+gEBAToBAgEaAQIBGQECAfkBAQHYAQEBlgEBAb4BdwH/AX8BuwEmAbgBAQEZAQIB+AEB
        AdcBAQGWAQEB3gF7AZwBcwgAAbwBcwH/AX8BcQExATABKQFQAS0BUAEtAVABLQFQAS0B7wEkARoBYwH/
        AX8B/wF/Af8BfwExAUYBUgFKAf8BfwG9AXcB3gF7Af8Bf0YAAb0BdwH/AX8BWQF7ASEBZAEgAWQBYgFk
        AYMBZAGDAWQBIAFgAf8BfwHeAX8BAAFgAe8BcQH/AX8BvQF3CAABvQF3Af8BfwF7AQ4B+QEBARkBAgEZ
        AQIB+AEBAXcBAQG6AS4B/wF/Af8BfwFaAQoB+QEBARkBAgH5AQEB+AEBAXYBAQF9AWMB3gF7Af8BfwYA
        AbwBdwH/AX8BsgE1AXEBMQGRATUBkQE1AZEBNQGRATUBUQExAY4BNQGsATUBjAExAYwBMQHvAT0B/wF/
        Ab0Bd04AAb0BdwH/AX8B3QF/ASgBbQEgAWgBAAFkAQABZAEAAWQBxQFoAc4BcQEAAWQBvQF/Ad4BewoA
        Af8BfwH/AX8B/wJ7ARYB2QEBAbgBAQG4AQEBmgEqAf8BfwH/AX8B/wF/AZwBHgH6AQEBGgECARkBAgH4
        AQEBlgEBAb4BcwG9AXcIAAG9AXcB/wF/AdMBPQGSATUBsgE5AbIBOQGyATkBsgE5AXEBMQFcAWsB/wF/
        Af8BfwH/AX8B/wF/Af8BfwH/AX9QAAHeAXsB/wF/Af8BfwH/AX8BewF/ARcBewGUAXYBEAF2Ae4BcQF7
        AX8B/wF/Ab0BdwwAAd4BewH/AX8B/wF/Ad8BdwG+AW8B/wF/Af8BfwHeAXsB/wF/Af8BfwHfAXMBGgEC
        AdkBAQHYAQEBdwEBAdsBOgH/AX8BvQF3CAABvQF3Af8BfwH0AT0BswE5AdMBPQHTAT0B0wE9AdMBPQGR
        ATUBOwFrAf8BfwG9AXcBvQF3Ad4BewH/AX9UAAH/AX8BvQF3Ab0BdwHeAXsB/wF/Af8BfwH/AX8B/wF/
        Ad4BewG9AXcSAAG9AXcBvQF3Ad4BewG9AXcB3gF7BAABvQF3Af8BfwHfAXcB3AE2AZsBKgFdAVsB/wF/
        Ad4BewoAAb0BdwH/AX8BVgFOAdMBPQH0AUEB9AFBAfQBQQH0AUEB0wE5AZ0BcwHeAXsB/wF/XgAB/wF/
        Ad4BewHeAXsB3gF7Ab0BdwG9AXcB3gF7JAAB3gF7Ad4BewH/AX8B/wF/Af8BfwG9AXcB/wF/CgAB/wF/
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Ab0Bd5QAAf8BfwHeAXsBvQF3Ad4BexAA
        Af8BfwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3SAABQgFNAT4HAAE+AwABKAMAAWADAAEY
        AwABAQEAAQEFAAEgAQEWAAP/AQAD/wH4AQ8B/wHgAQ8B/wMAAfwBfwH/AeABAwH/AcABBwH/AwAB+AE/
        Af8BgAEBAf8BwAEDAf8DAAHwAR8B/wGAAQAB/wHAAQABfwMAAeABBwH/AgABfwHAAQABPwMAAcABAwH/
        AgABfwHAAQABPwMAAcABAQHjAgABBwHAAQABDwMAAYABAAHDAgABAQHAAQABBwkAAcABAAEDCQABwAEA
        AQMGAAGAAgAB4AEAAQMGAAGAAgAB8AFAAQMDAAGAAQABAQHAAgAB8AEAAQMDAAHAAQABAQHgAgAB4AEA
        AQMDAAHgAQABAQHAAgABwAEAAQMDAAHwAQABAQHAAQABAQHAAQABAwMAAfgCAAHAAQABAwHAAQABAwMA
        AfwCAAHAAQABAwHAAQABBwMAAf8BAAEBAeABAAEBAcABAAE/AwAB/wGAAQMB4AEAAQMBwAEAAT8DAAH/
        AcABAwHwAQABAwHAAQABfwMAAf8B4AEHAfwBGAEHAcABAwH/AwAB/wH4AQ8B/wH8AQcBwAEHAf8DAAT/
        Af4BHwHgAQ8B/wMACw==
</value>
  </data>
</root>