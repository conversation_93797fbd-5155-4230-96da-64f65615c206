Public Class SubformAccountManager

    Private DataObject As AccountManager

#Region "Event Handlers"

    Private Sub Subform_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        ' Load values for all controls.
        If Not String.IsNullOrEmpty(DataObject.Username) Then
            HyperlinkUser.Text = DataObject.Username
        Else
            HyperlinkUser.Text = "Select..."
        End If
        TextEditFirstName.EditValue = DataObject.FirstName
        TextEditLastName.EditValue = DataObject.LastName
        TextEditEmail.EditValue = DataObject.Email
        TextEditCode.EditValue = DataObject.Code
        CheckEditDormant.EditValue = DataObject.Dormant

        ' Load grid data.
        GridClientAccountManager.AutoGenerateColumns = False
        GridClientAccountManager.DataSource = DataObject.ClientAccountMangerBindingSource
        GridLinkedContracts.AutoGenerateColumns = False
        GridLinkedContracts.DataSource = DataObject.LinkedContractsByClientPeriodBindingSource
        GridBudget.AutoGenerateColumns = False
        GridBudget.DataSource = DataObject.BudgetBindingSource
        GridBrandAccountManager.AutoGenerateColumns = False
        GridBrandAccountManager.DataSource = DataObject.BrandAccountMangerBindingSource


        ' Configure grid managers.
        Dim GridManagerClientAccountManager As New GridManager(GridClientAccountManager, TextEditSearchClientAccountManager, Nothing, Nothing, _
        PictureAdvancedSearchClientAccountManager, PictureClearSearchClientAccountManager, Nothing, ButtonRemoveClientAccountManager)
        Dim GridManagerLinkedContracts As New GridManager(GridLinkedContracts, TextEditSearchLinkedContracts, Nothing, Nothing, _
        PictureAdvancedSearchLinkedContracts, PictureClearSearchLinkedContracts, Nothing, Nothing)
        Dim GridManagerBudgets As New GridManager(GridBudget, TextEditSearchBudget, Nothing, Nothing,
        PictureAdvancedSearchBudget, PictureClearSearchBudget, ButtonEditBudget, ButtonDeleteBudget)
        Dim GridManagerBrandAccountManager As New GridManager(GridBrandAccountManager, TextEditSearchBrandAccountManager, Nothing, Nothing,
       PictureAdvancedSearchBrandAccountManager, PictureClearSearchBrandAccountManager, Nothing, ButtonRemoveBrandAccountManager)




    End Sub

    Public Sub New(ByVal AccountManagerObject As AccountManager)
        ' This call is required by the Windows Form Designer.
        InitializeComponent()
        ' Add any initialization after the InitializeComponent() call.
        DataObject = AccountManagerObject
    End Sub

    Private Sub ButtonSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonSave.Click
        Save(True)
    End Sub

    Private Sub ButtonPermissions_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonPermissions.Click

        ' Save first if required.
        If LiquidShell.LiquidAgent.SaveAndProceed(DataObject.Row, Me, My.Settings.DBConnection, DataObject.IsDirty) = False Then
            Exit Sub
        End If

        ' Get a bindingsource with only this one account manager in its list to pass into the constructor 
        ' of the permissions form.
        Dim Errors As String = String.Empty
        Dim AccountManagerBindingSource As BindingSource = AccountManager.GetListData _
        (My.Settings.DBConnection, Errors, Nothing, DataObject.Username)

        ' Create and display a new permissions form.
        If Errors.Length > 0 Then
            ' Data didn't load correctly.
            CType(TopLevelControl, BaseForm).ShowMessage("Errors occured while loading account manager permission data." _
            & vbCrLf & vbCrLf & Errors, _
            "Data Load Error", MessageBoxIcon.Error)
        Else
            ' Data loaded correctly. Display the form.
            Dim PermissionsForm As New FormAccountManagerUserPermissions _
            (New AccountManager(AccountManagerBindingSource, False, My.Settings.DBConnection))
            PermissionsForm.ShowDialog()
        End If

    End Sub

    Private Sub ButtonCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonCancel.Click
        DataObject.Close(Me)
    End Sub

    Private Sub TextEditName_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles TextEditFirstName.EditValueChanged, TextEditLastName.EditValueChanged

        ' Create an object to represent the control.
        Dim CurrentControl As TextEdit = CType(sender, TextEdit)

        ' Update the form title as the user types a name.
        If Not String.IsNullOrEmpty(TextEditFirstName.EditValue) AndAlso Not String.IsNullOrEmpty(TextEditLastName.EditValue) Then
            LabelTitle.Text = TextEditFirstName.EditValue & " " & TextEditLastName.EditValue
        End If

        ' Reset the error text of the control to an empty string.
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)

    End Sub

    Private Sub TextEditEmail_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles TextEditEmail.EditValueChanged, TextEditCode.EditValueChanged

        ' Create an object to represent the control.
        Dim CurrentControl As TextEdit = CType(sender, TextEdit)

        ' Reset the error text of the control to an empty string.
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)

    End Sub

    Private Sub TextEditFirstName_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles TextEditFirstName.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If ValidatedControl.Text.Length = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "The first name of the account manager may not be blank.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

        ' Update the data object.
        DataObject.FirstName = ValidatedControl.EditValue

    End Sub

    Private Sub TextEditLastName_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles TextEditLastName.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If ValidatedControl.Text.Length = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "The last name of the account manager may not be blank.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

        ' Update the data object.
        DataObject.LastName = ValidatedControl.EditValue

    End Sub

    Private Sub TextEditCode_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles TextEditCode.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        Dim Errors As New System.Text.StringBuilder
        If Not ValidatedControl.Text.Length = 2 Then
            Errors.Append("The code of the account manager must be two characters in length.")
        End If
        For i As Integer = 0 To ValidatedControl.Text.Length - 1
            If Not Char.IsLetter(ValidatedControl.Text, i) Then
                If Errors.Length > 0 Then
                    ' Add a line break before adding another error.
                    Errors.Append(vbCrLf)
                End If
                Errors.Append("The code of the account manager must contain only alphabetic letters.")
                Exit For
            End If
        Next

        ' Send validation data.
        LiquidAgent.ControlValidation(ValidatedControl, Errors.ToString)

        ' Update the data object.
        DataObject.Code = ValidatedControl.Text.ToUpper

    End Sub

    Private Sub TextEditEmail_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles TextEditEmail.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If Not LiquidAgent.IsValidEmailAddress(ValidatedControl.Text) Then
            LiquidAgent.ControlValidation(ValidatedControl, "The email address entered for the account manager is not valid.")
        End If

        ' Update the data object.
        DataObject.Email = ValidatedControl.Text

    End Sub

    Private Sub CheckEditDormant_EditValueChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles CheckEditDormant.EditValueChanged
        ' Update the data object.
        DataObject.Dormant = CType(sender, CheckEdit).Checked
    End Sub

    Private Sub ButtonAddClientAccountManager_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles ButtonAddClientAccountManager.Click

        ' Save first if required.
        If LiquidShell.LiquidAgent.SaveAndProceed(DataObject.Row, Me, My.Settings.DBConnection, False) = False Then
            Exit Sub
        End If

        ' Get a list of new client account datarows from the user.
        Dim ClientList As List(Of DataRow) = LookupClient.SelectRows(My.Settings.DBConnection, True, Nothing)

        If ClientList.Count = 0 Then
            ' User has not selected any clients.
            Exit Sub
        Else
            ' User has selected at least one client. Proceed to display config form.
            Dim ClientAccountManager As New FormClientAccountManager(DataObject.FirstName, ClientList)
            Dim NewClientAccounts As List(Of DataRow) = FormClientAccountManager.SelectRows(ClientAccountManager, String.Empty)
            ' Add the new client accounts to the data object.
            DataObject.AddClientAccountManager(NewClientAccounts)
        End If

    End Sub

    Private Sub ButtonRemoveClientAccountManager_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles ButtonRemoveClientAccountManager.Click
        DataObject.DeleteChildRow(GridClientAccountManager, "FullName")
    End Sub

    Private Sub HyperlinkUser_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles HyperlinkUser.Click

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupUser.SelectRows(My.Settings.DBConnection, False, Nothing)

        ' Update the data object and the label with the selection.
        If IsNothing(SelectedItems) = False Then
            If SelectedItems.Count > 0 Then
                DataObject.principal_id = SelectedItems(0).Item("principal_id")
                DataObject.Username = SelectedItems(0).Item("name")
                CurrentControl.Text = SelectedItems(0).Item("name").ToString
                ' Reset the error text of the control to an empty string.
                LiquidAgent.ControlValidation(CurrentControl, String.Empty)
            End If
        End If

    End Sub

    Private Sub HyperlinkUser_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles HyperlinkUser.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        If String.Compare(ValidatedControl.Text, "Select...") = 0 _
        Or String.Compare(ValidatedControl.Text, "(unknown)") = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "The account manager must be linked to a system user.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub ButtonAddBudget_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles ButtonAddBudget.Click
        OpenBudgetDetailSubform(True)
    End Sub

    Private Sub ButtonEditBudget_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles ButtonEditBudget.Click
        OpenBudgetDetailSubform(False)
    End Sub

    Private Sub ButtonDeleteBudget_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonDeleteBudget.Click
        DataObject.DeleteChildRow(GridBudget, "FullName")
    End Sub

    Private Sub GridBudget_CellMouseDoubleClick(ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) Handles GridBudget.CellMouseDoubleClick
        If e.RowIndex > -1 Then
            ' The row double-clicked is NOT the column header row. Handle the event.
            ButtonEditBudget_Click(sender, e)
        End If
    End Sub

#End Region

#Region "Methods"

    Protected Overrides Function Save() As Boolean
        ' Save the current object.
        Dim GridsToAudit As New List(Of DataGridView)
        GridsToAudit.Add(GridClientAccountManager)
        GridsToAudit.Add(GridBrandAccountManager)
        GridsToAudit.Add(GridBudget)
        DataObject.Save(My.Settings.DBConnection, GridsToAudit)
        Return True
    End Function

    Private Sub OpenBudgetDetailSubform(ByVal NewItem As Boolean)

        If NewItem = False Then
            ' User clicked the Edit button.
            Dim ParentForm As BaseForm = CType(TopLevelControl, BaseForm)
            If GridBudget.SelectedRows.Count < 1 Then
                ' Stop because no rows are selected.
                ParentForm.ShowMessage("Please select the row to edit before using the 'Edit' button.")
                Exit Sub
            ElseIf GridBudget.SelectedRows.Count > 1 Then
                ' Stop because more than one row is selected.
                ParentForm.ShowMessage("Please select only one row to edit before using the 'Edit' button.")
                Exit Sub
            End If
        End If

        ' Save first if required.
        If LiquidShell.LiquidAgent.SaveAndProceed(DataObject.Row, Me, My.Settings.DBConnection, False) = False Then
            Exit Sub
        End If

        ' Open the form to edit the item.
        AddChild(New SubformAccountManagerBudget(GridBudget, NewItem))

    End Sub

    Private Sub ButtonAddBrandAccountManager_Click(sender As Object, e As EventArgs) Handles ButtonAddBrandAccountManager.Click
        If LiquidShell.LiquidAgent.SaveAndProceed(DataObject.Row, Me, My.Settings.DBConnection, False) = False Then
            Exit Sub
        End If

        ' Get a list of new client account datarows from the user.
        Dim BrandList As List(Of DataRow) = LookupBrand.SelectRows(My.Settings.DBConnection, True, Nothing)

        If BrandList.Count = 0 Then
            ' User has not selected any clients.
            Exit Sub
        Else
            ' User has selected at least one client. Proceed to display config form.
            Dim ClientBrandManager As New FormBrandAccountManager(DataObject.FirstName, BrandList)
            Dim NewBrandAccounts As List(Of DataRow) = FormBrandAccountManager.SelectRows(ClientBrandManager, String.Empty)
            ' Add the new client accounts to the data object.
            DataObject.AddBrandAccountManager(NewBrandAccounts)
        End If
    End Sub

    Private Sub ButtonRemoveBrandAccountManager_Click(sender As Object, e As EventArgs) Handles ButtonRemoveBrandAccountManager.Click
        DataObject.DeleteChildRow(GridBrandAccountManager, "FullName")
    End Sub

#End Region

End Class
