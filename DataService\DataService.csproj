﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{8DDE8153-DA42-403B-8CE0-85AE455E6EA5}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>DataService</RootNamespace>
    <AssemblyName>DataService</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="System" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Data" />
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.IO.Compression.FileSystem" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="CampaignReviewServices.cs" />
    <Compile Include="CampaignReview\ArchiveDecompressor.cs" />
    <Compile Include="CampaignReview\DataConverter.cs" />
    <Compile Include="CampaignReview\DeleteScanFilesCommand.cs" />
    <Compile Include="CampaignReview\DeleteScanFilesCommandExecutor.cs" />
    <Compile Include="CampaignReview\GetTableOfScanFilesCommand.cs" />
    <Compile Include="CampaignReview\GetTableOfScanFilesCommandExecutor.cs" />
    <Compile Include="CampaignReview\ImportScannerDataCommand.cs" />
    <Compile Include="CampaignReview\ImportScannerDataCommandExecutor.cs" />
    <Compile Include="Finance\GetTableOfStorePaymentsCommandExecutor.cs" />
    <Compile Include="FinanceServices.cs" />
    <Compile Include="Finance\GetTableOfStorePaymentsCommand.cs" />
    <Compile Include="Legacy\AddLegacyRoleMembersCommand.cs" />
    <Compile Include="Legacy\AddLegacyRoleMembersCommandExecutor.cs" />
    <Compile Include="Legacy\AddLegacyRolesOfMemberCommand.cs" />
    <Compile Include="Legacy\AddLegacyRolesOfMemberCommandExecutor.cs" />
    <Compile Include="Legacy\DisableLegacyLoginsCommand.cs" />
    <Compile Include="Legacy\DisableLegacyLoginsCommandExecutor.cs" />
    <Compile Include="Legacy\EnableLegacyLoginsCommand.cs" />
    <Compile Include="Legacy\EnableLegacyLoginsCommandExecutor.cs" />
    <Compile Include="Legacy\GetContractStatusCommand.cs" />
    <Compile Include="Legacy\GetContractStatusCommandExecutor.cs" />
    <Compile Include="Legacy\RemoveLegacyRoleMembersCommand.cs" />
    <Compile Include="Legacy\RemoveLegacyRoleMembersCommandExecutor.cs" />
    <Compile Include="Legacy\RemoveLegacyRolesOfMemberCommand.cs" />
    <Compile Include="Legacy\RemoveLegacyRolesOfMemberCommandExecutor.cs" />
    <Compile Include="Sales\GetTableOfCategoriesCommand.cs" />
    <Compile Include="Sales\GetTableOfCategoriesCommandExecutor.cs" />
    <Compile Include="Sales\GetTableOfContractsCommand.cs" />
    <Compile Include="Sales\GetTableOfContractsCommandExecutor.cs" />
    <Compile Include="Sales\GetTableOfMediaServicesCommand.cs" />
    <Compile Include="Sales\GetTableOfMediaServicesCommandExecutor.cs" />
    <Compile Include="SalesServices.cs" />
    <Compile Include="Security\Roles\AddRoleMembersCommand.cs" />
    <Compile Include="Security\Roles\AddRoleMembersCommandExecutor.cs" />
    <Compile Include="Security\Roles\AddRoleOwnersCommand.cs" />
    <Compile Include="Security\Roles\AddRoleOwnersCommandExecutor.cs" />
    <Compile Include="Security\Roles\CheckRoleMembershipOfUserCommand.cs" />
    <Compile Include="Security\Roles\CheckRoleMembershipOfUserCommandExecutor.cs" />
    <Compile Include="Security\Roles\CreateRoleCommand.cs" />
    <Compile Include="Security\Roles\CreateRoleCommandExecutor.cs" />
    <Compile Include="Security\Roles\DeleteRolesCommand.cs" />
    <Compile Include="Security\Roles\DeleteRolesCommandExecutor.cs" />
    <Compile Include="Security\Roles\GetUserRolesCommand.cs" />
    <Compile Include="Security\Roles\GetUserRolesCommandExecutor.cs" />
    <Compile Include="Security\Users\DeleteUserChainPermissionsCommand.cs" />
    <Compile Include="Security\Users\DeleteUserChainPermissionsCommandExecutor.cs" />
    <Compile Include="Security\Roles\GetRoleHistoryCommand.cs" />
    <Compile Include="Security\Roles\GetRoleHistoryCommandExecutor.cs" />
    <Compile Include="Security\Roles\GetRoleMemberCandidatesCommand.cs" />
    <Compile Include="Security\Roles\GetRoleMemberCandidatesCommandExecutor.cs" />
    <Compile Include="Security\Roles\GetRoleMembersCommand.cs" />
    <Compile Include="Security\Roles\GetRoleMembersCommandExecutor.cs" />
    <Compile Include="Security\Roles\GetRoleOwnerCandidatesCommand.cs" />
    <Compile Include="Security\Roles\GetRoleOwnerCandidatesCommandExecutor.cs" />
    <Compile Include="Security\Roles\GetRoleOwnersCommand.cs" />
    <Compile Include="Security\Roles\GetRoleOwnersCommandExecutor.cs" />
    <Compile Include="Security\Roles\GetTableOfRolesCommand.cs" />
    <Compile Include="Security\Roles\GetTableOfRolesCommandExecutor.cs" />
    <Compile Include="Security\Users\AddRolesOfMemberCommand.cs" />
    <Compile Include="Security\Users\AddRolesOfMemberCommandExecutor.cs" />
    <Compile Include="Security\Users\AddRolesOfOwnerCommand.cs" />
    <Compile Include="Security\Users\AddRolesOfOwnerCommandExecutor.cs" />
    <Compile Include="Security\Users\DeleteUsersCommand.cs" />
    <Compile Include="Security\Users\DeleteUsersCommandExecutor.cs" />
    <Compile Include="Security\Users\GetChainPermissionCandidatesCommand.cs" />
    <Compile Include="Security\Users\GetChainPermissionCandidatesCommandExecutor.cs" />
    <Compile Include="Security\Users\GetRolesOfOwnerCandidatesCommand.cs" />
    <Compile Include="Security\Users\GetRolesOfOwnerCandidatesCommandExecutor.cs" />
    <Compile Include="Security\Users\GetRolesOfOwnerCommand.cs" />
    <Compile Include="Security\Users\GetRolesOfOwnerCommandExecutor.cs" />
    <Compile Include="Security\Users\GetTableOfUsersCommand.cs" />
    <Compile Include="Security\Users\GetTableOfUsersCommandExecutor.cs" />
    <Compile Include="Security\GetTermsOfUseCommand.cs" />
    <Compile Include="Security\GetTermsOfUseCommandExecutor.cs" />
    <Compile Include="Security\Users\GetUserAuditTrailCommand.cs" />
    <Compile Include="Security\Users\GetUserAuditTrailCommandExecutor.cs" />
    <Compile Include="Security\Users\GetUserHistoryCommand.cs" />
    <Compile Include="Security\Users\GetUserHistoryCommandExecutor.cs" />
    <Compile Include="Security\Roles\RemoveRoleMembersCommand.cs" />
    <Compile Include="Security\Roles\RemoveRoleMembersCommandExecutor.cs" />
    <Compile Include="Security\Roles\RemoveRoleOwnersCommand.cs" />
    <Compile Include="Security\Roles\RemoveRoleOwnersCommandExecutor.cs" />
    <Compile Include="Security\SaveAcceptanceOfTermsOfUseCommand.cs" />
    <Compile Include="Security\SaveAcceptanceOfTermsOfUseCommandExecutor.cs" />
    <Compile Include="SecurityServices.cs" />
    <Compile Include="Security\TerminateSessionCommand.cs" />
    <Compile Include="Security\TerminateSessionCommandExecutor.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Security\Roles\UpdateRoleCommand.cs" />
    <Compile Include="Security\Roles\UpdateRoleCommandExecutor.cs" />
    <Compile Include="Security\UpdateThemeColorCommand.cs" />
    <Compile Include="Security\UpdateThemeColorCommandExecutor.cs" />
    <Compile Include="Security\Users\GetRolesOfMemberCandidatesCommand.cs" />
    <Compile Include="Security\Users\GetRolesOfMemberCandidatesCommandExecutor.cs" />
    <Compile Include="Security\Users\GetRolesOfMemberCommand.cs" />
    <Compile Include="Security\Users\GetRolesOfMemberCommandExecutor.cs" />
    <Compile Include="Security\Users\GetUserChainPermissionsCommand.cs" />
    <Compile Include="Security\Users\GetUserChainPermissionsCommandExecutor.cs" />
    <Compile Include="Security\Users\RemoveRolesOfMemberCommand.cs" />
    <Compile Include="Security\Users\RemoveRolesOfMemberCommandExecutor.cs" />
    <Compile Include="Security\Users\RemoveRolesOfOwnerCommand.cs" />
    <Compile Include="Security\Users\RemoveRolesOfOwnerCommandExecutor.cs" />
    <Compile Include="Security\Users\SetUserChainPermissionCommand.cs" />
    <Compile Include="Security\Users\SetUserChainPermissionCommandExecutor.cs" />
    <Compile Include="Security\Users\UpdateUserCommand.cs" />
    <Compile Include="Security\Users\UpdateUserCommandExecutor.cs" />
    <Compile Include="StoreUniverse\GetTableOfChainsCommand.cs" />
    <Compile Include="StoreUniverse\GetTableOfChainsCommandExecutor.cs" />
    <Compile Include="StoreUniverse\GetTableOfHeadOfficesCommand.cs" />
    <Compile Include="StoreUniverse\GetTableOfHeadOfficesCommandExecutor.cs" />
    <Compile Include="StoreUniverse\GetTableOfStoresCommand.cs" />
    <Compile Include="StoreUniverse\GetTableOfStoresCommandExecutor.cs" />
    <Compile Include="StoreUniverseServices.cs" />
    <Compile Include="SystemSettingsServices.cs" />
    <Compile Include="SystemSettings\GetTableOfSystemSettingsCommand.cs" />
    <Compile Include="SystemSettings\GetTableOfSystemSettingsCommandExecutor.cs" />
    <Compile Include="SystemSettings\UpdateSystemSettingCommand.cs" />
    <Compile Include="SystemSettings\UpdateSystemSettingCommandExecutor.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\DataAccess\DataAccess.csproj">
      <Project>{e286623b-f881-4689-a82b-5b20f930d364}</Project>
      <Name>DataAccess</Name>
    </ProjectReference>
    <ProjectReference Include="..\Messaging\Messaging.csproj">
      <Project>{9bd12911-b2a2-4237-b90a-9191fdc54c2b}</Project>
      <Name>Messaging</Name>
    </ProjectReference>
    <ProjectReference Include="..\Universal\Universal.csproj">
      <Project>{b6b01178-1f34-4c5d-8601-e666a10f675b}</Project>
      <Name>Universal</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>