﻿using DataAccess;
using System.Data;

namespace DataService.StoreUniverse
{
    class GetTableOfHeadOfficesCommandExecutor : CommandExecutor<GetTableOfHeadOfficesCommand>
    {
        public override void Execute(GetTableOfHeadOfficesCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.GetHeadOfficeTable))
            {
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    command.Table = storedprocedure.OutputTable;
                    command.Table.PrimaryKey = new DataColumn[] { command.Table.Columns[0] };
                }
            }
        }
    }
}
