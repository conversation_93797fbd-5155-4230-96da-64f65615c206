﻿using DataAccess;

namespace DataService.Security
{
    class AddRolesOfMemberCommandExecutor : CommandExecutor<AddRolesOfMemberCommand>
    {

        public override void Execute(AddRolesOfMemberCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.AddRolesOfMember))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.AddInputParameter("userid", command.UserId);
                storedprocedure.AddInputParameter("newroles", command.NewRoles);
                storedprocedure.AddOutputParameter<string>("username");
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;

                if (string.IsNullOrEmpty(command.ErrorMessage))
                {
                    command.Username = storedprocedure.GetOutputParameterValue("username").ToString();
                }
            }
        }

    }
}
