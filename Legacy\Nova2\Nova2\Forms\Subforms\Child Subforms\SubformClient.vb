Public Class SubformClient

    Private NewAccountManagerID As Integer = 0

#Region "Fields"
    Private _ThisRow As DataSetClient.ClientRow
    Private _ThisTable As DataSetClient.ClientDataTable
    Private _ThisDataSet As DataSetClient
    Private _IsNew As Boolean = False
#End Region

#Region "Properties"

    Private Property ThisRow As DataSetClient.ClientRow
        Get
            Return _ThisRow
        End Get
        Set(value As DataSetClient.ClientRow)
            _ThisRow = value
            _ThisTable = value.Table
            _ThisDataSet = value.Table.DataSet
            If value.RowState = DataRowState.Added Then
                _IsNew = True
            End If
        End Set
    End Property

    Private ReadOnly Property ThisTable As DataSetClient.ClientDataTable
        Get
            Return _ThisTable
        End Get
    End Property

    Private ReadOnly Property ThisDataSet As DataSetClient
        Get
            Return _ThisDataSet
        End Get
    End Property

    Public ReadOnly Property IsNew As Boolean
        Get
            Return _IsNew
        End Get
    End Property

    Private WriteOnly Property Classification As DataRow
        Set(value As DataRow)
            ThisRow.ClassificationID = value("ClassificationID")
            ThisRow.ClassificationName = value("ClassificationName")
        End Set
    End Property

    Public ReadOnly Property ClassificationName As String
        Get
            Return ThisRow.ClassificationName
        End Get
    End Property

    Private WriteOnly Property City As DataRow
        Set(value As DataRow)
            ThisRow.CityID = value("CityID")
            ThisRow.CityName = value("CityName")
        End Set
    End Property

    Public ReadOnly Property CityName As String
        Get
            Return ThisRow.CityName
        End Get
    End Property

    Private WriteOnly Property Terms As DataRow
        Set(value As DataRow)
            ThisRow.TermsID = value("TermsID")
            ThisRow.TermsName = value("TermsName")
        End Set
    End Property

    Public ReadOnly Property TermsName As String
        Get
            Return ThisRow.TermsName
        End Get
    End Property

    Private WriteOnly Property AccountManager As DataRow
        Set(value As DataRow)
            NewAccountManagerID = value("AccountManagerID")
            ThisRow.AccountManagerName = value("FirstName") & " " & value("LastName")
        End Set
    End Property

    Public ReadOnly Property AccountManagerName As String
        Get
            Return ThisRow.AccountManagerName
        End Get
    End Property

#End Region

#Region "Constructors"

    Public Sub New(Row As DataSetClient.ClientRow)

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        ThisRow = Row

    End Sub

#End Region

#Region "Public Methods"

    Public Shared Sub CreateNew(Table As DataSetClient.ClientDataTable, ParentSubform As Subform)
        Dim NewRow As DataSetClient.ClientRow = Table.NewRow
        Table.Rows.Add(NewRow)
        EditExisting(NewRow, ParentSubform)
    End Sub

    Public Shared Sub EditExisting(Row As DataSetClient.ClientRow, ParentSubform As Subform)
        Dim Editor As New SubformClient(Row)
        ParentSubform.AddChild(Editor)
    End Sub

#End Region

#Region "Event Handlers"

    Private Sub Subform_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        AddDataBindings()
    End Sub

    Private Sub ButtonCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonCancel.Click
        ThisTable.RejectChanges()
        ClearDatabindings(Me)
        RevertToParentSubform()
    End Sub

    Private Sub TextEditClientName_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles TextEditClientName.EditValueChanged

        ' Create an object to represent the control.
        Dim CurrentControl As TextEdit = CType(sender, TextEdit)

        ' Update the form title as the user types a name.
        If Not String.IsNullOrEmpty(CurrentControl.Text) Then
            LabelTitle.Text = CurrentControl.Text
        End If

        ' Reset the error text of the control to an empty string.
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)

    End Sub

    Private Sub TextEdit_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles TextEditAddressLine1.EditValueChanged,
    TextEditAddressLine2.EditValueChanged,
    TextEditFax.EditValueChanged,
    MemoEditNotes.EditValueChanged,
    TextEditPostalCode.EditValueChanged,
    TextEditTelephone.EditValueChanged,
    TextEditClientAbbreviation.EditValueChanged

        ' Create an object to represent the control.
        Dim CurrentControl As TextEdit = CType(sender, TextEdit)

        ' Reset the error text of the control to an empty string.
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)

    End Sub

    Private Sub HyperlinkClassification_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles HyperlinkClassification.Click

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupClassification.SelectRows(My.Settings.DBConnection, True, Nothing)

        ' Update the data object and the label with the selection.
        If IsNothing(SelectedItems) = False Then
            If SelectedItems.Count > 0 Then
                Classification = SelectedItems(0)
                CurrentControl.DataBindings("Text").ReadValue()
                ' Reset the error text of the control to an empty string.
                LiquidAgent.ControlValidation(CurrentControl, String.Empty)
            End If
        End If

    End Sub

    Private Sub HyperlinkTerms_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles HyperlinkTerms.Click

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupTerms.SelectRows(My.Settings.DBConnection, True, Nothing)

        ' Update the data object and the label with the selection.
        If IsNothing(SelectedItems) = False Then
            If SelectedItems.Count > 0 Then
                Terms = SelectedItems(0)
                CurrentControl.DataBindings("Text").ReadValue()
                ' Reset the error text of the control to an empty string.
                LiquidAgent.ControlValidation(CurrentControl, String.Empty)
            End If
        End If

    End Sub

    Private Sub HyperlinkCity_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles HyperlinkCity.Click

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupCity.SelectRows(My.Settings.DBConnection, True, Nothing)

        ' Update the data object and the label with the selection.
        If IsNothing(SelectedItems) = False Then
            If SelectedItems.Count > 0 Then
                City = SelectedItems(0)
                CurrentControl.DataBindings("Text").ReadValue()
                ' Reset the error text of the control to an empty string.
                LiquidAgent.ControlValidation(CurrentControl, String.Empty)
            End If
        End If

    End Sub

    Private Sub HyperlinkAccountManager_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles HyperlinkAccountManager.Click

        ' Stop if the user is trying to modify an existing client.
        If IsNew = False AndAlso My.User.IsInRole("sales_manager") = False Then
            CType(TopLevelControl, BaseForm).ShowMessage("Only the sales manager may change the account manager for this client.",
                                                        "Not Permitted")
            Exit Sub
        End If

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupAccountManager.SelectRowsByPermission_EditMyClients(My.Settings.DBConnection, True, Nothing)

        ' Update the data object and the label with the selection.
        If IsNothing(SelectedItems) = False Then
            If SelectedItems.Count > 0 Then
                AccountManager = SelectedItems(0)
                CurrentControl.DataBindings("Text").ReadValue()
                ' Reset the error text of the control to an empty string.
                LiquidAgent.ControlValidation(CurrentControl, String.Empty)
            End If
        End If

    End Sub

    Private Sub ButtonSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonSave.Click
        ' Save and close.
        Save(True)
    End Sub

#End Region

#Region "Validation"

    Private Sub TextEditClientName_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles TextEditClientName.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If ValidatedControl.Text.Length = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "The name of the client may not be blank.")
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub
    Private Sub TextEditClientAbbreviation_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles TextEditClientAbbreviation.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If ValidatedControl.Text.Length = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "The abbreviation of the client may not be blank.")
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub TextEditFax_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles TextEditFax.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If Trim(ValidatedControl.EditValue).Length <> 0 AndAlso Trim(ValidatedControl.EditValue).Length <> 10 Then
            LiquidAgent.ControlValidation(ValidatedControl, "The fax number must be 10 digits in length.")
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub TextEditPostalCode_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles TextEditPostalCode.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If Trim(ValidatedControl.EditValue).Length <> 0 AndAlso Trim(ValidatedControl.EditValue).Length <> 4 Then
            LiquidAgent.ControlValidation(ValidatedControl, "The postal code must be 4 digits in length.")
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub TextEditTelephone_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles TextEditTelephone.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If Trim(ValidatedControl.EditValue).Length <> 0 AndAlso Trim(ValidatedControl.EditValue).Length <> 10 Then
            LiquidAgent.ControlValidation(ValidatedControl, "The telephone number must be 10 digits in length.")
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub TextEditVatNumber_Validated(ByVal sender As Object, ByVal e As System.EventArgs)

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If Trim(ValidatedControl.EditValue).Length <> 0 AndAlso Trim(ValidatedControl.EditValue).Length <> 10 Then
            LiquidAgent.ControlValidation(ValidatedControl, "The VAT number must be 10 digits in length.")
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkClassification_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles HyperlinkClassification.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        If String.Compare(ValidatedControl.Text, "Select...") = 0 _
        Or String.Compare(ValidatedControl.Text, "(unknown)") = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "The Classification must be selected.")
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkCity_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles HyperlinkCity.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        If String.Compare(ValidatedControl.Text, "Select...") = 0 _
        Or String.Compare(ValidatedControl.Text, "(unknown)") = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "The city must be selected.")
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkTerms_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles HyperlinkTerms.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        If String.Compare(ValidatedControl.Text, "Select...") = 0 _
        Or String.Compare(ValidatedControl.Text, "(unknown)") = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "The payment terms must be selected.")
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

#End Region

#Region "Private Methods"

    Private Sub AddDataBindings()

        HyperlinkAccountManager.DataBindings.Add("Text", Me, "AccountManagerName", False, DataSourceUpdateMode.OnPropertyChanged)
        HyperlinkClassification.DataBindings.Add("Text", Me, "ClassificationName", False, DataSourceUpdateMode.OnPropertyChanged)
        HyperlinkCity.DataBindings.Add("Text", Me, "CityName", False, DataSourceUpdateMode.OnPropertyChanged)
        HyperlinkTerms.DataBindings.Add("Text", Me, "TermsName", False, DataSourceUpdateMode.OnPropertyChanged)

        TextEditClientName.DataBindings.Add("EditValue", ThisRow, "ClientName", False, DataSourceUpdateMode.OnValidation)
        TextEditClientAbbreviation.DataBindings.Add("EditValue", ThisRow, "ClientAbbreviation", False, DataSourceUpdateMode.OnValidation)

        TextEditTelephone.DataBindings.Add("EditValue", ThisRow, "Telephone", False, DataSourceUpdateMode.OnValidation)
        TextEditFax.DataBindings.Add("EditValue", ThisRow, "Fax", False, DataSourceUpdateMode.OnValidation)
        TextEditAddressLine1.DataBindings.Add("EditValue", ThisRow, "AddressLine1", False, DataSourceUpdateMode.OnValidation)
        TextEditAddressLine2.DataBindings.Add("EditValue", ThisRow, "AddressLine2", False, DataSourceUpdateMode.OnValidation)
        TextEditPostalCode.DataBindings.Add("EditValue", ThisRow, "PostalCode", False, DataSourceUpdateMode.OnValidation)
        CheckEditAgency.DataBindings.Add("EditValue", ThisRow, "Agency", False, DataSourceUpdateMode.OnValidation)
        CheckEditRetailer.DataBindings.Add("EditValue", ThisRow, "Retailer", False, DataSourceUpdateMode.OnValidation)
        CheckEditDormant.DataBindings.Add("EditValue", ThisRow, "Dormant", False, DataSourceUpdateMode.OnValidation)
        CheckEditApproved.DataBindings.Add("EditValue", ThisRow, "ApprovedByFinance", False, DataSourceUpdateMode.OnValidation)
        MemoEditNotes.DataBindings.Add("EditValue", ThisRow, "Notes", False, DataSourceUpdateMode.OnValidation)

    End Sub

    Private Sub ClearDatabindings(Container As Control)
        ' Clear the databindings of all the controls contained in the given control.
        For Each DataBoundControl As Control In Container.Controls
            If DataBoundControl.HasChildren Then
                ClearDatabindings(DataBoundControl)
            End If
            DataBoundControl.DataBindings.Clear()
        Next
    End Sub

    Protected Overrides Function Save() As Boolean
        ' Instantiate table adapters to update the database, and then update the database.

        ' Create the required table adapters.
        Dim ClientAdapter As New DataSetClientTableAdapters.ClientTableAdapter
        Using SqlCon As New SqlClient.SqlConnection(My.Settings.DBConnection)
            ClientAdapter.Connection = SqlCon

            ClientAdapter.Update(ThisTable)

            ' Save the account manager associated with this client if it is a new client.
            If IsNew Then
                ' First we need to create the new account manager row in the ClientAccountManager table.
                Dim NewAccountManager As DataSetClient.ClientAccountManagerRow = ThisDataSet.ClientAccountManager.NewRow
                NewAccountManager.ClientID = ThisRow.ClientID
                NewAccountManager.AccountManagerID = NewAccountManagerID
                Dim LastMonday As Date = LiquidAgent.GetLastMonday(My.Settings.DBConnection, CType(TopLevelControl, BaseForm))
                NewAccountManager.EffectiveDate = LastMonday
                ThisDataSet.ClientAccountManager.Rows.Add(NewAccountManager)
                ' Now we need to save the data back to the database.
                Dim AccountManagerAdapter As New DataSetClientTableAdapters.ClientAccountManagerTableAdapter
                AccountManagerAdapter.Connection = SqlCon
                AccountManagerAdapter.Update(ThisDataSet.ClientAccountManager)
                AccountManagerAdapter.Dispose()
            Else
                If NewAccountManagerID <> 0 Then
                    Dim NewAccountManager As DataSetClient.ClientAccountManagerRow = ThisDataSet.ClientAccountManager.NewRow
                    NewAccountManager.ClientID = ThisRow.ClientID
                    NewAccountManager.AccountManagerID = NewAccountManagerID
                    Dim LastMonday As Date = LiquidAgent.GetLastMonday(My.Settings.DBConnection, CType(TopLevelControl, BaseForm))
                    NewAccountManager.EffectiveDate = LastMonday
                    ThisDataSet.ClientAccountManager.Rows.Add(NewAccountManager)
                    ' Now we need to save the data back to the database.
                    Dim AccountManagerAdapter As New DataSetClientTableAdapters.ClientAccountManagerTableAdapter
                    AccountManagerAdapter.Connection = SqlCon
                    AccountManagerAdapter.Update(ThisDataSet.ClientAccountManager)
                    AccountManagerAdapter.Dispose()
                End If
            End If
        End Using
        ClientAdapter.Dispose()

        ' Close the subform.
        RevertToParentSubform()

    End Function

#End Region

End Class
