﻿using System;
using System.Drawing;
using System.Windows.Forms;

namespace Framework
{
    public partial class MessageForm : Forms.DataForm
    {

        #region Startup

        private MessageForm(string message, string title)
        {
            Construct(message, title);
            SetButtonsOk();
        }

        private MessageForm(string message, string title, MessageFormButtons buttons)
        {
            Construct(message, title);
            if (buttons == MessageFormButtons.OkCancel)
            {
                SetButtonsOkCancel();
            }
            if (buttons == MessageFormButtons.YesNo)
            {
                SetButtonsYesNo();
            }
        }

        private void Construct(string message, string title)
        {
            InitializeComponent();
            Text = title;
            panelMessage.MaximumSize = new Size(Screen.PrimaryScreen.Bounds.Width - 56, Screen.PrimaryScreen.Bounds.Height - 179);

            int maxwidth = GetIdealMaxWidth(message, 400);
            int currentmaxheight = labelMessage.MaximumSize.Height;
            labelMessage.MaximumSize = new Size(maxwidth, currentmaxheight);

            labelMessage.Text = message;
        }

        private int GetIdealMaxWidth(string message, int desiredmaxheight)
        {
            // Create a temporary form needed to calculate the label width.
            using (Forms.DataForm form = new Forms.DataForm())
            {
                // Create a temporary label to calculate the ideal width.
                using (Label label = new Label())
                {
                    // Add the message to the label's Text property to check the minimum height when there's no width constraint.
                    form.Controls.Add(label);
                    label.AutoSize = true;
                    label.Text = message;
                    int minheight = label.PreferredHeight;

                    // If the desired maximum height is less than the minimum height, then the desired height is not possible.
                    desiredmaxheight = (desiredmaxheight < minheight ? minheight : desiredmaxheight);

                    // Set the maximum size of the temporary label to that of our actual label on the form.
                    label.MaximumSize = labelMessage.MaximumSize;

                    // If the needed height is greater than what is desired, keep increasing the width so that fewer lines are wapped
                    // and thus less height is needed.
                    while (label.PreferredHeight > desiredmaxheight)
                    {
                        label.Text = string.Empty;
                        label.MaximumSize = new Size(label.MaximumSize.Width + 100, label.MaximumSize.Height);
                        label.Text = message;
                    }
                    return label.MaximumSize.Width;
                }
            }
        }

        #endregion


        #region Public interface

        public static DialogResult Show(string message)
        {
            using (MessageForm messageform = new MessageForm(message, "Attention!"))
            {
                messageform.ShowDialog();
                return messageform.DialogResult;
            }
        }

        public static DialogResult Show(string message, string title)
        {
            using (MessageForm messageform = new MessageForm(message, title))
            {
                messageform.ShowDialog();
                return messageform.DialogResult;
            }
        }

        public static DialogResult Show(string message, string title, MessageFormButtons buttons)
        {
            using (MessageForm messageform = new MessageForm(message, title, buttons))
            {
                messageform.ShowDialog();
                return messageform.DialogResult;
            }
        }

        #endregion


        #region Button configuration

        public enum MessageFormButtons { OkCancel, YesNo }

        private void SetButtonsOk()
        {
            flatButtonOK.Text = "OK";
            flatButtonCancel.Visible = false;
            int CentreLocation = flatButtonOK.Parent.Width / 2 - flatButtonOK.Width / 2;
            flatButtonOK.Location = new System.Drawing.Point(CentreLocation, flatButtonOK.Location.Y);
            flatButtonOK.Click += DialogResultOf_FlatButtonOK_Click;
        }

        private void SetButtonsOkCancel()
        {
            flatButtonOK.Text = "OK";
            flatButtonCancel.Text = "Cancel";
            flatButtonOK.Click += DialogResultOf_FlatButtonOK_Click;
            flatButtonCancel.Click += DialogResultOf_FlatButtonCancel_Click;
        }

        private void DialogResultOf_FlatButtonOK_Click(object sender, System.EventArgs e)
        {
            DialogResult = DialogResult.OK;
        }

        private void DialogResultOf_FlatButtonCancel_Click(object sender, System.EventArgs e)
        {
            DialogResult = DialogResult.Cancel;
        }

        private void SetButtonsYesNo()
        {
            flatButtonOK.Text = "Yes";
            flatButtonCancel.Text = "No";
            flatButtonOK.Click += DialogResultOf_FlatButtonYes_Click;
            flatButtonCancel.Click += DialogResultOf_FlatButtonNo_Click;
        }

        private void DialogResultOf_FlatButtonYes_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Yes;
        }

        private void DialogResultOf_FlatButtonNo_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.No;
        }

        #endregion

    }
}
