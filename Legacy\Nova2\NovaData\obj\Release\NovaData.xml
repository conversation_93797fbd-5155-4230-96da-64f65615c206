﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
NovaData
</name>
</assembly>
<members>
<member name="T:NovaData.My.Resources.Resources">
<summary>
  A strongly-typed resource class, for looking up localized strings, etc.
</summary>
</member>
<member name="P:NovaData.My.Resources.Resources.ResourceManager">
<summary>
  Returns the cached ResourceManager instance used by this class.
</summary>
</member>
<member name="P:NovaData.My.Resources.Resources.Culture">
<summary>
  Overrides the current thread's CurrentUICulture property for all
  resource lookups using this strongly typed resource class.
</summary>
</member>
<member name="P:NovaData.My.Resources.Resources.Contract_Cancelled_64">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:NovaData.My.Resources.Resources.Contract_Signed_64">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="P:NovaData.My.Resources.Resources.Contract_Unsigned_64">
<summary>
  Looks up a localized resource of type System.Drawing.Bitmap.
</summary>
</member>
<member name="T:NovaData.DataSetBrandCategory">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:NovaData.DataSetBrandCategory.BrandCategoryDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetBrandCategory.BrandCategoryMemberDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetBrandCategory.BrandDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetBrandCategory.BrandCategoryRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetBrandCategory.BrandCategoryMemberRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetBrandCategory.BrandRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetBrandCategory.BrandCategoryRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetBrandCategory.BrandCategoryMemberRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetBrandCategory.BrandRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetBrandCategoryTableAdapters.BrandCategoryTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetBrandCategoryTableAdapters.BrandCategoryMemberTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetBrandCategoryTableAdapters.BrandTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetBrandFamily">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:NovaData.DataSetBrandFamily.BrandFamilyDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetBrandFamily.BrandFamilyMemberDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetBrandFamily.BrandDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetBrandFamily.BrandFamilyRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetBrandFamily.BrandFamilyMemberRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetBrandFamily.BrandRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetBrandFamily.BrandFamilyRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetBrandFamily.BrandFamilyMemberRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetBrandFamily.BrandRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetBudgets">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:NovaData.DataSetCategory">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:NovaData.DataSetCategory.BurstCountDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetCategory.MediaCategoryDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetCategory.MediaDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetCategory.CategoryDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetCategory.BurstCountRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetCategory.MediaCategoryRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetCategory.MediaRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetCategory.CategoryRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetCategory.BurstCountRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetCategory.MediaCategoryRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetCategory.MediaRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetCategory.CategoryRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetClient">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:NovaData.DataSetClient.ClientDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetClient.ClientAccountManagerDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetClient.ClientRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetClient.ClientAccountManagerRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetClient.ClientRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetClient.ClientAccountManagerRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetClientTableAdapters.ClientTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetClientTableAdapters.ClientAccountManagerTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetClientTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:NovaData.DataSetClientTableAdapters.TableAdapterManager.UpdateUpdatedRows(NovaData.DataSetClient,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:NovaData.DataSetClientTableAdapters.TableAdapterManager.UpdateInsertedRows(NovaData.DataSetClient,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:NovaData.DataSetClientTableAdapters.TableAdapterManager.UpdateDeletedRows(NovaData.DataSetClient,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:NovaData.DataSetClientTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:NovaData.DataSetClientTableAdapters.TableAdapterManager.UpdateAll(NovaData.DataSetClient)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:NovaData.DataSetClientTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:NovaData.DataSetClientTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:NovaData.DataSetContractDate">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:NovaData.DataSetContractDate.ContractDateDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContractDate.ContractActivityDateDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContractDate.ContractDateRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContractDate.ContractActivityDateRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContractDate.ContractDateRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContractDate.ContractActivityDateRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContractDateTableAdapters.ContractDateTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractDateTableAdapters.ContractActivityDateTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractDateTableAdapters.TableAdapterManager">
<summary>
TableAdapterManager is used to coordinate TableAdapters in the dataset to enable Hierarchical Update scenarios
</summary>
</member>
<member name="M:NovaData.DataSetContractDateTableAdapters.TableAdapterManager.UpdateUpdatedRows(NovaData.DataSetContractDate,System.Collections.Generic.List{System.Data.DataRow},System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Update rows in top-down order.
</summary>
</member>
<member name="M:NovaData.DataSetContractDateTableAdapters.TableAdapterManager.UpdateInsertedRows(NovaData.DataSetContractDate,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Insert rows in top-down order.
</summary>
</member>
<member name="M:NovaData.DataSetContractDateTableAdapters.TableAdapterManager.UpdateDeletedRows(NovaData.DataSetContractDate,System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Delete rows in bottom-up order.
</summary>
</member>
<member name="M:NovaData.DataSetContractDateTableAdapters.TableAdapterManager.GetRealUpdatedRows(System.Data.DataRow[],System.Collections.Generic.List{System.Data.DataRow})">
<summary>
Remove inserted rows that become updated rows after calling TableAdapter.Update(inserted rows) first
</summary>
</member>
<member name="M:NovaData.DataSetContractDateTableAdapters.TableAdapterManager.UpdateAll(NovaData.DataSetContractDate)">
<summary>
Update all changes to the dataset.
</summary>
</member>
<member name="T:NovaData.DataSetContractDateTableAdapters.TableAdapterManager.UpdateOrderOption">
<summary>
Update Order Option
</summary>
</member>
<member name="T:NovaData.DataSetContractDateTableAdapters.TableAdapterManager.SelfReferenceComparer">
<summary>
Used to sort self-referenced table's rows
</summary>
</member>
<member name="T:NovaData.DataSetContractInfo">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:NovaData.DataSetContractInfo.ContractInfoDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContractInfo.BurstInfoDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContractInfo.ContractInfoRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContractInfo.BurstInfoRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContractInfo.ContractInfoRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContractInfo.BurstInfoRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetStoreGroups">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:NovaData.DataSetStoreGroups.GroupChainDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetStoreGroups.StoreDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetStoreGroups.ChainGroupStoreDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetStoreGroups.GroupChainRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetStoreGroups.StoreRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetStoreGroups.ChainGroupStoreRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetStoreGroups.GroupChainRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetStoreGroups.StoreRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetStoreGroups.ChainGroupStoreRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetStoreGroupsTableAdapters.GroupChainTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetStoreGroupsTableAdapters.StoreTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetStoreGroupsTableAdapters.ChainGroupStoreTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetMediaGap">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:NovaData.DataSetMediaGap.BurstDataDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetMediaGap.ProvisionalBookingDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetMediaGap.ContractListDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetMediaGap.BurstDataRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetMediaGap.ProvisionalBookingRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetMediaGap.ContractListRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetMediaGap.BurstDataRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetMediaGap.ProvisionalBookingRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetMediaGap.ContractListRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetAudit">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:NovaData.DataSetAudit.AuditLogDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetAudit.AuditLogRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetAudit.AuditLogRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetBrand">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:NovaData.DataSetBrand.BrandDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetBrand.ClientBrandDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetBrand.ClientDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetBrand.BurstCountByBrandDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetBrand.BrandRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetBrand.ClientBrandRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetBrand.ClientRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetBrand.BurstCountByBrandRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetBrand.BrandRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetBrand.ClientBrandRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetBrand.ClientRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetBrand.BurstCountByBrandRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetBrandTableAdapters.BrandTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetBrandTableAdapters.ClientBrandTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetBrandTableAdapters.ClientTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetBrandTableAdapters.BurstCountByBrandTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetBrandFamilyTableAdapters.BrandFamilyTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetBrandFamilyTableAdapters.BrandFamilyMemberTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetBrandFamilyTableAdapters.BrandTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContract">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:NovaData.DataSetContract.ContractDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.BurstDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.AccountManagerDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.BurstLoadingFeeDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.BurstCategoryDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.LoadingFeeDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.CategoryDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.PurchaseOrderNumberDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.ContractInventoryQtyDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.ContractMiscellaneousChargeDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.StorePoolDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.BrandFamilyMemberDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.PeerBurstDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.CompetingBurstDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.StoreListDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.StoreDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.IndependentStoreListMemberDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.IndependentStoreListDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.ValidMediaDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.ResearchCategoryDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.BillingInstructionDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.ContractClassificaitonDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.BurstInstallationDayDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.BurstPcaStatusDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.MediaCostDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.ContractInvoicesDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.ContractCostEstimatesDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.ContractBrandAgencyDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.ContractRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.BurstRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.AccountManagerRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.BurstLoadingFeeRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.BurstCategoryRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.LoadingFeeRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.CategoryRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.PurchaseOrderNumberRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.ContractInventoryQtyRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.ContractMiscellaneousChargeRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.StorePoolRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.BrandFamilyMemberRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.PeerBurstRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.CompetingBurstRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.StoreListRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.StoreRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.IndependentStoreListMemberRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.IndependentStoreListRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.ValidMediaRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.ResearchCategoryRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.BillingInstructionRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.ContractClassificaitonRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.BurstInstallationDayRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.BurstPcaStatusRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.MediaCostRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.ContractInvoicesRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.ContractCostEstimatesRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.ContractBrandAgencyRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetContract.ContractRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContract.BurstRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContract.AccountManagerRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContract.BurstLoadingFeeRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContract.BurstCategoryRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContract.LoadingFeeRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContract.CategoryRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContract.PurchaseOrderNumberRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContract.ContractInventoryQtyRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContract.ContractMiscellaneousChargeRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContract.StorePoolRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContract.BrandFamilyMemberRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContract.PeerBurstRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContract.CompetingBurstRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContract.StoreListRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContract.StoreRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContract.IndependentStoreListMemberRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContract.IndependentStoreListRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContract.ValidMediaRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContract.ResearchCategoryRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContract.BillingInstructionRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContract.ContractClassificaitonRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContract.BurstInstallationDayRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContract.BurstPcaStatusRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContract.MediaCostRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContract.ContractInvoicesRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContract.ContractCostEstimatesRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContract.ContractBrandAgencyRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.ContractTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.BurstTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.AccountManagerTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.BurstLoadingFeeTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.BurstCategoryTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.LoadingFeeTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.CategoryTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.PurchaseOrderNumberTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.ContractInventoryQtyTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.ContractMiscellaneousChargeTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.StorePoolTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.BrandFamilyMemberTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.PeerBurstTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.CompetingBurstTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.StoreListTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.StoreTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.IndependentStoreListMemberTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.IndependentStoreListTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.ValidMediaTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.ResearchCategoryTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.BillingInstructionTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.ContractClassificaitonTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.BurstInstallationDayTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.BurstPcaStatusTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.MediaCostTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.ContractInvoicesTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.ContractCostEstimatesTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractTableAdapters.ContractBrandAgencyTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractInfoTableAdapters.ContractInfoTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetContractInfoTableAdapters.BurstInfoTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetMediaGapTableAdapters.BurstDataTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetMediaGapTableAdapters.ProvisionalBookingTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.ContractCountDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.ClientDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.vClientAccountManagerDatesDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.ClientAccountManagerDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.LinkedContractsByClientPeriodDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.ExistingAccountManagersDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.AccountManagerBudgetsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.FiscalDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.AccountManagerPermissionDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.sql_loginsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.AccountManagerPermissionUserDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.AccountManagerDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.BrandAccountManagerDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.BrandDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.ExistingBrandManagerDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.vBrandAccountManagersDatesDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.ContractCountRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.ClientRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.vClientAccountManagerDatesRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.ClientAccountManagerRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.LinkedContractsByClientPeriodRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.ExistingAccountManagersRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.AccountManagerBudgetsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.FiscalRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.AccountManagerPermissionRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.sql_loginsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.AccountManagerPermissionUserRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.AccountManagerRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.BrandAccountManagerRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.BrandRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.ExistingBrandManagerRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.vBrandAccountManagersDatesRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.ContractCountRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.ClientRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.vClientAccountManagerDatesRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.ClientAccountManagerRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.LinkedContractsByClientPeriodRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.ExistingAccountManagersRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.AccountManagerBudgetsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.FiscalRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.AccountManagerPermissionRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.sql_loginsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.AccountManagerPermissionUserRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.AccountManagerRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.BrandAccountManagerRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.BrandRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.ExistingBrandManagerRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetAccountManager.vBrandAccountManagersDatesRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetAccountManagerTableAdapters.ContractCountTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManagerTableAdapters.ClientTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManagerTableAdapters.vClientAccountManagerDatesTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManagerTableAdapters.ClientAccountManagerTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManagerTableAdapters.LinkedContractsByClientPeriodTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManagerTableAdapters.ExistingAccountManagersTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManagerTableAdapters.AccountManagerBudgetsTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManagerTableAdapters.FiscalTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManagerTableAdapters.AccountManagerPermissionTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManagerTableAdapters.sql_loginsTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManagerTableAdapters.AccountManagerPermissionUserTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManagerTableAdapters.AccountManagerTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManagerTableAdapters.BrandAccountManagerTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManagerTableAdapters.BrandTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManagerTableAdapters.ExistingBrandManagerTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetAccountManagerTableAdapters.vBrandAccountManagersDatesTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetAuditTableAdapters.AuditLogTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetCategoryTableAdapters.BurstCountTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetCategoryTableAdapters.MediaCategoryTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetCategoryTableAdapters.MediaTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetCategoryTableAdapters.CategoryTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetMedia">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaCategoryDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.CategoryDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaFamilyMemberDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaFamilyDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.CompetingMediaDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaLifeCycleDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.BurstCountDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.StoreMediaCategoryPermissionsDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.StoreMediaCategoryPermissionDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaGroupDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaGroupMemberDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaChannelDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaChannelGroupMemberDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaCostDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaCategoryRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.CategoryRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaFamilyMemberRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaFamilyRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.CompetingMediaRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaLifeCycleRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.BurstCountRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.StoreMediaCategoryPermissionsRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.StoreMediaCategoryPermissionRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaGroupRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaGroupMemberRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaChannelRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaChannelGroupMemberRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaCostRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaCategoryRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetMedia.CategoryRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaFamilyMemberRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaFamilyRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetMedia.CompetingMediaRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaLifeCycleRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetMedia.BurstCountRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetMedia.StoreMediaCategoryPermissionsRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetMedia.StoreMediaCategoryPermissionRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaGroupRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaGroupMemberRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaChannelRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaChannelGroupMemberRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetMedia.MediaCostRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetMediaTableAdapters.MediaTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetMediaTableAdapters.MediaCategoryTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetMediaTableAdapters.CategoryTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetMediaTableAdapters.MediaFamilyMemberTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetMediaTableAdapters.MediaFamilyTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetMediaTableAdapters.CompetingMediaTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetMediaTableAdapters.MediaLifeCycleTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetMediaTableAdapters.BurstCountTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetMediaTableAdapters.StoreMediaCategoryPermissionsTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetMediaTableAdapters.StoreMediaCategoryPermissionTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetMediaTableAdapters.MediaGroupTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetMediaTableAdapters.MediaGroupMemberTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetMediaTableAdapters.MediaChannelTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetMediaTableAdapters.MediaChannelGroupMemberTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetMediaTableAdapters.MediaCostTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetSetting">
<summary>
Represents a strongly typed in-memory cache of data.
</summary>
</member>
<member name="T:NovaData.DataSetSetting.SettingGroupDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetSetting.SettingDataTable">
<summary>
Represents the strongly named DataTable class.
</summary>
</member>
<member name="T:NovaData.DataSetSetting.SettingGroupRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetSetting.SettingRow">
<summary>
Represents strongly named DataRow class.
</summary>
</member>
<member name="T:NovaData.DataSetSetting.SettingGroupRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetSetting.SettingRowChangeEvent">
<summary>
Row event argument class
</summary>
</member>
<member name="T:NovaData.DataSetSettingTableAdapters.SettingGroupTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
<member name="T:NovaData.DataSetSettingTableAdapters.SettingTableAdapter">
<summary>
Represents the connection and commands used to retrieve and save data.
</summary>
</member>
</members>
</doc>
