Public Class MiscellaneousCharge

    Inherits OldBaseObject

    Private ConsumingForm As LiquidShell.BaseForm
    Private ParentContract As OldContract

#Region "Fields"

    Private _MiscellaneousChargeID As Integer
    Private _MiscellaneousChargeAmount As Decimal = 0

    Private _MiscellaneousChargeName As String = "Select..."

#End Region

#Region "Database Column Properties"

    Public ReadOnly Property MiscellaneousChargeID() As Integer
        Get
            Return _MiscellaneousChargeID
        End Get
    End Property

    Public Property MiscellaneousChargeAmount() As Decimal
        Get
            Return _MiscellaneousChargeAmount
        End Get
        Set(ByVal value As Decimal)
            _MiscellaneousChargeAmount = value
            IsDirty = True
        End Set
    End Property

#End Region

#Region "Custom Properties"

    Public WriteOnly Property SelectedMiscellaneousCharge() As DataRow
        Set(ByVal value As DataRow)
            _MiscellaneousChargeID = value("MiscellaneousChargeID")
            _MiscellaneousChargeName = value("MiscellaneousChargeName")
        End Set
    End Property

    Public ReadOnly Property MiscellaneousChargeName() As String
        Get
            Return _MiscellaneousChargeName
        End Get
    End Property

    Public ReadOnly Property RowTitle() As String
        Get
            Dim Separator As String = " / "
            Dim ReturnString As New System.Text.StringBuilder(ParentContract.ContractNumber & Separator)
            If String.Compare(_MiscellaneousChargeName, "Select...") = 0 Then
                ReturnString.Append("(new miscellaneous charge)")
            Else
                ReturnString.Append(_MiscellaneousChargeName & " Charge")
            End If
            Return ReturnString.ToString
        End Get
    End Property

#End Region

#Region "Public Methods"

    Public Shared Function GetRowDescription(ByVal Row As DataRow, ByVal ParentContractNumber As String) As String
        If Row.RowState = DataRowState.Detached AndAlso IsDBNull(Row("MiscellaneousChargeID")) Then
            Return "New miscellaneous charge for " & ParentContractNumber
        Else
            Return Row("MiscellaneousChargeName").ToString & " of " & CDec(Row("MiscellaneousChargeAmount")).ToString("C2") & " for " & ParentContractNumber
        End If
    End Function

    Public Sub New _
    (ByVal Parent As OldContract, _
    ByVal AddNew As Boolean, _
    ByVal Constring As String)

        ' Create a new row if this is not an existing row that the user is modifying.
        If AddNew Then
            Parent.MiscellaneousChargeBindingSource.AddNew()
        End If

        ' Update variables.
        ParentContract = Parent
        AuditLog = ParentContract.AuditLog
        ConsumingForm = ParentContract.ConsumingForm
        ConnectionString = Constring
        DataBindingSource = ParentContract.MiscellaneousChargeBindingSource

        ' Create a description for this burst to use in the audit log.
        _RowDescription = GetRowDescription(Row, ParentContract.ContractNumber)

        InitializeFields()

    End Sub

    Public Sub SaveToDataSet()

        ' Update all columns that do not require an audit log entry.
        Row("MiscellaneousChargeID") = _MiscellaneousChargeID

        ' Update all columns that require an audit log entry.
        If Not Object.Equals(Row("MiscellaneousChargeName"), _MiscellaneousChargeName) Then
            AddLog(Row, RowDescription, "MiscellaneousChargeName", Row("MiscellaneousChargeName").ToString, _MiscellaneousChargeName.ToString, AuditLog)
            Row("MiscellaneousChargeName") = _MiscellaneousChargeName
        End If
        If Not Object.Equals(Row("MiscellaneousChargeAmount"), _MiscellaneousChargeAmount) Then
            AddLog(Row, RowDescription, "MiscellaneousChargeAmount", CDec(Row("MiscellaneousChargeAmount")).ToString("c"), _MiscellaneousChargeAmount.ToString("c"), AuditLog)
            Row("MiscellaneousChargeAmount") = _MiscellaneousChargeAmount
        End If

        ' If this is a new row being created, add only one entry for the creation of the object (as opposed to
        ' adding entries for every modified property of this object).
        If Row.RowState = DataRowState.Detached Then
            _RowDescription = GetRowDescription(Row, ParentContract.ContractNumber)
            LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, "Contract Miscellaneous Charge", RowDescription, "Created")
        End If

        ' Remember the current item in the binding source list.
        Dim CurrentItem As Object = DataBindingSource.Current
        ' Commit changes to the dataset.
        DataBindingSource.EndEdit()     ' This line changes the binding source's current item.
        ' Restore the current item in the binding source list.
        DataBindingSource.Position = DataBindingSource.IndexOf(CurrentItem)

        ' Update production on the parent contract.
        ParentContract.ProductionUpdated()

    End Sub

    Public Sub RejectChanges()

        ' Remember the current item in the binding source list.
        Dim CurrentItem As Object = DataBindingSource.Current
        ' Commit changes to the dataset.
        DataBindingSource.CancelEdit()    ' This line changes the binding source's current item.
        ' Restore the current item in the binding source list.
        DataBindingSource.Position = DataBindingSource.IndexOf(CurrentItem)

    End Sub

#End Region

#Region "Private Methods"

    Private Sub InitializeFields()

        ' Stop if this is a new row being added.
        If Row.RowState = DataRowState.Detached Then
            Row("ContractMiscellaneousChargeID") = Guid.NewGuid
            Exit Sub
        End If

        ' Initialize field values to row values.
        _MiscellaneousChargeID = Row("MiscellaneousChargeID")
        _MiscellaneousChargeName = Row("MiscellaneousChargeName")
        _MiscellaneousChargeAmount = Row("MiscellaneousChargeAmount")

    End Sub

    Private Shared Shadows Sub AddLog _
    (ByVal AuditRow As DataRow, _
    ByVal RowDescription As String, _
    ByVal ChangedPropertyName As String, _
    ByVal ChangedPropertyOldValue As String, _
    ByVal ChangedPropertyNewValue As String, _
    ByVal AuditLog As DataTable)
        ' Add an entry into the audit log table for a modified row.

        ' Exit if the row being audited is a new detached row.
        If AuditRow.RowState = DataRowState.Detached Then
            Exit Sub
        End If

        ' Create a string builder to build the log description.
        Dim ActionBuilder As New System.Text.StringBuilder("Changed the value of " & ChangedPropertyName.ToUpper & " from ")

        ' Add the old value to the string builder.
        If String.IsNullOrEmpty(ChangedPropertyOldValue) Then
            ActionBuilder.Append("an empty string")
        Else
            ActionBuilder.Append("'" & ChangedPropertyOldValue & "'")
        End If
        ActionBuilder.Append(" to ")

        ' Add the new value to the string builder.
        If String.IsNullOrEmpty(ChangedPropertyNewValue) Then
            ActionBuilder.Append("an empty string")
        Else
            ActionBuilder.Append("'" & ChangedPropertyNewValue & "'")
        End If

        ' Log the entry.
        LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, "Contract Miscellaneous Charge", RowDescription, ActionBuilder.ToString)

    End Sub

#End Region

End Class
