﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class NavButton
    Inherits DevExpress.XtraEditors.XtraUserControl

    'UserControl overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.ButtonText = New DevExpress.XtraEditors.LabelControl
        Me.SuspendLayout()
        '
        'ButtonText
        '
        Me.ButtonText.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.ButtonText.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonText.Appearance.ForeColor = System.Drawing.Color.Black
        Me.ButtonText.Appearance.Options.UseBackColor = True
        Me.ButtonText.Appearance.Options.UseFont = True
        Me.ButtonText.Appearance.Options.UseForeColor = True
        Me.ButtonText.Appearance.Options.UseTextOptions = True
        Me.ButtonText.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near
        Me.ButtonText.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center
        Me.ButtonText.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.ButtonText.Dock = System.Windows.Forms.DockStyle.Fill
        Me.ButtonText.Location = New System.Drawing.Point(0, 0)
        Me.ButtonText.Name = "ButtonText"
        Me.ButtonText.Padding = New System.Windows.Forms.Padding(15, 0, 0, 0)
        Me.ButtonText.Size = New System.Drawing.Size(184, 50)
        Me.ButtonText.TabIndex = 1
        Me.ButtonText.Text = "NavButton"
        '
        'NavButton
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.BackColor2 = System.Drawing.Color.Gainsboro
        Me.Appearance.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical
        Me.Appearance.Options.UseBackColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(6.0!, 13.0!)
        Me.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font
        Me.Controls.Add(Me.ButtonText)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Name = "NavButton"
        Me.Size = New System.Drawing.Size(184, 50)
        Me.Tag = ""
        Me.ResumeLayout(False)

    End Sub
    Private WithEvents ButtonText As DevExpress.XtraEditors.LabelControl

End Class
