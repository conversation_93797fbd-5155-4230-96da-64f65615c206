﻿Public Class SubformResearchCategory

    Private DataObject As ResearchCategory = Nothing
    Private OriginalFee As Decimal = 0

#Region "Properties"

    Public ReadOnly Property TitleText As String
        Get
            If DataObject.Row.RowState = DataRowState.Detached Then
                Return "Contract " & DataObject.ParentContract.ContractNumber & " - Add a Research Category"
            Else
                Return "Contract " & DataObject.ParentContract.ContractNumber & " - Edit a Research Category"
            End If
        End Get
    End Property

#End Region

#Region "Event Handlers"

    Private Sub SubformResearchCategory_Load(sender As Object, e As System.EventArgs) Handles Me.Load
        AddDataBindings()
    End Sub

    Private Sub HyperlinkCategory_Click(sender As System.Object, e As System.EventArgs) Handles HyperlinkCategory.Click

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a category selection from the user.
        Dim SelectedItems As List(Of DataRow) = LookupCategory.SelectRows(My.Settings.DBConnection, False, Nothing)

        ' Update the data object and the label with the selection.
        If SelectedItems.Count > 0 Then
            If Not String.Compare(SelectedItems(0).Item("CategoryName"), DataObject.CategoryName) = 0 Then
                ' The user's input is different to the current value. Proceed with the update.
                DataObject.SelectedCategory = SelectedItems(0)
                CurrentControl.DataBindings("Text").ReadValue()
            End If
        End If

        ' Reset the error text of the related control to an empty string.
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)

    End Sub

    Private Sub HyperlinkFirstMonth_Click(sender As System.Object, e As System.EventArgs) Handles HyperlinkFirstMonth.Click

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a date selection from the user.
        Dim SelectedDate As Nullable(Of Date) = DateSelector.GetDate(False)

        ' Update the data object and the label with the selection.
        If SelectedDate.HasValue Then
            DataObject.SelectedFirstMonth = SelectedDate.Value
            CurrentControl.DataBindings("Text").ReadValue()
            HyperlinkLastMonth.DataBindings("Text").ReadValue()
        End If

        ' Reset the error text of the related control to an empty string.
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)

    End Sub

    Private Sub HyperlinkLastMonth_Click(sender As System.Object, e As System.EventArgs) Handles HyperlinkLastMonth.Click

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a date selection from the user.
        Dim SelectedDate As Nullable(Of Date) = DateSelector.GetDate(False)

        ' Update the data object and the label with the selection.
        If SelectedDate.HasValue Then
            DataObject.SelectedlastMonth = SelectedDate.Value
            CurrentControl.DataBindings("Text").ReadValue()
            HyperlinkFirstMonth.DataBindings("Text").ReadValue()
            TextEditMonths.DataBindings("EditValue").ReadValue()
        End If

        ' Reset the error text of the related control to an empty string.
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)

    End Sub

    Private Sub ButtonCancel_Click(sender As System.Object, e As System.EventArgs) Handles ButtonCancel.Click
        If Not DataObject.Row.RowState = DataRowState.Detached Then
            DataObject.Row.RejectChanges()
        End If
        RevertToParentSubform()
    End Sub

    Private Sub ButtonOK_Click(sender As System.Object, e As System.EventArgs) Handles ButtonOK.Click
        Save(True)
    End Sub

    Private Sub HyperlinkCategory_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles HyperlinkCategory.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        If String.Compare(ValidatedControl.Text, "Select...") = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "A category must be selected.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkLastMonth_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles HyperlinkLastMonth.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        If String.Compare(ValidatedControl.Text, "Select...") = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "The first month must be selected.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkFirstMonth_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles HyperlinkFirstMonth.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        If String.Compare(ValidatedControl.Text, "Select...") = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "The last month must be selected.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub TextEditFee_Validated(sender As Object, e As System.EventArgs) Handles TextEditFee.Validated, TextEditDiscount.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If DataObject.ParentContract.Signed AndAlso Not CDec(ValidatedControl.EditValue) = OriginalFee Then
            LiquidAgent.ControlValidation(ValidatedControl, "If the fee is anything except " & OriginalFee.ToString("C2") _
                        & " then the value of the contract will change." & vbCrLf & "This is not permitted because the contract has " _
                        & "already been signed.")
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

#End Region

#Region "Public Methods"

    Public Sub New(ResearchCategoryObject As ResearchCategory)

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        DataObject = ResearchCategoryObject

        ' Remember the value of this research category so that we can prevent the user from changing the value
        ' of a signed contract.
        OriginalFee = DataObject.Fee

    End Sub

#End Region

#Region "Private Methods"

    Private Sub AddDataBindings()

        ' Form elements
        LabelTitle.DataBindings.Add("Text", Me, "TitleText")

        ' Contract data
        HyperlinkCategory.DataBindings.Add("Text", DataObject, "CategoryName")
        HyperlinkFirstMonth.DataBindings.Add("Text", DataObject, "FirstMonth")
        HyperlinkLastMonth.DataBindings.Add("Text", DataObject, "LastMonth")
        TextEditMonths.DataBindings.Add("EditValue", DataObject, "Months", False, DataSourceUpdateMode.OnPropertyChanged)
        TextEditFee.DataBindings.Add("EditValue", DataObject, "Fee", False, DataSourceUpdateMode.OnValidation)
        TextEditDiscount.DataBindings.Add("EditValue", DataObject, "Discount", False, DataSourceUpdateMode.OnValidation)

    End Sub

#End Region

    Protected Overrides Function Save() As Boolean

        ' Save this research category.
        If DataObject.Save(My.Settings.DBConnection, CType(TopLevelControl, BaseForm)) Then
            ' Close the form.
            RevertToParentSubform()
        End If

    End Function

End Class
