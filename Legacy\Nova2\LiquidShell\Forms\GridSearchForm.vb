﻿Public Class GridSearchForm

    Public SearchBoxes As New List(Of TextEdit)
    Public SearchBoxNames As New List(Of String)
    Public SearchCheckBoxes As New List(Of CheckEdit)
    Private FilterGridManager As GridManager

#Region "Properties"

    Public ReadOnly Property SearchBoxesAreEmpty() As Boolean
        Get
            ' Check each search box for text.
            For Each SearchBox As TextEdit In SearchBoxes
                If String.IsNullOrEmpty(SearchBox.Text) = False Then
                    ' At least one of the search boxes contain text.
                    Return False
                End If
            Next
            For Each SearchCheckBox As CheckEdit In SearchCheckBoxes
                If Not SearchCheckBox.CheckState = CheckState.Indeterminate Then
                    ' At least one of the check boxes contain a value.
                    Return False
                End If
            Next
            Return True
        End Get
    End Property

#End Region

#Region "Constructors"

    Public Sub New(ByVal FilterGrid As DataGridView)
        ' This call is required by the Windows Form Designer.
        InitializeComponent()
        ' Add any initialization after the InitializeComponent() call.
        SetupLayoutPanel(FilterGrid)
        AddControls(FilterGrid)
        FilterGridManager = FilterGrid.Tag
    End Sub

#End Region

#Region "Event Handlers"

    Private Sub ButtonClear_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonClear.Click
        FilterGridManager.ClearSearch()
    End Sub

    Private Sub TableLayoutPanel1_SizeChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles TableLayoutPanel1.SizeChanged
        ' Adjust the size of the form.
        Me.Height = TableLayoutPanel1.Size.Height
    End Sub

#End Region

#Region "Methods"

    Private Sub SetupLayoutPanel(ByVal FilterGrid As DataGridView)
        ' Set the layout panel size depending on the number of columns n the grid.
        'TableLayoutPanel1.Size = New Size(TableLayoutPanel1.Width, FilterGrid.Columns.Count * 21)
    End Sub

    Private Sub AddControls(ByVal FilterGrid As DataGridView)
        ' Populate the form with search boxes for each column in the grid.

        For Each Column As DataGridViewColumn In FilterGrid.Columns
            ' Create the label and search control for this column.
            Dim ColumnLabel As Panel = NewLabel(Column.HeaderText)
            Dim ColumnFilterControl As Panel = Nothing

            ' Add a search control that is of the same type as the grid column.
            If TypeOf Column Is DataGridViewTextBoxColumn Then
                ' This column uses a text box for searches.
                ColumnFilterControl = NewTextBox(Column.DataPropertyName)
                ' Add the search box and its name to collections for easy referencing by the grid manager class.
                SearchBoxes.Add(ColumnFilterControl.Controls(0))
                SearchBoxNames.Add(ColumnFilterControl.Controls(0).Name)
            ElseIf TypeOf Column Is DataGridViewCheckBoxColumn Then
                ' This column uses a check box for searches.
                ColumnFilterControl = NewCheckBox(Column.DataPropertyName)
                ' Add the check edit to a collection for easy referencing by the grid manager class.
                SearchCheckBoxes.Add(ColumnFilterControl.Controls(0))
            End If

            ' Add the label and search control to the form using the table layout panel.
            If IsNothing(ColumnFilterControl) = False Then
                TableLayoutPanel1.Controls.Add(ColumnLabel, 0, Column.Index)
                TableLayoutPanel1.Controls.Add(ColumnFilterControl, 1, Column.Index)
            End If

        Next

    End Sub

    Private Function NewCheckBox(ByVal ControlName As String) As Panel
        ' Return a new CheckBox control with all the proper formatting.

        ' Instantiate and configure a new panel.
        Dim NewControlPanel As New Panel
        With NewControlPanel
            .Tag = "SearchBox"
            .Margin = New Padding(1, 1, 1, 0)
            .BackColor = Color.LightSteelBlue
            '.AutoSize = True
            .Anchor = AnchorStyles.Bottom Or AnchorStyles.Left Or AnchorStyles.Right Or AnchorStyles.Top
        End With

        ' Instantiate and configure a new CheckBox control.
        Dim NewControl As New CheckEdit
        With NewControl
            .Text = " (can be any value)"
            .Name = ControlName
            .Width = NewControlPanel.Width - 2
            .Location = New Point(1, 1)
            .Anchor = AnchorStyles.Bottom Or AnchorStyles.Left Or AnchorStyles.Right Or AnchorStyles.Top
            .Font = New Font(Me.Font, FontStyle.Regular)
            .Margin = New Padding(0)
            .Properties.AllowGrayed = True
            .CheckState = CheckState.Indeterminate
        End With

        ' Add the TextEdit to the panel.
        NewControlPanel.Controls.Add(NewControl)

        ' Return the new control.
        Return NewControlPanel

    End Function

    Private Function NewTextBox(ByVal ControlName As String) As Panel
        ' Return a new TextEdit control with all the proper formatting.

        ' Instantiate and configure a new panel.
        Dim NewControlPanel As New Panel
        With NewControlPanel
            .Tag = "SearchBox"
            .Margin = New Padding(1, 1, 1, 0)
            .BackColor = Color.LightSteelBlue
            '.AutoSize = True
            .Anchor = AnchorStyles.Bottom Or AnchorStyles.Left Or AnchorStyles.Right Or AnchorStyles.Top
        End With

        ' Instantiate and configure a new TextEdit control.
        Dim NewControl As New TextEdit
        With NewControl
            .Name = ControlName
            .Width = NewControlPanel.Width - 2
            .Location = New Point(1, 1)
            .BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
            .Anchor = AnchorStyles.Bottom Or AnchorStyles.Left Or AnchorStyles.Right Or AnchorStyles.Top
            .ForeColor = Color.Black
            .BackColor = Color.LightSteelBlue
            .Font = New Font(Me.Font, FontStyle.Regular)
            .Margin = New Padding(0)
        End With

        ' Add the TextEdit to the panel.
        NewControlPanel.Controls.Add(NewControl)

        ' Return the new control.
        Return NewControlPanel

    End Function

    Private Function NewLabel(ByVal LabelText As String) As Panel
        ' Return a new panelcontaining a TextEdit control with all the proper formatting.

        ' Instantiate and configure a new TextEdit control.
        Dim NewControl As New LabelControl
        With NewControl
            .ForeColor = Color.White
            .Font = New Font(Me.Font, FontStyle.Regular)
            .Text = LabelText & ":"
            .Location = New Point(3, 3)
        End With

        ' Instantiate and configure a new panel.
        Dim NewControlPanel As New Panel
        With NewControlPanel
            .Margin = New Padding(1, 1, 0, 0)
            .BackColor = Color.Gray
            '.AutoSize = True
            .Anchor = AnchorStyles.Bottom Or AnchorStyles.Left Or AnchorStyles.Right Or AnchorStyles.Top
        End With

        ' Add the TextEdit to the panel.
        NewControlPanel.Controls.Add(NewControl)

        ' Return the new control.
        Return NewControlPanel

    End Function

    Public Sub ClearSearchValues()

        ' Clear all text from the search boxes and values from the check boxes.
        For Each SearchboxPanel As Control In TableLayoutPanel1.Controls
            If TypeOf SearchboxPanel Is Panel _
            AndAlso String.Compare(CStr(SearchboxPanel.Tag), "SearchBox") = 0 Then
                ' This is not a label. Reset it.
                If TypeOf SearchboxPanel.Controls(0) Is TextEdit Then
                    ' This search control is a text box. Clear the text.
                    CType(SearchboxPanel.Controls(0), TextEdit).Text = String.Empty
                ElseIf TypeOf SearchboxPanel.Controls(0) Is CheckEdit Then
                    ' This search control is a check box. Reset to indeterminate state.
                    CType(SearchboxPanel.Controls(0), CheckEdit).CheckState = CheckState.Indeterminate
                End If
            End If
        Next

        ' Give focus to the first search box.
        For Each SearchboxPanel As Control In TableLayoutPanel1.Controls
            If TypeOf SearchboxPanel Is Panel _
            AndAlso String.Compare(CStr(SearchboxPanel.Tag), "SearchBox") = 0 Then
                ' This is a search box and not a label. Give it focus and exit.
                SearchboxPanel.Controls(0).Focus()
                Exit For
            End If
        Next

    End Sub

#End Region

End Class