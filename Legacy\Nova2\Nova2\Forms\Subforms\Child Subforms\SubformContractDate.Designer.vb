﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformContractDate
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformContractDate))
        Me.LabelFirstWeek = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkInstallationInstructions = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkPONumber = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl4 = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkStoreList = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl6 = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkArtwork = New DevExpress.XtraEditors.LabelControl()
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonBack = New DevExpress.XtraEditors.SimpleButton()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!)
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Size = New System.Drawing.Size(399, 30)
        Me.LabelTitle.Text = "Contract [NUMBER] Activity Dates"
        '
        'LabelFirstWeek
        '
        Me.LabelFirstWeek.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelFirstWeek.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelFirstWeek.Location = New System.Drawing.Point(12, 96)
        Me.LabelFirstWeek.Margin = New System.Windows.Forms.Padding(0, 3, 3, 10)
        Me.LabelFirstWeek.Name = "LabelFirstWeek"
        Me.LabelFirstWeek.Size = New System.Drawing.Size(187, 13)
        Me.LabelFirstWeek.TabIndex = 26
        Me.LabelFirstWeek.Text = "Installation Instructions entered:"
        '
        'HyperlinkInstallationInstructions
        '
        Me.HyperlinkInstallationInstructions.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkInstallationInstructions.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkInstallationInstructions.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkInstallationInstructions.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkInstallationInstructions.Location = New System.Drawing.Point(227, 96)
        Me.HyperlinkInstallationInstructions.Margin = New System.Windows.Forms.Padding(3, 3, 3, 10)
        Me.HyperlinkInstallationInstructions.Name = "HyperlinkInstallationInstructions"
        Me.HyperlinkInstallationInstructions.Size = New System.Drawing.Size(47, 13)
        Me.HyperlinkInstallationInstructions.TabIndex = 27
        Me.HyperlinkInstallationInstructions.Text = "Select..."
        '
        'LabelControl1
        '
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl1.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl1.Location = New System.Drawing.Point(12, 57)
        Me.LabelControl1.Margin = New System.Windows.Forms.Padding(0, 3, 3, 20)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(427, 13)
        Me.LabelControl1.TabIndex = 28
        Me.LabelControl1.Text = "Use this form to record the dates on which the indicated activities occured."
        '
        'LabelControl2
        '
        Me.LabelControl2.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl2.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl2.Location = New System.Drawing.Point(12, 123)
        Me.LabelControl2.Margin = New System.Windows.Forms.Padding(0, 3, 3, 10)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.Size = New System.Drawing.Size(118, 13)
        Me.LabelControl2.TabIndex = 29
        Me.LabelControl2.Text = "PO Number entered:"
        '
        'HyperlinkPONumber
        '
        Me.HyperlinkPONumber.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkPONumber.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkPONumber.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkPONumber.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkPONumber.Location = New System.Drawing.Point(227, 123)
        Me.HyperlinkPONumber.Margin = New System.Windows.Forms.Padding(3, 3, 3, 10)
        Me.HyperlinkPONumber.Name = "HyperlinkPONumber"
        Me.HyperlinkPONumber.Size = New System.Drawing.Size(47, 13)
        Me.HyperlinkPONumber.TabIndex = 30
        Me.HyperlinkPONumber.Text = "Select..."
        '
        'LabelControl4
        '
        Me.LabelControl4.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl4.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl4.Location = New System.Drawing.Point(12, 150)
        Me.LabelControl4.Margin = New System.Windows.Forms.Padding(0, 3, 3, 10)
        Me.LabelControl4.Name = "LabelControl4"
        Me.LabelControl4.Size = New System.Drawing.Size(117, 13)
        Me.LabelControl4.TabIndex = 35
        Me.LabelControl4.Text = "Store list confirmed:"
        '
        'HyperlinkStoreList
        '
        Me.HyperlinkStoreList.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkStoreList.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkStoreList.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkStoreList.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkStoreList.Location = New System.Drawing.Point(227, 150)
        Me.HyperlinkStoreList.Margin = New System.Windows.Forms.Padding(3, 3, 3, 10)
        Me.HyperlinkStoreList.Name = "HyperlinkStoreList"
        Me.HyperlinkStoreList.Size = New System.Drawing.Size(47, 13)
        Me.HyperlinkStoreList.TabIndex = 36
        Me.HyperlinkStoreList.Text = "Select..."
        '
        'LabelControl6
        '
        Me.LabelControl6.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl6.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl6.Location = New System.Drawing.Point(12, 176)
        Me.LabelControl6.Margin = New System.Windows.Forms.Padding(0, 3, 3, 10)
        Me.LabelControl6.Name = "LabelControl6"
        Me.LabelControl6.Size = New System.Drawing.Size(103, 13)
        Me.LabelControl6.TabIndex = 37
        Me.LabelControl6.Text = "Artwork received:"
        '
        'HyperlinkArtwork
        '
        Me.HyperlinkArtwork.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkArtwork.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkArtwork.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkArtwork.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkArtwork.Location = New System.Drawing.Point(227, 176)
        Me.HyperlinkArtwork.Margin = New System.Windows.Forms.Padding(3, 3, 3, 10)
        Me.HyperlinkArtwork.Name = "HyperlinkArtwork"
        Me.HyperlinkArtwork.Size = New System.Drawing.Size(47, 13)
        Me.HyperlinkArtwork.TabIndex = 38
        Me.HyperlinkArtwork.Text = "Select..."
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "back.png")
        '
        'ButtonBack
        '
        Me.ButtonBack.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonBack.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonBack.Appearance.Options.UseFont = True
        Me.ButtonBack.ImageIndex = 0
        Me.ButtonBack.ImageList = Me.ImageList24x24
        Me.ButtonBack.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonBack.Location = New System.Drawing.Point(709, 12)
        Me.ButtonBack.LookAndFeel.SkinName = "Black"
        Me.ButtonBack.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonBack.Margin = New System.Windows.Forms.Padding(3, 3, 5, 3)
        Me.ButtonBack.Name = "ButtonBack"
        Me.ButtonBack.Size = New System.Drawing.Size(100, 28)
        Me.ButtonBack.TabIndex = 39
        Me.ButtonBack.Text = "Back"
        '
        'SubformContractDate
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.Controls.Add(Me.ButtonBack)
        Me.Controls.Add(Me.LabelControl6)
        Me.Controls.Add(Me.HyperlinkArtwork)
        Me.Controls.Add(Me.LabelControl4)
        Me.Controls.Add(Me.HyperlinkStoreList)
        Me.Controls.Add(Me.LabelControl2)
        Me.Controls.Add(Me.HyperlinkPONumber)
        Me.Controls.Add(Me.LabelControl1)
        Me.Controls.Add(Me.LabelFirstWeek)
        Me.Controls.Add(Me.HyperlinkInstallationInstructions)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Name = "SubformContractDate"
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkInstallationInstructions, 0)
        Me.Controls.SetChildIndex(Me.LabelFirstWeek, 0)
        Me.Controls.SetChildIndex(Me.LabelControl1, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkPONumber, 0)
        Me.Controls.SetChildIndex(Me.LabelControl2, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkStoreList, 0)
        Me.Controls.SetChildIndex(Me.LabelControl4, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkArtwork, 0)
        Me.Controls.SetChildIndex(Me.LabelControl6, 0)
        Me.Controls.SetChildIndex(Me.ButtonBack, 0)
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents LabelFirstWeek As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkInstallationInstructions As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkPONumber As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl4 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkStoreList As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl6 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkArtwork As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents ButtonBack As DevExpress.XtraEditors.SimpleButton

End Class
