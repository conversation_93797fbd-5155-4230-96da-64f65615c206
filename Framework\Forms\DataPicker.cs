﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Windows.Forms;

namespace Framework.Forms
{
    public partial class DataPicker : DataForm, IPicker
    {
        public Func<DataPicker, DataTable> GetGridDataMethod;


        #region Properties

        private string _ErrorMessage = string.Empty;
        public string ErrorMessage
        {
            get { return _ErrorMessage; }
            set
            {
                _ErrorMessage = value;
            }
        }

        #endregion


        #region Startup

        public DataPicker()
        {
            InitializeComponent();
            flatButtonOK.Click += FlatButtonOK_Click;
            grid.DoubleClick += Grid_DoubleClick;
            Load += DataPicker_Load;
            Activated += DataPicker_Activated;
        }

        private void DataPicker_Load(object sender, EventArgs e)
        {
            if (GetGridDataMethod != null)
            {
                Cursor = Cursors.WaitCursor;
                grid.DataSource = GetGridDataMethod.Invoke(this);
                Cursor = Cursors.Default;

                if (string.IsNullOrEmpty(ErrorMessage) == false)
                {
                    Close();
                }
            }
            else
            {
                if (DesignMode == false)
                {
                    MessageForm.Show("DataPicker.DataPicker_Load: The GetGridData delegate needs a value in order to load data for this picker.", "Now What Do I Do?");
                }
            }
        }

        private void DataPicker_Activated(object sender, EventArgs e)
        {
            grid.SelectedDataRows = PreviouslySelectedDataRows;
            if (grid.SelectedRows.Count > 0)
            {
                grid.FirstDisplayedScrollingRowIndex = grid.SelectedRows[0].Index;
            }
            grid.Select();
        }

        #endregion


        #region Row selection management

        private List<DataRow> PreviouslySelectedDataRows = new List<DataRow>();

        private List<DataRow> _SelectedDataRows = new List<DataRow>();
        public List<DataRow> SelectedDataRows
        {
            get { return _SelectedDataRows; }
        }

        public void ResetSelection()
        {
            if (grid.Rows.Count > 0)
            {
                grid.CurrentRow = grid.Rows[0];
                PreviouslySelectedDataRows.Clear();
            }
        }

        private void Grid_DoubleClick(object sender, EventArgs e)
        {
            AcceptSelection();
        }

        private void FlatButtonOK_Click(object sender, EventArgs e)
        {
            AcceptSelection();
        }

        private void AcceptSelection()
        {
            DialogResult = DialogResult.OK;
            Close();
        }

        #endregion


        #region IPicker method implementations

        public object Pick()
        {
            object returnvalue = null;
            
            ShowDialog();

            // Update the return value with the selected rows.
            if (DialogResult == DialogResult.OK)
            {
                if (grid.SelectedRows.Count > 0)
                {
                    SelectedDataRows.Clear();
                    for (int i = 0; i < grid.SelectedRows.Count; i++)
                    {
                        DataRowView selectedrowview = (DataRowView)grid.SelectedRows[i].DataBoundItem;
                        SelectedDataRows.Add(selectedrowview.Row);
                    }
                    PreviouslySelectedDataRows = SelectedDataRows;
                    returnvalue = SelectedDataRows;
                }
            }
            return returnvalue;
        }
        
        #endregion

    }
}
