﻿using DataAccess;

namespace DataService.Sales
{
    class GetTableOfMediaServicesCommandExecutor : CommandExecutor<GetTableOfMediaServicesCommand>
    {
        public override void Execute(GetTableOfMediaServicesCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.GetMediaServiceTable))
            {
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    command.Table = storedprocedure.OutputTable;
                }
            }
        }
    }
}
