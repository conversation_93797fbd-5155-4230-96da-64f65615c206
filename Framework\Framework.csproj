﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{6C57B7A1-A6EC-48EA-A1EA-6585B4206E6F}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Framework</RootNamespace>
    <AssemblyName>Framework</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EPPlus, Version=4.1.0.0, Culture=neutral, PublicKeyToken=ea159fdaa78159a1, processorArchitecture=MSIL">
      <HintPath>..\packages\EPPlus.4.1.0\lib\net40\EPPlus.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="System" />
    <Reference Include="System.Design" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Data" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Breadcrumbs\BreadcrumbContainerSurface.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Breadcrumbs\BreadcrumbContainerSurface.Designer.cs">
      <DependentUpon>BreadcrumbContainerSurface.cs</DependentUpon>
    </Compile>
    <Compile Include="Breadcrumbs\BreadcrumbResizer.cs" />
    <Compile Include="Controls\GridSystem\ExcelExporter.cs" />
    <Compile Include="Controls\GridSystem\HistoryGridFiller.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Controls\GridSystem\HistoryGridFiller.Designer.cs">
      <DependentUpon>HistoryGridFiller.cs</DependentUpon>
    </Compile>
    <Compile Include="DirtyStateManager.cs" />
    <Compile Include="ErrorStateManager.cs" />
    <Compile Include="Forms\ColorPicker.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\ColorPicker.Designer.cs">
      <DependentUpon>ColorPicker.cs</DependentUpon>
    </Compile>
    <Compile Include="Surfaces\GridSurfaceFactory.cs" />
    <Compile Include="Surfaces\RowHistorySurfaceFactory.cs" />
    <Compile Include="Surfaces\ITabContentSurfaceFactory.cs" />
    <Compile Include="Surfaces\RowDetailSurface.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Surfaces\RowDetailSurface.Designer.cs">
      <DependentUpon>RowDetailSurface.cs</DependentUpon>
    </Compile>
    <Compile Include="Surfaces\RowHistorySurface.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Surfaces\RowHistorySurface.Designer.cs">
      <DependentUpon>RowHistorySurface.cs</DependentUpon>
    </Compile>
    <Compile Include="Surfaces\RowSurface.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Surfaces\RowSurface.Designer.cs">
      <DependentUpon>RowSurface.cs</DependentUpon>
    </Compile>
    <Compile Include="FrameworkMethods.cs" />
    <Compile Include="Controls\Data\CheckDataBox.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\Data\CheckDataBox.Designer.cs">
      <DependentUpon>CheckDataBox.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\Data\ComboDataBox.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\Data\ComboDataBox.Designer.cs">
      <DependentUpon>ComboDataBox.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\Data\DataBox.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\Data\DataBox.Designer.cs">
      <DependentUpon>DataBox.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\Data\DataControlDesigner.cs" />
    <Compile Include="Controls\Data\ErrorLabel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\Data\TextDataBox.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\Data\TextDataBox.Designer.cs">
      <DependentUpon>TextDataBox.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\Data\ToggleDataBox.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\Data\ToggleDataBox.Designer.cs">
      <DependentUpon>ToggleDataBox.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\FlatButton.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Controls\GridSystem\Grid.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\GridSystem\Grid.Designer.cs">
      <DependentUpon>Grid.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\GridSystem\GridFiller.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Controls\GridSystem\GridFiller.Designer.cs">
      <DependentUpon>GridFiller.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\GridSystem\GridFilter.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\GridSystem\GridFilter.Designer.cs">
      <DependentUpon>GridFilter.cs</DependentUpon>
    </Compile>
    <Compile Include="Surfaces\GridSurface.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Surfaces\GridSurface.Designer.cs">
      <DependentUpon>GridSurface.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\GridSystem\IGridFiller.cs" />
    <Compile Include="Controls\ProcessingAnimation.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\ProcessingAnimation.Designer.cs">
      <DependentUpon>ProcessingAnimation.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\TabSystem\Tab.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Controls\TabSystem\Tab.Designer.cs">
      <DependentUpon>Tab.cs</DependentUpon>
    </Compile>
    <Compile Include="Controls\TabSystem\TabGroup.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Forms\DataForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\DataForm.Designer.cs">
      <DependentUpon>DataForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\DataPicker.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\DataPicker.Designer.cs">
      <DependentUpon>DataPicker.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\DatePicker.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\DatePicker.Designer.cs">
      <DependentUpon>DatePicker.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\IPicker.cs" />
    <Compile Include="FrameworkSettings.cs" />
    <Compile Include="MessageForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MessageForm.Designer.cs">
      <DependentUpon>MessageForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="Surfaces\Surface.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Surfaces\Surface.Designer.cs">
      <DependentUpon>Surface.cs</DependentUpon>
    </Compile>
    <Compile Include="ToolBox.cs" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Breadcrumbs\BreadcrumbContainerSurface.resx">
      <DependentUpon>BreadcrumbContainerSurface.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\Data\CheckDataBox.resx">
      <DependentUpon>CheckDataBox.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\Data\ComboDataBox.resx">
      <DependentUpon>ComboDataBox.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\Data\DataBox.resx">
      <DependentUpon>DataBox.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\Data\TextDataBox.resx">
      <DependentUpon>TextDataBox.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\Data\ToggleDataBox.resx">
      <DependentUpon>ToggleDataBox.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\GridSystem\Grid.resx">
      <DependentUpon>Grid.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\GridSystem\GridFiller.resx">
      <DependentUpon>GridFiller.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\GridSystem\GridFilter.resx">
      <DependentUpon>GridFilter.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\ColorPicker.resx">
      <DependentUpon>ColorPicker.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Surfaces\RowDetailSurface.resx">
      <DependentUpon>RowDetailSurface.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Surfaces\RowSurface.resx">
      <DependentUpon>RowSurface.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Surfaces\GridSurface.resx">
      <DependentUpon>GridSurface.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\ProcessingAnimation.resx">
      <DependentUpon>ProcessingAnimation.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Controls\TabSystem\Tab.resx">
      <DependentUpon>Tab.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\DataForm.resx">
      <DependentUpon>DataForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\DataPicker.resx">
      <DependentUpon>DataPicker.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\DatePicker.resx">
      <DependentUpon>DatePicker.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MessageForm.resx">
      <DependentUpon>MessageForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Surfaces\Surface.resx">
      <DependentUpon>Surface.cs</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\processing_animation.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\appicon.ico" />
  </ItemGroup>
  <ItemGroup />
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>