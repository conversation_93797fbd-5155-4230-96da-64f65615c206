﻿Public Class LookupStoreByChain
    Public Sub New(ByVal GridData As BindingSource, ByVal AllowMultiSelect As Boolean)

        ' This call is required by the Windows Form Designer.
        InitializeComponent()
        InitializeChainComboBox(My.Settings.DBConnection)
        'load Data
        LoadData(GridData, AllowMultiSelect)





    End Sub

    Public Overloads Shared Function SelectRows _
    (ByVal ConnectionString As String,
    ByVal AllowMultiSelect As Boolean,
    ByVal GridToExclude As DataGridView) _
    As List(Of DataRow)

        ' A string variable to hold any potential errors during data retrieval.
        Dim ErrorMessage As String = String.Empty

        ' Create a data source for the list.
        Dim GridData As BindingSource = Lookup.GetStores(ConnectionString, ErrorMessage, GridToExclude)


        ' Create an instance of this lookup list form and return the user-selected rows.
        Using Selector As New LookupStoreByChain(GridData, AllowMultiSelect)
            Return SelectRows(Selector, ErrorMessage)
        End Using

    End Function

    Private Sub InitializeChainComboBox(ConnectionString)

        Dim ErrorMessage As String = String.Empty

        Dim GridData As DataTable = Lookup.GetChainsNonBindingSource(ConnectionString, ErrorMessage, Nothing)
        For Each ChainRow As DataRow In GridData.Rows
            ComboBoxChain.Properties.Items.Add(ChainRow.Item("ChainName"))

        Next

    End Sub



    Private Sub ComboBoxChain_EditValueChanged(sender As Object, e As EventArgs) Handles ComboBoxChain.SelectedValueChanged

        Dim ErrorMessage As String = String.Empty

        Dim GridData As BindingSource = Lookup.GetStores(My.Settings.DBConnection, ErrorMessage, Nothing, ComboBoxChain.SelectedText)
        LoadData(GridData, True)
    End Sub

    Private Sub LoadData(ByVal GridData As BindingSource, ByVal AllowMultiSelect As Boolean)
        GridController = New GridManager _
       (GridItems,
       TextEditSearch,
       GridData,
       My.Settings.DBConnection,
       PictureAdvancedSearch,
       PictureClearSearch,
       Nothing,
       Nothing)

        ' Apply multi-select restrictions if necessary.
        GridItems.MultiSelect = AllowMultiSelect
    End Sub
End Class