<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="ImageList24x24.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>144, 17</value>
  </metadata>
  <data name="ImageList24x24.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj00LjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAACU
        CgAAAk1TRnQBSQFMAgEBAgEAAUwBAAFMAQABFAEAARQBAAT/AREBAAj/AUIBTQE2BwABNgMAASgDAAFQ
        AwABFAMAAQEBAAEQBQABgAEMIAABWgFrATkBZwE5AWcBWgFrFgABWgFrAToBZwFaAWsBWgFnATkBZwFa
        AWsIAAFaAWsBOQFnAVoBZwFaAWsBOgFnAVoBa2AAAVoBawFaAWsBfAFvAXwBbwFaAWsBOQFnEgABOQFn
        ATkBZwF7AW8B9gFmATgBawGcAXMBOQFnAVoBawQAAVoBawE5AWcBewFvATkBawG1AWIBnAFzAVoBawE5
        AWdcAAFaAWsBOQFnAZwBcwE1AV8BFAFbAb4BdwE5AWcBWgFrDgABWgFrATkBZwF7AW8BEAFaAYMBPAHF
        AUABtQFmAZwBcwE5AWcBWgFrAgABOQFnAZwBbwH3AWoBCAFFAUIBOAGsAVEBewFvAVoBawFaAWtYAAFa
        AWsBGAFjAZwBdwHxAVIBJAEuAQIBKgETAVsBnQF3ATkBZw4AARgBYwG9AXMBMQFeAWIBPAFCATwBQgE8
        AaUBQAHWAWYBvQFzATkBZwEYAWMBnAFvATkBbwHnAUgBQgE8AWIBPAFBATwBjAFRAZwBcwE6AWdWAAFa
        AWsBGAFjAd8BfwE0AV8BIgEqAUQBLgFEAS4BIgEqAZwBcwF8AW8BOQFnDAABOQFnATkBbwFCAUABYgFA
        AWMBQAFjAUABQgFAAaQBRAEXAW8BnAFvAVsBawGbAXcB5wFIAUEBQAFjAUABYwFAAWIBQAFBAUABlAFi
        AVoBa1QAAVoBawE5AWcBvQF3ATMBWwFCAS4BZAEyAWQBMgFkATIBQgEuAe0BSgGcAXMBOgFnAVoBawoA
        ATkBZwF7AXMB5gFMAUEBRAFjAUQBYwFEAWMBRAFCAUABxQFIARgBcwGcAXcB5wFMAUEBQAFjAUQBYwFE
        AWMBRAFiAUQBpAFIAfcBagF7AWtSAAFaAWsBOgFnAZwBcwEzAVsBYgEuAYQBMgGEATYBhAE2AYQBNgGE
        ATYBhAE2AXYBZwF7AW8BOQFnCgABOQFnAb0BcwH3AW4BxgFMAUIBRAFjAUgBYwFIAWMBSAFCAUQBBwFR
        AUkBVQFBAUQBYwFIAWMBSAFjAUgBQgFEAYQBSAGTAWYBvQFzAVoBa1IAATkBZwGcAXMBUgFbAaUBOgGk
        ATYBpAE6AaQBOgGkAToBpAE6AaQBOgGDATYB6gFGAZoBbwE6AWsBWgFrCgABWgFrAZwBcwEYAW8BxQFQ
        AUEBSAGDAUwBgwFMAYMBTAFiAUgBYgFIAWMBTAGDAUwBgwFMAUIBSAFiAUgB1QFqAb0BdwF7AWsBWgFr
        UAABGAFjAb4BdwF0AWMBxAE6AaQBOgHEAToBxAE6AaQBOgGjATYBpAE6AcQBOgHEAToBpAE6AVMBXwG+
        AXcBGAFjCgABWgFrAVoBawG9AXcBOQFzAWIBTAFBAUwBgwFMAYMBTAGDAUwBgwFMAYMBTAGDAUwBQgFM
        AUEBTAEXAW8B3gF7AVoBawFaAWtSAAE5AWcBugFzAcQBOgHDAToBxAE+AcQBPgHDAToB5gFCAVABWwHE
        AToBxAE+AcQBPgHDAToB5QE+AbsBcwFbAWsBOQFnDAABWgFrAd4BdwHeAXsBxQFUAWIBUAGDAVABgwFQ
        AYMBUAGDAVABYgFQAaQBVAE5AXMB/wF7ATkBZ1YAAToBawG7AXcBBwFHAeMBOgHkAT4B4wE+AeUBQgGW
        AWsB/wF/AU0BUwHiAToB5AE+AeQBPgHiAToBKwFPAf4BfwE5AWcBWgFrCAABWgFrATkBZwGcAXMBnAF7
        ASkBXQFCAVABgwFUAYMBVAGDAVQBgwFUAWIBVAHnAVgBGAF3Ab0BcwE5AWcBWgFrVAABWgFrAb0BdwG3
        AWsBBwFHAeIBOgEEAUMBlgFrAb4BewGcAXMBuwF3AQUBQwHjAT4BBAFDAQQBQwHiAToBcQFfAd4BewFa
        AWsBWgFrBAABWgFrAToBZwGcAXMBOQF3AQcBXQFBAVQBgwFUAYMBWAGDAVgBgwFYAYMBWAGDAVgBQgFU
        AcYBWAEYAXcBvQFzAVoBawFaAWtUAAFaAWsBvQF3AbcBbwFOAVcBtwFvAd4CewFvAVoBawHeAXsBuAFv
        AeIBPgEDAUMBBAFDAQMBQwHiAT4BlQFrAb0BdwE6AWcBWgFrAgABOQFnAZwBcwE5AXcBCAFhAUEBWAGD
        AVgBgwFYAYMBWAFiAVgBYgFYAYMBWAGDAVgBgwFYAUIBWAGlAVwB9wFyAb0BdwE5AWcBWgFrUgABWgFr
        AVoBawGcAXMB3QF7AZ0BcwFbAWsBWgFrAgABWgFrAf8BfwFwAV8B4QFCAQMBRwEDAUcBAgFDAQQBRwG2
        AWsBvQF3ATkBZwE5AWcBnAFzATkBdwEIAWEBYgFcAYMBXAGDAVwBgwFcAWIBXAGkAVwBxQFgAUEBXAGD
        AVwBgwFcAYMBXAFiAVwBpQFcAdYBcgG9AXcBWgFrZAABewFvAfwBewEoAVMBAgFDAQMBRwEDAUcBAgFH
        ASYBSwHZAXMBWgFrARgBYwGcAXsBKQFlAUEBXAGDAWABgwFgAYMBYAFiAWABYgFcAVoBewH/AX8BYgFg
        AUEBXAGDAWABgwFgAYMBYAFiAVwBxgFgATkCewFvZgABvQF3AdgBcwElAU8BAgFHAQIBSwEBAUcBAgFH
        AbUBawF7AW8BWgFrAVoBewFjAWABYgFgAYMBZAGDAWQBYgFgAaQBZAG1AXYB3gF7Ad4BewE5AXsBxQFk
        AWIBYAGDAWQBgwFkAWMBYAFiAWABtAF2AXsBb2YAAVoBawHeAXsBtAFrASMBTwEBAUcBJQFPAZEBZwHc
        AXcBewFvAVoBawG9AXsB7wFxAWMBZAGDAWQBYgFkAYMBZAG1AXYB3gF7AVoBawE5AWcB3gF7ARgBewHm
        AWgBYgFkAYMBZAFiAWQBiwFtA3sBb2YAAVoBawF7AW8B3QF7AbYBbwGQAWMB2AFzAd0CewFvAVoBawFa
        AWsBnAFzAZsBewHuAXEBYgFoAWIBaAG1AXYB3gJ7AW8BWgFrAgABewFrAb0BdwH3AXoBxQFoASEBZAFr
        AW0BWgF7AZwBcwFaAWtoAAFaAWsBewFvAb0BdwHeAXsBnAFzAXsBbwFaAWsEAAFaAWsBnAFzAb0BewEY
        AXsBWgF7Ad4BewFaAWsBWgFrBAABWgFrAVoBawG9AnsBfwH3AXoBnAF/Ab0BdwFaAWtuAAE5AWcBOQFn
        AVoBawwAAXsBbwGcAXMBnAFzAVoBawwAAVoBawGcAXMBvQF3AXsBb1YAAUIBTQE+BwABPgMAASgDAAFQ
        AwABFAMAAQEBAAEBBQAB8BcAA/8BAAH+AR8B/AEPAQMHAAH8AQ8B+AEGAQEHAAH4AQcB8AECCAAB8AEH
        AfAJAAHgAQMB8AkAAcABAQHwCQABgAEBAfAJAAGAAQAB+AsAAfgBAAEBCQABfgEAAQcJAAE8AQABAwkA
        ARgBAAEBBwABgAEAAQgJAAKACgAB/wHACgAB/wHgCgAB/wHgCgAB/wHgAQABAggAAf8B8AEYAQYBAQcA
        Af8B/AF+AR8BhwcACw==
</value>
  </data>
  <metadata name="ErrorManager.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
</root>