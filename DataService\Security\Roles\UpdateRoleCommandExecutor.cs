﻿using DataAccess;

namespace DataService.Security
{
    class UpdateRoleCommandExecutor : CommandExecutor<UpdateRoleCommand>
    {

        public override void Execute(UpdateRoleCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.UpdateRole))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.AddInputParameter("roleid", command.RoleId);
                storedprocedure.AddInputParameter("rolename", command.RoleName);
                storedprocedure.AddInputParameter("roledescription", command.RoleDescription);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
            }
        }

    }
}
