﻿using System;
using System.Data;
using System.Data.SqlClient;

namespace DataAccess
{
    public class StoredProcedure : IDisposable
    {
        private SqlCommand SqlCommand;
        private string StoredProcedureName;


        #region Constructors

        public StoredProcedure(string storedprocedurename)
        {
            StoredProcedureName = storedprocedurename;
        }

        public StoredProcedure(string storedprocedurename, SqlConnection connection)
        {
            StoredProcedureName = storedprocedurename;
            Connection = connection;
        }

        #endregion


        #region Connection

        public SqlConnection Connection
        {
            get { return SqlCommand.Connection; }
            set
            {
                SqlCommand = new SqlCommand(StoredProcedureName, value);
                SqlCommand.CommandTimeout = 0;
                SqlCommand.CommandType = CommandType.StoredProcedure;

                // Every stored procedure needs an output parameter to report errors that may have occured during execution.
                AddOutputParameter<string>("errormessage");

                if (value.State != ConnectionState.Open)
                {
                    try
                    {
                        value.Open();
                    }
                    catch (Exception ex)
                    {
                        ErrorMessage = ex.Message;
                    }
                }
                SqlCommand.Connection = value;
            }
        }

        #endregion


        #region Parameters

        public void AddInputParameter(string parametername, object parametervalue)
        {
            SqlCommand.Parameters.AddWithValue(parametername, parametervalue);
        }

        public void RemoveInputParameter(string parametername)
        {
            SqlParameter parameter = SqlCommand.Parameters[parametername];
            SqlCommand.Parameters.Remove(parameter);
        }

        public void AddOutputParameter<TOutputParameter>(string parametername)
        {
            SqlParameter newparameter = NewParameter<TOutputParameter>(parametername, ParameterDirection.Output);
            SqlCommand.Parameters.Add(newparameter);
        }

        private SqlParameter NewParameter<TParameter>(string parametername, ParameterDirection direction)
        {
            // Create a new parameter and specify the name.
            SqlParameter parameter = new SqlParameter();
            parameter.ParameterName = parametername;
            parameter.Direction = direction;

            // Figure out what the SQL type of the output will be by checking the .NET type specified in <TParameter>.
            if (typeof(TParameter) == typeof(string))
            {
                parameter.SqlDbType = SqlDbType.NVarChar;
                parameter.Size = 3000;
            }
            if (typeof(TParameter) == typeof(Guid))
            {
                parameter.SqlDbType = SqlDbType.UniqueIdentifier;
            }
            if (typeof(TParameter) == typeof(int))
            {
                parameter.SqlDbType = SqlDbType.Int;
            }
            if (typeof(TParameter) == typeof(bool))
            {
                parameter.SqlDbType = SqlDbType.Bit;
            }

            // Return the new parameter.
            return parameter;
        }

        public object GetOutputParameterValue(string parametername)
        {
            return SqlCommand.Parameters[parametername].Value;
        }

        #endregion


        #region Execution

        private string _ErrorMessage = string.Empty;
        public string ErrorMessage
        {
            get { return _ErrorMessage; }
            private set { _ErrorMessage = value; }
        }

        private DataTable _OutputTable = new DataTable();
        public DataTable OutputTable
        {
            get { return _OutputTable; }
        }

        public object OutputScalarValue
        {
            get
            {
                return (OutputTable.Rows.Count > 0 && OutputTable.Columns.Count > 0 ? OutputTable.Rows[0][0] : null);
            }
        }

        public void Execute()
        {
            // Create a variable to hold the result.
            SqlDataReader sqldatareader = null;

            // Try to execute the command.
            try
            {
                sqldatareader = SqlCommand.ExecuteReader();
            }
            catch (Exception ex)
            {
                ErrorMessage = ex.Message;
            }

            // Populate the output data table, and extract the error message if there was one.
            if (string.IsNullOrEmpty(ErrorMessage))
            {
                OutputTable.Load(sqldatareader);
                SqlParameter parameter = SqlCommand.Parameters["errormessage"];
                string sqlerror = GetOutputParameterValue("errormessage").ToString();
                ErrorMessage = string.IsNullOrEmpty(sqlerror) ? string.Empty : sqlerror;
            }
        }

        #endregion


        #region Cleanup

        public void Dispose()
        {
            SqlCommand.Dispose();
        }

        #endregion

    }
}
