Public Class FormMediaGapSelectedChains

    Public UpdatedChainSelection As DataTable
    Private AvailableChains As DataTable

#Region "Public Methods"

    Public Shared Function UpdateSelectedChains(ByVal CurrentData As DataTable) As DataTable
        ' Create an instance of this form, allow the user to modify the list of chains, then when the form
        ' is closed, return the modified chain list.

        Using ChainSelectionForm As New FormMediaGapSelectedChains(CurrentData, False)
            ChainSelectionForm.ShowDialog()
            Return ChainSelectionForm.UpdatedChainSelection
        End Using

    End Function

    Public Shared Function SelectAll(ByVal CurrentData As DataTable) As DataTable
        ' Create an instance of this form and return the entire list of chains.

        Using SelectionForm As New FormMediaGapSelectedChains(CurrentData, True)
            Return SelectionForm.UpdatedChainSelection
        End Using

    End Function

    Public Sub New(ByVal CurrentData As DataTable, ByVal SelectAll As Boolean)

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        UpdatedChainSelection = CurrentData

        ' Create binding sources for the grid data.
        SetupSelectedGridDataSource()
        SetupAvailableGridDataSource()

        ' Setup a grid manager for the selected items.
        Dim SelectedGridController As New GridManager _
        (GridSelectedItems,
        TextEditSearchSelected,
        Nothing,
        My.Settings.DBConnection,
        PictureAdvancedSearchSelected,
        PictureClearSearchSelected,
        Nothing,
        ButtonRemove)

        ' Setup a grid manager for the selected items.
        Dim AvailableGridController As New GridManager _
        (GridAvailableItems,
        TextEditSearchAvailable,
        Nothing,
        My.Settings.DBConnection,
        PictureAdvancedSearchAvailable,
        PictureClearSearchAvailable,
        Nothing,
        ButtonAdd)

        ' Select all available items.
        If SelectAll Then
            GridAvailableItems.SelectAll()
            ButtonAdd_Click(Nothing, Nothing)
        End If

    End Sub

#End Region

#Region "Event Handlers"

    Private Sub ButtonAdd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonAdd.Click

        ' Move the selected chains from the available grid to the selected grid.
        For Each SelectedRow As DataGridViewRow In GridAvailableItems.SelectedRows
            AddItemToSelectedList(SelectedRow)
        Next

    End Sub

    Private Sub ButtonRemove_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonRemove.Click

        ' Move the selected chains from the selected grid to the available grid.
        For Each SelectedRow As DataGridViewRow In GridSelectedItems.SelectedRows
            Dim SelectedDataRow As DataRowView = CType(SelectedRow.DataBoundItem, DataRowView)
            Dim NewRow As DataRow = AvailableChains.NewRow
            NewRow("ChainID") = SelectedDataRow("ChainID")
            NewRow("ChainName") = SelectedDataRow("ChainName")
            AvailableChains.Rows.Add(NewRow)
            SelectedDataRow.Row.Delete()
        Next

    End Sub

    Private Sub GridAvailableItems_CellMouseDoubleClick _
    (ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) _
    Handles GridAvailableItems.CellMouseDoubleClick
        If e.RowIndex > -1 Then
            ' The row double-clicked is NOT the column header row. Handle the event.
            ButtonAdd_Click(sender, e)
        End If
    End Sub

    Private Sub GridSelectedItems_CellMouseDoubleClick _
    (ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) _
    Handles GridSelectedItems.CellMouseDoubleClick
        If e.RowIndex > -1 Then
            ' The row double-clicked is NOT the column header row. Handle the event.
            ButtonRemove_Click(sender, e)
        End If
    End Sub

#End Region

#Region "Private Methods"

    Private Sub AddItemToSelectedList(ByVal GridRow As DataGridViewRow)
        ' Add the given gridrow to the selected items list.
        Dim SelectedDataRow As DataRowView = CType(GridRow.DataBoundItem, DataRowView)
        Dim NewRow As DataRow = UpdatedChainSelection.NewRow
        NewRow("ChainID") = SelectedDataRow("ChainID")
        NewRow("ChainName") = SelectedDataRow("ChainName")
        UpdatedChainSelection.Rows.Add(NewRow)
        SelectedDataRow.Row.Delete()
    End Sub

    Private Sub SetupSelectedGridDataSource()

        ' Create a data set to hold the table.
        Dim DS As DataSet = UpdatedChainSelection.DataSet
        If IsNothing(DS) Then
            DS = New DataSet
            DS.Tables.Add(UpdatedChainSelection)
        End If

        ' Create a binding source that will be used as the grid's data source.
        Dim GridBindingSource As New BindingSource(DS, UpdatedChainSelection.TableName)
        GridBindingSource.Sort = "ChainName"

        ' Set the grid's data source property.
        GridSelectedItems.AutoGenerateColumns = False
        GridSelectedItems.DataSource = GridBindingSource

    End Sub

    Private Sub SetupAvailableGridDataSource()

        ' Create a string to hold data retrieval error messages.
        Dim ErrorMessage As String = String.Empty

        ' Get the data for the grid.
        Dim ProposedBindingSource As BindingSource = Lookup.GetChains(My.Settings.DBConnection, ErrorMessage, GridSelectedItems)
        ProposedBindingSource.Sort = "ChainName"
        If String.IsNullOrEmpty(ErrorMessage) = False Then
            ' Errors were generated during data collection.
            Dim MessageText As String = "An error occured while trying to load the list of chains." & vbCrLf & vbCrLf & ErrorMessage
            Dim ParentForm As BaseForm = CType(My.Application.ApplicationContext.MainForm, BaseForm)
            ParentForm.ShowMessage(MessageText, "Huston!  We have a problem!", MessageBoxIcon.Error)
            GridAvailableItems.Visible = False
            Exit Sub
        End If

        ' Set the grid's data source property.
        GridAvailableItems.AutoGenerateColumns = False
        GridAvailableItems.DataSource = ProposedBindingSource
        AvailableChains = LiquidAgent.GetBindingSourceTable(ProposedBindingSource)

    End Sub

#End Region

End Class
