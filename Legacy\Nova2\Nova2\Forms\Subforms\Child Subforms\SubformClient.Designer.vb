<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformClient
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformClient))
        Me.TableLayoutPanelDetails = New System.Windows.Forms.TableLayoutPanel()
        Me.PanelDetails = New System.Windows.Forms.Panel()
        Me.TextEditClientAbbreviation = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.CheckEditApproved = New DevExpress.XtraEditors.CheckEdit()
        Me.LabelFax = New DevExpress.XtraEditors.LabelControl()
        Me.LabelAccountManager = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkAccountManager = New DevExpress.XtraEditors.LabelControl()
        Me.CheckEditAgency = New DevExpress.XtraEditors.CheckEdit()
        Me.GroupControlNotes = New DevExpress.XtraEditors.GroupControl()
        Me.MemoEditNotes = New DevExpress.XtraEditors.MemoEdit()
        Me.TextEditTelephone = New DevExpress.XtraEditors.TextEdit()
        Me.LabelTelephone = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditFax = New DevExpress.XtraEditors.TextEdit()
        Me.TextEditAddressLine1 = New DevExpress.XtraEditors.TextEdit()
        Me.LabelAddressLine1 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelPaymentTerms = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditAddressLine2 = New DevExpress.XtraEditors.TextEdit()
        Me.LabelAddressLine2 = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditPostalCode = New DevExpress.XtraEditors.TextEdit()
        Me.LabelPostalCode = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkTerms = New DevExpress.XtraEditors.LabelControl()
        Me.LabelCity = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkCity = New DevExpress.XtraEditors.LabelControl()
        Me.LabelClassification = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkClassification = New DevExpress.XtraEditors.LabelControl()
        Me.CheckEditDormant = New DevExpress.XtraEditors.CheckEdit()
        Me.TextEditClientName = New DevExpress.XtraEditors.TextEdit()
        Me.LabelClientName = New DevExpress.XtraEditors.LabelControl()
        Me.HeadingDetails = New DevExpress.XtraEditors.LabelControl()
        Me.ImageList16x16 = New System.Windows.Forms.ImageList(Me.components)
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonSave = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.CheckEditRetailer = New DevExpress.XtraEditors.CheckEdit()
        Me.TableLayoutPanelDetails.SuspendLayout()
        Me.PanelDetails.SuspendLayout()
        CType(Me.TextEditClientAbbreviation.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditApproved.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditAgency.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControlNotes, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlNotes.SuspendLayout()
        CType(Me.MemoEditNotes.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditTelephone.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditFax.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditAddressLine1.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditAddressLine2.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditPostalCode.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditDormant.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditClientName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditRetailer.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!)
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Margin = New System.Windows.Forms.Padding(5, 5, 5, 21)
        Me.LabelTitle.Size = New System.Drawing.Size(170, 37)
        Me.LabelTitle.TabIndex = 0
        Me.LabelTitle.Text = "(new client)"
        '
        'TableLayoutPanelDetails
        '
        Me.TableLayoutPanelDetails.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanelDetails.BackColor = System.Drawing.Color.Transparent
        Me.TableLayoutPanelDetails.ColumnCount = 1
        Me.TableLayoutPanelDetails.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanelDetails.Controls.Add(Me.PanelDetails, 0, 2)
        Me.TableLayoutPanelDetails.Controls.Add(Me.HeadingDetails, 0, 0)
        Me.TableLayoutPanelDetails.Location = New System.Drawing.Point(15, 86)
        Me.TableLayoutPanelDetails.Margin = New System.Windows.Forms.Padding(4, 16, 4, 4)
        Me.TableLayoutPanelDetails.Name = "TableLayoutPanelDetails"
        Me.TableLayoutPanelDetails.RowCount = 3
        Me.TableLayoutPanelDetails.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanelDetails.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanelDetails.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanelDetails.Size = New System.Drawing.Size(1027, 560)
        Me.TableLayoutPanelDetails.TabIndex = 0
        '
        'PanelDetails
        '
        Me.PanelDetails.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelDetails.Controls.Add(Me.CheckEditRetailer)
        Me.PanelDetails.Controls.Add(Me.TextEditClientAbbreviation)
        Me.PanelDetails.Controls.Add(Me.LabelControl1)
        Me.PanelDetails.Controls.Add(Me.CheckEditApproved)
        Me.PanelDetails.Controls.Add(Me.LabelFax)
        Me.PanelDetails.Controls.Add(Me.LabelAccountManager)
        Me.PanelDetails.Controls.Add(Me.HyperlinkAccountManager)
        Me.PanelDetails.Controls.Add(Me.CheckEditAgency)
        Me.PanelDetails.Controls.Add(Me.GroupControlNotes)
        Me.PanelDetails.Controls.Add(Me.TextEditTelephone)
        Me.PanelDetails.Controls.Add(Me.LabelTelephone)
        Me.PanelDetails.Controls.Add(Me.TextEditFax)
        Me.PanelDetails.Controls.Add(Me.TextEditAddressLine1)
        Me.PanelDetails.Controls.Add(Me.LabelAddressLine1)
        Me.PanelDetails.Controls.Add(Me.LabelPaymentTerms)
        Me.PanelDetails.Controls.Add(Me.TextEditAddressLine2)
        Me.PanelDetails.Controls.Add(Me.LabelAddressLine2)
        Me.PanelDetails.Controls.Add(Me.TextEditPostalCode)
        Me.PanelDetails.Controls.Add(Me.LabelPostalCode)
        Me.PanelDetails.Controls.Add(Me.HyperlinkTerms)
        Me.PanelDetails.Controls.Add(Me.LabelCity)
        Me.PanelDetails.Controls.Add(Me.HyperlinkCity)
        Me.PanelDetails.Controls.Add(Me.LabelClassification)
        Me.PanelDetails.Controls.Add(Me.HyperlinkClassification)
        Me.PanelDetails.Controls.Add(Me.CheckEditDormant)
        Me.PanelDetails.Controls.Add(Me.TextEditClientName)
        Me.PanelDetails.Controls.Add(Me.LabelClientName)
        Me.PanelDetails.Location = New System.Drawing.Point(0, 44)
        Me.PanelDetails.Margin = New System.Windows.Forms.Padding(0)
        Me.PanelDetails.Name = "PanelDetails"
        Me.PanelDetails.Size = New System.Drawing.Size(1027, 516)
        Me.PanelDetails.TabIndex = 1
        '
        'TextEditClientAbbreviation
        '
        Me.TextEditClientAbbreviation.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditClientAbbreviation.Location = New System.Drawing.Point(157, 108)
        Me.TextEditClientAbbreviation.Margin = New System.Windows.Forms.Padding(4)
        Me.TextEditClientAbbreviation.Name = "TextEditClientAbbreviation"
        Me.TextEditClientAbbreviation.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditClientAbbreviation.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditClientAbbreviation.Properties.Appearance.Options.UseFont = True
        Me.TextEditClientAbbreviation.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditClientAbbreviation.Properties.MaxLength = 200
        Me.TextEditClientAbbreviation.Size = New System.Drawing.Size(482, 24)
        Me.TextEditClientAbbreviation.TabIndex = 7
        '
        'LabelControl1
        '
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl1.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl1.Location = New System.Drawing.Point(0, 112)
        Me.LabelControl1.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(138, 17)
        Me.LabelControl1.TabIndex = 6
        Me.LabelControl1.Text = "Client Abbreviation:"
        '
        'CheckEditApproved
        '
        Me.CheckEditApproved.Enabled = False
        Me.CheckEditApproved.Location = New System.Drawing.Point(154, 453)
        Me.CheckEditApproved.Margin = New System.Windows.Forms.Padding(4, 5, 4, 4)
        Me.CheckEditApproved.Name = "CheckEditApproved"
        Me.CheckEditApproved.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditApproved.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditApproved.Properties.Appearance.Options.UseFont = True
        Me.CheckEditApproved.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditApproved.Properties.AutoWidth = True
        Me.CheckEditApproved.Properties.Caption = "Approved by the finance department"
        Me.CheckEditApproved.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditApproved.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditApproved.Size = New System.Drawing.Size(281, 21)
        Me.CheckEditApproved.TabIndex = 24
        '
        'LabelFax
        '
        Me.LabelFax.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelFax.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelFax.Location = New System.Drawing.Point(0, 185)
        Me.LabelFax.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelFax.Name = "LabelFax"
        Me.LabelFax.Size = New System.Drawing.Size(31, 17)
        Me.LabelFax.TabIndex = 10
        Me.LabelFax.Text = "Fax:"
        '
        'LabelAccountManager
        '
        Me.LabelAccountManager.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelAccountManager.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelAccountManager.Location = New System.Drawing.Point(0, 8)
        Me.LabelAccountManager.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelAccountManager.Name = "LabelAccountManager"
        Me.LabelAccountManager.Size = New System.Drawing.Size(127, 17)
        Me.LabelAccountManager.TabIndex = 0
        Me.LabelAccountManager.Text = "Account Manager:"
        '
        'HyperlinkAccountManager
        '
        Me.HyperlinkAccountManager.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkAccountManager.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkAccountManager.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkAccountManager.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkAccountManager.Location = New System.Drawing.Point(157, 8)
        Me.HyperlinkAccountManager.Margin = New System.Windows.Forms.Padding(4)
        Me.HyperlinkAccountManager.Name = "HyperlinkAccountManager"
        Me.HyperlinkAccountManager.Size = New System.Drawing.Size(57, 17)
        Me.HyperlinkAccountManager.TabIndex = 1
        Me.HyperlinkAccountManager.Text = "Select..."
        '
        'CheckEditAgency
        '
        Me.CheckEditAgency.Location = New System.Drawing.Point(154, 385)
        Me.CheckEditAgency.Margin = New System.Windows.Forms.Padding(4, 9, 4, 4)
        Me.CheckEditAgency.Name = "CheckEditAgency"
        Me.CheckEditAgency.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditAgency.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditAgency.Properties.Appearance.Options.UseFont = True
        Me.CheckEditAgency.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditAgency.Properties.AutoWidth = True
        Me.CheckEditAgency.Properties.Caption = "This is an agency"
        Me.CheckEditAgency.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditAgency.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditAgency.Size = New System.Drawing.Size(143, 21)
        Me.CheckEditAgency.TabIndex = 22
        '
        'GroupControlNotes
        '
        Me.GroupControlNotes.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlNotes.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlNotes.Appearance.Options.UseFont = True
        Me.GroupControlNotes.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlNotes.AppearanceCaption.Options.UseFont = True
        Me.GroupControlNotes.Controls.Add(Me.MemoEditNotes)
        Me.GroupControlNotes.Location = New System.Drawing.Point(658, 4)
        Me.GroupControlNotes.LookAndFeel.SkinName = "Black"
        Me.GroupControlNotes.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlNotes.Margin = New System.Windows.Forms.Padding(15, 4, 4, 4)
        Me.GroupControlNotes.Name = "GroupControlNotes"
        Me.GroupControlNotes.Size = New System.Drawing.Size(365, 508)
        Me.GroupControlNotes.TabIndex = 23
        Me.GroupControlNotes.Text = "Notes"
        '
        'MemoEditNotes
        '
        Me.MemoEditNotes.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MemoEditNotes.Location = New System.Drawing.Point(2, 25)
        Me.MemoEditNotes.Margin = New System.Windows.Forms.Padding(4)
        Me.MemoEditNotes.Name = "MemoEditNotes"
        Me.MemoEditNotes.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.MemoEditNotes.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.MemoEditNotes.Properties.Appearance.Options.UseFont = True
        Me.MemoEditNotes.Properties.Appearance.Options.UseForeColor = True
        Me.MemoEditNotes.Properties.Appearance.Options.UseTextOptions = True
        Me.MemoEditNotes.Properties.Appearance.TextOptions.Trimming = DevExpress.Utils.Trimming.Word
        Me.MemoEditNotes.Properties.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top
        Me.MemoEditNotes.Properties.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.MemoEditNotes.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.MemoEditNotes.Properties.MaxLength = 2000
        Me.MemoEditNotes.Properties.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.MemoEditNotes.Size = New System.Drawing.Size(361, 481)
        Me.MemoEditNotes.TabIndex = 27
        '
        'TextEditTelephone
        '
        Me.TextEditTelephone.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditTelephone.Location = New System.Drawing.Point(157, 147)
        Me.TextEditTelephone.Margin = New System.Windows.Forms.Padding(4)
        Me.TextEditTelephone.Name = "TextEditTelephone"
        Me.TextEditTelephone.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditTelephone.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditTelephone.Properties.Appearance.Options.UseFont = True
        Me.TextEditTelephone.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditTelephone.Properties.Mask.EditMask = "\d?\d?\d? \d\d\d \d\d\d\d"
        Me.TextEditTelephone.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Regular
        Me.TextEditTelephone.Properties.Mask.SaveLiteral = False
        Me.TextEditTelephone.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TextEditTelephone.Properties.MaxLength = 200
        Me.TextEditTelephone.Size = New System.Drawing.Size(482, 24)
        Me.TextEditTelephone.TabIndex = 9
        '
        'LabelTelephone
        '
        Me.LabelTelephone.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelTelephone.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelTelephone.Location = New System.Drawing.Point(0, 151)
        Me.LabelTelephone.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelTelephone.Name = "LabelTelephone"
        Me.LabelTelephone.Size = New System.Drawing.Size(78, 17)
        Me.LabelTelephone.TabIndex = 8
        Me.LabelTelephone.Text = "Telephone:"
        '
        'TextEditFax
        '
        Me.TextEditFax.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditFax.Location = New System.Drawing.Point(157, 181)
        Me.TextEditFax.Margin = New System.Windows.Forms.Padding(4)
        Me.TextEditFax.Name = "TextEditFax"
        Me.TextEditFax.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditFax.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditFax.Properties.Appearance.Options.UseFont = True
        Me.TextEditFax.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditFax.Properties.Mask.EditMask = "\d?\d?\d? \d\d\d \d\d\d\d"
        Me.TextEditFax.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Regular
        Me.TextEditFax.Properties.Mask.SaveLiteral = False
        Me.TextEditFax.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TextEditFax.Properties.MaxLength = 200
        Me.TextEditFax.Size = New System.Drawing.Size(482, 24)
        Me.TextEditFax.TabIndex = 11
        '
        'TextEditAddressLine1
        '
        Me.TextEditAddressLine1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditAddressLine1.Location = New System.Drawing.Point(157, 215)
        Me.TextEditAddressLine1.Margin = New System.Windows.Forms.Padding(4)
        Me.TextEditAddressLine1.Name = "TextEditAddressLine1"
        Me.TextEditAddressLine1.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditAddressLine1.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditAddressLine1.Properties.Appearance.Options.UseFont = True
        Me.TextEditAddressLine1.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditAddressLine1.Properties.MaxLength = 200
        Me.TextEditAddressLine1.Size = New System.Drawing.Size(482, 24)
        Me.TextEditAddressLine1.TabIndex = 13
        '
        'LabelAddressLine1
        '
        Me.LabelAddressLine1.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelAddressLine1.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelAddressLine1.Location = New System.Drawing.Point(0, 219)
        Me.LabelAddressLine1.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelAddressLine1.Name = "LabelAddressLine1"
        Me.LabelAddressLine1.Size = New System.Drawing.Size(111, 17)
        Me.LabelAddressLine1.TabIndex = 12
        Me.LabelAddressLine1.Text = "Address Line 1:"
        '
        'LabelPaymentTerms
        '
        Me.LabelPaymentTerms.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelPaymentTerms.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelPaymentTerms.Location = New System.Drawing.Point(0, 355)
        Me.LabelPaymentTerms.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelPaymentTerms.Name = "LabelPaymentTerms"
        Me.LabelPaymentTerms.Size = New System.Drawing.Size(115, 17)
        Me.LabelPaymentTerms.TabIndex = 20
        Me.LabelPaymentTerms.Text = "Payment Terms:"
        '
        'TextEditAddressLine2
        '
        Me.TextEditAddressLine2.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditAddressLine2.Location = New System.Drawing.Point(157, 249)
        Me.TextEditAddressLine2.Margin = New System.Windows.Forms.Padding(4)
        Me.TextEditAddressLine2.Name = "TextEditAddressLine2"
        Me.TextEditAddressLine2.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditAddressLine2.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditAddressLine2.Properties.Appearance.Options.UseFont = True
        Me.TextEditAddressLine2.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditAddressLine2.Properties.MaxLength = 200
        Me.TextEditAddressLine2.Size = New System.Drawing.Size(482, 24)
        Me.TextEditAddressLine2.TabIndex = 15
        '
        'LabelAddressLine2
        '
        Me.LabelAddressLine2.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelAddressLine2.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelAddressLine2.Location = New System.Drawing.Point(0, 253)
        Me.LabelAddressLine2.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelAddressLine2.Name = "LabelAddressLine2"
        Me.LabelAddressLine2.Size = New System.Drawing.Size(111, 17)
        Me.LabelAddressLine2.TabIndex = 14
        Me.LabelAddressLine2.Text = "Address Line 2:"
        '
        'TextEditPostalCode
        '
        Me.TextEditPostalCode.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditPostalCode.Location = New System.Drawing.Point(157, 317)
        Me.TextEditPostalCode.Margin = New System.Windows.Forms.Padding(4)
        Me.TextEditPostalCode.Name = "TextEditPostalCode"
        Me.TextEditPostalCode.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditPostalCode.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditPostalCode.Properties.Appearance.Options.UseFont = True
        Me.TextEditPostalCode.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditPostalCode.Properties.MaxLength = 4
        Me.TextEditPostalCode.Size = New System.Drawing.Size(482, 24)
        Me.TextEditPostalCode.TabIndex = 19
        '
        'LabelPostalCode
        '
        Me.LabelPostalCode.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelPostalCode.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelPostalCode.Location = New System.Drawing.Point(0, 321)
        Me.LabelPostalCode.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelPostalCode.Name = "LabelPostalCode"
        Me.LabelPostalCode.Size = New System.Drawing.Size(89, 17)
        Me.LabelPostalCode.TabIndex = 18
        Me.LabelPostalCode.Text = "Postal Code:"
        '
        'HyperlinkTerms
        '
        Me.HyperlinkTerms.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkTerms.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkTerms.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkTerms.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkTerms.Location = New System.Drawing.Point(157, 355)
        Me.HyperlinkTerms.Margin = New System.Windows.Forms.Padding(4)
        Me.HyperlinkTerms.Name = "HyperlinkTerms"
        Me.HyperlinkTerms.Size = New System.Drawing.Size(57, 17)
        Me.HyperlinkTerms.TabIndex = 21
        Me.HyperlinkTerms.Text = "Select..."
        '
        'LabelCity
        '
        Me.LabelCity.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCity.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCity.Location = New System.Drawing.Point(0, 287)
        Me.LabelCity.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelCity.Name = "LabelCity"
        Me.LabelCity.Size = New System.Drawing.Size(33, 17)
        Me.LabelCity.TabIndex = 16
        Me.LabelCity.Text = "City:"
        '
        'HyperlinkCity
        '
        Me.HyperlinkCity.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkCity.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkCity.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkCity.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkCity.Location = New System.Drawing.Point(157, 287)
        Me.HyperlinkCity.Margin = New System.Windows.Forms.Padding(4)
        Me.HyperlinkCity.Name = "HyperlinkCity"
        Me.HyperlinkCity.Size = New System.Drawing.Size(57, 17)
        Me.HyperlinkCity.TabIndex = 17
        Me.HyperlinkCity.Text = "Select..."
        '
        'LabelClassification
        '
        Me.LabelClassification.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelClassification.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelClassification.Location = New System.Drawing.Point(0, 42)
        Me.LabelClassification.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelClassification.Name = "LabelClassification"
        Me.LabelClassification.Size = New System.Drawing.Size(96, 17)
        Me.LabelClassification.TabIndex = 2
        Me.LabelClassification.Text = "Classification:"
        '
        'HyperlinkClassification
        '
        Me.HyperlinkClassification.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkClassification.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkClassification.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkClassification.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkClassification.Location = New System.Drawing.Point(157, 42)
        Me.HyperlinkClassification.Margin = New System.Windows.Forms.Padding(4)
        Me.HyperlinkClassification.Name = "HyperlinkClassification"
        Me.HyperlinkClassification.Size = New System.Drawing.Size(57, 17)
        Me.HyperlinkClassification.TabIndex = 3
        Me.HyperlinkClassification.Text = "Select..."
        '
        'CheckEditDormant
        '
        Me.CheckEditDormant.Location = New System.Drawing.Point(154, 419)
        Me.CheckEditDormant.Margin = New System.Windows.Forms.Padding(4, 5, 4, 4)
        Me.CheckEditDormant.Name = "CheckEditDormant"
        Me.CheckEditDormant.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditDormant.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditDormant.Properties.Appearance.Options.UseFont = True
        Me.CheckEditDormant.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditDormant.Properties.AutoWidth = True
        Me.CheckEditDormant.Properties.Caption = "This client is dormant"
        Me.CheckEditDormant.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditDormant.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditDormant.Size = New System.Drawing.Size(173, 21)
        Me.CheckEditDormant.TabIndex = 23
        '
        'TextEditClientName
        '
        Me.TextEditClientName.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditClientName.Location = New System.Drawing.Point(157, 72)
        Me.TextEditClientName.Margin = New System.Windows.Forms.Padding(4)
        Me.TextEditClientName.Name = "TextEditClientName"
        Me.TextEditClientName.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditClientName.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditClientName.Properties.Appearance.Options.UseFont = True
        Me.TextEditClientName.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditClientName.Properties.MaxLength = 200
        Me.TextEditClientName.Size = New System.Drawing.Size(482, 24)
        Me.TextEditClientName.TabIndex = 5
        '
        'LabelClientName
        '
        Me.LabelClientName.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelClientName.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelClientName.Location = New System.Drawing.Point(0, 76)
        Me.LabelClientName.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelClientName.Name = "LabelClientName"
        Me.LabelClientName.Size = New System.Drawing.Size(89, 17)
        Me.LabelClientName.TabIndex = 4
        Me.LabelClientName.Text = "Client Name:"
        '
        'HeadingDetails
        '
        Me.HeadingDetails.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.HeadingDetails.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HeadingDetails.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HeadingDetails.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.HeadingDetails.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.HeadingDetails.LineVisible = True
        Me.HeadingDetails.Location = New System.Drawing.Point(4, 4)
        Me.HeadingDetails.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.HeadingDetails.Name = "HeadingDetails"
        Me.HeadingDetails.Size = New System.Drawing.Size(1019, 24)
        Me.HeadingDetails.TabIndex = 0
        Me.HeadingDetails.Text = "Client Details"
        '
        'ImageList16x16
        '
        Me.ImageList16x16.ImageStream = CType(resources.GetObject("ImageList16x16.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList16x16.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList16x16.Images.SetKeyName(0, "add.png")
        Me.ImageList16x16.Images.SetKeyName(1, "pencil.png")
        Me.ImageList16x16.Images.SetKeyName(2, "delete.png")
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        '
        'ButtonSave
        '
        Me.ButtonSave.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonSave.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonSave.Appearance.Options.UseFont = True
        Me.ButtonSave.ImageIndex = 1
        Me.ButtonSave.ImageList = Me.ImageList24x24
        Me.ButtonSave.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonSave.Location = New System.Drawing.Point(778, 666)
        Me.ButtonSave.LookAndFeel.SkinName = "Black"
        Me.ButtonSave.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonSave.Margin = New System.Windows.Forms.Padding(4, 16, 4, 4)
        Me.ButtonSave.Name = "ButtonSave"
        Me.ButtonSave.Size = New System.Drawing.Size(129, 37)
        Me.ButtonSave.TabIndex = 25
        Me.ButtonSave.Text = "Save"
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(914, 666)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Margin = New System.Windows.Forms.Padding(4, 16, 4, 4)
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(129, 37)
        Me.ButtonCancel.TabIndex = 26
        Me.ButtonCancel.Text = "Cancel"
        '
        'CheckEditRetailer
        '
        Me.CheckEditRetailer.Location = New System.Drawing.Point(155, 487)
        Me.CheckEditRetailer.Margin = New System.Windows.Forms.Padding(4, 9, 4, 4)
        Me.CheckEditRetailer.Name = "CheckEditRetailer"
        Me.CheckEditRetailer.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditRetailer.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditRetailer.Properties.Appearance.Options.UseFont = True
        Me.CheckEditRetailer.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditRetailer.Properties.AutoWidth = True
        Me.CheckEditRetailer.Properties.Caption = "This is a Retailer"
        Me.CheckEditRetailer.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditRetailer.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditRetailer.Size = New System.Drawing.Size(138, 21)
        Me.CheckEditRetailer.TabIndex = 25
        '
        'SubformClient
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(9.0!, 17.0!)
        Me.Controls.Add(Me.TableLayoutPanelDetails)
        Me.Controls.Add(Me.ButtonSave)
        Me.Controls.Add(Me.ButtonCancel)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(5)
        Me.Name = "SubformClient"
        Me.Padding = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        Me.Controls.SetChildIndex(Me.ButtonCancel, 0)
        Me.Controls.SetChildIndex(Me.ButtonSave, 0)
        Me.Controls.SetChildIndex(Me.TableLayoutPanelDetails, 0)
        Me.TableLayoutPanelDetails.ResumeLayout(False)
        Me.PanelDetails.ResumeLayout(False)
        Me.PanelDetails.PerformLayout()
        CType(Me.TextEditClientAbbreviation.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditApproved.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditAgency.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControlNotes, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlNotes.ResumeLayout(False)
        CType(Me.MemoEditNotes.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditTelephone.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditFax.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditAddressLine1.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditAddressLine2.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditPostalCode.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditDormant.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditClientName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditRetailer.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents TableLayoutPanelDetails As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents PanelDetails As System.Windows.Forms.Panel
    Friend WithEvents CheckEditDormant As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents TextEditClientName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelClientName As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HeadingDetails As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ImageList16x16 As System.Windows.Forms.ImageList
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents ButtonSave As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelClassification As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkClassification As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelCity As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkCity As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditTelephone As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelTelephone As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditFax As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEditAddressLine1 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelAddressLine1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditAddressLine2 As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelAddressLine2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditPostalCode As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelPostalCode As DevExpress.XtraEditors.LabelControl
    Friend WithEvents GroupControlNotes As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelPaymentTerms As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkTerms As DevExpress.XtraEditors.LabelControl
    Friend WithEvents MemoEditNotes As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents CheckEditAgency As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents LabelAccountManager As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkAccountManager As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelFax As DevExpress.XtraEditors.LabelControl
    Friend WithEvents CheckEditApproved As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents TextEditClientAbbreviation As TextEdit
    Friend WithEvents LabelControl1 As LabelControl
    Friend WithEvents CheckEditRetailer As CheckEdit
End Class
