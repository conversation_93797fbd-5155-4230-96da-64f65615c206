﻿using DataAccess;

namespace DataService.Security
{
    class UpdateThemeColorCommandExecutor : CommandExecutor<UpdateThemeColorCommand>
    {

        public override void Execute(UpdateThemeColorCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.UpdateUserThemeColor))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.AddInputParameter("color", command.ThemeColorARGB);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
            }
        }

    }
}
