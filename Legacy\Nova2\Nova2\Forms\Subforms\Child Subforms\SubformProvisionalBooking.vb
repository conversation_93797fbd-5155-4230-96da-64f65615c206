Public Class SubformProvisionalBooking

    Private _MediaGapManager As MediaGap
    Private SelectedCellColumns As New List(Of Integer)
    Private SelectedCellRows As New List(Of Integer)
    Private SelectedMediaFamilies As DataTable
    Private SelectedCategories As DataTable
    Private SelectedChains As DataTable
    Private _FirstWeek As Date
    Private _Brand As DataRow = Nothing
    Dim ServerTime As Nullable(Of Date)
    Dim ProvisionalBookingLifeSpan As Nullable(Of Integer)

#Region "Properties"

    Private Property MediaGapManager() As MediaGap
        Get
            Return _MediaGapManager
        End Get
        Set(ByVal value As MediaGap)
            _MediaGapManager = value
            ' Remember all the cells that are currently selected.
            For Each SelectedCell As DataGridViewCell In value.Grid.SelectedCells
                SelectedCellColumns.Add(SelectedCell.ColumnIndex)
                SelectedCellRows.Add(SelectedCell.RowIndex)
            Next
        End Set
    End Property

    Private Property FirstWeek() As Date
        Get
            ' Keep adding a day to the First Week date until it becomes a Monday.
            While Not _FirstWeek.DayOfWeek = DayOfWeek.Monday
                _FirstWeek = _FirstWeek.AddDays(1)
            End While
            Return _FirstWeek
        End Get
        Set(ByVal value As Date)
            ' Update the displayed date in the hyperlink.
            HyperlinkFirstWeek.Text = value.ToLongDateString
            ' Update the field's value.
            _FirstWeek = value
            ' Update the LastWeek value.
            LastWeek = DateAdd(DateInterval.WeekOfYear, CInt(TextEditWeeks.EditValue) - 1, value)
        End Set
    End Property

    Private Property LastWeek() As Date
        Get
            Return DateAdd(DateInterval.WeekOfYear, CInt(TextEditWeeks.EditValue) - 1, FirstWeek)
        End Get
        Set(ByVal value As Date)
            ' Update the text in the hyper link.
            HyperlinkLastWeek.Text = value.ToLongDateString
            ' Update the value in TextEditWeeks if necessary.
            Dim DateDifference As Integer = CInt(DateDiff(DateInterval.WeekOfYear, FirstWeek, value)) + 1
            Dim Weeks As Integer = CInt(TextEditWeeks.EditValue)
            If Not DateDifference = Weeks Then
                TextEditWeeks.EditValue = DateDifference
            End If
        End Set
    End Property

    Private Property Brand() As DataRow
        Get
            Return _Brand
        End Get
        Set(ByVal value As DataRow)
            If IsNothing(value) = False Then
                _Brand = value
                HyperlinkBrand.Text = value("BrandName")
            End If
        End Set
    End Property

#End Region

#Region "Public Methods"

    Public Sub New(ByVal MediaGapObject As MediaGap)

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        MediaGapManager = MediaGapObject

        ' Initialize the first week.
        Dim StartDate As Date = Today.Date
        While Not StartDate.DayOfWeek = DayOfWeek.Monday
            StartDate = StartDate.AddDays(1)
        End While
        FirstWeek = StartDate

        ' Configure grid managers.
        SetupGridDataSources(Nothing, Nothing, Nothing)

    End Sub

    Public Sub New _
    (ByVal MediaGapObject As MediaGap, _
    ByVal PreselectMediaFamilies As DataTable, _
    ByVal PreselectCategories As DataTable, _
    ByVal PreselectChains As List(Of DataRow), _
    ByVal PreselectFirstWeek As Date, _
    ByVal PreselectWeeks As Integer)

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        MediaGapManager = MediaGapObject
        SetupGridDataSources(PreselectMediaFamilies, PreselectCategories, PreselectChains)
        FirstWeek = PreselectFirstWeek
        TextEditWeeks.EditValue = PreselectWeeks

    End Sub

#End Region

#Region "Private Methods"

    Private Sub SetupGridDataSources _
    (ByVal PreselectMediaFamilies As DataTable, _
    ByVal PreselectCategories As DataTable, _
    ByVal PreselectChains As List(Of DataRow))

        ' Setup tables for the grid data.
        If IsNothing(PreselectMediaFamilies) Then
            CreateNewMediaFamiliesTable()
        Else
            SelectedMediaFamilies = PreselectMediaFamilies.Copy
        End If
        If IsNothing(PreselectCategories) Then
            CreateNewCategoriesTable()
        Else
            SelectedCategories = PreselectCategories.Copy
        End If
        CreateNewChainsTable(PreselectChains)

        ' Get a data set for each of the grid tables.
        Dim MediaFamilyDataSet As DataSet = SelectedMediaFamilies.DataSet
        Dim CategoryDataSet As DataSet = SelectedCategories.DataSet
        Dim ChainDataSet As DataSet = SelectedChains.DataSet

        ' If data sets don't exist for the grid tables, create them.
        If IsNothing(MediaFamilyDataSet) Then
            MediaFamilyDataSet = New DataSet
            MediaFamilyDataSet.Tables.Add(SelectedMediaFamilies)
        End If
        If IsNothing(CategoryDataSet) Then
            CategoryDataSet = New DataSet
            CategoryDataSet.Tables.Add(SelectedCategories)
        End If
        If IsNothing(ChainDataSet) Then
            ChainDataSet = New DataSet
            ChainDataSet.Tables.Add(SelectedChains)
        End If

        ' Use the tables and data sets to create binding sources for the grids.
        Dim MediaFamilyBindingSource As New BindingSource(MediaFamilyDataSet, SelectedMediaFamilies.TableName)
        Dim CategoryBindingSource As New BindingSource(CategoryDataSet, SelectedCategories.TableName)
        Dim ChainBindingSource As New BindingSource(ChainDataSet, SelectedChains.TableName)
        MediaFamilyBindingSource.Sort = "MediaFamilyName"
        CategoryBindingSource.Sort = "CategoryName"
        ChainBindingSource.Sort = "ChainName"

        ' Use the binding sources as the data sources for the grids.
        GridMediaFamilies.AutoGenerateColumns = False
        GridCategories.AutoGenerateColumns = False
        GridChains.AutoGenerateColumns = False
        GridMediaFamilies.DataSource = MediaFamilyBindingSource
        GridCategories.DataSource = CategoryBindingSource
        GridChains.DataSource = ChainBindingSource

        ' Create grid managers for each grid.
        Dim MediaFamilyGridManager As New GridManager _
        (GridMediaFamilies, _
        TextEditSearchMediaFamilies, _
        Nothing, _
        String.Empty, _
        PictureSearchMediaFamilies, _
        PictureClearSearchMediaFamilies, _
        Nothing, _
        ButtonRemoveMediaFamily)

        Dim CategoryGridManager As New GridManager _
        (GridCategories, _
        TextEditSearchCategories, _
        Nothing, _
        String.Empty, _
        PictureSearchCategories, _
        PictureClearSearchCategories, _
        Nothing, _
        ButtonRemoveCategory)

        Dim ChainGridManager As New GridManager _
        (GridChains, _
        TextEditSearchChains, _
        Nothing, _
        String.Empty, _
        PictureSearchChains, _
        PictureClearSearchChains, _
        Nothing, _
        ButtonRemoveChain)

    End Sub

    Private Sub CreateNewMediaFamiliesTable()
        ' Create a new table to populate the media familes grid.
        Dim Columns(1) As DataColumn
        Columns(0) = New DataColumn("MediaFamilyID", GetType(Integer))
        Columns(1) = New DataColumn("MediaFamilyName", GetType(String))
        SelectedMediaFamilies = New DataTable
        SelectedMediaFamilies.Columns.AddRange(Columns)
    End Sub

    Private Sub CreateNewCategoriesTable()
        ' Create a new table to populate the categories grid.
        Dim Columns(1) As DataColumn
        Columns(0) = New DataColumn("CategoryID", GetType(Integer))
        Columns(1) = New DataColumn("CategoryName", GetType(String))
        SelectedCategories = New DataTable
        SelectedCategories.Columns.AddRange(Columns)
    End Sub

    Private Sub CreateNewChainsTable(ByVal PreselectChains As List(Of DataRow))

        ' Create a new table to populate the chains grid.
        Dim Columns(1) As DataColumn
        Columns(0) = New DataColumn("ChainID", GetType(Integer))
        Columns(1) = New DataColumn("ChainName", GetType(String))
        SelectedChains = New DataTable
        SelectedChains.Columns.AddRange(Columns)

        ' Add rows to the table.
        If IsNothing(PreselectChains) = False Then
            For Each SelectedChain As DataRow In PreselectChains
                Dim NewRow As DataRow = SelectedChains.NewRow
                With NewRow
                    .Item("ChainID") = SelectedChain("ChainID")
                    .Item("ChainName") = SelectedChain("ChainName")
                End With
                SelectedChains.Rows.Add(NewRow)
            Next
        End If

    End Sub

    Protected Overrides Function Save() As Boolean

        ' Calculate the ExpiryTime.
        Dim ExpiryTime As Date = DateAdd(DateInterval.Day, ProvisionalBookingLifeSpan.Value, ServerTime.Value)

        ' Save the provisional bookings.
        Dim Result As Boolean = MediaGap.CreateProvisionalBookings _
        (My.Settings.DBConnection, _
        CStr(TextEditProvisionalBookingName.EditValue), _
        FirstWeek, _
        CInt(TextEditWeeks.EditValue), _
        SelectedMediaFamilies, _
        SelectedCategories, _
        SelectedChains, _
        Brand("BrandID"), _
        ServerTime, _
        ExpiryTime)

        ' Refresh media gap data.
        MediaGapManager.RefreshData(My.Settings.DBConnection)

        ' Reselect cells that user selected to create these provisional bookings, if any.
        For i As Integer = 0 To SelectedCellColumns.Count - 1
            MediaGapManager.Grid.Rows(SelectedCellRows(i)).Cells(SelectedCellColumns(i)).Selected = True
        Next

        Return Result

    End Function

    Private Function BrandIsBooked() As Boolean

        ' Check if the given brand has already booked anything in the desired space.
        Dim Errors As String = String.Empty
        Dim ReturnValue As Boolean = MediaGap.BrandIsAlreadyBooked _
        (Brand("BrandID"), _
        FirstWeek, _
        LastWeek, _
        SelectedMediaFamilies, _
        SelectedCategories, _
        SelectedChains, _
        My.Settings.DBConnection, _
        Errors)

        If String.IsNullOrEmpty(Errors) = False Then
            ' Cannot continue, errors resulted when checking for brand overlapping.
            CType(TopLevelControl, BaseForm).ShowMessage _
            ("Unfortunately I hurt my back while checking the database for existing bookings for this brand." & vbCrLf & vbCrLf _
            & Errors, "Spinal Surgey Is Complicated", MessageBoxIcon.Error)
            Return True
        Else
            Return ReturnValue
        End If

    End Function

#End Region

#Region "Event Handlers"

    Private Sub ButtonSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonSave.Click

        ' Get required settings.
        Dim ConsumingForm As LiquidShell.BaseForm = CType(TopLevelControl, LiquidShell.BaseForm)
        ServerTime = Settings.GetServerTime(My.Settings.DBConnection, ConsumingForm)
        ProvisionalBookingLifeSpan = MediaGap.CurrentProvisionalBookingLifeSpan(My.Settings.DBConnection, ConsumingForm)
        If ServerTime.HasValue = False Or ProvisionalBookingLifeSpan.HasValue = False Then
            Exit Sub
        End If

        ' Save
        Save(True)

    End Sub

    Private Sub ButtonCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonCancel.Click
        RevertToParentSubform()
    End Sub

    Private Sub TextEditWeeks_EditValueChanged(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles TextEditWeeks.EditValueChanged
        ' Update the text of HyperlinkLastWeek if necessary.
        Dim Weeks As Integer = CInt(TextEditWeeks.EditValue)
        HyperlinkLastWeek.Text = DateAdd(DateInterval.WeekOfYear, CDbl(Weeks - 1), FirstWeek).ToLongDateString
    End Sub

    Private Sub HyperlinkBrand_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles HyperlinkBrand.Click

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)
        LiquidAgent.ControlValidation(ValidatedControl, String.Empty)

        ' Get a selection from the user and update the related property.
        Dim Selection As List(Of DataRow) = LookupBrand.SelectRows(My.Settings.DBConnection, False, Nothing)
        If Selection.Count > 0 Then
            Brand = Selection(0)
            ' Add a description if the textbox is empty.
            If TextEditProvisionalBookingName.Text.Length = 0 Then
                TextEditProvisionalBookingName.EditValue = "Provisional booking for " & Selection(0).Item("BrandName")
            End If
        End If

    End Sub

    Private Sub HyperlinkFirstWeek_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles HyperlinkFirstWeek.Click
        Dim NewDate As Nullable(Of Date) = DateSelector.GetDate(True, FirstWeek)
        If NewDate.HasValue Then
            FirstWeek = NewDate.Value
        End If
    End Sub

    Private Sub HyperlinkLastWeek_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles HyperlinkLastWeek.Click
        Dim NewDate As Nullable(Of Date) = DateSelector.GetDate(True, LastWeek)
        If NewDate.HasValue Then
            LastWeek = NewDate.Value
        End If
    End Sub

    Private Sub TextEditProvisionalBookingName_EditValueChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles TextEditProvisionalBookingName.EditValueChanged

        ' Create an object to represent the control.
        Dim CurrentControl As TextEdit = CType(sender, TextEdit)

        ' Reset the error text of the control to an empty string.
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)

    End Sub

    Private Sub TextEditProvisionalBookingName_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles TextEditProvisionalBookingName.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If ValidatedControl.Text.Length = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "Your provisional booking needs a description.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub TextEditWeeks_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles TextEditWeeks.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If ValidatedControl.Text.Length = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "The number of weeks may not be blank.")
            Exit Sub
        ElseIf CInt(ValidatedControl.EditValue) < 1 Then
            LiquidAgent.ControlValidation(ValidatedControl, "The number of weeks must be greater than zero.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkBrand_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles HyperlinkBrand.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        If String.Compare(ValidatedControl.Text, "Select...") = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "A brand must be selected.")
            Exit Sub
        ElseIf BrandIsBooked() Then
            Dim ErrorText As String = "The selected brand has already been booked in at least one of the selected weeks. " _
            & "One brand may not be behind itself in the queue."
            LiquidAgent.ControlValidation(ValidatedControl, ErrorText)
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub GridCategories_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles GridCategories.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As DataGridView = CType(sender, DataGridView)

        ' Check for errors.
        If ValidatedControl.Rows.Count = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "At least one category must be selected.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub GridChains_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles GridChains.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As DataGridView = CType(sender, DataGridView)

        ' Check for errors.
        If ValidatedControl.Rows.Count = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "At least one chain must be selected.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub GridMediaFamilies_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles GridMediaFamilies.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As DataGridView = CType(sender, DataGridView)

        ' Check for errors.
        If ValidatedControl.Rows.Count = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "At least one media family must be selected.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub ButtonAddMediaFamily_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonAddMediaFamily.Click

        ' Get a selection of items from the user.
        Dim SelectedItems As List(Of DataRow) = LookupMediaFamily.SelectRows(My.Settings.DBConnection, True, GridMediaFamilies)
        ' Add the selected items to the grid.
        For Each SelectedItem As DataRow In SelectedItems
            Dim NewRow As DataRow = SelectedMediaFamilies.NewRow
            With NewRow
                .Item("MediaFamilyID") = SelectedItem("MediaFamilyID")
                .Item("MediaFamilyName") = SelectedItem("MediaFamilyName")
            End With
            SelectedMediaFamilies.Rows.Add(NewRow)
        Next

    End Sub

    Private Sub ButtonAddCategory_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonAddCategory.Click

        ' Get a selection of items from the user.
        Dim SelectedItems As List(Of DataRow) = LookupCategory.SelectRows(My.Settings.DBConnection, True, GridCategories)
        ' Add the selected items to the grid.
        For Each SelectedItem As DataRow In SelectedItems
            Dim NewRow As DataRow = SelectedCategories.NewRow
            With NewRow
                .Item("CategoryID") = SelectedItem("CategoryID")
                .Item("CategoryName") = SelectedItem("CategoryName")
            End With
            SelectedCategories.Rows.Add(NewRow)
        Next

    End Sub

    Private Sub ButtonAddChain_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonAddChain.Click

        ' Get a selection of items from the user.
        Dim SelectedItems As List(Of DataRow) = LookupChain.SelectRows(My.Settings.DBConnection, True, GridChains)
        ' Add the selected items to the grid.
        For Each SelectedItem As DataRow In SelectedItems
            Dim NewRow As DataRow = SelectedChains.NewRow
            With NewRow
                .Item("ChainID") = SelectedItem("ChainID")
                .Item("ChainName") = SelectedItem("ChainName")
            End With
            SelectedChains.Rows.Add(NewRow)
        Next

    End Sub

    Private Sub ButtonRemoveMediaFamily_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonRemoveMediaFamily.Click
        ' Delete all selected datarows.
        For Each SelectedItem As DataGridViewRow In GridMediaFamilies.SelectedRows
            CType(SelectedItem.DataBoundItem, DataRowView).Row.Delete()
        Next
    End Sub

    Private Sub ButtonRemoveCategory_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonRemoveCategory.Click
        ' Delete all selected datarows.
        For Each SelectedItem As DataGridViewRow In GridCategories.SelectedRows
            CType(SelectedItem.DataBoundItem, DataRowView).Row.Delete()
        Next
    End Sub

    Private Sub ButtonRemoveChain_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonRemoveChain.Click
        ' Delete all selected datarows.
        For Each SelectedItem As DataGridViewRow In GridChains.SelectedRows
            CType(SelectedItem.DataBoundItem, DataRowView).Row.Delete()
        Next
    End Sub

    Private Sub TextEditWeeks_KeyPress(ByVal sender As System.Object, ByVal e As System.Windows.Forms.KeyPressEventArgs) Handles TextEditWeeks.KeyPress
        ' Prevent user from entering a number with more than three digits.
        Dim KeyPressControl As TextEdit = CType(sender, TextEdit)
        If KeyPressControl.Text.Length = 3 AndAlso Char.IsDigit(e.KeyChar) Then
            e.Handled = True
        End If
    End Sub

#End Region

End Class
