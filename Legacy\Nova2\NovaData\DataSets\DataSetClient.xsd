﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DataSetClient" targetNamespace="http://tempuri.org/DataSetClient.xsd" xmlns:mstns="http://tempuri.org/DataSetClient.xsd" xmlns="http://tempuri.org/DataSetClient.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="DBConnection" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Public" Name="DBConnection (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.NovaData.My.MySettings.GlobalReference.Default.DBConnection" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ClientTableAdapter" GeneratorDataComponentClassName="ClientTableAdapter" Name="Client" UserDataComponentName="ClientTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Client.vClientsImPermittedToSee" DbObjectType="View" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Client].[Client] WHERE (([ClientID] = @Original_ClientID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ClientID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>INSERT INTO Client.Client
                  (ClassificationID, TermsID, ClientName, Telephone, Fax, AddressLine1, AddressLine2, CityID, PostalCode, Dormant, Notes, Agency, ClientAbbreviation, Retailer)
VALUES (@ClassificationID,@TermsID,@ClientName,@Telephone,@Fax,@AddressLine1,@AddressLine2,@CityID,@PostalCode,@Dormant,@Notes,@Agency,@ClientAbbreviation,@Retailer);  
SELECT ClientID, ClassificationID, TermsID, ClientName, Telephone, Fax, AddressLine1, AddressLine2, CityID, PostalCode, Dormant, Notes, Agency FROM Client.Client WHERE (ClientID = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ClassificationID" ColumnName="ClassificationID" DataSourceName="NovaDB.Client.Client" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ClassificationID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ClassificationID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="TermsID" ColumnName="TermsID" DataSourceName="NovaDB.Client.Client" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@TermsID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="TermsID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ClientName" ColumnName="ClientName" DataSourceName="NovaDB.Client.Client" DataTypeServer="nvarchar(150)" DbType="String" Direction="Input" ParameterName="@ClientName" Precision="0" ProviderType="NVarChar" Scale="0" Size="150" SourceColumn="ClientName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Telephone" ColumnName="Telephone" DataSourceName="NovaDB.Client.Client" DataTypeServer="nvarchar(10)" DbType="String" Direction="Input" ParameterName="@Telephone" Precision="0" ProviderType="NVarChar" Scale="0" Size="10" SourceColumn="Telephone" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Fax" ColumnName="Fax" DataSourceName="NovaDB.Client.Client" DataTypeServer="nvarchar(10)" DbType="String" Direction="Input" ParameterName="@Fax" Precision="0" ProviderType="NVarChar" Scale="0" Size="10" SourceColumn="Fax" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="AddressLine1" ColumnName="AddressLine1" DataSourceName="NovaDB.Client.Client" DataTypeServer="nvarchar(100)" DbType="String" Direction="Input" ParameterName="@AddressLine1" Precision="0" ProviderType="NVarChar" Scale="0" Size="100" SourceColumn="AddressLine1" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="AddressLine2" ColumnName="AddressLine2" DataSourceName="NovaDB.Client.Client" DataTypeServer="nvarchar(100)" DbType="String" Direction="Input" ParameterName="@AddressLine2" Precision="0" ProviderType="NVarChar" Scale="0" Size="100" SourceColumn="AddressLine2" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="CityID" ColumnName="CityID" DataSourceName="NovaDB.Client.Client" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@CityID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="CityID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="PostalCode" ColumnName="PostalCode" DataSourceName="NovaDB.Client.Client" DataTypeServer="nvarchar(4)" DbType="String" Direction="Input" ParameterName="@PostalCode" Precision="0" ProviderType="NVarChar" Scale="0" Size="4" SourceColumn="PostalCode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Dormant" ColumnName="Dormant" DataSourceName="NovaDB.Client.Client" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="Dormant" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Notes" ColumnName="Notes" DataSourceName="NovaDB.Client.Client" DataTypeServer="nvarchar(2000)" DbType="String" Direction="Input" ParameterName="@Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="2000" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Agency" ColumnName="Agency" DataSourceName="NovaDB.Client.Client" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@Agency" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="Agency" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ClientAbbreviation" ColumnName="ClientAbbreviation" DataSourceName="NovaDB.Client.Client" DataTypeServer="nvarchar(150)" DbType="String" Direction="Input" ParameterName="@ClientAbbreviation" Precision="0" ProviderType="NVarChar" Scale="0" Size="150" SourceColumn="ClientAbbreviation" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Retailer" ColumnName="Retailer" DataSourceName="NovaDB.Client.Client" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@Retailer" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="Retailer" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT ClientID, ClassificationID, TermsID, ClientName, Telephone, Fax, AddressLine1, AddressLine2, CityID, PostalCode, Dormant, Notes, Agency, ClassificationName, TermsName, CityName, BrandList, ApprovedByFinance, CreatedBy, 
                  CreationDate, AccountManagerName, LinkedContracts, ClientAbbreviation, Retailer
FROM     Client.vClientsImPermittedToSee
ORDER BY ClientName</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>UPDATE Client.Client
SET          ClassificationID = @ClassificationID, TermsID = @TermsID, ClientName = @ClientName, Telephone = @Telephone, Fax = @Fax, AddressLine1 = @AddressLine1, AddressLine2 = @AddressLine2, CityID = @CityID, 
                  PostalCode = @PostalCode, Dormant = @Dormant, Notes = @Notes, Agency = @Agency, ClientAbbreviation = @ClientAbbreviation, Retailer = @Retailer
WHERE  (ClientID = @Original_ClientID);  
SELECT ClientID, ClassificationID, TermsID, ClientName, Telephone, Fax, AddressLine1, AddressLine2, CityID, PostalCode, Dormant, Notes, Agency FROM Client.Client WHERE (ClientID = @ClientID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ClassificationID" ColumnName="ClassificationID" DataSourceName="NovaDB.Client.Client" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ClassificationID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ClassificationID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="TermsID" ColumnName="TermsID" DataSourceName="NovaDB.Client.Client" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@TermsID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="TermsID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ClientName" ColumnName="ClientName" DataSourceName="NovaDB.Client.Client" DataTypeServer="nvarchar(150)" DbType="String" Direction="Input" ParameterName="@ClientName" Precision="0" ProviderType="NVarChar" Scale="0" Size="150" SourceColumn="ClientName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Telephone" ColumnName="Telephone" DataSourceName="NovaDB.Client.Client" DataTypeServer="nvarchar(10)" DbType="String" Direction="Input" ParameterName="@Telephone" Precision="0" ProviderType="NVarChar" Scale="0" Size="10" SourceColumn="Telephone" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Fax" ColumnName="Fax" DataSourceName="NovaDB.Client.Client" DataTypeServer="nvarchar(10)" DbType="String" Direction="Input" ParameterName="@Fax" Precision="0" ProviderType="NVarChar" Scale="0" Size="10" SourceColumn="Fax" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="AddressLine1" ColumnName="AddressLine1" DataSourceName="NovaDB.Client.Client" DataTypeServer="nvarchar(100)" DbType="String" Direction="Input" ParameterName="@AddressLine1" Precision="0" ProviderType="NVarChar" Scale="0" Size="100" SourceColumn="AddressLine1" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="AddressLine2" ColumnName="AddressLine2" DataSourceName="NovaDB.Client.Client" DataTypeServer="nvarchar(100)" DbType="String" Direction="Input" ParameterName="@AddressLine2" Precision="0" ProviderType="NVarChar" Scale="0" Size="100" SourceColumn="AddressLine2" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="CityID" ColumnName="CityID" DataSourceName="NovaDB.Client.Client" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@CityID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="CityID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="PostalCode" ColumnName="PostalCode" DataSourceName="NovaDB.Client.Client" DataTypeServer="nvarchar(4)" DbType="String" Direction="Input" ParameterName="@PostalCode" Precision="0" ProviderType="NVarChar" Scale="0" Size="4" SourceColumn="PostalCode" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Dormant" ColumnName="Dormant" DataSourceName="NovaDB.Client.Client" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="Dormant" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Notes" ColumnName="Notes" DataSourceName="NovaDB.Client.Client" DataTypeServer="nvarchar(2000)" DbType="String" Direction="Input" ParameterName="@Notes" Precision="0" ProviderType="NVarChar" Scale="0" Size="2000" SourceColumn="Notes" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Agency" ColumnName="Agency" DataSourceName="NovaDB.Client.Client" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@Agency" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="Agency" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ClientAbbreviation" ColumnName="ClientAbbreviation" DataSourceName="NovaDB.Client.Client" DataTypeServer="nvarchar(150)" DbType="String" Direction="Input" ParameterName="@ClientAbbreviation" Precision="0" ProviderType="NVarChar" Scale="0" Size="150" SourceColumn="ClientAbbreviation" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Retailer" ColumnName="Retailer" DataSourceName="NovaDB.Client.Client" DataTypeServer="bit" DbType="Boolean" Direction="Input" ParameterName="@Retailer" Precision="0" ProviderType="Bit" Scale="0" Size="1" SourceColumn="Retailer" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="Original_ClientID" ColumnName="ClientID" DataSourceName="NovaDB.Client.Client" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@Original_ClientID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ClientID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="ClientID" ColumnName="ClientID" DataSourceName="NovaDB.Client.Client" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ClientID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ClientID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ClientID" DataSetColumn="ClientID" />
              <Mapping SourceColumn="ClassificationID" DataSetColumn="ClassificationID" />
              <Mapping SourceColumn="TermsID" DataSetColumn="TermsID" />
              <Mapping SourceColumn="ClientName" DataSetColumn="ClientName" />
              <Mapping SourceColumn="Telephone" DataSetColumn="Telephone" />
              <Mapping SourceColumn="Fax" DataSetColumn="Fax" />
              <Mapping SourceColumn="AddressLine1" DataSetColumn="AddressLine1" />
              <Mapping SourceColumn="AddressLine2" DataSetColumn="AddressLine2" />
              <Mapping SourceColumn="CityID" DataSetColumn="CityID" />
              <Mapping SourceColumn="PostalCode" DataSetColumn="PostalCode" />
              <Mapping SourceColumn="Dormant" DataSetColumn="Dormant" />
              <Mapping SourceColumn="Notes" DataSetColumn="Notes" />
              <Mapping SourceColumn="Agency" DataSetColumn="Agency" />
              <Mapping SourceColumn="ClassificationName" DataSetColumn="ClassificationName" />
              <Mapping SourceColumn="TermsName" DataSetColumn="TermsName" />
              <Mapping SourceColumn="CityName" DataSetColumn="CityName" />
              <Mapping SourceColumn="BrandList" DataSetColumn="BrandList" />
              <Mapping SourceColumn="ApprovedByFinance" DataSetColumn="ApprovedByFinance" />
              <Mapping SourceColumn="CreatedBy" DataSetColumn="CreatedBy" />
              <Mapping SourceColumn="CreationDate" DataSetColumn="CreationDate" />
              <Mapping SourceColumn="AccountManagerName" DataSetColumn="AccountManagerName" />
              <Mapping SourceColumn="LinkedContracts" DataSetColumn="LinkedContracts" />
              <Mapping SourceColumn="ClientAbbreviation" DataSetColumn="ClientAbbreviation" />
              <Mapping SourceColumn="Retailer" DataSetColumn="Retailer" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ClientAccountManagerTableAdapter" GeneratorDataComponentClassName="ClientAccountManagerTableAdapter" Name="ClientAccountManager" UserDataComponentName="ClientAccountManagerTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Client.ClientAccountManager" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Client].[ClientAccountManager] WHERE (([ClientID] = @Original_ClientID) AND ([EffectiveDate] = @Original_EffectiveDate))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ClientID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_EffectiveDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="EffectiveDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Client].[ClientAccountManager] ([ClientID], [EffectiveDate], [AccountManagerID]) VALUES (@ClientID, @EffectiveDate, @AccountManagerID);
SELECT ClientID, EffectiveDate, AccountManagerID FROM Client.ClientAccountManager WHERE (ClientID = @ClientID) AND (EffectiveDate = @EffectiveDate)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ClientID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@EffectiveDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="EffectiveDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT        ClientID, EffectiveDate, AccountManagerID
FROM            Client.ClientAccountManager</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Client].[ClientAccountManager] SET [ClientID] = @ClientID, [EffectiveDate] = @EffectiveDate, [AccountManagerID] = @AccountManagerID WHERE (([ClientID] = @Original_ClientID) AND ([EffectiveDate] = @Original_EffectiveDate));
SELECT ClientID, EffectiveDate, AccountManagerID FROM Client.ClientAccountManager WHERE (ClientID = @ClientID) AND (EffectiveDate = @EffectiveDate)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ClientID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@EffectiveDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="EffectiveDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ClientID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_EffectiveDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="EffectiveDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ClientID" DataSetColumn="ClientID" />
              <Mapping SourceColumn="EffectiveDate" DataSetColumn="EffectiveDate" />
              <Mapping SourceColumn="AccountManagerID" DataSetColumn="AccountManagerID" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DataSetClient" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="DataSetClient" msprop:Generator_UserDSName="DataSetClient">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="Client" msprop:Generator_TableClassName="ClientDataTable" msprop:Generator_TableVarName="tableClient" msprop:Generator_TablePropName="Client" msprop:Generator_RowDeletingName="ClientRowDeleting" msprop:Generator_RowChangingName="ClientRowChanging" msprop:Generator_RowEvHandlerName="ClientRowChangeEventHandler" msprop:Generator_RowDeletedName="ClientRowDeleted" msprop:Generator_UserTableName="Client" msprop:Generator_RowChangedName="ClientRowChanged" msprop:Generator_RowEvArgName="ClientRowChangeEvent" msprop:Generator_RowClassName="ClientRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ClientID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msdata:AutoIncrementSeed="-1" msdata:AutoIncrementStep="-1" msprop:Generator_ColumnVarNameInTable="columnClientID" msprop:Generator_ColumnPropNameInRow="ClientID" msprop:Generator_ColumnPropNameInTable="ClientIDColumn" msprop:Generator_UserColumnName="ClientID" type="xs:int" />
              <xs:element name="ClassificationID" msprop:Generator_ColumnVarNameInTable="columnClassificationID" msprop:Generator_ColumnPropNameInRow="ClassificationID" msprop:Generator_ColumnPropNameInTable="ClassificationIDColumn" msprop:Generator_UserColumnName="ClassificationID" type="xs:int" default="0" />
              <xs:element name="TermsID" msprop:Generator_ColumnVarNameInTable="columnTermsID" msprop:Generator_ColumnPropNameInRow="TermsID" msprop:Generator_ColumnPropNameInTable="TermsIDColumn" msprop:Generator_UserColumnName="TermsID" type="xs:int" default="0" />
              <xs:element name="ClientName" msprop:Generator_ColumnVarNameInTable="columnClientName" msprop:Generator_ColumnPropNameInRow="ClientName" msprop:Generator_ColumnPropNameInTable="ClientNameColumn" msprop:Generator_UserColumnName="ClientName" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Telephone" msprop:Generator_ColumnVarNameInTable="columnTelephone" msprop:Generator_ColumnPropNameInRow="Telephone" msprop:Generator_ColumnPropNameInTable="TelephoneColumn" msprop:Generator_UserColumnName="Telephone" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Fax" msprop:Generator_ColumnVarNameInTable="columnFax" msprop:Generator_ColumnPropNameInRow="Fax" msprop:Generator_ColumnPropNameInTable="FaxColumn" msprop:Generator_UserColumnName="Fax" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="10" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="AddressLine1" msprop:Generator_ColumnVarNameInTable="columnAddressLine1" msprop:Generator_ColumnPropNameInRow="AddressLine1" msprop:Generator_ColumnPropNameInTable="AddressLine1Column" msprop:Generator_UserColumnName="AddressLine1" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="AddressLine2" msprop:Generator_ColumnVarNameInTable="columnAddressLine2" msprop:Generator_ColumnPropNameInRow="AddressLine2" msprop:Generator_ColumnPropNameInTable="AddressLine2Column" msprop:Generator_UserColumnName="AddressLine2" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CityID" msprop:Generator_ColumnVarNameInTable="columnCityID" msprop:Generator_ColumnPropNameInRow="CityID" msprop:Generator_ColumnPropNameInTable="CityIDColumn" msprop:Generator_UserColumnName="CityID" type="xs:int" default="0" />
              <xs:element name="PostalCode" msprop:Generator_ColumnVarNameInTable="columnPostalCode" msprop:Generator_ColumnPropNameInRow="PostalCode" msprop:Generator_ColumnPropNameInTable="PostalCodeColumn" msprop:Generator_UserColumnName="PostalCode" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="4" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Dormant" msprop:Generator_ColumnVarNameInTable="columnDormant" msprop:Generator_ColumnPropNameInRow="Dormant" msprop:Generator_ColumnPropNameInTable="DormantColumn" msprop:Generator_UserColumnName="Dormant" type="xs:boolean" default="false" />
              <xs:element name="Notes" msprop:Generator_ColumnVarNameInTable="columnNotes" msprop:Generator_ColumnPropNameInRow="Notes" msprop:Generator_ColumnPropNameInTable="NotesColumn" msprop:Generator_UserColumnName="Notes" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2000" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Agency" msprop:Generator_ColumnVarNameInTable="columnAgency" msprop:Generator_ColumnPropNameInRow="Agency" msprop:Generator_ColumnPropNameInTable="AgencyColumn" msprop:Generator_UserColumnName="Agency" type="xs:boolean" default="false" />
              <xs:element name="ClassificationName" msprop:Generator_ColumnVarNameInTable="columnClassificationName" msprop:Generator_ColumnPropNameInRow="ClassificationName" msprop:Generator_ColumnPropNameInTable="ClassificationNameColumn" msprop:Generator_UserColumnName="ClassificationName" default="Select...">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="TermsName" msprop:Generator_ColumnVarNameInTable="columnTermsName" msprop:Generator_ColumnPropNameInRow="TermsName" msprop:Generator_ColumnPropNameInTable="TermsNameColumn" msprop:Generator_UserColumnName="TermsName" default="Select...">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CityName" msprop:Generator_ColumnVarNameInTable="columnCityName" msprop:Generator_ColumnPropNameInRow="CityName" msprop:Generator_ColumnPropNameInTable="CityNameColumn" msprop:Generator_UserColumnName="CityName" default="Select...">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="BrandList" msprop:Generator_ColumnVarNameInTable="columnBrandList" msprop:Generator_ColumnPropNameInRow="BrandList" msprop:Generator_ColumnPropNameInTable="BrandListColumn" msprop:Generator_UserColumnName="BrandList" default="" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2147483647" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ApprovedByFinance" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnApprovedByFinance" msprop:Generator_ColumnPropNameInRow="ApprovedByFinance" msprop:Generator_ColumnPropNameInTable="ApprovedByFinanceColumn" msprop:Generator_UserColumnName="ApprovedByFinance" type="xs:boolean" default="false" />
              <xs:element name="CreatedBy" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnCreatedBy" msprop:Generator_ColumnPropNameInRow="CreatedBy" msprop:Generator_ColumnPropNameInTable="CreatedByColumn" msprop:Generator_UserColumnName="CreatedBy" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="CreationDate" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnCreationDate" msprop:Generator_ColumnPropNameInRow="CreationDate" msprop:Generator_ColumnPropNameInTable="CreationDateColumn" msprop:Generator_UserColumnName="CreationDate" type="xs:dateTime" />
              <xs:element name="AccountManagerName" msprop:Generator_ColumnVarNameInTable="columnAccountManagerName" msprop:Generator_ColumnPropNameInRow="AccountManagerName" msprop:Generator_ColumnPropNameInTable="AccountManagerNameColumn" msprop:Generator_UserColumnName="AccountManagerName" default="Select..." minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="151" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="LinkedContracts" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnLinkedContracts" msprop:Generator_ColumnPropNameInRow="LinkedContracts" msprop:Generator_ColumnPropNameInTable="LinkedContractsColumn" msprop:Generator_UserColumnName="LinkedContracts" type="xs:boolean" default="false" minOccurs="0" />
              <xs:element name="ClientAbbreviation" msprop:Generator_ColumnVarNameInTable="columnClientAbbreviation" msprop:Generator_ColumnPropNameInRow="ClientAbbreviation" msprop:Generator_ColumnPropNameInTable="ClientAbbreviationColumn" msprop:Generator_UserColumnName="ClientAbbreviation" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Retailer" msprop:Generator_ColumnVarNameInTable="columnRetailer" msprop:Generator_ColumnPropNameInRow="Retailer" msprop:Generator_ColumnPropNameInTable="RetailerColumn" msprop:Generator_UserColumnName="Retailer" type="xs:boolean" default="false" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ClientAccountManager" msprop:Generator_TableClassName="ClientAccountManagerDataTable" msprop:Generator_TableVarName="tableClientAccountManager" msprop:Generator_TablePropName="ClientAccountManager" msprop:Generator_RowDeletingName="ClientAccountManagerRowDeleting" msprop:Generator_RowChangingName="ClientAccountManagerRowChanging" msprop:Generator_RowEvHandlerName="ClientAccountManagerRowChangeEventHandler" msprop:Generator_RowDeletedName="ClientAccountManagerRowDeleted" msprop:Generator_UserTableName="ClientAccountManager" msprop:Generator_RowChangedName="ClientAccountManagerRowChanged" msprop:Generator_RowEvArgName="ClientAccountManagerRowChangeEvent" msprop:Generator_RowClassName="ClientAccountManagerRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ClientID" msprop:Generator_ColumnVarNameInTable="columnClientID" msprop:Generator_ColumnPropNameInRow="ClientID" msprop:Generator_ColumnPropNameInTable="ClientIDColumn" msprop:Generator_UserColumnName="ClientID" type="xs:int" />
              <xs:element name="EffectiveDate" msprop:Generator_ColumnVarNameInTable="columnEffectiveDate" msprop:Generator_ColumnPropNameInRow="EffectiveDate" msprop:Generator_ColumnPropNameInTable="EffectiveDateColumn" msprop:Generator_UserColumnName="EffectiveDate" type="xs:dateTime" />
              <xs:element name="AccountManagerID" msprop:Generator_ColumnVarNameInTable="columnAccountManagerID" msprop:Generator_ColumnPropNameInRow="AccountManagerID" msprop:Generator_ColumnPropNameInTable="AccountManagerIDColumn" msprop:Generator_UserColumnName="AccountManagerID" type="xs:int" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Client" />
      <xs:field xpath="mstns:ClientID" />
    </xs:unique>
    <xs:unique name="ClientAccountManager_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ClientAccountManager" />
      <xs:field xpath="mstns:ClientID" />
      <xs:field xpath="mstns:EffectiveDate" />
    </xs:unique>
  </xs:element>
</xs:schema>