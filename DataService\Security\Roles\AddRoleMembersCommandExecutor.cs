﻿using DataAccess;

namespace DataService.Security
{
    class AddRoleMembersCommandExecutor : CommandExecutor<AddRoleMembersCommand>
    {

        public override void Execute(AddRoleMembersCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.AddRoleMembers))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.AddInputParameter("roleid", command.RoleId);
                storedprocedure.AddInputParameter("newmembers", command.NewMembers);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
            }
        }

    }
}
