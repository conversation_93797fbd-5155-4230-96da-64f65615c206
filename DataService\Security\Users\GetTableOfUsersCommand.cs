﻿using DataAccess;
using System;
using System.Data;

namespace DataService.Security
{
    internal class GetTableOfUsersCommand : Command
    {
        public Guid SessionId { get; set; }
        public bool GetDeletedItems { get; set; }
        public DataTable Table { get; set; }

        public GetTableOfUsersCommand(Guid sessionid, bool getdeleteditems)
        {
            SessionId = sessionid;
            GetDeletedItems = getdeleteditems;
        }
    }
}
