<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="ImageList24x24.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="ImageList24x24.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        ********************************************************************************
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAADo
        JgAAAk1TRnQBSQFMAgEBBQEAAVABBwFQAQcBGAEAARgBAAT/AREBAAj/AUIBTQE2BwABNgMAASgDAAFg
        AwABMAMAAQEBAAEQBgABJCIAAf8BfwG9AXcBnAFzAXsBbwF7AW8BnAFzAb0BdwH/AX+sAAH/AX8BewFv
        AXwBbwH/AXsB/wF7Af8BewH/AXsB/wF7Af8BewGcAW8BewFvAf8Bf6QAAf8BfwG9AXcBvQFzAf8BewG9
        AXMBlAFiAawBUQJJASkBSQGMAU0BcwFeAXsBcwH/AXsB3gF3AZwBcwH/AX+eAAH/AX8BewFvAf8BewGd
        AXcBjAFRAUEBOAEAATQBAAE0ASABOAEgATgBAAE0AQABNAEgATgBSQFNAVoBbwH/AnsBawH/AX+aAAH/
        AX8BewFvAf8BewEYAWsBIQE8AQABOAFCATwBYwFAAWMBQAFjAUABYwFAAWMBQAFjAUABYgE8AQABOAEA
        ATgBlAFiAf8BfwF7AW8B/wF/mAABvQF3Af8BewEYAWsBAAE8AUEBQAFjAUABYwFAAUEBQAEAATwBAAE8
        AQABPAEAATwBYgFAAWMBQAFjAUABQQFAAQABOAGUAWIB/wF/AXsBb5YAAf8BfwHfAXcBnAFzASABQAFB
        AUABgwFEAWIBRAEAATwBxQFIAVEBYgEYAW8BOQFvAdUBZgGkAUQBYgFEAWMBRAFjAUQBQgFAAQABPAEY
        AW8B/wF7Ad4Be5QAAXsBbwH/AX8BSQFRAQABQAFjAUgBYgFEAQABQAExAWIB/wF/Af8BfwH/AXsB/wF/
        AbUBZgFBAUQBYwFEAWMBSAFjAUgBYwFIASABRAGlAUgB/wJ7AW+SAAH/AX8B3gF3AXsBcwEAAUQBYgFI
        AWMBSAEAAUQBUQFiAf8BfwF8AW8BewFvAf8BfwFSAWYBAAFAAWIBSAGDAUgBgwFIAWMBSAGDAUgBYwFI
        AQABRAH3AW4B/wF7Af8Bf5AAAd4BewH/AX8BMQFiAQABRAGDAUwBIAFIAQcBVQH/AX8BewFvAXsBbwH/
        AX8BMQFiAQABRAFiAUwBgwFMAYMBTAFBAUgBQQFIAWIBTAGDAUwBAAFIAYsBWQH/AX8BvQF3kAABvQF3
        Af8BfwFJAVkBIAFIAYMBTAEAAUgB1QFuAf8CewFvAf8BfwFyAWYBAAFIAWIBTAGDAUwBgwFQASABTAGE
        AVAB1gFuAUEBTAFjAUwBQQFMAaQBUAH/AXsBnAFzkAABnAFzAf8BfwHFAVQBQQFMAWIBUAEgAUwBnAF3
        Af8BewH/AX8BMQFmAQABSAFiAVABgwFQAYMBUAEgAUwBYgFQAd4BewH/AX8BgwFQAWIBUAFiAVABYgFQ
        Ad8BewGcAXOQAAGcAXMB/wF/AaUBVAFBAVABYwFQAUEBUAHeAXsB/wF/AVEBagEAAUwBYgFQAYMBVAGD
        AVQBIAFQAUEBUAGbAXcB/wF/Af8BfwGDAVQBYgFQAWIBUAFiAVAB3gF7AZwBc5AAAZwBcwH/AX8B5wFc
        AUEBVAGDAVQBIAFQAb0BewG1AW4BAAFQAWIBVAGDAVQBgwFUASABVAFiAVQBnAF3Af8BfwHeAXsB3gF7
        AUEBVAFiAVQBYgFUAYMBVAH/AX8BnAFzkAAB3gF7Af8BfwGtAWUBAAFUAYMBWAFCAVQBBwFdAUEBVAFi
        AVgBgwFYAYMBWAEgAVQBQQFUAbwBewH/AX8BewFvAf8BfwG0AW4BAAFUAYMBWAEgAVQBKAFdAf8BfwG9
        AXeQAAH/AX8B/wF/AfcBcgEAAVQBgwFcAYMBWAFiAVgBYwFYAYMBXAGDAVwBIQFYAUEBWAGcAXsB/wF7
        AXwBcwGcAXMB/wF/AaQBXAFBAVgBgwFcAQABVAFSAW4B/wF/Af8Bf5IAAb0BcwH/AX8BgwFcAUEBXAGD
        AVwBgwFcAYMBXAGDAVwBQQFcAYMBXAG9AXsB/wF/Ad4BdwHeAXsB/wF/ASgBZQEgAVgBgwFcAWIBXAEg
        AVgB3gF/Ad4Be5QAAd4BewH/AX8BtQFyAQABWAGDAWABgwFgAYMBYAGDAWABYgFcAZwBewH/AX8B/wF/
        Af8BfwEYAXcBxQFgAQABXAGDAWABgwFgAQABXAEPAW4B/wF/Ab0Bd5YAAb0BcwH/AX8BjAFpAQABXAGD
        AWABgwFgAYMBYAFiAWABpAFkAQgBZQEIAWkBgwFgAQABXAFBAWABgwFgAYMBYAEAAWABBwFlAf8BfwG9
        AXeYAAH/AX8B3gF7Af8BfwGLAW0BAAFgAWIBZAGDAWQBgwFkAWIBZAFBAWQBQQFkAWIBZAGDAWQBgwFk
        AWIBZAEAAWABBwFpAf8BfwH/AX8B/wF/mgAB/wF/Af8BfwH/AX8BMQFyASABZAEAAWQBQQFkAWIBZAGD
        AWgBgwFoAYMBZAFBAWQBAAFkAQABZAHNAXEB/wF/Af8BfwHeAXueAAH/AX8BvQF3Af8BfwHeAX8BMQF2
        AeYBbAFBAWgBIAFoASABaAFBAWgBxQFsAe8BcQGcAX8B/wF/Ad4BewH/AX+kAAHeAXsB3gF7Af8BfwH/
        AX8B/wF/AbwBfwG9AX8B3gF/Af8BfwH/AX8B/wF/Ab0Bd6wAAf8BfwG9AXcBvQF3Ab0BdwG9AXcBvQF3
        Ab0BdwHeAXumAAH/AX8BnQFzAXsBbwGcAXMB/wF/EAAB/wF/AZwBcwF7AW8BnQFzAf8BfwoAAf8BfwGc
        AXMBewFvAXsBbwF7AW8BewFvAXsBbwF7AW8BewFvAXsBbwF7AW8BewFvAXsBbwF7AW8BewFvAXsBbwF7
        AW8BewFvAZwBcwH/AX8WAAH/AX8B/wF/Af8BfyQAAd4CewFvAXsBbwF7AW8BewFvAXsBbwF7AW8BewFv
        AXsBbwF7AW8BewFvAb0BdxAAAf8BfwF7AW8B/wF7Af8BewH/AXsBnAFzAf8BfwwAAf8BfwGcAXMB/wF7
        Af8BewH/AnsBbwH/AX8GAAH/AX8BewFvAf4BfwH+AX8B/gF/Af4BfwH+AX8B/gF/Af4BfwH+AX8B/gF/
        Af4BfwH+AX8B/gF/Af4BfwH+AX8B/gF/Af4BfwH+AX8B/gF/AXsBbwHeAXsUAAG9AXcBewFvAZwBbwHe
        AXsgAAHeAXsBnAFzAf4BfwH+AX8B/gF/Af4BfwH+AX8B/gF/Af4BfwH+AX8B/gF/Af4BfwG9AXsBvQF3
        DAAB/wF/AXsBbwH/AXsBGAFrAcYBQAHuAVUB/wF7AZwBcwH/AX8IAAH/AX8BnAFvAf8BewHuAVUBxgFA
        ARgBawH/AnsBbwH/AX8CAAH/AX8BewFvAf4BfwG5AUIB1QEZAfYBGQH2ARkB9gEZAfYBGQH2ARkB9gEZ
        AfYBGQH2ARkB1QEZAdUBGQHVARkB1QEZAdUBGQHVARkBlAEVAVcBNgHeAX8BnAF3Af8Bfw4AAf8BfwF7
        AW8BvwF7Ad8BfwHfAX8BnAFzAf8Bfx4AAXsBbwG9AXcBNwEqAdYBGQH2ARkB9gEZAfYBGQH2ARkB1QEZ
        AdUBGQHVARkB9QEhAZwBcwGcAXMB/wF/CAAB/wF/AXsBbwH/AXsB1gFmAQABNAEAATQBAAE0AWoBTQH/
        AXsBnQFzAf8BfwQAAf8BfwGdAXMB/wF7AWoBTQEAATQBAAE0AQABNAHWAWYB/wJ7AW8B/wF/Ab0BdwH+
        AX8BmAE6AdIBAAFTAQEBMwEBATMBAQEzAQEBMwEBATMBAQEzAQEBMwEBATMBAQETAQEBEgEBARIBAQES
        AQEB8gEAAfEBAAESAQEBjwEAAfUBIQH+AX8BnAFzDAAB/wF/AXsBbwHfAX8BeAFnAWgBOgGuAUoB3wJ7
        AW8B/wF/HAABvAF3ARsBWwHSAQABMwEBATMBAQEzAQEBMwEBATMBAQEzAQEBEgEBARIBAQGwAQABmAFC
        Af4BfwH/AX8IAAG9AXcB/wF7AfcBagEAATgBQQE8AWMBQAFjAUABAAE4AYwBUQH/AX8BnAFzAf8BfwH/
        AX8BnAFzAf8BfwGMAVEBAAE4AWMBQAFjAUABQQE8AQABOAH3AWoB/wF7Ab0BdwGcAXMB/gF/AXQBAQF0
        AQEB/wF/AZgBPgE0AQEBdAEBAXQBAQF0AQEBdAEBAXQBAQF0AQEBdAEBAXQBAQFTAQEBUwEBARIBAQEW
        ASYB/wF/AZQBEQHQAQABvQF3AXsBbwoAAf8BfwF7AW8B3wF/AVYBYwEBASYBAQEmAeABHQHwAU4B3wF/
        AZwBcxYAAf8BfwHeAXsBvQF3Ab0BewE7AVsBMwEBAVQBAQFTAQEBMwEBATMBAQEzAQEBMwEBATMBAQEz
        AQEB8gEAAbkBQgH+AX8BnAFzAb0BdwH/AX8EAAF8AW8B/wF7AWMBQAEgATwBgwFAAWMBQAFjAUABYgFA
        AQABOAGMAVUB/wF7AXwBbwF8AW8B/wF/AawBVQEAATgBYgFAAWMBQAFjAUABgwFAASABPAFjAUAB/wF7
        AZwBbwGcAXMB/gF/AZUBBQFUAQEBugE+AfYBEQF0AQEBlQEBAZUBAQGVAQEBlQEBAXUBAQF0AQEBdAEB
        AXQBAQF0AQEBVAEBAVMBAQGUAQkBuQFCAVMBAQESAQEBvQF3AXsBbwgAAf8BfwF7AW8B3wF/AXkBawEh
        ASoBQwEuAUQBMgFEAS4BIgEqAZwBcwG+AXcB/wF/EgABvQF3AZwBcwH+AX8B/gF/Af8BfwE7AVsBMwEB
        ATgBJgFYASoBWAEqAVgBKgFXASoBFwEiARcBHgEWASIBEgEBAbkBQgH/AX8B/gF/Af4BfwG9AXsBnAFz
        AgABnAFzAf8BewHmAUwBAAFAAYMBRAFjAUQBYwFEAWMBRAFiAUQBAAE8AWsBVQH/AX8B/wF/AWsBVQEA
        ATwBYgFEAWMBRAFjAUQBYwFEAYMBRAEAAUAB5gFMAf8BewGcAXMBnAFzAf4BfwG2AQkBdQEBAVQBAQF1
        AQEBlQEBAZUBAQGVAQEBlQEBAZUBAQGVAQEBlQEBAXUBAQF0AQEBdAEBAXQBAQF0AQEBUwEBARIBAQES
        AQEBMgEBAb0BdwF7AW8GAAH/AX8BewFvAd8BfwF4AWcBQgEuAUMBLgFkATIBZAEyAWQBMgFBASoBywFG
        Af8BfwF7AW8QAAH/AX8B3QF7Ab0BdwFYAS4BFwEeAZkBNgE7AVcBFwEaAd0BewFcAWMBXAFjAVwBYwFc
        AV8B/gF/Af8BfwH/AX8BFgEeAbkBQgG5AUYB1QEdARYBJgGcAW8B3gF/Ad4BewH/AX8B/wF7Ab0BdwFi
        AUQBIAFEAYMBSAFjAUgBYwFIAWMBSAFjAUgBAAFAAe8BXQHvAV0BAAFAAWIBSAFjAUgBYwFIAWMBSAGD
        AUgBIAFEAWIBRAG9AXcB/wF7Af8BfwGcAXMB/gF/AdYBCQF1AQEBtgEBAbYBAQG2AQEBtgEBAZYBAQGW
        AQEBlgEBAZUBAQGVAQEBlQEBAZUBAQF1AQEBdAEBAXQBAQF0AQEBUwEBATMBAQEzAQEB3gJ7AW8EAAH/
        AX8BewFvAd8BfwF5AWsBQQEuAWMBMgGEATYBhAE2AYQBNgGEATYBZAE2AUABKgF3AWcB3wF/Ad4Bew4A
        AZwBcwH+AX8BFwEeATQBAQFUAQEB1wEJATsBWwE4ASIBGwFXAXgBLgF5ATIBeQEyAVgBLgE7AV8BfAFr
        Ad4BewE3ASYB2gFGARcBIgHRAQAB0QEAAXMBBQH+AX8BewFvAgAB3gF7Af8BewGcAXcBgwFIASABRAGD
        AUgBgwFIAYMBSAGDAUgBYwFIASABRAEgAUQBYwFIAYMBSAGDAUgBgwFIAYMBSAEgAUQBgwFIAZwBdwH/
        AXsB3gF7AgABnAFzAf4BfwHXAQkBlgEBAbYBAQG2AQEBtgEBAbYBAQG2AQEBtgEBAbYBAQG2AQEBlgEB
        AZUBAQGVAQEBlQEBAXUBAQF0AQEBdAEBAXQBAQFTAQEBMwEBAd4CewFzAgAB/wF/AZwBcwHfAX8BmgFv
        AYMBMgGDATIBhAE2AYQBNgGEATYBhAE2AYQBNgGEATYBgwEyAaUBOgHfAXsBfAFzAf8BfwwAAZwBcwH+
        AX8BtgEFAZYBAQGWAQEBGAEWATwBWwE4AR4BGwFTAXkBKgF5AS4BeQEuAXkBLgFYASYBFwEeAdoBQgE3
        ASYB2gFGAVgBLgEzAQEBUwEBATIBAQHeAXcBewFzBAAB3gF7Af8BfwHeAXcBgwFMASABSAGDAUwBgwFM
        AYMBTAGDAUwBgwFMAYMBTAGDAUwBgwFMAYMBTAGDAUwBIAFIAYMBTAHeAXcB/wF/Ad4BewQAAZwBcwH+
        AX8B1wEJAZYBAQG3AQEBtwEBAbcBAQG3AQEBtwEBAbYBAQG2AQEBtgEBAbYBAQG2AQEBlQEBAZUBAQGV
        AQEBdQEBAXQBAQF0AQEBUwEBAVMBAQHeAXsBmwFzAgABnAFzAf8BfwGaAW8BggEyAYMBNgGkAToBpAE6
        AaQBOgGkAToBpAE6AaQBOgGkAToBpAE6AYABLgEvAVMB/wF/AZwBcwwAAZwBcwH+AX8B1wEJAZYBAQG2
        AQEB9wEJAZ0BawF5ASoBXAFbAboBOgG6AT4BugE+AboBPgG6AT4BmQE6ATsBWwFYASoBXAFfARcBIgEz
        AQEBUwEBAVMBAQHeAXsBmwFzBgAB3gF7Af8BfwG9AXcBYgFMASABSAGDAVABgwFQAYMBUAGDAVABgwFQ
        AYMBUAGDAVABgwFQASABSAFiAUwBvQF3Af8BfwHeAXsGAAGcAXMB/gF/AfcBCQG2AQEB1wEBAdcBAQHX
        AQEB1wEBAdcBAQHXAQEBtwEBAbYBAQG2AQEBtgEBAbYBAQGWAQEBlQEBAZUBAQF1AQEBdAEBAVQBAQFT
        AQEB3gF7AZsBcwHeAXsB3wF/AbsBcwGiATYBogE2AcQBOgHEAToBxAE6AcQBOgGiATYBogE2AcQBOgHE
        AToBxAE6AaQBOgGiATYBvAF3Ad8BfwH/AX8KAAGcAXMB/gF/AfcBCQG2AQEB1wEBAZYBAQHbAT4B3gF/
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B3gF7ARsBUwFUAQEBdAEBAVQBAQFTAQEB3gF7
        AZsBcwgAAd4BewH/AXsB3gF7AeYBVAFBAVABgwFQAYMBUAGDAVABgwFQAYMBUAGDAVABQQFQAeYBVAHe
        AXsB/wF7Ad4BewgAAZwBcwH/AX8B+AEJAbcBAQHXAQEB1wEBAdcBAQHXAQEB1wEBAdcBAQHXAQEB1wEB
        AbcBAQG2AQEBtgEBAbYBAQGWAQEBlQEBAZUBAQF1AQEBVAEBAVMBAQHeAXsBnAFzAZwBcwHfAX8B5gE+
        AcIBNgHEAT4BxAE+AcQBPgHEAT4BoAEyAVABVwFQAVsBoQE2AcQBPgHEAT4BxAE+AcIBNgHoAUYB/wF/
        AXwBbwH/AX8IAAGcAXMB/wF/AfgBCQG3AQEB1wEBAdcBAQG3AQEB1wEBAdcBAQHXAQEB1wEBAdcBAQG3
        AQEBtgEBAbYBAQG2AQEBtgEBAVQBAQF1AQEBdQEBAVQBAQFTAQEB3gF7AZwBcwgAAf8BfwG9AXMB/wF/
        Ac4BYQEgAVABgwFUAYMBVAGDAVQBgwFUAYMBVAGDAVQBIAFQAc4BYQH/AX8BvQFzAf8BfwgAAZwBcwH/
        AX8B+AEJAbcBAQH4AQEB2AEBAdgBAQHYAQEB1wEBAbcBAQG3AQEBtwEBAbcBAQGWAQEBlgEBAZYBAQF1
        AQEBdQEBAZUBAQGVAQEBdAEBAVQBAQHeAX8BnAFzAZwBcwH/AX8BBgFDAcEBNgHlAT4B5AE+AeQBPgHA
        ATYBLAFTAf8BfwH/AX8B5AE+AeMBOgHkAT4B5AE+AeQBPgHAATYBcQFfAf8BfwGcAXMIAAGcAXMB/wF/
        AfgBCQG3AQEB+AEBAfgBAQHYAQEB2AEBAdcBAQHXAQEB1wEBAbcBAQG3AQEBtgEBAbYBAQGWAQEBlgEB
        AZUBAQGVAQEBlQEBAXQBAQFUAQEB3gF/AZwBcwYAAf8BfwG9AXMB/wF/Aa0BYQEAAVABYwFUAYMBVAGD
        AVQBgwFUAYMBVAGDAVQBgwFUAWMBVAEAAVABrQFhAf8BfwG9AXMB/wF/BgABnAFzAf8BfwEYAQoB2AEB
        AfgBAQHYAQEBGQEKATkBEgE5AQ4BOQEOARgBDgEYAQ4BGAEOARgBDgH4AQ0B9wENAfcBEQHWAQkBVQEB
        AXUBAQF0AQEBVAEBAf4BfwGcAXMB/wF/Af8BfwHdAXcB5AE+AeIBOgEEAUMBwAE2ASsBUwH/AX8BvgF3
        Ad8BfwG4AW8B4AE2AeQBQgEEAUMBBAFDAeQBPgHhAToBugFzAf8BfwG9AXcGAAGcAXMB/wF/ARgBCgHY
        AQEB+AEBAfgBAQH4AQEB+AEBAfgBAQH4AQEB+AEBAdgBAQHXAQEB1wEBAbcBAQG2AQEBtgEBAbYBAQGV
        AQEBlQEBAXQBAQFUAQEB/gF/AZwBcwQAAf8BfwHeAXcB/wF/Ac0BZQEAAVABgwFYAYMBWAGDAVgBgwFY
        AYMBWAGDAVgBgwFYAYMBWAGDAVgBgwFYAQABUAHNAWUB/wF/Ab4BdwH/AX8EAAGcAXMB/wF/ARkBCgHY
        AQEB2AEBAdsBOgH9AX8B3QF/Ad0BfwHdAX8B3QF/Ad0BfwHdAX8B/QF/Af4BfwH/AX8B/wF/Af4BfwGZ
        ATYBVAEBAXQBAQF0AQEB/gF/AZwBcwIAAd4BewH/AX8B3QF3AeMBQgHAATYBKgFTAf8BfwHeAXsB/wF/
        Ab0BdwH/AX8BTQFXAeABOgEEAUMBBAFDAQQBQwHiAT4BBQFHAf8BfwHfAX8B/wF/BAABnAFzAf8BfwEZ
        AQoB2AEBAfkBAQH5AQEB+QEBAfkBAQH5AQEB+AEBAfgBAQH4AQEB2AEBAdcBAQHXAQEBtwEBAbYBAQG2
        AQEBlgEBAXQBAQFUAQEBdAEBAf4BfwGcAXMCAAH/AX8BvQFzAf8BfwHOAWkBAAFUAYMBWAGDAVwBgwFc
        AYMBXAGDAVwBYgFYAWIBWAGDAVwBgwFcAYMBXAGDAVwBgwFYAQABVAHOAWkB/wF/Ab0BcwH/AX8CAAGc
        AXMB/wF/ARkBCgHYAQEB+QEBAb0BdwHdAX8BvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcB/gF/AbkBRgGU
        AR0BNgEuAf4BfwGdAXMBVAEBAXQBAQF0AQEB/wF/AZwBcwQAAd4BewH/AX8B3QF7AZYBawH/AX8B3wF/
        Af8BfwIAAf8BfwHeAXsB/wF/AQMBQwECAUMBBAFHAQQBRwEEAUcB4AE+ASoBUwH/AX8BvQF3Af8BfwIA
        AZwBcwH/AX8BGQEGAfgBAQEZAQIBGQECARkBAgEZAQIBGQECARkBAgH4AQEB+AEBAfgBAQHYAQEB1wEB
        AdcBAQG2AQEBtgEBAXUBAQGZATIBtgENAVQBAQH/AX8BnAFzAf8BfwG9AXcB/wF/AawBaQEAAVgBgwFc
        AYMBXAGDAVwBgwFcAYMBXAEgAVwBxgFgAeYBYAEgAVwBgwFcAYMBXAGDAVwBgwFcAYMBXAEAAVgBrAFp
        Af8BfwG9AXcB/wF/AZwBcwH/AX8BOQEKAfkBAQH5AQEBvQF3Ad4BfwG9AXcBvQF3Ab0BdwG9AXcBvQF3
        Ab0BdwH/AX8B9gEZAdEBAAEyAQEB/gF/AZ0BcwF1AQEBdQEBAXQBAQH/AX8BnAFzBgAB3gF7Ab4BewH/
        AX8BvQF3Af8BfwYAAd4BewH/AX8BuAFvAeABPgEDAUcBAwFHAQMBRwEDAUcB4AE+AUwBVwH/AX8BvQF3
        Af8BfwG9AXcB/wF/AVoBFgG4AQEB+QEBARkBAgEZAQIBGQECAfkBAQHZAQEB2AEBAdgBAQHXAQEBtwEB
        AbcBAQGWAQEBtgEBAZYBAQF1AQEB2gE+AZUBBQF0AQEB/wF/AZwBcwGcAXMB/wF/AQ8BbgEAAVgBgwFg
        AYMBYAGDAWABgwFgAYMBYAEgAVwBgwFgAf8BfwH/AX8BYgFgASABXAGDAWABgwFgAYMBYAGDAWABgwFg
        AQABWAEPAW4B/wF/AZwBcwGcAXMB/wF/ATkBCgH5AQEB+QEBAb4BdwHeAX8B3gF7Ad4BewHeAXsB3gF7
        Ad4BewHeAXsB/wF/AVgBIgFUAQEBlQEBAf8BfwG9AXMBdQEBAXUBAQF0AQEB/wF/AZwBcxgAAb0BdwH/
        AX8BbgFfAeABPgEDAUcBAwFHAQMBRwEDAUcB4AE+AW8BXwH/AX8BnAFzAf8BfwH/AX8B3wF3AZsBIgFa
        ARIB+QEBAVsBDgE6AQYBmwEeAXoBGgF6ARoBWgEaAVoBGgFZARoBWQEaAVkBHgHXAQUB9wENAXUBAQGV
        AQEB9wEZAZ0BawH/AX8B3gF7AZwBcwH/AX8BQQFgAUEBYAGDAWABgwFgAYMBYAGDAWABIAFgAYMBYAG9
        AXsB/wF/Af8BfwG9AXsBgwFgASABYAGDAWABgwFgAYMBYAGDAWABQQFgAUEBYAH/AX8BnAFzAZwBcwH/
        AX8BOgEKAfkBAQEaAQIB3gF3Af4BfwHeAXsB3gF7Ad4BewHeAXsB3gF7Ad4BewH/AX8BegEiAZcBAQHX
        AQEB/wF/Ab4BcwF1AQEBdQEBAXQBAQH/AX8BnAFzGgABvQF3Af8BfwFIAVMBAAFDAQMBSwEDAUsBAwFL
        AQABQwEAAUcB/wF/AZwBcwIAAd4BewH/AX8B/wF/Af8BfwF7ARIBPQFDAdwBKgH/AX8B/wF/Af8BfwH/
        AX8B/wF/Af8BfwH/AX8B/wF/AboBNgHbAT4B9wERAf8BfwH/AX8B/wF/Ab0BdwIAAb0BdwH/AX8B7wFt
        AQABYAGDAWQBgwFkAYMBZAEgAWABgwFkAd4BfwH/AX8B3gF7Ad4BewH/AX8B3gF/AYMBZAEgAWABgwFk
        AYMBZAGDAWQBAAFgAQ8BbgH/AX8BvQF3AZwBcwH/AX8BOgEGAfkBAQEaAQIB3gF3Af8BfwHeAXsB3gF7
        Ad4BewHeAXsB3gF7Ad4BewH/AX8BmwEaAbgBAQG3AQEB/wF/Ab4BdwF1AQEBdQEBAXQBAQH/AX8BnAFz
        GgAB/wF/Af8BfwH/AX8BIgFLAQABRwEiAUsBAAFHAQABRwG2AW8B/wF/Ad4BewYAAd4BewH/AX8B/wF/
        AV0BUwFbAQYB/wF7Ad8BdwHfAXcB3wF3Ad8BdwHfAXcB3wF3Af8BewH3AQkB2wE+Af8BfwH/AX8BvQF3
        Af8BfwQAAf8BfwHeAXsB/wF/AYwBbQEAAWQBYgFkASABZAFiAWQB3gF/Af8BfwHeAXsEAAHeAXsB/wF/
        Ab0BfwFhAWQBIAFkAWIBZAEAAWQBjAFtAf8BfwHeAXsB/wF/Ab0BdwH/AX8BegEaAdkBAQEaAQIB3wF7
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8BHQFDAToBDgF6ASYB/wF/Ab4BcwF1AQEBNAEB
        AZUBAQH/AX8BnAFzHAAB3gF7Af8BfwH8AXsBIgFPAQABQwFIAVcB/QF7Af8BfwG9AXcKAAHeAXsB/wF/
        AZ4BYwHaAQEBGgECAfkBAQH5AQEB2AEBAdgBAQG3AQEBtwEBAbcBAQE1AQEB+wFGAf8BfwG9AXcKAAH/
        AX8B3gF7Af8BfwHOAXEBAAFkAYQBaAG9AX8B/wF/Ad4BewgAAd4BewH/AX8BvQF/AaQBaAEAAWQBzgFx
        Af8BfwHeAXsB/wF/AgAB/wF/Af8BfwHfAXcBOgEGAdkBAQHfAXcB/wF/Af8BfwH/AX8B/wF/Af8BfwH/
        AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8BvgFzARQBAQF0AQEBfQFnAf8BfwHeAXseAAHeAXsB/wF/
        Af8BfwHbAXcB/wF/Af8BfwHeAXsOAAG9AXcB3wFzAfoBAQH5AQEB2QEBAdgBAQHYAQEBuAEBAbcBAQGX
        AQEBlgEBAVUBAQF9AV8B/wF/Af8BfwwAAf8BfwHeAXsB/wF/Ab0BfwH/AX8B/wF/Ad4BewwAAd4BewH/
        AX8B/wF/Ab0BfwH/AX8B3gF7Af8BfwYAAd4BewH/AX8B/wF/Ab8BbwH/AX8B/wF/Af8BfwH/AX8B/wF/
        Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Ab4BbwH/AX8B/wF/Ab0BdyIAAd4BewHe
        AXsB/wF/Ab0BdxIAAb0BdwH/AX8B/wF7Ad8BcwHfAXMB3wFzAd8BcwHfAXMB3wFzAd8BcwHfAXMB3wF3
        Af8BfwG9AXcSAAG9AXcBvQF3Ab0BdwHeAXsQAAHeAXsBvQF3Ab0BdwG9AXcMAAH/AX8BvQF3Ab0BdwG9
        AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9
        AXcB3gF7JgAB/wF/Af8BfwH/AX8UAAHeAXsBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9
        AXcBvQF3Ab0BdwwAAUIBTQE+BwABPgMAASgDAAFgAwABMAMAAQEBAAEBBQABQAECFgAD/wEAAf8BAAH/
        CQAB/AEAAT8JAAHwAQABDwkAAeABAAEHCQABwAEAAQMJAAHAAQABAwkAAYABAAEBCQABgAEAAQFpAAGA
        AQABAQkAAYABAAEBCQABwAEAAQMJAAHAAQABAwkAAeABAAEHCQAB8AEAAQ8JAAH8AQABPwkAAf8BAAH/
        CQAB4AH/AQcBwAEAAQMB/wGPAf8B/AEAAT8BwAF+AQMBgAEAAQEB/wGHAf8B+AEAAR8BgAE8AQEDAAH+
        AQMB/wH4AQABDwEAARgEAAH8AQEB/wH4AQABDwYAAfgBAQH/AcABAAEDBgAB8AEAAf8BgAEAAQEGAAHg
        AQAB/wkAAcABAAF/AwABgAEAAQEDAAGAAQABPwMAAcABAAEDAwABgAEAAT8DAAHgAQABBwUAAR8DAAHw
        AQABDwUAAQ8DAAHwAQABDwUAAQ8DAAHgAQABBwUAAQcDAAHAAQABAwMAAYABAAEDAwABgAEAAQEDAAHA
        AUABAQkAAuAKAAH/AfAKAAH/AfgBAAGAAQABAQYAAf8B+AEAAeABAAEDAQABGAQAAf8B/AEBAfABAAEP
        AYABPAEBAwAB/wH+AQMB+AEAAQ8BwAF+AQMBgAEAAQEC/wEPAfgBAAEfAfAB/wEPAcABAAEDAv8BjwH8
        AQABPws=
</value>
  </data>
  <metadata name="ImageList16x16.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>153, 17</value>
  </metadata>
  <data name="ImageList16x16.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        ********************************************************************************
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAAAG
        DQAAAk1TRnQBSQFMAgEBBAEAATQBCAE0AQgBEAEAARABAAT/AREBAAj/AUIBTQE2BwABNgMAASgDAAFA
        AwABIAMAAQEBAAEQBgABEP8A/wD/AP8A/wD/AP8A/wAkAAE5AWcBGQFjAToBZwE6AWcBOQFnARgBYwoA
        AVoBawEYAWMBOQFnARgBYwEYAWMaAAEYAWMBWgFnARgBYwE5AWcIAAE5AWcBGAFjAVoBZwEYAWMOAAH/
        AX8B/wF/Af8BfwQAAf8BfwH/AX8B/wF/DgABWgFrAVsBbwE2AWMB0AFOAdABTgE1AV8BnAFzATkBZwgA
        ARgBYwF7AW8BtQFWARgBYwHeAXsBGAFjARgBYxQAAfcBXgH/AXsBEAFWAdYBZgGcAW8BOQFnBAABOQFn
        AZwBbwHWAWYBEAFWAf8BewH3AV4MAAHeAnsBbwGcAXMCAAH/AX8BvQF3AXsBbwG9AXcOAAE5AWcBvgF3
        ASMBKgHgASEBAAEiAQEBJgGcAXMBGAFjCAABGAFjAZwBcwHuAT0CAAEIASEBegFzAd4BdwE6AWMBGAFj
        DgABGAFfAf8BewFrAU0BAAEwAQABNAHWAWYBnAFvATkBZwE5AWcBnAFvAdYBZgEAATQBAAEwAWsBTQH/
        AXsBGAFfCAAB/wF/AZwBcwE1AV8BWAFnAZwBcwGdAXMBnAFzATQBXwGdAXMBvQF3DAABOQFnAb4BdwFE
        ATIBRAEyAUQBMgFDAS4BvAFzATkBZwgAAVoBawE5AWcBGAFjAecBHAGQAV4BVAF/ATUBewGXAXcBfQFr
        ATkBZwwAAXsBawHOAVkBAAE0AWMBQAFBAUABAAE8AfcBagF7AW8BewFvAfcBagEAATwBQQFAAWMBQAEA
        ATQBzgFZAXsBawgAAZwBcwETAVsB4AEhASMBKgHfAXsBvgF3AUQBLgHgASEBigE+AZ0BdwYAAVoBawEY
        AWMBGAFjAfcBXgHeAXsBZAE2AWMBMgFkATIBYwEyAbwBdwH3AV4BGAFjARgBYwFaAWsEAAE5AWcBvQFz
        AXcBewG6AX8BWAF/AQYBfwHgAX4BmAF3AToBZwwAAXsBawExAWIBAAE8AWIBRAFjAUQBQQFEAQABQAE5
        AW8BOQFvAQABQAFBAUQBYwFEAWIBRAEAATwBMQFiAXsBawgAAb0BdwFWAWMBQwEuAYgBOgHfAXsBvgF3
        AcwBRgFDAS4BEAFTAb0BdwQAATkBZwFbAWsB3wF7Ad8BewHfAX8B3wF/AaUBOgGDATYBhAE2AYMBNgG+
        AXsB3wF/Ad8BewHfAXsBfAFvARgBYwIAAVoBawHeAXcBvAF/AZkBfwFIAX8BAAF/AQABfwEAAX8BvQF3
        ARgBYwoAATkBZwH/AXsBEAFiAQABQAFiAUgBgwFIAUIBSAFiAUgBYgFIAUIBSAGDAUgBYgFIAQABQAEQ
        AWIB/wF7ATkBZwYAAf8BfwGcAXMB3wF/Ad8BewHfAXsBvgF3Ab4BdwHfAX8B3wF7Ad8BewG+AXsB/wF/
        AgABOQFnAXYBZwGlAToBxgE+AccBPgHHAUIBpAE6AaQBOgGkAToBpAE2AccBPgHHAT4BxgE+AaQBOgFU
        AV8BWgFrBAABnAFvAboBfwGPAX8BagF/ASQBfwEAAX8B4AF+ASMBewHfAXcB+AFeCgABOQFnAf8BfwEw
        AWIBAAFEAWIBTAGDAUwBYgFMAWIBTAGDAUwBYgFMAQABRAEwAWIB/wF/ATkBZwgAAb4BdwG9AXcBpQE6
        AaMBNgGjATYBowE2AaMBNgGjATYBowE2AYIBMgExAVcBfAFzAgABewFvAS0BUwGgATIBwwE6AcMBOgHD
        AToBxAE+AcQBPgHEAT4BxAE+AcMBOgHDAToBwwE6AaABMgEKAUsBewFvBAABWgFnAdwBewGPAX8BjwF/
        AWsBfwEjAXsBAAF/AeABfgFKAXsB3wF3ARgBYwoAATkBZwG9AXcBUgFmAUEBTAFjAVABgwFQAYMBUAFj
        AVABQQFMAVIBZgHeAXsBOQFnCgABvQF3AVIBWwHCATYB5QE+AQoBSwEKAUsBCgFLAQoBSwEKAUsBCQFH
        AbkBbwF7AW8B/wF/AXsBbwFNAVMBwQE2AeQBPgHkAT4B5AE+AeQBPgHkAT4B5AE+AeQBPgHkAT4B5AE+
        AeQBPgHCAToBKwFPAXsBbwYAAXwBbwHaAXsBrwF/AY8BfwFKAX8BIwF7AQABfwHgAX4BjwF/Ab0BcwE5
        AWcIAAE5AWcBewFvAVoBdwFBAVABYwFUAYMBVAGDAVQBYwFUAUEBUAFaAXcBewFvATkBZwoAAb0BdwEG
        AUMB4wE+AbkBbwErAU8BKgFPASoBTwEqAU8BKgFPASoBTwFMAVMB3wF/Ad4CewFvAXEBXwHAATYB4QE6
        AeEBOgHiAT4B4wFCAQQBQwEEAUMB4wFCAeIBPgHhAToB4QE6AcABNgFOAVcBewFvBgABWgFrAd4BdwHY
        AX8BjwF/AY8BfwFJAX8BIAF/AQABfwHtAWIBfQFvAXsBbwYAATkBZwGcAXMBOAFzASABVAFiAVQBgwFY
        AYMBWAGDAVgBgwFYAWIBVAEgAVQBOAFzAZwBcwE5AWcGAAH/AX8BuwF3AeABOgEGAUsBugFzAeABOgHg
        AToB4AE6AeABOgHgAToB4AE6AeIBPgH/AX8B3gF7ATkBZwH/AX8B2wF3AdsBdwHcAXcB2wF3AQQBQwED
        AUMBAwFDAQIBQwG6AXMB3AF3AdsBdwG7AXcB/wF/ATkBZwgAAToBZwH/AXsBtQF/AY8BfwGNAX8BaQF/
        AVYBZwGOAVEBYAFMAb0BewE5AWMBOQFnATkBZwGcAXMBOAF3ASABWAFBAVgBgwFcAWIBXAEgAVgBIAFY
        AWIBXAGDAVwBQQFYASABWAE4AXcBnAFzATkBZwH/AX8BvQF3AZwBcwG2AW8B4AE+AUwBVwG0AWsBbwFf
        AW4BWwFuAVsBbgFbAW4BWwFuAVsBbwFfAbcBbwG9AXcCAAFaAWsBWgFrAVoBawE5AWcB/wF/AQMBRwEC
        AUcBAwFHAQIBQwH/AX8BOQFnAVoBawFaAWsBWgFrDAABOgFnAf8BfwGyAX8BtAF7AVsBZwGqAV0BoAFY
        AUABTAFqAVkBnAFzAfcBXgE5AWcBGAF3AQABXAFBAVwBgwFgAWIBYAEAAVwBcwFyAXMBcgEAAVwBYgFg
        AYMBYAFBAVwBAAFcARgBdwE5AWcB3gF7Af0BewFKAVcBJgFPAQEBRwFtAVsBwAE6AQABQwEAAUMBAAFD
        AQABQwEAAUMBAAFDAQABQwHgAT4BvQF3CAABOQFnAf8BfwEDAUsBAgFHAQIBRwEBAUcB/gF7ARgBYxQA
        AVoBawH/AX8BFgFrAYQBZQEgAWUBIgFdAUABTAEPAWYBnAFzARgBYwGcAXMBiwFpAQABXAGDAWQBYwFg
        AQABYAExAXIB3gF7Ad4BewExAXIBAAFgAYMBYAGDAWQBAAFcAYsBbQF7AW8BvQF3AbQBawHgAUIBAAFH
        AQABRwHZAXMBswFrAZEBZwGSAWcBkgFnAZIBZwGSAWcBkgFnAZEBZwG3AXMBvgF3CAABOQFnAf8BfwEA
        AUcBAAFHAQABRwEAAUMB/gF7ATkBZxYAAZwBcwE1AXsBIAFtASABZQHAAVgBTwFqAf8BfwFaAWsCAAE5
        AWcB3gF/AcUBaAEAAWQBAAFkATABcgH/AX8BOQFnATkBZwH/AX8BMAFyAQABZAEAAWQBxQFoAd4BfwE5
        AWcB/wF/Af8BfwH9AXsB/QF7Af8BfwG9AXcB3gF7Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwHe
        AXsKAAFaAWsB/wF/AY4BYwFFAVMBRQFTAWsBXwH/AX8BOQFnFgABWgFrAd4BewHzAXYBhQFpARYBdwHe
        AXsBWgFrBgABWgFrAf8BfwEIAW0BMAF2Af8BfwE5AWcEAAE5AWcB/wF/ATEBdgEIAW0B/wF/AVoBawQA
        Af8BfwHeAXsB3gF7Ad4BewH/AX8eAAE5AWcBnAFzAb0BdwG9AXcBvQF3ATkBZxoAATkBZwGcAXMBvQF3
        AXsBbwFaAWsKAAFaAWsBvQF3AZwBcwE5AWcIAAE5AWcBnAFzAb0BdwFaAWskAAFCAU0BPgcAAT4DAAEo
        AwABQAMAASADAAEBAQABAQYAAQEWAAP/gQAB+AEfAQcB/wLDAfgBxwHwAQ8BAQH/AoEB+AGHAfABDwEA
        AX8CAAHwAQMB8AEPAQABPwIAAfABAwGAAQEBgAE/AgAB8AEDAgABgAEfAgAB4AEBAgABwAEPAYABAQHg
        AQECAAHAAQcBwAEDAeADAAHgAQMBwAEDAeADAAHgAQMBgAEBAcADAAHwBQABgAEBAfgFAAHwAQ8B/AUA
        AfABDwH+AQEDAAEBAfABDwH+AQMCgQGDAf8B+AEfAf8BBwLDAv8L
</value>
  </data>
  <metadata name="MediaNameColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="ChainNameColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="CategoryListColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="InstallCategories.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="InstallStoreQtyColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="BurstWeeksColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="FirstWeekColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="LastWeekColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="BrandNameColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="ProductNameColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="ProductionDescriptionColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="ProductionQuantityColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="ProductionBrandNameColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="ProductionAmountColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="ProductionNotesColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="MiscellaneousChargeNameColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="MiscellaneousChargeAmountColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn4.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="ColumnMediaName.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="MediaId.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="ColumnCostEstimateAmount.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DataGridViewTextBoxColumn1.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="MediaIdInvoices.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="ColumnMediaNameInvoices.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="ColumnInvoiceAmount.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="MeidaCostMediaNameColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="MediaCostSystemCalculatedColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="MediaCostUserInputedCostColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
</root>