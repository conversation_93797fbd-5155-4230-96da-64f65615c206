Public Class SubformProduction

    Private DataObject As Production
    Private ParentContract As OldContract

#Region "Event Handlers"

    Private Sub SubformBurst_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load
        PerformDataBinding()
    End Sub

    Private Sub ButtonOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonOK.Click
        ' Save and close.
        Save(True)
    End Sub

    Private Sub ButtonCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonCancel.Click
        DataObject.RejectChanges()
        RevertToParentSubform()
    End Sub

    Private Sub HyperlinkInventoryItem_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles HyperlinkInventoryItem.Click

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupInventory.SelectRows(My.Settings.DBConnection, False, Nothing)

        ' Update the data object and the label with the selection.
        If SelectedItems.Count > 0 Then
            DataObject.SelectedInventory = SelectedItems(0)
            CurrentControl.DataBindings("Text").ReadValue()
            LabelTitle.DataBindings("Text").ReadValue()
            ' Reset the error text of the control to an empty string.
            LiquidAgent.ControlValidation(CurrentControl, String.Empty)
            ' Reset the QTY hyperlink so that the user is forced to reselect a quantity specific
            ' to the new inventory selection.
            HyperlinkQty.Text = "Select..."
        End If

    End Sub

    Private Sub HyperlinkInventoryItem_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles HyperlinkInventoryItem.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        If String.Compare(ValidatedControl.Text, "Select...") = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "An inventory item must be selected.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkQty_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles HyperlinkQty.Click

        ' Stop if no inventory has been selected.
        If String.Compare(DataObject.ItemName, "Select...") = 0 Then
            CType(TopLevelControl, BaseForm).ShowMessage("Please select the desired inventory item first.", "Oh!  Wait!")
            Exit Sub
        End If

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupInventoryQty.SelectRowsByInventory _
        (My.Settings.DBConnection, False, ParentContract.ContractDate, DataObject.ItemID)

        ' Update the data object and the label with the selection.
        If SelectedItems.Count > 0 Then
            DataObject.SelectedQty = SelectedItems(0)
            CurrentControl.DataBindings("Text").ReadValue()
            TextEditSellPrice.DataBindings("EditValue").ReadValue()
            ' Reset the error text of the control to an empty string.
            LiquidAgent.ControlValidation(CurrentControl, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkBrand_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles HyperlinkBrand.Click

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupBrand.SelectRowsByClient _
        (My.Settings.DBConnection, False, Nothing, ParentContract.ClientID)

        ' Update the data object and the label with the selection.
        If SelectedItems.Count > 0 Then
            DataObject.SelectedBrand = SelectedItems(0)
            CurrentControl.DataBindings("Text").ReadValue()
            ' Reset the error text of the control to an empty string.
            LiquidAgent.ControlValidation(CurrentControl, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkQty_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles HyperlinkQty.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        If String.Compare(ValidatedControl.Text, "Select...") = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "A quantity must be selected.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkBrand_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles HyperlinkBrand.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        If String.Compare(ValidatedControl.Text, "Select...") = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "A brand must be selected.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub TextEditSellPrice_Validated(sender As Object, e As EventArgs) Handles TextEditSellPrice.Validated
        ' Warn the user if the sell price is less than the cost price.

        Dim CostPrice As Decimal = DataObject.CostPrice
        Dim SellPrice As Decimal = TextEditSellPrice.EditValue
        LabelSellPriceWarning.Visible = SellPrice < CostPrice

    End Sub

#End Region

#Region "Public Methods"

    Public Sub New(ByVal AddNew As Boolean, ByVal Parent As OldContract)

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        DataObject = New Production(Parent, AddNew, My.Settings.DBConnection)
        ParentContract = Parent

    End Sub

#End Region

#Region "Private Methods"

    Private Sub PerformDataBinding()

        LabelTitle.DataBindings.Add("Text", DataObject, "ProductionTitle")
        HyperlinkInventoryItem.DataBindings.Add("Text", DataObject, "ItemName", False, DataSourceUpdateMode.Never)
        HyperlinkQty.DataBindings.Add("Text", DataObject, "ItemQty", False, DataSourceUpdateMode.Never)
        HyperlinkBrand.DataBindings.Add("Text", DataObject, "BrandName", False, DataSourceUpdateMode.Never)
        TextEditSellPrice.DataBindings.Add("EditValue", DataObject, "SellPrice", False, DataSourceUpdateMode.OnPropertyChanged)
        MemoEditNotes.DataBindings.Add("EditValue", DataObject, "Notes", False, DataSourceUpdateMode.OnPropertyChanged)

    End Sub

    Protected Overrides Function Save() As Boolean
        DataObject.SaveToDataSet()
        Return True
    End Function

#End Region

End Class