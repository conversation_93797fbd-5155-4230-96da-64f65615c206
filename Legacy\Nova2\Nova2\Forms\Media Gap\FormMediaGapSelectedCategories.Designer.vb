<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class FormMediaGapSelectedCategories
    Inherits LiquidShell.EditorForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(FormMediaGapSelectedCategories))
        Dim SuperToolTip1 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem1 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem1 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip2 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem2 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem2 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim SuperToolTip3 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem3 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem3 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip4 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem4 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem4 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle4 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle5 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle6 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonOK = New DevExpress.XtraEditors.SimpleButton()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.LabelControl5 = New DevExpress.XtraEditors.LabelControl()
        Me.GroupControlAvailableItems = New DevExpress.XtraEditors.GroupControl()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearchAvailable = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearchAvailable = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearchAvailable = New DevExpress.XtraEditors.PictureEdit()
        Me.ButtonAdd = New DevExpress.XtraEditors.SimpleButton()
        Me.ImageList16x16 = New System.Windows.Forms.ImageList(Me.components)
        Me.GridAvailableItems = New System.Windows.Forms.DataGridView()
        Me.DataGridViewTextBoxColumn1 = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.GroupControlSelectedItems = New DevExpress.XtraEditors.GroupControl()
        Me.LabelControl7 = New DevExpress.XtraEditors.LabelControl()
        Me.ButtonRemove = New DevExpress.XtraEditors.SimpleButton()
        Me.TextEditSearchSelected = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearchSelected = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearchSelected = New DevExpress.XtraEditors.PictureEdit()
        Me.GridSelectedItems = New System.Windows.Forms.DataGridView()
        Me.CategoryNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelButtonBar.SuspendLayout()
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TableLayoutPanel1.SuspendLayout()
        CType(Me.GroupControlAvailableItems, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlAvailableItems.SuspendLayout()
        CType(Me.TextEditSearchAvailable.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearchAvailable.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearchAvailable.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridAvailableItems, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControlSelectedItems, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlSelectedItems.SuspendLayout()
        CType(Me.TextEditSearchSelected.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearchSelected.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearchSelected.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridSelectedItems, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'PanelButtonBar
        '
        Me.PanelButtonBar.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelButtonBar.Appearance.BackColor2 = System.Drawing.Color.LightSteelBlue
        Me.PanelButtonBar.Appearance.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical
        Me.PanelButtonBar.Appearance.Options.UseBackColor = True
        Me.PanelButtonBar.Controls.Add(Me.ButtonOK)
        Me.PanelButtonBar.Location = New System.Drawing.Point(0, 431)
        Me.PanelButtonBar.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Office2003
        Me.PanelButtonBar.LookAndFeel.UseDefaultLookAndFeel = False
        Me.PanelButtonBar.Size = New System.Drawing.Size(568, 52)
        Me.PanelButtonBar.TabIndex = 1
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        Me.ImageList24x24.Images.SetKeyName(3, "back.png")
        '
        'ButtonOK
        '
        Me.ButtonOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonOK.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonOK.Appearance.Options.UseFont = True
        Me.ButtonOK.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.ButtonOK.ImageIndex = 2
        Me.ButtonOK.ImageList = Me.ImageList24x24
        Me.ButtonOK.Location = New System.Drawing.Point(456, 12)
        Me.ButtonOK.LookAndFeel.SkinName = "Black"
        Me.ButtonOK.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonOK.Name = "ButtonOK"
        Me.ButtonOK.Size = New System.Drawing.Size(100, 28)
        Me.ButtonOK.TabIndex = 0
        Me.ButtonOK.Text = "OK"
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanel1.ColumnCount = 3
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 15.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.LabelControl5, 0, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.GroupControlAvailableItems, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.GroupControlSelectedItems, 2, 2)
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(12, 12)
        Me.TableLayoutPanel1.Margin = New System.Windows.Forms.Padding(3, 3, 3, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 3
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 15.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(544, 419)
        Me.TableLayoutPanel1.TabIndex = 0
        '
        'LabelControl5
        '
        Me.LabelControl5.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl5.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl5.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl5.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top
        Me.LabelControl5.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap
        Me.LabelControl5.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.TableLayoutPanel1.SetColumnSpan(Me.LabelControl5, 3)
        Me.LabelControl5.Location = New System.Drawing.Point(3, 3)
        Me.LabelControl5.Name = "LabelControl5"
        Me.LabelControl5.Size = New System.Drawing.Size(538, 39)
        Me.LabelControl5.TabIndex = 2
        Me.LabelControl5.Text = resources.GetString("LabelControl5.Text")
        '
        'GroupControlAvailableItems
        '
        Me.GroupControlAvailableItems.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlAvailableItems.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlAvailableItems.Appearance.Options.UseFont = True
        Me.GroupControlAvailableItems.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlAvailableItems.AppearanceCaption.Options.UseFont = True
        Me.GroupControlAvailableItems.Controls.Add(Me.LabelControl1)
        Me.GroupControlAvailableItems.Controls.Add(Me.TextEditSearchAvailable)
        Me.GroupControlAvailableItems.Controls.Add(Me.PictureClearSearchAvailable)
        Me.GroupControlAvailableItems.Controls.Add(Me.PictureAdvancedSearchAvailable)
        Me.GroupControlAvailableItems.Controls.Add(Me.ButtonAdd)
        Me.GroupControlAvailableItems.Controls.Add(Me.GridAvailableItems)
        Me.GroupControlAvailableItems.Location = New System.Drawing.Point(0, 60)
        Me.GroupControlAvailableItems.LookAndFeel.SkinName = "Black"
        Me.GroupControlAvailableItems.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlAvailableItems.Margin = New System.Windows.Forms.Padding(0)
        Me.GroupControlAvailableItems.Name = "GroupControlAvailableItems"
        Me.GroupControlAvailableItems.Size = New System.Drawing.Size(264, 359)
        Me.GroupControlAvailableItems.TabIndex = 0
        Me.GroupControlAvailableItems.Tag = ""
        Me.GroupControlAvailableItems.Text = "Available Categories"
        '
        'LabelControl1
        '
        Me.LabelControl1.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl1.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl1.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl1.Location = New System.Drawing.Point(106, 335)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(45, 13)
        Me.LabelControl1.TabIndex = 5
        Me.LabelControl1.Text = "Search:"
        '
        'TextEditSearchAvailable
        '
        Me.TextEditSearchAvailable.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchAvailable.EditValue = ""
        Me.TextEditSearchAvailable.Location = New System.Drawing.Point(157, 332)
        Me.TextEditSearchAvailable.Margin = New System.Windows.Forms.Padding(3, 4, 3, 5)
        Me.TextEditSearchAvailable.Name = "TextEditSearchAvailable"
        Me.TextEditSearchAvailable.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchAvailable.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchAvailable.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchAvailable.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchAvailable.Size = New System.Drawing.Size(80, 20)
        Me.TextEditSearchAvailable.TabIndex = 0
        '
        'PictureClearSearchAvailable
        '
        Me.PictureClearSearchAvailable.AllowDrop = True
        Me.PictureClearSearchAvailable.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchAvailable.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchAvailable.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchAvailable.Location = New System.Drawing.Point(243, 3)
        Me.PictureClearSearchAvailable.Name = "PictureClearSearchAvailable"
        Me.PictureClearSearchAvailable.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchAvailable.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchAvailable.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchAvailable.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem1.Text = "Clear Search"
        ToolTipItem1.LeftIndent = 6
        ToolTipItem1.Text = "Click here to clear all search boxes."
        SuperToolTip1.Items.Add(ToolTipTitleItem1)
        SuperToolTip1.Items.Add(ToolTipItem1)
        Me.PictureClearSearchAvailable.SuperTip = SuperToolTip1
        Me.PictureClearSearchAvailable.TabIndex = 2
        Me.PictureClearSearchAvailable.TabStop = True
        '
        'PictureAdvancedSearchAvailable
        '
        Me.PictureAdvancedSearchAvailable.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearchAvailable.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearchAvailable.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearchAvailable.Location = New System.Drawing.Point(243, 334)
        Me.PictureAdvancedSearchAvailable.Margin = New System.Windows.Forms.Padding(3, 6, 3, 7)
        Me.PictureAdvancedSearchAvailable.Name = "PictureAdvancedSearchAvailable"
        Me.PictureAdvancedSearchAvailable.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearchAvailable.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearchAvailable.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearchAvailable.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem2.Text = "Advanced Search"
        ToolTipItem2.LeftIndent = 6
        ToolTipItem2.Text = "Click here to search individual column values."
        SuperToolTip2.Items.Add(ToolTipTitleItem2)
        SuperToolTip2.Items.Add(ToolTipItem2)
        Me.PictureAdvancedSearchAvailable.SuperTip = SuperToolTip2
        Me.PictureAdvancedSearchAvailable.TabIndex = 1
        Me.PictureAdvancedSearchAvailable.TabStop = True
        '
        'ButtonAdd
        '
        Me.ButtonAdd.AccessibleRole = System.Windows.Forms.AccessibleRole.None
        Me.ButtonAdd.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAdd.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAdd.Appearance.Options.UseFont = True
        Me.ButtonAdd.ImageIndex = 0
        Me.ButtonAdd.ImageList = Me.ImageList16x16
        Me.ButtonAdd.Location = New System.Drawing.Point(5, 331)
        Me.ButtonAdd.LookAndFeel.SkinName = "Black"
        Me.ButtonAdd.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAdd.Name = "ButtonAdd"
        Me.ButtonAdd.Size = New System.Drawing.Size(75, 23)
        Me.ButtonAdd.TabIndex = 4
        Me.ButtonAdd.Text = "Add"
        '
        'ImageList16x16
        '
        Me.ImageList16x16.ImageStream = CType(resources.GetObject("ImageList16x16.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList16x16.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList16x16.Images.SetKeyName(0, "next.png")
        Me.ImageList16x16.Images.SetKeyName(1, "back.png")
        '
        'GridAvailableItems
        '
        Me.GridAvailableItems.AllowUserToAddRows = False
        Me.GridAvailableItems.AllowUserToDeleteRows = False
        Me.GridAvailableItems.AllowUserToOrderColumns = True
        Me.GridAvailableItems.AllowUserToResizeRows = False
        DataGridViewCellStyle1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridAvailableItems.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle1
        Me.GridAvailableItems.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridAvailableItems.BackgroundColor = System.Drawing.Color.White
        Me.GridAvailableItems.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridAvailableItems.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridAvailableItems.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle2.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle2.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridAvailableItems.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle2
        Me.GridAvailableItems.ColumnHeadersHeight = 22
        Me.GridAvailableItems.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridAvailableItems.ColumnHeadersVisible = False
        Me.GridAvailableItems.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.DataGridViewTextBoxColumn1})
        Me.GridAvailableItems.EnableHeadersVisualStyles = False
        Me.GridAvailableItems.GridColor = System.Drawing.Color.White
        Me.GridAvailableItems.Location = New System.Drawing.Point(2, 22)
        Me.GridAvailableItems.Name = "GridAvailableItems"
        Me.GridAvailableItems.ReadOnly = True
        Me.GridAvailableItems.RowHeadersVisible = False
        DataGridViewCellStyle3.ForeColor = System.Drawing.Color.DimGray
        Me.GridAvailableItems.RowsDefaultCellStyle = DataGridViewCellStyle3
        Me.GridAvailableItems.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridAvailableItems.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridAvailableItems.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridAvailableItems.RowTemplate.Height = 19
        Me.GridAvailableItems.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridAvailableItems.ShowCellToolTips = False
        Me.GridAvailableItems.Size = New System.Drawing.Size(260, 303)
        Me.GridAvailableItems.StandardTab = True
        Me.GridAvailableItems.TabIndex = 3
        '
        'DataGridViewTextBoxColumn1
        '
        Me.DataGridViewTextBoxColumn1.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.DataGridViewTextBoxColumn1.DataPropertyName = "CategoryName"
        Me.DataGridViewTextBoxColumn1.HeaderText = "Category"
        Me.DataGridViewTextBoxColumn1.Name = "DataGridViewTextBoxColumn1"
        Me.DataGridViewTextBoxColumn1.ReadOnly = True
        '
        'GroupControlSelectedItems
        '
        Me.GroupControlSelectedItems.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlSelectedItems.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlSelectedItems.Appearance.Options.UseFont = True
        Me.GroupControlSelectedItems.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlSelectedItems.AppearanceCaption.Options.UseFont = True
        Me.GroupControlSelectedItems.Controls.Add(Me.LabelControl7)
        Me.GroupControlSelectedItems.Controls.Add(Me.ButtonRemove)
        Me.GroupControlSelectedItems.Controls.Add(Me.TextEditSearchSelected)
        Me.GroupControlSelectedItems.Controls.Add(Me.PictureClearSearchSelected)
        Me.GroupControlSelectedItems.Controls.Add(Me.PictureAdvancedSearchSelected)
        Me.GroupControlSelectedItems.Controls.Add(Me.GridSelectedItems)
        Me.GroupControlSelectedItems.Location = New System.Drawing.Point(279, 60)
        Me.GroupControlSelectedItems.LookAndFeel.SkinName = "Black"
        Me.GroupControlSelectedItems.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlSelectedItems.Margin = New System.Windows.Forms.Padding(0)
        Me.GroupControlSelectedItems.Name = "GroupControlSelectedItems"
        Me.GroupControlSelectedItems.Size = New System.Drawing.Size(265, 359)
        Me.GroupControlSelectedItems.TabIndex = 1
        Me.GroupControlSelectedItems.Text = "Selected Categories"
        '
        'LabelControl7
        '
        Me.LabelControl7.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl7.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl7.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl7.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl7.Location = New System.Drawing.Point(107, 335)
        Me.LabelControl7.Name = "LabelControl7"
        Me.LabelControl7.Size = New System.Drawing.Size(45, 13)
        Me.LabelControl7.TabIndex = 3
        Me.LabelControl7.Text = "Search:"
        '
        'ButtonRemove
        '
        Me.ButtonRemove.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonRemove.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonRemove.Appearance.Options.UseFont = True
        Me.ButtonRemove.ImageIndex = 1
        Me.ButtonRemove.ImageList = Me.ImageList16x16
        Me.ButtonRemove.Location = New System.Drawing.Point(5, 331)
        Me.ButtonRemove.LookAndFeel.SkinName = "Black"
        Me.ButtonRemove.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonRemove.Name = "ButtonRemove"
        Me.ButtonRemove.Size = New System.Drawing.Size(75, 23)
        Me.ButtonRemove.TabIndex = 2
        Me.ButtonRemove.Text = "Remove"
        '
        'TextEditSearchSelected
        '
        Me.TextEditSearchSelected.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchSelected.EditValue = ""
        Me.TextEditSearchSelected.Location = New System.Drawing.Point(158, 332)
        Me.TextEditSearchSelected.Margin = New System.Windows.Forms.Padding(3, 4, 3, 5)
        Me.TextEditSearchSelected.Name = "TextEditSearchSelected"
        Me.TextEditSearchSelected.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchSelected.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchSelected.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchSelected.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchSelected.Size = New System.Drawing.Size(80, 20)
        Me.TextEditSearchSelected.TabIndex = 4
        '
        'PictureClearSearchSelected
        '
        Me.PictureClearSearchSelected.AllowDrop = True
        Me.PictureClearSearchSelected.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchSelected.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchSelected.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchSelected.Location = New System.Drawing.Point(244, 3)
        Me.PictureClearSearchSelected.Name = "PictureClearSearchSelected"
        Me.PictureClearSearchSelected.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchSelected.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchSelected.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchSelected.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem3.Text = "Clear Search"
        ToolTipItem3.LeftIndent = 6
        ToolTipItem3.Text = "Click here to clear all search boxes."
        SuperToolTip3.Items.Add(ToolTipTitleItem3)
        SuperToolTip3.Items.Add(ToolTipItem3)
        Me.PictureClearSearchSelected.SuperTip = SuperToolTip3
        Me.PictureClearSearchSelected.TabIndex = 0
        Me.PictureClearSearchSelected.TabStop = True
        '
        'PictureAdvancedSearchSelected
        '
        Me.PictureAdvancedSearchSelected.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearchSelected.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearchSelected.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearchSelected.Location = New System.Drawing.Point(244, 334)
        Me.PictureAdvancedSearchSelected.Margin = New System.Windows.Forms.Padding(3, 6, 3, 7)
        Me.PictureAdvancedSearchSelected.Name = "PictureAdvancedSearchSelected"
        Me.PictureAdvancedSearchSelected.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearchSelected.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearchSelected.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearchSelected.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem4.Text = "Advanced Search"
        ToolTipItem4.LeftIndent = 6
        ToolTipItem4.Text = "Click here to search individual column values."
        SuperToolTip4.Items.Add(ToolTipTitleItem4)
        SuperToolTip4.Items.Add(ToolTipItem4)
        Me.PictureAdvancedSearchSelected.SuperTip = SuperToolTip4
        Me.PictureAdvancedSearchSelected.TabIndex = 5
        Me.PictureAdvancedSearchSelected.TabStop = True
        '
        'GridSelectedItems
        '
        Me.GridSelectedItems.AllowUserToAddRows = False
        Me.GridSelectedItems.AllowUserToDeleteRows = False
        Me.GridSelectedItems.AllowUserToOrderColumns = True
        Me.GridSelectedItems.AllowUserToResizeRows = False
        DataGridViewCellStyle4.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridSelectedItems.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle4
        Me.GridSelectedItems.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridSelectedItems.BackgroundColor = System.Drawing.Color.White
        Me.GridSelectedItems.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridSelectedItems.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridSelectedItems.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle5.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle5.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle5.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle5.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle5.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle5.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle5.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridSelectedItems.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle5
        Me.GridSelectedItems.ColumnHeadersHeight = 22
        Me.GridSelectedItems.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridSelectedItems.ColumnHeadersVisible = False
        Me.GridSelectedItems.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.CategoryNameColumn})
        Me.GridSelectedItems.EnableHeadersVisualStyles = False
        Me.GridSelectedItems.GridColor = System.Drawing.Color.White
        Me.GridSelectedItems.Location = New System.Drawing.Point(2, 22)
        Me.GridSelectedItems.Name = "GridSelectedItems"
        Me.GridSelectedItems.ReadOnly = True
        Me.GridSelectedItems.RowHeadersVisible = False
        DataGridViewCellStyle6.ForeColor = System.Drawing.Color.DimGray
        Me.GridSelectedItems.RowsDefaultCellStyle = DataGridViewCellStyle6
        Me.GridSelectedItems.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridSelectedItems.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridSelectedItems.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridSelectedItems.RowTemplate.Height = 19
        Me.GridSelectedItems.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridSelectedItems.ShowCellToolTips = False
        Me.GridSelectedItems.Size = New System.Drawing.Size(261, 303)
        Me.GridSelectedItems.StandardTab = True
        Me.GridSelectedItems.TabIndex = 1
        '
        'CategoryNameColumn
        '
        Me.CategoryNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.CategoryNameColumn.DataPropertyName = "CategoryName"
        Me.CategoryNameColumn.HeaderText = "Category"
        Me.CategoryNameColumn.Name = "CategoryNameColumn"
        Me.CategoryNameColumn.ReadOnly = True
        '
        'FormMediaGapSelectedCategories
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.CancelButton = Me.ButtonOK
        Me.ClientSize = New System.Drawing.Size(568, 483)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.MinimumSize = New System.Drawing.Size(584, 300)
        Me.Name = "FormMediaGapSelectedCategories"
        Me.Text = "Categories To View"
        Me.Controls.SetChildIndex(Me.TableLayoutPanel1, 0)
        Me.Controls.SetChildIndex(Me.PanelButtonBar, 0)
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelButtonBar.ResumeLayout(False)
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TableLayoutPanel1.ResumeLayout(False)
        CType(Me.GroupControlAvailableItems, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlAvailableItems.ResumeLayout(False)
        CType(Me.TextEditSearchAvailable.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearchAvailable.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearchAvailable.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridAvailableItems, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControlSelectedItems, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlSelectedItems.ResumeLayout(False)
        CType(Me.TextEditSearchSelected.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearchSelected.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearchSelected.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridSelectedItems, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents ButtonOK As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents LabelControl5 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents GroupControlAvailableItems As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditSearchAvailable As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearchAvailable As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearchAvailable As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents ButtonAdd As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GridAvailableItems As System.Windows.Forms.DataGridView
    Friend WithEvents GroupControlSelectedItems As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelControl7 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ButtonRemove As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents TextEditSearchSelected As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearchSelected As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearchSelected As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents GridSelectedItems As System.Windows.Forms.DataGridView
    Friend WithEvents DataGridViewTextBoxColumn1 As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents CategoryNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents ImageList16x16 As System.Windows.Forms.ImageList

End Class
