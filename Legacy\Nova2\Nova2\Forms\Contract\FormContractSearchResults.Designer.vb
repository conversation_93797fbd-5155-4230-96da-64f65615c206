﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class FormContractSearchResults
    Inherits LiquidShell.EditorForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim SuperToolTip1 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem1 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem1 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip2 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem2 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem2 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(FormContractSearchResults))
        Me.GroupSearchResults = New DevExpress.XtraEditors.GroupControl()
        Me.LabelSearch = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearch = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearch = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearch = New DevExpress.XtraEditors.PictureEdit()
        Me.GridItems = New System.Windows.Forms.DataGridView()
        Me.ContractNumberColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.ProjectNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.ClientNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.BrandsColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.MediaServicesColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.FirstWeekColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.LastWeekColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.SignedColumn = New System.Windows.Forms.DataGridViewCheckBoxColumn()
        Me.CancelledColumn = New System.Windows.Forms.DataGridViewCheckBoxColumn()
        Me.LabelResults = New DevExpress.XtraEditors.LabelControl()
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonSelect = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton()
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelButtonBar.SuspendLayout()
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupSearchResults, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupSearchResults.SuspendLayout()
        CType(Me.TextEditSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearch.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridItems, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'PanelButtonBar
        '
        Me.PanelButtonBar.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelButtonBar.Appearance.BackColor2 = System.Drawing.Color.LightSteelBlue
        Me.PanelButtonBar.Appearance.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical
        Me.PanelButtonBar.Appearance.Options.UseBackColor = True
        Me.PanelButtonBar.Controls.Add(Me.ButtonSelect)
        Me.PanelButtonBar.Controls.Add(Me.ButtonCancel)
        Me.PanelButtonBar.Location = New System.Drawing.Point(0, 432)
        Me.PanelButtonBar.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Office2003
        Me.PanelButtonBar.LookAndFeel.UseDefaultLookAndFeel = False
        Me.PanelButtonBar.Size = New System.Drawing.Size(984, 52)
        Me.PanelButtonBar.TabIndex = 1
        '
        'GroupSearchResults
        '
        Me.GroupSearchResults.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupSearchResults.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupSearchResults.Appearance.Options.UseFont = True
        Me.GroupSearchResults.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupSearchResults.AppearanceCaption.ForeColor = System.Drawing.Color.DimGray
        Me.GroupSearchResults.AppearanceCaption.Options.UseFont = True
        Me.GroupSearchResults.AppearanceCaption.Options.UseForeColor = True
        Me.GroupSearchResults.Controls.Add(Me.LabelSearch)
        Me.GroupSearchResults.Controls.Add(Me.TextEditSearch)
        Me.GroupSearchResults.Controls.Add(Me.PictureClearSearch)
        Me.GroupSearchResults.Controls.Add(Me.PictureAdvancedSearch)
        Me.GroupSearchResults.Controls.Add(Me.GridItems)
        Me.GroupSearchResults.Location = New System.Drawing.Point(12, 43)
        Me.GroupSearchResults.LookAndFeel.SkinName = "Black"
        Me.GroupSearchResults.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupSearchResults.Margin = New System.Windows.Forms.Padding(3, 3, 3, 0)
        Me.GroupSearchResults.Name = "GroupSearchResults"
        Me.GroupSearchResults.Size = New System.Drawing.Size(960, 389)
        Me.GroupSearchResults.TabIndex = 0
        Me.GroupSearchResults.Text = "Search Results"
        '
        'LabelSearch
        '
        Me.LabelSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelSearch.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSearch.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelSearch.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelSearch.Location = New System.Drawing.Point(802, 365)
        Me.LabelSearch.Name = "LabelSearch"
        Me.LabelSearch.Size = New System.Drawing.Size(45, 13)
        Me.LabelSearch.TabIndex = 4
        Me.LabelSearch.Text = "Search:"
        '
        'TextEditSearch
        '
        Me.TextEditSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearch.EditValue = ""
        Me.TextEditSearch.Location = New System.Drawing.Point(853, 362)
        Me.TextEditSearch.Margin = New System.Windows.Forms.Padding(3, 4, 3, 5)
        Me.TextEditSearch.Name = "TextEditSearch"
        Me.TextEditSearch.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearch.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearch.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearch.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearch.Size = New System.Drawing.Size(80, 20)
        Me.TextEditSearch.TabIndex = 0
        '
        'PictureClearSearch
        '
        Me.PictureClearSearch.AllowDrop = True
        Me.PictureClearSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearch.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearch.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearch.Location = New System.Drawing.Point(939, 3)
        Me.PictureClearSearch.Name = "PictureClearSearch"
        Me.PictureClearSearch.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearch.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearch.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearch.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem1.Text = "Clear Search"
        ToolTipItem1.LeftIndent = 6
        ToolTipItem1.Text = "Click here to clear all search boxes."
        SuperToolTip1.Items.Add(ToolTipTitleItem1)
        SuperToolTip1.Items.Add(ToolTipItem1)
        Me.PictureClearSearch.SuperTip = SuperToolTip1
        Me.PictureClearSearch.TabIndex = 2
        Me.PictureClearSearch.TabStop = True
        '
        'PictureAdvancedSearch
        '
        Me.PictureAdvancedSearch.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearch.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearch.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearch.Location = New System.Drawing.Point(939, 364)
        Me.PictureAdvancedSearch.Margin = New System.Windows.Forms.Padding(3, 6, 3, 7)
        Me.PictureAdvancedSearch.Name = "PictureAdvancedSearch"
        Me.PictureAdvancedSearch.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearch.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearch.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearch.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem2.Text = "Advanced Search"
        ToolTipItem2.LeftIndent = 6
        ToolTipItem2.Text = "Click here to search individual column values."
        SuperToolTip2.Items.Add(ToolTipTitleItem2)
        SuperToolTip2.Items.Add(ToolTipItem2)
        Me.PictureAdvancedSearch.SuperTip = SuperToolTip2
        Me.PictureAdvancedSearch.TabIndex = 1
        Me.PictureAdvancedSearch.TabStop = True
        '
        'GridItems
        '
        Me.GridItems.AllowUserToAddRows = False
        Me.GridItems.AllowUserToDeleteRows = False
        Me.GridItems.AllowUserToOrderColumns = True
        Me.GridItems.AllowUserToResizeRows = False
        DataGridViewCellStyle1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridItems.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle1
        Me.GridItems.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridItems.BackgroundColor = System.Drawing.Color.White
        Me.GridItems.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridItems.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridItems.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle2.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle2.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridItems.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle2
        Me.GridItems.ColumnHeadersHeight = 22
        Me.GridItems.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridItems.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.ContractNumberColumn, Me.ProjectNameColumn, Me.ClientNameColumn, Me.BrandsColumn, Me.MediaServicesColumn, Me.FirstWeekColumn, Me.LastWeekColumn, Me.SignedColumn, Me.CancelledColumn})
        Me.GridItems.EnableHeadersVisualStyles = False
        Me.GridItems.GridColor = System.Drawing.Color.White
        Me.GridItems.Location = New System.Drawing.Point(2, 22)
        Me.GridItems.MultiSelect = False
        Me.GridItems.Name = "GridItems"
        Me.GridItems.ReadOnly = True
        Me.GridItems.RowHeadersVisible = False
        DataGridViewCellStyle3.ForeColor = System.Drawing.Color.DimGray
        Me.GridItems.RowsDefaultCellStyle = DataGridViewCellStyle3
        Me.GridItems.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridItems.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridItems.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridItems.RowTemplate.Height = 19
        Me.GridItems.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridItems.ShowCellToolTips = False
        Me.GridItems.Size = New System.Drawing.Size(956, 333)
        Me.GridItems.StandardTab = True
        Me.GridItems.TabIndex = 3
        '
        'ContractNumberColumn
        '
        Me.ContractNumberColumn.DataPropertyName = "ContractNumber"
        Me.ContractNumberColumn.HeaderText = "Contract"
        Me.ContractNumberColumn.Name = "ContractNumberColumn"
        Me.ContractNumberColumn.ReadOnly = True
        Me.ContractNumberColumn.Width = 80
        '
        'ProjectNameColumn
        '
        Me.ProjectNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.ProjectNameColumn.DataPropertyName = "ProjectName"
        Me.ProjectNameColumn.HeaderText = "Project"
        Me.ProjectNameColumn.Name = "ProjectNameColumn"
        Me.ProjectNameColumn.ReadOnly = True
        '
        'ClientNameColumn
        '
        Me.ClientNameColumn.DataPropertyName = "ClientName"
        Me.ClientNameColumn.HeaderText = "Client"
        Me.ClientNameColumn.Name = "ClientNameColumn"
        Me.ClientNameColumn.ReadOnly = True
        Me.ClientNameColumn.Width = 180
        '
        'BrandsColumn
        '
        Me.BrandsColumn.DataPropertyName = "Brands"
        Me.BrandsColumn.HeaderText = "Brands"
        Me.BrandsColumn.Name = "BrandsColumn"
        Me.BrandsColumn.ReadOnly = True
        Me.BrandsColumn.Width = 160
        '
        'MediaServicesColumn
        '
        Me.MediaServicesColumn.DataPropertyName = "MediaServices"
        Me.MediaServicesColumn.HeaderText = "Media Services"
        Me.MediaServicesColumn.Name = "MediaServicesColumn"
        Me.MediaServicesColumn.ReadOnly = True
        Me.MediaServicesColumn.Width = 170
        '
        'FirstWeekColumn
        '
        Me.FirstWeekColumn.DataPropertyName = "FirstWeek"
        Me.FirstWeekColumn.HeaderText = "FirstWeek"
        Me.FirstWeekColumn.Name = "FirstWeekColumn"
        Me.FirstWeekColumn.ReadOnly = True
        '
        'LastWeekColumn
        '
        Me.LastWeekColumn.DataPropertyName = "LastWeek"
        Me.LastWeekColumn.HeaderText = "LastWeek"
        Me.LastWeekColumn.Name = "LastWeekColumn"
        Me.LastWeekColumn.ReadOnly = True
        '
        'SignedColumn
        '
        Me.SignedColumn.DataPropertyName = "Signed"
        Me.SignedColumn.HeaderText = "Signed"
        Me.SignedColumn.Name = "SignedColumn"
        Me.SignedColumn.ReadOnly = True
        Me.SignedColumn.Visible = False
        Me.SignedColumn.Width = 80
        '
        'CancelledColumn
        '
        Me.CancelledColumn.DataPropertyName = "Cancelled"
        Me.CancelledColumn.HeaderText = "Cancelled"
        Me.CancelledColumn.Name = "CancelledColumn"
        Me.CancelledColumn.ReadOnly = True
        Me.CancelledColumn.Resizable = System.Windows.Forms.DataGridViewTriState.[True]
        Me.CancelledColumn.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Automatic
        Me.CancelledColumn.Visible = False
        Me.CancelledColumn.Width = 80
        '
        'LabelResults
        '
        Me.LabelResults.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelResults.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelResults.Location = New System.Drawing.Point(12, 12)
        Me.LabelResults.Margin = New System.Windows.Forms.Padding(3, 3, 3, 15)
        Me.LabelResults.Name = "LabelResults"
        Me.LabelResults.Size = New System.Drawing.Size(544, 13)
        Me.LabelResults.TabIndex = 2
        Me.LabelResults.Text = "Multiple contracts were found. Please select the contract you're looking for fro" & _
    "m the list below."
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        '
        'ButtonSelect
        '
        Me.ButtonSelect.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonSelect.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonSelect.Appearance.Options.UseFont = True
        Me.ButtonSelect.ImageIndex = 2
        Me.ButtonSelect.ImageList = Me.ImageList24x24
        Me.ButtonSelect.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonSelect.Location = New System.Drawing.Point(766, 12)
        Me.ButtonSelect.LookAndFeel.SkinName = "Black"
        Me.ButtonSelect.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonSelect.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonSelect.Name = "ButtonSelect"
        Me.ButtonSelect.Size = New System.Drawing.Size(100, 28)
        Me.ButtonSelect.TabIndex = 0
        Me.ButtonSelect.Text = "Select"
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(872, 12)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(100, 28)
        Me.ButtonCancel.TabIndex = 1
        Me.ButtonCancel.Text = "Cancel"
        '
        'FormContractSearchResults
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.CancelButton = Me.ButtonCancel
        Me.ClientSize = New System.Drawing.Size(984, 484)
        Me.Controls.Add(Me.LabelResults)
        Me.Controls.Add(Me.GroupSearchResults)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Name = "FormContractSearchResults"
        Me.Text = "Contract Search Results"
        Me.Controls.SetChildIndex(Me.PanelButtonBar, 0)
        Me.Controls.SetChildIndex(Me.GroupSearchResults, 0)
        Me.Controls.SetChildIndex(Me.LabelResults, 0)
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelButtonBar.ResumeLayout(False)
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupSearchResults, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupSearchResults.ResumeLayout(False)
        CType(Me.TextEditSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearch.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridItems, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents GroupSearchResults As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelSearch As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditSearch As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearch As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearch As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents GridItems As System.Windows.Forms.DataGridView
    Friend WithEvents LabelResults As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents ButtonSelect As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ContractNumberColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents ProjectNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents ClientNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents BrandsColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents MediaServicesColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents FirstWeekColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents LastWeekColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents SignedColumn As System.Windows.Forms.DataGridViewCheckBoxColumn
    Friend WithEvents CancelledColumn As System.Windows.Forms.DataGridViewCheckBoxColumn

End Class
