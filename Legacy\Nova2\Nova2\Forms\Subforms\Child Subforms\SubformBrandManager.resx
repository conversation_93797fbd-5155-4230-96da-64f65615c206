<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="ImageList16x16.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="ImageList16x16.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj00LjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAAAE
        CgAAAk1TRnQBSQFMAgEBAwEAASQBAQEkAQEBEAEAARABAAT/AREBAAj/AUIBTQE2BwABNgMAASgDAAFA
        AwABEAMAAQEBAAEQBgABCBwAATkBZwEZAWMBOgFnAToBZwE5AWcBGAFjCgABWgFrARgBYwE5AWcBGAFj
        ARgBYxoAARgBYwFaAWcBGAFjATkBZwgAATkBZwEYAWMBWgFnARgBYywAAVoBawFbAW8BNgFjAdABTgHQ
        AU4BNQFfAZwBcwE5AWcIAAEYAWMBewFvAbUBVgEYAWMB3gF7ARgBYwEYAWMUAAH3AV4B/wF7ARABVgHW
        AWYBnAFvATkBZwQAATkBZwGcAW8B1gFmARABVgH/AXsB9wFeKgABOQFnAb4BdwEjASoB4AEhAQABIgEB
        ASYBnAFzARgBYwgAARgBYwGcAXMB7gE9AgABCAEhAXoBcwHeAXcBOgFjARgBYw4AARgBXwH/AXsBawFN
        AQABMAEAATQB1gFmAZwBbwE5AWcBOQFnAZwBbwHWAWYBAAE0AQABMAFrAU0B/wF7ARgBXygAATkBZwG+
        AXcBRAEyAUQBMgFEATIBQwEuAbwBcwE5AWcIAAFaAWsBOQFnARgBYwHnARwBkAFeAVQBfwE1AXsBlwF3
        AX0BawE5AWcMAAF7AWsBzgFZAQABNAFjAUABQQFAAQABPAH3AWoBewFvAXsBbwH3AWoBAAE8AUEBQAFj
        AUABAAE0Ac4BWQF7AWsiAAFaAWsBGAFjARgBYwH3AV4B3gF7AWQBNgFjATIBZAEyAWMBMgG8AXcB9wFe
        ARgBYwEYAWMBWgFrBAABOQFnAb0BcwF3AXsBugF/AVgBfwEGAX8B4AF+AZgBdwE6AWcMAAF7AWsBMQFi
        AQABPAFiAUQBYwFEAUEBRAEAAUABOQFvATkBbwEAAUABQQFEAWMBRAFiAUQBAAE8ATEBYgF7AWsgAAE5
        AWcBWwFrAd8BewHfAXsB3wF/Ad8BfwGlAToBgwE2AYQBNgGDATYBvgF7Ad8BfwHfAXsB3wF7AXwBbwEY
        AWMCAAFaAWsB3gF3AbwBfwGZAX8BSAF/AQABfwEAAX8BAAF/Ab0BdwEYAWMKAAE5AWcB/wF7ARABYgEA
        AUABYgFIAYMBSAFCAUgBYgFIAWIBSAFCAUgBgwFIAWIBSAEAAUABEAFiAf8BewE5AWcgAAE5AWcBdgFn
        AaUBOgHGAT4BxwE+AccBQgGkAToBpAE6AaQBOgGkATYBxwE+AccBPgHGAT4BpAE6AVQBXwFaAWsEAAGc
        AW8BugF/AY8BfwFqAX8BJAF/AQABfwHgAX4BIwF7Ad8BdwH4AV4KAAE5AWcB/wF/ATABYgEAAUQBYgFM
        AYMBTAFiAUwBYgFMAYMBTAFiAUwBAAFEATABYgH/AX8BOQFnIgABewFvAS0BUwGgATIBwwE6AcMBOgHD
        AToBxAE+AcQBPgHEAT4BxAE+AcMBOgHDAToBwwE6AaABMgEKAUsBewFvBAABWgFnAdwBewGPAX8BjwF/
        AWsBfwEjAXsBAAF/AeABfgFKAXsB3wF3ARgBYwoAATkBZwG9AXcBUgFmAUEBTAFjAVABgwFQAYMBUAFj
        AVABQQFMAVIBZgHeAXsBOQFnJAABewFvAU0BUwHBATYB5AE+AeQBPgHkAT4B5AE+AeQBPgHkAT4B5AE+
        AeQBPgHkAT4B5AE+AcIBOgErAU8BewFvBgABfAFvAdoBewGvAX8BjwF/AUoBfwEjAXsBAAF/AeABfgGP
        AX8BvQFzATkBZwgAATkBZwF7AW8BWgF3AUEBUAFjAVQBgwFUAYMBVAFjAVQBQQFQAVoBdwF7AW8BOQFn
        JAABewFvAXEBXwHAATYB4QE6AeEBOgHiAT4B4wFCAQQBQwEEAUMB4wFCAeIBPgHhAToB4QE6AcABNgFO
        AVcBewFvBgABWgFrAd4BdwHYAX8BjwF/AY8BfwFJAX8BIAF/AQABfwHtAWIBfQFvAXsBbwYAATkBZwGc
        AXMBOAFzASABVAFiAVQBgwFYAYMBWAGDAVgBgwFYAWIBVAEgAVQBOAFzAZwBcwE5AWciAAE5AWcB/wF/
        AdsBdwHbAXcB3AF3AdsBdwEEAUMBAwFDAQMBQwECAUMBugFzAdwBdwHbAXcBuwF3Af8BfwE5AWcIAAE6
        AWcB/wF7AbUBfwGPAX8BjQF/AWkBfwFWAWcBjgFRAWABTAG9AXsBOQFjATkBZwE5AWcBnAFzATgBdwEg
        AVgBQQFYAYMBXAFiAVwBIAFYASABWAFiAVwBgwFcAUEBWAEgAVgBOAF3AZwBcwE5AWciAAFaAWsBWgFr
        AVoBawE5AWcB/wF/AQMBRwECAUcBAwFHAQIBQwH/AX8BOQFnAVoBawFaAWsBWgFrDAABOgFnAf8BfwGy
        AX8BtAF7AVsBZwGqAV0BoAFYAUABTAFqAVkBnAFzAfcBXgE5AWcBGAF3AQABXAFBAVwBgwFgAWIBYAEA
        AVwBcwFyAXMBcgEAAVwBYgFgAYMBYAFBAVwBAAFcARgBdwE5AWcoAAE5AWcB/wF/AQMBSwECAUcBAgFH
        AQEBRwH+AXsBGAFjFAABWgFrAf8BfwEWAWsBhAFlASABZQEiAV0BQAFMAQ8BZgGcAXMBGAFjAZwBcwGL
        AWkBAAFcAYMBZAFjAWABAAFgATEBcgHeAXsB3gF7ATEBcgEAAWABgwFgAYMBZAEAAVwBiwFtAXsBbygA
        ATkBZwH/AX8BAAFHAQABRwEAAUcBAAFDAf4BewE5AWcWAAGcAXMBNQF7ASABbQEgAWUBwAFYAU8BagH/
        AX8BWgFrAgABOQFnAd4BfwHFAWgBAAFkAQABZAEwAXIB/wF/ATkBZwE5AWcB/wF/ATABcgEAAWQBAAFk
        AcUBaAHeAX8BOQFnKAABWgFrAf8BfwGOAWMBRQFTAUUBUwFrAV8B/wF/ATkBZxYAAVoBawHeAXsB8wF2
        AYUBaQEWAXcB3gF7AVoBawYAAVoBawH/AX8BCAFtATABdgH/AX8BOQFnBAABOQFnAf8BfwExAXYBCAFt
        Af8BfwFaAWssAAE5AWcBnAFzAb0BdwG9AXcBvQF3ATkBZxoAATkBZwGcAXMBvQF3AXsBbwFaAWsKAAFa
        AWsBvQF3AZwBcwE5AWcIAAE5AWcBnAFzAb0BdwFaAWskAAFCAU0BPgcAAT4DAAEoAwABQAMAARADAAEB
        AQABAQUAAYAXAAP/AQAB+AEfAQcB/wLDAgAB8AEPAQEB/wKBAgAB8AEPAQABfwQAAfABDwEAAT8EAAGA
        AQEBgAE/BgABgAEfBgABwAEPAYABAQQAAcABBwHAAQMEAAHgAQMBwAEDBAAB4AEDAYABAQQAAfAFAAGA
        AQEB+AUAAfABDwH8BQAB8AEPAf4BAQQAAfABDwH+AQMCgQIAAfgBHwH/AQcCwwIACw==
</value>
  </data>
  <metadata name="ImageList24x24.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>153, 17</value>
  </metadata>
  <data name="ImageList24x24.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj00LjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAACm
        BgAAAk1TRnQBSQFMAwEBAAGIAQABiAEAARgBAAEYAQAE/wERAQAI/wFCAU0BNgcAATYDAAEoAwABYAMA
        ARgDAAEBAQABEAYAARLiAAH/AX8B/wF/Af8Bf7gAAf8BfwGcAXMBWwFvAXsBbwH/AX+0AAH/AX8BWwFr
        Ad8BewG9AXcBvgF3AZwBcwH/AX+wAAH/AX8BewFvAd8BfwETAVsB4AEhAYsBQgHfAX8BnAFzrgAB/wF/
        AXsBbwHfAX8BNQFfAQABJgECASYBRQEyAd8CewFvAf8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/
        AX8B/wF/mgAB/wF/AXsBbwHfAX8BNAFfASEBJgFEAS4BQwEuAWcBNgHfAX8BvgF3AZwBcwGdAXMBnQFz
        AZ0BcwGdAXMBnQFzAZ0BcwGdAXMBewFvAb0Bd5YAAf8BfwF7AW8B/wF/AVYBYwFAASoBYwEyAWQBNgFj
        ATIBhwE6AZwBcwGaAW8BmgFvAZoBbwGaAW8BmgFvAZoBbwGaAW8BmgFvAZoBbwG+AXcBvgF7Ad4Be5IA
        Af8BfwF7AW8B/wF/AXYBZwFhAS4BgwEyAYQBNgGEATYBhAE2AYQBNgFiAS4BYQEuAWEBLgFhAS4BYQEu
        AWEBLgFhAS4BYQEuAWEBLgFAASoBpwE+Ad8BewF8AW+SAAGcAXMB3wF/AXYBZwFhAS4BgwE2AaQBOgGk
        AToBpAE6AaQBOgGkAToBpAE6AaQBOgGkAToBpAE6AaQBOgGkAToBpAE6AaQBOgGkAToBhAE2AYMBMgG+
        AXcBfAFvkAAB/wF/Ad8BfwG7AXMBgQEyAaMBNgHEAToBxAE6AcQBOgHEAToBxAE6AcQBOgHEAToBxAE6
        AcQBOgHEAToBxAE6AcQBOgHEAToBxAE6AcQBOgGkAToBpAE6Ad4BewF8AW+QAAGcAXMB/wF/AQkBSwGh
        ATYBxAE+AcQBPgHEAT4BxAE+AcQBPgHEAT4BxAE+AcQBPgHEAT4BxAE+AcQBPgHEAT4BxAE+AcQBPgHE
        AT4BxAE+AcQBOgHEAToB3gF7AXwBc5AAAZwBcwH/AX8B5QFCAeIBOgHlAT4B5AE+AeQBPgHkAT4B5AE+
        AeQBPgHkAT4B5AE+AeQBPgHkAT4B5AE+AeQBPgHkAT4B5AE+AeQBPgHlAT4B5AE+AeQBPgHfAXsBnAFz
        kAAB3gF7Af8BfwFyAWMBwAE2AQQBQwEEAUMBBAFDAQQBQwEEAUMBBAFDAQQBQwEEAUMBBAFDAQQBQwEE
        AUMBBAFDAQQBQwEEAUMBBAFDAQQBQwHjAT4B5AE+Ad8BfwGcAXOSAAG9AXcB/wF/ASoBTwHgAToBBAFD
        AQQBQwEEAUMBBAFDAQQBQwEEAUMBBAFDAQQBQwEEAUMBBAFDAQQBQwEEAUMBBAFDAQQBQwEEAUMB4wFC
        AeMBQgHfAX8BnAFzkgAB/wF/Ad8BewH/AX8BKQFTAeABOgEDAUMBAwFHAQMBRwEEAUMBAwFDAeEBPgHh
        AT4B4QE+AeEBPgHhAT4B4QE+AeEBPgHhAT4B4QE+AeABPgHhAT4B/wF/AZwBc5QAAf8BfwH/AX8B/wF/
        ASkBUwHgAT4BAwFHAQMBRwECAUcBAwFHAUwBWwFLAVcBSwFXAUsBVwFLAVcBSwFXAUsBVwFLAVcBSwFX
        AUoBUwGVAWsB/wF/Ab0Bd5YAAf8BfwH/AX8B/wF/AScBUwHgAUIBAwFLAQEBRwEmAU8B/wF/Af8BfwH/
        AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8B/wF/Af8BfwG9AXeaAAH/AX8B/wF/Af8BfwFHAVMBAAFD
        AQEBRwEkAU8B/wF/AZwBcwH/AX8B3gF7Ad4BewHeAXsB3gF7Ad4BewHeAXsB3gF7Af8Bf54AAf8BfwH/
        AX8B/wF/AUYBUwHgAT4BJQFTAf8BfwG9AXeyAAH/AX8B/wF/Af8BfwGRAWcB2gF3Af8BfwH/AX+0AAH/
        AX8B3gF7Af8BfwH/AX8B3gF7uAAB3gF7Ad4BewHeAXv/AGsAAUIBTQE+BwABPgMAASgDAAFgAwABGAMA
        AQEBAAEBBQABIAEBFgAD/wEAA/8JAAH/AR8B/wkAAf4BDwH/CQAB/AEHAf8JAAH4AQcB/wkAAfABAAED
        CQAB4AEAAQEJAAHACwABgAsAAYA7AAGACwABgAsAAcALAAHgAQABAQkAAfABAAEDCQAB+AEHAf8JAAH8
        AQcB/wkAAf4BDwH/CQAB/wEfAf8JAAP/CQAL
</value>
  </data>
  <metadata name="BrandNameColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DormantColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
</root>