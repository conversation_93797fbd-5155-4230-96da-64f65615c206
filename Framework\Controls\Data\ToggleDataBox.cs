﻿using System;
using System.ComponentModel;

namespace Framework.Controls.Data
{
    public partial class ToggleDataBox : DataBox
    {

        #region Startup

        public ToggleDataBox()
        {
            InitializeComponent();
            ErrorLabel = errorLabel1;
            GetErrorMessageMethod = GetErrorMessage;
            Load += ToggleDataBox_Load;
        }

        private string GetErrorMessage()
        {
            string errormessage = string.Empty;
            if (Value == null)
            {
                errormessage = "Selection required";
            }
            return errormessage;
        }

        private void ToggleDataBox_Load(object sender, EventArgs e)
        {
            radioButtonTrue.CheckedChanged += radioButton_CheckedChanged;
            radioButtonFalse.CheckedChanged += radioButton_CheckedChanged;
        }

        #endregion


        #region Value

        private void radioButton_CheckedChanged(object sender, EventArgs e)
        {
            Value = radioButtonTrue.Checked;
        }

        protected override void OnValueChanged()
        {
            base.OnValueChanged();
            if (Value is bool)
            {
                bool newvalue = (bool)Value;
                if (newvalue)
                {
                    radioButtonTrue.Checked = true;
                }
                else
                {
                    radioButtonFalse.Checked = true;
                }
            }
        }

        public string TrueText
        {
            get
            {
                return radioButtonTrue.Text;
            }
            set
            {
                if (radioButtonTrue.Text != value)
                {
                    radioButtonTrue.Text = value;
                }
            }
        }

        public string FalseText
        {
            get
            {
                return radioButtonFalse.Text;
            }
            set
            {
                if (radioButtonFalse.Text != value)
                {
                    radioButtonFalse.Text = value;
                }
            }
        }

        #endregion

    }
}
