﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformContractTasks
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformContractTasks))
        Me.LabelMediaServicesValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelLastWeekValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelMediaServices = New DevExpress.XtraEditors.LabelControl()
        Me.LabelLastWeek = New DevExpress.XtraEditors.LabelControl()
        Me.LabelProjectName = New DevExpress.XtraEditors.LabelControl()
        Me.LabelFirstWeekValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelClientNameValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelInfo1 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelFirstWeek = New DevExpress.XtraEditors.LabelControl()
        Me.LabelContractClassification = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkContractClassificationValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelClientName = New DevExpress.XtraEditors.LabelControl()
        Me.LabelTasks = New DevExpress.XtraEditors.LabelControl()
        Me.LabelSignedBy = New DevExpress.XtraEditors.LabelControl()
        Me.LabelCreatedBy = New DevExpress.XtraEditors.LabelControl()
        Me.LabelSignedByValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelCreatedByValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelSignatureDate = New DevExpress.XtraEditors.LabelControl()
        Me.LabelClientAccountManager = New DevExpress.XtraEditors.LabelControl()
        Me.LabelCreationDate = New DevExpress.XtraEditors.LabelControl()
        Me.LabelSignDateValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelCreationDateValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelCancelledBy = New DevExpress.XtraEditors.LabelControl()
        Me.LabelCancelledByValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelCancelDate = New DevExpress.XtraEditors.LabelControl()
        Me.LabelCancelDateValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelClientAccountManagerValue = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkPrint = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkDelete = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkModifyContract = New DevExpress.XtraEditors.LabelControl()
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonBack = New DevExpress.XtraEditors.SimpleButton()
        Me.HyperlinkApproved = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkBilling = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl4 = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkModifySpecialConditions = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkProductLocatorProducts = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkClone = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl11 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelContractTypeValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelContractDate = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkProjectName = New DevExpress.XtraEditors.LabelControl()
        Me.PanelFlexiSized = New System.Windows.Forms.Panel()
        Me.LabelDemoProvider = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinDemoProvider = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkInfo1Value = New DevExpress.XtraEditors.LabelControl()
        Me.PanelStaticSized = New System.Windows.Forms.Panel()
        Me.LabelDemoOwner = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkDemoOwner = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkContractDateValue = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkModifyNotes = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkCancel = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkAuditLog = New DevExpress.XtraEditors.LabelControl()
        Me.LabelSaveInfo = New DevExpress.XtraEditors.LabelControl()
        Me.LabelBillingInstructionWarning = New DevExpress.XtraEditors.LabelControl()
        Me.CheckEditRollForward = New DevExpress.XtraEditors.CheckEdit()
        Me.CheckEditAddedValue = New DevExpress.XtraEditors.CheckEdit()
        Me.HyperlinkProductLocatorButtons = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkDates = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkCloneNewContract = New DevExpress.XtraEditors.LabelControl()
        Me.PanelFlexiSized.SuspendLayout()
        Me.PanelStaticSized.SuspendLayout()
        CType(Me.CheckEditRollForward.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditAddedValue.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 27.75!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Margin = New System.Windows.Forms.Padding(5, 5, 5, 21)
        Me.LabelTitle.Size = New System.Drawing.Size(435, 56)
        Me.LabelTitle.TabIndex = 0
        Me.LabelTitle.Text = "Contract AV105263"
        '
        'LabelMediaServicesValue
        '
        Me.LabelMediaServicesValue.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelMediaServicesValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelMediaServicesValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelMediaServicesValue.AutoEllipsis = True
        Me.LabelMediaServicesValue.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelMediaServicesValue.Location = New System.Drawing.Point(168, 140)
        Me.LabelMediaServicesValue.Margin = New System.Windows.Forms.Padding(4, 4, 12, 13)
        Me.LabelMediaServicesValue.Name = "LabelMediaServicesValue"
        Me.LabelMediaServicesValue.Size = New System.Drawing.Size(396, 17)
        Me.LabelMediaServicesValue.TabIndex = 9
        Me.LabelMediaServicesValue.Text = "MediaServicesValue"
        '
        'LabelLastWeekValue
        '
        Me.LabelLastWeekValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelLastWeekValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelLastWeekValue.Location = New System.Drawing.Point(168, 208)
        Me.LabelLastWeekValue.Margin = New System.Windows.Forms.Padding(4, 4, 12, 13)
        Me.LabelLastWeekValue.Name = "LabelLastWeekValue"
        Me.LabelLastWeekValue.Size = New System.Drawing.Size(107, 17)
        Me.LabelLastWeekValue.TabIndex = 13
        Me.LabelLastWeekValue.Text = "LastWeekValue"
        '
        'LabelMediaServices
        '
        Me.LabelMediaServices.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelMediaServices.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelMediaServices.Location = New System.Drawing.Point(4, 140)
        Me.LabelMediaServices.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelMediaServices.Name = "LabelMediaServices"
        Me.LabelMediaServices.Size = New System.Drawing.Size(108, 17)
        Me.LabelMediaServices.TabIndex = 8
        Me.LabelMediaServices.Text = "Media Services:"
        '
        'LabelLastWeek
        '
        Me.LabelLastWeek.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelLastWeek.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelLastWeek.Location = New System.Drawing.Point(4, 208)
        Me.LabelLastWeek.Margin = New System.Windows.Forms.Padding(4, 4, 4, 39)
        Me.LabelLastWeek.Name = "LabelLastWeek"
        Me.LabelLastWeek.Size = New System.Drawing.Size(71, 17)
        Me.LabelLastWeek.TabIndex = 12
        Me.LabelLastWeek.Text = "End Date:"
        '
        'LabelProjectName
        '
        Me.LabelProjectName.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelProjectName.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelProjectName.Location = New System.Drawing.Point(4, 72)
        Me.LabelProjectName.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelProjectName.Name = "LabelProjectName"
        Me.LabelProjectName.Size = New System.Drawing.Size(55, 17)
        Me.LabelProjectName.TabIndex = 4
        Me.LabelProjectName.Text = "Project:"
        '
        'LabelFirstWeekValue
        '
        Me.LabelFirstWeekValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelFirstWeekValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelFirstWeekValue.Location = New System.Drawing.Point(168, 174)
        Me.LabelFirstWeekValue.Margin = New System.Windows.Forms.Padding(4, 4, 12, 13)
        Me.LabelFirstWeekValue.Name = "LabelFirstWeekValue"
        Me.LabelFirstWeekValue.Size = New System.Drawing.Size(108, 17)
        Me.LabelFirstWeekValue.TabIndex = 11
        Me.LabelFirstWeekValue.Text = "FirstWeekValue"
        '
        'LabelClientNameValue
        '
        Me.LabelClientNameValue.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelClientNameValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelClientNameValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelClientNameValue.AutoEllipsis = True
        Me.LabelClientNameValue.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelClientNameValue.Location = New System.Drawing.Point(168, 38)
        Me.LabelClientNameValue.Margin = New System.Windows.Forms.Padding(4, 4, 12, 13)
        Me.LabelClientNameValue.Name = "LabelClientNameValue"
        Me.LabelClientNameValue.Size = New System.Drawing.Size(396, 17)
        Me.LabelClientNameValue.TabIndex = 3
        Me.LabelClientNameValue.Text = "ClientNameValue"
        '
        'LabelInfo1
        '
        Me.LabelInfo1.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelInfo1.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelInfo1.Location = New System.Drawing.Point(4, 106)
        Me.LabelInfo1.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelInfo1.Name = "LabelInfo1"
        Me.LabelInfo1.Size = New System.Drawing.Size(73, 17)
        Me.LabelInfo1.TabIndex = 6
        Me.LabelInfo1.Text = "LabelInfo1"
        '
        'LabelFirstWeek
        '
        Me.LabelFirstWeek.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelFirstWeek.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelFirstWeek.Location = New System.Drawing.Point(4, 174)
        Me.LabelFirstWeek.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelFirstWeek.Name = "LabelFirstWeek"
        Me.LabelFirstWeek.Size = New System.Drawing.Size(81, 17)
        Me.LabelFirstWeek.TabIndex = 10
        Me.LabelFirstWeek.Text = "First Week:"
        '
        'LabelContractClassification
        '
        Me.LabelContractClassification.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelContractClassification.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelContractClassification.Location = New System.Drawing.Point(4, 242)
        Me.LabelContractClassification.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelContractClassification.Name = "LabelContractClassification"
        Me.LabelContractClassification.Size = New System.Drawing.Size(96, 17)
        Me.LabelContractClassification.TabIndex = 10
        Me.LabelContractClassification.Text = "Classification:"
        '
        'HyperlinkContractClassificationValue
        '
        Me.HyperlinkContractClassificationValue.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.HyperlinkContractClassificationValue.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkContractClassificationValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkContractClassificationValue.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkContractClassificationValue.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkContractClassificationValue.AutoEllipsis = True
        Me.HyperlinkContractClassificationValue.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.HyperlinkContractClassificationValue.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkContractClassificationValue.Location = New System.Drawing.Point(168, 242)
        Me.HyperlinkContractClassificationValue.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.HyperlinkContractClassificationValue.Name = "HyperlinkContractClassificationValue"
        Me.HyperlinkContractClassificationValue.Size = New System.Drawing.Size(396, 17)
        Me.HyperlinkContractClassificationValue.TabIndex = 5
        Me.HyperlinkContractClassificationValue.Text = "HyperlinkContractClassificationValue"
        '
        'LabelClientName
        '
        Me.LabelClientName.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelClientName.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelClientName.Location = New System.Drawing.Point(4, 38)
        Me.LabelClientName.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelClientName.Name = "LabelClientName"
        Me.LabelClientName.Size = New System.Drawing.Size(89, 17)
        Me.LabelClientName.TabIndex = 2
        Me.LabelClientName.Text = "Client Name:"
        '
        'LabelTasks
        '
        Me.LabelTasks.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelTasks.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelTasks.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTasks.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelTasks.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelTasks.LineVisible = True
        Me.LabelTasks.Location = New System.Drawing.Point(15, 372)
        Me.LabelTasks.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelTasks.Name = "LabelTasks"
        Me.LabelTasks.Size = New System.Drawing.Size(1027, 24)
        Me.LabelTasks.TabIndex = 3
        Me.LabelTasks.Text = "What would you like to do with this contract?"
        '
        'LabelSignedBy
        '
        Me.LabelSignedBy.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSignedBy.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelSignedBy.Location = New System.Drawing.Point(4, 106)
        Me.LabelSignedBy.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelSignedBy.Name = "LabelSignedBy"
        Me.LabelSignedBy.Size = New System.Drawing.Size(77, 17)
        Me.LabelSignedBy.TabIndex = 6
        Me.LabelSignedBy.Text = "Signed By:"
        '
        'LabelCreatedBy
        '
        Me.LabelCreatedBy.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCreatedBy.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCreatedBy.Location = New System.Drawing.Point(4, 38)
        Me.LabelCreatedBy.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelCreatedBy.Name = "LabelCreatedBy"
        Me.LabelCreatedBy.Size = New System.Drawing.Size(84, 17)
        Me.LabelCreatedBy.TabIndex = 2
        Me.LabelCreatedBy.Text = "Created By:"
        '
        'LabelSignedByValue
        '
        Me.LabelSignedByValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSignedByValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelSignedByValue.Location = New System.Drawing.Point(190, 106)
        Me.LabelSignedByValue.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelSignedByValue.Name = "LabelSignedByValue"
        Me.LabelSignedByValue.Size = New System.Drawing.Size(104, 17)
        Me.LabelSignedByValue.TabIndex = 7
        Me.LabelSignedByValue.Text = "SignedByValue"
        '
        'LabelCreatedByValue
        '
        Me.LabelCreatedByValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCreatedByValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCreatedByValue.Location = New System.Drawing.Point(190, 38)
        Me.LabelCreatedByValue.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelCreatedByValue.Name = "LabelCreatedByValue"
        Me.LabelCreatedByValue.Size = New System.Drawing.Size(111, 17)
        Me.LabelCreatedByValue.TabIndex = 3
        Me.LabelCreatedByValue.Text = "CreatedByValue"
        '
        'LabelSignatureDate
        '
        Me.LabelSignatureDate.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSignatureDate.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelSignatureDate.Location = New System.Drawing.Point(4, 140)
        Me.LabelSignatureDate.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelSignatureDate.Name = "LabelSignatureDate"
        Me.LabelSignatureDate.Size = New System.Drawing.Size(112, 17)
        Me.LabelSignatureDate.TabIndex = 8
        Me.LabelSignatureDate.Text = "Signature Date:"
        '
        'LabelClientAccountManager
        '
        Me.LabelClientAccountManager.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelClientAccountManager.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelClientAccountManager.Location = New System.Drawing.Point(4, 242)
        Me.LabelClientAccountManager.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelClientAccountManager.Name = "LabelClientAccountManager"
        Me.LabelClientAccountManager.Size = New System.Drawing.Size(171, 17)
        Me.LabelClientAccountManager.TabIndex = 9
        Me.LabelClientAccountManager.Text = "Client Account Manager:"
        '
        'LabelCreationDate
        '
        Me.LabelCreationDate.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCreationDate.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCreationDate.Location = New System.Drawing.Point(4, 72)
        Me.LabelCreationDate.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelCreationDate.Name = "LabelCreationDate"
        Me.LabelCreationDate.Size = New System.Drawing.Size(103, 17)
        Me.LabelCreationDate.TabIndex = 4
        Me.LabelCreationDate.Text = "Creation Date:"
        '
        'LabelSignDateValue
        '
        Me.LabelSignDateValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSignDateValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelSignDateValue.Location = New System.Drawing.Point(190, 140)
        Me.LabelSignDateValue.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelSignDateValue.Name = "LabelSignDateValue"
        Me.LabelSignDateValue.Size = New System.Drawing.Size(102, 17)
        Me.LabelSignDateValue.TabIndex = 9
        Me.LabelSignDateValue.Text = "SignDateValue"
        '
        'LabelCreationDateValue
        '
        Me.LabelCreationDateValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCreationDateValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCreationDateValue.Location = New System.Drawing.Point(190, 72)
        Me.LabelCreationDateValue.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelCreationDateValue.Name = "LabelCreationDateValue"
        Me.LabelCreationDateValue.Size = New System.Drawing.Size(130, 17)
        Me.LabelCreationDateValue.TabIndex = 5
        Me.LabelCreationDateValue.Text = "CreationDateValue"
        '
        'LabelCancelledBy
        '
        Me.LabelCancelledBy.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCancelledBy.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCancelledBy.Location = New System.Drawing.Point(4, 174)
        Me.LabelCancelledBy.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelCancelledBy.Name = "LabelCancelledBy"
        Me.LabelCancelledBy.Size = New System.Drawing.Size(94, 17)
        Me.LabelCancelledBy.TabIndex = 10
        Me.LabelCancelledBy.Text = "Cancelled By:"
        '
        'LabelCancelledByValue
        '
        Me.LabelCancelledByValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCancelledByValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCancelledByValue.Location = New System.Drawing.Point(190, 174)
        Me.LabelCancelledByValue.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelCancelledByValue.Name = "LabelCancelledByValue"
        Me.LabelCancelledByValue.Size = New System.Drawing.Size(121, 17)
        Me.LabelCancelledByValue.TabIndex = 11
        Me.LabelCancelledByValue.Text = "CancelledByValue"
        '
        'LabelCancelDate
        '
        Me.LabelCancelDate.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCancelDate.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCancelDate.Location = New System.Drawing.Point(4, 208)
        Me.LabelCancelDate.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelCancelDate.Name = "LabelCancelDate"
        Me.LabelCancelDate.Size = New System.Drawing.Size(127, 17)
        Me.LabelCancelDate.TabIndex = 12
        Me.LabelCancelDate.Text = "Cancellation Date:"
        '
        'LabelCancelDateValue
        '
        Me.LabelCancelDateValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCancelDateValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCancelDateValue.Location = New System.Drawing.Point(190, 208)
        Me.LabelCancelDateValue.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelCancelDateValue.Name = "LabelCancelDateValue"
        Me.LabelCancelDateValue.Size = New System.Drawing.Size(154, 17)
        Me.LabelCancelDateValue.TabIndex = 13
        Me.LabelCancelDateValue.Text = "CancellationDateValue"
        '
        'LabelClientAccountManagerValue
        '
        Me.LabelClientAccountManagerValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelClientAccountManagerValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelClientAccountManagerValue.Location = New System.Drawing.Point(190, 242)
        Me.LabelClientAccountManagerValue.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelClientAccountManagerValue.Name = "LabelClientAccountManagerValue"
        Me.LabelClientAccountManagerValue.Size = New System.Drawing.Size(193, 17)
        Me.LabelClientAccountManagerValue.TabIndex = 13
        Me.LabelClientAccountManagerValue.Text = "ClientAccountManagerValue"
        '
        'HyperlinkPrint
        '
        Me.HyperlinkPrint.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkPrint.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkPrint.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkPrint.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkPrint.AutoEllipsis = True
        Me.HyperlinkPrint.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkPrint.Location = New System.Drawing.Point(15, 400)
        Me.HyperlinkPrint.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.HyperlinkPrint.Name = "HyperlinkPrint"
        Me.HyperlinkPrint.Size = New System.Drawing.Size(198, 17)
        Me.HyperlinkPrint.TabIndex = 4
        Me.HyperlinkPrint.Text = "Preview or print a hard copy"
        '
        'HyperlinkDelete
        '
        Me.HyperlinkDelete.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkDelete.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkDelete.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkDelete.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkDelete.AutoEllipsis = True
        Me.HyperlinkDelete.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkDelete.Location = New System.Drawing.Point(591, 430)
        Me.HyperlinkDelete.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.HyperlinkDelete.Name = "HyperlinkDelete"
        Me.HyperlinkDelete.Size = New System.Drawing.Size(135, 17)
        Me.HyperlinkDelete.TabIndex = 14
        Me.HyperlinkDelete.Text = "Delete the contract"
        '
        'HyperlinkModifyContract
        '
        Me.HyperlinkModifyContract.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkModifyContract.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkModifyContract.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkModifyContract.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkModifyContract.AutoEllipsis = True
        Me.HyperlinkModifyContract.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkModifyContract.Location = New System.Drawing.Point(15, 570)
        Me.HyperlinkModifyContract.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.HyperlinkModifyContract.Name = "HyperlinkModifyContract"
        Me.HyperlinkModifyContract.Size = New System.Drawing.Size(158, 17)
        Me.HyperlinkModifyContract.TabIndex = 9
        Me.HyperlinkModifyContract.Text = "Modify contract details"
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "back.png")
        '
        'ButtonBack
        '
        Me.ButtonBack.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonBack.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonBack.Appearance.Options.UseFont = True
        Me.ButtonBack.ImageIndex = 0
        Me.ButtonBack.ImageList = Me.ImageList24x24
        Me.ButtonBack.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonBack.Location = New System.Drawing.Point(914, 666)
        Me.ButtonBack.LookAndFeel.SkinName = "Black"
        Me.ButtonBack.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonBack.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonBack.Name = "ButtonBack"
        Me.ButtonBack.Size = New System.Drawing.Size(129, 37)
        Me.ButtonBack.TabIndex = 22
        Me.ButtonBack.Text = "Back"
        '
        'HyperlinkApproved
        '
        Me.HyperlinkApproved.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkApproved.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkApproved.Appearance.ForeColor = System.Drawing.Color.Firebrick
        Me.HyperlinkApproved.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkApproved.AutoEllipsis = True
        Me.HyperlinkApproved.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkApproved.Location = New System.Drawing.Point(15, 502)
        Me.HyperlinkApproved.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.HyperlinkApproved.Name = "HyperlinkApproved"
        Me.HyperlinkApproved.Size = New System.Drawing.Size(175, 17)
        Me.HyperlinkApproved.TabIndex = 18
        Me.HyperlinkApproved.Text = "Not approved by Finance"
        '
        'HyperlinkBilling
        '
        Me.HyperlinkBilling.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkBilling.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkBilling.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkBilling.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkBilling.AutoEllipsis = True
        Me.HyperlinkBilling.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkBilling.Location = New System.Drawing.Point(15, 536)
        Me.HyperlinkBilling.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.HyperlinkBilling.Name = "HyperlinkBilling"
        Me.HyperlinkBilling.Size = New System.Drawing.Size(185, 17)
        Me.HyperlinkBilling.TabIndex = 8
        Me.HyperlinkBilling.Text = "Manage billing instructions"
        '
        'LabelControl4
        '
        Me.LabelControl4.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.LabelControl4.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl4.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelControl4.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.LabelControl4.AutoEllipsis = True
        Me.LabelControl4.Cursor = System.Windows.Forms.Cursors.Hand
        Me.LabelControl4.Location = New System.Drawing.Point(864, 536)
        Me.LabelControl4.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelControl4.Name = "LabelControl4"
        Me.LabelControl4.Size = New System.Drawing.Size(150, 17)
        Me.LabelControl4.TabIndex = 19
        Me.LabelControl4.Text = "Change burst brands"
        Me.LabelControl4.Visible = False
        '
        'HyperlinkModifySpecialConditions
        '
        Me.HyperlinkModifySpecialConditions.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkModifySpecialConditions.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkModifySpecialConditions.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkModifySpecialConditions.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkModifySpecialConditions.AutoEllipsis = True
        Me.HyperlinkModifySpecialConditions.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkModifySpecialConditions.Location = New System.Drawing.Point(15, 434)
        Me.HyperlinkModifySpecialConditions.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.HyperlinkModifySpecialConditions.Name = "HyperlinkModifySpecialConditions"
        Me.HyperlinkModifySpecialConditions.Size = New System.Drawing.Size(173, 17)
        Me.HyperlinkModifySpecialConditions.TabIndex = 5
        Me.HyperlinkModifySpecialConditions.Text = "Modify special conditions"
        '
        'HyperlinkProductLocatorProducts
        '
        Me.HyperlinkProductLocatorProducts.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkProductLocatorProducts.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkProductLocatorProducts.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkProductLocatorProducts.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkProductLocatorProducts.AutoEllipsis = True
        Me.HyperlinkProductLocatorProducts.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkProductLocatorProducts.Location = New System.Drawing.Point(864, 400)
        Me.HyperlinkProductLocatorProducts.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.HyperlinkProductLocatorProducts.Name = "HyperlinkProductLocatorProducts"
        Me.HyperlinkProductLocatorProducts.Size = New System.Drawing.Size(175, 17)
        Me.HyperlinkProductLocatorProducts.TabIndex = 20
        Me.HyperlinkProductLocatorProducts.Text = "Product locator products"
        Me.HyperlinkProductLocatorProducts.Visible = False
        '
        'HyperlinkClone
        '
        Me.HyperlinkClone.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkClone.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkClone.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkClone.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkClone.AutoEllipsis = True
        Me.HyperlinkClone.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkClone.Location = New System.Drawing.Point(591, 460)
        Me.HyperlinkClone.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.HyperlinkClone.Name = "HyperlinkClone"
        Me.HyperlinkClone.Size = New System.Drawing.Size(133, 17)
        Me.HyperlinkClone.TabIndex = 15
        Me.HyperlinkClone.Text = "Clone the Contract"
        '
        'LabelControl11
        '
        Me.LabelControl11.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl11.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl11.Location = New System.Drawing.Point(4, 4)
        Me.LabelControl11.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelControl11.Name = "LabelControl11"
        Me.LabelControl11.Size = New System.Drawing.Size(106, 17)
        Me.LabelControl11.TabIndex = 0
        Me.LabelControl11.Text = "Contract Type:"
        '
        'LabelContractTypeValue
        '
        Me.LabelContractTypeValue.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelContractTypeValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelContractTypeValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelContractTypeValue.AutoEllipsis = True
        Me.LabelContractTypeValue.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelContractTypeValue.Location = New System.Drawing.Point(168, 4)
        Me.LabelContractTypeValue.Margin = New System.Windows.Forms.Padding(4, 4, 12, 13)
        Me.LabelContractTypeValue.Name = "LabelContractTypeValue"
        Me.LabelContractTypeValue.Size = New System.Drawing.Size(396, 17)
        Me.LabelContractTypeValue.TabIndex = 1
        Me.LabelContractTypeValue.Text = "ContractTypeValue"
        '
        'LabelContractDate
        '
        Me.LabelContractDate.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelContractDate.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelContractDate.Location = New System.Drawing.Point(4, 4)
        Me.LabelContractDate.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelContractDate.Name = "LabelContractDate"
        Me.LabelContractDate.Size = New System.Drawing.Size(105, 17)
        Me.LabelContractDate.TabIndex = 0
        Me.LabelContractDate.Text = "Contract Date:"
        '
        'HyperlinkProjectName
        '
        Me.HyperlinkProjectName.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.HyperlinkProjectName.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkProjectName.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkProjectName.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkProjectName.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkProjectName.AutoEllipsis = True
        Me.HyperlinkProjectName.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.HyperlinkProjectName.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkProjectName.Location = New System.Drawing.Point(168, 72)
        Me.HyperlinkProjectName.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.HyperlinkProjectName.Name = "HyperlinkProjectName"
        Me.HyperlinkProjectName.Size = New System.Drawing.Size(396, 17)
        Me.HyperlinkProjectName.TabIndex = 5
        Me.HyperlinkProjectName.Text = "HyperlinkProjectName"
        '
        'PanelFlexiSized
        '
        Me.PanelFlexiSized.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelFlexiSized.Controls.Add(Me.LabelDemoProvider)
        Me.PanelFlexiSized.Controls.Add(Me.HyperlinDemoProvider)
        Me.PanelFlexiSized.Controls.Add(Me.LabelControl11)
        Me.PanelFlexiSized.Controls.Add(Me.LabelClientName)
        Me.PanelFlexiSized.Controls.Add(Me.LabelFirstWeek)
        Me.PanelFlexiSized.Controls.Add(Me.LabelContractClassification)
        Me.PanelFlexiSized.Controls.Add(Me.HyperlinkContractClassificationValue)
        Me.PanelFlexiSized.Controls.Add(Me.LabelInfo1)
        Me.PanelFlexiSized.Controls.Add(Me.LabelClientNameValue)
        Me.PanelFlexiSized.Controls.Add(Me.HyperlinkInfo1Value)
        Me.PanelFlexiSized.Controls.Add(Me.LabelContractTypeValue)
        Me.PanelFlexiSized.Controls.Add(Me.LabelFirstWeekValue)
        Me.PanelFlexiSized.Controls.Add(Me.LabelProjectName)
        Me.PanelFlexiSized.Controls.Add(Me.HyperlinkProjectName)
        Me.PanelFlexiSized.Controls.Add(Me.LabelLastWeek)
        Me.PanelFlexiSized.Controls.Add(Me.LabelMediaServices)
        Me.PanelFlexiSized.Controls.Add(Me.LabelLastWeekValue)
        Me.PanelFlexiSized.Controls.Add(Me.LabelMediaServicesValue)
        Me.PanelFlexiSized.Location = New System.Drawing.Point(12, 70)
        Me.PanelFlexiSized.Margin = New System.Windows.Forms.Padding(0)
        Me.PanelFlexiSized.Name = "PanelFlexiSized"
        Me.PanelFlexiSized.Size = New System.Drawing.Size(568, 300)
        Me.PanelFlexiSized.TabIndex = 1
        '
        'LabelDemoProvider
        '
        Me.LabelDemoProvider.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelDemoProvider.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelDemoProvider.Location = New System.Drawing.Point(4, 276)
        Me.LabelDemoProvider.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelDemoProvider.Name = "LabelDemoProvider"
        Me.LabelDemoProvider.Size = New System.Drawing.Size(153, 17)
        Me.LabelDemoProvider.TabIndex = 14
        Me.LabelDemoProvider.Text = "Demo Service Provdr:"
        '
        'HyperlinDemoProvider
        '
        Me.HyperlinDemoProvider.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.HyperlinDemoProvider.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinDemoProvider.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinDemoProvider.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinDemoProvider.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinDemoProvider.AutoEllipsis = True
        Me.HyperlinDemoProvider.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.HyperlinDemoProvider.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinDemoProvider.Location = New System.Drawing.Point(168, 276)
        Me.HyperlinDemoProvider.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.HyperlinDemoProvider.Name = "HyperlinDemoProvider"
        Me.HyperlinDemoProvider.Size = New System.Drawing.Size(396, 17)
        Me.HyperlinDemoProvider.TabIndex = 15
        Me.HyperlinDemoProvider.Text = "HyperlinDemoProvider"
        '
        'HyperlinkInfo1Value
        '
        Me.HyperlinkInfo1Value.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.HyperlinkInfo1Value.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkInfo1Value.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkInfo1Value.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkInfo1Value.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkInfo1Value.AutoEllipsis = True
        Me.HyperlinkInfo1Value.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.HyperlinkInfo1Value.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkInfo1Value.Location = New System.Drawing.Point(168, 106)
        Me.HyperlinkInfo1Value.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.HyperlinkInfo1Value.Name = "HyperlinkInfo1Value"
        Me.HyperlinkInfo1Value.Size = New System.Drawing.Size(396, 17)
        Me.HyperlinkInfo1Value.TabIndex = 7
        Me.HyperlinkInfo1Value.Text = "HyperlinkInfo1Value"
        '
        'PanelStaticSized
        '
        Me.PanelStaticSized.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelStaticSized.Controls.Add(Me.LabelDemoOwner)
        Me.PanelStaticSized.Controls.Add(Me.HyperlinkDemoOwner)
        Me.PanelStaticSized.Controls.Add(Me.LabelContractDate)
        Me.PanelStaticSized.Controls.Add(Me.LabelSignedBy)
        Me.PanelStaticSized.Controls.Add(Me.LabelCancelledBy)
        Me.PanelStaticSized.Controls.Add(Me.LabelCreatedBy)
        Me.PanelStaticSized.Controls.Add(Me.LabelSignedByValue)
        Me.PanelStaticSized.Controls.Add(Me.LabelCancelledByValue)
        Me.PanelStaticSized.Controls.Add(Me.LabelCreatedByValue)
        Me.PanelStaticSized.Controls.Add(Me.LabelSignatureDate)
        Me.PanelStaticSized.Controls.Add(Me.LabelCancelDate)
        Me.PanelStaticSized.Controls.Add(Me.LabelCreationDate)
        Me.PanelStaticSized.Controls.Add(Me.LabelSignDateValue)
        Me.PanelStaticSized.Controls.Add(Me.LabelCancelDateValue)
        Me.PanelStaticSized.Controls.Add(Me.LabelCreationDateValue)
        Me.PanelStaticSized.Controls.Add(Me.LabelClientAccountManager)
        Me.PanelStaticSized.Controls.Add(Me.LabelClientAccountManagerValue)
        Me.PanelStaticSized.Controls.Add(Me.HyperlinkContractDateValue)
        Me.PanelStaticSized.Location = New System.Drawing.Point(588, 70)
        Me.PanelStaticSized.Margin = New System.Windows.Forms.Padding(0)
        Me.PanelStaticSized.Name = "PanelStaticSized"
        Me.PanelStaticSized.Size = New System.Drawing.Size(459, 300)
        Me.PanelStaticSized.TabIndex = 2
        '
        'LabelDemoOwner
        '
        Me.LabelDemoOwner.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelDemoOwner.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelDemoOwner.Location = New System.Drawing.Point(5, 276)
        Me.LabelDemoOwner.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelDemoOwner.Name = "LabelDemoOwner"
        Me.LabelDemoOwner.Size = New System.Drawing.Size(90, 17)
        Me.LabelDemoOwner.TabIndex = 16
        Me.LabelDemoOwner.Text = "Demo Team:"
        '
        'HyperlinkDemoOwner
        '
        Me.HyperlinkDemoOwner.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.HyperlinkDemoOwner.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkDemoOwner.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkDemoOwner.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkDemoOwner.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkDemoOwner.AutoEllipsis = True
        Me.HyperlinkDemoOwner.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.HyperlinkDemoOwner.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkDemoOwner.Location = New System.Drawing.Point(190, 270)
        Me.HyperlinkDemoOwner.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.HyperlinkDemoOwner.Name = "HyperlinkDemoOwner"
        Me.HyperlinkDemoOwner.Size = New System.Drawing.Size(216, 23)
        Me.HyperlinkDemoOwner.TabIndex = 17
        Me.HyperlinkDemoOwner.Text = "HyperlinkDemoTeam"
        '
        'HyperlinkContractDateValue
        '
        Me.HyperlinkContractDateValue.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkContractDateValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkContractDateValue.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkContractDateValue.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkContractDateValue.AutoEllipsis = True
        Me.HyperlinkContractDateValue.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkContractDateValue.Location = New System.Drawing.Point(190, 4)
        Me.HyperlinkContractDateValue.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.HyperlinkContractDateValue.Name = "HyperlinkContractDateValue"
        Me.HyperlinkContractDateValue.Size = New System.Drawing.Size(196, 17)
        Me.HyperlinkContractDateValue.TabIndex = 1
        Me.HyperlinkContractDateValue.Text = "HyperlinkContractDateValue"
        '
        'HyperlinkModifyNotes
        '
        Me.HyperlinkModifyNotes.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkModifyNotes.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkModifyNotes.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkModifyNotes.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkModifyNotes.AutoEllipsis = True
        Me.HyperlinkModifyNotes.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkModifyNotes.Location = New System.Drawing.Point(15, 468)
        Me.HyperlinkModifyNotes.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.HyperlinkModifyNotes.Name = "HyperlinkModifyNotes"
        Me.HyperlinkModifyNotes.Size = New System.Drawing.Size(90, 17)
        Me.HyperlinkModifyNotes.TabIndex = 6
        Me.HyperlinkModifyNotes.Text = "Modify notes"
        '
        'HyperlinkCancel
        '
        Me.HyperlinkCancel.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkCancel.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkCancel.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkCancel.AutoEllipsis = True
        Me.HyperlinkCancel.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkCancel.Location = New System.Drawing.Point(591, 400)
        Me.HyperlinkCancel.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.HyperlinkCancel.Name = "HyperlinkCancel"
        Me.HyperlinkCancel.Size = New System.Drawing.Size(136, 17)
        Me.HyperlinkCancel.TabIndex = 13
        Me.HyperlinkCancel.Text = "Cancel the contract"
        '
        'HyperlinkAuditLog
        '
        Me.HyperlinkAuditLog.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkAuditLog.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkAuditLog.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkAuditLog.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkAuditLog.AutoEllipsis = True
        Me.HyperlinkAuditLog.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkAuditLog.Location = New System.Drawing.Point(591, 524)
        Me.HyperlinkAuditLog.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.HyperlinkAuditLog.Name = "HyperlinkAuditLog"
        Me.HyperlinkAuditLog.Size = New System.Drawing.Size(122, 17)
        Me.HyperlinkAuditLog.TabIndex = 16
        Me.HyperlinkAuditLog.Text = "View user activity"
        '
        'LabelSaveInfo
        '
        Me.LabelSaveInfo.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSaveInfo.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelSaveInfo.Location = New System.Drawing.Point(15, 676)
        Me.LabelSaveInfo.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelSaveInfo.Name = "LabelSaveInfo"
        Me.LabelSaveInfo.Size = New System.Drawing.Size(255, 17)
        Me.LabelSaveInfo.TabIndex = 12
        Me.LabelSaveInfo.Text = "Contract saved on DATE at TIME"
        '
        'LabelBillingInstructionWarning
        '
        Me.LabelBillingInstructionWarning.Appearance.BackColor = System.Drawing.Color.FromArgb(CType(CType(192, Byte), Integer), CType(CType(0, Byte), Integer), CType(CType(0, Byte), Integer))
        Me.LabelBillingInstructionWarning.Appearance.Font = New System.Drawing.Font("Verdana", 15.75!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelBillingInstructionWarning.Appearance.ForeColor = System.Drawing.Color.White
        Me.LabelBillingInstructionWarning.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center
        Me.LabelBillingInstructionWarning.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap
        Me.LabelBillingInstructionWarning.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelBillingInstructionWarning.Location = New System.Drawing.Point(323, 575)
        Me.LabelBillingInstructionWarning.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelBillingInstructionWarning.Name = "LabelBillingInstructionWarning"
        Me.LabelBillingInstructionWarning.Size = New System.Drawing.Size(724, 78)
        Me.LabelBillingInstructionWarning.TabIndex = 17
        '
        'CheckEditRollForward
        '
        Me.CheckEditRollForward.Location = New System.Drawing.Point(13, 600)
        Me.CheckEditRollForward.Margin = New System.Windows.Forms.Padding(0, 4, 4, 4)
        Me.CheckEditRollForward.Name = "CheckEditRollForward"
        Me.CheckEditRollForward.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditRollForward.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditRollForward.Properties.Appearance.Options.UseFont = True
        Me.CheckEditRollForward.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditRollForward.Properties.AutoWidth = True
        Me.CheckEditRollForward.Properties.Caption = "This is a roll forward contract"
        Me.CheckEditRollForward.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditRollForward.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditRollForward.Size = New System.Drawing.Size(228, 21)
        Me.CheckEditRollForward.TabIndex = 10
        '
        'CheckEditAddedValue
        '
        Me.CheckEditAddedValue.Location = New System.Drawing.Point(13, 633)
        Me.CheckEditAddedValue.Margin = New System.Windows.Forms.Padding(0, 4, 4, 4)
        Me.CheckEditAddedValue.Name = "CheckEditAddedValue"
        Me.CheckEditAddedValue.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditAddedValue.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditAddedValue.Properties.Appearance.Options.UseFont = True
        Me.CheckEditAddedValue.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditAddedValue.Properties.AutoWidth = True
        Me.CheckEditAddedValue.Properties.Caption = "This is an added value contract"
        Me.CheckEditAddedValue.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditAddedValue.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditAddedValue.Size = New System.Drawing.Size(241, 21)
        Me.CheckEditAddedValue.TabIndex = 11
        '
        'HyperlinkProductLocatorButtons
        '
        Me.HyperlinkProductLocatorButtons.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkProductLocatorButtons.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkProductLocatorButtons.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkProductLocatorButtons.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkProductLocatorButtons.AutoEllipsis = True
        Me.HyperlinkProductLocatorButtons.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkProductLocatorButtons.Location = New System.Drawing.Point(864, 434)
        Me.HyperlinkProductLocatorButtons.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.HyperlinkProductLocatorButtons.Name = "HyperlinkProductLocatorButtons"
        Me.HyperlinkProductLocatorButtons.Size = New System.Drawing.Size(168, 17)
        Me.HyperlinkProductLocatorButtons.TabIndex = 23
        Me.HyperlinkProductLocatorButtons.Text = "Product locator buttons"
        Me.HyperlinkProductLocatorButtons.Visible = False
        '
        'HyperlinkDates
        '
        Me.HyperlinkDates.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkDates.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkDates.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkDates.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkDates.AutoEllipsis = True
        Me.HyperlinkDates.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkDates.Location = New System.Drawing.Point(591, 492)
        Me.HyperlinkDates.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.HyperlinkDates.Name = "HyperlinkDates"
        Me.HyperlinkDates.Size = New System.Drawing.Size(119, 17)
        Me.HyperlinkDates.TabIndex = 24
        Me.HyperlinkDates.Text = "Data entry dates"
        '
        'HyperlinkCloneNewContract
        '
        Me.HyperlinkCloneNewContract.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkCloneNewContract.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkCloneNewContract.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkCloneNewContract.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkCloneNewContract.AutoEllipsis = True
        Me.HyperlinkCloneNewContract.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkCloneNewContract.Enabled = False
        Me.HyperlinkCloneNewContract.Location = New System.Drawing.Point(864, 511)
        Me.HyperlinkCloneNewContract.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.HyperlinkCloneNewContract.Name = "HyperlinkCloneNewContract"
        Me.HyperlinkCloneNewContract.Size = New System.Drawing.Size(130, 17)
        Me.HyperlinkCloneNewContract.TabIndex = 25
        Me.HyperlinkCloneNewContract.Text = "Clone the contract"
        Me.HyperlinkCloneNewContract.Visible = False
        '
        'SubformContractTasks
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(9.0!, 17.0!)
        Me.Controls.Add(Me.HyperlinkCloneNewContract)
        Me.Controls.Add(Me.HyperlinkDates)
        Me.Controls.Add(Me.HyperlinkProductLocatorButtons)
        Me.Controls.Add(Me.CheckEditAddedValue)
        Me.Controls.Add(Me.PanelStaticSized)
        Me.Controls.Add(Me.PanelFlexiSized)
        Me.Controls.Add(Me.ButtonBack)
        Me.Controls.Add(Me.HyperlinkClone)
        Me.Controls.Add(Me.HyperlinkDelete)
        Me.Controls.Add(Me.LabelSaveInfo)
        Me.Controls.Add(Me.HyperlinkProductLocatorProducts)
        Me.Controls.Add(Me.HyperlinkModifySpecialConditions)
        Me.Controls.Add(Me.HyperlinkModifyContract)
        Me.Controls.Add(Me.HyperlinkModifyNotes)
        Me.Controls.Add(Me.LabelBillingInstructionWarning)
        Me.Controls.Add(Me.LabelControl4)
        Me.Controls.Add(Me.HyperlinkBilling)
        Me.Controls.Add(Me.HyperlinkCancel)
        Me.Controls.Add(Me.HyperlinkAuditLog)
        Me.Controls.Add(Me.HyperlinkApproved)
        Me.Controls.Add(Me.HyperlinkPrint)
        Me.Controls.Add(Me.LabelTasks)
        Me.Controls.Add(Me.CheckEditRollForward)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(5)
        Me.Name = "SubformContractTasks"
        Me.Padding = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.Controls.SetChildIndex(Me.CheckEditRollForward, 0)
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        Me.Controls.SetChildIndex(Me.LabelTasks, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkPrint, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkApproved, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkAuditLog, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkCancel, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkBilling, 0)
        Me.Controls.SetChildIndex(Me.LabelControl4, 0)
        Me.Controls.SetChildIndex(Me.LabelBillingInstructionWarning, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkModifyNotes, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkModifyContract, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkModifySpecialConditions, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkProductLocatorProducts, 0)
        Me.Controls.SetChildIndex(Me.LabelSaveInfo, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkDelete, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkClone, 0)
        Me.Controls.SetChildIndex(Me.ButtonBack, 0)
        Me.Controls.SetChildIndex(Me.PanelFlexiSized, 0)
        Me.Controls.SetChildIndex(Me.PanelStaticSized, 0)
        Me.Controls.SetChildIndex(Me.CheckEditAddedValue, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkProductLocatorButtons, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkDates, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkCloneNewContract, 0)
        Me.PanelFlexiSized.ResumeLayout(False)
        Me.PanelFlexiSized.PerformLayout()
        Me.PanelStaticSized.ResumeLayout(False)
        Me.PanelStaticSized.PerformLayout()
        CType(Me.CheckEditRollForward.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditAddedValue.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents LabelMediaServicesValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelLastWeekValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelMediaServices As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelLastWeek As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelProjectName As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelFirstWeekValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelClientNameValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelInfo1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelFirstWeek As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelContractClassification As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkContractClassificationValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelClientName As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelTasks As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelSignedBy As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelCreatedBy As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelSignedByValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelCreatedByValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelSignatureDate As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelClientAccountManager As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelClientAccountManagerValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelCreationDate As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelSignDateValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelCreationDateValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelCancelledBy As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelCancelledByValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelCancelDate As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelCancelDateValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkPrint As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkDelete As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkModifyContract As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents ButtonBack As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents HyperlinkApproved As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkBilling As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl4 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkModifySpecialConditions As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkProductLocatorProducts As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkClone As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl11 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelContractTypeValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelContractDate As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkProjectName As DevExpress.XtraEditors.LabelControl
    Friend WithEvents PanelFlexiSized As System.Windows.Forms.Panel
    Friend WithEvents PanelStaticSized As System.Windows.Forms.Panel
    Friend WithEvents HyperlinkModifyNotes As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkCancel As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkInfo1Value As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkAuditLog As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelSaveInfo As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkContractDateValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelBillingInstructionWarning As DevExpress.XtraEditors.LabelControl
    Friend WithEvents CheckEditRollForward As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents CheckEditAddedValue As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents HyperlinkProductLocatorButtons As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkDates As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkCloneNewContract As LabelControl
    Friend WithEvents LabelDemoProvider As LabelControl
    Friend WithEvents HyperlinDemoProvider As LabelControl
    Friend WithEvents LabelDemoOwner As LabelControl
    Friend WithEvents HyperlinkDemoOwner As LabelControl
End Class
