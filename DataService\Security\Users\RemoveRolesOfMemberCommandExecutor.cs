﻿using DataAccess;

namespace DataService.Security
{
    class RemoveRolesOfMemberCommandExecutor : CommandExecutor<RemoveRolesOfMemberCommand>
    {

        public override void Execute(RemoveRolesOfMemberCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.RemoveRolesOfMember))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.AddInputParameter("userid", command.UserId);
                storedprocedure.AddInputParameter("roles", command.Roles);
                storedprocedure.AddOutputParameter<string>("username");
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;

                if (string.IsNullOrEmpty(command.ErrorMessage))
                {
                    command.Username = storedprocedure.GetOutputParameterValue("username").ToString();
                }
            }
        }

    }
}
