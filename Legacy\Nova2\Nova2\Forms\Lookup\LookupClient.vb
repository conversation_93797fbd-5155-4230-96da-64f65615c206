Public Class LookupClient

    Public Sub New(ByVal GridData As BindingSource, ByVal AllowMultiSelect As Boolean)

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        GridController = New GridManager _
        (GridItems, _
        TextEditSearch, _
        GridData, _
        My.Settings.DBConnection, _
        PictureAdvancedSearch, _
        PictureClearSearch, _
        Nothing, _
        Nothing)

        ' Apply multi-select restrictions if necessary.
        GridItems.MultiSelect = AllowMultiSelect

    End Sub

    Public Overloads Shared Function SelectRows _
    (ByVal ConnectionString As String, _
    ByVal AllowMultiSelect As Boolean, _
    ByVal GridToExclude As DataGridView) _
    As List(Of DataRow)

        ' A string variable to hold any potential errors during data retrieval.
        Dim ErrorMessage As String = String.Empty

        ' Create a data source for the list.
        Dim GridData As BindingSource = Lookup.GetClients(ConnectionString, ErrorMessage, GridToExclude)

        ' Create an instance of this lookup list form and return the user-selected rows.
        Using Selector As New LookupClient(GridData, AllowMultiSelect)
            Return SelectRows(Selector, ErrorMessage)
        End Using

    End Function

    Public Overloads Shared Function SelectRowsByPermission_EditMyClients _
    (ByVal ConnectionString As String, _
    ByVal AllowMultiSelect As Boolean, _
    ByVal GridToExclude As DataGridView) _
    As List(Of DataRow)

        ' A string variable to hold any potential errors during data retrieval.
        Dim ErrorMessage As String = String.Empty

        ' Create a data source for the list.
        Dim GridData As BindingSource = Lookup.GetClientsByPermission_EditMyClients _
        (ConnectionString, ErrorMessage, GridToExclude)

        ' Create an instance of this lookup list form and return the user-selected rows.
        Using Selector As New LookupClient(GridData, AllowMultiSelect)
            Return SelectRows(Selector, ErrorMessage)
        End Using

    End Function

    Public Overloads Shared Function SelectRowsByContractHistoryByPermission_ViewMyContracts _
    (ByVal ConnectionString As String, _
    ByVal AllowMultiSelect As Boolean, _
    ByVal GridToExclude As DataGridView) _
    As List(Of DataRow)

        ' A string variable to hold any potential errors during data retrieval.
        Dim ErrorMessage As String = String.Empty

        ' Create a data source for the list.
        Dim GridData As BindingSource = Lookup.GetClientsByContractHistoryByPermission_ViewMyContracts _
        (ConnectionString, ErrorMessage, GridToExclude)

        ' Create an instance of this lookup list form and return the user-selected rows.
        Using Selector As New LookupClient(GridData, AllowMultiSelect)
            Return SelectRows(Selector, ErrorMessage)
        End Using

    End Function

    Public Shared Function SelectRowsByAccountManager _
    (ByVal ConnectionString As String, _
    ByVal AllowMultiSelect As Boolean, _
    ByVal GridToExclude As DataGridView, _
    ByVal AccountManagerID As Integer, _
    ByVal AtDate As Date) _
    As List(Of DataRow)

        ' A string variable to hold any potential errors during data retrieval.
        Dim ErrorMessage As String = String.Empty

        ' Create a data source for the list.
        Dim GridData As BindingSource = Lookup.GetClientsByAccountManager _
        (ConnectionString, ErrorMessage, GridToExclude, AccountManagerID, AtDate)

        ' Create an instance of this lookup list form and return the user-selected rows.
        Using Selector As New LookupClient(GridData, AllowMultiSelect)
            Return SelectRows(Selector, ErrorMessage)
        End Using

    End Function

    Public Shared Function SelectAgencyRows _
    (ByVal ConnectionString As String, _
    ByVal AllowMultiSelect As Boolean, _
    ByVal GridToExclude As DataGridView) _
    As List(Of DataRow)

        ' A string variable to hold any potential errors during data retrieval.
        Dim ErrorMessage As String = String.Empty

        ' Create a data source for the list.
        Dim GridData As BindingSource = Lookup.GetAgencies(ConnectionString, ErrorMessage, GridToExclude)

        ' Create an instance of this lookup list form and return the user-selected rows.
        Using Selector As New LookupClient(GridData, AllowMultiSelect)
            Return SelectRows(Selector, ErrorMessage)
        End Using

    End Function

    Public Overloads Shared Function SelectApprovedClients _
    (ByVal ConnectionString As String, _
    ByVal AllowMultiSelect As Boolean, _
    ByVal GridToExclude As DataGridView) _
    As List(Of DataRow)

        ' A string variable to hold any potential errors during data retrieval.
        Dim ErrorMessage As String = String.Empty

        ' Create a data source for the list.
        Dim GridData As BindingSource = Lookup.GetApprovedClients(ConnectionString, ErrorMessage, GridToExclude)

        ' Create an instance of this lookup list form and return the user-selected rows.
        Using Selector As New LookupClient(GridData, AllowMultiSelect)
            Return SelectRows(Selector, ErrorMessage)
        End Using

    End Function

End Class
