﻿using System;
using System.Windows.Forms;
using Framework.Breadcrumbs;
using Framework.Controls.TabSystem;
using Framework.Controls;

namespace Framework.Surfaces
{
    public partial class Surface : UserControl
    {
        public Tab ParentTab;


        #region Startup

        public Surface()
        {
            InitializeComponent();
            SetAppearanceProperties();
            DirtyStateManager = new DirtyStateManager(this);
            ErrorStateManager = new ErrorStateManager(this);
            FrameworkSettings.Colors.ThemeColorChanged += Settings_ThemeColorChanged;
            Load += Surface_Load;
        }

        private void SetAppearanceProperties()
        {
            Font = FrameworkSettings.Fonts.FORMFONT;
            ForeColor = FrameworkSettings.Colors.FORMFORECOLOR;
            BackColor = FrameworkSettings.Colors.FORMBACKCOLOR;
            Margin = new Padding(0);
        }

        private void Settings_ThemeColorChanged()
        {
            ApplyTheme();
        }

        private void Surface_Load(object sender, EventArgs e)
        {
            DirtyStateManager.InitializeState();
            ErrorStateManager.InitializeState();
            ApplyTheme();
        }

        protected virtual void ApplyTheme()
        {
            ApplyThemeToAllChildren(this);
        }

        private void ApplyThemeToAllChildren(Control parentcontrol)
        {
            for (int i = 0; i < parentcontrol.Controls.Count; i++)
            {
                Control childcontrol = parentcontrol.Controls[i];
                if (childcontrol is FlatButton)
                {
                    ((FlatButton)childcontrol).ApplyTheme();
                }
                else
                {
                    if (childcontrol is Surface == false)
                    {
                        if (childcontrol.HasChildren)
                        {
                            ApplyThemeToAllChildren(childcontrol);
                        }
                    }
                }
            }
        }

        #endregion


        #region Dirty state

        private DirtyStateManager _DirtyStateManager;
        public DirtyStateManager DirtyStateManager
        {
            get { return _DirtyStateManager; }
            private set { _DirtyStateManager = value; }
        }

        #endregion


        #region Error state

        private ErrorStateManager _ErrorStateManager;
        public ErrorStateManager ErrorStateManager
        {
            get { return _ErrorStateManager; }
            private set { _ErrorStateManager = value; }
        }

        #endregion


        #region Breadcrumbs

        public string BreadcrumbName = string.Empty;
        public string BreadcrumbTitleText = string.Empty;
        public BreadcrumbContainerSurface BreadcrumbContainer;

        #endregion        

    }
}
