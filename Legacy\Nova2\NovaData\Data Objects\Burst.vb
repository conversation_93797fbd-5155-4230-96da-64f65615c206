Imports System.Data.SqlClient

Public Class Burst
    Inherits OldBaseObject

    Private ConsumingForm As LiquidShell.BaseForm
    Private PeerBurstTable As DataTable
    Private PeerBurstAdapter As New DataSetContractTableAdapters.PeerBurstTableAdapter
    Private NonCompetingBrandAdapter As New DataSetContractTableAdapters.BrandFamilyMemberTableAdapter
    Private CompetingBurstTable As DataTable
    Private CompetingBurstAdapter As New DataSetContractTableAdapters.CompetingBurstTableAdapter
    Private NonCompetingBrandTable As DataTable
    Private CompetingMediaTable As DataTable
    Private MediaFamilyIDList As String

#Region "Fields"

    Private _ChainID As Integer
    Private _ChainName As String = "Select..."
    Private _ChainTypeID As Integer = 0
    Private _StorePoolID As Guid
    Private _MediaID As Integer
    Private _MediaName As String = "Select..."
    Private _IsPcaStatusMedia As Boolean = False
    Private _BrandID As Guid
    Private _BrandName As String = "Select..."
    Private _ProductName As String = String.Empty
    Private _FirstWeek As Date
    Private _InstallWeeks As Integer = 0
    Private _InstallStoreQty As Integer = 0
    Private _BillableStoreQty As Integer = 0
    Private _RentalRate As Decimal = 0
    Private _BillableWeeks As Integer = 0
    Private _Discount As Decimal = 0
    Private _CrossoverQty As Integer = 0
    Private _InstallationInstructions As String = String.Empty
    Private _LastWeek As Date
    Private _Categories As String = String.Empty
    Private _StoreListConfirmed As Boolean
    Private _CreationDate As Date
    Private _InstallAtHomesite As Boolean = True
    Private _ApplyInstructionsAcrossAllBursts As Boolean = False
    Private _ApplyDatesAcrossAllBursts As Boolean = False
    Private _AdsPerInstallation As Integer = 1
    Private _AdsPerCrossover As Integer = 0
    Private _AdsPerShelfTalk As Integer = 0
    Private _InstallRegardlessOfStock As Boolean

    Private _ClientID As Integer
    Private _PcaStatusId As Integer
    Private _PcaStatusName As String = "Select..."
    Private _MediaAllowsHomesite As Boolean = False
    Private _MediaAllowsCrossover As Boolean = False
    Private _BurstLoadingFeeTable As DataTable
    Private _BurstCategoryTable As DataTable
    Private _AdditionalCategoryView As DataView
    Private WithEvents _CrossoverPreferenceView As DataView
    Private _StorePoolTable As DataTable
    Private _PeerBurstView As DataView
    Private _RelatedBurstView As DataView
    Private _PeerRelativeBurstView As DataView
    Private _StoresAllowingCurrentMediaService As Integer = 0
    Private _StoresInUniverse As Integer = 0
    Private _ParentContract As OldContract
    Private _ConflictingContracts As String = String.Empty
    Private _BurstsWithHigherStoreClaimPriorityAwaitingStoreListConfirmation As List(Of DataRow)
    Private _StoreListTable As DataTable
    Private _StoreTable As DataTable
    Private _BurstInstallationDayTable As DataTable
    Private _BurstPcaStatusTable As DataTable
    Private _BurstInstallationDayView As DataView
    Private _Homesite As String = "Select..."

#End Region

#Region "Database Column Properties"

    Public ReadOnly Property ChainID() As Integer
        Get
            Return _ChainID
        End Get
    End Property

    Public ReadOnly Property ChainTypeID() As Integer
        Get
            Return _ChainTypeID
        End Get
    End Property

    Public ReadOnly Property ChainName() As String
        Get
            Return _ChainName
        End Get
    End Property

    Public Property StorePoolID() As Guid
        Get
            Return _StorePoolID
        End Get
        Set(ByVal value As Guid)
            _StorePoolID = value
            IsDirty = True
        End Set
    End Property

    Public ReadOnly Property MediaID() As Integer
        Get
            Return _MediaID
        End Get
    End Property

    Public ReadOnly Property MediaName() As String
        Get
            Return _MediaName
        End Get
    End Property

    Public ReadOnly Property IsPcaStatusMedia() As Boolean
        Get
            Return _IsPcaStatusMedia
        End Get
    End Property

    Public ReadOnly Property BrandID() As Guid
        Get
            Return _BrandID
        End Get
    End Property

    Public ReadOnly Property BrandName() As String
        Get
            Return _BrandName
        End Get
    End Property

    Public ReadOnly Property PcaStatusId() As Integer
        Get
            Return _PcaStatusId
        End Get
    End Property

    Public ReadOnly Property PcaStatusName() As String
        Get
            Return _PcaStatusName
        End Get
    End Property

    Public Property ProductName() As String
        Get
            Return _ProductName
        End Get
        Set(ByVal value As String)
            _ProductName = value
            IsDirty = True
        End Set
    End Property

    Public Property FirstWeek() As Date
        Get

            ' Check if the property is null, if so, first give it a value.
            If _FirstWeek.Year = 1 Then
                ' Find the next Monday to set as the new date.
                _FirstWeek = Today
                While Not _FirstWeek.DayOfWeek = DayOfWeek.Monday
                    _FirstWeek = _FirstWeek.AddDays(1)
                End While
            End If

            ' Return the property value.
            Return _FirstWeek

        End Get
        Set(ByVal value As Date)

            _FirstWeek = value
            IsDirty = True
            ' Update LastWeek using the given value.
            _LastWeek = DateAdd(DateInterval.WeekOfYear, _InstallWeeks - 1, value)
            UpdatePeerBurstTable()

        End Set
    End Property

    Public Property InstallWeeks() As Integer
        Get
            Return _InstallWeeks
        End Get
        Set(ByVal value As Integer)
            _InstallWeeks = value
            _BillableWeeks = value
            IsDirty = True
            ' Update related properties using the given value.
            _LastWeek = DateAdd(DateInterval.WeekOfYear, value - 1, _FirstWeek)
            UpdatePeerBurstTable()
        End Set
    End Property

    Public Property InstallStoreQty() As Integer
        Get
            Return _InstallStoreQty
        End Get
        Set(ByVal value As Integer)
            ' Set the field value.
            _InstallStoreQty = value
            _BillableStoreQty = value
            ' If this burst doesn't have any relatives, set the store pool capacity equal to the InstallStoreQty value.
            If _RelatedBurstView.Count = 0 Then
                StorePoolCapacity = value
            End If
            IsDirty = True
        End Set
    End Property

    Public Property BillableStoreQty() As Integer
        Get
            Return _BillableStoreQty
        End Get
        Set(ByVal value As Integer)
            _BillableStoreQty = value
            RecalculateLoadingFeeAmounts()
            IsDirty = True
        End Set
    End Property

    Public Property RentalRate() As Decimal
        Get
            Return _RentalRate
        End Get
        Set(ByVal value As Decimal)
            _RentalRate = value
            RecalculateLoadingFeeAmounts()
            IsDirty = True
        End Set
    End Property

    Public Property BillableWeeks() As Integer
        Get
            Return _BillableWeeks
        End Get
        Set(ByVal value As Integer)
            _BillableWeeks = value
            RecalculateLoadingFeeAmounts()
            IsDirty = True
        End Set
    End Property

    Public Property Discount() As Decimal
        Get
            Return _Discount
        End Get
        Set(ByVal value As Decimal)
            _Discount = value
            IsDirty = True
        End Set
    End Property

    Public Property CrossoverQty() As Integer
        Get
            Return _CrossoverQty
        End Get
        Set(ByVal value As Integer)
            _CrossoverQty = value
            IsDirty = True
            UpdateCategoriesColumn()
        End Set
    End Property

    Public Property InstallationInstructions() As String
        Get
            Return _InstallationInstructions
        End Get
        Set(ByVal value As String)
            ' Clean the proposed new value.
            value = value.Trim
            _InstallationInstructions = value
            IsDirty = True
        End Set
    End Property

    Public Property StoreListConfirmed() As Boolean
        Get
            Return _StoreListConfirmed
        End Get
        Set(ByVal value As Boolean)
            If ParentContract.Signed Then
                ' Only signed contracts may have their store lists confirmed.
                _StoreListConfirmed = value
                IsDirty = True
            End If
        End Set
    End Property

    Public Property CreationDate() As Date
        Get
            Return _CreationDate
        End Get
        Set(ByVal value As Date)
            _CreationDate = value
        End Set
    End Property

    Public Property InstallAtHomesite() As Boolean
        Get
            Return _InstallAtHomesite
        End Get
        Set(ByVal value As Boolean)
            _InstallAtHomesite = value
            UpdateCompetingBurstTable()
            UpdatePeerBurstTable()
            IsDirty = True
        End Set
    End Property


    Public Property ApplyDatesAcrossAllBursts() As Boolean
        Get
            Return _ApplyDatesAcrossAllBursts
        End Get
        Set(ByVal value As Boolean)
            _ApplyDatesAcrossAllBursts = value
            IsDirty = True
        End Set
    End Property
    Public Property ApplyInstructionsAcrossAllBursts() As Boolean
        Get
            Return _ApplyInstructionsAcrossAllBursts
        End Get
        Set(ByVal value As Boolean)
            _ApplyInstructionsAcrossAllBursts = value
            IsDirty = True
        End Set
    End Property

    Public Property AdsPerInstallation() As Integer
        Get
            Return _AdsPerInstallation
        End Get
        Set(ByVal value As Integer)
            _AdsPerInstallation = value
            IsDirty = True
        End Set
    End Property

    Public Property AdsPerCrossover() As Integer
        Get
            Return _AdsPerCrossover
        End Get
        Set(ByVal value As Integer)
            _AdsPerCrossover = value
            IsDirty = True
        End Set
    End Property
    Public Property AdsPerShelfTalk() As Integer
        Get
            Return _AdsPerShelfTalk
        End Get
        Set(ByVal value As Integer)
            _AdsPerShelfTalk = value
            IsDirty = True
        End Set
    End Property
    Public Property InstallRegardlessOfStock() As Boolean
        Get
            Return _InstallRegardlessOfStock
        End Get
        Set(ByVal value As Boolean)
            _InstallRegardlessOfStock = value
            UpdateCompetingBurstTable()
            UpdatePeerBurstTable()
            IsDirty = True
        End Set
    End Property

#End Region

#Region "Custom Properties"

    Public ReadOnly Property StoreTable() As DataTable
        Get
            Return _StoreTable
        End Get
    End Property

    Public ReadOnly Property ParentContract() As OldContract
        Get
            Return _ParentContract
        End Get
    End Property

    Public ReadOnly Property StoresInUniverse() As Integer
        Get
            Return _StoresInUniverse
        End Get
    End Property

    Public ReadOnly Property StoresAllowingCurrentMediaService() As Integer
        Get
            Return _StoresAllowingCurrentMediaService
        End Get
    End Property

    Public ReadOnly Property StoresAllowingCurrentMediaServiceLabelColor() As Color
        Get
            If IsNothing(StorePoolCapacity) Or IsNothing(_StoresAllowingCurrentMediaService) Then
                Return Color.DimGray
            ElseIf StorePoolCapacity <= _StoresAllowingCurrentMediaService Then
                Return Color.DimGray
            Else
                Return Color.Red
            End If
        End Get
    End Property

    Public WriteOnly Property SelectedHomesite() As DataRow
        Set(ByVal value As DataRow)

            ' Update the homesite name field.
            _Homesite = value("CategoryName")

            ' Check if the current burst categories contain a homesite row.
            For Each BurstCategory As DataRow In _BurstCategoryTable.Rows
                If Not BurstCategory.RowState = DataRowState.Deleted Then
                    If BurstCategory("Priority") = 0 Then
                        ' This category is the homesite. Delete it.
                        BurstCategory.Delete()
                        Exit For
                    End If
                End If
            Next

            ' Get the primary key for the new homesite row.
            Dim BurstID As Guid = Row("BurstID")
            Dim CategoryID As Integer = value("CategoryID")
            Dim Key() As Object = {BurstID, CategoryID}

            ' Check if a row with this key already exists in the BurstCategory table.
            If _BurstCategoryTable.Rows.Contains(Key) Then
                ' A row for this category already exists. Delete it.
                _BurstCategoryTable.Rows.Find(Key).Delete()
            End If

            ' Create a new homesite row in the BurstCategory table.
            Dim NewHomesite As DataRow = _BurstCategoryTable.NewRow
            NewHomesite("BurstID") = BurstID
            NewHomesite("CategoryID") = CategoryID
            NewHomesite("CategoryName") = value("CategoryName")
            NewHomesite("Priority") = 0
            _BurstCategoryTable.Rows.Add(NewHomesite)

            ' Update related info.
            UpdateCategoriesColumn()
            UpdatePeerBurstTable()

        End Set
    End Property

    Public ReadOnly Property Homesite() As String
        Get
            Return _Homesite.Replace("&", "&&")
        End Get
    End Property

    Public WriteOnly Property SelectedChain() As DataRow
        Set(ByVal value As DataRow)
            _ChainID = value("ChainID")
            _ChainName = value("ChainName")
            _ChainTypeID = value("ChainTypeID")
            UpdateUniverseStoreCount()
            UpdateStoreCountAllowingCurrentMediaService()
            UpdatePeerBurstTable()
            UpdateCompetingBurstTable()
            IsDirty = True
        End Set
    End Property

    Public WriteOnly Property SelectedBrand() As DataRow
        Set(ByVal value As DataRow)
            ' Update relatd variables.
            _BrandID = value("BrandID")
            _BrandName = value("BrandName")
            ' Update the NonCompetingBrand table.
            UpdateNonCompetingBrandTable()
            UpdatePeerBurstTable()
            UpdateCompetingBurstTable()
            ' Flag the object as dirty.
            IsDirty = True
        End Set
    End Property
    Public WriteOnly Property SelectedPcaStatus() As DataRow
        Set(ByVal value As DataRow)
            ' Update relatd variables.
            _PcaStatusId = value("PcaStatusId")
            _PcaStatusName = value("PcaStatusName")
            ' Flag the object as dirty.
            UpdatePcaSatusTable()
            IsDirty = True
        End Set
    End Property

    Public WriteOnly Property SelectedMedia() As DataRow
        Set(ByVal value As DataRow)
            _MediaID = value("MediaID")
            _MediaName = value("MediaName")
            _MediaAllowsHomesite = value("Homesite")
            _MediaAllowsCrossover = value("Crossover")
            _IsPcaStatusMedia = value("isPNPPcaStatus")
            MediaFamilyIDList = value("MediaFamilyIDList")
            UpdateUniverseStoreCount()
            UpdateStoreCountAllowingCurrentMediaService()
            UpdateCompetingMediaTable()
            UpdatePeerBurstTable()
            UpdateCompetingBurstTable()
            IsDirty = True
        End Set
    End Property

    Public ReadOnly Property MediaLifeCycleValid As Boolean
        Get

            ' Create a table adapter to collect the life cycle data.
            Dim BurstAdapter As New DataSetContractTableAdapters.ValidMediaTableAdapter
            Dim SqlCon As New SqlClient.SqlConnection(ConnectionString)
            BurstAdapter.Connection = SqlCon

            Try
                ' Get the most recent life cycle data.
                Dim CurrentBurstData As DataTable = BurstAdapter.GetData(MediaID, FirstWeek, LastWeek)
                ' Return the MediaLifeCycleValid column.
                Return CurrentBurstData.Rows(0).Item("Valid")
            Catch ex As Exception
                ConsumingForm.ShowMessage("Failed to check for media service life cycle validity." & vbCrLf & vbCrLf _
                                          & LiquidShell.LiquidAgent.GetErrorMessage(ex), "Well, This Is Embarassing", MessageBoxIcon.Error)
                Return False
            Finally
                ' Dispose of unneeded objects.
                SqlCon.Dispose()
                BurstAdapter.Dispose()
            End Try

        End Get
    End Property

    Public Property LastWeek() As Date
        Get
            ' Check if the property is null, if so, first give it a value.
            If _LastWeek.Year = 1 Then
                _LastWeek = DateAdd(DateInterval.WeekOfYear, InstallWeeks - 1, FirstWeek)
            End If
            ' Return the property value.
            Return _LastWeek
        End Get
        Set(ByVal value As Date)
            _LastWeek = value
            IsDirty = True
            ' Update InstallWeeks using the given LastWeek value.
            _InstallWeeks = DateDiff(DateInterval.WeekOfYear, FirstWeek, value) + 1
            UpdatePeerBurstTable()
        End Set
    End Property

    Public ReadOnly Property RentalAmount() As Decimal
        Get
            Return RentalRate * BillableStoreQty * BillableWeeks
        End Get
    End Property

    Public ReadOnly Property LoadingFees() As Decimal
        Get

            ' Add up all the percentages of loading fees that must be applied.
            Dim TotalLoadingPercent As Decimal = 0
            If IsNothing(_BurstLoadingFeeTable) = False Then
                For Each LoadingFee As DataRow In _BurstLoadingFeeTable.Rows
                    ' Only try and add this row's value if the row isn't deleted.
                    If Not LoadingFee.RowState = DataRowState.Deleted Then
                        TotalLoadingPercent += LoadingFee("Percentage")
                    End If
                Next
            End If

            ' Return the total amount of all loading fees.
            Return TotalLoadingPercent / 100 * RentalAmount

        End Get
    End Property

    Public ReadOnly Property DiscountAmount() As Decimal
        Get
            Return Discount / 100 * (RentalAmount + LoadingFees)
        End Get
    End Property

    Public ReadOnly Property NetRental() As Decimal
        Get
            Return RentalAmount + LoadingFees - DiscountAmount
        End Get
    End Property

    Public Property FreeWeeks() As Integer
        Get
            Return InstallWeeks - BillableWeeks
        End Get
        Set(ByVal value As Integer)
            BillableWeeks = InstallWeeks - value
        End Set
    End Property

    Public Property FreeStoreQty() As Integer
        Get
            Return InstallStoreQty - BillableStoreQty
        End Get
        Set(ByVal value As Integer)
            BillableStoreQty = InstallStoreQty - value
        End Set
    End Property

    Public ReadOnly Property ClientID() As Integer
        Get
            Return _ClientID
        End Get
    End Property

    Public ReadOnly Property MediaAllowsHomesite() As Boolean
        Get
            Return _MediaAllowsHomesite
        End Get
    End Property

    Public ReadOnly Property MediaAllowsCrossover() As Boolean
        Get
            Return _MediaAllowsCrossover
        End Get
    End Property

    Public ReadOnly Property BurstTitle() As String
        Get
            Dim Separator As String = " / "
            Dim ReturnString As New System.Text.StringBuilder(_ParentContract.ContractNumber & Separator)
            If String.Compare(ChainName, "Select...") = 0 Then
                ReturnString.Append("(new burst)")
            Else
                ReturnString.Append(ChainName & " Burst")
            End If
            Return ReturnString.ToString
        End Get
    End Property

    Public ReadOnly Property BurstLoadingFeeTable() As DataTable
        Get
            Return _BurstLoadingFeeTable
        End Get
    End Property

    Public ReadOnly Property BurstCategoryTable() As DataTable
        Get
            Return _BurstCategoryTable
        End Get
    End Property
    Public ReadOnly Property BurstInstallationDayTable() As DataTable
        Get
            Return _BurstInstallationDayTable
        End Get
    End Property
    Public ReadOnly Property AdditionalCategoryView() As DataView
        Get
            Return _AdditionalCategoryView
        End Get
    End Property

    Public ReadOnly Property CrossoverPreferenceView() As DataView
        Get
            Return _CrossoverPreferenceView
        End Get
    End Property

    Public ReadOnly Property BurstInstallationDayView() As DataView
        Get
            Return _BurstInstallationDayView
        End Get
    End Property

    Public ReadOnly Property PeerBurstView() As DataView
        Get
            _PeerBurstView.RowFilter = PeerBurstViewFilter()
            UpdatePeerBurstTable()
            Return _PeerBurstView
        End Get
    End Property

    Public ReadOnly Property RelatedBurstView() As DataView
        Get
            Return _RelatedBurstView
        End Get
    End Property

    Public ReadOnly Property PeerRelativeBurstView() As DataView
        Get
            Return _PeerRelativeBurstView
        End Get
    End Property

    Public Property StorePoolCapacity() As Integer
        Get
            If _StorePoolID.ToString() = "8a3180ee-28bb-4c45-91c1-5abb67b742b7" Then
                Return 42
            End If

            Dim ReturnValue As Integer = _StorePoolTable.Rows.Find(_StorePoolID).Item("StorePoolQty")
            Return ReturnValue
        End Get
        Set(ByVal value As Integer)
            Dim CurrentValue As Integer = _StorePoolTable.Rows.Find(_StorePoolID).Item("StorePoolQty")
            If value <> CurrentValue Then
                _StorePoolTable.Rows.Find(_StorePoolID).Item("StorePoolQty") = value
            End If
        End Set
    End Property

    Public ReadOnly Property MinStorePoolCapacity() As Integer
        Get
            ' Calculate the lowest possible value for StorePoolQty (i.e. the greatest InstallStoreQty value of all the bursts sharing this
            ' burst's store pool).

            ' A variable to record the greatest InstallStoreQty value of all bursts that share a store pool with this burst.
            Dim MinPoolCapacity As Integer = _InstallStoreQty

            ' Check all relative bursts.
            For i As Integer = 0 To _RelatedBurstView.Count - 1
                Dim InstallStores As Integer = _RelatedBurstView(i).Row("InstallStoreQty")
                If MinPoolCapacity < InstallStores Then
                    MinPoolCapacity = InstallStores
                End If
            Next

            Return MinPoolCapacity

        End Get
    End Property

    Public ReadOnly Property MaxStorePoolCapacity() As Integer
        Get
            ' Calculate the greatest possible value for StorePoolQty (i.e. the sum of all InstallStoreQty values of all the bursts sharing this
            ' burst's store pool).

            ' A variable to record the sum of all InstallStoreQty values.
            Dim MaxPoolCapacity As Integer = _InstallStoreQty

            ' Check all relative bursts.
            For i As Integer = 0 To _RelatedBurstView.Count - 1
                ' Exclude the current burst from the calculation.
                MaxPoolCapacity += _RelatedBurstView(i).Row("InstallStoreQty")
            Next

            Return MaxPoolCapacity

        End Get
    End Property

    Public ReadOnly Property ConflictingContracts() As String
        Get
            UpdateConflictingContracts()
            Return _ConflictingContracts
        End Get
    End Property

    Public ReadOnly Property BurstsWithHigherStoreClaimPriorityAwaitingStoreListConfirmation() As List(Of DataRow)
        Get
            UpdateBurstsWithHigherStoreClaimPriorityAwaitingStoreListConfirmation()
            Return _BurstsWithHigherStoreClaimPriorityAwaitingStoreListConfirmation
        End Get
    End Property

    Public ReadOnly Property StoreListTable() As DataTable
        Get
            Return _StoreListTable
        End Get
    End Property

    Public ReadOnly Property StoreListSize() As Integer
        Get

            ' Remember the original RowStateFilter for the _StoreListTable table.
            Dim OriginalRowStateFilter As DataViewRowState = _StoreListTable.DefaultView.RowStateFilter

            ' Switch the default view so that only current rows are visible (and not deleted ones).
            _StoreListTable.DefaultView.RowStateFilter = DataViewRowState.CurrentRows

            ' Count the rows and remember the result.
            Dim RowCount As Integer = _StoreListTable.DefaultView.Count

            ' Restore the original row state filter.
            _StoreListTable.DefaultView.RowStateFilter = OriginalRowStateFilter

            ' Return the number of rows that was counted.
            Return RowCount

        End Get
    End Property

    Public ReadOnly Property StoreListNotEmpty() As Boolean
        Get
            If StoreListSize > 0 Then
                Return True
            Else
                Return False
            End If
        End Get
    End Property

#End Region

#Region "Event Handlers"

    Private Sub _CrossoverPreferenceView_ListChanged _
    (ByVal sender As Object, ByVal e As System.ComponentModel.ListChangedEventArgs) _
    Handles _CrossoverPreferenceView.ListChanged

        If e.ListChangedType = System.ComponentModel.ListChangedType.ItemDeleted Then
            ' A category has been removed from the crossover preferences list. Reset the crossover priorities.
            ResetCrossoverPriorities()
        End If

    End Sub

#End Region

#Region "Public Methods"

    Public Shared Function GetRowDescription(ByVal Row As DataRow, ByVal ParentContractNumber As String) As String

        ' Create a string builder to contruct the description.
        Dim Description As New System.Text.StringBuilder

        ' Add descriptive elements to the string builder.
        If Row.RowState = DataRowState.Detached AndAlso IsDBNull(Row("FirstWeek")) Then
            Description.Append("New burst")
        Else
            ' Add the contract number.
            Description.Append(ParentContractNumber)
            ' Add the media service.
            Description.Append(" " & Row("MediaName") & " burst ")
            ' Add the product name.
            Description.Append("for " & Row("BrandName") & " " & Row("ProductName"))
            ' Add the duration.
            Description.Append(" running for " & Row("InstallWeeks") & " week(s) ")
            ' Add the chain.
            Description.Append("in " & Row("ChainName"))
            ' Add the first week date.
            Description.Append(" from " & CDate(Row("FirstWeek")).ToString("d MMMM yyyy"))
        End If

        Return Description.ToString

    End Function

    Public Sub New _
    (ByVal Parent As OldContract,
    ByVal AddNew As Boolean,
    ByVal Constring As String)

        ' Create a new row if this is not an existing row that the user is modifying.
        If AddNew Then
            Parent.BurstBindingSource.AddNew()
        End If

        ' Update variables.
        _ParentContract = Parent
        AuditLog = _ParentContract.AuditLog
        ConsumingForm = _ParentContract.ConsumingForm
        ConnectionString = Constring
        DataBindingSource = _ParentContract.BurstBindingSource
        NonCompetingBrandTable = DataSet.Tables("BrandFamilyMember")
        _ClientID = _ParentContract.ClientID

        ' Set connection properties for table adapters.
        PeerBurstAdapter.Connection = New SqlClient.SqlConnection(ConnectionString)
        NonCompetingBrandAdapter.Connection = New SqlClient.SqlConnection(ConnectionString)
        CompetingBurstAdapter.Connection = New SqlClient.SqlConnection(ConnectionString)

        ' Create a description for this burst to use in the audit log.
        _RowDescription = GetRowDescription(Row, Parent.ContractNumber)

        InitializeFields()
        CreateTemporaryTables()
        UpdateNonCompetingBrandTable()
        UpdateCompetingMediaTable()
        UpdateUniverseStoreCount()
        UpdateStoreCountAllowingCurrentMediaService()
        UpdatePeerBurstTable()

    End Sub

    Public Sub SaveToDataSet()

        ' Update all columns that do not require an audit log entry.
        Row("ChainID") = _ChainID
        Row("ChainTypeID") = _ChainTypeID
        Row("MediaID") = _MediaID
        Row("MediaAllowsHomesite") = _MediaAllowsHomesite
        Row("MediaAllowsCrossover") = _MediaAllowsCrossover
        Row("LastWeek") = _LastWeek
        Row("Categories") = _Categories
        Row("StorePoolID") = _StorePoolID
        Row("BrandID") = _BrandID
        Row("CreationDate") = Settings.GetServerTime(ConnectionString, ConsumingForm)
        Row("MediaFamilyIDList") = MediaFamilyIDList

        ' Update all columns that require an audit log entry.
        If Not Object.Equals(Row("ChainName"), _ChainName) Then
            AddLog(Row, RowDescription, "ChainName", Row("ChainName").ToString, _ChainName.ToString, AuditLog)
            Row("ChainName") = _ChainName
        End If
        If Not Object.Equals(Row("MediaName"), _MediaName) Then
            AddLog(Row, RowDescription, "MediaName", Row("MediaName").ToString, _MediaName.ToString, AuditLog)
            Row("MediaName") = _MediaName
        End If
        If Not Object.Equals(Row("StorePoolQty"), StorePoolCapacity) Then
            AddLog(Row, RowDescription, "StorePoolQty", Row("StorePoolQty").ToString, StorePoolCapacity.ToString, AuditLog)
            Row("StorePoolQty") = StorePoolCapacity
        End If
        If Not Object.Equals(Row("BrandName"), _BrandName) Then
            AddLog(Row, RowDescription, "BrandName", Row("BrandName").ToString, _BrandName.ToString, AuditLog)
            Row("BrandName") = _BrandName
        End If
        If Not Object.Equals(Row("ProductName"), _ProductName) Then
            AddLog(Row, RowDescription, "ProductName", Row("ProductName").ToString, _ProductName.ToString, AuditLog)
            Row("ProductName") = _ProductName
        End If
        If Not Object.Equals(Row("FirstWeek"), _FirstWeek) Then
            If Not Row.RowState = DataRowState.Detached Then
                AddLog(Row, RowDescription, "FirstWeek", CDate(Row("FirstWeek")).ToString("d MMMM yyyy"), _FirstWeek.ToString("d MMMM yyyy"), AuditLog)
            End If
            Row("FirstWeek") = _FirstWeek
        End If
        If Not Object.Equals(Row("InstallWeeks"), _InstallWeeks) Then
            AddLog(Row, RowDescription, "InstallWeeks", Row("InstallWeeks").ToString, _InstallWeeks.ToString, AuditLog)
            Row("InstallWeeks") = _InstallWeeks
        End If
        If Not Object.Equals(Row("InstallStoreQty"), _InstallStoreQty) Then
            AddLog(Row, RowDescription, "InstallStoreQty", Row("InstallStoreQty").ToString, _InstallStoreQty.ToString, AuditLog)
            Row("InstallStoreQty") = _InstallStoreQty
        End If
        If Not Object.Equals(Row("AdsPerInstallation"), _AdsPerInstallation) Then
            AddLog(Row, RowDescription, "AdsPerInstallation", Row("AdsPerInstallation").ToString, _AdsPerInstallation.ToString, AuditLog)
            Row("AdsPerInstallation") = _AdsPerInstallation
        End If
        If Not Object.Equals(Row("AdsPerCrossover"), _AdsPerCrossover) Then
            AddLog(Row, RowDescription, "AdsPerCrossover", Row("AdsPerCrossover").ToString, _AdsPerCrossover.ToString, AuditLog)
            Row("AdsPerCrossover") = _AdsPerCrossover
        End If
        If Not Object.Equals(Row("AdsPerShelfTalk"), _AdsPerShelfTalk) Then
            AddLog(Row, RowDescription, "AdsPerShelfTalk", Row("AdsPerShelfTalk").ToString, _AdsPerShelfTalk.ToString, AuditLog)
            Row("AdsPerShelfTalk") = _AdsPerShelfTalk
        End If
        If Not Object.Equals(Row("BillableStoreQty"), _BillableStoreQty) Then
            AddLog(Row, RowDescription, "BillableStoreQty", Row("BillableStoreQty").ToString, _BillableStoreQty.ToString, AuditLog)
            Row("BillableStoreQty") = _BillableStoreQty
        End If
        If Not Object.Equals(Row("RentalRate"), _RentalRate) Then
            AddLog(Row, RowDescription, "RentalRate", CDec(Row("RentalRate")).ToString("c"), _RentalRate.ToString("c"), AuditLog)
            Row("RentalRate") = _RentalRate
        End If
        If Not Object.Equals(Row("BillableWeeks"), _BillableWeeks) Then
            AddLog(Row, RowDescription, "BillableWeeks", Row("BillableWeeks").ToString, _BillableWeeks.ToString, AuditLog)
            Row("BillableWeeks") = _BillableWeeks
        End If
        If Not Object.Equals(Row("Discount"), _Discount) Then
            AddLog(Row, RowDescription, "Discount", CDec(Row("Discount")).ToString("#.00") & "%", _Discount.ToString("#.00") & "%", AuditLog)
            Row("Discount") = _Discount
        End If
        If Not Object.Equals(Row("CrossoverQty"), _CrossoverQty) Then
            AddLog(Row, RowDescription, "CrossoverQty", Row("CrossoverQty").ToString, _CrossoverQty.ToString, AuditLog)
            Row("CrossoverQty") = _CrossoverQty
        End If
        If Not Object.Equals(Row("InstallationInstructions"), _InstallationInstructions) Then
            AddLog(Row, RowDescription, "InstallationInstructions", Row("InstallationInstructions").ToString, _InstallationInstructions.ToString, AuditLog)
            Row("InstallationInstructions") = _InstallationInstructions
        End If
        If Not Object.Equals(Row("StoreListConfirmed"), _StoreListConfirmed) Then
            AddLog(Row, RowDescription, "StoreListConfirmed", Row("StoreListConfirmed").ToString, _StoreListConfirmed.ToString, AuditLog)
            Row("StoreListConfirmed") = _StoreListConfirmed
        End If
        If Not Object.Equals(Row("InstallAtHomesite"), _InstallAtHomesite) Then
            AddLog(Row, RowDescription, "InstallAtHomesite", Row("InstallAtHomesite").ToString, _InstallAtHomesite.ToString, AuditLog)
            Row("InstallAtHomesite") = _InstallAtHomesite
        End If

        If Not Object.Equals(Row("ApplyInstructionsAcrossAllBursts"), _ApplyInstructionsAcrossAllBursts) Then
            AddLog(Row, RowDescription, "ApplyInstructionsAcrossAllBursts", Row("ApplyInstructionsAcrossAllBursts").ToString, _ApplyInstructionsAcrossAllBursts.ToString, AuditLog)
            Row("ApplyInstructionsAcrossAllBursts") = _ApplyInstructionsAcrossAllBursts
        End If

        If Not Object.Equals(Row("InstallRegardlessOfStock"), _InstallRegardlessOfStock) Then
            AddLog(Row, RowDescription, "InstallRegardlessOfStock", Row("InstallRegardlessOfStock").ToString, _InstallRegardlessOfStock.ToString, AuditLog)
            Row("InstallRegardlessOfStock") = _InstallRegardlessOfStock
        End If

        ' Save the related tables.
        SaveLoadingFees()
        SaveCategories()
        SaveInstallationDates()
        SavePcaStatusses()
        UpdateCategoriesColumn()
        SaveStorePools()
        SavePeerBursts()
        SaveApplyInstructionsOnAllBursts()
        SaveApplyDatesOnAllBursts()
        SaveStoreList()

        If Row.RowState = DataRowState.Detached Then
            ' This is a new row being created, add only one log entry for the creation of the object (as opposed to
            ' adding entries for every modified property of this object).
            _RowDescription = GetRowDescription(Row, _ParentContract.ContractNumber)
            LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, "Burst", RowDescription, "Created")
            ' Add this new row to the table.
            Table.Rows.Add(Row)
        End If

        ' Remember the current item in the binding source list.
        Dim CurrentItem As Object = DataBindingSource.Current
        ' Commit changes to the dataset.
        DataBindingSource.EndEdit()     ' This line changes the binding source's current item.
        ' Restore the current item in the binding source list.
        DataBindingSource.Position = DataBindingSource.IndexOf(CurrentItem)

        ' Flag the parent contract as dirty.
        _ParentContract.IsDirty = True

        ' Update related info in the parent object.
        _ParentContract.UpdateBurstInfoColumns()

        ' Refresh the error list of the parent contract.
        ParentContract.RefreshAllErrors()

    End Sub

    Public Sub RejectChanges()

        ' Remember the current item in the binding source list.
        Dim CurrentItem As Object = DataBindingSource.Current
        ' Commit changes to the dataset.
        DataBindingSource.CancelEdit()    ' This line changes the binding source's current item.
        ' Restore the current item in the binding source list.
        DataBindingSource.Position = DataBindingSource.IndexOf(CurrentItem)

    End Sub

    Public Sub AddLoadingFees(ByVal NewLoadingFees As List(Of DataRow))
        ' Add new loading fees selected by the user to this burst's loading fees list.

        For Each NewLoadingFee As DataRow In NewLoadingFees
            Dim NewRow As DataRow = _BurstLoadingFeeTable.NewRow
            With NewRow
                .Item("BurstID") = Row("BurstID")
                .Item("LoadingFeeID") = NewLoadingFee("LoadingFeeID")
                .Item("Percentage") = NewLoadingFee("DefaultPercentage")
                .Item("LoadingFeeName") = NewLoadingFee("LoadingFeeName")
                .Item("LoadingFeeAmount") = NewLoadingFee("DefaultPercentage") / 100 * _BillableStoreQty * _BillableWeeks * _RentalRate
            End With
            _BurstLoadingFeeTable.Rows.Add(NewRow)
        Next

    End Sub

    Public Sub RemoveGridRows(ByVal Grid As DataGridView)

        ' Stop if no rows were selected.
        If Grid.SelectedRows.Count < 1 Then
            CType(Grid.TopLevelControl, LiquidShell.BaseForm).ShowMessage _
            ("Please select at least one row before using the 'Remove' button.", "Grated Cheese Paradigm")
            Exit Sub
        End If

        ' Confirm the remove action.
        If LiquidShell.LiquidAgent.ActionConfirmed(Grid, "Remove") = False Then
            Exit Sub
        End If

        ' Delete the datarows corresponding to all the selected grid rows.
        For Each SelectedGridRow As DataGridViewRow In Grid.SelectedRows
            Dim RowToRemove As DataRow = CType(SelectedGridRow.DataBoundItem, DataRowView).Row
            ' Remove the category.
            RowToRemove.Delete()
        Next

    End Sub

    Public Sub RemoveCategories(ByVal Grid As DataGridView)
        RemoveGridRows(Grid)
        UpdateCategoriesColumn()
        UpdatePeerBurstTable()
    End Sub
    Public Sub RemoveInstallationDays(ByVal Grid As DataGridView)
        RemoveGridRows(Grid)
    End Sub

    Public Sub AddAdditionalCategories(ByVal CategoryList As List(Of DataRow))
        ' Add categories that must be blocked like a homesite but where ads will not be installed in stores.

        For Each Category As DataRow In CategoryList
            Dim NewCategory As DataRow = _BurstCategoryTable.NewRow
            NewCategory("BurstID") = Row("BurstID")
            NewCategory("CategoryID") = Category("CategoryID")
            NewCategory("Priority") = -1
            NewCategory("CategoryName") = Category("CategoryName")
            _BurstCategoryTable.Rows.Add(NewCategory)
        Next

        UpdateCategoriesColumn()
        UpdatePeerBurstTable()

    End Sub

    Public Sub AddCrossoverPreferences(ByVal CategoryList As List(Of DataRow))
        ' Add categories to the list of crossover preferences.

        For Each Category As DataRow In CategoryList
            Dim NewCategory As DataRow = _BurstCategoryTable.NewRow
            NewCategory("BurstID") = Row("BurstID")
            NewCategory("CategoryID") = Category("CategoryID")
            NewCategory("Priority") = _CrossoverPreferenceView.Count + 1
            NewCategory("CategoryName") = Category("CategoryName")
            _BurstCategoryTable.Rows.Add(NewCategory)
        Next

        UpdateCategoriesColumn()

    End Sub


    Public Sub InstallationDays(ByVal InstallationDayList As List(Of DataRow))
        ' Add categories to the list of crossover preferences.

        For Each InstallationDay As DataRow In InstallationDayList
            Dim NewInstallationDay As DataRow = _BurstInstallationDayTable.NewRow
            NewInstallationDay("InstallationDayID") = InstallationDay.Item(0)
            NewInstallationDay("BurstId") = Row("BurstID").ToString
            NewInstallationDay("InstallationDayName") = InstallationDay.Item(1)
            ' NewCategory("CategoryName") = Category("CategoryName")
            _BurstInstallationDayTable.Rows.Add(NewInstallationDay)
        Next

        UpdateBurstDayColumn()

    End Sub

    Public Enum PriorityChangeValue
        Up
        Down
    End Enum

    Public Sub ChangeCrossoverPriority(ByVal CrossoverGrid As DataGridView, ByVal ChangeValue As PriorityChangeValue)
        ' Increase the priority of the selected crossover category.

        ' Exit if more than one category is selected, or if no categories are selected.
        Dim ErrorMessage As String = String.Empty
        If CrossoverGrid.SelectedRows.Count > 1 Then
            ErrorMessage = "Please select only one category before changing the priority."
        ElseIf CrossoverGrid.SelectedRows.Count < 1 Then
            ErrorMessage = "Please select a category before changing the priority."
        End If
        If String.IsNullOrEmpty(ErrorMessage) = False Then
            ConsumingForm.ShowMessage(ErrorMessage, "Sneeze If You're Happy")
            Exit Sub
        End If

        ' Get the currently selected crossover category row.
        Dim SelectedCrossover As DataRow = CType(CrossoverGrid.SelectedRows(0).DataBoundItem, DataRowView).Row

        ' Get the old and new values for the priority column.
        Dim OldPriority As Integer = SelectedCrossover("Priority")
        Dim NewPriority As Integer
        If ChangeValue = PriorityChangeValue.Up Then
            NewPriority = OldPriority - 1
        Else
            NewPriority = OldPriority + 1
        End If

        ' Stop if user is trying to increase a priority to less than one or to decrease the priorty to greater than the total number of crossovers.
        If NewPriority < 1 Or NewPriority > _CrossoverPreferenceView.Count Then
            Exit Sub
        End If

        ' Get the related crossover that's going to exchange places with the selected crossover.
        Dim RelatedCrossoverToChange As DataRow = _BurstCategoryTable.Select("Priority = " & NewPriority.ToString)(0)

        ' Change the priorities of the selected crossover, and also the crossover directly before or after the selected one.
        SelectedCrossover("Priority") = NewPriority
        RelatedCrossoverToChange("Priority") = OldPriority

        UpdateCategoriesColumn()

    End Sub

    Public Sub AddRelatives(ByVal SelectedPeers As List(Of DataRow))
        ' Set the StorePoolIDs of all selected peer bursts in the grid (and their relatives) to that of the current burst.

        ' Create a list of StorePoolIDs that need to be changed from the selected rows in the grid.
        Dim StorePoolIDList As New List(Of Guid)
        For Each SelectedPeer As DataRow In SelectedPeers
            ' Get this peer's StorePoolID.
            Dim SelectedPeerStorePoolID As Guid = SelectedPeer("StorePoolID")
            ' Add the StorePoolID to the list.
            If Not StorePoolIDList.Contains(SelectedPeerStorePoolID) Then
                StorePoolIDList.Add(SelectedPeerStorePoolID)
            End If
        Next

        ' Change all peers that use a store pool ID from the list to use this burst's store pool ID.
        For Each PeerBurst As DataRow In PeerBurstTable.Rows
            If StorePoolIDList.Contains(PeerBurst("StorePoolID")) Then
                ' Flag the burst's original store pool for deletion.
                Dim StorePoolToDelete As DataRow = _StorePoolTable.Rows.Find(PeerBurst("StorePoolID"))
                If IsNothing(StorePoolToDelete) Then
                    ' The store pool row for this peer burst isn't loaded into the table. Create a row for it so that it can be deleted.
                    StorePoolToDelete = _StorePoolTable.NewRow()
                    StorePoolToDelete("StorePoolID") = PeerBurst("StorePoolID")
                    _StorePoolTable.Rows.Add(StorePoolToDelete)
                    StorePoolToDelete.AcceptChanges()
                End If
                ' Change the peer burst's store pool ID to that of the current burst's StorePoolID.
                PeerBurst("StorePoolID") = _StorePoolID
                ' Delete the old store pool.
                If Not StorePoolToDelete.RowState = DataRowState.Deleted Then
                    StorePoolToDelete.Delete()
                End If
            End If
        Next

    End Sub

    Public Sub RemoveAllRelatives()

        ' Remember the original store pool id.
        Dim OldStorePoolID As Guid = StorePoolID

        ' Make a new store pool for the current burst so that it leaves the group of bursts that were sharing a pool.
        _StorePoolID = GetNewStorePoolID(_InstallStoreQty)

        ' Now that the group of bursts sharing a store pool has decreased by one, there will possibly be a new
        ' maximum store pool capacity. Check whether the capacity of the old store pool is still less than this.
        Dim MaxPoolCapacity As Integer = 0

        ' Check all the old relative bursts to get the maximum store pool capacity.
        For i As Integer = 0 To PeerBurstTable.Rows.Count - 1
            Dim PeerBurst As DataRow = PeerBurstTable.Rows(i)
            ' Exclude the current burst from the calculation.
            If Not Object.Equals(Row("BurstID"), PeerBurst("BurstID")) Then
                If Object.Equals(OldStorePoolID, PeerBurst("StorePoolID")) Then
                    MaxPoolCapacity += PeerBurst("InstallStoreQty")
                End If
            End If
        Next

        ' Update the RelativeBurst dataview.
        _RelatedBurstView.RowFilter = RelativeBurstViewFilter()

        ' Check the store pool capacity.
        Dim OldStorePool As DataRow = _StorePoolTable.Rows.Find(OldStorePoolID)
        Dim Capacity As Integer = OldStorePool("StorePoolQty")
        If Capacity > MaxPoolCapacity Then
            ' The store pool capacity needs to be reduced to prevent waste.
            OldStorePool("StorePoolQty") = MaxPoolCapacity
        End If

    End Sub

    Public Sub UpdatePeerRelativeFilter(ByVal PeerGrid As DataGridView)

        ' Exit if _PeerRelativeBurstView has not yet been initialized.
        If IsNothing(_PeerRelativeBurstView) Then
            Exit Sub
        End If

        ' Create a filter with no possible matches if nothing is selected.
        If PeerGrid.SelectedRows.Count = 0 Then
            _PeerRelativeBurstView.RowFilter = "ContractNumber = 'MY GOAT HAS THE FLU'"
            Exit Sub
        End If

        ' Build a filter based on the StorePoolIDs of selected peers.
        Dim StorePoolIDFilterBuilder As New System.Text.StringBuilder

        ' Add statements to match StorePoolIDs.
        For Each GridRow As DataGridViewRow In PeerGrid.SelectedRows
            Dim Peer As DataRow = CType(GridRow.DataBoundItem, DataRowView).Row
            ' Check if this StorePoolID value has already been added to the string builder.
            If Not StorePoolIDFilterBuilder.ToString.Contains(Peer("StorePoolID").ToString) Then
                ' This store pool ID has not yet been added to the string builder. Add it now.
                If StorePoolIDFilterBuilder.Length = 0 Then
                    ' The filter is empty. Start with an open bracket to begin the list of statements.
                    StorePoolIDFilterBuilder.Append("(")
                ElseIf StorePoolIDFilterBuilder.Length > 0 Then
                    ' The filter isn't empty - at least one statement has already been added.
                    StorePoolIDFilterBuilder.Append(" OR ")
                End If
                StorePoolIDFilterBuilder.Append("StorePoolID = '" & Peer("StorePoolID").ToString & "'")
            End If
        Next
        ' Finally, add a closing bracket.
        StorePoolIDFilterBuilder.Append(")")

        ' Build a filter based on the BurstIDs of selected peers.
        Dim BurstIDFilterBuilder As New System.Text.StringBuilder

        ' Add statements to exclude current peers.
        For Each GridRow As DataGridViewRow In PeerGrid.SelectedRows
            Dim Peer As DataRow = CType(GridRow.DataBoundItem, DataRowView).Row
            If BurstIDFilterBuilder.Length = 0 Then
                ' The filter is empty. Start with an open bracket to begin the list of statements.
                BurstIDFilterBuilder.Append("(")
            ElseIf BurstIDFilterBuilder.Length > 0 Then
                ' The filter isn't empty - at least one statement has already been added.
                BurstIDFilterBuilder.Append(" AND ")
            End If
            BurstIDFilterBuilder.Append("BurstID <> '" & Peer("BurstID").ToString & "'")
        Next
        ' Finally, add a closing bracket.
        BurstIDFilterBuilder.Append(")")

        ' Apply the filter to the dataview.
        _PeerRelativeBurstView.RowFilter = StorePoolIDFilterBuilder.ToString & " AND " & BurstIDFilterBuilder.ToString

    End Sub

    Public Sub SetPoolStoreCapacityToMin()
        ' Set the StorePoolQty to the minimum possible capacity.
        StorePoolCapacity = MinStorePoolCapacity
    End Sub

    Public Sub SetPoolStoreCapacityToMax()
        ' Set the StorePoolQty to the minimum possible capacity.
        StorePoolCapacity = MaxStorePoolCapacity
    End Sub

    Public Sub UpdateStoreTable()

        ' An adapter to fetch the data.
        Dim StoreAdapter As New DataSetContractTableAdapters.StoreTableAdapter

        ' Create a SQL connection to use.
        Dim SqlCon As New SqlClient.SqlConnection(ConnectionString)

        ' Set the connection properties.
        StoreAdapter.Connection = SqlCon

        ' Create a table to return.
        Dim ReturnTable As New DataSetContract.StoreDataTable

        ' Try and fill the table with store data.
        Try
            StoreAdapter.Fill(ReturnTable, ChainID, MediaID, Row("BurstID"), Row("ContractID"), StorePoolID)
        Catch ex As Exception
            ConsumingForm.ShowMessage("Error!" & vbCrLf & vbCrLf & LiquidShell.LiquidAgent.GetErrorMessage(ex),
                                      "Twisted an ankle while felling a tree.", MessageBoxIcon.Error)
        Finally
            SqlCon.Dispose()
            StoreAdapter.Dispose()
        End Try

        _StoreTable = ReturnTable

    End Sub

#End Region

#Region "Private Methods"

    Private Sub UpdateStoreCountAllowingCurrentMediaService()

        ' A variable to hold potential errors when retrieving data.
        Dim ErrorMessage As String = String.Empty

        ' The SQL command text.
        Dim SelectStatement As String = "SELECT COUNT(Store.Store.StoreID) AS StoreCount " _
        & "FROM Store.Chain INNER JOIN " _
        & " dbo.udfGetChainsByGroup(" & _ChainID.ToString & ") GCG on GCG.chainid = Store.Chain.ChainID INNER JOIN " _
        & "Store.Region ON Store.Chain.ChainID = Store.Region.ChainID INNER JOIN " _
        & "Store.Store ON Store.Region.RegionID = Store.Store.RegionID INNER JOIN " _
        & "Store.StoreMedia ON Store.Store.StoreID = Store.StoreMedia.StoreID " _
        & "WHERE (Store.Chain.Dormant = 0) AND (Store.Store.Dormant = 0)  " _
        & " AND (Store.StoreMedia.MediaID = " & _MediaID.ToString & ")"
        '& "WHERE (Store.Chain.Dormant = 0) AND (Store.Store.Dormant = 0) AND (Store.Chain.ChainID = " _
        '& _ChainID.ToString & ") AND (Store.StoreMedia.MediaID = " & _MediaID.ToString & ")"

        ' Try and fetch the data.
        Dim Result As Integer = CInt(LiquidShell.LiquidAgent.GetSqlScalarValue(ConnectionString, SelectStatement, ErrorMessage))

        ' Exit if any errors occured.
        If String.IsNullOrEmpty(ErrorMessage) = False Then
            ConsumingForm.ShowMessage(ErrorMessage, "Dropped the Ice Cream", MessageBoxIcon.Error)
            Exit Sub
        End If

        ' Set the value.
        _StoresAllowingCurrentMediaService = Result

    End Sub

    Private Sub UpdateUniverseStoreCount()

        ' A variable to hold potential errors when retrieving data.
        Dim ErrorMessage As String = String.Empty

        ' The SQL command text.
        Dim SelectStatement As String = "SELECT COUNT(Store.Store.StoreID) AS StoreCount " _
        & "FROM Store.Chain INNER JOIN " _
        & " dbo.udfGetChainsByGroup(" & _ChainID.ToString & ") GCG on GCG.chainid = Store.Chain.ChainID INNER JOIN " _
        & "Store.Region ON Store.Chain.ChainID = Store.Region.ChainID INNER JOIN " _
        & "Store.Store ON Store.Region.RegionID = Store.Store.RegionID " _
        & "WHERE (Store.Chain.Dormant = 0) AND (Store.Store.Dormant = 0) "
        ' & "WHERE (Store.Chain.Dormant = 0) AND (Store.Store.Dormant = 0) AND (Store.Chain.ChainID = " _
        '& _ChainID.ToString & ")"

        ' Try and fetch the data.
        Dim Result As Integer = CInt(LiquidShell.LiquidAgent.GetSqlScalarValue(ConnectionString, SelectStatement, ErrorMessage))

        ' Exit if any errors occured.
        If String.IsNullOrEmpty(ErrorMessage) = False Then
            ConsumingForm.ShowMessage(ErrorMessage, "Blew a Gasket", MessageBoxIcon.Error)
            Exit Sub
        End If

        ' Set the value.
        _StoresInUniverse = Result

    End Sub

    Private Sub UpdateBurstsWithHigherStoreClaimPriorityAwaitingStoreListConfirmation()

        ' Refresh competing burst data.
        UpdateCompetingBurstTable()

        ' A list to hold the bursts.
        Dim BurstList As New List(Of DataRow)

        ' The date on which this burst entered the priority queue. It is either the contract signature date, or
        ' the burst creation date (whichever came last).
        Dim ClaimDate As Date = New Date(2100, 1, 1)
        If ParentContract.Signed Then
            ' The default store claim date would be the date the contract was signed.
            ClaimDate = ParentContract.SignDate
            If _CreationDate > ClaimDate Then
                ' This burst was created AFTER the contract was signed, so its store claim date will be the date
                ' on which the burst was created.
                ClaimDate = _CreationDate
            End If
        End If

        ' Check store claim dates of competing bursts.
        For Each CompetingBurst As DataSetContract.CompetingBurstRow In CompetingBurstTable.Rows
            ' If this burst's store list is confirmed then it can be excluded.
            If CompetingBurst("StoreListConfirmed") = False Then
                ' The default store claim date would be the date the contract was signed.
                Dim CompetingClaimDate As Date = CompetingBurst.SignDate
                If CompetingBurst.CreationDate > CompetingClaimDate Then
                    ' This burst was created AFTER the contract was signed, so its store claim date will be the date
                    ' on which the burst was created.
                    CompetingClaimDate = CompetingBurst.CreationDate
                End If
                ' Compare the competing burst's claim date with this burst's claim date.
                If CompetingClaimDate < ClaimDate Then
                    ' The claim date of the competing burst is earlier than that of the current burst, so the competing
                    ' burst has a higher store claim priority, so it gets to choose its stores first.
                    If Not BurstList.Contains(CompetingBurst) Then
                        BurstList.Add(CompetingBurst)
                    End If
                End If
            End If
        Next

        _BurstsWithHigherStoreClaimPriorityAwaitingStoreListConfirmation = BurstList

    End Sub

    Private Sub UpdateConflictingContracts()
        ' Updates the '_ConflictingContracts' string, which is designed to contain a list of contracts whose
        ' store quantities, when added to that of the current burst, would exceed the maximum quantity available
        ' in the media gap.

        ' If the InstallStoreQty is zero the burst cannot be in conflict with any other burst.
        If InstallStoreQty = 0 Then
            _ConflictingContracts = String.Empty
            Exit Sub
        End If

        ' Update the CompetingBursts table.
        UpdateCompetingBurstTable()

        ' Update the PeerBursts table if it doesn't yet exist.
        If IsNothing(PeerBurstTable) Then
            ' Populate the table with any bursts that compete with this one.
            UpdatePeerBurstTable()
        End If

        ' Check if the competing burst table contains any rows.
        Dim CompetingBurstCount As Integer = 0
        Dim PeerBurstCount As Integer = 0
        If IsNothing(CompetingBurstTable) = False Then
            CompetingBurstCount = CompetingBurstTable.Rows.Count
        End If
        If IsNothing(PeerBurstTable) = False Then
            PeerBurstCount = PeerBurstTable.Rows.Count
        End If

        If CompetingBurstCount = 0 AndAlso PeerBurstCount = 0 Then
            ' No competing bursts or peer bursts exist, hence, no conflicts.
            _ConflictingContracts = String.Empty
            Exit Sub
        End If

        ' A variable to hold the list of conflicting contract numbers.
        Dim ConflictingContractList As New List(Of String)

        ' A variable to hold the value of the current week.
        Dim Week As Date = FirstWeek

        While Week <= LastWeek

            ' Create a filter to get the competing and peer bursts that run in this week.
            Dim Filter As String = "FirstWeek <= '" & Week.ToString & "'" _
            & " AND LastWeek >= '" & Week.ToString & "'"

            ' Get all competing and peer bursts that run in this week.
            Dim CompetingBursts() As DataRow = CompetingBurstTable.Select(Filter)
            Dim PeerBursts() As DataRow = PeerBurstTable.Select(Filter)

            ' Get the sum of the capacities of all store pools used in this week.
            Dim TotalStoresUsed As Integer = 0
            Dim Pools As New List(Of Guid)
            Dim ThisWeeksContracts As New List(Of String)

            ' Check all competing bursts.
            For Each CompetingBurst As DataRow In CompetingBursts
                If Not Pools.Contains(CompetingBurst("StorePoolID")) Then
                    ' This burst's store pool hasn't been added to the pool list. Add it now.
                    Pools.Add(CompetingBurst("StorePoolID"))
                    ' Add store pool capacity to the count total.
                    TotalStoresUsed += CompetingBurst("StorePoolQty")
                End If
                If Not ThisWeeksContracts.Contains(CompetingBurst("ContractNumber")) Then
                    ' Add this burst's contract number to the list of this week's conflicting contracts.
                    ThisWeeksContracts.Add(CompetingBurst("ContractNumber"))
                End If
            Next

            ' Check all peer bursts.
            For Each PeerBurst As DataRow In PeerBursts





                Dim BurstIdentifier As String = CType(PeerBurst("BurstID"), Guid).ToString
                Dim CurrentBurstID As String = CType(Row("BurstID"), Guid).ToString
                Dim SameBurst As Boolean = String.Equals(BurstIdentifier, CurrentBurstID)






                ' A variable to remember whether this burst needs to be checked or not.
                Dim CheckThisBurst As Boolean = False

                ' Only check peer bursts of signed contracts, or sibling bursts of the same contract. Do not
                ' check peer bursts of a different contract which isn't signed.
                If PeerBurst("Signed") Then
                    CheckThisBurst = True
                Else
                    If String.Compare(PeerBurst("ContractNumber").ToString, ParentContract.ContractNumber) = 0 Then
                        CheckThisBurst = True
                    End If
                End If

                If CheckThisBurst Then
                    If Not Object.Equals(PeerBurst("StorePoolID"), _StorePoolID) Then
                        ' This peer doesn't share a store pool with this burst. Check it.
                        If Not Pools.Contains(PeerBurst("StorePoolID")) Then
                            ' This burst's store pool hasn't been added to the pool list. Add it now.
                            Pools.Add(PeerBurst("StorePoolID"))
                            ' Add store pool capacity to the count total.
                            TotalStoresUsed += PeerBurst("StorePoolQty")
                        End If
                        If Not ThisWeeksContracts.Contains(PeerBurst("ContractNumber")) Then
                            ' Add this burst's contract number to the list of this week's conflicting contracts.
                            ThisWeeksContracts.Add(PeerBurst("ContractNumber"))
                        End If
                    End If
                End If

            Next

            ' Check stores desired versus store available.
            If StorePoolCapacity + TotalStoresUsed > _StoresAllowingCurrentMediaService Then
                ' This burst won't fit into the media gap. Add this week's conflicting contracts to the list.
                For Each ContractNumber As String In ThisWeeksContracts
                    If Not ConflictingContractList.Contains(ContractNumber) Then
                        ConflictingContractList.Add(ContractNumber)
                    End If
                Next
            End If

            ' Add another week to our week variable so that the next week can be tested.
            Week = Week.AddDays(7)

        End While

        ' Flatten the list into a string.
        Dim ReturnString As New System.Text.StringBuilder
        For Each ContractNumber As String In ConflictingContractList
            If ReturnString.Length > 0 Then
                ReturnString.AppendLine()
            End If
            ReturnString.Append(ContractNumber)
        Next

        _ConflictingContracts = ReturnString.ToString

    End Sub

    Private Sub UpdateCompetingBurstTable()
        ' A burst in the same space as this burst but with a competing brand will be listed as a competing
        ' burst under the following conditions:
        '   - If it belongs to the same contract (sibling)
        '   - If it belongs to a different contract which is also signed (a burst in a contract that is not
        '     signed, other than the current contract, will not be deemed to be a competing burst).

        ' Stop if criteria isn't met.
        If _InstallWeeks < 1 Then
            ' If the burst doesn't run for any number of weeks then it'll have no peers.
            Exit Sub
        End If

        ' Stop if all required data isn't available yet.
        If String.Compare(_ChainName, "Select...") = 0 Then
            ' A chain hasn't been selected. Exit.
            Exit Sub
        ElseIf String.Compare(_MediaName, "Select...") = 0 Then
            ' A media service hasn't been selected. Exit.
            Exit Sub
        ElseIf String.Compare(_BrandName, "Select...") = 0 Then
            ' A brand hasn't been selected. Exit.
            Exit Sub
        ElseIf BurstCategoryTable.Select("Priority < 1").Length = 0 Then
            ' A homesite or additional categories have not been selected. Exit.
            Exit Sub
        End If

        ' Build a list of categories.
        Dim CategoryIDList As New System.Text.StringBuilder
        For Each Category As DataRow In BurstCategoryTable.Select("Priority < 1")
            ' Cool, the priority is less than one, meaning this category is not a crossover.
            ' Now we need to check if this is a homesite AND the user does not want ads to be installed
            ' at the homesite. If that's the case, this category must be excluded.
            Dim IncludeThisCategory As Boolean = True
            If Category("Priority") = 0 AndAlso InstallAtHomesite = False Then
                ' This category is the homesite AND the user does not want ads installed here.
                IncludeThisCategory = False
            End If
            If IncludeThisCategory Then
                ' This category is not a crossover and is not a homesite with the "Install at Homesite" option
                ' set to "False". Add it.
                If CategoryIDList.Length > 0 Then
                    ' The list is not empty, add a comma.
                    CategoryIDList.Append(",")
                End If
                CategoryIDList.Append(Category("CategoryID"))
            End If
        Next

        ' Dispose the old table if it exists.
        If IsNothing(CompetingBurstTable) = False Then
            CompetingBurstTable.Dispose()
        End If

        ' Get a table of competing bursts from the database.
        Dim CategoryIDListString As String = CategoryIDList.ToString
        CompetingBurstTable = CompetingBurstAdapter.GetData _
        (_FirstWeek, _LastWeek, _ChainID, _BrandID, CategoryIDListString, MediaFamilyIDList, Row("BurstID"))

        ' If any bursts have been deleted from memory, delete them from the competing burst table too.
        Dim DeletedBursts As DataTable = Row.Table.GetChanges(DataRowState.Deleted)
        If Not IsNothing(DeletedBursts) Then
            DeletedBursts.RejectChanges()
            For Each DeletedBurst As DataRow In DeletedBursts.Rows
                Dim DeletedBurstID As Guid = DeletedBurst("BurstID")
                Dim CompetingBurstToDelete As DataRow = CompetingBurstTable.Rows.Find(DeletedBurstID)
                If Not IsNothing(CompetingBurstToDelete) Then
                    CompetingBurstToDelete.Delete()
                End If
            Next
            DeletedBursts.Dispose()
        End If

        ' Get competing burst information from the current contract that might not yet have been saved to the database,
        ' or that might have been modified since the last database read.
        For Each SiblingBurst As DataRow In _ParentContract.Row.GetChildRows("FK_Burst_Contract")
            ' "Sibling Bursts" are bursts belonging to the same contract.
            If Not SiblingBurst.RowState = DataRowState.Deleted Then
                If CompetingBurstTable.Rows.Contains(SiblingBurst("BurstID")) Then
                    ' The CompetingBurst table already contains this burst. But it may have been modified since it was
                    ' read from the database. Let's update it's properties, just in case it has.
                    Dim CompetingBurst As DataRow = CompetingBurstTable.Rows.Find(SiblingBurst("BurstID"))
                    CompetingBurst("StorePoolID") = SiblingBurst("StorePoolID")
                    CompetingBurst("StorePoolQty") = SiblingBurst("StorePoolQty")
                    CompetingBurst("BrandName") = SiblingBurst("BrandName")
                    CompetingBurst("InstallStoreQty") = SiblingBurst("InstallStoreQty")
                    CompetingBurst("FirstWeek") = SiblingBurst("FirstWeek")
                    CompetingBurst("LastWeek") = SiblingBurst("LastWeek")
                    CompetingBurst("InstallWeeks") = SiblingBurst("InstallWeeks")
                    CompetingBurst("CreationDate") = SiblingBurst("CreationDate")
                    CompetingBurst("SignDate") = ParentContract.SignDate
                    CompetingBurst("StoreListConfirmed") = SiblingBurst("StoreListConfirmed")
                Else
                    ' The CompetingBurst table doesn't contain this burst. Check if this burst competes with the current
                    ' burst and add it to the table if it does.
                    If Not Object.ReferenceEquals(SiblingBurst, Row) Then
                        ' This sibling burst is not the current burst.
                        If SiblingBurst("ChainID") = _ChainID Then
                            ' The sibling's chain ID is the same. This sibling could be a competing burst.
                            If SiblingBurst("FirstWeek") <= _LastWeek AndAlso SiblingBurst("LastWeek") >= _FirstWeek Then
                                ' The sibling's date range overlaps with the current burst's date range. It could be a competing.
                                If SiblingBurst.GetChildRows("Burst_BurstCategory").Length > 0 Then
                                    ' The sibling has categories. Cool.

                                    Dim CategoryMatch As Boolean = False
                                    ' Check if any of the sibling categories match any category of the current burst.
                                    For Each SiblingBurstCategory As DataRow In SiblingBurst.GetChildRows("Burst_BurstCategory")
                                        If BurstCategoryMatch(SiblingBurstCategory, SiblingBurst("InstallAtHomesite")) Then
                                            CategoryMatch = True
                                        End If
                                    Next

                                    If CategoryMatch Then
                                        ' There is at least one matching category in the sibling burst and the current burst.
                                        If Not NonCompetingBrandTable.Rows.Contains(SiblingBurst("BrandID")) Then
                                            ' The sibling burst uses a brand that competes with the current burst's brand. It could be a competing burst.
                                            If CompetingMediaTable.Rows.Contains(SiblingBurst("MediaID")) Then
                                                ' The sibling burst uses a media service in the same family as the current burst's media service.
                                                ' This sibling burst is a competitor of the current burst. Add it to the CompetingBurst table.
                                                Dim NewCompetitor As DataRow = CompetingBurstTable.NewRow
                                                With NewCompetitor
                                                    .Item("BurstID") = SiblingBurst("BurstID")
                                                    .Item("StorePoolID") = SiblingBurst("StorePoolID")
                                                    .Item("StorePoolQty") = SiblingBurst("StorePoolQty")
                                                    .Item("ContractNumber") = _ParentContract.ContractNumber
                                                    .Item("BrandName") = SiblingBurst("BrandName")
                                                    .Item("InstallStoreQty") = SiblingBurst("InstallStoreQty")
                                                    .Item("FirstWeek") = SiblingBurst("FirstWeek")
                                                    .Item("LastWeek") = SiblingBurst("LastWeek")
                                                    .Item("InstallWeeks") = SiblingBurst("InstallWeeks")
                                                    .Item("CreationDate") = SiblingBurst("CreationDate")
                                                    .Item("SignDate") = ParentContract.SignDate
                                                    .Item("StoreListConfirmed") = SiblingBurst("StoreListConfirmed")
                                                End With
                                                CompetingBurstTable.Rows.Add(NewCompetitor)
                                            End If
                                        End If
                                    End If

                                End If
                            End If
                        End If
                    End If
                End If
            End If
        Next

        CompetingBurstTable.AcceptChanges()

    End Sub

    Private Sub UpdatePeerBurstTable()
        ' A burst in the same space as this burst and with the same brand (or a brand in the same brand family)
        ' will be listed as a peer burst under the following conditions:
        '   - If it belongs to the same contract (sibling)
        '   - If it belongs to a different contract which is also signed (a burst in a contract that is not
        '     signed, other than the current contract, will not be deemed to be a peer burst).

        ' If a new burst has been added as a store pool sharer with this burst and the change has not yet been saved
        ' back to the database, then this method would undo that change because it would restore the burst's
        ' StorePoolID value back to its original value. So before we continue, let's make a list of the IDs of
        ' bursts which have been made relatives so that we can restore them later.
        Dim RelativeBurstIDList As New List(Of Guid)
        For Each PeerBurst As DataRow In PeerBurstTable.Rows
            If PeerBurst("StorePoolID") = StorePoolID Then
                ' This is a relative. Remember the BurstID.
                RelativeBurstIDList.Add(PeerBurst("BurstID"))
            End If
        Next

        ' Clear the table.
        PeerBurstTable.Clear()

        ' Stop if criteria isn't met.
        If _InstallWeeks < 1 Then
            ' If the burst doesn't run for any number of weeks then it'll have no peers.
            Exit Sub
        End If

        ' Stop if all required data isn't available yet.
        If String.Compare(_ChainName, "Select...") = 0 Then
            ' A chain hasn't been selected. Exit.
            Exit Sub
        ElseIf String.Compare(_MediaName, "Select...") = 0 Then
            ' A media service hasn't been selected. Exit.
            Exit Sub
        ElseIf String.Compare(_BrandName, "Select...") = 0 Then
            ' A brand hasn't been selected. Exit.
            Exit Sub
        ElseIf BurstCategoryTable.Select("Priority < 1").Length = 0 Then
            ' A homesite or additional categories have not been selected. Exit.
            Exit Sub
        End If

        ' Build a list of categories.
        Dim CategoryIDList As New System.Text.StringBuilder
        For Each Category As DataRow In BurstCategoryTable.Select("Priority < 1")
            If Category("Priority") < 1 Then
                ' Cool, the priority is less than one, meaning this category is not a crossover.
                ' Now we need to check if this is a homesite AND the user does not want ads to be installed
                ' at the homesite. If that's the case, this category must be excluded.
                Dim IncludeThisCategory As Boolean = True
                If Category("Priority") = 0 AndAlso InstallAtHomesite = False Then
                    ' This category is the homesite AND the user does not want ads installed here.
                    IncludeThisCategory = False
                End If
                If IncludeThisCategory Then
                    ' This category is not a crossover and is not a homesite with the "Install at Homesite" option
                    ' set to "False". Add it.
                    If CategoryIDList.Length > 0 Then
                        ' The list is not empty, add a comma.
                        CategoryIDList.Append(",")
                    End If
                    CategoryIDList.Append(Category("CategoryID"))
                End If
            End If
        Next

        ' Get peer burst information from the database.
        Dim CategoryIDListString As String = CategoryIDList.ToString
        PeerBurstAdapter.Fill(PeerBurstTable, Row("BurstID"), FirstWeek,
        LastWeek, ChainID, _BrandID, CategoryIDListString, MediaFamilyIDList)

        ' If any bursts have been deleted from memory, delete them from the competing burst table too.
        Dim DeletedBursts As DataTable = Row.Table.GetChanges(DataRowState.Deleted)
        If Not IsNothing(DeletedBursts) Then
            DeletedBursts.RejectChanges()
            For Each DeletedBurst As DataRow In DeletedBursts.Rows
                Dim DeletedBurstID As Guid = DeletedBurst("BurstID")
                Dim PeerBurstToDelete As DataRow = PeerBurstTable.Rows.Find(DeletedBurstID)
                If Not IsNothing(PeerBurstToDelete) Then
                    PeerBurstToDelete.Delete()
                End If
            Next
            DeletedBursts.Dispose()
        End If

        ' Get peer burst information from the current contract that might not yet have been saved to the database,
        ' or that might have been modified since the last database read.
        For Each SiblingBurst As DataRow In _ParentContract.Row.GetChildRows("FK_Burst_Contract")
            ' "Sibling Bursts" are bursts belonging to the same contract.
            If Not Object.ReferenceEquals(SiblingBurst, Row) Then
                ' This sibling burst is not the current burst.
                If Not SiblingBurst.RowState = DataRowState.Deleted Then
                    If PeerBurstTable.Rows.Contains(SiblingBurst("BurstID")) Then
                        ' The PeerBurst table already contains this burst. But it may have been modified since it was
                        ' read from the database. Let's update it's properties, just in case it has.
                        Dim PeerBurst As DataRow = PeerBurstTable.Rows.Find(SiblingBurst("BurstID"))
                        PeerBurst("StorePoolID") = SiblingBurst("StorePoolID")
                        PeerBurst("StorePoolQty") = SiblingBurst("StorePoolQty")
                        PeerBurst("BrandName") = SiblingBurst("BrandName")
                        PeerBurst("MediaName") = SiblingBurst("MediaName")
                        PeerBurst("InstallStoreQty") = SiblingBurst("InstallStoreQty")
                        PeerBurst("Stores") = SiblingBurst("InstallStoreQty")
                    Else
                        ' The PeerBurst table doesn't contain this burst. Check if this burst is a peer of the current
                        ' burst and add it to the table if it is.
                        If SiblingBurst("ChainID") = _ChainID Then
                            ' The sibling's chain ID is the same. This sibling could be a peer burst.
                            If SiblingBurst("FirstWeek") <= _LastWeek AndAlso SiblingBurst("LastWeek") >= _FirstWeek Then
                                ' The sibling's date range overlaps with the current burst's date range. It could be a peer.
                                If SiblingBurst.GetChildRows("Burst_BurstCategory").Length > 0 Then
                                    ' The sibling has categories. Cool.

                                    Dim CategoryMatch As Boolean = False
                                    ' Check if any of the sibling categories match any category of the current burst.
                                    For Each SiblingBurstCategory As DataRow In SiblingBurst.GetChildRows("Burst_BurstCategory")
                                        If BurstCategoryMatch(SiblingBurstCategory, SiblingBurst("InstallAtHomesite")) Then
                                            CategoryMatch = True
                                        End If
                                    Next

                                    If CategoryMatch Then
                                        ' There is at least one matching category in the sibling burst and the current burst.
                                        If NonCompetingBrandTable.Rows.Contains(SiblingBurst("BrandID")) Then
                                            ' The sibling burst uses a brand in the same family as the current burst's brand. It could be a peer.
                                            If CompetingMediaTable.Rows.Contains(SiblingBurst("MediaID")) Then
                                                ' The sibling burst uses a media service in the same family as the current burst's media service.
                                                ' This sibling burst is a peer of the current burst. Add it to the PeerBurst table.
                                                Dim NewPeer As DataRow = PeerBurstTable.NewRow
                                                With NewPeer
                                                    .Item("BurstID") = SiblingBurst("BurstID")
                                                    .Item("StorePoolID") = SiblingBurst("StorePoolID")
                                                    .Item("StorePoolQty") = SiblingBurst("StorePoolQty")
                                                    .Item("ContractNumber") = _ParentContract.ContractNumber
                                                    .Item("BrandName") = SiblingBurst("BrandName")
                                                    .Item("MediaName") = SiblingBurst("MediaName")
                                                    .Item("InstallStoreQty") = SiblingBurst("InstallStoreQty")
                                                    .Item("Stores") = SiblingBurst("InstallStoreQty")
                                                    .Item("FirstWeek") = SiblingBurst("FirstWeek")
                                                    .Item("LastWeek") = SiblingBurst("LastWeek")
                                                    .Item("Signed") = _ParentContract.Signed
                                                End With
                                                PeerBurstTable.Rows.Add(NewPeer)
                                            End If
                                        End If
                                    End If

                                End If
                            End If
                        End If
                    End If
                End If
            End If
        Next

        ' If any peer burst from a different contract were to have been made a relative of this burst, the above code would load such
        ' a burst into the PeerBurst table but with its original StorePoolID. But if it has been made a relative of this burst and
        ' has not yet been saved to the database, then it must have its new StorePoolId (which should be identical to that of the
        ' current burst) updated in the PeerBurst table.
        Dim OriginalPeersTable As DataTable = DataSet.Tables("PeerBurst")
        For Each OriginalPeer As DataRow In OriginalPeersTable.Rows
            If OriginalPeer.RowState = DataRowState.Modified Then
                If PeerBurstTable.Rows.Contains(OriginalPeer("BurstID")) Then
                    ' A peer burst has been updated. Update the corresponding row in the Peer Burst table.
                    Dim PeerToUpdate As DataRow = PeerBurstTable.Rows.Find(OriginalPeer("BurstID"))
                    PeerToUpdate("StorePoolID") = OriginalPeer("StorePoolID")
                End If
            End If
        Next

        ' Now that the PeerBurst table has been reconstructed and repopulated and stuff, let's restore the store pool IDs of any
        ' peers that were made relatives but not saved to the database yet.
        For Each NewPeerBurst As DataSetContract.PeerBurstRow In PeerBurstTable.Rows
            If Not NewPeerBurst.RowState = DataRowState.Deleted Then
                If RelativeBurstIDList.Contains(NewPeerBurst.BurstID) Then
                    ' This was a relative. Restore it as a relative now by making the store pool ID equal to that of the current burst.
                    NewPeerBurst.StorePoolID = StorePoolID
                End If
            End If
        Next

        PeerBurstTable.AcceptChanges()

    End Sub

    Private Sub UpdatePcaSatusTable()
        Dim NewPcaStatus As DataRow = _BurstPcaStatusTable.NewRow
        With NewPcaStatus
            .Item("BurstID") = Row("BurstId")
            .Item("PcaStatusId") = _PcaStatusId
            .Item("PcaStatusName") = _PcaStatusName
            .Item("CreationDate") = System.DateTime.Now

        End With
        'also remove current row

        _BurstPcaStatusTable.Rows.Add(NewPcaStatus)
    End Sub
    Private Function BurstCategoryMatch(ByVal SiblingBurstCategory As DataRow, ByVal SiblingBurstInstallAtHomesite As Boolean)
        ' This function takes a BurstCategory row and checks if it matches any category in the burst
        ' represented by this object. It is designed to work with the UpdatePeerBurstTable method,
        ' among others.

        ' Conditions required for a match to occur:
        '   - Both category IDs must be identical.
        '   - Neither category may be a crossover.
        '   - Neither category may be a homesite if the burst's InstallAtHomesite option equals FALSE.

        If SiblingBurstCategory("Priority") > 0 Then
            ' The category in the sibling burst is a crossover. No match.
            Return False
        Else
            ' The category in the sibling burst is not a crossover.
            If SiblingBurstCategory("Priority") = 0 AndAlso SiblingBurstInstallAtHomesite = False Then
                ' The category in the sibling burst is a homesite but ads won't be installed at the homesite. No match.
                Return False
            Else
                ' The category in the sibling burst is either:
                '   - an Additional Category, or
                '   - a homesite where ads will be installed

                ' Create a key to find a possible matching category row in the BurstCategory table of the burst
                ' represented by this object.
                Dim Keys() As Object = {Row("BurstID"), SiblingBurstCategory("CategoryID")}
                ' Look for a possible matching category row.
                Dim PossibleMatchingBurstCategory As DataRow = _BurstCategoryTable.Rows.Find(Keys)
                If IsNothing(PossibleMatchingBurstCategory) Then
                    ' The category ID in the sibling burst matches no category IDs in the current burst.
                    Return False
                Else
                    ' The category ID in the sibling burst matches a category ID in the current burst.
                    If PossibleMatchingBurstCategory("Priority") > 0 Then
                        ' The matching category in the current row is a crossover. No match.
                        Return False
                    Else
                        ' The matching category in the current row is not a crossover.
                        If PossibleMatchingBurstCategory("Priority") = 0 AndAlso InstallAtHomesite = False Then
                            ' The category in this burst is a homesite but ads won't be installed at the homesite. No match.
                            Return False
                        Else
                            ' The category in this burst is either:
                            '   - an Additional Category, or
                            '   - a homesite where ads will be installed
                            ' All conditions are met. A match has been found.
                            Return True
                        End If
                    End If
                End If

            End If
        End If

    End Function

    Private Sub UpdateCompetingMediaTable()

        ' Update the CompetingMedia table.
        Dim SelectStatement As String = "SELECT CompetingMediaID FROM Media.vCompetingMedia WHERE (MediaID = " & MediaID.ToString & ")"
        Dim Errors As String = String.Empty
        CompetingMediaTable = LiquidShell.LiquidAgent.GetSqlDataTable(ConnectionString, SelectStatement, Errors)

        ' Define the primary key for this table.
        Dim PKey() As DataColumn = {CompetingMediaTable.Columns(0)}
        CompetingMediaTable.PrimaryKey = PKey

    End Sub

    Private Sub UpdateNonCompetingBrandTable()
        ' Update the NonCompetingBrand table.
        NonCompetingBrandAdapter.Fill(NonCompetingBrandTable, _BrandID)
    End Sub

    Private Sub UpdateCategoriesColumn()
        ' Update the 'Categories' column of the burst datarow of this object to display all the selected categories for this burst.

        ' A string builder to build the category list.
        Dim CategoryList As New System.Text.StringBuilder

        ' Add category names to the string builder.
        For Each Category As DataRow In _BurstCategoryTable.Rows
            If Not Category.RowState = DataRowState.Deleted Then
                Dim Priority As Integer = Category("Priority")
                If Priority <= _CrossoverQty Then
                    ' This category is not a crossover with a priority that causes it to be excluded from the installation schedule
                    ' (i.e. it's priority is less than the crossover qty required), so it must be included.
                    If Not (Priority = 0 And _InstallAtHomesite = False) Then
                        If CategoryList.Length > 0 Then
                            CategoryList.Append(", ")
                        End If
                        CategoryList.Append(Category("CategoryName"))
                    End If
                End If
            End If
        Next

        ' Update the row column with the updated category list.
        _Categories = CategoryList.ToString

    End Sub

    Private Sub UpdateBurstDayColumn()
        '' Update the 'Categories' column of the burst datarow of this object to display all the selected categories for this burst.

        '' A string builder to build the category list.
        'Dim CategoryList As New System.Text.StringBuilder

        '' Add category names to the string builder.
        'For Each Category As DataRow In _BurstCategoryTable.Rows
        '    If Not Category.RowState = DataRowState.Deleted Then
        '        Dim Priority As Integer = Category("Priority")
        '        If Priority <= _CrossoverQty Then
        '            ' This category is not a crossover with a priority that causes it to be excluded from the installation schedule
        '            ' (i.e. it's priority is less than the crossover qty required), so it must be included.
        '            If Not (Priority = 0 And _InstallAtHomesite = False) Then
        '                If CategoryList.Length > 0 Then
        '                    CategoryList.Append(", ")
        '                End If
        '                CategoryList.Append(Category("CategoryName"))
        '            End If
        '        End If
        '    End If
        'Next

        '' Update the row column with the updated category list.
        '_Categories = CategoryList.ToString

    End Sub

    Private Sub ResetCrossoverPriorities()
        ' Change all priorities of crossover categories in the _BurstCategoryTable table to ensure that they all run in sequence.
        Dim Crossovers() As DataRow = _BurstCategoryTable.Select("Priority > 0")
        Dim CrossoverCount As Integer = Crossovers.Length
        For i As Integer = 0 To CrossoverCount - 1
            Crossovers(i).Item("Priority") = i + 1
        Next
    End Sub

    Private Sub RecalculateLoadingFeeAmounts()
        ' Recalculate all loading fee amounts.
        If IsNothing(_BurstLoadingFeeTable) = False Then
            For Each LoadingFee As DataRow In _BurstLoadingFeeTable.Rows
                If Not LoadingFee.RowState = DataRowState.Deleted Then
                    LoadingFee("LoadingFeeAmount") = CDec(LoadingFee("Percentage")) / 100 * RentalAmount
                End If
            Next
        End If
    End Sub

    Private Sub SaveLoadingFees()

        ' Get the original, untouched BurstLoadingFee table.
        Dim OriginalLoadingFeeTable As DataTable = Row.Table.ChildRelations("FK_BurstLoadingFee_Burst").ChildTable

        ' Get the added, modified and deleted burst loading fee rows.
        Dim AddedRows As DataTable = _BurstLoadingFeeTable.GetChanges(DataRowState.Added)
        Dim ModifiedRows As DataTable = _BurstLoadingFeeTable.GetChanges(DataRowState.Modified)
        Dim DeletedRows As DataTable = _BurstLoadingFeeTable.GetChanges(DataRowState.Deleted)

        ' Handle all the deleted loading fee rows.
        If Not IsNothing(DeletedRows) Then
            For Each DeletedRow As DataRow In DeletedRows.Rows
                ' Change the rowstate from Deleted so that we can access data in the row.
                DeletedRow.RejectChanges()
                ' Get the primary key value of this row.
                Dim Keys() As Object = {DeletedRow("BurstID"), DeletedRow("LoadingFeeID")}
                ' Get the corresponding row of the original BurstLoadingFee table in the data set.
                Dim OriginalLoadingFeeRow As DataRow = OriginalLoadingFeeTable.Rows.Find(Keys)
                ' Log it if this is not a new burst.
                If Not Row.RowState = DataRowState.Detached Then
                    Dim Action As String = "Deleted the '" & DeletedRow("LoadingFeeName") & "' loading fee of " _
                    & CDec(OriginalLoadingFeeRow("Percentage")).ToString("#.00") & "%"
                    LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, "Burst", RowDescription, Action)
                End If
                ' Make the changes to the original table.
                OriginalLoadingFeeRow.Delete()
            Next
        End If

        ' Handle all the newly created loading fee rows.
        If Not IsNothing(AddedRows) Then
            For Each AddedRow As DataRow In AddedRows.Rows
                ' Log it if this is not a new burst.
                If Not Row.RowState = DataRowState.Detached Then
                    Dim Action As String = "Added a '" & AddedRow("LoadingFeeName") & "' loading fee of " _
                    & CDec(AddedRow("Percentage")).ToString("#.00") & "%"
                    LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, "Burst", RowDescription, Action)
                End If
                ' Add it to the original table.
                Dim NewRow As DataRow = OriginalLoadingFeeTable.NewRow
                NewRow("BurstID") = AddedRow("BurstID")
                NewRow("LoadingFeeID") = AddedRow("LoadingFeeID")
                NewRow("Percentage") = AddedRow("Percentage")
                NewRow("LoadingFeeName") = AddedRow("LoadingFeeName")
                NewRow("LoadingFeeAmount") = AddedRow("LoadingFeeAmount")
                OriginalLoadingFeeTable.Rows.Add(NewRow)
            Next
        End If

        ' Handle all the modified loading fee rows.
        If Not IsNothing(ModifiedRows) Then
            For Each ModifiedRow As DataRow In ModifiedRows.Rows
                ' Get the primary key value of this row.
                Dim Keys() As Object = {ModifiedRow("BurstID"), ModifiedRow("LoadingFeeID")}
                ' Get the corresponding row of the original BurstLoadingFee table in the data set.
                Dim OriginalLoadingFeeRow As DataRow = OriginalLoadingFeeTable.Rows.Find(Keys)
                ' Log it if this is not a new burst.
                If Not Row.RowState = DataRowState.Detached Then
                    ' This loading fee row could have a state of "Modified" if the rental changed, thus changning the AMOUNT
                    ' of the loading fee. However, the PERCENTAGE (which is the value that gets saved to the database) may
                    ' not have changed at all. So let's first see if this PERCENTAGE column has changed before writing any
                    ' otherwise unnecessary log entries.
                    If Not CDec(OriginalLoadingFeeRow("Percentage")) = CDec(ModifiedRow("Percentage")) Then
                        ' Ok, we're happy that it was the actual PERCENTAGE of the row that has been changed. Proceed with
                        ' the log entry.
                        Dim Action As String = "Changed the '" & ModifiedRow("LoadingFeeName") & "' loading fee from " _
                        & CDec(OriginalLoadingFeeRow("Percentage")).ToString("#.00") & "% to " & CDec(ModifiedRow("Percentage")).ToString("#.00") & "% "
                        LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, "Burst", RowDescription, Action)
                    End If
                End If
                ' Make the changes to the original table.
                OriginalLoadingFeeRow("Percentage") = ModifiedRow("Percentage")
                OriginalLoadingFeeRow("LoadingFeeName") = ModifiedRow("LoadingFeeName")
                OriginalLoadingFeeRow("LoadingFeeAmount") = ModifiedRow("LoadingFeeAmount")
            Next
        End If

        ' Dispose all the temporary tables.
        If Not IsNothing(AddedRows) Then
            AddedRows.Dispose()
        End If
        If Not IsNothing(ModifiedRows) Then
            ModifiedRows.Dispose()
        End If
        If Not IsNothing(DeletedRows) Then
            DeletedRows.Dispose()
        End If

    End Sub

    Private Sub SaveCategories()

        ' Get the original, untouched BurstCategory table.
        Dim OriginalCategoryTable As DataTable = Row.Table.ChildRelations("Burst_BurstCategory").ChildTable

        ' Get the added, modified and deleted burst category rows.
        Dim DeletedRows As DataTable = _BurstCategoryTable.GetChanges(DataRowState.Deleted)
        Dim AddedRows As DataTable = _BurstCategoryTable.GetChanges(DataRowState.Added)
        Dim ModifiedRows As DataTable = _BurstCategoryTable.GetChanges(DataRowState.Modified)

        ' Handle all the deleted category rows.
        If Not IsNothing(DeletedRows) Then
            For Each DeletedRow As DataRow In DeletedRows.Rows
                ' Change the rowstate from Deleted so that we can access data in the row.
                DeletedRow.RejectChanges()
                ' Get the primary key value of this row.
                Dim Keys() As Object = {DeletedRow("BurstID"), DeletedRow("CategoryID")}
                ' Get the corresponding row of the original BurstCategory table in the data set.
                Dim OriginalCategoryRow As DataRow = OriginalCategoryTable.Rows.Find(Keys)

                ' Log it if this is not a new burst.
                If Not Row.RowState = DataRowState.Detached Then
                    ' Create a string to hold the log entry's action.
                    Dim Action As String = String.Empty

                    ' Build the action string.
                    Select Case DeletedRow("Priority")
                        Case Is = -1
                            Action = "Removed additional category '" & DeletedRow("CategoryName") & "'"
                        Case Is = 0
                            Action = "Removed homesite '" & DeletedRow("CategoryName") & "'"
                        Case Is > 0
                            Action = "Removed crossover '" & DeletedRow("CategoryName") & "'"
                    End Select

                    ' Write the log entry.
                    LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, "Burst", RowDescription, Action)
                End If

                ' Make the changes to the original table.
                OriginalCategoryRow.Delete()
            Next
        End If

        ' Handle all the newly created category rows.
        If Not IsNothing(AddedRows) Then
            For Each AddedRow As DataRow In AddedRows.Rows

                ' Log it if this is not a new burst.
                If Not Row.RowState = DataRowState.Detached Then
                    Dim Action As String = String.Empty
                    Select Case AddedRow("Priority")
                        ' Check what type of category this is.
                        Case Is < 0
                            ' This is an additional category.
                            Action = "Added '" & AddedRow("CategoryName") & "' as an additional category"
                        Case Is = 0
                            ' This is a homesite category.
                            Action = "Added '" & AddedRow("CategoryName") & "' as the homesite"
                        Case Is > 0
                            ' This is a crossover category.
                            Action = "Added '" & AddedRow("CategoryName") & "' as a crossover with a priority of " & CInt(AddedRow("Priority")).ToString
                    End Select
                    LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, "Burst", RowDescription, Action)
                End If

                ' Add it to the original table.
                Dim NewRow As DataRow = OriginalCategoryTable.NewRow
                NewRow("BurstID") = AddedRow("BurstID")
                NewRow("CategoryID") = AddedRow("CategoryID")
                NewRow("Priority") = AddedRow("Priority")
                NewRow("CategoryName") = AddedRow("CategoryName")
                OriginalCategoryTable.Rows.Add(NewRow)

            Next
        End If

        ' Handle all the modified category rows.
        If Not IsNothing(ModifiedRows) Then
            For Each ModifiedRow As DataRow In ModifiedRows.Rows
                ' Get the primary key value of this row.
                Dim Keys() As Object = {ModifiedRow("BurstID"), ModifiedRow("CategoryID")}
                ' Get the corresponding row of the original CategoryTable table in the data set.
                Dim OriginalCategoryRow As DataRow = OriginalCategoryTable.Rows.Find(Keys)

                ' Log it if this is not a new burst.
                If Not Row.RowState = DataRowState.Detached Then
                    ' Get the original and the new values for the modified category's priority.
                    Dim OriginalPriority As Integer = OriginalCategoryRow("Priority")
                    Dim NewPriority As Integer = ModifiedRow("Priority")
                    ' Create a string to hold the log entry's action.
                    Dim Action As String = String.Empty
                    Action = "Crossover '" & ModifiedRow("CategoryName") & "' changed from priority " _
                    & OriginalPriority.ToString & " to priority " & NewPriority.ToString
                    ' Write the log entry.
                    LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, "Burst", RowDescription, Action)
                End If

                ' Make the changes to the original table.
                OriginalCategoryRow("Priority") = ModifiedRow("Priority")
            Next
        End If

        ' Dispose all the temporary tables.
        If Not IsNothing(AddedRows) Then
            AddedRows.Dispose()
        End If
        If Not IsNothing(ModifiedRows) Then
            ModifiedRows.Dispose()
        End If
        If Not IsNothing(DeletedRows) Then
            DeletedRows.Dispose()
        End If

    End Sub

    Private Sub SaveInstallationDates()

        ' Get the original, untouched BurstCategory table.
        Dim OriginalInstallationDayTable As DataTable = Row.Table.ChildRelations("FK_Burst_BurstInstallationDay").ChildTable

        ' Get the added, modified and deleted burst category rows.
        Dim DeletedRows As DataTable = _BurstInstallationDayTable.GetChanges(DataRowState.Deleted)
        Dim AddedRows As DataTable = _BurstInstallationDayTable.GetChanges(DataRowState.Added)
        Dim ModifiedRows As DataTable = _BurstInstallationDayTable.GetChanges(DataRowState.Modified)

        ' Handle all the deleted category rows.
        If Not IsNothing(DeletedRows) Then
            For Each DeletedRow As DataRow In DeletedRows.Rows
                ' Change the rowstate from Deleted so that we can access data in the row.
                DeletedRow.RejectChanges()
                ' Get the primary key value of this row.
                Dim Keys() As Object = {DeletedRow("BurstID"), DeletedRow("InstallationDayID")}
                ' Get the corresponding row of the original BurstCategory table in the data set.
                Dim OriginalInstallationDayRow As DataRow = OriginalInstallationDayTable.Rows.Find(Keys)

                ' Log it if this is not a new burst.
                If Not Row.RowState = DataRowState.Detached Then
                    ' Create a string to hold the log entry's action.
                    Dim Action As String = String.Empty

                    ' Build the action string.
                    Action = "Removed installation day '" & DeletedRow("InstallationDayName") & "'"



                    ' Write the log entry.
                    LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, "Burst", RowDescription, Action)
                End If

                ' Make the changes to the original table.
                OriginalInstallationDayRow.Delete()
            Next
        End If

        ' Handle all the newly created category rows.
        If Not IsNothing(AddedRows) Then
            For Each AddedRow As DataRow In AddedRows.Rows

                ' Log it if this is not a new burst.
                If Not Row.RowState = DataRowState.Detached Then
                    Dim Action As String = String.Empty

                    Action = "Added '" & AddedRow("InstallationDayName") & "' as an installation day"


                    LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, "Burst", RowDescription, Action)
                End If

                ' Add it to the original table.
                Dim NewRow As DataRow = OriginalInstallationDayTable.NewRow
                NewRow("BurstID") = AddedRow("BurstID")
                NewRow("InstallationDayID") = AddedRow("InstallationDayID")

                NewRow("InstallationDayName") = AddedRow("InstallationDayName")
                OriginalInstallationDayTable.Rows.Add(NewRow)

            Next
        End If

        ' Handle all the modified category rows.
        'If Not IsNothing(ModifiedRows) Then
        '    For Each ModifiedRow As DataRow In ModifiedRows.Rows
        '        ' Get the primary key value of this row.
        '        Dim Keys() As Object = {ModifiedRow("BurstID"), ModifiedRow("CategoryID")}
        '        ' Get the corresponding row of the original CategoryTable table in the data set.
        '        Dim OriginalCategoryRow As DataRow = OriginalCategoryTable.Rows.Find(Keys)

        '        ' Log it if this is not a new burst.
        '        If Not Row.RowState = DataRowState.Detached Then
        '            ' Get the original and the new values for the modified category's priority.
        '            Dim OriginalPriority As Integer = OriginalCategoryRow("Priority")
        '            Dim NewPriority As Integer = ModifiedRow("Priority")
        '            ' Create a string to hold the log entry's action.
        '            Dim Action As String = String.Empty
        '            Action = "Crossover '" & ModifiedRow("CategoryName") & "' changed from priority " _
        '            & OriginalPriority.ToString & " to priority " & NewPriority.ToString
        '            ' Write the log entry.
        '            LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, "Burst", RowDescription, Action)
        '        End If

        '        ' Make the changes to the original table.
        '        OriginalCategoryRow("Priority") = ModifiedRow("Priority")
        '    Next
        'End If

        ' Dispose all the temporary tables.
        If Not IsNothing(AddedRows) Then
            AddedRows.Dispose()
        End If
        If Not IsNothing(ModifiedRows) Then
            ModifiedRows.Dispose()
        End If
        If Not IsNothing(DeletedRows) Then
            DeletedRows.Dispose()
        End If

    End Sub

    Private Sub SavePcaStatusses()

        ' Get the original, untouched BurstCategory table.
        Dim OriginalPcaStatusTable As DataTable = Row.Table.ChildRelations("Burst_BurstPcaStatus").ChildTable

        ' Get the added, modified and deleted burst category rows.
        'Dim DeletedRows As DataTable = _BurstInstallationDayTable.GetChanges(DataRowState.Deleted)
        Dim AddedRows As DataTable = _BurstPcaStatusTable.GetChanges(DataRowState.Added)
        Dim ModifiedRows As DataTable = _BurstPcaStatusTable.GetChanges(DataRowState.Modified)


        ' Handle all the newly created pcastatuses rows.
        If Not IsNothing(AddedRows) Then
            Dim PcaStatusDate As System.DateTime = AddedRows.Rows(0)("CreationDate")
            Dim NewRow As DataRow = OriginalPcaStatusTable.NewRow
            For Each AddedRow As DataRow In AddedRows.Rows
                If AddedRow("CreationDate") >= PcaStatusDate Then

                    NewRow("BurstID") = AddedRow("BurstID")
                    NewRow("PcaStatusId") = AddedRow("PcaStatusId")
                    NewRow("CreationDate") = AddedRow("CreationDate")
                    NewRow("PcaStatusName") = AddedRow("PcaStatusName")
                    PcaStatusDate = AddedRow("CreationDate")

                End If
                ' Log it if this is not a new burst.
                If Not Row.RowState = DataRowState.Detached Then
                    Dim Action As String = String.Empty
                    Action = "Added '" & AddedRow("PcaStatusName") & "' as an pca status name"
                    LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, "Burst", RowDescription, Action)
                End If
                'only mark the latest added row
            Next
            ' Add it to the original table.
            OriginalPcaStatusTable.Rows.Add(NewRow)
        End If

        'Handle all the modified pcaStatus rows. They will be handled as added rows
        'If Not IsNothing(ModifiedRows) Then
        '    For Each ModifiedRow As DataRow In ModifiedRows.Rows
        '        ' Get the primary key value of this row.
        '        Dim Keys() As Object = {ModifiedRow("BurstID"), ModifiedRow("PcaStatusId")}
        '        ' Get the corresponding row of the original CategoryTable table in the data set.
        '        Dim Original As DataRow = OriginalInstallationDayTable.Rows.Find(Keys)

        '        ' Log it if this is not a new burst.
        '        If Not Row.RowState = DataRowState.Detached Then
        '            ' Get the original and the new values for the modified category's priority.
        '            Dim OriginalPriority As Integer = OriginalCategoryRow("Priority")
        '            Dim NewPriority As Integer = ModifiedRow("Priority")
        '            ' Create a string to hold the log entry's action.
        '            Dim Action As String = String.Empty
        '            Action = "Crossover '" & ModifiedRow("CategoryName") & "' changed from priority " _
        '            & OriginalPriority.ToString & " to priority " & NewPriority.ToString
        '            ' Write the log entry.
        '            LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, "Burst", RowDescription, Action)
        '        End If

        '        ' Make the changes to the original table.
        '        OriginalCategoryRow("Priority") = ModifiedRow("Priority")
        '    Next
        'End If

        ' Dispose all the temporary tables.
        If Not IsNothing(AddedRows) Then
            AddedRows.Dispose()
        End If
        If Not IsNothing(ModifiedRows) Then
            ModifiedRows.Dispose()
        End If


    End Sub

    Private Sub SavePeerBursts()

        ' Get the original, untouched PeerBurst table.
        Dim OriginalPeerBurstTable As DataTable = DataSet.Tables("PeerBurst")

        ' A list of PeerBursts that need to be deleted from the PeerBurst table to prevent concurrency errors while saving.
        Dim PeersToDelete As New List(Of Guid)

        ' Handle all the modified rows.
        For Each PeerBurstRow As DataRow In PeerBurstTable.Rows
            ' Check if this row was changed.
            If PeerBurstRow.RowState = DataRowState.Modified Then
                ' Get the primary key value of this row.
                Dim Key As Object = PeerBurstRow("BurstID")
                ' Check if this row exists in the original table.
                Dim OriginalPeerBurstRow As DataRow = OriginalPeerBurstTable.Rows.Find(Key)
                If IsNothing(OriginalPeerBurstRow) = False Then
                    ' This peer burst already exists in the original table. Update it.
                    OriginalPeerBurstRow("StorePoolID") = PeerBurstRow("StorePoolID")
                Else
                    ' This peer burst does not exist in the original table. Create it.
                    OriginalPeerBurstRow = OriginalPeerBurstTable.NewRow
                    OriginalPeerBurstRow("BurstID") = Key
                    OriginalPeerBurstRow("StorePoolID") = PeerBurstRow("StorePoolID")
                    OriginalPeerBurstRow("FirstWeek") = PeerBurstRow("FirstWeek")
                    OriginalPeerBurstRow("LastWeek") = PeerBurstRow("LastWeek")
                    OriginalPeerBurstRow("Stores") = PeerBurstRow("Stores")
                    OriginalPeerBurstRow("MediaName") = PeerBurstRow("MediaName")
                    OriginalPeerBurstRow("Signed") = PeerBurstRow("Signed")
                    OriginalPeerBurstTable.Rows.Add(OriginalPeerBurstRow)
                    ' Because we'll never create new peer bursts or delete peer bursts, the original table will only ever have
                    ' rows with a 'Modified' row state.
                    OriginalPeerBurstRow.AcceptChanges()
                    OriginalPeerBurstRow.SetModified()
                End If
                ' If this PeerBurst is also a sibling burst in the same contract then we need to update the instance of the
                ' sibling in the Burst table, and remove the burst from the PeerBurst table to prevent concurrency errors while
                ' trying to save back to the database.
                For Each SiblingBurst As DataRow In ParentContract.Row.GetChildRows("FK_Burst_Contract")
                    If Object.Equals(SiblingBurst("BurstID"), Key) Then
                        ' This peer burst is also a sibling. Update the instance of this row in the burst table.
                        SiblingBurst("StorePoolID") = PeerBurstRow("StorePoolID")

                        ' Remember this peer burst's ID so that it can be deleted before saving to the database.
                        If PeersToDelete.Contains(Key) = False Then
                            PeersToDelete.Add(Key)
                        End If
                    End If
                Next
            End If
        Next

        ' Delete peer bursts that are also siblings so that we don't we don't try and save their changes twice.
        For Each ID As Guid In PeersToDelete
            Dim PeerBurstToDelete As DataRow = OriginalPeerBurstTable.Rows.Find(ID)
            PeerBurstToDelete.Delete()
            PeerBurstToDelete.AcceptChanges()
        Next

    End Sub

    Private Sub SaveApplyInstructionsOnAllBursts()
        If String.IsNullOrEmpty(InstallationInstructions) = False Then
            For Each SiblingBurst As DataRow In ParentContract.Row.GetChildRows("FK_Burst_Contract")
                If ApplyInstructionsAcrossAllBursts = True Then
                    If Row("BurstID") <> SiblingBurst("BurstID") Then
                        SiblingBurst("InstallationInstructions") = InstallationInstructions
                    End If
                End If
            Next
        Else
            If String.IsNullOrEmpty(InstallationInstructions) = True And ApplyInstructionsAcrossAllBursts = True Then
                ApplyInstructionsAcrossAllBursts = False
            End If
        End If
    End Sub
    Private Sub SaveApplyDatesOnAllBursts()
        If String.IsNullOrEmpty(FirstWeek) = False Then
            For Each SiblingBurst As DataRow In ParentContract.Row.GetChildRows("FK_Burst_Contract")
                If ApplyDatesAcrossAllBursts = True Then
                    If Row("BurstID") <> SiblingBurst("BurstID") Then
                        SiblingBurst("Firstweek") = FirstWeek
                        SiblingBurst("Lastweek") = DateAdd(DateInterval.WeekOfYear, SiblingBurst("InstallWeeks") - 1, SiblingBurst("Firstweek"))
                    End If
                End If
            Next
        Else
            If String.IsNullOrEmpty(FirstWeek) = True And ApplyDatesAcrossAllBursts = True Then
                ApplyDatesAcrossAllBursts = False
            End If
        End If
    End Sub

    'Public Function CreateDefaultBillingInstructions(connectionString As String) As Boolean

    '    Dim con As SqlConnection = New SqlConnection(connectionString)
    '    Dim errorMessage As String = String.Empty
    '    Try
    '        Dim SQL As String = "Sales.spDefaultBillingInstructions"
    '        Dim cmd As SqlCommand = New SqlCommand(SQL, con)
    '        cmd.CommandType = CommandType.StoredProcedure
    '        Dim param As SqlParameter
    '        param = cmd.Parameters.Add("@contractid", SqlDbType.UniqueIdentifier)
    '        param.Value = Row("ContractID")
    '        ' Execute the command.
    '        con.Open()
    '        Dim rowsAffected As Integer = cmd.ExecuteNonQuery

    '        Return True

    '    Catch ex As Exception
    '        errorMessage = ex.Message
    '    Finally
    '        con.Close()
    '    End Try

    '    If String.IsNullOrEmpty(errorMessage) = False Then
    '        ConsumingForm.ShowMessage(errorMessage, "Trying to Create Default Billing Instructions.")
    '    End If

    '    Return False

    'End Function


    Public Function prompSaveDefaultBillingInstructions(connectionString As String, ByRef ContractID As Guid) As Boolean

        Dim con As SqlConnection = New SqlConnection(connectionString)
        Dim errorMessage As String = String.Empty
        ContractID = Row("ContractID")
        Try
            Dim SQL As String = "Sales.spPrompDefaultBillingInstructions"
            Dim cmd As SqlCommand = New SqlCommand(SQL, con)
            cmd.CommandType = CommandType.StoredProcedure
            Dim param As SqlParameter
            param = cmd.Parameters.Add("@contractid", SqlDbType.UniqueIdentifier)
            param.Value = ContractID
            con.Open()
            ' Execute the command.
            Dim promptBillingInstructions As Boolean = Convert.ToBoolean(cmd.ExecuteScalar())

            Return promptBillingInstructions

        Catch ex As Exception
            errorMessage = ex.Message
        Finally
            con.Close()
        End Try

        If String.IsNullOrEmpty(errorMessage) = False Then
            ConsumingForm.ShowMessage(errorMessage, "Checking if it's safe to create Default Billing Instructions.")
        End If

        Return False

    End Function

    Private Sub SaveStorePools()

        ' Get the original, untouched StorePool table.
        Dim OriginalStorePoolTable As DataTable = Row.Table.ParentRelations("StorePool_Burst").ParentTable

        ' Get the added, modified and deleted StorePool rows.
        Dim Added As DataTable = _StorePoolTable.GetChanges(DataRowState.Added)
        Dim Modified As DataTable = _StorePoolTable.GetChanges(DataRowState.Modified)
        Dim Deleted As DataTable = _StorePoolTable.GetChanges(DataRowState.Deleted)

        ' Handle all the deleted rows.
        If Not IsNothing(Deleted) Then
            For Each DeletedRow As DataRow In Deleted.Rows
                ' Change the rowstate from Deleted so that we can access data in the row.
                DeletedRow.RejectChanges()
                ' Get the primary key value of this row.
                Dim Key As Object = DeletedRow("StorePoolID")
                ' Check if the deleted row existed in the original table.
                If OriginalStorePoolTable.Rows.Contains(Key) Then
                    ' Make the changes to the original table.
                    OriginalStorePoolTable.Rows.Find(Key).Delete()
                Else
                    ' The original table doesn't contain this store pool row.
                    ' Create a row for it so that it can be deleted.
                    Dim StorePoolToDelete As DataRow = OriginalStorePoolTable.NewRow()
                    StorePoolToDelete("StorePoolID") = Key
                    OriginalStorePoolTable.Rows.Add(StorePoolToDelete)
                    StorePoolToDelete.AcceptChanges()
                    StorePoolToDelete.Delete()
                End If
                DeletedRow.Delete()
            Next
        End If

        ' Handle all the newly created rows.
        If Not IsNothing(Added) Then
            For Each AddedRow As DataRow In Added.Rows
                ' Add it to the original table.
                Dim NewRow As DataRow = OriginalStorePoolTable.NewRow
                NewRow("StorePoolID") = AddedRow("StorePoolID")
                NewRow("StorePoolQty") = AddedRow("StorePoolQty")
                OriginalStorePoolTable.Rows.Add(NewRow)
            Next
        End If

        ' Handle all the modified rows.
        If Not IsNothing(Modified) Then
            For Each ModifiedRow As DataRow In Modified.Rows
                ' Get the primary key value of this row.
                Dim Key As Object = ModifiedRow("StorePoolID")
                ' Get the corresponding row of the original BurstLoadingFee table in the data set.
                Dim OriginalStorePoolRow As DataRow = OriginalStorePoolTable.Rows.Find(Key)
                ' Remember if the rowstate of the original row is 'Added'. This could happen if the user creates a new
                ' store pool row in a burst, clicks OK to accept the burst, but then before saving the contract goes back
                ' and edits the burst and changes the newly created store pool.
                Dim RowStateIsAdded As Boolean = False
                If OriginalStorePoolRow.RowState = DataRowState.Added Then
                    RowStateIsAdded = True
                End If
                ' Make the changes to the original table.
                If Not OriginalStorePoolRow("StorePoolQty") = ModifiedRow("StorePoolQty") Then
                    OriginalStorePoolRow("StorePoolQty") = ModifiedRow("StorePoolQty")
                    ' Restore the 'Added' rowstate if necessary.
                    If RowStateIsAdded AndAlso Not OriginalStorePoolRow.RowState = DataRowState.Added Then
                        OriginalStorePoolRow.SetAdded()
                    End If
                End If
            Next
        End If

        ' Dispose all the temporary tables.
        If Not IsNothing(Added) Then
            Added.Dispose()
        End If
        If Not IsNothing(Modified) Then
            Modified.Dispose()
        End If
        If Not IsNothing(Deleted) Then
            Deleted.Dispose()
        End If

    End Sub

    Private Sub SaveStoreList()

        ' Get the original, untouched StoreList table.
        Dim OriginalStoreListTable As DataTable = DataSet.Tables("StoreList")

        ' Get the added and deleted StoreList rows.
        Dim Added As DataTable = _StoreListTable.GetChanges(DataRowState.Added)
        Dim Deleted As DataTable = _StoreListTable.GetChanges(DataRowState.Deleted)

        ' Handle all the deleted rows.
        If Not IsNothing(Deleted) Then
            For Each DeletedRow As DataRow In Deleted.Rows
                ' Change the rowstate from Deleted so that we can access data in the row.
                DeletedRow.RejectChanges()
                ' Get the primary key value of this row.
                Dim Keys() As Object = {Row("BurstID"), DeletedRow("StoreID")}
                ' Check if the deleted row existed in the original table.
                If OriginalStoreListTable.Rows.Contains(Keys) Then
                    ' Make the changes to the original table.
                    OriginalStoreListTable.Rows.Find(Keys).Delete()
                End If
            Next
        End If

        ' Handle all the newly created rows.
        If Not IsNothing(Added) Then
            For Each AddedRow As DataRow In Added.Rows
                ' Add it to the original table.
                Dim NewRow As DataRow = OriginalStoreListTable.NewRow
                NewRow("BurstID") = AddedRow("BurstID")
                NewRow("StoreID") = AddedRow("StoreID")
                OriginalStoreListTable.Rows.Add(NewRow)
            Next
        End If

        ' Dispose all the temporary tables.
        If Not IsNothing(Added) Then
            Added.Dispose()
        End If
        If Not IsNothing(Deleted) Then
            Deleted.Dispose()
        End If

    End Sub




    Private Sub InitializeFields()

        ' Create a temporary StorePool table.
        _StorePoolTable = Row.Table.ParentRelations("StorePool_Burst").ParentTable.Copy
        _StorePoolTable.AcceptChanges()

        ' Create temporary BurstCategory table.
        CreateTemporaryBurstCategoryTable()

        ' Check if this is a new row being added.
        If Row.RowState = DataRowState.Detached Then
            ' It's a new row. Check if any other bursts exist in this contract.
            If _ParentContract.BurstBindingSource.List.Count > 1 Then

                ' This contract contains bursts other than this one. Auto populate fields using the first burst
                ' to make life a litlle easier for the account manager creating this burst.
                Dim FirstBurst As DataRow = CType(_ParentContract.BurstBindingSource.List(0), DataRowView).Row
                _MediaID = FirstBurst("MediaID")
                _MediaName = FirstBurst("MediaName")
                MediaFamilyIDList = FirstBurst("MediaFamilyIDList")
                _BrandID = FirstBurst("BrandID")
                _ProductName = FirstBurst("ProductName")
                _BrandID = FirstBurst("BrandID")
                _BrandName = FirstBurst("BrandName")
                _FirstWeek = FirstBurst("FirstWeek")
                _InstallWeeks = FirstBurst("InstallWeeks")
                _BillableWeeks = FirstBurst("BillableWeeks")
                _Discount = FirstBurst("Discount")
                _InstallationInstructions = FirstBurst("InstallationInstructions")
                _LastWeek = FirstBurst("LastWeek")
                _MediaAllowsHomesite = FirstBurst("MediaAllowsHomesite")
                _MediaAllowsCrossover = FirstBurst("MediaAllowsCrossover")


                ' Get the homesite of the first burst.
                Dim FirstBurstHomesiteRow As DataSetContract.BurstCategoryRow = Nothing
                Dim FirstBurstHomesiteRows() As DataSetContract.BurstCategoryRow = FirstBurst.GetChildRows("Burst_BurstCategory")
                Dim CategoryIndex As Integer = 0
                While IsNothing(FirstBurstHomesiteRow)
                    If FirstBurstHomesiteRows(CategoryIndex).Priority = 0 Then
                        FirstBurstHomesiteRow = FirstBurstHomesiteRows(CategoryIndex)
                    Else
                        CategoryIndex += 1
                    End If
                End While

                ' Update the homesite name field.
                _Homesite = FirstBurstHomesiteRow("CategoryName")

                ' Create a new homesite row in the BurstCategory table.
                Dim NewHomesite As DataRow = _BurstCategoryTable.NewRow
                NewHomesite("BurstID") = Row("BurstID")
                NewHomesite("CategoryID") = FirstBurstHomesiteRow("CategoryID")
                NewHomesite("CategoryName") = FirstBurstHomesiteRow("CategoryName")
                NewHomesite("Priority") = 0
                _BurstCategoryTable.Rows.Add(NewHomesite)

                ' Update related info.
                UpdateCategoriesColumn()

            End If
            ' Create a new store pool.
            _StorePoolID = GetNewStorePoolID()
            Exit Sub
        End If

        ' Initialize field values to row values.
        _ChainID = Row("ChainID")
        _ChainTypeID = Row("ChainTypeID")
        _ChainName = Row("ChainName")
        _StorePoolID = Row("StorePoolID")
        _MediaID = Row("MediaID")
        _MediaName = Row("MediaName")
        _BrandID = Row("BrandID")
        MediaFamilyIDList = Row("MediaFamilyIDList")
        _BrandName = Row("BrandName")
        _IsPcaStatusMedia = Row("isPNPPcaStatus")
        _ProductName = Row("ProductName")
        _FirstWeek = Row("FirstWeek")
        _InstallWeeks = Row("InstallWeeks")
        _InstallStoreQty = Row("InstallStoreQty")
        _BillableStoreQty = Row("BillableStoreQty")
        _RentalRate = Row("RentalRate")
        _BillableWeeks = Row("BillableWeeks")
        _Discount = Row("Discount")
        _CrossoverQty = Row("CrossoverQty")
        _InstallationInstructions = Row("InstallationInstructions")
        _LastWeek = Row("LastWeek")
        _MediaAllowsHomesite = Row("MediaAllowsHomesite")
        _MediaAllowsCrossover = Row("MediaAllowsCrossover")
        _StoreListConfirmed = Row("StoreListConfirmed")
        _InstallAtHomesite = Row("InstallAtHomesite")
        _ApplyInstructionsAcrossAllBursts = Row("ApplyInstructionsAcrossAllBursts")
        _AdsPerInstallation = Row("AdsPerInstallation")
        _AdsPerCrossover = Row("AdsPerCrossover")
        _AdsPerShelfTalk = Row("AdsPerShelfTalk")
        _InstallRegardlessOfStock = Row("InstallRegardlessOfStock")
        If IsDBNull(Row("Categories")) = False Then
            _Categories = Row("Categories")
        End If
        If IsDBNull(Row("CreationDate")) = False Then
            _CreationDate = Row("CreationDate")
        Else
            _CreationDate = Settings.GetServerTime(ConnectionString, ConsumingForm)
        End If
        For Each Category As DataRow In _BurstCategoryTable.Rows
            If Category("Priority") = 0 Then
                ' This category is the homesite.
                _Homesite = Category("CategoryName")
                Exit For
            End If
        Next

    End Sub

    Private Sub CreateTemporaryBurstCategoryTable()

        ' Create a table containing the BurstCategory rows belonging to this burst and set it as the value of BurstCategoryTable.
        Dim RowFilter As String = "BurstID = '" & CType(Row("BurstID"), Guid).ToString & "'"
        Dim CategoriesOfAllBursts As DataTable = Row.Table.ChildRelations("Burst_BurstCategory").ChildTable
        Dim CategoriesOfThisBurst As New DataView _
        (CategoriesOfAllBursts, RowFilter, String.Empty, DataViewRowState.CurrentRows)
        _BurstCategoryTable = CategoriesOfThisBurst.ToTable
        _BurstCategoryTable.AcceptChanges()
        CategoriesOfThisBurst.Dispose()
        ' Set the primary key for this table.
        Dim PKey(1) As DataColumn
        PKey(0) = _BurstCategoryTable.Columns("BurstID")
        PKey(1) = _BurstCategoryTable.Columns("CategoryID")
        _BurstCategoryTable.PrimaryKey = PKey

    End Sub

    Private Sub CreateTemporaryTables()

        ' Create a table containing the BurstLoadingFee rows belonging to this burst and set it as the value of _LoadingFeesTable.
        Dim LoadingFeesOfAllBursts As DataTable = Row.Table.ChildRelations("FK_BurstLoadingFee_Burst").ChildTable
        Dim RowFilter As String = "BurstID = '" & CType(Row("BurstID"), Guid).ToString & "'"
        Dim LoadingFeesOfThisBurst As New DataView _
        (LoadingFeesOfAllBursts, RowFilter, String.Empty, DataViewRowState.CurrentRows)
        _BurstLoadingFeeTable = LoadingFeesOfThisBurst.ToTable
        _BurstLoadingFeeTable.AcceptChanges()
        LoadingFeesOfThisBurst.Dispose()
        ' Set the primary key for this table.
        Dim PKey() As DataColumn = {_BurstLoadingFeeTable.Columns("BurstID"), _BurstLoadingFeeTable.Columns("LoadingFeeID")}
        _BurstLoadingFeeTable.PrimaryKey = PKey

        ' Create dataviews of the BurstCategory table for AdditionalCategories and CrossoverPreferences.
        _AdditionalCategoryView = New DataView _
        (_BurstCategoryTable, "Priority = -1", String.Empty, DataViewRowState.CurrentRows)
        _CrossoverPreferenceView = New DataView _
        (_BurstCategoryTable, "Priority > 0", "Priority", DataViewRowState.CurrentRows)


        Dim BurstInstallationDayOfAllBurts As DataTable = Row.Table.ChildRelations("FK_Burst_BurstInstallationDay").ChildTable

        Dim BurstInstallationDayOfThisBurst As New DataView _
        (BurstInstallationDayOfAllBurts, RowFilter, String.Empty, DataViewRowState.CurrentRows)
        _BurstInstallationDayTable = BurstInstallationDayOfThisBurst.ToTable
        _BurstInstallationDayTable.AcceptChanges()
        BurstInstallationDayOfThisBurst.Dispose()
        ' _BurstInstallationDayTable = Row.Table.ChildRelations("FK_Burst_BurstInstallationDay").ChildTable
        Dim BurstPcaStatusOfAllBursts As DataTable = Row.Table.ChildRelations("Burst_BurstPcaStatus").ChildTable
        Dim BurstPcaStatusOfThisBurst As New DataView _
        (BurstPcaStatusOfAllBursts, RowFilter, "CreationDate Desc", DataViewRowState.CurrentRows)
        _BurstPcaStatusTable = BurstPcaStatusOfThisBurst.ToTable
        _BurstPcaStatusTable.AcceptChanges()
        BurstPcaStatusOfThisBurst.Dispose()
        If _BurstPcaStatusTable.Rows.Count > 0 Then
            _PcaStatusName = _BurstPcaStatusTable.Rows(0)("PcaStatusName").ToString()
            _PcaStatusId = _BurstPcaStatusTable.Rows(0)("PcaStatusId")
        End If

        ' Create a temporary PeerBurst table.
        PeerBurstTable = DataSet.Tables("PeerBurst").Copy
        PeerBurstTable.AcceptChanges()

        ' Make this filter something that will return no rows. The filter will be updated as rows in the Peer
        ' view (which this view depends on) is selected.
        Dim PeerRelativeBurstViewFilter As String = "BurstID = 'WHEN IN DOUBT, MUMBLE!'"

        ' Create dataviews of the PeerBurstTable table for _PeerBurstView and _RelativeBurstView and _PeerRelativeBurstView.
        _PeerBurstView = New DataView(PeerBurstTable, PeerBurstViewFilter, String.Empty, DataViewRowState.CurrentRows)
        _RelatedBurstView = New DataView(PeerBurstTable, RelativeBurstViewFilter, String.Empty, DataViewRowState.CurrentRows)
        _PeerRelativeBurstView = New DataView(PeerBurstTable, PeerRelativeBurstViewFilter, String.Empty, DataViewRowState.CurrentRows)

        ' Create a temporary StoreList table.
        Dim StoreListView As New DataView(DataSet.Tables("StoreList"), "BurstID = '" & Row("BurstID").ToString & "'", String.Empty, DataViewRowState.CurrentRows)
        _StoreListTable = StoreListView.ToTable
        Dim Keys() As DataColumn = {_StoreListTable.Columns(0), _StoreListTable.Columns(1)}
        _StoreListTable.PrimaryKey = Keys
        _StoreListTable.AcceptChanges()

    End Sub

    Private Function PeerBurstViewFilter() As String
        Return "StorePoolID <> '" & _StorePoolID.ToString & "'" _
        & " AND BurstID <> '" & Row("BurstID").ToString & "'"
    End Function

    Private Function RelativeBurstViewFilter() As String
        Return "StorePoolID = '" & _StorePoolID.ToString & "'" _
        & " AND BurstID <> '" & Row("BurstID").ToString & "'"
    End Function

    Private Function GetNewStorePoolID() As Guid
        Return GetNewStorePoolID(-1)
    End Function

    Private Function GetNewStorePoolID(ByVal StorePoolCapacity As Integer) As Guid

        ' Create a new store pool row and set its properties.
        Dim NewStorePool As DataRow = _StorePoolTable.NewRow
        NewStorePool("StorePoolID") = Guid.NewGuid
        If StorePoolCapacity > -1 Then
            NewStorePool("StorePoolQty") = StorePoolCapacity
        End If
        ' Add the row to the table.
        _StorePoolTable.Rows.Add(NewStorePool)
        Return NewStorePool("StorePoolID")

    End Function

    Private Shared Shadows Sub AddLog _
    (ByVal AuditRow As DataRow,
    ByVal RowDescription As String,
    ByVal ChangedPropertyName As String,
    ByVal ChangedPropertyOldValue As String,
    ByVal ChangedPropertyNewValue As String,
    ByVal AuditLog As DataTable)
        ' Add an entry into the audit log table for a modified row.

        ' Exit if the row being audited is a new detached row.
        If AuditRow.RowState = DataRowState.Detached Then
            Exit Sub
        End If

        ' Create a string builder to build the log description.
        Dim ActionBuilder As New System.Text.StringBuilder("Changed the value of " & ChangedPropertyName.ToUpper & " from ")

        ' Add the old value to the string builder.
        If String.IsNullOrEmpty(ChangedPropertyOldValue) Then
            ActionBuilder.Append("an empty string")
        Else
            ActionBuilder.Append("'" & ChangedPropertyOldValue & "'")
        End If
        ActionBuilder.Append(" to ")

        ' Add the new value to the string builder.
        If String.IsNullOrEmpty(ChangedPropertyNewValue) Then
            ActionBuilder.Append("an empty string")
        Else
            ActionBuilder.Append("'" & ChangedPropertyNewValue & "'")
        End If

        ' Log the entry.
        LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, "Burst", RowDescription, ActionBuilder.ToString)

    End Sub

#End Region

End Class
