﻿using DataAccess;

namespace DataService.Security
{
    class TerminateSessionCommandExecutor : CommandExecutor<TerminateSessionCommand>
    {

        public override void Execute(TerminateSessionCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.TerminateSession))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
            }
        }

    }
}
