﻿Imports System.ComponentModel

Public Class Subform
    ' All subform objects must be inherited from this class.

    Private _NavButton As NavButton
    Private _Subforms As New List(Of Subform)
    Private _CurrentSubformIndex As Integer
    Private _ParentSubform As Subform
    Public Shadows Text As String = "Subform"
    Private _GridController As GridManager

#Region "Properties"

    Public ReadOnly Property ParentSubform() As Subform
        Get
            Return _ParentSubform
        End Get
    End Property

    Public Property NavButton() As NavButton
        Get
            Return _NavButton
        End Get
        Set(ByVal value As NavButton)
            _NavButton = value
        End Set
    End Property

    Public ReadOnly Property Subforms() As List(Of Subform)
        Get
            Return _Subforms
        End Get
    End Property

    Public Property CurrentSubformIndex() As Integer
        Get
            Return _CurrentSubformIndex
        End Get
        Set(ByVal value As Integer)
            _CurrentSubformIndex = value
        End Set
    End Property

    Public WriteOnly Property GridController() As GridManager
        Set(ByVal value As GridManager)
            _GridController = value
        End Set
    End Property

#End Region

#Region "Internal Methods"

    Private Function SaveAndRememberErrors(ByRef ErrorMessage As String) As Boolean
        ' Try and save and catch exception details into an error message.
        Try
            If Save() = False Then
                Return False
            End If
        Catch ex As Exception
            ErrorMessage = LiquidAgent.GetErrorMessage(ex)
            Return True
        End Try
        Return True
    End Function

#End Region

#Region "Protected Methods"

    Protected Overridable Function Save() As Boolean
        ' This method is intended to be overridden.
    End Function

#End Region

#Region "Public Methods"

    Public Function Save(ByVal CloseAfterSaving As Boolean) As Boolean

        If CType(Me.TopLevelControl, BaseForm).ErrorManager.ValidationSuccessful(Me) Then
            ' Control validation was successful.

            ' Create a string to hold error messages resulting from an attempt at a save operation.
            Dim SaveError As String = String.Empty
            ' Try and save.
            Dim SaveCompleted As Boolean = SaveAndRememberErrors(SaveError)
            If String.IsNullOrEmpty(SaveError) Then
                If SaveCompleted Then
                    ' Save operation was successful.
                    If CloseAfterSaving Then
                        RevertToParentSubform()
                    End If
                    Return True
                Else
                    ' Save operation didn't complete.
                    Return False
                End If
            Else
                ' Save operation was unsuccessful.
                Dim ParentForm As BaseForm = CType(TopLevelControl, BaseForm)
                ParentForm.ShowMessage(SaveError)
                Return False
            End If

        Else
            ' Control validation was unsuccessful.
            Return False

        End If

    End Function

    Public Sub AddChild(ByVal ChildSubform As Subform)

        ' Add the given subform to this subform's set of child subforms.
        ChildSubform.NavButton = _NavButton
        ChildSubform._ParentSubform = Me
        ChildSubform.Dock = DockStyle.Fill
        _Subforms.Add(ChildSubform)
        _CurrentSubformIndex = _Subforms.Count - 1

        ' Display the new child subform.
        _NavButton.DisplayContent()

    End Sub

    Public Sub RevertToParentSubform()

        ' Clear all subforms from this form's parent.
        _ParentSubform.Subforms.Clear()

        ' Display the parent subform.
        _NavButton.DisplayContent()

    End Sub

    Public Sub NextSiblingForm()
        ' Advance to the next subform in the list.
        If _ParentSubform.CurrentSubformIndex < _ParentSubform.Subforms.Count - 1 Then
            _ParentSubform.CurrentSubformIndex += 1
            _NavButton.DisplayContent()
        End If
    End Sub

    Public Sub PreviousSiblingForm()
        ' Move back to the previous subform in the list.
        If _ParentSubform.CurrentSubformIndex > 0 Then
            _ParentSubform.CurrentSubformIndex -= 1
            _NavButton.DisplayContent()
        End If
    End Sub

#End Region

End Class
