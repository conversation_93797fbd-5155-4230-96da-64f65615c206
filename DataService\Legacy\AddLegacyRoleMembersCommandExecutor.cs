﻿using DataAccess;

namespace DataService.Security
{
    class AddLegacyRoleMembersCommandExecutor : CommandExecutor<AddLegacyRoleMembersCommand>
    {

        public override void Execute(AddLegacyRoleMembersCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.AddLegacyRoleMembers))
            {
                storedprocedure.AddInputParameter("roleid", command.RoleId);
                storedprocedure.AddInputParameter("newmembers", command.NewMembers);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
            }
        }

    }
}
