﻿using DataAccess;
using DataService.StoreUniverse;
using System.Data;

namespace DataService
{
    public static class StoreUniverseServices
    {

        public static DataTable GetTableOfChains(ref string errormessage)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.LegacyConnectionString))
            {
                var command = new GetTableOfChainsCommand();
                manager.ExecuteCommand(ref errormessage, command, new GetTableOfChainsCommandExecutor());
                return command.Table;
            }
        }

        public static DataTable GetTableOfHeadOffices(ref string errormessage)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.LegacyConnectionString))
            {
                var command = new GetTableOfHeadOfficesCommand();
                manager.ExecuteCommand(ref errormessage, command, new GetTableOfHeadOfficesCommandExecutor());
                return command.Table;
            }
        }

        public static DataTable GetTableOfStores(ref string errormessage)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.LegacyConnectionString))
            {
                var command = new GetTableOfStoresCommand();
                manager.ExecuteCommand(ref errormessage, command, new GetTableOfStoresCommandExecutor());
                return command.Table;
            }
        }

    }
}
