Public Class LiquidErrorManager
    Inherits ErrorProvider

    Public Sub New()
        BlinkStyle = ErrorBlinkStyle.NeverBlink
        Icon = My.Resources.ErrorAlert
    End Sub

    Private Sub ValidateControl(ByVal ControlToCheck As Control)

        ' CheckEdit controls behave weirdly when they're setup as a radio button. Exclude them from the validation process.
        If TypeOf ControlToCheck Is CheckEdit Then
            Exit Sub
        End If

        ' Give the control focus and then remove focus so that its Validated event is fired.
        ControlToCheck.Focus()
        ControlToCheck.Parent.Focus()

        ' Check for errors in child controls.
        If Not TypeOf ControlToCheck Is TextEdit AndAlso ControlToCheck.HasChildren Then
            If TypeOf ControlToCheck Is XtraTabControl Then
                ' This control is a set of tab pages. The child controls on a tab page don't validate unless
                ' the tab is selected, for some odd reason. Select each tab so that all child controls can be validated.
                Dim TabSet As XtraTabControl = CType(ControlToCheck, XtraTabControl)
                Dim CurrentTabIndex As Integer = TabSet.SelectedTabPageIndex
                For Each Tab As XtraTabPage In TabSet.TabPages
                    TabSet.SelectedTabPage = Tab
                    For Each ChildControl As Control In Tab.Controls
                        ValidateControl(ChildControl)
                    Next
                Next
                TabSet.SelectedTabPageIndex = CurrentTabIndex
            Else
                ' Check all of this control's child controls.
                For Each ChildControl As Control In ControlToCheck.Controls
                    ValidateControl(ChildControl)
                Next
            End If
        End If

    End Sub

    Private Sub CheckControlError(ByVal ControlToCheck As Control, ByRef ErrorString As System.Text.StringBuilder)

        ' If this control has an error, add it to the error string.
        Dim ControlError As String = GetError(ControlToCheck)
        If String.IsNullOrEmpty(ControlError) = False Then
            ' This control has an error message. Add it to the error string.
            If ErrorString.Length > 0 Then
                ' Separate errors with a carriage return character.
                ErrorString.Append(vbCrLf)
            End If
            ErrorString.Append(ControlError)
        End If

        ' Check for errors in child controls.
        If Not TypeOf ControlToCheck Is TextEdit AndAlso ControlToCheck.HasChildren Then
            If TypeOf ControlToCheck Is XtraTabControl Then
                ' This control is a set of tab pages. The child controls on a tab page don't validate unless
                ' the tab is selected, for some odd reason. Select each tab so that all child controls can be validated.
                Dim TabSet As XtraTabControl = CType(ControlToCheck, XtraTabControl)
                Dim CurrentTabIndex As Integer = TabSet.SelectedTabPageIndex
                For Each Tab As XtraTabPage In TabSet.TabPages
                    TabSet.SelectedTabPage = Tab
                    For Each ChildControl As Control In Tab.Controls
                        CheckControlError(ChildControl, ErrorString)
                    Next
                Next
                TabSet.SelectedTabPageIndex = CurrentTabIndex
            Else
                ' Check all of this control's child controls.
                For Each ChildControl As Control In ControlToCheck.Controls
                    CheckControlError(ChildControl, ErrorString)
                Next
            End If
        End If

    End Sub

    Public Function ValidationSuccessful(ByVal FormToValidate As Control) As Boolean

        ' Create a stringbuilder to hold the errors.
        Dim ErrorString As New System.Text.StringBuilder

        ' Validate all controls.
        For i As Integer = 0 To FormToValidate.Controls.Count - 1
            ValidateControl(FormToValidate.Controls(i))
        Next

        ' Check all validated controls for errors.
        For i As Integer = 0 To FormToValidate.Controls.Count - 1
            CheckControlError(FormToValidate.Controls(i), ErrorString)
        Next

        ' Check if any control validation errors were added to the list.
        If ErrorString.Length = 0 Then
            ' No errors. All controls were successfully validated.
            Return True
        Else
            ' Validation errors exist.
            ' Add a message to the stringbuilder to display to the user.
            ErrorString.Insert(0, "Your input was checked and the following errors were discovered. " _
            & "Please correct these errors and try again." & vbCrLf & vbCrLf)
            ' Display the list of errors to the user.
            Dim TopContainerForm As BaseForm
            If TypeOf FormToValidate Is BaseForm Then
                TopContainerForm = CType(FormToValidate, BaseForm)
            Else
                TopContainerForm = CType(FormToValidate.TopLevelControl, BaseForm)
            End If
            TopContainerForm.ShowMessage(ErrorString.ToString, "Validation Failed")
            Return False
        End If

    End Function

End Class
