﻿using DataAccess;
using System;
using System.Data;

namespace DataService.Security
{
    internal class GetRoleOwnerCandidatesCommand : Command
    {
        public Guid SessionId;
        public Guid RoleId;
        public DataTable Table;

        public GetRoleOwnerCandidatesCommand(Guid sessionid, Guid roleid)
        {
            SessionId = sessionid;
            RoleId = roleid;
        }
    }
}
