﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="12.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.50727</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{3E5C068D-C18E-4B6D-A7D4-50C6C2365F3F}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>LiquidShell</RootNamespace>
    <AssemblyName>LiquidShell</AssemblyName>
    <MyType>Windows</MyType>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <OldToolsVersion>2.0</OldToolsVersion>
    <UpgradeBackupLocation />
    <SccProjectName>%24/Primedia Instore/Nova/Legacy/Nova2/LiquidShell</SccProjectName>
    <SccLocalPath>.</SccLocalPath>
    <SccAuxPath>https://dev.azure.com/primedia-instore</SccAuxPath>
    <SccProvider>{4CA58AB2-18FA-4F8D-95D4-32DDF27D184C}</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>LiquidShell.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>LiquidShell.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022,42353,42354,42355</NoWarn>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="DevExpress.Data.v12.2, Version=12.2.18.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\..\..\..\..\..\..\..\..\Devexpress\Framework\DevExpress.Data.v12.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Utils.v12.2, Version=12.2.18.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\..\..\..\..\..\..\..\..\Devexpress\Framework\DevExpress.Utils.v12.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraEditors.v12.2, Version=12.2.18.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\..\..\..\..\..\..\..\..\Devexpress\Framework\DevExpress.XtraEditors.v12.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraScheduler.v12.2, Version=12.2.18.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\..\..\..\..\..\..\..\..\Devexpress\Framework\DevExpress.XtraScheduler.v12.2.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.XtraScheduler.v12.2.Core, Version=12.2.18.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <HintPath>..\..\..\..\..\..\..\..\..\Devexpress\Framework\DevExpress.XtraScheduler.v12.2.Core.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="DevExpress.XtraEditors" />
    <Import Include="DevExpress.XtraTab" />
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Drawing" />
    <Import Include="System.Windows.Forms" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Content Switcher\ContentSwitcher.Designer.vb">
      <DependentUpon>ContentSwitcher.vb</DependentUpon>
    </Compile>
    <Compile Include="Content Switcher\ContentSwitcher.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Content Switcher\DefaultSubform.Designer.vb">
      <DependentUpon>DefaultSubform.vb</DependentUpon>
    </Compile>
    <Compile Include="Content Switcher\DefaultSubform.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\FormPleaseWait.Designer.vb">
      <DependentUpon>FormPleaseWait.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormPleaseWait.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LiquidErrorManager.vb">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Forms\BaseForm.Designer.vb">
      <DependentUpon>BaseForm.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\BaseForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\DateSelector.Designer.vb">
      <DependentUpon>DateSelector.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\DateSelector.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\EditorForm.Designer.vb">
      <DependentUpon>EditorForm.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\EditorForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormLogin.Designer.vb">
      <DependentUpon>FormLogin.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormLogin.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormChangePassword.Designer.vb">
      <DependentUpon>FormChangePassword.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormChangePassword.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\GridSearchForm.Designer.vb">
      <DependentUpon>GridSearchForm.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\GridSearchForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\InputBox.Designer.vb">
      <DependentUpon>InputBox.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\InputBox.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\LookupForm.Designer.vb">
      <DependentUpon>LookupForm.vb</DependentUpon>
    </Compile>
    <Compile Include="Forms\LookupForm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="GridManager.vb" />
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="LiquidAgent.vb" />
    <Compile Include="Content Switcher\NavButton.Designer.vb">
      <DependentUpon>NavButton.vb</DependentUpon>
    </Compile>
    <Compile Include="Content Switcher\NavButton.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Security\SqlIdentity.vb" />
    <Compile Include="Security\SqlPrincipal.vb" />
    <Compile Include="Content Switcher\Subform.designer.vb">
      <DependentUpon>Subform.vb</DependentUpon>
    </Compile>
    <Compile Include="Content Switcher\Subform.vb">
      <SubType>UserControl</SubType>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Content Switcher\ContentSwitcher.resx">
      <DependentUpon>ContentSwitcher.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Content Switcher\DefaultSubform.resx">
      <DependentUpon>DefaultSubform.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\BaseForm.resx">
      <DependentUpon>BaseForm.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\DateSelector.resx">
      <SubType>Designer</SubType>
      <DependentUpon>DateSelector.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\EditorForm.resx">
      <SubType>Designer</SubType>
      <DependentUpon>EditorForm.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormLogin.resx">
      <DependentUpon>FormLogin.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormChangePassword.resx">
      <DependentUpon>FormChangePassword.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormPleaseWait.resx">
      <SubType>Designer</SubType>
      <DependentUpon>FormPleaseWait.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\GridSearchForm.resx">
      <SubType>Designer</SubType>
      <DependentUpon>GridSearchForm.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\InputBox.resx">
      <DependentUpon>InputBox.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\LookupForm.resx">
      <DependentUpon>LookupForm.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\licenses.licx" />
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Content Switcher\NavButton.resx">
      <DependentUpon>NavButton.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Content Switcher\Subform.resx">
      <DependentUpon>Subform.vb</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
    <None Include="packages.config" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{94E38DFF-614B-4CBD-B67C-F211BB35CE8B}" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Resources\" />
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>