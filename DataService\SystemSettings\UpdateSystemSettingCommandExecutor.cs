﻿using DataAccess;

namespace DataService.StoreUniverse
{
    class UpdateSystemSettingCommandExecutor : CommandExecutor<UpdateSystemSettingCommand>
    {
        public override void Execute(UpdateSystemSettingCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.UpdateSystemSetting))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.AddInputParameter("settingid", command.SettingId);
                storedprocedure.AddInputParameter("settingvalue", command.SettingValue);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
            }
        }
    }
}
