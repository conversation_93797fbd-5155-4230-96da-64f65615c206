﻿using DataAccess;

namespace DataService.Security
{
    class RemoveRoleMembersCommandExecutor : CommandExecutor<RemoveRoleMembersCommand>
    {

        public override void Execute(RemoveRoleMembersCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.RemoveRoleMembers))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.AddInputParameter("roleid", command.RoleId);
                storedprocedure.AddInputParameter("members", command.Members);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
            }
        }

    }
}
