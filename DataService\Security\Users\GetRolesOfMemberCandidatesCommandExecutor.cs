﻿using DataAccess;

namespace DataService.Security
{
    internal class GetRolesOfMemberCandidatesCommandExecutor : CommandExecutor<GetRolesOfMemberCandidatesCommand>
    {
        public override void Execute(GetRolesOfMemberCandidatesCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.GetRolesOfMemberCandidates))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.AddInputParameter("userid", command.UserId);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    command.Table = storedprocedure.OutputTable;
                }
            }
        }
    }
}
