﻿using System;
using System.ComponentModel;
using System.Windows.Forms;

namespace Framework.Controls.Data
{
    [Designer(typeof(DataControlDesigner))]
    public partial class DataBox : UserControl
    {
        // These two variables MUST be assigned values in the constructors of all derived classes.
        protected Label ErrorLabel;
        protected Func<string> GetErrorMessageMethod;


        #region Startup

        public DataBox()
        {
            InitializeComponent();
            SetAppearanceProperties();
            DirtyStateManager = new DirtyStateManager(this);
            ErrorStateManager = new ErrorStateManager(this);
            Load += DataBox_Load;
        }

        private void DataBox_Load(object sender, EventArgs e)
        {
            DirtyStateManager.InitializeState();
            ErrorStateManager.InitializeState(ErrorLabel, GetErrorMessageMethod);
        }

        private void SetAppearanceProperties()
        {
            Font = FrameworkSettings.Fonts.FORMFONT;
            ForeColor = FrameworkSettings.Colors.FORMFORECOLOR;
            BackColor = FrameworkSettings.Colors.FORMBACKCOLOR;
            Margin = new Padding(0);
        }

        #endregion


        #region Dirty state

        private DirtyStateManager _DirtyStateManager;
        public DirtyStateManager DirtyStateManager
        {
            get { return _DirtyStateManager; }
            private set { _DirtyStateManager = value; }
        }

        #endregion


        #region Error state

        private ErrorStateManager _ErrorStateManager;
        public ErrorStateManager ErrorStateManager
        {
            get { return _ErrorStateManager; }
            private set { _ErrorStateManager = value; }
        }

        #endregion


        #region Value

        private object _Value = null;
        [Browsable(true)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        [Bindable(true)]
        public object Value
        {
            get { return _Value; }
            set
            {
                if (_Value != value)
                {
                    _Value = value;
                    OnValueChanged();
                }
            }
        }

        private object _OriginalValue;
        public object OriginalValue
        {
            get { return _OriginalValue; }
        }

        public void SaveOriginalValue()
        {
            _OriginalValue = Value;
        }

        public void RestoreOriginalValue()
        {
            Value = _OriginalValue;
        }

        public event EventHandler ValueChanged;
        protected virtual void OnValueChanged()
        {
            ValueChanged?.Invoke(this, EventArgs.Empty);
        }

        /// <summary>
        /// Gets or sets the name of the database column to which the DataBox is bound.
        /// </summary>
        private string _DataPropertyName = string.Empty;
        [Description("The name of the database column to which the DataBox is bound.")]
        public string DataPropertyName
        {
            get { return _DataPropertyName; }
            set { _DataPropertyName = value; }
        }

        #endregion

    }
}
