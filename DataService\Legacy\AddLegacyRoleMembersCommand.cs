﻿using DataAccess;
using System;
using System.Collections.Generic;
using System.Data;

namespace DataService.Security
{
    class AddLegacyRoleMembersCommand : Command
    {
        public Guid RoleId { get; set; }
        public DataTable NewMembers { get; set; }

        public AddLegacyRoleMembersCommand(Guid roleid, List<DataRow> newmemberslist)
        {
            RoleId = roleid;

            // Create a new table.
            NewMembers = new DataTable();
            NewMembers.Columns.Add("name", typeof(string));

            // Convert the rows in the specified list to rows that match the schema of the table and then add them to the table.
            if (newmemberslist != null && newmemberslist.Count > 0)
            {
                for (int i = 0; i < newmemberslist.Count; i++)
                {
                    DataRow newrow = NewMembers.NewRow();
                    newrow["name"] = newmemberslist[i]["username"];
                    NewMembers.Rows.Add(newrow);
                }
            }
        }
    }
}
