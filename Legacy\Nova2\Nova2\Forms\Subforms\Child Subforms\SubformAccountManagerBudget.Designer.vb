<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformAccountManagerBudget
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformAccountManagerBudget))
        Me.ButtonOK = New DevExpress.XtraEditors.SimpleButton
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton
        Me.LabelHeadingBudgetDetails = New DevExpress.XtraEditors.LabelControl
        Me.LabelFiscal = New DevExpress.XtraEditors.LabelControl
        Me.HyperlinkFiscal = New DevExpress.XtraEditors.LabelControl
        Me.LabelFirstName = New DevExpress.XtraEditors.LabelControl
        Me.TextEditBudget = New DevExpress.XtraEditors.TextEdit
        CType(Me.TextEditBudget.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!)
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Size = New System.Drawing.Size(311, 30)
        Me.LabelTitle.TabIndex = 0
        Me.LabelTitle.Text = "Account Manager Budget"
        '
        'ButtonOK
        '
        Me.ButtonOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonOK.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonOK.Appearance.Options.UseFont = True
        Me.ButtonOK.ImageIndex = 2
        Me.ButtonOK.ImageList = Me.ImageList24x24
        Me.ButtonOK.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonOK.Location = New System.Drawing.Point(424, 361)
        Me.ButtonOK.LookAndFeel.SkinName = "Black"
        Me.ButtonOK.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonOK.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonOK.Name = "ButtonOK"
        Me.ButtonOK.Size = New System.Drawing.Size(100, 28)
        Me.ButtonOK.TabIndex = 6
        Me.ButtonOK.Text = "OK"
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(530, 361)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(100, 28)
        Me.ButtonCancel.TabIndex = 7
        Me.ButtonCancel.Text = "Cancel"
        '
        'LabelHeadingBudgetDetails
        '
        Me.LabelHeadingBudgetDetails.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelHeadingBudgetDetails.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelHeadingBudgetDetails.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelHeadingBudgetDetails.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelHeadingBudgetDetails.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelHeadingBudgetDetails.LineVisible = True
        Me.LabelHeadingBudgetDetails.Location = New System.Drawing.Point(12, 57)
        Me.LabelHeadingBudgetDetails.Margin = New System.Windows.Forms.Padding(3, 3, 3, 12)
        Me.LabelHeadingBudgetDetails.Name = "LabelHeadingBudgetDetails"
        Me.LabelHeadingBudgetDetails.Size = New System.Drawing.Size(618, 18)
        Me.LabelHeadingBudgetDetails.TabIndex = 1
        Me.LabelHeadingBudgetDetails.Text = "Budget Options"
        '
        'LabelFiscal
        '
        Me.LabelFiscal.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelFiscal.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelFiscal.Location = New System.Drawing.Point(12, 93)
        Me.LabelFiscal.Name = "LabelFiscal"
        Me.LabelFiscal.Size = New System.Drawing.Size(84, 13)
        Me.LabelFiscal.TabIndex = 2
        Me.LabelFiscal.Text = "Financial Year:"
        '
        'HyperlinkFiscal
        '
        Me.HyperlinkFiscal.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkFiscal.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkFiscal.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkFiscal.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkFiscal.Location = New System.Drawing.Point(126, 93)
        Me.HyperlinkFiscal.Name = "HyperlinkFiscal"
        Me.HyperlinkFiscal.Size = New System.Drawing.Size(47, 13)
        Me.HyperlinkFiscal.TabIndex = 3
        Me.HyperlinkFiscal.Text = "Select..."
        '
        'LabelFirstName
        '
        Me.LabelFirstName.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelFirstName.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelFirstName.Location = New System.Drawing.Point(12, 119)
        Me.LabelFirstName.Name = "LabelFirstName"
        Me.LabelFirstName.Size = New System.Drawing.Size(45, 13)
        Me.LabelFirstName.TabIndex = 4
        Me.LabelFirstName.Text = "Budget:"
        '
        'TextEditBudget
        '
        Me.TextEditBudget.EditValue = ""
        Me.TextEditBudget.Location = New System.Drawing.Point(126, 116)
        Me.TextEditBudget.Name = "TextEditBudget"
        Me.TextEditBudget.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditBudget.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditBudget.Properties.Appearance.Options.UseFont = True
        Me.TextEditBudget.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditBudget.Properties.DisplayFormat.FormatString = "c"
        Me.TextEditBudget.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric
        Me.TextEditBudget.Properties.Mask.EditMask = "c"
        Me.TextEditBudget.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditBudget.Properties.MaxLength = 200
        Me.TextEditBudget.Size = New System.Drawing.Size(181, 20)
        Me.TextEditBudget.TabIndex = 5
        '
        'SubformAccountManagerBudget
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.Controls.Add(Me.LabelFiscal)
        Me.Controls.Add(Me.HyperlinkFiscal)
        Me.Controls.Add(Me.LabelFirstName)
        Me.Controls.Add(Me.TextEditBudget)
        Me.Controls.Add(Me.ButtonOK)
        Me.Controls.Add(Me.ButtonCancel)
        Me.Controls.Add(Me.LabelHeadingBudgetDetails)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Name = "SubformAccountManagerBudget"
        Me.Size = New System.Drawing.Size(642, 401)
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        Me.Controls.SetChildIndex(Me.LabelHeadingBudgetDetails, 0)
        Me.Controls.SetChildIndex(Me.ButtonCancel, 0)
        Me.Controls.SetChildIndex(Me.ButtonOK, 0)
        Me.Controls.SetChildIndex(Me.TextEditBudget, 0)
        Me.Controls.SetChildIndex(Me.LabelFirstName, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkFiscal, 0)
        Me.Controls.SetChildIndex(Me.LabelFiscal, 0)
        CType(Me.TextEditBudget.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents ButtonOK As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelHeadingBudgetDetails As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelFiscal As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkFiscal As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelFirstName As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditBudget As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList

End Class
