﻿Imports System.ComponentModel

Public Class NavButton

    Public Selected As Boolean = False
    Private _Subform As Subform
    Private _SelectedBackColor As Color = Color.CornflowerBlue

#Region "Properties"

    Private ReadOnly Property CurrentSubform() As Subform
        Get
            ' Get the current subform of all this button's subform's
            ' children and grand children and great grand children and...
            Dim ReturnSubform As Subform = _Subform
            If IsNothing(ReturnSubform) = False Then
                While ReturnSubform.Subforms.Count > 0
                    ReturnSubform = ReturnSubform.Subforms(ReturnSubform.CurrentSubformIndex)
                End While
            End If
            Return ReturnSubform
        End Get
    End Property

    <Browsable(True)> _
    Public Property SelectedBackColor() As Color
        Get
            Return _SelectedBackColor
        End Get
        Set(ByVal value As Color)
            _SelectedBackColor = value
        End Set
    End Property

    <Browsable(True)> _
    Public Overrides Property Text() As String
        Get
            Return Trim(ButtonText.Text)
        End Get
        Set(ByVal value As String)
            ButtonText.Text = value
        End Set
    End Property

    Public Property Subform() As Subform
        Get
            Return _Subform
        End Get
        Set(ByVal value As Subform)
            If IsNothing(value) = False Then
                _Subform = value
                value.NavButton = Me
            End If
        End Set
    End Property

#End Region

#Region "Methods"

    Public Sub New()

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        ApplyStyle(EventStyle.Standard)

    End Sub

    Public Sub DisplayContent()
        ' Refresh the content area with this buttons most current subform.
        Dim Switcher As ContentSwitcher = CType(Parent.Parent, ContentSwitcher)
        If IsNothing(Switcher) = False Then
            Switcher.PanelContent.Controls.Clear()
            Switcher.PanelContent.Controls.Add(CurrentSubform)
            If IsNothing(CurrentSubform) = False Then
                CurrentSubform.Focus()
            End If
        End If
    End Sub

#End Region

#Region "Event Handlers"

    Private Sub ButtonText_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles ButtonText.Click

        ' Stop if the button is already in the selected state.
        If Selected Then
            Exit Sub
        Else
            ' Apply style and reset sibling buttons.
            Selected = True
            ApplyStyle(EventStyle.Clicked)
            ResetSiblingStyles()
            ' Display the most current content.
            DisplayContent()
        End If

    End Sub

    Private Sub ButtonText_MouseEnter(ByVal sender As Object, ByVal e As System.EventArgs) Handles ButtonText.MouseEnter

        If Selected = False Then
            ' Button is not selected. Apply formatting.
            ApplyStyle(EventStyle.Highlighted)
        End If

    End Sub

    Private Sub ButtonText_MouseLeave(ByVal sender As Object, ByVal e As System.EventArgs) Handles ButtonText.MouseLeave

        If Selected = False Then
            ' Button is not selected. Reset formatting.
            ApplyStyle(EventStyle.Standard)
        End If

    End Sub

#End Region

#Region "Button Styling"

    Enum EventStyle
        Highlighted
        Standard
        Clicked
    End Enum

    Public Sub ApplyStyle(ByVal Style As EventStyle)

        ' Get the current button text font.
        Dim ButtonFont As Font = ButtonText.Font

        Select Case Style
            Case Is = EventStyle.Highlighted
                ' Apply highlighted formatting.
                Appearance.BackColor2 = Color.Black
                Appearance.BackColor = Color.Gainsboro
                ButtonText.Cursor = Cursors.Hand
                ButtonText.ForeColor = Color.White
                ButtonText.Font = New Font(ButtonFont, FontStyle.Bold)
            Case Is = EventStyle.Standard
                ' Reset to original formatting.
                Appearance.BackColor2 = Color.Gray
                Appearance.BackColor = Color.Gainsboro
                ButtonText.Cursor = Cursors.Default
                ButtonText.ForeColor = Color.White
                ButtonText.Font = New Font(ButtonFont, FontStyle.Bold)
            Case Is = EventStyle.Clicked
                ' Apply clicked formatting.
                Appearance.BackColor = _SelectedBackColor
                Appearance.BackColor2 = _SelectedBackColor
                ButtonText.Cursor = Cursors.Default
                ButtonText.ForeColor = Color.DimGray
                ButtonText.Font = New Font(ButtonFont, FontStyle.Bold)
        End Select

    End Sub

    Private Sub ResetSiblingStyles()

        ' Restyle all the other buttons to "unclick" them.
        Dim ParentContainer As Control = Me.Parent
        If IsNothing(ParentContainer) = False Then
            For Each Item As Control In ParentContainer.Controls
                If TypeOf Item Is NavButton Then
                    If Object.ReferenceEquals(Item, Me) = False Then
                        CType(Item, NavButton).Selected = False
                        CType(Item, NavButton).ApplyStyle(NavButton.EventStyle.Standard)
                    End If
                End If
            Next
        End If

    End Sub

#End Region

End Class
