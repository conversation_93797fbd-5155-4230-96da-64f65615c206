<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="ErrorManager.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="ImageList16x16.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=2.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>144, 17</value>
  </metadata>
  <data name="ImageList16x16.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj0yLjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAAC4
        CwAAAk1TRnQBSQFMAgEBBAEAARQBAAEUAQABEAEAARABAAT/AREBAAj/AUIBTQE2BwABNgMAASgDAAFA
        AwABIAMAAQEBAAEQBgABEP8A/wD/AP8A/wD/AP8A/wAmAAEYAWMBOQFnARgBYwFaAWsMAAFaAWsBGAFj
        ATkBZwEYAWMBGAFjIgABGAFjARkBYwE5AWMB9wFeDgABGAFjATkBYwE5AWMBOQFjATkBYwE5AWMBOQFj
        ATkBYwE6AWMB9wFeFAABWgFrAbwBcwGXAVYBGgFnATkBZwwAARgBYwF7AW8BtQFWARgBYwHeAXsBGAFj
        ARgBYxwAARgBXwHfAXMBkQFyAZABcgG+AXMBGAFfCgABGAFjAd4BcwHzAXIBsAFyAdEBcgHRAXIB0QFy
        AdEBcgHQAXIB0QFyAb4BcwEYAWMSAAEYAWMBvQF3ASoBDAHuASQB/wF/AVoBawoAARgBYwGcAXMB7gE9
        AgABCAEhAXoBcwHeAXcBOgFjARgBYxYAARgBXwG+AXcBCgFyASoBcgErAXIB6QFxAZwBdwEYAWMIAAE5
        AWMBEwF3AWMBdQHCAXUBwwF1AYMBdQGDAXUBwwF1AcIBdQGDAXUBjQF2AZwBaxIAARgBYwF7AW8BawEU
        AbIBOQHeAXsMAAFaAWsBOQFnARgBYwHnARwBkAFeAVQBfwE1AXsBlwF3AX0BawE5AWcSAAEYAWMB/wF3
        AQsBcgFsAXIBAwF2AQEBdgGNAXYB6QFxAb0BdwEYAV8GAAFaAWcBjAF2AYABdQHiAXUBoAF1Aa4BdgHz
        AXYBoAF1AeIBdQGgAXUBJQF2Ad4BcxIAARgBYwF8AW8BrQEYAe4BJAH+AX8BOQFnDAABOQFnAb0BcwF3
        AXsBugF/AVgBfwEGAX8B4AF+AZgBdwE6AWcSAAGcAW8BjwFyAUsBcgEjAXYB4AF1AQEBdgEBAXYBjAF2
        ASwBcgHfAXcBOQFnBAABWgFnAasBdgHAAXUBIwF2AcABdQESAXcBmgF3AaABdQEiAXYB4AF1AUUBdgH/
        AXcSAAEYAWMBnAFzAa0BHAGyATkB/wF/ATkBZwwAAVoBawHeAXcBvAF/AZkBfwFIAX8BAAF/AQABfwEA
        AX8BvQF3ARgBYw4AARgBYwF6AXcBKgFyAYcBdgEAAXYBIgF2ASIBdgEAAXYBZAF2AUsBdgEVAXcBOQFj
        BAABWgFnAcwBdgHgAXUBQgF2AcABdQFXAXcB3QF3AcABdQEiAXYBAAF2AWUBdgH/AXcSAAEYAWMBnAFz
        Ac4BHAGzATkB/wF/AVoBaw4AAZwBbwG6AX8BjwF/AWoBfwEkAX8BAAF/AeABfgEjAXsB3wF3AfgBXgwA
        Ab0BbwFtAXYBigF2ASABdgFBAXYBQQF2AUEBdgFCAXYBIAF2AakBdgEqAXIB3wF7ATkBZwIAAXsBawHK
        AXYB4AF1AUIBdgEAAXYBDwF3AVMBdwEAAXYBQgF2AQABdgFjAXYB3gF3AVoBawEYAWMBGAFjAVoBawoA
        ARgBYwGdAXMB7gEgAfQBQQH/AX8BOQFnDgABWgFnAdwBewGPAX8BjwF/AWsBfwEjAXsBAAF/AeABfgFK
        AXsB3wF3ARgBYwgAATkBYwG8AXsBSgF2AaYBdgFAAXYBYQF2AWEBdgFhAXYBYQF2AUABdgGDAXYBawF2
        AVgBdwEZAWMCAAFaAWcBMAF7AQABdgEgAXYBIAF2AUMBdgFEAXYBIAF2AUEBdgEAAXYBygF2AZwBbwE5
        AWcBWgFrAVsBawEZAWMIAAFaAWsBWgFrAb0BdwEwAS0BMAEpAZ0BcwF7AW8BWgFrDgABfAFvAdoBewGv
        AX8BjwF/AUoBfwEjAXsBAAF/AeABfgGPAX8BvQFzATkBZwYAATkBZwE1AXcBrAF2AYEBegFhAXoBgQF6
        AYEBegGBAXoBgQF6AYEBegFgAXoBqgF2AdEBdgFaAWcBOQFnATkBZwGaAXcBzgF6AcwBegHNAXoBzAF6
        Ac0BegHtAXoBQAF2AYcBegGYAXcBOQFnAXsBawEQAXsBMgF7ATkBYwgAAVkBZwG9AXcBsgE1AZIBNQGS
        ATUBkgE1AZ0BcwFaAWsOAAFaAWsB3gF3AdgBfwGPAX8BjwF/AUkBfwEgAX8BAAF/Ae0BYgF9AW8BewFv
        BAABOQFnAXsBawHRAXYByQF6AYABegGBAXoBgQF6AYEBegGBAXoBgQF6AYEBegGAAXoByAF6AY0BdgGc
        AXMBGAFjAVoBawGcAXMBvQFzAZsBcwGcAXMBnAFzAZwBcwH+AXsBAAF2AVEBewG9AXMBOQFnAf8BewEg
        AXYBowF6AXsBawYAAXsBbwH/AX8B0wE9AZIBNQGzATkBswE5AZIBNQGzATkB/wF/AXsBbw4AAToBZwH/
        AXsBtQF/AY8BfwGNAX8BaQF/AVYBZwGOAVEBYAFMAb0BewE5AWMBOQFnARgBYwGcAXMBrQF2AecBegGA
        AXoBoAF6AaABegGgAXoBoAF6AaABegGgAXoBgAF6AeUBegGMAXYB3wF7AfcBXgQAAVoBawFaAWcBOQFn
        ATkBZwEYAWMB/wF7AQABegFSAXsBWgFnATkBZwH/AX8BYAF6AcQBegF7AWsGAAGcAXMB3wF7AZEBMQHT
        AT0BswE5AbMBOQHTAT0BcQExAd4BewGcAXMQAAE6AWcB/wF/AbIBfwG0AXsBWwFnAaoBXQGgAVgBQAFM
        AWoBWQGcAXMB9wFeAfcBXgHeAXsBZwF2AckBegEJAXsBBQF7AcABegHAAXoBwAF6AcABegEEAXsBCQF7
        AeoBegFnAXYB/wF/AfcBXgwAARgBYwH/AX8BQAF6AS0CewFrAVoBawH+AX8BYAF6AeYBegF7AWsGAAGc
        AXMB/wF/AbIBOQGzATkBvQF3Ad4BewGzATkBkgE1Af8BfwGcAXMSAAFaAWsB/wF/ARYBawGEAWUBIAFl
        ASIBXQFAAUwBDwFmAZwBcwEYAWMBOQFnAZwBbwGZAX8BEgF7Ac4BegGqAXYBLAF7AQABewHgAXoBLAF7
        AasBdgHNAXoBEgF7AZoBfwGcAXMBOQFnDAABOQFnAf8BewGjAXoBoAF6AdkBfwGYAXcBlgF/AUABegFP
        AX8BWgFnBgABWgFrAb0BdwH6AWIBcQExAXwBbwGdAXMBcQExAdkBXgHeAnsBbxQAAZwBcwE1AXsBIAFt
        ASABZQHAAVgBTwFqAf8BfwFaAWsEAAFaAWsBWgFrAXsBbwG9AXcBuwF/AasBdgELAXsBLAF7AaoBegGZ
        AX8B3gF3AXsBbwFaAWsBWgFrEAABnAFzAZQBfwGDAXoBwAF6AeEBegGBAXoBxAF6Af8BewEYAWMIAAE5
        AWcB/wF/AVsBawFWAUoBVgFKATsBZwH/AX8BOQFnFgABWgFrAd4BewHzAXYBhQFpARYBdwHeAXsBWgFr
        DgABWgFrAf8BfwHuAXoBywF6Af8BfwFaAWsYAAE5AWcB/wF/AbgBfwECAXsB4QF6AUkBewH/AX8BewFv
        DAABWgFrAXsBbwG9AXcBvQF3AXsBbwFaAWsaAAE5AWcBnAFzAb0BdwF7AW8BWgFrEgABOQFnAZwBcwG9
        AXcBOQFnHAABWgFrAVoBawG9AXcBvQF3AXsBbwE5AWcEAAFCAU0BPgcAAT4DAAEoAwABQAMAASADAAEB
        AQABAQYAAQEWAAP/gQAB/AE/AQcB/wH8AT8BgAEfAfgBPwEBAf8B+AEfAQABDwH4AR8BAAF/AfABDwEA
        AQ8B+AE/AQABPwHgAQcBAAEPAfgBHwGAAT8B4AEDAQABDwH4AR8BgAEfAcABAwEAAQ8B+AEfAcABDwHA
        AQECAAH4AR8BwAEHAYABAQIAAfABDwHgAQMBgAMAAfABDwHgAQMEAAHgAQcB8AMAAcABAAHgAQcB+AMA
        AfwBAAHgAQcB/AMAAfwBAAHgAQcB/gEBAYABAQH+AQAB8AEPAf4BAwH4AR8B/gEBAfgBHwH/AQcB/AE/
        Af8BAws=
</value>
  </data>
</root>