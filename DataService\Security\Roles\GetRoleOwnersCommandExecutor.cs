﻿using DataAccess;

namespace DataService.Security
{
    internal class GetRoleOwnersCommandExecutor : CommandExecutor<GetRoleOwnersCommand>
    {
        public override void Execute(GetRoleOwnersCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.GetRoleOwners))
            {
                storedprocedure.AddInputParameter("sessionid", command.SessionId);
                storedprocedure.AddInputParameter("roleid", command.RoleId);
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    command.Table = storedprocedure.OutputTable;
                }
            }
        }
    }
}
