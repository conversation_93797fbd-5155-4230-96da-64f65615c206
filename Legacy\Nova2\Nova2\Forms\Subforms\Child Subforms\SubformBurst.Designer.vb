<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformBurst
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformBurst))
        Dim SuperToolTip5 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem5 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem5 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle21 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle22 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle25 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle23 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle24 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle26 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle27 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle28 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle29 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle30 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle31 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle32 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle33 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle37 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle34 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle35 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle36 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim SuperToolTip1 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem1 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem1 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip2 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem2 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem2 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip3 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem3 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem3 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle38 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle39 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle40 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonOK = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.ImageList16x16 = New System.Windows.Forms.ImageList(Me.components)
        Me.TabControl = New DevExpress.XtraTab.XtraTabControl()
        Me.TabPageSummary = New DevExpress.XtraTab.XtraTabPage()
        Me.PanelSummary = New System.Windows.Forms.Panel()
        Me.TableLayoutPanelSummary = New System.Windows.Forms.TableLayoutPanel()
        Me.PanelExecution = New System.Windows.Forms.Panel()
        Me.LabelPCAStatus = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkPcaStatus = New DevExpress.XtraEditors.LabelControl()
        Me.LabelExecutionDetailsHeading = New DevExpress.XtraEditors.LabelControl()
        Me.LabelChain = New DevExpress.XtraEditors.LabelControl()
        Me.LabelMediaService = New DevExpress.XtraEditors.LabelControl()
        Me.LabelBrand = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkMediaService = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkChain = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkBrand = New DevExpress.XtraEditors.LabelControl()
        Me.LabelFirstWeek = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkFirstWeek = New DevExpress.XtraEditors.LabelControl()
        Me.LabelStoresThatAllowMedia = New DevExpress.XtraEditors.LabelControl()
        Me.LabelStoresThatAllowMediaValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelTotalUniverseValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelTotalUniverse = New DevExpress.XtraEditors.LabelControl()
        Me.LabelTotalStores = New DevExpress.XtraEditors.LabelControl()
        Me.LabelTotalWeeks = New DevExpress.XtraEditors.LabelControl()
        Me.LabelLastWeek = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditInstallStoreQty = New DevExpress.XtraEditors.TextEdit()
        Me.TextEditInstallWeeks = New DevExpress.XtraEditors.TextEdit()
        Me.HyperlinkLastWeek = New DevExpress.XtraEditors.LabelControl()
        Me.PanelRental = New System.Windows.Forms.Panel()
        Me.HyperlinkApplyDatesAccrossBursts = New DevExpress.XtraEditors.LabelControl()
        Me.PictureApplyDatesOnAllBursts = New DevExpress.XtraEditors.PictureEdit()
        Me.LabelRentalHeading = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditFreeWeeks = New DevExpress.XtraEditors.TextEdit()
        Me.LabelLoadingFees = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditDiscount = New DevExpress.XtraEditors.TextEdit()
        Me.LabelDiscount = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditBillableWeeks = New DevExpress.XtraEditors.TextEdit()
        Me.LabelRentalAmount = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditFreeStoreQty = New DevExpress.XtraEditors.TextEdit()
        Me.LabelDiscountAmount = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditBillableStoreQty = New DevExpress.XtraEditors.TextEdit()
        Me.LabelRentalRate = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditRentalAmount = New DevExpress.XtraEditors.TextEdit()
        Me.LabelNetRental = New DevExpress.XtraEditors.LabelControl()
        Me.LabelBillableWeeks = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditNetRental = New DevExpress.XtraEditors.TextEdit()
        Me.LabelFreeWeeks = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditDiscountAmount = New DevExpress.XtraEditors.TextEdit()
        Me.TextEditRentalRate = New DevExpress.XtraEditors.TextEdit()
        Me.TextEditLoadingFees = New DevExpress.XtraEditors.TextEdit()
        Me.LabelBillableStoreQty = New DevExpress.XtraEditors.LabelControl()
        Me.LabelFreeStoreQty = New DevExpress.XtraEditors.LabelControl()
        Me.TabPageLoadingFees = New DevExpress.XtraTab.XtraTabPage()
        Me.PanelLoadingFees = New System.Windows.Forms.Panel()
        Me.TableLayoutPanelLoadingFees = New System.Windows.Forms.TableLayoutPanel()
        Me.LabelLoadingFeesInfo = New DevExpress.XtraEditors.LabelControl()
        Me.PanelLoadingFeesGrid = New System.Windows.Forms.Panel()
        Me.GroupControlLoadingFees = New DevExpress.XtraEditors.GroupControl()
        Me.ButtonRemoveLoadingFee = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAddLoadingFee = New DevExpress.XtraEditors.SimpleButton()
        Me.GridLoadingFees = New System.Windows.Forms.DataGridView()
        Me.LoadingFeeNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.LoadingFeePercentageColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.LoadingFeeAmountColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.LabelLoadingFeeTotalValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelLoadingFeeTotal = New DevExpress.XtraEditors.LabelControl()
        Me.TabPageCategories = New DevExpress.XtraTab.XtraTabPage()
        Me.PanelCategories = New System.Windows.Forms.Panel()
        Me.TableLayoutPanelCategories = New System.Windows.Forms.TableLayoutPanel()
        Me.PanelCategoriesRight = New System.Windows.Forms.Panel()
        Me.LabelCrossoversHeading = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditCrossoverQty = New DevExpress.XtraEditors.TextEdit()
        Me.LabelCrossoverQty = New DevExpress.XtraEditors.LabelControl()
        Me.GroupCrossoverPreferences = New DevExpress.XtraEditors.GroupControl()
        Me.ButtonIncreasePriority = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonDecreasePriority = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonRemoveCrossoverPreference = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAddCrossoverPreference = New DevExpress.XtraEditors.SimpleButton()
        Me.GridCrossoverPreferences = New System.Windows.Forms.DataGridView()
        Me.CrossoverCategoryNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.CrossoverPriorityColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.PanelCategoriesLeft = New System.Windows.Forms.Panel()
        Me.LabelHomesiteHeading = New DevExpress.XtraEditors.LabelControl()
        Me.LabelHomesite = New DevExpress.XtraEditors.LabelControl()
        Me.LabelAdditionalCategoriesInfo = New DevExpress.XtraEditors.LabelControl()
        Me.LabelAdditionalCategoriesHeading = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkHomesite = New DevExpress.XtraEditors.LabelControl()
        Me.GroupAdditionalCategories = New DevExpress.XtraEditors.GroupControl()
        Me.ButtonRemoveAdditionalCategory = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAddAdditionalCategory = New DevExpress.XtraEditors.SimpleButton()
        Me.GridAdditionalCategories = New System.Windows.Forms.DataGridView()
        Me.AdditionalCategoryNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.TabPageStoreList = New DevExpress.XtraTab.XtraTabPage()
        Me.PanelStoreList = New System.Windows.Forms.Panel()
        Me.PanelStoreListOptions = New System.Windows.Forms.Panel()
        Me.LabelStoreListSize = New DevExpress.XtraEditors.LabelControl()
        Me.LabelStoreListSizeValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelEditStoreList = New DevExpress.XtraEditors.LabelControl()
        Me.CheckEditConfirmed = New DevExpress.XtraEditors.CheckEdit()
        Me.ButtonEditStoreList = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelStoreListTitle = New DevExpress.XtraEditors.LabelControl()
        Me.PanelStoreSharing = New System.Windows.Forms.Panel()
        Me.GroupRelatedBursts = New DevExpress.XtraEditors.GroupControl()
        Me.ButtonRemoveBurst = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAddBurst = New DevExpress.XtraEditors.SimpleButton()
        Me.GridRelatedBursts = New System.Windows.Forms.DataGridView()
        Me.RelativeContractNumberColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.RelativeBrandNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.RelativeFirstWeekColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.RelativeLastWeekColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.RelativeInstallStoreQty = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.LabelMax = New DevExpress.XtraEditors.LabelControl()
        Me.LabelMin = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditStorePoolCapacity = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelStorePoolQty = New DevExpress.XtraEditors.LabelControl()
        Me.LabelStoreListOptionsTitle = New DevExpress.XtraEditors.LabelControl()
        Me.TabPageInstallationInstructions = New DevExpress.XtraTab.XtraTabPage()
        Me.PanelInstallationInstructions = New System.Windows.Forms.Panel()
        Me.HyperlinkApplyInstuctionsOnAllBursts = New DevExpress.XtraEditors.LabelControl()
        Me.PictureApplyInstuctionsOnAllBursts = New DevExpress.XtraEditors.PictureEdit()
        Me.CheckEditDoNotInstallRegardless = New DevExpress.XtraEditors.CheckEdit()
        Me.CheckEditInstallRegardless = New DevExpress.XtraEditors.CheckEdit()
        Me.TextEditAdsPerShelfTalk = New DevExpress.XtraEditors.TextEdit()
        Me.LabelAdsPerInstallationShelfTalk = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl2 = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditAdsPerCrossover = New DevExpress.XtraEditors.TextEdit()
        Me.PanelHomesiteInstallationOptions = New System.Windows.Forms.Panel()
        Me.PictureInstallAtHomesite = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureDontInstallAtHomesite = New DevExpress.XtraEditors.PictureEdit()
        Me.HyperlinkInstallAtHomesite = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkDontInstallAtHomesite = New DevExpress.XtraEditors.LabelControl()
        Me.LabelHomesiteInstallationTitle = New DevExpress.XtraEditors.LabelControl()
        Me.LabelProductNameTitle = New DevExpress.XtraEditors.LabelControl()
        Me.LabelAdsPerInstallation = New DevExpress.XtraEditors.LabelControl()
        Me.LabelProductName = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditAdsPerHomesite = New DevExpress.XtraEditors.TextEdit()
        Me.TextEditProductName = New DevExpress.XtraEditors.TextEdit()
        Me.GroupInstallationInstructions = New DevExpress.XtraEditors.GroupControl()
        Me.MemoEditInstallationInstructions = New DevExpress.XtraEditors.MemoEdit()
        Me.TabPageInteractionDates = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.GroupControlInstallationDays = New DevExpress.XtraEditors.GroupControl()
        Me.ButtonDeleteInstallationDay = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAddInstallationDay = New DevExpress.XtraEditors.SimpleButton()
        Me.GridInstallationDays = New System.Windows.Forms.DataGridView()
        Me.InstallationDayNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.LabelInterctionDateTitle = New DevExpress.XtraEditors.LabelControl()
        Me.LabelWarningMessage = New DevExpress.XtraEditors.LabelControl()
        CType(Me.TabControl, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabControl.SuspendLayout()
        Me.TabPageSummary.SuspendLayout()
        Me.PanelSummary.SuspendLayout()
        Me.TableLayoutPanelSummary.SuspendLayout()
        Me.PanelExecution.SuspendLayout()
        CType(Me.TextEditInstallStoreQty.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditInstallWeeks.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelRental.SuspendLayout()
        CType(Me.PictureApplyDatesOnAllBursts.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditFreeWeeks.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditDiscount.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditBillableWeeks.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditFreeStoreQty.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditBillableStoreQty.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditRentalAmount.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditNetRental.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditDiscountAmount.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditRentalRate.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditLoadingFees.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabPageLoadingFees.SuspendLayout()
        Me.PanelLoadingFees.SuspendLayout()
        Me.TableLayoutPanelLoadingFees.SuspendLayout()
        Me.PanelLoadingFeesGrid.SuspendLayout()
        CType(Me.GroupControlLoadingFees, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlLoadingFees.SuspendLayout()
        CType(Me.GridLoadingFees, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabPageCategories.SuspendLayout()
        Me.PanelCategories.SuspendLayout()
        Me.TableLayoutPanelCategories.SuspendLayout()
        Me.PanelCategoriesRight.SuspendLayout()
        CType(Me.TextEditCrossoverQty.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupCrossoverPreferences, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupCrossoverPreferences.SuspendLayout()
        CType(Me.GridCrossoverPreferences, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelCategoriesLeft.SuspendLayout()
        CType(Me.GroupAdditionalCategories, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupAdditionalCategories.SuspendLayout()
        CType(Me.GridAdditionalCategories, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabPageStoreList.SuspendLayout()
        Me.PanelStoreList.SuspendLayout()
        Me.PanelStoreListOptions.SuspendLayout()
        CType(Me.CheckEditConfirmed.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelStoreSharing.SuspendLayout()
        CType(Me.GroupRelatedBursts, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupRelatedBursts.SuspendLayout()
        CType(Me.GridRelatedBursts, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditStorePoolCapacity.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabPageInstallationInstructions.SuspendLayout()
        Me.PanelInstallationInstructions.SuspendLayout()
        CType(Me.PictureApplyInstuctionsOnAllBursts.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditDoNotInstallRegardless.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.CheckEditInstallRegardless.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditAdsPerShelfTalk.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditAdsPerCrossover.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelHomesiteInstallationOptions.SuspendLayout()
        CType(Me.PictureInstallAtHomesite.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureDontInstallAtHomesite.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditAdsPerHomesite.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditProductName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupInstallationInstructions, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupInstallationInstructions.SuspendLayout()
        CType(Me.MemoEditInstallationInstructions.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabPageInteractionDates.SuspendLayout()
        Me.Panel1.SuspendLayout()
        CType(Me.GroupControlInstallationDays, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlInstallationDays.SuspendLayout()
        CType(Me.GridInstallationDays, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!)
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Margin = New System.Windows.Forms.Padding(5, 5, 5, 21)
        Me.LabelTitle.Size = New System.Drawing.Size(66, 37)
        Me.LabelTitle.TabIndex = 0
        Me.LabelTitle.Text = "Burst"
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        '
        'ButtonOK
        '
        Me.ButtonOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonOK.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonOK.Appearance.Options.UseFont = True
        Me.ButtonOK.ImageIndex = 2
        Me.ButtonOK.ImageList = Me.ImageList24x24
        Me.ButtonOK.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonOK.Location = New System.Drawing.Point(778, 666)
        Me.ButtonOK.LookAndFeel.SkinName = "Black"
        Me.ButtonOK.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonOK.Margin = New System.Windows.Forms.Padding(4, 16, 4, 4)
        Me.ButtonOK.Name = "ButtonOK"
        Me.ButtonOK.Size = New System.Drawing.Size(129, 37)
        Me.ButtonOK.TabIndex = 2
        Me.ButtonOK.Text = "OK"
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(914, 666)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Margin = New System.Windows.Forms.Padding(4, 16, 4, 4)
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(129, 37)
        Me.ButtonCancel.TabIndex = 3
        Me.ButtonCancel.Text = "Cancel"
        '
        'ImageList16x16
        '
        Me.ImageList16x16.ImageStream = CType(resources.GetObject("ImageList16x16.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList16x16.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList16x16.Images.SetKeyName(0, "add.png")
        Me.ImageList16x16.Images.SetKeyName(1, "pencil.png")
        Me.ImageList16x16.Images.SetKeyName(2, "delete.png")
        Me.ImageList16x16.Images.SetKeyName(3, "up.png")
        Me.ImageList16x16.Images.SetKeyName(4, "down.png")
        Me.ImageList16x16.Images.SetKeyName(5, "next.png")
        Me.ImageList16x16.Images.SetKeyName(6, "back.png")
        Me.ImageList16x16.Images.SetKeyName(7, "upload.png")
        Me.ImageList16x16.Images.SetKeyName(8, "download.png")
        Me.ImageList16x16.Images.SetKeyName(9, "folder.png")
        Me.ImageList16x16.Images.SetKeyName(10, "shopping_cart.png")
        '
        'TabControl
        '
        Me.TabControl.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TabControl.AppearancePage.Header.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TabControl.AppearancePage.Header.Options.UseFont = True
        Me.TabControl.Location = New System.Drawing.Point(15, 75)
        Me.TabControl.LookAndFeel.SkinName = "Black"
        Me.TabControl.LookAndFeel.UseDefaultLookAndFeel = False
        Me.TabControl.Margin = New System.Windows.Forms.Padding(4)
        Me.TabControl.Name = "TabControl"
        Me.TabControl.SelectedTabPage = Me.TabPageSummary
        Me.TabControl.Size = New System.Drawing.Size(1027, 571)
        Me.TabControl.TabIndex = 27
        Me.TabControl.TabPages.AddRange(New DevExpress.XtraTab.XtraTabPage() {Me.TabPageSummary, Me.TabPageLoadingFees, Me.TabPageCategories, Me.TabPageStoreList, Me.TabPageInstallationInstructions, Me.TabPageInteractionDates})
        '
        'TabPageSummary
        '
        Me.TabPageSummary.Controls.Add(Me.PanelSummary)
        Me.TabPageSummary.Margin = New System.Windows.Forms.Padding(4)
        Me.TabPageSummary.Name = "TabPageSummary"
        Me.TabPageSummary.Size = New System.Drawing.Size(1021, 539)
        Me.TabPageSummary.Text = "Summary"
        '
        'PanelSummary
        '
        Me.PanelSummary.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelSummary.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelSummary.Controls.Add(Me.TableLayoutPanelSummary)
        Me.PanelSummary.Location = New System.Drawing.Point(4, 4)
        Me.PanelSummary.Margin = New System.Windows.Forms.Padding(4)
        Me.PanelSummary.Name = "PanelSummary"
        Me.PanelSummary.Padding = New System.Windows.Forms.Padding(12)
        Me.PanelSummary.Size = New System.Drawing.Size(1013, 531)
        Me.PanelSummary.TabIndex = 0
        '
        'TableLayoutPanelSummary
        '
        Me.TableLayoutPanelSummary.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanelSummary.ColumnCount = 3
        Me.TableLayoutPanelSummary.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanelSummary.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 19.0!))
        Me.TableLayoutPanelSummary.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 399.0!))
        Me.TableLayoutPanelSummary.Controls.Add(Me.PanelExecution, 0, 0)
        Me.TableLayoutPanelSummary.Controls.Add(Me.PanelRental, 2, 0)
        Me.TableLayoutPanelSummary.Location = New System.Drawing.Point(15, 16)
        Me.TableLayoutPanelSummary.Margin = New System.Windows.Forms.Padding(4)
        Me.TableLayoutPanelSummary.Name = "TableLayoutPanelSummary"
        Me.TableLayoutPanelSummary.RowCount = 1
        Me.TableLayoutPanelSummary.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanelSummary.Size = New System.Drawing.Size(982, 498)
        Me.TableLayoutPanelSummary.TabIndex = 0
        '
        'PanelExecution
        '
        Me.PanelExecution.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelExecution.Controls.Add(Me.LabelPCAStatus)
        Me.PanelExecution.Controls.Add(Me.HyperlinkPcaStatus)
        Me.PanelExecution.Controls.Add(Me.LabelExecutionDetailsHeading)
        Me.PanelExecution.Controls.Add(Me.LabelChain)
        Me.PanelExecution.Controls.Add(Me.LabelMediaService)
        Me.PanelExecution.Controls.Add(Me.LabelBrand)
        Me.PanelExecution.Controls.Add(Me.HyperlinkMediaService)
        Me.PanelExecution.Controls.Add(Me.HyperlinkChain)
        Me.PanelExecution.Controls.Add(Me.HyperlinkBrand)
        Me.PanelExecution.Controls.Add(Me.LabelFirstWeek)
        Me.PanelExecution.Controls.Add(Me.HyperlinkFirstWeek)
        Me.PanelExecution.Controls.Add(Me.LabelStoresThatAllowMedia)
        Me.PanelExecution.Controls.Add(Me.LabelStoresThatAllowMediaValue)
        Me.PanelExecution.Controls.Add(Me.LabelTotalUniverseValue)
        Me.PanelExecution.Controls.Add(Me.LabelTotalUniverse)
        Me.PanelExecution.Controls.Add(Me.LabelTotalStores)
        Me.PanelExecution.Controls.Add(Me.LabelTotalWeeks)
        Me.PanelExecution.Controls.Add(Me.LabelLastWeek)
        Me.PanelExecution.Controls.Add(Me.TextEditInstallStoreQty)
        Me.PanelExecution.Controls.Add(Me.TextEditInstallWeeks)
        Me.PanelExecution.Controls.Add(Me.HyperlinkLastWeek)
        Me.PanelExecution.Location = New System.Drawing.Point(0, 0)
        Me.PanelExecution.Margin = New System.Windows.Forms.Padding(0)
        Me.PanelExecution.Name = "PanelExecution"
        Me.PanelExecution.Size = New System.Drawing.Size(564, 498)
        Me.PanelExecution.TabIndex = 0
        '
        'LabelPCAStatus
        '
        Me.LabelPCAStatus.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelPCAStatus.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelPCAStatus.Location = New System.Drawing.Point(1, 348)
        Me.LabelPCAStatus.Margin = New System.Windows.Forms.Padding(0, 4, 4, 13)
        Me.LabelPCAStatus.Name = "LabelPCAStatus"
        Me.LabelPCAStatus.Size = New System.Drawing.Size(114, 17)
        Me.LabelPCAStatus.TabIndex = 15
        Me.LabelPCAStatus.Text = "Artwork Status:"
        '
        'HyperlinkPcaStatus
        '
        Me.HyperlinkPcaStatus.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.HyperlinkPcaStatus.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkPcaStatus.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkPcaStatus.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkPcaStatus.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkPcaStatus.AutoEllipsis = True
        Me.HyperlinkPcaStatus.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.HyperlinkPcaStatus.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkPcaStatus.Location = New System.Drawing.Point(264, 348)
        Me.HyperlinkPcaStatus.Margin = New System.Windows.Forms.Padding(4, 4, 0, 13)
        Me.HyperlinkPcaStatus.Name = "HyperlinkPcaStatus"
        Me.HyperlinkPcaStatus.Size = New System.Drawing.Size(301, 17)
        Me.HyperlinkPcaStatus.TabIndex = 16
        Me.HyperlinkPcaStatus.Text = "Select..."
        '
        'LabelExecutionDetailsHeading
        '
        Me.LabelExecutionDetailsHeading.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelExecutionDetailsHeading.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelExecutionDetailsHeading.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelExecutionDetailsHeading.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelExecutionDetailsHeading.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelExecutionDetailsHeading.LineVisible = True
        Me.LabelExecutionDetailsHeading.Location = New System.Drawing.Point(0, 0)
        Me.LabelExecutionDetailsHeading.Margin = New System.Windows.Forms.Padding(0, 0, 0, 12)
        Me.LabelExecutionDetailsHeading.Name = "LabelExecutionDetailsHeading"
        Me.LabelExecutionDetailsHeading.Size = New System.Drawing.Size(564, 24)
        Me.LabelExecutionDetailsHeading.TabIndex = 0
        Me.LabelExecutionDetailsHeading.Text = "Execution Details"
        '
        'LabelChain
        '
        Me.LabelChain.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelChain.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelChain.Location = New System.Drawing.Point(0, 43)
        Me.LabelChain.Margin = New System.Windows.Forms.Padding(0, 4, 4, 13)
        Me.LabelChain.Name = "LabelChain"
        Me.LabelChain.Size = New System.Drawing.Size(45, 17)
        Me.LabelChain.TabIndex = 1
        Me.LabelChain.Text = "Chain:"
        '
        'LabelMediaService
        '
        Me.LabelMediaService.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelMediaService.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelMediaService.Location = New System.Drawing.Point(0, 145)
        Me.LabelMediaService.Margin = New System.Windows.Forms.Padding(0, 4, 4, 13)
        Me.LabelMediaService.Name = "LabelMediaService"
        Me.LabelMediaService.Size = New System.Drawing.Size(100, 17)
        Me.LabelMediaService.TabIndex = 7
        Me.LabelMediaService.Text = "Media Service:"
        '
        'LabelBrand
        '
        Me.LabelBrand.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelBrand.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelBrand.Location = New System.Drawing.Point(0, 179)
        Me.LabelBrand.Margin = New System.Windows.Forms.Padding(0, 4, 4, 13)
        Me.LabelBrand.Name = "LabelBrand"
        Me.LabelBrand.Size = New System.Drawing.Size(48, 17)
        Me.LabelBrand.TabIndex = 9
        Me.LabelBrand.Text = "Brand:"
        '
        'HyperlinkMediaService
        '
        Me.HyperlinkMediaService.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.HyperlinkMediaService.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkMediaService.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkMediaService.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkMediaService.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkMediaService.AutoEllipsis = True
        Me.HyperlinkMediaService.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.HyperlinkMediaService.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkMediaService.Location = New System.Drawing.Point(264, 145)
        Me.HyperlinkMediaService.Margin = New System.Windows.Forms.Padding(4, 4, 0, 13)
        Me.HyperlinkMediaService.Name = "HyperlinkMediaService"
        Me.HyperlinkMediaService.Size = New System.Drawing.Size(301, 17)
        Me.HyperlinkMediaService.TabIndex = 8
        Me.HyperlinkMediaService.Text = "Select..."
        '
        'HyperlinkChain
        '
        Me.HyperlinkChain.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.HyperlinkChain.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkChain.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkChain.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkChain.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkChain.AutoEllipsis = True
        Me.HyperlinkChain.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.HyperlinkChain.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkChain.Location = New System.Drawing.Point(264, 43)
        Me.HyperlinkChain.Margin = New System.Windows.Forms.Padding(4, 4, 0, 13)
        Me.HyperlinkChain.Name = "HyperlinkChain"
        Me.HyperlinkChain.Size = New System.Drawing.Size(301, 17)
        Me.HyperlinkChain.TabIndex = 2
        Me.HyperlinkChain.Text = "Select..."
        '
        'HyperlinkBrand
        '
        Me.HyperlinkBrand.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.HyperlinkBrand.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkBrand.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkBrand.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkBrand.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap
        Me.HyperlinkBrand.AutoEllipsis = True
        Me.HyperlinkBrand.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.HyperlinkBrand.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkBrand.Location = New System.Drawing.Point(264, 179)
        Me.HyperlinkBrand.Margin = New System.Windows.Forms.Padding(4, 4, 0, 13)
        Me.HyperlinkBrand.Name = "HyperlinkBrand"
        Me.HyperlinkBrand.Size = New System.Drawing.Size(301, 17)
        Me.HyperlinkBrand.TabIndex = 10
        Me.HyperlinkBrand.Text = "Select..."
        '
        'LabelFirstWeek
        '
        Me.LabelFirstWeek.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelFirstWeek.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelFirstWeek.Location = New System.Drawing.Point(0, 77)
        Me.LabelFirstWeek.Margin = New System.Windows.Forms.Padding(0, 4, 4, 13)
        Me.LabelFirstWeek.Name = "LabelFirstWeek"
        Me.LabelFirstWeek.Size = New System.Drawing.Size(81, 17)
        Me.LabelFirstWeek.TabIndex = 3
        Me.LabelFirstWeek.Text = "First Week:"
        '
        'HyperlinkFirstWeek
        '
        Me.HyperlinkFirstWeek.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkFirstWeek.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkFirstWeek.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkFirstWeek.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkFirstWeek.Location = New System.Drawing.Point(264, 77)
        Me.HyperlinkFirstWeek.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.HyperlinkFirstWeek.Name = "HyperlinkFirstWeek"
        Me.HyperlinkFirstWeek.Size = New System.Drawing.Size(57, 17)
        Me.HyperlinkFirstWeek.TabIndex = 4
        Me.HyperlinkFirstWeek.Text = "Select..."
        '
        'LabelStoresThatAllowMedia
        '
        Me.LabelStoresThatAllowMedia.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelStoresThatAllowMedia.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelStoresThatAllowMedia.Location = New System.Drawing.Point(0, 315)
        Me.LabelStoresThatAllowMedia.Margin = New System.Windows.Forms.Padding(0, 4, 4, 13)
        Me.LabelStoresThatAllowMedia.Name = "LabelStoresThatAllowMedia"
        Me.LabelStoresThatAllowMedia.Size = New System.Drawing.Size(230, 17)
        Me.LabelStoresThatAllowMedia.TabIndex = 13
        Me.LabelStoresThatAllowMedia.Text = "Stores That Allow Media Service:"
        '
        'LabelStoresThatAllowMediaValue
        '
        Me.LabelStoresThatAllowMediaValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelStoresThatAllowMediaValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelStoresThatAllowMediaValue.Location = New System.Drawing.Point(264, 315)
        Me.LabelStoresThatAllowMediaValue.Margin = New System.Windows.Forms.Padding(0, 4, 4, 13)
        Me.LabelStoresThatAllowMediaValue.Name = "LabelStoresThatAllowMediaValue"
        Me.LabelStoresThatAllowMediaValue.Size = New System.Drawing.Size(9, 17)
        Me.LabelStoresThatAllowMediaValue.TabIndex = 13
        Me.LabelStoresThatAllowMediaValue.Text = "0"
        '
        'LabelTotalUniverseValue
        '
        Me.LabelTotalUniverseValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelTotalUniverseValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelTotalUniverseValue.Location = New System.Drawing.Point(264, 281)
        Me.LabelTotalUniverseValue.Margin = New System.Windows.Forms.Padding(0, 4, 4, 13)
        Me.LabelTotalUniverseValue.Name = "LabelTotalUniverseValue"
        Me.LabelTotalUniverseValue.Size = New System.Drawing.Size(9, 17)
        Me.LabelTotalUniverseValue.TabIndex = 13
        Me.LabelTotalUniverseValue.Text = "0"
        '
        'LabelTotalUniverse
        '
        Me.LabelTotalUniverse.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelTotalUniverse.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelTotalUniverse.Location = New System.Drawing.Point(0, 281)
        Me.LabelTotalUniverse.Margin = New System.Windows.Forms.Padding(0, 4, 4, 13)
        Me.LabelTotalUniverse.Name = "LabelTotalUniverse"
        Me.LabelTotalUniverse.Size = New System.Drawing.Size(203, 17)
        Me.LabelTotalUniverse.TabIndex = 13
        Me.LabelTotalUniverse.Text = "Total Stores in the Universe:"
        '
        'LabelTotalStores
        '
        Me.LabelTotalStores.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelTotalStores.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelTotalStores.Location = New System.Drawing.Point(0, 247)
        Me.LabelTotalStores.Margin = New System.Windows.Forms.Padding(0, 4, 4, 13)
        Me.LabelTotalStores.Name = "LabelTotalStores"
        Me.LabelTotalStores.Size = New System.Drawing.Size(219, 17)
        Me.LabelTotalStores.TabIndex = 13
        Me.LabelTotalStores.Text = "Total Stores (incl. free stores):"
        '
        'LabelTotalWeeks
        '
        Me.LabelTotalWeeks.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelTotalWeeks.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelTotalWeeks.Location = New System.Drawing.Point(0, 213)
        Me.LabelTotalWeeks.Margin = New System.Windows.Forms.Padding(0, 4, 4, 13)
        Me.LabelTotalWeeks.Name = "LabelTotalWeeks"
        Me.LabelTotalWeeks.Size = New System.Drawing.Size(217, 17)
        Me.LabelTotalWeeks.TabIndex = 11
        Me.LabelTotalWeeks.Text = "Total Weeks (incl. free weeks):"
        '
        'LabelLastWeek
        '
        Me.LabelLastWeek.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelLastWeek.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelLastWeek.Location = New System.Drawing.Point(0, 111)
        Me.LabelLastWeek.Margin = New System.Windows.Forms.Padding(0, 4, 4, 13)
        Me.LabelLastWeek.Name = "LabelLastWeek"
        Me.LabelLastWeek.Size = New System.Drawing.Size(80, 17)
        Me.LabelLastWeek.TabIndex = 5
        Me.LabelLastWeek.Text = "Last Week:"
        '
        'TextEditInstallStoreQty
        '
        Me.TextEditInstallStoreQty.EditValue = 0
        Me.TextEditInstallStoreQty.Location = New System.Drawing.Point(264, 243)
        Me.TextEditInstallStoreQty.Margin = New System.Windows.Forms.Padding(4)
        Me.TextEditInstallStoreQty.Name = "TextEditInstallStoreQty"
        Me.TextEditInstallStoreQty.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditInstallStoreQty.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditInstallStoreQty.Properties.Appearance.Options.UseFont = True
        Me.TextEditInstallStoreQty.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditInstallStoreQty.Properties.Mask.EditMask = "n0"
        Me.TextEditInstallStoreQty.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditInstallStoreQty.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TextEditInstallStoreQty.Properties.MaxLength = 4
        Me.TextEditInstallStoreQty.Size = New System.Drawing.Size(77, 24)
        Me.TextEditInstallStoreQty.TabIndex = 14
        '
        'TextEditInstallWeeks
        '
        Me.TextEditInstallWeeks.EditValue = 0
        Me.TextEditInstallWeeks.Location = New System.Drawing.Point(264, 209)
        Me.TextEditInstallWeeks.Margin = New System.Windows.Forms.Padding(4)
        Me.TextEditInstallWeeks.Name = "TextEditInstallWeeks"
        Me.TextEditInstallWeeks.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditInstallWeeks.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditInstallWeeks.Properties.Appearance.Options.UseFont = True
        Me.TextEditInstallWeeks.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditInstallWeeks.Properties.AppearanceReadOnly.BackColor = System.Drawing.Color.Gainsboro
        Me.TextEditInstallWeeks.Properties.AppearanceReadOnly.Options.UseBackColor = True
        Me.TextEditInstallWeeks.Properties.Mask.EditMask = "n0"
        Me.TextEditInstallWeeks.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditInstallWeeks.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TextEditInstallWeeks.Properties.MaxLength = 3
        Me.TextEditInstallWeeks.Size = New System.Drawing.Size(77, 24)
        Me.TextEditInstallWeeks.TabIndex = 12
        '
        'HyperlinkLastWeek
        '
        Me.HyperlinkLastWeek.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkLastWeek.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkLastWeek.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkLastWeek.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkLastWeek.Location = New System.Drawing.Point(264, 111)
        Me.HyperlinkLastWeek.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.HyperlinkLastWeek.Name = "HyperlinkLastWeek"
        Me.HyperlinkLastWeek.Size = New System.Drawing.Size(57, 17)
        Me.HyperlinkLastWeek.TabIndex = 6
        Me.HyperlinkLastWeek.Text = "Select..."
        '
        'PanelRental
        '
        Me.PanelRental.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelRental.Controls.Add(Me.HyperlinkApplyDatesAccrossBursts)
        Me.PanelRental.Controls.Add(Me.PictureApplyDatesOnAllBursts)
        Me.PanelRental.Controls.Add(Me.LabelRentalHeading)
        Me.PanelRental.Controls.Add(Me.TextEditFreeWeeks)
        Me.PanelRental.Controls.Add(Me.LabelLoadingFees)
        Me.PanelRental.Controls.Add(Me.TextEditDiscount)
        Me.PanelRental.Controls.Add(Me.LabelDiscount)
        Me.PanelRental.Controls.Add(Me.TextEditBillableWeeks)
        Me.PanelRental.Controls.Add(Me.LabelRentalAmount)
        Me.PanelRental.Controls.Add(Me.TextEditFreeStoreQty)
        Me.PanelRental.Controls.Add(Me.LabelDiscountAmount)
        Me.PanelRental.Controls.Add(Me.TextEditBillableStoreQty)
        Me.PanelRental.Controls.Add(Me.LabelRentalRate)
        Me.PanelRental.Controls.Add(Me.TextEditRentalAmount)
        Me.PanelRental.Controls.Add(Me.LabelNetRental)
        Me.PanelRental.Controls.Add(Me.LabelBillableWeeks)
        Me.PanelRental.Controls.Add(Me.TextEditNetRental)
        Me.PanelRental.Controls.Add(Me.LabelFreeWeeks)
        Me.PanelRental.Controls.Add(Me.TextEditDiscountAmount)
        Me.PanelRental.Controls.Add(Me.TextEditRentalRate)
        Me.PanelRental.Controls.Add(Me.TextEditLoadingFees)
        Me.PanelRental.Controls.Add(Me.LabelBillableStoreQty)
        Me.PanelRental.Controls.Add(Me.LabelFreeStoreQty)
        Me.PanelRental.Location = New System.Drawing.Point(583, 0)
        Me.PanelRental.Margin = New System.Windows.Forms.Padding(0)
        Me.PanelRental.Name = "PanelRental"
        Me.PanelRental.Size = New System.Drawing.Size(399, 498)
        Me.PanelRental.TabIndex = 1
        '
        'HyperlinkApplyDatesAccrossBursts
        '
        Me.HyperlinkApplyDatesAccrossBursts.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkApplyDatesAccrossBursts.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkApplyDatesAccrossBursts.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkApplyDatesAccrossBursts.AutoEllipsis = True
        Me.HyperlinkApplyDatesAccrossBursts.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkApplyDatesAccrossBursts.Location = New System.Drawing.Point(140, 466)
        Me.HyperlinkApplyDatesAccrossBursts.Margin = New System.Windows.Forms.Padding(4, 0, 0, 4)
        Me.HyperlinkApplyDatesAccrossBursts.Name = "HyperlinkApplyDatesAccrossBursts"
        Me.HyperlinkApplyDatesAccrossBursts.Size = New System.Drawing.Size(221, 17)
        Me.HyperlinkApplyDatesAccrossBursts.TabIndex = 23
        Me.HyperlinkApplyDatesAccrossBursts.Text = "Apply Dates Across ALL Bursts"
        '
        'PictureApplyDatesOnAllBursts
        '
        Me.PictureApplyDatesOnAllBursts.AllowDrop = True
        Me.PictureApplyDatesOnAllBursts.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureApplyDatesOnAllBursts.EditValue = Global.Nova2.My.Resources.Resources.accept16
        Me.PictureApplyDatesOnAllBursts.Location = New System.Drawing.Point(112, 466)
        Me.PictureApplyDatesOnAllBursts.Margin = New System.Windows.Forms.Padding(0, 4, 4, 0)
        Me.PictureApplyDatesOnAllBursts.Name = "PictureApplyDatesOnAllBursts"
        Me.PictureApplyDatesOnAllBursts.Properties.AllowFocused = False
        Me.PictureApplyDatesOnAllBursts.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureApplyDatesOnAllBursts.Properties.Appearance.Options.UseBackColor = True
        Me.PictureApplyDatesOnAllBursts.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureApplyDatesOnAllBursts.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem5.Text = "Minimum"
        ToolTipItem5.LeftIndent = 6
        ToolTipItem5.Text = "Set the store pool size to the smallest possible quantity"
        SuperToolTip5.Items.Add(ToolTipTitleItem5)
        SuperToolTip5.Items.Add(ToolTipItem5)
        Me.PictureApplyDatesOnAllBursts.SuperTip = SuperToolTip5
        Me.PictureApplyDatesOnAllBursts.TabIndex = 22
        Me.PictureApplyDatesOnAllBursts.TabStop = True
        '
        'LabelRentalHeading
        '
        Me.LabelRentalHeading.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelRentalHeading.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelRentalHeading.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelRentalHeading.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelRentalHeading.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelRentalHeading.LineVisible = True
        Me.LabelRentalHeading.Location = New System.Drawing.Point(0, 0)
        Me.LabelRentalHeading.Margin = New System.Windows.Forms.Padding(0, 0, 0, 12)
        Me.LabelRentalHeading.Name = "LabelRentalHeading"
        Me.LabelRentalHeading.Size = New System.Drawing.Size(399, 24)
        Me.LabelRentalHeading.TabIndex = 0
        Me.LabelRentalHeading.Text = "Rental"
        '
        'TextEditFreeWeeks
        '
        Me.TextEditFreeWeeks.EditValue = 0
        Me.TextEditFreeWeeks.Location = New System.Drawing.Point(242, 73)
        Me.TextEditFreeWeeks.Margin = New System.Windows.Forms.Padding(4, 4, 0, 4)
        Me.TextEditFreeWeeks.Name = "TextEditFreeWeeks"
        Me.TextEditFreeWeeks.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditFreeWeeks.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditFreeWeeks.Properties.Appearance.Options.UseFont = True
        Me.TextEditFreeWeeks.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditFreeWeeks.Properties.Appearance.Options.UseTextOptions = True
        Me.TextEditFreeWeeks.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.TextEditFreeWeeks.Properties.AppearanceReadOnly.BackColor = System.Drawing.Color.Gainsboro
        Me.TextEditFreeWeeks.Properties.AppearanceReadOnly.Options.UseBackColor = True
        Me.TextEditFreeWeeks.Properties.Mask.EditMask = "n0"
        Me.TextEditFreeWeeks.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditFreeWeeks.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TextEditFreeWeeks.Size = New System.Drawing.Size(157, 24)
        Me.TextEditFreeWeeks.TabIndex = 4
        '
        'LabelLoadingFees
        '
        Me.LabelLoadingFees.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelLoadingFees.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelLoadingFees.Location = New System.Drawing.Point(0, 281)
        Me.LabelLoadingFees.Margin = New System.Windows.Forms.Padding(0, 4, 4, 13)
        Me.LabelLoadingFees.Name = "LabelLoadingFees"
        Me.LabelLoadingFees.Size = New System.Drawing.Size(98, 17)
        Me.LabelLoadingFees.TabIndex = 15
        Me.LabelLoadingFees.Text = "Loading Fees:"
        '
        'TextEditDiscount
        '
        Me.TextEditDiscount.EditValue = "0"
        Me.TextEditDiscount.Location = New System.Drawing.Point(242, 175)
        Me.TextEditDiscount.Margin = New System.Windows.Forms.Padding(4, 4, 0, 4)
        Me.TextEditDiscount.Name = "TextEditDiscount"
        Me.TextEditDiscount.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditDiscount.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditDiscount.Properties.Appearance.Options.UseFont = True
        Me.TextEditDiscount.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditDiscount.Properties.Appearance.Options.UseTextOptions = True
        Me.TextEditDiscount.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.TextEditDiscount.Properties.AppearanceReadOnly.BackColor = System.Drawing.Color.Gainsboro
        Me.TextEditDiscount.Properties.AppearanceReadOnly.Options.UseBackColor = True
        Me.TextEditDiscount.Properties.Mask.EditMask = "P2"
        Me.TextEditDiscount.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditDiscount.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TextEditDiscount.Size = New System.Drawing.Size(157, 24)
        Me.TextEditDiscount.TabIndex = 10
        '
        'LabelDiscount
        '
        Me.LabelDiscount.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelDiscount.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelDiscount.Location = New System.Drawing.Point(0, 179)
        Me.LabelDiscount.Margin = New System.Windows.Forms.Padding(0, 4, 4, 13)
        Me.LabelDiscount.Name = "LabelDiscount"
        Me.LabelDiscount.Size = New System.Drawing.Size(150, 17)
        Me.LabelDiscount.TabIndex = 9
        Me.LabelDiscount.Text = "Discount Percentage:"
        '
        'TextEditBillableWeeks
        '
        Me.TextEditBillableWeeks.EditValue = "0"
        Me.TextEditBillableWeeks.Location = New System.Drawing.Point(242, 39)
        Me.TextEditBillableWeeks.Margin = New System.Windows.Forms.Padding(4, 4, 0, 4)
        Me.TextEditBillableWeeks.Name = "TextEditBillableWeeks"
        Me.TextEditBillableWeeks.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditBillableWeeks.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditBillableWeeks.Properties.Appearance.Options.UseFont = True
        Me.TextEditBillableWeeks.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditBillableWeeks.Properties.Appearance.Options.UseTextOptions = True
        Me.TextEditBillableWeeks.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.TextEditBillableWeeks.Properties.AppearanceReadOnly.BackColor = System.Drawing.Color.Gainsboro
        Me.TextEditBillableWeeks.Properties.AppearanceReadOnly.Options.UseBackColor = True
        Me.TextEditBillableWeeks.Properties.Mask.EditMask = "n0"
        Me.TextEditBillableWeeks.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditBillableWeeks.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TextEditBillableWeeks.Size = New System.Drawing.Size(157, 24)
        Me.TextEditBillableWeeks.TabIndex = 2
        '
        'LabelRentalAmount
        '
        Me.LabelRentalAmount.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelRentalAmount.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelRentalAmount.Location = New System.Drawing.Point(0, 247)
        Me.LabelRentalAmount.Margin = New System.Windows.Forms.Padding(0, 4, 4, 13)
        Me.LabelRentalAmount.Name = "LabelRentalAmount"
        Me.LabelRentalAmount.Size = New System.Drawing.Size(111, 17)
        Me.LabelRentalAmount.TabIndex = 13
        Me.LabelRentalAmount.Text = "Rental Amount:"
        '
        'TextEditFreeStoreQty
        '
        Me.TextEditFreeStoreQty.EditValue = 0
        Me.TextEditFreeStoreQty.Location = New System.Drawing.Point(242, 141)
        Me.TextEditFreeStoreQty.Margin = New System.Windows.Forms.Padding(4, 4, 0, 4)
        Me.TextEditFreeStoreQty.Name = "TextEditFreeStoreQty"
        Me.TextEditFreeStoreQty.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditFreeStoreQty.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditFreeStoreQty.Properties.Appearance.Options.UseFont = True
        Me.TextEditFreeStoreQty.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditFreeStoreQty.Properties.Appearance.Options.UseTextOptions = True
        Me.TextEditFreeStoreQty.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.TextEditFreeStoreQty.Properties.AppearanceReadOnly.BackColor = System.Drawing.Color.Gainsboro
        Me.TextEditFreeStoreQty.Properties.AppearanceReadOnly.Options.UseBackColor = True
        Me.TextEditFreeStoreQty.Properties.Mask.EditMask = "n0"
        Me.TextEditFreeStoreQty.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditFreeStoreQty.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TextEditFreeStoreQty.Properties.MaxLength = 4
        Me.TextEditFreeStoreQty.Size = New System.Drawing.Size(157, 24)
        Me.TextEditFreeStoreQty.TabIndex = 8
        '
        'LabelDiscountAmount
        '
        Me.LabelDiscountAmount.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelDiscountAmount.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelDiscountAmount.Location = New System.Drawing.Point(0, 315)
        Me.LabelDiscountAmount.Margin = New System.Windows.Forms.Padding(0, 4, 4, 13)
        Me.LabelDiscountAmount.Name = "LabelDiscountAmount"
        Me.LabelDiscountAmount.Size = New System.Drawing.Size(129, 17)
        Me.LabelDiscountAmount.TabIndex = 17
        Me.LabelDiscountAmount.Text = "Discount Amount:"
        '
        'TextEditBillableStoreQty
        '
        Me.TextEditBillableStoreQty.EditValue = 0
        Me.TextEditBillableStoreQty.Location = New System.Drawing.Point(242, 107)
        Me.TextEditBillableStoreQty.Margin = New System.Windows.Forms.Padding(4, 4, 0, 4)
        Me.TextEditBillableStoreQty.Name = "TextEditBillableStoreQty"
        Me.TextEditBillableStoreQty.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditBillableStoreQty.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditBillableStoreQty.Properties.Appearance.Options.UseFont = True
        Me.TextEditBillableStoreQty.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditBillableStoreQty.Properties.Appearance.Options.UseTextOptions = True
        Me.TextEditBillableStoreQty.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.TextEditBillableStoreQty.Properties.AppearanceReadOnly.BackColor = System.Drawing.Color.Gainsboro
        Me.TextEditBillableStoreQty.Properties.AppearanceReadOnly.Options.UseBackColor = True
        Me.TextEditBillableStoreQty.Properties.Mask.EditMask = "n0"
        Me.TextEditBillableStoreQty.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditBillableStoreQty.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TextEditBillableStoreQty.Properties.MaxLength = 4
        Me.TextEditBillableStoreQty.Size = New System.Drawing.Size(157, 24)
        Me.TextEditBillableStoreQty.TabIndex = 6
        '
        'LabelRentalRate
        '
        Me.LabelRentalRate.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelRentalRate.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelRentalRate.Location = New System.Drawing.Point(0, 213)
        Me.LabelRentalRate.Margin = New System.Windows.Forms.Padding(0, 4, 4, 13)
        Me.LabelRentalRate.Name = "LabelRentalRate"
        Me.LabelRentalRate.Size = New System.Drawing.Size(213, 17)
        Me.LabelRentalRate.TabIndex = 11
        Me.LabelRentalRate.Text = "Weekly Rental Rate Per Store:"
        '
        'TextEditRentalAmount
        '
        Me.TextEditRentalAmount.EditValue = "0"
        Me.TextEditRentalAmount.Location = New System.Drawing.Point(242, 243)
        Me.TextEditRentalAmount.Margin = New System.Windows.Forms.Padding(4, 4, 0, 4)
        Me.TextEditRentalAmount.Name = "TextEditRentalAmount"
        Me.TextEditRentalAmount.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditRentalAmount.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditRentalAmount.Properties.Appearance.Options.UseFont = True
        Me.TextEditRentalAmount.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditRentalAmount.Properties.Appearance.Options.UseTextOptions = True
        Me.TextEditRentalAmount.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.TextEditRentalAmount.Properties.AppearanceReadOnly.BackColor = System.Drawing.Color.Gainsboro
        Me.TextEditRentalAmount.Properties.AppearanceReadOnly.Options.UseBackColor = True
        Me.TextEditRentalAmount.Properties.Mask.EditMask = "c"
        Me.TextEditRentalAmount.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditRentalAmount.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TextEditRentalAmount.Properties.ReadOnly = True
        Me.TextEditRentalAmount.Size = New System.Drawing.Size(157, 24)
        Me.TextEditRentalAmount.TabIndex = 14
        '
        'LabelNetRental
        '
        Me.LabelNetRental.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelNetRental.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelNetRental.Location = New System.Drawing.Point(0, 349)
        Me.LabelNetRental.Margin = New System.Windows.Forms.Padding(0, 4, 4, 13)
        Me.LabelNetRental.Name = "LabelNetRental"
        Me.LabelNetRental.Size = New System.Drawing.Size(79, 17)
        Me.LabelNetRental.TabIndex = 19
        Me.LabelNetRental.Text = "Net Rental:"
        '
        'LabelBillableWeeks
        '
        Me.LabelBillableWeeks.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelBillableWeeks.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelBillableWeeks.Location = New System.Drawing.Point(0, 43)
        Me.LabelBillableWeeks.Margin = New System.Windows.Forms.Padding(0, 4, 4, 13)
        Me.LabelBillableWeeks.Name = "LabelBillableWeeks"
        Me.LabelBillableWeeks.Size = New System.Drawing.Size(105, 17)
        Me.LabelBillableWeeks.TabIndex = 1
        Me.LabelBillableWeeks.Text = "Billable Weeks:"
        '
        'TextEditNetRental
        '
        Me.TextEditNetRental.AllowDrop = True
        Me.TextEditNetRental.EditValue = "0"
        Me.TextEditNetRental.Location = New System.Drawing.Point(242, 345)
        Me.TextEditNetRental.Margin = New System.Windows.Forms.Padding(4, 4, 0, 4)
        Me.TextEditNetRental.Name = "TextEditNetRental"
        Me.TextEditNetRental.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditNetRental.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditNetRental.Properties.Appearance.Options.UseFont = True
        Me.TextEditNetRental.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditNetRental.Properties.Appearance.Options.UseTextOptions = True
        Me.TextEditNetRental.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.TextEditNetRental.Properties.AppearanceReadOnly.BackColor = System.Drawing.Color.Gainsboro
        Me.TextEditNetRental.Properties.AppearanceReadOnly.Options.UseBackColor = True
        Me.TextEditNetRental.Properties.Mask.EditMask = "c"
        Me.TextEditNetRental.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditNetRental.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TextEditNetRental.Properties.ReadOnly = True
        Me.TextEditNetRental.Size = New System.Drawing.Size(157, 24)
        Me.TextEditNetRental.TabIndex = 20
        '
        'LabelFreeWeeks
        '
        Me.LabelFreeWeeks.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelFreeWeeks.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelFreeWeeks.Location = New System.Drawing.Point(0, 77)
        Me.LabelFreeWeeks.Margin = New System.Windows.Forms.Padding(0, 4, 4, 13)
        Me.LabelFreeWeeks.Name = "LabelFreeWeeks"
        Me.LabelFreeWeeks.Size = New System.Drawing.Size(88, 17)
        Me.LabelFreeWeeks.TabIndex = 3
        Me.LabelFreeWeeks.Text = "Free Weeks:"
        '
        'TextEditDiscountAmount
        '
        Me.TextEditDiscountAmount.EditValue = "0"
        Me.TextEditDiscountAmount.Location = New System.Drawing.Point(242, 311)
        Me.TextEditDiscountAmount.Margin = New System.Windows.Forms.Padding(4, 4, 0, 4)
        Me.TextEditDiscountAmount.Name = "TextEditDiscountAmount"
        Me.TextEditDiscountAmount.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditDiscountAmount.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditDiscountAmount.Properties.Appearance.Options.UseFont = True
        Me.TextEditDiscountAmount.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditDiscountAmount.Properties.Appearance.Options.UseTextOptions = True
        Me.TextEditDiscountAmount.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.TextEditDiscountAmount.Properties.AppearanceReadOnly.BackColor = System.Drawing.Color.Gainsboro
        Me.TextEditDiscountAmount.Properties.AppearanceReadOnly.Options.UseBackColor = True
        Me.TextEditDiscountAmount.Properties.Mask.EditMask = "c"
        Me.TextEditDiscountAmount.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditDiscountAmount.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TextEditDiscountAmount.Properties.ReadOnly = True
        Me.TextEditDiscountAmount.Size = New System.Drawing.Size(157, 24)
        Me.TextEditDiscountAmount.TabIndex = 18
        '
        'TextEditRentalRate
        '
        Me.TextEditRentalRate.EditValue = ""
        Me.TextEditRentalRate.Location = New System.Drawing.Point(242, 209)
        Me.TextEditRentalRate.Margin = New System.Windows.Forms.Padding(4, 4, 0, 4)
        Me.TextEditRentalRate.Name = "TextEditRentalRate"
        Me.TextEditRentalRate.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditRentalRate.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditRentalRate.Properties.Appearance.Options.UseFont = True
        Me.TextEditRentalRate.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditRentalRate.Properties.Appearance.Options.UseTextOptions = True
        Me.TextEditRentalRate.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.TextEditRentalRate.Properties.AppearanceReadOnly.BackColor = System.Drawing.Color.Gainsboro
        Me.TextEditRentalRate.Properties.AppearanceReadOnly.Options.UseBackColor = True
        Me.TextEditRentalRate.Properties.Mask.EditMask = "c4"
        Me.TextEditRentalRate.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditRentalRate.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TextEditRentalRate.Size = New System.Drawing.Size(157, 24)
        Me.TextEditRentalRate.TabIndex = 12
        '
        'TextEditLoadingFees
        '
        Me.TextEditLoadingFees.EditValue = "0"
        Me.TextEditLoadingFees.Location = New System.Drawing.Point(242, 277)
        Me.TextEditLoadingFees.Margin = New System.Windows.Forms.Padding(4, 4, 0, 4)
        Me.TextEditLoadingFees.Name = "TextEditLoadingFees"
        Me.TextEditLoadingFees.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditLoadingFees.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditLoadingFees.Properties.Appearance.Options.UseFont = True
        Me.TextEditLoadingFees.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditLoadingFees.Properties.Appearance.Options.UseTextOptions = True
        Me.TextEditLoadingFees.Properties.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.TextEditLoadingFees.Properties.AppearanceReadOnly.BackColor = System.Drawing.Color.Gainsboro
        Me.TextEditLoadingFees.Properties.AppearanceReadOnly.Options.UseBackColor = True
        Me.TextEditLoadingFees.Properties.Mask.EditMask = "c"
        Me.TextEditLoadingFees.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditLoadingFees.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TextEditLoadingFees.Properties.ReadOnly = True
        Me.TextEditLoadingFees.Size = New System.Drawing.Size(157, 24)
        Me.TextEditLoadingFees.TabIndex = 16
        '
        'LabelBillableStoreQty
        '
        Me.LabelBillableStoreQty.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelBillableStoreQty.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelBillableStoreQty.Location = New System.Drawing.Point(0, 111)
        Me.LabelBillableStoreQty.Margin = New System.Windows.Forms.Padding(0, 4, 4, 13)
        Me.LabelBillableStoreQty.Name = "LabelBillableStoreQty"
        Me.LabelBillableStoreQty.Size = New System.Drawing.Size(105, 17)
        Me.LabelBillableStoreQty.TabIndex = 5
        Me.LabelBillableStoreQty.Text = "Billable Stores:"
        '
        'LabelFreeStoreQty
        '
        Me.LabelFreeStoreQty.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelFreeStoreQty.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelFreeStoreQty.Location = New System.Drawing.Point(0, 145)
        Me.LabelFreeStoreQty.Margin = New System.Windows.Forms.Padding(0, 4, 4, 13)
        Me.LabelFreeStoreQty.Name = "LabelFreeStoreQty"
        Me.LabelFreeStoreQty.Size = New System.Drawing.Size(88, 17)
        Me.LabelFreeStoreQty.TabIndex = 7
        Me.LabelFreeStoreQty.Text = "Free Stores:"
        '
        'TabPageLoadingFees
        '
        Me.TabPageLoadingFees.Controls.Add(Me.PanelLoadingFees)
        Me.TabPageLoadingFees.Margin = New System.Windows.Forms.Padding(4)
        Me.TabPageLoadingFees.Name = "TabPageLoadingFees"
        Me.TabPageLoadingFees.Size = New System.Drawing.Size(1021, 539)
        Me.TabPageLoadingFees.Text = "Loading Fees"
        '
        'PanelLoadingFees
        '
        Me.PanelLoadingFees.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelLoadingFees.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelLoadingFees.Controls.Add(Me.TableLayoutPanelLoadingFees)
        Me.PanelLoadingFees.Location = New System.Drawing.Point(4, 4)
        Me.PanelLoadingFees.Margin = New System.Windows.Forms.Padding(4)
        Me.PanelLoadingFees.Name = "PanelLoadingFees"
        Me.PanelLoadingFees.Padding = New System.Windows.Forms.Padding(12)
        Me.PanelLoadingFees.Size = New System.Drawing.Size(1013, 531)
        Me.PanelLoadingFees.TabIndex = 2
        '
        'TableLayoutPanelLoadingFees
        '
        Me.TableLayoutPanelLoadingFees.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanelLoadingFees.ColumnCount = 1
        Me.TableLayoutPanelLoadingFees.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanelLoadingFees.Controls.Add(Me.LabelLoadingFeesInfo, 0, 0)
        Me.TableLayoutPanelLoadingFees.Controls.Add(Me.PanelLoadingFeesGrid, 0, 1)
        Me.TableLayoutPanelLoadingFees.Location = New System.Drawing.Point(15, 16)
        Me.TableLayoutPanelLoadingFees.Margin = New System.Windows.Forms.Padding(4)
        Me.TableLayoutPanelLoadingFees.Name = "TableLayoutPanelLoadingFees"
        Me.TableLayoutPanelLoadingFees.RowCount = 2
        Me.TableLayoutPanelLoadingFees.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanelLoadingFees.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanelLoadingFees.Size = New System.Drawing.Size(982, 500)
        Me.TableLayoutPanelLoadingFees.TabIndex = 6
        '
        'LabelLoadingFeesInfo
        '
        Me.LabelLoadingFeesInfo.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelLoadingFeesInfo.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelLoadingFeesInfo.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelLoadingFeesInfo.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelLoadingFeesInfo.Location = New System.Drawing.Point(0, 0)
        Me.LabelLoadingFeesInfo.Margin = New System.Windows.Forms.Padding(0, 0, 0, 20)
        Me.LabelLoadingFeesInfo.Name = "LabelLoadingFeesInfo"
        Me.LabelLoadingFeesInfo.Size = New System.Drawing.Size(982, 51)
        Me.LabelLoadingFeesInfo.TabIndex = 5
        Me.LabelLoadingFeesInfo.Text = resources.GetString("LabelLoadingFeesInfo.Text")
        '
        'PanelLoadingFeesGrid
        '
        Me.PanelLoadingFeesGrid.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelLoadingFeesGrid.Controls.Add(Me.GroupControlLoadingFees)
        Me.PanelLoadingFeesGrid.Controls.Add(Me.LabelLoadingFeeTotalValue)
        Me.PanelLoadingFeesGrid.Controls.Add(Me.LabelLoadingFeeTotal)
        Me.PanelLoadingFeesGrid.Location = New System.Drawing.Point(0, 71)
        Me.PanelLoadingFeesGrid.Margin = New System.Windows.Forms.Padding(0)
        Me.PanelLoadingFeesGrid.Name = "PanelLoadingFeesGrid"
        Me.PanelLoadingFeesGrid.Size = New System.Drawing.Size(982, 429)
        Me.PanelLoadingFeesGrid.TabIndex = 4
        '
        'GroupControlLoadingFees
        '
        Me.GroupControlLoadingFees.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlLoadingFees.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlLoadingFees.Appearance.Options.UseFont = True
        Me.GroupControlLoadingFees.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlLoadingFees.AppearanceCaption.ForeColor = System.Drawing.Color.DimGray
        Me.GroupControlLoadingFees.AppearanceCaption.Options.UseFont = True
        Me.GroupControlLoadingFees.AppearanceCaption.Options.UseForeColor = True
        Me.GroupControlLoadingFees.Controls.Add(Me.ButtonRemoveLoadingFee)
        Me.GroupControlLoadingFees.Controls.Add(Me.ButtonAddLoadingFee)
        Me.GroupControlLoadingFees.Controls.Add(Me.GridLoadingFees)
        Me.GroupControlLoadingFees.Location = New System.Drawing.Point(0, 0)
        Me.GroupControlLoadingFees.LookAndFeel.SkinName = "Black"
        Me.GroupControlLoadingFees.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlLoadingFees.Margin = New System.Windows.Forms.Padding(0, 0, 0, 12)
        Me.GroupControlLoadingFees.Name = "GroupControlLoadingFees"
        Me.GroupControlLoadingFees.Size = New System.Drawing.Size(982, 390)
        Me.GroupControlLoadingFees.TabIndex = 0
        Me.GroupControlLoadingFees.Tag = ""
        Me.GroupControlLoadingFees.Text = "Loading Fee List"
        '
        'ButtonRemoveLoadingFee
        '
        Me.ButtonRemoveLoadingFee.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonRemoveLoadingFee.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonRemoveLoadingFee.Appearance.Options.UseFont = True
        Me.ButtonRemoveLoadingFee.ImageIndex = 2
        Me.ButtonRemoveLoadingFee.ImageList = Me.ImageList16x16
        Me.ButtonRemoveLoadingFee.Location = New System.Drawing.Point(111, 348)
        Me.ButtonRemoveLoadingFee.LookAndFeel.SkinName = "Black"
        Me.ButtonRemoveLoadingFee.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonRemoveLoadingFee.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonRemoveLoadingFee.Name = "ButtonRemoveLoadingFee"
        Me.ButtonRemoveLoadingFee.Size = New System.Drawing.Size(96, 30)
        Me.ButtonRemoveLoadingFee.TabIndex = 2
        Me.ButtonRemoveLoadingFee.Text = "Remove"
        '
        'ButtonAddLoadingFee
        '
        Me.ButtonAddLoadingFee.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAddLoadingFee.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAddLoadingFee.Appearance.Options.UseFont = True
        Me.ButtonAddLoadingFee.ImageIndex = 0
        Me.ButtonAddLoadingFee.ImageList = Me.ImageList16x16
        Me.ButtonAddLoadingFee.Location = New System.Drawing.Point(6, 348)
        Me.ButtonAddLoadingFee.LookAndFeel.SkinName = "Black"
        Me.ButtonAddLoadingFee.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAddLoadingFee.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonAddLoadingFee.Name = "ButtonAddLoadingFee"
        Me.ButtonAddLoadingFee.Size = New System.Drawing.Size(96, 30)
        Me.ButtonAddLoadingFee.TabIndex = 1
        Me.ButtonAddLoadingFee.Text = "Add"
        '
        'GridLoadingFees
        '
        Me.GridLoadingFees.AllowUserToAddRows = False
        Me.GridLoadingFees.AllowUserToDeleteRows = False
        Me.GridLoadingFees.AllowUserToOrderColumns = True
        Me.GridLoadingFees.AllowUserToResizeRows = False
        DataGridViewCellStyle21.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridLoadingFees.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle21
        Me.GridLoadingFees.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridLoadingFees.BackgroundColor = System.Drawing.Color.White
        Me.GridLoadingFees.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridLoadingFees.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridLoadingFees.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle22.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle22.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle22.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle22.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle22.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle22.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle22.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridLoadingFees.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle22
        Me.GridLoadingFees.ColumnHeadersHeight = 22
        Me.GridLoadingFees.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridLoadingFees.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.LoadingFeeNameColumn, Me.LoadingFeePercentageColumn, Me.LoadingFeeAmountColumn})
        Me.GridLoadingFees.EnableHeadersVisualStyles = False
        Me.GridLoadingFees.GridColor = System.Drawing.Color.White
        Me.GridLoadingFees.Location = New System.Drawing.Point(3, 29)
        Me.GridLoadingFees.Margin = New System.Windows.Forms.Padding(4)
        Me.GridLoadingFees.Name = "GridLoadingFees"
        Me.GridLoadingFees.RowHeadersVisible = False
        Me.GridLoadingFees.RowHeadersWidth = 51
        DataGridViewCellStyle25.ForeColor = System.Drawing.Color.DimGray
        Me.GridLoadingFees.RowsDefaultCellStyle = DataGridViewCellStyle25
        Me.GridLoadingFees.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridLoadingFees.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridLoadingFees.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridLoadingFees.RowTemplate.Height = 19
        Me.GridLoadingFees.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridLoadingFees.ShowCellToolTips = False
        Me.GridLoadingFees.Size = New System.Drawing.Size(977, 313)
        Me.GridLoadingFees.StandardTab = True
        Me.GridLoadingFees.TabIndex = 0
        '
        'LoadingFeeNameColumn
        '
        Me.LoadingFeeNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.LoadingFeeNameColumn.DataPropertyName = "LoadingFeeName"
        Me.LoadingFeeNameColumn.HeaderText = "Description"
        Me.LoadingFeeNameColumn.MinimumWidth = 6
        Me.LoadingFeeNameColumn.Name = "LoadingFeeNameColumn"
        Me.LoadingFeeNameColumn.ReadOnly = True
        '
        'LoadingFeePercentageColumn
        '
        Me.LoadingFeePercentageColumn.DataPropertyName = "Percentage"
        DataGridViewCellStyle23.Format = "0.00"
        DataGridViewCellStyle23.NullValue = Nothing
        Me.LoadingFeePercentageColumn.DefaultCellStyle = DataGridViewCellStyle23
        Me.LoadingFeePercentageColumn.HeaderText = "Percentage"
        Me.LoadingFeePercentageColumn.MinimumWidth = 6
        Me.LoadingFeePercentageColumn.Name = "LoadingFeePercentageColumn"
        Me.LoadingFeePercentageColumn.Width = 125
        '
        'LoadingFeeAmountColumn
        '
        Me.LoadingFeeAmountColumn.DataPropertyName = "LoadingFeeAmount"
        DataGridViewCellStyle24.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleRight
        DataGridViewCellStyle24.Format = "C2"
        DataGridViewCellStyle24.NullValue = Nothing
        Me.LoadingFeeAmountColumn.DefaultCellStyle = DataGridViewCellStyle24
        Me.LoadingFeeAmountColumn.HeaderText = "Amount"
        Me.LoadingFeeAmountColumn.MinimumWidth = 6
        Me.LoadingFeeAmountColumn.Name = "LoadingFeeAmountColumn"
        Me.LoadingFeeAmountColumn.ReadOnly = True
        Me.LoadingFeeAmountColumn.Width = 125
        '
        'LabelLoadingFeeTotalValue
        '
        Me.LabelLoadingFeeTotalValue.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelLoadingFeeTotalValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelLoadingFeeTotalValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelLoadingFeeTotalValue.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.LabelLoadingFeeTotalValue.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelLoadingFeeTotalValue.Location = New System.Drawing.Point(838, 404)
        Me.LabelLoadingFeeTotalValue.Margin = New System.Windows.Forms.Padding(4, 4, 0, 4)
        Me.LabelLoadingFeeTotalValue.Name = "LabelLoadingFeeTotalValue"
        Me.LabelLoadingFeeTotalValue.Size = New System.Drawing.Size(144, 17)
        Me.LabelLoadingFeeTotalValue.TabIndex = 2
        Me.LabelLoadingFeeTotalValue.Text = "R 000,000,000.00"
        '
        'LabelLoadingFeeTotal
        '
        Me.LabelLoadingFeeTotal.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelLoadingFeeTotal.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelLoadingFeeTotal.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelLoadingFeeTotal.Location = New System.Drawing.Point(-3285, 404)
        Me.LabelLoadingFeeTotal.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelLoadingFeeTotal.Name = "LabelLoadingFeeTotal"
        Me.LabelLoadingFeeTotal.Size = New System.Drawing.Size(41, 17)
        Me.LabelLoadingFeeTotal.TabIndex = 1
        Me.LabelLoadingFeeTotal.Text = "Total:"
        '
        'TabPageCategories
        '
        Me.TabPageCategories.Controls.Add(Me.PanelCategories)
        Me.TabPageCategories.Margin = New System.Windows.Forms.Padding(4)
        Me.TabPageCategories.Name = "TabPageCategories"
        Me.TabPageCategories.Size = New System.Drawing.Size(1021, 539)
        Me.TabPageCategories.Text = "Categories"
        '
        'PanelCategories
        '
        Me.PanelCategories.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelCategories.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelCategories.Controls.Add(Me.TableLayoutPanelCategories)
        Me.PanelCategories.Location = New System.Drawing.Point(4, 4)
        Me.PanelCategories.Margin = New System.Windows.Forms.Padding(4)
        Me.PanelCategories.Name = "PanelCategories"
        Me.PanelCategories.Padding = New System.Windows.Forms.Padding(12)
        Me.PanelCategories.Size = New System.Drawing.Size(1013, 531)
        Me.PanelCategories.TabIndex = 0
        '
        'TableLayoutPanelCategories
        '
        Me.TableLayoutPanelCategories.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanelCategories.ColumnCount = 3
        Me.TableLayoutPanelCategories.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 45.0!))
        Me.TableLayoutPanelCategories.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 19.0!))
        Me.TableLayoutPanelCategories.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 55.0!))
        Me.TableLayoutPanelCategories.Controls.Add(Me.PanelCategoriesRight, 2, 0)
        Me.TableLayoutPanelCategories.Controls.Add(Me.PanelCategoriesLeft, 0, 0)
        Me.TableLayoutPanelCategories.Location = New System.Drawing.Point(15, 16)
        Me.TableLayoutPanelCategories.Margin = New System.Windows.Forms.Padding(4)
        Me.TableLayoutPanelCategories.Name = "TableLayoutPanelCategories"
        Me.TableLayoutPanelCategories.RowCount = 1
        Me.TableLayoutPanelCategories.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 500.0!))
        Me.TableLayoutPanelCategories.Size = New System.Drawing.Size(982, 500)
        Me.TableLayoutPanelCategories.TabIndex = 0
        '
        'PanelCategoriesRight
        '
        Me.PanelCategoriesRight.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelCategoriesRight.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelCategoriesRight.Controls.Add(Me.LabelCrossoversHeading)
        Me.PanelCategoriesRight.Controls.Add(Me.TextEditCrossoverQty)
        Me.PanelCategoriesRight.Controls.Add(Me.LabelCrossoverQty)
        Me.PanelCategoriesRight.Controls.Add(Me.GroupCrossoverPreferences)
        Me.PanelCategoriesRight.Location = New System.Drawing.Point(452, 0)
        Me.PanelCategoriesRight.Margin = New System.Windows.Forms.Padding(0)
        Me.PanelCategoriesRight.Name = "PanelCategoriesRight"
        Me.PanelCategoriesRight.Size = New System.Drawing.Size(530, 500)
        Me.PanelCategoriesRight.TabIndex = 1
        '
        'LabelCrossoversHeading
        '
        Me.LabelCrossoversHeading.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelCrossoversHeading.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCrossoversHeading.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelCrossoversHeading.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelCrossoversHeading.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelCrossoversHeading.LineVisible = True
        Me.LabelCrossoversHeading.Location = New System.Drawing.Point(0, 0)
        Me.LabelCrossoversHeading.Margin = New System.Windows.Forms.Padding(0, 0, 0, 12)
        Me.LabelCrossoversHeading.Name = "LabelCrossoversHeading"
        Me.LabelCrossoversHeading.Size = New System.Drawing.Size(530, 24)
        Me.LabelCrossoversHeading.TabIndex = 0
        Me.LabelCrossoversHeading.Text = "Crossovers"
        '
        'TextEditCrossoverQty
        '
        Me.TextEditCrossoverQty.EditValue = "0"
        Me.TextEditCrossoverQty.Location = New System.Drawing.Point(198, 39)
        Me.TextEditCrossoverQty.Margin = New System.Windows.Forms.Padding(4, 4, 4, 12)
        Me.TextEditCrossoverQty.Name = "TextEditCrossoverQty"
        Me.TextEditCrossoverQty.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditCrossoverQty.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditCrossoverQty.Properties.Appearance.Options.UseFont = True
        Me.TextEditCrossoverQty.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditCrossoverQty.Properties.Mask.EditMask = "n0"
        Me.TextEditCrossoverQty.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditCrossoverQty.Properties.MaxLength = 3
        Me.TextEditCrossoverQty.Size = New System.Drawing.Size(77, 24)
        Me.TextEditCrossoverQty.TabIndex = 2
        '
        'LabelCrossoverQty
        '
        Me.LabelCrossoverQty.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCrossoverQty.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCrossoverQty.Location = New System.Drawing.Point(0, 43)
        Me.LabelCrossoverQty.Margin = New System.Windows.Forms.Padding(0, 4, 4, 4)
        Me.LabelCrossoverQty.Name = "LabelCrossoverQty"
        Me.LabelCrossoverQty.Size = New System.Drawing.Size(156, 17)
        Me.LabelCrossoverQty.TabIndex = 1
        Me.LabelCrossoverQty.Text = "Crossovers To Install:"
        '
        'GroupCrossoverPreferences
        '
        Me.GroupCrossoverPreferences.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupCrossoverPreferences.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupCrossoverPreferences.Appearance.Options.UseFont = True
        Me.GroupCrossoverPreferences.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupCrossoverPreferences.AppearanceCaption.ForeColor = System.Drawing.Color.DimGray
        Me.GroupCrossoverPreferences.AppearanceCaption.Options.UseFont = True
        Me.GroupCrossoverPreferences.AppearanceCaption.Options.UseForeColor = True
        Me.GroupCrossoverPreferences.Controls.Add(Me.ButtonIncreasePriority)
        Me.GroupCrossoverPreferences.Controls.Add(Me.ButtonDecreasePriority)
        Me.GroupCrossoverPreferences.Controls.Add(Me.ButtonRemoveCrossoverPreference)
        Me.GroupCrossoverPreferences.Controls.Add(Me.ButtonAddCrossoverPreference)
        Me.GroupCrossoverPreferences.Controls.Add(Me.GridCrossoverPreferences)
        Me.GroupCrossoverPreferences.Location = New System.Drawing.Point(0, 81)
        Me.GroupCrossoverPreferences.LookAndFeel.SkinName = "Black"
        Me.GroupCrossoverPreferences.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupCrossoverPreferences.Margin = New System.Windows.Forms.Padding(0, 4, 0, 0)
        Me.GroupCrossoverPreferences.Name = "GroupCrossoverPreferences"
        Me.GroupCrossoverPreferences.Size = New System.Drawing.Size(530, 418)
        Me.GroupCrossoverPreferences.TabIndex = 0
        Me.GroupCrossoverPreferences.Tag = ""
        Me.GroupCrossoverPreferences.Text = "Crossover Preferences"
        '
        'ButtonIncreasePriority
        '
        Me.ButtonIncreasePriority.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonIncreasePriority.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonIncreasePriority.Appearance.Options.UseFont = True
        Me.ButtonIncreasePriority.ImageIndex = 3
        Me.ButtonIncreasePriority.ImageList = Me.ImageList16x16
        Me.ButtonIncreasePriority.Location = New System.Drawing.Point(456, 382)
        Me.ButtonIncreasePriority.LookAndFeel.SkinName = "Black"
        Me.ButtonIncreasePriority.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonIncreasePriority.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonIncreasePriority.Name = "ButtonIncreasePriority"
        Me.ButtonIncreasePriority.Size = New System.Drawing.Size(30, 30)
        Me.ButtonIncreasePriority.TabIndex = 3
        '
        'ButtonDecreasePriority
        '
        Me.ButtonDecreasePriority.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonDecreasePriority.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonDecreasePriority.Appearance.Options.UseFont = True
        Me.ButtonDecreasePriority.ImageIndex = 4
        Me.ButtonDecreasePriority.ImageList = Me.ImageList16x16
        Me.ButtonDecreasePriority.Location = New System.Drawing.Point(494, 382)
        Me.ButtonDecreasePriority.LookAndFeel.SkinName = "Black"
        Me.ButtonDecreasePriority.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonDecreasePriority.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonDecreasePriority.Name = "ButtonDecreasePriority"
        Me.ButtonDecreasePriority.Size = New System.Drawing.Size(30, 30)
        Me.ButtonDecreasePriority.TabIndex = 4
        '
        'ButtonRemoveCrossoverPreference
        '
        Me.ButtonRemoveCrossoverPreference.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonRemoveCrossoverPreference.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonRemoveCrossoverPreference.Appearance.Options.UseFont = True
        Me.ButtonRemoveCrossoverPreference.ImageIndex = 2
        Me.ButtonRemoveCrossoverPreference.ImageList = Me.ImageList16x16
        Me.ButtonRemoveCrossoverPreference.Location = New System.Drawing.Point(111, 382)
        Me.ButtonRemoveCrossoverPreference.LookAndFeel.SkinName = "Black"
        Me.ButtonRemoveCrossoverPreference.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonRemoveCrossoverPreference.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonRemoveCrossoverPreference.Name = "ButtonRemoveCrossoverPreference"
        Me.ButtonRemoveCrossoverPreference.Size = New System.Drawing.Size(96, 30)
        Me.ButtonRemoveCrossoverPreference.TabIndex = 2
        Me.ButtonRemoveCrossoverPreference.Text = "Remove"
        '
        'ButtonAddCrossoverPreference
        '
        Me.ButtonAddCrossoverPreference.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAddCrossoverPreference.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAddCrossoverPreference.Appearance.Options.UseFont = True
        Me.ButtonAddCrossoverPreference.ImageIndex = 0
        Me.ButtonAddCrossoverPreference.ImageList = Me.ImageList16x16
        Me.ButtonAddCrossoverPreference.Location = New System.Drawing.Point(6, 382)
        Me.ButtonAddCrossoverPreference.LookAndFeel.SkinName = "Black"
        Me.ButtonAddCrossoverPreference.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAddCrossoverPreference.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonAddCrossoverPreference.Name = "ButtonAddCrossoverPreference"
        Me.ButtonAddCrossoverPreference.Size = New System.Drawing.Size(96, 30)
        Me.ButtonAddCrossoverPreference.TabIndex = 1
        Me.ButtonAddCrossoverPreference.Text = "Add"
        '
        'GridCrossoverPreferences
        '
        Me.GridCrossoverPreferences.AllowUserToAddRows = False
        Me.GridCrossoverPreferences.AllowUserToDeleteRows = False
        Me.GridCrossoverPreferences.AllowUserToOrderColumns = True
        Me.GridCrossoverPreferences.AllowUserToResizeRows = False
        DataGridViewCellStyle26.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridCrossoverPreferences.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle26
        Me.GridCrossoverPreferences.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridCrossoverPreferences.BackgroundColor = System.Drawing.Color.White
        Me.GridCrossoverPreferences.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridCrossoverPreferences.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridCrossoverPreferences.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle27.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle27.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle27.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle27.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle27.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle27.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle27.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridCrossoverPreferences.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle27
        Me.GridCrossoverPreferences.ColumnHeadersHeight = 22
        Me.GridCrossoverPreferences.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridCrossoverPreferences.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.CrossoverCategoryNameColumn, Me.CrossoverPriorityColumn})
        Me.GridCrossoverPreferences.EnableHeadersVisualStyles = False
        Me.GridCrossoverPreferences.GridColor = System.Drawing.Color.White
        Me.GridCrossoverPreferences.Location = New System.Drawing.Point(3, 29)
        Me.GridCrossoverPreferences.Margin = New System.Windows.Forms.Padding(4)
        Me.GridCrossoverPreferences.Name = "GridCrossoverPreferences"
        Me.GridCrossoverPreferences.ReadOnly = True
        Me.GridCrossoverPreferences.RowHeadersVisible = False
        Me.GridCrossoverPreferences.RowHeadersWidth = 51
        DataGridViewCellStyle28.ForeColor = System.Drawing.Color.DimGray
        Me.GridCrossoverPreferences.RowsDefaultCellStyle = DataGridViewCellStyle28
        Me.GridCrossoverPreferences.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridCrossoverPreferences.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridCrossoverPreferences.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridCrossoverPreferences.RowTemplate.Height = 19
        Me.GridCrossoverPreferences.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridCrossoverPreferences.ShowCellToolTips = False
        Me.GridCrossoverPreferences.Size = New System.Drawing.Size(525, 345)
        Me.GridCrossoverPreferences.StandardTab = True
        Me.GridCrossoverPreferences.TabIndex = 0
        '
        'CrossoverCategoryNameColumn
        '
        Me.CrossoverCategoryNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.CrossoverCategoryNameColumn.DataPropertyName = "CategoryName"
        Me.CrossoverCategoryNameColumn.HeaderText = "Category"
        Me.CrossoverCategoryNameColumn.MinimumWidth = 6
        Me.CrossoverCategoryNameColumn.Name = "CrossoverCategoryNameColumn"
        Me.CrossoverCategoryNameColumn.ReadOnly = True
        '
        'CrossoverPriorityColumn
        '
        Me.CrossoverPriorityColumn.DataPropertyName = "Priority"
        Me.CrossoverPriorityColumn.HeaderText = "Priority"
        Me.CrossoverPriorityColumn.MinimumWidth = 6
        Me.CrossoverPriorityColumn.Name = "CrossoverPriorityColumn"
        Me.CrossoverPriorityColumn.ReadOnly = True
        Me.CrossoverPriorityColumn.Width = 125
        '
        'PanelCategoriesLeft
        '
        Me.PanelCategoriesLeft.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelCategoriesLeft.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelCategoriesLeft.Controls.Add(Me.LabelHomesiteHeading)
        Me.PanelCategoriesLeft.Controls.Add(Me.LabelHomesite)
        Me.PanelCategoriesLeft.Controls.Add(Me.LabelAdditionalCategoriesInfo)
        Me.PanelCategoriesLeft.Controls.Add(Me.LabelAdditionalCategoriesHeading)
        Me.PanelCategoriesLeft.Controls.Add(Me.HyperlinkHomesite)
        Me.PanelCategoriesLeft.Controls.Add(Me.GroupAdditionalCategories)
        Me.PanelCategoriesLeft.Location = New System.Drawing.Point(0, 0)
        Me.PanelCategoriesLeft.Margin = New System.Windows.Forms.Padding(0)
        Me.PanelCategoriesLeft.Name = "PanelCategoriesLeft"
        Me.PanelCategoriesLeft.Size = New System.Drawing.Size(433, 500)
        Me.PanelCategoriesLeft.TabIndex = 0
        '
        'LabelHomesiteHeading
        '
        Me.LabelHomesiteHeading.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelHomesiteHeading.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelHomesiteHeading.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelHomesiteHeading.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelHomesiteHeading.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelHomesiteHeading.LineVisible = True
        Me.LabelHomesiteHeading.Location = New System.Drawing.Point(0, 0)
        Me.LabelHomesiteHeading.Margin = New System.Windows.Forms.Padding(0, 0, 0, 12)
        Me.LabelHomesiteHeading.Name = "LabelHomesiteHeading"
        Me.LabelHomesiteHeading.Size = New System.Drawing.Size(433, 24)
        Me.LabelHomesiteHeading.TabIndex = 0
        Me.LabelHomesiteHeading.Text = "Homesite"
        '
        'LabelHomesite
        '
        Me.LabelHomesite.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelHomesite.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelHomesite.Location = New System.Drawing.Point(0, 43)
        Me.LabelHomesite.Margin = New System.Windows.Forms.Padding(0, 4, 4, 20)
        Me.LabelHomesite.Name = "LabelHomesite"
        Me.LabelHomesite.Size = New System.Drawing.Size(71, 17)
        Me.LabelHomesite.TabIndex = 1
        Me.LabelHomesite.Text = "Homesite:"
        '
        'LabelAdditionalCategoriesInfo
        '
        Me.LabelAdditionalCategoriesInfo.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelAdditionalCategoriesInfo.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelAdditionalCategoriesInfo.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelAdditionalCategoriesInfo.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelAdditionalCategoriesInfo.Location = New System.Drawing.Point(0, 119)
        Me.LabelAdditionalCategoriesInfo.Margin = New System.Windows.Forms.Padding(0, 4, 4, 4)
        Me.LabelAdditionalCategoriesInfo.Name = "LabelAdditionalCategoriesInfo"
        Me.LabelAdditionalCategoriesInfo.Size = New System.Drawing.Size(433, 51)
        Me.LabelAdditionalCategoriesInfo.TabIndex = 4
        Me.LabelAdditionalCategoriesInfo.Text = "Add any other affected categories to this list. These categories will not be exec" &
    "uted (i.e. no ads will be installed in these categories), but they will be block" &
    "ed on the media gap."
        '
        'LabelAdditionalCategoriesHeading
        '
        Me.LabelAdditionalCategoriesHeading.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelAdditionalCategoriesHeading.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelAdditionalCategoriesHeading.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelAdditionalCategoriesHeading.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelAdditionalCategoriesHeading.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelAdditionalCategoriesHeading.LineVisible = True
        Me.LabelAdditionalCategoriesHeading.Location = New System.Drawing.Point(0, 80)
        Me.LabelAdditionalCategoriesHeading.Margin = New System.Windows.Forms.Padding(0, 0, 0, 12)
        Me.LabelAdditionalCategoriesHeading.Name = "LabelAdditionalCategoriesHeading"
        Me.LabelAdditionalCategoriesHeading.Size = New System.Drawing.Size(433, 24)
        Me.LabelAdditionalCategoriesHeading.TabIndex = 3
        Me.LabelAdditionalCategoriesHeading.Text = "Additional Categories"
        '
        'HyperlinkHomesite
        '
        Me.HyperlinkHomesite.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.HyperlinkHomesite.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkHomesite.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkHomesite.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkHomesite.AutoEllipsis = True
        Me.HyperlinkHomesite.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.HyperlinkHomesite.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkHomesite.Location = New System.Drawing.Point(103, 43)
        Me.HyperlinkHomesite.Margin = New System.Windows.Forms.Padding(4, 4, 0, 4)
        Me.HyperlinkHomesite.Name = "HyperlinkHomesite"
        Me.HyperlinkHomesite.Size = New System.Drawing.Size(330, 17)
        Me.HyperlinkHomesite.TabIndex = 2
        Me.HyperlinkHomesite.Text = "Select..."
        '
        'GroupAdditionalCategories
        '
        Me.GroupAdditionalCategories.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupAdditionalCategories.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupAdditionalCategories.Appearance.Options.UseFont = True
        Me.GroupAdditionalCategories.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupAdditionalCategories.AppearanceCaption.ForeColor = System.Drawing.Color.DimGray
        Me.GroupAdditionalCategories.AppearanceCaption.Options.UseFont = True
        Me.GroupAdditionalCategories.AppearanceCaption.Options.UseForeColor = True
        Me.GroupAdditionalCategories.Controls.Add(Me.ButtonRemoveAdditionalCategory)
        Me.GroupAdditionalCategories.Controls.Add(Me.ButtonAddAdditionalCategory)
        Me.GroupAdditionalCategories.Controls.Add(Me.GridAdditionalCategories)
        Me.GroupAdditionalCategories.Location = New System.Drawing.Point(0, 195)
        Me.GroupAdditionalCategories.LookAndFeel.SkinName = "Black"
        Me.GroupAdditionalCategories.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupAdditionalCategories.Margin = New System.Windows.Forms.Padding(0, 4, 0, 0)
        Me.GroupAdditionalCategories.Name = "GroupAdditionalCategories"
        Me.GroupAdditionalCategories.Size = New System.Drawing.Size(433, 305)
        Me.GroupAdditionalCategories.TabIndex = 5
        Me.GroupAdditionalCategories.Tag = ""
        Me.GroupAdditionalCategories.Text = "List of Additional Categories"
        '
        'ButtonRemoveAdditionalCategory
        '
        Me.ButtonRemoveAdditionalCategory.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonRemoveAdditionalCategory.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonRemoveAdditionalCategory.Appearance.Options.UseFont = True
        Me.ButtonRemoveAdditionalCategory.ImageIndex = 2
        Me.ButtonRemoveAdditionalCategory.ImageList = Me.ImageList16x16
        Me.ButtonRemoveAdditionalCategory.Location = New System.Drawing.Point(111, 268)
        Me.ButtonRemoveAdditionalCategory.LookAndFeel.SkinName = "Black"
        Me.ButtonRemoveAdditionalCategory.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonRemoveAdditionalCategory.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonRemoveAdditionalCategory.Name = "ButtonRemoveAdditionalCategory"
        Me.ButtonRemoveAdditionalCategory.Size = New System.Drawing.Size(96, 30)
        Me.ButtonRemoveAdditionalCategory.TabIndex = 2
        Me.ButtonRemoveAdditionalCategory.Text = "Remove"
        '
        'ButtonAddAdditionalCategory
        '
        Me.ButtonAddAdditionalCategory.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAddAdditionalCategory.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAddAdditionalCategory.Appearance.Options.UseFont = True
        Me.ButtonAddAdditionalCategory.ImageIndex = 0
        Me.ButtonAddAdditionalCategory.ImageList = Me.ImageList16x16
        Me.ButtonAddAdditionalCategory.Location = New System.Drawing.Point(6, 268)
        Me.ButtonAddAdditionalCategory.LookAndFeel.SkinName = "Black"
        Me.ButtonAddAdditionalCategory.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAddAdditionalCategory.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonAddAdditionalCategory.Name = "ButtonAddAdditionalCategory"
        Me.ButtonAddAdditionalCategory.Size = New System.Drawing.Size(96, 30)
        Me.ButtonAddAdditionalCategory.TabIndex = 1
        Me.ButtonAddAdditionalCategory.Text = "Add"
        '
        'GridAdditionalCategories
        '
        Me.GridAdditionalCategories.AllowUserToAddRows = False
        Me.GridAdditionalCategories.AllowUserToDeleteRows = False
        Me.GridAdditionalCategories.AllowUserToOrderColumns = True
        Me.GridAdditionalCategories.AllowUserToResizeRows = False
        DataGridViewCellStyle29.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridAdditionalCategories.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle29
        Me.GridAdditionalCategories.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridAdditionalCategories.BackgroundColor = System.Drawing.Color.White
        Me.GridAdditionalCategories.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridAdditionalCategories.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridAdditionalCategories.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle30.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle30.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle30.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle30.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle30.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle30.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle30.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridAdditionalCategories.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle30
        Me.GridAdditionalCategories.ColumnHeadersHeight = 22
        Me.GridAdditionalCategories.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridAdditionalCategories.ColumnHeadersVisible = False
        Me.GridAdditionalCategories.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.AdditionalCategoryNameColumn})
        Me.GridAdditionalCategories.EnableHeadersVisualStyles = False
        Me.GridAdditionalCategories.GridColor = System.Drawing.Color.White
        Me.GridAdditionalCategories.Location = New System.Drawing.Point(3, 29)
        Me.GridAdditionalCategories.Margin = New System.Windows.Forms.Padding(4)
        Me.GridAdditionalCategories.Name = "GridAdditionalCategories"
        Me.GridAdditionalCategories.ReadOnly = True
        Me.GridAdditionalCategories.RowHeadersVisible = False
        Me.GridAdditionalCategories.RowHeadersWidth = 51
        DataGridViewCellStyle31.ForeColor = System.Drawing.Color.DimGray
        Me.GridAdditionalCategories.RowsDefaultCellStyle = DataGridViewCellStyle31
        Me.GridAdditionalCategories.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridAdditionalCategories.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridAdditionalCategories.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridAdditionalCategories.RowTemplate.Height = 19
        Me.GridAdditionalCategories.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridAdditionalCategories.ShowCellToolTips = False
        Me.GridAdditionalCategories.Size = New System.Drawing.Size(428, 231)
        Me.GridAdditionalCategories.StandardTab = True
        Me.GridAdditionalCategories.TabIndex = 0
        Me.GridAdditionalCategories.Tag = "336, 168"
        '
        'AdditionalCategoryNameColumn
        '
        Me.AdditionalCategoryNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.AdditionalCategoryNameColumn.DataPropertyName = "CategoryName"
        Me.AdditionalCategoryNameColumn.HeaderText = "Category"
        Me.AdditionalCategoryNameColumn.MinimumWidth = 6
        Me.AdditionalCategoryNameColumn.Name = "AdditionalCategoryNameColumn"
        Me.AdditionalCategoryNameColumn.ReadOnly = True
        '
        'TabPageStoreList
        '
        Me.TabPageStoreList.Controls.Add(Me.PanelStoreList)
        Me.TabPageStoreList.Margin = New System.Windows.Forms.Padding(4)
        Me.TabPageStoreList.Name = "TabPageStoreList"
        Me.TabPageStoreList.Size = New System.Drawing.Size(1021, 539)
        Me.TabPageStoreList.Text = "Store List"
        '
        'PanelStoreList
        '
        Me.PanelStoreList.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelStoreList.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelStoreList.Controls.Add(Me.PanelStoreListOptions)
        Me.PanelStoreList.Controls.Add(Me.LabelStoreListTitle)
        Me.PanelStoreList.Controls.Add(Me.PanelStoreSharing)
        Me.PanelStoreList.Controls.Add(Me.LabelStoreListOptionsTitle)
        Me.PanelStoreList.Location = New System.Drawing.Point(4, 4)
        Me.PanelStoreList.Margin = New System.Windows.Forms.Padding(4)
        Me.PanelStoreList.Name = "PanelStoreList"
        Me.PanelStoreList.Padding = New System.Windows.Forms.Padding(12)
        Me.PanelStoreList.Size = New System.Drawing.Size(1013, 531)
        Me.PanelStoreList.TabIndex = 0
        '
        'PanelStoreListOptions
        '
        Me.PanelStoreListOptions.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelStoreListOptions.Controls.Add(Me.LabelStoreListSize)
        Me.PanelStoreListOptions.Controls.Add(Me.LabelStoreListSizeValue)
        Me.PanelStoreListOptions.Controls.Add(Me.LabelEditStoreList)
        Me.PanelStoreListOptions.Controls.Add(Me.CheckEditConfirmed)
        Me.PanelStoreListOptions.Controls.Add(Me.ButtonEditStoreList)
        Me.PanelStoreListOptions.Location = New System.Drawing.Point(685, 55)
        Me.PanelStoreListOptions.Margin = New System.Windows.Forms.Padding(4)
        Me.PanelStoreListOptions.Name = "PanelStoreListOptions"
        Me.PanelStoreListOptions.Size = New System.Drawing.Size(312, 460)
        Me.PanelStoreListOptions.TabIndex = 7
        '
        'LabelStoreListSize
        '
        Me.LabelStoreListSize.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelStoreListSize.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelStoreListSize.Location = New System.Drawing.Point(0, 4)
        Me.LabelStoreListSize.Margin = New System.Windows.Forms.Padding(0, 0, 4, 8)
        Me.LabelStoreListSize.Name = "LabelStoreListSize"
        Me.LabelStoreListSize.Size = New System.Drawing.Size(102, 17)
        Me.LabelStoreListSize.TabIndex = 0
        Me.LabelStoreListSize.Text = "Store list size:"
        '
        'LabelStoreListSizeValue
        '
        Me.LabelStoreListSizeValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelStoreListSizeValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelStoreListSizeValue.Location = New System.Drawing.Point(113, 4)
        Me.LabelStoreListSizeValue.Margin = New System.Windows.Forms.Padding(4, 0, 4, 4)
        Me.LabelStoreListSizeValue.Name = "LabelStoreListSizeValue"
        Me.LabelStoreListSizeValue.Size = New System.Drawing.Size(9, 17)
        Me.LabelStoreListSizeValue.TabIndex = 1
        Me.LabelStoreListSizeValue.Text = "0"
        '
        'LabelEditStoreList
        '
        Me.LabelEditStoreList.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelEditStoreList.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelEditStoreList.Location = New System.Drawing.Point(104, 39)
        Me.LabelEditStoreList.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelEditStoreList.Name = "LabelEditStoreList"
        Me.LabelEditStoreList.Size = New System.Drawing.Size(122, 17)
        Me.LabelEditStoreList.TabIndex = 3
        Me.LabelEditStoreList.Text = "Edit the store list"
        '
        'CheckEditConfirmed
        '
        Me.CheckEditConfirmed.Location = New System.Drawing.Point(-4, 75)
        Me.CheckEditConfirmed.Margin = New System.Windows.Forms.Padding(0, 4, 4, 4)
        Me.CheckEditConfirmed.Name = "CheckEditConfirmed"
        Me.CheckEditConfirmed.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditConfirmed.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditConfirmed.Properties.Appearance.Options.UseFont = True
        Me.CheckEditConfirmed.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditConfirmed.Properties.AutoWidth = True
        Me.CheckEditConfirmed.Properties.Caption = "Store list confirmed"
        Me.CheckEditConfirmed.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditConfirmed.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditConfirmed.Size = New System.Drawing.Size(160, 21)
        Me.CheckEditConfirmed.TabIndex = 6
        '
        'ButtonEditStoreList
        '
        Me.ButtonEditStoreList.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonEditStoreList.Appearance.Options.UseFont = True
        Me.ButtonEditStoreList.ImageIndex = 1
        Me.ButtonEditStoreList.ImageList = Me.ImageList16x16
        Me.ButtonEditStoreList.Location = New System.Drawing.Point(0, 33)
        Me.ButtonEditStoreList.LookAndFeel.SkinName = "Black"
        Me.ButtonEditStoreList.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonEditStoreList.Margin = New System.Windows.Forms.Padding(0, 4, 4, 8)
        Me.ButtonEditStoreList.Name = "ButtonEditStoreList"
        Me.ButtonEditStoreList.Size = New System.Drawing.Size(96, 30)
        Me.ButtonEditStoreList.TabIndex = 2
        Me.ButtonEditStoreList.Text = "Edit"
        '
        'LabelStoreListTitle
        '
        Me.LabelStoreListTitle.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelStoreListTitle.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelStoreListTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelStoreListTitle.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelStoreListTitle.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelStoreListTitle.LineVisible = True
        Me.LabelStoreListTitle.Location = New System.Drawing.Point(685, 16)
        Me.LabelStoreListTitle.Margin = New System.Windows.Forms.Padding(4, 4, 4, 12)
        Me.LabelStoreListTitle.Name = "LabelStoreListTitle"
        Me.LabelStoreListTitle.Size = New System.Drawing.Size(312, 24)
        Me.LabelStoreListTitle.TabIndex = 6
        Me.LabelStoreListTitle.Text = "Store List"
        '
        'PanelStoreSharing
        '
        Me.PanelStoreSharing.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelStoreSharing.Controls.Add(Me.GroupRelatedBursts)
        Me.PanelStoreSharing.Controls.Add(Me.LabelMax)
        Me.PanelStoreSharing.Controls.Add(Me.LabelMin)
        Me.PanelStoreSharing.Controls.Add(Me.TextEditStorePoolCapacity)
        Me.PanelStoreSharing.Controls.Add(Me.LabelControl1)
        Me.PanelStoreSharing.Controls.Add(Me.LabelStorePoolQty)
        Me.PanelStoreSharing.Location = New System.Drawing.Point(15, 55)
        Me.PanelStoreSharing.Margin = New System.Windows.Forms.Padding(4, 4, 15, 4)
        Me.PanelStoreSharing.Name = "PanelStoreSharing"
        Me.PanelStoreSharing.Size = New System.Drawing.Size(651, 460)
        Me.PanelStoreSharing.TabIndex = 5
        '
        'GroupRelatedBursts
        '
        Me.GroupRelatedBursts.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupRelatedBursts.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupRelatedBursts.Appearance.Options.UseFont = True
        Me.GroupRelatedBursts.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupRelatedBursts.AppearanceCaption.ForeColor = System.Drawing.Color.DimGray
        Me.GroupRelatedBursts.AppearanceCaption.Options.UseFont = True
        Me.GroupRelatedBursts.AppearanceCaption.Options.UseForeColor = True
        Me.GroupRelatedBursts.Controls.Add(Me.ButtonRemoveBurst)
        Me.GroupRelatedBursts.Controls.Add(Me.ButtonAddBurst)
        Me.GroupRelatedBursts.Controls.Add(Me.GridRelatedBursts)
        Me.GroupRelatedBursts.Location = New System.Drawing.Point(0, 222)
        Me.GroupRelatedBursts.LookAndFeel.SkinName = "Black"
        Me.GroupRelatedBursts.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupRelatedBursts.Margin = New System.Windows.Forms.Padding(0, 0, 0, 16)
        Me.GroupRelatedBursts.Name = "GroupRelatedBursts"
        Me.GroupRelatedBursts.Size = New System.Drawing.Size(651, 238)
        Me.GroupRelatedBursts.TabIndex = 5
        Me.GroupRelatedBursts.Text = "Share a Store Pool with These Bursts"
        '
        'ButtonRemoveBurst
        '
        Me.ButtonRemoveBurst.AccessibleRole = System.Windows.Forms.AccessibleRole.None
        Me.ButtonRemoveBurst.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonRemoveBurst.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonRemoveBurst.Appearance.Options.UseFont = True
        Me.ButtonRemoveBurst.ImageIndex = 2
        Me.ButtonRemoveBurst.ImageList = Me.ImageList16x16
        Me.ButtonRemoveBurst.Location = New System.Drawing.Point(111, 201)
        Me.ButtonRemoveBurst.LookAndFeel.SkinName = "Black"
        Me.ButtonRemoveBurst.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonRemoveBurst.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonRemoveBurst.Name = "ButtonRemoveBurst"
        Me.ButtonRemoveBurst.Size = New System.Drawing.Size(96, 30)
        Me.ButtonRemoveBurst.TabIndex = 1
        Me.ButtonRemoveBurst.Text = "Remove"
        '
        'ButtonAddBurst
        '
        Me.ButtonAddBurst.AccessibleRole = System.Windows.Forms.AccessibleRole.None
        Me.ButtonAddBurst.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAddBurst.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAddBurst.Appearance.Options.UseFont = True
        Me.ButtonAddBurst.ImageIndex = 0
        Me.ButtonAddBurst.ImageList = Me.ImageList16x16
        Me.ButtonAddBurst.Location = New System.Drawing.Point(6, 201)
        Me.ButtonAddBurst.LookAndFeel.SkinName = "Black"
        Me.ButtonAddBurst.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAddBurst.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonAddBurst.Name = "ButtonAddBurst"
        Me.ButtonAddBurst.Size = New System.Drawing.Size(96, 30)
        Me.ButtonAddBurst.TabIndex = 1
        Me.ButtonAddBurst.Text = "Add"
        '
        'GridRelatedBursts
        '
        Me.GridRelatedBursts.AllowUserToAddRows = False
        Me.GridRelatedBursts.AllowUserToDeleteRows = False
        Me.GridRelatedBursts.AllowUserToOrderColumns = True
        Me.GridRelatedBursts.AllowUserToResizeRows = False
        DataGridViewCellStyle32.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridRelatedBursts.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle32
        Me.GridRelatedBursts.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridRelatedBursts.BackgroundColor = System.Drawing.Color.White
        Me.GridRelatedBursts.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridRelatedBursts.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridRelatedBursts.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle33.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle33.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle33.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle33.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle33.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle33.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle33.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridRelatedBursts.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle33
        Me.GridRelatedBursts.ColumnHeadersHeight = 22
        Me.GridRelatedBursts.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridRelatedBursts.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.RelativeContractNumberColumn, Me.RelativeBrandNameColumn, Me.RelativeFirstWeekColumn, Me.RelativeLastWeekColumn, Me.RelativeInstallStoreQty})
        Me.GridRelatedBursts.EnableHeadersVisualStyles = False
        Me.GridRelatedBursts.GridColor = System.Drawing.Color.White
        Me.GridRelatedBursts.Location = New System.Drawing.Point(3, 29)
        Me.GridRelatedBursts.Margin = New System.Windows.Forms.Padding(4, 4, 0, 4)
        Me.GridRelatedBursts.Name = "GridRelatedBursts"
        Me.GridRelatedBursts.ReadOnly = True
        Me.GridRelatedBursts.RowHeadersVisible = False
        Me.GridRelatedBursts.RowHeadersWidth = 51
        DataGridViewCellStyle37.ForeColor = System.Drawing.Color.DimGray
        Me.GridRelatedBursts.RowsDefaultCellStyle = DataGridViewCellStyle37
        Me.GridRelatedBursts.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridRelatedBursts.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridRelatedBursts.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridRelatedBursts.RowTemplate.Height = 19
        Me.GridRelatedBursts.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridRelatedBursts.ShowCellToolTips = False
        Me.GridRelatedBursts.Size = New System.Drawing.Size(645, 165)
        Me.GridRelatedBursts.StandardTab = True
        Me.GridRelatedBursts.TabIndex = 0
        '
        'RelativeContractNumberColumn
        '
        Me.RelativeContractNumberColumn.DataPropertyName = "ContractNumber"
        Me.RelativeContractNumberColumn.HeaderText = "Contract"
        Me.RelativeContractNumberColumn.MinimumWidth = 6
        Me.RelativeContractNumberColumn.Name = "RelativeContractNumberColumn"
        Me.RelativeContractNumberColumn.ReadOnly = True
        Me.RelativeContractNumberColumn.Width = 91
        '
        'RelativeBrandNameColumn
        '
        Me.RelativeBrandNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.AllCells
        Me.RelativeBrandNameColumn.DataPropertyName = "BrandName"
        Me.RelativeBrandNameColumn.HeaderText = "Brand"
        Me.RelativeBrandNameColumn.MinimumWidth = 6
        Me.RelativeBrandNameColumn.Name = "RelativeBrandNameColumn"
        Me.RelativeBrandNameColumn.ReadOnly = True
        Me.RelativeBrandNameColumn.Width = 78
        '
        'RelativeFirstWeekColumn
        '
        Me.RelativeFirstWeekColumn.DataPropertyName = "FirstWeek"
        DataGridViewCellStyle34.Format = "d"
        DataGridViewCellStyle34.NullValue = Nothing
        Me.RelativeFirstWeekColumn.DefaultCellStyle = DataGridViewCellStyle34
        Me.RelativeFirstWeekColumn.HeaderText = "First Week"
        Me.RelativeFirstWeekColumn.MinimumWidth = 6
        Me.RelativeFirstWeekColumn.Name = "RelativeFirstWeekColumn"
        Me.RelativeFirstWeekColumn.ReadOnly = True
        Me.RelativeFirstWeekColumn.Width = 91
        '
        'RelativeLastWeekColumn
        '
        Me.RelativeLastWeekColumn.DataPropertyName = "LastWeek"
        DataGridViewCellStyle35.Format = "d"
        Me.RelativeLastWeekColumn.DefaultCellStyle = DataGridViewCellStyle35
        Me.RelativeLastWeekColumn.HeaderText = "Last Week"
        Me.RelativeLastWeekColumn.MinimumWidth = 6
        Me.RelativeLastWeekColumn.Name = "RelativeLastWeekColumn"
        Me.RelativeLastWeekColumn.ReadOnly = True
        Me.RelativeLastWeekColumn.Width = 91
        '
        'RelativeInstallStoreQty
        '
        Me.RelativeInstallStoreQty.DataPropertyName = "InstallStoreQty"
        DataGridViewCellStyle36.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter
        Me.RelativeInstallStoreQty.DefaultCellStyle = DataGridViewCellStyle36
        Me.RelativeInstallStoreQty.HeaderText = "Stores"
        Me.RelativeInstallStoreQty.MinimumWidth = 6
        Me.RelativeInstallStoreQty.Name = "RelativeInstallStoreQty"
        Me.RelativeInstallStoreQty.ReadOnly = True
        Me.RelativeInstallStoreQty.Width = 80
        '
        'LabelMax
        '
        Me.LabelMax.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.LabelMax.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelMax.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelMax.AutoEllipsis = True
        Me.LabelMax.Cursor = System.Windows.Forms.Cursors.Hand
        Me.LabelMax.Location = New System.Drawing.Point(282, 4)
        Me.LabelMax.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelMax.Name = "LabelMax"
        Me.LabelMax.Size = New System.Drawing.Size(30, 17)
        Me.LabelMax.TabIndex = 3
        Me.LabelMax.Text = "max"
        '
        'LabelMin
        '
        Me.LabelMin.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.LabelMin.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelMin.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelMin.AutoEllipsis = True
        Me.LabelMin.Cursor = System.Windows.Forms.Cursors.Hand
        Me.LabelMin.Location = New System.Drawing.Point(247, 4)
        Me.LabelMin.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelMin.Name = "LabelMin"
        Me.LabelMin.Size = New System.Drawing.Size(25, 17)
        Me.LabelMin.TabIndex = 2
        Me.LabelMin.Text = "min"
        '
        'TextEditStorePoolCapacity
        '
        Me.TextEditStorePoolCapacity.EditValue = 0
        Me.TextEditStorePoolCapacity.Location = New System.Drawing.Point(163, 0)
        Me.TextEditStorePoolCapacity.Margin = New System.Windows.Forms.Padding(4)
        Me.TextEditStorePoolCapacity.Name = "TextEditStorePoolCapacity"
        Me.TextEditStorePoolCapacity.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditStorePoolCapacity.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditStorePoolCapacity.Properties.Appearance.Options.UseFont = True
        Me.TextEditStorePoolCapacity.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditStorePoolCapacity.Properties.Mask.EditMask = "n0"
        Me.TextEditStorePoolCapacity.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Numeric
        Me.TextEditStorePoolCapacity.Properties.Mask.UseMaskAsDisplayFormat = True
        Me.TextEditStorePoolCapacity.Properties.MaxLength = 3
        Me.TextEditStorePoolCapacity.Size = New System.Drawing.Size(76, 24)
        Me.TextEditStorePoolCapacity.TabIndex = 1
        '
        'LabelControl1
        '
        Me.LabelControl1.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl1.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl1.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelControl1.Location = New System.Drawing.Point(0, 39)
        Me.LabelControl1.Margin = New System.Windows.Forms.Padding(0, 4, 8, 13)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(651, 170)
        Me.LabelControl1.TabIndex = 4
        Me.LabelControl1.Text = resources.GetString("LabelControl1.Text")
        '
        'LabelStorePoolQty
        '
        Me.LabelStorePoolQty.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelStorePoolQty.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelStorePoolQty.Location = New System.Drawing.Point(0, 4)
        Me.LabelStorePoolQty.Margin = New System.Windows.Forms.Padding(0, 4, 8, 13)
        Me.LabelStorePoolQty.Name = "LabelStorePoolQty"
        Me.LabelStorePoolQty.Size = New System.Drawing.Size(143, 17)
        Me.LabelStorePoolQty.TabIndex = 0
        Me.LabelStorePoolQty.Text = "Store Pool Capacity:"
        '
        'LabelStoreListOptionsTitle
        '
        Me.LabelStoreListOptionsTitle.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelStoreListOptionsTitle.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelStoreListOptionsTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelStoreListOptionsTitle.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelStoreListOptionsTitle.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelStoreListOptionsTitle.LineVisible = True
        Me.LabelStoreListOptionsTitle.Location = New System.Drawing.Point(15, 16)
        Me.LabelStoreListOptionsTitle.Margin = New System.Windows.Forms.Padding(4, 4, 15, 12)
        Me.LabelStoreListOptionsTitle.Name = "LabelStoreListOptionsTitle"
        Me.LabelStoreListOptionsTitle.Size = New System.Drawing.Size(651, 24)
        Me.LabelStoreListOptionsTitle.TabIndex = 0
        Me.LabelStoreListOptionsTitle.Text = "Advanced Store List Options"
        '
        'TabPageInstallationInstructions
        '
        Me.TabPageInstallationInstructions.Controls.Add(Me.PanelInstallationInstructions)
        Me.TabPageInstallationInstructions.Margin = New System.Windows.Forms.Padding(4)
        Me.TabPageInstallationInstructions.Name = "TabPageInstallationInstructions"
        Me.TabPageInstallationInstructions.Size = New System.Drawing.Size(1021, 539)
        Me.TabPageInstallationInstructions.Text = "Installation Instructions"
        '
        'PanelInstallationInstructions
        '
        Me.PanelInstallationInstructions.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelInstallationInstructions.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelInstallationInstructions.Controls.Add(Me.HyperlinkApplyInstuctionsOnAllBursts)
        Me.PanelInstallationInstructions.Controls.Add(Me.PictureApplyInstuctionsOnAllBursts)
        Me.PanelInstallationInstructions.Controls.Add(Me.CheckEditDoNotInstallRegardless)
        Me.PanelInstallationInstructions.Controls.Add(Me.CheckEditInstallRegardless)
        Me.PanelInstallationInstructions.Controls.Add(Me.TextEditAdsPerShelfTalk)
        Me.PanelInstallationInstructions.Controls.Add(Me.LabelAdsPerInstallationShelfTalk)
        Me.PanelInstallationInstructions.Controls.Add(Me.LabelControl2)
        Me.PanelInstallationInstructions.Controls.Add(Me.TextEditAdsPerCrossover)
        Me.PanelInstallationInstructions.Controls.Add(Me.PanelHomesiteInstallationOptions)
        Me.PanelInstallationInstructions.Controls.Add(Me.LabelHomesiteInstallationTitle)
        Me.PanelInstallationInstructions.Controls.Add(Me.LabelProductNameTitle)
        Me.PanelInstallationInstructions.Controls.Add(Me.LabelAdsPerInstallation)
        Me.PanelInstallationInstructions.Controls.Add(Me.LabelProductName)
        Me.PanelInstallationInstructions.Controls.Add(Me.TextEditAdsPerHomesite)
        Me.PanelInstallationInstructions.Controls.Add(Me.TextEditProductName)
        Me.PanelInstallationInstructions.Controls.Add(Me.GroupInstallationInstructions)
        Me.PanelInstallationInstructions.Location = New System.Drawing.Point(4, 4)
        Me.PanelInstallationInstructions.Margin = New System.Windows.Forms.Padding(4)
        Me.PanelInstallationInstructions.Name = "PanelInstallationInstructions"
        Me.PanelInstallationInstructions.Padding = New System.Windows.Forms.Padding(12)
        Me.PanelInstallationInstructions.Size = New System.Drawing.Size(1013, 531)
        Me.PanelInstallationInstructions.TabIndex = 0
        '
        'HyperlinkApplyInstuctionsOnAllBursts
        '
        Me.HyperlinkApplyInstuctionsOnAllBursts.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkApplyInstuctionsOnAllBursts.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkApplyInstuctionsOnAllBursts.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkApplyInstuctionsOnAllBursts.AutoEllipsis = True
        Me.HyperlinkApplyInstuctionsOnAllBursts.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkApplyInstuctionsOnAllBursts.Location = New System.Drawing.Point(724, 496)
        Me.HyperlinkApplyInstuctionsOnAllBursts.Margin = New System.Windows.Forms.Padding(4, 0, 0, 4)
        Me.HyperlinkApplyInstuctionsOnAllBursts.Name = "HyperlinkApplyInstuctionsOnAllBursts"
        Me.HyperlinkApplyInstuctionsOnAllBursts.Size = New System.Drawing.Size(259, 17)
        Me.HyperlinkApplyInstuctionsOnAllBursts.TabIndex = 21
        Me.HyperlinkApplyInstuctionsOnAllBursts.Text = "Apply Instuctions Across ALL Bursts"
        '
        'PictureApplyInstuctionsOnAllBursts
        '
        Me.PictureApplyInstuctionsOnAllBursts.AllowDrop = True
        Me.PictureApplyInstuctionsOnAllBursts.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureApplyInstuctionsOnAllBursts.EditValue = Global.Nova2.My.Resources.Resources.accept16
        Me.PictureApplyInstuctionsOnAllBursts.Location = New System.Drawing.Point(696, 496)
        Me.PictureApplyInstuctionsOnAllBursts.Margin = New System.Windows.Forms.Padding(0, 4, 4, 0)
        Me.PictureApplyInstuctionsOnAllBursts.Name = "PictureApplyInstuctionsOnAllBursts"
        Me.PictureApplyInstuctionsOnAllBursts.Properties.AllowFocused = False
        Me.PictureApplyInstuctionsOnAllBursts.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureApplyInstuctionsOnAllBursts.Properties.Appearance.Options.UseBackColor = True
        Me.PictureApplyInstuctionsOnAllBursts.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureApplyInstuctionsOnAllBursts.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem1.Text = "Minimum"
        ToolTipItem1.LeftIndent = 6
        ToolTipItem1.Text = "Set the store pool size to the smallest possible quantity"
        SuperToolTip1.Items.Add(ToolTipTitleItem1)
        SuperToolTip1.Items.Add(ToolTipItem1)
        Me.PictureApplyInstuctionsOnAllBursts.SuperTip = SuperToolTip1
        Me.PictureApplyInstuctionsOnAllBursts.TabIndex = 20
        Me.PictureApplyInstuctionsOnAllBursts.TabStop = True
        '
        'CheckEditDoNotInstallRegardless
        '
        Me.CheckEditDoNotInstallRegardless.Location = New System.Drawing.Point(581, 188)
        Me.CheckEditDoNotInstallRegardless.Margin = New System.Windows.Forms.Padding(4, 4, 4, 5)
        Me.CheckEditDoNotInstallRegardless.Name = "CheckEditDoNotInstallRegardless"
        Me.CheckEditDoNotInstallRegardless.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditDoNotInstallRegardless.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditDoNotInstallRegardless.Properties.Appearance.Options.UseFont = True
        Me.CheckEditDoNotInstallRegardless.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditDoNotInstallRegardless.Properties.AutoWidth = True
        Me.CheckEditDoNotInstallRegardless.Properties.Caption = "Do not Install if no stock"
        Me.CheckEditDoNotInstallRegardless.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Radio
        Me.CheckEditDoNotInstallRegardless.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditDoNotInstallRegardless.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditDoNotInstallRegardless.Properties.RadioGroupIndex = 1
        Me.CheckEditDoNotInstallRegardless.Size = New System.Drawing.Size(197, 21)
        Me.CheckEditDoNotInstallRegardless.TabIndex = 18
        Me.CheckEditDoNotInstallRegardless.TabStop = False
        '
        'CheckEditInstallRegardless
        '
        Me.CheckEditInstallRegardless.Location = New System.Drawing.Point(410, 187)
        Me.CheckEditInstallRegardless.Margin = New System.Windows.Forms.Padding(4, 4, 4, 5)
        Me.CheckEditInstallRegardless.Name = "CheckEditInstallRegardless"
        Me.CheckEditInstallRegardless.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditInstallRegardless.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditInstallRegardless.Properties.Appearance.Options.UseFont = True
        Me.CheckEditInstallRegardless.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditInstallRegardless.Properties.AutoWidth = True
        Me.CheckEditInstallRegardless.Properties.Caption = "Install Regardless"
        Me.CheckEditInstallRegardless.Properties.CheckStyle = DevExpress.XtraEditors.Controls.CheckStyles.Radio
        Me.CheckEditInstallRegardless.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditInstallRegardless.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditInstallRegardless.Properties.RadioGroupIndex = 1
        Me.CheckEditInstallRegardless.Size = New System.Drawing.Size(146, 21)
        Me.CheckEditInstallRegardless.TabIndex = 17
        Me.CheckEditInstallRegardless.TabStop = False
        '
        'TextEditAdsPerShelfTalk
        '
        Me.TextEditAdsPerShelfTalk.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditAdsPerShelfTalk.EditValue = "0"
        Me.TextEditAdsPerShelfTalk.Location = New System.Drawing.Point(415, 156)
        Me.TextEditAdsPerShelfTalk.Margin = New System.Windows.Forms.Padding(4, 4, 15, 4)
        Me.TextEditAdsPerShelfTalk.Name = "TextEditAdsPerShelfTalk"
        Me.TextEditAdsPerShelfTalk.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditAdsPerShelfTalk.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditAdsPerShelfTalk.Properties.Appearance.Options.UseFont = True
        Me.TextEditAdsPerShelfTalk.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditAdsPerShelfTalk.Properties.MaxLength = 15
        Me.TextEditAdsPerShelfTalk.Size = New System.Drawing.Size(276, 24)
        Me.TextEditAdsPerShelfTalk.TabIndex = 10
        Me.TextEditAdsPerShelfTalk.Visible = False
        '
        'LabelAdsPerInstallationShelfTalk
        '
        Me.LabelAdsPerInstallationShelfTalk.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelAdsPerInstallationShelfTalk.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelAdsPerInstallationShelfTalk.Location = New System.Drawing.Point(15, 158)
        Me.LabelAdsPerInstallationShelfTalk.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelAdsPerInstallationShelfTalk.Name = "LabelAdsPerInstallationShelfTalk"
        Me.LabelAdsPerInstallationShelfTalk.Size = New System.Drawing.Size(330, 17)
        Me.LabelAdsPerInstallationShelfTalk.TabIndex = 9
        Me.LabelAdsPerInstallationShelfTalk.Text = "Required number of ads for each SHELFTALK :"
        Me.LabelAdsPerInstallationShelfTalk.Visible = False
        '
        'LabelControl2
        '
        Me.LabelControl2.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl2.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl2.Location = New System.Drawing.Point(15, 127)
        Me.LabelControl2.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelControl2.Name = "LabelControl2"
        Me.LabelControl2.Size = New System.Drawing.Size(334, 17)
        Me.LabelControl2.TabIndex = 6
        Me.LabelControl2.Text = "Required number of ads for each CROSSOVER:"
        '
        'TextEditAdsPerCrossover
        '
        Me.TextEditAdsPerCrossover.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditAdsPerCrossover.EditValue = "0"
        Me.TextEditAdsPerCrossover.Location = New System.Drawing.Point(415, 123)
        Me.TextEditAdsPerCrossover.Margin = New System.Windows.Forms.Padding(4, 4, 15, 4)
        Me.TextEditAdsPerCrossover.Name = "TextEditAdsPerCrossover"
        Me.TextEditAdsPerCrossover.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditAdsPerCrossover.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditAdsPerCrossover.Properties.Appearance.Options.UseFont = True
        Me.TextEditAdsPerCrossover.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditAdsPerCrossover.Properties.MaxLength = 15
        Me.TextEditAdsPerCrossover.Size = New System.Drawing.Size(276, 24)
        Me.TextEditAdsPerCrossover.TabIndex = 7
        '
        'PanelHomesiteInstallationOptions
        '
        Me.PanelHomesiteInstallationOptions.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelHomesiteInstallationOptions.Controls.Add(Me.PictureInstallAtHomesite)
        Me.PanelHomesiteInstallationOptions.Controls.Add(Me.PictureDontInstallAtHomesite)
        Me.PanelHomesiteInstallationOptions.Controls.Add(Me.HyperlinkInstallAtHomesite)
        Me.PanelHomesiteInstallationOptions.Controls.Add(Me.HyperlinkDontInstallAtHomesite)
        Me.PanelHomesiteInstallationOptions.Location = New System.Drawing.Point(711, 58)
        Me.PanelHomesiteInstallationOptions.Margin = New System.Windows.Forms.Padding(4, 4, 4, 20)
        Me.PanelHomesiteInstallationOptions.Name = "PanelHomesiteInstallationOptions"
        Me.PanelHomesiteInstallationOptions.Size = New System.Drawing.Size(287, 55)
        Me.PanelHomesiteInstallationOptions.TabIndex = 6
        '
        'PictureInstallAtHomesite
        '
        Me.PictureInstallAtHomesite.AllowDrop = True
        Me.PictureInstallAtHomesite.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureInstallAtHomesite.EditValue = Global.Nova2.My.Resources.Resources.accept16
        Me.PictureInstallAtHomesite.Location = New System.Drawing.Point(0, 0)
        Me.PictureInstallAtHomesite.Margin = New System.Windows.Forms.Padding(0, 0, 4, 4)
        Me.PictureInstallAtHomesite.Name = "PictureInstallAtHomesite"
        Me.PictureInstallAtHomesite.Properties.AllowFocused = False
        Me.PictureInstallAtHomesite.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureInstallAtHomesite.Properties.Appearance.Options.UseBackColor = True
        Me.PictureInstallAtHomesite.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureInstallAtHomesite.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem2.Text = "Minimum"
        ToolTipItem2.LeftIndent = 6
        ToolTipItem2.Text = "Set the store pool size to the smallest possible quantity"
        SuperToolTip2.Items.Add(ToolTipTitleItem2)
        SuperToolTip2.Items.Add(ToolTipItem2)
        Me.PictureInstallAtHomesite.SuperTip = SuperToolTip2
        Me.PictureInstallAtHomesite.TabIndex = 0
        Me.PictureInstallAtHomesite.TabStop = True
        '
        'PictureDontInstallAtHomesite
        '
        Me.PictureDontInstallAtHomesite.AllowDrop = True
        Me.PictureDontInstallAtHomesite.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureDontInstallAtHomesite.EditValue = Global.Nova2.My.Resources.Resources.accept16
        Me.PictureDontInstallAtHomesite.Location = New System.Drawing.Point(0, 34)
        Me.PictureDontInstallAtHomesite.Margin = New System.Windows.Forms.Padding(0, 4, 4, 0)
        Me.PictureDontInstallAtHomesite.Name = "PictureDontInstallAtHomesite"
        Me.PictureDontInstallAtHomesite.Properties.AllowFocused = False
        Me.PictureDontInstallAtHomesite.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureDontInstallAtHomesite.Properties.Appearance.Options.UseBackColor = True
        Me.PictureDontInstallAtHomesite.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureDontInstallAtHomesite.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem3.Text = "Minimum"
        ToolTipItem3.LeftIndent = 6
        ToolTipItem3.Text = "Set the store pool size to the smallest possible quantity"
        SuperToolTip3.Items.Add(ToolTipTitleItem3)
        SuperToolTip3.Items.Add(ToolTipItem3)
        Me.PictureDontInstallAtHomesite.SuperTip = SuperToolTip3
        Me.PictureDontInstallAtHomesite.TabIndex = 2
        Me.PictureDontInstallAtHomesite.TabStop = True
        '
        'HyperlinkInstallAtHomesite
        '
        Me.HyperlinkInstallAtHomesite.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkInstallAtHomesite.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkInstallAtHomesite.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkInstallAtHomesite.AutoEllipsis = True
        Me.HyperlinkInstallAtHomesite.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkInstallAtHomesite.Location = New System.Drawing.Point(28, 1)
        Me.HyperlinkInstallAtHomesite.Margin = New System.Windows.Forms.Padding(4, 0, 0, 4)
        Me.HyperlinkInstallAtHomesite.Name = "HyperlinkInstallAtHomesite"
        Me.HyperlinkInstallAtHomesite.Size = New System.Drawing.Size(188, 17)
        Me.HyperlinkInstallAtHomesite.TabIndex = 1
        Me.HyperlinkInstallAtHomesite.Text = "Install ads at the homesite"
        '
        'HyperlinkDontInstallAtHomesite
        '
        Me.HyperlinkDontInstallAtHomesite.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkDontInstallAtHomesite.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkDontInstallAtHomesite.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkDontInstallAtHomesite.AutoEllipsis = True
        Me.HyperlinkDontInstallAtHomesite.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkDontInstallAtHomesite.Location = New System.Drawing.Point(28, 35)
        Me.HyperlinkDontInstallAtHomesite.Margin = New System.Windows.Forms.Padding(4, 4, 0, 0)
        Me.HyperlinkDontInstallAtHomesite.Name = "HyperlinkDontInstallAtHomesite"
        Me.HyperlinkDontInstallAtHomesite.Size = New System.Drawing.Size(248, 17)
        Me.HyperlinkDontInstallAtHomesite.TabIndex = 3
        Me.HyperlinkDontInstallAtHomesite.Text = "DO NOT install ads at the homesite"
        '
        'LabelHomesiteInstallationTitle
        '
        Me.LabelHomesiteInstallationTitle.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelHomesiteInstallationTitle.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelHomesiteInstallationTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelHomesiteInstallationTitle.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelHomesiteInstallationTitle.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelHomesiteInstallationTitle.LineVisible = True
        Me.LabelHomesiteInstallationTitle.Location = New System.Drawing.Point(711, 16)
        Me.LabelHomesiteInstallationTitle.Margin = New System.Windows.Forms.Padding(4, 4, 4, 12)
        Me.LabelHomesiteInstallationTitle.Name = "LabelHomesiteInstallationTitle"
        Me.LabelHomesiteInstallationTitle.Size = New System.Drawing.Size(287, 24)
        Me.LabelHomesiteInstallationTitle.TabIndex = 1
        Me.LabelHomesiteInstallationTitle.Text = "Homesite Installation"
        '
        'LabelProductNameTitle
        '
        Me.LabelProductNameTitle.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelProductNameTitle.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelProductNameTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelProductNameTitle.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelProductNameTitle.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelProductNameTitle.LineVisible = True
        Me.LabelProductNameTitle.Location = New System.Drawing.Point(15, 16)
        Me.LabelProductNameTitle.Margin = New System.Windows.Forms.Padding(4, 4, 15, 12)
        Me.LabelProductNameTitle.Name = "LabelProductNameTitle"
        Me.LabelProductNameTitle.Size = New System.Drawing.Size(676, 24)
        Me.LabelProductNameTitle.TabIndex = 0
        Me.LabelProductNameTitle.Text = "Product Description"
        '
        'LabelAdsPerInstallation
        '
        Me.LabelAdsPerInstallation.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelAdsPerInstallation.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelAdsPerInstallation.Location = New System.Drawing.Point(15, 93)
        Me.LabelAdsPerInstallation.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelAdsPerInstallation.Name = "LabelAdsPerInstallation"
        Me.LabelAdsPerInstallation.Size = New System.Drawing.Size(302, 17)
        Me.LabelAdsPerInstallation.TabIndex = 4
        Me.LabelAdsPerInstallation.Text = "Required number of ads at the HOMESITE:"
        '
        'LabelProductName
        '
        Me.LabelProductName.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelProductName.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelProductName.Location = New System.Drawing.Point(15, 59)
        Me.LabelProductName.Margin = New System.Windows.Forms.Padding(4)
        Me.LabelProductName.Name = "LabelProductName"
        Me.LabelProductName.Size = New System.Drawing.Size(351, 17)
        Me.LabelProductName.TabIndex = 2
        Me.LabelProductName.Text = "Descriptive Product Name (for installation teams):"
        '
        'TextEditAdsPerHomesite
        '
        Me.TextEditAdsPerHomesite.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditAdsPerHomesite.EditValue = ""
        Me.TextEditAdsPerHomesite.Location = New System.Drawing.Point(415, 89)
        Me.TextEditAdsPerHomesite.Margin = New System.Windows.Forms.Padding(4, 4, 15, 4)
        Me.TextEditAdsPerHomesite.Name = "TextEditAdsPerHomesite"
        Me.TextEditAdsPerHomesite.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditAdsPerHomesite.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditAdsPerHomesite.Properties.Appearance.Options.UseFont = True
        Me.TextEditAdsPerHomesite.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditAdsPerHomesite.Properties.MaxLength = 15
        Me.TextEditAdsPerHomesite.Size = New System.Drawing.Size(276, 24)
        Me.TextEditAdsPerHomesite.TabIndex = 5
        '
        'TextEditProductName
        '
        Me.TextEditProductName.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditProductName.EditValue = ""
        Me.TextEditProductName.Location = New System.Drawing.Point(415, 55)
        Me.TextEditProductName.Margin = New System.Windows.Forms.Padding(4, 4, 15, 4)
        Me.TextEditProductName.Name = "TextEditProductName"
        Me.TextEditProductName.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditProductName.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditProductName.Properties.Appearance.Options.UseFont = True
        Me.TextEditProductName.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditProductName.Properties.MaxLength = 20
        Me.TextEditProductName.Size = New System.Drawing.Size(276, 24)
        Me.TextEditProductName.TabIndex = 3
        '
        'GroupInstallationInstructions
        '
        Me.GroupInstallationInstructions.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupInstallationInstructions.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupInstallationInstructions.Appearance.Options.UseFont = True
        Me.GroupInstallationInstructions.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupInstallationInstructions.AppearanceCaption.Options.UseFont = True
        Me.GroupInstallationInstructions.Controls.Add(Me.MemoEditInstallationInstructions)
        Me.GroupInstallationInstructions.Location = New System.Drawing.Point(15, 216)
        Me.GroupInstallationInstructions.LookAndFeel.SkinName = "Black"
        Me.GroupInstallationInstructions.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupInstallationInstructions.Margin = New System.Windows.Forms.Padding(4)
        Me.GroupInstallationInstructions.Name = "GroupInstallationInstructions"
        Me.GroupInstallationInstructions.Size = New System.Drawing.Size(982, 267)
        Me.GroupInstallationInstructions.TabIndex = 8
        Me.GroupInstallationInstructions.Tag = ""
        Me.GroupInstallationInstructions.Text = "Instructions to Installation Teams"
        '
        'MemoEditInstallationInstructions
        '
        Me.MemoEditInstallationInstructions.Dock = System.Windows.Forms.DockStyle.Fill
        Me.MemoEditInstallationInstructions.EditValue = ""
        Me.MemoEditInstallationInstructions.Location = New System.Drawing.Point(2, 25)
        Me.MemoEditInstallationInstructions.Margin = New System.Windows.Forms.Padding(4, 4, 4, 0)
        Me.MemoEditInstallationInstructions.Name = "MemoEditInstallationInstructions"
        Me.MemoEditInstallationInstructions.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.MemoEditInstallationInstructions.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.MemoEditInstallationInstructions.Properties.Appearance.Options.UseFont = True
        Me.MemoEditInstallationInstructions.Properties.Appearance.Options.UseForeColor = True
        Me.MemoEditInstallationInstructions.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.MemoEditInstallationInstructions.Properties.ScrollBars = System.Windows.Forms.ScrollBars.None
        Me.MemoEditInstallationInstructions.Size = New System.Drawing.Size(978, 240)
        Me.MemoEditInstallationInstructions.TabIndex = 0
        '
        'TabPageInteractionDates
        '
        Me.TabPageInteractionDates.Controls.Add(Me.Panel1)
        Me.TabPageInteractionDates.Name = "TabPageInteractionDates"
        Me.TabPageInteractionDates.PageVisible = False
        Me.TabPageInteractionDates.Size = New System.Drawing.Size(1021, 539)
        Me.TabPageInteractionDates.Text = "Interaction Dates"
        '
        'Panel1
        '
        Me.Panel1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Panel1.Controls.Add(Me.GroupControlInstallationDays)
        Me.Panel1.Controls.Add(Me.LabelInterctionDateTitle)
        Me.Panel1.Location = New System.Drawing.Point(4, 4)
        Me.Panel1.Margin = New System.Windows.Forms.Padding(4)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Padding = New System.Windows.Forms.Padding(12)
        Me.Panel1.Size = New System.Drawing.Size(1013, 531)
        Me.Panel1.TabIndex = 1
        '
        'GroupControlInstallationDays
        '
        Me.GroupControlInstallationDays.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlInstallationDays.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlInstallationDays.Appearance.Options.UseFont = True
        Me.GroupControlInstallationDays.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlInstallationDays.AppearanceCaption.ForeColor = System.Drawing.Color.DimGray
        Me.GroupControlInstallationDays.AppearanceCaption.Options.UseFont = True
        Me.GroupControlInstallationDays.AppearanceCaption.Options.UseForeColor = True
        Me.GroupControlInstallationDays.Controls.Add(Me.ButtonDeleteInstallationDay)
        Me.GroupControlInstallationDays.Controls.Add(Me.ButtonAddInstallationDay)
        Me.GroupControlInstallationDays.Controls.Add(Me.GridInstallationDays)
        Me.GroupControlInstallationDays.Location = New System.Drawing.Point(15, 56)
        Me.GroupControlInstallationDays.LookAndFeel.SkinName = "Black"
        Me.GroupControlInstallationDays.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlInstallationDays.Margin = New System.Windows.Forms.Padding(4)
        Me.GroupControlInstallationDays.Name = "GroupControlInstallationDays"
        Me.GroupControlInstallationDays.Size = New System.Drawing.Size(548, 361)
        Me.GroupControlInstallationDays.TabIndex = 1
        Me.GroupControlInstallationDays.Tag = ""
        Me.GroupControlInstallationDays.Text = "Day List"
        '
        'ButtonDeleteInstallationDay
        '
        Me.ButtonDeleteInstallationDay.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonDeleteInstallationDay.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonDeleteInstallationDay.Appearance.Options.UseFont = True
        Me.ButtonDeleteInstallationDay.ImageIndex = 2
        Me.ButtonDeleteInstallationDay.ImageList = Me.ImageList16x16
        Me.ButtonDeleteInstallationDay.Location = New System.Drawing.Point(110, 324)
        Me.ButtonDeleteInstallationDay.LookAndFeel.SkinName = "Black"
        Me.ButtonDeleteInstallationDay.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonDeleteInstallationDay.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonDeleteInstallationDay.Name = "ButtonDeleteInstallationDay"
        Me.ButtonDeleteInstallationDay.Size = New System.Drawing.Size(96, 30)
        Me.ButtonDeleteInstallationDay.TabIndex = 4
        Me.ButtonDeleteInstallationDay.Text = "Remove"
        '
        'ButtonAddInstallationDay
        '
        Me.ButtonAddInstallationDay.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAddInstallationDay.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAddInstallationDay.Appearance.Options.UseFont = True
        Me.ButtonAddInstallationDay.ImageIndex = 0
        Me.ButtonAddInstallationDay.ImageList = Me.ImageList16x16
        Me.ButtonAddInstallationDay.Location = New System.Drawing.Point(6, 324)
        Me.ButtonAddInstallationDay.LookAndFeel.SkinName = "Black"
        Me.ButtonAddInstallationDay.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAddInstallationDay.Margin = New System.Windows.Forms.Padding(4)
        Me.ButtonAddInstallationDay.Name = "ButtonAddInstallationDay"
        Me.ButtonAddInstallationDay.Size = New System.Drawing.Size(96, 30)
        Me.ButtonAddInstallationDay.TabIndex = 2
        Me.ButtonAddInstallationDay.Text = "Add"
        '
        'GridInstallationDays
        '
        Me.GridInstallationDays.AllowUserToAddRows = False
        Me.GridInstallationDays.AllowUserToDeleteRows = False
        Me.GridInstallationDays.AllowUserToOrderColumns = True
        Me.GridInstallationDays.AllowUserToResizeRows = False
        DataGridViewCellStyle38.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridInstallationDays.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle38
        Me.GridInstallationDays.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridInstallationDays.BackgroundColor = System.Drawing.Color.White
        Me.GridInstallationDays.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridInstallationDays.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridInstallationDays.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle39.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle39.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle39.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle39.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle39.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle39.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle39.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridInstallationDays.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle39
        Me.GridInstallationDays.ColumnHeadersHeight = 22
        Me.GridInstallationDays.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridInstallationDays.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.InstallationDayNameColumn})
        Me.GridInstallationDays.EnableHeadersVisualStyles = False
        Me.GridInstallationDays.GridColor = System.Drawing.Color.White
        Me.GridInstallationDays.Location = New System.Drawing.Point(3, 29)
        Me.GridInstallationDays.Margin = New System.Windows.Forms.Padding(4)
        Me.GridInstallationDays.Name = "GridInstallationDays"
        Me.GridInstallationDays.ReadOnly = True
        Me.GridInstallationDays.RowHeadersVisible = False
        Me.GridInstallationDays.RowHeadersWidth = 100
        DataGridViewCellStyle40.ForeColor = System.Drawing.Color.DimGray
        Me.GridInstallationDays.RowsDefaultCellStyle = DataGridViewCellStyle40
        Me.GridInstallationDays.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridInstallationDays.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridInstallationDays.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridInstallationDays.RowTemplate.Height = 19
        Me.GridInstallationDays.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridInstallationDays.ShowCellToolTips = False
        Me.GridInstallationDays.Size = New System.Drawing.Size(543, 288)
        Me.GridInstallationDays.StandardTab = True
        Me.GridInstallationDays.TabIndex = 1
        '
        'InstallationDayNameColumn
        '
        Me.InstallationDayNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.InstallationDayNameColumn.DataPropertyName = "InstallationDayName"
        Me.InstallationDayNameColumn.HeaderText = "Installation Day"
        Me.InstallationDayNameColumn.MinimumWidth = 60
        Me.InstallationDayNameColumn.Name = "InstallationDayNameColumn"
        Me.InstallationDayNameColumn.ReadOnly = True
        '
        'LabelInterctionDateTitle
        '
        Me.LabelInterctionDateTitle.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelInterctionDateTitle.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelInterctionDateTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelInterctionDateTitle.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelInterctionDateTitle.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelInterctionDateTitle.LineVisible = True
        Me.LabelInterctionDateTitle.Location = New System.Drawing.Point(15, 16)
        Me.LabelInterctionDateTitle.Margin = New System.Windows.Forms.Padding(4, 4, 15, 12)
        Me.LabelInterctionDateTitle.Name = "LabelInterctionDateTitle"
        Me.LabelInterctionDateTitle.Size = New System.Drawing.Size(676, 24)
        Me.LabelInterctionDateTitle.TabIndex = 0
        Me.LabelInterctionDateTitle.Text = "Interaction Dates"
        '
        'LabelWarningMessage
        '
        Me.LabelWarningMessage.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelWarningMessage.Appearance.ForeColor = System.Drawing.Color.Red
        Me.LabelWarningMessage.Location = New System.Drawing.Point(15, 676)
        Me.LabelWarningMessage.Margin = New System.Windows.Forms.Padding(4, 4, 4, 13)
        Me.LabelWarningMessage.Name = "LabelWarningMessage"
        Me.LabelWarningMessage.Size = New System.Drawing.Size(126, 17)
        Me.LabelWarningMessage.TabIndex = 13
        Me.LabelWarningMessage.Text = "USER WARNING"
        Me.LabelWarningMessage.Visible = False
        '
        'SubformBurst
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(9.0!, 17.0!)
        Me.Controls.Add(Me.TabControl)
        Me.Controls.Add(Me.ButtonOK)
        Me.Controls.Add(Me.ButtonCancel)
        Me.Controls.Add(Me.LabelWarningMessage)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(5)
        Me.Name = "SubformBurst"
        Me.Padding = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.Tag = ""
        Me.Controls.SetChildIndex(Me.LabelWarningMessage, 0)
        Me.Controls.SetChildIndex(Me.ButtonCancel, 0)
        Me.Controls.SetChildIndex(Me.ButtonOK, 0)
        Me.Controls.SetChildIndex(Me.TabControl, 0)
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        CType(Me.TabControl, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabControl.ResumeLayout(False)
        Me.TabPageSummary.ResumeLayout(False)
        Me.PanelSummary.ResumeLayout(False)
        Me.TableLayoutPanelSummary.ResumeLayout(False)
        Me.PanelExecution.ResumeLayout(False)
        Me.PanelExecution.PerformLayout()
        CType(Me.TextEditInstallStoreQty.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditInstallWeeks.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelRental.ResumeLayout(False)
        Me.PanelRental.PerformLayout()
        CType(Me.PictureApplyDatesOnAllBursts.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditFreeWeeks.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditDiscount.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditBillableWeeks.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditFreeStoreQty.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditBillableStoreQty.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditRentalAmount.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditNetRental.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditDiscountAmount.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditRentalRate.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditLoadingFees.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabPageLoadingFees.ResumeLayout(False)
        Me.PanelLoadingFees.ResumeLayout(False)
        Me.TableLayoutPanelLoadingFees.ResumeLayout(False)
        Me.PanelLoadingFeesGrid.ResumeLayout(False)
        Me.PanelLoadingFeesGrid.PerformLayout()
        CType(Me.GroupControlLoadingFees, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlLoadingFees.ResumeLayout(False)
        CType(Me.GridLoadingFees, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabPageCategories.ResumeLayout(False)
        Me.PanelCategories.ResumeLayout(False)
        Me.TableLayoutPanelCategories.ResumeLayout(False)
        Me.PanelCategoriesRight.ResumeLayout(False)
        Me.PanelCategoriesRight.PerformLayout()
        CType(Me.TextEditCrossoverQty.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupCrossoverPreferences, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupCrossoverPreferences.ResumeLayout(False)
        CType(Me.GridCrossoverPreferences, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelCategoriesLeft.ResumeLayout(False)
        Me.PanelCategoriesLeft.PerformLayout()
        CType(Me.GroupAdditionalCategories, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupAdditionalCategories.ResumeLayout(False)
        CType(Me.GridAdditionalCategories, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabPageStoreList.ResumeLayout(False)
        Me.PanelStoreList.ResumeLayout(False)
        Me.PanelStoreListOptions.ResumeLayout(False)
        Me.PanelStoreListOptions.PerformLayout()
        CType(Me.CheckEditConfirmed.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelStoreSharing.ResumeLayout(False)
        Me.PanelStoreSharing.PerformLayout()
        CType(Me.GroupRelatedBursts, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupRelatedBursts.ResumeLayout(False)
        CType(Me.GridRelatedBursts, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditStorePoolCapacity.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabPageInstallationInstructions.ResumeLayout(False)
        Me.PanelInstallationInstructions.ResumeLayout(False)
        Me.PanelInstallationInstructions.PerformLayout()
        CType(Me.PictureApplyInstuctionsOnAllBursts.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditDoNotInstallRegardless.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.CheckEditInstallRegardless.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditAdsPerShelfTalk.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditAdsPerCrossover.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelHomesiteInstallationOptions.ResumeLayout(False)
        Me.PanelHomesiteInstallationOptions.PerformLayout()
        CType(Me.PictureInstallAtHomesite.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureDontInstallAtHomesite.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditAdsPerHomesite.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditProductName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupInstallationInstructions, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupInstallationInstructions.ResumeLayout(False)
        CType(Me.MemoEditInstallationInstructions.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabPageInteractionDates.ResumeLayout(False)
        Me.Panel1.ResumeLayout(False)
        CType(Me.GroupControlInstallationDays, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlInstallationDays.ResumeLayout(False)
        CType(Me.GridInstallationDays, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents ButtonOK As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ImageList16x16 As System.Windows.Forms.ImageList
    Friend WithEvents TabControl As DevExpress.XtraTab.XtraTabControl
    Friend WithEvents TabPageSummary As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents PanelSummary As System.Windows.Forms.Panel
    Friend WithEvents TableLayoutPanelSummary As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents PanelExecution As System.Windows.Forms.Panel
    Friend WithEvents LabelExecutionDetailsHeading As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelChain As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelMediaService As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelBrand As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkMediaService As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkChain As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkBrand As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelFirstWeek As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkFirstWeek As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelStoresThatAllowMedia As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelStoresThatAllowMediaValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelTotalUniverseValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelTotalUniverse As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelTotalStores As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelTotalWeeks As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelLastWeek As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditInstallStoreQty As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEditInstallWeeks As DevExpress.XtraEditors.TextEdit
    Friend WithEvents HyperlinkLastWeek As DevExpress.XtraEditors.LabelControl
    Friend WithEvents PanelRental As System.Windows.Forms.Panel
    Friend WithEvents LabelRentalHeading As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditFreeWeeks As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelLoadingFees As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditDiscount As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelDiscount As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditBillableWeeks As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelRentalAmount As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditFreeStoreQty As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelDiscountAmount As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditBillableStoreQty As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelRentalRate As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditRentalAmount As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelNetRental As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelBillableWeeks As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditNetRental As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelFreeWeeks As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditDiscountAmount As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEditRentalRate As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEditLoadingFees As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelBillableStoreQty As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelFreeStoreQty As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TabPageLoadingFees As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents PanelLoadingFees As System.Windows.Forms.Panel
    Friend WithEvents TableLayoutPanelLoadingFees As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents LabelLoadingFeesInfo As DevExpress.XtraEditors.LabelControl
    Friend WithEvents PanelLoadingFeesGrid As System.Windows.Forms.Panel
    Friend WithEvents GroupControlLoadingFees As DevExpress.XtraEditors.GroupControl
    Friend WithEvents ButtonRemoveLoadingFee As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonAddLoadingFee As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GridLoadingFees As System.Windows.Forms.DataGridView
    Friend WithEvents LoadingFeeNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents LoadingFeePercentageColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents LoadingFeeAmountColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents LabelLoadingFeeTotalValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelLoadingFeeTotal As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TabPageCategories As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents PanelCategories As System.Windows.Forms.Panel
    Friend WithEvents TableLayoutPanelCategories As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents PanelCategoriesRight As System.Windows.Forms.Panel
    Friend WithEvents LabelCrossoversHeading As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditCrossoverQty As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelCrossoverQty As DevExpress.XtraEditors.LabelControl
    Friend WithEvents GroupCrossoverPreferences As DevExpress.XtraEditors.GroupControl
    Friend WithEvents ButtonIncreasePriority As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonDecreasePriority As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonRemoveCrossoverPreference As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonAddCrossoverPreference As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GridCrossoverPreferences As System.Windows.Forms.DataGridView
    Friend WithEvents CrossoverCategoryNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents CrossoverPriorityColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents PanelCategoriesLeft As System.Windows.Forms.Panel
    Friend WithEvents LabelHomesiteHeading As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelHomesite As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelAdditionalCategoriesInfo As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelAdditionalCategoriesHeading As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkHomesite As DevExpress.XtraEditors.LabelControl
    Friend WithEvents GroupAdditionalCategories As DevExpress.XtraEditors.GroupControl
    Friend WithEvents ButtonRemoveAdditionalCategory As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonAddAdditionalCategory As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GridAdditionalCategories As System.Windows.Forms.DataGridView
    Friend WithEvents AdditionalCategoryNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents TabPageStoreList As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents PanelStoreList As System.Windows.Forms.Panel
    Friend WithEvents PanelStoreListOptions As System.Windows.Forms.Panel
    Friend WithEvents LabelStoreListSize As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelStoreListSizeValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelEditStoreList As DevExpress.XtraEditors.LabelControl
    Friend WithEvents CheckEditConfirmed As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents ButtonEditStoreList As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelStoreListTitle As DevExpress.XtraEditors.LabelControl
    Friend WithEvents PanelStoreSharing As System.Windows.Forms.Panel
    Friend WithEvents GroupRelatedBursts As DevExpress.XtraEditors.GroupControl
    Friend WithEvents ButtonAddBurst As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GridRelatedBursts As System.Windows.Forms.DataGridView
    Friend WithEvents RelativeContractNumberColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents RelativeBrandNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents RelativeFirstWeekColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents RelativeLastWeekColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents RelativeInstallStoreQty As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents LabelMax As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelMin As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditStorePoolCapacity As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelStorePoolQty As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelStoreListOptionsTitle As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TabPageInstallationInstructions As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents PanelInstallationInstructions As System.Windows.Forms.Panel
    Friend WithEvents LabelProductNameTitle As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelProductName As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditProductName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents HyperlinkDontInstallAtHomesite As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkInstallAtHomesite As DevExpress.XtraEditors.LabelControl
    Friend WithEvents PictureDontInstallAtHomesite As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureInstallAtHomesite As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents GroupInstallationInstructions As DevExpress.XtraEditors.GroupControl
    Friend WithEvents MemoEditInstallationInstructions As DevExpress.XtraEditors.MemoEdit
    Friend WithEvents LabelHomesiteInstallationTitle As DevExpress.XtraEditors.LabelControl
    Friend WithEvents PanelHomesiteInstallationOptions As System.Windows.Forms.Panel
    Friend WithEvents LabelWarningMessage As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelAdsPerInstallation As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditAdsPerHomesite As DevExpress.XtraEditors.TextEdit
    Friend WithEvents ButtonRemoveBurst As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelControl1 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl2 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditAdsPerCrossover As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEditAdsPerShelfTalk As TextEdit
    Friend WithEvents LabelAdsPerInstallationShelfTalk As LabelControl
    Friend WithEvents CheckEditDoNotInstallRegardless As CheckEdit
    Friend WithEvents CheckEditInstallRegardless As CheckEdit
    Friend WithEvents PictureApplyInstuctionsOnAllBursts As PictureEdit
    Friend WithEvents HyperlinkApplyInstuctionsOnAllBursts As LabelControl
    Friend WithEvents HyperlinkApplyDatesAccrossBursts As LabelControl
    Friend WithEvents PictureApplyDatesOnAllBursts As PictureEdit
    Friend WithEvents TabPageInteractionDates As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel1 As Panel
    Friend WithEvents LabelInterctionDateTitle As LabelControl
    Friend WithEvents GroupControlInstallationDays As GroupControl
    Friend WithEvents ButtonDeleteInstallationDay As SimpleButton
    Friend WithEvents ButtonAddInstallationDay As SimpleButton
    Friend WithEvents GridInstallationDays As DataGridView
    Friend WithEvents InstallationDayNameColumn As DataGridViewTextBoxColumn
    Friend WithEvents LabelPCAStatus As LabelControl
    Friend WithEvents HyperlinkPcaStatus As LabelControl
End Class
