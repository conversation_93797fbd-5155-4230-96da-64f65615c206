﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using Framework.Controls;
using Framework.Surfaces;

namespace Framework.Breadcrumbs
{
    public partial class BreadcrumbContainerSurface : Surface
    {
        private int BREADCRUMBBUTTONWIDTH = FrameworkSettings.Integers.BREADCRUMBBUTTONWIDTH;
        private BreadcrumbResizer Resizer;


        #region Startup

        public BreadcrumbContainerSurface()
        {
            InitializeComponent();
            Resizer = new BreadcrumbResizer();
        }

        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);
            SubscribeToEvents();
        }

        private void SubscribeToEvents()
        {
            Resize += BreadcrumbSurface_Resize;
            panelBreadcrumbButtons.ControlAdded += panelBreadcrumbButtons_ControlAdded;
        }

        #endregion


        #region Breadcrumb button appearance

        private void BreadcrumbSurface_Resize(object sender, EventArgs e)
        {
            ResizeBreadcrumbButtons();
        }

        private void panelBreadcrumbButtons_ControlAdded(object sender, ControlEventArgs e)
        {
            SetBreadcrumbButtonLocation(e.Control);
            ResizeBreadcrumbButtons();
        }

        private void SetBreadcrumbButtonLocation(Control control)
        {
            // Get the total width of all controls in the breadcrumbs panel.
            int totalwidthofcontrols = 0;
            for (int i = 0; i < panelBreadcrumbButtons.Controls.Count; i++)
            {
                totalwidthofcontrols += panelBreadcrumbButtons.Controls[i].Width;
            }

            int x = totalwidthofcontrols - control.Width;
            int y = 0;
            control.Location = new Point(x, y);
        }

        private void ResizeBreadcrumbButtons()
        {
            Resizer.Resize(panelBreadcrumbButtons, BREADCRUMBBUTTONWIDTH);
        }

        #endregion


        #region Breadcrumb navigation

        public void DisplayNextSurface(Surface newsurfacetodisplay)
        {
            // Make a new breadcrumb button for the existing surface so the user can go back to it later.
            for (int i = 0; i < panelContent.Controls.Count; i++)
            {
                if (panelContent.Controls[i] is Surface)
                {
                    Surface currentlydisplayedsurface = (Surface)panelContent.Controls[i];
                    FlatButton newcrumb = new FlatButton();
                    newcrumb.Text = "« Back to " + currentlydisplayedsurface.BreadcrumbName;
                    newcrumb.Tag = currentlydisplayedsurface;
                    newcrumb.Click += Newcrumb_Click;
                    panelBreadcrumbButtons.Controls.Add(newcrumb);
                    panelContent.Controls.Clear();
                }
            }

            // Add the new surface to the display area.
            newsurfacetodisplay.Dock = DockStyle.Fill;
            labelBreadcrumbTitleText.Text = newsurfacetodisplay.BreadcrumbTitleText;
            newsurfacetodisplay.BreadcrumbContainer = this;
            panelContent.Controls.Add(newsurfacetodisplay);
        }

        private void Newcrumb_Click(object sender, EventArgs e)
        {
            // Remove the currently displayed surface and display the surface linked to
            // the Tag property of the button that was clicked.
            panelContent.Controls.Clear();
            FlatButton clickedbutton = (FlatButton)sender;
            Surface crumbbuttoncontentsurface = (Surface)clickedbutton.Tag;
            panelContent.Controls.Add(crumbbuttoncontentsurface);
            labelBreadcrumbTitleText.Text = crumbbuttoncontentsurface.BreadcrumbTitleText;
            
            // Remove the clicked button and all buttons that appear after it.
            List<Control> buttonstoremove = new List<Control>();
            int clickedbuttonindex = panelBreadcrumbButtons.Controls.IndexOf(clickedbutton);
            for (int i = 0; i < panelBreadcrumbButtons.Controls.Count; i++)
            {
                if (i >= clickedbuttonindex)
                {
                    buttonstoremove.Add(panelBreadcrumbButtons.Controls[i]);
                }
            }
            foreach (Control buttontoremove in buttonstoremove)
            {
                panelBreadcrumbButtons.Controls.Remove(buttontoremove);
                buttontoremove.Dispose();
            }
        }

        #endregion
        
    }
}
