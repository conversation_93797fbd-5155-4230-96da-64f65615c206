<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DataSetAudit" targetNamespace="http://tempuri.org/DataSetAudit.xsd" xmlns:mstns="http://tempuri.org/DataSetAudit.xsd" xmlns="http://tempuri.org/DataSetAudit.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="DBConnection" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Public" Name="DBConnection (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.NovaData.My.MySettings.GlobalReference.Default.DBConnection" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="AuditLogTableAdapter" GeneratorDataComponentClassName="AuditLogTableAdapter" Name="AuditLog" UserDataComponentName="AuditLogTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.dbo.AuditLog" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [AuditLog] WHERE (([EntryID] = @Original_EntryID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_EntryID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="EntryID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [AuditLog] ([EntryID], [ObjectType], [ObjectName], [Action]) VALUES (@EntryID, @ObjectType, @ObjectName, @Action)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@EntryID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="EntryID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@ObjectType" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ObjectType" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@ObjectName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ObjectName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Action" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Action" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT        EntryID, ObjectType, ObjectName, Action, ActionDate, [User]
FROM            AuditLog</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [AuditLog] SET [EntryID] = @EntryID, [ObjectType] = @ObjectType, [ObjectName] = @ObjectName, [Action] = @Action WHERE (([EntryID] = @Original_EntryID))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@EntryID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="EntryID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@ObjectType" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ObjectType" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@ObjectName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="ObjectName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Action" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Action" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_EntryID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="EntryID" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="EntryID" DataSetColumn="EntryID" />
              <Mapping SourceColumn="ObjectType" DataSetColumn="ObjectType" />
              <Mapping SourceColumn="ObjectName" DataSetColumn="ObjectName" />
              <Mapping SourceColumn="Action" DataSetColumn="Action" />
              <Mapping SourceColumn="ActionDate" DataSetColumn="ActionDate" />
              <Mapping SourceColumn="User" DataSetColumn="User" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.dbo.AuditLog" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="FillByObjectName" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetDataByObjectName" GeneratorSourceName="FillByObjectName" GetMethodModifier="Public" GetMethodName="GetDataByObjectName" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataByObjectName" UserSourceName="FillByObjectName">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT        EntryID, ObjectType, ObjectName, Action, ActionDate, [User]
FROM            AuditLog
WHERE        (ObjectName = @ObjectName)
ORDER BY ActionDate DESC</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ObjectName" ColumnName="ObjectName" DataSourceName="NovaDB.dbo.AuditLog" DataTypeServer="nvarchar(250)" DbType="String" Direction="Input" ParameterName="@ObjectName" Precision="0" ProviderType="NVarChar" Scale="0" Size="250" SourceColumn="ObjectName" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DataSetAudit" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_DataSetName="DataSetAudit" msprop:Generator_UserDSName="DataSetAudit">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="AuditLog" msprop:Generator_TableClassName="AuditLogDataTable" msprop:Generator_TableVarName="tableAuditLog" msprop:Generator_RowChangedName="AuditLogRowChanged" msprop:Generator_TablePropName="AuditLog" msprop:Generator_RowDeletingName="AuditLogRowDeleting" msprop:Generator_RowChangingName="AuditLogRowChanging" msprop:Generator_RowEvHandlerName="AuditLogRowChangeEventHandler" msprop:Generator_RowDeletedName="AuditLogRowDeleted" msprop:Generator_RowClassName="AuditLogRow" msprop:Generator_UserTableName="AuditLog" msprop:Generator_RowEvArgName="AuditLogRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="EntryID" msdata:DataType="System.Guid, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnEntryID" msprop:Generator_ColumnPropNameInRow="EntryID" msprop:Generator_ColumnPropNameInTable="EntryIDColumn" msprop:Generator_UserColumnName="EntryID" type="xs:string" />
              <xs:element name="ObjectType" msprop:Generator_ColumnVarNameInTable="columnObjectType" msprop:Generator_ColumnPropNameInRow="ObjectType" msprop:Generator_ColumnPropNameInTable="ObjectTypeColumn" msprop:Generator_UserColumnName="ObjectType">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ObjectName" msprop:Generator_ColumnVarNameInTable="columnObjectName" msprop:Generator_ColumnPropNameInRow="ObjectName" msprop:Generator_ColumnPropNameInTable="ObjectNameColumn" msprop:Generator_UserColumnName="ObjectName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="500" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Action" msprop:Generator_ColumnVarNameInTable="columnAction" msprop:Generator_ColumnPropNameInRow="Action" msprop:Generator_ColumnPropNameInTable="ActionColumn" msprop:Generator_UserColumnName="Action">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="2500" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ActionDate" msprop:Generator_ColumnVarNameInTable="columnActionDate" msprop:Generator_ColumnPropNameInRow="ActionDate" msprop:Generator_ColumnPropNameInTable="ActionDateColumn" msprop:Generator_UserColumnName="ActionDate" type="xs:dateTime" minOccurs="0" />
              <xs:element name="User" msprop:Generator_ColumnVarNameInTable="columnUser" msprop:Generator_ColumnPropNameInRow="User" msprop:Generator_ColumnPropNameInTable="UserColumn" msprop:Generator_UserColumnName="User" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:AuditLog" />
      <xs:field xpath="mstns:EntryID" />
    </xs:unique>
  </xs:element>
</xs:schema>