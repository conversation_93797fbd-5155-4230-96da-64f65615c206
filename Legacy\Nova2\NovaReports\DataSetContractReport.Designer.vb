﻿'------------------------------------------------------------------------------
' <auto-generated>
'     This code was generated by a tool.
'     Runtime Version:4.0.30319.42000
'
'     Changes to this file may cause incorrect behavior and will be lost if
'     the code is regenerated.
' </auto-generated>
'------------------------------------------------------------------------------

Option Strict Off
Option Explicit On



'''<summary>
'''Represents a strongly typed in-memory cache of data.
'''</summary>
<Global.System.Serializable(),  _
 Global.System.ComponentModel.DesignerCategoryAttribute("code"),  _
 Global.System.ComponentModel.ToolboxItem(true),  _
 Global.System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedDataSetSchema"),  _
 Global.System.Xml.Serialization.XmlRootAttribute("DataSetContractReport"),  _
 Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.DataSet")>  _
Partial Public Class DataSetContractReport
    Inherits Global.System.Data.DataSet
    
    Private tableContract As ContractDataTable
    
    Private tableBillingInstruction As BillingInstructionDataTable
    
    Private tableBurst As BurstDataTable
    
    Private tableContractInventory As ContractInventoryDataTable
    
    Private tableMiscellaneousCharge As MiscellaneousChargeDataTable
    
    Private tableResearchContract As ResearchContractDataTable
    
    Private tableResearchCategory As ResearchCategoryDataTable
    
    Private relationContract_PurchaseOrderNumber As Global.System.Data.DataRelation
    
    Private relationContract_Burst As Global.System.Data.DataRelation
    
    Private relationContract_ContractInventory As Global.System.Data.DataRelation
    
    Private relationContract_MiscellaneousCharge As Global.System.Data.DataRelation
    
    Private relationResearchContract_PurchaseOrderNumber As Global.System.Data.DataRelation
    
    Private relationResearchContract_MiscellaneousCharge As Global.System.Data.DataRelation
    
    Private relationResearchContract_ResearchCategory As Global.System.Data.DataRelation
    
    Private _schemaSerializationMode As Global.System.Data.SchemaSerializationMode = Global.System.Data.SchemaSerializationMode.IncludeSchema
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Sub New()
        MyBase.New
        Me.BeginInit
        Me.InitClass
        Dim schemaChangedHandler As Global.System.ComponentModel.CollectionChangeEventHandler = AddressOf Me.SchemaChanged
        AddHandler MyBase.Tables.CollectionChanged, schemaChangedHandler
        AddHandler MyBase.Relations.CollectionChanged, schemaChangedHandler
        Me.EndInit
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Protected Sub New(ByVal info As Global.System.Runtime.Serialization.SerializationInfo, ByVal context As Global.System.Runtime.Serialization.StreamingContext)
        MyBase.New(info, context, false)
        If (Me.IsBinarySerialized(info, context) = true) Then
            Me.InitVars(false)
            Dim schemaChangedHandler1 As Global.System.ComponentModel.CollectionChangeEventHandler = AddressOf Me.SchemaChanged
            AddHandler Me.Tables.CollectionChanged, schemaChangedHandler1
            AddHandler Me.Relations.CollectionChanged, schemaChangedHandler1
            Return
        End If
        Dim strSchema As String = CType(info.GetValue("XmlSchema", GetType(String)),String)
        If (Me.DetermineSchemaSerializationMode(info, context) = Global.System.Data.SchemaSerializationMode.IncludeSchema) Then
            Dim ds As Global.System.Data.DataSet = New Global.System.Data.DataSet()
            ds.ReadXmlSchema(New Global.System.Xml.XmlTextReader(New Global.System.IO.StringReader(strSchema)))
            If (Not (ds.Tables("Contract")) Is Nothing) Then
                MyBase.Tables.Add(New ContractDataTable(ds.Tables("Contract")))
            End If
            If (Not (ds.Tables("BillingInstruction")) Is Nothing) Then
                MyBase.Tables.Add(New BillingInstructionDataTable(ds.Tables("BillingInstruction")))
            End If
            If (Not (ds.Tables("Burst")) Is Nothing) Then
                MyBase.Tables.Add(New BurstDataTable(ds.Tables("Burst")))
            End If
            If (Not (ds.Tables("ContractInventory")) Is Nothing) Then
                MyBase.Tables.Add(New ContractInventoryDataTable(ds.Tables("ContractInventory")))
            End If
            If (Not (ds.Tables("MiscellaneousCharge")) Is Nothing) Then
                MyBase.Tables.Add(New MiscellaneousChargeDataTable(ds.Tables("MiscellaneousCharge")))
            End If
            If (Not (ds.Tables("ResearchContract")) Is Nothing) Then
                MyBase.Tables.Add(New ResearchContractDataTable(ds.Tables("ResearchContract")))
            End If
            If (Not (ds.Tables("ResearchCategory")) Is Nothing) Then
                MyBase.Tables.Add(New ResearchCategoryDataTable(ds.Tables("ResearchCategory")))
            End If
            Me.DataSetName = ds.DataSetName
            Me.Prefix = ds.Prefix
            Me.Namespace = ds.Namespace
            Me.Locale = ds.Locale
            Me.CaseSensitive = ds.CaseSensitive
            Me.EnforceConstraints = ds.EnforceConstraints
            Me.Merge(ds, false, Global.System.Data.MissingSchemaAction.Add)
            Me.InitVars
        Else
            Me.ReadXmlSchema(New Global.System.Xml.XmlTextReader(New Global.System.IO.StringReader(strSchema)))
        End If
        Me.GetSerializationData(info, context)
        Dim schemaChangedHandler As Global.System.ComponentModel.CollectionChangeEventHandler = AddressOf Me.SchemaChanged
        AddHandler MyBase.Tables.CollectionChanged, schemaChangedHandler
        AddHandler Me.Relations.CollectionChanged, schemaChangedHandler
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
     Global.System.ComponentModel.Browsable(false),  _
     Global.System.ComponentModel.DesignerSerializationVisibility(Global.System.ComponentModel.DesignerSerializationVisibility.Content)>  _
    Public ReadOnly Property Contract() As ContractDataTable
        Get
            Return Me.tableContract
        End Get
    End Property
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
     Global.System.ComponentModel.Browsable(false),  _
     Global.System.ComponentModel.DesignerSerializationVisibility(Global.System.ComponentModel.DesignerSerializationVisibility.Content)>  _
    Public ReadOnly Property BillingInstruction() As BillingInstructionDataTable
        Get
            Return Me.tableBillingInstruction
        End Get
    End Property
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
     Global.System.ComponentModel.Browsable(false),  _
     Global.System.ComponentModel.DesignerSerializationVisibility(Global.System.ComponentModel.DesignerSerializationVisibility.Content)>  _
    Public ReadOnly Property Burst() As BurstDataTable
        Get
            Return Me.tableBurst
        End Get
    End Property
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
     Global.System.ComponentModel.Browsable(false),  _
     Global.System.ComponentModel.DesignerSerializationVisibility(Global.System.ComponentModel.DesignerSerializationVisibility.Content)>  _
    Public ReadOnly Property ContractInventory() As ContractInventoryDataTable
        Get
            Return Me.tableContractInventory
        End Get
    End Property
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
     Global.System.ComponentModel.Browsable(false),  _
     Global.System.ComponentModel.DesignerSerializationVisibility(Global.System.ComponentModel.DesignerSerializationVisibility.Content)>  _
    Public ReadOnly Property MiscellaneousCharge() As MiscellaneousChargeDataTable
        Get
            Return Me.tableMiscellaneousCharge
        End Get
    End Property
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
     Global.System.ComponentModel.Browsable(false),  _
     Global.System.ComponentModel.DesignerSerializationVisibility(Global.System.ComponentModel.DesignerSerializationVisibility.Content)>  _
    Public ReadOnly Property ResearchContract() As ResearchContractDataTable
        Get
            Return Me.tableResearchContract
        End Get
    End Property
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
     Global.System.ComponentModel.Browsable(false),  _
     Global.System.ComponentModel.DesignerSerializationVisibility(Global.System.ComponentModel.DesignerSerializationVisibility.Content)>  _
    Public ReadOnly Property ResearchCategory() As ResearchCategoryDataTable
        Get
            Return Me.tableResearchCategory
        End Get
    End Property
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
     Global.System.ComponentModel.BrowsableAttribute(true),  _
     Global.System.ComponentModel.DesignerSerializationVisibilityAttribute(Global.System.ComponentModel.DesignerSerializationVisibility.Visible)>  _
    Public Overrides Property SchemaSerializationMode() As Global.System.Data.SchemaSerializationMode
        Get
            Return Me._schemaSerializationMode
        End Get
        Set
            Me._schemaSerializationMode = value
        End Set
    End Property
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
     Global.System.ComponentModel.DesignerSerializationVisibilityAttribute(Global.System.ComponentModel.DesignerSerializationVisibility.Hidden)>  _
    Public Shadows ReadOnly Property Tables() As Global.System.Data.DataTableCollection
        Get
            Return MyBase.Tables
        End Get
    End Property
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
     Global.System.ComponentModel.DesignerSerializationVisibilityAttribute(Global.System.ComponentModel.DesignerSerializationVisibility.Hidden)>  _
    Public Shadows ReadOnly Property Relations() As Global.System.Data.DataRelationCollection
        Get
            Return MyBase.Relations
        End Get
    End Property
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Protected Overrides Sub InitializeDerivedDataSet()
        Me.BeginInit
        Me.InitClass
        Me.EndInit
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Overrides Function Clone() As Global.System.Data.DataSet
        Dim cln As DataSetContractReport = CType(MyBase.Clone,DataSetContractReport)
        cln.InitVars
        cln.SchemaSerializationMode = Me.SchemaSerializationMode
        Return cln
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Protected Overrides Function ShouldSerializeTables() As Boolean
        Return false
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Protected Overrides Function ShouldSerializeRelations() As Boolean
        Return false
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Protected Overrides Sub ReadXmlSerializable(ByVal reader As Global.System.Xml.XmlReader)
        If (Me.DetermineSchemaSerializationMode(reader) = Global.System.Data.SchemaSerializationMode.IncludeSchema) Then
            Me.Reset
            Dim ds As Global.System.Data.DataSet = New Global.System.Data.DataSet()
            ds.ReadXml(reader)
            If (Not (ds.Tables("Contract")) Is Nothing) Then
                MyBase.Tables.Add(New ContractDataTable(ds.Tables("Contract")))
            End If
            If (Not (ds.Tables("BillingInstruction")) Is Nothing) Then
                MyBase.Tables.Add(New BillingInstructionDataTable(ds.Tables("BillingInstruction")))
            End If
            If (Not (ds.Tables("Burst")) Is Nothing) Then
                MyBase.Tables.Add(New BurstDataTable(ds.Tables("Burst")))
            End If
            If (Not (ds.Tables("ContractInventory")) Is Nothing) Then
                MyBase.Tables.Add(New ContractInventoryDataTable(ds.Tables("ContractInventory")))
            End If
            If (Not (ds.Tables("MiscellaneousCharge")) Is Nothing) Then
                MyBase.Tables.Add(New MiscellaneousChargeDataTable(ds.Tables("MiscellaneousCharge")))
            End If
            If (Not (ds.Tables("ResearchContract")) Is Nothing) Then
                MyBase.Tables.Add(New ResearchContractDataTable(ds.Tables("ResearchContract")))
            End If
            If (Not (ds.Tables("ResearchCategory")) Is Nothing) Then
                MyBase.Tables.Add(New ResearchCategoryDataTable(ds.Tables("ResearchCategory")))
            End If
            Me.DataSetName = ds.DataSetName
            Me.Prefix = ds.Prefix
            Me.Namespace = ds.Namespace
            Me.Locale = ds.Locale
            Me.CaseSensitive = ds.CaseSensitive
            Me.EnforceConstraints = ds.EnforceConstraints
            Me.Merge(ds, false, Global.System.Data.MissingSchemaAction.Add)
            Me.InitVars
        Else
            Me.ReadXml(reader)
            Me.InitVars
        End If
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Protected Overrides Function GetSchemaSerializable() As Global.System.Xml.Schema.XmlSchema
        Dim stream As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
        Me.WriteXmlSchema(New Global.System.Xml.XmlTextWriter(stream, Nothing))
        stream.Position = 0
        Return Global.System.Xml.Schema.XmlSchema.Read(New Global.System.Xml.XmlTextReader(stream), Nothing)
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Friend Overloads Sub InitVars()
        Me.InitVars(true)
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Friend Overloads Sub InitVars(ByVal initTable As Boolean)
        Me.tableContract = CType(MyBase.Tables("Contract"),ContractDataTable)
        If (initTable = true) Then
            If (Not (Me.tableContract) Is Nothing) Then
                Me.tableContract.InitVars
            End If
        End If
        Me.tableBillingInstruction = CType(MyBase.Tables("BillingInstruction"),BillingInstructionDataTable)
        If (initTable = true) Then
            If (Not (Me.tableBillingInstruction) Is Nothing) Then
                Me.tableBillingInstruction.InitVars
            End If
        End If
        Me.tableBurst = CType(MyBase.Tables("Burst"),BurstDataTable)
        If (initTable = true) Then
            If (Not (Me.tableBurst) Is Nothing) Then
                Me.tableBurst.InitVars
            End If
        End If
        Me.tableContractInventory = CType(MyBase.Tables("ContractInventory"),ContractInventoryDataTable)
        If (initTable = true) Then
            If (Not (Me.tableContractInventory) Is Nothing) Then
                Me.tableContractInventory.InitVars
            End If
        End If
        Me.tableMiscellaneousCharge = CType(MyBase.Tables("MiscellaneousCharge"),MiscellaneousChargeDataTable)
        If (initTable = true) Then
            If (Not (Me.tableMiscellaneousCharge) Is Nothing) Then
                Me.tableMiscellaneousCharge.InitVars
            End If
        End If
        Me.tableResearchContract = CType(MyBase.Tables("ResearchContract"),ResearchContractDataTable)
        If (initTable = true) Then
            If (Not (Me.tableResearchContract) Is Nothing) Then
                Me.tableResearchContract.InitVars
            End If
        End If
        Me.tableResearchCategory = CType(MyBase.Tables("ResearchCategory"),ResearchCategoryDataTable)
        If (initTable = true) Then
            If (Not (Me.tableResearchCategory) Is Nothing) Then
                Me.tableResearchCategory.InitVars
            End If
        End If
        Me.relationContract_PurchaseOrderNumber = Me.Relations("Contract_PurchaseOrderNumber")
        Me.relationContract_Burst = Me.Relations("Contract_Burst")
        Me.relationContract_ContractInventory = Me.Relations("Contract_ContractInventory")
        Me.relationContract_MiscellaneousCharge = Me.Relations("Contract_MiscellaneousCharge")
        Me.relationResearchContract_PurchaseOrderNumber = Me.Relations("ResearchContract_PurchaseOrderNumber")
        Me.relationResearchContract_MiscellaneousCharge = Me.Relations("ResearchContract_MiscellaneousCharge")
        Me.relationResearchContract_ResearchCategory = Me.Relations("ResearchContract_ResearchCategory")
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Private Sub InitClass()
        Me.DataSetName = "DataSetContractReport"
        Me.Prefix = ""
        Me.Namespace = "http://tempuri.org/DataSetContractReport.xsd"
        Me.EnforceConstraints = true
        Me.SchemaSerializationMode = Global.System.Data.SchemaSerializationMode.IncludeSchema
        Me.tableContract = New ContractDataTable()
        MyBase.Tables.Add(Me.tableContract)
        Me.tableBillingInstruction = New BillingInstructionDataTable()
        MyBase.Tables.Add(Me.tableBillingInstruction)
        Me.tableBurst = New BurstDataTable()
        MyBase.Tables.Add(Me.tableBurst)
        Me.tableContractInventory = New ContractInventoryDataTable()
        MyBase.Tables.Add(Me.tableContractInventory)
        Me.tableMiscellaneousCharge = New MiscellaneousChargeDataTable()
        MyBase.Tables.Add(Me.tableMiscellaneousCharge)
        Me.tableResearchContract = New ResearchContractDataTable()
        MyBase.Tables.Add(Me.tableResearchContract)
        Me.tableResearchCategory = New ResearchCategoryDataTable()
        MyBase.Tables.Add(Me.tableResearchCategory)
        Me.relationContract_PurchaseOrderNumber = New Global.System.Data.DataRelation("Contract_PurchaseOrderNumber", New Global.System.Data.DataColumn() {Me.tableContract.ContractIDColumn}, New Global.System.Data.DataColumn() {Me.tableBillingInstruction.ContractIDColumn}, false)
        Me.Relations.Add(Me.relationContract_PurchaseOrderNumber)
        Me.relationContract_Burst = New Global.System.Data.DataRelation("Contract_Burst", New Global.System.Data.DataColumn() {Me.tableContract.ContractIDColumn}, New Global.System.Data.DataColumn() {Me.tableBurst.ContractIDColumn}, false)
        Me.Relations.Add(Me.relationContract_Burst)
        Me.relationContract_ContractInventory = New Global.System.Data.DataRelation("Contract_ContractInventory", New Global.System.Data.DataColumn() {Me.tableContract.ContractIDColumn}, New Global.System.Data.DataColumn() {Me.tableContractInventory.ContractIDColumn}, false)
        Me.Relations.Add(Me.relationContract_ContractInventory)
        Me.relationContract_MiscellaneousCharge = New Global.System.Data.DataRelation("Contract_MiscellaneousCharge", New Global.System.Data.DataColumn() {Me.tableContract.ContractIDColumn}, New Global.System.Data.DataColumn() {Me.tableMiscellaneousCharge.ContractIDColumn}, false)
        Me.Relations.Add(Me.relationContract_MiscellaneousCharge)
        Me.relationResearchContract_PurchaseOrderNumber = New Global.System.Data.DataRelation("ResearchContract_PurchaseOrderNumber", New Global.System.Data.DataColumn() {Me.tableResearchContract.ContractIDColumn}, New Global.System.Data.DataColumn() {Me.tableBillingInstruction.ContractIDColumn}, false)
        Me.Relations.Add(Me.relationResearchContract_PurchaseOrderNumber)
        Me.relationResearchContract_MiscellaneousCharge = New Global.System.Data.DataRelation("ResearchContract_MiscellaneousCharge", New Global.System.Data.DataColumn() {Me.tableResearchContract.ContractIDColumn}, New Global.System.Data.DataColumn() {Me.tableMiscellaneousCharge.ContractIDColumn}, false)
        Me.Relations.Add(Me.relationResearchContract_MiscellaneousCharge)
        Me.relationResearchContract_ResearchCategory = New Global.System.Data.DataRelation("ResearchContract_ResearchCategory", New Global.System.Data.DataColumn() {Me.tableResearchContract.ContractIDColumn}, New Global.System.Data.DataColumn() {Me.tableResearchCategory.ContractIDColumn}, false)
        Me.Relations.Add(Me.relationResearchContract_ResearchCategory)
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Private Function ShouldSerializeContract() As Boolean
        Return false
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Private Function ShouldSerializeBillingInstruction() As Boolean
        Return false
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Private Function ShouldSerializeBurst() As Boolean
        Return false
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Private Function ShouldSerializeContractInventory() As Boolean
        Return false
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Private Function ShouldSerializeMiscellaneousCharge() As Boolean
        Return false
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Private Function ShouldSerializeResearchContract() As Boolean
        Return false
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Private Function ShouldSerializeResearchCategory() As Boolean
        Return false
    End Function
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Private Sub SchemaChanged(ByVal sender As Object, ByVal e As Global.System.ComponentModel.CollectionChangeEventArgs)
        If (e.Action = Global.System.ComponentModel.CollectionChangeAction.Remove) Then
            Me.InitVars
        End If
    End Sub
    
    <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
     Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Shared Function GetTypedDataSetSchema(ByVal xs As Global.System.Xml.Schema.XmlSchemaSet) As Global.System.Xml.Schema.XmlSchemaComplexType
        Dim ds As DataSetContractReport = New DataSetContractReport()
        Dim type As Global.System.Xml.Schema.XmlSchemaComplexType = New Global.System.Xml.Schema.XmlSchemaComplexType()
        Dim sequence As Global.System.Xml.Schema.XmlSchemaSequence = New Global.System.Xml.Schema.XmlSchemaSequence()
        Dim any As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
        any.Namespace = ds.Namespace
        sequence.Items.Add(any)
        type.Particle = sequence
        Dim dsSchema As Global.System.Xml.Schema.XmlSchema = ds.GetSchemaSerializable
        If xs.Contains(dsSchema.TargetNamespace) Then
            Dim s1 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
            Dim s2 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
            Try 
                Dim schema As Global.System.Xml.Schema.XmlSchema = Nothing
                dsSchema.Write(s1)
                Dim schemas As Global.System.Collections.IEnumerator = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator
                Do While schemas.MoveNext
                    schema = CType(schemas.Current,Global.System.Xml.Schema.XmlSchema)
                    s2.SetLength(0)
                    schema.Write(s2)
                    If (s1.Length = s2.Length) Then
                        s1.Position = 0
                        s2.Position = 0
                        
                        Do While ((s1.Position <> s1.Length)  _
                                    AndAlso (s1.ReadByte = s2.ReadByte))
                            
                            
                        Loop
                        If (s1.Position = s1.Length) Then
                            Return type
                        End If
                    End If
                    
                Loop
            Finally
                If (Not (s1) Is Nothing) Then
                    s1.Close
                End If
                If (Not (s2) Is Nothing) Then
                    s2.Close
                End If
            End Try
        End If
        xs.Add(dsSchema)
        Return type
    End Function
    
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Delegate Sub ContractRowChangeEventHandler(ByVal sender As Object, ByVal e As ContractRowChangeEvent)
    
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Delegate Sub BillingInstructionRowChangeEventHandler(ByVal sender As Object, ByVal e As BillingInstructionRowChangeEvent)
    
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Delegate Sub BurstRowChangeEventHandler(ByVal sender As Object, ByVal e As BurstRowChangeEvent)
    
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Delegate Sub ContractInventoryRowChangeEventHandler(ByVal sender As Object, ByVal e As ContractInventoryRowChangeEvent)
    
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Delegate Sub MiscellaneousChargeRowChangeEventHandler(ByVal sender As Object, ByVal e As MiscellaneousChargeRowChangeEvent)
    
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Delegate Sub ResearchContractRowChangeEventHandler(ByVal sender As Object, ByVal e As ResearchContractRowChangeEvent)
    
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Delegate Sub ResearchCategoryRowChangeEventHandler(ByVal sender As Object, ByVal e As ResearchCategoryRowChangeEvent)
    
    '''<summary>
    '''Represents the strongly named DataTable class.
    '''</summary>
    <Global.System.Serializable(),  _
     Global.System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")>  _
    Partial Public Class ContractDataTable
        Inherits Global.System.Data.TypedTableBase(Of ContractRow)
        
        Private columnContractID As Global.System.Data.DataColumn
        
        Private columnAccountManagerID As Global.System.Data.DataColumn
        
        Private columnClientID As Global.System.Data.DataColumn
        
        Private columnClientName As Global.System.Data.DataColumn
        
        Private columnClientBillingAddress As Global.System.Data.DataColumn
        
        Private columnContractNumber As Global.System.Data.DataColumn
        
        Private columnSigned As Global.System.Data.DataColumn
        
        Private columnSignDate As Global.System.Data.DataColumn
        
        Private columnSignedBy As Global.System.Data.DataColumn
        
        Private columnSpecialConditions As Global.System.Data.DataColumn
        
        Private columnProjectName As Global.System.Data.DataColumn
        
        Private columnApplyAgencyComm As Global.System.Data.DataColumn
        
        Private columnAgencyID As Global.System.Data.DataColumn
        
        Private columnAgencyName As Global.System.Data.DataColumn
        
        Private columnAgencyCommPercentage As Global.System.Data.DataColumn
        
        Private columnCancelled As Global.System.Data.DataColumn
        
        Private columnCancelDate As Global.System.Data.DataColumn
        
        Private columnCancelledBy As Global.System.Data.DataColumn
        
        Private columnCreatedBy As Global.System.Data.DataColumn
        
        Private columnCreationDate As Global.System.Data.DataColumn
        
        Private columnBillingInstructions As Global.System.Data.DataColumn
        
        Private columnContractNotes As Global.System.Data.DataColumn
        
        Private columnClassificationName As Global.System.Data.DataColumn
        
        Private columnWeeks As Global.System.Data.DataColumn
        
        Private columnFirstWeek As Global.System.Data.DataColumn
        
        Private columnLastWeek As Global.System.Data.DataColumn
        
        Private columnPrintInfo As Global.System.Data.DataColumn
        
        Private columnNetRental As Global.System.Data.DataColumn
        
        Private columnProduction As Global.System.Data.DataColumn
        
        Private columnTerminationDate As Global.System.Data.DataColumn
        
        Private columnDiscountAmount As Global.System.Data.DataColumn
        
        Private columnPrintAgencyComm As Global.System.Data.DataColumn
        
        Private columnAgencyCommAmount As Global.System.Data.DataColumn
        
        Private columnClientAccountManagerCode As Global.System.Data.DataColumn
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New()
            MyBase.New
            Me.TableName = "Contract"
            Me.BeginInit
            Me.InitClass
            Me.EndInit
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub New(ByVal table As Global.System.Data.DataTable)
            MyBase.New
            Me.TableName = table.TableName
            If (table.CaseSensitive <> table.DataSet.CaseSensitive) Then
                Me.CaseSensitive = table.CaseSensitive
            End If
            If (table.Locale.ToString <> table.DataSet.Locale.ToString) Then
                Me.Locale = table.Locale
            End If
            If (table.Namespace <> table.DataSet.Namespace) Then
                Me.Namespace = table.Namespace
            End If
            Me.Prefix = table.Prefix
            Me.MinimumCapacity = table.MinimumCapacity
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Sub New(ByVal info As Global.System.Runtime.Serialization.SerializationInfo, ByVal context As Global.System.Runtime.Serialization.StreamingContext)
            MyBase.New(info, context)
            Me.InitVars
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ContractIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnContractID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property AccountManagerIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnAccountManagerID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ClientIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnClientID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ClientNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnClientName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ClientBillingAddressColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnClientBillingAddress
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ContractNumberColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnContractNumber
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property SignedColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnSigned
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property SignDateColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnSignDate
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property SignedByColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnSignedBy
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property SpecialConditionsColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnSpecialConditions
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ProjectNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnProjectName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ApplyAgencyCommColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnApplyAgencyComm
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property AgencyIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnAgencyID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property AgencyNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnAgencyName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property AgencyCommPercentageColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnAgencyCommPercentage
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property CancelledColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnCancelled
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property CancelDateColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnCancelDate
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property CancelledByColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnCancelledBy
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property CreatedByColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnCreatedBy
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property CreationDateColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnCreationDate
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property BillingInstructionsColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnBillingInstructions
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ContractNotesColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnContractNotes
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ClassificationNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnClassificationName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property WeeksColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnWeeks
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property FirstWeekColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnFirstWeek
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property LastWeekColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnLastWeek
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property PrintInfoColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnPrintInfo
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property NetRentalColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnNetRental
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ProductionColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnProduction
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property TerminationDateColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnTerminationDate
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property DiscountAmountColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnDiscountAmount
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property PrintAgencyCommColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnPrintAgencyComm
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property AgencyCommAmountColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnAgencyCommAmount
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ClientAccountManagerCodeColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnClientAccountManagerCode
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Browsable(false)>  _
        Public ReadOnly Property Count() As Integer
            Get
                Return Me.Rows.Count
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Default ReadOnly Property Item(ByVal index As Integer) As ContractRow
            Get
                Return CType(Me.Rows(index),ContractRow)
            End Get
        End Property
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ContractRowChanging As ContractRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ContractRowChanged As ContractRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ContractRowDeleting As ContractRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ContractRowDeleted As ContractRowChangeEventHandler
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overloads Sub AddContractRow(ByVal row As ContractRow)
            Me.Rows.Add(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overloads Function AddContractRow( _
                    ByVal ContractID As System.Guid,  _
                    ByVal AccountManagerID As Integer,  _
                    ByVal ClientID As Integer,  _
                    ByVal ClientName As String,  _
                    ByVal ClientBillingAddress As String,  _
                    ByVal ContractNumber As String,  _
                    ByVal Signed As Boolean,  _
                    ByVal SignDate As Date,  _
                    ByVal SignedBy As String,  _
                    ByVal SpecialConditions As String,  _
                    ByVal ProjectName As String,  _
                    ByVal ApplyAgencyComm As Boolean,  _
                    ByVal AgencyID As Integer,  _
                    ByVal AgencyName As String,  _
                    ByVal AgencyCommPercentage As Decimal,  _
                    ByVal Cancelled As Boolean,  _
                    ByVal CancelDate As Date,  _
                    ByVal CancelledBy As String,  _
                    ByVal CreatedBy As String,  _
                    ByVal CreationDate As Date,  _
                    ByVal BillingInstructions As String,  _
                    ByVal ContractNotes As String,  _
                    ByVal ClassificationName As String,  _
                    ByVal Weeks As Integer,  _
                    ByVal FirstWeek As Date,  _
                    ByVal LastWeek As Date,  _
                    ByVal PrintInfo As String,  _
                    ByVal NetRental As Decimal,  _
                    ByVal Production As Decimal,  _
                    ByVal TerminationDate As Date,  _
                    ByVal DiscountAmount As Decimal,  _
                    ByVal PrintAgencyComm As Boolean,  _
                    ByVal AgencyCommAmount As Decimal,  _
                    ByVal ClientAccountManagerCode As String) As ContractRow
            Dim rowContractRow As ContractRow = CType(Me.NewRow,ContractRow)
            Dim columnValuesArray() As Object = New Object() {ContractID, AccountManagerID, ClientID, ClientName, ClientBillingAddress, ContractNumber, Signed, SignDate, SignedBy, SpecialConditions, ProjectName, ApplyAgencyComm, AgencyID, AgencyName, AgencyCommPercentage, Cancelled, CancelDate, CancelledBy, CreatedBy, CreationDate, BillingInstructions, ContractNotes, ClassificationName, Weeks, FirstWeek, LastWeek, PrintInfo, NetRental, Production, TerminationDate, DiscountAmount, PrintAgencyComm, AgencyCommAmount, ClientAccountManagerCode}
            rowContractRow.ItemArray = columnValuesArray
            Me.Rows.Add(rowContractRow)
            Return rowContractRow
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function FindByContractID(ByVal ContractID As System.Guid) As ContractRow
            Return CType(Me.Rows.Find(New Object() {ContractID}),ContractRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overrides Function Clone() As Global.System.Data.DataTable
            Dim cln As ContractDataTable = CType(MyBase.Clone,ContractDataTable)
            cln.InitVars
            Return cln
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function CreateInstance() As Global.System.Data.DataTable
            Return New ContractDataTable()
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub InitVars()
            Me.columnContractID = MyBase.Columns("ContractID")
            Me.columnAccountManagerID = MyBase.Columns("AccountManagerID")
            Me.columnClientID = MyBase.Columns("ClientID")
            Me.columnClientName = MyBase.Columns("ClientName")
            Me.columnClientBillingAddress = MyBase.Columns("ClientBillingAddress")
            Me.columnContractNumber = MyBase.Columns("ContractNumber")
            Me.columnSigned = MyBase.Columns("Signed")
            Me.columnSignDate = MyBase.Columns("SignDate")
            Me.columnSignedBy = MyBase.Columns("SignedBy")
            Me.columnSpecialConditions = MyBase.Columns("SpecialConditions")
            Me.columnProjectName = MyBase.Columns("ProjectName")
            Me.columnApplyAgencyComm = MyBase.Columns("ApplyAgencyComm")
            Me.columnAgencyID = MyBase.Columns("AgencyID")
            Me.columnAgencyName = MyBase.Columns("AgencyName")
            Me.columnAgencyCommPercentage = MyBase.Columns("AgencyCommPercentage")
            Me.columnCancelled = MyBase.Columns("Cancelled")
            Me.columnCancelDate = MyBase.Columns("CancelDate")
            Me.columnCancelledBy = MyBase.Columns("CancelledBy")
            Me.columnCreatedBy = MyBase.Columns("CreatedBy")
            Me.columnCreationDate = MyBase.Columns("CreationDate")
            Me.columnBillingInstructions = MyBase.Columns("BillingInstructions")
            Me.columnContractNotes = MyBase.Columns("ContractNotes")
            Me.columnClassificationName = MyBase.Columns("ClassificationName")
            Me.columnWeeks = MyBase.Columns("Weeks")
            Me.columnFirstWeek = MyBase.Columns("FirstWeek")
            Me.columnLastWeek = MyBase.Columns("LastWeek")
            Me.columnPrintInfo = MyBase.Columns("PrintInfo")
            Me.columnNetRental = MyBase.Columns("NetRental")
            Me.columnProduction = MyBase.Columns("Production")
            Me.columnTerminationDate = MyBase.Columns("TerminationDate")
            Me.columnDiscountAmount = MyBase.Columns("DiscountAmount")
            Me.columnPrintAgencyComm = MyBase.Columns("PrintAgencyComm")
            Me.columnAgencyCommAmount = MyBase.Columns("AgencyCommAmount")
            Me.columnClientAccountManagerCode = MyBase.Columns("ClientAccountManagerCode")
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitClass()
            Me.columnContractID = New Global.System.Data.DataColumn("ContractID", GetType(Global.System.Guid), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnContractID)
            Me.columnAccountManagerID = New Global.System.Data.DataColumn("AccountManagerID", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnAccountManagerID)
            Me.columnClientID = New Global.System.Data.DataColumn("ClientID", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnClientID)
            Me.columnClientName = New Global.System.Data.DataColumn("ClientName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnClientName)
            Me.columnClientBillingAddress = New Global.System.Data.DataColumn("ClientBillingAddress", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnClientBillingAddress)
            Me.columnContractNumber = New Global.System.Data.DataColumn("ContractNumber", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnContractNumber)
            Me.columnSigned = New Global.System.Data.DataColumn("Signed", GetType(Boolean), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnSigned)
            Me.columnSignDate = New Global.System.Data.DataColumn("SignDate", GetType(Date), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnSignDate)
            Me.columnSignedBy = New Global.System.Data.DataColumn("SignedBy", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnSignedBy)
            Me.columnSpecialConditions = New Global.System.Data.DataColumn("SpecialConditions", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnSpecialConditions)
            Me.columnProjectName = New Global.System.Data.DataColumn("ProjectName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnProjectName)
            Me.columnApplyAgencyComm = New Global.System.Data.DataColumn("ApplyAgencyComm", GetType(Boolean), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnApplyAgencyComm)
            Me.columnAgencyID = New Global.System.Data.DataColumn("AgencyID", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnAgencyID)
            Me.columnAgencyName = New Global.System.Data.DataColumn("AgencyName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnAgencyName)
            Me.columnAgencyCommPercentage = New Global.System.Data.DataColumn("AgencyCommPercentage", GetType(Decimal), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnAgencyCommPercentage)
            Me.columnCancelled = New Global.System.Data.DataColumn("Cancelled", GetType(Boolean), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnCancelled)
            Me.columnCancelDate = New Global.System.Data.DataColumn("CancelDate", GetType(Date), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnCancelDate)
            Me.columnCancelledBy = New Global.System.Data.DataColumn("CancelledBy", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnCancelledBy)
            Me.columnCreatedBy = New Global.System.Data.DataColumn("CreatedBy", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnCreatedBy)
            Me.columnCreationDate = New Global.System.Data.DataColumn("CreationDate", GetType(Date), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnCreationDate)
            Me.columnBillingInstructions = New Global.System.Data.DataColumn("BillingInstructions", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnBillingInstructions)
            Me.columnContractNotes = New Global.System.Data.DataColumn("ContractNotes", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnContractNotes)
            Me.columnClassificationName = New Global.System.Data.DataColumn("ClassificationName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnClassificationName)
            Me.columnWeeks = New Global.System.Data.DataColumn("Weeks", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnWeeks)
            Me.columnFirstWeek = New Global.System.Data.DataColumn("FirstWeek", GetType(Date), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnFirstWeek)
            Me.columnLastWeek = New Global.System.Data.DataColumn("LastWeek", GetType(Date), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnLastWeek)
            Me.columnPrintInfo = New Global.System.Data.DataColumn("PrintInfo", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnPrintInfo)
            Me.columnNetRental = New Global.System.Data.DataColumn("NetRental", GetType(Decimal), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnNetRental)
            Me.columnProduction = New Global.System.Data.DataColumn("Production", GetType(Decimal), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnProduction)
            Me.columnTerminationDate = New Global.System.Data.DataColumn("TerminationDate", GetType(Date), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnTerminationDate)
            Me.columnDiscountAmount = New Global.System.Data.DataColumn("DiscountAmount", GetType(Decimal), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnDiscountAmount)
            Me.columnPrintAgencyComm = New Global.System.Data.DataColumn("PrintAgencyComm", GetType(Boolean), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnPrintAgencyComm)
            Me.columnAgencyCommAmount = New Global.System.Data.DataColumn("AgencyCommAmount", GetType(Decimal), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnAgencyCommAmount)
            Me.columnClientAccountManagerCode = New Global.System.Data.DataColumn("ClientAccountManagerCode", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnClientAccountManagerCode)
            Me.Constraints.Add(New Global.System.Data.UniqueConstraint("Constraint1", New Global.System.Data.DataColumn() {Me.columnContractID}, true))
            Me.columnContractID.AllowDBNull = false
            Me.columnContractID.Unique = true
            Me.columnAccountManagerID.AllowDBNull = false
            Me.columnClientID.AllowDBNull = false
            Me.columnClientName.AllowDBNull = false
            Me.columnClientName.MaxLength = 250
            Me.columnClientBillingAddress.AllowDBNull = false
            Me.columnClientBillingAddress.MaxLength = 1000
            Me.columnContractNumber.AllowDBNull = false
            Me.columnContractNumber.MaxLength = 8
            Me.columnSigned.AllowDBNull = false
            Me.columnSignedBy.AllowDBNull = false
            Me.columnSignedBy.MaxLength = 50
            Me.columnSpecialConditions.AllowDBNull = false
            Me.columnSpecialConditions.MaxLength = 2000
            Me.columnProjectName.AllowDBNull = false
            Me.columnProjectName.MaxLength = 250
            Me.columnApplyAgencyComm.AllowDBNull = false
            Me.columnAgencyName.AllowDBNull = false
            Me.columnAgencyName.MaxLength = 250
            Me.columnAgencyCommPercentage.AllowDBNull = false
            Me.columnCancelled.AllowDBNull = false
            Me.columnCancelledBy.MaxLength = 50
            Me.columnCreatedBy.AllowDBNull = false
            Me.columnCreatedBy.MaxLength = 50
            Me.columnCreationDate.AllowDBNull = false
            Me.columnBillingInstructions.AllowDBNull = false
            Me.columnBillingInstructions.MaxLength = 2000
            Me.columnContractNotes.AllowDBNull = false
            Me.columnContractNotes.MaxLength = 2000
            Me.columnClassificationName.AllowDBNull = false
            Me.columnClassificationName.MaxLength = 50
            Me.columnWeeks.ReadOnly = true
            Me.columnFirstWeek.ReadOnly = true
            Me.columnLastWeek.ReadOnly = true
            Me.columnPrintInfo.ReadOnly = true
            Me.columnPrintInfo.MaxLength = 222
            Me.columnNetRental.ReadOnly = true
            Me.columnProduction.ReadOnly = true
            Me.columnTerminationDate.ReadOnly = true
            Me.columnDiscountAmount.ReadOnly = true
            Me.columnPrintAgencyComm.AllowDBNull = false
            Me.columnClientAccountManagerCode.ReadOnly = true
            Me.columnClientAccountManagerCode.MaxLength = **********
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function NewContractRow() As ContractRow
            Return CType(Me.NewRow,ContractRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function NewRowFromBuilder(ByVal builder As Global.System.Data.DataRowBuilder) As Global.System.Data.DataRow
            Return New ContractRow(builder)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function GetRowType() As Global.System.Type
            Return GetType(ContractRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowChanged(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanged(e)
            If (Not (Me.ContractRowChangedEvent) Is Nothing) Then
                RaiseEvent ContractRowChanged(Me, New ContractRowChangeEvent(CType(e.Row,ContractRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowChanging(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanging(e)
            If (Not (Me.ContractRowChangingEvent) Is Nothing) Then
                RaiseEvent ContractRowChanging(Me, New ContractRowChangeEvent(CType(e.Row,ContractRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowDeleted(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleted(e)
            If (Not (Me.ContractRowDeletedEvent) Is Nothing) Then
                RaiseEvent ContractRowDeleted(Me, New ContractRowChangeEvent(CType(e.Row,ContractRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowDeleting(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleting(e)
            If (Not (Me.ContractRowDeletingEvent) Is Nothing) Then
                RaiseEvent ContractRowDeleting(Me, New ContractRowChangeEvent(CType(e.Row,ContractRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub RemoveContractRow(ByVal row As ContractRow)
            Me.Rows.Remove(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Shared Function GetTypedTableSchema(ByVal xs As Global.System.Xml.Schema.XmlSchemaSet) As Global.System.Xml.Schema.XmlSchemaComplexType
            Dim type As Global.System.Xml.Schema.XmlSchemaComplexType = New Global.System.Xml.Schema.XmlSchemaComplexType()
            Dim sequence As Global.System.Xml.Schema.XmlSchemaSequence = New Global.System.Xml.Schema.XmlSchemaSequence()
            Dim ds As DataSetContractReport = New DataSetContractReport()
            Dim any1 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any1.Namespace = "http://www.w3.org/2001/XMLSchema"
            any1.MinOccurs = New Decimal(0)
            any1.MaxOccurs = Decimal.MaxValue
            any1.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any1)
            Dim any2 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1"
            any2.MinOccurs = New Decimal(1)
            any2.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any2)
            Dim attribute1 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute1.Name = "namespace"
            attribute1.FixedValue = ds.Namespace
            type.Attributes.Add(attribute1)
            Dim attribute2 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute2.Name = "tableTypeName"
            attribute2.FixedValue = "ContractDataTable"
            type.Attributes.Add(attribute2)
            type.Particle = sequence
            Dim dsSchema As Global.System.Xml.Schema.XmlSchema = ds.GetSchemaSerializable
            If xs.Contains(dsSchema.TargetNamespace) Then
                Dim s1 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Dim s2 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Try 
                    Dim schema As Global.System.Xml.Schema.XmlSchema = Nothing
                    dsSchema.Write(s1)
                    Dim schemas As Global.System.Collections.IEnumerator = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator
                    Do While schemas.MoveNext
                        schema = CType(schemas.Current,Global.System.Xml.Schema.XmlSchema)
                        s2.SetLength(0)
                        schema.Write(s2)
                        If (s1.Length = s2.Length) Then
                            s1.Position = 0
                            s2.Position = 0
                            
                            Do While ((s1.Position <> s1.Length)  _
                                        AndAlso (s1.ReadByte = s2.ReadByte))
                                
                                
                            Loop
                            If (s1.Position = s1.Length) Then
                                Return type
                            End If
                        End If
                        
                    Loop
                Finally
                    If (Not (s1) Is Nothing) Then
                        s1.Close
                    End If
                    If (Not (s2) Is Nothing) Then
                        s2.Close
                    End If
                End Try
            End If
            xs.Add(dsSchema)
            Return type
        End Function
    End Class
    
    '''<summary>
    '''Represents the strongly named DataTable class.
    '''</summary>
    <Global.System.Serializable(),  _
     Global.System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")>  _
    Partial Public Class BillingInstructionDataTable
        Inherits Global.System.Data.TypedTableBase(Of BillingInstructionRow)
        
        Private columnContractID As Global.System.Data.DataColumn
        
        Private columnPeriodName As Global.System.Data.DataColumn
        
        Private columnAmount As Global.System.Data.DataColumn
        
        Private columnPONumber As Global.System.Data.DataColumn
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New()
            MyBase.New
            Me.TableName = "BillingInstruction"
            Me.BeginInit
            Me.InitClass
            Me.EndInit
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub New(ByVal table As Global.System.Data.DataTable)
            MyBase.New
            Me.TableName = table.TableName
            If (table.CaseSensitive <> table.DataSet.CaseSensitive) Then
                Me.CaseSensitive = table.CaseSensitive
            End If
            If (table.Locale.ToString <> table.DataSet.Locale.ToString) Then
                Me.Locale = table.Locale
            End If
            If (table.Namespace <> table.DataSet.Namespace) Then
                Me.Namespace = table.Namespace
            End If
            Me.Prefix = table.Prefix
            Me.MinimumCapacity = table.MinimumCapacity
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Sub New(ByVal info As Global.System.Runtime.Serialization.SerializationInfo, ByVal context As Global.System.Runtime.Serialization.StreamingContext)
            MyBase.New(info, context)
            Me.InitVars
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ContractIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnContractID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property PeriodNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnPeriodName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property AmountColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnAmount
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property PONumberColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnPONumber
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Browsable(false)>  _
        Public ReadOnly Property Count() As Integer
            Get
                Return Me.Rows.Count
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Default ReadOnly Property Item(ByVal index As Integer) As BillingInstructionRow
            Get
                Return CType(Me.Rows(index),BillingInstructionRow)
            End Get
        End Property
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event BillingInstructionRowChanging As BillingInstructionRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event BillingInstructionRowChanged As BillingInstructionRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event BillingInstructionRowDeleting As BillingInstructionRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event BillingInstructionRowDeleted As BillingInstructionRowChangeEventHandler
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overloads Sub AddBillingInstructionRow(ByVal row As BillingInstructionRow)
            Me.Rows.Add(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overloads Function AddBillingInstructionRow(ByVal parentContractRowByContract_PurchaseOrderNumber As ContractRow, ByVal PeriodName As String, ByVal Amount As Decimal, ByVal PONumber As String) As BillingInstructionRow
            Dim rowBillingInstructionRow As BillingInstructionRow = CType(Me.NewRow,BillingInstructionRow)
            Dim columnValuesArray() As Object = New Object() {Nothing, PeriodName, Amount, PONumber}
            If (Not (parentContractRowByContract_PurchaseOrderNumber) Is Nothing) Then
                columnValuesArray(0) = parentContractRowByContract_PurchaseOrderNumber(0)
            End If
            rowBillingInstructionRow.ItemArray = columnValuesArray
            Me.Rows.Add(rowBillingInstructionRow)
            Return rowBillingInstructionRow
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overrides Function Clone() As Global.System.Data.DataTable
            Dim cln As BillingInstructionDataTable = CType(MyBase.Clone,BillingInstructionDataTable)
            cln.InitVars
            Return cln
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function CreateInstance() As Global.System.Data.DataTable
            Return New BillingInstructionDataTable()
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub InitVars()
            Me.columnContractID = MyBase.Columns("ContractID")
            Me.columnPeriodName = MyBase.Columns("PeriodName")
            Me.columnAmount = MyBase.Columns("Amount")
            Me.columnPONumber = MyBase.Columns("PONumber")
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitClass()
            Me.columnContractID = New Global.System.Data.DataColumn("ContractID", GetType(Global.System.Guid), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnContractID)
            Me.columnPeriodName = New Global.System.Data.DataColumn("PeriodName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnPeriodName)
            Me.columnAmount = New Global.System.Data.DataColumn("Amount", GetType(Decimal), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnAmount)
            Me.columnPONumber = New Global.System.Data.DataColumn("PONumber", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnPONumber)
            Me.columnContractID.AllowDBNull = false
            Me.columnPeriodName.AllowDBNull = false
            Me.columnPeriodName.MaxLength = 150
            Me.columnAmount.AllowDBNull = false
            Me.columnPONumber.AllowDBNull = false
            Me.columnPONumber.MaxLength = 250
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function NewBillingInstructionRow() As BillingInstructionRow
            Return CType(Me.NewRow,BillingInstructionRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function NewRowFromBuilder(ByVal builder As Global.System.Data.DataRowBuilder) As Global.System.Data.DataRow
            Return New BillingInstructionRow(builder)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function GetRowType() As Global.System.Type
            Return GetType(BillingInstructionRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowChanged(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanged(e)
            If (Not (Me.BillingInstructionRowChangedEvent) Is Nothing) Then
                RaiseEvent BillingInstructionRowChanged(Me, New BillingInstructionRowChangeEvent(CType(e.Row,BillingInstructionRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowChanging(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanging(e)
            If (Not (Me.BillingInstructionRowChangingEvent) Is Nothing) Then
                RaiseEvent BillingInstructionRowChanging(Me, New BillingInstructionRowChangeEvent(CType(e.Row,BillingInstructionRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowDeleted(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleted(e)
            If (Not (Me.BillingInstructionRowDeletedEvent) Is Nothing) Then
                RaiseEvent BillingInstructionRowDeleted(Me, New BillingInstructionRowChangeEvent(CType(e.Row,BillingInstructionRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowDeleting(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleting(e)
            If (Not (Me.BillingInstructionRowDeletingEvent) Is Nothing) Then
                RaiseEvent BillingInstructionRowDeleting(Me, New BillingInstructionRowChangeEvent(CType(e.Row,BillingInstructionRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub RemoveBillingInstructionRow(ByVal row As BillingInstructionRow)
            Me.Rows.Remove(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Shared Function GetTypedTableSchema(ByVal xs As Global.System.Xml.Schema.XmlSchemaSet) As Global.System.Xml.Schema.XmlSchemaComplexType
            Dim type As Global.System.Xml.Schema.XmlSchemaComplexType = New Global.System.Xml.Schema.XmlSchemaComplexType()
            Dim sequence As Global.System.Xml.Schema.XmlSchemaSequence = New Global.System.Xml.Schema.XmlSchemaSequence()
            Dim ds As DataSetContractReport = New DataSetContractReport()
            Dim any1 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any1.Namespace = "http://www.w3.org/2001/XMLSchema"
            any1.MinOccurs = New Decimal(0)
            any1.MaxOccurs = Decimal.MaxValue
            any1.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any1)
            Dim any2 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1"
            any2.MinOccurs = New Decimal(1)
            any2.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any2)
            Dim attribute1 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute1.Name = "namespace"
            attribute1.FixedValue = ds.Namespace
            type.Attributes.Add(attribute1)
            Dim attribute2 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute2.Name = "tableTypeName"
            attribute2.FixedValue = "BillingInstructionDataTable"
            type.Attributes.Add(attribute2)
            type.Particle = sequence
            Dim dsSchema As Global.System.Xml.Schema.XmlSchema = ds.GetSchemaSerializable
            If xs.Contains(dsSchema.TargetNamespace) Then
                Dim s1 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Dim s2 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Try 
                    Dim schema As Global.System.Xml.Schema.XmlSchema = Nothing
                    dsSchema.Write(s1)
                    Dim schemas As Global.System.Collections.IEnumerator = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator
                    Do While schemas.MoveNext
                        schema = CType(schemas.Current,Global.System.Xml.Schema.XmlSchema)
                        s2.SetLength(0)
                        schema.Write(s2)
                        If (s1.Length = s2.Length) Then
                            s1.Position = 0
                            s2.Position = 0
                            
                            Do While ((s1.Position <> s1.Length)  _
                                        AndAlso (s1.ReadByte = s2.ReadByte))
                                
                                
                            Loop
                            If (s1.Position = s1.Length) Then
                                Return type
                            End If
                        End If
                        
                    Loop
                Finally
                    If (Not (s1) Is Nothing) Then
                        s1.Close
                    End If
                    If (Not (s2) Is Nothing) Then
                        s2.Close
                    End If
                End Try
            End If
            xs.Add(dsSchema)
            Return type
        End Function
    End Class
    
    '''<summary>
    '''Represents the strongly named DataTable class.
    '''</summary>
    <Global.System.Serializable(),  _
     Global.System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")>  _
    Partial Public Class BurstDataTable
        Inherits Global.System.Data.TypedTableBase(Of BurstRow)
        
        Private columnBurstID As Global.System.Data.DataColumn
        
        Private columnContractID As Global.System.Data.DataColumn
        
        Private columnChainID As Global.System.Data.DataColumn
        
        Private columnChainName As Global.System.Data.DataColumn
        
        Private columnStorePoolID As Global.System.Data.DataColumn
        
        Private columnMediaID As Global.System.Data.DataColumn
        
        Private columnMediaName As Global.System.Data.DataColumn
        
        Private columnBrandID As Global.System.Data.DataColumn
        
        Private columnBrandName As Global.System.Data.DataColumn
        
        Private columnProductName As Global.System.Data.DataColumn
        
        Private columnFirstWeek As Global.System.Data.DataColumn
        
        Private columnInstallWeeks As Global.System.Data.DataColumn
        
        Private columnInstallStoreQty As Global.System.Data.DataColumn
        
        Private columnBillableStoreQty As Global.System.Data.DataColumn
        
        Private columnRentalRate As Global.System.Data.DataColumn
        
        Private columnBillableWeeks As Global.System.Data.DataColumn
        
        Private columnDiscount As Global.System.Data.DataColumn
        
        Private columnCrossoverQty As Global.System.Data.DataColumn
        
        Private columnInstallAtHomesite As Global.System.Data.DataColumn
        
        Private columnInstallationInstructions As Global.System.Data.DataColumn
        
        Private columnStoreListConfirmed As Global.System.Data.DataColumn
        
        Private columnCreatedBy As Global.System.Data.DataColumn
        
        Private columnCreationDate As Global.System.Data.DataColumn
        
        Private columnLastWeek As Global.System.Data.DataColumn
        
        Private columnLoadingPercentage As Global.System.Data.DataColumn
        
        Private columnLoadingAmount As Global.System.Data.DataColumn
        
        Private columnTotalFreeStores As Global.System.Data.DataColumn
        
        Private columnHomesite As Global.System.Data.DataColumn
        
        Private columnGroupDefinition As Global.System.Data.DataColumn
        
        Private columnDiscountAmount As Global.System.Data.DataColumn
        
        Private columnTotalFreeWeeks As Global.System.Data.DataColumn
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New()
            MyBase.New
            Me.TableName = "Burst"
            Me.BeginInit
            Me.InitClass
            Me.EndInit
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub New(ByVal table As Global.System.Data.DataTable)
            MyBase.New
            Me.TableName = table.TableName
            If (table.CaseSensitive <> table.DataSet.CaseSensitive) Then
                Me.CaseSensitive = table.CaseSensitive
            End If
            If (table.Locale.ToString <> table.DataSet.Locale.ToString) Then
                Me.Locale = table.Locale
            End If
            If (table.Namespace <> table.DataSet.Namespace) Then
                Me.Namespace = table.Namespace
            End If
            Me.Prefix = table.Prefix
            Me.MinimumCapacity = table.MinimumCapacity
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Sub New(ByVal info As Global.System.Runtime.Serialization.SerializationInfo, ByVal context As Global.System.Runtime.Serialization.StreamingContext)
            MyBase.New(info, context)
            Me.InitVars
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property BurstIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnBurstID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ContractIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnContractID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ChainIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnChainID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ChainNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnChainName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property StorePoolIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnStorePoolID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property MediaIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnMediaID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property MediaNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnMediaName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property BrandIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnBrandID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property BrandNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnBrandName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ProductNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnProductName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property FirstWeekColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnFirstWeek
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property InstallWeeksColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnInstallWeeks
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property InstallStoreQtyColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnInstallStoreQty
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property BillableStoreQtyColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnBillableStoreQty
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property RentalRateColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnRentalRate
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property BillableWeeksColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnBillableWeeks
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property DiscountColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnDiscount
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property CrossoverQtyColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnCrossoverQty
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property InstallAtHomesiteColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnInstallAtHomesite
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property InstallationInstructionsColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnInstallationInstructions
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property StoreListConfirmedColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnStoreListConfirmed
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property CreatedByColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnCreatedBy
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property CreationDateColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnCreationDate
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property LastWeekColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnLastWeek
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property LoadingPercentageColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnLoadingPercentage
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property LoadingAmountColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnLoadingAmount
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property TotalFreeStoresColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnTotalFreeStores
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property HomesiteColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnHomesite
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property GroupDefinitionColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnGroupDefinition
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property DiscountAmountColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnDiscountAmount
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property TotalFreeWeeksColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnTotalFreeWeeks
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Browsable(false)>  _
        Public ReadOnly Property Count() As Integer
            Get
                Return Me.Rows.Count
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Default ReadOnly Property Item(ByVal index As Integer) As BurstRow
            Get
                Return CType(Me.Rows(index),BurstRow)
            End Get
        End Property
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event BurstRowChanging As BurstRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event BurstRowChanged As BurstRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event BurstRowDeleting As BurstRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event BurstRowDeleted As BurstRowChangeEventHandler
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overloads Sub AddBurstRow(ByVal row As BurstRow)
            Me.Rows.Add(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overloads Function AddBurstRow( _
                    ByVal BurstID As System.Guid,  _
                    ByVal parentContractRowByContract_Burst As ContractRow,  _
                    ByVal ChainID As Integer,  _
                    ByVal ChainName As String,  _
                    ByVal StorePoolID As System.Guid,  _
                    ByVal MediaID As Integer,  _
                    ByVal MediaName As String,  _
                    ByVal BrandID As System.Guid,  _
                    ByVal BrandName As String,  _
                    ByVal ProductName As String,  _
                    ByVal FirstWeek As Date,  _
                    ByVal InstallWeeks As Integer,  _
                    ByVal InstallStoreQty As Integer,  _
                    ByVal BillableStoreQty As Integer,  _
                    ByVal RentalRate As Decimal,  _
                    ByVal BillableWeeks As Integer,  _
                    ByVal Discount As Decimal,  _
                    ByVal CrossoverQty As Integer,  _
                    ByVal InstallAtHomesite As Boolean,  _
                    ByVal InstallationInstructions As String,  _
                    ByVal StoreListConfirmed As Boolean,  _
                    ByVal CreatedBy As String,  _
                    ByVal CreationDate As Date,  _
                    ByVal LastWeek As Date,  _
                    ByVal LoadingPercentage As Decimal,  _
                    ByVal LoadingAmount As Decimal,  _
                    ByVal TotalFreeStores As Integer,  _
                    ByVal Homesite As String,  _
                    ByVal GroupDefinition As String,  _
                    ByVal DiscountAmount As Decimal,  _
                    ByVal TotalFreeWeeks As Integer) As BurstRow
            Dim rowBurstRow As BurstRow = CType(Me.NewRow,BurstRow)
            Dim columnValuesArray() As Object = New Object() {BurstID, Nothing, ChainID, ChainName, StorePoolID, MediaID, MediaName, BrandID, BrandName, ProductName, FirstWeek, InstallWeeks, InstallStoreQty, BillableStoreQty, RentalRate, BillableWeeks, Discount, CrossoverQty, InstallAtHomesite, InstallationInstructions, StoreListConfirmed, CreatedBy, CreationDate, LastWeek, LoadingPercentage, LoadingAmount, TotalFreeStores, Homesite, GroupDefinition, DiscountAmount, TotalFreeWeeks}
            If (Not (parentContractRowByContract_Burst) Is Nothing) Then
                columnValuesArray(1) = parentContractRowByContract_Burst(0)
            End If
            rowBurstRow.ItemArray = columnValuesArray
            Me.Rows.Add(rowBurstRow)
            Return rowBurstRow
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function FindByBurstID(ByVal BurstID As System.Guid) As BurstRow
            Return CType(Me.Rows.Find(New Object() {BurstID}),BurstRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overrides Function Clone() As Global.System.Data.DataTable
            Dim cln As BurstDataTable = CType(MyBase.Clone,BurstDataTable)
            cln.InitVars
            Return cln
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function CreateInstance() As Global.System.Data.DataTable
            Return New BurstDataTable()
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub InitVars()
            Me.columnBurstID = MyBase.Columns("BurstID")
            Me.columnContractID = MyBase.Columns("ContractID")
            Me.columnChainID = MyBase.Columns("ChainID")
            Me.columnChainName = MyBase.Columns("ChainName")
            Me.columnStorePoolID = MyBase.Columns("StorePoolID")
            Me.columnMediaID = MyBase.Columns("MediaID")
            Me.columnMediaName = MyBase.Columns("MediaName")
            Me.columnBrandID = MyBase.Columns("BrandID")
            Me.columnBrandName = MyBase.Columns("BrandName")
            Me.columnProductName = MyBase.Columns("ProductName")
            Me.columnFirstWeek = MyBase.Columns("FirstWeek")
            Me.columnInstallWeeks = MyBase.Columns("InstallWeeks")
            Me.columnInstallStoreQty = MyBase.Columns("InstallStoreQty")
            Me.columnBillableStoreQty = MyBase.Columns("BillableStoreQty")
            Me.columnRentalRate = MyBase.Columns("RentalRate")
            Me.columnBillableWeeks = MyBase.Columns("BillableWeeks")
            Me.columnDiscount = MyBase.Columns("Discount")
            Me.columnCrossoverQty = MyBase.Columns("CrossoverQty")
            Me.columnInstallAtHomesite = MyBase.Columns("InstallAtHomesite")
            Me.columnInstallationInstructions = MyBase.Columns("InstallationInstructions")
            Me.columnStoreListConfirmed = MyBase.Columns("StoreListConfirmed")
            Me.columnCreatedBy = MyBase.Columns("CreatedBy")
            Me.columnCreationDate = MyBase.Columns("CreationDate")
            Me.columnLastWeek = MyBase.Columns("LastWeek")
            Me.columnLoadingPercentage = MyBase.Columns("LoadingPercentage")
            Me.columnLoadingAmount = MyBase.Columns("LoadingAmount")
            Me.columnTotalFreeStores = MyBase.Columns("TotalFreeStores")
            Me.columnHomesite = MyBase.Columns("Homesite")
            Me.columnGroupDefinition = MyBase.Columns("GroupDefinition")
            Me.columnDiscountAmount = MyBase.Columns("DiscountAmount")
            Me.columnTotalFreeWeeks = MyBase.Columns("TotalFreeWeeks")
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitClass()
            Me.columnBurstID = New Global.System.Data.DataColumn("BurstID", GetType(Global.System.Guid), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnBurstID)
            Me.columnContractID = New Global.System.Data.DataColumn("ContractID", GetType(Global.System.Guid), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnContractID)
            Me.columnChainID = New Global.System.Data.DataColumn("ChainID", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnChainID)
            Me.columnChainName = New Global.System.Data.DataColumn("ChainName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnChainName)
            Me.columnStorePoolID = New Global.System.Data.DataColumn("StorePoolID", GetType(Global.System.Guid), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnStorePoolID)
            Me.columnMediaID = New Global.System.Data.DataColumn("MediaID", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnMediaID)
            Me.columnMediaName = New Global.System.Data.DataColumn("MediaName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnMediaName)
            Me.columnBrandID = New Global.System.Data.DataColumn("BrandID", GetType(Global.System.Guid), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnBrandID)
            Me.columnBrandName = New Global.System.Data.DataColumn("BrandName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnBrandName)
            Me.columnProductName = New Global.System.Data.DataColumn("ProductName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnProductName)
            Me.columnFirstWeek = New Global.System.Data.DataColumn("FirstWeek", GetType(Date), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnFirstWeek)
            Me.columnInstallWeeks = New Global.System.Data.DataColumn("InstallWeeks", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnInstallWeeks)
            Me.columnInstallStoreQty = New Global.System.Data.DataColumn("InstallStoreQty", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnInstallStoreQty)
            Me.columnBillableStoreQty = New Global.System.Data.DataColumn("BillableStoreQty", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnBillableStoreQty)
            Me.columnRentalRate = New Global.System.Data.DataColumn("RentalRate", GetType(Decimal), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnRentalRate)
            Me.columnBillableWeeks = New Global.System.Data.DataColumn("BillableWeeks", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnBillableWeeks)
            Me.columnDiscount = New Global.System.Data.DataColumn("Discount", GetType(Decimal), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnDiscount)
            Me.columnCrossoverQty = New Global.System.Data.DataColumn("CrossoverQty", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnCrossoverQty)
            Me.columnInstallAtHomesite = New Global.System.Data.DataColumn("InstallAtHomesite", GetType(Boolean), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnInstallAtHomesite)
            Me.columnInstallationInstructions = New Global.System.Data.DataColumn("InstallationInstructions", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnInstallationInstructions)
            Me.columnStoreListConfirmed = New Global.System.Data.DataColumn("StoreListConfirmed", GetType(Boolean), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnStoreListConfirmed)
            Me.columnCreatedBy = New Global.System.Data.DataColumn("CreatedBy", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnCreatedBy)
            Me.columnCreationDate = New Global.System.Data.DataColumn("CreationDate", GetType(Date), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnCreationDate)
            Me.columnLastWeek = New Global.System.Data.DataColumn("LastWeek", GetType(Date), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnLastWeek)
            Me.columnLoadingPercentage = New Global.System.Data.DataColumn("LoadingPercentage", GetType(Decimal), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnLoadingPercentage)
            Me.columnLoadingAmount = New Global.System.Data.DataColumn("LoadingAmount", GetType(Decimal), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnLoadingAmount)
            Me.columnTotalFreeStores = New Global.System.Data.DataColumn("TotalFreeStores", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnTotalFreeStores)
            Me.columnHomesite = New Global.System.Data.DataColumn("Homesite", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnHomesite)
            Me.columnGroupDefinition = New Global.System.Data.DataColumn("GroupDefinition", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnGroupDefinition)
            Me.columnDiscountAmount = New Global.System.Data.DataColumn("DiscountAmount", GetType(Decimal), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnDiscountAmount)
            Me.columnTotalFreeWeeks = New Global.System.Data.DataColumn("TotalFreeWeeks", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnTotalFreeWeeks)
            Me.Constraints.Add(New Global.System.Data.UniqueConstraint("Constraint1", New Global.System.Data.DataColumn() {Me.columnBurstID}, true))
            Me.columnBurstID.AllowDBNull = false
            Me.columnBurstID.Unique = true
            Me.columnContractID.AllowDBNull = false
            Me.columnChainID.AllowDBNull = false
            Me.columnChainName.AllowDBNull = false
            Me.columnChainName.MaxLength = 200
            Me.columnStorePoolID.AllowDBNull = false
            Me.columnMediaID.AllowDBNull = false
            Me.columnMediaName.AllowDBNull = false
            Me.columnMediaName.MaxLength = 200
            Me.columnBrandID.AllowDBNull = false
            Me.columnBrandName.AllowDBNull = false
            Me.columnBrandName.MaxLength = 200
            Me.columnProductName.AllowDBNull = false
            Me.columnProductName.MaxLength = 200
            Me.columnFirstWeek.AllowDBNull = false
            Me.columnInstallWeeks.AllowDBNull = false
            Me.columnInstallStoreQty.AllowDBNull = false
            Me.columnBillableStoreQty.AllowDBNull = false
            Me.columnRentalRate.AllowDBNull = false
            Me.columnBillableWeeks.AllowDBNull = false
            Me.columnDiscount.AllowDBNull = false
            Me.columnCrossoverQty.AllowDBNull = false
            Me.columnInstallAtHomesite.AllowDBNull = false
            Me.columnInstallationInstructions.AllowDBNull = false
            Me.columnInstallationInstructions.MaxLength = 2000
            Me.columnStoreListConfirmed.AllowDBNull = false
            Me.columnCreatedBy.AllowDBNull = false
            Me.columnCreatedBy.MaxLength = 50
            Me.columnCreationDate.AllowDBNull = false
            Me.columnLastWeek.ReadOnly = true
            Me.columnLoadingPercentage.ReadOnly = true
            Me.columnLoadingAmount.ReadOnly = true
            Me.columnTotalFreeStores.ReadOnly = true
            Me.columnHomesite.MaxLength = 200
            Me.columnGroupDefinition.ReadOnly = true
            Me.columnGroupDefinition.MaxLength = **********
            Me.columnDiscountAmount.ReadOnly = true
            Me.columnTotalFreeWeeks.ReadOnly = true
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function NewBurstRow() As BurstRow
            Return CType(Me.NewRow,BurstRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function NewRowFromBuilder(ByVal builder As Global.System.Data.DataRowBuilder) As Global.System.Data.DataRow
            Return New BurstRow(builder)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function GetRowType() As Global.System.Type
            Return GetType(BurstRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowChanged(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanged(e)
            If (Not (Me.BurstRowChangedEvent) Is Nothing) Then
                RaiseEvent BurstRowChanged(Me, New BurstRowChangeEvent(CType(e.Row,BurstRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowChanging(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanging(e)
            If (Not (Me.BurstRowChangingEvent) Is Nothing) Then
                RaiseEvent BurstRowChanging(Me, New BurstRowChangeEvent(CType(e.Row,BurstRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowDeleted(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleted(e)
            If (Not (Me.BurstRowDeletedEvent) Is Nothing) Then
                RaiseEvent BurstRowDeleted(Me, New BurstRowChangeEvent(CType(e.Row,BurstRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowDeleting(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleting(e)
            If (Not (Me.BurstRowDeletingEvent) Is Nothing) Then
                RaiseEvent BurstRowDeleting(Me, New BurstRowChangeEvent(CType(e.Row,BurstRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub RemoveBurstRow(ByVal row As BurstRow)
            Me.Rows.Remove(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Shared Function GetTypedTableSchema(ByVal xs As Global.System.Xml.Schema.XmlSchemaSet) As Global.System.Xml.Schema.XmlSchemaComplexType
            Dim type As Global.System.Xml.Schema.XmlSchemaComplexType = New Global.System.Xml.Schema.XmlSchemaComplexType()
            Dim sequence As Global.System.Xml.Schema.XmlSchemaSequence = New Global.System.Xml.Schema.XmlSchemaSequence()
            Dim ds As DataSetContractReport = New DataSetContractReport()
            Dim any1 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any1.Namespace = "http://www.w3.org/2001/XMLSchema"
            any1.MinOccurs = New Decimal(0)
            any1.MaxOccurs = Decimal.MaxValue
            any1.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any1)
            Dim any2 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1"
            any2.MinOccurs = New Decimal(1)
            any2.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any2)
            Dim attribute1 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute1.Name = "namespace"
            attribute1.FixedValue = ds.Namespace
            type.Attributes.Add(attribute1)
            Dim attribute2 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute2.Name = "tableTypeName"
            attribute2.FixedValue = "BurstDataTable"
            type.Attributes.Add(attribute2)
            type.Particle = sequence
            Dim dsSchema As Global.System.Xml.Schema.XmlSchema = ds.GetSchemaSerializable
            If xs.Contains(dsSchema.TargetNamespace) Then
                Dim s1 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Dim s2 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Try 
                    Dim schema As Global.System.Xml.Schema.XmlSchema = Nothing
                    dsSchema.Write(s1)
                    Dim schemas As Global.System.Collections.IEnumerator = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator
                    Do While schemas.MoveNext
                        schema = CType(schemas.Current,Global.System.Xml.Schema.XmlSchema)
                        s2.SetLength(0)
                        schema.Write(s2)
                        If (s1.Length = s2.Length) Then
                            s1.Position = 0
                            s2.Position = 0
                            
                            Do While ((s1.Position <> s1.Length)  _
                                        AndAlso (s1.ReadByte = s2.ReadByte))
                                
                                
                            Loop
                            If (s1.Position = s1.Length) Then
                                Return type
                            End If
                        End If
                        
                    Loop
                Finally
                    If (Not (s1) Is Nothing) Then
                        s1.Close
                    End If
                    If (Not (s2) Is Nothing) Then
                        s2.Close
                    End If
                End Try
            End If
            xs.Add(dsSchema)
            Return type
        End Function
    End Class
    
    '''<summary>
    '''Represents the strongly named DataTable class.
    '''</summary>
    <Global.System.Serializable(),  _
     Global.System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")>  _
    Partial Public Class ContractInventoryDataTable
        Inherits Global.System.Data.TypedTableBase(Of ContractInventoryRow)
        
        Private columnContractID As Global.System.Data.DataColumn
        
        Private columnItemName As Global.System.Data.DataColumn
        
        Private columnItemQty As Global.System.Data.DataColumn
        
        Private columnSellPrice As Global.System.Data.DataColumn
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New()
            MyBase.New
            Me.TableName = "ContractInventory"
            Me.BeginInit
            Me.InitClass
            Me.EndInit
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub New(ByVal table As Global.System.Data.DataTable)
            MyBase.New
            Me.TableName = table.TableName
            If (table.CaseSensitive <> table.DataSet.CaseSensitive) Then
                Me.CaseSensitive = table.CaseSensitive
            End If
            If (table.Locale.ToString <> table.DataSet.Locale.ToString) Then
                Me.Locale = table.Locale
            End If
            If (table.Namespace <> table.DataSet.Namespace) Then
                Me.Namespace = table.Namespace
            End If
            Me.Prefix = table.Prefix
            Me.MinimumCapacity = table.MinimumCapacity
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Sub New(ByVal info As Global.System.Runtime.Serialization.SerializationInfo, ByVal context As Global.System.Runtime.Serialization.StreamingContext)
            MyBase.New(info, context)
            Me.InitVars
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ContractIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnContractID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ItemNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnItemName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ItemQtyColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnItemQty
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property SellPriceColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnSellPrice
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Browsable(false)>  _
        Public ReadOnly Property Count() As Integer
            Get
                Return Me.Rows.Count
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Default ReadOnly Property Item(ByVal index As Integer) As ContractInventoryRow
            Get
                Return CType(Me.Rows(index),ContractInventoryRow)
            End Get
        End Property
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ContractInventoryRowChanging As ContractInventoryRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ContractInventoryRowChanged As ContractInventoryRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ContractInventoryRowDeleting As ContractInventoryRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ContractInventoryRowDeleted As ContractInventoryRowChangeEventHandler
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overloads Sub AddContractInventoryRow(ByVal row As ContractInventoryRow)
            Me.Rows.Add(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overloads Function AddContractInventoryRow(ByVal parentContractRowByContract_ContractInventory As ContractRow, ByVal ItemName As String, ByVal ItemQty As Integer, ByVal SellPrice As Decimal) As ContractInventoryRow
            Dim rowContractInventoryRow As ContractInventoryRow = CType(Me.NewRow,ContractInventoryRow)
            Dim columnValuesArray() As Object = New Object() {Nothing, ItemName, ItemQty, SellPrice}
            If (Not (parentContractRowByContract_ContractInventory) Is Nothing) Then
                columnValuesArray(0) = parentContractRowByContract_ContractInventory(0)
            End If
            rowContractInventoryRow.ItemArray = columnValuesArray
            Me.Rows.Add(rowContractInventoryRow)
            Return rowContractInventoryRow
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overrides Function Clone() As Global.System.Data.DataTable
            Dim cln As ContractInventoryDataTable = CType(MyBase.Clone,ContractInventoryDataTable)
            cln.InitVars
            Return cln
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function CreateInstance() As Global.System.Data.DataTable
            Return New ContractInventoryDataTable()
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub InitVars()
            Me.columnContractID = MyBase.Columns("ContractID")
            Me.columnItemName = MyBase.Columns("ItemName")
            Me.columnItemQty = MyBase.Columns("ItemQty")
            Me.columnSellPrice = MyBase.Columns("SellPrice")
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitClass()
            Me.columnContractID = New Global.System.Data.DataColumn("ContractID", GetType(Global.System.Guid), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnContractID)
            Me.columnItemName = New Global.System.Data.DataColumn("ItemName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnItemName)
            Me.columnItemQty = New Global.System.Data.DataColumn("ItemQty", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnItemQty)
            Me.columnSellPrice = New Global.System.Data.DataColumn("SellPrice", GetType(Decimal), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnSellPrice)
            Me.columnContractID.AllowDBNull = false
            Me.columnItemName.AllowDBNull = false
            Me.columnItemName.MaxLength = 250
            Me.columnItemQty.AllowDBNull = false
            Me.columnSellPrice.AllowDBNull = false
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function NewContractInventoryRow() As ContractInventoryRow
            Return CType(Me.NewRow,ContractInventoryRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function NewRowFromBuilder(ByVal builder As Global.System.Data.DataRowBuilder) As Global.System.Data.DataRow
            Return New ContractInventoryRow(builder)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function GetRowType() As Global.System.Type
            Return GetType(ContractInventoryRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowChanged(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanged(e)
            If (Not (Me.ContractInventoryRowChangedEvent) Is Nothing) Then
                RaiseEvent ContractInventoryRowChanged(Me, New ContractInventoryRowChangeEvent(CType(e.Row,ContractInventoryRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowChanging(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanging(e)
            If (Not (Me.ContractInventoryRowChangingEvent) Is Nothing) Then
                RaiseEvent ContractInventoryRowChanging(Me, New ContractInventoryRowChangeEvent(CType(e.Row,ContractInventoryRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowDeleted(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleted(e)
            If (Not (Me.ContractInventoryRowDeletedEvent) Is Nothing) Then
                RaiseEvent ContractInventoryRowDeleted(Me, New ContractInventoryRowChangeEvent(CType(e.Row,ContractInventoryRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowDeleting(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleting(e)
            If (Not (Me.ContractInventoryRowDeletingEvent) Is Nothing) Then
                RaiseEvent ContractInventoryRowDeleting(Me, New ContractInventoryRowChangeEvent(CType(e.Row,ContractInventoryRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub RemoveContractInventoryRow(ByVal row As ContractInventoryRow)
            Me.Rows.Remove(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Shared Function GetTypedTableSchema(ByVal xs As Global.System.Xml.Schema.XmlSchemaSet) As Global.System.Xml.Schema.XmlSchemaComplexType
            Dim type As Global.System.Xml.Schema.XmlSchemaComplexType = New Global.System.Xml.Schema.XmlSchemaComplexType()
            Dim sequence As Global.System.Xml.Schema.XmlSchemaSequence = New Global.System.Xml.Schema.XmlSchemaSequence()
            Dim ds As DataSetContractReport = New DataSetContractReport()
            Dim any1 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any1.Namespace = "http://www.w3.org/2001/XMLSchema"
            any1.MinOccurs = New Decimal(0)
            any1.MaxOccurs = Decimal.MaxValue
            any1.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any1)
            Dim any2 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1"
            any2.MinOccurs = New Decimal(1)
            any2.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any2)
            Dim attribute1 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute1.Name = "namespace"
            attribute1.FixedValue = ds.Namespace
            type.Attributes.Add(attribute1)
            Dim attribute2 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute2.Name = "tableTypeName"
            attribute2.FixedValue = "ContractInventoryDataTable"
            type.Attributes.Add(attribute2)
            type.Particle = sequence
            Dim dsSchema As Global.System.Xml.Schema.XmlSchema = ds.GetSchemaSerializable
            If xs.Contains(dsSchema.TargetNamespace) Then
                Dim s1 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Dim s2 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Try 
                    Dim schema As Global.System.Xml.Schema.XmlSchema = Nothing
                    dsSchema.Write(s1)
                    Dim schemas As Global.System.Collections.IEnumerator = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator
                    Do While schemas.MoveNext
                        schema = CType(schemas.Current,Global.System.Xml.Schema.XmlSchema)
                        s2.SetLength(0)
                        schema.Write(s2)
                        If (s1.Length = s2.Length) Then
                            s1.Position = 0
                            s2.Position = 0
                            
                            Do While ((s1.Position <> s1.Length)  _
                                        AndAlso (s1.ReadByte = s2.ReadByte))
                                
                                
                            Loop
                            If (s1.Position = s1.Length) Then
                                Return type
                            End If
                        End If
                        
                    Loop
                Finally
                    If (Not (s1) Is Nothing) Then
                        s1.Close
                    End If
                    If (Not (s2) Is Nothing) Then
                        s2.Close
                    End If
                End Try
            End If
            xs.Add(dsSchema)
            Return type
        End Function
    End Class
    
    '''<summary>
    '''Represents the strongly named DataTable class.
    '''</summary>
    <Global.System.Serializable(),  _
     Global.System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")>  _
    Partial Public Class MiscellaneousChargeDataTable
        Inherits Global.System.Data.TypedTableBase(Of MiscellaneousChargeRow)
        
        Private columnContractID As Global.System.Data.DataColumn
        
        Private columnMiscellaneousChargeName As Global.System.Data.DataColumn
        
        Private columnMiscellaneousChargeAmount As Global.System.Data.DataColumn
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New()
            MyBase.New
            Me.TableName = "MiscellaneousCharge"
            Me.BeginInit
            Me.InitClass
            Me.EndInit
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub New(ByVal table As Global.System.Data.DataTable)
            MyBase.New
            Me.TableName = table.TableName
            If (table.CaseSensitive <> table.DataSet.CaseSensitive) Then
                Me.CaseSensitive = table.CaseSensitive
            End If
            If (table.Locale.ToString <> table.DataSet.Locale.ToString) Then
                Me.Locale = table.Locale
            End If
            If (table.Namespace <> table.DataSet.Namespace) Then
                Me.Namespace = table.Namespace
            End If
            Me.Prefix = table.Prefix
            Me.MinimumCapacity = table.MinimumCapacity
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Sub New(ByVal info As Global.System.Runtime.Serialization.SerializationInfo, ByVal context As Global.System.Runtime.Serialization.StreamingContext)
            MyBase.New(info, context)
            Me.InitVars
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ContractIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnContractID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property MiscellaneousChargeNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnMiscellaneousChargeName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property MiscellaneousChargeAmountColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnMiscellaneousChargeAmount
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Browsable(false)>  _
        Public ReadOnly Property Count() As Integer
            Get
                Return Me.Rows.Count
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Default ReadOnly Property Item(ByVal index As Integer) As MiscellaneousChargeRow
            Get
                Return CType(Me.Rows(index),MiscellaneousChargeRow)
            End Get
        End Property
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event MiscellaneousChargeRowChanging As MiscellaneousChargeRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event MiscellaneousChargeRowChanged As MiscellaneousChargeRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event MiscellaneousChargeRowDeleting As MiscellaneousChargeRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event MiscellaneousChargeRowDeleted As MiscellaneousChargeRowChangeEventHandler
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overloads Sub AddMiscellaneousChargeRow(ByVal row As MiscellaneousChargeRow)
            Me.Rows.Add(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overloads Function AddMiscellaneousChargeRow(ByVal parentContractRowByContract_MiscellaneousCharge As ContractRow, ByVal MiscellaneousChargeName As String, ByVal MiscellaneousChargeAmount As Decimal) As MiscellaneousChargeRow
            Dim rowMiscellaneousChargeRow As MiscellaneousChargeRow = CType(Me.NewRow,MiscellaneousChargeRow)
            Dim columnValuesArray() As Object = New Object() {Nothing, MiscellaneousChargeName, MiscellaneousChargeAmount}
            If (Not (parentContractRowByContract_MiscellaneousCharge) Is Nothing) Then
                columnValuesArray(0) = parentContractRowByContract_MiscellaneousCharge(0)
            End If
            rowMiscellaneousChargeRow.ItemArray = columnValuesArray
            Me.Rows.Add(rowMiscellaneousChargeRow)
            Return rowMiscellaneousChargeRow
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overrides Function Clone() As Global.System.Data.DataTable
            Dim cln As MiscellaneousChargeDataTable = CType(MyBase.Clone,MiscellaneousChargeDataTable)
            cln.InitVars
            Return cln
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function CreateInstance() As Global.System.Data.DataTable
            Return New MiscellaneousChargeDataTable()
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub InitVars()
            Me.columnContractID = MyBase.Columns("ContractID")
            Me.columnMiscellaneousChargeName = MyBase.Columns("MiscellaneousChargeName")
            Me.columnMiscellaneousChargeAmount = MyBase.Columns("MiscellaneousChargeAmount")
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitClass()
            Me.columnContractID = New Global.System.Data.DataColumn("ContractID", GetType(Global.System.Guid), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnContractID)
            Me.columnMiscellaneousChargeName = New Global.System.Data.DataColumn("MiscellaneousChargeName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnMiscellaneousChargeName)
            Me.columnMiscellaneousChargeAmount = New Global.System.Data.DataColumn("MiscellaneousChargeAmount", GetType(Decimal), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnMiscellaneousChargeAmount)
            Me.columnContractID.AllowDBNull = false
            Me.columnMiscellaneousChargeName.AllowDBNull = false
            Me.columnMiscellaneousChargeName.MaxLength = 250
            Me.columnMiscellaneousChargeAmount.AllowDBNull = false
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function NewMiscellaneousChargeRow() As MiscellaneousChargeRow
            Return CType(Me.NewRow,MiscellaneousChargeRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function NewRowFromBuilder(ByVal builder As Global.System.Data.DataRowBuilder) As Global.System.Data.DataRow
            Return New MiscellaneousChargeRow(builder)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function GetRowType() As Global.System.Type
            Return GetType(MiscellaneousChargeRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowChanged(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanged(e)
            If (Not (Me.MiscellaneousChargeRowChangedEvent) Is Nothing) Then
                RaiseEvent MiscellaneousChargeRowChanged(Me, New MiscellaneousChargeRowChangeEvent(CType(e.Row,MiscellaneousChargeRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowChanging(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanging(e)
            If (Not (Me.MiscellaneousChargeRowChangingEvent) Is Nothing) Then
                RaiseEvent MiscellaneousChargeRowChanging(Me, New MiscellaneousChargeRowChangeEvent(CType(e.Row,MiscellaneousChargeRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowDeleted(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleted(e)
            If (Not (Me.MiscellaneousChargeRowDeletedEvent) Is Nothing) Then
                RaiseEvent MiscellaneousChargeRowDeleted(Me, New MiscellaneousChargeRowChangeEvent(CType(e.Row,MiscellaneousChargeRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowDeleting(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleting(e)
            If (Not (Me.MiscellaneousChargeRowDeletingEvent) Is Nothing) Then
                RaiseEvent MiscellaneousChargeRowDeleting(Me, New MiscellaneousChargeRowChangeEvent(CType(e.Row,MiscellaneousChargeRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub RemoveMiscellaneousChargeRow(ByVal row As MiscellaneousChargeRow)
            Me.Rows.Remove(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Shared Function GetTypedTableSchema(ByVal xs As Global.System.Xml.Schema.XmlSchemaSet) As Global.System.Xml.Schema.XmlSchemaComplexType
            Dim type As Global.System.Xml.Schema.XmlSchemaComplexType = New Global.System.Xml.Schema.XmlSchemaComplexType()
            Dim sequence As Global.System.Xml.Schema.XmlSchemaSequence = New Global.System.Xml.Schema.XmlSchemaSequence()
            Dim ds As DataSetContractReport = New DataSetContractReport()
            Dim any1 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any1.Namespace = "http://www.w3.org/2001/XMLSchema"
            any1.MinOccurs = New Decimal(0)
            any1.MaxOccurs = Decimal.MaxValue
            any1.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any1)
            Dim any2 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1"
            any2.MinOccurs = New Decimal(1)
            any2.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any2)
            Dim attribute1 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute1.Name = "namespace"
            attribute1.FixedValue = ds.Namespace
            type.Attributes.Add(attribute1)
            Dim attribute2 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute2.Name = "tableTypeName"
            attribute2.FixedValue = "MiscellaneousChargeDataTable"
            type.Attributes.Add(attribute2)
            type.Particle = sequence
            Dim dsSchema As Global.System.Xml.Schema.XmlSchema = ds.GetSchemaSerializable
            If xs.Contains(dsSchema.TargetNamespace) Then
                Dim s1 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Dim s2 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Try 
                    Dim schema As Global.System.Xml.Schema.XmlSchema = Nothing
                    dsSchema.Write(s1)
                    Dim schemas As Global.System.Collections.IEnumerator = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator
                    Do While schemas.MoveNext
                        schema = CType(schemas.Current,Global.System.Xml.Schema.XmlSchema)
                        s2.SetLength(0)
                        schema.Write(s2)
                        If (s1.Length = s2.Length) Then
                            s1.Position = 0
                            s2.Position = 0
                            
                            Do While ((s1.Position <> s1.Length)  _
                                        AndAlso (s1.ReadByte = s2.ReadByte))
                                
                                
                            Loop
                            If (s1.Position = s1.Length) Then
                                Return type
                            End If
                        End If
                        
                    Loop
                Finally
                    If (Not (s1) Is Nothing) Then
                        s1.Close
                    End If
                    If (Not (s2) Is Nothing) Then
                        s2.Close
                    End If
                End Try
            End If
            xs.Add(dsSchema)
            Return type
        End Function
    End Class
    
    '''<summary>
    '''Represents the strongly named DataTable class.
    '''</summary>
    <Global.System.Serializable(),  _
     Global.System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")>  _
    Partial Public Class ResearchContractDataTable
        Inherits Global.System.Data.TypedTableBase(Of ResearchContractRow)
        
        Private columnContractID As Global.System.Data.DataColumn
        
        Private columnAccountManagerID As Global.System.Data.DataColumn
        
        Private columnClientID As Global.System.Data.DataColumn
        
        Private columnClientName As Global.System.Data.DataColumn
        
        Private columnClientBillingAddress As Global.System.Data.DataColumn
        
        Private columnContractNumber As Global.System.Data.DataColumn
        
        Private columnSigned As Global.System.Data.DataColumn
        
        Private columnSignDate As Global.System.Data.DataColumn
        
        Private columnSignedBy As Global.System.Data.DataColumn
        
        Private columnSpecialConditions As Global.System.Data.DataColumn
        
        Private columnProjectName As Global.System.Data.DataColumn
        
        Private columnApplyAgencyComm As Global.System.Data.DataColumn
        
        Private columnAgencyID As Global.System.Data.DataColumn
        
        Private columnAgencyName As Global.System.Data.DataColumn
        
        Private columnAgencyCommPercentage As Global.System.Data.DataColumn
        
        Private columnCancelled As Global.System.Data.DataColumn
        
        Private columnCancelDate As Global.System.Data.DataColumn
        
        Private columnCancelledBy As Global.System.Data.DataColumn
        
        Private columnCreatedBy As Global.System.Data.DataColumn
        
        Private columnCreationDate As Global.System.Data.DataColumn
        
        Private columnBillingInstructions As Global.System.Data.DataColumn
        
        Private columnContractNotes As Global.System.Data.DataColumn
        
        Private columnClassificationName As Global.System.Data.DataColumn
        
        Private columnPrintInfo As Global.System.Data.DataColumn
        
        Private columnFirstMonth As Global.System.Data.DataColumn
        
        Private columnLastMonth As Global.System.Data.DataColumn
        
        Private columnMonths As Global.System.Data.DataColumn
        
        Private columnResearchFee As Global.System.Data.DataColumn
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New()
            MyBase.New
            Me.TableName = "ResearchContract"
            Me.BeginInit
            Me.InitClass
            Me.EndInit
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub New(ByVal table As Global.System.Data.DataTable)
            MyBase.New
            Me.TableName = table.TableName
            If (table.CaseSensitive <> table.DataSet.CaseSensitive) Then
                Me.CaseSensitive = table.CaseSensitive
            End If
            If (table.Locale.ToString <> table.DataSet.Locale.ToString) Then
                Me.Locale = table.Locale
            End If
            If (table.Namespace <> table.DataSet.Namespace) Then
                Me.Namespace = table.Namespace
            End If
            Me.Prefix = table.Prefix
            Me.MinimumCapacity = table.MinimumCapacity
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Sub New(ByVal info As Global.System.Runtime.Serialization.SerializationInfo, ByVal context As Global.System.Runtime.Serialization.StreamingContext)
            MyBase.New(info, context)
            Me.InitVars
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ContractIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnContractID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property AccountManagerIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnAccountManagerID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ClientIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnClientID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ClientNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnClientName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ClientBillingAddressColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnClientBillingAddress
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ContractNumberColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnContractNumber
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property SignedColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnSigned
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property SignDateColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnSignDate
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property SignedByColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnSignedBy
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property SpecialConditionsColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnSpecialConditions
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ProjectNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnProjectName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ApplyAgencyCommColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnApplyAgencyComm
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property AgencyIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnAgencyID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property AgencyNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnAgencyName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property AgencyCommPercentageColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnAgencyCommPercentage
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property CancelledColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnCancelled
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property CancelDateColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnCancelDate
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property CancelledByColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnCancelledBy
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property CreatedByColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnCreatedBy
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property CreationDateColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnCreationDate
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property BillingInstructionsColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnBillingInstructions
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ContractNotesColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnContractNotes
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ClassificationNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnClassificationName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property PrintInfoColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnPrintInfo
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property FirstMonthColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnFirstMonth
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property LastMonthColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnLastMonth
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property MonthsColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnMonths
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ResearchFeeColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnResearchFee
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Browsable(false)>  _
        Public ReadOnly Property Count() As Integer
            Get
                Return Me.Rows.Count
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Default ReadOnly Property Item(ByVal index As Integer) As ResearchContractRow
            Get
                Return CType(Me.Rows(index),ResearchContractRow)
            End Get
        End Property
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ResearchContractRowChanging As ResearchContractRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ResearchContractRowChanged As ResearchContractRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ResearchContractRowDeleting As ResearchContractRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ResearchContractRowDeleted As ResearchContractRowChangeEventHandler
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overloads Sub AddResearchContractRow(ByVal row As ResearchContractRow)
            Me.Rows.Add(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overloads Function AddResearchContractRow( _
                    ByVal ContractID As System.Guid,  _
                    ByVal AccountManagerID As Integer,  _
                    ByVal ClientID As Integer,  _
                    ByVal ClientName As String,  _
                    ByVal ClientBillingAddress As String,  _
                    ByVal ContractNumber As String,  _
                    ByVal Signed As Boolean,  _
                    ByVal SignDate As Date,  _
                    ByVal SignedBy As String,  _
                    ByVal SpecialConditions As String,  _
                    ByVal ProjectName As String,  _
                    ByVal ApplyAgencyComm As Boolean,  _
                    ByVal AgencyID As Integer,  _
                    ByVal AgencyName As String,  _
                    ByVal AgencyCommPercentage As Decimal,  _
                    ByVal Cancelled As Boolean,  _
                    ByVal CancelDate As Date,  _
                    ByVal CancelledBy As String,  _
                    ByVal CreatedBy As String,  _
                    ByVal CreationDate As Date,  _
                    ByVal BillingInstructions As String,  _
                    ByVal ContractNotes As String,  _
                    ByVal ClassificationName As String,  _
                    ByVal PrintInfo As String,  _
                    ByVal FirstMonth As String,  _
                    ByVal LastMonth As String,  _
                    ByVal Months As Integer,  _
                    ByVal ResearchFee As Decimal) As ResearchContractRow
            Dim rowResearchContractRow As ResearchContractRow = CType(Me.NewRow,ResearchContractRow)
            Dim columnValuesArray() As Object = New Object() {ContractID, AccountManagerID, ClientID, ClientName, ClientBillingAddress, ContractNumber, Signed, SignDate, SignedBy, SpecialConditions, ProjectName, ApplyAgencyComm, AgencyID, AgencyName, AgencyCommPercentage, Cancelled, CancelDate, CancelledBy, CreatedBy, CreationDate, BillingInstructions, ContractNotes, ClassificationName, PrintInfo, FirstMonth, LastMonth, Months, ResearchFee}
            rowResearchContractRow.ItemArray = columnValuesArray
            Me.Rows.Add(rowResearchContractRow)
            Return rowResearchContractRow
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function FindByContractID(ByVal ContractID As System.Guid) As ResearchContractRow
            Return CType(Me.Rows.Find(New Object() {ContractID}),ResearchContractRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overrides Function Clone() As Global.System.Data.DataTable
            Dim cln As ResearchContractDataTable = CType(MyBase.Clone,ResearchContractDataTable)
            cln.InitVars
            Return cln
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function CreateInstance() As Global.System.Data.DataTable
            Return New ResearchContractDataTable()
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub InitVars()
            Me.columnContractID = MyBase.Columns("ContractID")
            Me.columnAccountManagerID = MyBase.Columns("AccountManagerID")
            Me.columnClientID = MyBase.Columns("ClientID")
            Me.columnClientName = MyBase.Columns("ClientName")
            Me.columnClientBillingAddress = MyBase.Columns("ClientBillingAddress")
            Me.columnContractNumber = MyBase.Columns("ContractNumber")
            Me.columnSigned = MyBase.Columns("Signed")
            Me.columnSignDate = MyBase.Columns("SignDate")
            Me.columnSignedBy = MyBase.Columns("SignedBy")
            Me.columnSpecialConditions = MyBase.Columns("SpecialConditions")
            Me.columnProjectName = MyBase.Columns("ProjectName")
            Me.columnApplyAgencyComm = MyBase.Columns("ApplyAgencyComm")
            Me.columnAgencyID = MyBase.Columns("AgencyID")
            Me.columnAgencyName = MyBase.Columns("AgencyName")
            Me.columnAgencyCommPercentage = MyBase.Columns("AgencyCommPercentage")
            Me.columnCancelled = MyBase.Columns("Cancelled")
            Me.columnCancelDate = MyBase.Columns("CancelDate")
            Me.columnCancelledBy = MyBase.Columns("CancelledBy")
            Me.columnCreatedBy = MyBase.Columns("CreatedBy")
            Me.columnCreationDate = MyBase.Columns("CreationDate")
            Me.columnBillingInstructions = MyBase.Columns("BillingInstructions")
            Me.columnContractNotes = MyBase.Columns("ContractNotes")
            Me.columnClassificationName = MyBase.Columns("ClassificationName")
            Me.columnPrintInfo = MyBase.Columns("PrintInfo")
            Me.columnFirstMonth = MyBase.Columns("FirstMonth")
            Me.columnLastMonth = MyBase.Columns("LastMonth")
            Me.columnMonths = MyBase.Columns("Months")
            Me.columnResearchFee = MyBase.Columns("ResearchFee")
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitClass()
            Me.columnContractID = New Global.System.Data.DataColumn("ContractID", GetType(Global.System.Guid), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnContractID)
            Me.columnAccountManagerID = New Global.System.Data.DataColumn("AccountManagerID", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnAccountManagerID)
            Me.columnClientID = New Global.System.Data.DataColumn("ClientID", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnClientID)
            Me.columnClientName = New Global.System.Data.DataColumn("ClientName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnClientName)
            Me.columnClientBillingAddress = New Global.System.Data.DataColumn("ClientBillingAddress", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnClientBillingAddress)
            Me.columnContractNumber = New Global.System.Data.DataColumn("ContractNumber", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnContractNumber)
            Me.columnSigned = New Global.System.Data.DataColumn("Signed", GetType(Boolean), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnSigned)
            Me.columnSignDate = New Global.System.Data.DataColumn("SignDate", GetType(Date), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnSignDate)
            Me.columnSignedBy = New Global.System.Data.DataColumn("SignedBy", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnSignedBy)
            Me.columnSpecialConditions = New Global.System.Data.DataColumn("SpecialConditions", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnSpecialConditions)
            Me.columnProjectName = New Global.System.Data.DataColumn("ProjectName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnProjectName)
            Me.columnApplyAgencyComm = New Global.System.Data.DataColumn("ApplyAgencyComm", GetType(Boolean), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnApplyAgencyComm)
            Me.columnAgencyID = New Global.System.Data.DataColumn("AgencyID", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnAgencyID)
            Me.columnAgencyName = New Global.System.Data.DataColumn("AgencyName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnAgencyName)
            Me.columnAgencyCommPercentage = New Global.System.Data.DataColumn("AgencyCommPercentage", GetType(Decimal), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnAgencyCommPercentage)
            Me.columnCancelled = New Global.System.Data.DataColumn("Cancelled", GetType(Boolean), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnCancelled)
            Me.columnCancelDate = New Global.System.Data.DataColumn("CancelDate", GetType(Date), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnCancelDate)
            Me.columnCancelledBy = New Global.System.Data.DataColumn("CancelledBy", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnCancelledBy)
            Me.columnCreatedBy = New Global.System.Data.DataColumn("CreatedBy", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnCreatedBy)
            Me.columnCreationDate = New Global.System.Data.DataColumn("CreationDate", GetType(Date), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnCreationDate)
            Me.columnBillingInstructions = New Global.System.Data.DataColumn("BillingInstructions", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnBillingInstructions)
            Me.columnContractNotes = New Global.System.Data.DataColumn("ContractNotes", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnContractNotes)
            Me.columnClassificationName = New Global.System.Data.DataColumn("ClassificationName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnClassificationName)
            Me.columnPrintInfo = New Global.System.Data.DataColumn("PrintInfo", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnPrintInfo)
            Me.columnFirstMonth = New Global.System.Data.DataColumn("FirstMonth", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnFirstMonth)
            Me.columnLastMonth = New Global.System.Data.DataColumn("LastMonth", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnLastMonth)
            Me.columnMonths = New Global.System.Data.DataColumn("Months", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnMonths)
            Me.columnResearchFee = New Global.System.Data.DataColumn("ResearchFee", GetType(Decimal), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnResearchFee)
            Me.Constraints.Add(New Global.System.Data.UniqueConstraint("Constraint1", New Global.System.Data.DataColumn() {Me.columnContractID}, true))
            Me.columnContractID.AllowDBNull = false
            Me.columnContractID.Unique = true
            Me.columnAccountManagerID.AllowDBNull = false
            Me.columnClientID.AllowDBNull = false
            Me.columnClientName.AllowDBNull = false
            Me.columnClientName.MaxLength = 250
            Me.columnClientBillingAddress.AllowDBNull = false
            Me.columnClientBillingAddress.MaxLength = 1000
            Me.columnContractNumber.AllowDBNull = false
            Me.columnContractNumber.MaxLength = 8
            Me.columnSigned.AllowDBNull = false
            Me.columnSignedBy.AllowDBNull = false
            Me.columnSignedBy.MaxLength = 50
            Me.columnSpecialConditions.AllowDBNull = false
            Me.columnSpecialConditions.MaxLength = 2000
            Me.columnProjectName.AllowDBNull = false
            Me.columnProjectName.MaxLength = 250
            Me.columnApplyAgencyComm.AllowDBNull = false
            Me.columnAgencyName.AllowDBNull = false
            Me.columnAgencyName.MaxLength = 250
            Me.columnAgencyCommPercentage.AllowDBNull = false
            Me.columnCancelled.AllowDBNull = false
            Me.columnCancelledBy.MaxLength = 50
            Me.columnCreatedBy.AllowDBNull = false
            Me.columnCreatedBy.MaxLength = 50
            Me.columnCreationDate.AllowDBNull = false
            Me.columnBillingInstructions.AllowDBNull = false
            Me.columnBillingInstructions.MaxLength = 2000
            Me.columnContractNotes.AllowDBNull = false
            Me.columnContractNotes.MaxLength = 2000
            Me.columnClassificationName.AllowDBNull = false
            Me.columnClassificationName.MaxLength = 50
            Me.columnPrintInfo.ReadOnly = true
            Me.columnPrintInfo.MaxLength = 222
            Me.columnFirstMonth.ReadOnly = true
            Me.columnFirstMonth.MaxLength = 35
            Me.columnLastMonth.ReadOnly = true
            Me.columnLastMonth.MaxLength = 35
            Me.columnMonths.ReadOnly = true
            Me.columnResearchFee.ReadOnly = true
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function NewResearchContractRow() As ResearchContractRow
            Return CType(Me.NewRow,ResearchContractRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function NewRowFromBuilder(ByVal builder As Global.System.Data.DataRowBuilder) As Global.System.Data.DataRow
            Return New ResearchContractRow(builder)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function GetRowType() As Global.System.Type
            Return GetType(ResearchContractRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowChanged(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanged(e)
            If (Not (Me.ResearchContractRowChangedEvent) Is Nothing) Then
                RaiseEvent ResearchContractRowChanged(Me, New ResearchContractRowChangeEvent(CType(e.Row,ResearchContractRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowChanging(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanging(e)
            If (Not (Me.ResearchContractRowChangingEvent) Is Nothing) Then
                RaiseEvent ResearchContractRowChanging(Me, New ResearchContractRowChangeEvent(CType(e.Row,ResearchContractRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowDeleted(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleted(e)
            If (Not (Me.ResearchContractRowDeletedEvent) Is Nothing) Then
                RaiseEvent ResearchContractRowDeleted(Me, New ResearchContractRowChangeEvent(CType(e.Row,ResearchContractRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowDeleting(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleting(e)
            If (Not (Me.ResearchContractRowDeletingEvent) Is Nothing) Then
                RaiseEvent ResearchContractRowDeleting(Me, New ResearchContractRowChangeEvent(CType(e.Row,ResearchContractRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub RemoveResearchContractRow(ByVal row As ResearchContractRow)
            Me.Rows.Remove(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Shared Function GetTypedTableSchema(ByVal xs As Global.System.Xml.Schema.XmlSchemaSet) As Global.System.Xml.Schema.XmlSchemaComplexType
            Dim type As Global.System.Xml.Schema.XmlSchemaComplexType = New Global.System.Xml.Schema.XmlSchemaComplexType()
            Dim sequence As Global.System.Xml.Schema.XmlSchemaSequence = New Global.System.Xml.Schema.XmlSchemaSequence()
            Dim ds As DataSetContractReport = New DataSetContractReport()
            Dim any1 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any1.Namespace = "http://www.w3.org/2001/XMLSchema"
            any1.MinOccurs = New Decimal(0)
            any1.MaxOccurs = Decimal.MaxValue
            any1.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any1)
            Dim any2 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1"
            any2.MinOccurs = New Decimal(1)
            any2.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any2)
            Dim attribute1 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute1.Name = "namespace"
            attribute1.FixedValue = ds.Namespace
            type.Attributes.Add(attribute1)
            Dim attribute2 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute2.Name = "tableTypeName"
            attribute2.FixedValue = "ResearchContractDataTable"
            type.Attributes.Add(attribute2)
            type.Particle = sequence
            Dim dsSchema As Global.System.Xml.Schema.XmlSchema = ds.GetSchemaSerializable
            If xs.Contains(dsSchema.TargetNamespace) Then
                Dim s1 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Dim s2 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Try 
                    Dim schema As Global.System.Xml.Schema.XmlSchema = Nothing
                    dsSchema.Write(s1)
                    Dim schemas As Global.System.Collections.IEnumerator = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator
                    Do While schemas.MoveNext
                        schema = CType(schemas.Current,Global.System.Xml.Schema.XmlSchema)
                        s2.SetLength(0)
                        schema.Write(s2)
                        If (s1.Length = s2.Length) Then
                            s1.Position = 0
                            s2.Position = 0
                            
                            Do While ((s1.Position <> s1.Length)  _
                                        AndAlso (s1.ReadByte = s2.ReadByte))
                                
                                
                            Loop
                            If (s1.Position = s1.Length) Then
                                Return type
                            End If
                        End If
                        
                    Loop
                Finally
                    If (Not (s1) Is Nothing) Then
                        s1.Close
                    End If
                    If (Not (s2) Is Nothing) Then
                        s2.Close
                    End If
                End Try
            End If
            xs.Add(dsSchema)
            Return type
        End Function
    End Class
    
    '''<summary>
    '''Represents the strongly named DataTable class.
    '''</summary>
    <Global.System.Serializable(),  _
     Global.System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")>  _
    Partial Public Class ResearchCategoryDataTable
        Inherits Global.System.Data.TypedTableBase(Of ResearchCategoryRow)
        
        Private columnResearchCategoryID As Global.System.Data.DataColumn
        
        Private columnContractID As Global.System.Data.DataColumn
        
        Private columnCategoryName As Global.System.Data.DataColumn
        
        Private columnFirstMonth As Global.System.Data.DataColumn
        
        Private columnLastMonth As Global.System.Data.DataColumn
        
        Private columnMonths As Global.System.Data.DataColumn
        
        Private columnFee As Global.System.Data.DataColumn
        
        Private columnDiscount As Global.System.Data.DataColumn
        
        Private columnDiscountAmount As Global.System.Data.DataColumn
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New()
            MyBase.New
            Me.TableName = "ResearchCategory"
            Me.BeginInit
            Me.InitClass
            Me.EndInit
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub New(ByVal table As Global.System.Data.DataTable)
            MyBase.New
            Me.TableName = table.TableName
            If (table.CaseSensitive <> table.DataSet.CaseSensitive) Then
                Me.CaseSensitive = table.CaseSensitive
            End If
            If (table.Locale.ToString <> table.DataSet.Locale.ToString) Then
                Me.Locale = table.Locale
            End If
            If (table.Namespace <> table.DataSet.Namespace) Then
                Me.Namespace = table.Namespace
            End If
            Me.Prefix = table.Prefix
            Me.MinimumCapacity = table.MinimumCapacity
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Sub New(ByVal info As Global.System.Runtime.Serialization.SerializationInfo, ByVal context As Global.System.Runtime.Serialization.StreamingContext)
            MyBase.New(info, context)
            Me.InitVars
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ResearchCategoryIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnResearchCategoryID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property ContractIDColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnContractID
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property CategoryNameColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnCategoryName
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property FirstMonthColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnFirstMonth
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property LastMonthColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnLastMonth
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property MonthsColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnMonths
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property FeeColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnFee
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property DiscountColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnDiscount
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property DiscountAmountColumn() As Global.System.Data.DataColumn
            Get
                Return Me.columnDiscountAmount
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Browsable(false)>  _
        Public ReadOnly Property Count() As Integer
            Get
                Return Me.Rows.Count
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Default ReadOnly Property Item(ByVal index As Integer) As ResearchCategoryRow
            Get
                Return CType(Me.Rows(index),ResearchCategoryRow)
            End Get
        End Property
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ResearchCategoryRowChanging As ResearchCategoryRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ResearchCategoryRowChanged As ResearchCategoryRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ResearchCategoryRowDeleting As ResearchCategoryRowChangeEventHandler
        
        <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Event ResearchCategoryRowDeleted As ResearchCategoryRowChangeEventHandler
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overloads Sub AddResearchCategoryRow(ByVal row As ResearchCategoryRow)
            Me.Rows.Add(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overloads Function AddResearchCategoryRow(ByVal ResearchCategoryID As System.Guid, ByVal parentResearchContractRowByResearchContract_ResearchCategory As ResearchContractRow, ByVal CategoryName As String, ByVal FirstMonth As String, ByVal LastMonth As String, ByVal Months As Integer, ByVal Fee As Decimal, ByVal Discount As Decimal, ByVal DiscountAmount As Decimal) As ResearchCategoryRow
            Dim rowResearchCategoryRow As ResearchCategoryRow = CType(Me.NewRow,ResearchCategoryRow)
            Dim columnValuesArray() As Object = New Object() {ResearchCategoryID, Nothing, CategoryName, FirstMonth, LastMonth, Months, Fee, Discount, DiscountAmount}
            If (Not (parentResearchContractRowByResearchContract_ResearchCategory) Is Nothing) Then
                columnValuesArray(1) = parentResearchContractRowByResearchContract_ResearchCategory(0)
            End If
            rowResearchCategoryRow.ItemArray = columnValuesArray
            Me.Rows.Add(rowResearchCategoryRow)
            Return rowResearchCategoryRow
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function FindByResearchCategoryID(ByVal ResearchCategoryID As System.Guid) As ResearchCategoryRow
            Return CType(Me.Rows.Find(New Object() {ResearchCategoryID}),ResearchCategoryRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Overrides Function Clone() As Global.System.Data.DataTable
            Dim cln As ResearchCategoryDataTable = CType(MyBase.Clone,ResearchCategoryDataTable)
            cln.InitVars
            Return cln
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function CreateInstance() As Global.System.Data.DataTable
            Return New ResearchCategoryDataTable()
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub InitVars()
            Me.columnResearchCategoryID = MyBase.Columns("ResearchCategoryID")
            Me.columnContractID = MyBase.Columns("ContractID")
            Me.columnCategoryName = MyBase.Columns("CategoryName")
            Me.columnFirstMonth = MyBase.Columns("FirstMonth")
            Me.columnLastMonth = MyBase.Columns("LastMonth")
            Me.columnMonths = MyBase.Columns("Months")
            Me.columnFee = MyBase.Columns("Fee")
            Me.columnDiscount = MyBase.Columns("Discount")
            Me.columnDiscountAmount = MyBase.Columns("DiscountAmount")
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitClass()
            Me.columnResearchCategoryID = New Global.System.Data.DataColumn("ResearchCategoryID", GetType(Global.System.Guid), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnResearchCategoryID)
            Me.columnContractID = New Global.System.Data.DataColumn("ContractID", GetType(Global.System.Guid), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnContractID)
            Me.columnCategoryName = New Global.System.Data.DataColumn("CategoryName", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnCategoryName)
            Me.columnFirstMonth = New Global.System.Data.DataColumn("FirstMonth", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnFirstMonth)
            Me.columnLastMonth = New Global.System.Data.DataColumn("LastMonth", GetType(String), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnLastMonth)
            Me.columnMonths = New Global.System.Data.DataColumn("Months", GetType(Integer), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnMonths)
            Me.columnFee = New Global.System.Data.DataColumn("Fee", GetType(Decimal), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnFee)
            Me.columnDiscount = New Global.System.Data.DataColumn("Discount", GetType(Decimal), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnDiscount)
            Me.columnDiscountAmount = New Global.System.Data.DataColumn("DiscountAmount", GetType(Decimal), Nothing, Global.System.Data.MappingType.Element)
            MyBase.Columns.Add(Me.columnDiscountAmount)
            Me.Constraints.Add(New Global.System.Data.UniqueConstraint("Constraint1", New Global.System.Data.DataColumn() {Me.columnResearchCategoryID}, true))
            Me.columnResearchCategoryID.AllowDBNull = false
            Me.columnResearchCategoryID.Unique = true
            Me.columnContractID.AllowDBNull = false
            Me.columnCategoryName.AllowDBNull = false
            Me.columnCategoryName.MaxLength = 200
            Me.columnFirstMonth.ReadOnly = true
            Me.columnFirstMonth.MaxLength = 35
            Me.columnLastMonth.ReadOnly = true
            Me.columnLastMonth.MaxLength = 35
            Me.columnMonths.AllowDBNull = false
            Me.columnFee.AllowDBNull = false
            Me.columnDiscount.AllowDBNull = false
            Me.columnDiscountAmount.ReadOnly = true
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function NewResearchCategoryRow() As ResearchCategoryRow
            Return CType(Me.NewRow,ResearchCategoryRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function NewRowFromBuilder(ByVal builder As Global.System.Data.DataRowBuilder) As Global.System.Data.DataRow
            Return New ResearchCategoryRow(builder)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Function GetRowType() As Global.System.Type
            Return GetType(ResearchCategoryRow)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowChanged(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanged(e)
            If (Not (Me.ResearchCategoryRowChangedEvent) Is Nothing) Then
                RaiseEvent ResearchCategoryRowChanged(Me, New ResearchCategoryRowChangeEvent(CType(e.Row,ResearchCategoryRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowChanging(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowChanging(e)
            If (Not (Me.ResearchCategoryRowChangingEvent) Is Nothing) Then
                RaiseEvent ResearchCategoryRowChanging(Me, New ResearchCategoryRowChangeEvent(CType(e.Row,ResearchCategoryRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowDeleted(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleted(e)
            If (Not (Me.ResearchCategoryRowDeletedEvent) Is Nothing) Then
                RaiseEvent ResearchCategoryRowDeleted(Me, New ResearchCategoryRowChangeEvent(CType(e.Row,ResearchCategoryRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected Overrides Sub OnRowDeleting(ByVal e As Global.System.Data.DataRowChangeEventArgs)
            MyBase.OnRowDeleting(e)
            If (Not (Me.ResearchCategoryRowDeletingEvent) Is Nothing) Then
                RaiseEvent ResearchCategoryRowDeleting(Me, New ResearchCategoryRowChangeEvent(CType(e.Row,ResearchCategoryRow), e.Action))
            End If
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub RemoveResearchCategoryRow(ByVal row As ResearchCategoryRow)
            Me.Rows.Remove(row)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Shared Function GetTypedTableSchema(ByVal xs As Global.System.Xml.Schema.XmlSchemaSet) As Global.System.Xml.Schema.XmlSchemaComplexType
            Dim type As Global.System.Xml.Schema.XmlSchemaComplexType = New Global.System.Xml.Schema.XmlSchemaComplexType()
            Dim sequence As Global.System.Xml.Schema.XmlSchemaSequence = New Global.System.Xml.Schema.XmlSchemaSequence()
            Dim ds As DataSetContractReport = New DataSetContractReport()
            Dim any1 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any1.Namespace = "http://www.w3.org/2001/XMLSchema"
            any1.MinOccurs = New Decimal(0)
            any1.MaxOccurs = Decimal.MaxValue
            any1.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any1)
            Dim any2 As Global.System.Xml.Schema.XmlSchemaAny = New Global.System.Xml.Schema.XmlSchemaAny()
            any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1"
            any2.MinOccurs = New Decimal(1)
            any2.ProcessContents = Global.System.Xml.Schema.XmlSchemaContentProcessing.Lax
            sequence.Items.Add(any2)
            Dim attribute1 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute1.Name = "namespace"
            attribute1.FixedValue = ds.Namespace
            type.Attributes.Add(attribute1)
            Dim attribute2 As Global.System.Xml.Schema.XmlSchemaAttribute = New Global.System.Xml.Schema.XmlSchemaAttribute()
            attribute2.Name = "tableTypeName"
            attribute2.FixedValue = "ResearchCategoryDataTable"
            type.Attributes.Add(attribute2)
            type.Particle = sequence
            Dim dsSchema As Global.System.Xml.Schema.XmlSchema = ds.GetSchemaSerializable
            If xs.Contains(dsSchema.TargetNamespace) Then
                Dim s1 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Dim s2 As Global.System.IO.MemoryStream = New Global.System.IO.MemoryStream()
                Try 
                    Dim schema As Global.System.Xml.Schema.XmlSchema = Nothing
                    dsSchema.Write(s1)
                    Dim schemas As Global.System.Collections.IEnumerator = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator
                    Do While schemas.MoveNext
                        schema = CType(schemas.Current,Global.System.Xml.Schema.XmlSchema)
                        s2.SetLength(0)
                        schema.Write(s2)
                        If (s1.Length = s2.Length) Then
                            s1.Position = 0
                            s2.Position = 0
                            
                            Do While ((s1.Position <> s1.Length)  _
                                        AndAlso (s1.ReadByte = s2.ReadByte))
                                
                                
                            Loop
                            If (s1.Position = s1.Length) Then
                                Return type
                            End If
                        End If
                        
                    Loop
                Finally
                    If (Not (s1) Is Nothing) Then
                        s1.Close
                    End If
                    If (Not (s2) Is Nothing) Then
                        s2.Close
                    End If
                End Try
            End If
            xs.Add(dsSchema)
            Return type
        End Function
    End Class
    
    '''<summary>
    '''Represents strongly named DataRow class.
    '''</summary>
    Partial Public Class ContractRow
        Inherits Global.System.Data.DataRow
        
        Private tableContract As ContractDataTable
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub New(ByVal rb As Global.System.Data.DataRowBuilder)
            MyBase.New(rb)
            Me.tableContract = CType(Me.Table,ContractDataTable)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ContractID() As System.Guid
            Get
                Return CType(Me(Me.tableContract.ContractIDColumn),Global.System.Guid)
            End Get
            Set
                Me(Me.tableContract.ContractIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property AccountManagerID() As Integer
            Get
                Return CType(Me(Me.tableContract.AccountManagerIDColumn),Integer)
            End Get
            Set
                Me(Me.tableContract.AccountManagerIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ClientID() As Integer
            Get
                Return CType(Me(Me.tableContract.ClientIDColumn),Integer)
            End Get
            Set
                Me(Me.tableContract.ClientIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ClientName() As String
            Get
                Return CType(Me(Me.tableContract.ClientNameColumn),String)
            End Get
            Set
                Me(Me.tableContract.ClientNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ClientBillingAddress() As String
            Get
                Return CType(Me(Me.tableContract.ClientBillingAddressColumn),String)
            End Get
            Set
                Me(Me.tableContract.ClientBillingAddressColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ContractNumber() As String
            Get
                Return CType(Me(Me.tableContract.ContractNumberColumn),String)
            End Get
            Set
                Me(Me.tableContract.ContractNumberColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property Signed() As Boolean
            Get
                Return CType(Me(Me.tableContract.SignedColumn),Boolean)
            End Get
            Set
                Me(Me.tableContract.SignedColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property SignDate() As Date
            Get
                Try 
                    Return CType(Me(Me.tableContract.SignDateColumn),Date)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'SignDate' in table 'Contract' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableContract.SignDateColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property SignedBy() As String
            Get
                Return CType(Me(Me.tableContract.SignedByColumn),String)
            End Get
            Set
                Me(Me.tableContract.SignedByColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property SpecialConditions() As String
            Get
                Return CType(Me(Me.tableContract.SpecialConditionsColumn),String)
            End Get
            Set
                Me(Me.tableContract.SpecialConditionsColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ProjectName() As String
            Get
                Return CType(Me(Me.tableContract.ProjectNameColumn),String)
            End Get
            Set
                Me(Me.tableContract.ProjectNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ApplyAgencyComm() As Boolean
            Get
                Return CType(Me(Me.tableContract.ApplyAgencyCommColumn),Boolean)
            End Get
            Set
                Me(Me.tableContract.ApplyAgencyCommColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property AgencyID() As Integer
            Get
                Try 
                    Return CType(Me(Me.tableContract.AgencyIDColumn),Integer)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'AgencyID' in table 'Contract' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableContract.AgencyIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property AgencyName() As String
            Get
                Return CType(Me(Me.tableContract.AgencyNameColumn),String)
            End Get
            Set
                Me(Me.tableContract.AgencyNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property AgencyCommPercentage() As Decimal
            Get
                Return CType(Me(Me.tableContract.AgencyCommPercentageColumn),Decimal)
            End Get
            Set
                Me(Me.tableContract.AgencyCommPercentageColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property Cancelled() As Boolean
            Get
                Return CType(Me(Me.tableContract.CancelledColumn),Boolean)
            End Get
            Set
                Me(Me.tableContract.CancelledColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property CancelDate() As Date
            Get
                Try 
                    Return CType(Me(Me.tableContract.CancelDateColumn),Date)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'CancelDate' in table 'Contract' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableContract.CancelDateColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property CancelledBy() As String
            Get
                Try 
                    Return CType(Me(Me.tableContract.CancelledByColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'CancelledBy' in table 'Contract' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableContract.CancelledByColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property CreatedBy() As String
            Get
                Return CType(Me(Me.tableContract.CreatedByColumn),String)
            End Get
            Set
                Me(Me.tableContract.CreatedByColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property CreationDate() As Date
            Get
                Return CType(Me(Me.tableContract.CreationDateColumn),Date)
            End Get
            Set
                Me(Me.tableContract.CreationDateColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property BillingInstructions() As String
            Get
                Return CType(Me(Me.tableContract.BillingInstructionsColumn),String)
            End Get
            Set
                Me(Me.tableContract.BillingInstructionsColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ContractNotes() As String
            Get
                Return CType(Me(Me.tableContract.ContractNotesColumn),String)
            End Get
            Set
                Me(Me.tableContract.ContractNotesColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ClassificationName() As String
            Get
                Return CType(Me(Me.tableContract.ClassificationNameColumn),String)
            End Get
            Set
                Me(Me.tableContract.ClassificationNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property Weeks() As Integer
            Get
                Try 
                    Return CType(Me(Me.tableContract.WeeksColumn),Integer)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'Weeks' in table 'Contract' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableContract.WeeksColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property FirstWeek() As Date
            Get
                Try 
                    Return CType(Me(Me.tableContract.FirstWeekColumn),Date)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'FirstWeek' in table 'Contract' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableContract.FirstWeekColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property LastWeek() As Date
            Get
                Try 
                    Return CType(Me(Me.tableContract.LastWeekColumn),Date)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'LastWeek' in table 'Contract' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableContract.LastWeekColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property PrintInfo() As String
            Get
                Try 
                    Return CType(Me(Me.tableContract.PrintInfoColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'PrintInfo' in table 'Contract' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableContract.PrintInfoColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property NetRental() As Decimal
            Get
                Try 
                    Return CType(Me(Me.tableContract.NetRentalColumn),Decimal)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'NetRental' in table 'Contract' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableContract.NetRentalColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property Production() As Decimal
            Get
                Try 
                    Return CType(Me(Me.tableContract.ProductionColumn),Decimal)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'Production' in table 'Contract' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableContract.ProductionColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property TerminationDate() As Date
            Get
                Try 
                    Return CType(Me(Me.tableContract.TerminationDateColumn),Date)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'TerminationDate' in table 'Contract' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableContract.TerminationDateColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property DiscountAmount() As Decimal
            Get
                Try 
                    Return CType(Me(Me.tableContract.DiscountAmountColumn),Decimal)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'DiscountAmount' in table 'Contract' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableContract.DiscountAmountColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property PrintAgencyComm() As Boolean
            Get
                Return CType(Me(Me.tableContract.PrintAgencyCommColumn),Boolean)
            End Get
            Set
                Me(Me.tableContract.PrintAgencyCommColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property AgencyCommAmount() As Decimal
            Get
                Try 
                    Return CType(Me(Me.tableContract.AgencyCommAmountColumn),Decimal)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'AgencyCommAmount' in table 'Contract' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableContract.AgencyCommAmountColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ClientAccountManagerCode() As String
            Get
                Try 
                    Return CType(Me(Me.tableContract.ClientAccountManagerCodeColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'ClientAccountManagerCode' in table 'Contract' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableContract.ClientAccountManagerCodeColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsSignDateNull() As Boolean
            Return Me.IsNull(Me.tableContract.SignDateColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetSignDateNull()
            Me(Me.tableContract.SignDateColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsAgencyIDNull() As Boolean
            Return Me.IsNull(Me.tableContract.AgencyIDColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetAgencyIDNull()
            Me(Me.tableContract.AgencyIDColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsCancelDateNull() As Boolean
            Return Me.IsNull(Me.tableContract.CancelDateColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetCancelDateNull()
            Me(Me.tableContract.CancelDateColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsCancelledByNull() As Boolean
            Return Me.IsNull(Me.tableContract.CancelledByColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetCancelledByNull()
            Me(Me.tableContract.CancelledByColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsWeeksNull() As Boolean
            Return Me.IsNull(Me.tableContract.WeeksColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetWeeksNull()
            Me(Me.tableContract.WeeksColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsFirstWeekNull() As Boolean
            Return Me.IsNull(Me.tableContract.FirstWeekColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetFirstWeekNull()
            Me(Me.tableContract.FirstWeekColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsLastWeekNull() As Boolean
            Return Me.IsNull(Me.tableContract.LastWeekColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetLastWeekNull()
            Me(Me.tableContract.LastWeekColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsPrintInfoNull() As Boolean
            Return Me.IsNull(Me.tableContract.PrintInfoColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetPrintInfoNull()
            Me(Me.tableContract.PrintInfoColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsNetRentalNull() As Boolean
            Return Me.IsNull(Me.tableContract.NetRentalColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetNetRentalNull()
            Me(Me.tableContract.NetRentalColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsProductionNull() As Boolean
            Return Me.IsNull(Me.tableContract.ProductionColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetProductionNull()
            Me(Me.tableContract.ProductionColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsTerminationDateNull() As Boolean
            Return Me.IsNull(Me.tableContract.TerminationDateColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetTerminationDateNull()
            Me(Me.tableContract.TerminationDateColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsDiscountAmountNull() As Boolean
            Return Me.IsNull(Me.tableContract.DiscountAmountColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetDiscountAmountNull()
            Me(Me.tableContract.DiscountAmountColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsAgencyCommAmountNull() As Boolean
            Return Me.IsNull(Me.tableContract.AgencyCommAmountColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetAgencyCommAmountNull()
            Me(Me.tableContract.AgencyCommAmountColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsClientAccountManagerCodeNull() As Boolean
            Return Me.IsNull(Me.tableContract.ClientAccountManagerCodeColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetClientAccountManagerCodeNull()
            Me(Me.tableContract.ClientAccountManagerCodeColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function GetBillingInstructionRows() As BillingInstructionRow()
            If (Me.Table.ChildRelations("Contract_PurchaseOrderNumber") Is Nothing) Then
                Return New BillingInstructionRow(-1) {}
            Else
                Return CType(MyBase.GetChildRows(Me.Table.ChildRelations("Contract_PurchaseOrderNumber")),BillingInstructionRow())
            End If
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function GetBurstRows() As BurstRow()
            If (Me.Table.ChildRelations("Contract_Burst") Is Nothing) Then
                Return New BurstRow(-1) {}
            Else
                Return CType(MyBase.GetChildRows(Me.Table.ChildRelations("Contract_Burst")),BurstRow())
            End If
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function GetContractInventoryRows() As ContractInventoryRow()
            If (Me.Table.ChildRelations("Contract_ContractInventory") Is Nothing) Then
                Return New ContractInventoryRow(-1) {}
            Else
                Return CType(MyBase.GetChildRows(Me.Table.ChildRelations("Contract_ContractInventory")),ContractInventoryRow())
            End If
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function GetMiscellaneousChargeRows() As MiscellaneousChargeRow()
            If (Me.Table.ChildRelations("Contract_MiscellaneousCharge") Is Nothing) Then
                Return New MiscellaneousChargeRow(-1) {}
            Else
                Return CType(MyBase.GetChildRows(Me.Table.ChildRelations("Contract_MiscellaneousCharge")),MiscellaneousChargeRow())
            End If
        End Function
    End Class
    
    '''<summary>
    '''Represents strongly named DataRow class.
    '''</summary>
    Partial Public Class BillingInstructionRow
        Inherits Global.System.Data.DataRow
        
        Private tableBillingInstruction As BillingInstructionDataTable
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub New(ByVal rb As Global.System.Data.DataRowBuilder)
            MyBase.New(rb)
            Me.tableBillingInstruction = CType(Me.Table,BillingInstructionDataTable)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ContractID() As System.Guid
            Get
                Return CType(Me(Me.tableBillingInstruction.ContractIDColumn),Global.System.Guid)
            End Get
            Set
                Me(Me.tableBillingInstruction.ContractIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property PeriodName() As String
            Get
                Return CType(Me(Me.tableBillingInstruction.PeriodNameColumn),String)
            End Get
            Set
                Me(Me.tableBillingInstruction.PeriodNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property Amount() As Decimal
            Get
                Return CType(Me(Me.tableBillingInstruction.AmountColumn),Decimal)
            End Get
            Set
                Me(Me.tableBillingInstruction.AmountColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property PONumber() As String
            Get
                Return CType(Me(Me.tableBillingInstruction.PONumberColumn),String)
            End Get
            Set
                Me(Me.tableBillingInstruction.PONumberColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ContractRow() As ContractRow
            Get
                Return CType(Me.GetParentRow(Me.Table.ParentRelations("Contract_PurchaseOrderNumber")),ContractRow)
            End Get
            Set
                Me.SetParentRow(value, Me.Table.ParentRelations("Contract_PurchaseOrderNumber"))
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ResearchContractRow() As ResearchContractRow
            Get
                Return CType(Me.GetParentRow(Me.Table.ParentRelations("ResearchContract_PurchaseOrderNumber")),ResearchContractRow)
            End Get
            Set
                Me.SetParentRow(value, Me.Table.ParentRelations("ResearchContract_PurchaseOrderNumber"))
            End Set
        End Property
    End Class
    
    '''<summary>
    '''Represents strongly named DataRow class.
    '''</summary>
    Partial Public Class BurstRow
        Inherits Global.System.Data.DataRow
        
        Private tableBurst As BurstDataTable
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub New(ByVal rb As Global.System.Data.DataRowBuilder)
            MyBase.New(rb)
            Me.tableBurst = CType(Me.Table,BurstDataTable)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property BurstID() As System.Guid
            Get
                Return CType(Me(Me.tableBurst.BurstIDColumn),Global.System.Guid)
            End Get
            Set
                Me(Me.tableBurst.BurstIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ContractID() As System.Guid
            Get
                Return CType(Me(Me.tableBurst.ContractIDColumn),Global.System.Guid)
            End Get
            Set
                Me(Me.tableBurst.ContractIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ChainID() As Integer
            Get
                Return CType(Me(Me.tableBurst.ChainIDColumn),Integer)
            End Get
            Set
                Me(Me.tableBurst.ChainIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ChainName() As String
            Get
                Return CType(Me(Me.tableBurst.ChainNameColumn),String)
            End Get
            Set
                Me(Me.tableBurst.ChainNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property StorePoolID() As System.Guid
            Get
                Return CType(Me(Me.tableBurst.StorePoolIDColumn),Global.System.Guid)
            End Get
            Set
                Me(Me.tableBurst.StorePoolIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property MediaID() As Integer
            Get
                Return CType(Me(Me.tableBurst.MediaIDColumn),Integer)
            End Get
            Set
                Me(Me.tableBurst.MediaIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property MediaName() As String
            Get
                Return CType(Me(Me.tableBurst.MediaNameColumn),String)
            End Get
            Set
                Me(Me.tableBurst.MediaNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property BrandID() As System.Guid
            Get
                Return CType(Me(Me.tableBurst.BrandIDColumn),Global.System.Guid)
            End Get
            Set
                Me(Me.tableBurst.BrandIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property BrandName() As String
            Get
                Return CType(Me(Me.tableBurst.BrandNameColumn),String)
            End Get
            Set
                Me(Me.tableBurst.BrandNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ProductName() As String
            Get
                Return CType(Me(Me.tableBurst.ProductNameColumn),String)
            End Get
            Set
                Me(Me.tableBurst.ProductNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property FirstWeek() As Date
            Get
                Return CType(Me(Me.tableBurst.FirstWeekColumn),Date)
            End Get
            Set
                Me(Me.tableBurst.FirstWeekColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property InstallWeeks() As Integer
            Get
                Return CType(Me(Me.tableBurst.InstallWeeksColumn),Integer)
            End Get
            Set
                Me(Me.tableBurst.InstallWeeksColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property InstallStoreQty() As Integer
            Get
                Return CType(Me(Me.tableBurst.InstallStoreQtyColumn),Integer)
            End Get
            Set
                Me(Me.tableBurst.InstallStoreQtyColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property BillableStoreQty() As Integer
            Get
                Return CType(Me(Me.tableBurst.BillableStoreQtyColumn),Integer)
            End Get
            Set
                Me(Me.tableBurst.BillableStoreQtyColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property RentalRate() As Decimal
            Get
                Return CType(Me(Me.tableBurst.RentalRateColumn),Decimal)
            End Get
            Set
                Me(Me.tableBurst.RentalRateColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property BillableWeeks() As Integer
            Get
                Return CType(Me(Me.tableBurst.BillableWeeksColumn),Integer)
            End Get
            Set
                Me(Me.tableBurst.BillableWeeksColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property Discount() As Decimal
            Get
                Return CType(Me(Me.tableBurst.DiscountColumn),Decimal)
            End Get
            Set
                Me(Me.tableBurst.DiscountColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property CrossoverQty() As Integer
            Get
                Return CType(Me(Me.tableBurst.CrossoverQtyColumn),Integer)
            End Get
            Set
                Me(Me.tableBurst.CrossoverQtyColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property InstallAtHomesite() As Boolean
            Get
                Return CType(Me(Me.tableBurst.InstallAtHomesiteColumn),Boolean)
            End Get
            Set
                Me(Me.tableBurst.InstallAtHomesiteColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property InstallationInstructions() As String
            Get
                Return CType(Me(Me.tableBurst.InstallationInstructionsColumn),String)
            End Get
            Set
                Me(Me.tableBurst.InstallationInstructionsColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property StoreListConfirmed() As Boolean
            Get
                Return CType(Me(Me.tableBurst.StoreListConfirmedColumn),Boolean)
            End Get
            Set
                Me(Me.tableBurst.StoreListConfirmedColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property CreatedBy() As String
            Get
                Return CType(Me(Me.tableBurst.CreatedByColumn),String)
            End Get
            Set
                Me(Me.tableBurst.CreatedByColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property CreationDate() As Date
            Get
                Return CType(Me(Me.tableBurst.CreationDateColumn),Date)
            End Get
            Set
                Me(Me.tableBurst.CreationDateColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property LastWeek() As Date
            Get
                Try 
                    Return CType(Me(Me.tableBurst.LastWeekColumn),Date)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'LastWeek' in table 'Burst' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableBurst.LastWeekColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property LoadingPercentage() As Decimal
            Get
                Try 
                    Return CType(Me(Me.tableBurst.LoadingPercentageColumn),Decimal)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'LoadingPercentage' in table 'Burst' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableBurst.LoadingPercentageColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property LoadingAmount() As Decimal
            Get
                Try 
                    Return CType(Me(Me.tableBurst.LoadingAmountColumn),Decimal)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'LoadingAmount' in table 'Burst' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableBurst.LoadingAmountColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property TotalFreeStores() As Integer
            Get
                Try 
                    Return CType(Me(Me.tableBurst.TotalFreeStoresColumn),Integer)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'TotalFreeStores' in table 'Burst' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableBurst.TotalFreeStoresColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property Homesite() As String
            Get
                Try 
                    Return CType(Me(Me.tableBurst.HomesiteColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'Homesite' in table 'Burst' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableBurst.HomesiteColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property GroupDefinition() As String
            Get
                Try 
                    Return CType(Me(Me.tableBurst.GroupDefinitionColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'GroupDefinition' in table 'Burst' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableBurst.GroupDefinitionColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property DiscountAmount() As Decimal
            Get
                Try 
                    Return CType(Me(Me.tableBurst.DiscountAmountColumn),Decimal)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'DiscountAmount' in table 'Burst' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableBurst.DiscountAmountColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property TotalFreeWeeks() As Integer
            Get
                Try 
                    Return CType(Me(Me.tableBurst.TotalFreeWeeksColumn),Integer)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'TotalFreeWeeks' in table 'Burst' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableBurst.TotalFreeWeeksColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ContractRow() As ContractRow
            Get
                Return CType(Me.GetParentRow(Me.Table.ParentRelations("Contract_Burst")),ContractRow)
            End Get
            Set
                Me.SetParentRow(value, Me.Table.ParentRelations("Contract_Burst"))
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsLastWeekNull() As Boolean
            Return Me.IsNull(Me.tableBurst.LastWeekColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetLastWeekNull()
            Me(Me.tableBurst.LastWeekColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsLoadingPercentageNull() As Boolean
            Return Me.IsNull(Me.tableBurst.LoadingPercentageColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetLoadingPercentageNull()
            Me(Me.tableBurst.LoadingPercentageColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsLoadingAmountNull() As Boolean
            Return Me.IsNull(Me.tableBurst.LoadingAmountColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetLoadingAmountNull()
            Me(Me.tableBurst.LoadingAmountColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsTotalFreeStoresNull() As Boolean
            Return Me.IsNull(Me.tableBurst.TotalFreeStoresColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetTotalFreeStoresNull()
            Me(Me.tableBurst.TotalFreeStoresColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsHomesiteNull() As Boolean
            Return Me.IsNull(Me.tableBurst.HomesiteColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetHomesiteNull()
            Me(Me.tableBurst.HomesiteColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsGroupDefinitionNull() As Boolean
            Return Me.IsNull(Me.tableBurst.GroupDefinitionColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetGroupDefinitionNull()
            Me(Me.tableBurst.GroupDefinitionColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsDiscountAmountNull() As Boolean
            Return Me.IsNull(Me.tableBurst.DiscountAmountColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetDiscountAmountNull()
            Me(Me.tableBurst.DiscountAmountColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsTotalFreeWeeksNull() As Boolean
            Return Me.IsNull(Me.tableBurst.TotalFreeWeeksColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetTotalFreeWeeksNull()
            Me(Me.tableBurst.TotalFreeWeeksColumn) = Global.System.Convert.DBNull
        End Sub
    End Class
    
    '''<summary>
    '''Represents strongly named DataRow class.
    '''</summary>
    Partial Public Class ContractInventoryRow
        Inherits Global.System.Data.DataRow
        
        Private tableContractInventory As ContractInventoryDataTable
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub New(ByVal rb As Global.System.Data.DataRowBuilder)
            MyBase.New(rb)
            Me.tableContractInventory = CType(Me.Table,ContractInventoryDataTable)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ContractID() As System.Guid
            Get
                Return CType(Me(Me.tableContractInventory.ContractIDColumn),Global.System.Guid)
            End Get
            Set
                Me(Me.tableContractInventory.ContractIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ItemName() As String
            Get
                Return CType(Me(Me.tableContractInventory.ItemNameColumn),String)
            End Get
            Set
                Me(Me.tableContractInventory.ItemNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ItemQty() As Integer
            Get
                Return CType(Me(Me.tableContractInventory.ItemQtyColumn),Integer)
            End Get
            Set
                Me(Me.tableContractInventory.ItemQtyColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property SellPrice() As Decimal
            Get
                Return CType(Me(Me.tableContractInventory.SellPriceColumn),Decimal)
            End Get
            Set
                Me(Me.tableContractInventory.SellPriceColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ContractRow() As ContractRow
            Get
                Return CType(Me.GetParentRow(Me.Table.ParentRelations("Contract_ContractInventory")),ContractRow)
            End Get
            Set
                Me.SetParentRow(value, Me.Table.ParentRelations("Contract_ContractInventory"))
            End Set
        End Property
    End Class
    
    '''<summary>
    '''Represents strongly named DataRow class.
    '''</summary>
    Partial Public Class MiscellaneousChargeRow
        Inherits Global.System.Data.DataRow
        
        Private tableMiscellaneousCharge As MiscellaneousChargeDataTable
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub New(ByVal rb As Global.System.Data.DataRowBuilder)
            MyBase.New(rb)
            Me.tableMiscellaneousCharge = CType(Me.Table,MiscellaneousChargeDataTable)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ContractID() As System.Guid
            Get
                Return CType(Me(Me.tableMiscellaneousCharge.ContractIDColumn),Global.System.Guid)
            End Get
            Set
                Me(Me.tableMiscellaneousCharge.ContractIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property MiscellaneousChargeName() As String
            Get
                Return CType(Me(Me.tableMiscellaneousCharge.MiscellaneousChargeNameColumn),String)
            End Get
            Set
                Me(Me.tableMiscellaneousCharge.MiscellaneousChargeNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property MiscellaneousChargeAmount() As Decimal
            Get
                Return CType(Me(Me.tableMiscellaneousCharge.MiscellaneousChargeAmountColumn),Decimal)
            End Get
            Set
                Me(Me.tableMiscellaneousCharge.MiscellaneousChargeAmountColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ContractRow() As ContractRow
            Get
                Return CType(Me.GetParentRow(Me.Table.ParentRelations("Contract_MiscellaneousCharge")),ContractRow)
            End Get
            Set
                Me.SetParentRow(value, Me.Table.ParentRelations("Contract_MiscellaneousCharge"))
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ResearchContractRow() As ResearchContractRow
            Get
                Return CType(Me.GetParentRow(Me.Table.ParentRelations("ResearchContract_MiscellaneousCharge")),ResearchContractRow)
            End Get
            Set
                Me.SetParentRow(value, Me.Table.ParentRelations("ResearchContract_MiscellaneousCharge"))
            End Set
        End Property
    End Class
    
    '''<summary>
    '''Represents strongly named DataRow class.
    '''</summary>
    Partial Public Class ResearchContractRow
        Inherits Global.System.Data.DataRow
        
        Private tableResearchContract As ResearchContractDataTable
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub New(ByVal rb As Global.System.Data.DataRowBuilder)
            MyBase.New(rb)
            Me.tableResearchContract = CType(Me.Table,ResearchContractDataTable)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ContractID() As System.Guid
            Get
                Return CType(Me(Me.tableResearchContract.ContractIDColumn),Global.System.Guid)
            End Get
            Set
                Me(Me.tableResearchContract.ContractIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property AccountManagerID() As Integer
            Get
                Return CType(Me(Me.tableResearchContract.AccountManagerIDColumn),Integer)
            End Get
            Set
                Me(Me.tableResearchContract.AccountManagerIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ClientID() As Integer
            Get
                Return CType(Me(Me.tableResearchContract.ClientIDColumn),Integer)
            End Get
            Set
                Me(Me.tableResearchContract.ClientIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ClientName() As String
            Get
                Return CType(Me(Me.tableResearchContract.ClientNameColumn),String)
            End Get
            Set
                Me(Me.tableResearchContract.ClientNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ClientBillingAddress() As String
            Get
                Return CType(Me(Me.tableResearchContract.ClientBillingAddressColumn),String)
            End Get
            Set
                Me(Me.tableResearchContract.ClientBillingAddressColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ContractNumber() As String
            Get
                Return CType(Me(Me.tableResearchContract.ContractNumberColumn),String)
            End Get
            Set
                Me(Me.tableResearchContract.ContractNumberColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property Signed() As Boolean
            Get
                Return CType(Me(Me.tableResearchContract.SignedColumn),Boolean)
            End Get
            Set
                Me(Me.tableResearchContract.SignedColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property SignDate() As Date
            Get
                Try 
                    Return CType(Me(Me.tableResearchContract.SignDateColumn),Date)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'SignDate' in table 'ResearchContract' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableResearchContract.SignDateColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property SignedBy() As String
            Get
                Return CType(Me(Me.tableResearchContract.SignedByColumn),String)
            End Get
            Set
                Me(Me.tableResearchContract.SignedByColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property SpecialConditions() As String
            Get
                Return CType(Me(Me.tableResearchContract.SpecialConditionsColumn),String)
            End Get
            Set
                Me(Me.tableResearchContract.SpecialConditionsColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ProjectName() As String
            Get
                Return CType(Me(Me.tableResearchContract.ProjectNameColumn),String)
            End Get
            Set
                Me(Me.tableResearchContract.ProjectNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ApplyAgencyComm() As Boolean
            Get
                Return CType(Me(Me.tableResearchContract.ApplyAgencyCommColumn),Boolean)
            End Get
            Set
                Me(Me.tableResearchContract.ApplyAgencyCommColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property AgencyID() As Integer
            Get
                Try 
                    Return CType(Me(Me.tableResearchContract.AgencyIDColumn),Integer)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'AgencyID' in table 'ResearchContract' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableResearchContract.AgencyIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property AgencyName() As String
            Get
                Return CType(Me(Me.tableResearchContract.AgencyNameColumn),String)
            End Get
            Set
                Me(Me.tableResearchContract.AgencyNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property AgencyCommPercentage() As Decimal
            Get
                Return CType(Me(Me.tableResearchContract.AgencyCommPercentageColumn),Decimal)
            End Get
            Set
                Me(Me.tableResearchContract.AgencyCommPercentageColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property Cancelled() As Boolean
            Get
                Return CType(Me(Me.tableResearchContract.CancelledColumn),Boolean)
            End Get
            Set
                Me(Me.tableResearchContract.CancelledColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property CancelDate() As Date
            Get
                Try 
                    Return CType(Me(Me.tableResearchContract.CancelDateColumn),Date)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'CancelDate' in table 'ResearchContract' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableResearchContract.CancelDateColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property CancelledBy() As String
            Get
                Try 
                    Return CType(Me(Me.tableResearchContract.CancelledByColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'CancelledBy' in table 'ResearchContract' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableResearchContract.CancelledByColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property CreatedBy() As String
            Get
                Return CType(Me(Me.tableResearchContract.CreatedByColumn),String)
            End Get
            Set
                Me(Me.tableResearchContract.CreatedByColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property CreationDate() As Date
            Get
                Return CType(Me(Me.tableResearchContract.CreationDateColumn),Date)
            End Get
            Set
                Me(Me.tableResearchContract.CreationDateColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property BillingInstructions() As String
            Get
                Return CType(Me(Me.tableResearchContract.BillingInstructionsColumn),String)
            End Get
            Set
                Me(Me.tableResearchContract.BillingInstructionsColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ContractNotes() As String
            Get
                Return CType(Me(Me.tableResearchContract.ContractNotesColumn),String)
            End Get
            Set
                Me(Me.tableResearchContract.ContractNotesColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ClassificationName() As String
            Get
                Return CType(Me(Me.tableResearchContract.ClassificationNameColumn),String)
            End Get
            Set
                Me(Me.tableResearchContract.ClassificationNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property PrintInfo() As String
            Get
                Try 
                    Return CType(Me(Me.tableResearchContract.PrintInfoColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'PrintInfo' in table 'ResearchContract' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableResearchContract.PrintInfoColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property FirstMonth() As String
            Get
                Try 
                    Return CType(Me(Me.tableResearchContract.FirstMonthColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'FirstMonth' in table 'ResearchContract' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableResearchContract.FirstMonthColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property LastMonth() As String
            Get
                Try 
                    Return CType(Me(Me.tableResearchContract.LastMonthColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'LastMonth' in table 'ResearchContract' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableResearchContract.LastMonthColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property Months() As Integer
            Get
                Try 
                    Return CType(Me(Me.tableResearchContract.MonthsColumn),Integer)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'Months' in table 'ResearchContract' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableResearchContract.MonthsColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ResearchFee() As Decimal
            Get
                Try 
                    Return CType(Me(Me.tableResearchContract.ResearchFeeColumn),Decimal)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'ResearchFee' in table 'ResearchContract' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableResearchContract.ResearchFeeColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsSignDateNull() As Boolean
            Return Me.IsNull(Me.tableResearchContract.SignDateColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetSignDateNull()
            Me(Me.tableResearchContract.SignDateColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsAgencyIDNull() As Boolean
            Return Me.IsNull(Me.tableResearchContract.AgencyIDColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetAgencyIDNull()
            Me(Me.tableResearchContract.AgencyIDColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsCancelDateNull() As Boolean
            Return Me.IsNull(Me.tableResearchContract.CancelDateColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetCancelDateNull()
            Me(Me.tableResearchContract.CancelDateColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsCancelledByNull() As Boolean
            Return Me.IsNull(Me.tableResearchContract.CancelledByColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetCancelledByNull()
            Me(Me.tableResearchContract.CancelledByColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsPrintInfoNull() As Boolean
            Return Me.IsNull(Me.tableResearchContract.PrintInfoColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetPrintInfoNull()
            Me(Me.tableResearchContract.PrintInfoColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsFirstMonthNull() As Boolean
            Return Me.IsNull(Me.tableResearchContract.FirstMonthColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetFirstMonthNull()
            Me(Me.tableResearchContract.FirstMonthColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsLastMonthNull() As Boolean
            Return Me.IsNull(Me.tableResearchContract.LastMonthColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetLastMonthNull()
            Me(Me.tableResearchContract.LastMonthColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsMonthsNull() As Boolean
            Return Me.IsNull(Me.tableResearchContract.MonthsColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetMonthsNull()
            Me(Me.tableResearchContract.MonthsColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsResearchFeeNull() As Boolean
            Return Me.IsNull(Me.tableResearchContract.ResearchFeeColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetResearchFeeNull()
            Me(Me.tableResearchContract.ResearchFeeColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function GetPurchaseOrderNumberRows() As BillingInstructionRow()
            If (Me.Table.ChildRelations("ResearchContract_PurchaseOrderNumber") Is Nothing) Then
                Return New BillingInstructionRow(-1) {}
            Else
                Return CType(MyBase.GetChildRows(Me.Table.ChildRelations("ResearchContract_PurchaseOrderNumber")),BillingInstructionRow())
            End If
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function GetMiscellaneousChargeRows() As MiscellaneousChargeRow()
            If (Me.Table.ChildRelations("ResearchContract_MiscellaneousCharge") Is Nothing) Then
                Return New MiscellaneousChargeRow(-1) {}
            Else
                Return CType(MyBase.GetChildRows(Me.Table.ChildRelations("ResearchContract_MiscellaneousCharge")),MiscellaneousChargeRow())
            End If
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function GetResearchCategoryRows() As ResearchCategoryRow()
            If (Me.Table.ChildRelations("ResearchContract_ResearchCategory") Is Nothing) Then
                Return New ResearchCategoryRow(-1) {}
            Else
                Return CType(MyBase.GetChildRows(Me.Table.ChildRelations("ResearchContract_ResearchCategory")),ResearchCategoryRow())
            End If
        End Function
    End Class
    
    '''<summary>
    '''Represents strongly named DataRow class.
    '''</summary>
    Partial Public Class ResearchCategoryRow
        Inherits Global.System.Data.DataRow
        
        Private tableResearchCategory As ResearchCategoryDataTable
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Sub New(ByVal rb As Global.System.Data.DataRowBuilder)
            MyBase.New(rb)
            Me.tableResearchCategory = CType(Me.Table,ResearchCategoryDataTable)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ResearchCategoryID() As System.Guid
            Get
                Return CType(Me(Me.tableResearchCategory.ResearchCategoryIDColumn),Global.System.Guid)
            End Get
            Set
                Me(Me.tableResearchCategory.ResearchCategoryIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ContractID() As System.Guid
            Get
                Return CType(Me(Me.tableResearchCategory.ContractIDColumn),Global.System.Guid)
            End Get
            Set
                Me(Me.tableResearchCategory.ContractIDColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property CategoryName() As String
            Get
                Return CType(Me(Me.tableResearchCategory.CategoryNameColumn),String)
            End Get
            Set
                Me(Me.tableResearchCategory.CategoryNameColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property FirstMonth() As String
            Get
                Try 
                    Return CType(Me(Me.tableResearchCategory.FirstMonthColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'FirstMonth' in table 'ResearchCategory' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableResearchCategory.FirstMonthColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property LastMonth() As String
            Get
                Try 
                    Return CType(Me(Me.tableResearchCategory.LastMonthColumn),String)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'LastMonth' in table 'ResearchCategory' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableResearchCategory.LastMonthColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property Months() As Integer
            Get
                Return CType(Me(Me.tableResearchCategory.MonthsColumn),Integer)
            End Get
            Set
                Me(Me.tableResearchCategory.MonthsColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property Fee() As Decimal
            Get
                Return CType(Me(Me.tableResearchCategory.FeeColumn),Decimal)
            End Get
            Set
                Me(Me.tableResearchCategory.FeeColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property Discount() As Decimal
            Get
                Return CType(Me(Me.tableResearchCategory.DiscountColumn),Decimal)
            End Get
            Set
                Me(Me.tableResearchCategory.DiscountColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property DiscountAmount() As Decimal
            Get
                Try 
                    Return CType(Me(Me.tableResearchCategory.DiscountAmountColumn),Decimal)
                Catch e As Global.System.InvalidCastException
                    Throw New Global.System.Data.StrongTypingException("The value for column 'DiscountAmount' in table 'ResearchCategory' is DBNull.", e)
                End Try
            End Get
            Set
                Me(Me.tableResearchCategory.DiscountAmountColumn) = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ResearchContractRow() As ResearchContractRow
            Get
                Return CType(Me.GetParentRow(Me.Table.ParentRelations("ResearchContract_ResearchCategory")),ResearchContractRow)
            End Get
            Set
                Me.SetParentRow(value, Me.Table.ParentRelations("ResearchContract_ResearchCategory"))
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsFirstMonthNull() As Boolean
            Return Me.IsNull(Me.tableResearchCategory.FirstMonthColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetFirstMonthNull()
            Me(Me.tableResearchCategory.FirstMonthColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsLastMonthNull() As Boolean
            Return Me.IsNull(Me.tableResearchCategory.LastMonthColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetLastMonthNull()
            Me(Me.tableResearchCategory.LastMonthColumn) = Global.System.Convert.DBNull
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Function IsDiscountAmountNull() As Boolean
            Return Me.IsNull(Me.tableResearchCategory.DiscountAmountColumn)
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub SetDiscountAmountNull()
            Me(Me.tableResearchCategory.DiscountAmountColumn) = Global.System.Convert.DBNull
        End Sub
    End Class
    
    '''<summary>
    '''Row event argument class
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Class ContractRowChangeEvent
        Inherits Global.System.EventArgs
        
        Private eventRow As ContractRow
        
        Private eventAction As Global.System.Data.DataRowAction
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New(ByVal row As ContractRow, ByVal action As Global.System.Data.DataRowAction)
            MyBase.New
            Me.eventRow = row
            Me.eventAction = action
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property Row() As ContractRow
            Get
                Return Me.eventRow
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property Action() As Global.System.Data.DataRowAction
            Get
                Return Me.eventAction
            End Get
        End Property
    End Class
    
    '''<summary>
    '''Row event argument class
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Class BillingInstructionRowChangeEvent
        Inherits Global.System.EventArgs
        
        Private eventRow As BillingInstructionRow
        
        Private eventAction As Global.System.Data.DataRowAction
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New(ByVal row As BillingInstructionRow, ByVal action As Global.System.Data.DataRowAction)
            MyBase.New
            Me.eventRow = row
            Me.eventAction = action
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property Row() As BillingInstructionRow
            Get
                Return Me.eventRow
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property Action() As Global.System.Data.DataRowAction
            Get
                Return Me.eventAction
            End Get
        End Property
    End Class
    
    '''<summary>
    '''Row event argument class
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Class BurstRowChangeEvent
        Inherits Global.System.EventArgs
        
        Private eventRow As BurstRow
        
        Private eventAction As Global.System.Data.DataRowAction
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New(ByVal row As BurstRow, ByVal action As Global.System.Data.DataRowAction)
            MyBase.New
            Me.eventRow = row
            Me.eventAction = action
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property Row() As BurstRow
            Get
                Return Me.eventRow
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property Action() As Global.System.Data.DataRowAction
            Get
                Return Me.eventAction
            End Get
        End Property
    End Class
    
    '''<summary>
    '''Row event argument class
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Class ContractInventoryRowChangeEvent
        Inherits Global.System.EventArgs
        
        Private eventRow As ContractInventoryRow
        
        Private eventAction As Global.System.Data.DataRowAction
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New(ByVal row As ContractInventoryRow, ByVal action As Global.System.Data.DataRowAction)
            MyBase.New
            Me.eventRow = row
            Me.eventAction = action
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property Row() As ContractInventoryRow
            Get
                Return Me.eventRow
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property Action() As Global.System.Data.DataRowAction
            Get
                Return Me.eventAction
            End Get
        End Property
    End Class
    
    '''<summary>
    '''Row event argument class
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Class MiscellaneousChargeRowChangeEvent
        Inherits Global.System.EventArgs
        
        Private eventRow As MiscellaneousChargeRow
        
        Private eventAction As Global.System.Data.DataRowAction
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New(ByVal row As MiscellaneousChargeRow, ByVal action As Global.System.Data.DataRowAction)
            MyBase.New
            Me.eventRow = row
            Me.eventAction = action
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property Row() As MiscellaneousChargeRow
            Get
                Return Me.eventRow
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property Action() As Global.System.Data.DataRowAction
            Get
                Return Me.eventAction
            End Get
        End Property
    End Class
    
    '''<summary>
    '''Row event argument class
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Class ResearchContractRowChangeEvent
        Inherits Global.System.EventArgs
        
        Private eventRow As ResearchContractRow
        
        Private eventAction As Global.System.Data.DataRowAction
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New(ByVal row As ResearchContractRow, ByVal action As Global.System.Data.DataRowAction)
            MyBase.New
            Me.eventRow = row
            Me.eventAction = action
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property Row() As ResearchContractRow
            Get
                Return Me.eventRow
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property Action() As Global.System.Data.DataRowAction
            Get
                Return Me.eventAction
            End Get
        End Property
    End Class
    
    '''<summary>
    '''Row event argument class
    '''</summary>
    <Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
    Public Class ResearchCategoryRowChangeEvent
        Inherits Global.System.EventArgs
        
        Private eventRow As ResearchCategoryRow
        
        Private eventAction As Global.System.Data.DataRowAction
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New(ByVal row As ResearchCategoryRow, ByVal action As Global.System.Data.DataRowAction)
            MyBase.New
            Me.eventRow = row
            Me.eventAction = action
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property Row() As ResearchCategoryRow
            Get
                Return Me.eventRow
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public ReadOnly Property Action() As Global.System.Data.DataRowAction
            Get
                Return Me.eventAction
            End Get
        End Property
    End Class
End Class

Namespace DataSetContractReportTableAdapters
    
    '''<summary>
    '''Represents the connection and commands used to retrieve and save data.
    '''</summary>
    <Global.System.ComponentModel.DesignerCategoryAttribute("code"),  _
     Global.System.ComponentModel.ToolboxItem(true),  _
     Global.System.ComponentModel.DataObjectAttribute(true),  _
     Global.System.ComponentModel.DesignerAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterDesigner, Microsoft.VSDesigner"& _ 
        ", Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"),  _
     Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
    Partial Public Class ContractTableAdapter
        Inherits Global.System.ComponentModel.Component
        
        Private WithEvents _adapter As Global.System.Data.SqlClient.SqlDataAdapter
        
        Private _connection As Global.System.Data.SqlClient.SqlConnection
        
        Private _commandCollection() As Global.System.Data.SqlClient.SqlCommand
        
        Private _clearBeforeFill As Boolean
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New()
            MyBase.New
            Me.ClearBeforeFill = true
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private ReadOnly Property Adapter() As Global.System.Data.SqlClient.SqlDataAdapter
            Get
                If (Me._adapter Is Nothing) Then
                    Me.InitAdapter
                End If
                Return Me._adapter
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Property Connection() As Global.System.Data.SqlClient.SqlConnection
            Get
                If (Me._connection Is Nothing) Then
                    Me.InitConnection
                End If
                Return Me._connection
            End Get
            Set
                Me._connection = value
                If (Not (Me.Adapter.InsertCommand) Is Nothing) Then
                    Me.Adapter.InsertCommand.Connection = value
                End If
                If (Not (Me.Adapter.DeleteCommand) Is Nothing) Then
                    Me.Adapter.DeleteCommand.Connection = value
                End If
                If (Not (Me.Adapter.UpdateCommand) Is Nothing) Then
                    Me.Adapter.UpdateCommand.Connection = value
                End If
                Dim i As Integer = 0
                Do While (i < Me.CommandCollection.Length)
                    If (Not (Me.CommandCollection(i)) Is Nothing) Then
                        CType(Me.CommandCollection(i),Global.System.Data.SqlClient.SqlCommand).Connection = value
                    End If
                    i = (i + 1)
                Loop
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected ReadOnly Property CommandCollection() As Global.System.Data.SqlClient.SqlCommand()
            Get
                If (Me._commandCollection Is Nothing) Then
                    Me.InitCommandCollection
                End If
                Return Me._commandCollection
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ClearBeforeFill() As Boolean
            Get
                Return Me._clearBeforeFill
            End Get
            Set
                Me._clearBeforeFill = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitAdapter()
            Me._adapter = New Global.System.Data.SqlClient.SqlDataAdapter()
            Dim tableMapping As Global.System.Data.Common.DataTableMapping = New Global.System.Data.Common.DataTableMapping()
            tableMapping.SourceTable = "Table"
            tableMapping.DataSetTable = "Contract"
            tableMapping.ColumnMappings.Add("ContractID", "ContractID")
            tableMapping.ColumnMappings.Add("AccountManagerID", "AccountManagerID")
            tableMapping.ColumnMappings.Add("ClientID", "ClientID")
            tableMapping.ColumnMappings.Add("ClientName", "ClientName")
            tableMapping.ColumnMappings.Add("ClientBillingAddress", "ClientBillingAddress")
            tableMapping.ColumnMappings.Add("ContractNumber", "ContractNumber")
            tableMapping.ColumnMappings.Add("Signed", "Signed")
            tableMapping.ColumnMappings.Add("SignDate", "SignDate")
            tableMapping.ColumnMappings.Add("SignedBy", "SignedBy")
            tableMapping.ColumnMappings.Add("SpecialConditions", "SpecialConditions")
            tableMapping.ColumnMappings.Add("ProjectName", "ProjectName")
            tableMapping.ColumnMappings.Add("ApplyAgencyComm", "ApplyAgencyComm")
            tableMapping.ColumnMappings.Add("AgencyID", "AgencyID")
            tableMapping.ColumnMappings.Add("AgencyName", "AgencyName")
            tableMapping.ColumnMappings.Add("AgencyCommPercentage", "AgencyCommPercentage")
            tableMapping.ColumnMappings.Add("Cancelled", "Cancelled")
            tableMapping.ColumnMappings.Add("CancelDate", "CancelDate")
            tableMapping.ColumnMappings.Add("CancelledBy", "CancelledBy")
            tableMapping.ColumnMappings.Add("CreatedBy", "CreatedBy")
            tableMapping.ColumnMappings.Add("CreationDate", "CreationDate")
            tableMapping.ColumnMappings.Add("BillingInstructions", "BillingInstructions")
            tableMapping.ColumnMappings.Add("ContractNotes", "ContractNotes")
            tableMapping.ColumnMappings.Add("ClassificationName", "ClassificationName")
            tableMapping.ColumnMappings.Add("Weeks", "Weeks")
            tableMapping.ColumnMappings.Add("FirstWeek", "FirstWeek")
            tableMapping.ColumnMappings.Add("LastWeek", "LastWeek")
            tableMapping.ColumnMappings.Add("PrintInfo", "PrintInfo")
            tableMapping.ColumnMappings.Add("NetRental", "NetRental")
            tableMapping.ColumnMappings.Add("Production", "Production")
            tableMapping.ColumnMappings.Add("TerminationDate", "TerminationDate")
            tableMapping.ColumnMappings.Add("DiscountAmount", "DiscountAmount")
            tableMapping.ColumnMappings.Add("PrintAgencyComm", "PrintAgencyComm")
            tableMapping.ColumnMappings.Add("AgencyCommAmount", "AgencyCommAmount")
            tableMapping.ColumnMappings.Add("ClientAccountManagerCode", "ClientAccountManagerCode")
            Me._adapter.TableMappings.Add(tableMapping)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitConnection()
            Me._connection = New Global.System.Data.SqlClient.SqlConnection()
            Me._connection.ConnectionString = Global.NovaReports.My.MySettings.Default.NovaDBConnectionString
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitCommandCollection()
            Me._commandCollection = New Global.System.Data.SqlClient.SqlCommand(0) {}
            Me._commandCollection(0) = New Global.System.Data.SqlClient.SqlCommand()
            Me._commandCollection(0).Connection = Me.Connection
            Me._commandCollection(0).CommandText = "SELECT ContractID, AccountManagerID, ClientID, ClientName, ClientBillingAddress, "& _ 
                "ContractNumber, Signed, SignDate, SignedBy, SpecialConditions, ProjectName, Appl"& _ 
                "yAgencyComm, AgencyID, AgencyName, AgencyCommPercentage, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                  Can"& _ 
                "celled, CancelDate, CancelledBy, CreatedBy, CreationDate, BillingInstructions, C"& _ 
                "ontractNotes, ClassificationName, DATEDIFF(wk, ISNULL(FirstWeek, CreationDate), "& _ 
                "ISNULL(LastWeek, CreationDate)) + 1 AS Weeks, ISNULL(FirstWeek, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"              "& _ 
                "    CreationDate) AS FirstWeek, ISNULL(LastWeek, CreationDate) AS LastWeek, 'Pri"& _ 
                "nted by ' + @PrintingUser + ' at ' + CONVERT(nvarchar(2), DATEPART(hour, GETDATE"& _ 
                "())) + N':' + CONVERT(nvarchar(2), DATEPART(minute, GETDATE())) "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"              "& _ 
                "    + ' on ' + DATENAME(weekday, GETDATE()) + ', ' + CONVERT(nvarchar(2), DATEPA"& _ 
                "RT(day, GETDATE())) + ' ' + DATENAME(month, GETDATE()) + ' ' + CONVERT(nvarchar("& _ 
                "4), DATEPART(year, GETDATE())) AS PrintInfo, NetRental, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                  ISNU"& _ 
                "LL(Production, 0) AS Production, DATEADD(wk, 1, ISNULL(LastWeek, CreationDate)) "& _ 
                "AS TerminationDate, DiscountAmount, PrintAgencyComm, AgencyCommAmount, dbo.udfGe"& _ 
                "tClientAccountManagerCode(ClientID, CreationDate) "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                  AS ClientA"& _ 
                "ccountManagerCode"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"FROM     reporting.udf_contracthardcopy(@ContractID) AS udf"
            Me._commandCollection(0).CommandType = Global.System.Data.CommandType.Text
            Me._commandCollection(0).Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@ContractID", Global.System.Data.SqlDbType.UniqueIdentifier, 0, Global.System.Data.ParameterDirection.Input, 0, 0, "", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._commandCollection(0).Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@PrintingUser", Global.System.Data.SqlDbType.VarChar, 1024, Global.System.Data.ParameterDirection.Input, 0, 0, "", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.Fill, true)>  _
        Public Overloads Overridable Function Fill(ByVal dataTable As DataSetContractReport.ContractDataTable, ByVal ContractID As System.Guid, ByVal PrintingUser As String) As Integer
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            Me.Adapter.SelectCommand.Parameters(0).Value = CType(ContractID,System.Guid)
            If (PrintingUser Is Nothing) Then
                Throw New Global.System.ArgumentNullException("PrintingUser")
            Else
                Me.Adapter.SelectCommand.Parameters(1).Value = CType(PrintingUser,String)
            End If
            If (Me.ClearBeforeFill = true) Then
                dataTable.Clear
            End If
            Dim returnValue As Integer = Me.Adapter.Fill(dataTable)
            Return returnValue
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.[Select], true)>  _
        Public Overloads Overridable Function GetData(ByVal ContractID As System.Guid, ByVal PrintingUser As String) As DataSetContractReport.ContractDataTable
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            Me.Adapter.SelectCommand.Parameters(0).Value = CType(ContractID,System.Guid)
            If (PrintingUser Is Nothing) Then
                Throw New Global.System.ArgumentNullException("PrintingUser")
            Else
                Me.Adapter.SelectCommand.Parameters(1).Value = CType(PrintingUser,String)
            End If
            Dim dataTable As DataSetContractReport.ContractDataTable = New DataSetContractReport.ContractDataTable()
            Me.Adapter.Fill(dataTable)
            Return dataTable
        End Function
    End Class
    
    '''<summary>
    '''Represents the connection and commands used to retrieve and save data.
    '''</summary>
    <Global.System.ComponentModel.DesignerCategoryAttribute("code"),  _
     Global.System.ComponentModel.ToolboxItem(true),  _
     Global.System.ComponentModel.DataObjectAttribute(true),  _
     Global.System.ComponentModel.DesignerAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterDesigner, Microsoft.VSDesigner"& _ 
        ", Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"),  _
     Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
    Partial Public Class BillingInstructionTableAdapter
        Inherits Global.System.ComponentModel.Component
        
        Private WithEvents _adapter As Global.System.Data.SqlClient.SqlDataAdapter
        
        Private _connection As Global.System.Data.SqlClient.SqlConnection
        
        Private _commandCollection() As Global.System.Data.SqlClient.SqlCommand
        
        Private _clearBeforeFill As Boolean
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New()
            MyBase.New
            Me.ClearBeforeFill = true
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private ReadOnly Property Adapter() As Global.System.Data.SqlClient.SqlDataAdapter
            Get
                If (Me._adapter Is Nothing) Then
                    Me.InitAdapter
                End If
                Return Me._adapter
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Property Connection() As Global.System.Data.SqlClient.SqlConnection
            Get
                If (Me._connection Is Nothing) Then
                    Me.InitConnection
                End If
                Return Me._connection
            End Get
            Set
                Me._connection = value
                If (Not (Me.Adapter.InsertCommand) Is Nothing) Then
                    Me.Adapter.InsertCommand.Connection = value
                End If
                If (Not (Me.Adapter.DeleteCommand) Is Nothing) Then
                    Me.Adapter.DeleteCommand.Connection = value
                End If
                If (Not (Me.Adapter.UpdateCommand) Is Nothing) Then
                    Me.Adapter.UpdateCommand.Connection = value
                End If
                Dim i As Integer = 0
                Do While (i < Me.CommandCollection.Length)
                    If (Not (Me.CommandCollection(i)) Is Nothing) Then
                        CType(Me.CommandCollection(i),Global.System.Data.SqlClient.SqlCommand).Connection = value
                    End If
                    i = (i + 1)
                Loop
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected ReadOnly Property CommandCollection() As Global.System.Data.SqlClient.SqlCommand()
            Get
                If (Me._commandCollection Is Nothing) Then
                    Me.InitCommandCollection
                End If
                Return Me._commandCollection
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ClearBeforeFill() As Boolean
            Get
                Return Me._clearBeforeFill
            End Get
            Set
                Me._clearBeforeFill = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitAdapter()
            Me._adapter = New Global.System.Data.SqlClient.SqlDataAdapter()
            Dim tableMapping As Global.System.Data.Common.DataTableMapping = New Global.System.Data.Common.DataTableMapping()
            tableMapping.SourceTable = "Table"
            tableMapping.DataSetTable = "BillingInstruction"
            tableMapping.ColumnMappings.Add("ContractID", "ContractID")
            tableMapping.ColumnMappings.Add("PeriodName", "PeriodName")
            tableMapping.ColumnMappings.Add("Amount", "Amount")
            tableMapping.ColumnMappings.Add("PONumber", "PONumber")
            Me._adapter.TableMappings.Add(tableMapping)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitConnection()
            Me._connection = New Global.System.Data.SqlClient.SqlConnection()
            Me._connection.ConnectionString = Global.NovaReports.My.MySettings.Default.DBConnection
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitCommandCollection()
            Me._commandCollection = New Global.System.Data.SqlClient.SqlCommand(0) {}
            Me._commandCollection(0) = New Global.System.Data.SqlClient.SqlCommand()
            Me._commandCollection(0).Connection = Me.Connection
            Me._commandCollection(0).CommandText = "SELECT        Sales.BillingInstruction.ContractID, Finance.Period.PeriodName, Sal"& _ 
                "es.BillingInstruction.Amount, Sales.BillingInstruction.PONumber"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"FROM           "& _ 
                " Sales.BillingInstruction INNER JOIN"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         Finance.Period ON"& _ 
                " Sales.BillingInstruction.PeriodID = Finance.Period.PeriodID"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"WHERE        (Sale"& _ 
                "s.BillingInstruction.ContractID = @ContractID)"
            Me._commandCollection(0).CommandType = Global.System.Data.CommandType.Text
            Me._commandCollection(0).Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@ContractID", Global.System.Data.SqlDbType.UniqueIdentifier, 16, Global.System.Data.ParameterDirection.Input, 0, 0, "ContractID", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.Fill, true)>  _
        Public Overloads Overridable Function Fill(ByVal dataTable As DataSetContractReport.BillingInstructionDataTable, ByVal ContractID As System.Guid) As Integer
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            Me.Adapter.SelectCommand.Parameters(0).Value = CType(ContractID,System.Guid)
            If (Me.ClearBeforeFill = true) Then
                dataTable.Clear
            End If
            Dim returnValue As Integer = Me.Adapter.Fill(dataTable)
            Return returnValue
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.[Select], true)>  _
        Public Overloads Overridable Function GetData(ByVal ContractID As System.Guid) As DataSetContractReport.BillingInstructionDataTable
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            Me.Adapter.SelectCommand.Parameters(0).Value = CType(ContractID,System.Guid)
            Dim dataTable As DataSetContractReport.BillingInstructionDataTable = New DataSetContractReport.BillingInstructionDataTable()
            Me.Adapter.Fill(dataTable)
            Return dataTable
        End Function
    End Class
    
    '''<summary>
    '''Represents the connection and commands used to retrieve and save data.
    '''</summary>
    <Global.System.ComponentModel.DesignerCategoryAttribute("code"),  _
     Global.System.ComponentModel.ToolboxItem(true),  _
     Global.System.ComponentModel.DataObjectAttribute(true),  _
     Global.System.ComponentModel.DesignerAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterDesigner, Microsoft.VSDesigner"& _ 
        ", Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"),  _
     Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
    Partial Public Class BurstTableAdapter
        Inherits Global.System.ComponentModel.Component
        
        Private WithEvents _adapter As Global.System.Data.SqlClient.SqlDataAdapter
        
        Private _connection As Global.System.Data.SqlClient.SqlConnection
        
        Private _commandCollection() As Global.System.Data.SqlClient.SqlCommand
        
        Private _clearBeforeFill As Boolean
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New()
            MyBase.New
            Me.ClearBeforeFill = true
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private ReadOnly Property Adapter() As Global.System.Data.SqlClient.SqlDataAdapter
            Get
                If (Me._adapter Is Nothing) Then
                    Me.InitAdapter
                End If
                Return Me._adapter
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Property Connection() As Global.System.Data.SqlClient.SqlConnection
            Get
                If (Me._connection Is Nothing) Then
                    Me.InitConnection
                End If
                Return Me._connection
            End Get
            Set
                Me._connection = value
                If (Not (Me.Adapter.InsertCommand) Is Nothing) Then
                    Me.Adapter.InsertCommand.Connection = value
                End If
                If (Not (Me.Adapter.DeleteCommand) Is Nothing) Then
                    Me.Adapter.DeleteCommand.Connection = value
                End If
                If (Not (Me.Adapter.UpdateCommand) Is Nothing) Then
                    Me.Adapter.UpdateCommand.Connection = value
                End If
                Dim i As Integer = 0
                Do While (i < Me.CommandCollection.Length)
                    If (Not (Me.CommandCollection(i)) Is Nothing) Then
                        CType(Me.CommandCollection(i),Global.System.Data.SqlClient.SqlCommand).Connection = value
                    End If
                    i = (i + 1)
                Loop
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected ReadOnly Property CommandCollection() As Global.System.Data.SqlClient.SqlCommand()
            Get
                If (Me._commandCollection Is Nothing) Then
                    Me.InitCommandCollection
                End If
                Return Me._commandCollection
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ClearBeforeFill() As Boolean
            Get
                Return Me._clearBeforeFill
            End Get
            Set
                Me._clearBeforeFill = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitAdapter()
            Me._adapter = New Global.System.Data.SqlClient.SqlDataAdapter()
            Dim tableMapping As Global.System.Data.Common.DataTableMapping = New Global.System.Data.Common.DataTableMapping()
            tableMapping.SourceTable = "Table"
            tableMapping.DataSetTable = "Burst"
            tableMapping.ColumnMappings.Add("BurstID", "BurstID")
            tableMapping.ColumnMappings.Add("ContractID", "ContractID")
            tableMapping.ColumnMappings.Add("ChainID", "ChainID")
            tableMapping.ColumnMappings.Add("ChainName", "ChainName")
            tableMapping.ColumnMappings.Add("StorePoolID", "StorePoolID")
            tableMapping.ColumnMappings.Add("MediaID", "MediaID")
            tableMapping.ColumnMappings.Add("MediaName", "MediaName")
            tableMapping.ColumnMappings.Add("BrandID", "BrandID")
            tableMapping.ColumnMappings.Add("BrandName", "BrandName")
            tableMapping.ColumnMappings.Add("ProductName", "ProductName")
            tableMapping.ColumnMappings.Add("FirstWeek", "FirstWeek")
            tableMapping.ColumnMappings.Add("InstallWeeks", "InstallWeeks")
            tableMapping.ColumnMappings.Add("InstallStoreQty", "InstallStoreQty")
            tableMapping.ColumnMappings.Add("BillableStoreQty", "BillableStoreQty")
            tableMapping.ColumnMappings.Add("RentalRate", "RentalRate")
            tableMapping.ColumnMappings.Add("BillableWeeks", "BillableWeeks")
            tableMapping.ColumnMappings.Add("Discount", "Discount")
            tableMapping.ColumnMappings.Add("CrossoverQty", "CrossoverQty")
            tableMapping.ColumnMappings.Add("InstallAtHomesite", "InstallAtHomesite")
            tableMapping.ColumnMappings.Add("InstallationInstructions", "InstallationInstructions")
            tableMapping.ColumnMappings.Add("StoreListConfirmed", "StoreListConfirmed")
            tableMapping.ColumnMappings.Add("CreatedBy", "CreatedBy")
            tableMapping.ColumnMappings.Add("CreationDate", "CreationDate")
            tableMapping.ColumnMappings.Add("LastWeek", "LastWeek")
            tableMapping.ColumnMappings.Add("LoadingPercentage", "LoadingPercentage")
            tableMapping.ColumnMappings.Add("LoadingAmount", "LoadingAmount")
            tableMapping.ColumnMappings.Add("TotalFreeStores", "TotalFreeStores")
            tableMapping.ColumnMappings.Add("Homesite", "Homesite")
            tableMapping.ColumnMappings.Add("GroupDefinition", "GroupDefinition")
            tableMapping.ColumnMappings.Add("DiscountAmount", "DiscountAmount")
            tableMapping.ColumnMappings.Add("TotalFreeWeeks", "TotalFreeWeeks")
            Me._adapter.TableMappings.Add(tableMapping)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitConnection()
            Me._connection = New Global.System.Data.SqlClient.SqlConnection()
            Me._connection.ConnectionString = Global.NovaReports.My.MySettings.Default.NovaDBConnectionString
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitCommandCollection()
            Me._commandCollection = New Global.System.Data.SqlClient.SqlCommand(0) {}
            Me._commandCollection(0) = New Global.System.Data.SqlClient.SqlCommand()
            Me._commandCollection(0).Connection = Me.Connection
            Me._commandCollection(0).CommandText = "SELECT        TOP (100) PERCENT Sales.Burst.BurstID, Sales.Burst.ContractID, Sale"& _ 
                "s.Burst.ChainID, Sales.Burst.ChainName, Sales.Burst.StorePoolID, Sales.Burst.Med"& _ 
                "iaID, Sales.Burst.MediaName, Sales.Burst.BrandID, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         Sal"& _ 
                "es.Burst.BrandName, Sales.Burst.ProductName, Sales.Burst.FirstWeek, DATEADD(wk, "& _ 
                "Sales.Burst.InstallWeeks - 1, Sales.Burst.FirstWeek) AS LastWeek, Sales.Burst.In"& _ 
                "stallWeeks, Sales.Burst.InstallStoreQty, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         Sales.Burst."& _ 
                "BillableStoreQty, Sales.Burst.RentalRate, Sales.Burst.BillableWeeks, Sales.Burst"& _ 
                ".Discount, Sales.Burst.CrossoverQty, Sales.Burst.InstallAtHomesite, Sales.Burst."& _ 
                "InstallationInstructions, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         Sales.Burst.StoreListConfir"& _ 
                "med, Sales.Burst.CreatedBy, Sales.Burst.CreationDate, ISNULL(dtLoadingPercentage"& _ 
                ".LoadingPercentage, 0) AS LoadingPercentage, ISNULL(dtLoadingAmount.LoadingAmoun"& _ 
                "t, 0) AS LoadingAmount, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         dtFreeStoreTotal.TotalFreeSto"& _ 
                "res, ISNULL(dtHomesite.Homesite, N'no homesite selected') AS Homesite, ISNULL(Sa"& _ 
                "les.Burst.MediaName + N' in ' + dbo.udfCategoryListByBurst(Sales.Burst.BurstID),"& _ 
                " Sales.Burst.MediaName) "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         AS GroupDefinition, ISNULL(Sa"& _ 
                "les.vBase_Revenue_contracthardcopy.DiscountAmount, 0) AS DiscountAmount, dtFreeW"& _ 
                "eekTotal.TotalFreeWeeks"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"FROM            Sales.Burst INNER JOIN"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"               "& _ 
                "              (SELECT        ContractID, SUM(InstallStoreQty - BillableStoreQty)"& _ 
                " AS TotalFreeStores"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                               FROM            Sales.Burst "& _ 
                "AS Burst_2"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                               GROUP BY ContractID) AS dtFreeStoreTo"& _ 
                "tal ON Sales.Burst.ContractID = dtFreeStoreTotal.ContractID INNER JOIN"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"        "& _ 
                "                     (SELECT        ContractID, SUM(InstallWeeks - BillableWeeks"& _ 
                ") AS TotalFreeWeeks"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                               FROM            Sales.Burst "& _ 
                "AS Burst_3"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                               GROUP BY ContractID) AS dtFreeWeekTot"& _ 
                "al ON Sales.Burst.ContractID = dtFreeWeekTotal.ContractID LEFT OUTER JOIN"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"     "& _ 
                "                    Sales.vBase_Revenue_contracthardcopy ON Sales.Burst.BurstID "& _ 
                "= Sales.vBase_Revenue_contracthardcopy.BurstID LEFT OUTER JOIN"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                "& _ 
                "             (SELECT        Sales.BurstCategory.BurstID, Store.Category.Category"& _ 
                "Name AS Homesite"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                               FROM            Sales.BurstCate"& _ 
                "gory INNER JOIN"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                                                         Store."& _ 
                "Category ON Sales.BurstCategory.CategoryID = Store.Category.CategoryID"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"        "& _ 
                "                       WHERE        (Sales.BurstCategory.Priority = 0)) AS dtHom"& _ 
                "esite ON Sales.Burst.BurstID = dtHomesite.BurstID LEFT OUTER JOIN"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"             "& _ 
                "                (SELECT        Burst_1.BurstID, SUM(BurstLoadingFee_1.Percentage"& _ 
                " / 100 * Burst_1.RentalRate * Burst_1.BillableStoreQty * Burst_1.BillableWeeks) "& _ 
                "AS LoadingAmount"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                               FROM            Sales.BurstLoad"& _ 
                "ingFee AS BurstLoadingFee_1 INNER JOIN"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                                        "& _ 
                "                 Sales.Burst AS Burst_1 ON BurstLoadingFee_1.BurstID = Burst_1.B"& _ 
                "urstID"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                               GROUP BY Burst_1.BurstID) AS dtLoadingAmo"& _ 
                "unt ON Sales.Burst.BurstID = dtLoadingAmount.BurstID LEFT OUTER JOIN"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"          "& _ 
                "                   (SELECT        BurstID, SUM(Percentage) AS LoadingPercentage"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                               FROM            Sales.BurstLoadingFee"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"         "& _ 
                "                      GROUP BY BurstID) AS dtLoadingPercentage ON Sales.Burst.Bu"& _ 
                "rstID = dtLoadingPercentage.BurstID"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"WHERE        (Sales.Burst.ContractID = @Con"& _ 
                "tractID) AND (Sales.Burst.ChainID IN"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                             (SELECT      "& _ 
                "  ChainID"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                               FROM            dbo.udfChainPermission"& _ 
                "(@Username) AS udfChainPermission_1))"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"ORDER BY Sales.Burst.MediaName, Sales.Bur"& _ 
                "st.BrandName, Sales.Burst.ChainName"
            Me._commandCollection(0).CommandType = Global.System.Data.CommandType.Text
            Me._commandCollection(0).Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@ContractID", Global.System.Data.SqlDbType.UniqueIdentifier, 16, Global.System.Data.ParameterDirection.Input, 0, 0, "ContractID", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
            Me._commandCollection(0).Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@Username", Global.System.Data.SqlDbType.VarChar, 1024, Global.System.Data.ParameterDirection.Input, 0, 0, "", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.Fill, true)>  _
        Public Overloads Overridable Function Fill(ByVal dataTable As DataSetContractReport.BurstDataTable, ByVal ContractID As System.Guid, ByVal Username As String) As Integer
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            Me.Adapter.SelectCommand.Parameters(0).Value = CType(ContractID,System.Guid)
            If (Username Is Nothing) Then
                Throw New Global.System.ArgumentNullException("Username")
            Else
                Me.Adapter.SelectCommand.Parameters(1).Value = CType(Username,String)
            End If
            If (Me.ClearBeforeFill = true) Then
                dataTable.Clear
            End If
            Dim returnValue As Integer = Me.Adapter.Fill(dataTable)
            Return returnValue
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.[Select], true)>  _
        Public Overloads Overridable Function GetData(ByVal ContractID As System.Guid, ByVal Username As String) As DataSetContractReport.BurstDataTable
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            Me.Adapter.SelectCommand.Parameters(0).Value = CType(ContractID,System.Guid)
            If (Username Is Nothing) Then
                Throw New Global.System.ArgumentNullException("Username")
            Else
                Me.Adapter.SelectCommand.Parameters(1).Value = CType(Username,String)
            End If
            Dim dataTable As DataSetContractReport.BurstDataTable = New DataSetContractReport.BurstDataTable()
            Me.Adapter.Fill(dataTable)
            Return dataTable
        End Function
    End Class
    
    '''<summary>
    '''Represents the connection and commands used to retrieve and save data.
    '''</summary>
    <Global.System.ComponentModel.DesignerCategoryAttribute("code"),  _
     Global.System.ComponentModel.ToolboxItem(true),  _
     Global.System.ComponentModel.DataObjectAttribute(true),  _
     Global.System.ComponentModel.DesignerAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterDesigner, Microsoft.VSDesigner"& _ 
        ", Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"),  _
     Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
    Partial Public Class ContractInventoryTableAdapter
        Inherits Global.System.ComponentModel.Component
        
        Private WithEvents _adapter As Global.System.Data.SqlClient.SqlDataAdapter
        
        Private _connection As Global.System.Data.SqlClient.SqlConnection
        
        Private _commandCollection() As Global.System.Data.SqlClient.SqlCommand
        
        Private _clearBeforeFill As Boolean
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New()
            MyBase.New
            Me.ClearBeforeFill = true
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private ReadOnly Property Adapter() As Global.System.Data.SqlClient.SqlDataAdapter
            Get
                If (Me._adapter Is Nothing) Then
                    Me.InitAdapter
                End If
                Return Me._adapter
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Property Connection() As Global.System.Data.SqlClient.SqlConnection
            Get
                If (Me._connection Is Nothing) Then
                    Me.InitConnection
                End If
                Return Me._connection
            End Get
            Set
                Me._connection = value
                If (Not (Me.Adapter.InsertCommand) Is Nothing) Then
                    Me.Adapter.InsertCommand.Connection = value
                End If
                If (Not (Me.Adapter.DeleteCommand) Is Nothing) Then
                    Me.Adapter.DeleteCommand.Connection = value
                End If
                If (Not (Me.Adapter.UpdateCommand) Is Nothing) Then
                    Me.Adapter.UpdateCommand.Connection = value
                End If
                Dim i As Integer = 0
                Do While (i < Me.CommandCollection.Length)
                    If (Not (Me.CommandCollection(i)) Is Nothing) Then
                        CType(Me.CommandCollection(i),Global.System.Data.SqlClient.SqlCommand).Connection = value
                    End If
                    i = (i + 1)
                Loop
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected ReadOnly Property CommandCollection() As Global.System.Data.SqlClient.SqlCommand()
            Get
                If (Me._commandCollection Is Nothing) Then
                    Me.InitCommandCollection
                End If
                Return Me._commandCollection
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ClearBeforeFill() As Boolean
            Get
                Return Me._clearBeforeFill
            End Get
            Set
                Me._clearBeforeFill = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitAdapter()
            Me._adapter = New Global.System.Data.SqlClient.SqlDataAdapter()
            Dim tableMapping As Global.System.Data.Common.DataTableMapping = New Global.System.Data.Common.DataTableMapping()
            tableMapping.SourceTable = "Table"
            tableMapping.DataSetTable = "ContractInventory"
            tableMapping.ColumnMappings.Add("ContractID", "ContractID")
            tableMapping.ColumnMappings.Add("ItemName", "ItemName")
            tableMapping.ColumnMappings.Add("ItemQty", "ItemQty")
            tableMapping.ColumnMappings.Add("SellPrice", "SellPrice")
            Me._adapter.TableMappings.Add(tableMapping)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitConnection()
            Me._connection = New Global.System.Data.SqlClient.SqlConnection()
            Me._connection.ConnectionString = Global.NovaReports.My.MySettings.Default.DBConnection
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitCommandCollection()
            Me._commandCollection = New Global.System.Data.SqlClient.SqlCommand(0) {}
            Me._commandCollection(0) = New Global.System.Data.SqlClient.SqlCommand()
            Me._commandCollection(0).Connection = Me.Connection
            Me._commandCollection(0).CommandText = "SELECT        Sales.ContractInventoryQty.ContractID, Ops.Inventory.ItemName, Ops."& _ 
                "InventoryQty.ItemQty, Sales.ContractInventoryQty.SellPrice"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"FROM            Sale"& _ 
                "s.ContractInventoryQty INNER JOIN"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         Ops.InventoryQty ON "& _ 
                "Sales.ContractInventoryQty.ItemQtyID = Ops.InventoryQty.ItemQtyID INNER JOIN"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"  "& _ 
                "                       Ops.Inventory ON Ops.InventoryQty.ItemID = Ops.Inventory."& _ 
                "ItemID"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"WHERE        (Sales.ContractInventoryQty.ContractID = @ContractID)"
            Me._commandCollection(0).CommandType = Global.System.Data.CommandType.Text
            Me._commandCollection(0).Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@ContractID", Global.System.Data.SqlDbType.UniqueIdentifier, 16, Global.System.Data.ParameterDirection.Input, 0, 0, "ContractID", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.Fill, true)>  _
        Public Overloads Overridable Function Fill(ByVal dataTable As DataSetContractReport.ContractInventoryDataTable, ByVal ContractID As System.Guid) As Integer
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            Me.Adapter.SelectCommand.Parameters(0).Value = CType(ContractID,System.Guid)
            If (Me.ClearBeforeFill = true) Then
                dataTable.Clear
            End If
            Dim returnValue As Integer = Me.Adapter.Fill(dataTable)
            Return returnValue
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.[Select], true)>  _
        Public Overloads Overridable Function GetData(ByVal ContractID As System.Guid) As DataSetContractReport.ContractInventoryDataTable
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            Me.Adapter.SelectCommand.Parameters(0).Value = CType(ContractID,System.Guid)
            Dim dataTable As DataSetContractReport.ContractInventoryDataTable = New DataSetContractReport.ContractInventoryDataTable()
            Me.Adapter.Fill(dataTable)
            Return dataTable
        End Function
    End Class
    
    '''<summary>
    '''Represents the connection and commands used to retrieve and save data.
    '''</summary>
    <Global.System.ComponentModel.DesignerCategoryAttribute("code"),  _
     Global.System.ComponentModel.ToolboxItem(true),  _
     Global.System.ComponentModel.DataObjectAttribute(true),  _
     Global.System.ComponentModel.DesignerAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterDesigner, Microsoft.VSDesigner"& _ 
        ", Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"),  _
     Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
    Partial Public Class MiscellaneousChargeTableAdapter
        Inherits Global.System.ComponentModel.Component
        
        Private WithEvents _adapter As Global.System.Data.SqlClient.SqlDataAdapter
        
        Private _connection As Global.System.Data.SqlClient.SqlConnection
        
        Private _commandCollection() As Global.System.Data.SqlClient.SqlCommand
        
        Private _clearBeforeFill As Boolean
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New()
            MyBase.New
            Me.ClearBeforeFill = true
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private ReadOnly Property Adapter() As Global.System.Data.SqlClient.SqlDataAdapter
            Get
                If (Me._adapter Is Nothing) Then
                    Me.InitAdapter
                End If
                Return Me._adapter
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Property Connection() As Global.System.Data.SqlClient.SqlConnection
            Get
                If (Me._connection Is Nothing) Then
                    Me.InitConnection
                End If
                Return Me._connection
            End Get
            Set
                Me._connection = value
                If (Not (Me.Adapter.InsertCommand) Is Nothing) Then
                    Me.Adapter.InsertCommand.Connection = value
                End If
                If (Not (Me.Adapter.DeleteCommand) Is Nothing) Then
                    Me.Adapter.DeleteCommand.Connection = value
                End If
                If (Not (Me.Adapter.UpdateCommand) Is Nothing) Then
                    Me.Adapter.UpdateCommand.Connection = value
                End If
                Dim i As Integer = 0
                Do While (i < Me.CommandCollection.Length)
                    If (Not (Me.CommandCollection(i)) Is Nothing) Then
                        CType(Me.CommandCollection(i),Global.System.Data.SqlClient.SqlCommand).Connection = value
                    End If
                    i = (i + 1)
                Loop
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected ReadOnly Property CommandCollection() As Global.System.Data.SqlClient.SqlCommand()
            Get
                If (Me._commandCollection Is Nothing) Then
                    Me.InitCommandCollection
                End If
                Return Me._commandCollection
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ClearBeforeFill() As Boolean
            Get
                Return Me._clearBeforeFill
            End Get
            Set
                Me._clearBeforeFill = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitAdapter()
            Me._adapter = New Global.System.Data.SqlClient.SqlDataAdapter()
            Dim tableMapping As Global.System.Data.Common.DataTableMapping = New Global.System.Data.Common.DataTableMapping()
            tableMapping.SourceTable = "Table"
            tableMapping.DataSetTable = "MiscellaneousCharge"
            tableMapping.ColumnMappings.Add("ContractID", "ContractID")
            tableMapping.ColumnMappings.Add("MiscellaneousChargeName", "MiscellaneousChargeName")
            tableMapping.ColumnMappings.Add("MiscellaneousChargeAmount", "MiscellaneousChargeAmount")
            Me._adapter.TableMappings.Add(tableMapping)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitConnection()
            Me._connection = New Global.System.Data.SqlClient.SqlConnection()
            Me._connection.ConnectionString = Global.NovaReports.My.MySettings.Default.DBConnection
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitCommandCollection()
            Me._commandCollection = New Global.System.Data.SqlClient.SqlCommand(0) {}
            Me._commandCollection(0) = New Global.System.Data.SqlClient.SqlCommand()
            Me._commandCollection(0).Connection = Me.Connection
            Me._commandCollection(0).CommandText = "SELECT        Sales.ContractMiscellaneousCharge.ContractID, Sales.MiscellaneousCh"& _ 
                "arge.MiscellaneousChargeName, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         Sales.ContractMiscellan"& _ 
                "eousCharge.MiscellaneousChargeAmount"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"FROM            Sales.ContractMiscellaneou"& _ 
                "sCharge INNER JOIN"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         Sales.MiscellaneousCharge ON Sales."& _ 
                "ContractMiscellaneousCharge.MiscellaneousChargeID = Sales.MiscellaneousCharge.Mi"& _ 
                "scellaneousChargeID"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"WHERE        (Sales.ContractMiscellaneousCharge.ContractID "& _ 
                "= @ContractID)"
            Me._commandCollection(0).CommandType = Global.System.Data.CommandType.Text
            Me._commandCollection(0).Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@ContractID", Global.System.Data.SqlDbType.UniqueIdentifier, 16, Global.System.Data.ParameterDirection.Input, 0, 0, "ContractID", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.Fill, true)>  _
        Public Overloads Overridable Function Fill(ByVal dataTable As DataSetContractReport.MiscellaneousChargeDataTable, ByVal ContractID As System.Guid) As Integer
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            Me.Adapter.SelectCommand.Parameters(0).Value = CType(ContractID,System.Guid)
            If (Me.ClearBeforeFill = true) Then
                dataTable.Clear
            End If
            Dim returnValue As Integer = Me.Adapter.Fill(dataTable)
            Return returnValue
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.[Select], true)>  _
        Public Overloads Overridable Function GetData(ByVal ContractID As System.Guid) As DataSetContractReport.MiscellaneousChargeDataTable
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            Me.Adapter.SelectCommand.Parameters(0).Value = CType(ContractID,System.Guid)
            Dim dataTable As DataSetContractReport.MiscellaneousChargeDataTable = New DataSetContractReport.MiscellaneousChargeDataTable()
            Me.Adapter.Fill(dataTable)
            Return dataTable
        End Function
    End Class
    
    '''<summary>
    '''Represents the connection and commands used to retrieve and save data.
    '''</summary>
    <Global.System.ComponentModel.DesignerCategoryAttribute("code"),  _
     Global.System.ComponentModel.ToolboxItem(true),  _
     Global.System.ComponentModel.DataObjectAttribute(true),  _
     Global.System.ComponentModel.DesignerAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterDesigner, Microsoft.VSDesigner"& _ 
        ", Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"),  _
     Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
    Partial Public Class ResearchContractTableAdapter
        Inherits Global.System.ComponentModel.Component
        
        Private WithEvents _adapter As Global.System.Data.SqlClient.SqlDataAdapter
        
        Private _connection As Global.System.Data.SqlClient.SqlConnection
        
        Private _commandCollection() As Global.System.Data.SqlClient.SqlCommand
        
        Private _clearBeforeFill As Boolean
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New()
            MyBase.New
            Me.ClearBeforeFill = true
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private ReadOnly Property Adapter() As Global.System.Data.SqlClient.SqlDataAdapter
            Get
                If (Me._adapter Is Nothing) Then
                    Me.InitAdapter
                End If
                Return Me._adapter
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Property Connection() As Global.System.Data.SqlClient.SqlConnection
            Get
                If (Me._connection Is Nothing) Then
                    Me.InitConnection
                End If
                Return Me._connection
            End Get
            Set
                Me._connection = value
                If (Not (Me.Adapter.InsertCommand) Is Nothing) Then
                    Me.Adapter.InsertCommand.Connection = value
                End If
                If (Not (Me.Adapter.DeleteCommand) Is Nothing) Then
                    Me.Adapter.DeleteCommand.Connection = value
                End If
                If (Not (Me.Adapter.UpdateCommand) Is Nothing) Then
                    Me.Adapter.UpdateCommand.Connection = value
                End If
                Dim i As Integer = 0
                Do While (i < Me.CommandCollection.Length)
                    If (Not (Me.CommandCollection(i)) Is Nothing) Then
                        CType(Me.CommandCollection(i),Global.System.Data.SqlClient.SqlCommand).Connection = value
                    End If
                    i = (i + 1)
                Loop
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected ReadOnly Property CommandCollection() As Global.System.Data.SqlClient.SqlCommand()
            Get
                If (Me._commandCollection Is Nothing) Then
                    Me.InitCommandCollection
                End If
                Return Me._commandCollection
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ClearBeforeFill() As Boolean
            Get
                Return Me._clearBeforeFill
            End Get
            Set
                Me._clearBeforeFill = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitAdapter()
            Me._adapter = New Global.System.Data.SqlClient.SqlDataAdapter()
            Dim tableMapping As Global.System.Data.Common.DataTableMapping = New Global.System.Data.Common.DataTableMapping()
            tableMapping.SourceTable = "Table"
            tableMapping.DataSetTable = "ResearchContract"
            tableMapping.ColumnMappings.Add("ContractID", "ContractID")
            tableMapping.ColumnMappings.Add("AccountManagerID", "AccountManagerID")
            tableMapping.ColumnMappings.Add("ClientID", "ClientID")
            tableMapping.ColumnMappings.Add("ClientName", "ClientName")
            tableMapping.ColumnMappings.Add("ClientBillingAddress", "ClientBillingAddress")
            tableMapping.ColumnMappings.Add("ContractNumber", "ContractNumber")
            tableMapping.ColumnMappings.Add("Signed", "Signed")
            tableMapping.ColumnMappings.Add("SignDate", "SignDate")
            tableMapping.ColumnMappings.Add("SignedBy", "SignedBy")
            tableMapping.ColumnMappings.Add("SpecialConditions", "SpecialConditions")
            tableMapping.ColumnMappings.Add("ProjectName", "ProjectName")
            tableMapping.ColumnMappings.Add("ApplyAgencyComm", "ApplyAgencyComm")
            tableMapping.ColumnMappings.Add("AgencyID", "AgencyID")
            tableMapping.ColumnMappings.Add("AgencyName", "AgencyName")
            tableMapping.ColumnMappings.Add("AgencyCommPercentage", "AgencyCommPercentage")
            tableMapping.ColumnMappings.Add("Cancelled", "Cancelled")
            tableMapping.ColumnMappings.Add("CancelDate", "CancelDate")
            tableMapping.ColumnMappings.Add("CancelledBy", "CancelledBy")
            tableMapping.ColumnMappings.Add("CreatedBy", "CreatedBy")
            tableMapping.ColumnMappings.Add("CreationDate", "CreationDate")
            tableMapping.ColumnMappings.Add("BillingInstructions", "BillingInstructions")
            tableMapping.ColumnMappings.Add("ContractNotes", "ContractNotes")
            tableMapping.ColumnMappings.Add("ClassificationName", "ClassificationName")
            tableMapping.ColumnMappings.Add("PrintInfo", "PrintInfo")
            tableMapping.ColumnMappings.Add("FirstMonth", "FirstMonth")
            tableMapping.ColumnMappings.Add("LastMonth", "LastMonth")
            tableMapping.ColumnMappings.Add("Months", "Months")
            tableMapping.ColumnMappings.Add("ResearchFee", "ResearchFee")
            Me._adapter.TableMappings.Add(tableMapping)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitConnection()
            Me._connection = New Global.System.Data.SqlClient.SqlConnection()
            Me._connection.ConnectionString = Global.NovaReports.My.MySettings.Default.DBConnection
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitCommandCollection()
            Me._commandCollection = New Global.System.Data.SqlClient.SqlCommand(0) {}
            Me._commandCollection(0) = New Global.System.Data.SqlClient.SqlCommand()
            Me._commandCollection(0).Connection = Me.Connection
            Me._commandCollection(0).CommandText = "SELECT        Sales.Contract.ContractID, Sales.Contract.AccountManagerID, Sales.C"& _ 
                "ontract.ClientID, Sales.Contract.ClientName, Sales.Contract.ClientBillingAddress"& _ 
                ", "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         Sales.Contract.ContractNumber, Sales.Contract.Signe"& _ 
                "d, Sales.Contract.SignDate, Sales.Contract.SignedBy, Sales.Contract.SpecialCondi"& _ 
                "tions, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         Sales.Contract.ProjectName, Sales.Contract.App"& _ 
                "lyAgencyComm, Sales.Contract.AgencyID, Sales.Contract.AgencyName, Sales.Contract"& _ 
                ".AgencyCommPercentage, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         Sales.Contract.Cancelled, Sale"& _ 
                "s.Contract.CancelDate, Sales.Contract.CancelledBy, Sales.Contract.CreatedBy, Sal"& _ 
                "es.Contract.CreationDate, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         Sales.Contract.BillingInstr"& _ 
                "uctions, Sales.Contract.ContractNotes, Client.Classification.ClassificationName,"& _ 
                " 'Printed by ' + SUSER_SNAME() "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         + ' at ' + CONVERT(nva"& _ 
                "rchar(2), DATEPART(hour, GETDATE())) + N':' + CONVERT(nvarchar(2), DATEPART(minu"& _ 
                "te, GETDATE())) + ' on ' + DATENAME(weekday, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         GETDATE("& _ 
                ")) + ', ' + CONVERT(nvarchar(2), DATEPART(day, GETDATE())) + ' ' + DATENAME(mont"& _ 
                "h, GETDATE()) + ' ' + CONVERT(nvarchar(4), DATEPART(year, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                    "& _ 
                "     GETDATE())) AS PrintInfo, { fn MONTHNAME(dtFromDate.EarliestFromDate) } + '"& _ 
                " ' + CONVERT(nchar(4), YEAR(dtFromDate.EarliestFromDate)) AS FirstMonth, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"     "& _ 
                "                    { fn MONTHNAME(dtToDate.LatestToDate) } + ' ' + CONVERT(ncha"& _ 
                "r(4), YEAR(dtToDate.LatestToDate)) AS LastMonth, DATEDIFF(m, dtFromDate.Earliest"& _ 
                "FromDate, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         dtToDate.LatestToDate) + 1 AS Months, ISNUL"& _ 
                "L(dtResearchFee.ResearchFee, 0) AS ResearchFee"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"FROM            Sales.Contract I"& _ 
                "NNER JOIN"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         Client.Client ON Sales.Contract.ClientID = C"& _ 
                "lient.Client.ClientID INNER JOIN"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         Client.Classification"& _ 
                " ON Client.Client.ClassificationID = Client.Classification.ClassificationID INNE"& _ 
                "R JOIN"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                             (SELECT        ContractID, MAX(DATEADD(m, M"& _ 
                "onths - 1, FromDate)) AS LatestToDate"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                               FROM      "& _ 
                "      Sales.ResearchCategory AS ResearchCategory_2"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                            "& _ 
                "   GROUP BY ContractID) AS dtToDate ON Sales.Contract.ContractID = dtToDate.Cont"& _ 
                "ractID INNER JOIN"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                             (SELECT        ContractID, MIN(F"& _ 
                "romDate) AS EarliestFromDate"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                               FROM            Sal"& _ 
                "es.ResearchCategory AS ResearchCategory_1"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                               GROUP "& _ 
                "BY ContractID) AS dtFromDate ON Sales.Contract.ContractID = dtFromDate.ContractI"& _ 
                "D LEFT OUTER JOIN"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                             (SELECT        ContractID, SUM(F"& _ 
                "ee - Discount / 100 * Fee) AS ResearchFee"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                               FROM  "& _ 
                "          Sales.ResearchCategory"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                               GROUP BY Contra"& _ 
                "ctID) AS dtResearchFee ON Sales.Contract.ContractID = dtResearchFee.ContractID"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)& _ 
                "WHERE        (Sales.Contract.ContractID = @ContractID)"
            Me._commandCollection(0).CommandType = Global.System.Data.CommandType.Text
            Me._commandCollection(0).Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@ContractID", Global.System.Data.SqlDbType.UniqueIdentifier, 16, Global.System.Data.ParameterDirection.Input, 0, 0, "ContractID", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.Fill, true)>  _
        Public Overloads Overridable Function Fill(ByVal dataTable As DataSetContractReport.ResearchContractDataTable, ByVal ContractID As System.Guid) As Integer
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            Me.Adapter.SelectCommand.Parameters(0).Value = CType(ContractID,System.Guid)
            If (Me.ClearBeforeFill = true) Then
                dataTable.Clear
            End If
            Dim returnValue As Integer = Me.Adapter.Fill(dataTable)
            Return returnValue
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.[Select], true)>  _
        Public Overloads Overridable Function GetData(ByVal ContractID As System.Guid) As DataSetContractReport.ResearchContractDataTable
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            Me.Adapter.SelectCommand.Parameters(0).Value = CType(ContractID,System.Guid)
            Dim dataTable As DataSetContractReport.ResearchContractDataTable = New DataSetContractReport.ResearchContractDataTable()
            Me.Adapter.Fill(dataTable)
            Return dataTable
        End Function
    End Class
    
    '''<summary>
    '''Represents the connection and commands used to retrieve and save data.
    '''</summary>
    <Global.System.ComponentModel.DesignerCategoryAttribute("code"),  _
     Global.System.ComponentModel.ToolboxItem(true),  _
     Global.System.ComponentModel.DataObjectAttribute(true),  _
     Global.System.ComponentModel.DesignerAttribute("Microsoft.VSDesigner.DataSource.Design.TableAdapterDesigner, Microsoft.VSDesigner"& _ 
        ", Version=10.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"),  _
     Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter")>  _
    Partial Public Class ResearchCategoryTableAdapter
        Inherits Global.System.ComponentModel.Component
        
        Private WithEvents _adapter As Global.System.Data.SqlClient.SqlDataAdapter
        
        Private _connection As Global.System.Data.SqlClient.SqlConnection
        
        Private _commandCollection() As Global.System.Data.SqlClient.SqlCommand
        
        Private _clearBeforeFill As Boolean
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Sub New()
            MyBase.New
            Me.ClearBeforeFill = true
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private ReadOnly Property Adapter() As Global.System.Data.SqlClient.SqlDataAdapter
            Get
                If (Me._adapter Is Nothing) Then
                    Me.InitAdapter
                End If
                Return Me._adapter
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Friend Property Connection() As Global.System.Data.SqlClient.SqlConnection
            Get
                If (Me._connection Is Nothing) Then
                    Me.InitConnection
                End If
                Return Me._connection
            End Get
            Set
                Me._connection = value
                If (Not (Me.Adapter.InsertCommand) Is Nothing) Then
                    Me.Adapter.InsertCommand.Connection = value
                End If
                If (Not (Me.Adapter.DeleteCommand) Is Nothing) Then
                    Me.Adapter.DeleteCommand.Connection = value
                End If
                If (Not (Me.Adapter.UpdateCommand) Is Nothing) Then
                    Me.Adapter.UpdateCommand.Connection = value
                End If
                Dim i As Integer = 0
                Do While (i < Me.CommandCollection.Length)
                    If (Not (Me.CommandCollection(i)) Is Nothing) Then
                        CType(Me.CommandCollection(i),Global.System.Data.SqlClient.SqlCommand).Connection = value
                    End If
                    i = (i + 1)
                Loop
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Protected ReadOnly Property CommandCollection() As Global.System.Data.SqlClient.SqlCommand()
            Get
                If (Me._commandCollection Is Nothing) Then
                    Me.InitCommandCollection
                End If
                Return Me._commandCollection
            End Get
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Public Property ClearBeforeFill() As Boolean
            Get
                Return Me._clearBeforeFill
            End Get
            Set
                Me._clearBeforeFill = value
            End Set
        End Property
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitAdapter()
            Me._adapter = New Global.System.Data.SqlClient.SqlDataAdapter()
            Dim tableMapping As Global.System.Data.Common.DataTableMapping = New Global.System.Data.Common.DataTableMapping()
            tableMapping.SourceTable = "Table"
            tableMapping.DataSetTable = "ResearchCategory"
            tableMapping.ColumnMappings.Add("ResearchCategoryID", "ResearchCategoryID")
            tableMapping.ColumnMappings.Add("ContractID", "ContractID")
            tableMapping.ColumnMappings.Add("CategoryName", "CategoryName")
            tableMapping.ColumnMappings.Add("FirstMonth", "FirstMonth")
            tableMapping.ColumnMappings.Add("LastMonth", "LastMonth")
            tableMapping.ColumnMappings.Add("Months", "Months")
            tableMapping.ColumnMappings.Add("Fee", "Fee")
            tableMapping.ColumnMappings.Add("Discount", "Discount")
            tableMapping.ColumnMappings.Add("DiscountAmount", "DiscountAmount")
            Me._adapter.TableMappings.Add(tableMapping)
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitConnection()
            Me._connection = New Global.System.Data.SqlClient.SqlConnection()
            Me._connection.ConnectionString = Global.NovaReports.My.MySettings.Default.DBConnection
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********")>  _
        Private Sub InitCommandCollection()
            Me._commandCollection = New Global.System.Data.SqlClient.SqlCommand(0) {}
            Me._commandCollection(0) = New Global.System.Data.SqlClient.SqlCommand()
            Me._commandCollection(0).Connection = Me.Connection
            Me._commandCollection(0).CommandText = "SELECT        Sales.ResearchCategory.ResearchCategoryID, Sales.ResearchCategory.C"& _ 
                "ontractID, Store.Category.CategoryName, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         { fn MONTHNAM"& _ 
                "E(Sales.ResearchCategory.FromDate) } + ' ' + CONVERT(nchar(4), YEAR(Sales.Resear"& _ 
                "chCategory.FromDate)) AS FirstMonth, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         { fn MONTHNAME(D"& _ 
                "ATEADD(m, Sales.ResearchCategory.Months - 1, Sales.ResearchCategory.FromDate)) }"& _ 
                " + ' ' + CONVERT(nchar(4), YEAR(DATEADD(m, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         Sales.Rese"& _ 
                "archCategory.Months - 1, Sales.ResearchCategory.FromDate))) AS LastMonth, Sales."& _ 
                "ResearchCategory.Months, Sales.ResearchCategory.Fee, "&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         "& _ 
                "Sales.ResearchCategory.Discount, Sales.ResearchCategory.Discount / 100 * Sales.R"& _ 
                "esearchCategory.Fee AS DiscountAmount"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"FROM            Sales.ResearchCategory IN"& _ 
                "NER JOIN"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"                         Store.Category ON Sales.ResearchCategory.Cate"& _ 
                "goryID = Store.Category.CategoryID"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"WHERE        (Sales.ResearchCategory.Contrac"& _ 
                "tID = @ContractID)"&Global.Microsoft.VisualBasic.ChrW(13)&Global.Microsoft.VisualBasic.ChrW(10)&"ORDER BY Store.Category.CategoryName"
            Me._commandCollection(0).CommandType = Global.System.Data.CommandType.Text
            Me._commandCollection(0).Parameters.Add(New Global.System.Data.SqlClient.SqlParameter("@ContractID", Global.System.Data.SqlDbType.UniqueIdentifier, 16, Global.System.Data.ParameterDirection.Input, 0, 0, "ContractID", Global.System.Data.DataRowVersion.Current, false, Nothing, "", "", ""))
        End Sub
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.Fill, true)>  _
        Public Overloads Overridable Function Fill(ByVal dataTable As DataSetContractReport.ResearchCategoryDataTable, ByVal ContractID As System.Guid) As Integer
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            Me.Adapter.SelectCommand.Parameters(0).Value = CType(ContractID,System.Guid)
            If (Me.ClearBeforeFill = true) Then
                dataTable.Clear
            End If
            Dim returnValue As Integer = Me.Adapter.Fill(dataTable)
            Return returnValue
        End Function
        
        <Global.System.Diagnostics.DebuggerNonUserCodeAttribute(),  _
         Global.System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "********"),  _
         Global.System.ComponentModel.Design.HelpKeywordAttribute("vs.data.TableAdapter"),  _
         Global.System.ComponentModel.DataObjectMethodAttribute(Global.System.ComponentModel.DataObjectMethodType.[Select], true)>  _
        Public Overloads Overridable Function GetData(ByVal ContractID As System.Guid) As DataSetContractReport.ResearchCategoryDataTable
            Me.Adapter.SelectCommand = Me.CommandCollection(0)
            Me.Adapter.SelectCommand.Parameters(0).Value = CType(ContractID,System.Guid)
            Dim dataTable As DataSetContractReport.ResearchCategoryDataTable = New DataSetContractReport.ResearchCategoryDataTable()
            Me.Adapter.Fill(dataTable)
            Return dataTable
        End Function
    End Class
End Namespace
