<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformMediaLifeCycle
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformMediaLifeCycle))
        Me.LabelHeadingLifeCycleDetails = New DevExpress.XtraEditors.LabelControl
        Me.LabelLastWeek = New DevExpress.XtraEditors.LabelControl
        Me.LabelFirstWeek = New DevExpress.XtraEditors.LabelControl
        Me.ButtonOK = New DevExpress.XtraEditors.SimpleButton
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton
        Me.HyperlinkFirstWeek = New DevExpress.XtraEditors.LabelControl
        Me.HyperlinkLastWeek = New DevExpress.XtraEditors.LabelControl
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Size = New System.Drawing.Size(298, 30)
        Me.LabelTitle.TabIndex = 0
        Me.LabelTitle.Text = "Media Service Life Cycle"
        '
        'LabelHeadingLifeCycleDetails
        '
        Me.LabelHeadingLifeCycleDetails.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
                    Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelHeadingLifeCycleDetails.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelHeadingLifeCycleDetails.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelHeadingLifeCycleDetails.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelHeadingLifeCycleDetails.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelHeadingLifeCycleDetails.LineVisible = True
        Me.LabelHeadingLifeCycleDetails.Location = New System.Drawing.Point(12, 57)
        Me.LabelHeadingLifeCycleDetails.Margin = New System.Windows.Forms.Padding(3, 3, 3, 12)
        Me.LabelHeadingLifeCycleDetails.Name = "LabelHeadingLifeCycleDetails"
        Me.LabelHeadingLifeCycleDetails.Size = New System.Drawing.Size(674, 18)
        Me.LabelHeadingLifeCycleDetails.TabIndex = 1
        Me.LabelHeadingLifeCycleDetails.Text = "Media Service Life Cycle Options"
        '
        'LabelLastWeek
        '
        Me.LabelLastWeek.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelLastWeek.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelLastWeek.Location = New System.Drawing.Point(12, 119)
        Me.LabelLastWeek.Name = "LabelLastWeek"
        Me.LabelLastWeek.Size = New System.Drawing.Size(64, 13)
        Me.LabelLastWeek.TabIndex = 4
        Me.LabelLastWeek.Text = "Last Week:"
        '
        'LabelFirstWeek
        '
        Me.LabelFirstWeek.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelFirstWeek.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelFirstWeek.Location = New System.Drawing.Point(12, 93)
        Me.LabelFirstWeek.Name = "LabelFirstWeek"
        Me.LabelFirstWeek.Size = New System.Drawing.Size(65, 13)
        Me.LabelFirstWeek.TabIndex = 2
        Me.LabelFirstWeek.Text = "First Week:"
        '
        'ButtonOK
        '
        Me.ButtonOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonOK.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonOK.Appearance.Options.UseFont = True
        Me.ButtonOK.ImageIndex = 2
        Me.ButtonOK.ImageList = Me.ImageList24x24
        Me.ButtonOK.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonOK.Location = New System.Drawing.Point(480, 310)
        Me.ButtonOK.LookAndFeel.SkinName = "Black"
        Me.ButtonOK.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonOK.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonOK.Name = "ButtonOK"
        Me.ButtonOK.Size = New System.Drawing.Size(100, 28)
        Me.ButtonOK.TabIndex = 8
        Me.ButtonOK.Text = "OK"
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(586, 310)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(100, 28)
        Me.ButtonCancel.TabIndex = 9
        Me.ButtonCancel.Text = "Cancel"
        '
        'HyperlinkFirstWeek
        '
        Me.HyperlinkFirstWeek.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkFirstWeek.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkFirstWeek.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkFirstWeek.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkFirstWeek.Location = New System.Drawing.Point(122, 93)
        Me.HyperlinkFirstWeek.Name = "HyperlinkFirstWeek"
        Me.HyperlinkFirstWeek.Size = New System.Drawing.Size(47, 13)
        Me.HyperlinkFirstWeek.TabIndex = 10
        Me.HyperlinkFirstWeek.Text = "Select..."
        '
        'HyperlinkLastWeek
        '
        Me.HyperlinkLastWeek.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkLastWeek.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkLastWeek.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkLastWeek.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkLastWeek.Location = New System.Drawing.Point(122, 119)
        Me.HyperlinkLastWeek.Name = "HyperlinkLastWeek"
        Me.HyperlinkLastWeek.Size = New System.Drawing.Size(47, 13)
        Me.HyperlinkLastWeek.TabIndex = 10
        Me.HyperlinkLastWeek.Text = "Select..."
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        '
        'SubformMediaLifeCycle
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.Controls.Add(Me.HyperlinkLastWeek)
        Me.Controls.Add(Me.HyperlinkFirstWeek)
        Me.Controls.Add(Me.ButtonOK)
        Me.Controls.Add(Me.ButtonCancel)
        Me.Controls.Add(Me.LabelLastWeek)
        Me.Controls.Add(Me.LabelFirstWeek)
        Me.Controls.Add(Me.LabelHeadingLifeCycleDetails)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Name = "SubformMediaLifeCycle"
        Me.Size = New System.Drawing.Size(698, 350)
        Me.Controls.SetChildIndex(Me.LabelHeadingLifeCycleDetails, 0)
        Me.Controls.SetChildIndex(Me.LabelFirstWeek, 0)
        Me.Controls.SetChildIndex(Me.LabelLastWeek, 0)
        Me.Controls.SetChildIndex(Me.ButtonCancel, 0)
        Me.Controls.SetChildIndex(Me.ButtonOK, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkFirstWeek, 0)
        Me.Controls.SetChildIndex(Me.HyperlinkLastWeek, 0)
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents LabelHeadingLifeCycleDetails As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelLastWeek As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelFirstWeek As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ButtonOK As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents HyperlinkFirstWeek As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkLastWeek As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList

End Class
