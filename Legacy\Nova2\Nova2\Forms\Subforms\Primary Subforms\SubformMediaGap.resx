<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="ImageList16x16.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="ImageList16x16.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj00LjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAAAg
        FQAAAk1TRnQBSQFMAgEBBwEAAfQBAgH0AQIBEAEAARABAAT/AREBAAj/AUIBTQE2BwABNgMAASgDAAFA
        AwABIAMAAQEBAAEQBgABECAAAf8BfwF7AW8BnAFzFgAB/wF/Ab0BdwG8AXcBvAF3AbwBdwG9AXcB/wF/
        DgABnAFzAXsBbwH/AX8IAAH/AX8BewFvAXsBbzIAAf8BfwF7AXMBewFfAZwBbwF7AW8B/wF/EgABvQF3
        Ad4BfwE2AS4BtAEZAbgBRgH+AX8BnAF3DAAB/wF/AXwBbwGXAVIBvAF3AZwBcwGbAW8BewFvAZwBcwGc
        AXMBtwFWAVsBawHeAXssAAH/AX8BewFzAXsBdwHeAX8BOgFDAbcBEgGdAXMBewFzAf8Bfw4AAf8BfwGc
        AW8BMgEFARIBAQHxAQAB1QEpAVcBNgGcAXMB/wF/CgAB/wF/AZ0BcwGtARwBOgFnARkBYwFWAU4BVQFK
        AfkBXgF7AW8BrQEcAVsBawH/AX8qAAHeAXsBvQF7AZwBZwEZATsB2AEmAdgBHgG3ARYBtwEaAZwBawF7
        AXMB/wF/DAAB/wF/AXwBawHyAQABUwEBARIBAQHdAXsBnAF3Ad4BewwAAd4BewH/AX8BNQFKAc0BHAFs
        ARQBbAEUAWwBFAFsARQBrQEcAfQBQQH/AX8BvQF3KAAB/wF/Ad4BfwFaAUsBtwESAbcBFgG4ARoB2AEe
        AdgBHgG3ARoBtwEOAZwBawG9AXcOAAGcAXMBMwEBAXQBAQETAQEBfAFnAf8BfwwAAf8BfwHeAXsBNQFK
        AWwBFAHOASAB7gEkAe4BJAHuASQBzgEgAY0BGAFsARQB0wE9Af8BfwH/AX8mAAGcAXMBewFXAZYBCgG3
        ARIBtwEWAbcBFgG3ARoB2AEiAbcBFgHYASIBvQFzAd4Bew4AAb0BdwE4ASYBdQEBAXQBAQHaAUYBnAFz
        CgAB/wF/AZwBcwE7AWcBbAEUAe4BIAHvASQB7gEkAe4BIAGNARgBLwEpATUBSgHOARwBbAEUAdkBXgGc
        AXMB/wF/IgAB3gF7Ad4BewG3ARYB+QEqAToBRwFbAVMBWwFTAfkBLgGXARIB+QEuAd4BfwG9AXcB/wF/
        DgAB3gF7AX0BZwF1AQEBlgEBAXkBNgG9AXcB/wF/CAAB/wF/AZwBcwEUAUYBrQEcAQ8BKQEPASkBDwEl
        Ae8BJAEVAUYB3gF7ATUBSgHuASABzgEcAbMBOQG9AXcB/wF/IgABnAF3AZwBYwFbAU8B3QF7AZwBcwH/
        AX8B/wF/AVsBTwH5AS4B/gF/Ad4CewFvAb0BdwHeAXsMAAHeAXsB/wF/AdcBBQG2AQEB9wEVAf8BfwH/
        AX8IAAH/AX8B3gF7AfQBQQEPASUBUQEtAVEBLQEPASUBVgFKAd4BewFRAS0B7wEkAVEBLQEQASkBsgE5
        Ad4BewHeAXsiAAHeAXsB3gF7Ab0BewHeAXsBvQF3AVsBSwF8AVcB/gF/Af8BfwF7AW8BewFvAZ0BZwGd
        AWcBvQF3CAAB/wF/Ab0BdwG8AXcB/wF/ARgBGgHXAQEBtwEBAf8BfwHeAXsIAAH/AX8BvQJ3AVIBMAEp
        AZIBNQGSATUBUQEtAZcBVgG4AVYBEAEpAZIBNQGSATUBMAEtARUBRgG9AXcB/wF/JgABvQF3Af8BfwFb
        AVMB2AEiAVsBSwHeAXcB3gFzAb0BawF8AVcB+QEuAd4BdwHeAXsIAAHeAXsB/wF/AdsBOgG6AS4B2AEB
        AfgBAQHXAQkB/wF/Ad4BewoAAb0BdwGdAXMBcgExAbMBOQGzATkBcgE1AbgBVgH6AV4BcgExAbMBOQGz
        ATkBUQExAVwBawG9AXcB/wF/JAAB/wF/Af8BfwFbAU8B+QEyARoBOwEaATcBGQEzARkBNwEZATMB2AEq
        AXwBVwG9AXcKAAH/AX8B3QF7AX0BXwHbAT4BmgEuAbsBNgF9AV8B/wF/Af8BfwwAAb0BdwEaAWMBtAE5
        AdQBPQGzATkB2QFeARsBZwGzATkB1AE9AbMBOQHZAV4B3gF7Af8BfyYAAb0BdwG+AXMB+QEyAToBQwE7
        AUMBOwFDAToBQwE6AUMBGgE7AVsBTwH/AX8B/wF/DgAB/wF/Ab0BdwH/AX8BfQFbAXoBGgGeAWsBvQF3
        DAAB3gF7Af8BfwG5AVoBdwFSAfUBRQF4AVIBmAFWAfUBQQF3AVIBuQFaAd4BewG9AXcoAAH/AX8B/wF/
        Ab0BawE6AUMBOwFLATsBRwE7AUcBWwFLAZ0BZwH/AX8B3gF7EgAB3gF7AV4BVwHZAQEB+AEBAZcBAQGd
        AWsB3gF3CgAB3gF7Ab0BdwEWAUYBuQFaAdoBXgH6AWIBGwFjAdoBXgHaAV4BFgFGAVwBbwG9AXcqAAH/
        AX8B3gF7Ad4BcwE7AUcBnQFjAf8BfwH/AX8BvQF3Af8BfxQAAd4BewFdAVMB2QEBAfkBAQF2AQEBngFr
        Ad4BewoAAf8BfwH/AX8B+gFeATcBSgE3AUoBvgF3Af8BfwFXAU4BNwFKAbkBWgH/AX8B/wF/LAAB/wF/
        Ab0BdwHeAXMB3gFvAZwBcxoAAf8BfwHeAXsBPQFHAbsBOgFdAVcB3gF7Af8BfwwAAd4BewH/AX8B3gF7
        AVwBawFcAWsBXAFrAVwBawG+AXsB/wF/Ab0BdzIAAb0BdwHeAXsB/wF/HAAB/wF/Ab0BdwG9AXcBvQF3
        EgAB/wF/Ad4BewG9AXcBvQF3Ab0BdwG9AXcB3gF7Af8BfzIAATkBZwEZAWMBOgFnAToBZwE5AWcBGAFj
        CgABWgFrARgBYwE5AWcBGAFjARgBYxoAARgBYwFaAWcBGAFjATkBZwgAATkBZwEYAWMBWgFnARgBYxYA
        Af8BfwG9AXcBewFvAXsBbwH/AX8MAAFaAWsBWwFvATYBYwHQAU4B0AFOATUBXwGcAXMBOQFnCAABGAFj
        AXsBbwG1AVYBGAFjAd4BewEYAWMBGAFjFAAB9wFeAf8BewEQAVYB1gFmAZwBbwE5AWcEAAE5AWcBnAFv
        AdYBZgEQAVYB/wF7AfcBXhIAAb0BdwGdAXcBmwFvAa4BSgE2AWMBnAFzAf8BfwoAATkBZwG+AXcBIwEq
        AeABIQEAASIBAQEmAZwBcwEYAWMIAAEYAWMBnAFzAe4BPQIAAQgBIQF6AXMB3gF3AToBYwEYAWMOAAEY
        AV8B/wF7AWsBTQEAATABAAE0AdYBZgGcAW8BOQFnATkBZwGcAW8B1gFmAQABNAEAATABawFNAf8BewEY
        AV8MAAH/AX8BfAFvAb4BdwGsAUYBwAEdAYsBQgG9AXcBvQF3DAABOQFnAb4BdwFEATIBRAEyAUQBMgFD
        AS4BvAFzATkBZwgAAVoBawE5AWcBGAFjAecBHAGQAV4BVAF/ATUBewGXAXcBfQFrATkBZwwAAXsBawHO
        AVkBAAE0AWMBQAFBAUABAAE8AfcBagF7AW8BewFvAfcBagEAATwBQQFAAWMBQAEAATQBzgFZAXsBawwA
        AXwBbwGbAW8BJAEyAeABIQFmATYB/wF/Af8BfwgAAVoBawEYAWMBGAFjAfcBXgHeAXsBZAE2AWMBMgFk
        ATIBYwEyAbwBdwH3AV4BGAFjARgBYwFaAWsEAAE5AWcBvQFzAXcBewG6AX8BWAF/AQYBfwHgAX4BmAF3
        AToBZwwAAXsBawExAWIBAAE8AWIBRAFjAUQBQQFEAQABQAE5AW8BOQFvAQABQAFBAUQBYwFEAWIBRAEA
        ATwBMQFiAXsBawoAAb0BdwG9AXcBQwE2ASEBLgEgAS4B8AFSAZwBcwgAATkBZwFbAWsB3wF7Ad8BewHf
        AX8B3wF/AaUBOgGDATYBhAE2AYMBNgG+AXsB3wF/Ad8BewHfAXsBfAFvARgBYwIAAVoBawHeAXcBvAF/
        AZkBfwFIAX8BAAF/AQABfwEAAX8BvQF3ARgBYwoAATkBZwH/AXsBEAFiAQABQAFiAUgBgwFIAUIBSAFi
        AUgBYgFIAUIBSAGDAUgBYgFIAQABQAEQAWIB/wF7ATkBZwoAAb0BdwHtAU4BQAEuAWMBOgFAAS4BVQFj
        Ab0BdwgAATkBZwF2AWcBpQE6AcYBPgHHAT4BxwFCAaQBOgGkAToBpAE6AaQBNgHHAT4BxwE+AcYBPgGk
        AToBVAFfAVoBawQAAZwBbwG6AX8BjwF/AWoBfwEkAX8BAAF/AeABfgEjAXsB3wF3AfgBXgoAATkBZwH/
        AX8BMAFiAQABRAFiAUwBgwFMAWIBTAFiAUwBgwFMAWIBTAEAAUQBMAFiAf8BfwE5AWcKAAHeAXsBvQF3
        AWEBNgFiAToBgwE6AUABMgF4AWsBvQF3CAABewFvAS0BUwGgATIBwwE6AcMBOgHDAToBxAE+AcQBPgHE
        AT4BxAE+AcMBOgHDAToBwwE6AaABMgEKAUsBewFvBAABWgFnAdwBewGPAX8BjwF/AWsBfwEjAXsBAAF/
        AeABfgFKAXsB3wF3ARgBYwoAATkBZwG9AXcBUgFmAUEBTAFjAVABgwFQAYMBUAFjAVABQQFMAVIBZgHe
        AXsBOQFnDAABvQF3AXYBZwFgATYBowE+AaIBPgFgATYBmAFvAb0BdwgAAXsBbwFNAVMBwQE2AeQBPgHk
        AT4B5AE+AeQBPgHkAT4B5AE+AeQBPgHkAT4B5AE+AeQBPgHCAToBKwFPAXsBbwYAAXwBbwHaAXsBrwF/
        AY8BfwFKAX8BIwF7AQABfwHgAX4BjwF/Ab0BcwE5AWcIAAE5AWcBewFvAVoBdwFBAVABYwFUAYMBVAGD
        AVQBYwFUAUEBUAFaAXcBewFvATkBZwgAAd4BewF8AW8BvQF3AVMBYwGAAToBogFCAaIBQgGAAToBlwFr
        Ab0BdwF8AW8B3gF7Af8BfwIAAXsBbwFxAV8BwAE2AeEBOgHhAToB4gE+AeMBQgEEAUMBBAFDAeMBQgHi
        AT4B4QE6AeEBOgHAATYBTgFXAXsBbwYAAVoBawHeAXcB2AF/AY8BfwGPAX8BSQF/ASABfwEAAX8B7QFi
        AX0BbwF7AW8GAAE5AWcBnAFzATgBcwEgAVQBYgFUAYMBWAGDAVgBgwFYAYMBWAFiAVQBIAFUATgBcwGc
        AXMBOQFnBAAB/wF/Ad4BewFyAWcBTwFfAQgBUwGgAUIBwgFGAcIBRgGgAUIBCQFTAU8BXwFRAWMB3wF7
        Ad4BewIAATkBZwH/AX8B2wF3AdsBdwHcAXcB2wF3AQQBQwEDAUMBAwFDAQIBQwG6AXMB3AF3AdsBdwG7
        AXcB/wF/ATkBZwgAAToBZwH/AXsBtQF/AY8BfwGNAX8BaQF/AVYBZwGOAVEBYAFMAb0BewE5AWMBOQFn
        ATkBZwGcAXMBOAF3ASABWAFBAVgBgwFcAWIBXAEgAVgBIAFYAWIBXAGDAVwBQQFYASABWAE4AXcBnAFz
        ATkBZwIAAf8BfwG9AXcBcwFnAYABOgHAAUIB4gFKAeIBRgHiAUYB4gFKAcABQgGAAToBcgFnAb0BdwH/
        AX8EAAFaAWsBWgFrAVoBawE5AWcB/wF/AQMBRwECAUcBAwFHAQIBQwH/AX8BOQFnAVoBawFaAWsBWgFr
        DAABOgFnAf8BfwGyAX8BtAF7AVsBZwGqAV0BoAFYAUABTAFqAVkBnAFzAfcBXgE5AWcBGAF3AQABXAFB
        AVwBgwFgAWIBYAEAAVwBcwFyAXMBcgEAAVwBYgFgAYMBYAFBAVwBAAFcARgBdwE5AWcGAAHeAXsBlAFr
        AcABQgHhAUoB4QFKAeEBSgHhAUoBwAFCAZIBZwHeAXsB/wF/DAABOQFnAf8BfwEDAUsBAgFHAQIBRwEB
        AUcB/gF7ARgBYxQAAVoBawH/AX8BFgFrAYQBZQEgAWUBIgFdAUABTAEPAWYBnAFzARgBYwGcAXMBiwFp
        AQABXAGDAWQBYwFgAQABYAExAXIB3gF7Ad4BewExAXIBAAFgAYMBYAGDAWQBAAFcAYsBbQF7AW8GAAH/
        AX8B3gF7AZEBZwHgAUYBAAFPAQABTwHgAUYBcAFnAf8BfwH/AX8OAAE5AWcB/wF/AQABRwEAAUcBAAFH
        AQABQwH+AXsBOQFnFgABnAFzATUBewEgAW0BIAFlAcABWAFPAWoB/wF/AVoBawIAATkBZwHeAX8BxQFo
        AQABZAEAAWQBMAFyAf8BfwE5AWcBOQFnAf8BfwEwAXIBAAFkAQABZAHFAWgB3gF/ATkBZwgAAf8BfwH/
        AX8BjgFnAQABSwEAAUsBbAFjAf8BfwH/AX8QAAFaAWsB/wF/AY4BYwFFAVMBRQFTAWsBXwH/AX8BOQFn
        FgABWgFrAd4BewHzAXYBhQFpARYBdwHeAXsBWgFrBgABWgFrAf8BfwEIAW0BMAF2Af8BfwE5AWcEAAE5
        AWcB/wF/ATEBdgEIAW0B/wF/AVoBawwAAf8BfwH/AX8BjAFnAYsBYwH/AX8B/wF/FAABOQFnAZwBcwG9
        AXcBvQF3Ab0BdwE5AWcaAAE5AWcBnAFzAb0BdwF7AW8BWgFrCgABWgFrAb0BdwGcAXMBOQFnCAABOQFn
        AZwBcwG9AXcBWgFrEAAB/wF/Ab0BdwG9AXcB/wF/DAABQgFNAT4HAAE+AwABKAMAAUADAAEgAwABAQEA
        AQEGAAEBFgAD/wEAAf4BPwH4AQ8B4wHHAgAB/AEPAfgBDwHAAQMCAAHwAQcB8AEHAcABAwIAAeABAwHw
        AQ8BwAEDAgABwAEDAfgBHwGAAQECAAHAAQMB+AEfBAABgAEDAfgBDwQAAYABAQH4AQ8EAAGAAQEB4AEP
        BAAB4AEBAeABDwGAAwABwAEDAeABDwHAAQECAAHAAQMB+AEPAcABAwIAAcABBwH8AQcBwAEDAgAB4AEP
        AfwBBwHAAQMCAAHwAX8B/AEHAeABBwIAAfwBfwH+AR8B8AEPAgAB+AEfAQcB/wLDAf8BgwHwAQ8BAQH/
        AoEB/wEBAfABDwEAAX8CAAH8AQMB8AEPAQABPwIAAfwBBwGAAQEBgAE/AgAB+AEPAgABgAEfAgAB+AEP
        AgABwAEPAYABAQHwAQ8CAAHAAQcBwAEDAfABDwIAAeABAwHAAQMBwAEBAgAB4AEDAYABAQGAAQECAAHw
        AwABgAEBAYABAQH4AwAB4AEDAfABDwH8AwAB4AEHAfABDwH+AQECAAHwAQ8B8AEPAf4BAwKBAfgBHwH4
        AR8B/wEHAsMB/AE/Cw==
</value>
  </data>
  <metadata name="DescriptionColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="MediaFamilyNameColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="CategoryColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="ChainColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="BrandColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="FirstWeekColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="LastWeekColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="DurationColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="BookedByColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="BookTimeColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="ExpiryTimeColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="ContractNumberColumn.UserAddedColumn" type="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>True</value>
  </metadata>
  <metadata name="ImageList24x24.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>153, 17</value>
  </metadata>
  <metadata name="ImageList24x24.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>153, 17</value>
  </metadata>
  <data name="ImageList24x24.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        AAEAAAD/////AQAAAAAAAAAMAgAAAFdTeXN0ZW0uV2luZG93cy5Gb3JtcywgVmVyc2lvbj00LjAuMC4w
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAACI
        BgAAAk1TRnQBSQFMAwEBAAE4AQEBOAEBARgBAAEYAQAE/wERAQAI/wFCAU0BNgcAATYDAAEoAwABYAMA
        ARgDAAEBAQABEAYAARIoAAH/AX8BfAFvAXsBbwH/AX+4AAF7AW8BvQF3Ab0BfwF7AXMB/wF/tAAB/wF/
        AXsBbwF8AWMBGQE7Ab0BfwGcAXcB3gF7rAAB/wF/Ab0BdwFaAWsBewFzAd4BfwGcAWcBlgECAfkBLgG9
        AX8BvQF7Ab0Bd6gAAd4CewFvAd4BfwG9AX8BnAFrAXwBYwE6AUsBtwEWAbcBEgH5ASoBvQF7Ab0BfwGc
        AXOkAAGcAXMBvQF/Ad0BfwFbAVMB2AEiAbcBFgG3ARIBtwEWAdgBHgHYAR4BtwESAdgBJgG9AXMB3gF/
        AZwBc6AAAb0BdwHeAX8BvQFzAdgBIgG3ARIBtwEaAdgBHgHYAR4B2AEeAdgBHgHYAR4B2AEiAbcBFgHX
        ARoBnQFvAb0BewH/AX+cAAH/AX8BvQF7Ab0BdwHYAR4BtwEWAdgBIgHYASIB2AEiAdgBIgHYASIB2AEi
        AdgBIgHYASIB2AEeAZYBCgFbAU8B3gF/Af8Bf5wAAXsBbwHeAX8BGQEzAbcBFgHYAR4BtwEaAbcBGgG3
        ARoBtwEaAdgBHgHYASIB2AEiAdgBHgG3ARIBGQE7Ad4BfwGcAXOcAAH/AX8B3gF/AZwBYwGWAQoBtwEW
        AdgBHgH5ASoBGQE3ARkBNwH5AS4B2AEiAdgBHgHYAR4BtwESAToBRwH+AX8BvQF3Af8Bf5oAAf8BfwGc
        AXMB/gF/AfkBLgH4ASoBnQFnAd4BewH+AX8B/gF/Af8BfwH/AX8BnQFnAbcBGgG3ARYBWwFPAf8BfwG9
        AXcB/wF/AZwBcwHeAXuYAAH/AX8BnAFzAd4BdwEZATMB/gF/Af4BfwGcAXMBewFvAd4BfwHeAX8B3gF/
        AZ0BZwG3ARIBfAFbAf8BfwGcAXMB/wF/AZwBcwHeAX8B3gF7Ab0Bd5YAAf8BfwG9AXcB3gF7Ad4BewHe
        AX8B/wF/Ab0BdwH/AX8B3gF7AfkBKgGdAWsB3gF3AZ0BZwH/AX8BewFvAd4CewFvAf8BfwFbAVMB3gF3
        AZwBc5oAAb0BdwG9AXcB3gJ7AW8B/wF/Ad4BdwH5ATIB2AEeAb4BcwH/AX8B/wF/Af8BfwHeAXsB/wF/
        Af8BfwF8AVsB+QEuAf8BfwGcAXOcAAH/AX8BewFvAf8BfwG+AXMBGQEzAfkBMgH5ATIBWwFPAZ0BZwGd
        AWcBnQFnAZwBXwFbAU8BGgE7AdgBIgF8AVsB/wF/Ad4Be5wAAXsBbwH/AX8BvQFrARkBNwEZATcBGgE7
        ARoBOwEZATcB+QEyAfkBMgH5ATIB+QEyARkBMwEZATcBGgE7Af8BfwG9AXucAAH/AX8B/wF/Ab0BawEZ
        ATcBOgE/AToBQwE6AUMBOgFDAToBQwE6AUMBOgFDAToBQwE6AUMBOgE/ARkBNwG+AWsB/wF/Ad4Be5wA
        Af8BfwH/AX8BvQFnARkBNwE6AUMBOwFHATsBRwE7AUcBOwFHATsBRwE7AUcBOwFHAToBQwEaATsBnQFj
        Af8BfwG9AXegAAG9AXcB/wF/Ab0BZwE6AUMBOwFHAVsBSwE7AUsBOwFHATsBRwE6AUMBOgFDAVsBTwHe
        AXMB/wF/Ab0Bd6QAAb0BdwH/AX8B3gFzATsBSwFbAUsBWwFLAXwBVwF8AVsBnQFjAd4BcwH/AX8B/wF/
        Ab0Bd6gAAb0BdwH/AX8B3gF3AVsBTwE7AUsB3wF7Af8BfwH/AX8B/wF/Ab0BdwH/AX+sAAG9AXcB/wF/
        Af8BewFcAVMB3gF3Ad4BewH/AX+0AAHeAXsB/wF/Af8BfwH/AX8BnAF3uAAB3gF7Ab0BdwG9AXcB/wF/
        pgABQgFNAT4HAAE+AwABKAMAAWADAAEYAwABAQEAAQEFAAEgAQEWAAP/AQAB/wHhAf8JAAH/AeAB/wkA
        Af8BwAF/CQAB/gEAAT8JAAH8AQABHwkAAfgBAAEPCQAB8AEAAQcJAAHgAQABBwkAAeABAAEPCQABwAEA
        AQ8JAAGAAQABBwkAAYABAAEDCQABgAEAAQMJAAHgAQABAwkAAfABAAEDCQAB8AEAAQcJAAHgAQABBwkA
        AeABAAEPCQAB8AEAAR8JAAH4AQABPwkAAfwBAAF/CQAB/gEDAf8JAAH/AQcB/wkAAf8BhwH/CQAL
</value>
  </data>
</root>