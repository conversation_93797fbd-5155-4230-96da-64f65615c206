Public Class BrandCategory
    Inherits OldBaseObject

#Region "Fields"

    ' Custom fields
    Private _BrandCategoryMemberBindingSource As BindingSource

#End Region

#Region "Database Column Properties"

    Public ReadOnly Property BrandCategoryID() As Integer
        ' This object's identifier.
        Get
            Return Row("BrandCategoryID")
        End Get
    End Property

    Public Property BrandCategoryName() As String
        ' Descriptive name of the media.
        Get
            Return Row("BrandCategoryName")
        End Get
        Set(ByVal value As String)
            ' Clean the proposed new value.
            value = value.Trim
            If Not String.Compare(Row("BrandCategoryName"), value, False) = 0 Then
                ' New value is different than existing value. Proceed with update.
                AddLog("BrandCategoryName", "BrandCategoryName", value.ToString)
                Row("BrandCategoryName") = value
                IsDirty = True
            End If
        End Set
    End Property

    Public Property Brands() As String
        ' Descriptive name of the media.
        Get
            Return Row("Brands")
        End Get
        Set(ByVal value As String)
            ' Clean the proposed new value.
            value = value.Trim
            If Not String.Compare(Row("Brands"), value, False) = 0 Then
                ' New value is different than existing value. Proceed with update.
                Row("Brands") = value
                IsDirty = True
            End If
        End Set
    End Property

#End Region

#Region "Custom Properties"

    Public ReadOnly Property BrandCategoryMemberBindingSource() As BindingSource
        Get
            Return _BrandCategoryMemberBindingSource
        End Get
    End Property

#End Region

#Region "Public Shared Methods"

    Public Shared Function GetListData _
    (ByVal ConString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) _
    As System.Windows.Forms.BindingSource

        ' Instantiate a new data set and create a sql connection to use to populate it.
        Dim ListDataSet As New DataSetBrandCategory
        Dim SqlCon As New SqlClient.SqlConnection(ConString)

        ' Create table adapters to collect list data and related data.
        Dim ListAdapter As New DataSetBrandCategoryTableAdapters.BrandCategoryTableAdapter
        ListAdapter.Connection = SqlCon

        ' Populate tables in the dataset.
        Try
            ListAdapter.Fill(ListDataSet.Tables("BrandCategory"))
        Catch ex As Exception
            ErrorMessage += LiquidShell.LiquidAgent.GetErrorMessage(ex)
            Return Nothing
        Finally
            ListAdapter.Dispose()
            SqlCon.Dispose()
        End Try

        ' Return a binding source linked to the retrieved data.
        Dim ReturnBindingSource As BindingSource = New BindingSource(ListDataSet, "BrandCategory")
        ReturnBindingSource.Sort = "BrandCategoryName"
        Return ReturnBindingSource

    End Function

    Public Shared Sub Delete(ByVal Grid As DataGridView, ByVal ConnectionString As String)
        ' Delete one or more rows from the database.

        ' Build a list of data relation names of child rows that must be deleted with the parent row.
        Dim DeletableChildRelationNames As New List(Of String)
        DeletableChildRelationNames.Add("FK_BrandCategoryMember_BrandCategory")

        ' Create an audit table to record this action.
        Dim AuditLog As New DataSetAudit.AuditLogDataTable

        ' Create a list of names of the columns that will be used to describe each row being deleted.
        Dim ColumnNames As New List(Of String)
        ColumnNames.Add("BrandCategoryName")

        ' Add row descriptions to the Tag properties of all selected grid rows (these will be used as the object name
        ' in the audit log entries for rows that are deleted.)
        For Each SelectedGridRow As DataGridViewRow In Grid.SelectedRows
            Dim Row As DataRow = CType(SelectedGridRow.DataBoundItem, DataRowView).Row
            SelectedGridRow.Tag = Row("BrandCategoryName")
        Next

        ' Delete rows from the dataset.
        LiquidShell.LiquidAgent.DeleteParentRows _
        (Grid, String.Empty, DeletableChildRelationNames, AuditLog, String.Empty)

        ' Get the tables and data set from which rows are being deleted to use as a parameter for the table adapters.
        Dim GridTable As DataTable = CType(CType(Grid.DataSource, BindingSource).List, DataView).Table
        Dim DataSet As DataSet = GridTable.DataSet

        ' If any rows were deleted from the data set, delete them from the database too.
        Using DeletedRows As New DataView(GridTable, "", "", DataViewRowState.Deleted)
            If IsNothing(DeletedRows) = False AndAlso DeletedRows.Count > 0 Then

                ' Create and configure table adapters to connect to the database.
                Dim Adapter As New DataSetBrandCategoryTableAdapters.BrandCategoryTableAdapter
                Dim BrandCategoryMemberAdapter As New DataSetBrandCategoryTableAdapters.BrandCategoryMemberTableAdapter
                Dim AuditAdapter As New DataSetAuditTableAdapters.AuditLogTableAdapter

                ' Create a new connection for the table adapters to use.
                Dim SqlCon As New SqlClient.SqlConnection(ConnectionString)
                Adapter.Connection = SqlCon
                BrandCategoryMemberAdapter.Connection = SqlCon
                AuditAdapter.Connection = SqlCon

                ' Get the form that is consuming this method so that the ShowMessage method can be used.
                Dim Consumingform As LiquidShell.BaseForm = CType(Grid.TopLevelControl, LiquidShell.BaseForm)

                ' Perform the delete operation.
                Try
                    BrandCategoryMemberAdapter.Update(DataSet.Tables("BrandCategoryMember"))
                    Adapter.Update(GridTable)
                    AuditAdapter.Update(AuditLog)
                Catch ex As Exception
                    Consumingform.ShowMessage("Aaaagghh!!!  An error occured while trying to delete. The technical description of the error is:" _
                    & vbCrLf & vbCrLf & LiquidShell.LiquidAgent.GetErrorMessage(ex))
                Finally
                    ' Dispose all data adapters.
                    Adapter.Dispose()
                    BrandCategoryMemberAdapter.Dispose()
                    AuditAdapter.Dispose()
                    ' Close all connections.
                    SqlCon.Dispose()
                End Try

            End If
        End Using

    End Sub

#End Region

#Region "Public Methods"

    Public Sub New(ByVal DataSource As BindingSource, ByVal AddNew As Boolean)
        ' This constructor takes a binding source and a boolean indicating whether a new
        ' row must be added or whether this is an existing row being edited.

        If AddNew Then
            ' This is a new row being added.
            DataSource.AddNew()
        End If

        ' Update the base class binding source variable.
        DataBindingSource = DataSource

    End Sub

    Public Sub Save(ByVal ConnectionString As String, ByVal GridsToAudit As List(Of DataGridView))
        ' Save this object to the database.

        ' Check if this is a new row being added.
        If Row.RowState = DataRowState.Detached Then
            ' This is a new row being added. Clear the audit log and create only one entry for the new row.
            AuditLog.Clear()
            AddLog("BrandCategoryName")
        Else
            ' This row was edited. Update audit log with child row addition details.
            For Each Grid As DataGridView In GridsToAudit
                ' Check each row in this grid.
                For Each GridRow As DataGridViewRow In Grid.Rows
                    ' Get the datarow feeding this grid row.
                    Dim Row As DataRow = CType(GridRow.DataBoundItem, DataRowView).Row
                    If Row.RowState = DataRowState.Added Then
                        ' This row is newly added. Add an entry for it into the audit log.
                        Dim ActionText As String = BuildChildRowActionText(Grid, GridRow, ChildRowAction.Added)
                        LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, Table.TableName, BrandCategoryName, ActionText)
                    End If
                    If Row.RowState = DataRowState.Modified Then
                        ' This row has been modified. Add an entry for it into the audit log.
                        Dim ActionText As String = BuildChildRowActionText(Grid, GridRow, ChildRowAction.Modified)
                        LiquidShell.LiquidAgent.AddAuditLogEntry(AuditLog, Table.TableName, BrandCategoryName, ActionText)
                    End If
                Next
            Next
        End If

        ' Create and configure table adapters to connect to the database.
        Dim SqlAdapter As New DataSetBrandCategoryTableAdapters.BrandCategoryTableAdapter
        Dim BrandCategoryMemberAdapter As New DataSetBrandCategoryTableAdapters.BrandCategoryMemberTableAdapter
        Dim AuditAdapter As New DataSetAuditTableAdapters.AuditLogTableAdapter

        ' Create a new connection for the table adapters to use.
        Dim SqlCon As New SqlClient.SqlConnection(ConnectionString)
        SqlAdapter.Connection = SqlCon
        BrandCategoryMemberAdapter.Connection = SqlCon
        AuditAdapter.Connection = SqlCon

        ' Perform the save operation.
        Try
            ' Remember the current item in the binding source list.
            Dim CurrentItem As Object = DataBindingSource.Current
            ' Commit changes to the dataset.
            DataBindingSource.EndEdit()     ' This line changes the binding source's current item.
            ' Restore the current item in the binding source list.
            DataBindingSource.Position = DataBindingSource.IndexOf(CurrentItem)
            ' Update the database.
            SqlAdapter.Update(Row)
            BrandCategoryMemberAdapter.Update(DataSet.Tables("BrandCategoryMember"))
            IsDirty = False
            AuditAdapter.Update(AuditLog)
        Finally
            ' Dispose all data adapters.
            SqlAdapter.Dispose()
            BrandCategoryMemberAdapter.Dispose()
            AuditAdapter.Dispose()
            ' Dispose the connection.
            SqlCon.Close()
        End Try

    End Sub

    Public Sub AddBrandCategoryMember _
    (ByVal ConsumingSubform As LiquidShell.Subform,
    ByVal ConnectionString As String,
    ByVal SelectedItems As List(Of DataRow))

        ' Check for legal selection by user.
        If IsNothing(SelectedItems) Then
            Exit Sub
        Else
            If SelectedItems.Count = 0 Then
                Exit Sub
            End If
        End If

        ' Add a new row to the table for each item that was selected.
        For Each SelectedItem As DataRow In SelectedItems
            ' Create a new row for the table.
            Dim NewRow As DataRow = DataSet.Tables("BrandCategoryMember").NewRow
            NewRow("BrandCategoryID") = Row("BrandCategoryID")
            NewRow("BrandID") = SelectedItem("BrandID")
            DataSet.Tables("BrandCategoryMember").Rows.Add(NewRow)
            ' Flag the object as dirty.
            IsDirty = True
        Next

        ' Update the item list column of the parent row.
        UpdateBrands()

    End Sub

    Public Sub UpdateBrands()

        ' Create a string builder to hold the list of brands.
        Dim BrandsBuilder As New System.Text.StringBuilder

        ' Cycle throught the brands to build the list.
        For Each Brand As DataRow In Row.GetChildRows("FK_BrandCategoryMember_BrandCategory")
            If BrandsBuilder.ToString.Length > 0 Then
                BrandsBuilder.Append(", ")
            End If
            BrandsBuilder.Append(Brand("BrandName"))
        Next

        ' Update the brands property.
        Brands = BrandsBuilder.ToString

    End Sub

#End Region

#Region "Private Methods"

    Protected Overrides Sub UpdateProperties()
        ' Update properties to match the current Row.
        UpdateCustomProperties()
    End Sub

    Private Sub UpdateCustomProperties()
        _BrandCategoryMemberBindingSource = New BindingSource(DataBindingSource, "FK_BrandCategoryMember_BrandCategory")
    End Sub

#End Region

End Class
