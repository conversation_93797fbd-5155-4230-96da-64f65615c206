<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DataSetAccountManager" targetNamespace="http://tempuri.org/DataSetAccountManager.xsd" xmlns:mstns="http://tempuri.org/DataSetAccountManager.xsd" xmlns="http://tempuri.org/DataSetAccountManager.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections>
          <Connection AppSettingsObjectName="MySettings" AppSettingsPropertyName="DBConnection" ConnectionStringObject="" IsAppSettingsProperty="true" Modifier="Public" Name="DBConnection (MySettings)" ParameterPrefix="@" PropertyReference="ApplicationSettings.NovaData.My.MySettings.GlobalReference.Default.DBConnection" Provider="System.Data.SqlClient" />
        </Connections>
        <Tables>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ContractCountTableAdapter" GeneratorDataComponentClassName="ContractCountTableAdapter" Name="ContractCount" UserDataComponentName="ContractCountTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Sales.Contract" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT        AccountManagerID, COUNT(ContractID) AS ContractCount
FROM            Sales.Contract
GROUP BY AccountManagerID</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="AccountManagerID" DataSetColumn="AccountManagerID" />
              <Mapping SourceColumn="ContractCount" DataSetColumn="ContractCount" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ClientTableAdapter" GeneratorDataComponentClassName="ClientTableAdapter" Name="Client" UserDataComponentName="ClientTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Client.Client" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT        ClientID, ClientName
FROM            Client.Client</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ClientID" DataSetColumn="ClientID" />
              <Mapping SourceColumn="ClientName" DataSetColumn="ClientName" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="vClientAccountManagerDatesTableAdapter" GeneratorDataComponentClassName="vClientAccountManagerDatesTableAdapter" Name="vClientAccountManagerDates" UserDataComponentName="vClientAccountManagerDatesTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Client.vClientAccountManagerDates" DbObjectType="View" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT        ClientID, AccountManagerID, [From] AS EffectiveDate, [To] AS EndDate
FROM            Client.vClientAccountManagerDates
WHERE        (AccountManagerID = @AccountManagerID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="AccountManagerID" ColumnName="AccountManagerID" DataSourceName="NovaDB.Client.vClientAccountManagerDates" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ClientID" DataSetColumn="ClientID" />
              <Mapping SourceColumn="AccountManagerID" DataSetColumn="AccountManagerID" />
              <Mapping SourceColumn="EffectiveDate" DataSetColumn="EffectiveDate" />
              <Mapping SourceColumn="EndDate" DataSetColumn="EndDate" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ClientAccountManagerTableAdapter" GeneratorDataComponentClassName="ClientAccountManagerTableAdapter" Name="ClientAccountManager" UserDataComponentName="ClientAccountManagerTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Client.ClientAccountManager" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Client].[ClientAccountManager] WHERE (([ClientID] = @Original_ClientID) AND ([AccountManagerID] = @Original_AccountManagerID) AND ([EffectiveDate] = @Original_EffectiveDate) AND ([CareTaker] = @Original_CareTaker) AND ([CareTakerWeeks] = @Original_CareTakerWeeks))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ClientID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_EffectiveDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="EffectiveDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_CareTaker" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="CareTaker" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_CareTakerWeeks" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CareTakerWeeks" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Client].[ClientAccountManager] ([ClientID], [AccountManagerID], [EffectiveDate], [CareTaker], [CareTakerWeeks]) VALUES (@ClientID, @AccountManagerID, @EffectiveDate, @CareTaker, @CareTakerWeeks);
SELECT ClientID, AccountManagerID, EffectiveDate, CareTaker, CareTakerWeeks FROM Client.ClientAccountManager WHERE (AccountManagerID = @AccountManagerID) AND (ClientID = @ClientID) AND (EffectiveDate = @EffectiveDate)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ClientID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@EffectiveDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="EffectiveDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@CareTaker" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="CareTaker" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@CareTakerWeeks" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CareTakerWeeks" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT        ClientID, AccountManagerID, EffectiveDate, CareTaker, CareTakerWeeks
FROM            Client.ClientAccountManager
WHERE        (AccountManagerID = @AccountManagerID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="AccountManagerID" ColumnName="AccountManagerID" DataSourceName="NovaDB.Client.ClientAccountManager" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Client].[ClientAccountManager] SET [ClientID] = @ClientID, [AccountManagerID] = @AccountManagerID, [EffectiveDate] = @EffectiveDate, [CareTaker] = @CareTaker, [CareTakerWeeks] = @CareTakerWeeks WHERE (([ClientID] = @Original_ClientID) AND ([AccountManagerID] = @Original_AccountManagerID) AND ([EffectiveDate] = @Original_EffectiveDate) AND ([CareTaker] = @Original_CareTaker) AND ([CareTakerWeeks] = @Original_CareTakerWeeks));
SELECT ClientID, AccountManagerID, EffectiveDate, CareTaker, CareTakerWeeks FROM Client.ClientAccountManager WHERE (AccountManagerID = @AccountManagerID) AND (ClientID = @ClientID) AND (EffectiveDate = @EffectiveDate)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@ClientID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@EffectiveDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="EffectiveDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@CareTaker" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="CareTaker" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@CareTakerWeeks" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CareTakerWeeks" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_ClientID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="ClientID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_EffectiveDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="EffectiveDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_CareTaker" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="CareTaker" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_CareTakerWeeks" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CareTakerWeeks" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ClientID" DataSetColumn="ClientID" />
              <Mapping SourceColumn="AccountManagerID" DataSetColumn="AccountManagerID" />
              <Mapping SourceColumn="EffectiveDate" DataSetColumn="EffectiveDate" />
              <Mapping SourceColumn="CareTaker" DataSetColumn="CareTaker" />
              <Mapping SourceColumn="CareTakerWeeks" DataSetColumn="CareTakerWeeks" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="LinkedContractsByClientPeriodTableAdapter" GeneratorDataComponentClassName="LinkedContractsByClientPeriodTableAdapter" Name="LinkedContractsByClientPeriod" UserDataComponentName="LinkedContractsByClientPeriodTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Client.vClientAccountManagerPeriodsWithActivity" DbObjectType="View" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT        ClientID, AccountManagerID, [From], ContractNumber
FROM            Client.vClientAccountManagerPeriodsWithActivity
WHERE        (AccountManagerID = @AccountManagerID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="AccountManagerID" ColumnName="AccountManagerID" DataSourceName="NovaDB.Client.vClientAccountManagerPeriodsWithActivity" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="ClientID" DataSetColumn="ClientID" />
              <Mapping SourceColumn="AccountManagerID" DataSetColumn="AccountManagerID" />
              <Mapping SourceColumn="From" DataSetColumn="From" />
              <Mapping SourceColumn="ContractNumber" DataSetColumn="ContractNumber" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ExistingAccountManagersTableAdapter" GeneratorDataComponentClassName="ExistingAccountManagersTableAdapter" Name="ExistingAccountManagers" UserDataComponentName="ExistingAccountManagersTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Client.ClientAccountManager" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT        Sales.AccountManager.FirstName + N' ' + Sales.AccountManager.LastName AS AccountManagerFullName, Client.ClientAccountManager.ClientID, 
                         Client.ClientAccountManager.EffectiveDate
FROM            Client.ClientAccountManager INNER JOIN
                         Sales.AccountManager ON Client.ClientAccountManager.AccountManagerID = Sales.AccountManager.AccountManagerID
WHERE        (Client.ClientAccountManager.ClientID = @ClientID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="ClientID" ColumnName="ClientID" DataSourceName="NovaDB.Client.ClientAccountManager" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@ClientID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="ClientID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="AccountManagerFullName" DataSetColumn="AccountManagerFullName" />
              <Mapping SourceColumn="ClientID" DataSetColumn="ClientID" />
              <Mapping SourceColumn="EffectiveDate" DataSetColumn="EffectiveDate" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="AccountManagerBudgetsTableAdapter" GeneratorDataComponentClassName="AccountManagerBudgetsTableAdapter" Name="AccountManagerBudgets" UserDataComponentName="AccountManagerBudgetsTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Sales.AccountManagerBudgets" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Sales].[AccountManagerBudgets] WHERE (([AccountManagerID] = @Original_AccountManagerID) AND ([FiscalID] = @Original_FiscalID) AND ([Budget] = @Original_Budget))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_FiscalID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="FiscalID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Currency" Direction="Input" ParameterName="@Original_Budget" Precision="0" ProviderType="Money" Scale="0" Size="0" SourceColumn="Budget" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Sales].[AccountManagerBudgets] ([AccountManagerID], [FiscalID], [Budget]) VALUES (@AccountManagerID, @FiscalID, @Budget);
SELECT AccountManagerID, FiscalID, Budget FROM Sales.AccountManagerBudgets WHERE (AccountManagerID = @AccountManagerID) AND (FiscalID = @FiscalID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@FiscalID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="FiscalID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Currency" Direction="Input" ParameterName="@Budget" Precision="0" ProviderType="Money" Scale="0" Size="0" SourceColumn="Budget" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT        AccountManagerID, FiscalID, Budget
FROM            Sales.AccountManagerBudgets
WHERE        (AccountManagerID = @AccountManagerID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="AccountManagerID" ColumnName="AccountManagerID" DataSourceName="NovaDB.Sales.AccountManagerBudgets" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Sales].[AccountManagerBudgets] SET [AccountManagerID] = @AccountManagerID, [FiscalID] = @FiscalID, [Budget] = @Budget WHERE (([AccountManagerID] = @Original_AccountManagerID) AND ([FiscalID] = @Original_FiscalID) AND ([Budget] = @Original_Budget));
SELECT AccountManagerID, FiscalID, Budget FROM Sales.AccountManagerBudgets WHERE (AccountManagerID = @AccountManagerID) AND (FiscalID = @FiscalID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@FiscalID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="FiscalID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Currency" Direction="Input" ParameterName="@Budget" Precision="0" ProviderType="Money" Scale="0" Size="0" SourceColumn="Budget" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_FiscalID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="FiscalID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Currency" Direction="Input" ParameterName="@Original_Budget" Precision="0" ProviderType="Money" Scale="0" Size="0" SourceColumn="Budget" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="AccountManagerID" DataSetColumn="AccountManagerID" />
              <Mapping SourceColumn="FiscalID" DataSetColumn="FiscalID" />
              <Mapping SourceColumn="Budget" DataSetColumn="Budget" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="FiscalTableAdapter" GeneratorDataComponentClassName="FiscalTableAdapter" Name="Fiscal" UserDataComponentName="FiscalTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Sales.Fiscal" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT        FiscalID, FiscalStartDate, FiscalName
FROM            Sales.Fiscal</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="FiscalID" DataSetColumn="FiscalID" />
              <Mapping SourceColumn="FiscalStartDate" DataSetColumn="FiscalStartDate" />
              <Mapping SourceColumn="FiscalName" DataSetColumn="FiscalName" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="AccountManagerPermissionTableAdapter" GeneratorDataComponentClassName="AccountManagerPermissionTableAdapter" Name="AccountManagerPermission" UserDataComponentName="AccountManagerPermissionTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Sales.AccountManagerPermission" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT        AccountManagerPermissionID, AccountManagerPermissionName
FROM            Sales.AccountManagerPermission</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="AccountManagerPermissionID" DataSetColumn="AccountManagerPermissionID" />
              <Mapping SourceColumn="AccountManagerPermissionName" DataSetColumn="AccountManagerPermissionName" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="sql_loginsTableAdapter" GeneratorDataComponentClassName="sql_loginsTableAdapter" Name="sql_logins" UserDataComponentName="sql_loginsTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.sys.sql_logins" DbObjectType="View" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="false" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT        principal_id, name
FROM            sys.sql_logins</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="principal_id" DataSetColumn="principal_id" />
              <Mapping SourceColumn="name" DataSetColumn="name" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="AccountManagerPermissionUserTableAdapter" GeneratorDataComponentClassName="AccountManagerPermissionUserTableAdapter" Name="AccountManagerPermissionUser" UserDataComponentName="AccountManagerPermissionUserTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Sales.AccountManagerPermissionUser" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Sales].[AccountManagerPermissionUser] WHERE (([AccountManagerPermissionID] = @Original_AccountManagerPermissionID) AND ([AccountManagerID] = @Original_AccountManagerID) AND ([principal_id] = @Original_principal_id))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_AccountManagerPermissionID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AccountManagerPermissionID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_principal_id" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="principal_id" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Sales].[AccountManagerPermissionUser] ([AccountManagerPermissionID], [AccountManagerID], [principal_id]) VALUES (@AccountManagerPermissionID, @AccountManagerID, @principal_id);
SELECT AccountManagerPermissionID, AccountManagerID, principal_id FROM Sales.AccountManagerPermissionUser WHERE (AccountManagerID = @AccountManagerID) AND (AccountManagerPermissionID = @AccountManagerPermissionID) AND (principal_id = @principal_id)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@AccountManagerPermissionID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AccountManagerPermissionID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@principal_id" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="principal_id" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT        AccountManagerPermissionID, AccountManagerID, principal_id
FROM            Sales.AccountManagerPermissionUser
WHERE        (AccountManagerID = @AccountManagerID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="AccountManagerID" ColumnName="AccountManagerID" DataSourceName="NovaDB.Sales.AccountManagerPermissionUser" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Sales].[AccountManagerPermissionUser] SET [AccountManagerPermissionID] = @AccountManagerPermissionID, [AccountManagerID] = @AccountManagerID, [principal_id] = @principal_id WHERE (([AccountManagerPermissionID] = @Original_AccountManagerPermissionID) AND ([AccountManagerID] = @Original_AccountManagerID) AND ([principal_id] = @Original_principal_id));
SELECT AccountManagerPermissionID, AccountManagerID, principal_id FROM Sales.AccountManagerPermissionUser WHERE (AccountManagerID = @AccountManagerID) AND (AccountManagerPermissionID = @AccountManagerPermissionID) AND (principal_id = @principal_id)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@AccountManagerPermissionID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AccountManagerPermissionID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@principal_id" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="principal_id" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_AccountManagerPermissionID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AccountManagerPermissionID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_principal_id" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="principal_id" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="AccountManagerPermissionID" DataSetColumn="AccountManagerPermissionID" />
              <Mapping SourceColumn="AccountManagerID" DataSetColumn="AccountManagerID" />
              <Mapping SourceColumn="principal_id" DataSetColumn="principal_id" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="AccountManagerTableAdapter" GeneratorDataComponentClassName="AccountManagerTableAdapter" Name="AccountManager" UserDataComponentName="AccountManagerTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Sales].[AccountManager] WHERE (([AccountManagerID] = @Original_AccountManagerID) AND ([principal_id] = @Original_principal_id) AND ([FirstName] = @Original_FirstName) AND ([LastName] = @Original_LastName) AND ([Email] = @Original_Email) AND ([Code] = @Original_Code) AND ([Dormant] = @Original_Dormant))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_principal_id" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="principal_id" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_FirstName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="FirstName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_LastName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="LastName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Email" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Email" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Code" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Code" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Dormant" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Sales].[AccountManager] ([principal_id], [FirstName], [LastName], [Email], [Code], [Dormant]) VALUES (@principal_id, @FirstName, @LastName, @Email, @Code, @Dormant);
SELECT AccountManagerID, principal_id, FirstName, LastName, Email, Code, Dormant FROM Sales.AccountManager WHERE (AccountManagerID = SCOPE_IDENTITY())</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@principal_id" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="principal_id" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@FirstName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="FirstName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@LastName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="LastName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Email" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Email" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Code" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Code" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Dormant" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT        Sales.AccountManager.AccountManagerID, Sales.AccountManager.principal_id, Sales.AccountManager.FirstName, Sales.AccountManager.LastName, 
                         Sales.AccountManager.Email, Sales.AccountManager.Code, Sales.AccountManager.Dormant, sys.sql_logins.name AS Username
FROM            Sales.AccountManager LEFT OUTER JOIN
                         sys.sql_logins ON Sales.AccountManager.principal_id = sys.sql_logins.principal_id</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Sales].[AccountManager] SET [principal_id] = @principal_id, [FirstName] = @FirstName, [LastName] = @LastName, [Email] = @Email, [Code] = @Code, [Dormant] = @Dormant WHERE (([AccountManagerID] = @Original_AccountManagerID) AND ([principal_id] = @Original_principal_id) AND ([FirstName] = @Original_FirstName) AND ([LastName] = @Original_LastName) AND ([Email] = @Original_Email) AND ([Code] = @Original_Code) AND ([Dormant] = @Original_Dormant));
SELECT AccountManagerID, principal_id, FirstName, LastName, Email, Code, Dormant FROM Sales.AccountManager WHERE (AccountManagerID = @AccountManagerID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@principal_id" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="principal_id" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@FirstName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="FirstName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@LastName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="LastName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Email" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Email" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Code" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Code" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Dormant" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_principal_id" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="principal_id" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_FirstName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="FirstName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_LastName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="LastName" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Email" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Email" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_Code" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="Code" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_Dormant" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="Dormant" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="AccountManagerID" ColumnName="AccountManagerID" DataSourceName="" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="AccountManagerID" DataSetColumn="AccountManagerID" />
              <Mapping SourceColumn="principal_id" DataSetColumn="principal_id" />
              <Mapping SourceColumn="FirstName" DataSetColumn="FirstName" />
              <Mapping SourceColumn="LastName" DataSetColumn="LastName" />
              <Mapping SourceColumn="Email" DataSetColumn="Email" />
              <Mapping SourceColumn="Code" DataSetColumn="Code" />
              <Mapping SourceColumn="Dormant" DataSetColumn="Dormant" />
              <Mapping SourceColumn="Username" DataSetColumn="Username" />
            </Mappings>
            <Sources>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectType="Unknown" FillMethodModifier="Public" FillMethodName="FillByAccountManager" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetDataByAccountManager" GeneratorSourceName="FillByAccountManager" GetMethodModifier="Public" GetMethodName="GetDataByAccountManager" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetDataByAccountManager" UserSourceName="FillByAccountManager">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT        Sales.AccountManager.AccountManagerID, Sales.AccountManager.principal_id, Sales.AccountManager.FirstName, Sales.AccountManager.LastName, 
                         Sales.AccountManager.Email, Sales.AccountManager.Code, Sales.AccountManager.Dormant, sys.sql_logins.name AS Username
FROM            Sales.AccountManager LEFT OUTER JOIN
                         sys.sql_logins ON Sales.AccountManager.principal_id = sys.sql_logins.principal_id
WHERE        (sys.sql_logins.name = @Username)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="Username" ColumnName="name" DataSourceName="NovaDB.sys.sql_logins" DataTypeServer="sysname" DbType="String" Direction="Input" ParameterName="@Username" Precision="0" ProviderType="NVarChar" Scale="0" Size="256" SourceColumn="Username" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </Sources>
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="BrandAccountManagerTableAdapter" GeneratorDataComponentClassName="BrandAccountManagerTableAdapter" Name="BrandAccountManager" UserDataComponentName="BrandAccountManagerTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Client.BrandAccountManager" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="true" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <DeleteCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>DELETE FROM [Client].[BrandAccountManager] WHERE (([BrandID] = @Original_BrandID) AND ([AccountManagerID] = @Original_AccountManagerID) AND ([EffectiveDate] = @Original_EffectiveDate) AND ([CareTaker] = @Original_CareTaker) AND ([CareTakerWeeks] = @Original_CareTakerWeeks))</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BrandID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_EffectiveDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="EffectiveDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_CareTaker" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="CareTaker" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_CareTakerWeeks" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CareTakerWeeks" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </DeleteCommand>
                <InsertCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>INSERT INTO [Client].[BrandAccountManager] ([BrandID], [AccountManagerID], [EffectiveDate], [CareTaker], [CareTakerWeeks]) VALUES (@BrandID, @AccountManagerID, @EffectiveDate, @CareTaker, @CareTakerWeeks);
SELECT BrandID, AccountManagerID, EffectiveDate, CareTaker, CareTakerWeeks FROM Client.BrandAccountManager WHERE (BrandID = @BrandID) AND (EffectiveDate = @EffectiveDate)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BrandID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@EffectiveDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="EffectiveDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@CareTaker" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="CareTaker" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@CareTakerWeeks" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CareTakerWeeks" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </InsertCommand>
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT BrandID, AccountManagerID, EffectiveDate, CareTaker, CareTakerWeeks FROM Client.BrandAccountManager WHERE (AccountManagerID = @AccountManagerID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="AccountManagerID" ColumnName="AccountManagerID" DataSourceName="NovaDB.Client.BrandAccountManager" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Client].[BrandAccountManager] SET [BrandID] = @BrandID, [AccountManagerID] = @AccountManagerID, [EffectiveDate] = @EffectiveDate, [CareTaker] = @CareTaker, [CareTakerWeeks] = @CareTakerWeeks WHERE (([BrandID] = @Original_BrandID) AND ([AccountManagerID] = @Original_AccountManagerID) AND ([EffectiveDate] = @Original_EffectiveDate) AND ([CareTaker] = @Original_CareTaker) AND ([CareTakerWeeks] = @Original_CareTakerWeeks));
SELECT BrandID, AccountManagerID, EffectiveDate, CareTaker, CareTakerWeeks FROM Client.BrandAccountManager WHERE (BrandID = @BrandID) AND (EffectiveDate = @EffectiveDate)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BrandID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@EffectiveDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="EffectiveDate" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@CareTaker" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="CareTaker" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@CareTakerWeeks" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CareTakerWeeks" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BrandID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="DateTime" Direction="Input" ParameterName="@Original_EffectiveDate" Precision="0" ProviderType="DateTime" Scale="0" Size="0" SourceColumn="EffectiveDate" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Boolean" Direction="Input" ParameterName="@Original_CareTaker" Precision="0" ProviderType="Bit" Scale="0" Size="0" SourceColumn="CareTaker" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Int32" Direction="Input" ParameterName="@Original_CareTakerWeeks" Precision="0" ProviderType="Int" Scale="0" Size="0" SourceColumn="CareTakerWeeks" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="BrandID" DataSetColumn="BrandID" />
              <Mapping SourceColumn="AccountManagerID" DataSetColumn="AccountManagerID" />
              <Mapping SourceColumn="EffectiveDate" DataSetColumn="EffectiveDate" />
              <Mapping SourceColumn="CareTaker" DataSetColumn="CareTaker" />
              <Mapping SourceColumn="CareTakerWeeks" DataSetColumn="CareTakerWeeks" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="BrandTableAdapter" GeneratorDataComponentClassName="BrandTableAdapter" Name="Brand" UserDataComponentName="BrandTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Client.Brand" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT BrandID, BrandName 
FROM     Client.Brand</CommandText>
                    <Parameters />
                  </DbCommand>
                </SelectCommand>
                <UpdateCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>UPDATE [Client].[Brand] SET [BrandID] = @BrandID, [BrandName] = @BrandName WHERE (([BrandID] = @Original_BrandID) AND ([BrandName] = @Original_BrandName));
SELECT BrandID, BrandName FROM Client.Brand WHERE (BrandID = @BrandID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BrandID" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@BrandName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="BrandName" SourceColumnNullMapping="false" SourceVersion="Current" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="Guid" Direction="Input" ParameterName="@Original_BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="0" SourceColumn="BrandID" SourceColumnNullMapping="false" SourceVersion="Original" />
                      <Parameter AllowDbNull="false" AutogeneratedName="" DataSourceName="" DbType="String" Direction="Input" ParameterName="@Original_BrandName" Precision="0" ProviderType="NVarChar" Scale="0" Size="0" SourceColumn="BrandName" SourceColumnNullMapping="false" SourceVersion="Original" />
                    </Parameters>
                  </DbCommand>
                </UpdateCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="BrandID" DataSetColumn="BrandID" />
              <Mapping SourceColumn="BrandName" DataSetColumn="BrandName" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="ExistingBrandManagerTableAdapter" GeneratorDataComponentClassName="ExistingBrandManagerTableAdapter" Name="ExistingBrandManager" UserDataComponentName="ExistingBrandManagerTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Client.BrandAccountManager" DbObjectType="Table" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="true">
                    <CommandText>SELECT        Sales.AccountManager.FirstName + N' ' + Sales.AccountManager.LastName AS AccountManagerFullName, Client.BrandAccountManager.BrandId, 
                         Client.BrandAccountManager.EffectiveDate
FROM            Client.BrandAccountManager INNER JOIN
                         Sales.AccountManager ON Client.BrandAccountManager.AccountManagerID = Sales.AccountManager.AccountManagerID
WHERE        (Client.BrandAccountManager.BrandID = @BrandID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="BrandID" ColumnName="BrandID" DataSourceName="NovaDB.Client.BrandAccountManager" DataTypeServer="uniqueidentifier" DbType="Guid" Direction="Input" ParameterName="@BrandID" Precision="0" ProviderType="UniqueIdentifier" Scale="0" Size="16" SourceColumn="BrandID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="AccountManagerFullName" DataSetColumn="AccountManagerFullName" />
              <Mapping SourceColumn="BrandId" DataSetColumn="BrandId" />
              <Mapping SourceColumn="EffectiveDate" DataSetColumn="EffectiveDate" />
            </Mappings>
            <Sources />
          </TableAdapter>
          <TableAdapter BaseClass="System.ComponentModel.Component" DataAccessorModifier="AutoLayout, AnsiClass, Class, Public" DataAccessorName="vBrandAccountManagersDatesTableAdapter" GeneratorDataComponentClassName="vBrandAccountManagersDatesTableAdapter" Name="vBrandAccountManagersDates" UserDataComponentName="vBrandAccountManagersDatesTableAdapter">
            <MainSource>
              <DbSource ConnectionRef="DBConnection (MySettings)" DbObjectName="NovaDB.Client.vBrandAccountManagersDates" DbObjectType="View" FillMethodModifier="Public" FillMethodName="Fill" GenerateMethods="Both" GenerateShortCommands="false" GeneratorGetMethodName="GetData" GeneratorSourceName="Fill" GetMethodModifier="Public" GetMethodName="GetData" QueryType="Rowset" ScalarCallRetval="System.Object, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" UseOptimisticConcurrency="true" UserGetMethodName="GetData" UserSourceName="Fill">
                <SelectCommand>
                  <DbCommand CommandType="Text" ModifiedByUser="false">
                    <CommandText>SELECT BrandID, AccountManagerID, [From] AS EffectiveDate, [To] AS EndDate
FROM     Client.vBrandAccountManagersDates
WHERE  (AccountManagerID = @AccountManagerID)</CommandText>
                    <Parameters>
                      <Parameter AllowDbNull="false" AutogeneratedName="AccountManagerID" ColumnName="AccountManagerID" DataSourceName="NovaDB.Client.vBrandAccountManagersDates" DataTypeServer="int" DbType="Int32" Direction="Input" ParameterName="@AccountManagerID" Precision="0" ProviderType="Int" Scale="0" Size="4" SourceColumn="AccountManagerID" SourceColumnNullMapping="false" SourceVersion="Current" />
                    </Parameters>
                  </DbCommand>
                </SelectCommand>
              </DbSource>
            </MainSource>
            <Mappings>
              <Mapping SourceColumn="BrandID" DataSetColumn="BrandID" />
              <Mapping SourceColumn="AccountManagerID" DataSetColumn="AccountManagerID" />
              <Mapping SourceColumn="EffectiveDate" DataSetColumn="EffectiveDate" />
              <Mapping SourceColumn="EndDate" DataSetColumn="EndDate" />
            </Mappings>
            <Sources />
          </TableAdapter>
        </Tables>
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DataSetAccountManager" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:Generator_DataSetName="DataSetAccountManager" msprop:Generator_UserDSName="DataSetAccountManager">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="ContractCount" msprop:Generator_UserTableName="ContractCount" msprop:Generator_RowEvArgName="ContractCountRowChangeEvent" msprop:Generator_TableVarName="tableContractCount" msprop:Generator_TablePropName="ContractCount" msprop:Generator_RowDeletingName="ContractCountRowDeleting" msprop:Generator_RowChangingName="ContractCountRowChanging" msprop:Generator_RowDeletedName="ContractCountRowDeleted" msprop:Generator_RowEvHandlerName="ContractCountRowChangeEventHandler" msprop:Generator_TableClassName="ContractCountDataTable" msprop:Generator_RowChangedName="ContractCountRowChanged" msprop:Generator_RowClassName="ContractCountRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="AccountManagerID" msprop:Generator_ColumnVarNameInTable="columnAccountManagerID" msprop:Generator_ColumnPropNameInRow="AccountManagerID" msprop:Generator_ColumnPropNameInTable="AccountManagerIDColumn" msprop:Generator_UserColumnName="AccountManagerID" type="xs:int" />
              <xs:element name="ContractCount" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnContractCount" msprop:Generator_ColumnPropNameInRow="ContractCount" msprop:Generator_ColumnPropNameInTable="ContractCountColumn" msprop:Generator_UserColumnName="ContractCount" type="xs:int" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Client" msprop:Generator_UserTableName="Client" msprop:Generator_RowEvArgName="ClientRowChangeEvent" msprop:Generator_TableVarName="tableClient" msprop:Generator_TablePropName="Client" msprop:Generator_RowDeletingName="ClientRowDeleting" msprop:Generator_RowChangingName="ClientRowChanging" msprop:Generator_RowDeletedName="ClientRowDeleted" msprop:Generator_RowEvHandlerName="ClientRowChangeEventHandler" msprop:Generator_TableClassName="ClientDataTable" msprop:Generator_RowChangedName="ClientRowChanged" msprop:Generator_RowClassName="ClientRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ClientID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msprop:Generator_ColumnVarNameInTable="columnClientID" msprop:Generator_ColumnPropNameInRow="ClientID" msprop:Generator_ColumnPropNameInTable="ClientIDColumn" msprop:Generator_UserColumnName="ClientID" type="xs:int" />
              <xs:element name="ClientName" msprop:Generator_ColumnVarNameInTable="columnClientName" msprop:Generator_ColumnPropNameInRow="ClientName" msprop:Generator_ColumnPropNameInTable="ClientNameColumn" msprop:Generator_UserColumnName="ClientName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="150" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="vClientAccountManagerDates" msprop:Generator_UserTableName="vClientAccountManagerDates" msprop:Generator_RowEvArgName="vClientAccountManagerDatesRowChangeEvent" msprop:Generator_TableVarName="tablevClientAccountManagerDates" msprop:Generator_TablePropName="vClientAccountManagerDates" msprop:Generator_RowDeletingName="vClientAccountManagerDatesRowDeleting" msprop:Generator_RowChangingName="vClientAccountManagerDatesRowChanging" msprop:Generator_RowDeletedName="vClientAccountManagerDatesRowDeleted" msprop:Generator_RowEvHandlerName="vClientAccountManagerDatesRowChangeEventHandler" msprop:Generator_TableClassName="vClientAccountManagerDatesDataTable" msprop:Generator_RowChangedName="vClientAccountManagerDatesRowChanged" msprop:Generator_RowClassName="vClientAccountManagerDatesRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ClientID" msprop:Generator_ColumnVarNameInTable="columnClientID" msprop:Generator_ColumnPropNameInRow="ClientID" msprop:Generator_ColumnPropNameInTable="ClientIDColumn" msprop:Generator_UserColumnName="ClientID" type="xs:int" />
              <xs:element name="AccountManagerID" msprop:Generator_ColumnVarNameInTable="columnAccountManagerID" msprop:Generator_ColumnPropNameInRow="AccountManagerID" msprop:Generator_ColumnPropNameInTable="AccountManagerIDColumn" msprop:Generator_UserColumnName="AccountManagerID" type="xs:int" />
              <xs:element name="EffectiveDate" msprop:Generator_ColumnVarNameInTable="columnEffectiveDate" msprop:Generator_ColumnPropNameInRow="EffectiveDate" msprop:Generator_ColumnPropNameInTable="EffectiveDateColumn" msprop:Generator_UserColumnName="EffectiveDate" type="xs:dateTime" />
              <xs:element name="EndDate" msprop:Generator_ColumnVarNameInTable="columnEndDate" msprop:Generator_ColumnPropNameInRow="EndDate" msprop:Generator_ColumnPropNameInTable="EndDateColumn" msprop:Generator_UserColumnName="EndDate" type="xs:dateTime" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ClientAccountManager" msprop:Generator_UserTableName="ClientAccountManager" msprop:Generator_RowEvArgName="ClientAccountManagerRowChangeEvent" msprop:Generator_TableVarName="tableClientAccountManager" msprop:Generator_TablePropName="ClientAccountManager" msprop:Generator_RowDeletingName="ClientAccountManagerRowDeleting" msprop:Generator_RowChangingName="ClientAccountManagerRowChanging" msprop:Generator_RowDeletedName="ClientAccountManagerRowDeleted" msprop:Generator_RowEvHandlerName="ClientAccountManagerRowChangeEventHandler" msprop:Generator_TableClassName="ClientAccountManagerDataTable" msprop:Generator_RowChangedName="ClientAccountManagerRowChanged" msprop:Generator_RowClassName="ClientAccountManagerRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ClientID" msprop:Generator_ColumnVarNameInTable="columnClientID" msprop:Generator_ColumnPropNameInRow="ClientID" msprop:Generator_ColumnPropNameInTable="ClientIDColumn" msprop:Generator_UserColumnName="ClientID" type="xs:int" />
              <xs:element name="AccountManagerID" msprop:Generator_ColumnVarNameInTable="columnAccountManagerID" msprop:Generator_ColumnPropNameInRow="AccountManagerID" msprop:Generator_ColumnPropNameInTable="AccountManagerIDColumn" msprop:Generator_UserColumnName="AccountManagerID" type="xs:int" />
              <xs:element name="EffectiveDate" msprop:Generator_ColumnVarNameInTable="columnEffectiveDate" msprop:Generator_ColumnPropNameInRow="EffectiveDate" msprop:Generator_ColumnPropNameInTable="EffectiveDateColumn" msprop:Generator_UserColumnName="EffectiveDate" type="xs:dateTime" />
              <xs:element name="CareTaker" msprop:Generator_ColumnVarNameInTable="columnCareTaker" msprop:Generator_ColumnPropNameInRow="CareTaker" msprop:Generator_ColumnPropNameInTable="CareTakerColumn" msprop:Generator_UserColumnName="CareTaker" type="xs:boolean" />
              <xs:element name="CareTakerWeeks" msprop:Generator_ColumnVarNameInTable="columnCareTakerWeeks" msprop:Generator_ColumnPropNameInRow="CareTakerWeeks" msprop:Generator_ColumnPropNameInTable="CareTakerWeeksColumn" msprop:Generator_UserColumnName="CareTakerWeeks" type="xs:int" />
              <xs:element name="ClientName" msdata:ReadOnly="true" msdata:Expression="Parent(FK_ClientAccountManager_Client).ClientName" msprop:Generator_ColumnVarNameInTable="columnClientName" msprop:Generator_ColumnPropNameInRow="ClientName" msprop:Generator_ColumnPropNameInTable="ClientNameColumn" msprop:Generator_UserColumnName="ClientName" type="xs:string" minOccurs="0" />
              <xs:element name="EndDate" msdata:ReadOnly="true" msdata:Expression="Parent(vClientAccountManagerDates_ClientAccountManager).EndDate" msprop:Generator_ColumnVarNameInTable="columnEndDate" msprop:Generator_ColumnPropNameInRow="EndDate" msprop:Generator_ColumnPropNameInTable="EndDateColumn" msprop:Generator_UserColumnName="EndDate" type="xs:dateTime" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="LinkedContractsByClientPeriod" msprop:Generator_UserTableName="LinkedContractsByClientPeriod" msprop:Generator_RowEvArgName="LinkedContractsByClientPeriodRowChangeEvent" msprop:Generator_TableVarName="tableLinkedContractsByClientPeriod" msprop:Generator_TablePropName="LinkedContractsByClientPeriod" msprop:Generator_RowDeletingName="LinkedContractsByClientPeriodRowDeleting" msprop:Generator_RowChangingName="LinkedContractsByClientPeriodRowChanging" msprop:Generator_RowDeletedName="LinkedContractsByClientPeriodRowDeleted" msprop:Generator_RowEvHandlerName="LinkedContractsByClientPeriodRowChangeEventHandler" msprop:Generator_TableClassName="LinkedContractsByClientPeriodDataTable" msprop:Generator_RowChangedName="LinkedContractsByClientPeriodRowChanged" msprop:Generator_RowClassName="LinkedContractsByClientPeriodRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="ClientID" msprop:Generator_ColumnVarNameInTable="columnClientID" msprop:Generator_ColumnPropNameInRow="ClientID" msprop:Generator_ColumnPropNameInTable="ClientIDColumn" msprop:Generator_UserColumnName="ClientID" type="xs:int" />
              <xs:element name="AccountManagerID" msprop:Generator_ColumnVarNameInTable="columnAccountManagerID" msprop:Generator_ColumnPropNameInRow="AccountManagerID" msprop:Generator_ColumnPropNameInTable="AccountManagerIDColumn" msprop:Generator_UserColumnName="AccountManagerID" type="xs:int" />
              <xs:element name="From" msprop:Generator_ColumnVarNameInTable="columnFrom" msprop:Generator_ColumnPropNameInRow="From" msprop:Generator_ColumnPropNameInTable="FromColumn" msprop:Generator_UserColumnName="From" type="xs:dateTime" />
              <xs:element name="ContractNumber" msprop:Generator_ColumnVarNameInTable="columnContractNumber" msprop:Generator_ColumnPropNameInRow="ContractNumber" msprop:Generator_ColumnPropNameInTable="ContractNumberColumn" msprop:Generator_UserColumnName="ContractNumber">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="8" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ExistingAccountManagers" msprop:Generator_UserTableName="ExistingAccountManagers" msprop:Generator_RowEvArgName="ExistingAccountManagersRowChangeEvent" msprop:Generator_TableVarName="tableExistingAccountManagers" msprop:Generator_TablePropName="ExistingAccountManagers" msprop:Generator_RowDeletingName="ExistingAccountManagersRowDeleting" msprop:Generator_RowChangingName="ExistingAccountManagersRowChanging" msprop:Generator_RowDeletedName="ExistingAccountManagersRowDeleted" msprop:Generator_RowEvHandlerName="ExistingAccountManagersRowChangeEventHandler" msprop:Generator_TableClassName="ExistingAccountManagersDataTable" msprop:Generator_RowChangedName="ExistingAccountManagersRowChanged" msprop:Generator_RowClassName="ExistingAccountManagersRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="AccountManagerFullName" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnAccountManagerFullName" msprop:Generator_ColumnPropNameInRow="AccountManagerFullName" msprop:Generator_ColumnPropNameInTable="AccountManagerFullNameColumn" msprop:Generator_UserColumnName="AccountManagerFullName" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="151" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="ClientID" msprop:Generator_ColumnVarNameInTable="columnClientID" msprop:Generator_ColumnPropNameInRow="ClientID" msprop:Generator_ColumnPropNameInTable="ClientIDColumn" msprop:Generator_UserColumnName="ClientID" type="xs:int" />
              <xs:element name="EffectiveDate" msprop:Generator_ColumnVarNameInTable="columnEffectiveDate" msprop:Generator_ColumnPropNameInRow="EffectiveDate" msprop:Generator_ColumnPropNameInTable="EffectiveDateColumn" msprop:Generator_UserColumnName="EffectiveDate" type="xs:dateTime" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="AccountManagerBudgets" msprop:Generator_UserTableName="AccountManagerBudgets" msprop:Generator_RowEvArgName="AccountManagerBudgetsRowChangeEvent" msprop:Generator_TableVarName="tableAccountManagerBudgets" msprop:Generator_TablePropName="AccountManagerBudgets" msprop:Generator_RowDeletingName="AccountManagerBudgetsRowDeleting" msprop:Generator_RowChangingName="AccountManagerBudgetsRowChanging" msprop:Generator_RowDeletedName="AccountManagerBudgetsRowDeleted" msprop:Generator_RowEvHandlerName="AccountManagerBudgetsRowChangeEventHandler" msprop:Generator_TableClassName="AccountManagerBudgetsDataTable" msprop:Generator_RowChangedName="AccountManagerBudgetsRowChanged" msprop:Generator_RowClassName="AccountManagerBudgetsRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="AccountManagerID" msprop:Generator_ColumnVarNameInTable="columnAccountManagerID" msprop:Generator_ColumnPropNameInRow="AccountManagerID" msprop:Generator_ColumnPropNameInTable="AccountManagerIDColumn" msprop:Generator_UserColumnName="AccountManagerID" type="xs:int" />
              <xs:element name="FiscalID" msprop:Generator_ColumnVarNameInTable="columnFiscalID" msprop:Generator_ColumnPropNameInRow="FiscalID" msprop:Generator_ColumnPropNameInTable="FiscalIDColumn" msprop:Generator_UserColumnName="FiscalID" type="xs:int" />
              <xs:element name="Budget" msprop:Generator_ColumnVarNameInTable="columnBudget" msprop:Generator_ColumnPropNameInRow="Budget" msprop:Generator_ColumnPropNameInTable="BudgetColumn" msprop:Generator_UserColumnName="Budget" type="xs:decimal" default="0" />
              <xs:element name="FiscalName" msdata:ReadOnly="true" msdata:Expression="Parent(FK_AccountManagerBudgets_Fiscal).FiscalName" msprop:Generator_ColumnVarNameInTable="columnFiscalName" msprop:Generator_ColumnPropNameInRow="FiscalName" msprop:Generator_ColumnPropNameInTable="FiscalNameColumn" msprop:Generator_UserColumnName="FiscalName" type="xs:string" default="Select..." minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Fiscal" msprop:Generator_UserTableName="Fiscal" msprop:Generator_RowEvArgName="FiscalRowChangeEvent" msprop:Generator_TableVarName="tableFiscal" msprop:Generator_TablePropName="Fiscal" msprop:Generator_RowDeletingName="FiscalRowDeleting" msprop:Generator_RowChangingName="FiscalRowChanging" msprop:Generator_RowDeletedName="FiscalRowDeleted" msprop:Generator_RowEvHandlerName="FiscalRowChangeEventHandler" msprop:Generator_TableClassName="FiscalDataTable" msprop:Generator_RowChangedName="FiscalRowChanged" msprop:Generator_RowClassName="FiscalRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="FiscalID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msprop:Generator_ColumnVarNameInTable="columnFiscalID" msprop:Generator_ColumnPropNameInRow="FiscalID" msprop:Generator_ColumnPropNameInTable="FiscalIDColumn" msprop:Generator_UserColumnName="FiscalID" type="xs:int" />
              <xs:element name="FiscalStartDate" msprop:Generator_ColumnVarNameInTable="columnFiscalStartDate" msprop:Generator_ColumnPropNameInRow="FiscalStartDate" msprop:Generator_ColumnPropNameInTable="FiscalStartDateColumn" msprop:Generator_UserColumnName="FiscalStartDate" type="xs:dateTime" />
              <xs:element name="FiscalName" msprop:Generator_ColumnVarNameInTable="columnFiscalName" msprop:Generator_ColumnPropNameInRow="FiscalName" msprop:Generator_ColumnPropNameInTable="FiscalNameColumn" msprop:Generator_UserColumnName="FiscalName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="250" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="AccountManagerPermission" msprop:Generator_UserTableName="AccountManagerPermission" msprop:Generator_RowEvArgName="AccountManagerPermissionRowChangeEvent" msprop:Generator_TableVarName="tableAccountManagerPermission" msprop:Generator_TablePropName="AccountManagerPermission" msprop:Generator_RowDeletingName="AccountManagerPermissionRowDeleting" msprop:Generator_RowChangingName="AccountManagerPermissionRowChanging" msprop:Generator_RowDeletedName="AccountManagerPermissionRowDeleted" msprop:Generator_RowEvHandlerName="AccountManagerPermissionRowChangeEventHandler" msprop:Generator_TableClassName="AccountManagerPermissionDataTable" msprop:Generator_RowChangedName="AccountManagerPermissionRowChanged" msprop:Generator_RowClassName="AccountManagerPermissionRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="AccountManagerPermissionID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msprop:Generator_ColumnVarNameInTable="columnAccountManagerPermissionID" msprop:Generator_ColumnPropNameInRow="AccountManagerPermissionID" msprop:Generator_ColumnPropNameInTable="AccountManagerPermissionIDColumn" msprop:Generator_UserColumnName="AccountManagerPermissionID" type="xs:int" />
              <xs:element name="AccountManagerPermissionName" msprop:Generator_ColumnVarNameInTable="columnAccountManagerPermissionName" msprop:Generator_ColumnPropNameInRow="AccountManagerPermissionName" msprop:Generator_ColumnPropNameInTable="AccountManagerPermissionNameColumn" msprop:Generator_UserColumnName="AccountManagerPermissionName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="200" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="sql_logins" msprop:Generator_UserTableName="sql_logins" msprop:Generator_RowEvArgName="sql_loginsRowChangeEvent" msprop:Generator_TableVarName="tablesql_logins" msprop:Generator_TablePropName="sql_logins" msprop:Generator_RowDeletingName="sql_loginsRowDeleting" msprop:Generator_RowChangingName="sql_loginsRowChanging" msprop:Generator_RowDeletedName="sql_loginsRowDeleted" msprop:Generator_RowEvHandlerName="sql_loginsRowChangeEventHandler" msprop:Generator_TableClassName="sql_loginsDataTable" msprop:Generator_RowChangedName="sql_loginsRowChanged" msprop:Generator_RowClassName="sql_loginsRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="principal_id" msprop:Generator_ColumnVarNameInTable="columnprincipal_id" msprop:Generator_ColumnPropNameInRow="principal_id" msprop:Generator_ColumnPropNameInTable="principal_idColumn" msprop:Generator_UserColumnName="principal_id" type="xs:int" />
              <xs:element name="name" msprop:Generator_ColumnVarNameInTable="columnname" msprop:Generator_ColumnPropNameInRow="name" msprop:Generator_ColumnPropNameInTable="nameColumn" msprop:Generator_UserColumnName="name">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="128" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="AccountManagerPermissionUser" msprop:Generator_UserTableName="AccountManagerPermissionUser" msprop:Generator_RowEvArgName="AccountManagerPermissionUserRowChangeEvent" msprop:Generator_TableVarName="tableAccountManagerPermissionUser" msprop:Generator_TablePropName="AccountManagerPermissionUser" msprop:Generator_RowDeletingName="AccountManagerPermissionUserRowDeleting" msprop:Generator_RowChangingName="AccountManagerPermissionUserRowChanging" msprop:Generator_RowDeletedName="AccountManagerPermissionUserRowDeleted" msprop:Generator_RowEvHandlerName="AccountManagerPermissionUserRowChangeEventHandler" msprop:Generator_TableClassName="AccountManagerPermissionUserDataTable" msprop:Generator_RowChangedName="AccountManagerPermissionUserRowChanged" msprop:Generator_RowClassName="AccountManagerPermissionUserRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="AccountManagerPermissionID" msprop:Generator_ColumnVarNameInTable="columnAccountManagerPermissionID" msprop:Generator_ColumnPropNameInRow="AccountManagerPermissionID" msprop:Generator_ColumnPropNameInTable="AccountManagerPermissionIDColumn" msprop:Generator_UserColumnName="AccountManagerPermissionID" type="xs:int" />
              <xs:element name="AccountManagerID" msprop:Generator_ColumnVarNameInTable="columnAccountManagerID" msprop:Generator_ColumnPropNameInRow="AccountManagerID" msprop:Generator_ColumnPropNameInTable="AccountManagerIDColumn" msprop:Generator_UserColumnName="AccountManagerID" type="xs:int" />
              <xs:element name="principal_id" msprop:Generator_ColumnVarNameInTable="columnprincipal_id" msprop:Generator_ColumnPropNameInRow="principal_id" msprop:Generator_ColumnPropNameInTable="principal_idColumn" msprop:Generator_UserColumnName="principal_id" type="xs:int" />
              <xs:element name="name" msdata:ReadOnly="true" msdata:Expression="Parent(sql_logins_AccountManagerPermissionUser).name" msprop:Generator_ColumnVarNameInTable="columnname" msprop:Generator_ColumnPropNameInRow="name" msprop:Generator_ColumnPropNameInTable="nameColumn" msprop:Generator_UserColumnName="name" type="xs:string" default="" minOccurs="0" />
              <xs:element name="AccountManagerPermissionName" msdata:ReadOnly="true" msdata:Expression="Parent(FK_AccountManagerPermissionUser_AccountManagerPermission).AccountManagerPermissionName" msprop:Generator_ColumnVarNameInTable="columnAccountManagerPermissionName" msprop:Generator_ColumnPropNameInRow="AccountManagerPermissionName" msprop:Generator_ColumnPropNameInTable="AccountManagerPermissionNameColumn" msprop:Generator_UserColumnName="AccountManagerPermissionName" type="xs:string" default="" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="AccountManager" msprop:Generator_TableClassName="AccountManagerDataTable" msprop:Generator_RowEvArgName="AccountManagerRowChangeEvent" msprop:Generator_TableVarName="tableAccountManager" msprop:Generator_TablePropName="AccountManager" msprop:Generator_RowDeletingName="AccountManagerRowDeleting" msprop:Generator_RowChangingName="AccountManagerRowChanging" msprop:Generator_RowDeletedName="AccountManagerRowDeleted" msprop:Generator_RowClassName="AccountManagerRow" msprop:Generator_RowEvHandlerName="AccountManagerRowChangeEventHandler" msprop:Generator_RowChangedName="AccountManagerRowChanged" msprop:Generator_UserTableName="AccountManager">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="AccountManagerID" msdata:ReadOnly="true" msdata:AutoIncrement="true" msprop:Generator_ColumnVarNameInTable="columnAccountManagerID" msprop:Generator_ColumnPropNameInRow="AccountManagerID" msprop:Generator_ColumnPropNameInTable="AccountManagerIDColumn" msprop:Generator_UserColumnName="AccountManagerID" type="xs:int" />
              <xs:element name="principal_id" msprop:Generator_ColumnVarNameInTable="columnprincipal_id" msprop:Generator_ColumnPropNameInRow="principal_id" msprop:Generator_ColumnPropNameInTable="principal_idColumn" msprop:Generator_UserColumnName="principal_id" type="xs:int" default="-1" />
              <xs:element name="FirstName" msprop:Generator_ColumnVarNameInTable="columnFirstName" msprop:Generator_ColumnPropNameInRow="FirstName" msprop:Generator_ColumnPropNameInTable="FirstNameColumn" msprop:Generator_UserColumnName="FirstName" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="LastName" msprop:Generator_ColumnVarNameInTable="columnLastName" msprop:Generator_ColumnPropNameInRow="LastName" msprop:Generator_ColumnPropNameInTable="LastNameColumn" msprop:Generator_UserColumnName="LastName" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="100" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Email" msprop:Generator_ColumnVarNameInTable="columnEmail" msprop:Generator_ColumnPropNameInRow="Email" msprop:Generator_ColumnPropNameInTable="EmailColumn" msprop:Generator_UserColumnName="Email" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Code" msprop:Generator_ColumnVarNameInTable="columnCode" msprop:Generator_ColumnPropNameInRow="Code" msprop:Generator_ColumnPropNameInTable="CodeColumn" msprop:Generator_UserColumnName="Code" default="">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="Dormant" msprop:Generator_ColumnVarNameInTable="columnDormant" msprop:Generator_ColumnPropNameInRow="Dormant" msprop:Generator_ColumnPropNameInTable="DormantColumn" msprop:Generator_UserColumnName="Dormant" type="xs:boolean" default="false" />
              <xs:element name="FullName" msdata:ReadOnly="true" msdata:Expression="FirstName + ' ' + LastName" msprop:Generator_ColumnVarNameInTable="columnFullName" msprop:Generator_ColumnPropNameInRow="FullName" msprop:Generator_ColumnPropNameInTable="FullNameColumn" msprop:Generator_UserColumnName="FullName" type="xs:string" default="" minOccurs="0" />
              <xs:element name="Username" msprop:Generator_ColumnVarNameInTable="columnUsername" msprop:Generator_ColumnPropNameInRow="Username" msprop:Generator_ColumnPropNameInTable="UsernameColumn" msprop:Generator_UserColumnName="Username" default="" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="128" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="BrandAccountManager" msprop:Generator_TableClassName="BrandAccountManagerDataTable" msprop:Generator_TableVarName="tableBrandAccountManager" msprop:Generator_RowChangedName="BrandAccountManagerRowChanged" msprop:Generator_TablePropName="BrandAccountManager" msprop:Generator_RowDeletingName="BrandAccountManagerRowDeleting" msprop:Generator_RowChangingName="BrandAccountManagerRowChanging" msprop:Generator_RowEvHandlerName="BrandAccountManagerRowChangeEventHandler" msprop:Generator_RowDeletedName="BrandAccountManagerRowDeleted" msprop:Generator_RowClassName="BrandAccountManagerRow" msprop:Generator_UserTableName="BrandAccountManager" msprop:Generator_RowEvArgName="BrandAccountManagerRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="BrandID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnBrandID" msprop:Generator_ColumnPropNameInRow="BrandID" msprop:Generator_ColumnPropNameInTable="BrandIDColumn" msprop:Generator_UserColumnName="BrandID" type="xs:string" />
              <xs:element name="AccountManagerID" msprop:Generator_ColumnVarNameInTable="columnAccountManagerID" msprop:Generator_ColumnPropNameInRow="AccountManagerID" msprop:Generator_ColumnPropNameInTable="AccountManagerIDColumn" msprop:Generator_UserColumnName="AccountManagerID" type="xs:int" />
              <xs:element name="EffectiveDate" msprop:Generator_ColumnVarNameInTable="columnEffectiveDate" msprop:Generator_ColumnPropNameInRow="EffectiveDate" msprop:Generator_ColumnPropNameInTable="EffectiveDateColumn" msprop:Generator_UserColumnName="EffectiveDate" type="xs:dateTime" />
              <xs:element name="CareTaker" msprop:Generator_ColumnVarNameInTable="columnCareTaker" msprop:Generator_ColumnPropNameInRow="CareTaker" msprop:Generator_ColumnPropNameInTable="CareTakerColumn" msprop:Generator_UserColumnName="CareTaker" type="xs:boolean" />
              <xs:element name="CareTakerWeeks" msprop:Generator_ColumnVarNameInTable="columnCareTakerWeeks" msprop:Generator_ColumnPropNameInRow="CareTakerWeeks" msprop:Generator_ColumnPropNameInTable="CareTakerWeeksColumn" msprop:Generator_UserColumnName="CareTakerWeeks" type="xs:int" />
              <xs:element name="BrandName" msdata:ReadOnly="true" msdata:Expression="Parent(FK_BrandAccountManager_Brand).BrandName" msprop:Generator_ColumnVarNameInTable="columnBrandName" msprop:Generator_ColumnPropNameInRow="BrandName" msprop:Generator_ColumnPropNameInTable="BrandNameColumn" msprop:Generator_UserColumnName="BrandName" type="xs:string" minOccurs="0" />
              <xs:element name="EndDate" msdata:ReadOnly="true" msdata:Expression="Parent(vBrandAccountManagersDates_BrandAccountManager).EndDate" msprop:Generator_ColumnVarNameInTable="columnEndDate" msprop:Generator_ColumnPropNameInRow="EndDate" msprop:Generator_ColumnPropNameInTable="EndDateColumn" msprop:Generator_UserColumnName="EndDate" type="xs:dateTime" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="Brand" msprop:Generator_TableClassName="BrandDataTable" msprop:Generator_TableVarName="tableBrand" msprop:Generator_RowChangedName="BrandRowChanged" msprop:Generator_TablePropName="Brand" msprop:Generator_RowDeletingName="BrandRowDeleting" msprop:Generator_RowChangingName="BrandRowChanging" msprop:Generator_RowEvHandlerName="BrandRowChangeEventHandler" msprop:Generator_RowDeletedName="BrandRowDeleted" msprop:Generator_RowClassName="BrandRow" msprop:Generator_UserTableName="Brand" msprop:Generator_RowEvArgName="BrandRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="BrandID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnBrandID" msprop:Generator_ColumnPropNameInRow="BrandID" msprop:Generator_ColumnPropNameInTable="BrandIDColumn" msprop:Generator_UserColumnName="BrandID" type="xs:string" />
              <xs:element name="BrandName" msprop:Generator_ColumnVarNameInTable="columnBrandName" msprop:Generator_ColumnPropNameInRow="BrandName" msprop:Generator_ColumnPropNameInTable="BrandNameColumn" msprop:Generator_UserColumnName="BrandName">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="50" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="ExistingBrandManager" msprop:Generator_TableClassName="ExistingBrandManagerDataTable" msprop:Generator_TableVarName="tableExistingBrandManager" msprop:Generator_RowChangedName="ExistingBrandManagerRowChanged" msprop:Generator_TablePropName="ExistingBrandManager" msprop:Generator_RowDeletingName="ExistingBrandManagerRowDeleting" msprop:Generator_RowChangingName="ExistingBrandManagerRowChanging" msprop:Generator_RowEvHandlerName="ExistingBrandManagerRowChangeEventHandler" msprop:Generator_RowDeletedName="ExistingBrandManagerRowDeleted" msprop:Generator_RowClassName="ExistingBrandManagerRow" msprop:Generator_UserTableName="ExistingBrandManager" msprop:Generator_RowEvArgName="ExistingBrandManagerRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="AccountManagerFullName" msdata:ReadOnly="true" msprop:Generator_ColumnVarNameInTable="columnAccountManagerFullName" msprop:Generator_ColumnPropNameInRow="AccountManagerFullName" msprop:Generator_ColumnPropNameInTable="AccountManagerFullNameColumn" msprop:Generator_UserColumnName="AccountManagerFullName" minOccurs="0">
                <xs:simpleType>
                  <xs:restriction base="xs:string">
                    <xs:maxLength value="151" />
                  </xs:restriction>
                </xs:simpleType>
              </xs:element>
              <xs:element name="BrandId" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnBrandId" msprop:Generator_ColumnPropNameInRow="BrandId" msprop:Generator_ColumnPropNameInTable="BrandIdColumn" msprop:Generator_UserColumnName="BrandId" type="xs:string" />
              <xs:element name="EffectiveDate" msprop:Generator_ColumnVarNameInTable="columnEffectiveDate" msprop:Generator_ColumnPropNameInRow="EffectiveDate" msprop:Generator_ColumnPropNameInTable="EffectiveDateColumn" msprop:Generator_UserColumnName="EffectiveDate" type="xs:dateTime" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="vBrandAccountManagersDates" msprop:Generator_TableClassName="vBrandAccountManagersDatesDataTable" msprop:Generator_TableVarName="tablevBrandAccountManagersDates" msprop:Generator_TablePropName="vBrandAccountManagersDates" msprop:Generator_RowDeletingName="vBrandAccountManagersDatesRowDeleting" msprop:Generator_RowChangingName="vBrandAccountManagersDatesRowChanging" msprop:Generator_RowEvHandlerName="vBrandAccountManagersDatesRowChangeEventHandler" msprop:Generator_RowDeletedName="vBrandAccountManagersDatesRowDeleted" msprop:Generator_UserTableName="vBrandAccountManagersDates" msprop:Generator_RowChangedName="vBrandAccountManagersDatesRowChanged" msprop:Generator_RowEvArgName="vBrandAccountManagersDatesRowChangeEvent" msprop:Generator_RowClassName="vBrandAccountManagersDatesRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="BrandID" msdata:DataType="System.Guid, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" msprop:Generator_ColumnVarNameInTable="columnBrandID" msprop:Generator_ColumnPropNameInRow="BrandID" msprop:Generator_ColumnPropNameInTable="BrandIDColumn" msprop:Generator_UserColumnName="BrandID" type="xs:string" />
              <xs:element name="AccountManagerID" msprop:Generator_ColumnVarNameInTable="columnAccountManagerID" msprop:Generator_ColumnPropNameInRow="AccountManagerID" msprop:Generator_ColumnPropNameInTable="AccountManagerIDColumn" msprop:Generator_UserColumnName="AccountManagerID" type="xs:int" />
              <xs:element name="EffectiveDate" msprop:Generator_ColumnVarNameInTable="columnEffectiveDate" msprop:Generator_ColumnPropNameInRow="EffectiveDate" msprop:Generator_ColumnPropNameInTable="EffectiveDateColumn" msprop:Generator_UserColumnName="EffectiveDate" type="xs:dateTime" />
              <xs:element name="EndDate" msprop:Generator_ColumnVarNameInTable="columnEndDate" msprop:Generator_ColumnPropNameInRow="EndDate" msprop:Generator_ColumnPropNameInTable="EndDateColumn" msprop:Generator_UserColumnName="EndDate" type="xs:dateTime" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
    <xs:unique name="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Client" />
      <xs:field xpath="mstns:ClientID" />
    </xs:unique>
    <xs:unique name="vClientAccountManagerDates_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:vClientAccountManagerDates" />
      <xs:field xpath="mstns:ClientID" />
      <xs:field xpath="mstns:EffectiveDate" />
    </xs:unique>
    <xs:unique name="ClientAccountManager_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ClientAccountManager" />
      <xs:field xpath="mstns:ClientID" />
      <xs:field xpath="mstns:EffectiveDate" />
    </xs:unique>
    <xs:unique name="ExistingAccountManagers_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ExistingAccountManagers" />
      <xs:field xpath="mstns:ClientID" />
      <xs:field xpath="mstns:EffectiveDate" />
    </xs:unique>
    <xs:unique name="AccountManagerBudgets_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:AccountManagerBudgets" />
      <xs:field xpath="mstns:AccountManagerID" />
      <xs:field xpath="mstns:FiscalID" />
    </xs:unique>
    <xs:unique name="Fiscal_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Fiscal" />
      <xs:field xpath="mstns:FiscalID" />
    </xs:unique>
    <xs:unique name="AccountManagerPermission_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:AccountManagerPermission" />
      <xs:field xpath="mstns:AccountManagerPermissionID" />
    </xs:unique>
    <xs:unique name="sql_logins_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:sql_logins" />
      <xs:field xpath="mstns:principal_id" />
    </xs:unique>
    <xs:unique name="AccountManagerPermissionUser_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:AccountManagerPermissionUser" />
      <xs:field xpath="mstns:AccountManagerPermissionID" />
      <xs:field xpath="mstns:AccountManagerID" />
      <xs:field xpath="mstns:principal_id" />
    </xs:unique>
    <xs:unique name="AccountManager_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:AccountManager" />
      <xs:field xpath="mstns:AccountManagerID" />
    </xs:unique>
    <xs:unique name="BrandAccountManager_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:BrandAccountManager" />
      <xs:field xpath="mstns:BrandID" />
      <xs:field xpath="mstns:EffectiveDate" />
    </xs:unique>
    <xs:unique name="Brand_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:Brand" />
      <xs:field xpath="mstns:BrandID" />
    </xs:unique>
    <xs:unique name="ExistingBrandManager_Constraint1" msdata:ConstraintName="Constraint1" msdata:PrimaryKey="true">
      <xs:selector xpath=".//mstns:ExistingBrandManager" />
      <xs:field xpath="mstns:BrandId" />
      <xs:field xpath="mstns:EffectiveDate" />
    </xs:unique>
  </xs:element>
  <xs:annotation>
    <xs:appinfo>
      <msdata:Relationship name="vClientAccountManagerDates_ClientAccountManager" msdata:parent="vClientAccountManagerDates" msdata:child="ClientAccountManager" msdata:parentkey="ClientID AccountManagerID EffectiveDate" msdata:childkey="ClientID AccountManagerID EffectiveDate" msprop:Generator_UserChildTable="ClientAccountManager" msprop:Generator_ChildPropName="GetClientAccountManagerRows" msprop:Generator_UserRelationName="vClientAccountManagerDates_ClientAccountManager" msprop:Generator_RelationVarName="relationvClientAccountManagerDates_ClientAccountManager" msprop:Generator_UserParentTable="vClientAccountManagerDates" msprop:Generator_ParentPropName="vClientAccountManagerDatesRowParent" />
      <msdata:Relationship name="FK_ClientAccountManager_Client" msdata:parent="Client" msdata:child="ClientAccountManager" msdata:parentkey="ClientID" msdata:childkey="ClientID" msprop:Generator_UserChildTable="ClientAccountManager" msprop:Generator_ChildPropName="GetClientAccountManagerRows" msprop:Generator_UserRelationName="FK_ClientAccountManager_Client" msprop:Generator_RelationVarName="relationFK_ClientAccountManager_Client" msprop:Generator_UserParentTable="Client" msprop:Generator_ParentPropName="ClientRow" />
      <msdata:Relationship name="ClientAccountManager_vClientAccountManagerPeriodsWithActivity" msdata:parent="ClientAccountManager" msdata:child="LinkedContractsByClientPeriod" msdata:parentkey="ClientID EffectiveDate" msdata:childkey="ClientID From" msprop:Generator_UserChildTable="LinkedContractsByClientPeriod" msprop:Generator_ChildPropName="GetLinkedContractsByClientPeriodRows" msprop:Generator_UserRelationName="ClientAccountManager_vClientAccountManagerPeriodsWithActivity" msprop:Generator_RelationVarName="relationClientAccountManager_vClientAccountManagerPeriodsWithActivity" msprop:Generator_UserParentTable="ClientAccountManager" msprop:Generator_ParentPropName="ClientAccountManagerRowParent" />
      <msdata:Relationship name="FK_AccountManagerBudgets_Fiscal" msdata:parent="Fiscal" msdata:child="AccountManagerBudgets" msdata:parentkey="FiscalID" msdata:childkey="FiscalID" msprop:Generator_UserChildTable="AccountManagerBudgets" msprop:Generator_ChildPropName="GetAccountManagerBudgetsRows" msprop:Generator_UserRelationName="FK_AccountManagerBudgets_Fiscal" msprop:Generator_RelationVarName="relationFK_AccountManagerBudgets_Fiscal" msprop:Generator_UserParentTable="Fiscal" msprop:Generator_ParentPropName="FiscalRow" />
      <msdata:Relationship name="FK_AccountManagerPermissionUser_AccountManagerPermission" msdata:parent="AccountManagerPermission" msdata:child="AccountManagerPermissionUser" msdata:parentkey="AccountManagerPermissionID" msdata:childkey="AccountManagerPermissionID" msprop:Generator_UserChildTable="AccountManagerPermissionUser" msprop:Generator_ChildPropName="GetAccountManagerPermissionUserRows" msprop:Generator_UserRelationName="FK_AccountManagerPermissionUser_AccountManagerPermission" msprop:Generator_RelationVarName="relationFK_AccountManagerPermissionUser_AccountManagerPermission" msprop:Generator_UserParentTable="AccountManagerPermission" msprop:Generator_ParentPropName="AccountManagerPermissionRow" />
      <msdata:Relationship name="sql_logins_AccountManagerPermissionUser" msdata:parent="sql_logins" msdata:child="AccountManagerPermissionUser" msdata:parentkey="principal_id" msdata:childkey="principal_id" msprop:Generator_UserChildTable="AccountManagerPermissionUser" msprop:Generator_ChildPropName="GetAccountManagerPermissionUserRows" msprop:Generator_UserRelationName="sql_logins_AccountManagerPermissionUser" msprop:Generator_RelationVarName="relationsql_logins_AccountManagerPermissionUser" msprop:Generator_UserParentTable="sql_logins" msprop:Generator_ParentPropName="sql_loginsRow" />
      <msdata:Relationship name="AccountManager_ContractCount" msdata:parent="AccountManager" msdata:child="ContractCount" msdata:parentkey="AccountManagerID" msdata:childkey="AccountManagerID" msprop:Generator_UserChildTable="ContractCount" msprop:Generator_ChildPropName="GetContractCountRows" msprop:Generator_UserRelationName="AccountManager_ContractCount" msprop:Generator_RelationVarName="relationAccountManager_ContractCount" msprop:Generator_UserParentTable="AccountManager" msprop:Generator_ParentPropName="AccountManagerRow" />
      <msdata:Relationship name="AccountManager_ClientAccountManager" msdata:parent="AccountManager" msdata:child="ClientAccountManager" msdata:parentkey="AccountManagerID" msdata:childkey="AccountManagerID" msprop:Generator_UserChildTable="ClientAccountManager" msprop:Generator_ChildPropName="GetClientAccountManagerRows" msprop:Generator_UserRelationName="AccountManager_ClientAccountManager" msprop:Generator_RelationVarName="relationAccountManager_ClientAccountManager" msprop:Generator_UserParentTable="AccountManager" msprop:Generator_ParentPropName="AccountManagerRow" />
      <msdata:Relationship name="AccountManager_AccountManagerBudgets" msdata:parent="AccountManager" msdata:child="AccountManagerBudgets" msdata:parentkey="AccountManagerID" msdata:childkey="AccountManagerID" msprop:Generator_UserChildTable="AccountManagerBudgets" msprop:Generator_ChildPropName="GetAccountManagerBudgetsRows" msprop:Generator_UserRelationName="AccountManager_AccountManagerBudgets" msprop:Generator_RelationVarName="relationAccountManager_AccountManagerBudgets" msprop:Generator_UserParentTable="AccountManager" msprop:Generator_ParentPropName="AccountManagerRow" />
      <msdata:Relationship name="AccountManager_AccountManagerPermissionUser" msdata:parent="AccountManager" msdata:child="AccountManagerPermissionUser" msdata:parentkey="AccountManagerID" msdata:childkey="AccountManagerID" msprop:Generator_UserChildTable="AccountManagerPermissionUser" msprop:Generator_ChildPropName="GetAccountManagerPermissionUserRows" msprop:Generator_UserRelationName="AccountManager_AccountManagerPermissionUser" msprop:Generator_RelationVarName="relationAccountManager_AccountManagerPermissionUser" msprop:Generator_UserParentTable="AccountManager" msprop:Generator_ParentPropName="AccountManagerRow" />
      <msdata:Relationship name="FK_BrandAccountManager_Brand" msdata:parent="Brand" msdata:child="BrandAccountManager" msdata:parentkey="BrandID" msdata:childkey="BrandID" msprop:Generator_UserChildTable="BrandAccountManager" msprop:Generator_ChildPropName="GetBrandAccountManagerRows" msprop:Generator_UserRelationName="FK_BrandAccountManager_Brand" msprop:Generator_ParentPropName="BrandRow" msprop:Generator_RelationVarName="relationFK_BrandAccountManager_Brand" msprop:Generator_UserParentTable="Brand" />
      <msdata:Relationship name="FK_BrandAccountManager_Brand1" msdata:parent="Brand" msdata:child="ExistingBrandManager" msdata:parentkey="BrandID" msdata:childkey="BrandId" msprop:Generator_UserChildTable="ExistingBrandManager" msprop:Generator_ChildPropName="GetExistingBrandManagerRows" msprop:Generator_UserRelationName="FK_BrandAccountManager_Brand1" msprop:Generator_RelationVarName="relationFK_BrandAccountManager_Brand1" msprop:Generator_UserParentTable="Brand" msprop:Generator_ParentPropName="BrandRow" />
      <msdata:Relationship name="AccountManager_BrandAccountManager" msdata:parent="AccountManager" msdata:child="BrandAccountManager" msdata:parentkey="AccountManagerID" msdata:childkey="AccountManagerID" msprop:Generator_UserChildTable="BrandAccountManager" msprop:Generator_ChildPropName="GetBrandAccountManagerRows" msprop:Generator_UserRelationName="AccountManager_BrandAccountManager" msprop:Generator_RelationVarName="relationAccountManager_BrandAccountManager" msprop:Generator_UserParentTable="AccountManager" msprop:Generator_ParentPropName="AccountManagerRow" />
      <msdata:Relationship name="vBrandAccountManagersDates_BrandAccountManager" msdata:parent="vBrandAccountManagersDates" msdata:child="BrandAccountManager" msdata:parentkey="BrandID AccountManagerID EffectiveDate" msdata:childkey="BrandID AccountManagerID EffectiveDate" msprop:Generator_UserChildTable="BrandAccountManager" msprop:Generator_ChildPropName="GetBrandAccountManagerRows" msprop:Generator_UserRelationName="vBrandAccountManagersDates_BrandAccountManager" msprop:Generator_RelationVarName="relationvBrandAccountManagersDates_BrandAccountManager" msprop:Generator_UserParentTable="vBrandAccountManagersDates" msprop:Generator_ParentPropName="vBrandAccountManagersDatesRowParent" />
    </xs:appinfo>
  </xs:annotation>
</xs:schema>