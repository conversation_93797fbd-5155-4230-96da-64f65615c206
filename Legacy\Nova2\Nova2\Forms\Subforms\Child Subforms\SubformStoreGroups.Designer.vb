﻿<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformStoreGroups
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()>
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()>
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformStoreGroups))
        Dim SuperToolTip1 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem1 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem1 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip2 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem2 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem2 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Me.ImageList16x16 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonSave = New DevExpress.XtraEditors.SimpleButton()
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.PanelDetails = New System.Windows.Forms.Panel()
        Me.CheckEditDormant = New DevExpress.XtraEditors.CheckEdit()
        Me.TextEditGroupChainName = New DevExpress.XtraEditors.TextEdit()
        Me.LabelGroupChainName = New DevExpress.XtraEditors.LabelControl()
        Me.GroupControlMediaCategory = New DevExpress.XtraEditors.GroupControl()
        Me.LabelControl14 = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearchMediaCategory = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearchMediaCategory = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearchMediaCategory = New DevExpress.XtraEditors.PictureEdit()
        Me.ButtonRemoveMediaCategory = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAddStoreGroup = New DevExpress.XtraEditors.SimpleButton()
        Me.GridGroupChainStores = New System.Windows.Forms.DataGridView()
        Me.StoreNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.LabelControl3 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl4 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl9 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl10 = New DevExpress.XtraEditors.LabelControl()
        Me.XtraTabControl1 = New DevExpress.XtraTab.XtraTabControl()
        Me.TabPageDetails = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.TableLayoutPanelDetails = New System.Windows.Forms.TableLayoutPanel()
        Me.TabPageChains = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel3 = New System.Windows.Forms.Panel()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.PanelDetails.SuspendLayout()
        CType(Me.CheckEditDormant.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditGroupChainName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GroupControlMediaCategory, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlMediaCategory.SuspendLayout()
        CType(Me.TextEditSearchMediaCategory.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearchMediaCategory.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearchMediaCategory.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridGroupChainStores, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabControl1.SuspendLayout()
        Me.TabPageDetails.SuspendLayout()
        Me.Panel1.SuspendLayout()
        Me.TableLayoutPanelDetails.SuspendLayout()
        Me.TabPageChains.SuspendLayout()
        Me.Panel3.SuspendLayout()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Margin = New System.Windows.Forms.Padding(5, 5, 5, 21)
        Me.LabelTitle.Size = New System.Drawing.Size(260, 37)
        Me.LabelTitle.TabIndex = 0
        Me.LabelTitle.Text = "(new groupchain)"
        '
        'ImageList16x16
        '
        Me.ImageList16x16.ImageStream = CType(resources.GetObject("ImageList16x16.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList16x16.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList16x16.Images.SetKeyName(0, "add.png")
        Me.ImageList16x16.Images.SetKeyName(1, "pencil.png")
        Me.ImageList16x16.Images.SetKeyName(2, "delete.png")
        '
        'ButtonSave
        '
        Me.ButtonSave.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonSave.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonSave.Appearance.Options.UseFont = True
        Me.ButtonSave.ImageIndex = 1
        Me.ButtonSave.ImageList = Me.ImageList24x24
        Me.ButtonSave.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonSave.Location = New System.Drawing.Point(778, 666)
        Me.ButtonSave.LookAndFeel.SkinName = "Black"
        Me.ButtonSave.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonSave.Margin = New System.Windows.Forms.Padding(4, 16, 4, 4)
        Me.ButtonSave.Name = "ButtonSave"
        Me.ButtonSave.Size = New System.Drawing.Size(129, 37)
        Me.ButtonSave.TabIndex = 2
        Me.ButtonSave.Text = "Save"
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(914, 666)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Margin = New System.Windows.Forms.Padding(4, 16, 4, 4)
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(129, 37)
        Me.ButtonCancel.TabIndex = 3
        Me.ButtonCancel.Text = "Cancel"
        '
        'PanelDetails
        '
        Me.PanelDetails.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelDetails.Controls.Add(Me.CheckEditDormant)
        Me.PanelDetails.Controls.Add(Me.TextEditGroupChainName)
        Me.PanelDetails.Controls.Add(Me.LabelGroupChainName)
        Me.PanelDetails.Location = New System.Drawing.Point(0, 98)
        Me.PanelDetails.Margin = New System.Windows.Forms.Padding(0)
        Me.PanelDetails.Name = "PanelDetails"
        Me.PanelDetails.Size = New System.Drawing.Size(982, 400)
        Me.PanelDetails.TabIndex = 2
        '
        'CheckEditDormant
        '
        Me.CheckEditDormant.Location = New System.Drawing.Point(154, 38)
        Me.CheckEditDormant.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.CheckEditDormant.Name = "CheckEditDormant"
        Me.CheckEditDormant.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditDormant.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditDormant.Properties.Appearance.Options.UseFont = True
        Me.CheckEditDormant.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditDormant.Properties.AutoWidth = True
        Me.CheckEditDormant.Properties.Caption = "This store group is dormant"
        Me.CheckEditDormant.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditDormant.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditDormant.Size = New System.Drawing.Size(221, 21)
        Me.CheckEditDormant.TabIndex = 2
        '
        'TextEditGroupChainName
        '
        Me.TextEditGroupChainName.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditGroupChainName.Location = New System.Drawing.Point(157, 4)
        Me.TextEditGroupChainName.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.TextEditGroupChainName.Name = "TextEditGroupChainName"
        Me.TextEditGroupChainName.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditGroupChainName.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditGroupChainName.Properties.Appearance.Options.UseFont = True
        Me.TextEditGroupChainName.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditGroupChainName.Properties.MaxLength = 200
        Me.TextEditGroupChainName.Size = New System.Drawing.Size(822, 24)
        Me.TextEditGroupChainName.TabIndex = 1
        '
        'LabelGroupChainName
        '
        Me.LabelGroupChainName.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelGroupChainName.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelGroupChainName.Location = New System.Drawing.Point(0, 8)
        Me.LabelGroupChainName.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.LabelGroupChainName.Name = "LabelGroupChainName"
        Me.LabelGroupChainName.Size = New System.Drawing.Size(138, 17)
        Me.LabelGroupChainName.TabIndex = 0
        Me.LabelGroupChainName.Text = "Store Group Name:"
        '
        'GroupControlMediaCategory
        '
        Me.GroupControlMediaCategory.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlMediaCategory.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlMediaCategory.Appearance.Options.UseFont = True
        Me.GroupControlMediaCategory.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlMediaCategory.AppearanceCaption.Options.UseFont = True
        Me.GroupControlMediaCategory.Controls.Add(Me.LabelControl14)
        Me.GroupControlMediaCategory.Controls.Add(Me.TextEditSearchMediaCategory)
        Me.GroupControlMediaCategory.Controls.Add(Me.PictureClearSearchMediaCategory)
        Me.GroupControlMediaCategory.Controls.Add(Me.PictureAdvancedSearchMediaCategory)
        Me.GroupControlMediaCategory.Controls.Add(Me.ButtonRemoveMediaCategory)
        Me.GroupControlMediaCategory.Controls.Add(Me.ButtonAddStoreGroup)
        Me.GroupControlMediaCategory.Controls.Add(Me.GridGroupChainStores)
        Me.GroupControlMediaCategory.Location = New System.Drawing.Point(4, 85)
        Me.GroupControlMediaCategory.LookAndFeel.SkinName = "Black"
        Me.GroupControlMediaCategory.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlMediaCategory.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.GroupControlMediaCategory.Name = "GroupControlMediaCategory"
        Me.GroupControlMediaCategory.Size = New System.Drawing.Size(972, 401)
        Me.GroupControlMediaCategory.TabIndex = 5
        Me.GroupControlMediaCategory.Text = "Stores in this group"
        '
        'LabelControl14
        '
        Me.LabelControl14.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl14.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl14.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl14.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl14.Location = New System.Drawing.Point(769, 369)
        Me.LabelControl14.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.LabelControl14.Name = "LabelControl14"
        Me.LabelControl14.Size = New System.Drawing.Size(58, 17)
        Me.LabelControl14.TabIndex = 4
        Me.LabelControl14.Text = "Search:"
        '
        'TextEditSearchMediaCategory
        '
        Me.TextEditSearchMediaCategory.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchMediaCategory.EditValue = ""
        Me.TextEditSearchMediaCategory.Location = New System.Drawing.Point(834, 365)
        Me.TextEditSearchMediaCategory.Margin = New System.Windows.Forms.Padding(4, 5, 4, 7)
        Me.TextEditSearchMediaCategory.Name = "TextEditSearchMediaCategory"
        Me.TextEditSearchMediaCategory.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchMediaCategory.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchMediaCategory.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchMediaCategory.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchMediaCategory.Size = New System.Drawing.Size(103, 24)
        Me.TextEditSearchMediaCategory.TabIndex = 5
        '
        'PictureClearSearchMediaCategory
        '
        Me.PictureClearSearchMediaCategory.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchMediaCategory.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchMediaCategory.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchMediaCategory.Location = New System.Drawing.Point(945, 4)
        Me.PictureClearSearchMediaCategory.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.PictureClearSearchMediaCategory.Name = "PictureClearSearchMediaCategory"
        Me.PictureClearSearchMediaCategory.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchMediaCategory.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchMediaCategory.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchMediaCategory.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem1.Text = "Clear Search"
        ToolTipItem1.LeftIndent = 6
        ToolTipItem1.Text = "Click here to clear all search boxes."
        SuperToolTip1.Items.Add(ToolTipTitleItem1)
        SuperToolTip1.Items.Add(ToolTipItem1)
        Me.PictureClearSearchMediaCategory.SuperTip = SuperToolTip1
        Me.PictureClearSearchMediaCategory.TabIndex = 0
        Me.PictureClearSearchMediaCategory.TabStop = True
        '
        'PictureAdvancedSearchMediaCategory
        '
        Me.PictureAdvancedSearchMediaCategory.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearchMediaCategory.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearchMediaCategory.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearchMediaCategory.Location = New System.Drawing.Point(945, 368)
        Me.PictureAdvancedSearchMediaCategory.Margin = New System.Windows.Forms.Padding(4, 8, 4, 9)
        Me.PictureAdvancedSearchMediaCategory.Name = "PictureAdvancedSearchMediaCategory"
        Me.PictureAdvancedSearchMediaCategory.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearchMediaCategory.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearchMediaCategory.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearchMediaCategory.Size = New System.Drawing.Size(21, 21)
        ToolTipTitleItem2.Text = "Advanced Search"
        ToolTipItem2.LeftIndent = 6
        ToolTipItem2.Text = "Click here to search individual column values."
        SuperToolTip2.Items.Add(ToolTipTitleItem2)
        SuperToolTip2.Items.Add(ToolTipItem2)
        Me.PictureAdvancedSearchMediaCategory.SuperTip = SuperToolTip2
        Me.PictureAdvancedSearchMediaCategory.TabIndex = 6
        Me.PictureAdvancedSearchMediaCategory.TabStop = True
        '
        'ButtonRemoveMediaCategory
        '
        Me.ButtonRemoveMediaCategory.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonRemoveMediaCategory.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonRemoveMediaCategory.Appearance.Options.UseFont = True
        Me.ButtonRemoveMediaCategory.ImageIndex = 2
        Me.ButtonRemoveMediaCategory.ImageList = Me.ImageList16x16
        Me.ButtonRemoveMediaCategory.Location = New System.Drawing.Point(111, 364)
        Me.ButtonRemoveMediaCategory.LookAndFeel.SkinName = "Black"
        Me.ButtonRemoveMediaCategory.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonRemoveMediaCategory.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.ButtonRemoveMediaCategory.Name = "ButtonRemoveMediaCategory"
        Me.ButtonRemoveMediaCategory.Size = New System.Drawing.Size(96, 30)
        Me.ButtonRemoveMediaCategory.TabIndex = 3
        Me.ButtonRemoveMediaCategory.Text = "Remove"
        '
        'ButtonAddStoreGroup
        '
        Me.ButtonAddStoreGroup.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAddStoreGroup.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAddStoreGroup.Appearance.Options.UseFont = True
        Me.ButtonAddStoreGroup.ImageIndex = 0
        Me.ButtonAddStoreGroup.ImageList = Me.ImageList16x16
        Me.ButtonAddStoreGroup.Location = New System.Drawing.Point(6, 364)
        Me.ButtonAddStoreGroup.LookAndFeel.SkinName = "Black"
        Me.ButtonAddStoreGroup.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAddStoreGroup.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.ButtonAddStoreGroup.Name = "ButtonAddStoreGroup"
        Me.ButtonAddStoreGroup.Size = New System.Drawing.Size(96, 30)
        Me.ButtonAddStoreGroup.TabIndex = 2
        Me.ButtonAddStoreGroup.Text = "Add"
        '
        'GridGroupChainStores
        '
        Me.GridGroupChainStores.AllowUserToAddRows = False
        Me.GridGroupChainStores.AllowUserToDeleteRows = False
        Me.GridGroupChainStores.AllowUserToOrderColumns = True
        Me.GridGroupChainStores.AllowUserToResizeRows = False
        DataGridViewCellStyle1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridGroupChainStores.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle1
        Me.GridGroupChainStores.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridGroupChainStores.BackgroundColor = System.Drawing.Color.White
        Me.GridGroupChainStores.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridGroupChainStores.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridGroupChainStores.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle2.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle2.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridGroupChainStores.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle2
        Me.GridGroupChainStores.ColumnHeadersHeight = 22
        Me.GridGroupChainStores.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridGroupChainStores.ColumnHeadersVisible = False
        Me.GridGroupChainStores.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.StoreNameColumn})
        Me.GridGroupChainStores.EnableHeadersVisualStyles = False
        Me.GridGroupChainStores.GridColor = System.Drawing.Color.White
        Me.GridGroupChainStores.Location = New System.Drawing.Point(3, 29)
        Me.GridGroupChainStores.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.GridGroupChainStores.Name = "GridGroupChainStores"
        Me.GridGroupChainStores.ReadOnly = True
        Me.GridGroupChainStores.RowHeadersVisible = False
        DataGridViewCellStyle3.ForeColor = System.Drawing.Color.DimGray
        Me.GridGroupChainStores.RowsDefaultCellStyle = DataGridViewCellStyle3
        Me.GridGroupChainStores.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridGroupChainStores.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridGroupChainStores.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridGroupChainStores.RowTemplate.Height = 19
        Me.GridGroupChainStores.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridGroupChainStores.ShowCellToolTips = False
        Me.GridGroupChainStores.Size = New System.Drawing.Size(967, 327)
        Me.GridGroupChainStores.StandardTab = True
        Me.GridGroupChainStores.TabIndex = 1
        '
        'StoreNameColumn
        '
        Me.StoreNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.StoreNameColumn.DataPropertyName = "StoreName"
        Me.StoreNameColumn.HeaderText = "Store Name"
        Me.StoreNameColumn.Name = "StoreNameColumn"
        Me.StoreNameColumn.ReadOnly = True
        '
        'LabelControl3
        '
        Me.LabelControl3.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl3.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl3.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl3.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top
        Me.LabelControl3.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap
        Me.LabelControl3.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelControl3.Location = New System.Drawing.Point(4, 48)
        Me.LabelControl3.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelControl3.Name = "LabelControl3"
        Me.LabelControl3.Size = New System.Drawing.Size(974, 34)
        Me.LabelControl3.TabIndex = 1
        Me.LabelControl3.Text = "This section allows you to edit the Store Group properties. You can create a new " &
    "store group, or rename an existing one. You can also make a store group dormant." &
    ""
        '
        'LabelControl4
        '
        Me.LabelControl4.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl4.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl4.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelControl4.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl4.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelControl4.LineVisible = True
        Me.LabelControl4.Location = New System.Drawing.Point(4, 4)
        Me.LabelControl4.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelControl4.Name = "LabelControl4"
        Me.LabelControl4.Size = New System.Drawing.Size(974, 24)
        Me.LabelControl4.TabIndex = 0
        Me.LabelControl4.Text = "Store Group Details"
        '
        'LabelControl9
        '
        Me.LabelControl9.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl9.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl9.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelControl9.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl9.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelControl9.LineVisible = True
        Me.LabelControl9.Location = New System.Drawing.Point(4, 4)
        Me.LabelControl9.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelControl9.Name = "LabelControl9"
        Me.LabelControl9.Size = New System.Drawing.Size(972, 24)
        Me.LabelControl9.TabIndex = 3
        Me.LabelControl9.Text = "Store Group Management"
        '
        'LabelControl10
        '
        Me.LabelControl10.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl10.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl10.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl10.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top
        Me.LabelControl10.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap
        Me.LabelControl10.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelControl10.Location = New System.Drawing.Point(4, 48)
        Me.LabelControl10.Margin = New System.Windows.Forms.Padding(4, 4, 4, 16)
        Me.LabelControl10.Name = "LabelControl10"
        Me.LabelControl10.Size = New System.Drawing.Size(972, 17)
        Me.LabelControl10.TabIndex = 4
        Me.LabelControl10.Text = "Here you can choose what stores belong to the group. You can add or remove stores" &
    " from the group."
        '
        'XtraTabControl1
        '
        Me.XtraTabControl1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.XtraTabControl1.AppearancePage.Header.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.XtraTabControl1.AppearancePage.Header.Options.UseFont = True
        Me.XtraTabControl1.Location = New System.Drawing.Point(15, 75)
        Me.XtraTabControl1.LookAndFeel.SkinName = "Black"
        Me.XtraTabControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.XtraTabControl1.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.XtraTabControl1.Name = "XtraTabControl1"
        Me.XtraTabControl1.SelectedTabPage = Me.TabPageDetails
        Me.XtraTabControl1.Size = New System.Drawing.Size(1027, 571)
        Me.XtraTabControl1.TabIndex = 1
        Me.XtraTabControl1.TabPages.AddRange(New DevExpress.XtraTab.XtraTabPage() {Me.TabPageDetails, Me.TabPageChains})
        '
        'TabPageDetails
        '
        Me.TabPageDetails.Controls.Add(Me.Panel1)
        Me.TabPageDetails.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.TabPageDetails.Name = "TabPageDetails"
        Me.TabPageDetails.Size = New System.Drawing.Size(1021, 539)
        Me.TabPageDetails.Text = "Details"
        '
        'Panel1
        '
        Me.Panel1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Panel1.Controls.Add(Me.TableLayoutPanelDetails)
        Me.Panel1.Location = New System.Drawing.Point(4, 4)
        Me.Panel1.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(1013, 530)
        Me.Panel1.TabIndex = 3
        '
        'TableLayoutPanelDetails
        '
        Me.TableLayoutPanelDetails.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanelDetails.BackColor = System.Drawing.Color.Transparent
        Me.TableLayoutPanelDetails.ColumnCount = 1
        Me.TableLayoutPanelDetails.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanelDetails.Controls.Add(Me.PanelDetails, 0, 2)
        Me.TableLayoutPanelDetails.Controls.Add(Me.LabelControl4, 0, 0)
        Me.TableLayoutPanelDetails.Controls.Add(Me.LabelControl3, 0, 1)
        Me.TableLayoutPanelDetails.Location = New System.Drawing.Point(15, 16)
        Me.TableLayoutPanelDetails.Margin = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.TableLayoutPanelDetails.Name = "TableLayoutPanelDetails"
        Me.TableLayoutPanelDetails.RowCount = 3
        Me.TableLayoutPanelDetails.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanelDetails.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanelDetails.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanelDetails.Size = New System.Drawing.Size(982, 498)
        Me.TableLayoutPanelDetails.TabIndex = 3
        '
        'TabPageChains
        '
        Me.TabPageChains.Controls.Add(Me.Panel3)
        Me.TabPageChains.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.TabPageChains.Name = "TabPageChains"
        Me.TabPageChains.Size = New System.Drawing.Size(1021, 539)
        Me.TabPageChains.Text = "Stores"
        '
        'Panel3
        '
        Me.Panel3.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel3.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Panel3.Controls.Add(Me.TableLayoutPanel1)
        Me.Panel3.Location = New System.Drawing.Point(4, 4)
        Me.Panel3.Margin = New System.Windows.Forms.Padding(4, 4, 4, 4)
        Me.Panel3.Name = "Panel3"
        Me.Panel3.Size = New System.Drawing.Size(1011, 522)
        Me.Panel3.TabIndex = 4
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanel1.BackColor = System.Drawing.Color.Transparent
        Me.TableLayoutPanel1.ColumnCount = 1
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.GroupControlMediaCategory, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.LabelControl9, 0, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.LabelControl10, 0, 1)
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(15, 16)
        Me.TableLayoutPanel1.Margin = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 3
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(980, 490)
        Me.TableLayoutPanel1.TabIndex = 3
        '
        'SubformStoreGroups
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(9.0!, 17.0!)
        Me.Controls.Add(Me.XtraTabControl1)
        Me.Controls.Add(Me.ButtonSave)
        Me.Controls.Add(Me.ButtonCancel)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Margin = New System.Windows.Forms.Padding(5, 5, 5, 5)
        Me.Name = "SubformStoreGroups"
        Me.Padding = New System.Windows.Forms.Padding(15, 16, 15, 16)
        Me.Tag = "821, 543"
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        Me.Controls.SetChildIndex(Me.ButtonCancel, 0)
        Me.Controls.SetChildIndex(Me.ButtonSave, 0)
        Me.Controls.SetChildIndex(Me.XtraTabControl1, 0)
        Me.PanelDetails.ResumeLayout(False)
        Me.PanelDetails.PerformLayout()
        CType(Me.CheckEditDormant.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditGroupChainName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GroupControlMediaCategory, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlMediaCategory.ResumeLayout(False)
        CType(Me.TextEditSearchMediaCategory.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearchMediaCategory.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearchMediaCategory.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridGroupChainStores, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabControl1.ResumeLayout(False)
        Me.TabPageDetails.ResumeLayout(False)
        Me.Panel1.ResumeLayout(False)
        Me.TableLayoutPanelDetails.ResumeLayout(False)
        Me.TabPageChains.ResumeLayout(False)
        Me.Panel3.ResumeLayout(False)
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents ImageList16x16 As System.Windows.Forms.ImageList
    Friend WithEvents ButtonSave As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents PanelDetails As System.Windows.Forms.Panel
    Friend WithEvents CheckEditDormant As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents LabelGroupChainName As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditGroupChainName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents GroupControlMediaCategory As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelControl14 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditSearchMediaCategory As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearchMediaCategory As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearchMediaCategory As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents ButtonRemoveMediaCategory As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonAddStoreGroup As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GridGroupChainStores As System.Windows.Forms.DataGridView
    Friend WithEvents LabelControl3 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl4 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl9 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl10 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents StoreNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents XtraTabControl1 As DevExpress.XtraTab.XtraTabControl
    Friend WithEvents TabPageDetails As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents TableLayoutPanelDetails As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents TabPageChains As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel3 As System.Windows.Forms.Panel
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList

End Class
