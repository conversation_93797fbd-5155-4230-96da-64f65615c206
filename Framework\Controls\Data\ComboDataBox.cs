﻿using System;
using System.ComponentModel;
using System.Data;
using Framework.Forms;
using System.Collections.Generic;

namespace Framework.Controls.Data
{
    public partial class ComboDataBox : DataBox
    {
        const string PLACEHOLDERTEXT = "Select...";
        // The picker is a form from which the user may pick a value for this control. It could be a date
        // picker which displays a calendar, or a data picker which displays rows of data as available
        // values for this control. The default is a date picker.
        public IPicker Picker = new DatePicker();


        #region Startup

        public ComboDataBox()
        {
            InitializeComponent();
            labelValue.Text = PLACEHOLDERTEXT;
            ErrorLabel = errorLabel1;
            GetErrorMessageMethod = GetErrorMessage;
            SubscribeToEvents();
        }

        private string GetErrorMessage()
        {
            string errormessage = string.Empty;
            return errormessage;
        }

        private void SubscribeToEvents()
        {
            labelValue.Click += LabelValue_Click;
        }

        #endregion


        #region Value

        [Browsable(true)]
        [EditorBrowsable(EditorBrowsableState.Always)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Visible)]
        [Bindable(true)]
        public override string Text
        {
            get { return labelValue.Text; }
        }

        protected override void OnValueChanged()
        {
            base.OnValueChanged();
            if (Value == null)
            {
                if (Picker is DataPicker)
                {
                    ((DataPicker)Picker).ResetSelection();
                }
            }
            UpdateDisplayedText();
        }

        public enum DataBoxDateDisplayFormat
        {
            DateOnly,
            DateAndTime
        }

        private DataBoxDateDisplayFormat _DateDisplayFormat = DataBoxDateDisplayFormat.DateOnly;
        public DataBoxDateDisplayFormat DateDisplayFormat
        {
            get { return _DateDisplayFormat; }
            set { _DateDisplayFormat = value; }
        }

        private void UpdateDisplayedText()
        {
            if (Value is DateTime)
            {
                DateTime datevalue = (DateTime)Value;
                if (DateDisplayFormat == DataBoxDateDisplayFormat.DateOnly)
                {
                    labelValue.Text = datevalue.Date.ToLongDateString();
                }
                else
                {
                    labelValue.Text = datevalue.Date.ToLongDateString() + " at " + datevalue.ToShortTimeString();
                }
            }
            else
            {
                if (Picker is DataPicker)
                {
                    var datapicker = (DataPicker)Picker;
                    if (datapicker.SelectedDataRows.Count > 0)
                    {
                        var selecteddatarow = datapicker.SelectedDataRows[0];
                        var stringtodisplay = (string)selecteddatarow[DisplayMember];
                        labelValue.Text = stringtodisplay.Replace("&", "&&");
                    }
                    else
                    {
                        labelValue.Text = PLACEHOLDERTEXT;
                    }
                }
                else
                {
                }
            }
        }

        private string _ValueMember = string.Empty;
        public string ValueMember
        {
            get { return _ValueMember; }
            set
            {
                if (_ValueMember != value)
                {
                    _ValueMember = value;
                }
            }
        }

        private string _DisplayMember = string.Empty;
        public string DisplayMember
        {
            get { return _DisplayMember; }
            set
            {
                if (_DisplayMember != value)
                {
                    _DisplayMember = value;
                }
            }
        }

        #endregion


        #region Click event

        private void LabelValue_Click(object sender, EventArgs e)
        {
            UsePickerToUpdateControl();
        }

        private void UsePickerToUpdateControl()
        {
            if (Picker == null)
            {
                MessageForm.Show("There is no picker for this control. I have no idea how to prompt you for input.", "Please Report This Error");
            }
            else
            {
                // If this control has a value, pre-select it in the picker.
                if (Value != null)
                {
                    if (Value is DateTime)
                    {
                        if (Picker is DatePicker)
                        {
                            ((DatePicker)Picker).SelectedDate = (DateTime)Value;
                        }
                    }
                }

                // Display the Picker and get a selection from the user.
                object selection = Picker.Pick();

                if (string.IsNullOrEmpty(Picker.ErrorMessage) == false)
                {
                    DisplayErrorMessage(Picker.ErrorMessage);
                }
                else
                {
                    if (selection != null)
                    {
                        if (selection is DateTime)
                        {
                            Value = selection;
                        }
                        else
                        {
                            if (selection is List<DataRow>)
                            {
                                if (string.IsNullOrEmpty(ValueMember))
                                {
                                    MessageForm.Show("ComboDataBox.ValueMemeber needs a string value.");
                                }
                                else
                                {
                                    var selecteddatarow = ((List<DataRow>)selection)[0];
                                    Value = selecteddatarow[ValueMember];
                                }
                            }
                            else
                            {
                                MessageForm.Show("Invalid selection in ComboDataBox.UsePickerToUpdateControl().");
                            }
                        }
                    }
                }
            }
        }

        private void DisplayErrorMessage(string errormessage)
        {
            MessageForm.Show(errormessage, "Error in Data Picker of Combo Data Box");
        }

        #endregion

    }
}
