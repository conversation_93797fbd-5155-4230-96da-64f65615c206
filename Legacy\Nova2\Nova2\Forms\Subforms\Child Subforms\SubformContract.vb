Imports System.Globalization

Public Class SubformContract

    Private WithEvents OldDataObject As OldContract = Nothing
    Private TabsUnaffectedByContractLock As New List(Of DevExpress.XtraTab.XtraTabPage)

#Region "Event Handlers"

    Public Sub New(ByVal ContractObject As OldContract)

        ' This call is required by the Windows Form Designer.
        InitializeComponent()
        ' Add any initialization after the InitializeComponent() call.
        OldDataObject = ContractObject
        ConfigureTabLockBehaviour()


    End Sub

    Private Sub Subform_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        PerformDataBinding(OldDataObject)

        ' Load grid data
        GridBursts.AutoGenerateColumns = False
        GridBursts.DataSource = OldDataObject.BurstBindingSource
        GridProduction.AutoGenerateColumns = False
        GridProduction.DataSource = OldDataObject.ProductionBindingSource
        GridMiscellaneousCharges.AutoGenerateColumns = False
        GridMiscellaneousCharges.DataSource = OldDataObject.MiscellaneousChargeBindingSource
        GridMediaCosts.AutoGenerateColumns = False
        GridMediaCosts.DataSource = OldDataObject.MediaCostBindingSource
        GridInvoiceNumbers.AutoGenerateColumns = False
        GridInvoiceNumbers.DataSource = OldDataObject.InvoiceNumberBindingSource
        GridCostEstimates.AutoGenerateColumns = False
        GridCostEstimates.DataSource = OldDataObject.CostEstimateBindingSource


        ' Configure grid managers.
        If IsNothing(OldDataObject) = False Then
            Dim GridManagerBursts As New GridManager(GridBursts, TextEditSearchBurst, Nothing, Nothing,
            PictureAdvancedSearchBurst, PictureClearSearchBursts, ButtonEditBurst, ButtonDeleteBurst)
            Dim GridManagerProduction As New GridManager(GridProduction, TextEditSearchProduction, Nothing, Nothing,
            PictureAdvancedSearchProduction, PictureClearSearchProduction, ButtonEditProduction, ButtonRemoveProduction)

            Dim GridManagerMediaCost As New GridManager(GridMediaCosts, Nothing, Nothing, Nothing,
           Nothing, Nothing, ButtonEditMediaCost, Nothing)

            Dim GridManagerInvoiceNumbers As New GridManager(GridInvoiceNumbers, Nothing, Nothing, Nothing,
           Nothing, Nothing, ButtonEditInvoiceNumber, ButtonDeleteInvoiceNumber)
            Dim GridManagerCostEstimates As New GridManager(GridCostEstimates, Nothing, Nothing, Nothing,
           Nothing, Nothing, ButtonEditCostEstimate, ButtonDeleteCostEstimate)
        End If
        Dim GridManagerMiscellaneousCharges As New GridManager(GridMiscellaneousCharges, TextEditSearchMiscellaneousCharge, Nothing, Nothing,
        PictureAdvancedSearchMiscellaneousCharge, PictureClearSearchMiscellaneousCharges, ButtonEditMiscellaneousCharge, ButtonRemoveMiscellaneousCharge)

        ' Set miscellaneous properties.
        LiquidAgent.EnableHyperlink(LabelSignedBy, Not OldDataObject.Signed)

        ' Alert user if the contract is in the lock period.
        If IsNothing(OldDataObject) = False Then
            If Not OldDataObject.Row.RowState = DataRowState.Detached AndAlso OldDataObject.Cancelled = False Then
                ' The user is not creating a new contract and the contract is not cancelled.
                If OldDataObject.InLockPeriod AndAlso My.User.IsInRole("ops_contractmodifier") = False Then
                    ' The contract is in the lock period.
                    If OldDataObject.Signed Then
                        ' Display message to alert the user that some properties can't be changed.
                        CType(TopLevelControl, BaseForm).ShowMessage _
                        (OldDataObject.ErrorMessage_LockedContractUnchangeable, "Contract Locked", MessageBoxIcon.Information)
                        ' Disable tabs so that user cannot change their contents.
                        For Each Tab As DevExpress.XtraTab.XtraTabPage In TabControl.TabPages
                            If TabsUnaffectedByContractLock.Contains(Tab) = False Then
                                Tab.PageEnabled = False
                            End If
                        Next
                    Else
                        ' Display message to alert the user that the contract can't be signed.
                        CType(TopLevelControl, BaseForm).ShowMessage _
                        (OldDataObject.ErrorMessage_LockedContractUnsignable, "Contract Locked", MessageBoxIcon.Information)
                    End If
                End If
            End If
        End If

        DataObject_ErrorInfoChanged()
        DisableControlsAfterSignature()
        CalculateMediaCostTotals()
        CalculateCETotals()
        CalculateInvoiceTotals()
    End Sub

    Private Sub ButtonSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonSave.Click

        ' Stop if the contract is cancelled because cancelled contracts may not be modified.
        If OldDataObject.Cancelled Then
            CType(TopLevelControl, BaseForm).ShowMessage("Cancelled contracts may not be modified and saved after " _
            & "cancellation.", "Changing Cancelled Contracts Not Permitted", MessageBoxIcon.Error)
            Exit Sub
        End If

        ' Check for possible errors.
        OldDataObject.RefreshAllErrors()

        ' Save and close.
        Save(True)

        ' Open the contract for viewing.
        If TypeOf ParentSubform Is SubformContractStart Then
            CType(ParentSubform, SubformContractStart).OpenContract(OldDataObject.ContractNumber)
        ElseIf TypeOf ParentSubform Is SubformContractTasks Then
            CType(ParentSubform, SubformContractTasks).RefreshControls(ParentSubform)
            CType(ParentSubform, SubformContractTasks).RefreshWarnings()
        End If

    End Sub

    Private Sub ButtonCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonCancel.Click

        If IsNothing(OldDataObject) = False Then
            OldDataObject.Close(Me)
        Else
            RevertToParentSubform()
        End If

    End Sub

    Private Sub TextEditCommission_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles TextEditCommission.EditValueChanged

        ' Create an object to represent the control.
        Dim CurrentControl As TextEdit = CType(sender, TextEdit)

        UpdateExampleAgencyCommCalc()

        ' Reset the error text of the control to an empty string.
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)

    End Sub

    Private Sub HyperlinkAccountManager_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles HyperlinkAccountManager.Click

        ' Prevent changes to the contract if it has already been signed.
        If OldDataObject.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupAccountManager.SelectRowsByPermission_EditMyContracts(My.Settings.DBConnection, False, Nothing)

        ' Update the data object and the label with the selection.
        If SelectedItems.Count > 0 Then
            OldDataObject.AccountManagerID = SelectedItems(0).Item("AccountManagerID")
            CurrentControl.DataBindings("Text").ReadValue()
            LabelTitle.DataBindings("Text").ReadValue()
            ' Reset the error text of the control to an empty string.
            LiquidAgent.ControlValidation(CurrentControl, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkClient_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles HyperlinkClient.Click

        ' Prevent changes to the contract if it has already been signed.
        If OldDataObject.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If

        ' Stop if no Account manager has been selected.
        If OldDataObject.AccountManagerID = -1 Then
            CType(TopLevelControl, BaseForm).ShowMessage _
            ("Please select an account manager before selecting a client.", "Account Manager Required")
            Exit Sub
        End If

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupClient.SelectRows(My.Settings.DBConnection, False, Nothing)

        ' Update the data object and the label with the selection.
        If SelectedItems.Count > 0 Then
            OldDataObject.SelectedClient = SelectedItems(0)
            CurrentControl.DataBindings("Text").ReadValue()
            ' Reset the error text of the control to an empty string.
            LiquidAgent.ControlValidation(CurrentControl, String.Empty)
        End If

    End Sub


    Private Sub HyperlinkContractProposalHeat_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles HyperlinkContractProposalHeat.Click

        ' Prevent changes to the contract if it has already been signed.
        If OldDataObject.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If

        ' Stop if no Account manager has been selected.
        If OldDataObject.AccountManagerID = -1 Then
            CType(TopLevelControl, BaseForm).ShowMessage _
            ("Please select an account manager before selecting a proposal heat.", "Account Manager Required")
            Exit Sub
        End If

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupProposalHeat.SelectRows(My.Settings.DBConnection, False, Nothing)

        ' Update the data object and the label with the selection.
        If SelectedItems.Count > 0 Then
            OldDataObject.SelectedProposalHeat = SelectedItems(0)
            CurrentControl.DataBindings("Text").ReadValue()
            ' Reset the error text of the control to an empty string.
            LiquidAgent.ControlValidation(CurrentControl, String.Empty)
        End If

    End Sub


    Private Sub HyperlinkAgency_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles HyperlinkAgency.Click

        ' Prevent changes to the contract if it has already been signed.
        If OldDataObject.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If

        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupClient.SelectAgencyRows(My.Settings.DBConnection, False, Nothing)

        ' Update the data object and the label with the selection.
        If SelectedItems.Count > 0 Then
            OldDataObject.AgencyName = SelectedItems(0).Item("ClientName")
            OldDataObject.AgencyID = SelectedItems(0).Item("ClientID")
            CurrentControl.DataBindings("Text").ReadValue()
            LabelAgencySummaryValue.DataBindings("Text").ReadValue()
            ' Reset the error text of the control to an empty string.
            LiquidAgent.ControlValidation(CurrentControl, String.Empty)
        End If

    End Sub

    Private Sub CheckEditApplyAgencyComm_CheckedChanged(ByVal sender As Object, ByVal e As System.EventArgs) Handles CheckEditApplyAgencyComm.CheckedChanged

        ' Create an object to represent the control.
        Dim CheckedControl As CheckEdit = CType(sender, CheckEdit)

        ' Update the data object.
        OldDataObject.ApplyAgencyComm = CheckedControl.Checked

        ' Update the error status of the hyperlink.
        LiquidAgent.ControlValidation(HyperlinkAgency, String.Empty)

    End Sub

    Private Sub CheckEditPercentageOfNetRental_CheckedChanged(sender As Object, e As EventArgs) Handles CheckEditPercentageOfNetRental.CheckedChanged
        UpdateExampleAgencyCommCalc()
    End Sub

    Private Sub GridBursts_CellMouseDoubleClick _
    (ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) _
    Handles GridBursts.CellMouseDoubleClick
        If e.RowIndex > -1 Then
            ' The row double-clicked is NOT the column header row. Handle the event.
            ButtonEditBurst_Click(sender, e)
        End If
    End Sub

    Private Sub GridProduction_CellMouseDoubleClick _
    (ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) _
    Handles GridProduction.CellMouseDoubleClick
        If e.RowIndex > -1 Then
            ' The row double-clicked is NOT the column header row. Handle the event.
            ButtonEditProduction_Click(sender, e)
        End If
    End Sub

    Private Sub GridMiscellaneousCharges_CellMouseDoubleClick _
    (ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs)

        If e.RowIndex > -1 Then
            ' The row double-clicked is NOT the column header row. Handle the event.
            ButtonEditMiscellaneousCharge_Click(sender, e)
        End If
    End Sub

    Private Sub ButtonAddBurst_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonAddBurst.Click

        ' Prevent changes to the contract if it has already been signed.
        If OldDataObject.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If

        AddChild(New SubformBurst(True, OldDataObject))
    End Sub

    Private Sub ButtonEditBurst_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonEditBurst.Click
        ' Stop if no rows were selected.
        If GridBursts.SelectedRows.Count < 1 Then
            CType(TopLevelControl, LiquidShell.BaseForm).ShowMessage _
            ("Please select at least one row before using the 'Edit' button.", "Grated Cheese Paradigm")
            Exit Sub
        Else
            AddChild(New SubformBurst(False, OldDataObject))
        End If
    End Sub

    Private Sub ButtonDeleteBurst_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonDeleteBurst.Click

        ' Prevent changes to the contract if it has already been signed.
        If OldDataObject.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If
        OldDataObject.DeleteBursts(GridBursts)
    End Sub

    Private Sub ButtonAddProduction_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonAddProduction.Click

        ' Prevent changes to the contract if it has already been signed.
        If OldDataObject.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If

        ' Check if bursts have been added.
        If GridBursts.Rows.Count = 0 Then
            ' Production may not be added before bursts are added.
            CType(TopLevelControl, BaseForm).ShowMessage("Please add at least one burst before adding production to this contract.", "Bursts Required")
            Exit Sub
        End If

        ' Add production.
        OpenProductionDetailSubform(True)

    End Sub

    Private Sub ButtonEditProduction_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonEditProduction.Click

        ' Prevent changes to the contract if it has already been signed.
        If OldDataObject.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If
        OpenProductionDetailSubform(False)
    End Sub

    Private Sub ButtonRemoveProduction_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonRemoveProduction.Click

        ' Prevent changes to the contract if it has already been signed.
        If OldDataObject.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If
        OldDataObject.DeleteProduction(GridProduction)
    End Sub

    Private Sub ButtonAddMiscellaneousCharge_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonAddMiscellaneousCharge.Click

        ' Prevent changes to the contract if it has already been signed.
        If OldDataObject.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If
        OpenMiscellaneousChargeDetailSubform(True)
    End Sub

    Private Sub ButtonEditMiscellaneousCharge_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonEditMiscellaneousCharge.Click

        ' Prevent changes to the contract if it has already been signed.
        If OldDataObject.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If
        OpenMiscellaneousChargeDetailSubform(False)
    End Sub

    Private Sub ButtonDeleteMiscellaneousCharge_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonRemoveMiscellaneousCharge.Click

        ' Prevent changes to the contract if it has already been signed.
        If OldDataObject.Signed Then
            Contract.ModifyingAfterSignatureNotPermitted(Me)
            Exit Sub
        End If
        OldDataObject.DeleteMiscellaneousCharge(GridMiscellaneousCharges)
    End Sub

    Private Sub DataObject_BurstInfoChanged() Handles OldDataObject.BurstInfoChanged

        ' Refresh all related controls.
        For Each Item As Control In PanelBurstInfo2.Controls
            If TypeOf Item Is LabelControl Then
                If IsNothing(Item.DataBindings("Text")) = False Then
                    Item.DataBindings("Text").ReadValue()
                End If
                If IsNothing(Item.DataBindings("Visible")) = False Then
                    Item.DataBindings("Visible").ReadValue()
                End If
            End If
        Next
        For Each Item As Control In PanelBurstInfo1.Controls
            If TypeOf Item Is LabelControl Then
                If IsNothing(Item.DataBindings("Text")) = False Then
                    Item.DataBindings("Text").ReadValue()
                End If
                If IsNothing(Item.DataBindings("Visible")) = False Then
                    Item.DataBindings("Visible").ReadValue()
                End If
            End If
        Next

    End Sub

    Private Sub DataObject_ErrorInfoChanged() Handles OldDataObject.ErrorInfoChanged
        ' Display the errors tab page if errors exist.
        If IsNothing(OldDataObject) = False Then
            If String.IsNullOrEmpty(OldDataObject.ContractErrors) Then
                TabPageErrors.PageVisible = False
            Else
                TabPageErrors.PageVisible = True
            End If
        End If
    End Sub

    Private Sub DataObject_ProductionInfoChanged() Handles OldDataObject.ProductionInfoChanged

        LabelProductionChargesValue.DataBindings("Text").ReadValue()
        LabelMiscellaneousChargesValue.DataBindings("Text").ReadValue()

    End Sub

    Private Sub LabelSignedBy_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles LabelSignedBy.Click

        ' PROVISIONALLY sign the contract.
        OldDataObject.Signed = True

        ' Get the containing parent form and use its error manager to validate all fields.
        Dim TheParent As BaseForm = CType(TopLevelControl, BaseForm)
        Dim NoErrors As Boolean = TheParent.ErrorManager.ValidationSuccessful(TheParent)

        ' Check if validation was successful.
        If NoErrors = False Then
            ' Validation was unsuccessful. Reset the signature status and exit.
            OldDataObject.Signed = False
            Exit Sub
        End If

        ' Sign the contract.
        Dim SignatureSuccess As Boolean = OldDataObject.SignContract()

        ' Save and close if the signature was successful.
        If SignatureSuccess Then
            Save(True)
            CType(ParentSubform, SubformContractTasks).RefreshControls(ParentSubform)
        End If

    End Sub

    Private Sub LabelErrors_Click(ByVal sender As Object, ByVal e As System.EventArgs) Handles LabelErrors.Click

        ' Display the errors tab page if there are errors.
        If String.IsNullOrEmpty(OldDataObject.ContractErrors) = False Then
            ' Errors exist. Display the errors tab page.
            TabControl.SelectedTabPage = TabControl.TabPages(TabControl.TabPages.IndexOf(TabPageErrors))
        End If

        ' Reset the error status of the label.
        LiquidAgent.ControlValidation(LabelErrors, String.Empty)
        LabelErrors.ForeColor = Color.WhiteSmoke
        LabelErrors.Cursor = Cursors.Default

    End Sub


    Private Sub ButtonEditMediaCost_Click(sender As Object, e As EventArgs) Handles ButtonEditMediaCost.Click
        'Allow user to edit the amount
        OpenMediaCostDetailSubform(False)
    End Sub

    Private Sub GridMediaCosts_CellValueChanged(sender As Object, e As DataGridViewCellEventArgs) Handles GridMediaCosts.CellValueChanged
        CalculateMediaCostTotals()
    End Sub

    Private Sub ButtonEditInvoiceNumber_Click(sender As Object, e As EventArgs) Handles ButtonEditInvoiceNumber.Click
        OpenInvoiceNumberDetailSubform(False, OldDataObject.ContractNumber)
    End Sub
    Private Sub ButtonAddInvoiceNumber_Click(sender As Object, e As EventArgs) Handles ButtonAddInvoiceNumber.Click
        OpenInvoiceNumberDetailSubform(True, OldDataObject.ContractNumber)
    End Sub

    Private Sub GridInvoiceNumbers_CellMouseDoubleClick _
    (ByVal sender As Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) _
    Handles GridInvoiceNumbers.CellMouseDoubleClick
        If e.RowIndex > -1 Then
            ' The row double-clicked is NOT the column header row. Handle the event.
            ButtonEditInvoiceNumber_Click(sender, e)
        End If
    End Sub


    Private Sub ButtonDeleteInvoiceNumber_Click(sender As Object, e As EventArgs) Handles ButtonDeleteInvoiceNumber.Click
        OldDataObject.DeleteInvoiceNumber(GridInvoiceNumbers)
    End Sub

    Private Sub ButtonAddCostEstimate_Click(sender As Object, e As EventArgs) Handles ButtonAddCostEstimate.Click
        OpenCostEstimateDetailSubform(True, OldDataObject.ContractNumber)
    End Sub

    Private Sub ButtonEditCostEstimate_Click(sender As Object, e As EventArgs) Handles ButtonEditCostEstimate.Click
        OpenCostEstimateDetailSubform(False, OldDataObject.ContractNumber)
    End Sub

    Private Sub ButtonDeleteCostEstimate_Click(sender As Object, e As EventArgs) Handles ButtonDeleteCostEstimate.Click
        OldDataObject.DeleteCostEstimateNumber(GridCostEstimates)
    End Sub

#End Region


#Region "Validation"

    Private Sub TextEditCommission_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles TextEditCommission.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If CDec(ValidatedControl.EditValue) > 100 Then
            LiquidAgent.ControlValidation(ValidatedControl, "The agency commission may not be greater than 100%.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkClient_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles HyperlinkClient.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        If String.Compare(ValidatedControl.Text, "Select...") = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "A client must be selected.")
        Else
            If OldDataObject.Signed Then
                ' Check whether finance has approved this client.
                Dim Errors As String = String.Empty
                Dim ClientID As Integer = OldDataObject.ClientID
                Dim ClientApproved As Boolean = ClientIsApproved(ClientID, Errors)
                ' Check if errors occured.
                If Not String.IsNullOrEmpty(Errors) Then
                    ' Errors occured while checking client approval.
                    Dim ErrorMessage As String = "A problem occured while checking whether the client is approved by the finance department."
                    CType(TopLevelControl, BaseForm).ShowMessage(ErrorMessage & vbCrLf & vbCrLf & Errors, "Bad News...", MessageBoxIcon.Error)
                    LiquidAgent.ControlValidation(ValidatedControl, ErrorMessage)
                Else
                    ' No errors occured. Check if the client is approved by finance.
                    If Not ClientApproved Then
                        LiquidAgent.ControlValidation(ValidatedControl, "The selected client needs to be approved by the finance department.")
                    Else
                        LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
                    End If
                End If
            Else
                LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
            End If
        End If

    End Sub


    Private Sub HyperlinkContractProposalHeat_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles HyperlinkContractProposalHeat.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        If String.Compare(ValidatedControl.Text, "Select...") = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "A proposal type must be selected.")
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkAccountManager_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles HyperlinkAccountManager.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        If String.Compare(ValidatedControl.Text, "Select...") = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "An account manager must be selected.")
        ElseIf My.User.IsInRole("ops_contractmodifier") Then
            If OldDataObject.InLockPeriodWhenOpened.Value = False AndAlso OldDataObject.InLockPeriod = False Then
                LiquidAgent.ControlValidation(ValidatedControl, "You are only permitted to save contracts for the selected account manager " _
                                              & "when this contract is locked." & vbCrLf & "Currently, this contract is not locked.")
            End If
        ElseIf My.User.IsInRole("ops_contractmodifier") = False _
            AndAlso OldContract.ContractIsReadOnly(OldDataObject.Row, My.Settings.DBConnection) Then
            LiquidAgent.ControlValidation(ValidatedControl, "You are not permitted to save contracts for the selected account manager.")
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub HyperlinkAgency_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles HyperlinkAgency.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        If CheckEditApplyAgencyComm.Checked AndAlso String.Compare(ValidatedControl.Text, "Select...") = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "An agency must be selected.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub LabelErrors_Validated(ByVal sender As Object, ByVal e As System.EventArgs) Handles LabelErrors.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Check for errors.
        OldDataObject.RefreshAllErrors()
        If OldDataObject.ContractErrors.Length > 0 Then
            LiquidAgent.ControlValidation _
            (ValidatedControl, "The current configuration of this contract prevents it from being saved " _
            & "(see the 'Errors' tab page for details).")
            LabelErrors.Cursor = Cursors.Hand
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

    End Sub

    Private Sub LabelSignedBy_Validated(sender As Object, e As System.EventArgs) Handles LabelSignedBy.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As LabelControl = CType(sender, LabelControl)

        ' Error if no billing instructions have been specified.
        ' Define a variable to hold possible errors while checking for billing instructions.
        If OldDataObject.Signed Then

            Dim Errors As String = String.Empty
            ' Define the select statement we need to check for billing instructions.
            Dim SelectStatement As String = "SELECT BillingInstructionID FROM Sales.BillingInstruction WHERE (ContractID = '" & OldDataObject.ContractID.ToString & "')"
            ' Get a table containing billing instructions.
            Dim BillingInstruction As DataTable = LiquidShell.LiquidAgent.GetSqlDataTable(My.Settings.DBConnection, SelectStatement.ToString, Errors)
            ' Check if any errors occured during the command execution.
            If Errors.Length > 0 Then
                ' Errors occured.
                LiquidAgent.ControlValidation(ValidatedControl, "Couldn't check for billing instructions: " & Errors)
                Exit Sub
            End If

            ' Check if any results were returned.
            If BillingInstruction.Rows.Count = 0 AndAlso (OldDataObject.TotalRental + OldDataObject.TotalProduction > 0) Then
                ' We have no billing instructions. Inform the user and set the error message.
                LiquidAgent.ControlValidation(ValidatedControl, "Please specify billing instructions before signing the contract.")
            Else
                LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
            End If

        End If

    End Sub

#End Region

#Region "Methods"

    Private Function ClientIsApproved(ClientID As Integer, ByRef Errors As String) As Boolean

        ' Build the SQL command text.
        Dim CmdText As String = $"SELECT ApprovedByFinance FROM Client.Client WHERE (ClientID = {ClientID.ToString})"

        ' Get the result.
        Dim Result As Boolean = CBool(LiquidShell.LiquidAgent.GetSqlScalarValue(My.Settings.DBConnection, CmdText, Errors))

        ' Return the result.
        Return Result

    End Function

    Private Sub ConfigureTabLockBehaviour()

        ' Add any tab pages that aren't affected if the contract is in the LOCK status, to the
        ' TabsUnaffectedByContractLock list.
        With TabsUnaffectedByContractLock
            .Add(TabPageSummary)
            .Add(TabPageMiscellaneousCharges)
            .Add(TabPageErrors)
            .Add(TabPageMediaCosts)
        End With

    End Sub

    Private Sub PerformDataBinding(BindingObject As OldContract)

        ' Form title.
        LabelTitle.DataBindings.Add("Text", BindingObject, "ContractNumber")

        ' Contract Properties
        HyperlinkClient.DataBindings.Add("Text", BindingObject, "ClientName", False, DataSourceUpdateMode.Never)
        HyperlinkAccountManager.DataBindings.Add("Text", BindingObject, "AccountManagerName", False, DataSourceUpdateMode.Never)
        HyperlinkContractProposalHeat.DataBindings.Add("Text", BindingObject, "ContractProposalHeatName", False, DataSourceUpdateMode.Never)


        ' Production
        LabelProductionChargesValue.DataBindings.Add("Text", BindingObject, "ProductionCharges", True, DataSourceUpdateMode.Never, 0, "C2", New CultureInfo("en-ZA"))
        LabelMiscellaneousChargesValue.DataBindings.Add("Text", BindingObject, "MiscellaneousCharges", True, DataSourceUpdateMode.Never, 0, "C2", New CultureInfo("en-ZA"))

        ' Signature Status
        LabelSignedBy.DataBindings.Add("Text", BindingObject, "SignedByLabel")
        LabelSignedByValue.DataBindings.Add("Visible", BindingObject, "Signed")
        LabelSignedByValue.DataBindings.Add("Text", BindingObject, "SignedBy")
        LabelSignatureDate.DataBindings.Add("Visible", BindingObject, "Signed")
        LabelSignatureDateValue.DataBindings.Add("Visible", BindingObject, "Signed")
        LabelSignatureDateValue.DataBindings.Add("Text", BindingObject, "SignDate", True, DataSourceUpdateMode.Never, String.Empty, "d MMMM yyyy 'at' HH:mm")
        LabelCancellationDateValue.DataBindings.Add("Text", BindingObject, "CancelDate", True, DataSourceUpdateMode.Never, String.Empty, "d MMMM yyyy 'at' HH:mm")
        LabelCancelledBy.DataBindings.Add("Visible", BindingObject, "Cancelled")
        LabelCancelledByValue.DataBindings.Add("Visible", BindingObject, "Cancelled")
        LabelCancelledByValue.DataBindings.Add("Text", BindingObject, "CancelledBy")
        LabelCancellationDate.DataBindings.Add("Visible", BindingObject, "Cancelled")
        LabelCancellationDateValue.DataBindings.Add("Visible", BindingObject, "Cancelled")
        PictureSignatureStatus.DataBindings.Add("Image", BindingObject, "SignatureStatusIcon")


        ' Bursts
        LabelMediaServicesValue.DataBindings.Add("Text", BindingObject, "MediaServices")
        LabelChainsValue.DataBindings.Add("Text", BindingObject, "Chains")
        LabelCategoriesValue.DataBindings.Add("Text", BindingObject, "Categories")
        LabelBrandsValue.DataBindings.Add("Text", BindingObject, "Brands")
        LabelFirstWeekValue.DataBindings.Add("Visible", BindingObject, "BurstsExist")
        LabelFirstWeekValue.DataBindings.Add("Text", BindingObject, "FirstWeek", True, DataSourceUpdateMode.Never, 0, "d MMMM yyyy")
        LabelLastWeekValue.DataBindings.Add("Visible", BindingObject, "BurstsExist")
        LabelLastWeekValue.DataBindings.Add("Text", BindingObject, "LastWeek", True, DataSourceUpdateMode.Never, 0, "d MMMM yyyy")
        LabelTotalWeeksValue.DataBindings.Add("Text", BindingObject, "TotalWeeks")
        LabelRentalValue.DataBindings.Add("Text", BindingObject, "Rental", True, DataSourceUpdateMode.Never, 0, "C2", New CultureInfo("en-ZA"))
        LabelTotalWeeksValue.DataBindings.Add("Visible", BindingObject, "BurstsExist")
        LabelRentalValue.DataBindings.Add("Visible", BindingObject, "BurstsExist")

        ' Agency Details
        LabelAgencySummaryValue.DataBindings.Add("Visible", BindingObject, "ApplyAgencyComm")
        LabelCommissionSummary.DataBindings.Add("Visible", BindingObject, "ApplyAgencyComm")
        LabelCommissionSummaryValue.DataBindings.Add("Visible", BindingObject, "ApplyAgencyComm")
        LabelAgencyCommission.DataBindings.Add("Visible", BindingObject, "ApplyAgencyComm")
        LabelAgencyCommissionValue.DataBindings.Add("Visible", BindingObject, "ApplyAgencyComm")
        LabelAgencySummary.DataBindings.Add("Text", BindingObject, "AgencySummaryLabel")
        LabelAgencySummaryValue.DataBindings.Add("Text", BindingObject, "AgencyName")
        LabelCommissionSummaryValue.DataBindings.Add("Text", BindingObject, "AgencyCommPercentage", True, DataSourceUpdateMode.Never, 0, "0.00 '%'")
        LabelAgencyCommissionValue.DataBindings.Add("Text", BindingObject, "AgencyCommission", True, DataSourceUpdateMode.Never, 0, "C2", New CultureInfo("en-ZA"))

        ' Agency tab page
        CheckEditApplyAgencyComm.DataBindings.Add("Checked", BindingObject, "ApplyAgencyComm", False, DataSourceUpdateMode.OnPropertyChanged)
        PanelCommOptions.DataBindings.Add("Visible", CheckEditApplyAgencyComm, "Checked")
        HyperlinkAgency.DataBindings.Add("Text", BindingObject, "AgencyName")
        TextEditCommission.DataBindings.Add("EditValue", BindingObject, "AgencyCommPercentage")
        CheckEditPercentageOfNetRental.DataBindings.Add _
            ("Checked", BindingObject, "AgencyCommIsPercentageOfNetRental", False, DataSourceUpdateMode.OnPropertyChanged)
        CheckEditPrintAgencyComm.DataBindings.Add _
            ("Checked", BindingObject, "PrintAgencyComm", False, DataSourceUpdateMode.OnPropertyChanged)

        ' Errors tab page
        MemoEditErrors.DataBindings.Add("EditValue", BindingObject, "ContractErrors")

    End Sub

    Private Sub UpdateExampleAgencyCommCalc()

        ' Update the example calculation of the agency commission using the percentage entered by the user.
        Dim TotalRental As Decimal = 100000
        Dim Discount As Decimal = 20000
        Dim NetRental As Decimal = TotalRental - Discount
        Dim CommPercentage As Decimal = 0
        If IsDBNull(TextEditCommission.EditValue) = False Then
            CommPercentage = TextEditCommission.EditValue
        End If
        Dim CommAsPercentOfNetRental As Decimal = CommPercentage / 100 * NetRental
        Dim CommAsPercentOfRevenue As Decimal = NetRental - (NetRental / (1 + CommPercentage / 100))
        Dim Revenue As Decimal = 0

        LabelTotalRental.Text = TotalRental.ToString("C2", New CultureInfo("en-ZA"))
        LabelDiscount.Text = Discount.ToString("C2", New CultureInfo("en-ZA"))
        LabelNetRental.Text = NetRental.ToString("C2", New CultureInfo("en-ZA"))
        LabelCommLabel.Text = "Agency commission (" & CommPercentage.ToString("0.00") & "%):"
        If CheckEditPercentageOfNetRental.Checked Then
            LabelComm.Text = CommAsPercentOfNetRental.ToString("C2", New CultureInfo("en-ZA"))
            Revenue = NetRental - CommAsPercentOfNetRental
        Else
            LabelComm.Text = CommAsPercentOfRevenue.ToString("C2", New CultureInfo("en-ZA"))
            Revenue = NetRental - CommAsPercentOfRevenue
        End If
        LabelRevenue.Text = Revenue.ToString("C2", New CultureInfo("en-ZA"))

    End Sub

    Protected Overrides Function Save() As Boolean

        ' Get the grids that need to be audited. These will be passed into the Save method.
        Dim GridsToAudit As New List(Of DataGridView)

        ' Proceed with the save.
        Return OldDataObject.Save(My.Settings.DBConnection, GridsToAudit)

    End Function

    Private Sub OpenProductionDetailSubform(ByVal NewItem As Boolean)

        If NewItem = False Then
            ' User clicked the Edit button.
            Dim ParentForm As BaseForm = CType(TopLevelControl, BaseForm)
            If GridProduction.SelectedRows.Count < 1 Then
                ' Stop because no rows are selected.
                ParentForm.ShowMessage("Please select the row to edit before using the 'Edit' button.")
                Exit Sub
            ElseIf GridProduction.SelectedRows.Count > 1 Then
                ' Stop because more than one row is selected.
                ParentForm.ShowMessage("Please select only one row to edit before using the 'Edit' button.")
                Exit Sub
            End If
        End If

        ' Open the form to edit the item.
        AddChild(New SubformProduction(NewItem, OldDataObject))

    End Sub

    Private Sub OpenMiscellaneousChargeDetailSubform(ByVal NewItem As Boolean)

        If NewItem = False Then
            ' User clicked the Edit button.
            Dim ParentForm As BaseForm = CType(TopLevelControl, BaseForm)
            If GridMiscellaneousCharges.SelectedRows.Count < 1 Then
                ' Stop because no rows are selected.
                ParentForm.ShowMessage("Please select the row to edit before using the 'Edit' button.")
                Exit Sub
            ElseIf GridMiscellaneousCharges.SelectedRows.Count > 1 Then
                ' Stop because more than one row is selected.
                ParentForm.ShowMessage("Please select only one row to edit before using the 'Edit' button.")
                Exit Sub
            End If
        End If

        ' Open the form to edit the item.
        AddChild(New SubformMiscellaneousCharge(NewItem, OldDataObject))

    End Sub
    Private Sub OpenMediaCostDetailSubform(ByVal NewItem As Boolean)
        'I think we need to create the row here if it doesn't exist


        If NewItem = False Then
            ' User clicked the Edit button.
            Dim ParentForm As BaseForm = CType(TopLevelControl, BaseForm)
            If GridMediaCosts.SelectedRows.Count < 1 Then
                ' Stop because no rows are selected.
                ParentForm.ShowMessage("Please select the row to edit before using the 'Edit' button.")
                Exit Sub
            ElseIf GridMediaCosts.SelectedRows.Count > 1 Then
                ' Stop because more than one row is selected.
                ParentForm.ShowMessage("Please select only one row to edit before using the 'Edit' button.")
                Exit Sub
            End If
        End If

        ' Open the form to edit the item.
        AddChild(New SubformContractMediaCost(GridMediaCosts, NewItem))


    End Sub
    Private Sub OpenInvoiceNumberDetailSubform(ByVal NewItem As Boolean, ByVal ContractNumber As String)

        If NewItem = False Then
            ' User clicked the Edit button.
            Dim ParentForm As BaseForm = CType(TopLevelControl, BaseForm)
            If GridInvoiceNumbers.SelectedRows.Count < 1 Then
                ' Stop because no rows are selected.
                ParentForm.ShowMessage("Please select the row to edit before using the 'Edit' button.")
                Exit Sub
            ElseIf GridInvoiceNumbers.SelectedRows.Count > 1 Then
                ' Stop because more than one row is selected.
                ParentForm.ShowMessage("Please select only one row to edit before using the 'Edit' button.")
                Exit Sub
            End If
        End If

        ' Open the form to edit the item.
        AddChild(New SubformInvoiceNumber(GridInvoiceNumbers, NewItem, ContractNumber))


    End Sub
    Private Sub OpenCostEstimateDetailSubform(ByVal NewItem As Boolean, ByVal ContractNumber As String)

        If NewItem = False Then
            ' User clicked the Edit button.
            Dim ParentForm As BaseForm = CType(TopLevelControl, BaseForm)
            If GridMediaCosts.SelectedRows.Count < 1 Then
                ' Stop because no rows are selected.
                ParentForm.ShowMessage("Please select the row to edit before using the 'Edit' button.")
                Exit Sub
            ElseIf GridMediaCosts.SelectedRows.Count > 1 Then
                ' Stop because more than one row is selected.
                ParentForm.ShowMessage("Please select only one row to edit before using the 'Edit' button.")
                Exit Sub
            End If
        End If

        ' Open the form to edit the item.
        AddChild(New SubformCostEstimate(GridCostEstimates, NewItem, ContractNumber))


    End Sub

    Private Sub DisableControlsAfterSignature()

        ' Prevent changes to the contract if it has already been signed.
        If OldDataObject.Signed Then
            TextEditCommission.Enabled = False
            CheckEditApplyAgencyComm.Enabled = False
        End If

    End Sub

    Private Sub CalculateMediaCostTotals()
        Dim TotalMediaCostSystemCalculated As Decimal = 0
        Dim TotalMediaCostUserInputted As Decimal = 0
        For Each GridRow As DataGridViewRow In GridMediaCosts.Rows
            'we need to calc both amount
            TotalMediaCostSystemCalculated += GridRow.Cells("MediaCostSystemCalculatedColumn").Value
            TotalMediaCostUserInputted += GridRow.Cells("MediaCostUserInputedCostColumn").Value
        Next
        LabelTotalMediaCostSystemCalculated.Text = TotalMediaCostSystemCalculated.ToString("C2", New CultureInfo("en-ZA"))
        LabelTotalMediaCostUserInputted.Text = TotalMediaCostUserInputted.ToString("C2", New CultureInfo("en-ZA"))
    End Sub

    Private Sub CalculateCETotals()
        Dim TotalCETotals As Decimal = 0

        For Each GridRow As DataGridViewRow In GridCostEstimates.Rows
            'we need to calc both amount
            TotalCETotals += GridRow.Cells("ColumnCostEstimateAmount").Value

        Next
        LabelTotalCEAmount.Text = TotalCETotals.ToString("C2", New CultureInfo("en-ZA"))
    End Sub

    Private Sub CalculateInvoiceTotals()
        Dim TotalInvTotals As Decimal = 0

        For Each GridRow As DataGridViewRow In GridInvoiceNumbers.Rows
            'we need to calc both amount
            TotalInvTotals += GridRow.Cells("ColumnInvoiceAmount").Value

        Next
        LabelTotalInvoiceAmount.Text = TotalInvTotals.ToString("C2", New CultureInfo("en-ZA"))

    End Sub




#End Region

End Class
