﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="ImageList24x24.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <data name="ImageList24x24.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        ********************************************************************************
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAABo
        FAAAAk1TRnQBSQFMAgEBAwEAASgBAQEoAQEBGAEAARgBAAT/AREBAAj/AUIBTQE2BwABNgMAASgDAAFg
        AwABGAMAAQEBAAEQBgABEiIAAf8BfwG9AXcBewFvAXsBbwF7AW8BewFvAZwBcwH/AX8UAAH/AX8BfAFv
        AZwBcwHeAXsqAAH/AX8BnQFzAXsBbwGcAXMB/wF/EAAB/wF/AZwBcwF7AW8BnQFzAf8Bf0QAAf8BfwF7
        AW8BvwF7Ad8BfwHfAX8B3wF/Ad8BfwG/AXsBWwFrAf8BfxAAAf8BfwF7AW8B3gF7Af8BfwHeAXcBWgFr
        Ad4BewH/AX8iAAH/AX8BewFvAf8BewH/AXsB/wF7AZwBcwH/AX8MAAH/AX8BnAFzAf8BewH/AXsB/wJ7
        AW8B/wF/QgAB3gF7Ab8BewFYAWcBagE+AYoBPgGKAT4BaQE6ATUBXwHfAXsBnAFzEAABvQF3Ad4BewG1
        AVYBjAE1AVoBawH/AX8B/wF/AVoBawHeAXsB/wF/HAAB/wF/AXsBbwH/AXsBGAFrAcYBQAHuAVUB/wF7
        AZwBcwH/AX8IAAH/AX8BnAFvAf8BewHuAVUBxgFAARgBawH/AnsBbwH/AX9AAAF7AW8B3gF7ASQBLgHg
        ASEBAgEqAQIBKgEBASYBAQEmAZwBcwF7AW8QAAG9AXcB/wF/AbUBVgEhAQgBYwEQASgBJQGUAVIB3wF7
        Ab4BcwF7AW8BvQF3Af8BfxYAAf8BfwF7AW8B/wF7AdYBZgEAATQBAAE0AQABNAFqAU0B/wF7AZ0BcwH/
        AX8EAAH/AX8BnQFzAf8BewFqAU0BAAE0AQABNAEAATQB1gFmAf8CewFvAf8Bfz4AAXsBbwG+AXcBRAEu
        AUQBLgFFATIBRQEyAUQBLgEiASoBmwFvAXwBbxAAAf8BfwGcAXMBnAFzAQgBIQGmARgBQgEIASwBVgF1
        AX8BeQF3Ab8BdwG/AXcBewFvAf8BfxQAAb0BdwH/AXsB9wFqAQABOAFBATwBYwFAAWMBQAEAATgBjAFR
        Af8BfwGcAXMB/wF/Af8BfwGcAXMB/wF/AYwBUQEAATgBYwFAAWMBQAFBATwBAAE4AfcBagH/AXsBvQF3
        PgABewFvAb4BdwFEATIBRAEyAWQBMgFkATIBRAEyAUIBKgGbAW8BnAFzEAAB/wF/AXwBbwHfAXsBjAEx
        AUoBLQE1AXcBtwF/ATQBfwEUAXsBEAF7AZYBdwHfAXcBnAFzFAABfAFvAf8BewFjAUABIAE8AYMBQAFj
        AUABYwFAAWIBQAEAATgBjAFVAf8BewF8AW8BfAFvAf8BfwGsAVUBAAE4AWIBQAFjAUABYwFAAYMBQAEg
        ATwBYwFAAf8BewGcAW8+AAF8AW8BvgF7AWQBMgFjATIBZAE2AWQBNgFkATIBQgEuAZsBcwF8AXMSAAG9
        AXcB3wF7AfYBYgGYAX8BmAF/AVYBfwFYAXsBMAF7AeABfgHgAX4BugF3Ad8BdwHeAXsSAAGcAXMB/wF7
        AeYBTAEAAUABgwFEAWMBRAFjAUQBYwFEAWIBRAEAATwBawFVAf8BfwH/AX8BawFVAQABPAFiAUQBYwFE
        AWMBRAFjAUQBgwFEAQABQAHmAUwB/wF7AZwBczIAAf8BfwHeAnsBbwF7AW8BewFvAXsBbwF7AW8B3gF7
        AYQBNgGDATIBhAE2AYQBNgGEATYBYgEuAbsBcwGdAXMBewFvAXsBbwF7AW8BewFvAb0BdwH/AX8GAAH/
        AX8BvgF3AdwBfwGZAX8BeAF/AXoBfwFPAX8BAAF/AQABfwHgAX4BAAF/Ad8BdwG+AXcB/wF/EAAB/wF/
        Af8BewG9AXcBYgFEASABRAGDAUgBYwFIAWMBSAFjAUgBYwFIAQABQAHvAV0B7wFdAQABQAFiAUgBYwFI
        AWMBSAFjAUgBgwFIASABRAFiAUQBvQF3Af8BewH/AX8wAAH/AX8BewFvAd8BfwHfAX8B3wF/Ad8BfwHf
        AX8B3wF/Ad8BfwGlAToBgwE2AaQBNgGkATYBhAE2AYIBMgG+AXcB/wF/Ad8BfwHfAX8B3wF/Ad8BfwHf
        AX8BewFvAf8BfwQAAf8BfwG9AXMBvQF7AZoBfwGaAX8BcQF/ASIBfwEAAX8BAAF/AQABfwHgAX4BJgF7
        Ad8BdwF8AW8B/wF/EAAB3gF7Af8BewGcAXcBgwFIASABRAGDAUgBgwFIAYMBSAGDAUgBYwFIASABRAEg
        AUQBYwFIAYMBSAGDAUgBgwFIAYMBSAEgAUQBgwFIAZwBdwH/AXsB3gF7MgABvQF3Ad8BfwGYAWsBxwE+
        AccBPgHHAUIBxwFCAcgBQgHIAUIBpAE6AaQBOgGkAToBpAE6AaQBOgGkATYByAFCAcgBQgHHAUIBxwFC
        AccBQgHGAT4BdQFjAd8BfwG9AXcGAAGcAXMB3gF7AbsBfwGSAX8BawF/AUkBfwElAX8BAAF/AQABfwEA
        AX8B4AF+AU0BewH/AXcBewFvAf8BfxAAAd4BewH/AX8B3gF3AYMBTAEgAUgBgwFMAYMBTAGDAUwBgwFM
        AYMBTAGDAUwBgwFMAYMBTAGDAUwBgwFMASABSAGDAUwB3gF3Af8BfwHeAXs0AAGcAXMB3wF/AcYBPgGA
        ATIBowE2AaMBNgGjATYBowE2AaMBNgHEAToBxAE6AcQBOgHEAToBxAE6AcQBOgGjAToBowE2AaMBNgGj
        ATYBowE2AaEBMgGjATYB3wF7AXwBbwYAAZwBcwHfAXcBtAF/AY4BfwGQAX8BbQF/AUgBfwEkAX8BAAF/
        AQABfwEAAX8B4AF+AZUBewHfAXcBnAFzEgAB3gF7Af8BfwG9AXcBYgFMASABSAGDAVABgwFQAYMBUAGD
        AVABgwFQAYMBUAGDAVABgwFQASABSAFiAUwBvQF3Af8BfwHeAXs2AAGcAXMB/wF/AeYBQgHDAToB5QE+
        AeQBPgHkAT4B5AE+AeQBPgHkAT4B5AE+AeQBPgHkAT4B5AE+AeQBPgHkAT4B5AE+AeQBPgHkAT4B5QE+
        AcQBOgHEAToB3gF7AZwBcwYAAf8BfwHfAXsB3gF7AbEBfwGQAX8BkAF/AW0BfwFIAX8BIwF/AQABfwEA
        AX8BAAF/AeABfgG7AXsB3wF7Ab0BdxIAAd4BewH/AXsB3gF7AeYBVAFBAVABgwFQAYMBUAGDAVABgwFQ
        AYMBUAGDAVABQQFQAeYBVAHeAXsB/wF7Ad4BezgAAZwBcwH/AX8BBgFDAeMBOgHkAT4B5AE+AeQBPgHk
        AT4B5AE+AeQBPgHkAT4B5AE+AeQBPgHkAT4B5AE+AeQBPgHkAT4B5AE+AeQBPgHkAT4B5AE+AeQBPgHf
        AXsBnAFzCAAB3gF7Ad8BewHcAXsBrwF/AZABfwGPAX8BbAF/AUcBfwEjAX8BAAF/AQABfwHgAX4BAQF7
        Af8BewHfAXsB/wF/EAAB/wF/Ab0BcwH/AX8BzgFhASABUAGDAVQBgwFUAYMBVAGDAVQBgwFUAYMBVAEg
        AVABzgFhAf8BfwG9AXMB/wF/OAABnAFzAf8BfwEFAUMB4gE+AQQBQwEEAUMBBAFDAQQBQwEEAUMBBAFD
        AQQBQwEEAUMBBAFDAQQBQwEEAUMBBAFDAQQBQwEEAUMBBAFDAQQBQwHjAT4B4gE+Ad4BewGcAXMKAAG9
        AXcB3wF7AdoBfwGvAX8BkAF/AY8BfwFrAX8BRwF/ASMBfwEAAX8BAAF/AQABfwEoAXcB/wF7AZwBcwH/
        AX8MAAH/AX8BvQFzAf8BfwGtAWEBAAFQAWMBVAGDAVQBgwFUAYMBVAGDAVQBgwFUAYMBVAFjAVQBAAFQ
        Aa0BYQH/AX8BvQFzAf8BfzYAAb0BdwH/AX8BTgFXAcABNgHgAToB4AE6AeABOgHgAToB4QE6AeMBQgEE
        AUMBBAFDAQQBQwEEAUMBAwFDAeEBPgHgAToB4AE6AeABOgHgAToBwAE2ASkBTwH/AX8BnAFzDAABvQF3
        Ad8BewHXAX8BjwF/AZABfwGPAX8BawF/AUcBfwEiAX8BAAF/AQcBcwHXAVIBtQFiAf8BfwF7AW8B/wF/
        CAAB/wF/Ad4BdwH/AX8BzQFlAQABUAGDAVgBgwFYAYMBWAGDAVgBgwFYAYMBWAGDAVgBgwFYAYMBWAGD
        AVgBAAFQAc0BZQH/AX8BvgF3Af8BfzQAAf8BfwHeAXsB/wF/AbkBbwG4AW8BuAFvAbgBbwG4AW8BuAFv
        AQQBQwEDAUMBBAFDAQQBQwEDAUMB4gE+AZYBawG5AXMBuAFvAbgBbwG4AW8BuAFvAf8BfwH/AX8B/wF/
        DgABvgF3Af8BewGzAX8BkAF/AZABfwGPAX8BagF/AUMBfwEtAW8BPQFbAc4BVQEAAUgBtQFuAf8BfwGc
        AXMGAAH/AX8BvQFzAf8BfwHOAWkBAAFUAYMBWAGDAVwBgwFcAYMBXAGDAVwBYgFYAWIBWAGDAVwBgwFc
        AYMBXAGDAVwBgwFYAQABVAHOAWkB/wF/Ab0BcwH/AX80AAH/AX8BvQF3Ad4BewHeAXsB3gF7Ad4BewH/
        AX8B/wF/AQMBRwECAUMBAwFHAQMBRwEDAUcB4QE+Af4BfwH/AX8B3gF7Ad4BewHeAXsB3gF7Ab0BdwH/
        AX8QAAH/AX8B3wF7Af8BfwGyAX8BkAF/AY8BfwGNAX8BdwFvATwBXwFoAVkBgAFUAaIBUAEgAUgBnAF3
        Ad4BewHeAXsCAAH/AX8BvQF3Af8BfwGsAWkBAAFYAYMBXAGDAVwBgwFcAYMBXAGDAVwBIAFcAcYBYAHm
        AWABIAFcAYMBXAGDAVwBgwFcAYMBXAGDAVwBAAFYAawBaQH/AX8BvQF3Af8Bfz4AAZwBcwH/AX8BAwFH
        AQIBRwEDAUcBAwFHAQMBRwHgAUIB3QF7AZwBcx4AAd4BewH/AX8B/QF/Aa8BfwGxAX8BnAFrARoBYwFE
        AWEB4AFcAQMBWQHjAVQBIAFIAQ8BYgH/AX8BvQF3AgABnAFzAf8BfwEPAW4BAAFYAYMBYAGDAWABgwFg
        AYMBYAGDAWABIAFcAYMBYAH/AX8B/wF/AWIBYAEgAVwBgwFgAYMBYAGDAWABgwFgAYMBYAEAAVgBDwFu
        Af8BfwGcAXM+AAGcAXMB/wF/AQMBRwECAUcBAwFLAQMBSwECAUcBAAFDAd0BewGdAXcgAAG9AXcB/wF/
        AdoBfwF8AWsB1gFmAWEBaQEhAWUBQwFhASMBXQHjAVgBAAFIAZMBagH/AX8BvQF3AgABnAFzAf8BfwFB
        AWABQQFgAYMBYAGDAWABgwFgAYMBYAEgAWABgwFgAb0BewH/AX8B/wF/Ab0BewGDAWABIAFgAYMBYAGD
        AWABgwFgAYMBYAFBAWABQQFgAf8BfwGcAXM+AAGcAXMB/wF/AQIBSwEBAUcBIgFLASIBSwECAUsBAAFH
        Af0BewG9AXciAAG9AXcB/wF/ATYBcwEgAW0BgQFtAYMBaQFDAWUBIgFdAYABVAGqAWEB/wF/Ab0BdwH/
        AX8CAAG9AXcB/wF/Ae8BbQEAAWABgwFkAYMBZAGDAWQBIAFgAYMBZAHeAX8B/wF/Ad4BewHeAXsB/wF/
        Ad4BfwGDAWQBIAFgAYMBZAGDAWQBgwFkAQABYAEPAW4B/wF/Ab0Bdz4AAZwBcwH/AX8BAAFHAQABRwEi
        AUsBIgFLASEBSwEAAUMB/AF7Ab0BdyIAAf8BfwHeAXsB/wF/AQYBcgFgAW0BggFpASEBYQGgAVgBUAFq
        Af8BfwH/AX8B/wF/BAAB/wF/Ad4BewH/AX8BjAFtAQABZAFiAWQBIAFkAWIBZAHeAX8B/wF/Ad4BewQA
        Ad4BewH/AX8BvQF/AWEBZAEgAWQBYgFkAQABZAGMAW0B/wF/Ad4BewH/AX8+AAG9AXcB/wF/AWwBXwEA
        AUMBAAFHAQABRwEAAUMBRwFXAf8BfwGcAXMkAAH/AX8B/wF/Af8BfwHmAW0BAAFhASEBYQE3AXcB/wF/
        Ab0BdwH/AX8IAAH/AX8B3gF7Af8BfwHOAXEBAAFkAYQBaAG9AX8B/wF/Ad4BewgAAd4BewH/AX8BvQF/
        AaQBaAEAAWQBzgFxAf8BfwHeAXsB/wF/QAAB/wF/Af8BfwH/AX8B/QF7AfwBewH8AXsB/AF7Af8BfwH/
        AX8B/wF/JgAB3gF7Af8BfwH/AX8BvAF7Af8BfwH/AX8BvQF3DgAB/wF/Ad4BewH/AX8BvQF/Af8BfwH/
        AX8B3gF7DAAB3gF7Af8BfwH/AX8BvQF/Af8BfwHeAXsB/wF/RAAB/wF/Ab0BdwG9AXcBvQF3Ab0BdwG9
        AXcBvQF3Af8BfyoAAf8BfwG9AXcBvQF3Ab0BdwHeAXsUAAG9AXcBvQF3Ab0BdwHeAXsQAAHeAXsBvQF3
        Ab0BdwG9AXc4AAFCAU0BPgcAAT4DAAEoAwABYAMAARgDAAEBAQABAQUAASABARYAA/8BAAH/AQAB/wHD
        Av8B4AH/AQcDAAH+AQABfwGAAX8B/wHAAX4BAwMAAf4BAAF/AYABHwH/AYABPAEBAwAB/gEAAX8BgAEH
        Af8BAAEYBAAB/gEAAX8BgAEDAf8GAAH+AQABfwGAAQMB/wYAAf4BAAF/AcABAQH/BgABgAEAAQEBwAEA
        Af8JAAHAAQABfwGAAQABAQYAAeABAAE/AcABAAEDBgAB4AEAAT8B4AEAAQcGAAHgAQABHwHwAQABDwYA
        AfABAAEPAfABAAEPBgAB+AEAAQcB4AEAAQcGAAH8AQABAwHAAQABAwYAAf4BAAEDAYABAAEBAwABgAEA
        AQEB/gEAAQEGAAH+AQABfwH/AQABAQYAAf4BAAF/Af8BgAEBBgAB/gEAAX8B/wHAAQEGAAH+AQABfwH/
        AcABAwEAARgEAAH+AQABfwH/AeABBwGAATwBAQMAAf4BAAF/Af8B8AEfAcABfgEDAwAB/wEAAv8B+AE/
        AfAB/wEPAwAL
</value>
  </data>
  <metadata name="ToolTipControllerDefault.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>289, 17</value>
  </metadata>
  <metadata name="ImageList16x16.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>153, 17</value>
  </metadata>
  <data name="ImageList16x16.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        ********************************************************************************
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAACM
        GgAAAk1TRnQBSQFMAgEBCAEAAUwBBQFMAQUBEAEAARABAAT/AREBAAj/AUIBTQE2BwABNgMAASgDAAFA
        AwABMAMAAQEBAAEQBgABGP8A/wD/AP8A/wD/AP8A/wAgAAH/AX8BnAFzAXsBcwF7AXMBewFzAXsBcwF7
        AXMBewFzAZwBcwH/AX8OAAH/AX8B3gF7AZwBcwGcAW8BnAFvAZwBcwHeAXsB/wF/FgAB/wF/AXsBbwGc
        AXMQAAH/AX8BewFvAXsBbwF7AW8BewFvAXsBbwF7AW8BewFvAb0BdxAAAZwBcwF8AWcBeAE6AZgBOgGY
        AToBmAE6AZgBOgFXAToBWwFjAZwBcwwAAf8BfwGcAW8B3gF3AdYBZgEwAVoBEAFaAbUBYgG9AXcBnAFz
        Ad4BexIAAf8BfwF7AXMBewFfAZwBbwF7AW8B/wF/CgAB/wF/AXsBbwFaAWsB9wFeAfcBXgH3AV4B9wFe
        AfcBXgH3AV4BvQF3Ab0BdwwAAf8BfwGbAXMBeAEyAbEBAAHyAQAB8gEAAfIBAAHRAQABkAEAARYBIgGc
        AXMB/wF/CAABvQF3Af8BewFRAV4BYgE8AQABNAEAATQBAAE0AQABNAFBATwB7wFZAf8BewG9AXMMAAH/
        AX8BewFzAXsBdwHeAX8BOgFDAbcBEgGdAXMBewFzAf8BfwgAAb0BdwG9AXcBcwFOAXIBTgFzAU4BcwFO
        AXMBTgFzAU4BUQFKAZQBUgHeAXsBnAFzBgAB/wF/AXsBbwG8AXcB/gF/AZkBNgGVAQkBFwEeARcBGgH2
        ARkB1gEVAXQBBQE3ASYB/wF/Ab0BdwF7AW8B/wF/AgAB/wF/Af8BewFrAVEBAAE4ASABPAEAATwBAAE8
        ASABPAFBAUABQQFAAQABOAEoAU0B/wF7Ad4BewgAAd4BewG9AXsBnAFnARkBOwHYASYB2AEeAbcBFgG3
        ARoBnAFrAXsBcwH/AX8GAAG9AXMBvQF3AbUBVgHWAVoB1gFaAdYBWgHWAVoB1gFeAbUBVgHvAT0BtQFW
        Ad4BewG9AXcEAAGcAXMBfAFnAdYBEQE4ASYBuQE+ATsBVwE7AV8BOwFbAXwBZwH/AX8BvQF3AVgBLgFX
        AS4BlAERATsBXwGcAXMB/wF/Ab0BcwEwAV4BAAE8ASEBQAFBAUQBMAFeAVoBcwG+AXcBKQFRAUEBQAFj
        AUQBAAE8Ac0BWQHeAXcB/wF/BAAB/wF/Ad4BfwFaAUsBtwESAbcBFgG4ARoB2AEeAdgBHgG3ARoBtwEO
        AZwBawG9AXcGAAG9AXcBvQF3AfcBXgH3AWIB9wFiAfcBYgH3AWIBGAFjARgBYwFzAU4BMQFKAdYBWgHe
        AXsBvQF3AgABnAFzAVgBKgEUAQEB1wEJAdoBQgHaAUIBeQEuAXkBKgF4AS4BeQE2AdoBRgGZATYB1gEV
        AbEBAAHVARkBnAFzAd4BewHeAXcBQQFEASEBRAFBAUgBewFzAb0BdwH/AX8BcwFmASEBRAFiAUgBYgFI
        AWIBSAEAAUQBewFzAb0BdwQAAZwBcwF7AVcBlgEKAbcBEgG3ARYBtwEWAbcBGgHYASIBtwEWAdgBIgG9
        AXMB3gF7BgABvQF3Ab0BdwEYAWMBGAFjARgBYwEYAWMBGAFjARgBYwEYAWcBOQFnATkBZwH3AWIBvQF3
        AZ0BcwIAAZwBcwF5AS4BdQEBAdcBBQE8AVcBnQFvAXwBawF8AWcBfQFnAXwBZwGdAW8BOwFbAbUBDQES
        AQEBFgEiAZwBcwGcAXMBlAFqAQABRAEAAUgBcgFmAZwBcwGcAXMBMQFmAQABRAFiAUwBIAFIAaQBUAFj
        AUwBAAFEATABYgGcAXMCAAHeAXsB3gF7AbcBFgH5ASoBOgFHAVsBUwFbAVMB+QEuAZcBEgH5AS4B3gF/
        Ab0BdwH/AX8GAAG9AXcB3gF7ARgBZwE5AWcBOQFnATkBZwE5AWcBOQFnATkBZwE5AWcBOQFnARgBZwG9
        AXcBvQF3AgABnAFzAZoBLgGWAQEBtwEBATgBEgG6ATYBugE2AboBNgG6ATYBmgE2AboBNgEXARoBVAEB
        ATMBAQEWASIBnAFzAZwBcwHOAWEBAAFIAUEBTAG9AXsB/wF/ATEBZgEAAUgBYgFQAQABTAHmAVQB/wF7
        AYMBUAEAAUwBagFdAZwBcwIAAZwBdwGcAWMBWwFPAd0BewGcAXMB/wF/Af8BfwFbAU8B+QEuAf4BfwHe
        AnsBbwG9AXcB3gF7BAABvQF3Ad4BewE5AWcBWgFrAVoBawFaAWsBWgFrAVoBawFaAWsBWgFrAVoBawE5
        AWcB3gF3Ab0BdwIAAZwBcwGaAS4BtwEBAfgBAQHYAQEBtwEBAZcBAQGXAQEBlgEBAXUBAQF1AQEBdQEB
        AZUBAQE0AQEBFwEiAZwBcwGcAXMBzQFhAQABUAFCAVAB/wF/AXMBagEAAUwBYgFUAQABUAHGAVgB/wF/
        Af8BfwGDAVQBAAFQAWoBXQGcAXMCAAHeAXsB3gF7Ab0BewHeAXsBvQF3AVsBSwF8AVcB/gF/Af8BfwF7
        AW8BewFvAZ0BZwGdAWcBvQF3BAABvQF3Ad4BewFaAWsBWgFrAVoBawFaAWsBWgFrAVoBawFaAWsBWgFr
        AVoBawFaAWsB3gF7Ab0BdwIAAZwBcwG6AS4BuAEBAfkBAQEZAQIB+AEBAfgBAQH4AQEB1wEBAdcBAQG2
        AQEBtgEBAZUBAQEzAQEBFwEiAZwBcwGcAXMBUgFqAQABUAFiAVgBjAFlASABVAFiAVgBAAFUAeYBXAH/
        AXsBnAFzAXsBdwEgAVQBAAFUAc4BZQGcAXMGAAG9AXcB/wF/AVsBUwHYASIBWwFLAd4BdwHeAXMBvQFr
        AXwBVwH5AS4B3gF3Ad4BewQAAb0BdwHeAXsBWgFrAXsBbwF7AW8BewFvAXsBbwF7AW8BewFvAXsBbwF7
        AW8BWgFrAd4BewG9AXcCAAGcAXMBuwEqAZgBAQEZAQIBGQECAfkBAQH4AQEB2AEBAbcBAQG3AQEBtgEB
        AZYBAQH3AQ0B9gEVAdYBEQGcAXMB3gF7AZwBewEAAVgBYgFcAUEBWAGDAVwBIAFYAeYBYAH/AX8BnAFz
        Ad4BewEpAWEBIAFYAQABWAE5AXcBvQF3BAAB/wF/Af8BfwFbAU8B+QEyARoBOwEaATcBGQEzARkBNwEZ
        ATMB2AEqAXwBVwG9AXcGAAG9AXcB/wJ7AW8BewFvAXsBbwF7AW8BewFvAXsBbwF7AW8BewFvAXsBbwF7
        AW8B3gF7Ab0BdwIAAb0BdwGeAWcBWgESARoBAgE6AQIBWgESAVoBEgFZARIBOQESATkBEgEYARIBtgEB
        AZUBAQH3ARkBPAFbAZwBcwH/AX8B/wF/AWsBaQEAAVwBgwFgAWIBXAHmAWAB/wF/Af8BfwG9AXsBSgFl
        ASABXAEAAVwBBwFlAf8BfwH/AX8EAAG9AXcBvgFzAfkBMgE6AUMBOwFDATsBQwE6AUMBOgFDARoBOwFb
        AU8B/wF/Af8BfwYAAb0BdwH/AX8BewFvAZwBcwGcAXMBnAFzAZwBcwGcAXMBnAFzAZwBcwGcAXMBewFv
        Af8BfwG9AXcEAAG9AXcB/wF/AX0BVwHcASoB/wF/Af8BfwH/AX8B/wF/Af8BfwH/AX8BeQEmARwBTwH/
        AX8BvQF3Af8BfwIAAd4BewH/AX8BpAFkAQABYAFjAWABgwFkAeYBZAHFAWQBIQFgASABYAEgAWABYgFg
        Ad4BfwG9AXcGAAH/AX8B/wF/Ab0BawE6AUMBOwFLATsBRwE7AUcBWwFLAZ0BZwH/AX8B3gF7CAABvQF3
        Af8BfwGcAXMBnAFzAZwBcwGcAXMBnAFzAZwBcwGcAXMBnAFzAZwBcwGcAXMB/wF/Ab0BdwYAAf8BfwHe
        AXsB/QEyARoBAgF7ARYBWgESAVkBEgE5ARYB1wEFAVkBIgH/AX8B/wF/CAABvQF3Af8BfwEoAW0BAAFk
        AQABZAEAAWQBAAFkAQABZAEAAWQB5gFoAb0BfwG9AXcKAAH/AX8B3gF7Ad4BcwE7AUcBnQFjAf8BfwH/
        AX8BvQF3Af8BfwoAAb0BdwH/AX8BnAFzAZwBdwGcAXcBnAF3AZwBdwGcAXcBnAF3AZwBdwGcAXcBnAFz
        Af8BfwG9AXcIAAG9AXcBfgFbAToBCgE6AQoBOQEKARkBCgEYAQoB1wEFATwBTwGcAXMMAAG9AXcB/wF/
        ATkBewGtAXEBBwFtAQcBbQGsAXEBFwF7Af8BfwG9AXcOAAH/AX8BvQF3Ad4BcwHeAW8BnAFzEAAB3gF7
        Af8BfwHeAXsBvQF7Ab0BewG9AXsBvQF7Ab0BewG9AXsBvQF7Ab0BewHeAXsB/wF/Ad4BewgAAf8BfwG9
        AXcB3gF7Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3Af8Bfw4AAf8BfwG9AXcBvQF3Ab0BdwG9AXcBvQF3
        Ab0BdwH/AX8UAAG9AXcB3gF7Af8BfxIAAf8BfwG9AXcBvQF3Ab0BdwG9AXcBvQF3Ab0BdwG9AXcBvQF3
        Ab0BdwG9AXcB/wF/DgABOQFnARkBYwE6AWcBOgFnATkBZwEYAWMKAAFaAWsBGAFjATkBZwEYAWMBGAFj
        GgABGAFjAVoBZwEYAWMBOQFnCAABOQFnARgBYwFaAWcBGAFjEAAB/wF/Af8BfwH/AX8WAAFaAWsBWwFv
        ATYBYwHQAU4B0AFOATUBXwGcAXMBOQFnCAABGAFjAXsBbwG1AVYBGAFjAd4BewEYAWMBGAFjFAAB9wFe
        Af8BewEQAVYB1gFmAZwBbwE5AWcEAAE5AWcBnAFvAdYBZgEQAVYB/wF7AfcBXgwAAb0BdwGdAXMBvgF3
        AXwBbxYAATkBZwG+AXcBIwEqAeABIQEAASIBAQEmAZwBcwEYAWMIAAEYAWMBnAFzAe4BPQIAAQgBIQF6
        AXMB3gF3AToBYwEYAWMOAAEYAV8B/wF7AWsBTQEAATABAAE0AdYBZgGcAW8BOQFnATkBZwGcAW8B1gFm
        AQABNAEAATABawFNAf8BewEYAV8IAAG9AXcB3wF/Aa0BRgFHATYBvQF3Ab0BdxQAATkBZwG+AXcBRAEy
        AUQBMgFEATIBQwEuAbwBcwE5AWcIAAFaAWsBOQFnARgBYwHnARwBkAFeAVQBfwE1AXsBlwF3AX0BawE5
        AWcMAAF7AWsBzgFZAQABNAFjAUABQQFAAQABPAH3AWoBewFvAXsBbwH3AWoBAAE8AUEBQAFjAUABAAE0
        Ac4BWQF7AWsGAAHeAXsB3wF/AcwBRgEAASIBAAEiAcwBRgHeAXsB/wF/DAABWgFrARgBYwEYAWMB9wFe
        Ad4BewFkATYBYwEyAWQBMgFjATIBvAF3AfcBXgEYAWMBGAFjAVoBawQAATkBZwG9AXMBdwF7AboBfwFY
        AX8BBgF/AeABfgGYAXcBOgFnDAABewFrATEBYgEAATwBYgFEAWMBRAFBAUQBAAFAATkBbwE5AW8BAAFA
        AUEBRAFjAUQBYgFEAQABPAExAWIBewFrBAAB3gF7Af8BfwHNAUoBIAEqAWQBMgFkATIBQQEqAXkBawGc
        AXMKAAE5AWcBWwFrAd8BewHfAXsB3wF/Ad8BfwGlAToBgwE2AYQBNgGDATYBvgF7Ad8BfwHfAXsB3wF7
        AXwBbwEYAWMCAAFaAWsB3gF3AbwBfwGZAX8BSAF/AQABfwEAAX8BAAF/Ab0BdwEYAWMKAAE5AWcB/wF7
        ARABYgEAAUABYgFIAYMBSAFCAUgBYgFIAWIBSAFCAUgBgwFIAWIBSAEAAUABEAFiAf8BewE5AWcCAAHe
        AXsB/wF/AQ4BTwFgASoBhAE2AYQBNgGEATYBYgEyAacBOgHfAX8B/wF/CAABOQFnAXYBZwGlAToBxgE+
        AccBPgHHAUIBpAE6AaQBOgGkAToBpAE2AccBPgHHAT4BxgE+AaQBOgFUAV8BWgFrBAABnAFvAboBfwGP
        AX8BagF/ASQBfwEAAX8B4AF+ASMBewHfAXcB+AFeCgABOQFnAf8BfwEwAWIBAAFEAWIBTAGDAUwBYgFM
        AWIBTAGDAUwBYgFMAQABRAEwAWIB/wF/ATkBZwIAAf8BfwH/AX8BLgFTAYABLgGkAToBpAE6AaMBNgGk
        ATYBpAE6AYABLgFSAVsBnQF3Af8BfwYAAXsBbwEtAVMBoAEyAcMBOgHDAToBwwE6AcQBPgHEAT4BxAE+
        AcQBPgHDAToBwwE6AcMBOgGgATIBCgFLAXsBbwQAAVoBZwHcAXsBjwF/AY8BfwFrAX8BIwF7AQABfwHg
        AX4BSgF7Ad8BdwEYAWMKAAE5AWcBvQF3AVIBZgFBAUwBYwFQAYMBUAGDAVABYwFQAUEBTAFSAWYB3gF7
        ATkBZwQAAZwBcwFQAVsBoAEuAcQBOgHEAT4BoQE2ASwBTwHEAToBxAE6AcQBOgGiATYBvQF3AZwBcwYA
        AXsBbwFNAVMBwQE2AeQBPgHkAT4B5AE+AeQBPgHkAT4B5AE+AeQBPgHkAT4B5AE+AeQBPgHCAToBKwFP
        AXsBbwYAAXwBbwHaAXsBrwF/AY8BfwFKAX8BIwF7AQABfwHgAX4BjwF/Ab0BcwE5AWcIAAE5AWcBewFv
        AVoBdwFBAVABYwFUAYMBVAGDAVQBYwFUAUEBUAFaAXcBewFvATkBZwQAAZwBcwFyAV8BwAEyAeQBPgHA
        ATYBTgFXAf8BfwFyAV8BwAE2AeUBPgHiAToBCQFLAf8BfwH/AX8EAAF7AW8BcQFfAcABNgHhAToB4QE6
        AeIBPgHjAUIBBAFDAQQBQwHjAUIB4gE+AeEBOgHhAToBwAE2AU4BVwF7AW8GAAFaAWsB3gF3AdgBfwGP
        AX8BjwF/AUkBfwEgAX8BAAF/Ae0BYgF9AW8BewFvBgABOQFnAZwBcwE4AXMBIAFUAWIBVAGDAVgBgwFY
        AYMBWAGDAVgBYgFUASABVAE4AXMBnAFzATkBZwIAAf8BfwH/AX8BcAFbAcABMgFMAVMB/wF/Ad4BewH/
        AX8BBgFHAeIBPgEEAUMBwAE6AXEBXwHeAXsB/wF/AgABOQFnAf8BfwHbAXcB2wF3AdwBdwHbAXcBBAFD
        AQMBQwEDAUMBAgFDAboBcwHcAXcB2wF3AbsBdwH/AX8BOQFnCAABOgFnAf8BewG1AX8BjwF/AY0BfwFp
        AX8BVgFnAY4BUQFgAUwBvQF7ATkBYwE5AWcBOQFnAZwBcwE4AXcBIAFYAUEBWAGDAVwBYgFcASABWAEg
        AVgBYgFcAYMBXAFBAVgBIAFYATgBdwGcAXMBOQFnAgAB/wF/Af8BfwG6AXMB/wF/Ad4BewIAAb0BdwHa
        AXMB4AE+AQMBQwEDAUMB4AE6AbcBbwGcAXMB/wF/AgABWgFrAVoBawFaAWsBOQFnAf8BfwEDAUcBAgFH
        AQMBRwECAUMB/wF/ATkBZwFaAWsBWgFrAVoBawwAAToBZwH/AX8BsgF/AbQBewFbAWcBqgFdAaABWAFA
        AUwBagFZAZwBcwH3AV4BOQFnARgBdwEAAVwBQQFcAYMBYAFiAWABAAFcAXMBcgFzAXIBAAFcAWIBYAGD
        AWABQQFcAQABXAEYAXcBOQFnBgAB3gF7Af8BfwYAAd4BewGSAWMB4AE+AQMBRwECAUcB4AE+AbgBbwGc
        AXMIAAE5AWcB/wF/AQMBSwECAUcBAgFHAQEBRwH+AXsBGAFjFAABWgFrAf8BfwEWAWsBhAFlASABZQEi
        AV0BQAFMAQ8BZgGcAXMBGAFjAZwBcwGLAWkBAAFcAYMBZAFjAWABAAFgATEBcgHeAXsB3gF7ATEBcgEA
        AWABgwFgAYMBZAEAAVwBiwFtAXsBbxAAAf8BfwH/AX8BSQFXAQABQwECAUsB4AE+AW0BXwGcAXMIAAE5
        AWcB/wF/AQABRwEAAUcBAAFHAQABQwH+AXsBOQFnFgABnAFzATUBewEgAW0BIAFlAcABWAFPAWoB/wF/
        AVoBawIAATkBZwHeAX8BxQFoAQABZAEAAWQBMAFyAf8BfwE5AWcBOQFnAf8BfwEwAXIBAAFkAQABZAHF
        AWgB3gF/ATkBZxIAAd4BewH/AX8BJQFTAQABQwGRAWcB/wF/Ad4BewgAAVoBawH/AX8BjgFjAUUBUwFF
        AVMBawFfAf8BfwE5AWcWAAFaAWsB3gF7AfMBdgGFAWkBFgF3Ad4BewFaAWsGAAFaAWsB/wF/AQgBbQEw
        AXYB/wF/ATkBZwQAATkBZwH/AX8BMQF2AQgBbQH/AX8BWgFrFgABvQF3Af8BfwH+AX8BvQF3Af8BfwwA
        ATkBZwGcAXMBvQF3Ab0BdwG9AXcBOQFnGgABOQFnAZwBcwG9AXcBewFvAVoBawoAAVoBawG9AXcBnAFz
        ATkBZwgAATkBZwGcAXMBvQF3AVoBaxoAAd4BewG9AXcB/wF/BAABQgFNAT4HAAE+AwABKAMAAUADAAEw
        AwABAQEAAQEFAAGAAQEWAAP/gQAB4AEHAfABDwH+AT8BwAEfAeABBwHgAQcB/AEPAYABDwHAAQMBwAED
        AfABBwGAAQcCAAGAAQEB4AEDAYABAwQAAcABAwGAAQEEAAHAAQMBgAEBBAABgAEDAYABAQQAAYABAQGA
        AQEEAAGAAQEBgAEBBAAB4AEBAYABAQQAAcABAwGAAQEEAAHAAQMBgAEBAYABAAGAAQEBwAEHAYABAQHA
        AQMBwAEDAeABDwGAAQEB4AEHAeABBwHwAX8BgAEBAeABBwHwAQ8B/AF/AcABAwH4AR8BBwH/AsMB/AF/
        AfABDwEBAf8CgQH4AX8B8AEPAQABfwIAAfABPwHwAQ8BAAE/AgAB4AEfAYABAQGAAT8CAAHAAR8CAAGA
        AR8CAAGAAQ8CAAHAAQ8BgAEBAQABBwIAAcABBwHAAQMBAAEHAgAB4AEDAcABAwEAAQMCAAHgAQMBgAEB
        AQABAQIAAfADAAGCAQABgAEBAfgDAAHnAQAB8AEPAfwDAAH/AQAB8AEPAf4BAQIAAf8BgAHwAQ8B/gED
        AoEB/wHBAfgBHwH/AQcCwwH/AeML
</value>
  </data>
</root>