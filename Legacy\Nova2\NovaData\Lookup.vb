Imports System.Text
Imports System.Data.SqlClient

Public Class Lookup

    Public Shared Function GetChains _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT ChainID, ChainName, ChainTypeID FROM Store.Chain ")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Format("WHERE ChainID IN (SELECT ChainID FROM [dbo].[udfChainPermission]('{0}'))", My.User.CurrentPrincipal.Identity.Name), GridToExclude, "ChainID", True, "ChainName"))

        ' Return the data.
        ' NEED TO FIX THIS TO USE CONFIG FILE
        'LIVE
        'Return LiquidShell.LiquidAgent.GetSqlDataBindingSource("Data Source=primenova.primedomain.co.za;Initial Catalog=NovaDB;User ID=NovaApp;Password=******************;Connect Timeout=90", SelectStatement.ToString, ErrorMessage)
        'DEV
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource("Data Source=primenovasql\Live;Initial Catalog=NovaDB;User ID=NovaApp;Password=******************;Connect Timeout=0", SelectStatement.ToString, ErrorMessage)

    End Function


    Public Shared Function GetStores _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView, Optional ChainName As String = "") As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        If ChainName = "" Then
            SelectStatement.Append("SELECT StoreID, StoreName FROM Store.Store where store.storeid not in
                    (select distinct storeid from store.ChainGroupStore)")
        Else
            SelectStatement.Append(String.Format("SELECT        Store.Store.StoreID, Store.Store.StoreName
	                    FROM            Store.Store INNER JOIN
							                     Store.Region ON Store.Store.RegionID = Store.Region.RegionID INNER JOIN
							                     Store.Chain ON Store.Region.ChainID = Store.Chain.ChainID
	                    WHERE        (Store.Chain.Dormant = 0) AND (Store.Store.Dormant = 0) AND (Store.Chain.ChainTypeID = 1) AND (Store.Chain.ChainName = '{0}') AND Store.Chain.ChainID IN (SELECT ChainID FROM [dbo].[udfChainPermission]('{1}'))
	                    ORDER BY Store.Store.StoreName", ChainName, My.User.CurrentPrincipal.Identity.Name))
        End If


        ' Attach a WHERE clause to the select statement if required.
        'SelectStatement.Append(QueryOptions(String.Format("WHERE StoreID IN (SELECT ChainID FROM [dbo].[udfChainPermission]('{0}'))", My.User.CurrentPrincipal.Identity.Name), GridToExclude, "ChainID", True, "ChainName"))

        ' Return the data.
        ' NEED TO FIX THIS TO USE CONFIG FILE
        'LIVE
        'Return LiquidShell.LiquidAgent.GetSqlDataBindingSource("Data Source=primenova.primedomain.co.za;Initial Catalog=NovaDB;User ID=NovaApp;Password=******************;Connect Timeout=90", SelectStatement.ToString, ErrorMessage)
        'DEV
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource("Data Source=primenovasql\Live;Initial Catalog=NovaDB;User ID=NovaApp;Password=******************;Connect Timeout=0", SelectStatement.ToString, ErrorMessage)

    End Function


    Public Shared Function GetTerms _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT TermsID, TermsName FROM Client.Terms ")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "TermsID", False, "TermsName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetBrandFamilies _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT DISTINCT BrandFamilyID, BrandFamilyName ")
        SelectStatement.Append("FROM (SELECT Client.BrandFamily.BrandFamilyID, Client.BrandFamily.BrandFamilyName ")
        SelectStatement.Append("FROM Client.BrandFamily INNER JOIN ")
        SelectStatement.Append("Client.vBrandFamiliesByPermission_EditMyClients ON ")
        SelectStatement.Append("Client.BrandFamily.BrandFamilyID = ")
        SelectStatement.Append("Client.vBrandFamiliesByPermission_EditMyClients.BrandFamilyID) AS dtBrandFamily")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "BrandFamilyID", False, "BrandFamilyName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetBrands _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) As BindingSource

        'Dim SelectStatement As New StringBuilder
        'SelectStatement.Append("SELECT ClientID, ClientName, Agency, AccountManagerName, Dormant, ClientAddress ")
        'SelectStatement.Append("FROM (SELECT Client.Client.ClientID, Client.Client.ClientName, Client.Client.Agency, ISNULL(Client.vCurrentClientAccountManager.AccountManagerName, N'') ")
        'SelectStatement.Append("AS AccountManagerName, Client.Client.Dormant, Client.Client.AddressLine1 + SUBSTRING(', ', 1, LEN(Client.Client.AddressLine1)) ")
        'SelectStatement.Append("+ Client.Client.AddressLine2 + SUBSTRING(', ', 1, LEN(Client.Client.AddressLine2)) ")
        'SelectStatement.Append("+ dbo.City.CityName + N' ' + Client.Client.PostalCode AS ClientAddress ")
        'SelectStatement.Append("FROM Client.Client INNER JOIN ")
        'SelectStatement.Append("dbo.City ON Client.Client.CityID = dbo.City.CityID LEFT OUTER JOIN ")
        'SelectStatement.Append("Client.vCurrentClientAccountManager ON Client.Client.ClientID = Client.vCurrentClientAccountManager.ClientID) AS dtClient")

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT DISTINCT BrandID, BrandName,AccountManagerName,Dormant ")
        SelectStatement.Append("FROM (SELECT DISTINCT Client.Brand.BrandID, Client.Brand.Dormant, BrandName,ISNULL(Client.vCurrentBrandAccountManager.AccountManagerName, N'') AS AccountManagerName  ")
        SelectStatement.Append("FROM Client.Brand ")
        SelectStatement.Append("LEFT OUTER JOIN ")
        SelectStatement.Append("Client.vCurrentBrandAccountManager ON Client.Brand.Brandid = Client.vCurrentBrandAccountManager.BrandID) AS dtBrand")


        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "BrandID", True, "BrandName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetBrandsByClient _
    (ByVal ConnectionString As String,
    ByRef ErrorMessage As String,
    ByVal GridToExclude As DataGridView,
    ByVal ClientID As Integer) _
    As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT BrandID, BrandName ")
        SelectStatement.Append("FROM (SELECT Client.Brand.BrandID, Client.Brand.BrandName, Client.ClientBrand.ClientID, Client.Brand.Dormant ")
        SelectStatement.Append("FROM Client.Brand INNER JOIN ")
        SelectStatement.Append("Client.ClientBrand ON Client.Brand.BrandID = Client.ClientBrand.BrandID) AS dtBrands ")

        ' Attach a WHERE clause to the select statement if required.
        Dim CustomWhereClause As String = "WHERE ClientID = " & ClientID.ToString
        SelectStatement.Append(QueryOptions(CustomWhereClause, GridToExclude, "BrandID", True, "BrandName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetBrandsByPermission_EditMyProvisionalBookings _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT DISTINCT BrandID, BrandName FROM ")
        SelectStatement.Append("(SELECT Client.Brand.BrandID, Client.Brand.BrandName, Client.Brand.Dormant ")
        SelectStatement.Append("FROM Client.vBrandsByPermission_EditMyProvisionalBookings INNER JOIN ")
        SelectStatement.Append("Client.Brand ON Client.vBrandsByPermission_EditMyProvisionalBookings.BrandID = Client.Brand.BrandID) AS dtBrand ")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "BrandID", True, "BrandName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetFiscals _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT FiscalID, FiscalStartDate, FiscalName FROM Sales.Fiscal")

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetUsers _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT principal_id, name FROM sys.sql_logins")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "principal_id", False, "name"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetCities _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT CityID, CityName FROM dbo.City")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "CityID", False, "CityName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetPcaStatusses _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT PcaStatusId, PcaStatusName FROM Media.PcaStatus")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "PcaStatusId", False, "PcaStatusName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetClassifications _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT ClassificationID, ClassificationName FROM Client.Classification")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "ClassificationID", False, "ClassificationName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetCategoriesExcludingTable _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal TableToExclude As DataTable, Optional ByVal isCrossover As Boolean = False) _
    As BindingSource
        Return GetCategoryData(ConnectionString, ErrorMessage, TableToExclude, isCrossover)
    End Function

    Public Shared Function GetCategoriesExcludingGrid _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView, Optional ByVal isCrossover As Boolean = False) _
    As BindingSource
        Return GetCategoryData(ConnectionString, ErrorMessage, GridToExclude, isCrossover)
    End Function

    Private Shared Function GetCategoryData _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal RowsToExclude As Object, Optional ByVal isCrossover As Boolean = False) As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT CategoryID, CategoryName FROM Store.Category")
        Dim CustomWhereClause As String = ""
        If isCrossover Then
            CustomWhereClause = "WHERE crossoverallowed = " & 1
            ' SelectStatement.Append(QueryOptions(CustomWhereClause, Nothing, "CategoryID", True, "CategoryName"))
        End If
        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(CustomWhereClause, RowsToExclude, "CategoryID", True, "CategoryName"))



        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetCategoriesByMediaService _
    (ByVal ConnectionString As String,
    ByRef ErrorMessage As String,
    ByVal GridToExclude As DataGridView,
    ByVal MediaServiceID As Integer) _
    As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT CategoryID, CategoryName ")
        SelectStatement.Append("FROM (SELECT Store.Category.CategoryID, Store.Category.CategoryName, Media.MediaCategory.MediaID, Store.Category.Dormant ")
        SelectStatement.Append("FROM Media.MediaCategory INNER JOIN ")
        SelectStatement.Append("Store.Category ON Media.MediaCategory.CategoryID = Store.Category.CategoryID AND Media.MediaCategory.CategoryID = Store.Category.CategoryID) ")
        SelectStatement.Append("AS dtCategories ")

        ' Attach a WHERE clause to the select statement if required.
        Dim CustomWhereClause As String = "WHERE MediaID = " & MediaServiceID.ToString
        SelectStatement.Append(QueryOptions(CustomWhereClause, GridToExclude, "CategoryID", True, "CategoryName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetMediaFamilies _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT MediaFamilyID, MediaFamilyName FROM Media.MediaFamily")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "MediaFamilyID", True, "MediaFamilyName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetIndependentStoreLists _
        (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal ChainID As Integer) _
        As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT Sales.IndependentStoreList.IndependentStoreListID, Sales.IndependentStoreList.IndependentStoreListName ")
        SelectStatement.Append("FROM Sales.IndependentStoreList INNER JOIN ")
        SelectStatement.Append("Sales.IndependentStoreListMember ON Sales.IndependentStoreList.IndependentStoreListID = Sales.IndependentStoreListMember.IndependentStoreListID INNER JOIN ")
        SelectStatement.Append("Store.Store ON Sales.IndependentStoreListMember.StoreID = Store.Store.StoreID AND Sales.IndependentStoreListMember.StoreID = Store.Store.StoreID INNER JOIN ")
        SelectStatement.Append("Store.Region ON Store.Store.RegionID = Store.Region.RegionID AND Store.Store.RegionID = Store.Region.RegionID ")
        SelectStatement.Append("WHERE (Sales.IndependentStoreList.principal_id IN ")
        SelectStatement.Append("(SELECT principal_id FROM sys.sql_logins WHERE (name = SUSER_SNAME()))) ")
        SelectStatement.Append("GROUP BY Sales.IndependentStoreList.IndependentStoreListID, Sales.IndependentStoreList.IndependentStoreListName ")
        SelectStatement.Append("HAVING (MIN(Store.Region.ChainID) = " & ChainID.ToString & ")")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, Nothing, "IndependentStoreListID", False, "IndependentStoreListName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetLoadingFees _
      (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) _
      As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT LoadingFeeID, LoadingFeeName, DefaultPercentage FROM Sales.LoadingFee")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "LoadingFeeID", True, "LoadingFeeName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetBursts _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal ExcludeBurstID As Guid, ByVal ChainID As Integer) _
    As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT DISTINCT BurstID, ContractID, ContractNumber, MediaName, BrandName, FirstWeek, ")
        SelectStatement.Append("LastInstallWeek AS LastWeek, InstallStoreQty AS Stores, ChainID, ChainName ")
        SelectStatement.Append("FROM Sales.vEffectiveBurst ")
        SelectStatement.Append("WHERE (BrandName <> N'') AND (BurstID <> '" & ExcludeBurstID.ToString & "') ")
        SelectStatement.Append("AND (ChainID = " & ChainID.ToString & ")")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, Nothing, "BurstID", False, "ContractNumber"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetMedia _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) _
    As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT MediaID, MediaName, Homesite, Crossover FROM Media.Media")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "MediaID", False, "MediaName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function


    Public Shared Function GetMediaGroup _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) _
    As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT MediaGroupID, MediaGroupName, Dormant FROM Media.MediaGroup")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "MediaGroupID", False, "MediaGroupName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetMediaThatsStillCurrent _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) _
    As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT MediaID, MediaName, Homesite, Crossover, ISNULL(dbo.udfMediaFamilyIDListByMedia(MediaID), N'') AS MediaFamilyIDList ")
        SelectStatement.Append("FROM Media.vCurrentMediaServices ")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "MediaID", False, "MediaName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetMediaByContract _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView, ByVal ContractNumber As String) _
    As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT MediaID, MediaName, Homesite, Crossover, ISNULL(dbo.udfMediaFamilyIDListByMedia(MediaID), N'') AS MediaFamilyIDList ")
        SelectStatement.Append("FROM Media.vCurrentMediaServices where mediaId in (select distinct sales.Burst.MediaID from sales.Contract inner join sales.Burst on sales.Contract.ContractID = sales.Burst.ContractID
        where sales.Contract.ContractNumber = '" + ContractNumber + "') ")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "MediaID", False, "MediaName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetMediaAllowedBetweenDates _
    (ByVal ConnectionString As String,
     ByRef ErrorMessage As String,
     ByVal GridToExclude As DataGridView,
     ByVal StartDate As Date,
     ByVal EndDate As Date) _
    As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT DISTINCT Media.Media.MediaID, Media.Media.MediaName, Media.Media.Homesite, Media.Media.Crossover, derivedtbl_1.FirstWeek, derivedtbl_1.LastWeek, ")
        SelectStatement.Append("ISNULL(dbo.udfMediaFamilyIDListByMedia(Media.Media.MediaID), N'') AS MediaFamilyIDList, Media.isPNPPcaStatus FROM Media.Media INNER JOIN ")
        SelectStatement.Append("(SELECT MediaID, FirstWeek, LastWeek FROM  Media.MediaLifeCycle ")
        SelectStatement.Append("WHERE (FirstWeek <= '" & LiquidShell.LiquidAgent.GetSqlFriendlyDate(StartDate))
        SelectStatement.Append("') AND (LastWeek >= '" & LiquidShell.LiquidAgent.GetSqlFriendlyDate(EndDate) & "'))  ")
        SelectStatement.Append("AS derivedtbl_1 ON Media.Media.MediaID = derivedtbl_1.MediaID")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "MediaID", False, "MediaName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetAccountManagers _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) _
    As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT AccountManagerID, Code, FirstName, LastName ")
        SelectStatement.Append("FROM Sales.AccountManager")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "AccountManagerID", True, "Code"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetAccountManagersByPermission_EditMyContracts _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) _
    As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT Sales.AccountManager.AccountManagerID, Sales.AccountManager.Code, ")
        SelectStatement.Append("Sales.AccountManager.FirstName, Sales.AccountManager.LastName ")
        SelectStatement.Append("FROM Sales.AccountManager INNER JOIN Sales.vAccountManagersByPermission_EditMyContracts ")
        SelectStatement.Append("ON Sales.AccountManager.AccountManagerID = Sales.vAccountManagersByPermission_EditMyContracts.AccountManagerID")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "AccountManagerID", True, "Code"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetAccountManagersByPermission_EditMyClients _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) _
    As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT Sales.AccountManager.AccountManagerID, Sales.AccountManager.Code, ")
        SelectStatement.Append("Sales.AccountManager.FirstName, Sales.AccountManager.LastName ")
        SelectStatement.Append("FROM Sales.AccountManager INNER JOIN Sales.vAccountManagersByPermission_EditMyClients ")
        SelectStatement.Append("ON Sales.AccountManager.AccountManagerID = Sales.vAccountManagersByPermission_EditMyClients.AccountManagerID")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "AccountManagerID", True, "Code"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetClientsByAccountManager _
    (ByVal ConnectionString As String,
    ByRef ErrorMessage As String,
    ByVal GridToExclude As DataGridView,
    ByVal AccountManagerID As Integer,
    ByVal AtDate As Date) _
    As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT ClientID, ClientName, ClientBillingAddress, CareTaker FROM udfClientAccountManagerAtDate('")
        SelectStatement.Append(LiquidShell.LiquidAgent.GetSqlFriendlyDate(AtDate) & "')")

        ' Attach a WHERE clause to the select statement if required.
        Dim CustomWhereClause As String = "WHERE AccountManagerID = " & AccountManagerID.ToString
        SelectStatement.Append(QueryOptions(CustomWhereClause, GridToExclude, "ClientID", False, "ClientName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetClientsByPermission_EditMyClients _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT ClientID, ClientName FROM Client.vClientsImPermittedToSee")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "ClientID", True, "ClientName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetClientsByContractHistoryByPermission_ViewMyContracts _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT DISTINCT ClientID, ClientName ")
        SelectStatement.Append("FROM (SELECT Client.Client.ClientID, Client.Client.ClientName ")
        SelectStatement.Append("FROM Client.Client INNER JOIN ")
        SelectStatement.Append("Sales.Contract ON Client.Client.ClientID = Sales.Contract.ClientID INNER JOIN ")
        SelectStatement.Append("Sales.vAccountManagersByPermission_ViewMyContracts ON ")
        SelectStatement.Append("Sales.Contract.AccountManagerID = Sales.vAccountManagersByPermission_ViewMyContracts.AccountManagerID) AS dtClient ")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "ClientID", False, "ClientName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetClients _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT ClientID, ClientName, Agency, AccountManagerName, Dormant, ClientAddress ")
        SelectStatement.Append("FROM (SELECT Client.Client.ClientID, Client.Client.ClientName,Client.Client.ApprovedByFinance, Client.Client.Agency, ISNULL(Client.vCurrentClientAccountManager.AccountManagerName, N'') ")
        SelectStatement.Append("AS AccountManagerName, Client.Client.Dormant, Client.Client.AddressLine1 + SUBSTRING(', ', 1, LEN(Client.Client.AddressLine1)) ")
        SelectStatement.Append("+ Client.Client.AddressLine2 + SUBSTRING(', ', 1, LEN(Client.Client.AddressLine2)) ")
        SelectStatement.Append("+ dbo.City.CityName + N' ' + Client.Client.PostalCode AS ClientAddress ")
        SelectStatement.Append("FROM Client.Client INNER JOIN ")
        SelectStatement.Append("dbo.City ON Client.Client.CityID = dbo.City.CityID LEFT OUTER JOIN ")
        SelectStatement.Append("Client.vCurrentClientAccountManager ON Client.Client.ClientID = Client.vCurrentClientAccountManager.ClientID) AS dtClient")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "ClientID", True, "ClientName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetContractClassifications _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT Classification, ContractClassificationId ")
        SelectStatement.Append("FROM Sales.ContractClassification ")
        'SelectStatement.Append("Where Dormant = 0")


        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "ContractClassificationId", True, "Classification"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetProposalHeat _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT ContractProposalHeatName, ContractProposalHeatId ")
        SelectStatement.Append("FROM Sales.ContractProposalHeat ")
        'SelectStatement.Append("Where Dormant = 0")


        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "ContractProposalHeatId", True, "ContractProposalHeatName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function


    Public Shared Function GetApprovedClients _
  (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT TOP (100) PERCENT ClientID, ClientName, Agency, AccountManagerName, ClientAddress ")
        SelectStatement.Append("FROM (SELECT Client.Client.ClientID, Client.Client.ClientName, Client.Client.Agency, ISNULL(Client.vCurrentClientAccountManager.AccountManagerName, N'') ")
        SelectStatement.Append("AS AccountManagerName, Client.Client.Dormant, ISNULL(Client.Client.AddressLine1, N'') + N', ' + ISNULL(Client.Client.AddressLine2, N'') + SUBSTRING(', ', ")
        SelectStatement.Append("1, LEN(ISNULL(Client.Client.AddressLine2, N''))) + dbo.City.CityName + N', ' + ISNULL(Client.Client.PostalCode, N'') AS ClientAddress ")
        SelectStatement.Append("FROM Client.Client INNER JOIN ")
        SelectStatement.Append("dbo.City ON Client.Client.CityID = dbo.City.CityID LEFT OUTER JOIN ")
        SelectStatement.Append("Client.vCurrentClientAccountManager ON Client.Client.ClientID = Client.vCurrentClientAccountManager.ClientID ")
        SelectStatement.Append("WHERE (Client.Client.ApprovedByFinance = 1)) AS dtClient")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "ClientID", True, "ClientName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetAgencies _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT ClientID, ClientName FROM Client.Client")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions("WHERE (Agency = 1)", GridToExclude, "ClientID", True, "ClientName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetInventory _
      (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) _
      As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder("SELECT ItemID, ItemName FROM Ops.Inventory")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions("WHERE AllowInContracts = 1", GridToExclude, "ItemID", True, "ItemName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetInventoryByMediaList _
    (ByVal ConnectionString As String,
    ByRef ErrorMessage As String,
    ByVal GridToExclude As DataGridView,
    ByVal MediaIDList As String) _
    As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT ItemID, ItemName ")
        SelectStatement.Append("FROM (SELECT Ops.Inventory.ItemID, Ops.Inventory.ItemName, Ops.Inventory.Dormant ")
        SelectStatement.Append("FROM Ops.Inventory INNER JOIN ")
        SelectStatement.Append("Ops.MediaInventory ON Ops.Inventory.ItemID = Ops.MediaInventory.ItemID INNER JOIN ")
        SelectStatement.Append("dbo.udfTableOfIDs('" & MediaIDList & "') ")
        SelectStatement.Append("AS TableOfIDs ON Ops.MediaInventory.MediaID = TableOfIDs.ID) AS dtInventory ")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "ItemID", True, "ItemName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetInventoryQuantities _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) _
    As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder("SELECT ItemQtyID, ItemQty FROM Ops.InventoryQty")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "ItemQtyID", True, "ItemQty"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetInventoryQuantitiesByInventory _
    (ByVal ConnectionString As String,
    ByRef ErrorMessage As String,
    ByVal PointInTime As Date,
    ByVal ItemID As String) _
    As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT Ops.InventoryQty.ItemQtyID, Ops.InventoryQty.ItemQty, ISNULL(dtSellPrice.SellPrice, 0) AS SellPrice, ISNULL(dtSellPrice.CostPrice, 0) AS CostPrice ")
        SelectStatement.Append("FROM Ops.InventoryQty LEFT OUTER JOIN ")
        SelectStatement.Append("(SELECT ItemQtyID, ISNULL(CostPrice, 0) AS CostPrice, ISNULL(SellPrice, 0) AS SellPrice ")
        SelectStatement.Append("FROM Ops.vInventoryQtyPriceDates AS vInventoryQtyPriceDates_1 ")
        SelectStatement.Append("WHERE ([From] <= '" & LiquidShell.LiquidAgent.GetSqlFriendlyDate(PointInTime) & "') ")
        SelectStatement.Append("AND ([To] >= '" & LiquidShell.LiquidAgent.GetSqlFriendlyDate(PointInTime) & "' OR ")
        SelectStatement.Append("[To] IS NULL)) AS dtSellPrice ON Ops.InventoryQty.ItemQtyID = dtSellPrice.ItemQtyID ")

        ' Attach a WHERE clause to the select statement if required.
        Dim CustomWhereClause As String = "WHERE ItemID = " & ItemID.ToString
        SelectStatement.Append(QueryOptions(CustomWhereClause, Nothing, "ItemQtyID", False, "ItemQty"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetMiscellaneousCharges _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) _
    As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder _
        ("SELECT MiscellaneousChargeID, MiscellaneousChargeName FROM Sales.MiscellaneousCharge")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "MiscellaneousChargeID", True, "MiscellaneousChargeName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function



    Public Shared Function GetChainsNonBindingSource _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) As DataTable

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT ChainID, ChainName, ChainTypeID FROM Store.Chain ")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Format("WHERE ChainID IN (SELECT ChainID FROM [dbo].[udfChainPermission]('{0}'))", My.User.CurrentPrincipal.Identity.Name), GridToExclude, "ChainID", True, "ChainName"))

        ' Return the data.
        ' NEED TO FIX THIS TO USE CONFIG FILE
        'LIVE
        'Return LiquidShell.LiquidAgent.GetSqlDataBindingSource("Data Source=primenova.primedomain.co.za;Initial Catalog=NovaDB;User ID=NovaApp;Password=******************;Connect Timeout=90", SelectStatement.ToString, ErrorMessage)
        'DEV
        Return LiquidShell.LiquidAgent.GetSqlDataTable(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

    Public Shared Function GetInstallationDays _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String, ByVal GridToExclude As DataGridView) As BindingSource

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT InstallationDayID, MediaInstallationDayName ")
        SelectStatement.Append("FROM [Media].[MediaInstallationDay] ")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, GridToExclude, "InstallationDayID", True, "InstallationDayID"))
        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataBindingSource(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

#Region "Private Functions"

    Private Shared Function QueryOptions _
    (ByVal CustomWhereClause As String, _
    ByVal RowsToExclude As Object, _
    ByVal IDColumnName As String, _
    ByVal OnlyActiveRows As Boolean, _
    ByVal OrderBy As String) As String

        ' Create a string builder to build the return string.
        Dim ReturnString As New StringBuilder(CustomWhereClause)

        ' Add a space before the custom WHERE clause if one was specified.
        If ReturnString.Length > 0 Then
            ReturnString.Insert(0, " ")
        End If

        ' Add a dormant criteria if required.
        If OnlyActiveRows Then
            If ReturnString.Length = 0 Then
                ReturnString.Append(" WHERE ")
            Else
                ReturnString.Append(" AND ")
            End If
            ReturnString.Append("(Dormant = 0)")
        End If

        ' If rows need to be excluded from the list, build a WHERE clause for the SELECT statement
        ' using the supplied DataGridView or DataTable.
        If IsNothing(RowsToExclude) = False Then

            ' Count the rows to exclude.
            Dim ExcludeRowCount As Integer = 0
            If TypeOf RowsToExclude Is DataTable Then
                ' Make a view of the table to exclude deleted rows.
                Dim TableView As New DataView(CType(RowsToExclude, DataTable), String.Empty, String.Empty, DataViewRowState.CurrentRows)
                ExcludeRowCount = TableView.Count
            Else
                ExcludeRowCount = CType(RowsToExclude, DataGridView).Rows.Count
            End If

            If ExcludeRowCount > 0 Then

                ' Add the WHERE clause to the select statement if it's not there already.
                If ReturnString.Length = 0 Then
                    ReturnString.Append(" WHERE ")
                Else
                    ReturnString.Append(" AND ")
                End If

                ' Create a string list to hold all the pieces of the WHERE clause.
                Dim Criteria As New List(Of String)

                ' Add criteria to the Criteria list.
                Dim Row As DataRow
                If TypeOf RowsToExclude Is DataTable Then
                    For Each Row In CType(RowsToExclude, DataTable).Rows
                        If Not Row.RowState = DataRowState.Deleted Then
                            AddRowExclusion(Criteria, Row, IDColumnName)
                        End If
                    Next
                Else
                    For Each GridRow As DataGridViewRow In CType(RowsToExclude, DataGridView).Rows
                        Row = CType(GridRow.DataBoundItem, DataRowView).Row
                        AddRowExclusion(Criteria, Row, IDColumnName)
                    Next
                End If

                ' Modify the SELECT statement to exclude all rows specified in the Criteria list.
                For Each Exclusion As String In Criteria
                    ReturnString.Append(Exclusion)
                Next

            End If
        End If

        ' Attach an ORDER BY clause to the select statement if required.
        If String.IsNullOrEmpty(OrderBy) = False Then
            ReturnString.Append(" ORDER BY " & OrderBy)
        End If

        Return ReturnString.ToString

    End Function

    Private Shared Sub AddRowExclusion(ByRef Criteria As List(Of String), ByVal Row As DataRow, ByVal IDColumnName As String)

        ' Separate items in the list with AND.
        If Criteria.Count > 0 Then
            Criteria.Add(" AND ")
        End If
        ' Add a criteria to exclude this row to our criteria list.
        Criteria.Add("(" & IDColumnName & " <> '" & Row(IDColumnName).ToString & "')")

    End Sub


#End Region


#Region "Validation"
    Public Shared Function CheckIfBrandExists _
    (ByVal ConnectionString As String, ByRef ErrorMessage As String) As DataTable

        ' Build the select statement to fetch data.
        Dim SelectStatement As New StringBuilder
        SelectStatement.Append("SELECT DISTINCT BrandID, BrandName ")
        SelectStatement.Append("FROM Client.Brand ")

        ' Attach a WHERE clause to the select statement if required.
        SelectStatement.Append(QueryOptions(String.Empty, Nothing, "BrandID", True, "BrandName"))

        ' Return the data.
        Return LiquidShell.LiquidAgent.GetSqlDataTable(ConnectionString, SelectStatement.ToString, ErrorMessage)

    End Function

#End Region
End Class
