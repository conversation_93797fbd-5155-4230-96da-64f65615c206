﻿using DataAccess;
using System;
using System.Collections.Generic;
using System.Data;

namespace DataService.Security
{
    class RemoveRolesOfOwnerCommand : Command
    {
        public Guid SessionId { get; set; }
        public Guid UserId { get; set; }
        public DataTable Roles { get; set; }

        public RemoveRolesOfOwnerCommand(Guid sessionid, Guid userid, List<DataRow> roleslist)
        {
            SessionId = sessionid;
            UserId = userid;

            // Create a new table.
            Roles = new DataTable();
            Roles.Columns.Add("id", typeof(Guid));

            // Convert the rows in the specified list to rows that match the schema of the table and then add them to the table.
            if (roleslist != null && roleslist.Count > 0)
            {
                for (int i = 0; i < roleslist.Count; i++)
                {
                    DataRow newrow = Roles.NewRow();
                    newrow["id"] = roleslist[i]["roleid"];
                    Roles.Rows.Add(newrow);
                }
            }
        }
    }
}
