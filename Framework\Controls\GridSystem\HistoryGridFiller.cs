﻿using System;
using System.Data;
using System.Windows.Forms;

namespace Framework.Controls.GridSystem
{
    public partial class HistoryGridFiller : GridFiller, IGridFiller
    {
        private Guid SelectedRowId;
        private string GridTitle = string.Empty;
        

        #region Startup

        public HistoryGridFiller(Guid selectedrowid, string gridtitle, Func<object[], DataTable> getdatamethod)
        {
            InitializeComponent();
            SelectedRowId = selectedrowid;
            GetGridDataMethod = getdatamethod;
            UpdateArgumentsForGetGridDataMethod = UpdateArgumentsForGetGridDataMethodImplementation;
            GridTitle = gridtitle;
            SetTitleMethod = SetTitle;
            AddColumnsMethod = AddColumns;
        }

        #endregion


        #region Initialize the grid

        private void SetTitle()
        {
            Grid.Title = GridTitle;
        }

        private void AddColumns()
        {
            Grid.Columns.Clear();

            var usercolumn = new DataGridViewTextBoxColumn();
            usercolumn.HeaderText = "User";
            usercolumn.DataPropertyName = "user";
            usercolumn.Width = 150;
            Grid.Columns.Add(usercolumn);

            var datecolumn = new DataGridViewTextBoxColumn();
            datecolumn.HeaderText = "Date";
            datecolumn.DataPropertyName = "date";
            datecolumn.Width = 130;
            Grid.Columns.Add(datecolumn);

            var actioncolumn = new DataGridViewTextBoxColumn();
            actioncolumn.HeaderText = "Action";
            actioncolumn.DataPropertyName = "action";
            actioncolumn.FillWeight = 100;
            actioncolumn.AutoSizeMode = DataGridViewAutoSizeColumnMode.Fill;
            Grid.Columns.Add(actioncolumn);

            var requestorcolumn = new DataGridViewTextBoxColumn();
            requestorcolumn.HeaderText = "Requested By";
            requestorcolumn.DataPropertyName = "requestor";
            requestorcolumn.Width = 150;
            Grid.Columns.Add(requestorcolumn);

            var requestdatecolumn = new DataGridViewTextBoxColumn();
            requestdatecolumn.HeaderText = "Request Date";
            requestdatecolumn.DataPropertyName = "requestdate";
            requestdatecolumn.Width = 130;
            Grid.Columns.Add(requestdatecolumn);

        }

        #endregion


        #region Load data into the grid

        public void Fill()
        {
            Fill(false);
        }

        private void UpdateArgumentsForGetGridDataMethodImplementation()
        {
            object[] args = { SelectedRowId, Grid.Title };
            ArgumentsForGetGridDataMethod = args;
        }

        #endregion
    }
}
