Public Class SubformBrandFamily

    Private DataObject As BrandFamily

#Region "Event Handlers"

    Private Sub Subform_Load(ByVal sender As Object, ByVal e As System.EventArgs) Handles Me.Load

        ' Load values for other controls.
        TextEditBrandFamilyName.EditValue = DataObject.BrandFamilyName

        ' Load grid data.
        GridBrandFamilyMember.AutoGenerateColumns = False
        GridBrandFamilyMember.DataSource = DataObject.BrandFamilyMemberBindingSource

        ' Configure grid managers.
        Dim GridManagerBrandFamilyMember As New GridManager(GridBrandFamilyMember, TextEditSearch, Nothing, Nothing, _
        PictureAdvancedSearch, PictureClearSearchClientBrand, Nothing, ButtonRemove)

    End Sub

    Public Sub New(ByVal BrandFamilyObject As BrandFamily)
        ' This call is required by the Windows Form Designer.
        InitializeComponent()
        ' Add any initialization after the InitializeComponent() call.
        DataObject = BrandFamilyObject
    End Sub

    Private Sub ButtonSave_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonSave.Click
        ' Save and close.
        Save(True)
    End Sub

    Private Sub ButtonCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonCancel.Click
        DataObject.Close(Me)
    End Sub

    Private Sub TextEditBrandFamilyName_EditValueChanged(ByVal sender As System.Object, ByVal e As System.EventArgs) _
    Handles TextEditBrandFamilyName.EditValueChanged

        ' Create an object to represent the control.
        Dim CurrentControl As TextEdit = CType(sender, TextEdit)

        ' Update the form title as the user types a name.
        If Not String.IsNullOrEmpty(CurrentControl.Text) Then
            LabelTitle.Text = CurrentControl.Text
        End If

        ' Reset the error text of the control to an empty string.
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)

    End Sub

    Private Sub TextEditBrandFamilyName_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
    Handles TextEditBrandFamilyName.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If ValidatedControl.Text.Length = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "The name of the brand family may not be blank.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If

        ' Update the data object.
        DataObject.BrandFamilyName = CType(sender, TextEdit).EditValue

    End Sub

    Private Sub ButtonAdd_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonAdd.Click

        ' Save first if required.
        If LiquidShell.LiquidAgent.SaveAndProceed(DataObject.Row, Me, My.Settings.DBConnection, False) = False Then
            Exit Sub
        End If

        ' Get a selection of data rows from the user and add the selected items.
        Dim SelectedItems As List(Of DataRow) = LookupBrand.SelectRows(My.Settings.DBConnection, True, GridBrandFamilyMember)
        DataObject.AddBrandFamilyMember(Me, My.Settings.DBConnection, SelectedItems)

    End Sub

    Private Sub ButtonDelete_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonRemove.Click
        DataObject.DeleteChildRow(GridBrandFamilyMember, "BrandFamilyName")
        DataObject.UpdateBrands()
    End Sub

#End Region

#Region "Methods"

    Protected Overrides Function Save() As Boolean

        ' Get the grids that need to be audited. These will be passed into the Save method.
        Dim GridsToAudit As New List(Of DataGridView)
        GridsToAudit.Add(GridBrandFamilyMember)
        DataObject.Save(My.Settings.DBConnection, GridsToAudit)
        Return True

    End Function

#End Region

End Class
