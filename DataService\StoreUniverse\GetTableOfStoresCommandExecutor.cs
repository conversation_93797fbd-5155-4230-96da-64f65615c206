﻿using DataAccess;
using System.Data;

namespace DataService.StoreUniverse
{
    class GetTableOfStoresCommandExecutor : CommandExecutor<GetTableOfStoresCommand>
    {
        public override void Execute(GetTableOfStoresCommand command)
        {
            using (StoredProcedure storedprocedure = NewStoredProcedure(StoredProcedureNames.GetStoreTable))
            {
                storedprocedure.Execute();
                command.ErrorMessage = storedprocedure.ErrorMessage;
                if (string.IsNullOrEmpty(storedprocedure.ErrorMessage))
                {
                    command.Table = storedprocedure.OutputTable;
                    command.Table.PrimaryKey = new DataColumn[] { command.Table.Columns[0] };
                }
            }
        }
    }
}
