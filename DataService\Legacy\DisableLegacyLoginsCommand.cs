﻿using DataAccess;
using System.Collections.Generic;
using System.Data;

namespace DataService.Security
{
    class DisableLegacyLoginsCommand : Command
    {
        public DataTable Logins { get; set; }

        public DisableLegacyLoginsCommand(List<DataRow> userslist)
        {
            // Create a new table.
            Logins = new DataTable();
            Logins.Columns.Add("name", typeof(string));

            // Convert the rows in the specified list to rows that match the schema of the table and then add them to the table.
            if (userslist != null && userslist.Count > 0)
            {
                for (int i = 0; i < userslist.Count; i++)
                {
                    DataRow newrow = Logins.NewRow();
                    newrow["name"] = userslist[i]["username"];
                    Logins.Rows.Add(newrow);
                }
            }
        }
    }
}
