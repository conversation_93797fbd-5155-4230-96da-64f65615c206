﻿using Framework.Surfaces;
using System.Windows.Forms;
using Framework.Controls.GridSystem;
using System.Data;
using System;
using System.Collections.Generic;

namespace UserInterface.Security
{
    public class GridSurfaceFactory : ITabContentSurfaceFactory
    {
        protected ExcelExporter ExcelExporter;
        string IdColumnName = string.Empty;
        protected DataRow ParentRow;


        public GridSurfaceFactory(ExcelExporter excelexporter, string idcolumnname, string tabtext)
        {
            ExcelExporter = excelexporter;
            TabText = tabtext;
            IdColumnName = idcolumnname;
        }

        private string _TabText = "Tab Text Goes Here";
        public string TabText
        {
            get
            {
                return _TabText;
            }
            private set
            {
                _TabText = value;
            }
        }

        protected DataRow GetSelectedRow(BindingSource bindingsource)
        {
            return ((DataRowView)bindingsource.Current).Row;
        }

        protected Guid GetSelectedRowId(BindingSource bindingsource)
        {
            var selectedrowid = Guid.Empty;
            var selectedrow = GetSelectedRow(bindingsource);
            if (selectedrow.RowState != DataRowState.Detached)
            {
                selectedrowid = (Guid)selectedrow[IdColumnName];
            }
            return selectedrowid;
        }

        public virtual Surface NewSurface(BindingSource bindingsource)
        {
            return null;
        }

        protected virtual string DeleteRows(List<DataRow> rowstodelete, object parentrowid)
        {
            return string.Empty;
        }
    }
}
