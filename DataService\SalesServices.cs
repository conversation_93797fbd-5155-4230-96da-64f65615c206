﻿using DataAccess;
using DataService.Legacy;
using DataService.Sales;
using System;
using System.Data;
using Universal.Entities;

namespace DataService
{
    public static class SalesServices
    {

        public static DataTable GetTableOfCategories(ref string errormessage)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.LegacyConnectionString))
            {
                var command = new GetTableOfCategoriesCommand();
                manager.ExecuteCommand(ref errormessage, command, new GetTableOfCategoriesCommandExecutor());
                return command.Table;
            }
        }

        public static DataTable GetTableOfMediaServices(ref string errormessage)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.LegacyConnectionString))
            {
                var command = new GetTableOfMediaServicesCommand();
                manager.ExecuteCommand(ref errormessage, command, new GetTableOfMediaServicesCommandExecutor());
                return command.Table;
            }
        }

        public static DataTable GetTableOfContracts(ref string errormessage)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.LegacyConnectionString))
            {
                var command = new GetTableOfContractsCommand();
                manager.ExecuteCommand(ref errormessage, command, new GetTableOfContractsCommandExecutor());
                return command.Table;
            }
        }

        public static ContractStatus GetContractStatus(ref string errormessage, Guid contractid)
        {
            using (CommandExecutionManager manager = new CommandExecutionManager(ref errormessage, Universal.Settings.CurrentSession.LegacyConnectionString))
            {
                var command = new GetContractStatusCommand(contractid);
                manager.ExecuteCommand(ref errormessage, command, new GetContractStatusCommandExecutor());
                return command.ContractStatus;
            }
        }

    }
}
