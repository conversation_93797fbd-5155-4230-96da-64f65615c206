<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class FormEditStoreList
    Inherits LiquidShell.EditorForm

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(FormEditStoreList))
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim SuperToolTip1 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem1 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem1 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip2 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem2 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem2 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle4 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle5 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle6 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim SuperToolTip3 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem3 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem3 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip4 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem4 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem4 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ImageList16x16 = New System.Windows.Forms.ImageList(Me.components)
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.PanelIndependentStoreList = New System.Windows.Forms.Panel()
        Me.ButtonNewIndependentStoreList = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonDeleteIndependentStoreList = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelIndependentStoreList = New DevExpress.XtraEditors.LabelControl()
        Me.LabelIndependentStoreListDescription = New DevExpress.XtraEditors.LabelControl()
        Me.GroupStoreList = New DevExpress.XtraEditors.GroupControl()
        Me.GridStoreList = New System.Windows.Forms.DataGridView()
        Me.SelectedStoreNumberColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.SelectedStoreNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.SelectedStoreGroupColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.SelectedCityNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.SelectedRegionNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DormantListColumn = New System.Windows.Forms.DataGridViewCheckBoxColumn()
        Me.MediaNotAllowedListColumn = New System.Windows.Forms.DataGridViewCheckBoxColumn()
        Me.TakenListColumn = New System.Windows.Forms.DataGridViewCheckBoxColumn()
        Me.ButtonRemove = New DevExpress.XtraEditors.SimpleButton()
        Me.LabelSearchStoreList = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearchStoreList = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearchStoreList = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearchStoreList = New DevExpress.XtraEditors.PictureEdit()
        Me.PanelAvailableStores = New System.Windows.Forms.Panel()
        Me.HyperlinkIndependentStoreList = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkBurst = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkStorePool = New DevExpress.XtraEditors.LabelControl()
        Me.LabelAvailableStores = New DevExpress.XtraEditors.LabelControl()
        Me.LabelDisplayStoresFrom = New DevExpress.XtraEditors.LabelControl()
        Me.HyperlinkStoreUniverse = New DevExpress.XtraEditors.LabelControl()
        Me.PanelStoreList = New System.Windows.Forms.Panel()
        Me.LabelStoreListTitle = New DevExpress.XtraEditors.LabelControl()
        Me.LabelInstallStoreQtyValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelRemainingStorePoolQty = New DevExpress.XtraEditors.LabelControl()
        Me.LabelInstallStoreQty = New DevExpress.XtraEditors.LabelControl()
        Me.LabelRemainingStorePoolQtyValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelRemainingQty = New DevExpress.XtraEditors.LabelControl()
        Me.LabelRemainingUniverseQtyValue = New DevExpress.XtraEditors.LabelControl()
        Me.LabelRemainingUniverseQty = New DevExpress.XtraEditors.LabelControl()
        Me.LabelRemainingQtyValue = New DevExpress.XtraEditors.LabelControl()
        Me.GroupAvailableStores = New DevExpress.XtraEditors.GroupControl()
        Me.ButtonAdd = New DevExpress.XtraEditors.SimpleButton()
        Me.GridAvailableStores = New System.Windows.Forms.DataGridView()
        Me.AvailableStoreNumberColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.AvailableStoreNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.StoreGroupColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.AvailableCityNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.AvailableRegionNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.DormantAvailableColumn = New System.Windows.Forms.DataGridViewCheckBoxColumn()
        Me.MediaNotAllowedAvailableColumn = New System.Windows.Forms.DataGridViewCheckBoxColumn()
        Me.TakenAvailableColumn = New System.Windows.Forms.DataGridViewCheckBoxColumn()
        Me.MediaAllowedInCategoryForStore = New System.Windows.Forms.DataGridViewCheckBoxColumn()
        Me.LabelSearchAvailableStores = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearchAvailableStores = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearchAvailableStores = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearchAvailableStores = New DevExpress.XtraEditors.PictureEdit()
        Me.PanelLegend = New System.Windows.Forms.Panel()
        Me.TextEditLegendMediaNotAllowedForCategory = New DevExpress.XtraEditors.TextEdit()
        Me.LabelControl1 = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditLegendMediaNotAllwed = New DevExpress.XtraEditors.TextEdit()
        Me.LabelLegend = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditLegendTaken = New DevExpress.XtraEditors.TextEdit()
        Me.TextEditLegendDormant = New DevExpress.XtraEditors.TextEdit()
        Me.LabelLegendMediaNotAllwed = New DevExpress.XtraEditors.LabelControl()
        Me.LabelLegendTaken = New DevExpress.XtraEditors.LabelControl()
        Me.LabelLegendDormant = New DevExpress.XtraEditors.LabelControl()
        Me.ButtonOK = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton()
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelButtonBar.SuspendLayout()
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TableLayoutPanel1.SuspendLayout()
        Me.PanelIndependentStoreList.SuspendLayout()
        CType(Me.GroupStoreList, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupStoreList.SuspendLayout()
        CType(Me.GridStoreList, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditSearchStoreList.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearchStoreList.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearchStoreList.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelAvailableStores.SuspendLayout()
        Me.PanelStoreList.SuspendLayout()
        CType(Me.GroupAvailableStores, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupAvailableStores.SuspendLayout()
        CType(Me.GridAvailableStores, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditSearchAvailableStores.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearchAvailableStores.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearchAvailableStores.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.PanelLegend.SuspendLayout()
        CType(Me.TextEditLegendMediaNotAllowedForCategory.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditLegendMediaNotAllwed.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditLegendTaken.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditLegendDormant.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'PanelButtonBar
        '
        Me.PanelButtonBar.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelButtonBar.Appearance.BackColor2 = System.Drawing.Color.LightSteelBlue
        Me.PanelButtonBar.Appearance.GradientMode = System.Drawing.Drawing2D.LinearGradientMode.Vertical
        Me.PanelButtonBar.Appearance.Options.UseBackColor = True
        Me.PanelButtonBar.Controls.Add(Me.ButtonOK)
        Me.PanelButtonBar.Controls.Add(Me.ButtonCancel)
        Me.PanelButtonBar.Location = New System.Drawing.Point(0, 622)
        Me.PanelButtonBar.LookAndFeel.Style = DevExpress.LookAndFeel.LookAndFeelStyle.Office2003
        Me.PanelButtonBar.LookAndFeel.UseDefaultLookAndFeel = False
        Me.PanelButtonBar.Size = New System.Drawing.Size(984, 52)
        Me.PanelButtonBar.TabIndex = 1
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        Me.ImageList24x24.Images.SetKeyName(3, "back.png")
        '
        'ImageList16x16
        '
        Me.ImageList16x16.ImageStream = CType(resources.GetObject("ImageList16x16.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList16x16.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList16x16.Images.SetKeyName(0, "next.png")
        Me.ImageList16x16.Images.SetKeyName(1, "back.png")
        Me.ImageList16x16.Images.SetKeyName(2, "delete.png")
        Me.ImageList16x16.Images.SetKeyName(3, "add.png")
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanel1.ColumnCount = 3
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 15.0!))
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.PanelIndependentStoreList, 2, 1)
        Me.TableLayoutPanel1.Controls.Add(Me.GroupStoreList, 2, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.PanelAvailableStores, 0, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.PanelStoreList, 2, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.GroupAvailableStores, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.PanelLegend, 0, 1)
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(12, 12)
        Me.TableLayoutPanel1.Margin = New System.Windows.Forms.Padding(3, 3, 3, 0)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 3
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 112.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 98.0!))
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(960, 610)
        Me.TableLayoutPanel1.TabIndex = 0
        '
        'PanelIndependentStoreList
        '
        Me.PanelIndependentStoreList.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelIndependentStoreList.Controls.Add(Me.ButtonNewIndependentStoreList)
        Me.PanelIndependentStoreList.Controls.Add(Me.ButtonDeleteIndependentStoreList)
        Me.PanelIndependentStoreList.Controls.Add(Me.LabelIndependentStoreList)
        Me.PanelIndependentStoreList.Controls.Add(Me.LabelIndependentStoreListDescription)
        Me.PanelIndependentStoreList.Location = New System.Drawing.Point(487, 112)
        Me.PanelIndependentStoreList.Margin = New System.Windows.Forms.Padding(0)
        Me.PanelIndependentStoreList.Name = "PanelIndependentStoreList"
        Me.PanelIndependentStoreList.Size = New System.Drawing.Size(473, 98)
        Me.PanelIndependentStoreList.TabIndex = 3
        '
        'ButtonNewIndependentStoreList
        '
        Me.ButtonNewIndependentStoreList.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonNewIndependentStoreList.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonNewIndependentStoreList.Appearance.Options.UseFont = True
        Me.ButtonNewIndependentStoreList.ImageIndex = 3
        Me.ButtonNewIndependentStoreList.ImageList = Me.ImageList16x16
        Me.ButtonNewIndependentStoreList.Location = New System.Drawing.Point(317, 60)
        Me.ButtonNewIndependentStoreList.LookAndFeel.SkinName = "Black"
        Me.ButtonNewIndependentStoreList.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonNewIndependentStoreList.Margin = New System.Windows.Forms.Padding(3, 3, 3, 15)
        Me.ButtonNewIndependentStoreList.Name = "ButtonNewIndependentStoreList"
        Me.ButtonNewIndependentStoreList.Size = New System.Drawing.Size(75, 23)
        Me.ButtonNewIndependentStoreList.TabIndex = 2
        Me.ButtonNewIndependentStoreList.Text = "New..."
        '
        'ButtonDeleteIndependentStoreList
        '
        Me.ButtonDeleteIndependentStoreList.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonDeleteIndependentStoreList.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonDeleteIndependentStoreList.Appearance.Options.UseFont = True
        Me.ButtonDeleteIndependentStoreList.ImageIndex = 2
        Me.ButtonDeleteIndependentStoreList.ImageList = Me.ImageList16x16
        Me.ButtonDeleteIndependentStoreList.Location = New System.Drawing.Point(398, 60)
        Me.ButtonDeleteIndependentStoreList.LookAndFeel.SkinName = "Black"
        Me.ButtonDeleteIndependentStoreList.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonDeleteIndependentStoreList.Margin = New System.Windows.Forms.Padding(3, 3, 0, 15)
        Me.ButtonDeleteIndependentStoreList.Name = "ButtonDeleteIndependentStoreList"
        Me.ButtonDeleteIndependentStoreList.Size = New System.Drawing.Size(75, 23)
        Me.ButtonDeleteIndependentStoreList.TabIndex = 3
        Me.ButtonDeleteIndependentStoreList.Text = "Delete"
        '
        'LabelIndependentStoreList
        '
        Me.LabelIndependentStoreList.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelIndependentStoreList.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelIndependentStoreList.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelIndependentStoreList.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelIndependentStoreList.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelIndependentStoreList.LineVisible = True
        Me.LabelIndependentStoreList.Location = New System.Drawing.Point(0, 0)
        Me.LabelIndependentStoreList.Margin = New System.Windows.Forms.Padding(0, 0, 0, 6)
        Me.LabelIndependentStoreList.Name = "LabelIndependentStoreList"
        Me.LabelIndependentStoreList.Size = New System.Drawing.Size(473, 18)
        Me.LabelIndependentStoreList.TabIndex = 0
        Me.LabelIndependentStoreList.Text = "Independent Store List"
        '
        'LabelIndependentStoreListDescription
        '
        Me.LabelIndependentStoreListDescription.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelIndependentStoreListDescription.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelIndependentStoreListDescription.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelIndependentStoreListDescription.Location = New System.Drawing.Point(0, 27)
        Me.LabelIndependentStoreListDescription.Margin = New System.Windows.Forms.Padding(0, 3, 3, 3)
        Me.LabelIndependentStoreListDescription.Name = "LabelIndependentStoreListDescription"
        Me.LabelIndependentStoreListDescription.Size = New System.Drawing.Size(473, 26)
        Me.LabelIndependentStoreListDescription.TabIndex = 1
        Me.LabelIndependentStoreListDescription.Text = "An independent store list (which can be used for any contract) can be created usi" &
    "ng the stores currently selected below."
        '
        'GroupStoreList
        '
        Me.GroupStoreList.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupStoreList.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupStoreList.Appearance.Options.UseFont = True
        Me.GroupStoreList.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupStoreList.AppearanceCaption.Options.UseFont = True
        Me.GroupStoreList.Controls.Add(Me.GridStoreList)
        Me.GroupStoreList.Controls.Add(Me.ButtonRemove)
        Me.GroupStoreList.Controls.Add(Me.LabelSearchStoreList)
        Me.GroupStoreList.Controls.Add(Me.TextEditSearchStoreList)
        Me.GroupStoreList.Controls.Add(Me.PictureClearSearchStoreList)
        Me.GroupStoreList.Controls.Add(Me.PictureAdvancedSearchStoreList)
        Me.GroupStoreList.Location = New System.Drawing.Point(487, 210)
        Me.GroupStoreList.LookAndFeel.SkinName = "Black"
        Me.GroupStoreList.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupStoreList.Margin = New System.Windows.Forms.Padding(0)
        Me.GroupStoreList.Name = "GroupStoreList"
        Me.GroupStoreList.Size = New System.Drawing.Size(473, 400)
        Me.GroupStoreList.TabIndex = 5
        Me.GroupStoreList.Text = "Store List"
        '
        'GridStoreList
        '
        Me.GridStoreList.AllowUserToAddRows = False
        Me.GridStoreList.AllowUserToDeleteRows = False
        Me.GridStoreList.AllowUserToOrderColumns = True
        Me.GridStoreList.AllowUserToResizeRows = False
        DataGridViewCellStyle1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridStoreList.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle1
        Me.GridStoreList.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridStoreList.BackgroundColor = System.Drawing.Color.White
        Me.GridStoreList.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridStoreList.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridStoreList.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle2.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle2.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridStoreList.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle2
        Me.GridStoreList.ColumnHeadersHeight = 22
        Me.GridStoreList.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridStoreList.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.SelectedStoreNumberColumn, Me.SelectedStoreNameColumn, Me.SelectedStoreGroupColumn, Me.SelectedCityNameColumn, Me.SelectedRegionNameColumn, Me.DormantListColumn, Me.MediaNotAllowedListColumn, Me.TakenListColumn})
        Me.GridStoreList.EnableHeadersVisualStyles = False
        Me.GridStoreList.GridColor = System.Drawing.Color.White
        Me.GridStoreList.Location = New System.Drawing.Point(2, 22)
        Me.GridStoreList.Margin = New System.Windows.Forms.Padding(0, 0, 0, 3)
        Me.GridStoreList.Name = "GridStoreList"
        Me.GridStoreList.ReadOnly = True
        Me.GridStoreList.RowHeadersVisible = False
        DataGridViewCellStyle3.ForeColor = System.Drawing.Color.DimGray
        Me.GridStoreList.RowsDefaultCellStyle = DataGridViewCellStyle3
        Me.GridStoreList.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridStoreList.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridStoreList.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridStoreList.RowTemplate.Height = 19
        Me.GridStoreList.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridStoreList.ShowCellToolTips = False
        Me.GridStoreList.Size = New System.Drawing.Size(469, 344)
        Me.GridStoreList.StandardTab = True
        Me.GridStoreList.TabIndex = 1
        '
        'SelectedStoreNumberColumn
        '
        Me.SelectedStoreNumberColumn.DataPropertyName = "StoreNumber"
        Me.SelectedStoreNumberColumn.HeaderText = "Number"
        Me.SelectedStoreNumberColumn.Name = "SelectedStoreNumberColumn"
        Me.SelectedStoreNumberColumn.ReadOnly = True
        Me.SelectedStoreNumberColumn.Width = 76
        '
        'SelectedStoreNameColumn
        '
        Me.SelectedStoreNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.SelectedStoreNameColumn.DataPropertyName = "StoreName"
        Me.SelectedStoreNameColumn.FillWeight = 40.0!
        Me.SelectedStoreNameColumn.HeaderText = "Name"
        Me.SelectedStoreNameColumn.Name = "SelectedStoreNameColumn"
        Me.SelectedStoreNameColumn.ReadOnly = True
        '
        'SelectedStoreGroupColumn
        '
        Me.SelectedStoreGroupColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.SelectedStoreGroupColumn.DataPropertyName = "StoreGroup"
        Me.SelectedStoreGroupColumn.FillWeight = 40.0!
        Me.SelectedStoreGroupColumn.HeaderText = "Group"
        Me.SelectedStoreGroupColumn.Name = "SelectedStoreGroupColumn"
        Me.SelectedStoreGroupColumn.ReadOnly = True
        '
        'SelectedCityNameColumn
        '
        Me.SelectedCityNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.SelectedCityNameColumn.DataPropertyName = "CityName"
        Me.SelectedCityNameColumn.FillWeight = 30.0!
        Me.SelectedCityNameColumn.HeaderText = "City"
        Me.SelectedCityNameColumn.Name = "SelectedCityNameColumn"
        Me.SelectedCityNameColumn.ReadOnly = True
        '
        'SelectedRegionNameColumn
        '
        Me.SelectedRegionNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.SelectedRegionNameColumn.DataPropertyName = "RegionName"
        Me.SelectedRegionNameColumn.FillWeight = 30.0!
        Me.SelectedRegionNameColumn.HeaderText = "Region"
        Me.SelectedRegionNameColumn.Name = "SelectedRegionNameColumn"
        Me.SelectedRegionNameColumn.ReadOnly = True
        '
        'DormantListColumn
        '
        Me.DormantListColumn.DataPropertyName = "Dormant"
        Me.DormantListColumn.HeaderText = "Red (Dormant)"
        Me.DormantListColumn.Name = "DormantListColumn"
        Me.DormantListColumn.ReadOnly = True
        Me.DormantListColumn.Visible = False
        '
        'MediaNotAllowedListColumn
        '
        Me.MediaNotAllowedListColumn.DataPropertyName = "MediaNotAllowed"
        Me.MediaNotAllowedListColumn.HeaderText = "Orange (Media Not Allowed)"
        Me.MediaNotAllowedListColumn.Name = "MediaNotAllowedListColumn"
        Me.MediaNotAllowedListColumn.ReadOnly = True
        Me.MediaNotAllowedListColumn.Visible = False
        '
        'TakenListColumn
        '
        Me.TakenListColumn.DataPropertyName = "Taken"
        Me.TakenListColumn.HeaderText = "Yellow (Taken)"
        Me.TakenListColumn.Name = "TakenListColumn"
        Me.TakenListColumn.ReadOnly = True
        Me.TakenListColumn.Visible = False
        '
        'ButtonRemove
        '
        Me.ButtonRemove.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonRemove.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonRemove.Appearance.Options.UseFont = True
        Me.ButtonRemove.ImageIndex = 1
        Me.ButtonRemove.ImageList = Me.ImageList16x16
        Me.ButtonRemove.Location = New System.Drawing.Point(5, 372)
        Me.ButtonRemove.LookAndFeel.SkinName = "Black"
        Me.ButtonRemove.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonRemove.Name = "ButtonRemove"
        Me.ButtonRemove.Size = New System.Drawing.Size(75, 23)
        Me.ButtonRemove.TabIndex = 2
        Me.ButtonRemove.Text = "Remove"
        '
        'LabelSearchStoreList
        '
        Me.LabelSearchStoreList.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelSearchStoreList.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSearchStoreList.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelSearchStoreList.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelSearchStoreList.Location = New System.Drawing.Point(315, 376)
        Me.LabelSearchStoreList.Name = "LabelSearchStoreList"
        Me.LabelSearchStoreList.Size = New System.Drawing.Size(45, 13)
        Me.LabelSearchStoreList.TabIndex = 3
        Me.LabelSearchStoreList.Text = "Search:"
        '
        'TextEditSearchStoreList
        '
        Me.TextEditSearchStoreList.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchStoreList.EditValue = ""
        Me.TextEditSearchStoreList.Location = New System.Drawing.Point(366, 373)
        Me.TextEditSearchStoreList.Margin = New System.Windows.Forms.Padding(3, 4, 3, 5)
        Me.TextEditSearchStoreList.Name = "TextEditSearchStoreList"
        Me.TextEditSearchStoreList.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchStoreList.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchStoreList.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchStoreList.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchStoreList.Size = New System.Drawing.Size(80, 20)
        Me.TextEditSearchStoreList.TabIndex = 4
        '
        'PictureClearSearchStoreList
        '
        Me.PictureClearSearchStoreList.AllowDrop = True
        Me.PictureClearSearchStoreList.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchStoreList.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchStoreList.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchStoreList.Location = New System.Drawing.Point(452, 3)
        Me.PictureClearSearchStoreList.Name = "PictureClearSearchStoreList"
        Me.PictureClearSearchStoreList.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchStoreList.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchStoreList.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchStoreList.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem1.Text = "Clear Search"
        ToolTipItem1.LeftIndent = 6
        ToolTipItem1.Text = "Click here to clear all search boxes."
        SuperToolTip1.Items.Add(ToolTipTitleItem1)
        SuperToolTip1.Items.Add(ToolTipItem1)
        Me.PictureClearSearchStoreList.SuperTip = SuperToolTip1
        Me.PictureClearSearchStoreList.TabIndex = 0
        Me.PictureClearSearchStoreList.TabStop = True
        '
        'PictureAdvancedSearchStoreList
        '
        Me.PictureAdvancedSearchStoreList.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearchStoreList.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearchStoreList.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearchStoreList.Location = New System.Drawing.Point(452, 375)
        Me.PictureAdvancedSearchStoreList.Margin = New System.Windows.Forms.Padding(3, 6, 3, 7)
        Me.PictureAdvancedSearchStoreList.Name = "PictureAdvancedSearchStoreList"
        Me.PictureAdvancedSearchStoreList.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearchStoreList.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearchStoreList.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearchStoreList.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem2.Text = "Advanced Search"
        ToolTipItem2.LeftIndent = 6
        ToolTipItem2.Text = "Click here to search individual column values."
        SuperToolTip2.Items.Add(ToolTipTitleItem2)
        SuperToolTip2.Items.Add(ToolTipItem2)
        Me.PictureAdvancedSearchStoreList.SuperTip = SuperToolTip2
        Me.PictureAdvancedSearchStoreList.TabIndex = 5
        Me.PictureAdvancedSearchStoreList.TabStop = True
        '
        'PanelAvailableStores
        '
        Me.PanelAvailableStores.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelAvailableStores.BackColor = System.Drawing.Color.WhiteSmoke
        Me.PanelAvailableStores.Controls.Add(Me.HyperlinkIndependentStoreList)
        Me.PanelAvailableStores.Controls.Add(Me.HyperlinkBurst)
        Me.PanelAvailableStores.Controls.Add(Me.HyperlinkStorePool)
        Me.PanelAvailableStores.Controls.Add(Me.LabelAvailableStores)
        Me.PanelAvailableStores.Controls.Add(Me.LabelDisplayStoresFrom)
        Me.PanelAvailableStores.Controls.Add(Me.HyperlinkStoreUniverse)
        Me.PanelAvailableStores.Location = New System.Drawing.Point(0, 0)
        Me.PanelAvailableStores.Margin = New System.Windows.Forms.Padding(0)
        Me.PanelAvailableStores.Name = "PanelAvailableStores"
        Me.PanelAvailableStores.Size = New System.Drawing.Size(472, 112)
        Me.PanelAvailableStores.TabIndex = 0
        '
        'HyperlinkIndependentStoreList
        '
        Me.HyperlinkIndependentStoreList.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkIndependentStoreList.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkIndependentStoreList.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkIndependentStoreList.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkIndependentStoreList.Location = New System.Drawing.Point(123, 87)
        Me.HyperlinkIndependentStoreList.Name = "HyperlinkIndependentStoreList"
        Me.HyperlinkIndependentStoreList.Size = New System.Drawing.Size(142, 13)
        Me.HyperlinkIndependentStoreList.TabIndex = 5
        Me.HyperlinkIndependentStoreList.Text = "An independent store list"
        '
        'HyperlinkBurst
        '
        Me.HyperlinkBurst.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkBurst.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkBurst.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkBurst.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkBurst.Location = New System.Drawing.Point(123, 68)
        Me.HyperlinkBurst.Name = "HyperlinkBurst"
        Me.HyperlinkBurst.Size = New System.Drawing.Size(93, 13)
        Me.HyperlinkBurst.TabIndex = 4
        Me.HyperlinkBurst.Text = "A different burst"
        '
        'HyperlinkStorePool
        '
        Me.HyperlinkStorePool.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.HyperlinkStorePool.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Underline, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkStorePool.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.HyperlinkStorePool.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkStorePool.Location = New System.Drawing.Point(123, 49)
        Me.HyperlinkStorePool.Name = "HyperlinkStorePool"
        Me.HyperlinkStorePool.Size = New System.Drawing.Size(82, 13)
        Me.HyperlinkStorePool.TabIndex = 3
        Me.HyperlinkStorePool.Text = "The store pool"
        '
        'LabelAvailableStores
        '
        Me.LabelAvailableStores.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelAvailableStores.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelAvailableStores.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelAvailableStores.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelAvailableStores.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelAvailableStores.LineVisible = True
        Me.LabelAvailableStores.Location = New System.Drawing.Point(0, 0)
        Me.LabelAvailableStores.Margin = New System.Windows.Forms.Padding(0, 0, 0, 9)
        Me.LabelAvailableStores.Name = "LabelAvailableStores"
        Me.LabelAvailableStores.Size = New System.Drawing.Size(472, 18)
        Me.LabelAvailableStores.TabIndex = 0
        Me.LabelAvailableStores.Text = "Available Stores"
        '
        'LabelDisplayStoresFrom
        '
        Me.LabelDisplayStoresFrom.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelDisplayStoresFrom.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelDisplayStoresFrom.Location = New System.Drawing.Point(0, 30)
        Me.LabelDisplayStoresFrom.Margin = New System.Windows.Forms.Padding(0, 3, 3, 3)
        Me.LabelDisplayStoresFrom.Name = "LabelDisplayStoresFrom"
        Me.LabelDisplayStoresFrom.Size = New System.Drawing.Size(117, 13)
        Me.LabelDisplayStoresFrom.TabIndex = 1
        Me.LabelDisplayStoresFrom.Text = "Display stores from:"
        '
        'HyperlinkStoreUniverse
        '
        Me.HyperlinkStoreUniverse.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.HyperlinkStoreUniverse.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.HyperlinkStoreUniverse.Cursor = System.Windows.Forms.Cursors.Hand
        Me.HyperlinkStoreUniverse.Location = New System.Drawing.Point(123, 30)
        Me.HyperlinkStoreUniverse.Name = "HyperlinkStoreUniverse"
        Me.HyperlinkStoreUniverse.Size = New System.Drawing.Size(107, 13)
        Me.HyperlinkStoreUniverse.TabIndex = 2
        Me.HyperlinkStoreUniverse.Text = "The store universe"
        '
        'PanelStoreList
        '
        Me.PanelStoreList.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelStoreList.Controls.Add(Me.LabelStoreListTitle)
        Me.PanelStoreList.Controls.Add(Me.LabelInstallStoreQtyValue)
        Me.PanelStoreList.Controls.Add(Me.LabelRemainingStorePoolQty)
        Me.PanelStoreList.Controls.Add(Me.LabelInstallStoreQty)
        Me.PanelStoreList.Controls.Add(Me.LabelRemainingStorePoolQtyValue)
        Me.PanelStoreList.Controls.Add(Me.LabelRemainingQty)
        Me.PanelStoreList.Controls.Add(Me.LabelRemainingUniverseQtyValue)
        Me.PanelStoreList.Controls.Add(Me.LabelRemainingUniverseQty)
        Me.PanelStoreList.Controls.Add(Me.LabelRemainingQtyValue)
        Me.PanelStoreList.Location = New System.Drawing.Point(487, 0)
        Me.PanelStoreList.Margin = New System.Windows.Forms.Padding(0)
        Me.PanelStoreList.Name = "PanelStoreList"
        Me.PanelStoreList.Size = New System.Drawing.Size(473, 112)
        Me.PanelStoreList.TabIndex = 1
        '
        'LabelStoreListTitle
        '
        Me.LabelStoreListTitle.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelStoreListTitle.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelStoreListTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelStoreListTitle.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelStoreListTitle.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelStoreListTitle.LineVisible = True
        Me.LabelStoreListTitle.Location = New System.Drawing.Point(0, 0)
        Me.LabelStoreListTitle.Margin = New System.Windows.Forms.Padding(0, 0, 0, 9)
        Me.LabelStoreListTitle.Name = "LabelStoreListTitle"
        Me.LabelStoreListTitle.Size = New System.Drawing.Size(473, 18)
        Me.LabelStoreListTitle.TabIndex = 0
        Me.LabelStoreListTitle.Text = "Selected Stores"
        '
        'LabelInstallStoreQtyValue
        '
        Me.LabelInstallStoreQtyValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelInstallStoreQtyValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelInstallStoreQtyValue.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.LabelInstallStoreQtyValue.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelInstallStoreQtyValue.Location = New System.Drawing.Point(286, 30)
        Me.LabelInstallStoreQtyValue.Margin = New System.Windows.Forms.Padding(3, 3, 0, 3)
        Me.LabelInstallStoreQtyValue.Name = "LabelInstallStoreQtyValue"
        Me.LabelInstallStoreQtyValue.Size = New System.Drawing.Size(51, 13)
        Me.LabelInstallStoreQtyValue.TabIndex = 2
        Me.LabelInstallStoreQtyValue.Text = "0"
        '
        'LabelRemainingStorePoolQty
        '
        Me.LabelRemainingStorePoolQty.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelRemainingStorePoolQty.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelRemainingStorePoolQty.Location = New System.Drawing.Point(0, 87)
        Me.LabelRemainingStorePoolQty.Margin = New System.Windows.Forms.Padding(0, 3, 3, 12)
        Me.LabelRemainingStorePoolQty.Name = "LabelRemainingStorePoolQty"
        Me.LabelRemainingStorePoolQty.Size = New System.Drawing.Size(280, 13)
        Me.LabelRemainingStorePoolQty.TabIndex = 5
        Me.LabelRemainingStorePoolQty.Text = "Stores that may be selected from the store pool:"
        '
        'LabelInstallStoreQty
        '
        Me.LabelInstallStoreQty.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelInstallStoreQty.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelInstallStoreQty.Location = New System.Drawing.Point(0, 30)
        Me.LabelInstallStoreQty.Margin = New System.Windows.Forms.Padding(0, 3, 3, 3)
        Me.LabelInstallStoreQty.Name = "LabelInstallStoreQty"
        Me.LabelInstallStoreQty.Size = New System.Drawing.Size(180, 13)
        Me.LabelInstallStoreQty.TabIndex = 1
        Me.LabelInstallStoreQty.Text = "Stores requested for this burst:"
        '
        'LabelRemainingStorePoolQtyValue
        '
        Me.LabelRemainingStorePoolQtyValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelRemainingStorePoolQtyValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelRemainingStorePoolQtyValue.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.LabelRemainingStorePoolQtyValue.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelRemainingStorePoolQtyValue.Location = New System.Drawing.Point(286, 87)
        Me.LabelRemainingStorePoolQtyValue.Margin = New System.Windows.Forms.Padding(3, 3, 0, 3)
        Me.LabelRemainingStorePoolQtyValue.Name = "LabelRemainingStorePoolQtyValue"
        Me.LabelRemainingStorePoolQtyValue.Size = New System.Drawing.Size(51, 13)
        Me.LabelRemainingStorePoolQtyValue.TabIndex = 6
        Me.LabelRemainingStorePoolQtyValue.Text = "0"
        '
        'LabelRemainingQty
        '
        Me.LabelRemainingQty.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelRemainingQty.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelRemainingQty.Location = New System.Drawing.Point(0, 49)
        Me.LabelRemainingQty.Margin = New System.Windows.Forms.Padding(0, 3, 3, 3)
        Me.LabelRemainingQty.Name = "LabelRemainingQty"
        Me.LabelRemainingQty.Size = New System.Drawing.Size(259, 13)
        Me.LabelRemainingQty.TabIndex = 3
        Me.LabelRemainingQty.Text = "Additional stores required to fill the store list:"
        '
        'LabelRemainingUniverseQtyValue
        '
        Me.LabelRemainingUniverseQtyValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelRemainingUniverseQtyValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelRemainingUniverseQtyValue.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.LabelRemainingUniverseQtyValue.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelRemainingUniverseQtyValue.Location = New System.Drawing.Point(286, 68)
        Me.LabelRemainingUniverseQtyValue.Margin = New System.Windows.Forms.Padding(3, 3, 0, 3)
        Me.LabelRemainingUniverseQtyValue.Name = "LabelRemainingUniverseQtyValue"
        Me.LabelRemainingUniverseQtyValue.Size = New System.Drawing.Size(51, 13)
        Me.LabelRemainingUniverseQtyValue.TabIndex = 4
        Me.LabelRemainingUniverseQtyValue.Text = "0"
        '
        'LabelRemainingUniverseQty
        '
        Me.LabelRemainingUniverseQty.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelRemainingUniverseQty.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelRemainingUniverseQty.Location = New System.Drawing.Point(0, 68)
        Me.LabelRemainingUniverseQty.Margin = New System.Windows.Forms.Padding(0, 3, 3, 3)
        Me.LabelRemainingUniverseQty.Name = "LabelRemainingUniverseQty"
        Me.LabelRemainingUniverseQty.Size = New System.Drawing.Size(272, 13)
        Me.LabelRemainingUniverseQty.TabIndex = 3
        Me.LabelRemainingUniverseQty.Text = "Stores that may be selected from the universe:"
        '
        'LabelRemainingQtyValue
        '
        Me.LabelRemainingQtyValue.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelRemainingQtyValue.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelRemainingQtyValue.Appearance.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far
        Me.LabelRemainingQtyValue.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelRemainingQtyValue.Location = New System.Drawing.Point(286, 49)
        Me.LabelRemainingQtyValue.Margin = New System.Windows.Forms.Padding(3, 3, 0, 3)
        Me.LabelRemainingQtyValue.Name = "LabelRemainingQtyValue"
        Me.LabelRemainingQtyValue.Size = New System.Drawing.Size(51, 13)
        Me.LabelRemainingQtyValue.TabIndex = 4
        Me.LabelRemainingQtyValue.Text = "0"
        '
        'GroupAvailableStores
        '
        Me.GroupAvailableStores.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupAvailableStores.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupAvailableStores.Appearance.Options.UseFont = True
        Me.GroupAvailableStores.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupAvailableStores.AppearanceCaption.Options.UseFont = True
        Me.GroupAvailableStores.Controls.Add(Me.ButtonAdd)
        Me.GroupAvailableStores.Controls.Add(Me.GridAvailableStores)
        Me.GroupAvailableStores.Controls.Add(Me.LabelSearchAvailableStores)
        Me.GroupAvailableStores.Controls.Add(Me.TextEditSearchAvailableStores)
        Me.GroupAvailableStores.Controls.Add(Me.PictureClearSearchAvailableStores)
        Me.GroupAvailableStores.Controls.Add(Me.PictureAdvancedSearchAvailableStores)
        Me.GroupAvailableStores.Location = New System.Drawing.Point(0, 210)
        Me.GroupAvailableStores.LookAndFeel.SkinName = "Black"
        Me.GroupAvailableStores.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupAvailableStores.Margin = New System.Windows.Forms.Padding(0)
        Me.GroupAvailableStores.Name = "GroupAvailableStores"
        Me.GroupAvailableStores.Size = New System.Drawing.Size(472, 400)
        Me.GroupAvailableStores.TabIndex = 4
        Me.GroupAvailableStores.Text = "List of Available Stores"
        '
        'ButtonAdd
        '
        Me.ButtonAdd.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAdd.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAdd.Appearance.Options.UseFont = True
        Me.ButtonAdd.ImageIndex = 0
        Me.ButtonAdd.ImageList = Me.ImageList16x16
        Me.ButtonAdd.Location = New System.Drawing.Point(5, 372)
        Me.ButtonAdd.LookAndFeel.SkinName = "Black"
        Me.ButtonAdd.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAdd.Name = "ButtonAdd"
        Me.ButtonAdd.Size = New System.Drawing.Size(75, 23)
        Me.ButtonAdd.TabIndex = 2
        Me.ButtonAdd.Text = "Add"
        '
        'GridAvailableStores
        '
        Me.GridAvailableStores.AllowUserToAddRows = False
        Me.GridAvailableStores.AllowUserToDeleteRows = False
        Me.GridAvailableStores.AllowUserToOrderColumns = True
        Me.GridAvailableStores.AllowUserToResizeRows = False
        DataGridViewCellStyle4.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridAvailableStores.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle4
        Me.GridAvailableStores.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridAvailableStores.BackgroundColor = System.Drawing.Color.White
        Me.GridAvailableStores.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridAvailableStores.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridAvailableStores.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle5.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle5.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle5.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle5.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle5.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle5.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle5.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridAvailableStores.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle5
        Me.GridAvailableStores.ColumnHeadersHeight = 22
        Me.GridAvailableStores.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridAvailableStores.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.AvailableStoreNumberColumn, Me.AvailableStoreNameColumn, Me.StoreGroupColumn, Me.AvailableCityNameColumn, Me.AvailableRegionNameColumn, Me.DormantAvailableColumn, Me.MediaNotAllowedAvailableColumn, Me.TakenAvailableColumn, Me.MediaAllowedInCategoryForStore})
        Me.GridAvailableStores.EnableHeadersVisualStyles = False
        Me.GridAvailableStores.GridColor = System.Drawing.Color.White
        Me.GridAvailableStores.Location = New System.Drawing.Point(2, 22)
        Me.GridAvailableStores.Margin = New System.Windows.Forms.Padding(0, 0, 0, 3)
        Me.GridAvailableStores.Name = "GridAvailableStores"
        Me.GridAvailableStores.ReadOnly = True
        Me.GridAvailableStores.RowHeadersVisible = False
        DataGridViewCellStyle6.ForeColor = System.Drawing.Color.DimGray
        Me.GridAvailableStores.RowsDefaultCellStyle = DataGridViewCellStyle6
        Me.GridAvailableStores.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridAvailableStores.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridAvailableStores.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridAvailableStores.RowTemplate.Height = 19
        Me.GridAvailableStores.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridAvailableStores.ShowCellToolTips = False
        Me.GridAvailableStores.Size = New System.Drawing.Size(468, 344)
        Me.GridAvailableStores.StandardTab = True
        Me.GridAvailableStores.TabIndex = 1
        '
        'AvailableStoreNumberColumn
        '
        Me.AvailableStoreNumberColumn.DataPropertyName = "StoreNumber"
        Me.AvailableStoreNumberColumn.HeaderText = "Number"
        Me.AvailableStoreNumberColumn.Name = "AvailableStoreNumberColumn"
        Me.AvailableStoreNumberColumn.ReadOnly = True
        Me.AvailableStoreNumberColumn.Width = 76
        '
        'AvailableStoreNameColumn
        '
        Me.AvailableStoreNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.AvailableStoreNameColumn.DataPropertyName = "StoreName"
        Me.AvailableStoreNameColumn.FillWeight = 40.0!
        Me.AvailableStoreNameColumn.HeaderText = "Name"
        Me.AvailableStoreNameColumn.Name = "AvailableStoreNameColumn"
        Me.AvailableStoreNameColumn.ReadOnly = True
        '
        'StoreGroupColumn
        '
        Me.StoreGroupColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.StoreGroupColumn.DataPropertyName = "StoreGroup"
        Me.StoreGroupColumn.FillWeight = 40.0!
        Me.StoreGroupColumn.HeaderText = "Group"
        Me.StoreGroupColumn.Name = "StoreGroupColumn"
        Me.StoreGroupColumn.ReadOnly = True
        '
        'AvailableCityNameColumn
        '
        Me.AvailableCityNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.AvailableCityNameColumn.DataPropertyName = "CityName"
        Me.AvailableCityNameColumn.FillWeight = 30.0!
        Me.AvailableCityNameColumn.HeaderText = "City"
        Me.AvailableCityNameColumn.Name = "AvailableCityNameColumn"
        Me.AvailableCityNameColumn.ReadOnly = True
        '
        'AvailableRegionNameColumn
        '
        Me.AvailableRegionNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.AvailableRegionNameColumn.DataPropertyName = "RegionName"
        Me.AvailableRegionNameColumn.FillWeight = 30.0!
        Me.AvailableRegionNameColumn.HeaderText = "Region"
        Me.AvailableRegionNameColumn.Name = "AvailableRegionNameColumn"
        Me.AvailableRegionNameColumn.ReadOnly = True
        '
        'DormantAvailableColumn
        '
        Me.DormantAvailableColumn.DataPropertyName = "Dormant"
        Me.DormantAvailableColumn.HeaderText = "Red (Dormant)"
        Me.DormantAvailableColumn.Name = "DormantAvailableColumn"
        Me.DormantAvailableColumn.ReadOnly = True
        Me.DormantAvailableColumn.Visible = False
        '
        'MediaNotAllowedAvailableColumn
        '
        Me.MediaNotAllowedAvailableColumn.DataPropertyName = "MediaNotAllowed"
        Me.MediaNotAllowedAvailableColumn.HeaderText = "Orange (Media Not Allowed)"
        Me.MediaNotAllowedAvailableColumn.Name = "MediaNotAllowedAvailableColumn"
        Me.MediaNotAllowedAvailableColumn.ReadOnly = True
        Me.MediaNotAllowedAvailableColumn.Visible = False
        '
        'TakenAvailableColumn
        '
        Me.TakenAvailableColumn.DataPropertyName = "Taken"
        Me.TakenAvailableColumn.HeaderText = "Yellow (Taken)"
        Me.TakenAvailableColumn.Name = "TakenAvailableColumn"
        Me.TakenAvailableColumn.ReadOnly = True
        Me.TakenAvailableColumn.Visible = False
        '
        'MediaAllowedInCategoryForStore
        '
        Me.MediaAllowedInCategoryForStore.DataPropertyName = "MediaAllowedInCategoryForStore"
        Me.MediaAllowedInCategoryForStore.HeaderText = "Purple (Media Not Allowed In Category)"
        Me.MediaAllowedInCategoryForStore.Name = "MediaAllowedInCategoryForStore"
        Me.MediaAllowedInCategoryForStore.ReadOnly = True
        Me.MediaAllowedInCategoryForStore.Resizable = System.Windows.Forms.DataGridViewTriState.[True]
        Me.MediaAllowedInCategoryForStore.SortMode = System.Windows.Forms.DataGridViewColumnSortMode.Automatic
        Me.MediaAllowedInCategoryForStore.Visible = False
        '
        'LabelSearchAvailableStores
        '
        Me.LabelSearchAvailableStores.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelSearchAvailableStores.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSearchAvailableStores.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelSearchAvailableStores.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelSearchAvailableStores.Location = New System.Drawing.Point(314, 376)
        Me.LabelSearchAvailableStores.Name = "LabelSearchAvailableStores"
        Me.LabelSearchAvailableStores.Size = New System.Drawing.Size(45, 13)
        Me.LabelSearchAvailableStores.TabIndex = 4
        Me.LabelSearchAvailableStores.Text = "Search:"
        '
        'TextEditSearchAvailableStores
        '
        Me.TextEditSearchAvailableStores.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchAvailableStores.EditValue = ""
        Me.TextEditSearchAvailableStores.Location = New System.Drawing.Point(365, 373)
        Me.TextEditSearchAvailableStores.Margin = New System.Windows.Forms.Padding(3, 4, 3, 5)
        Me.TextEditSearchAvailableStores.Name = "TextEditSearchAvailableStores"
        Me.TextEditSearchAvailableStores.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchAvailableStores.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchAvailableStores.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchAvailableStores.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchAvailableStores.Size = New System.Drawing.Size(80, 20)
        Me.TextEditSearchAvailableStores.TabIndex = 5
        '
        'PictureClearSearchAvailableStores
        '
        Me.PictureClearSearchAvailableStores.AllowDrop = True
        Me.PictureClearSearchAvailableStores.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchAvailableStores.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchAvailableStores.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchAvailableStores.Location = New System.Drawing.Point(451, 3)
        Me.PictureClearSearchAvailableStores.Name = "PictureClearSearchAvailableStores"
        Me.PictureClearSearchAvailableStores.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchAvailableStores.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchAvailableStores.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchAvailableStores.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem3.Text = "Clear Search"
        ToolTipItem3.LeftIndent = 6
        ToolTipItem3.Text = "Click here to clear all search boxes."
        SuperToolTip3.Items.Add(ToolTipTitleItem3)
        SuperToolTip3.Items.Add(ToolTipItem3)
        Me.PictureClearSearchAvailableStores.SuperTip = SuperToolTip3
        Me.PictureClearSearchAvailableStores.TabIndex = 0
        Me.PictureClearSearchAvailableStores.TabStop = True
        '
        'PictureAdvancedSearchAvailableStores
        '
        Me.PictureAdvancedSearchAvailableStores.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearchAvailableStores.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearchAvailableStores.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearchAvailableStores.Location = New System.Drawing.Point(451, 375)
        Me.PictureAdvancedSearchAvailableStores.Margin = New System.Windows.Forms.Padding(3, 6, 3, 7)
        Me.PictureAdvancedSearchAvailableStores.Name = "PictureAdvancedSearchAvailableStores"
        Me.PictureAdvancedSearchAvailableStores.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearchAvailableStores.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearchAvailableStores.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearchAvailableStores.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem4.Text = "Advanced Search"
        ToolTipItem4.LeftIndent = 6
        ToolTipItem4.Text = "Click here to search individual column values."
        SuperToolTip4.Items.Add(ToolTipTitleItem4)
        SuperToolTip4.Items.Add(ToolTipItem4)
        Me.PictureAdvancedSearchAvailableStores.SuperTip = SuperToolTip4
        Me.PictureAdvancedSearchAvailableStores.TabIndex = 6
        Me.PictureAdvancedSearchAvailableStores.TabStop = True
        '
        'PanelLegend
        '
        Me.PanelLegend.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelLegend.Controls.Add(Me.TextEditLegendMediaNotAllowedForCategory)
        Me.PanelLegend.Controls.Add(Me.LabelControl1)
        Me.PanelLegend.Controls.Add(Me.TextEditLegendMediaNotAllwed)
        Me.PanelLegend.Controls.Add(Me.LabelLegend)
        Me.PanelLegend.Controls.Add(Me.TextEditLegendTaken)
        Me.PanelLegend.Controls.Add(Me.TextEditLegendDormant)
        Me.PanelLegend.Controls.Add(Me.LabelLegendMediaNotAllwed)
        Me.PanelLegend.Controls.Add(Me.LabelLegendTaken)
        Me.PanelLegend.Controls.Add(Me.LabelLegendDormant)
        Me.PanelLegend.Location = New System.Drawing.Point(0, 112)
        Me.PanelLegend.Margin = New System.Windows.Forms.Padding(0)
        Me.PanelLegend.Name = "PanelLegend"
        Me.PanelLegend.Size = New System.Drawing.Size(472, 98)
        Me.PanelLegend.TabIndex = 2
        '
        'TextEditLegendMediaNotAllowedForCategory
        '
        Me.TextEditLegendMediaNotAllowedForCategory.EditValue = ""
        Me.TextEditLegendMediaNotAllowedForCategory.Enabled = False
        Me.TextEditLegendMediaNotAllowedForCategory.Location = New System.Drawing.Point(1, 80)
        Me.TextEditLegendMediaNotAllowedForCategory.Margin = New System.Windows.Forms.Padding(0, 3, 9, 3)
        Me.TextEditLegendMediaNotAllowedForCategory.Name = "TextEditLegendMediaNotAllowedForCategory"
        Me.TextEditLegendMediaNotAllowedForCategory.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditLegendMediaNotAllowedForCategory.Properties.Appearance.Options.UseFont = True
        Me.TextEditLegendMediaNotAllowedForCategory.Properties.AutoHeight = False
        Me.TextEditLegendMediaNotAllowedForCategory.Size = New System.Drawing.Size(13, 13)
        Me.TextEditLegendMediaNotAllowedForCategory.TabIndex = 8
        '
        'LabelControl1
        '
        Me.LabelControl1.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl1.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl1.Location = New System.Drawing.Point(23, 79)
        Me.LabelControl1.Margin = New System.Windows.Forms.Padding(0, 3, 3, 3)
        Me.LabelControl1.Name = "LabelControl1"
        Me.LabelControl1.Size = New System.Drawing.Size(235, 13)
        Me.LabelControl1.TabIndex = 7
        Me.LabelControl1.Text = "PURPLE: Media Not Allowed For Category"
        '
        'TextEditLegendMediaNotAllwed
        '
        Me.TextEditLegendMediaNotAllwed.EditValue = ""
        Me.TextEditLegendMediaNotAllwed.Enabled = False
        Me.TextEditLegendMediaNotAllwed.Location = New System.Drawing.Point(1, 43)
        Me.TextEditLegendMediaNotAllwed.Margin = New System.Windows.Forms.Padding(0, 3, 3, 3)
        Me.TextEditLegendMediaNotAllwed.Name = "TextEditLegendMediaNotAllwed"
        Me.TextEditLegendMediaNotAllwed.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditLegendMediaNotAllwed.Properties.Appearance.Options.UseFont = True
        Me.TextEditLegendMediaNotAllwed.Properties.AutoHeight = False
        Me.TextEditLegendMediaNotAllwed.Size = New System.Drawing.Size(13, 13)
        Me.TextEditLegendMediaNotAllwed.TabIndex = 6
        '
        'LabelLegend
        '
        Me.LabelLegend.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelLegend.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelLegend.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelLegend.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelLegend.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelLegend.LineVisible = True
        Me.LabelLegend.Location = New System.Drawing.Point(0, 0)
        Me.LabelLegend.Margin = New System.Windows.Forms.Padding(0, 0, 0, 6)
        Me.LabelLegend.Name = "LabelLegend"
        Me.LabelLegend.Size = New System.Drawing.Size(472, 18)
        Me.LabelLegend.TabIndex = 0
        Me.LabelLegend.Text = "Legend"
        '
        'TextEditLegendTaken
        '
        Me.TextEditLegendTaken.EditValue = ""
        Me.TextEditLegendTaken.Enabled = False
        Me.TextEditLegendTaken.Location = New System.Drawing.Point(1, 62)
        Me.TextEditLegendTaken.Margin = New System.Windows.Forms.Padding(0, 3, 9, 3)
        Me.TextEditLegendTaken.Name = "TextEditLegendTaken"
        Me.TextEditLegendTaken.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditLegendTaken.Properties.Appearance.Options.UseFont = True
        Me.TextEditLegendTaken.Properties.AutoHeight = False
        Me.TextEditLegendTaken.Size = New System.Drawing.Size(13, 13)
        Me.TextEditLegendTaken.TabIndex = 6
        '
        'TextEditLegendDormant
        '
        Me.TextEditLegendDormant.EditValue = ""
        Me.TextEditLegendDormant.Enabled = False
        Me.TextEditLegendDormant.Location = New System.Drawing.Point(1, 24)
        Me.TextEditLegendDormant.Margin = New System.Windows.Forms.Padding(0, 3, 9, 3)
        Me.TextEditLegendDormant.Name = "TextEditLegendDormant"
        Me.TextEditLegendDormant.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditLegendDormant.Properties.Appearance.Options.UseFont = True
        Me.TextEditLegendDormant.Properties.AutoHeight = False
        Me.TextEditLegendDormant.Size = New System.Drawing.Size(13, 13)
        Me.TextEditLegendDormant.TabIndex = 6
        '
        'LabelLegendMediaNotAllwed
        '
        Me.LabelLegendMediaNotAllwed.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelLegendMediaNotAllwed.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelLegendMediaNotAllwed.Location = New System.Drawing.Point(23, 42)
        Me.LabelLegendMediaNotAllwed.Margin = New System.Windows.Forms.Padding(0, 3, 3, 3)
        Me.LabelLegendMediaNotAllwed.Name = "LabelLegendMediaNotAllwed"
        Me.LabelLegendMediaNotAllwed.Size = New System.Drawing.Size(376, 13)
        Me.LabelLegendMediaNotAllwed.TabIndex = 1
        Me.LabelLegendMediaNotAllwed.Text = "ORANGE:  Store cannot accommodate the selected media service"
        '
        'LabelLegendTaken
        '
        Me.LabelLegendTaken.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelLegendTaken.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelLegendTaken.Location = New System.Drawing.Point(23, 61)
        Me.LabelLegendTaken.Margin = New System.Windows.Forms.Padding(0, 3, 3, 3)
        Me.LabelLegendTaken.Name = "LabelLegendTaken"
        Me.LabelLegendTaken.Size = New System.Drawing.Size(269, 13)
        Me.LabelLegendTaken.TabIndex = 1
        Me.LabelLegendTaken.Text = "YELLOW: Store is taken by a different store list"
        '
        'LabelLegendDormant
        '
        Me.LabelLegendDormant.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelLegendDormant.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelLegendDormant.Location = New System.Drawing.Point(23, 23)
        Me.LabelLegendDormant.Margin = New System.Windows.Forms.Padding(0, 3, 3, 3)
        Me.LabelLegendDormant.Name = "LabelLegendDormant"
        Me.LabelLegendDormant.Size = New System.Drawing.Size(177, 13)
        Me.LabelLegendDormant.TabIndex = 1
        Me.LabelLegendDormant.Text = "RED:  Store is no longer active"
        '
        'ButtonOK
        '
        Me.ButtonOK.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonOK.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonOK.Appearance.Options.UseFont = True
        Me.ButtonOK.ImageIndex = 2
        Me.ButtonOK.ImageList = Me.ImageList24x24
        Me.ButtonOK.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonOK.Location = New System.Drawing.Point(766, 12)
        Me.ButtonOK.LookAndFeel.SkinName = "Black"
        Me.ButtonOK.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonOK.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonOK.Name = "ButtonOK"
        Me.ButtonOK.Size = New System.Drawing.Size(100, 28)
        Me.ButtonOK.TabIndex = 0
        Me.ButtonOK.Text = "OK"
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.DialogResult = System.Windows.Forms.DialogResult.Cancel
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(872, 12)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(100, 28)
        Me.ButtonCancel.TabIndex = 1
        Me.ButtonCancel.Text = "Cancel"
        '
        'FormEditStoreList
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.CancelButton = Me.ButtonCancel
        Me.ClientSize = New System.Drawing.Size(984, 674)
        Me.Controls.Add(Me.TableLayoutPanel1)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.MinimumSize = New System.Drawing.Size(1000, 712)
        Me.Name = "FormEditStoreList"
        Me.Tag = "1000, 700"
        Me.Text = "Edit Store List"
        Me.Controls.SetChildIndex(Me.TableLayoutPanel1, 0)
        Me.Controls.SetChildIndex(Me.PanelButtonBar, 0)
        CType(Me.PanelButtonBar, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelButtonBar.ResumeLayout(False)
        CType(Me.ErrorManager, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TableLayoutPanel1.ResumeLayout(False)
        Me.PanelIndependentStoreList.ResumeLayout(False)
        CType(Me.GroupStoreList, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupStoreList.ResumeLayout(False)
        CType(Me.GridStoreList, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditSearchStoreList.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearchStoreList.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearchStoreList.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelAvailableStores.ResumeLayout(False)
        Me.PanelAvailableStores.PerformLayout()
        Me.PanelStoreList.ResumeLayout(False)
        Me.PanelStoreList.PerformLayout()
        CType(Me.GroupAvailableStores, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupAvailableStores.ResumeLayout(False)
        CType(Me.GridAvailableStores, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditSearchAvailableStores.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearchAvailableStores.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearchAvailableStores.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.PanelLegend.ResumeLayout(False)
        Me.PanelLegend.PerformLayout()
        CType(Me.TextEditLegendMediaNotAllowedForCategory.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditLegendMediaNotAllwed.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditLegendTaken.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditLegendDormant.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)

    End Sub
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents ImageList16x16 As System.Windows.Forms.ImageList
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents GroupAvailableStores As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelSearchAvailableStores As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditSearchAvailableStores As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearchAvailableStores As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearchAvailableStores As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents LabelInstallStoreQty As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelInstallStoreQtyValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelRemainingUniverseQty As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelRemainingQtyValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelStoreListTitle As DevExpress.XtraEditors.LabelControl
    Friend WithEvents GridAvailableStores As System.Windows.Forms.DataGridView
    Friend WithEvents ButtonOK As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonAdd As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents PanelStoreList As System.Windows.Forms.Panel
    Friend WithEvents GroupStoreList As DevExpress.XtraEditors.GroupControl
    Friend WithEvents ButtonRemove As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GridStoreList As System.Windows.Forms.DataGridView
    Friend WithEvents LabelSearchStoreList As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditSearchStoreList As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearchStoreList As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearchStoreList As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PanelAvailableStores As System.Windows.Forms.Panel
    Friend WithEvents HyperlinkBurst As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkStorePool As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelAvailableStores As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkStoreUniverse As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelDisplayStoresFrom As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelRemainingStorePoolQty As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelRemainingStorePoolQtyValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelRemainingQty As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelRemainingUniverseQtyValue As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditLegendMediaNotAllwed As DevExpress.XtraEditors.TextEdit
    Friend WithEvents TextEditLegendDormant As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelLegend As DevExpress.XtraEditors.LabelControl
    Friend WithEvents PanelLegend As System.Windows.Forms.Panel
    Friend WithEvents LabelLegendMediaNotAllwed As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelLegendDormant As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditLegendTaken As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelLegendTaken As DevExpress.XtraEditors.LabelControl
    Friend WithEvents HyperlinkIndependentStoreList As DevExpress.XtraEditors.LabelControl
    Friend WithEvents PanelIndependentStoreList As System.Windows.Forms.Panel
    Friend WithEvents ButtonNewIndependentStoreList As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonDeleteIndependentStoreList As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents LabelIndependentStoreList As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelIndependentStoreListDescription As DevExpress.XtraEditors.LabelControl
    Friend WithEvents SelectedStoreNumberColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents SelectedStoreNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents SelectedStoreGroupColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents SelectedCityNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents SelectedRegionNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn
    Friend WithEvents DormantListColumn As System.Windows.Forms.DataGridViewCheckBoxColumn
    Friend WithEvents MediaNotAllowedListColumn As System.Windows.Forms.DataGridViewCheckBoxColumn
    Friend WithEvents TakenListColumn As System.Windows.Forms.DataGridViewCheckBoxColumn
    Friend WithEvents TextEditLegendMediaNotAllowedForCategory As TextEdit
    Friend WithEvents LabelControl1 As LabelControl
    Friend WithEvents AvailableStoreNumberColumn As DataGridViewTextBoxColumn
    Friend WithEvents AvailableStoreNameColumn As DataGridViewTextBoxColumn
    Friend WithEvents StoreGroupColumn As DataGridViewTextBoxColumn
    Friend WithEvents AvailableCityNameColumn As DataGridViewTextBoxColumn
    Friend WithEvents AvailableRegionNameColumn As DataGridViewTextBoxColumn
    Friend WithEvents DormantAvailableColumn As DataGridViewCheckBoxColumn
    Friend WithEvents MediaNotAllowedAvailableColumn As DataGridViewCheckBoxColumn
    Friend WithEvents TakenAvailableColumn As DataGridViewCheckBoxColumn
    Friend WithEvents MediaAllowedInCategoryForStore As DataGridViewCheckBoxColumn
End Class
