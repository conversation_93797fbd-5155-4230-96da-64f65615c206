Public Class SubformInvoiceNumber

    Private _DataBindingSource As BindingSource
    Private ParentContract As OldContract
    Private _ContractNumber As String
    Private _GridOfInvoiceNumbers As DataGridView
#Region "Properties"
    Private Property DataBindingSource() As BindingSource
        Get
            Return _DataBindingSource
        End Get
        Set(ByVal value As BindingSource)
            _DataBindingSource = value
        End Set
    End Property
    Private ReadOnly Property Row() As DataRow
        Get
            Return CType(DataBindingSource.Current, DataRowView).Row
        End Get
    End Property
#End Region
#Region "Event Handlers"
    Private Sub ButtonCancel_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonCancel.Click
        DataBindingSource.CancelEdit()
        RevertToParentSubform()
    End Sub
    Private Sub ButtonOK_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ButtonOK.Click
        Save(True)
    End Sub
#End Region
#Region "Public Methods"
    Public Sub New(ByVal Grid As DataGridView, ByVal NewItem As Boolean, ByVal ContractNumber As String)

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.

        ' Get the binding source of the supplied grid.
        DataBindingSource = CType(Grid.DataSource, BindingSource)
        _ContractNumber = ContractNumber
        ' Add a new row to the binding source list if required.
        If NewItem Then
            DataBindingSource.AddNew()
        End If
        _GridOfInvoiceNumbers = Grid
        ' Do data binding.
        TextEditInvoiceNumber.DataBindings.Add("EditValue", DataBindingSource, "InvoiceNumber", False, DataSourceUpdateMode.OnPropertyChanged)
        TextEditInvoiceAmount.DataBindings.Add("EditValue", DataBindingSource, "InvoiceAmount", False, DataSourceUpdateMode.OnPropertyChanged)
        HyperlinkMediaService.DataBindings.Add("Text", DataBindingSource, "MediaName", False, DataSourceUpdateMode.OnValidation)


    End Sub

    Private Sub TextEditInvoiceAmount_EditValueChanged(sender As Object, e As EventArgs) Handles TextEditInvoiceAmount.EditValueChanged
        ' Create an object to represent the control.
        Dim CurrentControl As TextEdit = CType(sender, TextEdit)


        ' Reset the error text of the control to an empty string.
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)
    End Sub

    Private Sub TextEditInvoiceNumber_EditValueChanged(sender As Object, e As EventArgs) Handles TextEditInvoiceNumber.EditValueChanged
        ' Create an object to represent the control.
        Dim CurrentControl As TextEdit = CType(sender, TextEdit)


        ' Reset the error text of the control to an empty string.
        LiquidAgent.ControlValidation(CurrentControl, String.Empty)
    End Sub

    Private Sub TextEditInvoiceNumber_Validated(ByVal sender As Object, ByVal e As System.EventArgs) _
   Handles TextEditInvoiceNumber.Validated

        ' Create an object to represent the control.
        Dim ValidatedControl As TextEdit = CType(sender, TextEdit)

        ' Check for errors.
        If ValidatedControl.Text.Length = 0 Then
            LiquidAgent.ControlValidation(ValidatedControl, "You must enter a cost estimate number.")
            Exit Sub
        Else
            LiquidAgent.ControlValidation(ValidatedControl, String.Empty)
        End If



    End Sub
#End Region
#Region "Protected Methods"
    Protected Overrides Function Save() As Boolean
        DataBindingSource.EndEdit()
        Return True
    End Function

    Private Sub HyperlinkMediaService_Click(sender As Object, e As EventArgs) Handles HyperlinkMediaService.Click
        ' Create an object to represent the control.
        Dim CurrentControl As LabelControl = CType(sender, LabelControl)

        ' Get a selection from the user by displaying the relevant list.
        Dim SelectedItems As List(Of DataRow) = LookupMedia.SelectRowsByContractMedia(My.Settings.DBConnection, False, Nothing, _ContractNumber)

        ' Update the data object and the label with the selection.
        If IsNothing(SelectedItems) = False Then
            If SelectedItems.Count > 0 Then
                'DataObject.principal_id = SelectedItems(0).Item("principal_id")
                'DataObject.Username = SelectedItems(0).Item("name")
                CurrentControl.Text = SelectedItems(0).Item("MediaName").ToString
                _MediaId = SelectedItems(0).Item("MediaId")
                _GridOfInvoiceNumbers.CurrentRow.Cells("MediaIdInvoices").Value = _MediaId
                ' Reset the error text of the control to an empty string.
                LiquidAgent.ControlValidation(CurrentControl, String.Empty)
            End If
        End If
    End Sub
#End Region

#Region "Private Methods"
#End Region

End Class