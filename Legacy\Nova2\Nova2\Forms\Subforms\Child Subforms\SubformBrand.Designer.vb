<Global.Microsoft.VisualBasic.CompilerServices.DesignerGenerated()> _
Partial Class SubformBrand
    Inherits LiquidShell.Subform

    'Form overrides dispose to clean up the component list.
    <System.Diagnostics.DebuggerNonUserCode()> _
    Protected Overrides Sub Dispose(ByVal disposing As Boolean)
        If disposing AndAlso components IsNot Nothing Then
            components.Dispose()
        End If
        MyBase.Dispose(disposing)
    End Sub

    'Required by the Windows Form Designer
    Private components As System.ComponentModel.IContainer

    'NOTE: The following procedure is required by the Windows Form Designer
    'It can be modified using the Windows Form Designer. 
    'Do not modify it using the code editor.
    <System.Diagnostics.DebuggerStepThrough()> _
    Private Sub InitializeComponent()
        Me.components = New System.ComponentModel.Container()
        Dim resources As System.ComponentModel.ComponentResourceManager = New System.ComponentModel.ComponentResourceManager(GetType(SubformBrand))
        Dim SuperToolTip1 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem1 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem1 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim SuperToolTip2 As DevExpress.Utils.SuperToolTip = New DevExpress.Utils.SuperToolTip()
        Dim ToolTipTitleItem2 As DevExpress.Utils.ToolTipTitleItem = New DevExpress.Utils.ToolTipTitleItem()
        Dim ToolTipItem2 As DevExpress.Utils.ToolTipItem = New DevExpress.Utils.ToolTipItem()
        Dim DataGridViewCellStyle1 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle2 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Dim DataGridViewCellStyle3 As System.Windows.Forms.DataGridViewCellStyle = New System.Windows.Forms.DataGridViewCellStyle()
        Me.ImageList16x16 = New System.Windows.Forms.ImageList(Me.components)
        Me.ImageList24x24 = New System.Windows.Forms.ImageList(Me.components)
        Me.ButtonSave = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonCancel = New DevExpress.XtraEditors.SimpleButton()
        Me.XtraTabControl1 = New DevExpress.XtraTab.XtraTabControl()
        Me.TabPageDetails = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel1 = New System.Windows.Forms.Panel()
        Me.PanelDetails = New System.Windows.Forms.Panel()
        Me.CheckEditDormant = New DevExpress.XtraEditors.CheckEdit()
        Me.TextEditBrandName = New DevExpress.XtraEditors.TextEdit()
        Me.LabelCategoryName = New DevExpress.XtraEditors.LabelControl()
        Me.TabPageClients = New DevExpress.XtraTab.XtraTabPage()
        Me.Panel3 = New System.Windows.Forms.Panel()
        Me.TableLayoutPanel1 = New System.Windows.Forms.TableLayoutPanel()
        Me.GroupControlClientBrand = New DevExpress.XtraEditors.GroupControl()
        Me.LabelSearchClientBrand = New DevExpress.XtraEditors.LabelControl()
        Me.TextEditSearchClientBrand = New DevExpress.XtraEditors.TextEdit()
        Me.PictureClearSearchClientBrand = New DevExpress.XtraEditors.PictureEdit()
        Me.PictureAdvancedSearchClientBrand = New DevExpress.XtraEditors.PictureEdit()
        Me.ButtonRemoveClientBrand = New DevExpress.XtraEditors.SimpleButton()
        Me.ButtonAddClientBrand = New DevExpress.XtraEditors.SimpleButton()
        Me.GridClientBrand = New System.Windows.Forms.DataGridView()
        Me.ClientNameColumn = New System.Windows.Forms.DataGridViewTextBoxColumn()
        Me.LabelControl9 = New DevExpress.XtraEditors.LabelControl()
        Me.LabelControl10 = New DevExpress.XtraEditors.LabelControl()
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.XtraTabControl1.SuspendLayout()
        Me.TabPageDetails.SuspendLayout()
        Me.Panel1.SuspendLayout()
        Me.PanelDetails.SuspendLayout()
        CType(Me.CheckEditDormant.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.TextEditBrandName.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.TabPageClients.SuspendLayout()
        Me.Panel3.SuspendLayout()
        Me.TableLayoutPanel1.SuspendLayout()
        CType(Me.GroupControlClientBrand, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.GroupControlClientBrand.SuspendLayout()
        CType(Me.TextEditSearchClientBrand.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureClearSearchClientBrand.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.PictureAdvancedSearchClientBrand.Properties, System.ComponentModel.ISupportInitialize).BeginInit()
        CType(Me.GridClientBrand, System.ComponentModel.ISupportInitialize).BeginInit()
        Me.SuspendLayout()
        '
        'LabelTitle
        '
        Me.LabelTitle.Appearance.Font = New System.Drawing.Font("Century Gothic", 18.0!)
        Me.LabelTitle.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelTitle.Size = New System.Drawing.Size(145, 30)
        Me.LabelTitle.TabIndex = 0
        Me.LabelTitle.Text = "(new brand)"
        '
        'ImageList16x16
        '
        Me.ImageList16x16.ImageStream = CType(resources.GetObject("ImageList16x16.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList16x16.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList16x16.Images.SetKeyName(0, "add.png")
        Me.ImageList16x16.Images.SetKeyName(1, "pencil.png")
        Me.ImageList16x16.Images.SetKeyName(2, "delete.png")
        '
        'ImageList24x24
        '
        Me.ImageList24x24.ImageStream = CType(resources.GetObject("ImageList24x24.ImageStream"), System.Windows.Forms.ImageListStreamer)
        Me.ImageList24x24.TransparentColor = System.Drawing.Color.Transparent
        Me.ImageList24x24.Images.SetKeyName(0, "delete.png")
        Me.ImageList24x24.Images.SetKeyName(1, "save.png")
        Me.ImageList24x24.Images.SetKeyName(2, "accept.png")
        '
        'ButtonSave
        '
        Me.ButtonSave.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonSave.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonSave.Appearance.Options.UseFont = True
        Me.ButtonSave.ImageIndex = 1
        Me.ButtonSave.ImageList = Me.ImageList24x24
        Me.ButtonSave.ImageLocation = DevExpress.XtraEditors.ImageLocation.MiddleLeft
        Me.ButtonSave.Location = New System.Drawing.Point(605, 509)
        Me.ButtonSave.LookAndFeel.SkinName = "Black"
        Me.ButtonSave.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonSave.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonSave.Name = "ButtonSave"
        Me.ButtonSave.Size = New System.Drawing.Size(100, 28)
        Me.ButtonSave.TabIndex = 2
        Me.ButtonSave.Text = "Save"
        '
        'ButtonCancel
        '
        Me.ButtonCancel.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.ButtonCancel.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonCancel.Appearance.Options.UseFont = True
        Me.ButtonCancel.ImageIndex = 0
        Me.ButtonCancel.ImageList = Me.ImageList24x24
        Me.ButtonCancel.Location = New System.Drawing.Point(711, 509)
        Me.ButtonCancel.LookAndFeel.SkinName = "Black"
        Me.ButtonCancel.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonCancel.Margin = New System.Windows.Forms.Padding(3, 12, 3, 3)
        Me.ButtonCancel.Name = "ButtonCancel"
        Me.ButtonCancel.Size = New System.Drawing.Size(100, 28)
        Me.ButtonCancel.TabIndex = 3
        Me.ButtonCancel.Text = "Cancel"
        '
        'XtraTabControl1
        '
        Me.XtraTabControl1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.XtraTabControl1.AppearancePage.Header.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.XtraTabControl1.AppearancePage.Header.Options.UseFont = True
        Me.XtraTabControl1.Location = New System.Drawing.Point(12, 57)
        Me.XtraTabControl1.LookAndFeel.SkinName = "Black"
        Me.XtraTabControl1.LookAndFeel.UseDefaultLookAndFeel = False
        Me.XtraTabControl1.Name = "XtraTabControl1"
        Me.XtraTabControl1.SelectedTabPage = Me.TabPageDetails
        Me.XtraTabControl1.Size = New System.Drawing.Size(799, 437)
        Me.XtraTabControl1.TabIndex = 1
        Me.XtraTabControl1.TabPages.AddRange(New DevExpress.XtraTab.XtraTabPage() {Me.TabPageDetails, Me.TabPageClients})
        '
        'TabPageDetails
        '
        Me.TabPageDetails.Controls.Add(Me.Panel1)
        Me.TabPageDetails.Name = "TabPageDetails"
        Me.TabPageDetails.Size = New System.Drawing.Size(793, 409)
        Me.TabPageDetails.Text = "Details"
        '
        'Panel1
        '
        Me.Panel1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Panel1.Controls.Add(Me.PanelDetails)
        Me.Panel1.Location = New System.Drawing.Point(3, 3)
        Me.Panel1.Name = "Panel1"
        Me.Panel1.Size = New System.Drawing.Size(787, 403)
        Me.Panel1.TabIndex = 0
        '
        'PanelDetails
        '
        Me.PanelDetails.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PanelDetails.Controls.Add(Me.CheckEditDormant)
        Me.PanelDetails.Controls.Add(Me.TextEditBrandName)
        Me.PanelDetails.Controls.Add(Me.LabelCategoryName)
        Me.PanelDetails.Location = New System.Drawing.Point(12, 12)
        Me.PanelDetails.Margin = New System.Windows.Forms.Padding(12)
        Me.PanelDetails.Name = "PanelDetails"
        Me.PanelDetails.Size = New System.Drawing.Size(763, 379)
        Me.PanelDetails.TabIndex = 0
        '
        'CheckEditDormant
        '
        Me.CheckEditDormant.Location = New System.Drawing.Point(120, 29)
        Me.CheckEditDormant.Name = "CheckEditDormant"
        Me.CheckEditDormant.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.CheckEditDormant.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.CheckEditDormant.Properties.Appearance.Options.UseFont = True
        Me.CheckEditDormant.Properties.Appearance.Options.UseForeColor = True
        Me.CheckEditDormant.Properties.AutoWidth = True
        Me.CheckEditDormant.Properties.Caption = "This brand is dormant"
        Me.CheckEditDormant.Properties.LookAndFeel.SkinName = "Black"
        Me.CheckEditDormant.Properties.LookAndFeel.UseDefaultLookAndFeel = False
        Me.CheckEditDormant.Size = New System.Drawing.Size(148, 19)
        Me.CheckEditDormant.TabIndex = 2
        '
        'TextEditBrandName
        '
        Me.TextEditBrandName.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditBrandName.Location = New System.Drawing.Point(122, 3)
        Me.TextEditBrandName.Name = "TextEditBrandName"
        Me.TextEditBrandName.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditBrandName.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditBrandName.Properties.Appearance.Options.UseFont = True
        Me.TextEditBrandName.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditBrandName.Properties.MaxLength = 200
        Me.TextEditBrandName.Size = New System.Drawing.Size(390, 20)
        Me.TextEditBrandName.TabIndex = 1
        '
        'LabelCategoryName
        '
        Me.LabelCategoryName.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelCategoryName.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelCategoryName.Location = New System.Drawing.Point(3, 6)
        Me.LabelCategoryName.Name = "LabelCategoryName"
        Me.LabelCategoryName.Size = New System.Drawing.Size(76, 13)
        Me.LabelCategoryName.TabIndex = 0
        Me.LabelCategoryName.Text = "Brand Name:"
        '
        'TabPageClients
        '
        Me.TabPageClients.Controls.Add(Me.Panel3)
        Me.TabPageClients.Name = "TabPageClients"
        Me.TabPageClients.Size = New System.Drawing.Size(793, 409)
        Me.TabPageClients.Text = "Clients"
        '
        'Panel3
        '
        Me.Panel3.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.Panel3.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Panel3.Controls.Add(Me.TableLayoutPanel1)
        Me.Panel3.Location = New System.Drawing.Point(3, 3)
        Me.Panel3.Name = "Panel3"
        Me.Panel3.Size = New System.Drawing.Size(787, 403)
        Me.Panel3.TabIndex = 0
        '
        'TableLayoutPanel1
        '
        Me.TableLayoutPanel1.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TableLayoutPanel1.BackColor = System.Drawing.Color.Transparent
        Me.TableLayoutPanel1.ColumnCount = 1
        Me.TableLayoutPanel1.ColumnStyles.Add(New System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.Controls.Add(Me.GroupControlClientBrand, 0, 2)
        Me.TableLayoutPanel1.Controls.Add(Me.LabelControl9, 0, 0)
        Me.TableLayoutPanel1.Controls.Add(Me.LabelControl10, 0, 1)
        Me.TableLayoutPanel1.Location = New System.Drawing.Point(12, 12)
        Me.TableLayoutPanel1.Margin = New System.Windows.Forms.Padding(12)
        Me.TableLayoutPanel1.Name = "TableLayoutPanel1"
        Me.TableLayoutPanel1.RowCount = 3
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle())
        Me.TableLayoutPanel1.RowStyles.Add(New System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 100.0!))
        Me.TableLayoutPanel1.Size = New System.Drawing.Size(763, 379)
        Me.TableLayoutPanel1.TabIndex = 0
        '
        'GroupControlClientBrand
        '
        Me.GroupControlClientBrand.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GroupControlClientBrand.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlClientBrand.Appearance.Options.UseFont = True
        Me.GroupControlClientBrand.AppearanceCaption.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.GroupControlClientBrand.AppearanceCaption.Options.UseFont = True
        Me.GroupControlClientBrand.Controls.Add(Me.LabelSearchClientBrand)
        Me.GroupControlClientBrand.Controls.Add(Me.TextEditSearchClientBrand)
        Me.GroupControlClientBrand.Controls.Add(Me.PictureClearSearchClientBrand)
        Me.GroupControlClientBrand.Controls.Add(Me.PictureAdvancedSearchClientBrand)
        Me.GroupControlClientBrand.Controls.Add(Me.ButtonRemoveClientBrand)
        Me.GroupControlClientBrand.Controls.Add(Me.ButtonAddClientBrand)
        Me.GroupControlClientBrand.Controls.Add(Me.GridClientBrand)
        Me.GroupControlClientBrand.Location = New System.Drawing.Point(3, 77)
        Me.GroupControlClientBrand.LookAndFeel.SkinName = "Black"
        Me.GroupControlClientBrand.LookAndFeel.UseDefaultLookAndFeel = False
        Me.GroupControlClientBrand.Name = "GroupControlClientBrand"
        Me.GroupControlClientBrand.Size = New System.Drawing.Size(757, 299)
        Me.GroupControlClientBrand.TabIndex = 2
        Me.GroupControlClientBrand.Text = "Client List"
        '
        'LabelSearchClientBrand
        '
        Me.LabelSearchClientBrand.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelSearchClientBrand.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelSearchClientBrand.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelSearchClientBrand.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelSearchClientBrand.Location = New System.Drawing.Point(599, 275)
        Me.LabelSearchClientBrand.Name = "LabelSearchClientBrand"
        Me.LabelSearchClientBrand.Size = New System.Drawing.Size(45, 13)
        Me.LabelSearchClientBrand.TabIndex = 4
        Me.LabelSearchClientBrand.Text = "Search:"
        '
        'TextEditSearchClientBrand
        '
        Me.TextEditSearchClientBrand.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.TextEditSearchClientBrand.EditValue = ""
        Me.TextEditSearchClientBrand.Location = New System.Drawing.Point(650, 272)
        Me.TextEditSearchClientBrand.Margin = New System.Windows.Forms.Padding(3, 4, 3, 5)
        Me.TextEditSearchClientBrand.Name = "TextEditSearchClientBrand"
        Me.TextEditSearchClientBrand.Properties.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.TextEditSearchClientBrand.Properties.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.TextEditSearchClientBrand.Properties.Appearance.Options.UseFont = True
        Me.TextEditSearchClientBrand.Properties.Appearance.Options.UseForeColor = True
        Me.TextEditSearchClientBrand.Size = New System.Drawing.Size(80, 20)
        Me.TextEditSearchClientBrand.TabIndex = 5
        '
        'PictureClearSearchClientBrand
        '
        Me.PictureClearSearchClientBrand.Anchor = CType((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureClearSearchClientBrand.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureClearSearchClientBrand.EditValue = Global.Nova2.My.Resources.Resources.delete16
        Me.PictureClearSearchClientBrand.Location = New System.Drawing.Point(736, 3)
        Me.PictureClearSearchClientBrand.Name = "PictureClearSearchClientBrand"
        Me.PictureClearSearchClientBrand.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureClearSearchClientBrand.Properties.Appearance.Options.UseBackColor = True
        Me.PictureClearSearchClientBrand.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureClearSearchClientBrand.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem1.Text = "Clear Search"
        ToolTipItem1.LeftIndent = 6
        ToolTipItem1.Text = "Click here to clear all search boxes."
        SuperToolTip1.Items.Add(ToolTipTitleItem1)
        SuperToolTip1.Items.Add(ToolTipItem1)
        Me.PictureClearSearchClientBrand.SuperTip = SuperToolTip1
        Me.PictureClearSearchClientBrand.TabIndex = 0
        Me.PictureClearSearchClientBrand.TabStop = True
        '
        'PictureAdvancedSearchClientBrand
        '
        Me.PictureAdvancedSearchClientBrand.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.PictureAdvancedSearchClientBrand.Cursor = System.Windows.Forms.Cursors.Hand
        Me.PictureAdvancedSearchClientBrand.EditValue = Global.Nova2.My.Resources.Resources.search16
        Me.PictureAdvancedSearchClientBrand.Location = New System.Drawing.Point(736, 274)
        Me.PictureAdvancedSearchClientBrand.Margin = New System.Windows.Forms.Padding(3, 6, 3, 7)
        Me.PictureAdvancedSearchClientBrand.Name = "PictureAdvancedSearchClientBrand"
        Me.PictureAdvancedSearchClientBrand.Properties.Appearance.BackColor = System.Drawing.Color.Transparent
        Me.PictureAdvancedSearchClientBrand.Properties.Appearance.Options.UseBackColor = True
        Me.PictureAdvancedSearchClientBrand.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder
        Me.PictureAdvancedSearchClientBrand.Size = New System.Drawing.Size(16, 16)
        ToolTipTitleItem2.Text = "Advanced Search"
        ToolTipItem2.LeftIndent = 6
        ToolTipItem2.Text = "Click here to search individual column values."
        SuperToolTip2.Items.Add(ToolTipTitleItem2)
        SuperToolTip2.Items.Add(ToolTipItem2)
        Me.PictureAdvancedSearchClientBrand.SuperTip = SuperToolTip2
        Me.PictureAdvancedSearchClientBrand.TabIndex = 6
        Me.PictureAdvancedSearchClientBrand.TabStop = True
        '
        'ButtonRemoveClientBrand
        '
        Me.ButtonRemoveClientBrand.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonRemoveClientBrand.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonRemoveClientBrand.Appearance.Options.UseFont = True
        Me.ButtonRemoveClientBrand.ImageIndex = 2
        Me.ButtonRemoveClientBrand.ImageList = Me.ImageList16x16
        Me.ButtonRemoveClientBrand.Location = New System.Drawing.Point(86, 271)
        Me.ButtonRemoveClientBrand.LookAndFeel.SkinName = "Black"
        Me.ButtonRemoveClientBrand.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonRemoveClientBrand.Name = "ButtonRemoveClientBrand"
        Me.ButtonRemoveClientBrand.Size = New System.Drawing.Size(75, 23)
        Me.ButtonRemoveClientBrand.TabIndex = 3
        Me.ButtonRemoveClientBrand.Text = "Remove"
        '
        'ButtonAddClientBrand
        '
        Me.ButtonAddClientBrand.Anchor = CType((System.Windows.Forms.AnchorStyles.Bottom Or System.Windows.Forms.AnchorStyles.Left), System.Windows.Forms.AnchorStyles)
        Me.ButtonAddClientBrand.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.ButtonAddClientBrand.Appearance.Options.UseFont = True
        Me.ButtonAddClientBrand.ImageIndex = 0
        Me.ButtonAddClientBrand.ImageList = Me.ImageList16x16
        Me.ButtonAddClientBrand.Location = New System.Drawing.Point(5, 271)
        Me.ButtonAddClientBrand.LookAndFeel.SkinName = "Black"
        Me.ButtonAddClientBrand.LookAndFeel.UseDefaultLookAndFeel = False
        Me.ButtonAddClientBrand.Name = "ButtonAddClientBrand"
        Me.ButtonAddClientBrand.Size = New System.Drawing.Size(75, 23)
        Me.ButtonAddClientBrand.TabIndex = 2
        Me.ButtonAddClientBrand.Text = "Add"
        '
        'GridClientBrand
        '
        Me.GridClientBrand.AllowUserToAddRows = False
        Me.GridClientBrand.AllowUserToDeleteRows = False
        Me.GridClientBrand.AllowUserToOrderColumns = True
        Me.GridClientBrand.AllowUserToResizeRows = False
        DataGridViewCellStyle1.BackColor = System.Drawing.Color.WhiteSmoke
        Me.GridClientBrand.AlternatingRowsDefaultCellStyle = DataGridViewCellStyle1
        Me.GridClientBrand.Anchor = CType((((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Bottom) _
            Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.GridClientBrand.BackgroundColor = System.Drawing.Color.White
        Me.GridClientBrand.BorderStyle = System.Windows.Forms.BorderStyle.None
        Me.GridClientBrand.ClipboardCopyMode = System.Windows.Forms.DataGridViewClipboardCopyMode.EnableAlwaysIncludeHeaderText
        Me.GridClientBrand.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.[Single]
        DataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft
        DataGridViewCellStyle2.BackColor = System.Drawing.Color.Gray
        DataGridViewCellStyle2.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        DataGridViewCellStyle2.ForeColor = System.Drawing.Color.White
        DataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight
        DataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText
        DataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.[True]
        Me.GridClientBrand.ColumnHeadersDefaultCellStyle = DataGridViewCellStyle2
        Me.GridClientBrand.ColumnHeadersHeight = 22
        Me.GridClientBrand.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing
        Me.GridClientBrand.ColumnHeadersVisible = False
        Me.GridClientBrand.Columns.AddRange(New System.Windows.Forms.DataGridViewColumn() {Me.ClientNameColumn})
        Me.GridClientBrand.EnableHeadersVisualStyles = False
        Me.GridClientBrand.GridColor = System.Drawing.Color.White
        Me.GridClientBrand.Location = New System.Drawing.Point(2, 22)
        Me.GridClientBrand.Name = "GridClientBrand"
        Me.GridClientBrand.ReadOnly = True
        Me.GridClientBrand.RowHeadersVisible = False
        DataGridViewCellStyle3.ForeColor = System.Drawing.Color.DimGray
        Me.GridClientBrand.RowsDefaultCellStyle = DataGridViewCellStyle3
        Me.GridClientBrand.RowTemplate.DefaultCellStyle.ForeColor = System.Drawing.Color.DimGray
        Me.GridClientBrand.RowTemplate.DefaultCellStyle.SelectionBackColor = System.Drawing.Color.LightSteelBlue
        Me.GridClientBrand.RowTemplate.DefaultCellStyle.SelectionForeColor = System.Drawing.Color.Black
        Me.GridClientBrand.RowTemplate.Height = 19
        Me.GridClientBrand.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect
        Me.GridClientBrand.ShowCellToolTips = False
        Me.GridClientBrand.Size = New System.Drawing.Size(753, 243)
        Me.GridClientBrand.StandardTab = True
        Me.GridClientBrand.TabIndex = 1
        '
        'ClientNameColumn
        '
        Me.ClientNameColumn.AutoSizeMode = System.Windows.Forms.DataGridViewAutoSizeColumnMode.Fill
        Me.ClientNameColumn.DataPropertyName = "ClientName"
        Me.ClientNameColumn.HeaderText = "Client"
        Me.ClientNameColumn.Name = "ClientNameColumn"
        Me.ClientNameColumn.ReadOnly = True
        '
        'LabelControl9
        '
        Me.LabelControl9.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl9.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl9.Appearance.ForeColor = System.Drawing.Color.SteelBlue
        Me.LabelControl9.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.None
        Me.LabelControl9.LineLocation = DevExpress.XtraEditors.LineLocation.Bottom
        Me.LabelControl9.LineVisible = True
        Me.LabelControl9.Location = New System.Drawing.Point(3, 3)
        Me.LabelControl9.Margin = New System.Windows.Forms.Padding(3, 3, 3, 12)
        Me.LabelControl9.Name = "LabelControl9"
        Me.LabelControl9.Size = New System.Drawing.Size(757, 18)
        Me.LabelControl9.TabIndex = 0
        Me.LabelControl9.Text = "Brand Client Management"
        '
        'LabelControl10
        '
        Me.LabelControl10.Anchor = CType(((System.Windows.Forms.AnchorStyles.Top Or System.Windows.Forms.AnchorStyles.Left) _
            Or System.Windows.Forms.AnchorStyles.Right), System.Windows.Forms.AnchorStyles)
        Me.LabelControl10.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.LabelControl10.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.LabelControl10.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Top
        Me.LabelControl10.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap
        Me.LabelControl10.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical
        Me.LabelControl10.Location = New System.Drawing.Point(3, 36)
        Me.LabelControl10.Margin = New System.Windows.Forms.Padding(3, 3, 3, 12)
        Me.LabelControl10.Name = "LabelControl10"
        Me.LabelControl10.Size = New System.Drawing.Size(757, 26)
        Me.LabelControl10.TabIndex = 1
        Me.LabelControl10.Text = "Here you can specify which clients can use this brand. At least one client must " & _
    "be listed here, otherwise this brand will be floating around in the system witho" & _
    "ut a home. Nobody wants that."
        '
        'SubformBrand
        '
        Me.Appearance.BackColor = System.Drawing.Color.WhiteSmoke
        Me.Appearance.Font = New System.Drawing.Font("Verdana", 8.25!, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, CType(0, Byte))
        Me.Appearance.ForeColor = System.Drawing.Color.DimGray
        Me.Appearance.Options.UseBackColor = True
        Me.Appearance.Options.UseFont = True
        Me.Appearance.Options.UseForeColor = True
        Me.AutoScaleDimensions = New System.Drawing.SizeF(7.0!, 13.0!)
        Me.Controls.Add(Me.XtraTabControl1)
        Me.Controls.Add(Me.ButtonSave)
        Me.Controls.Add(Me.ButtonCancel)
        Me.LookAndFeel.SkinName = "Black"
        Me.LookAndFeel.UseDefaultLookAndFeel = False
        Me.Name = "SubformBrand"
        Me.Controls.SetChildIndex(Me.LabelTitle, 0)
        Me.Controls.SetChildIndex(Me.ButtonCancel, 0)
        Me.Controls.SetChildIndex(Me.ButtonSave, 0)
        Me.Controls.SetChildIndex(Me.XtraTabControl1, 0)
        CType(Me.XtraTabControl1, System.ComponentModel.ISupportInitialize).EndInit()
        Me.XtraTabControl1.ResumeLayout(False)
        Me.TabPageDetails.ResumeLayout(False)
        Me.Panel1.ResumeLayout(False)
        Me.PanelDetails.ResumeLayout(False)
        Me.PanelDetails.PerformLayout()
        CType(Me.CheckEditDormant.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.TextEditBrandName.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        Me.TabPageClients.ResumeLayout(False)
        Me.Panel3.ResumeLayout(False)
        Me.TableLayoutPanel1.ResumeLayout(False)
        CType(Me.GroupControlClientBrand, System.ComponentModel.ISupportInitialize).EndInit()
        Me.GroupControlClientBrand.ResumeLayout(False)
        CType(Me.TextEditSearchClientBrand.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureClearSearchClientBrand.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.PictureAdvancedSearchClientBrand.Properties, System.ComponentModel.ISupportInitialize).EndInit()
        CType(Me.GridClientBrand, System.ComponentModel.ISupportInitialize).EndInit()
        Me.ResumeLayout(False)
        Me.PerformLayout()

    End Sub
    Friend WithEvents ImageList16x16 As System.Windows.Forms.ImageList
    Friend WithEvents ImageList24x24 As System.Windows.Forms.ImageList
    Friend WithEvents ButtonSave As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonCancel As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents XtraTabControl1 As DevExpress.XtraTab.XtraTabControl
    Friend WithEvents TabPageDetails As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel1 As System.Windows.Forms.Panel
    Friend WithEvents PanelDetails As System.Windows.Forms.Panel
    Friend WithEvents CheckEditDormant As DevExpress.XtraEditors.CheckEdit
    Friend WithEvents TextEditBrandName As DevExpress.XtraEditors.TextEdit
    Friend WithEvents LabelCategoryName As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TabPageClients As DevExpress.XtraTab.XtraTabPage
    Friend WithEvents Panel3 As System.Windows.Forms.Panel
    Friend WithEvents TableLayoutPanel1 As System.Windows.Forms.TableLayoutPanel
    Friend WithEvents GroupControlClientBrand As DevExpress.XtraEditors.GroupControl
    Friend WithEvents LabelSearchClientBrand As DevExpress.XtraEditors.LabelControl
    Friend WithEvents TextEditSearchClientBrand As DevExpress.XtraEditors.TextEdit
    Friend WithEvents PictureClearSearchClientBrand As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents PictureAdvancedSearchClientBrand As DevExpress.XtraEditors.PictureEdit
    Friend WithEvents ButtonRemoveClientBrand As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents ButtonAddClientBrand As DevExpress.XtraEditors.SimpleButton
    Friend WithEvents GridClientBrand As System.Windows.Forms.DataGridView
    Friend WithEvents LabelControl9 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents LabelControl10 As DevExpress.XtraEditors.LabelControl
    Friend WithEvents ClientNameColumn As System.Windows.Forms.DataGridViewTextBoxColumn

End Class
