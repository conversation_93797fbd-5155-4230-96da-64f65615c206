Public Class LookupCategory

    Public Sub New(ByVal GridData As BindingSource, ByVal AllowMultiSelect As Boolean)

        ' This call is required by the Windows Form Designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        GridController = New GridManager _
        (GridItems, _
        TextEditSearch, _
        GridData, _
        My.Settings.DBConnection, _
        PictureAdvancedSearch, _
        PictureClearSearch, _
        Nothing, _
        Nothing)

        ' Apply multi-select restrictions if necessary.
        GridItems.MultiSelect = AllowMultiSelect

    End Sub

    Public Overloads Shared Function SelectRows _
    (ByVal ConnectionString As String, _
    ByVal AllowMultiSelect As Boolean) _
    As List(Of DataRow)

        Return SelectRows(ConnectionString, AllowMultiSelect, Nothing)

    End Function

    Public Overloads Shared Function SelectRows _
    (ByVal ConnectionString As String,
    ByVal AllowMultiSelect As Boolean,
    ByVal RowsToExclude As Object,
     Optional ByVal isCrossover As Boolean = False) _
    As List(Of DataRow)

        ' A string variable to hold any potential errors during data retrieval.
        Dim ErrorMessage As String = String.Empty

        ' Create a data source for the list.
        Dim GridData As BindingSource = Nothing
        If IsNothing(RowsToExclude) Then
            GridData = Lookup.GetCategoriesExcludingTable(ConnectionString, ErrorMessage, Nothing, isCrossover)
        ElseIf TypeOf RowsToExclude Is DataTable Then
            GridData = Lookup.GetCategoriesExcludingTable(ConnectionString, ErrorMessage, RowsToExclude, isCrossover)
        ElseIf TypeOf RowsToExclude Is DataGridView Then
            GridData = Lookup.GetCategoriesExcludingGrid(ConnectionString, ErrorMessage, RowsToExclude, isCrossover)
        End If

        ' Create an instance of this lookup list form and return the user-selected rows.
        Using Selector As New LookupCategory(GridData, AllowMultiSelect)
            Return SelectRows(Selector, ErrorMessage)
        End Using

    End Function

    Public Overloads Shared Function SelectRowsByMediaService _
    (ByVal ConnectionString As String, _
    ByVal AllowMultiSelect As Boolean, _
    ByVal GridToExclude As DataGridView, _
    ByVal MediaServiceID As Integer) _
    As List(Of DataRow)

        ' A string variable to hold any potential errors during data retrieval.
        Dim ErrorMessage As String = String.Empty

        ' Create a data source for the list.
        Dim GridData As BindingSource = Lookup.GetCategoriesByMediaService(ConnectionString, ErrorMessage, GridToExclude, MediaServiceID)

        ' Create an instance of this lookup list form and return the user-selected rows.
        Using Selector As New LookupCategory(GridData, AllowMultiSelect)
            Return SelectRows(Selector, ErrorMessage)
        End Using

    End Function

End Class
