<?xml version="1.0"?>
<doc>
    <assembly>
        <name>DevExpress.Utils.v12.2</name>
    </assembly>
    <members>
        <member name="T:DevExpress.Utils.SharedImageCollectionImageSizeMode">

            <summary>
                <para>The enumeration specifies the size of images being displayed within an object, when images are obtained from a <see cref="T:DevExpress.Utils.SharedImageCollection"/>.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Utils.SharedImageCollectionImageSizeMode.UseCollectionImageSize">
            <summary>
                <para>The size of an image being displayed by an object is determined by the <b>ImageSize</b> property of the bound <see cref="T:DevExpress.Utils.SharedImageCollection"/>.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.SharedImageCollectionImageSizeMode.UseImageSize">
            <summary>
                <para>The size of an image being displayed by an object is determined by the size of the corresponding Image item in the bound <see cref="T:DevExpress.Utils.SharedImageCollection"/>.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.Controls.IXtraResizableControl">

            <summary>
                <para>Defines an interface a control that can be implemented to provide layout information to an XtraLayoutControl.

</para>
            </summary>

        </member>
        <member name="E:DevExpress.Utils.Controls.IXtraResizableControl.Changed">
            <summary>
                <para>The control that implements this interface must fire the <b>Changed</b> event when specific settings that affect the layout information have been changed, and the XtraLayoutControl must update the layout as a result.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.Utils.Controls.IXtraResizableControl.IsCaptionVisible">
            <summary>
                <para>When implemented by a control, specifies whether a text label should be visible by default when this control is added to an XtraLayoutControl.
</para>
            </summary>
            <value><b>true</b> if a text label should be visible by default when the control is added to an XtraLayoutControl; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Utils.Controls.IXtraResizableControl.MaxSize">
            <summary>
                <para>When implemented by a control, specifies its default maximum size which is in effect when the control is displayed within an XtraLayoutControl.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Size"/> structure that specifies the control's default maximum size.
</value>


        </member>
        <member name="P:DevExpress.Utils.Controls.IXtraResizableControl.MinSize">
            <summary>
                <para>When implemented by a control, specifies its default minimum size which is in effect when the control is displayed within an XtraLayoutControl.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Size"/> structure that specifies the control's default minimum size.
</value>


        </member>
        <member name="T:DevExpress.Utils.Controls.ExpandButtonMode">

            <summary>
                <para>Contains values that specify the direction of an expand button's arrow.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Utils.Controls.ExpandButtonMode.Inverted">
            <summary>
                <para>Indicates the normal direction of an expand/collapse button's arrow.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Controls.ExpandButtonMode.Normal">
            <summary>
                <para>Indicates the inverted direction of an expand/collapse button's arrow.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Skins.SkinManager">

            <summary>
                <para>Manages skins for Developer Express controls.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Skins.SkinManager.#ctor">
            <summary>
                <para>Initializes a new instance of the SkinManager class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Skins.SkinManager.AllowArrowDragIndicators">
            <summary>
                <para>Gets or sets whether dragging-and-dropping columns/fields is indicated using arrow indicators.

</para>
            </summary>
            <value><b>true</b> if dragging-and-dropping columns is indicated using arrow indicators; otherwise, <b>false</b>.

</value>


        </member>
        <member name="P:DevExpress.Skins.SkinManager.AllowFormSkins">
            <summary>
                <para>Gets whether the form title bar skinning feature is enabled.
</para>
            </summary>
            <value><b>true</b> if the form title bar skinning feature is enabled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Skins.SkinManager.AllowMdiFormSkins">
            <summary>
                <para>Gets whether the form title bar skinning feature is enabled for MDI child windows.
</para>
            </summary>
            <value><b>true</b> if the form title bar skinning feature for MDI child windows is enabled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Skins.SkinManager.AllowWindowGhosting">
            <summary>
                <para>Gets or sets whether the window ghosting feature is enabled for skinned XtraForm objects.
</para>
            </summary>
            <value>A Boolean value that specifies whether the window ghosting feature is enabled. The default value is <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Skins.SkinManager.CurrentPaintControl">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.Skins.SkinManager.Default">
            <summary>
                <para>Provides access to the default skin manager.
</para>
            </summary>
            <value>A SkinManager object that represents the default skin manager.
</value>


        </member>
        <member name="F:DevExpress.Skins.SkinManager.DefaultSkinName">
            <summary>
                <para>Gets the name of the default skin, which is used by controls by default.
</para>
            </summary>
            <returns>A string that specifies the name of the default skin.
</returns>


        </member>
        <member name="M:DevExpress.Skins.SkinManager.DisableFormSkins">
            <summary>
                <para>Disables the title bar skinning feature for Developer Express forms.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.Skins.SkinManager.DisableMdiFormSkins">
            <summary>
                <para>Disables the title bar skinning feature for MDI child DevExpress forms.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Skins.SkinManager.DisableSkinHitTesting">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Skins.SkinManager.EnableFormSkins">
            <summary>
                <para>Enables the title bar skinning feature for Developer Express forms.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Skins.SkinManager.EnableFormSkinsIfNotVista">
            <summary>
                <para>Enables the title bar skinning feature for Developer Express forms, if the application is not running under Microsoft Vista.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Skins.SkinManager.EnableMdiFormSkins">
            <summary>
                <para>Enables the title bar skinning feature for MDI child Developer Express forms.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Skins.SkinManager.EnableSkinHitTesting">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Skins.SkinManager.GetSkin(System.Object)">
            <summary>
                <para>This method supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="productId">
		@nbsp;

            </param>
            <returns>@nbsp;
</returns>


        </member>
        <member name="M:DevExpress.Skins.SkinManager.GetSkin(System.Object,DevExpress.Skins.ISkinProvider)">
            <summary>
                <para>This method supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="productId">
		@nbsp;

            </param>
            <param name="provider">
		@nbsp;

            </param>
            <returns>@nbsp;
</returns>


        </member>
        <member name="M:DevExpress.Skins.SkinManager.GetSkin(System.Object,System.String)">
            <summary>
                <para>This method supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="productId">
		@nbsp;

            </param>
            <param name="skinName">
		@nbsp;

            </param>
            <returns>@nbsp;
</returns>


        </member>
        <member name="M:DevExpress.Skins.SkinManager.GetSkin(DevExpress.Skins.SkinProductId,DevExpress.Skins.ISkinProvider)">
            <summary>
                <para>This method supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="productId">
		@nbsp;


            </param>
            <param name="provider">
		@nbsp;


            </param>
            <returns>@nbsp;

</returns>


        </member>
        <member name="M:DevExpress.Skins.SkinManager.GetValidSkinName(System.String)">
            <summary>
                <para>This method returns the specified skin name, if it's valid. If the skin name is invalid, the default skin's name is returned.
</para>
            </summary>
            <param name="skinName">
		A string that specifies the skin name to test.

            </param>
            <returns>A string that specifies the valid skin name.
</returns>


        </member>
        <member name="P:DevExpress.Skins.SkinManager.HitElements">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.Skins.SkinManager.HitPoint">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="F:DevExpress.Skins.SkinManager.InvalidPoint">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Skins.SkinManager.IsSkinHitTestingEnabled">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="M:DevExpress.Skins.SkinManager.RegisterAssembly(System.Reflection.Assembly)">
            <summary>
                <para>This method supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="asm">
		@nbsp;

            </param>
            <returns>@nbsp;
</returns>


        </member>
        <member name="M:DevExpress.Skins.SkinManager.RegisterSkin(DevExpress.Skins.SkinCreator)">
            <summary>
                <para>Registers the specified skin for runtime use. This method supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="creator">
		@nbsp;

            </param>


        </member>
        <member name="M:DevExpress.Skins.SkinManager.RegisterSkin(System.Object,DevExpress.Skins.SkinBuilder)">
            <summary>
                <para>This method supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="productId">
		@nbsp;

            </param>
            <param name="skinBuilder">
		@nbsp;

            </param>
            <returns>@nbsp;
</returns>


        </member>
        <member name="M:DevExpress.Skins.SkinManager.RegisterSkinAssembly(System.Reflection.Assembly)">
            <summary>
                <para>This method supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="asm">
		@nbsp;

            </param>
            <returns>@nbsp;
</returns>


        </member>
        <member name="M:DevExpress.Skins.SkinManager.RegisterSkins(System.Reflection.Assembly)">
            <summary>
                <para>This method supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="asm">
		@nbsp;

            </param>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.Skins.SkinManager.Skins">
            <summary>
                <para>Gets the collection of skins that are currently available for use in Developer Express controls.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Skins.SkinContainerCollection"/> collection that stores available skins.
</value>


        </member>
        <member name="T:DevExpress.Utils.Drawing.ImageLayoutMode">

            <summary>
                <para>Specifies how an image can be aligned within another object.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Utils.Drawing.ImageLayoutMode.BottomCenter">
            <summary>
                <para>An image is vertically aligned at the bottom, and horizontally aligned at the center. 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Drawing.ImageLayoutMode.BottomLeft">
            <summary>
                <para>An image is vertically aligned at the bottom, and horizontally aligned on the left.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Drawing.ImageLayoutMode.BottomRight">
            <summary>
                <para>An image is vertically aligned at the bottom, and horizontally aligned on the right. 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Drawing.ImageLayoutMode.Default">
            <summary>
                <para>The default layout.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Drawing.ImageLayoutMode.MiddleCenter">
            <summary>
                <para>An image is horizontally and vertically aligned at the center.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Drawing.ImageLayoutMode.MiddleLeft">
            <summary>
                <para>An image is vertically aligned at the center, and horizontally aligned on the left.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Drawing.ImageLayoutMode.MiddleRight">
            <summary>
                <para>An image is vertically aligned at the center, and horizontally aligned on the right.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Drawing.ImageLayoutMode.Stretch">
            <summary>
                <para>An image is stretched to fill the available client area.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Drawing.ImageLayoutMode.StretchHorizontal">
            <summary>
                <para>An image is stretched horizontally.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Drawing.ImageLayoutMode.StretchVertical">
            <summary>
                <para>An image is stretched vertically.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Drawing.ImageLayoutMode.TopCenter">
            <summary>
                <para>An image is vertically aligned at the top, and horizontally aligned at the center. 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Drawing.ImageLayoutMode.TopLeft">
            <summary>
                <para>An image is vertically aligned at the top, and horizontally aligned on the left.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Drawing.ImageLayoutMode.TopRight">
            <summary>
                <para>An image is vertically aligned at the top, and horizontally aligned on the right.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Drawing.ImageLayoutMode.ZoomInside">
            <summary>
                <para>Zooms an image proportionally so that it's displayed within the client area in its entirety.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Drawing.ImageLayoutMode.ZoomOutside">
            <summary>
                <para>Zooms an image proportionally, making its smaller side (width or height) to be displayed entirely. The image is centered, so the larger side (height or width) will not be displayed in its entirety.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.Controls.ControlBase">

            <summary>
                <para>Represents the base class for most controls and editors available in the <b>XtraEditors</b> library, along with their descendants.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.Controls.ControlBase.#ctor">
            <summary>
                <para>Initializes a new instance of the ControlBase class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.Controls.ControlBase.ClearPrefferedSizeCache(System.Windows.Forms.Control)">
            <summary>
                <para>Clears the value of the size of a rectangular area, into which the specified control can fit, from the program cache.

</para>
            </summary>
            <param name="control">
		A <see cref="T:System.Windows.Forms.Control"/> object.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Controls.ControlBase.GetCanProcessMnemonic(System.Windows.Forms.Control)">
            <summary>
                <para>Returns the value of the control's <b>CanProcessMnemonic</b> property, if this property exists.
</para>
            </summary>
            <param name="control">
		The control whose <b>CanProcessMnemonic</b> property value is returned.

            </param>
            <returns>The value of the control's <b>CanProcessMnemonic</b> property. <b>true</b> if the property doesn't exist.
</returns>


        </member>
        <member name="M:DevExpress.Utils.Controls.ControlBase.GetUnvalidatedControl(System.Windows.Forms.Control)">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="control">
		@nbsp;

            </param>
            <returns>@nbsp;
</returns>


        </member>
        <member name="M:DevExpress.Utils.Controls.ControlBase.GetValidationCanceled(System.Windows.Forms.Control)">
            <summary>
                <para>Returns the <b>ValidationCancelled</b> property value of a control, if this property exists.
</para>
            </summary>
            <param name="control">
		A <see cref="T:System.Object"/> that represents the control whose <b>ValidationCancelled</b> property value is returned.


            </param>
            <returns><b>true</b> if the validation is cancelled; otherwise, <b>false</b>.

</returns>


        </member>
        <member name="M:DevExpress.Utils.Controls.ControlBase.IsUnvalidatedControlIsParent(System.Windows.Forms.Control)">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="control">
		@nbsp;

            </param>
            <returns>@nbsp;
</returns>


        </member>
        <member name="M:DevExpress.Utils.Controls.ControlBase.ResetValidationCanceled(System.Windows.Forms.Control)">
            <summary>
                <para>Resets the control's <b>ValidationCancelled</b> property. This method supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="control">
		A control.

            </param>


        </member>
        <member name="T:DevExpress.XtraEditors.XtraScrollableControl">

            <summary>
                <para>Represents the <b>XtraScrollableControl</b>.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraEditors.XtraScrollableControl.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraEditors.XtraScrollableControl"/> class.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraScrollableControl.AllowTouchScroll">
            <summary>
                <para>Gets or sets if finger scrolling is enabled.
</para>
            </summary>
            <value><b>true</b> if finger scrolling is enabled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraScrollableControl.AlwaysScrollActiveControlIntoView">
            <summary>
                <para>Gets or sets whether the <b>XtraScrollableControl</b>'s view is automatically scrolled to completely display the active control if it is invisible, or partly visible.


</para>
            </summary>
            <value><b>true</b> to automatically scroll the view to completely display the active control if it is invisible, or partly visible; otherwise, <b>false</b>.


</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraScrollableControl.Appearance">
            <summary>
                <para>Gets the control's appearance settings.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that contains appearance settings used to paint the control.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraScrollableControl.AutoScroll">
            <summary>
                <para>Gets or sets whether the <b>XtraScrollableControl</b> enables the user to scroll to any controls placed outside its visible boundaries.

</para>
            </summary>
            <value><b>true</b> to enable auto-scrolling; otherwise, <b>false</b>.

</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraScrollableControl.AutoScrollMargin">
            <summary>
                <para>Gets or sets the size of the auto-scroll margin.

</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Size"/> object that represents the height and width of the auto-scroll margin, in pixels.

</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraScrollableControl.AutoScrollMinSize">
            <summary>
                <para>Gets or sets the minimum size of the auto-scroll.

</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Size"/> object that represents the minimum height and width of the area through which a user can scroll, in pixels.

</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraScrollableControl.AutoScrollPosition">
            <summary>
                <para>Gets or sets the location of the auto-scroll position.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Point"/> structure that represents the auto-scroll position, in pixels.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraScrollableControl.BackColor">
            <summary>
                <para>Gets or sets the control's background color.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Color"/> value that specifies the control's background color.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraScrollableControl.DisplayRectangle">
            <summary>
                <para>Gets the rectangle that represents the <b>XtraScrollableControl</b>'s view area.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Rectangle"/> object that represents the control's view area.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraScrollableControl.FireScrollEventOnMouseWheel">
            <summary>
                <para>Gets or sets whether the <see cref="E:DevExpress.XtraEditors.XtraScrollableControl.Scroll"/> event fires when using the mouse wheel.
</para>
            </summary>
            <value><b>true</b> if the <see cref="E:DevExpress.XtraEditors.XtraScrollableControl.Scroll"/> event fires when using the mouse wheel; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraScrollableControl.ForeColor">
            <summary>
                <para>Gets or sets the control's foreground color.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Color"/> value that specifies the control's foreground color.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraScrollableControl.HorizontalScroll">
            <summary>
                <para>Gets the horizontal scrollbar's settings.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.HorizontalScroll"/> object that represents the horizontal scrollbar.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraScrollableControl.LookAndFeel">
            <summary>
                <para>Gets the control's look and feel settings.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object that contains look and feel settings.
</value>


        </member>
        <member name="M:DevExpress.XtraEditors.XtraScrollableControl.ResetBackColor">
            <summary>
                <para>Resets the <see cref="P:DevExpress.XtraEditors.XtraScrollableControl.BackColor"/> property back to its default value.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraEditors.XtraScrollableControl.Scroll">
            <summary>
                <para>Fires when the control's view is scrolled.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraScrollableControl.ScrollBarSmallChange">
            <summary>
                <para>Gets or sets the distance the <b>XtraScrollableControl</b>'s view is scrolled when the user clicks one of the scroll buttons.


</para>
            </summary>
            <value>An integer value that specifies the distance, in pixels, the control's view is scrolled when the user clicks one of the scroll buttons.


</value>


        </member>
        <member name="M:DevExpress.XtraEditors.XtraScrollableControl.ScrollControlIntoView(System.Windows.Forms.Control)">
            <summary>
                <para>Scrolls the <b>XtraScrollableControl</b>'s view to display the specified control.
</para>
            </summary>
            <param name="activeControl">
		A <see cref="T:System.Windows.Forms.Control"/> descendant that represents the control which is invisible, or partly visible, within the <b>XtraScrollableControl</b>.


            </param>


        </member>
        <member name="M:DevExpress.XtraEditors.XtraScrollableControl.SetAutoScrollMargin(System.Int32,System.Int32)">
            <summary>
                <para>Specifies the size of the auto-scroll margin.
</para>
            </summary>
            <param name="x">
		An integer value that specifies the width of the auto-scroll margin, in pixels.

            </param>
            <param name="y">
		An integer value that specifies the height of the auto-scroll margin, in pixels.

            </param>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraScrollableControl.Text">
            <summary>
                <para>Gets or sets the text associated with the control.
</para>
            </summary>
            <value>A string value that specifies the text associated with the control.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraScrollableControl.VerticalScroll">
            <summary>
                <para>Gets the vertical scrollbar's settings.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.VerticalScroll"/> object that represents the vertical scrollbar.
</value>


        </member>
        <member name="T:DevExpress.Utils.ToolTipType">

            <summary>
                <para>Enumerates tooltip types that are supported by controls.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Utils.ToolTipType.Default">
            <summary>
                <para>The default tooltip type. The actual tooltip type is determined by a control.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.ToolTipType.Standard">
            <summary>
                <para>A regular tooltip that consists of the title and contents regions.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.ToolTipType.SuperTip">
            <summary>
                <para>A <see cref="T:DevExpress.Utils.SuperToolTip"/> that supports multiple text and image regions.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.SuperToolTip">

            <summary>
                <para>Represents a tooltip that supports multiple text and image regions.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.SuperToolTip.#ctor">
            <summary>
                <para>Initializes a new instance of the SuperToolTip class with the default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.SuperToolTip.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
                <para>Initializes a new instance of the SuperToolTip class with the specified settings.
</para>
            </summary>
            <param name="info">
		A SerializationInfo object.

            </param>
            <param name="context">
		A StreamingContext object.

            </param>


        </member>
        <member name="P:DevExpress.Utils.SuperToolTip.AllowHtmlText">
            <summary>
                <para>Gets or sets whether HTML formatting is allowed in the current SuperToolTip object.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.DefaultBoolean"/> value that specifies whether HTML formatting is allowed in the tooltip.
</value>


        </member>
        <member name="P:DevExpress.Utils.SuperToolTip.Appearance">
            <summary>
                <para>This property is not supported by the SuperToolTip class.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="M:DevExpress.Utils.SuperToolTip.Assign(DevExpress.Utils.SuperToolTip)">
            <summary>
                <para>Copies the settings and contents of the specified tooltip to the current object.
</para>
            </summary>
            <param name="source">
		A source <see cref="T:DevExpress.Utils.SuperToolTip"/> object.

            </param>


        </member>
        <member name="M:DevExpress.Utils.SuperToolTip.Clone">
            <summary>
                <para>Returns a copy of the current object.
</para>
            </summary>
            <returns>A SuperToolTip object which is a copy of the current tooltip.
</returns>


        </member>
        <member name="F:DevExpress.Utils.SuperToolTip.DefaultDistanceBetweenItems">
            <summary>
                <para>The default distance between items. This constant specifies the default value for the <see cref="P:DevExpress.Utils.SuperToolTip.DistanceBetweenItems"/> property.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Utils.SuperToolTip.DistanceBetweenItems">
            <summary>
                <para>Specifies the distance between tooltip items.
</para>
            </summary>
            <value>An integer which specifies the distance between tooltip items.
</value>


        </member>
        <member name="P:DevExpress.Utils.SuperToolTip.FixedTooltipWidth">
            <summary>
                <para>Gets or sets whether the tooltip's width is fixed or automatically adjusted to fit the tooltip's contents entirely.
</para>
            </summary>
            <value><b>true</b> if the tooltip's width is fixed; <b>false</b> if the tooltip is automatically resized to fit its contents entirely.
</value>


        </member>
        <member name="P:DevExpress.Utils.SuperToolTip.IsEmpty">
            <summary>
                <para>Gets whether the SuperToolTip is empty.
</para>
            </summary>
            <value><b>true</b> if the SuperToolTip doesn't contain any information to display; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Utils.SuperToolTip.Items">
            <summary>
                <para>Gets the collection of items which constitute the SuperToolTip object.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.ToolTipItemCollection"/> which contains items displayed by the SuperToolTip object.
</value>


        </member>
        <member name="P:DevExpress.Utils.SuperToolTip.LookAndFeel">
            <summary>
                <para>This property is not supported by this class. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object.
</value>


        </member>
        <member name="P:DevExpress.Utils.SuperToolTip.MaxWidth">
            <summary>
                <para>Specifies the tooltip window's maximum width, in pixels.
</para>
            </summary>
            <value>An integer value that specifies the tooltip window's maximum width.
</value>


        </member>
        <member name="P:DevExpress.Utils.SuperToolTip.OwnerAllowHtmlText">
            <summary>
                <para>Gets whether HTML formatting in a SuperToolTip is allowed by the ToolTipController object that displays the current SuperToolTip object.
</para>
            </summary>
            <value><b>true</b> if HTML formatting is allowed by the ToolTipController object that displays the current SuperToolTip object; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Utils.SuperToolTip.Padding">
            <summary>
                <para>Gets or sets padding within the SuperToolTip.

</para>
            </summary>
            <value>A <see cref="T:System.Windows.Forms.Padding"/> object that specifies the tooltip's internal spacing characteristics.
</value>


        </member>
        <member name="M:DevExpress.Utils.SuperToolTip.Setup(DevExpress.Utils.SuperToolTipSetupArgs)">
            <summary>
                <para>Creates tooltip items based on the specified setup information.
</para>
            </summary>
            <param name="info">
		A <see cref="T:DevExpress.Utils.SuperToolTipSetupArgs"/> object which contains initialization information.

            </param>


        </member>
        <member name="M:DevExpress.Utils.SuperToolTip.ToString">
            <summary>
                <para>Gets the textual representation of the current SuperToolTip.
</para>
            </summary>
            <returns>A string which specifies the tooltip's textual representation.
</returns>


        </member>
        <member name="T:DevExpress.Utils.Drawing.ProgressAnimationMode">

            <summary>
                <para>Contains values that specify how progress is indicated on progress bar controls.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Utils.Drawing.ProgressAnimationMode.Cycle">
            <summary>
                <para>A control repeatedly scrolls the block from one edge to another.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Drawing.ProgressAnimationMode.Default">
            <summary>
                <para>The same, as the <see cref="F:DevExpress.Utils.Drawing.ProgressAnimationMode.Cycle"/> option.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Drawing.ProgressAnimationMode.PingPong">
            <summary>
                <para>A control scrolls the block from one edge to another, by moving the block back when it reaches any control's edge.

</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.Menu.DXPopupMenu">

            <summary>
                <para>A base class for popup menus in Developer Express .NET controls.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.Menu.DXPopupMenu.#ctor">
            <summary>
                <para>Initializes a new instance of the DXPopupMenu class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.Menu.DXPopupMenu.#ctor(System.EventHandler)">
            <summary>
                <para>Initializes a new instance of the DXPopupMenu class with the specified handler for the <see cref="E:DevExpress.Utils.Menu.DXSubMenuItem.BeforePopup"/> event.
</para>
            </summary>
            <param name="beforePopup">
		An event handler that will be invoked when the menu is about to be displayed. This value is assigned to the <see cref="E:DevExpress.Utils.Menu.DXSubMenuItem.BeforePopup"/> event.

            </param>


        </member>
        <member name="P:DevExpress.Utils.Menu.DXPopupMenu.Alignment">
            <summary>
                <para>Gets or sets the menu's alignment relative to the mouse cursor.
</para>
            </summary>
            <value>The <b>ContentAlignment</b> value that is the alignment of the menu relative to the mouse cursor.

</value>


        </member>
        <member name="E:DevExpress.Utils.Menu.DXPopupMenu.CloseUp">
            <summary>
                <para>Fires after the menu has been closed.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.Menu.DXPopupMenu.GenerateCloseUpEvent">
            <summary>
                <para>Fires a specific event that typically occurs after the menu has disappeared.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.Menu.DXPopupMenu.HidePopup">
            <summary>
                <para>Hides the current popup menu.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Utils.Menu.DXPopupMenu.MenuViewType">
            <summary>
                <para>Gets or sets how the current menu is displayed (as a menu, floating bar or <see cref="T:DevExpress.XtraBars.Ribbon.RibbonMiniToolbar"/>).
</para>
            </summary>
            <value>A MenuViewType value
</value>


        </member>
        <member name="P:DevExpress.Utils.Menu.DXPopupMenu.OwnerPopup">
            <summary>
                <para>Gets or sets the object that created the current popup menu. For internal use.
</para>
            </summary>
            <value>An object that created the current popup menu.
</value>


        </member>
        <member name="E:DevExpress.Utils.Menu.DXPopupMenu.PopupHide">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.Menu.DXSubMenuItem">

            <summary>
                <para>Specifies a submenu.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.Menu.DXSubMenuItem.#ctor(System.String)">
            <summary>
                <para>Initializes a new instance of the DXSubMenuItem class with the specified caption.
</para>
            </summary>
            <param name="caption">
		A string that specifies the text displayed by the menu item. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Caption"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Menu.DXSubMenuItem.#ctor">
            <summary>
                <para>Initializes a new instance of the DXSubMenuItem class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.Menu.DXSubMenuItem.#ctor(System.String,System.EventHandler)">
            <summary>
                <para>Initializes a new instance of the DXSubMenuItem class with the specified caption and the handler for the <see cref="E:DevExpress.Utils.Menu.DXSubMenuItem.BeforePopup"/> event.
</para>
            </summary>
            <param name="caption">
		A string that specifies the text displayed by the menu item. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Caption"/> property.

            </param>
            <param name="beforePopup">
		An event handler that will be invoked when the submenu is about to be displayed. This value is assigned to the <see cref="E:DevExpress.Utils.Menu.DXSubMenuItem.BeforePopup"/> event.

            </param>


        </member>
        <member name="E:DevExpress.Utils.Menu.DXSubMenuItem.BeforePopup">
            <summary>
                <para>Occurs when the DXSubMenuItem is about to be displayed onscreen.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.Menu.DXSubMenuItem.Dispose">
            <summary>
                <para>Releases all the resources used by the current object.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.Menu.DXSubMenuItem.GenerateBeforePopupEvent">
            <summary>
                <para>Invokes the event handler assigned to the <see cref="E:DevExpress.Utils.Menu.DXSubMenuItem.BeforePopup"/> event.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Utils.Menu.DXSubMenuItem.Items">
            <summary>
                <para>Gets a collection of items displayed when the current submenu is expanded.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.Menu.DXMenuItemCollection"/> object which contains menu items displayed when the submenu is expanded.
</value>


        </member>
        <member name="T:DevExpress.Utils.Menu.DXMenuCheckItem">

            <summary>
                <para>Represents a menu item that can be checked and unchecked.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.Menu.DXMenuCheckItem.#ctor(System.String)">
            <summary>
                <para>Initializes a new instance of the DXMenuCheckItem class with the specified caption.
</para>
            </summary>
            <param name="caption">
		A string that specifies the menu item's caption. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Caption"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Menu.DXMenuCheckItem.#ctor(System.String,System.Boolean,System.Drawing.Image,System.EventHandler)">
            <summary>
                <para>Initializes a new instance of the DXMenuCheckItem class with the specified caption, image, check state and event handler.
</para>
            </summary>
            <param name="caption">
		A string that specifies the menu item's caption. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Caption"/> property.

            </param>
            <param name="check">
		A Boolean value that specifies the menu item's check state. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuCheckItem.Checked"/> property.

            </param>
            <param name="image">
		An image that is displayed within the menu item. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Image"/> property.

            </param>
            <param name="checkedChanged">
		An event handler that will be invoked when the check state is toggled. This value is assigned to the <see cref="E:DevExpress.Utils.Menu.DXMenuCheckItem.CheckedChanged"/> event.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Menu.DXMenuCheckItem.#ctor(System.String,System.Boolean)">
            <summary>
                <para>Initializes a new instance of the DXMenuCheckItem class with the specified caption and check state.
</para>
            </summary>
            <param name="caption">
		A string that specifies the menu item's caption. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Caption"/> property.

            </param>
            <param name="check">
		A Boolean value that specifies the menu item's check state. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuCheckItem.Checked"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Menu.DXMenuCheckItem.#ctor">
            <summary>
                <para>Initializes a new instance of the DXMenuCheckItem class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Utils.Menu.DXMenuCheckItem.Checked">
            <summary>
                <para>Gets or sets a value indicating whether the menu item is checked.
</para>
            </summary>
            <value><b>true</b> if the menu item is checked; otherwise, <b>false</b>.
</value>


        </member>
        <member name="E:DevExpress.Utils.Menu.DXMenuCheckItem.CheckedChanged">
            <summary>
                <para>Occurs when the menu item's check state is toggled.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.Menu.DXMenuCheckItem.Dispose">
            <summary>
                <para>Releases all the resources used by the current object.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.Menu.DXMenuItem">

            <summary>
                <para>Represents a regular menu item.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.Menu.DXMenuItem.#ctor">
            <summary>
                <para>Initializes a new instance of the DXMenuItem class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.Menu.DXMenuItem.#ctor(System.String,System.EventHandler,System.Drawing.Image)">
            <summary>
                <para>Initializes a new instance of the DXMenuItem class with the specified caption, image and <b>Click</b> event handler
</para>
            </summary>
            <param name="caption">
		A string that specifies the text displayed by the menu item. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Caption"/> property.

            </param>
            <param name="click">
		An event handler that will be invoked when the menu item is clicked or selected. This value is assigned to the <see cref="E:DevExpress.Utils.Menu.DXMenuItem.Click"/> event.

            </param>
            <param name="image">
		An image that is displayed within the menu item. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Image"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Menu.DXMenuItem.#ctor(System.String,System.EventHandler)">
            <summary>
                <para>Initializes a new instance of the DXMenuItem class with the specified caption and <b>Click</b> event handler
</para>
            </summary>
            <param name="caption">
		A string that specifies the text displayed by the menu item. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Caption"/> property.

            </param>
            <param name="click">
		An event handler that will be invoked when the menu item is clicked or selected. This value is assigned to the <see cref="E:DevExpress.Utils.Menu.DXMenuItem.Click"/> event.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Menu.DXMenuItem.#ctor(System.String)">
            <summary>
                <para>Initializes a new instance of the DXMenuItem class with the specified caption.
</para>
            </summary>
            <param name="caption">
		A string that specifies the text displayed by the menu item. This value is assigned to the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Caption"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Menu.DXMenuItem.#ctor(System.String,System.EventHandler,System.Drawing.Image,System.Drawing.Image)">
            <summary>
                <para>Initializes a new instance of the DXMenuItem class with the specified settings.
</para>
            </summary>
            <param name="caption">
		A string used to initialize the item's <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Caption"/> property.

            </param>
            <param name="click">
		A handler for the <see cref="E:DevExpress.Utils.Menu.DXMenuItem.Click"/> event.

            </param>
            <param name="image">
		An Image used to initialize the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Image"/> property.

            </param>
            <param name="imageDisabled">
		An Image used to initialize the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.ImageDisabled"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Menu.DXMenuItem.#ctor(System.String,System.EventHandler,System.Drawing.Image,System.Drawing.Image,System.Drawing.Image,System.Drawing.Image)">
            <summary>
                <para>Initializes a new instance of the DXMenuItem class with the specified settings.
</para>
            </summary>
            <param name="caption">
		A string used to initialize the item's <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Caption"/> property.

            </param>
            <param name="click">
		A handler for the <see cref="E:DevExpress.Utils.Menu.DXMenuItem.Click"/> event.

            </param>
            <param name="image">
		An Image used to initialize the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Image"/> property.

            </param>
            <param name="imageDisabled">
		An Image used to initialize the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.ImageDisabled"/> property.

            </param>
            <param name="largeImage">
		An Image used to initialize the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.LargeImage"/> property.

            </param>
            <param name="largeImageDisabled">
		An Image used to initialize the <see cref="P:DevExpress.Utils.Menu.DXMenuItem.LargeImageDisabled"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Utils.Menu.DXMenuItem.BeginGroup">
            <summary>
                <para>Gets or sets whether the current menu item starts a group.
</para>
            </summary>
            <value><b>true</b> if the menu item starts a group; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Utils.Menu.DXMenuItem.Caption">
            <summary>
                <para>Specifies the text displayed within the menu item.
</para>
            </summary>
            <value>A string that specifies the text displayed within the menu item.
</value>


        </member>
        <member name="E:DevExpress.Utils.Menu.DXMenuItem.Click">
            <summary>
                <para>Occurs when the menu item's functionality needs to be invoked.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Utils.Menu.DXMenuItem.CloseMenuOnClick">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.Utils.Menu.DXMenuItem.Collection">
            <summary>
                <para>Gets the menu item collection which owns the current menu item.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.Menu.DXMenuItemCollection"/> collection which owns the current menu item.
</value>


        </member>
        <member name="M:DevExpress.Utils.Menu.DXMenuItem.Dispose">
            <summary>
                <para>Releases all the resources used by the current object.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Utils.Menu.DXMenuItem.Enabled">
            <summary>
                <para>Gets or sets whether the menu item is enabled.
</para>
            </summary>
            <value><b>true</b> if the menu item is enabled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.Utils.Menu.DXMenuItem.GenerateClickEvent">
            <summary>
                <para>Invokes the event handler assigned to the <see cref="E:DevExpress.Utils.Menu.DXMenuItem.Click"/> event.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.Menu.DXMenuItem.GetImage">
            <summary>
                <para>Gets the image that currently represents the menu item.

</para>
            </summary>
            <returns>An Image object that specifies the image currently representing the menu item.
</returns>


        </member>
        <member name="P:DevExpress.Utils.Menu.DXMenuItem.Image">
            <summary>
                <para>Gets or sets an image displayed within the menu item,
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Image"/> object that specifies the image displayed within the menu item.
</value>


        </member>
        <member name="P:DevExpress.Utils.Menu.DXMenuItem.ImageDisabled">
            <summary>
                <para>Gets or sets an image representing the menu item in the disabled state.
</para>
            </summary>
            <value>An Image object representing the menu item in the disabled state.
</value>


        </member>
        <member name="P:DevExpress.Utils.Menu.DXMenuItem.LargeImage">
            <summary>
                <para>Gets or sets a large image for the current menu item.
</para>
            </summary>
            <value>An Image object that is a large image for the current menu item.
</value>


        </member>
        <member name="P:DevExpress.Utils.Menu.DXMenuItem.LargeImageDisabled">
            <summary>
                <para>Gets or sets a large image displayed in the current menu item when it is in the disabled state.
</para>
            </summary>
            <value>An Image object that is a large image displayed in the current menu item when it is in the disabled state.
</value>


        </member>
        <member name="P:DevExpress.Utils.Menu.DXMenuItem.Shortcut">
            <summary>
                <para>Gets or sets a shortcut displayed within the menu item
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Forms.Shortcut"/> value that specifies a shortcut.
</value>


        </member>
        <member name="P:DevExpress.Utils.Menu.DXMenuItem.ShowHotKeyPrefix">
            <summary>
                <para>Gets or sets whether the ampersand ('@amp;') character, when it is found in the item's <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Caption"/>, acts as a shortcut prefix, or it is displayed as is.
</para>
            </summary>
            <value><b>true</b> if the ampersand ('@amp;') character, when it is found in the item's <see cref="P:DevExpress.Utils.Menu.DXMenuItem.Caption"/>, acts as a shortcut prefix; <b>false</b> if the ampersand ('@amp;') is displayed as is.
</value>


        </member>
        <member name="P:DevExpress.Utils.Menu.DXMenuItem.Tag">
            <summary>
                <para>Gets or sets the data associated with the menu item.
</para>
            </summary>
            <value>An object that contains the information which is associated with the menu item.
</value>


        </member>
        <member name="P:DevExpress.Utils.Menu.DXMenuItem.Visible">
            <summary>
                <para>Gets or sets whether the menu item is visible.
</para>
            </summary>
            <value><b>true</b> if the menu item is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.Utils.DefaultToolTipController">

            <summary>
                <para>Represents the Default ToolTipController.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.DefaultToolTipController.#ctor">
            <summary>
                <para>Initializes a new instance of the DefaultToolTipController class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.DefaultToolTipController.#ctor(System.ComponentModel.IContainer)">
            <summary>
                <para>Initializes a new instance of the DefaultToolTipController class with the specified container.
</para>
            </summary>
            <param name="container">
		An object that implements the <see cref="T:System.ComponentModel.IContainer"/> interface.

            </param>


        </member>
        <member name="P:DevExpress.Utils.DefaultToolTipController.DefaultController">
            <summary>
                <para>Returns the object which represents the Default ToolTipController.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.ToolTipController"/> object which represents the Default ToolTipController.
</value>


        </member>
        <member name="M:DevExpress.Utils.DefaultToolTipController.GetAllowHtmlText(System.Windows.Forms.Control)">
            <summary>
                <para>Returns whether HTML formatting is enabled in tooltips for the specified control.
</para>
            </summary>
            <param name="control">
		A control for which this tooltip setting is to be obtained.

            </param>
            <returns>A <see cref="T:DevExpress.Utils.DefaultBoolean"/> value that specifies whether HTML formatting is supported in tooltips for the specified control.
</returns>


        </member>
        <member name="M:DevExpress.Utils.DefaultToolTipController.GetSuperTip(System.Windows.Forms.Control)">
            <summary>
                <para>Gets a <see cref="T:DevExpress.Utils.SuperToolTip"/> object associated with the specified control.
</para>
            </summary>
            <param name="control">
		A control whose SuperToolTip object is to be obtained.


            </param>
            <returns>A <see cref="T:DevExpress.Utils.SuperToolTip"/> object associated with the control.
</returns>


        </member>
        <member name="M:DevExpress.Utils.DefaultToolTipController.GetTitle(System.Windows.Forms.Control)">
            <summary>
                <para>Gets a regular tooltip's title displayed within the specified control.

</para>
            </summary>
            <param name="control">
		A control whose tooltip's title is to be obtained.


            </param>
            <returns>A string representing a regular tooltip's title for the control.

</returns>


        </member>
        <member name="M:DevExpress.Utils.DefaultToolTipController.GetToolTip(System.Windows.Forms.Control)">
            <summary>
                <para>Gets a regular tooltip for the specified control.
</para>
            </summary>
            <param name="control">
		A control whose tooltip should be obtained.


            </param>
            <returns>A string representing a regular tooltip for the control.

</returns>


        </member>
        <member name="M:DevExpress.Utils.DefaultToolTipController.GetToolTipIconType(System.Windows.Forms.Control)">
            <summary>
                <para>Gets the type of the icon displayed within the specified control's regular tooltip.

</para>
            </summary>
            <param name="control">
		A control whose tooltip's icon type is to be obtained.



            </param>
            <returns>A <see cref="T:DevExpress.Utils.ToolTipIconType"/> value representing the icon type displayed within the specified control's regular tooltip. 



</returns>


        </member>
        <member name="M:DevExpress.Utils.DefaultToolTipController.SetAllowHtmlText(System.Windows.Forms.Control,DevExpress.Utils.DefaultBoolean)">
            <summary>
                <para>Sets whether HTML formatting is enabled in tooltips for the specified control.
</para>
            </summary>
            <param name="control">
		A control for which the tooltip information is to be changed.


            </param>
            <param name="value">
		A <see cref="T:DevExpress.Utils.DefaultBoolean"/> value that specifies whether HTML formatting is enabled in tooltips for the specified control.

            </param>


        </member>
        <member name="M:DevExpress.Utils.DefaultToolTipController.SetSuperTip(System.Windows.Forms.Control,DevExpress.Utils.SuperToolTip)">
            <summary>
                <para>Associates a <see cref="T:DevExpress.Utils.SuperToolTip"/> object with the specified control.
</para>
            </summary>
            <param name="control">
		A control for which to set the tooltip.


            </param>
            <param name="value">
		A <see cref="T:DevExpress.Utils.SuperToolTip"/> object to associate with the control.

            </param>


        </member>
        <member name="M:DevExpress.Utils.DefaultToolTipController.SetTitle(System.Windows.Forms.Control,System.String)">
            <summary>
                <para>Sets a regular tooltip's title for the specified control.
</para>
            </summary>
            <param name="control">
		A control for which to set a regular tooltip's title.


            </param>
            <param name="value">
		A string representing a regular tooltip's title.


            </param>


        </member>
        <member name="M:DevExpress.Utils.DefaultToolTipController.SetToolTip(System.Windows.Forms.Control,System.String)">
            <summary>
                <para>Sets a regular tooltip for the specified control.
</para>
            </summary>
            <param name="control">
		A control for which to set the tooltip.


            </param>
            <param name="value">
		A string representing a regular tooltip.


            </param>


        </member>
        <member name="M:DevExpress.Utils.DefaultToolTipController.SetToolTipIconType(System.Windows.Forms.Control,DevExpress.Utils.ToolTipIconType)">
            <summary>
                <para>Sets the type of the icon displayed within the specified control's regular tooltip.

</para>
            </summary>
            <param name="control">
		A control for which the icon type is set.



            </param>
            <param name="value">
		A <see cref="T:DevExpress.Utils.ToolTipIconType"/> value representing the type of the icon that should be displayed within the specified control's regular tooltip.

            </param>


        </member>
        <member name="T:DevExpress.Utils.ToolTipControlInfo">

            <summary>
                <para>Contains tooltip information.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.ToolTipControlInfo.#ctor(System.Object,System.String,System.String)">
            <summary>
                <para>Initializes a new instance of the ToolTipControlInfo class with the specified identifier, tooltip text and title. 
</para>
            </summary>
            <param name="_object">
		An object used as an identifier of the currently processed visual element. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControlInfo.Object"/> property.

            </param>
            <param name="text">
		A string that specifies a tooltip's text. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControlInfo.Text"/> property.

            </param>
            <param name="title">
		A string that specifies a tooltip's title. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControlInfo.Title"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipControlInfo.#ctor(System.Object,System.String,System.Boolean,DevExpress.Utils.ToolTipIconType)">
            <summary>
                <para>Initializes a new instance of the ToolTipControlInfo class with the specified identifier, tooltip text, icon type and delay flag.
</para>
            </summary>
            <param name="_object">
		An object used as an identifier of the currently processed visual element. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControlInfo.Object"/> property.

            </param>
            <param name="text">
		A string that specifies a tooltip's text. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControlInfo.Text"/> property.

            </param>
            <param name="immediateToolTip">
		A Boolean value that specifies whether a tooltip needs to be displayed immediately or after a delay. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControlInfo.ImmediateToolTip"/> property.

            </param>
            <param name="iconType">
		A <see cref="T:DevExpress.Utils.ToolTipIconType"/> value that specifies the kind of predefined icon to display in a tooltip. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControlInfo.IconType"/>

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipControlInfo.#ctor(System.Object,System.String,System.String,System.Boolean,DevExpress.Utils.ToolTipIconType)">
            <summary>
                <para>Initializes a new instance of the ToolTipControlInfo class with the specified identifier, tooltip text, title, icon type and delay flag.
</para>
            </summary>
            <param name="_object">
		An object used as an identifier of the currently processed visual element. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControlInfo.Object"/> property.

            </param>
            <param name="text">
		A string that specifies a tooltip's text. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControlInfo.Text"/> property.

            </param>
            <param name="title">
		A string that specifies a tooltip's title. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControlInfo.Title"/> property.

            </param>
            <param name="immediateToolTip">
		A Boolean value that specifies whether a tooltip needs to be displayed immediately or after a delay. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControlInfo.ImmediateToolTip"/> property.

            </param>
            <param name="iconType">
		A <see cref="T:DevExpress.Utils.ToolTipIconType"/> value that specifies the kind of predefined icon to display in a tooltip. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControlInfo.IconType"/>

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipControlInfo.#ctor(System.Object,System.String,System.String,DevExpress.Utils.ToolTipIconType)">
            <summary>
                <para>Initializes a new instance of the ToolTipControlInfo class with the specified identifier, tooltip text, title and icon type.
</para>
            </summary>
            <param name="_object">
		An object used as an identifier of the currently processed visual element. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControlInfo.Object"/> property.

            </param>
            <param name="text">
		A string that specifies a tooltip's text. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControlInfo.Text"/> property.

            </param>
            <param name="title">
		A string that specifies a tooltip's title. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControlInfo.Title"/> property.

            </param>
            <param name="iconType">
		A <see cref="T:DevExpress.Utils.ToolTipIconType"/> value that specifies the kind of predefined icon to display in a tooltip. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControlInfo.IconType"/>

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipControlInfo.#ctor">
            <summary>
                <para>Initializes a new instance of the ToolTipControlInfo class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.ToolTipControlInfo.#ctor(System.Object,System.String,DevExpress.Utils.ToolTipIconType)">
            <summary>
                <para>Initializes a new instance of the ToolTipControlInfo class with the specified identifier, tooltip text and icon type.
</para>
            </summary>
            <param name="_object">
		An object used as an identifier of the currently processed visual element. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControlInfo.Object"/> property.

            </param>
            <param name="text">
		A string that specifies a tooltip's text. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControlInfo.Text"/> property.

            </param>
            <param name="iconType">
		A <see cref="T:DevExpress.Utils.ToolTipIconType"/> value that specifies the kind of predefined icon to display in a tooltip. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControlInfo.IconType"/>

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipControlInfo.#ctor(System.Object,System.String)">
            <summary>
                <para>Initializes a new instance of the ToolTipControlInfo class with the specified identifier and tooltip text.
</para>
            </summary>
            <param name="_object">
		An object used as an identifier of the currently processed visual element. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControlInfo.Object"/> property.

            </param>
            <param name="text">
		A string that specifies a tooltip's text. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControlInfo.Text"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipControlInfo.#ctor(System.Object,System.String,System.String,System.Boolean,DevExpress.Utils.ToolTipIconType,DevExpress.Utils.DefaultBoolean)">
            <summary>
                <para>Initializes a new instance of the ToolTipControlInfo class with the specified settings.
</para>
            </summary>
            <param name="_object">
		An object used as an identifier of the currently processed visual element. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControlInfo.Object"/> property.

            </param>
            <param name="text">
		A string that specifies a tooltip's text. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControlInfo.Text"/> property.

            </param>
            <param name="title">
		A string that specifies a tooltip's title. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControlInfo.Title"/> property.

            </param>
            <param name="immediateToolTip">
		A Boolean value that specifies whether a tooltip needs to be displayed immediately or after a delay. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControlInfo.ImmediateToolTip"/> property.

            </param>
            <param name="iconType">
		A <see cref="T:DevExpress.Utils.ToolTipIconType"/> value that specifies the kind of predefined icon to display in a tooltip. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControlInfo.IconType"/>

            </param>
            <param name="allowHtmlText">
		A <see cref="T:DevExpress.Utils.DefaultBoolean"/> value that specifies whether HTML formatting is supported in tooltips. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControlInfo.AllowHtmlText"/> property

            </param>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControlInfo.AllowHtmlText">
            <summary>
                <para>Gets or sets whether HTML formatting is supported in tooltips.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.DefaultBoolean"/> value that specifies whether HTML formatting is supported in tooltips.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControlInfo.IconType">
            <summary>
                <para>Gets or sets the kind of predefined icon to display in a tooltip.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.ToolTipIconType"/> value specifying the kind of predefined icon to display.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControlInfo.ImmediateToolTip">
            <summary>
                <para>Gets or sets whether a tooltip will be displayed immediately or after a delay.
</para>
            </summary>
            <value>A Boolean value that specifies whether a tooltip will be displayed immediately after an event handler call.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControlInfo.Interval">
            <summary>
                <para>Gets or sets the interval that must pass before a tooltip is displayed.
</para>
            </summary>
            <value>An integer value that specifies the delay, in milliseconds.
</value>


        </member>
        <member name="M:DevExpress.Utils.ToolTipControlInfo.Normalize">
            <summary>
                <para> [To be supplied] </para>
            </summary>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControlInfo.Object">
            <summary>
                <para>Gets or sets an object which uniquely identifies the currently processed element.
</para>
            </summary>
            <value>An object which uniquely identifies the currently processed element.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControlInfo.SuperTip">
            <summary>
                <para>Gets or sets a <see cref="T:DevExpress.Utils.SuperToolTip"/> that will be displayed if the <see cref="P:DevExpress.Utils.ToolTipControlInfo.ToolTipType"/> property is set to <b>SuperTip</b>
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.SuperToolTip"/> object that has been assigned to this property. The default value is <b>null</b>. 

</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControlInfo.Text">
            <summary>
                <para>Gets or sets the tooltip's text.
</para>
            </summary>
            <value>A string that specifies the tooltip's text.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControlInfo.Title">
            <summary>
                <para>Gets or sets the tooltip's title.
</para>
            </summary>
            <value>A string that specifies the tooltip's title.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControlInfo.ToolTipImage">
            <summary>
                <para>Gets or sets the image to display within the current tooltip.
</para>
            </summary>
            <value>An <see cref="T:System.Drawing.Image"/> object that specifies the image to display within the current tooltip.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControlInfo.ToolTipLocation">
            <summary>
                <para>Gets or sets the tooltip location.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.ToolTipLocation"/> enumeration value.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControlInfo.ToolTipPosition">
            <summary>
                <para>Gets or sets the tooltip's position in screen coordinates.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Point"/> structure that specifies the tooltip's position, in pixels, relative to the top left corner of the screen.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControlInfo.ToolTipType">
            <summary>
                <para>Gets or sets the type of tooltip to be displayed.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.ToolTipType"/> value that specifies the type of tooltip to be displayed.

</value>


        </member>
        <member name="T:DevExpress.Utils.ToolTipControllerGetActiveObjectInfoEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Utils.ToolTipController.GetActiveObjectInfo"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.ToolTipControllerGetActiveObjectInfoEventHandler.Invoke(System.Object,DevExpress.Utils.ToolTipControllerGetActiveObjectInfoEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Utils.ToolTipController.GetActiveObjectInfo"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the ToolTip Controller which fires this event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Utils.ToolTipControllerGetActiveObjectInfoEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Utils.ToolTipControllerGetActiveObjectInfoEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Utils.ToolTipController.GetActiveObjectInfo"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.ToolTipControllerGetActiveObjectInfoEventArgs.#ctor(System.Windows.Forms.Control,System.Object,DevExpress.Utils.ToolTipControlInfo,System.Drawing.Point)">
            <summary>
                <para>Initializes a new instance of the ToolTipControllerGetActiveObjectInfoEventArgs class with the specified settings.
</para>
            </summary>
            <param name="control">
		The control for which a tooltip controller's event is fired. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerEventArgsBase.SelectedControl"/> property.

            </param>
            <param name="obj">
		The element of the control (or the type of the element) for which the tooltip is displayed. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerEventArgsBase.SelectedObject"/> property.

            </param>
            <param name="info">
		An object which uniquely identifies the current element for which the tooltip is displayed. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerGetActiveObjectInfoEventArgs.Info"/> property.

            </param>
            <param name="controlMousePosition">
		A <see cref="T:System.Drawing.Point"/> structure that specifies the position of the mouse cursor relative to the control's upper left corner. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerGetActiveObjectInfoEventArgs.ControlMousePosition"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerGetActiveObjectInfoEventArgs.ControlMousePosition">
            <summary>
                <para>Gets the position of the mouse cursor relative to the control's upper left corner. 

</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Point"/> structure that specifies the position of the mouse cursor relative to the control's upper left corner.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerGetActiveObjectInfoEventArgs.Info">
            <summary>
                <para>Gets or sets an object which uniquely identifies the visual element at the current position.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.ToolTipControlInfo"/> object which identifies the visual element at the current position.
</value>


        </member>
        <member name="T:DevExpress.Utils.Images">

            <summary>
                <para>Represents a collection of <see cref="T:System.Drawing.Image"/> objects.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.Images.#ctor(DevExpress.Utils.ImageCollection)">
            <summary>
                <para>Initializes a new instance of the Images class with the specified owner.
</para>
            </summary>
            <param name="owner">
		An <see cref="T:DevExpress.Utils.ImageCollection"/> object that represents the image collection which owns this object.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Images.#ctor(DevExpress.Utils.ImageCollection,System.Boolean)">
            <summary>
                <para>Initializes a new instance of the Images class with the specified settings.
</para>
            </summary>
            <param name="owner">
		An <see cref="T:DevExpress.Utils.ImageCollection"/> object representing the Images object's owner.

            </param>
            <param name="allowModifyImages">
		<b>true</b> to permit image modification; otherwise, <b>false</b>.


            </param>


        </member>
        <member name="M:DevExpress.Utils.Images.Add(System.Drawing.Image)">
            <summary>
                <para>Appends the specified image to the current collection of images.

</para>
            </summary>
            <param name="image">
		A <see cref="T:System.Drawing.Image"/> object to append to the collection.


            </param>
            <returns>An integer value indicating the position at which the new element was inserted. <b>-1</b> if the specified image is <b>null</b> (<b>Nothing</b> in Visual Basic) or empty.
</returns>


        </member>
        <member name="M:DevExpress.Utils.Images.Add(System.Drawing.Image,System.String)">
            <summary>
                <para>Adds an image with the specified name to the collection.
</para>
            </summary>
            <param name="image">
		An image to add to the collection.

            </param>
            <param name="name">
		A string that specifies the name of the image,

            </param>
            <returns>An integer value that specifies the position at which the image has been added.
</returns>


        </member>
        <member name="M:DevExpress.Utils.Images.AddImageStrip(System.Drawing.Image)">
            <summary>
                <para>Adds images from the specified horizontal image strip to the collection.

</para>
            </summary>
            <param name="sourceImage">
		The image(s) to add to the collection.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Images.AddImageStripVertical(System.Drawing.Image)">
            <summary>
                <para>Adds images from the specified vertical image strip to the collection.

</para>
            </summary>
            <param name="sourceImage">
		The image(s) to add to the collection.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Images.AddRange(System.Object[])">
            <summary>
                <para>Adds an array of images to the current collection.
</para>
            </summary>
            <param name="images">
		An array of images to be added to the collection.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Images.AddRange(System.Collections.IList)">
            <summary>
                <para>Adds images from the specified list to the current collection.
</para>
            </summary>
            <param name="images">
		A IList object that contains images to be added to the collection.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Images.Clear">
            <summary>
                <para>Clears the current collection.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.Images.Contains(System.Object)">
            <summary>
                <para>Gets whether the collection contains the specified element.
</para>
            </summary>
            <param name="value">
		An object to locate in the collection.

            </param>
            <returns>A Boolean value that specifies whether the collection contains the specified element.
</returns>


        </member>
        <member name="M:DevExpress.Utils.Images.CopyTo(System.Array,System.Int32)">
            <summary>
                <para>Copies the collection to a compatible one-dimensional Array, starting at the specified index of the target array.
</para>
            </summary>
            <param name="array">
		The one-dimensional Array that is the destination of the elements copied from the collection. The Array must have zero-based indexing. 


            </param>
            <param name="index">
		The zero-based index in the array in which copying begins. 


            </param>


        </member>
        <member name="P:DevExpress.Utils.Images.Count">
            <summary>
                <para>Gets the number of items in the collection.
</para>
            </summary>
            <value>An integer value that specifies the number of items in the collection.
</value>


        </member>
        <member name="M:DevExpress.Utils.Images.GetEnumerator">
            <summary>
                <para>Returns an enumerator for the collection.
</para>
            </summary>
            <returns>An IEnumerator for the collection.
</returns>


        </member>
        <member name="M:DevExpress.Utils.Images.GetImageFromCollection(System.Drawing.Image,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
                <para>Returns an image from the image collection at the specified position within the specified height and width.

</para>
            </summary>
            <param name="iml">
		A <see cref="T:System.Drawing.Image"/> object which resolution settings will be applied to the resulting image.


            </param>
            <param name="width">
		An integer value that specifies the width of the resulting image.

            </param>
            <param name="height">
		An integer value that specifies the height of the resulting image.

            </param>
            <param name="wIndex">
		An integer value that specifies the vertical position of the resulting image in the images collection.


            </param>
            <param name="hIndex">
		An integer value that specifies the horizontal position of the resulting image in the images collection.


            </param>
            <returns>A <see cref="T:System.Drawing.Image"/> object which represents an image at the specified position of the specified size.

</returns>


        </member>
        <member name="M:DevExpress.Utils.Images.IndexOf(System.Object)">
            <summary>
                <para>Searches for the specified Object and returns the zero-based index of the first occurrence within the collection.
</para>
            </summary>
            <param name="value">
		The zero-based index of the first occurrence of value within the collection, if found; otherwise, <b>-1</b>.

            </param>
            <returns>The Object to locate in the collection.
</returns>


        </member>
        <member name="P:DevExpress.Utils.Images.InnerImages">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>An InnerImagesList object.
</value>


        </member>
        <member name="M:DevExpress.Utils.Images.Insert(System.Int32,System.Object)">
            <summary>
                <para>Inserts an image into the collection at the specified position.
</para>
            </summary>
            <param name="index">
		An integer value specifying the zero-based position at which the specified image is inserted.

            </param>
            <param name="value">
		An image to insert into the collection.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Images.Insert(System.Int32,System.Object,System.String)">
            <summary>
                <para>Inserts an image with the assigned name into the collection at the specified position.
</para>
            </summary>
            <param name="index">
		An integer value specifying the zero-based position at which the specified image is inserted.

            </param>
            <param name="value">
		An image to insert into the collection.

            </param>
            <param name="name">
		A string that specifies the name to be associated with the image.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Images.Insert(System.Drawing.Image,System.String,System.Type,System.Int32)">
            <summary>
                <para>Inserts an image from project resources.
</para>
            </summary>
            <param name="image">
		An image to be inserted.

            </param>
            <param name="name">
		The name of the image to be inserted.

            </param>
            <param name="resourceType">
		The type of the class where the image to be inserted is defined.

            </param>
            <param name="index">
		An integer value that specifies the position at which the image needs to be inserted in the image collection.

            </param>
            <returns>An integer value that specifies the index of the inserted image in the image collection.
</returns>


        </member>
        <member name="P:DevExpress.Utils.Images.IsFixedSize">
            <summary>
                <para>Gets a value indicating whether the collection has a fixed size.
</para>
            </summary>
            <value><b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Utils.Images.IsReadOnly">
            <summary>
                <para>Gets a value indicating whether the collection is read-only.
</para>
            </summary>
            <value><b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Utils.Images.IsSynchronized">
            <summary>
                <para>Gets a value indicating whether access to the collection is synchronized (thread safe).
</para>
            </summary>
            <value><b>true</b>.
</value>


        </member>
        <member name="P:DevExpress.Utils.Images.Item(System.Int32)">
            <summary>
                <para>Provides indexed access to individual items in the collection.
</para>
            </summary>
            <param name="index">
		A zero-based integer specifying the desired item's position within the collection. If it's negative or exceeds the last available index, an exception is raised. 

            </param>
            <value>A <see cref="T:System.Drawing.Image"/> object which represents the image at the specified position.

</value>


        </member>
        <member name="P:DevExpress.Utils.Images.Item(System.String)">
            <summary>
                <para>Provides access to the images in the collection by their names.
</para>
            </summary>
            <param name="name">
		A string that specifies the name of the image to locate in the collection.

            </param>
            <value>An image with the specified name.
</value>


        </member>
        <member name="P:DevExpress.Utils.Images.Keys">
            <summary>
                <para>Gets the collection of names assigned to the images in the current collection.
</para>
            </summary>
            <value>A StringCollection of names assigned to the images in the current collection.
</value>


        </member>
        <member name="M:DevExpress.Utils.Images.Remove(System.Object)">
            <summary>
                <para>Removes the specified image from the collection.
</para>
            </summary>
            <param name="value">
		An image to remove from the collection.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Images.RemoveAt(System.Int32)">
            <summary>
                <para>Removes an image at the specified position from the collection.
</para>
            </summary>
            <param name="index">
		A zero-based index of the required image.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Images.RemoveByName(System.String)">
            <summary>
                <para>Removes an image with the specified name from the collection.
</para>
            </summary>
            <param name="name">
		A string that specifies the name of the required image.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Images.SetKeyName(System.Int32,System.String)">
            <summary>
                <para>Assigns a name to the image located at the specified position within the collection.
</para>
            </summary>
            <param name="index">
		An integer value that specifies the zero-based index of the required image.

            </param>
            <param name="name">
		A string that specifies the name to be assigned to the image.

            </param>


        </member>
        <member name="P:DevExpress.Utils.Images.SyncRoot">
            <summary>
                <para>Gets an object that can be used to synchronize access to the collection.
</para>
            </summary>
            <value>The current object.
</value>


        </member>
        <member name="M:DevExpress.Utils.Images.ToArray">
            <summary>
                <para>Copies the elements of the Images to a new array of <see cref="T:System.Drawing.Image"/> objects.
</para>
            </summary>
            <returns>An array of <see cref="T:System.Drawing.Image"/> objects containing the copies of the elements of the Images.
</returns>


        </member>
        <member name="T:DevExpress.Utils.WordWrap">

            <summary>
                <para>Enumerates wrapping modes.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Utils.WordWrap.Default">
            <summary>
                <para>Default wrapping mode. The actual wrapping mode is determined by a control.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.WordWrap.NoWrap">
            <summary>
                <para>The word wrapping feature is disabled.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.WordWrap.Wrap">
            <summary>
                <para>The word wrapping feature is enabled.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.Locations">

            <summary>
                <para>Contains values that specify how a specific element is positioned relative to another element.

</para>
            </summary>

        </member>
        <member name="F:DevExpress.Utils.Locations.Bottom">
            <summary>
                <para>An element is located at the bottom edge of another element.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Locations.Default">
            <summary>
                <para>The same as the <see cref="F:DevExpress.Utils.Locations.Top"/> option.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Locations.Left">
            <summary>
                <para>An element is located at the left edge of another element.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Locations.Right">
            <summary>
                <para>An element is located at the right edge of another element.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Locations.Top">
            <summary>
                <para>An element is located at the top edge of another element.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.TextOptions">

            <summary>
                <para>Contains options that specify how text is rendered.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.TextOptions.#ctor(DevExpress.Utils.HorzAlignment,DevExpress.Utils.VertAlignment,DevExpress.Utils.WordWrap,DevExpress.Utils.Trimming)">
            <summary>
                <para>Initializes a new instance of the TextOptions class with the specified alignments, word wrapping and text trimming options.

</para>
            </summary>
            <param name="hAlignment">
		A <see cref="T:DevExpress.Utils.HorzAlignment"/> value that specifies the horizontal alignment of text. This value is assigned to the <see cref="P:DevExpress.Utils.TextOptions.HAlignment"/> property.


            </param>
            <param name="vAlignment">
		A <see cref="T:DevExpress.Utils.HorzAlignment"/> value that specifies the vertical alignment of text. This value is assigned to the <see cref="P:DevExpress.Utils.TextOptions.VAlignment"/> property.


            </param>
            <param name="wordWrap">
		A <see cref="T:DevExpress.Utils.WordWrap"/> value that specifies word wrapping mode. This value is assigned to the <see cref="P:DevExpress.Utils.TextOptions.WordWrap"/> property.


            </param>
            <param name="trimming">
		A <see cref="T:DevExpress.Utils.Trimming"/> value that specifies text trimming mode. This value is assigned to the <see cref="P:DevExpress.Utils.TextOptions.Trimming"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Utils.TextOptions.#ctor(DevExpress.Utils.AppearanceObject)">
            <summary>
                <para>Initializes a new instance of the TextOptions class with the specified owner, the text settings are set to default values.

</para>
            </summary>
            <param name="owner">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which becomes the owner of the current object.

            </param>


        </member>
        <member name="M:DevExpress.Utils.TextOptions.#ctor(DevExpress.Utils.HorzAlignment,DevExpress.Utils.VertAlignment,DevExpress.Utils.WordWrap,DevExpress.Utils.Trimming,DevExpress.Utils.HKeyPrefix)">
            <summary>
                <para>Initializes a new instance of the TextOptions class with the specified alignments, word wrapping and text trimming options.

</para>
            </summary>
            <param name="hAlignment">
		A <see cref="T:DevExpress.Utils.HorzAlignment"/> value that specifies the horizontal alignment of text. This value is assigned to the <see cref="P:DevExpress.Utils.TextOptions.HAlignment"/> property.

            </param>
            <param name="vAlignment">
		A <see cref="T:DevExpress.Utils.HorzAlignment"/> value that specifies the vertical alignment of text. This value is assigned to the <see cref="P:DevExpress.Utils.TextOptions.VAlignment"/> property.


            </param>
            <param name="wordWrap">
		A <see cref="T:DevExpress.Utils.WordWrap"/> value that specifies word wrapping mode. This value is assigned to the <see cref="P:DevExpress.Utils.TextOptions.WordWrap"/> property.

            </param>
            <param name="trimming">
		A <see cref="T:DevExpress.Utils.Trimming"/> value that specifies text trimming mode. This value is assigned to the <see cref="P:DevExpress.Utils.TextOptions.Trimming"/> property.

            </param>
            <param name="hotKeyPrefix">
		A <see cref="T:DevExpress.Utils.HKeyPrefix"/> enumeration value that specifies the hotkey prefix for the text. This value is assigned to the <see cref="P:DevExpress.Utils.TextOptions.HotkeyPrefix"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Utils.TextOptions.Assign(DevExpress.Utils.TextOptions)">
            <summary>
                <para>Copies settings from the specified object to the current TextOptions object.
</para>
            </summary>
            <param name="options">
		A TextOptions object whose settings are copied to the current object.

            </param>


        </member>
        <member name="P:DevExpress.Utils.TextOptions.DefaultOptions">
            <summary>
                <para>Gets an object that specifies the default text options.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.TextOptions"/> object that specifies the default text options.

</value>


        </member>
        <member name="P:DevExpress.Utils.TextOptions.DefaultOptionsCenteredWithEllipsis">
            <summary>
                <para>Gets an object whose settings force text to be centered.

</para>
            </summary>
            <value>A TextOptions object with its settings set to the corresponding values.

</value>


        </member>
        <member name="P:DevExpress.Utils.TextOptions.DefaultOptionsMultiLine">
            <summary>
                <para>Gets an object whose settings allow text to wrap.
</para>
            </summary>
            <value>A TextOptions object with its settings set to the corresponding values.

</value>


        </member>
        <member name="P:DevExpress.Utils.TextOptions.DefaultOptionsNoWrap">
            <summary>
                <para>Gets an object whose settings prevent text from wrapping.

</para>
            </summary>
            <value>A TextOptions object with its settings set to the corresponding values.

</value>


        </member>
        <member name="P:DevExpress.Utils.TextOptions.DefaultOptionsNoWrapEx">
            <summary>
                <para>Gets an object whose settings force text to be centered and prevent it from wrapping.

</para>
            </summary>
            <value>A TextOptions object with its settings set to the corresponding values.

</value>


        </member>
        <member name="P:DevExpress.Utils.TextOptions.DefaultStringFormat">
            <summary>
                <para>Gets an object that contains the default formatting settings.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.StringFormat"/> object containing the default formatting settings.
</value>


        </member>
        <member name="M:DevExpress.Utils.TextOptions.ForceUseGenericDefaultStringFormat">
            <summary>
                <para>Specifies how the default string format referred to by the <see cref="P:DevExpress.Utils.TextOptions.DefaultStringFormat"/> property is constructed.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.TextOptions.GetStringFormat">
            <summary>
                <para>Returns a <see cref="T:System.Drawing.StringFormat"/> object whose settings reflect the alignment, word wrapping and text trimming options of the <see cref="P:DevExpress.Utils.TextOptions.DefaultOptions"/> object.

</para>
            </summary>
            <returns>A <see cref="T:System.Drawing.StringFormat"/> object whose properties are set to match the appropriate values.

</returns>


        </member>
        <member name="M:DevExpress.Utils.TextOptions.GetStringFormat(DevExpress.Utils.TextOptions)">
            <summary>
                <para>Returns a <see cref="T:System.Drawing.StringFormat"/> object whose settings reflect the alignment, word wrapping and text trimming options of the specified TextOptions object.

</para>
            </summary>
            <param name="defaultOptions">
		A TextOptions object whose settings are used to initialize the corresponding settings of the <see cref="T:System.Drawing.StringFormat"/> object that is to be returned.

            </param>
            <returns>A <see cref="T:System.Drawing.StringFormat"/> object whose properties are set to match the appropriate values.

</returns>


        </member>
        <member name="P:DevExpress.Utils.TextOptions.HAlignment">
            <summary>
                <para>Gets or sets the horizontal alignment of text.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.HorzAlignment"/> value that specifies the text horizontal alignment.
</value>


        </member>
        <member name="M:DevExpress.Utils.TextOptions.HorzAlignmentToStringAlignment(DevExpress.Utils.HorzAlignment)">
            <summary>
                <para>Converts the specified <see cref="T:DevExpress.Utils.HorzAlignment"/> value to a corresponding <see cref="T:System.Drawing.StringAlignment"/> value.

</para>
            </summary>
            <param name="align">
		A <see cref="T:DevExpress.Utils.HorzAlignment"/> value that is to be converted.

            </param>
            <returns>A <see cref="T:System.Drawing.StringAlignment"/> value that corresponds to the specified <see cref="T:DevExpress.Utils.HorzAlignment"/> value.
</returns>


        </member>
        <member name="P:DevExpress.Utils.TextOptions.HotkeyPrefix">
            <summary>
                <para>Gets or sets the hotkey prefix for the text. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.HKeyPrefix"/> enumeration value which represents the hotkey prefix for the text.
</value>


        </member>
        <member name="M:DevExpress.Utils.TextOptions.IsEqual(DevExpress.Utils.TextOptions)">
            <summary>
                <para>Tests whether two objects have the same property values.
</para>
            </summary>
            <param name="options">
		The TextOptions object to which the current object is compared.

            </param>
            <returns><b>true</b> if the current object has the same property values as the specified object; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Utils.TextOptions.Reset">
            <summary>
                <para>Resets all the text settings to their default values.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.TextOptions.ToString">
            <summary>
                <para>Returns the text representation of the current object.
</para>
            </summary>
            <returns>The text representation of the current object.
</returns>


        </member>
        <member name="P:DevExpress.Utils.TextOptions.Trimming">
            <summary>
                <para>Gets or sets text trimming mode.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.Trimming"/> value that specifies text trimming mode.
</value>


        </member>
        <member name="M:DevExpress.Utils.TextOptions.UpdateDefaultOptions(DevExpress.Utils.TextOptions)">
            <summary>
                <para>For internal use. Copies settings from the specified object to the current object.
</para>
            </summary>
            <param name="defaultOptions">
		A <see cref="T:DevExpress.Utils.TextOptions"/> object whose settings are copied to the current object.

            </param>


        </member>
        <member name="P:DevExpress.Utils.TextOptions.VAlignment">
            <summary>
                <para>Gets or sets the vertical alignment of text.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.VertAlignment"/> value that specifies the text vertical alignment.
</value>


        </member>
        <member name="M:DevExpress.Utils.TextOptions.VertAlignmentToStringAlignment(DevExpress.Utils.VertAlignment)">
            <summary>
                <para>Converts the specified <see cref="T:DevExpress.Utils.VertAlignment"/> value to a corresponding <see cref="T:System.Drawing.StringAlignment"/> value.
</para>
            </summary>
            <param name="align">
		A <see cref="T:DevExpress.Utils.VertAlignment"/> value that is to be converted.

            </param>
            <returns>A <see cref="T:System.Drawing.StringAlignment"/> value that corresponds to the specified <see cref="T:DevExpress.Utils.VertAlignment"/> value.
</returns>


        </member>
        <member name="P:DevExpress.Utils.TextOptions.WordWrap">
            <summary>
                <para>Gets or sets text wrapping mode.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.WordWrap"/> value that specifies text wrapping mode.
</value>


        </member>
        <member name="T:DevExpress.Utils.ToolTipControllerCustomDrawEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Utils.ToolTipController.CustomDraw"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.ToolTipControllerCustomDrawEventHandler.Invoke(System.Object,DevExpress.Utils.ToolTipControllerCustomDrawEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Utils.ToolTipController.CustomDraw"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the tooltip controller which raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Utils.ToolTipControllerCustomDrawEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.Utils.ToolTipControllerCustomDrawEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Utils.ToolTipController.CustomDraw"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.ToolTipControllerCustomDrawEventArgs.#ctor(DevExpress.Utils.Drawing.GraphicsCache,DevExpress.Utils.ToolTipControllerShowEventArgs,System.Drawing.Rectangle)">
            <summary>
                <para>Initializes a new instance of the ToolTipControllerCustomDrawEventArgs class.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which specifies the storage for the most  used  pens, fonts and brushes. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerCustomDrawEventArgs.Cache"/> property.

            </param>
            <param name="args">
		A <see cref="T:DevExpress.Utils.ToolTipControllerShowEventArgs"/> object which provides the information required to paint a tooltip. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerCustomDrawEventArgs.ShowInfo"/> property.

            </param>
            <param name="bounds">
		A <see cref="T:System.Drawing.Rectangle"/> structure which specifies the tooltip's boundaries. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerCustomDrawEventArgs.Bounds"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerCustomDrawEventArgs.Bounds">
            <summary>
                <para>Gets the bounding rectangle of the painted tooltip.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Rectangle"/> structure which specifies the tooltip's boundaries.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerCustomDrawEventArgs.Cache">
            <summary>
                <para>Gets an object which specifies the storage for the most  used pens, fonts and brushes
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which specifies the storage for the most  used  pens, fonts and brushes.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerCustomDrawEventArgs.Handled">
            <summary>
                <para>Gets or sets whether an event was handled, if it was handled the default actions are not required.
</para>
            </summary>
            <value><b>true</b> if default painting isn't required; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerCustomDrawEventArgs.ShowInfo">
            <summary>
                <para>Gets an object which provides the information required to paint a tooltip.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.ToolTipControllerShowEventArgs"/> object which provides the information required to paint a tooltip.
</value>


        </member>
        <member name="T:DevExpress.Utils.ImageCollection">

            <summary>
                <para>A collection of <see cref="T:System.Drawing.Image"/> objects. The ImageCollection is also used as a part of the <see cref="T:DevExpress.Utils.SharedImageCollection"/> component.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.ImageCollection.#ctor">
            <summary>
                <para>Initializes a new instance of the ImageCollection class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.ImageCollection.#ctor(System.ComponentModel.IContainer)">
            <summary>
                <para>Initializes a new instance of the ImageCollection class with the specified container.
</para>
            </summary>
            <param name="container">
		An object that implements the <see cref="T:System.ComponentModel.IContainer"/> interface.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ImageCollection.#ctor(System.Boolean)">
            <summary>
                <para>Initializes a new instance of the ImageCollection class with the specified settings.
</para>
            </summary>
            <param name="allowModifyImages">
		<b>true</b> to permit image modification; otherwise, <b>false</b>.


            </param>


        </member>
        <member name="M:DevExpress.Utils.ImageCollection.AddImage(System.Drawing.Image)">
            <summary>
                <para>Appends the specified image to the collection.
</para>
            </summary>
            <param name="image">
		The image to add to the collection.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ImageCollection.AddImage(System.Object,System.Drawing.Image)">
            <summary>
                <para>Appends the specified image to the specified list.
</para>
            </summary>
            <param name="images">
		An object that represents the collection of images.

            </param>
            <param name="image">
		The image to add to the specified collection.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ImageCollection.AddImage(System.Drawing.Image,System.String)">
            <summary>
                <para>Adds an image with the specified name to the collection.
</para>
            </summary>
            <param name="image">
		An Image to be added to the collection.

            </param>
            <param name="name">
		A string that specifies the image's name. You can then retrieve images by name via the <see cref="P:DevExpress.Utils.ImageCollection.Images"/> property.


            </param>


        </member>
        <member name="M:DevExpress.Utils.ImageCollection.AddImageStrip(System.Drawing.Image)">
            <summary>
                <para>Adds the images from the specified horizontal image strip to the collection.
</para>
            </summary>
            <param name="image">
		The image(s) to add to the collection.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ImageCollection.AddImageStripVertical(System.Drawing.Image)">
            <summary>
                <para>Adds the images from the specified vertical image strip to the collection.
</para>
            </summary>
            <param name="image">
		The image(s) to add to the collection.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ImageCollection.BeginInit">
            <summary>
                <para>Notifies the image collection that initialization is starting.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Utils.ImageCollection.Changed">
            <summary>
                <para>Fires after the image collection has been changed.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.ImageCollection.Clear">
            <summary>
                <para>Removes all images from the collection.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.ImageCollection.DrawImageListImage(DevExpress.Utils.Drawing.GraphicsInfoArgs,System.Object,System.Int32,System.Drawing.Rectangle,System.Boolean)">
            <summary>
                <para>Draws the specified image in the specified state and at the specified location.
</para>
            </summary>
            <param name="info">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsInfoArgs"/> object.

            </param>
            <param name="images">
		An object that represents the source of the images.

            </param>
            <param name="index">
		A zero-based integer that specifies the index of the image within the <i>images</i>.

            </param>
            <param name="bounds">
		A <see cref="T:System.Drawing.Rectangle"/> structure that specifies the drawing area.

            </param>
            <param name="enabled">
		<b>true</b> to draw the image in the enabled state; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ImageCollection.DrawImageListImage(DevExpress.Utils.Drawing.GraphicsCache,System.Object,System.Int32,System.Drawing.Rectangle)">
            <summary>
                <para>Draws the specified image at the specified location.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object that specifies the storage for the most  used pens, fonts and brushes.

            </param>
            <param name="images">
		An object that represents the source of the images.


            </param>
            <param name="index">
		A zero-based integer that specifies the index of the image within the <i>images</i>.

            </param>
            <param name="bounds">
		A <see cref="T:System.Drawing.Rectangle"/> structure that specifies the drawing area.


            </param>


        </member>
        <member name="M:DevExpress.Utils.ImageCollection.DrawImageListImage(DevExpress.Utils.Drawing.GraphicsCache,System.Object,System.Int32,System.Drawing.Rectangle,System.Boolean)">
            <summary>
                <para>Draws the image.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object that provides storage for pens, fonts and brushes used when painting.


            </param>
            <param name="images">
		An object that represents the image source.

            </param>
            <param name="index">
		An integer value that identifies the image within the collection.

            </param>
            <param name="bounds">
		A <see cref="T:System.Drawing.Rectangle"/> structure that specifies the bounding rectangle of the image.

            </param>
            <param name="enabled">
		<b>true</b> to draw an image in enabled state; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ImageCollection.DrawImageListImage(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Image,System.Object,System.Int32,System.Drawing.Rectangle,System.Boolean)">
            <summary>
                <para>Draws the image.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object that provides storage for pens, fonts and brushes used when painting.


            </param>
            <param name="image">
		The <see cref="T:System.Drawing.Image"/> to draw.

            </param>
            <param name="images">
		An object that represents the image source.

            </param>
            <param name="index">
		An integer value that identifies the image within the collection.

            </param>
            <param name="bounds">
		A <see cref="T:System.Drawing.Rectangle"/> structure that specifies the bounding rectangle of the image.

            </param>
            <param name="enabled">
		<b>true</b> to draw an image in enabled state; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ImageCollection.EndInit">
            <summary>
                <para>Notifies the image collection that initialization is complete.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.ImageCollection.ExportToFile(System.String)">
            <summary>
                <para>Saves the contents of the current collection to a file.
</para>
            </summary>
            <param name="fileName">
		A string that specifies the name of the file to which the contents of the image collection is saved.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ImageCollection.GetImageListImage(System.Object,System.Int32)">
            <summary>
                <para>Returns the image at the specified position within the specified image list.
</para>
            </summary>
            <param name="images">
		An object that represents the source of images.

            </param>
            <param name="index">
		A zero-based integer that specifies the index of the image within the <i>images</i>.

            </param>
            <returns>An <see cref="T:System.Drawing.Image"/> descendant that represents the image at the specified position within the specified image list. <b>null</b> (<b>Nothing</b> in Visual Basic) if the image isn't found.
</returns>


        </member>
        <member name="M:DevExpress.Utils.ImageCollection.GetImageListImage(System.Object,System.String)">
            <summary>
                <para>Returns an image from the specified ImageList or ImageCollection.
</para>
            </summary>
            <param name="images">
		An ImageList or ImageCollection that stores an image to be returned.

            </param>
            <param name="id">
		A string value that is the key/name of the image to be returned.

            </param>
            <returns>An image with the specified key/name stored in an ImageList/ImageCollection.
</returns>


        </member>
        <member name="M:DevExpress.Utils.ImageCollection.GetImageListImageCount(System.Object)">
            <summary>
                <para>Returns the number of images within the specified source of images.
</para>
            </summary>
            <param name="images">
		An object that represents the source of images.

            </param>
            <returns>An integer value that specifies the number of images within the specified image source.
</returns>


        </member>
        <member name="M:DevExpress.Utils.ImageCollection.GetImageListSize(System.Object)">
            <summary>
                <para>Returns the size of the images in the specified image list.
</para>
            </summary>
            <param name="images">
		An object that represents the source of the images.


            </param>
            <returns>A <see cref="T:System.Drawing.Size"/> structure that specifies the height and width of the images in the specified list. <see cref="F:System.Drawing.Size.Empty"/> if <i>images</i> is <b>null</b> (<b>Nothing</b> in Visual Basic) or isn't a source of images.

</returns>


        </member>
        <member name="M:DevExpress.Utils.ImageCollection.GetImageListSize(System.Drawing.Image,System.Object,System.Int32)">
            <summary>
                <para>Gets the size of the specified image or of an image in the image list addressed by a specific index.

</para>
            </summary>
            <param name="image">
		An image whose size is returned.


            </param>
            <param name="images">
		An object that represents the source of images. This parameter is in effect if the <i>image</i> parameter is set to <b>null</b>.


            </param>
            <param name="index">
		The index of the required image in the <i>images</i> list.

            </param>
            <returns>A <see cref="T:System.Drawing.Size"/> value that specifies the size of a specific image.

</returns>


        </member>
        <member name="M:DevExpress.Utils.ImageCollection.ImageFromFile(System.String)">
            <summary>
                <para>Returns the image stored in the specified file.
</para>
            </summary>
            <param name="fileName">
		A string that specifies the full path to the file.

            </param>
            <returns>A <see cref="T:System.Drawing.Image"/> object that represents the image stored in the specified file; <b>null</b> if the image cannot be loaded.
</returns>


        </member>
        <member name="P:DevExpress.Utils.ImageCollection.Images">
            <summary>
                <para>Provides access to the collection of images.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.Images"/> object that represents the collection of images.
</value>


        </member>
        <member name="P:DevExpress.Utils.ImageCollection.ImageSize">
            <summary>
                <para>Gets or sets the size of images in the image collection.
For the <see cref="T:DevExpress.Utils.SharedImageCollection"/>, this property specifies the size of images fetched from an image strip (at design time or via the <see cref="M:DevExpress.Utils.ImageCollection.AddImageStrip"/> and <see cref="M:DevExpress.Utils.ImageCollection.AddImageStripVertical"/> methods).
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Size"/> structure which specifies the height and width of the images in the list.
</value>


        </member>
        <member name="P:DevExpress.Utils.ImageCollection.ImageStream">
            <summary>
                <para>Gets or sets the handle to the <see cref="T:System.Windows.Forms.ImageListStreamer"/> associated with the current image collection.

</para>
            </summary>
            <value>A handle to the <see cref="T:System.Windows.Forms.ImageListStreamer"/> for the current image collection. <b>null</b> (<b>Nothing</b> in Visual Basic) if the current image collection is empty.
</value>


        </member>
        <member name="M:DevExpress.Utils.ImageCollection.InsertImage(System.Drawing.Image,System.String,System.Type,System.Int32)">
            <summary>
                <para>Inserts an image from project resources.
</para>
            </summary>
            <param name="image">
		An image to be inserted.

            </param>
            <param name="name">
		The name of the image to be inserted.

            </param>
            <param name="resourceType">
		The type of the class where the image to be inserted is defined.

            </param>
            <param name="index">
		An integer value that specifies the position at which the image needs to be inserted in the image collection.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ImageCollection.IsImageExists(System.Drawing.Image,System.Object,System.Int32)">
            <summary>
                <para>This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="image">
		The <see cref="T:System.Drawing.Image"/>.

            </param>
            <param name="images">
		An object that represents the image source.

            </param>
            <param name="index">
		An integer value that specifies the position of the image within the collection.

            </param>
            <returns>The Boolean value.
</returns>


        </member>
        <member name="M:DevExpress.Utils.ImageCollection.IsImageListImageExists(System.Object,System.Int32)">
            <summary>
                <para>Determines whether the image exists at the specified position within the specified source of images.
</para>
            </summary>
            <param name="images">
		An object that represents the source of images.

            </param>
            <param name="index">
		A zero-based integer that specifies the index of the image within the <i>images</i>.

            </param>
            <returns><b>true</b> if the image exists at the specified position within the specified source of images; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.Utils.ImageCollection.TransparentColor">
            <summary>
                <para>Gets or sets the color to treat as transparent.
</para>
            </summary>
            <value>The <see cref="T:System.Drawing.Color"/> which represents the color that is not rendered when the image is drawn.
</value>


        </member>
        <member name="T:DevExpress.Utils.EditorShowMode">

            <summary>
                <para>Contains values that specify how an in-place editor is activated for a cell in a container control.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Utils.EditorShowMode.Click">
            <summary>
                <para>If a cell is focused and not being edited, an in-place editor is opened on a click (the mouse button is pressed and then released). Clicking a non-focused cell focuses the cell first, and a subsequent click activates the in-place editor (if not prohibited).
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.EditorShowMode.Default">
            <summary>
                <para>Specifies the default behavior.
<para>
For the XtraGrid control, this option acts as the <see cref="F:DevExpress.Utils.EditorShowMode.Click"/> option if multiple cell selection is enabled (see the <see cref="F:DevExpress.XtraGrid.Views.Grid.GridMultiSelectMode.CellSelect"/> topic). Otherwise, this option acts as the <see cref="F:DevExpress.Utils.EditorShowMode.MouseDown"/> option.
</para>
<para>
For the XtraPivotGrid control, this option acts as the <see cref="F:DevExpress.Utils.EditorShowMode.MouseDownFocused"/> option.
</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.EditorShowMode.MouseDown">
            <summary>
                <para>A cell's in-place editor is activated when the left mouse button is pressed.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.EditorShowMode.MouseDownFocused">
            <summary>
                <para>If a cell is focused and not being edited, an in-place editor is opened on pressing the mouse button. Clicking a non-focused cell focuses the cell first, and a subsequent mouse-down event activates the in-place editor (if not prohibited).
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.EditorShowMode.MouseUp">
            <summary>
                <para>A cell's in-place editor is activated when a mouse button is released from a click within the cell.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.Menu.CommandPopupMenu`1">

            <summary>
                <para> [To be supplied] </para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.Menu.CommandPopupMenu`1.#ctor">
            <summary>
                <para>Initializes a new instance of the CommandPopupMenu@lt;T@gt; class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.Menu.CommandPopupMenu`1.#ctor(System.EventHandler)">
            <summary>
                <para>Initializes a new instance of the CommandPopupMenu@lt;T@gt; class with the specified caption and the handler for the <see cref="E:DevExpress.Utils.Menu.DXSubMenuItem.BeforePopup"/> event.
</para>
            </summary>
            <param name="beforePopup">
		An event handler that will be invoked when the menu is about to be displayed. This value is assigned to the <see cref="E:DevExpress.Utils.Menu.DXSubMenuItem.BeforePopup"/> event.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Menu.CommandPopupMenu`1.DisableMenuItem(DevExpress.Utils.Menu.T)">
            <summary>
                <para>Find a menu item specified by its Id and disable it.

</para>
            </summary>
            <param name="id">
		An object serving as a menu identifier.


            </param>


        </member>
        <member name="M:DevExpress.Utils.Menu.CommandPopupMenu`1.DisableMenuItem(DevExpress.Utils.Menu.T,System.Boolean)">
            <summary>
                <para>Find a menu item specified by its Id and disable it.

</para>
            </summary>
            <param name="id">
		An object serving as a menu identifier.


            </param>
            <param name="recursive">
		<b>true</b> to search nested menus recursively; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Menu.CommandPopupMenu`1.EnableMenuItem(DevExpress.Utils.Menu.T)">
            <summary>
                <para>Find a menu item specified by its Id and enable it.
</para>
            </summary>
            <param name="id">
		An object serving as a menu identifier.


            </param>


        </member>
        <member name="M:DevExpress.Utils.Menu.CommandPopupMenu`1.EnableMenuItem(DevExpress.Utils.Menu.T,System.Boolean)">
            <summary>
                <para>Find a menu item specified by its Id and enable it.
</para>
            </summary>
            <param name="id">
		An object serving as a menu identifier.


            </param>
            <param name="recursive">
		<b>true</b> to search nested menus recursively; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Menu.CommandPopupMenu`1.GetDXMenuItemById(DevExpress.Utils.Menu.DXPopupMenu,DevExpress.Utils.Menu.T,System.Boolean)">
            <summary>
                <para>Searches for a menu item specified by its ID within a specified menu object.
</para>
            </summary>
            <param name="menu">
		A <see cref="T:DevExpress.Utils.Menu.DXPopupMenu"/> object that is the menu from which a search starts.

            </param>
            <param name="id">
		An integer that is the position of the menu item within the menu object.


            </param>
            <param name="recursive">
		<b>true</b>, to search nested menus recursively; otherwise, <b>false</b>.

            </param>
            <returns>A <see cref="T:DevExpress.Utils.Menu.DXMenuItem"/> object if the menu with a specified identifier is found; otherwise <b>null</b> (or <b>Nothing</b> in Visual Basic)
</returns>


        </member>
        <member name="P:DevExpress.Utils.Menu.CommandPopupMenu`1.Id">
            <summary>
                <para>Gets or sets menu identifier.
</para>
            </summary>
            <value>An object serving as a menu identifier.
</value>


        </member>
        <member name="M:DevExpress.Utils.Menu.CommandPopupMenu`1.MoveMenuCheckItem(DevExpress.Utils.Menu.T,System.Int32)">
            <summary>
                <para>Moves a menu item with a check mark to a specified position.
</para>
            </summary>
            <param name="id">
		An object serving as a menu identifier.

            </param>
            <param name="to">
		An integer that is the position of a menu item within the menu object.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Menu.CommandPopupMenu`1.MoveMenuItem(DevExpress.Utils.Menu.T,System.Int32)">
            <summary>
                <para>Moves a menu item to a specified position.
</para>
            </summary>
            <param name="id">
		An object serving as a menu identifier.


            </param>
            <param name="to">
		An integer that is the position of a menu item within the menu object.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Menu.CommandPopupMenu`1.MoveMenuItem(DevExpress.Utils.Menu.DXMenuItem,System.Int32)">
            <summary>
                <para>Moves a menu item to a specified position.
</para>
            </summary>
            <param name="item">
		A <see cref="T:DevExpress.Utils.Menu.DXMenuItem"/> object that is the menu item.

            </param>
            <param name="to">
		An integer that is the position of a menu item within the menu object.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Menu.CommandPopupMenu`1.MoveSubMenuItem(DevExpress.Utils.Menu.T,System.Int32)">
            <summary>
                <para>Moves a submenu item to a specified position.
</para>
            </summary>
            <param name="id">
		An object serving as a menu identifier.

            </param>
            <param name="to">
		An integer that is the position of a menu item within the menu object.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Menu.CommandPopupMenu`1.RemoveMenuItem(DevExpress.Utils.Menu.T,System.Boolean)">
            <summary>
                <para>Find a menu item specified by its Id and remove it.

</para>
            </summary>
            <param name="id">
		An object serving as a menu identifier.


            </param>
            <param name="recursive">
		<b>true</b>, to search nested menus recursively; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Menu.CommandPopupMenu`1.RemoveMenuItem(DevExpress.Utils.Menu.T)">
            <summary>
                <para>Find a menu item specified by its Id and remove it.

</para>
            </summary>
            <param name="id">
		An object serving as a menu identifier.


            </param>


        </member>
        <member name="T:DevExpress.XtraEditors.XtraUserControl">

            <summary>
                <para>Represents an <b>XtraUserControl</b>.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraEditors.XtraUserControl.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraEditors.XtraUserControl"/> class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraUserControl.Appearance">
            <summary>
                <para>Gets the appearance settings used to paint the user control.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which contains the user control's appearance settings.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraUserControl.BackColor">
            <summary>
                <para>Gets or sets the user control's background color.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Color"/> structure which specifies the user control's background color.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraUserControl.Font">
            <summary>
                <para>Gets or sets font settings applied to the control.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Font"/> object that contains font settings.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraUserControl.ForeColor">
            <summary>
                <para>Gets or sets the control's foreground color.
</para>
            </summary>
            <value>A Color structure that specifies the control's foreground color.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraUserControl.LookAndFeel">
            <summary>
                <para>Provides access to the settings which control the user control's look and feel. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object whose properties specify the user control's look and feel.
</value>


        </member>
        <member name="M:DevExpress.XtraEditors.XtraUserControl.ResetBackColor">
            <summary>
                <para>Resets the <see cref="P:DevExpress.XtraEditors.XtraUserControl.BackColor"/> property back to its default value.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraEditors.XtraUserControl.ResetForeColor">
            <summary>
                <para>Sets the <see cref="P:DevExpress.XtraEditors.XtraUserControl.ForeColor"/> property to <b>Color.Empty</b>.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraEditors.XtraForm">

            <summary>
                <para>Represents a window or dialog box that makes up an application's user interface.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraEditors.XtraForm.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraEditors.XtraForm"/> class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraForm.AllowFormSkin">
            <summary>
                <para>Gets or sets whether the form's title bar and borders are painted using the current skin. This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value><b>true</b> if the form's title bar and borders are painted using the current skin; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraForm.AllowMdiBar">
            <summary>
                <para>Gets or sets whether an MDI bar is allowed for this form. This property is in effect for a parent MDI form, when the title bar skinning feature is enabled.
</para>
            </summary>
            <value><b>true</b> if the MDI bar is allowed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraForm.Appearance">
            <summary>
                <para>Gets the appearance settings used to paint the form.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which contains the form's appearance settings.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraForm.BackColor">
            <summary>
                <para>Gets or sets the background color of the form's client region. 
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Color"/> structure which specifies the background color of the form's client region.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraForm.BackgroundImage">
            <summary>
                <para>Specifies the background image for the form.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Image"/> object that represents the background image.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraForm.BackgroundImageLayout">
            <summary>
                <para>Gets or sets the background image layout as defined in the <see cref="T:System.Windows.Forms.ImageLayout"/> enumeration.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Forms.ImageLayout"/> value that specifies the background image layout.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraForm.BackgroundImageLayoutStore">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>An ImageLayout value.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraForm.BackgroundImageStore">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>An Image object.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraForm.CloseBox">
            <summary>
                <para>Gets or sets whether the form's Close button (x) is visible. This property is in effect when the Form Title Bar Skinning feature is enabled.
</para>
            </summary>
            <value><b>true</b> if the form's Close button (x) is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraForm.Font">
            <summary>
                <para>Gets or sets the form's font. 
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Font"/> object which specifies the form's font. 
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraForm.ForeColor">
            <summary>
                <para>Gets or sets the foreground color of the form's client region. 
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Color"/> structure which specifies the foreground color of the form's client region.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraForm.HtmlImages">
            <summary>
                <para>Gets or sets a collection of images to be inserted into the form's caption using HTML tags. This property is in effect when the Form Title Bar Skinning feature is enabled.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.ImageCollection"/> object that stores a collection of images.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraForm.HtmlText">
            <summary>
                <para>Allows you to specify the form's caption and format it using HTML tags. The property is in effect when the Form Title Bar Skinning feature is applied.
</para>
            </summary>
            <value>A string that specifies the form's caption, formatted using HTML tags.
</value>


        </member>
        <member name="M:DevExpress.XtraEditors.XtraForm.IsAllowSmartMouseWheel(System.Windows.Forms.Control)">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="control">
		@nbsp;

            </param>
            <returns>@nbsp;
</returns>


        </member>
        <member name="M:DevExpress.XtraEditors.XtraForm.IsAllowSmartMouseWheel(System.Windows.Forms.Control,System.Windows.Forms.MouseEventArgs)">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="control">
		@nbsp;

            </param>
            <param name="e">
		@nbsp;

            </param>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraForm.IsSuspendRedraw">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraForm.LookAndFeel">
            <summary>
                <para>Provides access to the settings that control the look and feel of all the DevExpress controls contained within the form.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object whose properties specify the look and feel of the controls owned by the form.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraForm.MaximumClientSize">
            <summary>
                <para>Gets or sets the maximum allowed client size for a form.

</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Size"/> value.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraForm.MaximumSize">
            <summary>
                <para>Gets or sets the form's maximum size.
</para>
            </summary>
            <value>A Size structure that specifies the form's maximum size.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraForm.MinimumClientSize">
            <summary>
                <para>Gets or sets the minimum allowed client size for a form.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Size"/> value.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraForm.MinimumSize">
            <summary>
                <para>Gets or sets the form's minimum size.
</para>
            </summary>
            <value>A Size structure that specifies the form's minimum size.
</value>


        </member>
        <member name="M:DevExpress.XtraEditors.XtraForm.ProcessSmartMouseWheel(System.Windows.Forms.Control,System.Windows.Forms.MouseEventArgs)">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="control">
		@nbsp;

            </param>
            <param name="e">
		@nbsp;

            </param>
            <returns>@nbsp;
</returns>


        </member>
        <member name="M:DevExpress.XtraEditors.XtraForm.ResetBackColor">
            <summary>
                <para>Resets the <see cref="P:DevExpress.XtraEditors.XtraForm.BackColor"/> property back to its default value.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraEditors.XtraForm.ResetForeColor">
            <summary>
                <para>Resets the <see cref="P:DevExpress.XtraEditors.XtraForm.ForeColor"/> property back to its default value.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraEditors.XtraForm.ResumeLayout(System.Boolean)">
            <summary>
                <para>Resumes the usual layout logic, optionally forcing an immediate layout of pending layout requests.

</para>
            </summary>
            <param name="performLayout">
		<b>true</b> to execute pending layout requests; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="M:DevExpress.XtraEditors.XtraForm.ResumeLayout">
            <summary>
                <para>Resumes the usual layout logic.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraEditors.XtraForm.ResumeRedraw">
            <summary>
                <para>Resumes painting the form whose painting had been suspended.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraForm.RightToLeft">
            <summary>
                <para>This property is not supported by the XtraForm class and its descendants.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraForm.RightToLeftLayout">
            <summary>
                <para>This property is not supported by the XtraForm class and its descendants.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraForm.SuppressDeactivation">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="M:DevExpress.XtraEditors.XtraForm.SuspendLayout">
            <summary>
                <para>Temporarily suspends the layout logic for the control.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraEditors.XtraForm.SuspendRedraw">
            <summary>
                <para>Suspends painting the form.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraForm.Text">
            <summary>
                <para>Gets or sets the form's caption.

</para>
            </summary>
            <value>A string that specifies the form's caption.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraForm.TextMdiTab">
            <summary>
                <para>Gets or sets the text displayed within a tab corresponding to the current form. This property is in effect when the current form represents an MDI child within an <see cref="T:DevExpress.XtraTabbedMdi.XtraTabbedMdiManager"/> container.
</para>
            </summary>
            <value>A string value.
</value>


        </member>
        <member name="T:DevExpress.XtraEditors.SplitterControl">

            <summary>
                <para>Represents a <b>splitter</b> control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraEditors.SplitterControl.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraEditors.SplitterControl"/> class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraEditors.SplitterControl.Appearance">
            <summary>
                <para>Gets the appearance settings used to paint the splitter.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which contains the splitter's appearance settings.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.SplitterControl.BackColor">
            <summary>
                <para>Gets or sets the splitter's background color.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Color"/> structure which specifies the splitter's background color.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.SplitterControl.LookAndFeel">
            <summary>
                <para>Provides access to the settings which control the splitter's look and feel. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object whose properties specify the splitter's look and feel.
</value>


        </member>
        <member name="T:DevExpress.XtraEditors.SplitContainerControl">

            <summary>
                <para>Represents a <b>Split Container</b>.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraEditors.SplitContainerControl.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraEditors.SplitContainerControl"/> class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraEditors.SplitContainerControl.AllowSuspendRedraw">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>A Boolean value.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.SplitContainerControl.AutoSize">
            <summary>
                <para>This member is not supported by the SplitContainerControl class.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.SplitContainerControl.AutoSizeMode">
            <summary>
                <para>This member is not supported by the SplitContainerControl class.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="M:DevExpress.XtraEditors.SplitContainerControl.BeginInit">
            <summary>
                <para>Starts the split container's initialization. Initialization occurs at runtime.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraEditors.SplitContainerControl.BeginSplitterMoving">
            <summary>
                <para>Occurs after an end-user clicks the SplitContainerControl's splitter.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraEditors.SplitContainerControl.Collapsed">
            <summary>
                <para>Gets or sets a value indicating whether the panel referred to by the <see cref="P:DevExpress.XtraEditors.SplitContainerControl.CollapsePanel"/> property is collapsed.
</para>
            </summary>
            <value><b>true</b> if the panel is collapsed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.SplitContainerControl.CollapsePanel">
            <summary>
                <para>Gets or sets the panel that is automatically collapsed when clicking on a splitter.
</para>
            </summary>
            <value>A SplitCollapsePanel enumeration value that specifies the panel automatically collapsed when clicking on a splitter.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.SplitContainerControl.Controls">
            <summary>
                <para>Gets the collection of controls contained within the split container control.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Forms.Control.ControlCollection"/> object which represents the collection of controls contained within the split container control.
</value>


        </member>
        <member name="M:DevExpress.XtraEditors.SplitContainerControl.EndInit">
            <summary>
                <para>Ends the split container's initialization. 
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraEditors.SplitContainerControl.FixedPanel">
            <summary>
                <para>Gets or sets the fixed panel.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.SplitFixedPanel"/> enumeration value which specifies the fixed panel.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.SplitContainerControl.Horizontal">
            <summary>
                <para>Gets or sets whether the panels are located one after another or one above another.
</para>
            </summary>
            <value><b>true</b> if the panels are located one after another; <b>false</b> if the panels are located one above another.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.SplitContainerControl.IsPanelCollapsed">
            <summary>
                <para>Gets whether the panel specified by the <see cref="P:DevExpress.XtraEditors.SplitContainerControl.CollapsePanel"/> property is collapsed.
</para>
            </summary>
            <value><b>true</b> if this panel is collapsed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.SplitContainerControl.IsSplitterFixed">
            <summary>
                <para>Gets or sets whether the SplitContainerControl's splitter is locked at the current position.
</para>
            </summary>
            <value><b>true</b>, if the SplitContainerControl's splitter is locked at the current position; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.SplitContainerControl.Panel1">
            <summary>
                <para>Gets the left (or top) panel.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.SplitGroupPanel"/> object which represents the left (or top) panel.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.SplitContainerControl.Panel2">
            <summary>
                <para>Gets the right (or bottom) panel.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.SplitGroupPanel"/> object which represents the right (or bottom) panel.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.SplitContainerControl.PanelVisibility">
            <summary>
                <para>Gets or sets the visibility of the panels.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.SplitPanelVisibility"/> enumeration value which specifies the visibility of panels.
</value>


        </member>
        <member name="M:DevExpress.XtraEditors.SplitContainerControl.SetPanelBorderStyle(DevExpress.XtraEditors.Controls.BorderStyles)">
            <summary>
                <para>Specifies the border style of the panels.
</para>
            </summary>
            <param name="border">
		A <see cref="T:DevExpress.XtraEditors.Controls.BorderStyles"/> enumeration value specifying the border style of the panels.

            </param>


        </member>
        <member name="M:DevExpress.XtraEditors.SplitContainerControl.SetPanelCollapsed(System.Boolean)">
            <summary>
                <para>Collapses or restores the panel referred to by the <see cref="P:DevExpress.XtraEditors.SplitContainerControl.CollapsePanel"/> property.
</para>
            </summary>
            <param name="collapsed">
		<b>true</b> to collapse the panel; <b>false</b> to restore the panel.

            </param>


        </member>
        <member name="P:DevExpress.XtraEditors.SplitContainerControl.ShowCaption">
            <summary>
                <para>Gets or sets whether the split container's caption is displayed.
</para>
            </summary>
            <value><b>true</b> to display the split container's caption; otherwise, <b>false</b>.
</value>


        </member>
        <member name="E:DevExpress.XtraEditors.SplitContainerControl.SplitGroupPanelCollapsed">
            <summary>
                <para>Allows you to respond to collapsing/restoring a panel.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraEditors.SplitContainerControl.SplitGroupPanelCollapsing">
            <summary>
                <para>Allows you to prevent a panel from being collapsed/restored.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraEditors.SplitContainerControl.SplitterBounds">
            <summary>
                <para>Gets the splitter's size and location.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Rectangle"/> structure which represents the size and location of the splitter.
</value>


        </member>
        <member name="E:DevExpress.XtraEditors.SplitContainerControl.SplitterMoved">
            <summary>
                <para>Occurs after the splitter's position has been changed.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraEditors.SplitContainerControl.SplitterMoving">
            <summary>
                <para>Occurs when the splitter's position is being changed.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraEditors.SplitContainerControl.SplitterPosition">
            <summary>
                <para>Gets or sets the splitter's location.
</para>
            </summary>
            <value>An integer value specifying the splitter's current location in pixels, from the side of the fixed panel. 
</value>


        </member>
        <member name="E:DevExpress.XtraEditors.SplitContainerControl.SplitterPositionChanged">
            <summary>
                <para>Occurs after the splitter's position has been changed. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraEditors.SplitContainerControl.SwapPanels">
            <summary>
                <para>Swaps the contents of the first and second panels.

</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraEditors.PanelControl">

            <summary>
                <para>Represents a <b>panel</b> control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraEditors.PanelControl.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraEditors.PanelControl"/> class with default settings.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraEditors.PanelControl.Appearance">
            <summary>
                <para>Gets the appearance settings used to paint the panel.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which contains the panel's appearance settings.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.PanelControl.AutoScroll">
            <summary>
                <para>Not supported.
</para>
            </summary>
            <value>A Boolean value.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.PanelControl.AutoScrollMargin">
            <summary>
                <para>Not supported.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Size"/> object that represents the height and width of the auto-scroll margin, in pixels.

</value>


        </member>
        <member name="P:DevExpress.XtraEditors.PanelControl.AutoScrollMinSize">
            <summary>
                <para>Not supported.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Size"/> object that represents the minimum height and width of the control's scrollbars, in pixels.

</value>


        </member>
        <member name="P:DevExpress.XtraEditors.PanelControl.BackColor">
            <summary>
                <para>Gets or sets the panel's background color.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Color"/> structure which specifies the panel's background color. 
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.PanelControl.BackgroundImage">
            <summary>
                <para>Gets or sets the control's background image. This member is not supported by the PanelControl class. Use the <b>Appearance.Image</b> property instead.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Image"/> object.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.PanelControl.BackgroundImageLayout">
            <summary>
                <para>This member is not supported by the PanelControl class. 

</para>
            </summary>
            <value>An <see cref="T:System.Windows.Forms.ImageLayout"/> value that specifies the position of an image on the control.
</value>


        </member>
        <member name="M:DevExpress.XtraEditors.PanelControl.BeginInit">
            <summary>
                <para>Starts the panel's initialization. Initialization occurs at runtime.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraEditors.PanelControl.BorderStyle">
            <summary>
                <para>Gets or sets the panel's border style.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.Controls.BorderStyles"/> enumeration value specifying the panel's border style.
</value>


        </member>
        <member name="M:DevExpress.XtraEditors.PanelControl.CalcBoundsByClient(System.Drawing.Graphics,System.Drawing.Rectangle)">
            <summary>
                <para>Returns the panel's bounding rectangle, calculated from the bounds of its client region.
</para>
            </summary>
            <param name="graphics">
		A <see cref="T:System.Drawing.Graphics"/> object.

            </param>
            <param name="bounds">
		A <see cref="T:System.Drawing.Rectangle"/> structure which represents the bounding rectangle of the panel's client region.

            </param>
            <returns>A <see cref="T:System.Drawing.Rectangle"/> structure which represents the panel's bounding rectangle, calculated from the bounds of its client region.
</returns>


        </member>
        <member name="P:DevExpress.XtraEditors.PanelControl.ContentImage">
            <summary>
                <para>Gets or sets the group's background image that is displayed "as is", and can be aligned to any panel's edge.
</para>
            </summary>
            <value>An <see cref="T:System.Drawing.Image"/> object.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.PanelControl.ContentImageAlignement">
            <summary>
                <para>Gets or sets the content image's alignment within the panel.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.ContentAlignment"/> value that specifies the content image's alignment.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.PanelControl.ContentImageAlignment">
            <summary>
                <para>Gets or sets the content image's alignment within the panel.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.ContentAlignment"/> value that specifies the content image's alignment.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.PanelControl.DisplayRectangle">
            <summary>
                <para>Gets the bounds of the panel's client region.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Rectangle"/> structure which represents the bounding rectangle of the panel's client region.
</value>


        </member>
        <member name="M:DevExpress.XtraEditors.PanelControl.EndInit">
            <summary>
                <para>Ends the PanelControl's initialization.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraEditors.PanelControl.Font">
            <summary>
                <para>Gets or sets the panel's font.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Font"/> object which specifies the panel's font.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.PanelControl.ForeColor">
            <summary>
                <para>Gets or sets the panel's foreground color.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Color"/> structure which specifies the panel's foreground color.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.PanelControl.LookAndFeel">
            <summary>
                <para>Provides access to the settings which control the panel's look and feel. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object whose properties specify the panel's look and feel.
</value>


        </member>
        <member name="M:DevExpress.XtraEditors.PanelControl.ResetBackColor">
            <summary>
                <para>Resets the <see cref="P:DevExpress.XtraEditors.PanelControl.BackColor"/> property back to its default value.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraEditors.PanelControl.ResetForeColor">
            <summary>
                <para>Resets the <see cref="P:DevExpress.XtraEditors.PanelControl.ForeColor"/> property back to its default value.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraEditors.PanelControl.Text">
            <summary>
                <para>Gets or sets the text associated with the panel control.
</para>
            </summary>
            <value>A string value specifying the text associated with the panel.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.PanelControl.UseCompatibleDrawingMode">
            <summary>
                <para>Gets or sets whether the PanelControl should use the non-default painting mode.

</para>
            </summary>
            <value><b>true</b> if the PanelControl control uses the non-default painting mode; otherwise, <b>false</b>.

</value>


        </member>
        <member name="P:DevExpress.XtraEditors.PanelControl.UseDisabledStatePainter">
            <summary>
                <para>Gets or sets whether the control is painted grayed out, when it's in the disabled state.
</para>
            </summary>
            <value><b>true</b> if the control is painted grayed out, when it's in the disabled state; otherwise, <b>false</b>
</value>


        </member>
        <member name="T:DevExpress.XtraEditors.GroupControl">

            <summary>
                <para>Represents a <b>group</b> control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraEditors.GroupControl.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraEditors.GroupControl"/> class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraEditors.GroupControl.AllowHtmlText">
            <summary>
                <para>Gets or sets whether HTML tags can be used to format the control's <see cref="P:DevExpress.XtraEditors.GroupControl.Text"/>.
</para>
            </summary>
            <value><b>true</b> if HTML tags can be used to format the control's <see cref="P:DevExpress.XtraEditors.GroupControl.Text"/>; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.GroupControl.AppearanceCaption">
            <summary>
                <para>Gets the appearance settings used to paint the group's caption.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which contains the caption's appearance settings.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.GroupControl.CaptionImage">
            <summary>
                <para>Gets or sets the image to display within the group's caption area.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Image"/> object that specifies the group's caption image.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.GroupControl.CaptionImageLocation">
            <summary>
                <para>Gets or sets the relative position of an image within the group caption.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.GroupElementLocation"/> value that specifies the image's position. 
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.GroupControl.CaptionImagePadding">
            <summary>
                <para>Gets or sets the outer indents for the image within the group caption, in pixels.
</para>
            </summary>
            <value>A System.Windows.Forms.Padding object that specifies padding information for the image.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.GroupControl.CaptionLocation">
            <summary>
                <para>Gets or sets the location of the group control's caption.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.Locations"/> enumeration member which specifies the group caption's location.
</value>


        </member>
        <member name="E:DevExpress.XtraEditors.GroupControl.CustomDrawCaption">
            <summary>
                <para>Enables group caption to be custom painted.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraEditors.GroupControl.DisplayRectangle">
            <summary>
                <para>Gets the bounds of the control's client region. 
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Rectangle"/> structure which represents the bounding rectangle of the control's client region. 
</value>


        </member>
        <member name="M:DevExpress.XtraEditors.GroupControl.GetTextBottomLine">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="M:DevExpress.XtraEditors.GroupControl.GetTextLeftLine">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="M:DevExpress.XtraEditors.GroupControl.GetTextRightLine">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="M:DevExpress.XtraEditors.GroupControl.GetTextTopLine">
            <summary>
                <para>This member supports the internal infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <returns>@nbsp;
</returns>


        </member>
        <member name="P:DevExpress.XtraEditors.GroupControl.HtmlImages">
            <summary>
                <para>Gets or sets a collection of images to be inserted into the control's caption (<see cref="P:DevExpress.XtraEditors.GroupControl.Text"/>) using HTML tags.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.ImageCollection"/> object that stores a collection of images.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.GroupControl.ShowCaption">
            <summary>
                <para>Gets or sets whether the group's caption is displayed.
</para>
            </summary>
            <value><b>true</b> to display the group's caption; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.GroupControl.Text">
            <summary>
                <para>Gets or sets the GroupControl's text.
</para>
            </summary>
            <value>A string that specifies the control's text.
</value>


        </member>
        <member name="T:DevExpress.XtraEditors.GroupCaptionCustomDrawEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraEditors.GroupControl.CustomDrawCaption"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraEditors.GroupCaptionCustomDrawEventHandler.Invoke(System.Object,DevExpress.XtraEditors.GroupCaptionCustomDrawEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.XtraEditors.GroupControl.CustomDrawCaption"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This parameter identifies the group control which raised the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.XtraEditors.GroupCaptionCustomDrawEventArgs"/> object that contains event data.

            </param>


        </member>
        <member name="T:DevExpress.XtraEditors.GroupCaptionCustomDrawEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.XtraEditors.GroupControl.CustomDrawCaption"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraEditors.GroupCaptionCustomDrawEventArgs.#ctor(DevExpress.Utils.Drawing.GraphicsCache,DevExpress.Utils.Drawing.ObjectPainter,DevExpress.Utils.Drawing.ObjectInfoArgs)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.XtraEditors.GroupCaptionCustomDrawEventArgs"/> class.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which specifies the storage for the most  used pens, fonts and brushes. This value is assigned to the <b>Cache</b> property.

            </param>
            <param name="painter">
		An <see cref="T:DevExpress.Utils.Drawing.ObjectPainter"/> object that provides facilities for painting an element using the default mechanism. This value is assigned to the <b>Painter</b> property.

            </param>
            <param name="info">
		A <see cref="T:DevExpress.Utils.Drawing.ObjectInfoArgs"/> object that contains information about the element being painted. This value is assigned to the <see cref="P:DevExpress.XtraEditors.GroupCaptionCustomDrawEventArgs.Info"/> property.

            </param>


        </member>
        <member name="P:DevExpress.XtraEditors.GroupCaptionCustomDrawEventArgs.CaptionBounds">
            <summary>
                <para>Gets the painted caption's bounding rectangle.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Rectangle"/> structure which represents the painted caption's bounding rectangle.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.GroupCaptionCustomDrawEventArgs.Info">
            <summary>
                <para>Gets information on the painted group.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.Drawing.GroupObjectInfoArgs"/> object which provides information about the painted group.
</value>


        </member>
        <member name="T:DevExpress.XtraEditors.SplitPanelVisibility">

            <summary>
                <para>Lists values that specify the visibility of the panels within a split container.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraEditors.SplitPanelVisibility.Both">
            <summary>
                <para>Both <b>Panel1</b> and <b>Panel2</b> are visible.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.SplitPanelVisibility.Panel1">
            <summary>
                <para><b>Panel2</b> is hidden.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.SplitPanelVisibility.Panel2">
            <summary>
                <para><b>Panel1</b> is hidden.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.DXMouseEventArgs">

            <summary>
                <para>Provides data for the mouse events.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.DXMouseEventArgs.#ctor(System.Windows.Forms.MouseButtons,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Utils.DXMouseEventArgs"/> class with the specified parameters.
</para>
            </summary>
            <param name="buttons">
		A <see cref="T:System.Windows.Forms.MouseButtons"/> enumeration value that defines which mouse button was pressed. This value is assigned to the <see cref="P:System.Windows.Forms.MouseEventArgs.Button"/> property.

            </param>
            <param name="clicks">
		An integer value which specifies the number of times the mouse button was pressed and released. This value is assigned to the <see cref="P:System.Windows.Forms.MouseEventArgs.Clicks"/> property.

            </param>
            <param name="x">
		An integer value which specifies the x-coordinate of a mouse click, in pixels. This value is assigned to the <see cref="P:System.Windows.Forms.MouseEventArgs.X"/> property.

            </param>
            <param name="y">
		An integer value which specifies the y-coordinate of a mouse click, in pixels. This value is assigned to the <see cref="P:System.Windows.Forms.MouseEventArgs.Y"/> property.

            </param>
            <param name="delta">
		An integer value which specifies a signed count of the number of detents the mouse wheel has rotated. This value is assigned to the <see cref="P:System.Windows.Forms.MouseEventArgs.Delta"/> property.


            </param>


        </member>
        <member name="M:DevExpress.Utils.DXMouseEventArgs.#ctor(System.Windows.Forms.MouseButtons,System.Int32,System.Int32,System.Int32,System.Int32,System.Boolean)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Utils.DXMouseEventArgs"/> class.
</para>
            </summary>
            <param name="buttons">
		A <see cref="T:System.Windows.Forms.MouseButtons"/> enumeration value that defines which mouse button was pressed. This value is assigned to the <see cref="P:System.Windows.Forms.MouseEventArgs.Button"/> property.

            </param>
            <param name="clicks">
		An integer value which specifies the number of times the mouse button was pressed and released. This value is assigned to the <see cref="P:System.Windows.Forms.MouseEventArgs.Clicks"/> property.

            </param>
            <param name="x">
		An integer value which specifies the x-coordinate of a mouse click, in pixels. This value is assigned to the <see cref="P:System.Windows.Forms.MouseEventArgs.X"/> property.

            </param>
            <param name="y">
		An integer value which specifies the y-coordinate of a mouse click, in pixels. This value is assigned to the <see cref="P:System.Windows.Forms.MouseEventArgs.Y"/> property.

            </param>
            <param name="delta">
		An integer value which specifies a signed count of the number of detents the mouse wheel has rotated. This value is assigned to the <see cref="P:System.Windows.Forms.MouseEventArgs.Delta"/> property.

            </param>
            <param name="handled">
		<b>true</b> if the event hasn't been handled by a control; otherwise, <b>false</b>. This value is assigned to the <see cref="P:DevExpress.Utils.DXMouseEventArgs.Handled"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Utils.DXMouseEventArgs.GetMouseArgs(System.Windows.Forms.MouseEventArgs)">
            <summary>
                <para>Converts the <see cref="T:System.Windows.Forms.MouseEventArgs"/> object passed as a parameter to a <see cref="T:DevExpress.Utils.DXMouseEventArgs"/> object.
</para>
            </summary>
            <param name="eventArgs">
		A <see cref="T:System.Windows.Forms.MouseEventArgs"/> object which contains data for the <b>MouseUp</b>, <b>MouseDown</b>, and <b>MouseMove</b> events.

            </param>
            <returns>A <see cref="T:DevExpress.Utils.DXMouseEventArgs"/> object which provides data for the mouse events.
</returns>


        </member>
        <member name="M:DevExpress.Utils.DXMouseEventArgs.GetMouseArgs(System.Windows.Forms.Control,System.EventArgs)">
            <summary>
                <para>Converts the <see cref="T:System.Windows.Forms.MouseEventArgs"/> object passed as a parameter to a <see cref="T:DevExpress.Utils.DXMouseEventArgs"/> object.
</para>
            </summary>
            <param name="control">
		A <see cref="T:System.Windows.Forms.Control"/> descendant.

            </param>
            <param name="eventArgs">
		A <see cref="T:System.Windows.Forms.MouseEventArgs"/> object which contains data for the mouse events.

            </param>
            <returns>A <see cref="T:DevExpress.Utils.DXMouseEventArgs"/> object which provides data for the mouse events.
</returns>


        </member>
        <member name="P:DevExpress.Utils.DXMouseEventArgs.Handled">
            <summary>
                <para>Gets or sets a value specifying whether an event has been handled.
</para>
            </summary>
            <value><b>true</b> if the event hasn't been handled by a control; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Utils.DXMouseEventArgs.IsHMouseWheel">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="M:DevExpress.Utils.DXMouseEventArgs.Sync">
            <summary>
                <para>For internal use.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.BaseAppearanceCollection">

            <summary>
                <para>Serves as a base for the classes that provide the appearance settings used to paint Developer Express .NET controls.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.BaseAppearanceCollection.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Utils.BaseAppearanceCollection"/> class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.BaseAppearanceCollection.Assign(DevExpress.Utils.BaseAppearanceCollection)">
            <summary>
                <para>Copies the settings from the object passed as the parameter.
</para>
            </summary>
            <param name="source">
		A <see cref="T:DevExpress.Utils.BaseAppearanceCollection"/> descendant representing the source of the operation. If <b>null</b> (<b>Nothing</b> in Visual Basic) this method does nothing.

            </param>


        </member>
        <member name="M:DevExpress.Utils.BaseAppearanceCollection.AssignInternal(DevExpress.Utils.BaseAppearanceCollection)">
            <summary>
                <para>Copies the settings from the object passed as the parameter without raising the <see cref="E:DevExpress.Utils.BaseAppearanceCollection.Changed"/> event.
</para>
            </summary>
            <param name="source">
		A <see cref="T:DevExpress.Utils.BaseAppearanceCollection"/> descendant representing the source of the operation. If <b>null</b> (<b>Nothing</b> in Visual Basic) this method does nothing.

            </param>


        </member>
        <member name="M:DevExpress.Utils.BaseAppearanceCollection.BeginUpdate">
            <summary>
                <para>Locks the BaseAppearanceCollection, preventing change notifications (and visual updates) from being raised by the object until the <b>EndUpdate</b> or <b>CancelUpdate</b> method is called.


</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.BaseAppearanceCollection.CancelUpdate">
            <summary>
                <para>Unlocks the BaseAppearanceCollection object after it has been locked by the <b>BeginUpdate</b> method, without causing an immediate visual update.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Utils.BaseAppearanceCollection.Changed">
            <summary>
                <para>Fires when the collection is changed.

</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.BaseAppearanceCollection.Combine(DevExpress.Utils.BaseAppearanceCollection,DevExpress.Utils.AppearanceDefaultInfo[])">
            <summary>
                <para>Copies the activated appearance settings of the object passed as the parameter to the current object, any properties that aren't activated are set to the value in the default appearance object.
</para>
            </summary>
            <param name="appearances">
		A <see cref="T:DevExpress.Utils.BaseAppearanceCollection"/> descendant representing the source of the operation.

            </param>
            <param name="defaults">
		An array of DevExpress.Utils.AppearanceDefaultInfo objects which represent the default appearance settings.

            </param>


        </member>
        <member name="M:DevExpress.Utils.BaseAppearanceCollection.Combine(DevExpress.Utils.BaseAppearanceCollection,DevExpress.Utils.AppearanceDefaultInfo[],System.Boolean)">
            <summary>
                <para>Copies the activated appearance settings of the object passed as the parameter and activates their corresponding options, any properties not activated are set to the value in the default appearance object.
</para>
            </summary>
            <param name="appearances">
		A <see cref="T:DevExpress.Utils.BaseAppearanceCollection"/> descendant representing the source of the operation.

            </param>
            <param name="defaults">
		An array of DevExpress.Utils.AppearanceDefaultInfo objects which represent the default appearance settings.

            </param>
            <param name="setDefaultUseFlag">
		<b>true</b> to enable options which correspond to the activated appearance settings; otherwise, <b>false</b>.

            </param>


        </member>
        <member name="M:DevExpress.Utils.BaseAppearanceCollection.Dispose">
            <summary>
                <para>Releases all the resources used by the current object. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.BaseAppearanceCollection.EndUpdate">
            <summary>
                <para>Unlocks the BaseAppearanceCollection object after a call to the <b>BeginUpdate</b> method and causes an immediate visual update.


</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.BaseAppearanceCollection.GetAppearance(System.String)">
            <summary>
                <para>Gets an appearance object contained within the collection by its name.
</para>
            </summary>
            <param name="name">
		A <see cref="T:System.String"/> value specifying the name of the required appearance object.

            </param>
            <returns>The <see cref="T:DevExpress.Utils.AppearanceObject"/> object within the collection which has the specified name.
</returns>


        </member>
        <member name="M:DevExpress.Utils.BaseAppearanceCollection.GetEnumerator">
            <summary>
                <para>Returns an <see cref="T:System.Collections.IDictionaryEnumerator"/> that can iterate through the hashtable. 
</para>
            </summary>
            <returns>An <see cref="T:System.Collections.IDictionaryEnumerator"/> for the hashtable.
</returns>


        </member>
        <member name="P:DevExpress.Utils.BaseAppearanceCollection.IsDisposed">
            <summary>
                <para>Gets whether the current object has been disposed of.
</para>
            </summary>
            <value><b>true</b> if the current object has been disposed of; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Utils.BaseAppearanceCollection.IsLoading">
            <summary>
                <para>Indicates whether the collection's owner is currently being initialized.
</para>
            </summary>
            <value><b>false</b> always.
</value>


        </member>
        <member name="E:DevExpress.Utils.BaseAppearanceCollection.PaintChanged">
            <summary>
                <para>Occurs when specific settings that control the text appearance are changed.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.BaseAppearanceCollection.Reset">
            <summary>
                <para>Resets the properties of all the appearance objects within the collection to their default values. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.BaseAppearanceCollection.RestoreLayoutFromRegistry(System.String)">
            <summary>
                <para>Restores the appearance settings stored at the specified system registry path. 
</para>
            </summary>
            <param name="path">
		A string value specifying the system registry path. If the specified path doesn't exist, calling this method has no effect.

            </param>


        </member>
        <member name="M:DevExpress.Utils.BaseAppearanceCollection.RestoreLayoutFromStream(System.IO.Stream)">
            <summary>
                <para>Restores the appearance settings from the specified stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> descendant from which the appearance settings are read.

            </param>


        </member>
        <member name="M:DevExpress.Utils.BaseAppearanceCollection.RestoreLayoutFromXml(System.String)">
            <summary>
                <para>Loads the appearance settings stored in the specified XML file.
</para>
            </summary>
            <param name="xmlFile">
		A string value specifying the path to the XML file which contains the appearance settings to be loaded. If the specified file doesn't exist, an exception is raised.

            </param>


        </member>
        <member name="M:DevExpress.Utils.BaseAppearanceCollection.SaveLayoutToRegistry(System.String)">
            <summary>
                <para>Saves the appearance settings to a system registry path.
</para>
            </summary>
            <param name="path">
		A string value specifying the system registry path to which the appearance settings are saved.

            </param>


        </member>
        <member name="M:DevExpress.Utils.BaseAppearanceCollection.SaveLayoutToStream(System.IO.Stream)">
            <summary>
                <para>Saves the appearance settings to a specific stream.
</para>
            </summary>
            <param name="stream">
		A <see cref="T:System.IO.Stream"/> descendant to which the appearance settings are written.

            </param>


        </member>
        <member name="M:DevExpress.Utils.BaseAppearanceCollection.SaveLayoutToXml(System.String)">
            <summary>
                <para>Saves the appearance settings to a specific XML file. 
</para>
            </summary>
            <param name="xmlFile">
		A string value specifying the path to the file in which the appearance settings should be stored. If an empty string is specified, an exception is raised.

            </param>


        </member>
        <member name="M:DevExpress.Utils.BaseAppearanceCollection.ShouldSerialize">
            <summary>
                <para>Tests whether the <see cref="T:DevExpress.Utils.BaseAppearanceCollection"/> should be persisted.
</para>
            </summary>
            <returns><b>true</b> if the object should be persisted; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="E:DevExpress.Utils.BaseAppearanceCollection.SizeChanged">
            <summary>
                <para>Occurs when specific settings that control text size  are changed.

</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.AppearanceOptionsEx">

            <summary>
                <para>Provides appearance options.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.AppearanceOptionsEx.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Utils.AppearanceOptionsEx"/> class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.AppearanceOptionsEx.Assign(DevExpress.Utils.Controls.BaseOptions)">
            <summary>
                <para>Copies all the settings from the options object passed as the parameter and assigns them to the current object.
</para>
            </summary>
            <param name="options">
		A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object.

            </param>


        </member>
        <member name="P:DevExpress.Utils.AppearanceOptionsEx.HighPriority">
            <summary>
                <para>Gets or sets whether the settings specified by the current appearance object have the highest priority.
</para>
            </summary>
            <value><b>true</b> to set the current appearance settings to the highest priority; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.Utils.AppearanceObjectEx">

            <summary>
                <para>Represents an appearance object with extended settings.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.AppearanceObjectEx.#ctor(DevExpress.Utils.IAppearanceOwner)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Utils.AppearanceObjectEx"/> class with the specified owner.
</para>
            </summary>
            <param name="owner">
		An object implementing the <b>IAppearanceOwner</b> interface.

            </param>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObjectEx.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Utils.AppearanceObjectEx"/> class with default settings.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObjectEx.#ctor(DevExpress.Utils.IAppearanceOwner,DevExpress.Utils.AppearanceObject,System.String)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Utils.AppearanceObjectEx"/> class with the specified settings.
</para>
            </summary>
            <param name="owner">
		An object implementing the <b>IAppearanceOwner</b> interface.

            </param>
            <param name="parentAppearance">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which represents the parent appearance object. This value is assigned to the <see cref="P:DevExpress.Utils.AppearanceObject.ParentAppearance"/> property.

            </param>
            <param name="name">
		A <see cref="T:System.String"/> value specifying the name of the created object. This value is assigned to the <see cref="P:DevExpress.Utils.AppearanceObject.Name"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObjectEx.Clone">
            <summary>
                <para>Creates a copy of the current <see cref="T:DevExpress.Utils.AppearanceObjectEx"/> instance.
</para>
            </summary>
            <returns>An <see cref="T:DevExpress.Utils.AppearanceObjectEx"/> object which represents an exact copy of the current object.
</returns>


        </member>
        <member name="P:DevExpress.Utils.AppearanceObjectEx.Options">
            <summary>
                <para>Provides access to the appearance object's options.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceOptionsEx"/> object containing the appearance options.
</value>


        </member>
        <member name="T:DevExpress.Utils.Trimming">

            <summary>
                <para>List values that specify how the characters in a string that does not completely fit into a layout shape are trimmed.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Utils.Trimming.Character">
            <summary>
                <para>Specifies that the text is trimmed to the nearest character.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Trimming.Default">
            <summary>
                <para>The trimming is determined by a control's current settings. The default value is automatically set for a control if an end-user doesn't specify a value.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Trimming.EllipsisCharacter">
            <summary>
                <para>Specifies that the text is trimmed to the nearest character, and an ellipsis is inserted at the end of a trimmed line.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Trimming.EllipsisPath">
            <summary>
                <para>The center is removed from trimmed lines and replaced by an ellipsis. The algorithm keeps as much of the last slash-delimited segment of the line as possible.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Trimming.EllipsisWord">
            <summary>
                <para>Specifies that text is trimmed to the nearest word, and an ellipsis is inserted at the end of a trimmed line.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Trimming.None">
            <summary>
                <para>Specifies no trimming.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Trimming.Word">
            <summary>
                <para>Specifies that text is trimmed to the nearest word.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.AppearanceOptions">

            <summary>
                <para>Provides appearance options.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.AppearanceOptions.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Utils.AppearanceOptions"/> class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.AppearanceOptions.Assign(DevExpress.Utils.Controls.BaseOptions)">
            <summary>
                <para>Copies all settings from the options object passed as the parameter and assigns them to the current object.
</para>
            </summary>
            <param name="options">
		A <see cref="T:DevExpress.Utils.Controls.BaseOptions"/> descendant whose settings are assigned to the current object.

            </param>


        </member>
        <member name="P:DevExpress.Utils.AppearanceOptions.Empty">
            <summary>
                <para>Returns an AppearanceOptions object whose settings are not initialized.
</para>
            </summary>
            <value>An AppearanceOptions object whose settings are not initialized.
</value>


        </member>
        <member name="M:DevExpress.Utils.AppearanceOptions.IsEqual(DevExpress.Utils.AppearanceOptions)">
            <summary>
                <para>Tests whether two objects have the same property values.
</para>
            </summary>
            <param name="options">
		The AppearanceOptions object to which the current object is compared.

            </param>
            <returns><b>true</b> if the current object has the same property values as the specified object; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.Utils.AppearanceOptions.UseBackColor">
            <summary>
                <para>Gets or sets whether to use the <see cref="P:DevExpress.Utils.AppearanceObject.BackColor"/> property value.
</para>
            </summary>
            <value><b>true</b> to use the <see cref="P:DevExpress.Utils.AppearanceObject.BackColor"/> property value; <b>false</b> to use the background color specified by the parent appearance object.
</value>


        </member>
        <member name="P:DevExpress.Utils.AppearanceOptions.UseBorderColor">
            <summary>
                <para>Gets or sets whether to use the <see cref="P:DevExpress.Utils.AppearanceObject.BorderColor"/> property value.
</para>
            </summary>
            <value><b>true</b> to use the <see cref="P:DevExpress.Utils.AppearanceObject.BorderColor"/> property value; <b>false</b> to use the border color specified by the parent appearance object.
</value>


        </member>
        <member name="P:DevExpress.Utils.AppearanceOptions.UseFont">
            <summary>
                <para>Gets or sets whether to use the <see cref="P:DevExpress.Utils.AppearanceObject.Font"/> property value.
</para>
            </summary>
            <value><b>true</b> to use the <see cref="P:DevExpress.Utils.AppearanceObject.Font"/> property value; <b>false</b> to use the font settings specified by the parent appearance object.
</value>


        </member>
        <member name="P:DevExpress.Utils.AppearanceOptions.UseForeColor">
            <summary>
                <para>Gets or sets whether to use the <see cref="P:DevExpress.Utils.AppearanceObject.ForeColor"/> property value.
</para>
            </summary>
            <value><b>true</b> to use the <see cref="P:DevExpress.Utils.AppearanceObject.ForeColor"/> property value; <b>false</b> to use the foreground color specified by the parent appearance object.
</value>


        </member>
        <member name="P:DevExpress.Utils.AppearanceOptions.UseImage">
            <summary>
                <para>Gets or sets whether to use the <see cref="P:DevExpress.Utils.AppearanceObject.Image"/> property value.
</para>
            </summary>
            <value><b>true</b> to use the <see cref="P:DevExpress.Utils.AppearanceObject.Image"/> property value; <b>false</b> to use the background image specified by the parent appearance object.
</value>


        </member>
        <member name="P:DevExpress.Utils.AppearanceOptions.UseTextOptions">
            <summary>
                <para>Gets or sets whether to use the <see cref="P:DevExpress.Utils.AppearanceObject.TextOptions"/> property value.
</para>
            </summary>
            <value><b>true</b> to use the <see cref="P:DevExpress.Utils.AppearanceObject.TextOptions"/> property value; <b>false</b> to use the text options specified by the parent appearance object.
</value>


        </member>
        <member name="T:DevExpress.Utils.AppearanceObject">

            <summary>
                <para>Represents an appearance object.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.#ctor">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Utils.AppearanceObject"/> class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.#ctor(DevExpress.Utils.AppearanceObject)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Utils.AppearanceObject"/> class with the specified parent.
</para>
            </summary>
            <param name="parentAppearance">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object representing the parent appearance object. This value is assigned to the <see cref="P:DevExpress.Utils.AppearanceObject.ParentAppearance"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.#ctor(DevExpress.Utils.AppearanceObject,DevExpress.Utils.AppearanceObject)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Utils.AppearanceObject"/> class.
</para>
            </summary>
            <param name="main">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> representing the appearance object whose settings are used to initialize the created object's properties.

            </param>
            <param name="defaultAppearance">
		A <b>DefaultAppearance</b> object representing default appearance settings.

            </param>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.#ctor(DevExpress.Utils.AppearanceDefault)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Utils.AppearanceObject"/> class with default settings.
</para>
            </summary>
            <param name="appearanceDefault">
		A <see cref="T:DevExpress.Utils.AppearanceDefault"/> object containing the default appearance settings.


            </param>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.#ctor(DevExpress.Utils.IAppearanceOwner,DevExpress.Utils.AppearanceObject)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Utils.AppearanceObject"/> class with the specified owner and parent appearance object.
</para>
            </summary>
            <param name="owner">
		An object implementing the <b>IAppearanceOwner</b> interface.

            </param>
            <param name="parentAppearance">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which represents the parent appearance object. This value is assigned to the <see cref="P:DevExpress.Utils.AppearanceObject.ParentAppearance"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.#ctor(DevExpress.Utils.AppearanceObject,DevExpress.Utils.AppearanceDefault)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Utils.AppearanceObject"/> class with the specified settings.
</para>
            </summary>
            <param name="main">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object whose settings are used to initialize the created object's properties.

            </param>
            <param name="appearanceDefault">
		A <see cref="T:DevExpress.Utils.AppearanceDefault"/> object.

            </param>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.#ctor(System.String)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Utils.AppearanceObject"/> class with the specified name.
</para>
            </summary>
            <param name="name">
		A <see cref="T:System.String"/> value specifying the name of the created object. This value is assigned to the <see cref="P:DevExpress.Utils.AppearanceObject.Name"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.#ctor(DevExpress.Utils.IAppearanceOwner,DevExpress.Utils.AppearanceObject,System.String)">
            <summary>
                <para>Initializes a new instance of the AppearanceObject class with the specified owner, parent and name.
</para>
            </summary>
            <param name="owner">
		An object implementing the <b>IAppearanceOwner</b> interface.

            </param>
            <param name="parentAppearance">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which represents the parent of the created object. This value is assigned to the <see cref="P:DevExpress.Utils.AppearanceObject.ParentAppearance"/> property.

            </param>
            <param name="name">
		A <see cref="T:System.String"/> value specifying the name of the created object. This value is assigned to the <see cref="P:DevExpress.Utils.AppearanceObject.Name"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.#ctor(DevExpress.Utils.AppearanceObject,System.String)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Utils.AppearanceObject"/> class with the specified name and parent.
</para>
            </summary>
            <param name="parentAppearance">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which represents the parent of the created object. This value is assigned to the <see cref="P:DevExpress.Utils.AppearanceObject.ParentAppearance"/> property.

            </param>
            <param name="name">
		A <see cref="T:System.String"/> value specifying the name of the created object. This value is assigned to the <see cref="P:DevExpress.Utils.AppearanceObject.Name"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.#ctor(DevExpress.Utils.IAppearanceOwner,System.Boolean)">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="owner">
		@nbsp;

            </param>
            <param name="reserved">
		@nbsp;

            </param>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.Assign(DevExpress.Utils.AppearanceObject)">
            <summary>
                <para>Copies all settings from the appearance object passed as a parameter.
</para>
            </summary>
            <param name="val">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object representing the source of the operation.

            </param>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.Assign(DevExpress.Utils.AppearanceDefault)">
            <summary>
                <para>Sets the appearance object's settings to default values.
</para>
            </summary>
            <param name="appearanceDefault">
		An <b>AppearanceDefault</b> object containing default appearance settings.

            </param>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.AssignInternal(DevExpress.Utils.AppearanceObject)">
            <summary>
                <para>Copies all the settings in the appearance object passed as the parameter to the current object without raising the <see cref="E:DevExpress.Utils.AppearanceObject.Changed"/> event.
</para>
            </summary>
            <param name="val">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object representing the source of the operation.

            </param>


        </member>
        <member name="P:DevExpress.Utils.AppearanceObject.BackColor">
            <summary>
                <para>Gets or sets the background color.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Color"/> object specifying the background color.
</value>


        </member>
        <member name="P:DevExpress.Utils.AppearanceObject.BackColor2">
            <summary>
                <para>Gets or sets the ending color of the  background's gradient brush.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Color"/> object specifying the ending color of the linear gradient brush used to fill the background.
</value>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.BeginUpdate">
            <summary>
                <para>Locks the AppearanceObject object by disallowing visual updates until the <b>EndUpdate</b> or <b>CancelUpdate</b> method is called.


</para>
            </summary>


        </member>
        <member name="P:DevExpress.Utils.AppearanceObject.BorderColor">
            <summary>
                <para>Gets or sets the border color.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Color"/> object specifying the border color.
</value>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.CalcDefaultTextSize(System.Drawing.Graphics)">
            <summary>
                <para>Calculates the size of a string painted with the font specified by the current object.
</para>
            </summary>
            <param name="g">
		A <see cref="T:System.Drawing.Graphics"/> object which provides painting facilities.

            </param>
            <returns>A <see cref="T:System.Drawing.Size"/> structure which represents the size (in pixels) of a string.
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.CalcDefaultTextSize">
            <summary>
                <para>Calculates the size of a string painted with the default font.
</para>
            </summary>
            <returns>A <see cref="T:System.Drawing.Size"/> structure which represents the size (in pixels) of a string.
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.CalcTextSize(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.StringFormat,System.String,System.Int32)">
            <summary>
                <para>Calculates the length of the specified string drawn with the specified <see cref="T:System.Drawing.StringFormat"/> object.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which provides storage for pens, fonts and brushes.

            </param>
            <param name="sf">
		A <see cref="T:System.Drawing.StringFormat"/> object which represents formatting information, such as line spacing and alignment, for the string.

            </param>
            <param name="s">
		A <see cref="T:System.String"/> value which represents a string to measure.

            </param>
            <param name="width">
		An integer value specifying the maximum width of the string (in pixels).

            </param>
            <returns>A <see cref="T:System.Drawing.SizeF"/> structure which represents the size, in pixels, of the string.
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.CalcTextSize(System.Drawing.Graphics,System.Drawing.StringFormat,System.String,System.Int32)">
            <summary>
                <para>Calculates the length of the specified string drawn with the specified <see cref="T:System.Drawing.StringFormat"/> object.
</para>
            </summary>
            <param name="g">
		A <see cref="T:System.Drawing.Graphics"/> object which provides painting facilities.

            </param>
            <param name="sf">
		A <see cref="T:System.Drawing.StringFormat"/> object which represents formatting information, such as the line spacing for the string.

            </param>
            <param name="s">
		A <see cref="T:System.String"/> value which represents the string to measure.

            </param>
            <param name="width">
		An integer value specifying the maximum width of the string (in pixels).

            </param>
            <returns>A <see cref="T:System.Drawing.SizeF"/> structure which represents the size (in pixels) of the string.
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.CalcTextSize(System.Drawing.Graphics,System.String,System.Int32)">
            <summary>
                <para>Calculates the length of the specified string.
</para>
            </summary>
            <param name="g">
		A <see cref="T:System.Drawing.Graphics"/> object which provides painting facilities.

            </param>
            <param name="s">
		A <see cref="T:System.String"/> value which represents a string to measure.

            </param>
            <param name="width">
		An integer value specifying the maximum width of the string (in pixels).

            </param>
            <returns>A <see cref="T:System.Drawing.SizeF"/> structure which represents the size (in pixels) of the string.
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.CalcTextSize(DevExpress.Utils.Drawing.GraphicsCache,System.String,System.Int32)">
            <summary>
                <para>Calculates the length of the specified string.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which provides storage for pens, fonts and brushes.

            </param>
            <param name="s">
		A <see cref="T:System.String"/> value which represents the string to measure.

            </param>
            <param name="width">
		An integer value specifying the maximum width of the string (in pixels).

            </param>
            <returns>A <see cref="T:System.Drawing.SizeF"/> structure which represents the size, in pixels, of the string.
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.CalcTextSizeInt(System.Drawing.Graphics,System.Drawing.StringFormat,System.String,System.Int32)">
            <summary>
                <para>Calculates the length of the specified string drawn with the specified <see cref="T:System.Drawing.StringFormat"/> object.
</para>
            </summary>
            <param name="g">
		A <see cref="T:System.Drawing.Graphics"/> object that provides painting facilities.

            </param>
            <param name="sf">
		A <see cref="T:System.Drawing.StringFormat"/> object that contains formatting information, such as the line spacing and alignment of the string.

            </param>
            <param name="s">
		The string to measure.

            </param>
            <param name="width">
		An integer value specifying the maximum width of the string (in pixels).

            </param>
            <returns>A <see cref="T:System.Drawing.Size"/> structure that is the size of the string (in pixels).
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.CalcTextSizeInt(DevExpress.Utils.Drawing.GraphicsCache,System.String,System.Int32)">
            <summary>
                <para>Calculates the length of the specified string.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object that provides storage for pens, fonts and brushes.

            </param>
            <param name="s">
		The string to measure.

            </param>
            <param name="width">
		An integer value specifying the maximum width of the string (in pixels).

            </param>
            <returns>A <see cref="T:System.Drawing.Size"/> structure that is the size of the string (in pixels).
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.CalcTextSizeInt(System.Drawing.Graphics,System.String,System.Int32)">
            <summary>
                <para>Calculates the length of the specified string.
</para>
            </summary>
            <param name="g">
		A <see cref="T:System.Drawing.Graphics"/> object that provides painting facilities.

            </param>
            <param name="s">
		The string to measure.

            </param>
            <param name="width">
		An integer value specifying the maximum width of the string (in pixels).

            </param>
            <returns>A <see cref="T:System.Drawing.Size"/> structure that is the size of the string (in pixels).
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.CalcTextSizeInt(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.StringFormat,System.String,System.Int32)">
            <summary>
                <para>Calculates the length of the specified string drawn with the specified <see cref="T:System.Drawing.StringFormat"/> object.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object that provides storage for pens, fonts and brushes.

            </param>
            <param name="sf">
		A <see cref="T:System.Drawing.StringFormat"/> object that contains formatting information, such as the line spacing and alignment of the string.

            </param>
            <param name="s">
		The string to measure.

            </param>
            <param name="width">
		An integer value specifying the maximum width of the string (in pixels).

            </param>
            <returns>A <see cref="T:System.Drawing.Size"/> structure that is the size of the string (in pixels).
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.CancelUpdate">
            <summary>
                <para>Unlocks the AppearanceObject object after it has been locked by the <b>BeginUpdate</b> method, without causing an immediate visual update.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Utils.AppearanceObject.Changed">
            <summary>
                <para>Fires when the appearance object's properties are changed. 
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.Clone">
            <summary>
                <para>Creates a copy of the current <see cref="T:DevExpress.Utils.AppearanceObject"/> instance.
</para>
            </summary>
            <returns>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which represents an exact copy of the current appearance object.
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.Combine(DevExpress.Utils.AppearanceObject)">
            <summary>
                <para>Copies the activated settings from the appearance object passed as the parameter.
</para>
            </summary>
            <param name="val">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object whose settings are going to be copied.

            </param>


        </member>
        <member name="P:DevExpress.Utils.AppearanceObject.ControlAppearance">
            <summary>
                <para>Gets the default appearance settings which are used to paint 3-D elements in Windows.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which represents the default appearance settings used to paint 3-D elements in Windows.
</value>


        </member>
        <member name="P:DevExpress.Utils.AppearanceObject.DefaultFont">
            <summary>
                <para>Gets the default font of the appearance object.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Font"/> object which represents the default font. The default value is the <b>Tahoma</b> font.
</value>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.Dispose">
            <summary>
                <para>Releases all resources used by the current appearance object.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.DrawBackground(System.Drawing.Graphics,DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle)">
            <summary>
                <para>Draws the background.
</para>
            </summary>
            <param name="graphics">
		A <see cref="T:System.Drawing.Graphics"/> object that provides a means for painting.

            </param>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object providing storage for the pens, fonts and brushes used during painting.

            </param>
            <param name="bounds">
		A <see cref="T:System.Drawing.Rectangle"/> structure specifying the drawing area.

            </param>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.DrawBackground(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle)">
            <summary>
                <para>Draws the background for the area contained within the specified boundary.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object providing storage for the pens, fonts and brushes used during painting.

            </param>
            <param name="bounds">
		A <see cref="T:System.Drawing.Rectangle"/> structure specifying the drawing area.

            </param>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.DrawBackground(System.Drawing.Graphics,DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle,System.Boolean)">
            <summary>
                <para>Draws the background for the area contained within the specified boundary.
</para>
            </summary>
            <param name="graphics">
		A <see cref="T:System.Drawing.Graphics"/> object that provides a means for painting.

            </param>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object providing storage for the pens, fonts and brushes used during painting.

            </param>
            <param name="bounds">
		A <see cref="T:System.Drawing.Rectangle"/> structure specifying the drawing area.

            </param>
            <param name="useZeroOffset">
		A Boolean value.

            </param>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.DrawBackground(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle,System.Boolean)">
            <summary>
                <para>Draws the background for the area contained within the specified boundary.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object providing storage for the pens, fonts and brushes used during painting.

            </param>
            <param name="bounds">
		A <see cref="T:System.Drawing.Rectangle"/> structure specifying the drawing area.

            </param>
            <param name="useZeroOffset">
		A Boolean value.

            </param>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.DrawString(DevExpress.Utils.Drawing.GraphicsCache,System.String,System.Drawing.Rectangle)">
            <summary>
                <para>Draws a text string at the specified position.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object providing storage for the pens, fonts and brushes used during painting.

            </param>
            <param name="text">
		A <see cref="T:System.String"/> value representing the text to be drawn.

            </param>
            <param name="bounds">
		A <see cref="T:System.Drawing.Rectangle"/> value representing the drawing area.

            </param>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.DrawString(DevExpress.Utils.Drawing.GraphicsCache,System.String,System.Drawing.Rectangle,System.Drawing.Brush)">
            <summary>
                <para>Draws a text string at the specified position using the specified color.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object providing storage for the pens, fonts and brushes used during painting.

            </param>
            <param name="text">
		A <see cref="T:System.String"/> value representing the text to be drawn.

            </param>
            <param name="bounds">
		A <see cref="T:System.Drawing.Rectangle"/> value representing the drawing area.

            </param>
            <param name="foreBrush">
		A <see cref="T:System.Drawing.Brush"/> object which determines the color and texture of the drawn text.

            </param>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.DrawString(DevExpress.Utils.Drawing.GraphicsCache,System.String,System.Drawing.Rectangle,System.Drawing.Brush,System.Drawing.StringFormat)">
            <summary>
                <para>Draws a text string at the specified position using the specified color and format.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object providing storage for the pens, fonts and brushes used during painting.

            </param>
            <param name="text">
		A <see cref="T:System.String"/> value representing the text to be drawn.

            </param>
            <param name="bounds">
		A <see cref="T:System.Drawing.Rectangle"/> value representing the drawing area.

            </param>
            <param name="foreBrush">
		A <see cref="T:System.Drawing.Brush"/> object which determines the color and texture of the drawn text.

            </param>
            <param name="format">
		A <see cref="T:System.Drawing.StringFormat"/> object which specifies formatting attributes, such as the line spacing and alignment, that are applied to the drawn text.

            </param>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.DrawString(DevExpress.Utils.Drawing.GraphicsCache,System.String,System.Drawing.Rectangle,System.Drawing.StringFormat)">
            <summary>
                <para>Draws a text string at the specified position using the specified format.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object providing storage for the pens, fonts and brushes used during painting.

            </param>
            <param name="text">
		A <see cref="T:System.String"/> value representing the text to be drawn.

            </param>
            <param name="bounds">
		A <see cref="T:System.Drawing.Rectangle"/> value representing the drawing area.

            </param>
            <param name="format">
		A <see cref="T:System.Drawing.StringFormat"/> object which specifies formatting attributes, such as the line spacing and alignment, that are applied to the drawn text.

            </param>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.DrawString(DevExpress.Utils.Drawing.GraphicsCache,System.String,System.Drawing.Rectangle,System.Drawing.Font,System.Drawing.StringFormat)">
            <summary>
                <para>Draws a text string at the specified position using the specified font and format.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object providing storage for the pens, fonts and brushes used during painting.

            </param>
            <param name="text">
		A <see cref="T:System.String"/> value representing the text to be drawn.

            </param>
            <param name="bounds">
		A <see cref="T:System.Drawing.Rectangle"/> value representing the drawing area.

            </param>
            <param name="font">
		A <see cref="T:System.Drawing.Font"/> object which defines the font and associated settings of the text to be drawn.

            </param>
            <param name="format">
		A <see cref="T:System.Drawing.StringFormat"/> object which specifies formatting attributes, such as the line spacing and alignment, that are applied to the drawn text.

            </param>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.DrawString(DevExpress.Utils.Drawing.GraphicsCache,System.String,System.Drawing.Rectangle,System.Drawing.Font,System.Drawing.Brush,System.Drawing.StringFormat)">
            <summary>
                <para>Draws a text string at the specified position using the specified font, color and format.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which provides the storage for the pens, fonts and brushes used during painting.

            </param>
            <param name="s">
		A <see cref="T:System.String"/> value which represents the text to be drawn.

            </param>
            <param name="bounds">
		A <see cref="T:System.Drawing.Rectangle"/> structure which represents the drawing area.

            </param>
            <param name="font">
		A <see cref="T:System.Drawing.Font"/> object which defines the font and associated settings of the text to be drawn.

            </param>
            <param name="foreBrush">
		A <see cref="T:System.Drawing.Brush"/> object which specifies the color and texture of the drawn text.

            </param>
            <param name="strFormat">
		A <see cref="T:System.Drawing.StringFormat"/> object which specifies formatting attributes such as the line spacing and alignment that are applied to the drawn text.

            </param>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.DrawVString(DevExpress.Utils.Drawing.GraphicsCache,System.String,System.Drawing.Font,System.Drawing.Brush,System.Drawing.Rectangle,System.Drawing.StringFormat,System.Int32)">
            <summary>
                <para>Draws a text string vertically with the specified angle and at the specified position using the specified font, color and format.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object which provides the storage for the pens, fonts and brushes used during painting.

            </param>
            <param name="text">
		A <see cref="T:System.String"/> value which represents the text to be drawn.

            </param>
            <param name="font">
		A <see cref="T:System.Drawing.Font"/> object which defines the font and associated settings of the text to be drawn.

            </param>
            <param name="foreBrush">
		A <see cref="T:System.Drawing.Brush"/> object which specifies the color and texture of the drawn text.

            </param>
            <param name="bounds">
		A <see cref="T:System.Drawing.Rectangle"/> structure which represents the drawing area.

            </param>
            <param name="strFormat">
		A <see cref="T:System.Drawing.StringFormat"/> object which specifies formatting attributes such as the line spacing and alignment that are applied to the drawn text.

            </param>
            <param name="angle">
		An integer value specifying the angle in degrees at which the text should be drawn.

            </param>


        </member>
        <member name="P:DevExpress.Utils.AppearanceObject.EmptyAppearance">
            <summary>
                <para>Returns an empty appearance object.
</para>
            </summary>
            <value>An AppearanceObject object whose settings are not initialized.
</value>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.EndUpdate">
            <summary>
                <para>Unlocks the AppearanceObject object after a call to the <b>BeginUpdate</b> method and causes an immediate visual update.


</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.FillRectangle(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle)">
            <summary>
                <para>Fills the interior of a rectangle specified by a <see cref="T:System.Drawing.Rectangle"/> structure.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object providing storage for the pens, fonts and brushes used during painting.

            </param>
            <param name="bounds">
		A <see cref="T:System.Drawing.Rectangle"/> value representing the drawing area.

            </param>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.FillRectangle(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle,System.Boolean)">
            <summary>
                <para>Fills the interior of a rectangle specified by a <see cref="T:System.Drawing.Rectangle"/> structure.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object providing storage for the pens, fonts and brushes used during painting.

            </param>
            <param name="bounds">
		A <see cref="T:System.Drawing.Rectangle"/> value representing the drawing area.

            </param>
            <param name="useZeroOffset">
		A Boolean value.

            </param>


        </member>
        <member name="P:DevExpress.Utils.AppearanceObject.Font">
            <summary>
                <para>Gets or sets the font used to paint the text.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Font"/> object specifying the font used to display the contents of elements.
</value>


        </member>
        <member name="P:DevExpress.Utils.AppearanceObject.FontHeight">
            <summary>
                <para>Gets the  height of the font used by the apperance object.
</para>
            </summary>
            <value>An integer value which specifies the font's height.
</value>


        </member>
        <member name="P:DevExpress.Utils.AppearanceObject.ForeColor">
            <summary>
                <para>Gets or sets the foreground color. 
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Color"/> structure specifying the foreground color.
</value>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.GetBackBrush(DevExpress.Utils.Drawing.GraphicsCache)">
            <summary>
                <para>Gets the solid brush used to draw the element's background.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object that provides access to the pens and brushes used for painting.

            </param>
            <returns>A <see cref="T:System.Drawing.Brush"/> descendant representing the brush specified by the appearance object's settings.
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.GetBackBrush(DevExpress.Utils.Drawing.GraphicsCache,System.Drawing.Rectangle)">
            <summary>
                <para>Gets the brush used to draw an element's background.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object that provides access to the pens and brushes used for painting.

            </param>
            <param name="rect">
		A <see cref="T:System.Drawing.Rectangle"/> structure specifying the rectangle for which a brush is created.

            </param>
            <returns>A <see cref="T:System.Drawing.Brush"/> descendant representing the brush specified by the appearance object's settings.
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.GetBackColor">
            <summary>
                <para>Gets the background color specified by the appearance object's settings.
</para>
            </summary>
            <returns>A <see cref="T:System.Drawing.Color"/> object representing the background color specified by the appearance object's settings.
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.GetBackColor2">
            <summary>
                <para>Gets the ending color of the linear gradient brush, specified by the appearance object's settings.
</para>
            </summary>
            <returns>A <see cref="T:System.Drawing.Color"/> object representing the ending color of the linear gradient brush as specified by the appearance object's settings.
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.GetBackPen(DevExpress.Utils.Drawing.GraphicsCache)">
            <summary>
                <para>Gets the pen used to draw the element's background lines and curves.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object that provides access to the pens and brushes used for painting.

            </param>
            <returns>A <see cref="T:System.Drawing.Pen"/> object representing a pen specified by the appearance object's settings.
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.GetBorderBrush(DevExpress.Utils.Drawing.GraphicsCache)">
            <summary>
                <para>Gets the brush used to draw the element's border.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object that provides access to the pens and brushes used for painting.

            </param>
            <returns>A <see cref="T:System.Drawing.Brush"/> descendant representing a brush specified by the appearance object's settings.
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.GetBorderColor">
            <summary>
                <para>Gets the element's border color specified by the appearance object's settings.
</para>
            </summary>
            <returns>A <see cref="T:System.Drawing.Color"/> object representing the element's border color specified by the appearance object's settings.
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.GetBorderPen(DevExpress.Utils.Drawing.GraphicsCache)">
            <summary>
                <para>Gets the pen used to draw the border's lines and curves. 
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object that provides access to the pens and brushes used for painting.

            </param>
            <returns>A <see cref="T:System.Drawing.Pen"/> object representing a pen specified by the appearance object's settings.
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.GetFont">
            <summary>
                <para>Gets the font settings specified by the appearance object's settings.
</para>
            </summary>
            <returns>A <see cref="T:System.Drawing.Font"/> object specifying the font used to display the contents of elements.
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.GetForeBrush(DevExpress.Utils.Drawing.GraphicsCache)">
            <summary>
                <para>Gets the solid brush whose color is specified by the <see cref="P:DevExpress.Utils.AppearanceObject.ForeColor"/> property.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object that provides access to the pens and brushes used for painting.

            </param>
            <returns>A <see cref="T:System.Drawing.Brush"/> descendant representing a brush specified by the appearance object's settings.
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.GetForeColor">
            <summary>
                <para>Gets the foreground color specified by the appearance object's settings.
</para>
            </summary>
            <returns>A <see cref="T:System.Drawing.Color"/> structure specifying the element content's foreground color.
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.GetForePen(DevExpress.Utils.Drawing.GraphicsCache)">
            <summary>
                <para>Gets the pen whose color is specified by the <see cref="P:DevExpress.Utils.AppearanceObject.ForeColor"/> property.
</para>
            </summary>
            <param name="cache">
		A <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object that provides access to the pens and brushes used for painting.

            </param>
            <returns>A <see cref="T:System.Drawing.Pen"/> object representing a pen specified by the appearance object's settings.
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.GetGradientMode">
            <summary>
                <para>Gets the background gradient's direction specified by the appearance object's settings.
</para>
            </summary>
            <returns>A <see cref="T:System.Drawing.Drawing2D.LinearGradientMode"/> enumeration value giving the gradient's direction as specified by the appearance object's settings.
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.GetImage">
            <summary>
                <para>Gets the background image specified by the appearance object's settings.
</para>
            </summary>
            <returns>A <see cref="T:System.Drawing.Image"/> object representing the element's background image as specified by the appearance object's settings.
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.GetStringFormat">
            <summary>
                <para>Gets the formatting attributes specified by the appearance object's settings.
</para>
            </summary>
            <returns>A <see cref="T:System.Drawing.StringFormat"/> object which specifies formatting attributes, such as the line spacing and alignment.
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.GetStringFormat(DevExpress.Utils.TextOptions)">
            <summary>
                <para>Gets the formatting attributes specified by the <see cref="P:DevExpress.Utils.AppearanceObject.TextOptions"/> object passed as the parameter.
</para>
            </summary>
            <param name="defaultOptions">
		A <see cref="T:DevExpress.Utils.TextOptions"/> object which provides the text settings.

            </param>
            <returns>A <see cref="T:System.Drawing.StringFormat"/> object which specifies formatting attributes (such as the line spacing and alignment) based on the text settings specified.
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.GetTextOptions">
            <summary>
                <para>Gets the text options specified by the appearance object's settings.
</para>
            </summary>
            <returns>A <b>TextOptions</b> object containing the text options.
</returns>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.GetTextureBrush">
            <summary>
                <para>Returns a TextureBrush object which uses the image specified by the <see cref="P:DevExpress.Utils.AppearanceObject.Image"/> property to fill the interior of a shape.
</para>
            </summary>
            <returns>A System.Drawing.TextureBrush object which represents the brush that fills the interior of a shape with the image specified by the <see cref="P:DevExpress.Utils.AppearanceObject.Image"/> property.
</returns>


        </member>
        <member name="P:DevExpress.Utils.AppearanceObject.GradientMode">
            <summary>
                <para>Gets or sets the background gradient's direction.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Drawing2D.LinearGradientMode"/> enumeration value specifying the gradient's direction.
</value>


        </member>
        <member name="P:DevExpress.Utils.AppearanceObject.HAlignment">
            <summary>
                <para>Gets the horizontal alignment of the appearance object's text.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.HorzAlignment"/> enumeration value specifying the horizontal alignment of the text.
</value>


        </member>
        <member name="P:DevExpress.Utils.AppearanceObject.Image">
            <summary>
                <para>Gets or sets the background image.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Image"/> object specifying the background image.
</value>


        </member>
        <member name="P:DevExpress.Utils.AppearanceObject.IsDisposed">
            <summary>
                <para>Gets whether the current object has been disposed of.
</para>
            </summary>
            <value><b>true</b> if the current object has been disposed of; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.IsEqual(DevExpress.Utils.AppearanceObject)">
            <summary>
                <para>Tests whether two objects have the same property values.
</para>
            </summary>
            <param name="val">
		The AppearanceObject object to which the current object is compared.

            </param>
            <returns><b>true</b> if the current object has the same property values as the specified object; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.Utils.AppearanceObject.Name">
            <summary>
                <para>Gets or sets the name of the current appearance object.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value which specifies the name of the current appearance object.
</value>


        </member>
        <member name="P:DevExpress.Utils.AppearanceObject.Options">
            <summary>
                <para>Provides access to the appearance object's options.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceOptions"/> object containing the appearance options.
</value>


        </member>
        <member name="E:DevExpress.Utils.AppearanceObject.PaintChanged">
            <summary>
                <para>Occurs when specific settings that control the text appearance are changed.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Utils.AppearanceObject.ParentAppearance">
            <summary>
                <para>Gets the parent appearance object.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object representing the parent appearance object. <b>null</b> (<b>Nothing</b> in Visual Basic) if no parent appearance object is assigned.
</value>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.Reset">
            <summary>
                <para>Reverts the appearance object's properties to their default values.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.ShouldSerialize">
            <summary>
                <para>Tests whether the <see cref="T:DevExpress.Utils.AppearanceObject"/> should be persisted.
</para>
            </summary>
            <returns><b>true</b> if the object should be persisted; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="E:DevExpress.Utils.AppearanceObject.SizeChanged">
            <summary>
                <para>Occurs when specific settings that control the size of text are changed.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Utils.AppearanceObject.TextOptions">
            <summary>
                <para>Provides access to the appearance object's text options.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.TextOptions"/> object containing text options.
</value>


        </member>
        <member name="M:DevExpress.Utils.AppearanceObject.ToString">
            <summary>
                <para>Returns a string representing the current appearance object.
</para>
            </summary>
            <returns>A <see cref="T:System.String"/> value which represents an appearance object.
</returns>


        </member>
        <member name="T:DevExpress.Utils.GroupElementLocation">

            <summary>
                <para>Contains values that specify how an element is displayed relative to a text label within a group control.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Utils.GroupElementLocation.AfterText">
            <summary>
                <para>Displays an element after a text label.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.GroupElementLocation.BeforeText">
            <summary>
                <para>Displays an element before a text label.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.GroupElementLocation.Default">
            <summary>
                <para>Specifies the default position of an element relative to a text label.
Currently, the <b>Default</b> option is equivalent to the <b>BeforeText</b> option.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.ToolTipStyle">

            <summary>
                <para>Enumerates paint styles for tooltips.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Utils.ToolTipStyle.Default">
            <summary>
                <para>The style of regular tooltips is automatically determined by the Windows OS and currently selected Windows theme.
In Windows Vista and Windows 7, if no classic Windows theme is applied, tooltips are painted using the <see cref="F:DevExpress.Utils.ToolTipStyle.Windows7"/> style.
In other cases, tooltips are painted using the <see cref="F:DevExpress.Utils.ToolTipStyle.WindowsXP"/> style.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.ToolTipStyle.Windows7">
            <summary>
                <para>Regular tooltips are painted as those in Windows 7 (this style is in effect when a program runs under Windows Vista or Windows 7, provided that no classic Windows theme is applied).
<para>

</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.ToolTipStyle.WindowsXP">
            <summary>
                <para>Regular tooltips are painted as those in Windows XP.
<para>

</para>
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.HideException">

            <summary>
                <para>Prevents default actions when handling specific events. 
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.HideException.#ctor">
            <summary>
                <para>Initializes a new <see cref="T:DevExpress.Utils.HideException"/> class instance.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.SharedImageCollection">

            <summary>
                <para>An image collection that allows you to share images between controls within multiple forms.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.SharedImageCollection.#ctor">
            <summary>
                <para>Initializes a new instance of the SharedImageCollection class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.SharedImageCollection.#ctor(System.ComponentModel.IContainer)">
            <summary>
                <para>Initializes a new instance of the SharedImageCollection class with the specified container.
</para>
            </summary>
            <param name="container">
		An object that implements the <see cref="T:System.ComponentModel.IContainer"/> interface.

            </param>


        </member>
        <member name="M:DevExpress.Utils.SharedImageCollection.BeginInit">
            <summary>
                <para>Notifies the image collection that initialization has started
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.SharedImageCollection.EndInit">
            <summary>
                <para>Notifies the image collection that initialization is complete.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.SharedImageCollection.ImageSizeResourceName">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="P:DevExpress.Utils.SharedImageCollection.ImageSource">
            <summary>
                <para>Gets or sets the inner collection of images for the SharedImageCollection.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.ImageCollection"/> object that is the inner collection of images.
</value>


        </member>
        <member name="P:DevExpress.Utils.SharedImageCollection.InstanceCount">
            <summary>
                <para>Gets the number of existing SharedImageCollection instances.
</para>
            </summary>
            <value>An integer value that is the number of existing SharedImageCollection instances.
</value>


        </member>
        <member name="P:DevExpress.Utils.SharedImageCollection.ParentControl">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="F:DevExpress.Utils.SharedImageCollection.TimestampResourceName">
            <summary>
                <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="T:DevExpress.Utils.ToolTipLocation">

            <summary>
                <para>Specifies the location where a tooltip is displayed in relation to the mouse cursor position.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Utils.ToolTipLocation.BottomCenter">
            <summary>
                <para>A tooltip appears vertically aligned at the bottom, and horizontally centered relative to the mouse cursor position.
<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.ToolTipLocation.BottomLeft">
            <summary>
                <para>A tooltip appears vertically aligned at the bottom, and horizontally aligned on the left relative to the mouse cursor position.
<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.ToolTipLocation.BottomRight">
            <summary>
                <para>A tooltip appears vertically aligned at the bottom, and horizontally aligned on the right relative to the mouse cursor position.
<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.ToolTipLocation.Default">
            <summary>
                <para>The default tooltip location.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.ToolTipLocation.Fixed">
            <summary>
                <para>For internal use.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.ToolTipLocation.LeftBottom">
            <summary>
                <para>A tooltip appears horizontally aligned on the left, and vertically aligned at the bottom relative to the mouse cursor position.
<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.ToolTipLocation.LeftCenter">
            <summary>
                <para>A tooltip appears horizontally aligned on the left, and vertically centered relative to the mouse cursor position.
<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.ToolTipLocation.LeftTop">
            <summary>
                <para>A tooltip appears horizontally aligned on the left, and vertically aligned at the top relative to the mouse cursor position.
<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.ToolTipLocation.RightBottom">
            <summary>
                <para>A tooltip appears horizontally aligned on the right, and vertically aligned at the bottom relative to the mouse cursor position.
<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.ToolTipLocation.RightCenter">
            <summary>
                <para>A tooltip appears horizontally aligned on the right, and vertically centered relative to the mouse cursor position.
<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.ToolTipLocation.RightTop">
            <summary>
                <para>A tooltip appears horizontally aligned on the right, and vertically aligned at the top relative to the mouse cursor position.
<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.ToolTipLocation.TopCenter">
            <summary>
                <para>A tooltip appears vertically aligned at the top, and horizontally centered relative to the mouse cursor position.
<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.ToolTipLocation.TopLeft">
            <summary>
                <para>A tooltip appears vertically aligned at the top, and horizontally aligned on the left relative to the mouse cursor position.
<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.ToolTipLocation.TopRight">
            <summary>
                <para>A tooltip appears vertically aligned at the top, and horizontally aligned on the right relative to the mouse cursor position.
<para></para>
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.ToolTipIconType">

            <summary>
                <para>Lists values specifying icon types to be displayed within tooltips.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Utils.ToolTipIconType.Application">
            <summary>
                <para>A tooltip contains the default application icon.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.ToolTipIconType.Asterisk">
            <summary>
                <para>A tooltip contains the system asterisk icon.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.ToolTipIconType.Error">
            <summary>
                <para>A tooltip contains the system error icon.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.ToolTipIconType.Exclamation">
            <summary>
                <para>A tooltip contains the system exclamation icon.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.ToolTipIconType.Hand">
            <summary>
                <para>A tooltip contains the system hand icon.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.ToolTipIconType.Information">
            <summary>
                <para>A tooltip contains the system information icon.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.ToolTipIconType.None">
            <summary>
                <para>A tooltip contains no predefined icon.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.ToolTipIconType.Question">
            <summary>
                <para>A tooltip contains the system question icon.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.ToolTipIconType.Warning">
            <summary>
                <para>A tooltip contains the system warning icon.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.ToolTipIconType.WindLogo">
            <summary>
                <para>A tooltip contains the Windows logo icon.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.ToolTipIconSize">

            <summary>
                <para>Specifies the size of a predefined icon displayed within a tooltip.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Utils.ToolTipIconSize.Large">
            <summary>
                <para>An icon displayed within a tooltip is large. The icon type can be selected via the <see cref="P:DevExpress.Utils.ToolTipController.IconType"/> property or can be specified in a handler of the <see cref="E:DevExpress.Utils.ToolTipController.BeforeShow"/> event via <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.IconType"/> property.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.ToolTipIconSize.Small">
            <summary>
                <para>An icon displayed within a tooltip is small. The icon type can be selected via the <see cref="P:DevExpress.Utils.ToolTipController.IconType"/> property or can be specified in a handler of the <see cref="E:DevExpress.Utils.ToolTipController.BeforeShow"/> event via <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.IconType"/> property.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.ToolTipControllerShowEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Utils.ToolTipController.BeforeShow"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.ToolTipControllerShowEventArgs.#ctor(System.Windows.Forms.Control,System.Object)">
            <summary>
                <para>Creates an instance of the <see cref="T:DevExpress.Utils.ToolTipControllerShowEventArgs"/> class and initializes the <see cref="P:DevExpress.Utils.ToolTipControllerEventArgsBase.SelectedControl"/> and <see cref="P:DevExpress.Utils.ToolTipControllerEventArgsBase.SelectedObject"/> properties.
</para>
            </summary>
            <param name="control">
		The object to initialize the <see cref="P:DevExpress.Utils.ToolTipControllerEventArgsBase.SelectedControl"/> property.

            </param>
            <param name="obj">
		The object to initialize the <see cref="P:DevExpress.Utils.ToolTipControllerEventArgsBase.SelectedObject"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipControllerShowEventArgs.#ctor">
            <summary>
                <para>Creates an instance of the <see cref="T:DevExpress.Utils.ToolTipControllerShowEventArgs"/> class and initializes properties with default values
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.ToolTipControllerShowEventArgs.#ctor(System.Windows.Forms.Control,System.Object,System.String,System.String,DevExpress.Utils.ToolTipLocation,System.Boolean,System.Boolean,System.Int32,DevExpress.Utils.ToolTipIconType,DevExpress.Utils.ToolTipIconSize,System.Object,System.Int32,DevExpress.Utils.AppearanceObject,DevExpress.Utils.AppearanceObject)">
            <summary>
                <para>Initializes a new instance of the ToolTipControllerShowEventArgs class with the specified settings.
</para>
            </summary>
            <param name="control">
		The object to initialize the <see cref="P:DevExpress.Utils.ToolTipControllerEventArgsBase.SelectedControl"/> property.

            </param>
            <param name="obj">
		The object to initialize the <see cref="P:DevExpress.Utils.ToolTipControllerEventArgsBase.SelectedObject"/> property.

            </param>
            <param name="toolTip">
		A <see cref="T:System.String"/> value that specifies the tooltip's text. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.ToolTip"/> property.

            </param>
            <param name="title">
		A <see cref="T:System.String"/> value that specifies the tooltip's title. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.Title"/> property.

            </param>
            <param name="toolTipLocation">
		A <see cref="T:DevExpress.Utils.ToolTipLocation"/> value that specifies the relative position of the tooltip window. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.ToolTipLocation"/> property.

            </param>
            <param name="showBeak">
		<b>true</b> if the callout beak is displayed when a hint appears; otherwise, <b>false</b>. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.ShowBeak"/> property.

            </param>
            <param name="rounded">
		<b>true</b> if tooltip window's corners are rounded; otherwise, <b>false</b>. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.Rounded"/> property.

            </param>
            <param name="roundRadius">
		The radius of the rounded corners. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.RoundRadius"/> property.

            </param>
            <param name="iconType">
		A <see cref="T:DevExpress.Utils.ToolTipIconType"/> value that specifies the kind of the predefined icon. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.IconType"/> property.

            </param>
            <param name="iconSize">
		A <see cref="T:DevExpress.Utils.ToolTipIconSize"/> value that specifies the icon's size. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.IconSize"/> property.

            </param>
            <param name="imageList">
		An object that represents the source of the images that can be displayed within tooltips. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.ImageList"/> property.

            </param>
            <param name="imageIndex">
		The index of the image to display in the tooltip. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.ImageIndex"/> property.

            </param>
            <param name="appearance">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the tooltip's appearance settings. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.Appearance"/> property.

            </param>
            <param name="appearanceTitle">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint the tooltip's title. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.AppearanceTitle"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipControllerShowEventArgs.#ctor(System.Windows.Forms.Control,System.Object,System.String,System.String,DevExpress.Utils.ToolTipLocation,System.Boolean,System.Boolean,System.Int32,DevExpress.Utils.ToolTipStyle,DevExpress.Utils.ToolTipIconType,DevExpress.Utils.ToolTipIconSize,System.Object,System.Int32,DevExpress.Utils.AppearanceObject,DevExpress.Utils.AppearanceObject)">
            <summary>
                <para>Initializes a new instance of the ToolTipControllerShowEventArgs class with the specified settings.
</para>
            </summary>
            <param name="control">
		The object to initialize the <see cref="P:DevExpress.Utils.ToolTipControllerEventArgsBase.SelectedControl"/> property.

            </param>
            <param name="obj">
		The object to initialize the <see cref="P:DevExpress.Utils.ToolTipControllerEventArgsBase.SelectedObject"/> property.

            </param>
            <param name="toolTip">
		A <see cref="T:System.String"/> value that specifies the tooltip's text. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.ToolTip"/> property.

            </param>
            <param name="title">
		A <see cref="T:System.String"/> value that specifies the tooltip's title. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.Title"/> property.

            </param>
            <param name="toolTipLocation">
		A <see cref="T:DevExpress.Utils.ToolTipLocation"/> value that specifies the relative position of the tooltip window. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.ToolTipLocation"/> property.

            </param>
            <param name="showBeak">
		<b>true</b> if the callout beak is displayed when a hint appears; otherwise, <b>false</b>. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.ShowBeak"/> property.

            </param>
            <param name="rounded">
		<b>true</b> if tooltip window's corners are rounded; otherwise, <b>false</b>. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.Rounded"/> property.

            </param>
            <param name="roundRadius">
		The radius of the rounded corners. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.RoundRadius"/> property.

            </param>
            <param name="style">
		The regular tooltip's style, which controls the look-and-feel settings. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.ToolTipStyle"/> property.

            </param>
            <param name="iconType">
		A <see cref="T:DevExpress.Utils.ToolTipIconType"/> value that specifies the kind of the predefined icon. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.IconType"/> property.

            </param>
            <param name="iconSize">
		A <see cref="T:DevExpress.Utils.ToolTipIconSize"/> value that specifies the icon's size. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.IconSize"/> property.

            </param>
            <param name="imageList">
		An object that is the source of the images that can be displayed within tooltips. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.ImageList"/> property.


            </param>
            <param name="imageIndex">
		The index of the image to display in the tooltip. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.ImageIndex"/> property.

            </param>
            <param name="appearance">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the tooltip's appearance settings. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.Appearance"/> property.

            </param>
            <param name="appearanceTitle">
		An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that provides the appearance settings used to paint the tooltip's title. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.AppearanceTitle"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerShowEventArgs.AllowHtmlText">
            <summary>
                <para>Gets or sets whether HTML formatting tags can be used to format the tooltip's text.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.DefaultBoolean"/> value that specifies whether HTML formatting tags can be used to format the tooltip's text.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerShowEventArgs.Appearance">
            <summary>
                <para>Gets the tooltip's appearance settings. This property is not in effect for <see cref="T:DevExpress.Utils.SuperToolTip"/> objects.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the tooltip's appearance settings.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerShowEventArgs.AppearanceTitle">
            <summary>
                <para>Gets the appearance settings used to paint the tooltip's title. This property is not in effect for <see cref="T:DevExpress.Utils.SuperToolTip"/> objects.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the tooltip's title.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerShowEventArgs.AutoHide">
            <summary>
                <para>Gets or sets whether the tooltip is hidden after a specific time ellapses.
</para>
            </summary>
            <value><b>true</b> if the tooltip is hidden after a specific time period ellapses; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.Utils.ToolTipControllerShowEventArgs.Equals(System.Object)">
            <summary>
                <para>Determines whether two objects are equal.
</para>
            </summary>
            <param name="obj">
		The object specifying a <see cref="T:DevExpress.Utils.ToolTipControllerShowEventArgs"/> instance to compare with the current object.

            </param>
            <returns><b>true</b> if the specified object is equal to the current <see cref="T:DevExpress.Utils.ToolTipControllerShowEventArgs"/> instance.
</returns>


        </member>
        <member name="M:DevExpress.Utils.ToolTipControllerShowEventArgs.GetHashCode">
            <summary>
                <para>Gets the hash code (a number) that corresponds to the value of the current object.
</para>
            </summary>
            <returns>The hash code for the current object.
</returns>


        </member>
        <member name="M:DevExpress.Utils.ToolTipControllerShowEventArgs.GetToolTipLocation">
            <summary>
                <para>Gets the actual location of the tooltip window relative to the position where the tooltip should appear.

</para>
            </summary>
            <returns>A <see cref="T:DevExpress.Utils.ToolTipLocation"/> value specifying the relative position of the tooltip window.
</returns>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerShowEventArgs.IconSize">
            <summary>
                <para>Gets the size of the predefined icon to display in the tooltip.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.ToolTipIconSize"/> value specifying the icon size.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerShowEventArgs.IconType">
            <summary>
                <para>Gets or sets the kind of predefined icons to display in the tooltip.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.ToolTipIconType"/> value specifying the kind of predefined icon to display.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerShowEventArgs.ImageIndex">
            <summary>
                <para>Gets or sets the index of an image from the <see cref="P:DevExpress.Utils.ToolTipControllerShowEventArgs.ImageList"/> to display in the tooltip.
</para>
            </summary>
            <value>The index of a custom image to display in the tooltip.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerShowEventArgs.ImageList">
            <summary>
                <para>Gets or sets the source of images that can be displayed within tooltips.
</para>
            </summary>
            <value>An object that represents the source of images that can be displayed within tooltips.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerShowEventArgs.Rounded">
            <summary>
                <para>Gets or sets whether the tooltip window has rounded corners.
</para>
            </summary>
            <value><b>true</b> if tooltip window's corners are rounded; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerShowEventArgs.RoundRadius">
            <summary>
                <para>Gets or sets the radius of the rounded corners of the tooltip window.
</para>
            </summary>
            <value>The radius of the rounded corners.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerShowEventArgs.Show">
            <summary>
                <para>Gets or sets whether the tooltip should be displayed.
</para>
            </summary>
            <value><b>true</b> if the tooltip should be displayed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerShowEventArgs.ShowBeak">
            <summary>
                <para>Tests whether callout beaks are displayed for hints.
</para>
            </summary>
            <value><b>true</b> if the callout beak is displayed when a hint appears; otherwise, <b>false</b>
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerShowEventArgs.SuperTip">
            <summary>
                <para>Gets or sets a <see cref="T:DevExpress.Utils.SuperToolTip"/>.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.SuperToolTip"/> object.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerShowEventArgs.Title">
            <summary>
                <para>Gets or sets the regular tooltip's title.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value which specifies the regular tooltip's title.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerShowEventArgs.ToolTip">
            <summary>
                <para>Gets or sets the text for the regular tooltip.
</para>
            </summary>
            <value>The string that is the text to display in the regular tooltip.

</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerShowEventArgs.ToolTipImage">
            <summary>
                <para>Gets or sets the image to display within the current tooltip.
</para>
            </summary>
            <value>An <see cref="T:System.Drawing.Image"/> object that specifies the image to display within the current tooltip.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerShowEventArgs.ToolTipLocation">
            <summary>
                <para>Gets or sets the location of the tooltip window relative to the position where the tooltip should appear.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.ToolTipLocation"/> value specifying the relative position of the tooltip window.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerShowEventArgs.ToolTipStyle">
            <summary>
                <para>Gets or sets the look-and-feel settings of the regular tooltip displayed.
 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.ToolTipStyle"/> value that specifies the look-and-feel of regular tooltips.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerShowEventArgs.ToolTipType">
            <summary>
                <para>Gets or sets the type of tooltip to be displayed.

</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.ToolTipType"/> value that specifies the type of tooltip to be displayed.

</value>


        </member>
        <member name="T:DevExpress.Utils.ToolTipControllerEventArgsBase">

            <summary>
                <para>The base class for classes providing data for <see cref="T:DevExpress.Utils.ToolTipController"/>'s events.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.ToolTipControllerEventArgsBase.#ctor(System.Windows.Forms.Control,System.Object)">
            <summary>
                <para>Creates an instance of the <see cref="T:DevExpress.Utils.ToolTipControllerEventArgsBase"/> class.
</para>
            </summary>
            <param name="control">
		The object to initialize the <see cref="P:DevExpress.Utils.ToolTipControllerEventArgsBase.SelectedControl"/> property.

            </param>
            <param name="obj">
		The object to initialize the <see cref="P:DevExpress.Utils.ToolTipControllerEventArgsBase.SelectedObject"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipControllerEventArgsBase.#ctor">
            <summary>
                <para>Creates an instance of the <see cref="T:DevExpress.Utils.ToolTipControllerEventArgsBase"/> class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerEventArgsBase.SelectedControl">
            <summary>
                <para>Gets or sets the control for which a tooltip controller's event is fired.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Forms.Control"/> object for which the event is fired.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerEventArgsBase.SelectedObject">
            <summary>
                <para>Gets or sets the element of the control for which the tooltip should be displayed.
</para>
            </summary>
            <value>An object defining the element for which to display the tooltip.
</value>


        </member>
        <member name="T:DevExpress.Utils.ToolTipControllerCalcSizeEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Utils.ToolTipController.CalcSize"/> event.

</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.ToolTipControllerCalcSizeEventHandler.Invoke(System.Object,DevExpress.Utils.ToolTipControllerCalcSizeEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Utils.ToolTipController.CalcSize"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This identifies the <see cref="T:DevExpress.Utils.ToolTipController"/> component which fires the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Utils.ToolTipControllerCalcSizeEventArgs"/> object containing data related to the event.

            </param>


        </member>
        <member name="T:DevExpress.Utils.ToolTipControllerCalcSizeEventArgs">

            <summary>
                <para>Provides data for the <see cref="E:DevExpress.Utils.ToolTipController.CalcSize"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.ToolTipControllerCalcSizeEventArgs.#ctor(System.Windows.Forms.Control,System.Object,System.String,System.String,System.Drawing.Size,System.Drawing.Point)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Utils.ToolTipControllerCalcSizeEventArgs"/> class with the specified settings.
</para>
            </summary>
            <param name="control">
		An object for which the event is fired. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerEventArgsBase.SelectedControl"/> property.

            </param>
            <param name="obj">
		An object which identifies the element which the tooltip is displayed for. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerEventArgsBase.SelectedObject"/> property.

            </param>
            <param name="toolTip">
		A <see cref="T:System.String"/> value which specifies the tooltip's contents. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerCalcSizeEventArgs.ToolTip"/> property.

            </param>
            <param name="title">
		A <see cref="T:System.String"/> value which specifies the tooltip's title. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerCalcSizeEventArgs.Title"/> property.

            </param>
            <param name="size">
		A <see cref="T:System.Drawing.Size"/> structure which specifies the tooltip's bounds. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerCalcSizeEventArgs.Size"/> property.

            </param>
            <param name="position">
		A <see cref="T:System.Drawing.Point"/> structure which specifies the screen coordinates of the top left tooltip corner. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerCalcSizeEventArgs.Position"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipControllerCalcSizeEventArgs.#ctor(System.Windows.Forms.Control,System.Object,DevExpress.Utils.ToolTipControllerShowEventArgs)">
            <summary>
                <para>Initializes a new instance of the ToolTipControllerCalcSizeEventArgs class with the specified settings.
</para>
            </summary>
            <param name="control">
		An object for which the event is fired. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerEventArgsBase.SelectedControl"/> property.

            </param>
            <param name="obj">
		An object which identifies the element that the tooltip is displayed for. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerEventArgsBase.SelectedObject"/> property.

            </param>
            <param name="showArgs">
		A <see cref="T:DevExpress.Utils.ToolTipControllerShowEventArgs"/> object that provides additional event data. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerCalcSizeEventArgs.ShowInfo"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipControllerCalcSizeEventArgs.#ctor(System.Windows.Forms.Control,System.Object,System.String,System.String)">
            <summary>
                <para>Initializes a new instance of the ToolTipControllerCalcSizeEventArgs class with the specified settings.
</para>
            </summary>
            <param name="control">
		An object for which the event is fired. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerEventArgsBase.SelectedControl"/> property.

            </param>
            <param name="obj">
		An object which identifies the element for which the tooltip is displayed. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerEventArgsBase.SelectedObject"/> property.


            </param>
            <param name="toolTip">
		A <see cref="T:System.String"/> value which specifies the tooltip's contents. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerCalcSizeEventArgs.ToolTip"/> property.

            </param>
            <param name="title">
		A <see cref="T:System.String"/> value which specifies the tooltip's title. This value is assigned to the <see cref="P:DevExpress.Utils.ToolTipControllerCalcSizeEventArgs.Title"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerCalcSizeEventArgs.Position">
            <summary>
                <para>Gets or sets the coordinates of the top left tooltip corner.
</para>
            </summary>
            <value>The <see cref="T:System.Drawing.Point"/> object specifying the coordinates of the top left tooltip corner relative to the desktop.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerCalcSizeEventArgs.ShowInfo">
            <summary>
                <para>Gets the additional event data.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.ToolTipControllerShowEventArgs"/> object that provides additional event data.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerCalcSizeEventArgs.Size">
            <summary>
                <para>Gets or sets the tooltip dimensions.
</para>
            </summary>
            <value>The <see cref="T:System.Drawing.Size"/> object specifying the width and height of a tooltip rectangle. 
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerCalcSizeEventArgs.Title">
            <summary>
                <para>Gets the tooltip's title.
</para>
            </summary>
            <value>A <see cref="T:System.String"/> value which specifies the tooltip's title.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipControllerCalcSizeEventArgs.ToolTip">
            <summary>
                <para>Gets the text to be displayed within the tooltip.
</para>
            </summary>
            <value>The <see cref="T:System.String"/> object specifying the text to be displayed within the tooltip.
</value>


        </member>
        <member name="T:DevExpress.Utils.ToolTipControllerBeforeShowEventHandler">

            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Utils.ToolTipController.BeforeShow"/> event.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.ToolTipControllerBeforeShowEventHandler.Invoke(System.Object,DevExpress.Utils.ToolTipControllerShowEventArgs)">
            <summary>
                <para>Represents a method that will handle the <see cref="E:DevExpress.Utils.ToolTipController.BeforeShow"/> event.
</para>
            </summary>
            <param name="sender">
		The event source. This identifies the <see cref="T:DevExpress.Utils.ToolTipController"/> component which fires the event.

            </param>
            <param name="e">
		A <see cref="T:DevExpress.Utils.ToolTipControllerShowEventArgs"/> object containing data related to the event.

            </param>


        </member>
        <member name="T:DevExpress.Utils.ToolTipController">

            <summary>
                <para>Provides tooltip appearance and behavior options for controls.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.ToolTipController.#ctor">
            <summary>
                <para>Creates a new instance of the <see cref="T:DevExpress.Utils.ToolTipController"/> class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.#ctor(System.ComponentModel.IContainer)">
            <summary>
                <para>Initializes a new instance of the ToolTipController class with the specified container.
</para>
            </summary>
            <param name="container">
		A <see cref="T:System.ComponentModel.IContainer"/> that represents the container for the tooltip controller.

            </param>


        </member>
        <member name="P:DevExpress.Utils.ToolTipController.Active">
            <summary>
                <para>Gets or sets whether the component's functionality is enabled.
</para>
            </summary>
            <value><b>true</b> if displaying tooltips is allowed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipController.ActiveControlClient">
            <summary>
                <para>Gets the active control, if it implements the IToolTipControlClient interface. 
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.IToolTipControlClient"/> object.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipController.ActiveObject">
            <summary>
                <para>Returns the object that identifies which of the bound control's elements the tooltip is displayed for.
</para>
            </summary>
            <value>A <see cref="T:System.Object"/> object representing the bound control's element that the tooltip is displayed for.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipController.ActiveObjectInfo">
            <summary>
                <para>An object containing tooltip information for the current element of a bound control.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.ToolTipControlInfo"/> object containing tooltip information for the current element of a bound control.
</value>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.AddClientControl(System.Windows.Forms.Control)">
            <summary>
                <para>Enables the display of tooltips for the specified control implementing the <b>DevExpress.Utils.IToolTipControlClient</b> interface.
</para>
            </summary>
            <param name="control">
		The control, implementing the <b>DevExpress.Utils.IToolTipControlClient</b> interface, for which tooltips should be enabled.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.AddClientControl(System.Windows.Forms.Control,DevExpress.Utils.IToolTipControlClient)">
            <summary>
                <para>Enables the display of tooltips for the specified control implementing the <b>DevExpress.Utils.IToolTipControlClient</b> interface. 
</para>
            </summary>
            <param name="control">
		@nbsp;

            </param>
            <param name="owner">
		@nbsp;

            </param>


        </member>
        <member name="P:DevExpress.Utils.ToolTipController.AllowHtmlText">
            <summary>
                <para>Gets or sets whether HTML formatting tags can be used to format text in regular tooltips.
</para>
            </summary>
            <value>A Boolean value that specifies whether HTML formatting tags can be used to format text in regular tooltips.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipController.Appearance">
            <summary>
                <para>Gets or sets the settings that control the appearance of a tooltip's window and text.

</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that contains the appearance settings.

</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipController.AppearanceTitle">
            <summary>
                <para>Provide the settings that control the appearance of a tooltip's title.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object that contains the appearance settings.

</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipController.AutoPopDelay">
            <summary>
                <para>Gets or sets the tooltip delay in milliseconds.
</para>
            </summary>
            <value>Integer value specifying tooltip delay.
</value>


        </member>
        <member name="E:DevExpress.Utils.ToolTipController.BeforeShow">
            <summary>
                <para>Enables you to customize the text and settings of the tooltip before displaying it onscreen.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.Utils.ToolTipController.CalcSize">
            <summary>
                <para>Enables you to specify the position  and size of the tooltip window.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Utils.ToolTipController.CloseOnClick">
            <summary>
                <para>Gets or sets whether a tooltip is closed when it's clicked.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.DefaultBoolean"/> value that specifies whether a tooltip is closed when it's clicked.
</value>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.CreateShowArgs">
            <summary>
                <para>Returns a <see cref="T:DevExpress.Utils.ToolTipControllerShowEventArgs"/> object containing settings of  the current tooltip controller.
</para>
            </summary>
            <returns>A <see cref="T:DevExpress.Utils.ToolTipControllerShowEventArgs"/> object with settings of the current tooltip controller.
</returns>


        </member>
        <member name="E:DevExpress.Utils.ToolTipController.CustomDraw">
            <summary>
                <para>Enables a tooltip's window to be custom painted.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Utils.ToolTipController.DefaultController">
            <summary>
                <para>Gets the default <see cref="T:DevExpress.Utils.ToolTipController"/> used for displaying tooltips.
</para>
            </summary>
            <value>A default <see cref="T:DevExpress.Utils.ToolTipController"/> object.
</value>


        </member>
        <member name="E:DevExpress.Utils.ToolTipController.GetActiveObjectInfo">
            <summary>
                <para>Allows you to provide custom tooltips for any element of a control that implements the <see cref="T:DevExpress.Utils.IToolTipControlClient"/> interface.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.GetAllowHtmlText(System.Windows.Forms.Control)">
            <summary>
                <para>Returns whether HTML formatting is enabled in tooltips for the specified control.
</para>
            </summary>
            <param name="control">
		A control for which this tooltip setting is to be obtained.

            </param>
            <returns>A <see cref="T:DevExpress.Utils.DefaultBoolean"/> value that specifies whether HTML formatting is supported in tooltips for the specified control.
</returns>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.GetSuperTip(System.Windows.Forms.Control)">
            <summary>
                <para>Gets a <see cref="T:DevExpress.Utils.SuperToolTip"/> object associated with the specified control.
</para>
            </summary>
            <param name="control">
		The control whose SuperToolTip object is to be obtained.

            </param>
            <returns>A <see cref="T:DevExpress.Utils.SuperToolTip"/> object associated with the control.
</returns>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.GetTitle(System.Windows.Forms.Control)">
            <summary>
                <para>Gets a regular tooltip's title displayed within the specified control.

</para>
            </summary>
            <param name="control">
		A control whose regular tooltip's title is to be obtained.


            </param>
            <returns>A string representing a regular tooltip's title for the control.

</returns>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.GetToolTip(System.Windows.Forms.Control)">
            <summary>
                <para>Gets a regular tooltip for the specified control.
</para>
            </summary>
            <param name="control">
		A control whose tooltip should be obtained.


            </param>
            <returns>A string representing a regular tooltip for the control.

</returns>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.GetToolTipIconType(System.Windows.Forms.Control)">
            <summary>
                <para>Gets the type of the icon displayed within the specified control's regular tooltip.
</para>
            </summary>
            <param name="control">
		A control whose tooltip's icon type is to be obtained.



            </param>
            <returns>A <see cref="T:DevExpress.Utils.ToolTipIconType"/> value representing the type of the icon displayed within the specified control's regular tooltip.


</returns>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.HideHint">
            <summary>
                <para>Hides the hint if it is being displayed.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Utils.ToolTipController.IconSize">
            <summary>
                <para>Gets or sets tooltip icon size.
</para>
            </summary>
            <value>The <see cref="T:DevExpress.Utils.ToolTipIconSize"/> enumerator.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipController.IconType">
            <summary>
                <para>Gets or sets tooltip icon type.
</para>
            </summary>
            <value>The <see cref="T:DevExpress.Utils.ToolTipIconType"/> enumerator.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipController.ImageIndex">
            <summary>
                <para>Gets or sets the index of an image to be displayed within tooltips.
</para>
            </summary>
            <value>The integer value specifying the index of an image to be displayed within tooltips.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipController.ImageList">
            <summary>
                <para>Gets or sets the source of the images that can be displayed within tooltips.
</para>
            </summary>
            <value>An object that is the source of images that can be displayed within tooltips.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipController.InitialDelay">
            <summary>
                <para>Gets or sets the initial tooltip delay in milliseconds.
</para>
            </summary>
            <value>Integer value, specifying initial tooltip delay.
</value>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.RemoveClientControl(System.Windows.Forms.Control)">
            <summary>
                <para>Disables displaying tooltips for the specified control implementing the <b>DevExpress.Utils.IToolTipControlClient</b> interface.
</para>
            </summary>
            <param name="control">
		The control, implementing the <b>DevExpress.Utils.IToolTipControlClient</b> interface, for which the <see cref="T:DevExpress.Utils.ToolTipController"/>'s functionality should be disabled.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.ResetAutoPopupDelay">
            <summary>
                <para>Resets the timer which controls when a tooltip's window is hidden.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.Utils.ToolTipController.ReshowDelay">
            <summary>
                <para>Gets or sets the time interval that must pass before another hint is displayed if another hint is currently visible.
</para>
            </summary>
            <value>An integer value specifying the time interval in milliseconds.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipController.Rounded">
            <summary>
                <para>Gets or sets whether the tooltip's corners are rounded.
</para>
            </summary>
            <value><b>true</b> to display hints with rounded corners; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipController.RoundRadius">
            <summary>
                <para>Gets or sets the radius of the rounded corners of the tooltip window.
</para>
            </summary>
            <value>The radius of the rounded corners.
</value>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.SetAllowHtmlText(System.Windows.Forms.Control,DevExpress.Utils.DefaultBoolean)">
            <summary>
                <para>Sets whether HTML formatting is enabled in tooltips for the specified control.
</para>
            </summary>
            <param name="control">
		A control for which the tooltip information is to be changed.

            </param>
            <param name="value">
		A <see cref="T:DevExpress.Utils.DefaultBoolean"/> value that specifies whether HTML formatting is enabled in tooltips for the specified control.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.SetSuperTip(System.Windows.Forms.Control,DevExpress.Utils.SuperToolTip)">
            <summary>
                <para>Associates a <see cref="T:DevExpress.Utils.SuperToolTip"/> object with the specified control. 
</para>
            </summary>
            <param name="control">
		A control for which to set the tooltip.


            </param>
            <param name="value">
		A <see cref="T:DevExpress.Utils.SuperToolTip"/> object to associate with the control.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.SetTitle(System.Windows.Forms.Control,System.String)">
            <summary>
                <para>Sets a regular tooltip's title for the specified control.
</para>
            </summary>
            <param name="control">
		A control for which to set a regular tooltip's title.


            </param>
            <param name="value">
		A string representing a regular tooltip's title.


            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.SetToolTip(System.Windows.Forms.Control,System.String)">
            <summary>
                <para>Sets a regular tooltip for the specified control.
</para>
            </summary>
            <param name="control">
		A control for which to set the tooltip.


            </param>
            <param name="value">
		A string representing a regular tooltip's text.


            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.SetToolTipIconType(System.Windows.Forms.Control,DevExpress.Utils.ToolTipIconType)">
            <summary>
                <para>Sets the type of the icon displayed within the specified control's regular tooltip.

</para>
            </summary>
            <param name="control">
		A control for which the icon type is set.



            </param>
            <param name="value">
		A <see cref="T:DevExpress.Utils.ToolTipIconType"/> value representing the type of the icon that should be displayed within the specified control's regular tooltip.

            </param>


        </member>
        <member name="P:DevExpress.Utils.ToolTipController.ShowBeak">
            <summary>
                <para>Tests whether callout beaks are displayed for hints.
</para>
            </summary>
            <value><b>true</b> if the callout beak is displayed when a hint appears; otherwise, <b>false</b>.
</value>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.ShowHint(DevExpress.Utils.ToolTipControllerShowEventArgs,System.Windows.Forms.Control)">
            <summary>
                <para>Displays a hint for a control using specified settings.
</para>
            </summary>
            <param name="eShow">
		A <see cref="T:DevExpress.Utils.ToolTipControllerShowEventArgs"/> object specifying the settings used to display tooltips.

            </param>
            <param name="control">
		The control for which to display the tooltip.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.ShowHint(System.String,System.Windows.Forms.Control,DevExpress.Utils.ToolTipLocation)">
            <summary>
                <para>Displays a hint relative to a specific control.
</para>
            </summary>
            <param name="toolTip">
		The text to display as a tooltip.

            </param>
            <param name="control">
		The control for which to display the tooltip.

            </param>
            <param name="toolTipLocation">
		The position relative to the specified control at which the tooltip should be displayed.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.ShowHint(System.String)">
            <summary>
                <para>Displays a hint relative to the mouse cursor using current <see cref="T:DevExpress.Utils.ToolTipController"/> settings.
</para>
            </summary>
            <param name="toolTip">
		The text to display as a tooltip.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.ShowHint(System.String,DevExpress.Utils.ToolTipLocation)">
            <summary>
                <para>Displays a hint at the specified position relative to the mouse cursor.
</para>
            </summary>
            <param name="toolTip">
		The text to display as a tooltip.

            </param>
            <param name="toolTipLocation">
		The position relative to the mouse cursor at which the hint should be displayed.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.ShowHint(System.String,System.Drawing.Point)">
            <summary>
                <para>Displays a hint relative to the specified point using current <see cref="T:DevExpress.Utils.ToolTipController"/> settings

</para>
            </summary>
            <param name="toolTip">
		The text to display as a tooltip.

            </param>
            <param name="cursorPosition">
		The point relative to which the tooltip should be displayed.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.ShowHint(System.String,DevExpress.Utils.ToolTipLocation,System.Drawing.Point)">
            <summary>
                <para>Displays a hint at a specified position relative to a specific point.
</para>
            </summary>
            <param name="toolTip">
		The text to display as a tooltip.

            </param>
            <param name="toolTipLocation">
		The position relative to the specified point at which the hint should be displayed.

            </param>
            <param name="cursorPosition">
		The point relative to which the hint should be displayed.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.ShowHint(DevExpress.Utils.ToolTipControllerShowEventArgs,System.Drawing.Point)">
            <summary>
                <para>Displays a hint relative to a specific point using specified settings.
</para>
            </summary>
            <param name="eShow">
		A <see cref="T:DevExpress.Utils.ToolTipControllerShowEventArgs"/> object specifying settings for displaying tooltips.


            </param>
            <param name="cursorPosition">
		A point relative to which the hint should be displayed.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.ShowHint(DevExpress.Utils.ToolTipControllerShowEventArgs)">
            <summary>
                <para>Displays a hint relative to the mouse cursor using specified settings.
</para>
            </summary>
            <param name="eShow">
		A <see cref="T:DevExpress.Utils.ToolTipControllerShowEventArgs"/> object specifying settings for displaying tooltips.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.ShowHint(DevExpress.Utils.ToolTipControlInfo)">
            <summary>
                <para>Displays a hint using the specified tooltip information.
</para>
            </summary>
            <param name="info">
		A <see cref="T:DevExpress.Utils.ToolTipControlInfo"/> object containing information for displaying a hint.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.ShowHint(System.String,System.String,System.Windows.Forms.Control,DevExpress.Utils.ToolTipLocation)">
            <summary>
                <para>Displays a hint with the specified text and title relative to the specified control.
</para>
            </summary>
            <param name="toolTip">
		A string which represents the tooltip's text.

            </param>
            <param name="title">
		A string which represents the tooltip's title.

            </param>
            <param name="control">
		The control to display the tooltip for. 


            </param>
            <param name="toolTipLocation">
		The position relative to the specified control at which the tooltip should be displayed. 

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.ShowHint(System.String,System.String)">
            <summary>
                <para>Displays a hint with the specified text and title relative to the mouse cursor using the current ToolTipController settings. 

</para>
            </summary>
            <param name="toolTip">
		A string that represents a tooltip's text.

            </param>
            <param name="title">
		A string that represents a tooltip's title.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.ShowHint(System.String,System.String,DevExpress.Utils.ToolTipLocation,System.Drawing.Point)">
            <summary>
                <para>Displays a hint with the specified text and title relative to the specified point.

</para>
            </summary>
            <param name="toolTip">
		A string that represents a tooltip's text.

            </param>
            <param name="title">
		A string that represents a tooltip's title.

            </param>
            <param name="toolTipLocation">
		The position relative to the specified control at which the tooltip should be displayed. 

            </param>
            <param name="cursorPosition">
		A point relative to which the hint should be displayed.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.ShowHint(System.String,System.String,System.Drawing.Point)">
            <summary>
                <para>Displays a hint with the specified text and title at the specified point using the current ToolTipController settings 

</para>
            </summary>
            <param name="toolTip">
		A string that represents a tooltip's text.

            </param>
            <param name="title">
		A string that represents a tooltip's title.

            </param>
            <param name="cursorPosition">
		A point relative to which the hint should be displayed.

            </param>


        </member>
        <member name="M:DevExpress.Utils.ToolTipController.ShowHint(System.String,System.String,DevExpress.Utils.ToolTipLocation)">
            <summary>
                <para>Displays a hint with the specified text and title relative to the mouse cursor's position.

</para>
            </summary>
            <param name="toolTip">
		A string that represents a tooltip's text.

            </param>
            <param name="title">
		A string that represents a tooltip's title.

            </param>
            <param name="toolTipLocation">
		The position relative to the specified control at which the tooltip should be displayed. 

            </param>


        </member>
        <member name="P:DevExpress.Utils.ToolTipController.ShowShadow">
            <summary>
                <para>Gets or sets whether the tooltips are shown shadowed.
</para>
            </summary>
            <value><b>true</b> if the tooltips are shown shadowed; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipController.Style">
            <summary>
                <para>Gets the appearance settings used to paint the hints of bound controls.
</para>
            </summary>
            <value>An <see cref="T:DevExpress.Utils.AppearanceObject"/> object which provides the appearance settings used to paint the hints of bound controls.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipController.ToolTipLocation">
            <summary>
                <para>Gets or sets the tooltip location.
</para>
            </summary>
            <value>The <see cref="T:DevExpress.Utils.ToolTipLocation"/> enumerator.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipController.ToolTipStyle">
            <summary>
                <para>Gets or sets the look-and-feel of regular tooltips.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.ToolTipStyle"/> value that specifies the look-and-feel of regular tooltips.
</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipController.ToolTipType">
            <summary>
                <para>Gets or sets the type of tooltips displayed by the controller.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.ToolTipType"/> value that specifies the type of tooltips to be displayed.

</value>


        </member>
        <member name="P:DevExpress.Utils.ToolTipController.UseWindowStyle">
            <summary>
                <para>Obsolete. Gets or sets whether the default style should be used for the hint.
</para>
            </summary>
            <value><b>true</b> if the default style is applied to the hint; <b>false</b> if the <see cref="P:DevExpress.Utils.ToolTipController.Style"/> is used
</value>


        </member>
        <member name="T:DevExpress.LookAndFeel.DefaultLookAndFeel">

            <summary>
                <para>A component providing access to Default LookAndFeel settings.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.LookAndFeel.DefaultLookAndFeel.#ctor">
            <summary>
                <para>Creates an instance of the <see cref="T:DevExpress.LookAndFeel.DefaultLookAndFeel"/> object.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.LookAndFeel.DefaultLookAndFeel.#ctor(System.ComponentModel.IContainer)">
            <summary>
                <para>Initializes a new instance of the DefaultLookAndFeel class with the specified container.
</para>
            </summary>
            <param name="container">
		A <see cref="T:System.ComponentModel.IContainer"/> that represents the container for the Default LookAndFeel object.

            </param>


        </member>
        <member name="P:DevExpress.LookAndFeel.DefaultLookAndFeel.LookAndFeel">
            <summary>
                <para>Gets the <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object defining Default LookAndFeel settings.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object defining Default LookAndFeel settings.
</value>


        </member>
        <member name="T:DevExpress.Utils.Menu.IDXDropDownControl">

            <summary>
                <para>Contains methods to operate on <see cref="T:DevExpress.Utils.Menu.DXPopupMenu"/> objects.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.Menu.IDXDropDownControl.Hide">
            <summary>
                <para>Hides the current popup control.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.Menu.IDXDropDownControl.Show(DevExpress.Utils.Menu.IDXMenuManager,System.Windows.Forms.Control,System.Drawing.Point)">
            <summary>
                <para>Displays the current popup control using a menu manager.
</para>
            </summary>
            <param name="manager">
		An IDXMenuManager object that displays a popup control in a specific manner.

            </param>
            <param name="control">
		A parent control for the current popup control.

            </param>
            <param name="pos">
		A position where a popup control will be displayed.

            </param>


        </member>
        <member name="P:DevExpress.Utils.Menu.IDXDropDownControl.Visible">
            <summary>
                <para>Gets whether the current popup control is visible.
</para>
            </summary>
            <value><b>true</b> if the current popup control is visible; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.XtraEditors.VScrollBarBase">

            <summary>
                <para>Serves as the base for <see cref="T:DevExpress.XtraEditors.VScrollBar"/> class.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.XtraEditors.VScrollBarBase.ScrollBarType">
            <summary>
                <para>Gets the value indicating whether the scroll bar is horizontal or vertical.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.ScrollBarType"/> enumeration member specifying scroll bar orientation.
</value>


        </member>
        <member name="T:DevExpress.XtraEditors.VScrollBar">

            <summary>
                <para>Represents the vertical scroll bar control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraEditors.VScrollBar.#ctor">
            <summary>
                <para>Creates a new <see cref="T:DevExpress.XtraEditors.VScrollBar"/> object.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraEditors.VScrollBar.RightToLeft">
            <summary>
                <para>This member overrides <b>Control.RightToLeft</b>.
</para>
            </summary>
            <value>One of the <b>System.Windows.Forms.RightToLeft</b> values.
</value>


        </member>
        <member name="T:DevExpress.XtraEditors.ScrollBarBase">

            <summary>
                <para>Serves as the base for <see cref="T:DevExpress.XtraEditors.HScrollBarBase"/>, <see cref="T:DevExpress.XtraEditors.HScrollBar"/>, <see cref="T:DevExpress.XtraEditors.VScrollBarBase"/> and <see cref="T:DevExpress.XtraEditors.VScrollBar"/> classes.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraEditors.ScrollBarBase.#ctor">
            <summary>
                <para>Creates a new <see cref="T:DevExpress.XtraEditors.ScrollBarBase"/> object.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraEditors.ScrollBarBase.AutoSize">
            <summary>
                <para>Gets or sets a value specifying whether the scroll bar is autosized.
</para>
            </summary>
            <value><b>true</b> if auto size is applied; otherwise <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.ScrollBarBase.BackColor">
            <summary>
                <para>Overrides the base class <b>BackColor</b> property.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Color"/> object representing the control's background color.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.ScrollBarBase.BackgroundImage">
            <summary>
                <para>Overrides the base class <b>BackgroundImage</b> property.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Image"/> object representing the image to display in the control's background.
</value>


        </member>
        <member name="M:DevExpress.XtraEditors.ScrollBarBase.BeginUpdate">
            <summary>
                <para>Locks the ScrollBarBase object by disallowing visual updates until the <b>EndUpdate</b> or <b>CancelUpdate</b> method is called.


<para>@nbsp;</para>
<para>@nbsp;</para>
Prevents the control from being updated until the <see cref="M:DevExpress.XtraEditors.ScrollBarBase.EndUpdate"/> or <see cref="M:DevExpress.XtraEditors.ScrollBarBase.CancelUpdate"/> method is called.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraEditors.ScrollBarBase.CancelUpdate">
            <summary>
                <para>Unlocks the control after a <see cref="M:DevExpress.XtraEditors.ScrollBarBase.BeginUpdate"/> method call without causing immediate repainting.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraEditors.ScrollBarBase.EndUpdate">
            <summary>
                <para>Unlocks the control after a <see cref="M:DevExpress.XtraEditors.ScrollBarBase.BeginUpdate"/> method call and causes its immediate repainting.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraEditors.ScrollBarBase.Font">
            <summary>
                <para>Overrides the base class <b>Font</b> property.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Font"/> object to apply to the text displayed by the control.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.ScrollBarBase.ForeColor">
            <summary>
                <para>Overrides the base class <b>ForeColor</b> property.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Color"/> object representing the control's foreground color.
</value>


        </member>
        <member name="M:DevExpress.XtraEditors.ScrollBarBase.GetAccessible">
            <summary>
                <para>Returns an object which implements the accessibility information.
</para>
            </summary>
            <returns>A BaseAccessibility object.
</returns>


        </member>
        <member name="M:DevExpress.XtraEditors.ScrollBarBase.GetEnabled">
            <summary>
                <para>Returns the value of the control's <b>Enabled</b> property.
</para>
            </summary>
            <returns>The value of the <b>Enabled</b> property.
</returns>


        </member>
        <member name="M:DevExpress.XtraEditors.ScrollBarBase.GetHeight">
            <summary>
                <para>Returns the value of the control's <b>Height</b> property.
</para>
            </summary>
            <returns>The value of the <b>Height</b> property.
</returns>


        </member>
        <member name="M:DevExpress.XtraEditors.ScrollBarBase.GetWidth">
            <summary>
                <para>Returns the value of the control's <b>Width</b> property.
</para>
            </summary>
            <returns>The value of the <b>Width</b> property.
</returns>


        </member>
        <member name="P:DevExpress.XtraEditors.ScrollBarBase.ImeMode">
            <summary>
                <para>Gets or sets the Input Method Editor(IME) mode supported by this control.
</para>
            </summary>
            <value>A <b>ImeMode</b> enumeration member specifying the Input Method Editor (IME) status of an object when the object is selected.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.ScrollBarBase.LargeChange">
            <summary>
                <para>Gets or sets the increment applied to the <see cref="P:DevExpress.XtraEditors.ScrollBarBase.Value"/> property when the scroll box is moved by a 'page'.
</para>
            </summary>
            <value>An integer value representing the increment applied when the scroll box is moved by a 'page'.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.ScrollBarBase.LookAndFeel">
            <summary>
                <para>Gets a value providing access to settings controlling the control's look and feel.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object whose properties specify the control's look and feel.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.ScrollBarBase.Maximum">
            <summary>
                <para>Gets or sets the upper limit of values of the scrollable range.
</para>
            </summary>
            <value>An integer value representing the value's upper limit.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.ScrollBarBase.Minimum">
            <summary>
                <para>Gets or sets the lower limit of the scrollable range.
</para>
            </summary>
            <value>An integer value representing a lower limit.
</value>


        </member>
        <member name="E:DevExpress.XtraEditors.ScrollBarBase.Scroll">
            <summary>
                <para>Fires when the scroll thumb has been moved either by a mouse or keyboard action.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraEditors.ScrollBarBase.ScrollBarAutoSize">
            <summary>
                <para>Gets or sets a value specifying whether the scroll bar is automatically sized.
</para>
            </summary>
            <value><b>true</b> if the auto sizing feature is enabled; otherwise <b>false</b>.
</value>


        </member>
        <member name="E:DevExpress.XtraEditors.ScrollBarBase.ScrollBarAutoSizeChanged">
            <summary>
                <para>Fires after the <see cref="P:DevExpress.XtraEditors.ScrollBarBase.ScrollBarAutoSize"/> property's value has been changed.

</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraEditors.ScrollBarBase.ScrollBarType">
            <summary>
                <para>Gets the value indicating whether the scroll bar is horizontal or vertical.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.ScrollBarType"/> enumeration member specifying the scroll bar type.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.ScrollBarBase.SmallChange">
            <summary>
                <para>Gets or sets the value by which the <see cref="P:DevExpress.XtraEditors.ScrollBarBase.Value"/> property changes when the user presses one of the arrow keys or clicks one of the scroll-bar buttons.
</para>
            </summary>
            <value>An integer value by which the <see cref="P:DevExpress.XtraEditors.ScrollBarBase.Value"/> property changes.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.ScrollBarBase.TabStop">
            <summary>
                <para>Gets or sets a value indicating whether a user can focus the scroll bar control using the TAB key.
</para>
            </summary>
            <value><b>true</b> if a user can  focus the scroll bar using the TAB key; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.ScrollBarBase.Text">
            <summary>
                <para>Overrides the base class <b>Text</b> property.
</para>
            </summary>
            <value>A string value representing the text associated with this control.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.ScrollBarBase.Value">
            <summary>
                <para>Gets or sets a value specifying the scroll box's current position.
</para>
            </summary>
            <value>An integer value representing the scroll box's current position.
</value>


        </member>
        <member name="E:DevExpress.XtraEditors.ScrollBarBase.ValueChanged">
            <summary>
                <para>Fires immediately after the <see cref="P:DevExpress.XtraEditors.ScrollBarBase.Value"/> property has been changed.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraEditors.HScrollBarBase">

            <summary>
                <para>Serves as the base for <see cref="T:DevExpress.XtraEditors.HScrollBar"/> class.
</para>
            </summary>

        </member>
        <member name="P:DevExpress.XtraEditors.HScrollBarBase.ScrollBarType">
            <summary>
                <para>Gets the value indicating whether the scroll bar is horizontal or vertical.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.XtraEditors.ScrollBarType"/> enumeration member specifying the scroll bar's orientation.
</value>


        </member>
        <member name="T:DevExpress.XtraEditors.HScrollBar">

            <summary>
                <para>Represents the horizontal scroll bar control.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraEditors.HScrollBar.#ctor">
            <summary>
                <para>Creates a new <see cref="T:DevExpress.XtraEditors.HScrollBar"/> object.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraEditors.ScrollBarType">

            <summary>
                <para>Contains values specifying the type of <b>ScrollBar</b> control.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraEditors.ScrollBarType.Horizontal">
            <summary>
                <para><para>Corresponds to the horizontal orientation of the scroll bar.</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.ScrollBarType.Vertical">
            <summary>
                <para><para>Corresponds to the vertical orientation of the scroll bar.</para>

</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.KeyShortcut">

            <summary>
                <para>A class used by various controls to provide shortcut keys.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.KeyShortcut.#ctor(System.Windows.Forms.Shortcut)">
            <summary>
                <para>Creates an instance of the <see cref="T:DevExpress.Utils.KeyShortcut"/> class with the specified shortcut.
</para>
            </summary>
            <param name="shortcut">
		The key combination to initialize the <see cref="P:DevExpress.Utils.KeyShortcut.Key"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Utils.KeyShortcut.#ctor(System.Windows.Forms.Keys)">
            <summary>
                <para>Creates an instance of the <see cref="T:DevExpress.Utils.KeyShortcut"/> class with the specified shortcut.
</para>
            </summary>
            <param name="key">
		The key combination to initialize the <see cref="P:DevExpress.Utils.KeyShortcut.Key"/> property. Values of the <see cref="T:System.Windows.Forms.Keys"/> type can be combined using the OR operator.

            </param>


        </member>
        <member name="M:DevExpress.Utils.KeyShortcut.#ctor">
            <summary>
                <para>Creates an instance of the <see cref="T:DevExpress.Utils.KeyShortcut"/> class and sets the shortcut to the <see cref="F:System.Windows.Forms.Keys.None"/> value.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Utils.KeyShortcut.AltKeyName">
            <summary>
                <para>Gets the name of the ALT key which is dependent upon the end-user's regional settings.

</para>
            </summary>
            <value>A string which specifies the name of the ALT key.
</value>


        </member>
        <member name="P:DevExpress.Utils.KeyShortcut.ControlKeyName">
            <summary>
                <para>Gets the name of the CONTROL key which is dependent upon the end-user's regional settings.

</para>
            </summary>
            <value>A string which specifies the name of the CONTROL key.
</value>


        </member>
        <member name="F:DevExpress.Utils.KeyShortcut.Empty">
            <summary>
                <para>Gets the static <see cref="T:DevExpress.Utils.KeyShortcut"/> object whose <see cref="P:DevExpress.Utils.KeyShortcut.Key"/> property is set to <see cref="F:System.Windows.Forms.Keys.None"/>. 

</para>
            </summary>
            <returns>The static <see cref="T:DevExpress.Utils.KeyShortcut"/> object with no valid shortcut assigned.
</returns>


        </member>
        <member name="M:DevExpress.Utils.KeyShortcut.Equals(System.Object)">
            <summary>
                <para>Tests whether the shortcuts of the current and specified <see cref="T:DevExpress.Utils.KeyShortcut"/> objects are identical.
</para>
            </summary>
            <param name="value">
		The object representing the <see cref="T:DevExpress.Utils.KeyShortcut"/> object.

            </param>
            <returns><b>true</b> if the function parameter represents a <see cref="T:DevExpress.Utils.KeyShortcut"/> object and its shortcut is equal to the shortcut of the current object; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="M:DevExpress.Utils.KeyShortcut.GetHashCode">
            <summary>
                <para>Gets the hash code (a number) that corresponds to the value of the current object.
</para>
            </summary>
            <returns>The hash code for the current object.
</returns>


        </member>
        <member name="M:DevExpress.Utils.KeyShortcut.GetKeyDisplayText(System.Windows.Forms.Keys)">
            <summary>
                <para>Returns the text representation of the specified <b>System.Windows.Forms.Keys</b> object.
</para>
            </summary>
            <param name="key">
		The object whose text representation is to be obtained.

            </param>
            <returns>The text representation of the specified Keys object.
</returns>


        </member>
        <member name="P:DevExpress.Utils.KeyShortcut.IsExist">
            <summary>
                <para>Tests whether the current shortcut specifies a valid key combination.
</para>
            </summary>
            <value><b>true</b> if the current shortcut specifies a valid key combination; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.Utils.KeyShortcut.Key">
            <summary>
                <para>Gets the shortcut used to activate particular functionality.
</para>
            </summary>
            <value>The <see cref="T:System.Windows.Forms.Keys"/> value representing the shortcut.
</value>


        </member>
        <member name="P:DevExpress.Utils.KeyShortcut.ShiftKeyName">
            <summary>
                <para>Gets the name of the SHIFT key which is dependent upon the end-user's regional settings.

</para>
            </summary>
            <value>A string which specifies the name of the SHIFT key.
</value>


        </member>
        <member name="M:DevExpress.Utils.KeyShortcut.ToString">
            <summary>
                <para>Gets the text representation of the current shortcut.
</para>
            </summary>
            <returns>The text representation of the current shortcut.
</returns>


        </member>
        <member name="T:DevExpress.LookAndFeel.UserLookAndFeel">

            <summary>
                <para>Represents look and feel settings for controls provided by Developer Express.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.LookAndFeel.UserLookAndFeel.#ctor(System.Object)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> class.
</para>
            </summary>
            <param name="ownerControl">
		An object which represents the control that will own the created <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object. This value is assigned to the <see cref="P:DevExpress.LookAndFeel.UserLookAndFeel.OwnerControl"/> property.

            </param>


        </member>
        <member name="P:DevExpress.LookAndFeel.UserLookAndFeel.ActiveLookAndFeel">
            <summary>
                <para>Gets the actual <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object currently applied.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object currently applied.
</value>


        </member>
        <member name="P:DevExpress.LookAndFeel.UserLookAndFeel.ActiveSkinName">
            <summary>
                <para>Gets the name of the currently applied skin. 
</para>
            </summary>
            <value>A string which specifies the name of the currently applied skin.
</value>


        </member>
        <member name="P:DevExpress.LookAndFeel.UserLookAndFeel.ActiveStyle">
            <summary>
                <para>Gets the style currently applied.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.LookAndFeel.ActiveLookAndFeelStyle"/> value specifying the currently applied style.
</value>


        </member>
        <member name="M:DevExpress.LookAndFeel.UserLookAndFeel.Assign(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Copies properties of the specified <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object to the current object.
</para>
            </summary>
            <param name="source">
		The source <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object.

            </param>


        </member>
        <member name="P:DevExpress.LookAndFeel.UserLookAndFeel.Default">
            <summary>
                <para>Gets the Default LookAndFeel object which should be used for painting controls when the <see cref="P:DevExpress.LookAndFeel.UserLookAndFeel.UseDefaultLookAndFeel"/> property is set to <b>true</b>.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object referring to the Default LookAndFeel object.
</value>


        </member>
        <member name="F:DevExpress.LookAndFeel.UserLookAndFeel.DefaultSkinName">
            <summary>
                <para>Gets the name of the default skin style.
</para>
            </summary>
            <returns> [To be supplied] </returns>


        </member>
        <member name="M:DevExpress.LookAndFeel.UserLookAndFeel.Dispose">
            <summary>
                <para>Releases all resources used by the current object.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.LookAndFeel.UserLookAndFeel.IsEquals(DevExpress.LookAndFeel.UserLookAndFeel)">
            <summary>
                <para>Tests whether two objects have the same property values.
</para>
            </summary>
            <param name="source">
		The <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object to which the current object is compared.

            </param>
            <returns><b>true</b> if the current object has the same property values as the specified object; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.LookAndFeel.UserLookAndFeel.OwnerControl">
            <summary>
                <para>Gets the control that owns the current <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object.
</para>
            </summary>
            <value>An object which represents the control that owns the current <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object.
</value>


        </member>
        <member name="P:DevExpress.LookAndFeel.UserLookAndFeel.Painter">
            <summary>
                <para>Gets the painter based on the style currently applied for drawing control's elements.
</para>
            </summary>
            <value>A <b>BaseLookAndFeelPainters</b> class descendant specifying the painter corresponding to the current style which is defined by <see cref="P:DevExpress.LookAndFeel.UserLookAndFeel.ActiveStyle"/>.
</value>


        </member>
        <member name="P:DevExpress.LookAndFeel.UserLookAndFeel.ParentLookAndFeel">
            <summary>
                <para>Gets or sets the parent LookAndFeel.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object specifying the parent LookAndFeel; <b>null</b> if the parent LookAndFeel is not assigned.
</value>


        </member>
        <member name="M:DevExpress.LookAndFeel.UserLookAndFeel.Reset">
            <summary>
                <para>Reverts the look and feel settings to their default values.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.LookAndFeel.UserLookAndFeel.ResetParentLookAndFeel">
            <summary>
                <para>Sets the <see cref="P:DevExpress.LookAndFeel.UserLookAndFeel.ParentLookAndFeel"/> property to its default value.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.LookAndFeel.UserLookAndFeel.SetDefaultStyle">
            <summary>
                <para>Applies the default look and feel settings to a control.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.LookAndFeel.UserLookAndFeel.SetFlatStyle">
            <summary>
                <para>Applies the <b>Flat</b> style to a control.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.LookAndFeel.UserLookAndFeel.SetOffice2003Style">
            <summary>
                <para>Applies the <b>Office2003</b> style to a control.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.LookAndFeel.UserLookAndFeel.SetSkinStyle(System.String)">
            <summary>
                <para>Specifies the skin style by its name.
</para>
            </summary>
            <param name="skinName">
		A string value specifying the name of a skin style.

            </param>


        </member>
        <member name="M:DevExpress.LookAndFeel.UserLookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle,System.Boolean,System.Boolean,System.String)">
            <summary>
                <para>Sets the look and feel settings of the current object to the specified values.
</para>
            </summary>
            <param name="style">
		A <see cref="T:DevExpress.LookAndFeel.LookAndFeelStyle"/> enumeration value which specifies the style of the current object.

            </param>
            <param name="useWindowsXPTheme">
		<b>true</b> if a control should be painted using the WindowsXP theme; otherwise, <b>false</b>.

            </param>
            <param name="useDefaultLookAndFeel">
		<b>true</b> to use the look and feel settings provided by the parent object or default look and feel object; <b>false</b> to enable the current object's settings.

            </param>
            <param name="skinName">
		A string value specifying the skin style's name.

            </param>


        </member>
        <member name="M:DevExpress.LookAndFeel.UserLookAndFeel.SetStyle(DevExpress.LookAndFeel.LookAndFeelStyle,System.Boolean,System.Boolean)">
            <summary>
                <para>Sets the look and feel settings of the current object.
</para>
            </summary>
            <param name="style">
		A <see cref="T:DevExpress.LookAndFeel.LookAndFeelStyle"/> enumeration value which specifies the style of the current object.

            </param>
            <param name="useWindowsXPTheme">
		<b>true</b> if a control should be painted using the WindowsXP theme; otherwise, <b>false</b>.

            </param>
            <param name="useDefaultLookAndFeel">
		<b>true</b> to use the look and feel settings provided by the parent object or default look and feel object; <b>false</b> to enable the current object's settings.

            </param>


        </member>
        <member name="M:DevExpress.LookAndFeel.UserLookAndFeel.SetStyle3D">
            <summary>
                <para>Applies the <b>Style3D</b> style to a control.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.LookAndFeel.UserLookAndFeel.SetUltraFlatStyle">
            <summary>
                <para>Applies the <b>UltraFlat</b> style to a control.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.LookAndFeel.UserLookAndFeel.SetWindowsXPStyle">
            <summary>
                <para>Applies the <b>WindowsXP</b> style to a control.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.LookAndFeel.UserLookAndFeel.ShouldSerialize">
            <summary>
                <para>Tests whether the <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object should be persisted.
</para>
            </summary>
            <returns><b>true</b> if the object should be persisted; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.LookAndFeel.UserLookAndFeel.SkinName">
            <summary>
                <para>Gets or sets the name of a skin style.
</para>
            </summary>
            <value>A string value specifying the skin style's name.
</value>


        </member>
        <member name="P:DevExpress.LookAndFeel.UserLookAndFeel.Style">
            <summary>
                <para>Gets or sets the style of the current <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object.
</para>
            </summary>
            <value>The style of the current <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object.
</value>


        </member>
        <member name="E:DevExpress.LookAndFeel.UserLookAndFeel.StyleChanged">
            <summary>
                <para>Occurs on changing properties of the current <see cref="T:DevExpress.LookAndFeel.UserLookAndFeel"/> object or the parent LookAndFeel specified by the <see cref="P:DevExpress.LookAndFeel.UserLookAndFeel.ParentLookAndFeel"/> member.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.LookAndFeel.UserLookAndFeel.ToString">
            <summary>
                <para>Returns the text representation of the current object.
</para>
            </summary>
            <returns>The text representation of the current object.
</returns>


        </member>
        <member name="P:DevExpress.LookAndFeel.UserLookAndFeel.UseDefaultLookAndFeel">
            <summary>
                <para>Gets or sets whether the current object's settings are in effect.
</para>
            </summary>
            <value><b>true</b> to use look and feel settings provided by the parent object or default look and feel object; <b>false</b> to enable this object's settings.
</value>


        </member>
        <member name="P:DevExpress.LookAndFeel.UserLookAndFeel.UseWindows7Border">
            <summary>
                <para>Gets or sets whether borders of <see cref="T:DevExpress.XtraEditors.TextEdit"/> controls are painted in the same manner as borders of standard text editors built into Microsoft Windows 7. This property is in effect if the current OS is Windows 7 and the <see cref="P:DevExpress.LookAndFeel.UserLookAndFeel.UseWindowsXPTheme"/> property is set to <b>true</b>.
</para>
            </summary>
            <value><b>true</b> if borders of <see cref="T:DevExpress.XtraEditors.TextEdit"/> controls are painted in the same manner as borders of standard text editors; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.LookAndFeel.UserLookAndFeel.UseWindowsXPTheme">
            <summary>
                <para>Gets or sets whether controls should be painted using the native Windows theme.
</para>
            </summary>
            <value><b>true</b> if controls should be painted using the native Windows theme; otherwise, <b>false</b>.
</value>


        </member>
        <member name="T:DevExpress.LookAndFeel.LookAndFeelStyle">

            <summary>
                <para>Enumerates values for the 
<see cref="P:DevExpress.LookAndFeel.UserLookAndFeel.Style"/> property.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.LookAndFeel.LookAndFeelStyle.Flat">
            <summary>
                <para>Control borders are flat. The following screenshot shows the <b>Flat</b> style applied to a button edit control.
<para>

</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.LookAndFeel.LookAndFeelStyle.Office2003">
            <summary>
                <para>Borders and buttons have an Office 2003 style. The borders and client area are highlighted when the mouse pointer is positioned over them or they are focused. 
The following screenshot shows the <b>Office2003</b> style applied to a button edit control.
<para>

</para>
<para>

</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.LookAndFeel.LookAndFeelStyle.Skin">
            <summary>
                <para><para>Control elements are painted using the skin specified by the <see cref="P:DevExpress.LookAndFeel.UserLookAndFeel.SkinName"/> property. The following screenshot shows a button editor painted using the default <b>Caramel</b> skin. </para>


</para>
            </summary>


        </member>
        <member name="F:DevExpress.LookAndFeel.LookAndFeelStyle.Style3D">
            <summary>
                <para>Control borders are three-dimensional. The following screenshot shows the <b>Style3D</b> style applied to a button edit control.
<para>

</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.LookAndFeel.LookAndFeelStyle.UltraFlat">
            <summary>
                <para>Borders have an Office XP style. Borders and the client area are highlighted when the mouse pointer is positioned over them or they are focused. 
The following screenshot shows the <b>UltraFlat</b> style applied to a button edit control.
<para>

</para>
<para>

</para>
</para>
            </summary>


        </member>
        <member name="T:DevExpress.LookAndFeel.ActiveLookAndFeelStyle">

            <summary>
                <para>Enumerates values for the <see cref="P:DevExpress.LookAndFeel.UserLookAndFeel.ActiveStyle"/> property.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.LookAndFeel.ActiveLookAndFeelStyle.Flat">
            <summary>
                <para>Control borders are flat. The following screenshot shows the <b>Flat</b> style applied to a button edit control.
<para>

</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.LookAndFeel.ActiveLookAndFeelStyle.Office2003">
            <summary>
                <para>Borders and buttons have an Office 2003 style. The borders and client area are highlighted when the mouse pointer is positioned over them or they are focused. 
The following screenshot shows the <b>Office2003</b> style applied to a button edit control.
<para>

</para>
<para>

</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.LookAndFeel.ActiveLookAndFeelStyle.Skin">
            <summary>
                <para><para>Control elements are painted using the skin determined by the <see cref="P:DevExpress.LookAndFeel.UserLookAndFeel.SkinName"/> property. The following screenshot shows a button editor painted using the default <b>Caramel</b> skin. </para>


</para>
            </summary>


        </member>
        <member name="F:DevExpress.LookAndFeel.ActiveLookAndFeelStyle.Style3D">
            <summary>
                <para>Control borders are three-dimensional. The following screenshot shows the <b>Style3D</b> style applied to a button edit control.
<para>

</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.LookAndFeel.ActiveLookAndFeelStyle.UltraFlat">
            <summary>
                <para>Borders have an Office XP style. Borders and the client area are highlighted when the mouse pointer is positioned over them or they are focused. 
The following screenshot shows the <b>UltraFlat</b> style applied to a button edit control.
<para>

</para>
<para>

</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.LookAndFeel.ActiveLookAndFeelStyle.WindowsXP">
            <summary>
                <para>Control elements are painted using the XP theme.  The following screenshot shows the <b>WindowsXP</b> style applied to a button edit control.
<para>

</para>
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.Menu.MenuViewType">

            <summary>
                <para>Contains values that specify how a <see cref="T:DevExpress.Utils.Menu.DXPopupMenu"/> is displayed.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Utils.Menu.MenuViewType.Menu">
            <summary>
                <para>A menu is displayed as a regular popup menu.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Menu.MenuViewType.RibbonMiniToolbar">
            <summary>
                <para>A menu is displayed as a <see cref="T:DevExpress.XtraBars.Ribbon.RibbonMiniToolbar"/>.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.Menu.MenuViewType.Toolbar">
            <summary>
                <para>A menu is displayed as a popup bar.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.Menu.DXButtonGroupItem">

            <summary>
                <para>A group of buttons that can be displayed within a <see cref="T:DevExpress.Utils.Menu.DXPopupMenu"/> object.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.Menu.DXButtonGroupItem.#ctor">
            <summary>
                <para>Initializes a new DXButtonGroupItem instance with the default settings.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraEditors.Controls.BorderSide">

            <summary>
                <para>Contains values specifying border sides.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraEditors.Controls.BorderSide.All">
            <summary>
                <para>All border sides are painted. Enabling this flag results in enabling the <b>Left</b>, <b>Top</b>, <b>Right</b> and <b>Bottom</b> flags and disabling the <b>None</b> flag.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.BorderSide.Bottom">
            <summary>
                <para>Bottom border side is painted.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.BorderSide.Left">
            <summary>
                <para>Left border side is painted.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.BorderSide.None">
            <summary>
                <para>None of border sides is painted. Enabling this option results in disabling all other options.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.BorderSide.Right">
            <summary>
                <para>Right border side is painted.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.BorderSide.Top">
            <summary>
                <para>Top border side is painted.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.Utils.Drawing.GraphicsCache">

            <summary>
                <para>Provides storage for pens, fonts and brushes used during painting.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.Utils.Drawing.GraphicsCache.#ctor(System.Windows.Forms.PaintEventArgs,DevExpress.Utils.Paint.XPaint)">
            <summary>
                <para>Creates an instance of the <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> class.
</para>
            </summary>
            <param name="e">
		A <see cref="T:System.Windows.Forms.PaintEventArgs"/> object that contains data used for painting. This value is assigned to the <see cref="P:DevExpress.Utils.Drawing.GraphicsCache.PaintArgs"/> property.

            </param>
            <param name="paint">
		A <b>DevExpress.Utils.Paint.XPaint</b> object providing painting methods used in the <see cref="T:DevExpress.XtraNavBar.NavBarControl"/> control. This value is assigned to the <see cref="P:DevExpress.Utils.Drawing.GraphicsCache.Paint"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Drawing.GraphicsCache.#ctor(System.Windows.Forms.PaintEventArgs)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> class.
</para>
            </summary>
            <param name="e">
		A <see cref="T:System.Windows.Forms.PaintEventArgs"/> object that contains data used to paint.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Drawing.GraphicsCache.#ctor(System.Drawing.Graphics)">
            <summary>
                <para>Creates an instance of the <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> class.
</para>
            </summary>
            <param name="g">
		The <see cref="T:System.Drawing.Graphics"/> object to initialize the <see cref="P:DevExpress.Utils.Drawing.GraphicsCache.Graphics"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Drawing.GraphicsCache.#ctor(DevExpress.Utils.Drawing.DXPaintEventArgs)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> class.
</para>
            </summary>
            <param name="e">
		A <see cref="T:DevExpress.Utils.Drawing.DXPaintEventArgs"/> object that contains data used to perform painting. This value is assigned to the <see cref="P:DevExpress.Utils.Drawing.GraphicsCache.PaintArgs"/> property.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Drawing.GraphicsCache.#ctor(DevExpress.Utils.Drawing.DXPaintEventArgs,DevExpress.Utils.Paint.XPaint)">
            <summary>
                <para>Initializes a new instance of the <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> class
</para>
            </summary>
            <param name="e">
		A <see cref="T:DevExpress.Utils.Drawing.DXPaintEventArgs"/> object that contains data used to perform painting. This value is assigned to the <see cref="P:DevExpress.Utils.Drawing.GraphicsCache.PaintArgs"/> property.

            </param>
            <param name="paint">
		A <b>DevExpress.Utils.Paint.XPaint</b>  object that provides methods used for drawing objects. This value is assigned to the <see cref="P:DevExpress.Utils.Drawing.GraphicsCache.Paint"/> property.

            </param>


        </member>
        <member name="P:DevExpress.Utils.Drawing.GraphicsCache.Cache">
            <summary>
                <para>This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.Drawing.ResourceCache"/> object.
</value>


        </member>
        <member name="M:DevExpress.Utils.Drawing.GraphicsCache.CalcClipRectangle(System.Drawing.Rectangle)">
            <summary>
                <para>This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="r">
		A <see cref="T:System.Drawing.Rectangle"/> structure.

            </param>
            <returns>A <see cref="T:System.Drawing.Rectangle"/> structure.
</returns>


        </member>
        <member name="M:DevExpress.Utils.Drawing.GraphicsCache.CalcRectangle(System.Drawing.Rectangle)">
            <summary>
                <para>This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <param name="r">
		A <see cref="T:System.Drawing.Rectangle"/> structure.

            </param>
            <returns>A <see cref="T:System.Drawing.Rectangle"/> structure.
</returns>


        </member>
        <member name="M:DevExpress.Utils.Drawing.GraphicsCache.CalcTextSize(System.String,System.Drawing.Font,System.Drawing.StringFormat,System.Int32)">
            <summary>
                <para>Calculates the length of the specified string when it's drawn with the specified font and using the specified formatting.
</para>
            </summary>
            <param name="text">
		A <see cref="T:System.String"/> value which represents a string to measure.

            </param>
            <param name="font">
		A <see cref="T:System.Drawing.Font"/> object which defines the font and the associated settings of the text.

            </param>
            <param name="strFormat">
		A <see cref="T:System.Drawing.StringFormat"/> object which represents formatting information, such as the line spacing and alignment of the string.

            </param>
            <param name="maxWidth">
		An integer value specifying the maximum width of the string (in pixels).

            </param>
            <returns>A <see cref="T:System.Drawing.SizeF"/> structure which represents the size (in pixels) of the string.
</returns>


        </member>
        <member name="M:DevExpress.Utils.Drawing.GraphicsCache.Clear">
            <summary>
                <para>Clears the internal hash tables that store the pens, fonts and, solid and linear gradient brushes that have recently been used.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Utils.Drawing.GraphicsCache.ClipInfo">
            <summary>
                <para>This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.Drawing.GraphicsClip"/> object.
</value>


        </member>
        <member name="M:DevExpress.Utils.Drawing.GraphicsCache.Dispose">
            <summary>
                <para>Releases all resources used by this <see cref="T:DevExpress.Utils.Drawing.GraphicsCache"/> object.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.Utils.Drawing.GraphicsCache.DrawRectangle(System.Drawing.Pen,System.Drawing.Rectangle)">
            <summary>
                <para>Draws a rectangle specified by a <see cref="T:System.Drawing.Rectangle"/> structure.
</para>
            </summary>
            <param name="pen">
		A <see cref="T:System.Drawing.Pen"/> object which specifies the color, width and style of the rectangle.

            </param>
            <param name="r">
		A <see cref="T:System.Drawing.Rectangle"/> structure which represents the rectangle to draw.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Drawing.GraphicsCache.DrawString(System.String,System.Drawing.Font,System.Drawing.Brush,System.Drawing.Rectangle,System.Drawing.StringFormat)">
            <summary>
                <para>Draws a text string at the specified position using the specified font, color and format.
</para>
            </summary>
            <param name="text">
		A <see cref="T:System.String"/> value representing the text to be drawn.

            </param>
            <param name="font">
		A <see cref="T:System.Drawing.Font"/> object which defines the font and associated settings of the text to be drawn.

            </param>
            <param name="foreBrush">
		A <see cref="T:System.Drawing.Brush"/> object which specifies the color and texture of the drawn text.

            </param>
            <param name="bounds">
		A <see cref="T:System.Drawing.Rectangle"/> structure which represents the drawing area.

            </param>
            <param name="strFormat">
		A <see cref="T:System.Drawing.StringFormat"/> object which specifies formatting attributes, such as the line spacing and alignment, that are applied to the drawn text.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Drawing.GraphicsCache.DrawVString(System.String,System.Drawing.Font,System.Drawing.Brush,System.Drawing.Rectangle,System.Drawing.StringFormat,System.Int32)">
            <summary>
                <para>Draws a text string vertically with the specified angle and at the specified position using the specified font, color and format.
</para>
            </summary>
            <param name="text">
		A <see cref="T:System.String"/> value which represents the text to be drawn.

            </param>
            <param name="font">
		A <see cref="T:System.Drawing.Font"/> object which defines the font and associated settings of the text to be drawn.

            </param>
            <param name="foreBrush">
		A <see cref="T:System.Drawing.Brush"/> object which specifies the color and texture of the drawn text.

            </param>
            <param name="bounds">
		A <see cref="T:System.Drawing.Rectangle"/> structure which represents the drawing area.

            </param>
            <param name="strFormat">
		A <see cref="T:System.Drawing.StringFormat"/> object which specifies formatting attributes such as the line spacing and alignment that are applied to the drawn text.

            </param>
            <param name="angle">
		An integer value specifying the angle in degrees at which the text should be drawn.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Drawing.GraphicsCache.FillRectangle(System.Drawing.Brush,System.Drawing.Rectangle)">
            <summary>
                <para>Fills the interior of a rectangle specified by a <see cref="T:System.Drawing.Rectangle"/> structure.
</para>
            </summary>
            <param name="brush">
		A <see cref="T:System.Drawing.Brush"/> object which represents the brush used to fill the rectangle.

            </param>
            <param name="rect">
		A <see cref="T:System.Drawing.Rectangle"/> value which represents the rectangle to fill.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Drawing.GraphicsCache.FillRectangle(System.Drawing.Color,System.Drawing.Rectangle)">
            <summary>
                <para>Fills the specified rectangular area with the specified color.
</para>
            </summary>
            <param name="color">
		A <see cref="T:System.Drawing.Color"/> value that specifies the filling color.

            </param>
            <param name="rect">
		A <see cref="T:System.Drawing.Rectangle"/> value which represents the rectangle to be filled.


            </param>


        </member>
        <member name="M:DevExpress.Utils.Drawing.GraphicsCache.FillRectangle(System.Drawing.Brush,System.Drawing.RectangleF)">
            <summary>
                <para>Fills the interior of a rectangle specified by a <see cref="T:System.Drawing.RectangleF"/> structure. 
</para>
            </summary>
            <param name="brush">
		A <see cref="T:System.Drawing.Brush"/> object which represents the brush used to fill the rectangle.

            </param>
            <param name="rect">
		A <see cref="T:System.Drawing.Rectangle"/> value which represents the rectangle to fill.

            </param>


        </member>
        <member name="M:DevExpress.Utils.Drawing.GraphicsCache.GetFont(System.Drawing.Font,System.Drawing.FontStyle)">
            <summary>
                <para>Gets a font object with the specified settings.
</para>
            </summary>
            <param name="font">
		A <see cref="T:System.Drawing.Font"/> object whose settings specify the desired font's typeface and size.

            </param>
            <param name="fontStyle">
		A bitwise combination of <see cref="T:System.Drawing.Font"/> enumeration values specifying the desired font style.

            </param>
            <returns>A <see cref="T:System.Drawing.Font"/> object representing a font with the specified settings.
</returns>


        </member>
        <member name="M:DevExpress.Utils.Drawing.GraphicsCache.GetGradientBrush(System.Drawing.Rectangle,System.Drawing.Color,System.Drawing.Color,System.Drawing.Drawing2D.LinearGradientMode)">
            <summary>
                <para>Returns a linear gradient brush with specified settings.
</para>
            </summary>
            <param name="rect">
		A <b>System.Drawing.Rectangle</b> structure that specifies the endpoints of the linear gradient. The starting point is the upper-left corner of the rectangle, and the endpoint is the upper-right corner of the rectangle. 

            </param>
            <param name="startColor">
		A <b>System.Drawing.Color</b> object that represents the starting color for the gradient. 

            </param>
            <param name="endColor">
		A <b>System.Drawing.Color</b> object that represents the ending color for the gradient. 

            </param>
            <param name="mode">
		A <b>System.Drawing.Drawing2D.LinearGradientMode</b> enumeration value specifying the gradient orientation. 

            </param>
            <returns>A <b>System.Drawing.Brush</b> descendant representing the requested linear gradient brush (typically a <b>System.Drawing.Drawing2D.LinearGradientBrush</b> object).
</returns>


        </member>
        <member name="M:DevExpress.Utils.Drawing.GraphicsCache.GetGradientBrush(System.Drawing.Rectangle,System.Drawing.Color,System.Drawing.Color,System.Drawing.Drawing2D.LinearGradientMode,System.Int32)">
            <summary>
                <para>Returns a linear gradient brush with the specified settings.
</para>
            </summary>
            <param name="rect">
		A <see cref="T:System.Drawing.Rectangle"/> structure that specifies the endpoints of the linear gradient. The starting point is the upper-left corner of the rectangle, and the endpoint is the upper-right corner of the rectangle. 

            </param>
            <param name="startColor">
		A <see cref="T:System.Drawing.Color"/> object that represents the starting color for the gradient. 

            </param>
            <param name="endColor">
		A <see cref="T:System.Drawing.Color"/> object that represents the ending color for the gradient. 

            </param>
            <param name="mode">
		A <see cref="T:System.Drawing.Drawing2D.LinearGradientMode"/> enumeration value specifying the gradient's orientation. 

            </param>
            <param name="blendCount">
		An integer value specifying the number of blend patterns for the brush.

            </param>
            <returns>A <see cref="T:System.Drawing.Brush"/> descendant representing the requested linear gradient brush (typically a <b>System.Drawing.Drawing2D.LinearGradientBrush</b> object).
</returns>


        </member>
        <member name="M:DevExpress.Utils.Drawing.GraphicsCache.GetPen(System.Drawing.Color,System.Int32)">
            <summary>
                <para>Returns a pen with specified settings.
</para>
            </summary>
            <param name="color">
		A <b>System.Drawing.Color</b> object specifying the pen color.

            </param>
            <param name="width">
		An integer value specifying the pen width.

            </param>
            <returns>A <b>System.Drawing.Pen</b> object representing the requested pen.
</returns>


        </member>
        <member name="M:DevExpress.Utils.Drawing.GraphicsCache.GetPen(System.Drawing.Color)">
            <summary>
                <para>Returns the pixel-wide pen with a specified color.
</para>
            </summary>
            <param name="color">
		A <b>System.Drawing.Color</b> object specifying the pen color.

            </param>
            <returns>A <b>System.Drawing.Pen</b> object representing the requested pen.
</returns>


        </member>
        <member name="M:DevExpress.Utils.Drawing.GraphicsCache.GetSolidBrush(System.Drawing.Color)">
            <summary>
                <para>Gets a solid brush with specified parameters.
</para>
            </summary>
            <param name="color">
		A <b>System.Drawing.Color</b> object specifying the brush color.

            </param>
            <returns>A <b>System.Drawing.Brush</b> descendant representing the requested brush (typically a <b>System.Drawing.SolidBrush</b> object).
</returns>


        </member>
        <member name="P:DevExpress.Utils.Drawing.GraphicsCache.Graphics">
            <summary>
                <para>Gets an object that serves as the painting surface and provides painting facilities.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Graphics"/> object providing painting facilities.
</value>


        </member>
        <member name="M:DevExpress.Utils.Drawing.GraphicsCache.IsNeedDrawRect(System.Drawing.Rectangle)">
            <summary>
                <para>Returns whether the region which is bounded by the specified rectangle needs to be redrawn.

</para>
            </summary>
            <param name="r">
		A <see cref="T:System.Drawing.Rectangle"/> structure that specifies the rectangle to test.

            </param>
            <returns><b>true</b> if the specified rectangle needs to be redrawn; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.Utils.Drawing.GraphicsCache.Offset">
            <summary>
                <para>This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Point"/> structure.
</value>


        </member>
        <member name="P:DevExpress.Utils.Drawing.GraphicsCache.OffsetEx">
            <summary>
                <para>This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Point"/> structure.
</value>


        </member>
        <member name="P:DevExpress.Utils.Drawing.GraphicsCache.Paint">
            <summary>
                <para>Gets or sets a <b>DevExpress.Utils.Paint.XPaint</b> object.
</para>
            </summary>
            <value>A <b>DevExpress.Utils.Paint.XPaint</b> object.
</value>


        </member>
        <member name="P:DevExpress.Utils.Drawing.GraphicsCache.PaintArgs">
            <summary>
                <para>Gets an object providing information for painting.
</para>
            </summary>
            <value>A <see cref="T:DevExpress.Utils.Drawing.DXPaintEventArgs"/> object that contains data used to perform painting.
</value>


        </member>
        <member name="M:DevExpress.Utils.Drawing.GraphicsCache.ResetMatrix">
            <summary>
                <para>Resets a Matrix object used to perform geometric transformations.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.Utils.Drawing.GraphicsCache.TransformMatrix">
            <summary>
                <para>This member supports the .NET Framework infrastructure and is not intended to be used directly from your code.
</para>
            </summary>
            <value>A <see cref="T:System.Drawing.Drawing2D.Matrix"/> object.
</value>


        </member>
        <member name="T:DevExpress.Utils.XPThemeSupport">

            <summary>
                <para>Contains values specifying whether bound controls is drawn using XP style.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.Utils.XPThemeSupport.Default">
            <summary>
                <para>If bound control can display or edit data using inplace editors, latter are painted in the same style as its container. Otherwise it is drawn in XP style, if it is supported by a user's system.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.XPThemeSupport.Disabled">
            <summary>
                <para>An editor is drawn in Windows Classic style.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.Utils.XPThemeSupport.Enabled">
            <summary>
                <para>An editor is drawn in XP style if it is supported by user's system.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraEditors.XtraPanel">

            <summary>
                <para>Represents the base class for panels.
</para>
            </summary>

        </member>
        <member name="M:DevExpress.XtraEditors.XtraPanel.#ctor">
            <summary>
                <para>Initializes a new instance of the XtraPanel class.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraPanel.AutoScroll">
            <summary>
                <para>Gets or sets a value indicating whether the container enables the user to scroll to any control placed outside its visible boundaries.

</para>
            </summary>
            <value><b>true</b> if the auto-scrolling feature is enabled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraPanel.AutoSize">
            <summary>
                <para>Gets or sets whether the panel is automatically resized according to the value of the <see cref="P:DevExpress.XtraEditors.XtraPanel.AutoSizeMode"/> property.
</para>
            </summary>
            <value><b>true</b> if the panel's auto-resizing feature is enabled; otherwise, <b>false</b>.
</value>


        </member>
        <member name="E:DevExpress.XtraEditors.XtraPanel.AutoSizeChanged">
            <summary>
                <para>Fires when the value of the <see cref="P:DevExpress.XtraEditors.XtraPanel.AutoSize"/> property is changed.
</para>
            </summary>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraPanel.AutoSizeMode">
            <summary>
                <para>Indicates the automatic sizing behavior of the panel.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Forms.AutoSizeMode"/> value.
</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraPanel.BorderStyle">
            <summary>
                <para>Gets or sets the panel's border style.
</para>
            </summary>
            <value>A <see cref="T:System.Windows.Forms.BorderStyle"/> value that specifies the panel's border style.
</value>


        </member>
        <member name="M:DevExpress.XtraEditors.XtraPanel.GetPreferredSize(System.Drawing.Size)">
            <summary>
                <para>Retrieves the size of a rectangular area into which the panel can be fitted.
</para>
            </summary>
            <param name="proposedSize">
		A custom-sized area for the panel.


            </param>
            <returns>A Size value that specifies a rectangle into which the panel can be fit.

</returns>


        </member>
        <member name="E:DevExpress.XtraEditors.XtraPanel.KeyDown">
            <summary>
                <para>This member is not supported by the XtraPanel class.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraEditors.XtraPanel.KeyPress">
            <summary>
                <para>This member is not supported by the XtraPanel class.
</para>
            </summary>


        </member>
        <member name="E:DevExpress.XtraEditors.XtraPanel.KeyUp">
            <summary>
                <para>This member is not supported by the XtraPanel class.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraEditors.XtraPanel.ResetAutoSizeMode">
            <summary>
                <para>Resets the  <see cref="P:DevExpress.XtraEditors.XtraPanel.AutoSizeMode"/> property to its default value.
</para>
            </summary>


        </member>
        <member name="M:DevExpress.XtraEditors.XtraPanel.ShouldSerializeAutoSizeMode">
            <summary>
                <para>Gets whether the <see cref="P:DevExpress.XtraEditors.XtraPanel.AutoSizeMode"/> property should be serialized.
</para>
            </summary>
            <returns><b>true</b> if the <see cref="P:DevExpress.XtraEditors.XtraPanel.AutoSizeMode"/> property should be serialized; otherwise, <b>false</b>.
</returns>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraPanel.TabStop">
            <summary>
                <para>Gets or sets a value indicating whether the user can focus this control using the TAB key.


</para>
            </summary>
            <value><b>true</b> if the user can focus this control using the TAB key; otherwise, <b>false</b>.

</value>


        </member>
        <member name="P:DevExpress.XtraEditors.XtraPanel.Text">
            <summary>
                <para>This member is not supported by the XtraPanel class.
</para>
            </summary>
            <value>@nbsp;
</value>


        </member>
        <member name="E:DevExpress.XtraEditors.XtraPanel.TextChanged">
            <summary>
                <para>This member is not supported by the XtraPanel class.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraEditors.SplitFixedPanel">

            <summary>
                <para>Lists values that specify which panel within the split container is fixed.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraEditors.SplitFixedPanel.None">
            <summary>
                <para>The panels' widths (or heights) are proportionally changed when a split container is being resized.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.SplitFixedPanel.Panel1">
            <summary>
                <para>The width (height if the panels are oriented one above another) of the <see cref="P:DevExpress.XtraEditors.SplitContainerControl.Panel1"/> isn't changed when the split container is resized.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.SplitFixedPanel.Panel2">
            <summary>
                <para>The width (height if the panels are oriented one above another) of the <see cref="P:DevExpress.XtraEditors.SplitContainerControl.Panel2"/> isn't changed when the split container is resized.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraEditors.Controls.StyleIndeterminate">

            <summary>
                <para>Contains values specifying the display style for the check box when an editor is in an indeterminate state.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraEditors.Controls.StyleIndeterminate.Inactive">
            <summary>
                <para>A check box is drawn inactive (grayed). 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.StyleIndeterminate.InactiveChecked">
            <summary>
                <para>A check box is drawn grayed and checked (partially checked). 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.StyleIndeterminate.Unchecked">
            <summary>
                <para>A check box is drawn unchecked. 
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraEditors.Controls.CheckStyles">

            <summary>
                <para>Contains values specifying the look of the check box within a <see cref="T:DevExpress.XtraEditors.CheckEdit"/> control.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraEditors.Controls.CheckStyles.Radio">
            <summary>
                <para>A check box element looks like a standard radio button within a check editor. 
<para>
 - when Windows XP Themes are supported;
</para>
 - when Windows XP Themes are not supported.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.CheckStyles.Standard">
            <summary>
                <para>The default look and feel defined by the control class. 
<para>
 - when Windows XP Themes are supported;
</para>
 - when Windows XP Themes are not supported.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.CheckStyles.Style1">
            <summary>
                <para>Predefined custom style. 
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.CheckStyles.Style10">
            <summary>
                <para>Predefined custom style. 

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.CheckStyles.Style11">
            <summary>
                <para>Predefined custom style. 

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.CheckStyles.Style12">
            <summary>
                <para>Predefined custom style. 

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.CheckStyles.Style13">
            <summary>
                <para>Predefined custom style. 

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.CheckStyles.Style14">
            <summary>
                <para>Predefined custom style. 

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.CheckStyles.Style15">
            <summary>
                <para>Predefined custom style. 

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.CheckStyles.Style16">
            <summary>
                <para>Predefined custom style. 

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.CheckStyles.Style2">
            <summary>
                <para>Predefined custom style. 

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.CheckStyles.Style3">
            <summary>
                <para>Predefined custom style. 

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.CheckStyles.Style4">
            <summary>
                <para>Predefined custom style. 

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.CheckStyles.Style5">
            <summary>
                <para>Predefined custom style. 

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.CheckStyles.Style6">
            <summary>
                <para>Predefined custom style. 

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.CheckStyles.Style7">
            <summary>
                <para>Predefined custom style. 

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.CheckStyles.Style8">
            <summary>
                <para>Predefined custom style. 

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.CheckStyles.Style9">
            <summary>
                <para>Predefined custom style. 

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.CheckStyles.UserDefined">
            <summary>
                <para>Allows a user to embed custom check mark pictures into a check editor via 
1) the <see cref="P:DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit.PictureChecked"/>, <see cref="P:DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit.PictureUnchecked"/> and <see cref="P:DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit.PictureGrayed"/> properties or 
2) the <see cref="P:DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit.ImageIndexChecked"/>, <see cref="P:DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit.ImageIndexUnchecked"/> and <see cref="P:DevExpress.XtraEditors.Repository.RepositoryItemCheckEdit.ImageIndexGrayed"/> properties.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraEditors.Controls.ButtonStates">

            <summary>
                <para>Specifies the state of an <see cref="T:DevExpress.XtraEditors.Controls.EditorButton"/>.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraEditors.Controls.ButtonStates.DeepPush">
            <summary>
                <para>A button is highlighted when mouse crosses the entire button boundaries.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.ButtonStates.Disabled">
            <summary>
                <para>A button is disabled and does not respond to end-user actions.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.ButtonStates.Hottrack">
            <summary>
                <para>A button is highlighted when the mouse enters its boundaries.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.ButtonStates.None">
            <summary>
                <para>A button is drawn using its default look and feel.
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.ButtonStates.Push">
            <summary>
                <para>A button is drawn to reflect a pressed state.
</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraEditors.Controls.ButtonPredefines">

            <summary>
                <para>Specifies the surface image of a button.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraEditors.Controls.ButtonPredefines.Close">
            <summary>
                <para>A Close symbol is displayed on the button's surface.
<para>

</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.ButtonPredefines.Combo">
            <summary>
                <para>A Down-arrow for a combo box is drawn on the button's surface.
<para>

</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.ButtonPredefines.Delete">
            <summary>
                <para>A Delete symbol is drawn on the button's surface.
<para>

</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.ButtonPredefines.Down">
            <summary>
                <para>A Down-arrow is drawn on the button's surface.
<para>

</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.ButtonPredefines.DropDown">
            <summary>
                <para>A Down-arrow is drawn on the button's surface. Unlike, the <b>Down</b> button, this kind of button allows text to be displayed next to the down-arrow.

<para>

</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.ButtonPredefines.Ellipsis">
            <summary>
                <para>An Ellipsis symbol is drawn on the button's surface.
<para>

</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.ButtonPredefines.Glyph">
            <summary>
                <para>A custom bitmap is drawn on the button's surface.


</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.ButtonPredefines.Left">
            <summary>
                <para>A Left-arrow symbol is drawn on the button's surface.
<para>

</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.ButtonPredefines.Minus">
            <summary>
                <para>A Minus sign is drawn on the button's surface.
<para>

</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.ButtonPredefines.OK">
            <summary>
                <para>An OK sign is drawn on the button's surface.
<para>

</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.ButtonPredefines.Plus">
            <summary>
                <para>A Plus sign is drawn on the button's surface.
<para>

</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.ButtonPredefines.Redo">
            <summary>
                <para>A Redo symbol is drawn on the button's surface.
<para>

</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.ButtonPredefines.Right">
            <summary>
                <para>A Right-arrow is drawn the button's surface.
<para>

</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.ButtonPredefines.SpinDown">
            <summary>
                <para>A down-arrow for a spin editor is displayed on the button's surface.
<para>

</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.ButtonPredefines.SpinLeft">
            <summary>
                <para>A left-arrow for a spin editor is displayed on the button's surface.
<para>

</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.ButtonPredefines.SpinRight">
            <summary>
                <para>A right-arrow for a spin editor is displayed on the button's surface.
<para>

</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.ButtonPredefines.SpinUp">
            <summary>
                <para>An up-arrow for a spin editor is displayed on the button's surface.
<para>

</para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.ButtonPredefines.Undo">
            <summary>
                <para>An Undo symbol is drawn on the button's surface.
<para>

</para>

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.ButtonPredefines.Up">
            <summary>
                <para>An Up-arrow is drawn on the button's surface.
<para>

</para>

</para>
            </summary>


        </member>
        <member name="T:DevExpress.XtraEditors.Controls.BorderStyles">

            <summary>
                <para>Specifies the style for borders to draw on a <b>Graphics</b> surface.
</para>
            </summary>

        </member>
        <member name="F:DevExpress.XtraEditors.Controls.BorderStyles.Default">
            <summary>
                <para><para>The style of borders is determined by the current settings of the <b>LookAndFeel</b> object.</para>
For <see cref="T:DevExpress.XtraEditors.Repository.RepositoryItem"/> class, for example, borders are drawn based on the settings of the <see cref="P:DevExpress.XtraEditors.Repository.RepositoryItem.LookAndFeel"/> property in this case.

</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.BorderStyles.Flat">
            <summary>
                <para><para>Borders are flat.</para>
The image below demonstrates the <b>Flat</b> style applied to the <see cref="T:DevExpress.XtraEditors.ButtonEdit"/> control.

<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.BorderStyles.HotFlat">
            <summary>
                <para><para>Borders are flat. Borders and the client area are highlighted when the mouse pointer is positioned over them.</para>
The image below demonstrates the <b>HotFlat</b> style applied to the <see cref="T:DevExpress.XtraEditors.ButtonEdit"/> control.
<para></para>
<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.BorderStyles.NoBorder">
            <summary>
                <para><para>There are no borders.</para>
The image below demonstrates the <b>NoBorder</b> style applied to the <see cref="T:DevExpress.XtraEditors.ButtonEdit"/> control.

<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.BorderStyles.Office2003">
            <summary>
                <para><para>Borders have an Office 2003 style.</para>
<para>The image below demonstrates the <b>Office2003</b> style applied to a <see cref="T:DevExpress.XtraEditors.ButtonEdit"/> control.</para>
<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.BorderStyles.Simple">
            <summary>
                <para><para>Borders are flat.</para>
The image below demonstrates the <b>Simple</b> style applied to the <see cref="T:DevExpress.XtraEditors.ButtonEdit"/> control.

<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.BorderStyles.Style3D">
            <summary>
                <para><para>Borders are three-dimensional.</para>
The image below demonstrates the <b>Style3D</b> style applied to the <see cref="T:DevExpress.XtraEditors.ButtonEdit"/> control.

<para></para>
</para>
            </summary>


        </member>
        <member name="F:DevExpress.XtraEditors.Controls.BorderStyles.UltraFlat">
            <summary>
                <para><para>Borders have an Office XP style. Borders and the client area are highlighted when the mouse pointer is positioned over them or they are focused. Otherwise, there are no borders.</para>
The image below demonstrates the <b>UltraFlat</b> style applied to the <see cref="T:DevExpress.XtraEditors.ButtonEdit"/> control.

<para></para>
<para></para>
</para>
            </summary>


        </member>
    </members>
</doc>
